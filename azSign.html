<!DOCTYPE html>
<html>
<head>
  <meta charset='UTF-8'>
  <meta name="viewport" content="width=device-width,initial-scale=1.0,user-scalable=no">
  <style>
      #az-iframe {
          position: fixed;
          z-index: 99999;
          top: 0;
          left: 0;
          width: 100vw;
          height: 100vh;
          background-color: #fff;
      }
  </style>
</head>
<body>
<div>
  <iframe id='az-iframe' src='' frameborder='no'></iframe>
</div>
</body>
</html>
<script type='text/javascript' src='jweixin-1.3.2.js'></script>
<script>
  let AzDom = document.getElementById('az-iframe');
  // 获取？之后的参数
  let params = location.search;
  // 获取域名
  let basePath = location.origin;
  // 设置src属性值
  AzDom.setAttribute('src', `${basePath}/js/front/h5/acceptAdapterCmdCode${params}`)
  window.addEventListener('message', getMessage)
  let userAgent = navigator.userAgent;
  // 是否是微信小程序
  let isMini = /miniProgram/i.test(userAgent);
  // 与iframe之前通讯
  function getMessage(e) {
    if(isMini){
      wx.miniProgram.postMessage({
        data: e.data
      })
      // 向小程序发送消息，会在特定时机（小程序后退、组件销毁、分享）触发组件的 message 事件
      wx.miniProgram.navigateBack({ delta: 1 })
    }else{
      console.log("请在微信小程序打开")
    }

  }

</script>
