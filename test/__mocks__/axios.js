const preTelCheckRes = {"retCode": '0', "retMsg": '该号码已经补录过', "data": {"installType": "1"}}

const successRes = {
  "retCode": "0",
  "retMsg": "成功",
  "data":{
    "recoId":'24444444444444',
    "recId":'24444444444444'
  }
}
const failRes = {
  "retCode": "-1",
  "retMsg": "请求失败"
}
function getQueryVariable(url){
  var idx = url.indexOf('?');
  let query = url.substr(idx+1);;
  let vars = query.split("&");
  let qryMap = {};
  for (let i = 0; i < vars.length; i++) {
      var pair = vars[i].split("=");
      let value = pair[1];
      if(pair.length > 2){
          for(let j = 2; j < pair.length; j++){
              if(pair[j]){
                  value += pair[j];
              } else {
                  value += '=';
              }
          }
      }
      qryMap[pair[0]] = value;
  }
  return qryMap;
}
export default {
    get (url) {
      // console.info(url);
      let qryMap = getQueryVariable(url);
      if (url === '/xsb/ability/nationCard/h5qryNationList?type=2') {
        return new Promise((resolve) => {
          resolve(preTelCheckRes)
        })
      } else if(url && ~url.indexOf('/xsb/ability/businessLimit/h5GetPayModes')){//支付方式 话费、现金、在线
        let dictId = qryMap['dictId'];
        let res = successRes;
        if(dictId == '1024'){//模拟返回三种支付方式的情况
          res.data = [{"payType": "online-pay", "result": "1"},
                      {"payType": "cash-pay", "result": "1"},
                      {"payType": "tel-pay", "result": "1"}];
        } else if(dictId == '1040'){//模拟返回两种支付方式的情况
          res.data = [{"payType": "online-pay", "result": "1"},
                      {"payType": "tel-pay", "result": "1"}];
        } else if(dictId == '1052'){//模拟返回一种非现金支付方式的情况
          res.data = [{"payType": "tel-pay", "result": "1"}];
        } else if(dictId == '1025'){//模拟返回一种现金支付方式的情况
          res.data = [{"payType": "online-pay", "result": "1"}]
        }
        // console.info(res);
        return new Promise((resolve) => {
          resolve(addAxiosRes(res));
        })
      } else {
        return new Promise((resolve) => {
          resolve(addAxiosRes(successRes))
        })
      }
    },
    post(url,param){
        if(url == '/xsb/personBusiness/preTelEnterNet/h5preTelCheck'){
            return new Promise((resolve) => {
                resolve(addAxiosRes(preTelCheckRes))
            }) 
        }
    }
  }

  function addAxiosRes(basicRes){
    return {
      data:basicRes
    }
  }