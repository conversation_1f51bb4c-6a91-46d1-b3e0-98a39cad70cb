/**
 * 和客户端交互的接口
 */
const ClientJs = {
	longitude: '',//经度
	latitude: '',//纬度
	address: '',//定位地址
	isAndroid: true,

	/**
	 * 获取路由、手机设备信息
	 * phonemodel 手机型号
	 * phonesystemNum 手机版本
	 * serverUrl 当前路由信息
	 * verison 版本信息
	 */
	getSysInfo:function(callbackfunc){

		callbackfunc({serverUrl:'http://**************:8080',
		location:'江苏省南京市建邺区嘉陵江东街18号国家广告园6栋16楼新大陆科技有限公司',
		latitude:'123.43',
		longitude:'2343.454'})

	},
	
	/* 
	* 打开GPS
	*/
	getLocation: function(locType,callbackfunc){
		callbackfunc({address:'新大陆',location:'newload',latitude:'1.3',longitude:'1.45'})
	},
	/* 
	* 打开webview页面
	*/
	openWebKit : function(url,title,titleFlag
		,screenType,param,hasRightBtn,rightIcon
		,rightJs,hasRightBtn1,rightIcon1,rightJs1){
			//Storage.get('webUrl') +
		var webUrl = window.localStorage.getItem('webUrl');
		var isMain = 1;//走现网
		if(~webUrl.indexOf('183')){//走备用中心
			isMain = 0;
		}
		url += '&isMain=' +isMain;
		console.info(url+':::in webkit');
		var params = {};
		var paramStr;
		params.url = url;
		params.title=title;
		params.titleFlag=titleFlag;
		params.screenType=screenType;
		params.param=param;
		params.hasRightBtn=hasRightBtn;
		params.rightIcon=rightIcon;
		params.rightJs=rightJs;
		params.hasRightBtn1=hasRightBtn1;
		params.rightIcon1=rightIcon1;
		params.rightJs1=rightJs1;
		paramStr=JSON.stringify(params);
		if(this.isAndroid){
			window.WebViewFunc.openWebKit(paramStr);
		}else{
			window.location="clientRequest:openWebKit::"+paramStr;
		}
	},

	//调用客户端扫描匹配过的蓝牙
	autoReadDevise:function(cbFn){
		console.info('-----')
		cbFn({retCode:'0',retMsg:'nono',status:'1',deviceType:'3',deviceName:'华大'})
	},
	//打开拍照 1表示行商
	openCamera: function(param,noticeCallBack,cbfun2,tmpIsPublicSrc){
		console.info(tmpIsPublicSrc+"=====tmpIsPublicSrc");
		if(this.isAndroid){
			window.WebViewFunc.openCameraForALD(param,noticeCallBack,cbfun2,"1",tmpIsPublicSrc);/*  */
		}else{
			window.location="clientrequest:fsopOpenCamera::"+ param + "::" +noticeCallBack+"::" + cbfun2+"::"+"1" +"::"+tmpIsPublicSrc;
		}
	},
	//读取身份证
	readIdCard: function(cbFn,saveCb,tmpIsPublicSrc){
		console.info(tmpIsPublicSrc+"=====tmpIsPublicSrc")
		if(this.isAndroid){//安卓设备
			let tmpFlg = false;
			if(tmpIsPublicSrc == '1'){
				tmpFlg = true;
			}
			window.upload.readIdCard(cbFn,saveCb,tmpFlg);
		}else{//苹果设备
			window.location="clientrequest:readIdCard::"+cbFn +"::" + saveCb +"::"+tmpIsPublicSrc;
		}
	},
	//展示所有的蓝牙设备
	showLanyaDevice: function(cbFn,saveCb,tmpIsPublicSrc){
		console.info(tmpIsPublicSrc+"=====tmpIsPublicSrc")
		if(this.isAndroid){//安卓设备
			let tmpFlg = false;
			if(tmpIsPublicSrc == '1'){
				tmpFlg = true;
			}
			window.upload.getIdNum("1",cbFn,saveCb,tmpFlg);// 调用安卓客户端 
		}else{//苹果设备
			window.location="clientrequest:getIdNum::" + cbFn +"::" + saveCb +"::"+tmpIsPublicSrc;
		}
	},
	
	/**
	 * 刷脸展示所有的蓝牙设备
	 * @param {读证回调H5的方法} cbFn 
	 * @param {上传证件到服务端后回调H5的方法} saveCb 
	 * @param {是否走公用（1则是非预配号）} tmpIsPublicSrc 
	 * @param {透传的参数} idcarParam 
	 * @param {上传的服务端路径} idcardServerUrl 
	 */
	showFaceLanya: function(cbFn,saveCb,tmpIsPublicSrc,idcarParam,idcardServerUrl){
		console.info(tmpIsPublicSrc+"=====tmpIsPublicSrc")
		if(this.isAndroid){//安卓设备
			// getIdNumExtension(String uploadUrl, String uploadParams,
			// 	String callBack,  String serverCallBack) 
			window.upload.getIdNumExtension(idcardServerUrl,idcarParam,cbFn,saveCb);// 调用安卓客户端 
		}else{//苹果设备
			// blueToothCallBackName 	读证成功后回调前端身份证信息
			// aldCallBackName	上传成功后回调结果 ，不解析 直接把json报文返回给前端
			// isAldSelectNumber	是否选号入网    1,是 其它否  
			// idcarParam	新增字段 透传参数，必须传入 没有透传信息传{} , 客户端3des加密,服务器获取参数 key:idCardInfo
			// idcardServerUrl	新增字段 动态上传地址，非常关键字段，如果不传就走老一套逻辑     比如格式: xsb/ability/trueName/faceCheck    

			window.location="clientrequest:getIdNum::" + cbFn +"::" + saveCb +"::"+tmpIsPublicSrc+"::"+idcarParam+"::"+idcardServerUrl;
		}
	},
	
	//读取身份证
	readFaceIdCard: function(cbFn,saveCb,tmpIsPublicSrc,idcarParam,idcardServerUrl){
		console.info(tmpIsPublicSrc+"=====tmpIsPublicSrc")
		if(this.isAndroid){//安卓设备
			// let tmpFlg = false;
			// if(tmpIsPublicSrc == '1'){
			// 	tmpFlg = true;
			// }
			//String uploadUrl, String uploadParams, String callBack, final String callBack2
			window.upload.readIdCard(idcardServerUrl,idcarParam,cbFn,saveCb);
		}else{//苹果设备
			// blueToothCallBackName 	读证成功后回调前端身份证信息
			// aldCallBackName	上传成功后回调结果 ，不解析 直接把json报文返回给前端
			// isAldSelectNumber	是否选号入网    1,是 其它否  
			// idcarParam	新增字段 透传参数，必须传入 没有透传信息传{} , 客户端3des加密,服务器获取参数 key:idCardInfo
			// idcardServerUrl	新增字段 动态上传地址，非常关键字段，如果不传就走老一套逻辑     比如格式: xsb/ability/trueName/faceCheck    

			window.location="clientrequest:readIdCard::"+cbFn +"::" + saveCb +"::"+tmpIsPublicSrc+"::"+idcarParam+"::"+idcardServerUrl;
		}
	},
	//刷脸拍照
	openFaceCamera: function(param,noticeCallBack,cbfun2,tmpIsPublicSrc,compareUrl){
		console.info(tmpIsPublicSrc+"=====tmpIsPublicSrc");
		if(this.isAndroid){
			//openCameraForALD(String compareParams, String mCallBackMethod, String cbFunc2,
			//String isFsop, String tmpPublicSrc, String compareUrl)
			window.WebViewFunc.openCameraForALD(param,cbfun2,noticeCallBack,"1",tmpIsPublicSrc,compareUrl);/*  */
		}else{
			// compareParams
			// photoCallBackName
			// aldPhotoCallBackName
			// isFsopPhoto
			// isAldSelectNumber
			// faceServerUrl
			window.location="clientrequest:fsopOpenCamera::"+ param +"::" + cbfun2+ "::" +noticeCallBack +"::"+"1" +"::"+tmpIsPublicSrc+"::"+ compareUrl;
		}
	},
	//签名
	qianming:function(cbFn){
		let type='Sign';
        if (this.isAndroid) {
            window.WebViewFunc.createImageForPhoneLocal(type,cbFn);
        } else {
            window.location = "clientRequest:createImageForPhoneLocal::" + type+"::"+ cbFn;
        }
	},

	//登录成功回调
	autologinInterface:function(cbFn){
		if (this.isAndroid) {
            window.WebViewFunc.autologinInterface(cbFn);
        } else {
            window.location = "clientRequest:autologinInterface::" + cbFn;
        }
	},

    //调用发短信功能
    openMessageView:function(phoneNumber,messageContent){
        if(this.isAndroid){
            window.WebViewFunc.openMessageView(phoneNumber,messageContent);
        }else{
            window.location="clientrequest:openMessageView::"+phoneNumber+"::"+messageContent;
        }
	},
	
	/* 
	* 自动搜索蓝牙并连接
	*/
	searchSimDevice:function(callbackfunc){
		if(this.isAndroid){
			window.WebViewFunc.searchSimDevice(callbackfunc);
		}else{
			window.location="clientRequest:searchSimDevice::"+callbackfunc;
		}
	},
	/* 
	* 搜索蓝牙设备列表
	*/
	connectDevice:function(callbackfunc){
		if(this.isAndroid){
			window.WebViewFunc.connectDevice(callbackfunc);
		}else{
			window.location="clientRequest:connectDevice::"+callbackfunc;
		}
	},
    /* 
	* 断开连接SIM卡设备
	*/
	disConnectSimDevice:function(callbackfunc){
	   	if(this.isAndroid){
	   		window.WebViewFunc.disConnectSimDevice(callbackfunc);
	   	}else{
	   		window.location="clientRequest:disConnectSimDevice::"+callbackfunc;
	   	}
    },
	/* 
	* 获取SIM卡信息
	*/
	getSimCardInfo:function(callbackfunc){
		if(this.isAndroid){
			if(window.WebViewFunc.getCardInfoNewMethord){
				window.WebViewFunc.getCardInfoNewMethord(callbackfunc);
			} else {
				alert('请升级APP');
			}
		}else{
			window.location="clientRequest:getCardInfoNewMethord::"+callbackfunc;
		}
	},
	/* 
	* 写SIM卡
	*/
	writeCardData:function(writeData,callbackfunc){
		if(this.isAndroid){
			if(window.WebViewFunc.writeCardNewMethod){
				window.WebViewFunc.writeCardNewMethod(writeData,callbackfunc);
			} else {
				alert('请升级APP');
			}
		}else{
			window.location="clientRequest:writeCardNewMethod::"+writeData+"::"+callbackfunc;
		}
	},

	//打开拍照 1表示行商
	openCameraOne: function(photoCb){
		if(this.isAndroid){
			window.WebViewFunc.openCamera(photoCb,"1");
		}else{
			window.location="clientrequest:fsopOpenCamera::"+ photoCb + "::"+"1";
		}
	},
	//手机相册拍照 
	//isPreView 是否需要预览，1是，其他值否。预览时是，会将照片转换成base64字符串回调给页面
	openCameraAndShow: function(isPreView,callbackfunc){
		//图片固定传值 image_file
		let fileType = 'image_file';
		let fileNameDomId = '';
		let filePathDomId = '';
		let fileIdDomId = '';
		if(this.isAndroid){
			window.WebViewFunc.openFileSelect(fileType,fileNameDomId,filePathDomId,fileIdDomId,callbackfunc,isPreView);
		}else{
			window.location = "clientrequest:openFileFeedBackSelect::"+callbackfunc+"::1::0";
		}
	},

  //手机拍照(压缩)
  openCameraAndYaSuo: function(callbackfunc,CameraParams){
    //{"params":[{"title":"相册","type":"2"},{"title":"相机","type":"1"},{"title":"取消","type":"-1"}],"width":"","height":"","fileSize":"60"}
    if(this.isAndroid){
      window.WebViewFunc.openCameraOrPhoto(callbackfunc,CameraParams);
    }else{
      window.location = "clientrequest:openCamraComperssion::"+callbackfunc+"::"+CameraParams;
    }
  },
	//图片上传 fileSavePath 文件路径数组，多个文件时以，进行分割,不需要可传空字符串
	submitFormWithMultiFiles: function(filePaths,uploadURL,uploadParams,callbackfunc){
		let formid = '';
		let fileSavePath =''; //文件保存路径
		let fileIds = '';//预留，没使用不需要可传空字符串
		let uploadHeader = '';//请求头，不需要传空,传值时形式如：  key1,value1;key2,value2 Android有，iOS没有该参数,不需要可传空字符串

		if(this.isAndroid){
			window.WebViewFunc.submitFormWithMultiFiles(formid,filePaths,fileIds,fileSavePath,uploadURL,uploadParams,uploadHeader,callbackfunc);
		}else{
			window.location="clientrequest:submitFormWithMultiFiles::"+formid+"::"+filePaths+"::"+fileIds+"::"+fileSavePath+"::"+uploadURL+"::"+uploadParams+"::"+callbackfunc;
		}
	},

	/* 
	* 打开webview页面错误返回
	*/
	openWebKitError: function(msg){
		alert("错误："+msg);
	},

	/* 
	* 关闭webview页面
	*/
	closeCallBack: function(params){
		if(!params){
			if(this.isAndroid){
				window.WebViewFunc.closeCallBack("");
			}else{
				window.location="clientRequest:closeCallBack::";
			}
		}else{
			//eval(params);
			if(this.isAndroid){
				window.WebViewFunc.closeCallBack(params);
			}else{
				window.location="clientRequest:closeCallBack::"+params;
			}
		}
	},

	/* 
	* 关闭webview页面错误返回
	*/
	closeCallBackError: function(msg){
		alert("错误："+msg);
	},



	/* 
	* 打开GPS错误返回
	*/
	getLocationError: function(msg){
		alert("错误："+msg);
	},

	/* 
	* 打开GPS回调返回经纬度和地址
	*/
	getLocationBack: function(longitude,latitude,address){
		this.longitude=longitude;
		this.latitude=latitude;
		this.address=address;
		alert("longitude:"+longitude+" latitude:"+latitude+" address:"+address);
	},

	openLoading: function(msg){
		if(this.isAndroid){
			window.WebViewFunc.openWaitView(msg);
		}else{
			window.location="clientrequest:openWaitView::"+msg;
		}
	},
	closeLoading: function(msg){
		if(this.isAndroid){
			window.WebViewFunc.dismissWaitView();
		}else{
			window.location="clientrequest:dismissWaitView::";
		}
	},

	checkAPKIsInstall: function(packageName,callbackfunc){
		if(this.isAndroid){
			window.WebViewFunc.checkAPKIsInstall(packageName,callbackfunc);
		}
	},

	downloadAPP: function(androidInstallUrl,iosInstallUrl){
		if(this.isAndroid){
			window.WebViewFunc.downloadAPP(androidInstallUrl,iosInstallUrl);
		}
	},

	invokeJar: function(className,methodName,jsonParam) {
		if(this.isAndroid){
			window.WebViewFunc.invokeJar(className,methodName,jsonParam);
		}
	},

	close: function(params) {
		if(this.isAndroid){
			window.WebViewFunc.closeCallBack("");
		}else{
			window.location="clientRequest:closeCallBack::";
		}
	},

	openGeo: function(regionId,operatorId,operatorName,operatorPhone,systemId,callbackfunc) {
		if(this.isAndroid){
			window.WebViewFunc.startTimeLocation(regionId,operatorId,operatorName,operatorPhone,systemId,callbackfunc);
		}else{
			window.location="clientrequest:startTimeLocation::"+regionId+"::"+operatorId+"::"+operatorName+"::"+operatorPhone+"::"+systemId+"::"+callbackfunc;
		}
	},


    /*
     * 调用打电话功能
     */
    openCallPhoneView:function(phoneNumber){
        if(this.isAndroid){
            window.WebViewFunc.openCallPhoneView(phoneNumber);
        }else{
            window.location="clientrequest:openCallPhoneView::"+phoneNumber;
        }
    }
}
export default ClientJs;