import Vue from 'vue';
import Sign from '@/components/common/Sign.vue'
import { mount} from '@vue/test-utils'


describe('签名组件单元测试', () =>{
    let wrapper,vm;
    it('create',() =>{
        wrapper = mount(Sign,{
            propsData:{
                isShowSign:true
            }
        });
        vm = wrapper.vm;
        let signDom = wrapper.find('.sign-wrapper');
        // console.info(signDom);
        expect(signDom.isVisible()).toBe(true);

        jest.spyOn(window, 'signCallBackFn');
        window.signCallBackFn({retCode:'0',retImg:'adsadfadfds'}) 
        expect(window.signCallBackFn).toHaveBeenCalledWith({retCode:'0',retImg:'adsadfadfds'});//无参数的监听

    })
});