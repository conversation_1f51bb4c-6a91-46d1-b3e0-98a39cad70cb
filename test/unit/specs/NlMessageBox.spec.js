import MessageBox from '@/components/common/NlMessageBox/message-box.js'
import Vue from 'vue';

describe('弹出框组件 NlMessageBox',()=>{
    afterEach(()=>{
        //每个单元测试用例跑完关闭对话框
        MessageBox.close();
    })
    it('初始化alert弹出框,带头【温馨提示】', ()=>{
        MessageBox.alert('会话已失效请重新登录','温馨提示').then((action) => {}).catch(() => {});
        const boxDom =  document.getElementById("boxDom");
        expect(boxDom.style.display).toEqual('none');//对话框初始状态是不展示，下次刷新后才展示
        let btnDiv = boxDom.querySelector('.mint-msgbox-btns');
        let confirmBtn = btnDiv.lastChild;//确认按钮
        let cancelBtn = btnDiv.firstChild;//取消按钮
        
        return Vue.nextTick().then(()=>{
            expect(boxDom.style.display).toEqual('');//对话框展示
            let titleDiv = boxDom.querySelector('.mint-msgbox-title');
            expect(titleDiv.textContent).toEqual('温馨提示');//弹框标题
            let contentDiv = boxDom.querySelector('.mint-msgbox-message');
            expect(contentDiv.textContent).toEqual('会话已失效请重新登录');//弹框内容
            expect(btnDiv.children.length).toBe(2);//有两个按钮
            expect(confirmBtn.style.display).toEqual('');//预期确认按钮展示
            expect(cancelBtn.style.display).toEqual('none');//预期取消按钮不展示
            
        })
    });
    it('初始化alert弹出框,不带title',()=>{
        MessageBox.alert('会话已失效请重新登录').then((action) => {}).catch(() => {});
        return Vue.nextTick().then(()=>{
            let titleDiv = document.querySelector('.mint-msgbox-title');
            expect(titleDiv.textContent).toEqual('提示')
        })
    });
    it('初始化confirm弹出框', () => {
        
        MessageBox.confirm('确认删除吗？').then( action => {});
        let btnDiv = document.querySelector('.mint-msgbox-btns');
        let confirmBtn = btnDiv.lastChild;//确认按钮
        let cancelBtn = btnDiv.firstChild;//取消按钮
        return Vue.nextTick().then(()=>{
            expect(btnDiv.children.length).toBe(2);//有两个按钮
            expect(confirmBtn.style.display).toEqual('');//预期确认按钮展示
            expect(cancelBtn.style.display).toEqual('');//预期取消按钮展示
        });
      });

      it('点击confirm弹出框中的确认', (done) => {
        MessageBox.confirm('确认删除吗？').then( action => {
            expect(action).toEqual('confirm');//点击确认的预期
            done();
        });
        let btnDiv = document.querySelector('.mint-msgbox-btns');
        let confirmBtn = btnDiv.lastChild;//确认按钮
        return Vue.nextTick().then(()=>{
            confirmBtn.click();
            
        });
      });
      
      it('点击confirm弹出框中的取消', (done) => {
        MessageBox.confirm('确认删除吗？').then( action => {}).catch(action => {
            expect(action).toEqual('cancel');//点击取消的预期
            done();
        });
        let btnDiv = document.querySelector('.mint-msgbox-btns');
        let cancelBtn = btnDiv.firstChild;//取消按钮
        return Vue.nextTick().then(()=>{
            cancelBtn.click();
        });
      });
    // 弹出输入框测试用例待补充
    //弹出框内容为html用例待补充
})