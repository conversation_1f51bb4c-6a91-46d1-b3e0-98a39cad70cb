import Vue from 'vue';

import Clickoutside from '@/base/clickoutside';
import TelnumIpt from '@/components/yupeihao/TelnumIpt.vue'
import { mount} from '@vue/test-utils'
Vue.directive('clickoutside',Clickoutside);


describe('TelnumIpt.vue', () => {
    let wrapper,vm;
    beforeEach(() => {
        //先获取包裹器
        wrapper = mount(TelnumIpt, {
            propsData: {
                value:'123',
                active: false
            }
        });
    });

    it('初始化手机号',() => {
        vm = wrapper.vm;
        expect(vm.telnum).toEqual('123');
        expect(vm.focusIndex).toEqual(3);
        expect(vm.focusIndex).toEqual(vm.telnum.length);
        let ipt = vm.$refs.telnumDom;
        expect(vm.telnum).toEqual(ipt.value);
        expect(wrapper.props().value).toEqual(vm.telnum);
        expect(document.hasFocus()).toBe(false);//初始状态手机号没有获取焦点
        vm.focusInput(3);
        expect(document.hasFocus()).toBe(true);
        Vue.prototype.active = true;
        expect(document.hasFocus()).toBe(true);
    });
    it.only('手机号输入事件',async () => {
        wrapper = mount(TelnumIpt, {
            propsData: {
                value:'12345678',
                active: false
            }
        });
        vm = wrapper.vm;
        await vm.$nextTick()
        expect(vm.value).toEqual('12345678');//手机号最长11位
    });
});