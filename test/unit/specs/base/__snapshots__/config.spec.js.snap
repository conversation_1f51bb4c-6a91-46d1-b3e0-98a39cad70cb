// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`配置文件测试 CONSTVAL常量配置文件 1`] = `
Object {
  "BS_PAKETID": "2013000009",
  "BS_PAKETID_CP": "2013000012",
  "BUSI_TYPE_ARREARS_PAY": "arrears_pay",
  "BUSI_TYPE_CHANGECARD_PROD": "changecard_prod",
  "BUSI_TYPE_END_RECACT": "end_recact",
  "BUSI_TYPE_ENTER": "enter_net",
  "BUSI_TYPE_FACE": "oper_face_register",
  "BUSI_TYPE_FAMILY_NET_GRP_MEM": "national_family_net_maintain_member",
  "BUSI_TYPE_FAMILY_NET_GRP_SHORTNO": "family_net_grp_shortno",
  "BUSI_TYPE_FMY_KAITONG": "FAMILY_PROD_ORDER",
  "BUSI_TYPE_FMY_WEIHU": "FAMILY_MEMBER_WEIHU",
  "BUSI_TYPE_GROUP_ENTER_NET": "group_enter_net",
  "BUSI_TYPE_GROUP_USER_CHANGE": "group_user_change",
  "BUSI_TYPE_HAPPY": "worry_free_purchase",
  "BUSI_TYPE_INSTALLMENT_ORDER": "INSTALLMENT_ORDER",
  "BUSI_TYPE_IOT": "iot_enter_net",
  "BUSI_TYPE_MARKET": "TERMINA_SELL",
  "BUSI_TYPE_MARKET_PAPERLESS": "market_paperless",
  "BUSI_TYPE_MBAND": "mband_prod_kaitong",
  "BUSI_TYPE_MBAND_PROD_CHANGE": "mband_prod_change",
  "BUSI_TYPE_MBAND_PROD_CHANGE_PAPERLESS": "mband_prod_change_paperless",
  "BUSI_TYPE_NETTV_PAPERLESS": "netTv_paperless",
  "BUSI_TYPE_NETTV_SECOND_PAPERLESS": "netTv_second_paperless",
  "BUSI_TYPE_NUMBER_TRANSFER_NET": "NUMBER_TRANSFER_NET",
  "BUSI_TYPE_ORDER_ENTER_NET": "order_enter_net",
  "BUSI_TYPE_ORDER_NUMBER_TRANSFER_NET": "ORDER_NUMBER_TRANSFER_NET",
  "BUSI_TYPE_PROMOTION_CENTER": "promotion_center",
  "BUSI_TYPE_REPAIR_PHOTO": "repair_realname_photo",
  "BUSI_TYPE_RONGHE": "rong_market",
  "BUSI_TYPE_SCHOOL_BOOKING": "school_booking",
  "BUSI_TYPE_SMARTNET_MRKTCASE": "smartnet_mrktcase",
  "BUSI_TYPE_STORE_TEL_RECHARGE": "store_tel_recharge",
  "BUSI_TYPE_TEL_RECHARGE": "tel_recharge",
  "BUSI_TYPE_TV_NETTV": "nettv_prod_kaitong",
  "BUSI_TYPE_TV_NETTV_SNYGYH": "nettv_snygyh_prod_kaitong",
  "BUSI_TYPE_TV_NETTV_SNYGYH_TWO": "nettv_snygyh_prod_kaitong_two",
  "BUSI_TYPE_TV_NETTV_TWO": "nettv_prod_kaitong_two",
  "BUSI_TYPE_TV_NETTV__SNYS": "nettv_snys_prod_kaitong",
  "BUSI_TYPE_TV_NETTV__SNYS_TWO": "nettv_snys_prod_kaitong_two",
  "BUSI_TYPE_XQKD": "creatembandSubs",
  "BUSI_TYPE_ZDMARKET": "TERMINAL_MARKET",
  "BUSI_TYPE_ZHENGZHI_PAPERLESS": "zhengzhi_paperless",
  "BUSI_TYPE_ZHENGZHI_PROD": "zengzhi_prod",
  "BUSI_TYPE_ZHUTI_PROD": "zhuti_prod",
  "BUSU_TYPE_CALYX": "Calyx",
  "BUSU_TYPE_CREDIT_LOAN": "credit_loan",
  "BUSU_TYPE_KM_KAITONG": "FAMILY_KEY_ORDER",
  "BUSU_TYPE_KM_WEIHU": "KEY_MEMBER_WEIHU",
  "BUSU_TYPE_SHARE_MORE": "share_more",
  "BUSU_TYPE_WANNENE_FUKA": "wanneng_prod",
  "FLAG_SIMCARD": "simcard",
  "PRIV_ID_BAND": "10000012",
  "PRIV_ID_CREDIT": "10000003",
  "PRIV_ID_ENTER_NET": "10000007",
  "PRIV_ID_MAINPRODCHANGE": "10000005",
  "PRIV_ID_NUMBER": "10000004",
  "PRIV_ID_PSEQ_RELIEVE": "10000021",
  "PRIV_ID_VALUEADD": "10000026",
  "PRIV_ID_YUPEIHAO": "10000008",
  "STATION_10086": "9988022513355102",
  "STATION_BLD": "9988020224955046",
  "STATION_DSQUDAO": "1811489",
  "STATION_DSSYS": "1811493",
  "STATION_HEZUO": "1811486",
  "STATION_HUWAI": "9988021979165266",
  "STATION_JIATING": "9988019488290574",
  "STATION_JKSX": "9988022838662806",
  "STATION_KHJL": "9988020036385338",
  "STATION_QUDAO": "1488018962830350",
  "STATION_SCHOOL": "9988021245683406",
  "STATION_SHEQU": "9988021754066058",
  "STATION_SUDI": "9988021979185150",
  "STATION_ZHICHENG": "1811480",
  "STATION_ZHUT": "1811500",
  "STATION_ZWSX": "9988022838662806",
  "TV_PAKETID": "2013000004",
  "TV_PAKETID_CP": "2013000010",
  "YS_PAKETID": "2013000008",
  "YS_PAKETID_CP": "2013000011",
}
`;

exports[`配置文件测试 LONG_CITY_LIST地市常量配置文件: [object Object] 1`] = `
Array [
  Object {
    "id": "1000250",
    "label": "南京",
    "shortId": "14",
  },
  Object {
    "id": "1000510",
    "label": "无锡",
    "shortId": "19",
  },
  Object {
    "id": "1000511",
    "label": "镇江",
    "shortId": "18",
  },
  Object {
    "id": "1000512",
    "label": "苏州",
    "shortId": "11",
  },
  Object {
    "id": "1000513",
    "label": "南通",
    "shortId": "20",
  },
  Object {
    "id": "1000514",
    "label": "扬州",
    "shortId": "23",
  },
  Object {
    "id": "1000515",
    "label": "盐城",
    "shortId": "22",
  },
  Object {
    "id": "1000516",
    "label": "徐州",
    "shortId": "16",
  },
  Object {
    "id": "1000517",
    "label": "淮安",
    "shortId": "12",
  },
  Object {
    "id": "1000518",
    "label": "连云港",
    "shortId": "15",
  },
  Object {
    "id": "1000519",
    "label": "常州",
    "shortId": "17",
  },
  Object {
    "id": "1000523",
    "label": "泰州",
    "shortId": "21",
  },
  Object {
    "id": "1000527",
    "label": "宿迁",
    "shortId": "13",
  },
]
`;

exports[`配置文件测试 log配置文件 1`] = `
Object {
  "CsView": false,
  "PlanDetail": false,
  "WorkStage": false,
  "YuPeiHao": false,
}
`;
