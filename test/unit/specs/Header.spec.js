import Header from '@/components/common/Header.vue'
import { mount} from '@vue/test-utils'

describe('Header.vue', () => {
    let wrapper,vm;
    beforeEach(() => {
    });

    test('初始化头部组件',() => {
        wrapper = mount(Header, {
            propsData: {
                tsTitleTxt:'我是页面公用脑袋'
            }
        });
        vm = wrapper.vm;
        expect(wrapper.props().tsTitleTxt).toEqual(vm.tsTitleTxt);
        
    });

    it('返回事件',() => {
        wrapper = mount(Header, {
            propsData: {
                tsTitleTxt:'我是页面公用脑袋'
            }
        });
        vm = wrapper.vm;
        wrapper.find('.gl-back').trigger('click') 
        expect(wrapper.emitted('emGoPrev')).toBeUndefined();
    });

    it('自定义返回事件',() => {
        wrapper = mount(Header, {
            propsData: {
                tsTitleTxt:'我是页面公用脑袋',
                backType:'custom'
            }
        });
        vm = wrapper.vm;
        wrapper.find('.gl-back').trigger('click');
        //自定义emit事件触发
        expect(wrapper.emitted('emGoPrev')).toHaveLength(1)
    });
    
});