import NlDropdown from '@/components/common/NlDropdown/dropdown.js'
import Vue from 'vue';

describe('下拉框组件NlDropdown',()=>{
    let curInstance;
    beforeEach(()=>{
        //每个单元测试用例跑完关闭对话框
        curInstance && curInstance.close();
    })
    const nlList = [{id: 'apple',label: '苹果'},{id: 'banana',label: '香蕉'}];
    it('初始化下拉框空数据',()=>{
        let checkItem = {};
        curInstance = NlDropdown({
            confirmBtn: true,
            datalist: []
        }, (retVal) => {
            checkItem = retVal;
        })
        let boxDom = document.querySelector('.chooose-component');
        const titleWrap = document.querySelector('.choose-title-wrap');//头部dom
        expect(titleWrap.querySelector('.choose-btn-cancel').textContent).toEqual('取消');//有取消按钮
        const ulWrap = boxDom.querySelector('.choose-content-ul');//下拉列表UL
        return Vue.nextTick().then(()=>{
            expect(titleWrap.querySelector('.choose-btn-confirm').textContent).toEqual('确定');//confirmBtn为false 不展示确认
            expect(ulWrap.children.length).toBe(0);
        })
    })
    it('初始化下拉框两条数据',()=>{
        let checkItem = {};
        curInstance = NlDropdown({
            confirmBtn: false,
            datalist: nlList
        }, (retVal) => {
            checkItem = retVal;
        })
        let boxDom = document.querySelector('.chooose-component');
        const ulWrap = boxDom.querySelector('.choose-content-ul');//下拉列表UL
        return Vue.nextTick().then(()=>{
            expect(ulWrap.children.length).toBe(nlList.length);
            let firstLi = ulWrap.firstChild;
            let lastLi = ulWrap.lastChild;
            expect(firstLi.textContent).toEqual(nlList[0].label);//展示的列表项是数据列表的label
            expect(lastLi.textContent).toEqual(nlList[nlList.length-1].label);
        })
    })
    it('下拉框点击事件',()=>{
        let cbFn = jest.fn();
        curInstance = NlDropdown({
            confirmBtn: false,
            datalist: nlList
        }, cbFn)
        let boxDom = document.querySelector('.chooose-component');
        const ulWrap = boxDom.querySelector('.choose-content-ul');//下拉列表UL
        return Vue.nextTick().then(()=>{
            let firstLi = ulWrap.firstChild;
            firstLi && firstLi.click();
            expect(cbFn).toBeCalled();
        })
    })

})