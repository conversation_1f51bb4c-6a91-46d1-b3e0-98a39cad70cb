import NoDataPage from '@/components/common/NoDataPage.vue'
import { mount} from '@vue/test-utils'

describe('暂无数据组件 NoDataPage 相关',() => {
    let wrapper;
    beforeEach(()=>{
        //先获取包裹器
        wrapper = mount(NoDataPage, {});
    })

    test('初始化没有传参数的情况', ()=>{
        let vm = wrapper.vm;
        return wrapper.vm.$nextTick().then(() => {            
            expect(vm.tipTxt).toEqual('暂无数据')
            expect(wrapper.find('.nodata-page').find('p').text()).toEqual('暂无数据');//默认参数
            expect(wrapper.find('img').attributes().src).toEqual('static/img/emptybox.png');//默认图片
        })
    })
    test('初始化传参数的情况', ()=>{
        wrapper.setProps({tipTxt:'请更换搜索条件试试',imgPath:'static/img/emptybox2.png'})
        let vm = wrapper.vm;
        return wrapper.vm.$nextTick().then(() => {            
            expect(vm.tipTxt).toEqual('请更换搜索条件试试')
            expect(wrapper.find('.nodata-page').find('p').text()).toEqual('请更换搜索条件试试')
            expect(wrapper.find('img').attributes().src).toEqual('static/img/emptybox2.png');//传参中的图片
        })
    })

})

