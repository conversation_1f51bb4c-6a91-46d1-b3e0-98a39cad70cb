import BandwidthPay from '@/components/business/BandTvMarket/BandwidthPay.vue'
import { mount} from '@vue/test-utils'

describe('支付组件相关',() => {
    let wrapper;
    
    describe('三种支付方式的情况',()=>{
        beforeEach(()=>{
            //先获取包裹器
            wrapper = mount(BandwidthPay, {
                propsData: {
                    tsBusiType:'mband_prod_kaitong',
                    isDevicefee:'1'
                }
            });
        })
        test('初始化有三种支付方式的情况',()=>{
            let vm = wrapper.vm;
            return wrapper.vm.$nextTick().then( () => {
                expect(vm.payTypeList.length).toBe(3);
                expect(wrapper.find('.bandwidthPay').findAll('.radio').length).toBe(3)
            })
        });
        test('测试点击支付方式', async()=>{
            let vm = wrapper.vm;
            await wrapper.vm.$nextTick()
            let radioDoms = wrapper.find('.bandwidthPay').findAll('.radio');
            radioDoms.at(0).trigger('click')
            expect(wrapper.emitted('esClick')).toBeTruthy()
            let payItem = vm.payTypeList[0];
            expect(radioDoms.at(0).text()).toEqual(vm.payMap[payItem.payType].txt);
            expect(vm.curPayType+'').toEqual(vm.payMap[payItem.payType].payType);
        });
    })
    
    test('初始化有两种支付方式的情况',()=>{
        //先获取包裹器
        wrapper = mount(BandwidthPay, {
            propsData: {
                tsBusiType:'OLE_MBAND',
                isDevicefee:'1'
            }
        });
        let vm = wrapper.vm;
        return wrapper.vm.$nextTick().then( () => {  
            expect(vm.payTypeList.length).toBe(2)     
            expect(wrapper.find('.bandwidthPay').findAll('.radio').length).toBe(2)
        })
    })
    test('初始化有一种非现金支付方式1052的情况',()=>{
        //先获取包裹器
        wrapper = mount(BandwidthPay, {
            propsData: {
                tsBusiType:'TERMINA_SELL',
                isDevicefee:'1'
            }
        });
        let vm = wrapper.vm;
        return wrapper.vm.$nextTick().then(() => {            
            expect(vm.payTypeList.length).toBe(1)
            expect(wrapper.find('.bandwidthPay').findAll('.radio').length).toBe(1)
            let payItem = vm.payTypeList[0];            
            expect(vm.curPayType+'').toEqual(vm.payMap[payItem.payType].payType);
            expect(wrapper.emitted('esClick')).toBeTruthy()
        })
    })
    test('初始化有一种非现金支付方式1025的情况', ()=>{
        //先获取包裹器
        wrapper = mount(BandwidthPay, {
            propsData: {
                tsBusiType:'TERMINAL_MARKET',
                isDevicefee:'1'
            }
        });
        let vm = wrapper.vm;
        return wrapper.vm.$nextTick().then(() => {            
            expect(vm.payTypeList.length).toBe(1)
            expect(wrapper.find('.bandwidthPay').findAll('.radio').length).toBe(1)
        })
    })
    test('初始化没有支付方式的情况，默认有现金支付的情况',()=>{
        //先获取包裹器
        wrapper = mount(BandwidthPay, {
            propsData: {
                tsBusiType:'zengzhi_prod',
                isDevicefee:'1'
            }
        });
        let vm = wrapper.vm;
        return wrapper.vm.$nextTick().then(() => {            
            expect(vm.payTypeList.length).toBe(1)
            expect(wrapper.find('.bandwidthPay').findAll('.radio').length).toBe(1)
        })
    })

})

