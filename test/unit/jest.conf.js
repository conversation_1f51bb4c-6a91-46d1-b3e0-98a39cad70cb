const path = require('path')
// Object.defineProperty(global.window.document.documentElement, "style", {
//   writable: false, 
//   WebkitAppearance: "webkit"
// });
module.exports = {
  //以下两行解决 localStorage is not available for opaque origins
  verbose: true,
  testURL: "http://localhost/",

  rootDir: path.resolve(__dirname, '../../'),
  moduleFileExtensions: [
    'js',
    'json',
    // 告诉 Jest 处理 `*.vue` 文件
    'vue'
  ],
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^components/(.*)$':'<rootDir>/src/components/common/$1'
  },
  transform: {
    '^.+\\.js$': '<rootDir>/node_modules/babel-jest',
    // 用 `vue-jest` 处理 `*.vue` 文件
    '.*\\.(vue)$': '<rootDir>/node_modules/vue-jest'
  },
  testEnvironmentOptions:{
    document:{
      documentElement:{
        style:{
          WebkitAppearance:'webkit'
        }
      }
    },
    userAgent:{

    }
  },
  snapshotSerializers: ['<rootDir>/node_modules/jest-serializer-vue'],
  setupFiles: ['<rootDir>/test/unit/setup'],
  //注释，不然会报 Option "mapCoverage" has been removed, as it's no longer necessary.
  // mapCoverage: true,
  coverageDirectory: '<rootDir>/test/unit/coverage',
  collectCoverageFrom: [
    'src/components/common/*.{js,vue}',
    '!src/main.js',
    '!src/router/index.js',
    '!**/node_modules/**'
  ]
}
