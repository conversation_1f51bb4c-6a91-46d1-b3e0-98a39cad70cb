# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Environment Setup
```bash
npm install                    # Install dependencies
```

### Development Server
```bash
npm run dev                   # Default development server (port 8080)
npm run dev-ald              # Development for xsbh5 project
npm run dev-panc             # Development for panc project  
npm run dev-pad              # Development for pad project
npm run dev-ole              # Development for ole project
npm run dev-thirds           # Development for thirds project
npm run dev-handhall         # Development for handhall project
npm run dev-amsdw            # Development for amsdw project
```

### Building
```bash
npm run build                # Build for production
npm run build-all            # Build all projects
npm run build-ald            # Build xsbh5 project specifically
npm run build-panc           # Build panc project specifically
```

### Testing and Code Quality
```bash
npm run test                 # Run unit tests with Jest
npm run unit                 # Run unit tests directly
npm run lint                 # Run ESLint on src and test files
```

## Project Architecture

This is a multi-tenant Vue.js 2.6 application with separate entry points for different products:

### Multi-Project Structure
- **xsbh5**: Main mobile application (行商宝前端)
- **panc**: Panel/management interface
- **pad**: Tablet-optimized interface  
- **ole**: Dedicated ole interface
- **thirds**: Third-party integrations
- **handhall**: Hand hall interface
- **amsdw**: AMS data warehouse interface

Each project has its own entry point in `src/pages/` with dedicated routing and components.

### Key Directories
- `src/pages/`: Entry points for different projects
- `src/router/main/`: Main routing configuration split across multiple router files
- `src/components/`: Shared Vue components organized by functionality
  - `src/components/common/`: Shared UI components across all projects
  - `src/components/business/`: Business-specific components
  - `src/components/area/`: Area/region-related components
- `src/base/`: Core utilities, mixins, axios configuration, and shared logic
  - `src/base/mixins/`: Vue mixins for shared functionality
  - `src/base/request/`: API request utilities and interceptors
  - `src/base/log/`: Logging utilities and configuration
- `src/store/`: Vuex state management with modular structure
- `src/panComponents/`: Panel-specific components for panc project
- `src/thirdComponents/`: Third-party integration components
- `config/`: Webpack and environment configuration
- `build/`: Build scripts and webpack configurations

### Routing Architecture
The main router (`src/router/main/mainRouter.js`) combines multiple router modules:
- `villageRouter.js`: Village/community-related routes
- `commonRouter.js`: Common/shared routes  
- `ald_Router2019.js`, `ald_Router2020.js`, `ald_Router2021.js`, `ald_Router2023.js`, `ald_Router2025.js`: Year-based feature routes (currently using 2023 and 2025)

### Development Environment
- Uses Webpack 3.x with Vue CLI 2.x template
- Hot reload enabled for development  
- Proxy configuration for multiple API endpoints:
  - `/apiM`: Main API backend
  - `/api5g`: 5G message center services
  - `/api`: Grid services (zqgrid.it.10086.cn)
- ESLint integration for code quality
- HTTPS support available (requires SSL certificates in `build/cert/`)
- Development server runs on port 8080 with host `0.0.0.0` for network access

### Key Dependencies
- Vue 2.6.10 with Vue Router 3.0.6
- Vuex 3.1.1 for state management
- Mint UI 2.2.13 for mobile components
- Axios 0.21.1 for HTTP requests
- ECharts 4.8.0 for charts
- Better Scroll 1.15.2 for mobile scrolling
- Crypto-js 4.2.0 for encryption utilities
- HTML2Canvas 1.1.1 for screenshot functionality
- Various mobile-specific libraries (fastclick, better-scroll, etc.)

### Testing
- Jest 22.4.4 configured for unit testing
- Vue Test Utils for component testing
- Test files should be placed in `test/unit/`
- Coverage reports generated in `test/unit/coverage/`
- Focuses on testing common components in `src/components/common/`

### Build Configuration
- Separate build configurations for development, production, and test environments
- Asset optimization and minification for production builds
- Support for multiple output formats based on project type
- Different asset subdirectories for each project (e.g., `xsbh5/static`, `xsbtest/static`)
- Bundle analyzer available with `npm run build --report`

### Development Notes
- Node.js version requirement: 10.24.1 (as specified in README)
- The application uses a legacy Vue CLI 2.x setup with Webpack 3.x
- Cross-origin requests are handled through webpack-dev-server proxy configuration
- Multiple environment configurations support different backend targets
- ESLint configured to check `.js` and `.vue` files in `src` and `test/unit` directories