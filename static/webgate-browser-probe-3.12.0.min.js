!function(){"use strict";var i=function(){function i(){this.i={}}return i.prototype.subscribe=function(i,t){this.i[i]||(this.i[i]=[]),this.i[i].push(t)},i.prototype.t=function(i,t){if(this.i[i])for(var n=0,e=this.i[i];n<e.length;n++){(0,e[n])(t)}},i}(),t=function(i,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,t){i.__proto__=t}||function(i,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(i[n]=t[n])},t(i,n)};function n(i,n){function e(){this.constructor=i}t(i,n),i.prototype=null===n?Object.create(n):(e.prototype=n.prototype,new e)}var e=function(){return e=Object.assign||function(i){for(var t,n=1,e=arguments.length;n<e;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(i[r]=t[r]);return i},e.apply(this,arguments)};function r(){for(var i=0,t=0,n=arguments.length;t<n;t++)i+=arguments[t].length;var e=Array(i),r=0;for(t=0;t<n;t++)for(var o=arguments[t],a=0,s=o.length;a<s;a++,r++)e[r]=o[a];return e}var o="webgate-browser-probe",a="3.12.0",s="__WGBP_device_id__",u="__WGBP_session_id__",c="__WGBP_transactions__",d="traceparent",f="tracestate",w="server-timing",h="WG_END_ELAPSE",v="userId",l="WGBP_STACK_CAPTURE",p="unknown",b="wg-browser-probe",_="trace",m="removeEventListener",g="attachEvent",y="detachEvent",k="/collector/browser/api/v2",S=k+"/config",T=k+"/report",x=k+"/data-track",E="error",R="unhandledrejection",q="error",I="history",O="fetch",C="xhr",M="resource",P="operation",B="page-load",L="router-change",z=["HISTORY","ERROR","REQUEST","RESOURCE","OPERATION"],A="OK",j="NETWORK_ERROR",H="TIMEOUT",N="ABORT",U="GET",W="navigation",G="xmlhttprequest",V="fetch",D="beacon",F={o:"navigationStart",u:"unloadEventStart",h:"unloadEventEnd",v:"redirectStart",l:"redirectEnd",p:"fetchStart",_:"domainLookupStart",m:"domainLookupEnd",g:"connectStart",k:"secureConnectionStart",S:"connectEnd",T:"requestStart",R:"responseStart",q:"responseEnd",I:"domInteractive",O:"domContentLoadedEventStart",C:"domContentLoadedEventEnd",M:"domComplete",P:"loadEventStart",B:"loadEventEnd"},K={L:"redirectCount",A:"nextHopProtocol",j:"serverTiming",H:"transferSize",N:"encodedBodySize",U:"decodedBodySize"},X={g:"connectStart",k:"secureConnectionStart",S:"connectEnd",_:"domainLookupStart",m:"domainLookupEnd",v:"redirectStart",l:"redirectEnd",p:"fetchStart",T:"requestStart",R:"responseStart",q:"responseEnd",W:"workerStart"},$={G:"initiatorType",V:"name",A:"nextHopProtocol",j:"serverTiming",H:"transferSize",N:"encodedBodySize",U:"decodedBodySize"},Q="PC",Y="Mobile",Z="Tablet",J="Cookie",ii="Document",ti="LocalStorage",ni="SessionStorage",ei="RequestHeader",ri="RequestParam",oi="RequestBody",ai="ResponseHeader",si="ResponseBody",ui="PageUrlParam",ci="PageUrlHashParam",di="complete",fi="click",wi="change",hi="readystatechange",vi="timeout",li="abort",pi="loadend",bi=function(){},_i=function(){};function mi(i){return i?i.replace(/^\s+|\s+$/g,""):i}function gi(i,t){for(var n=0;n<i.length;n++)if(i[n]===t)return n;return-1}"addEventListener"in window?bi=window.addEventListener:g in window&&(bi=window.attachEvent),m in window?_i=window.removeEventListener:y in window&&(_i=window.detachEvent);var yi={parse:function(i,t){},stringify:function(i,t,n){return""}};function ki(){return(new Date).getTime()}"JSON"in window&&"parse"in window.JSON&&"stringify"in window.JSON&&(yi.parse=window.JSON.parse,yi.stringify=window.JSON.stringify);var Si="now"in window.performance&&"function"==typeof window.performance.now;function Ti(){return Si?Math.floor(window.performance.now()):ki()-window.performance.timing.navigationStart}var xi=function(i,t){if(void 0===t&&(t=!0),"console"in window){if("info"in window.console&&"function"==typeof window.console.info)return void window.console.info(t?"[WebGateBrowser info]: ":"",i);"log"in window.console&&"function"==typeof window.console.log&&window.console.log(t?"[WebGateBrowser info]: ":"",i)}},Ei=function(i,t){if(void 0===t&&(t=!0),"console"in window){if("warn"in window.console&&"function"==typeof window.console.warn)return void window.console.warn(t?"[WebGateBrowser warn]: ":"",i);"log"in window.console&&"function"==typeof window.console.log&&window.console.log(t?"[WebGateBrowser warn]: ":"",i)}},Ri=function(i,t){if(void 0===t&&(t=!0),"console"in window){if("error"in window.console&&"function"==typeof window.console.error)return void window.console.error(t?"[WebGateBrowser error]: ":"",i);"log"in window.console&&"function"==typeof window.console.log&&window.console.log(t?"[WebGateBrowser error]: ":"",i)}},qi=function(i,t){void 0===t&&(t=!0),"console"in window&&"log"in window.console&&"function"==typeof window.console.log&&window.console.log(t?"[WebGateBrowser success]: ":"",i)},Ii=function(i){try{var t=document.cookie.match(new RegExp("(^| )"+i+"=([^;]*)(;|$)"));if(t&&t.length>=3)try{return decodeURIComponent(t[2])}catch(i){return Ri(i),t[2]}}catch(i){Ri(i)}};function Oi(i){return!i||!mi(i)}function Ci(i){return!Oi(i)}function Mi(i,t,n){return void 0===t&&(t=50),void 0===n&&(n="..."),t<4&&(t=4),i.length>t?i.substr(0,t)+n:i}var Pi=function(i){if(!i)return p;try{var t=i;if("string"==typeof i&&"querySelector"in document&&(t=document.querySelector(i)),t&&t instanceof HTMLElement){var n=t.innerText||t.textContent;if(!n&&t instanceof HTMLInputElement&&(n=t.value),n)return Mi(n.trim())}}catch(i){Ri(i)}return p},Bi=function(i){if(""!==i.id)return'//*[@id="'+i.id+'"]';if(i===document.body)return"/HTML/BODY";for(var t=0,n=i.parentNode.childNodes,e=0;e<n.length;e++){var r=n[e];if(r===i)return Bi(i.parentNode)+"/"+i.tagName+"["+(t+1)+"]";1===r.nodeType&&r.tagName===i.tagName&&t++}return""},Li=function(){function i(i){var t=this;this.transactions={},this.sample=!1,this.D=i,this.F=c+this.D.X.K,this.$(),document.readyState===di?this.Y():bi("load",(function(){return t.Y()}))}return i.Z=function(t){return this.J||(this.J=new i(t)),this.J},i.prototype.ii=function(){return this.transactions},i.prototype.clone=function(){return e({},this.transactions)},i.prototype.ti=function(i,t,n){void 0===n&&(n=!0),this.transactions[i]=t,this.ni(),n&&this.ei()},i.prototype.ri=function(i,t){delete this.transactions[i],this.ni(),this.ei()},i.prototype.oi=function(){return this.sample},i.prototype.ni=function(){var i=this.D.X.ai;if(i&&0!==i.length){var t=this.transactions[v];this.sample=!!t&&gi(i,t)>=0}else this.sample=!1},i.prototype.$=function(){var i,t=sessionStorage.getItem(this.F);if(t)try{i=yi.parse(t)}catch(i){sessionStorage.removeItem(this.F)}if(i)for(var n in i)this.ti(n,i[n],!1)},i.prototype.ei=function(){sessionStorage.setItem(this.F,yi.stringify(this.transactions))},i.prototype.Y=function(){for(var i=0,t=this.D.X.si;i<t.length;i++){var n=t[i],e=void 0;switch(n.ui){case J:e=Ii(n.ci);break;case ii:e=Pi(n.ci);break;case ti:e=localStorage.getItem(n.ci);break;case ni:e=sessionStorage.getItem(n.ci)}Ci(e)&&this.ti(n.di,Mi(e,this.D.X.fi))}},i}();function zi(i){for(var t="";0!==i;){t=(i%16).toString(16)+t,i=Math.floor(i/16)}return t}function Ai(i,t){void 0===t&&(t=16);for(var n=i,e=0;e<t-i.length;e++)n="0"+n;return n}function ji(){return Ai("1")}function Hi(){return Ai(zi((new Date).getTime()))+Ai(zi(Math.round(0x8000000000000000*Math.random())))}function Ni(i,t,n){return void 0===n&&(n="0"),"00-"+i+"-"+t+"-"+Ai(parseInt(n,2).toString(16),2)}var Ui="function",Wi="undefined",Gi="object",Vi="string",Di="model",Fi="name",Ki="type",Xi="vendor",$i="version",Qi="architecture",Yi="console",Zi="Mobile",Ji="Tablet",it="smarttv",tt="wearable",nt="embedded",et="Amazon",rt="Apple",ot="ASUS",at="BlackBerry",st="Firefox",ut="Google",ct="Huawei",dt="LG",ft="Microsoft",wt="Motorola",ht="Opera",vt="Samsung",lt="Sony",pt="Xiaomi",bt="Zebra",_t="Facebook",mt=function(i){for(var t={},n=0;n<i.length;n++)t[i[n].toUpperCase()]=i[n];return t},gt=function(i,t){return typeof i===Vi&&-1!==yt(t).indexOf(yt(i))},yt=function(i){return i.toLowerCase()},kt=function(i,t){if(typeof i===Vi)return i=i.replace(/^\s\s*/,"").replace(/\s\s*$/,""),typeof t===Wi?i:i.substring(0,255)},St=function(i,t){for(var n,e,r,o,a,s,u=0;u<t.length&&!a;){var c=t[u],d=t[u+1];for(n=e=0;n<c.length&&!a;)if(a=c[n++].exec(i))for(r=0;r<d.length;r++)s=a[++e],typeof(o=d[r])===Gi&&o.length>0?2===o.length?typeof o[1]==Ui?this[o[0]]=o[1].call(this,s):this[o[0]]=o[1]:3===o.length?typeof o[1]!==Ui||o[1].exec&&o[1].test?this[o[0]]=s?s.replace(o[1],o[2]):void 0:this[o[0]]=s?o[1].call(this,s,o[2]):void 0:4===o.length&&(this[o[0]]=s?o[3].call(this,s.replace(o[1],o[2])):void 0):this[o]=s||void 0;u+=2}},Tt=function(i,t){for(var n in t)if(typeof t[n]===Gi&&t[n].length>0){for(var e=0;e<t[n].length;e++)if(gt(t[n][e],i))return"?"===n?void 0:n}else if(gt(t[n],i))return"?"===n?void 0:n;return i},xt={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Et={wi:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[$i,[Fi,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[$i,[Fi,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[Fi,$i],[/opios[\/ ]+([\w\.]+)/i],[$i,[Fi,"Opera Mini"]],[/\bopr\/([\w\.]+)/i],[$i,[Fi,ht]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale|qqbrowserlite|qq)\/([-\w\.]+)/i,/(weibo)__([\d\.]+)/i],[Fi,$i],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[$i,[Fi,"UCBrowser"]],[/\bqbcore\/([\w\.]+)/i],[$i,[Fi,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[$i,[Fi,"WeChat"]],[/konqueror\/([\w\.]+)/i],[$i,[Fi,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[$i,[Fi,"IE"]],[/yabrowser\/([\w\.]+)/i],[$i,[Fi,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[Fi,/(.+)/,"$1 Secure Browser"],$i],[/\bfocus\/([\w\.]+)/i],[$i,[Fi,"Firefox Focus"]],[/\bopt\/([\w\.]+)/i],[$i,[Fi,"Opera Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[$i,[Fi,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[$i,[Fi,"Dolphin"]],[/coast\/([\w\.]+)/i],[$i,[Fi,"Opera Coast"]],[/miuibrowser\/([\w\.]+)/i],[$i,[Fi,"MIUI Browser"]],[/fxios\/([-\w\.]+)/i],[$i,[Fi,st]],[/\bqihu|(qi?ho?o?|360)browser/i],[[Fi,"360 Browser"]],[/(oculus|samsung|sailfish)browser\/([\w\.]+)/i],[[Fi,/(.+)/,"$1 Browser"],$i],[/(comodo_dragon)\/([\w\.]+)/i],[[Fi,/_/g," "],$i],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[Fi,$i],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i],[Fi],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[Fi,_t],$i],[/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[Fi,$i],[/\bgsa\/([\w\.]+) .*safari\//i],[$i,[Fi,"GSA"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[$i,[Fi,"Chrome Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[Fi,"Chrome WebView"],$i],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[$i,[Fi,"Android Browser"]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[Fi,$i],[/version\/([\w\.]+) .*mobile\/\w+ (safari)/i],[$i,[Fi,"Mobile Safari"]],[/version\/([\w\.]+) .*(mobile ?safari|safari)/i],[$i,Fi],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[Fi,[$i,Tt,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[Fi,$i],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[Fi,"Netscape"],$i],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[$i,[Fi,"Firefox Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i],[Fi,$i]],hi:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[Qi,"amd64"]],[/(ia32(?=;))/i],[[Qi,yt]],[/((?:i[346]|x)86)[;\)]/i],[[Qi,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[Qi,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[Qi,"armhf"]],[/windows (ce|mobile); ppc;/i],[[Qi,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[Qi,/ower/,"",yt]],[/(sun4\w)[;\)]/i],[[Qi,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[Qi,yt]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[pt]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[Di,[Xi,vt],[Ki,Ji]],[/\b((?:s[cgp]h|gt|sm)-\w+|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[Di,[Xi,vt],[Ki,Zi]],[/\((ip(?:hone|od)[\w ]*);/i],[Di,[Xi,rt],[Ki,Zi]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[Di,[Xi,rt],[Ki,Ji]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[Di,[Xi,ct],[Ki,Ji]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}-[atu]?[ln][01259x][012359][an]?)\b(?!.+d\/s)/i],[Di,[Xi,ct],[Ki,Zi]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[Di,/_/g," "],[Xi,pt],[Ki,Zi]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[Di,/_/g," "],[Xi,pt],[Ki,Ji]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[Di,[Xi,"OPPO"],[Ki,Zi]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[Di,[Xi,"Vivo"],[Ki,Zi]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[Di,[Xi,"Realme"],[Ki,Zi]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[Di,[Xi,wt],[Ki,Zi]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[Di,[Xi,wt],[Ki,Ji]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[Di,[Xi,dt],[Ki,Ji]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[Di,[Xi,dt],[Ki,Zi]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[Di,[Xi,"Lenovo"],[Ki,Ji]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[Di,/_/g," "],[Xi,"Nokia"],[Ki,Zi]],[/(pixel c)\b/i],[Di,[Xi,ut],[Ki,Ji]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[Di,[Xi,ut],[Ki,Zi]],[/droid.+ ([c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[Di,[Xi,lt],[Ki,Zi]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[Di,"Xperia Tablet"],[Xi,lt],[Ki,Ji]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[Di,[Xi,"OnePlus"],[Ki,Zi]],[/(alexa)webm/i,/(kf[a-z]{2}wi)( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[Di,[Xi,et],[Ki,Ji]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[Di,/(.+)/g,"Fire Phone $1"],[Xi,et],[Ki,Zi]],[/(playbook);[-\w\),; ]+(rim)/i],[Di,Xi,[Ki,Ji]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[Di,[Xi,at],[Ki,Zi]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[Di,[Xi,ot],[Ki,Ji]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[Di,[Xi,ot],[Ki,Zi]],[/(nexus 9)/i],[Di,[Xi,"HTC"],[Ki,Ji]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic|sony)[-_ ]?([-\w]*)/i],[Xi,[Di,/_/g," "],[Ki,Zi]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[Di,[Xi,"Acer"],[Ki,Ji]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[Di,[Xi,"Meizu"],[Ki,Zi]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[Di,[Xi,"Sharp"],[Ki,Zi]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[Xi,Di,[Ki,Zi]],[/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[Xi,Di,[Ki,Ji]],[/(surface duo)/i],[Di,[Xi,ft],[Ki,Ji]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[Di,[Xi,"Fairphone"],[Ki,Zi]],[/(u304aa)/i],[Di,[Xi,"AT&T"],[Ki,Zi]],[/\bsie-(\w*)/i],[Di,[Xi,"Siemens"],[Ki,Zi]],[/\b(rct\w+) b/i],[Di,[Xi,"RCA"],[Ki,Ji]],[/\b(venue[\d ]{2,7}) b/i],[Di,[Xi,"Dell"],[Ki,Ji]],[/\b(q(?:mv|ta)\w+) b/i],[Di,[Xi,"Verizon"],[Ki,Ji]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[Di,[Xi,"Barnes & Noble"],[Ki,Ji]],[/\b(tm\d{3}\w+) b/i],[Di,[Xi,"NuVision"],[Ki,Ji]],[/\b(k88) b/i],[Di,[Xi,"ZTE"],[Ki,Ji]],[/\b(nx\d{3}j) b/i],[Di,[Xi,"ZTE"],[Ki,Zi]],[/\b(gen\d{3}) b.+49h/i],[Di,[Xi,"Swiss"],[Ki,Zi]],[/\b(zur\d{3}) b/i],[Di,[Xi,"Swiss"],[Ki,Ji]],[/\b((zeki)?tb.*\b) b/i],[Di,[Xi,"Zeki"],[Ki,Ji]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[Xi,"Dragon Touch"],Di,[Ki,Ji]],[/\b(ns-?\w{0,9}) b/i],[Di,[Xi,"Insignia"],[Ki,Ji]],[/\b((nxa|next)-?\w{0,9}) b/i],[Di,[Xi,"NextBook"],[Ki,Ji]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[Xi,"Voice"],Di,[Ki,Zi]],[/\b(lvtel\-)?(v1[12]) b/i],[[Xi,"LvTel"],Di,[Ki,Zi]],[/\b(ph-1) /i],[Di,[Xi,"Essential"],[Ki,Zi]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[Di,[Xi,"Envizen"],[Ki,Ji]],[/\b(trio[-\w\. ]+) b/i],[Di,[Xi,"MachSpeed"],[Ki,Ji]],[/\btu_(1491) b/i],[Di,[Xi,"Rotor"],[Ki,Ji]],[/(shield[\w ]+) b/i],[Di,[Xi,"Nvidia"],[Ki,Ji]],[/(sprint) (\w+)/i],[Xi,Di,[Ki,Zi]],[/(kin\.[onetw]{3})/i],[[Di,/\./g," "],[Xi,ft],[Ki,Zi]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[Di,[Xi,bt],[Ki,Ji]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[Di,[Xi,bt],[Ki,Zi]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[Xi,Di,[Ki,Yi]],[/droid.+; (shield) bui/i],[Di,[Xi,"Nvidia"],[Ki,Yi]],[/(playstation [345portablevi]+)/i],[Di,[Xi,lt],[Ki,Yi]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[Di,[Xi,ft],[Ki,Yi]],[/smart-tv.+(samsung)/i],[Xi,[Ki,it]],[/hbbtv.+maple;(\d+)/i],[[Di,/^/,"SmartTV"],[Xi,vt],[Ki,it]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[Xi,dt],[Ki,it]],[/(apple) ?tv/i],[Xi,[Di,"Apple TV"],[Ki,it]],[/crkey/i],[[Di,"Chromecast"],[Xi,ut],[Ki,it]],[/droid.+aft(\w)( bui|\))/i],[Di,[Xi,et],[Ki,it]],[/\(dtv[\);].+(aquos)/i],[Di,[Xi,"Sharp"],[Ki,it]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w ]*; *(\w[^;]*);([^;]*)/i],[[Xi,kt],[Di,kt],[Ki,it]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[Ki,it]],[/((pebble))app/i],[Xi,Di,[Ki,tt]],[/droid.+; (glass) \d/i],[Di,[Xi,ut],[Ki,tt]],[/droid.+; (wt63?0{2,3})\)/i],[Di,[Xi,bt],[Ki,tt]],[/(quest( 2)?)/i],[Di,[Xi,_t],[Ki,tt]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[Xi,[Ki,nt]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[Di,[Ki,Zi]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[Di,[Ki,Ji]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[Ki,Ji]],[/(phone|mobile(?:[;\/]| safari)|pda(?=.+windows ce))/i],[[Ki,Zi]],[/(android[-\w\. ]{0,9});.+buil/i],[Di,[Xi,"Generic"]]],vi:[[/windows.+ edge\/([\w\.]+)/i],[$i,[Fi,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[$i,[Fi,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i],[Fi,$i],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[$i,Fi]],li:[[/microsoft (windows) (vista|xp)/i],[Fi,$i],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[Fi,[$i,Tt,xt]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[Fi,"Windows"],[$i,Tt,xt]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/cfnetwork\/.+darwin/i],[[$i,/_/g,"."],[Fi,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[Fi,"Mac OS"],[$i,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86)/i],[$i,Fi],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[Fi,$i],[/\(bb(10);/i],[$i,[Fi,at]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[$i,[Fi,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[$i,[Fi,"Firefox OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[$i,[Fi,"webOS"]],[/crkey\/([\d\.]+)/i],[$i,[Fi,"Chromecast"]],[/(cros) [\w]+ ([\w\.]+\w)/i],[[Fi,"Chromium OS"],$i],[/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[Fi,$i],[/(sunos) ?([\w\.\d]*)/i],[[Fi,"Solaris"],$i],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux)/i,/(unix) ?([\w\.]*)/i],[Fi,$i]]},Rt=function(i,t){if(typeof i===Gi&&(t=i,i=void 0),!(this instanceof Rt))return new Rt(i,t).pi();var n=i||(typeof window!==Wi&&window.navigator&&window.navigator.userAgent?window.navigator.userAgent:""),e=t?function(i,t){var n={};for(var e in i)t[e]&&t[e].length%2==0?n[e]=t[e].concat(i[e]):n[e]=i[e];return n}(Et,t):Et;return this.bi=function(){var i={name:void 0,version:void 0};return St.call(i,n,e.wi),i._i=function(i){return typeof i===Vi?i.replace(/[^\d\.]/g,"").split(".")[0]:void 0}(i.version),i},this.mi=function(){var i={gi:void 0};return St.call(i,n,e.hi),i},this.yi=function(){var i={vendor:void 0,ki:void 0,type:void 0};return St.call(i,n,e.device),i},this.Si=function(){var i={name:void 0,version:void 0};return St.call(i,n,e.vi),i},this.Ti=function(){var i={name:void 0,version:void 0};return St.call(i,n,e.li),i},this.pi=function(){return{xi:this.Ei(),wi:this.bi(),vi:this.Si(),li:this.Ti(),device:this.yi(),hi:this.mi()}},this.Ei=function(){return n},this.Ri=function(i){return n=typeof i===Vi&&i.length>255?kt(i,255):i,this},this.Ri(n),this};Rt.VERSION="1.0.2",Rt.qi=mt([Fi,$i,"major"]),Rt.Ii=mt([Qi]),Rt.Oi=mt([Di,Xi,Ki,Yi,Zi,it,Ji,tt,nt]),Rt.Ci=Rt.Mi=mt([Fi,$i]);var qt,It=function(){function i(i){this.metadata=function(i){var t=window.localStorage.getItem(s),n=window.sessionStorage.getItem(u);t||(t=Hi(),window.localStorage.setItem(s,t)),n||(n=Hi(),window.sessionStorage.setItem(u,n));var e={wi:{name:p,version:p},li:{name:p,version:p},device:{type:p,vendor:p,ki:p},vi:{name:p}};try{e=new Rt(i).pi()}catch(i){Ri(i)}var r=(e.wi.version||p).split("."),o=r.length>1?r[0]+"."+r[1]:r[0],a={Pi:e.wi.name||p,Bi:o,Li:e.wi.version||p,zi:e.li.name||p,Ai:e.li.version||p,ji:!e.device.type||e.device.type!==Y&&e.device.type!==Z?Q:e.device.type,Hi:e.device.vendor||p,Ni:e.device.ki||p,Ui:e.vi.name||p,Wi:window.navigator.browserLanguage||window.navigator.language||p,Gi:p,Vi:p,Di:i,Fi:t,Ki:n,K:"",Xi:"",$i:""},c=window.devicePixelRatio||1,d=window.screen.width,f=window.screen.height;return a.Vi=a.ji===Q?d.toFixed(0)+"*"+f.toFixed(0):(d*c).toFixed(0)+"*"+(f*c).toFixed(0),a}(window.navigator.userAgent),this.D=i}return i.Qi=function(t){return this.J||(this.J=new i(t)),this.J},i.prototype.getMetadata=function(){var i=window.navigator.connection;i&&i.effectiveType&&(this.metadata.Gi=i.effectiveType);var t=this.D.X,n=t.K,r=t.Xi;return this.metadata=e(e({},this.metadata),{K:n,Xi:r,$i:a}),this.metadata},i}();function Ot(i){void 0===i&&(i=window.location.href);try{return qt||(qt=document.createElement("a")),qt.href=i,"http"!==i.slice(0,4)&&(qt.href=qt.href),{Yi:qt.hash,Zi:qt.host,Ji:qt.hostname,it:qt.href,tt:qt.protocol+"//"+qt.host,nt:qt.password,et:qt.pathname.replace(/^([^\/])/,"/$1")||"/",rt:qt.port,A:qt.protocol.replace(":",""),ot:qt.search,at:qt.username}}catch(i){return Ri(i),{Yi:window.location.hash,Zi:window.location.host,Ji:window.location.hostname,it:window.location.href,tt:window.location.protocol+"//"+window.location.host,nt:"",et:window.location.pathname.replace(/^([^\/])/,"/$1")||"/",rt:window.location.port,A:window.location.protocol.replace(":",""),ot:window.location.search,at:""}}}function Ct(i){return Mt(Ot(i).ot.replace("?","").trim())}function Mt(i){var t={};if(i)for(var n=0,e=i.split("&");n<e.length;n++){var r=e[n].split("="),o=r[0],a=r[1];void 0!==o&&void 0!==a&&(t[o]=void 0!==t[o]?t[o]+", "+a:a)}return t}for(var Pt,Bt=function(){function i(i){this.st={ut:void 0,ct:void 0,dt:void 0,ft:void 0,wt:void 0,ht:void 0,vt:void 0},this.lt={},this.D=i,this.bt(B)}return i._t=function(t){return this.J||(this.J=new i(t)),this.J},i.prototype.gt=function(){return this.st.ht=window.document.title,e({},this.st)},i.prototype.yt=function(){return this.lt},i.prototype.bt=function(i){void 0===i&&(i=L),this.st={ut:"",ct:"",dt:Hi(),ft:window.location.href,wt:i,ht:window.document.title,vt:document.referrer},this.lt={};try{var t=this.D.X.si.filter((function(i){return i.ui===ui})),n=this.D.X.si.filter((function(i){return i.ui===ci}));if(t.length)for(var e=Ct(this.st.ft),r=0,o=t;r<o.length;r++){Ci(c=e[(u=o[r]).ci])&&(this.lt[u.di]=Mi(c,this.D.X.fi))}if(n.length){e=(d=this.st.ft,f=Ot(d).Yi,(w=f.indexOf("?"))>0?Mt(f.substr(w+1)):{});for(var a=0,s=n;a<s.length;a++){var u,c;Ci(c=e[(u=s[a]).ci])&&(this.lt[u.di]=Mi(c,this.D.X.fi))}}}catch(i){}var d,f,w;try{var h=window.parent;h&&h!==window&&h.WGBrowserProbe&&(this.st.ct=h.WGBrowserProbe.context.pageId);var v=window.top;v&&v!==window&&v.WGBrowserProbe&&(this.st.ut=v.WGBrowserProbe.context.pageId),window.WGBrowserProbe.context.pageId=this.st.dt}catch(i){}return this.st},i}(),Lt=function(){function t(t){this.kt=new i,this.St={Tt:"",xt:!1,K:"",Xi:p,Et:"",Rt:z,qt:!1,It:!1,Ot:"webgate-",Ct:!1,ai:[],Mt:[],si:[],Pt:[],Bt:2e3,Lt:5e3,zt:5e3,fi:500},this.X=this.At(t)}return t.jt=function(i){return this.J||(this.J=new t(i),this.Ht()),this.J},t.Ht=function(){this.J.Nt=Li.Z(this.J),this.J.Ut=It.Qi(this.J),this.J.Wt=Bt._t(this.J)},t.prototype.Gt=function(i){this.X=this.At(i)},t.prototype.At=function(i){return{Tt:i.Tt||this.St.Tt,xt:i.xt||this.St.xt,K:i.K||this.St.K,Xi:i.Xi||this.St.Xi,Et:i.Et||this.St.Et,Rt:i.Rt||this.St.Rt,Mt:i.Mt||this.St.Mt,qt:i.qt||this.St.qt,ai:i.ai||this.St.ai,It:i.It||this.St.It,Ot:i.Ot||this.St.Ot,Ct:i.Ct||this.St.Ct,si:i.si||this.St.si,Pt:i.Pt||this.St.Pt,Bt:i.Bt||this.St.Bt,Lt:i.Lt||this.St.Lt,zt:i.zt||this.St.zt,fi:i.fi||this.St.fi}},t}(),zt="",At=0;At<o.length;At++)zt+=o.charCodeAt(At);function jt(i,t){for(var n=0,e="",r=0;r<i.length;r++)e+=String.fromCharCode(t?i.charCodeAt(r)+ +zt[n]:i.charCodeAt(r)-+zt[n]),++n===zt.length&&(n=0);return e}function Ht(i){return jt(i,!0)}function Nt(){return Pt}var Ut=function(){function i(){var i=this;this.D=Lt.J,this.Vt=this.D.X.Bt,this.Dt=[],this.Ft=function(t,n){return void 0===n&&(n=0),i.Kt(i.Xt,t,Hi(),n)},this.$t=function(t,n){return void 0===n&&(n=0),i.Kt(i.Qt,t,Hi(),n)},this.Kt=function(t,n,e,r){void 0===r&&(r=0);var o=i;try{var a=new XMLHttpRequest;a.__WGBP_ignore__=!0,a.open("POST",t+"?d="+e+"&t="+(new Date).getTime()+"&rt="+r,!0),a.timeout=5e3,a.onloadend=function(){4===a.readyState&&0===a.status&&r<1&&setTimeout((function(){o.Kt(t,n,e,r+1)}),o.Vt)},a.send(n)}catch(i){Ri(i)}};var t=this.D.X.Et;this.Xt=t+T,this.Qt=t+x,this.Yt()}return i.prototype.send=function(i){this.Dt.length<this.D.X.Lt&&(this.Dt.push(i),i.Zt===I&&i.Jt.wt===B&&this.tn())},i.prototype.nn=function(i){this.$t(Ht(yi.stringify(i)))},i.prototype.Yt=function(){var i,t=this,n=this;this.en=window.setInterval((function(){return t.tn()}),this.Vt),window.onbeforeunload&&(i=window.onbeforeunload),window.onbeforeunload=function(){n.tn(),i&&i()}},i.prototype.rn=function(){void 0!==this.en&&(window.clearInterval(this.en),this.en=void 0)},i.prototype.tn=function(){if(0!==this.Dt.length){var i=this.Dt.splice(0,this.Dt.length),t=function(i,t){var n,e=[];return e.push(["device",(n=i).Fi,n.Ki,n.$i,n.K,n.Xi,n.Pi,n.Bi,n.Li,n.zi,n.Ai,n.ji,n.Hi,n.Ni,n.Gi,n.Ui,n.Wi,n.Vi,n.Di]),t.forEach((function(i){var t;switch(i.Zt){case I:t=function(i){return[i.Zt,i.on,i.Jt.wt,i.Jt.dt,i.Jt.ft,i.Jt.ht,i.Jt.ut,i.Jt.ct,i.Jt.vt,i.an,i.sn,i.un,i.cn,i.dn,i.fn,i.S,i.g,i.M,i.C,i.O,i.I,i.wn,i.m,i._,i.p,i.B,i.P,i.o,i.l,i.v,i.T,i.q,i.R,i.k,i.h,i.u,i.hn,i.vn,i.ln,i.L,i.j,i.A,i.H,i.N,i.U,i.pn]}(i);break;case C:case O:t=function(i){return[i.Zt,i.on,i.Jt.dt,i.Jt.ft,i.Jt.ht,i.an,i.bn,i._n,i.sn,i.mn,i.gn,i.yn,i.kn,i.Sn,i.Tn,i.xn,i.En,i.Rn,i.qn,i.T,i.In,i.On,i.Cn,i.Mn,i.j]}(i);break;case M:t=function(i){return[i.Zt,i.on,i.Jt.dt,i.Jt.ft,i.Jt.ht,i.an,i.Pn,i.V,i.G,i.A,i.H,i.U,i.N,i.S,i.g,i.m,i._,i.p,i.l,i.v,i.T,i.q,i.R,i.k,i.W,i.j]}(i);break;case P:t=function(i){return[i.Zt,i.on,i.Jt.dt,i.Jt.ft,i.Jt.ht,i.an,i.sn,i.Bn,i.Ln,i.zn,i.An,i.jn,i.Hn,i.Nn]}(i);break;case q:t=function(i){return[i.Zt,i.on,i.Jt.dt,i.Jt.ft,i.Jt.ht,i.an,i.Un,i.Tt,i.Wn,i.Gn,i.Vn,i.Dn,i.xn,i.Fn,i.Kn]}(i);break;default:Ri("unknown metric type: "+i.Zt)}t&&e.push(t)})),yi.stringify(e)}(this.D.Ut.getMetadata(),i),n=Ht(t);this.Ft(n)}},i}(),Wt=function(i){function t(){return null!==i&&i.apply(this,arguments)||this}return n(t,i),t.prototype.Xn=function(i){if(!i)return i;var t=Lt.J,n=t.X,e=t.Nt;return!n.Ct||(null==e?void 0:e.oi())?i:void 0},t.prototype.$n=function(i){return!0},t}(function(){function i(){}return i.prototype.Qn=function(i){return this.$n(i)?this.Xn(i):i},i}()),Gt=new Array;var Vt=function(i,t,n){void 0===n&&(n=[]);try{var e=i.apply(t,n);if(!e)return;(e=function(i){for(var t=0,n=Gt;t<n.length;t++)i=n[t].Qn(i);return i}(e))&&Nt().send(e)}catch(i){Ri(i)}},Dt=function(i,t,n){var e=this;this.Yn=Lt.J.kt,this.D=Lt.J,this.Qn=function(i){var t=e;e.Zn((function(){t.Jn(t.ie,t,[i])}),t)},this.te=i,this.Zn=t,this.Jn=n,this.Yn.subscribe(this.te,this.Qn),this.ne(this.te)},Ft=function(i,t,n){void 0===n&&(n=[]),setTimeout((function(){i.apply(t,n)}),0)},Kt=function(i,t,n){void 0===n&&(n=[]),window.queueMicrotask&&"function"==typeof window.queueMicrotask?window.queueMicrotask((function(){i.apply(t,n)})):i.apply(t,n)},Xt=function(i){function t(){return i.call(this,q,Kt,Vt)||this}return n(t,i),t.ee=function(i,t){var n=t.target;if(n&&n!==window){var e=void 0;if(n.src&&"string"==typeof n.src&&0===n.src.indexOf("http")?e=n.src:n.href&&"string"==typeof n.href&&0===n.href.indexOf("http")&&(e=n.href),!e)return;return i.Wn=Ot(e).it,i.Gn="resource",i.Vn="resource",i.Dn="Resource load error.",i.Tt="Load "+i.Wn+" failed.",i}},t.re=function(i,t){var n=t.message,e=t.error,r="",o="";return e&&(r=e.name,o=e.stack),r||n&&(r=n.split(":")[0]),i.Wn=Ot(t.filename||i.Wn).it,i.Gn="javascript",i.Vn="javascript",i.Tt=n,i.Dn=r||"Script error",i.xn=o,i.Fn=t.lineno,i.Kn=t.colno,i},t.oe=function(i,t){var n="Unhandled promise rejection: ",e=t.reason;return i.Dn="Unhandled promise rejection",i.Gn="javascript",i.Vn=t.type,i.Tt=n+"<no reason specified>",null!=e&&("object"!=typeof e?i.Tt=n+e:"string"==typeof e.message&&(i.Tt=n+e.message,i.xn=e.stack?e.stack:null)),i},t.prototype.ne=function(i){var t=this,n=function(){return function(i){t.Yn.t(q,i)}};this.ae=n(),this.se=n(),bi(E,this.ae,!0),bi(R,this.se,!0)},t.prototype.ue=function(){"removeEventListener"in window&&"function"==typeof window.removeEventListener&&(this.ae&&_i(E,this.ae,!0),this.se&&_i(R,this.se,!0))},t.prototype.ie=function(i){var n,r,o=this.D.Wt.gt(),a={Zt:q,on:(new Date).getTime(),Un:Hi(),Tt:"",Wn:o.ft,Gn:"",Vn:"",Dn:"",xn:"",Fn:"-",Kn:"-",an:e(e({},null===(n=this.D.Nt)||void 0===n?void 0:n.ii()),null===(r=this.D.Wt)||void 0===r?void 0:r.yt()),Jt:o};return a="PromiseRejectionEvent"in window&&i instanceof window.PromiseRejectionEvent?t.oe(a,i):"ErrorEvent"in window&&i instanceof window.ErrorEvent?t.re(a,i):t.ee(a,i)},t}(Dt);function $t(i){return"__WGBP__"+i}function Qt(i,t,n){for(var e=i;e&&!e.hasOwnProperty(t);)e=Object.getPrototypeOf(e);!e&&i[t]&&(e=i);var r,o,a,s,u=$t(t);if(e&&!(r=e[u])){r=e[u]=e[t];var c=e&&Object.getOwnPropertyDescriptor(e,t);if(!(s=c)||!1!==s.writable&&("function"!=typeof s.get||void 0!==s.set)){var d=n(r,u,t);e[t]=function(){return d(this,arguments)},o=e[t],a=r,o[$t("OriginalDelegate")]=a}}return r}var Yt=function(i){function t(){var t=i.call(this,I,Ft,Vt)||this;return t.ce={},t.de=[],t}return n(t,i),t.fe=function(){var i=window.document.getElementById(b);if(i){var t=i.getAttribute(_);if(t&&mi(t))return mi(t)}},t.prototype.ne=function(i){this.we(),this.he(),this.ve()},t.prototype.ue=function(){},t.prototype.ie=function(i){var t,n;return i.Jt.wt===B&&(i.on=window.performance.timing.navigationStart,this.le(i)),i.an=e(e({},null===(t=this.D.Nt)||void 0===t?void 0:t.ii()),null===(n=this.D.Wt)||void 0===n?void 0:n.yt()),window.performance.memory&&(i.cn=window.performance.memory.jsHeapSizeLimit,i.dn=window.performance.memory.totalJSHeapSize,i.fn=window.performance.memory.usedJSHeapSize),i},t.prototype.le=function(i){this.de&&(i.pn=this.de,this.de=[]),this.ce&&(this.ce.pe&&(i.hn=this.ce.pe),this.ce.be&&(i.vn=this.ce.be),this.ce._e&&(i.ln=this.ce._e),this.ce={});var t=window.performance.timing;if("getEntriesByType"in window.performance&&"function"==typeof window.performance.getEntriesByType&&"PerformanceNavigationTiming"in window){var n=window.performance.getEntriesByType(W);n.length>0&&n[0]instanceof PerformanceNavigationTiming&&(t=n[0])}if(t instanceof PerformanceTiming){for(var e in F)i[e]=t[F[e]]?t[F[e]]-i.on:0;i.L=window.performance.navigation&&window.performance.navigation.redirectCount,i.U=window.document.documentElement.outerHTML.length}else{for(var e in F)i[e]=Math.floor(t[F[e]]||0);for(var e in K)i[e]=t[K[e]]}},t.prototype.ve=function(){var i=this;if("performance"in window&&"timing"in window.performance){var n=t.fe(),e=function(){i.me();var t={Zt:I,on:ki(),Jt:i.D.Wt.gt()};n&&(t.un=n),i.Yn.t(I,t)};document.readyState===di?(e(),this.ge()):bi("load",(function(t){e(),i.ge()}))}else Ei("Current browser not support performance api, page load info will not report.")},t.prototype.ge=function(){var i=this;if("history"in window&&"pushState"in window.history&&"replaceState"in window.history){var t=function(){var t;try{var n=i.D.Wt.bt(),e={Zt:I,on:ki(),Jt:n,sn:null===(t=window.event)||void 0===t?void 0:t.ye};i.Yn.t(I,e)}catch(i){Ri(i)}},n=this;this.ke=window.history.pushState,window.history.pushState=function(i,e,r){var o;null===(o=n.ke)||void 0===o||o.apply(this,arguments),t()},this.Se=window.history.replaceState,window.history.replaceState=function(i,e,r){var o;null===(o=n.Se)||void 0===o||o.apply(this,arguments),t()}}else Ei("Current browser not support history api, router change info will not report.")},t.prototype.we=function(){var i=this;"alert"in window&&(this.Te=Qt(window,"alert",(function(){return function(t,n){var e;if(document.readyState!==di){var r=Ti();e=i.Te.apply(t,n),i.de.push([r,Ti(),"alert",Mi(n[0]||"")])}else e=i.Te.apply(t,n);return e}}))),"confirm"in window&&(this.xe=Qt(window,"confirm",(function(){return function(t,n){var e;if(document.readyState!==di){var r=Ti();e=i.xe.apply(t,n),i.de.push([r,Ti(),"confirm",Mi(n[0]||"")])}else e=i.xe.apply(t,n);return e}}))),"prompt"in window&&(this.Ee=Qt(window,"prompt",(function(){return function(t,n){var e;if(document.readyState!==di){var r=Ti();e=i.Ee.apply(t,n),i.de.push([r,Ti(),"prompt",Mi(n[0]||"")])}else e=i.Ee.apply(t,n);return e}})))},t.prototype.he=function(){var i,t=this;if("MutationObserver"in window){var n=0;this.Re=new window.MutationObserver((function(i,e){if(document.readyState!==di){t.ce.pe||(t.ce.pe=Ti());for(var r=0,o=0,a=i;o<a.length;o++)for(var s=a[o],u=0;u<s.addedNodes.length;u++){var c=s.addedNodes.item(u);c&&c instanceof Element&&c.clientWidth&&c.clientHeight&&r++}r>0&&(t.ce.be||(t.ce.be=Ti()),r>n&&(t.ce._e=Ti(),n=r))}})),null===(i=this.Re)||void 0===i||i.observe(document,{attributes:!1,childList:!0,subtree:!0})}},t.prototype.me=function(){this.Re&&this.Re.disconnect(),this.Re=void 0},t}(Dt),Zt="undefined"!=typeof window;function Jt(i){return"string"==typeof i?i:"FormData"in window&&i instanceof FormData?function(i){if(i&&"forEach"in i){var t={};return i.forEach((function(i,n){t[n]=i})),yi.stringify(t)}return i.toString()}(i):yi.stringify(i)}function tn(i){var t=Ot(i).tt,n=Lt.J.X,e=n.qt,r=n.It;return!!e&&!(!function(i,t){void 0===t&&(t=window.location.href);try{var n=Ot(t),e=Ot(i);return n.tt===e.tt}catch(i){Ri(i)}return!1}(t)&&!r)}var nn=function(i){var t=[];try{if(Oi(i))return t;for(var n=mi(i).split(","),e=0,r=n;e<r.length;e++){var o=r[e];if(!Oi(o)){for(var a=mi(o).split(";"),s={name:""},u=0,c=a;u<c.length;u++){var d=c[u];if(!Oi(d)){var f=mi(d).split("=");if(1===f.length&&(s.name=f[0]),2===f.length)switch(f[0]){case"dur":s.duration=+f[1];break;case"desc":s.description=f[1]}}}s.name&&null!==s.duration&&void 0!==s.duration&&t.push(s)}}}catch(i){Ri(i)}return t};function en(i,t){var n={};if("string"==typeof i)try{for(var e=yi.parse(i),r=0,o=t;r<o.length;r++){null!=(f=rn(e,(d=o[r]).ci))&&(n[d.di]=Jt(f))}}catch(i){}else if("FormData"in window&&i instanceof FormData)for(var a=0,s=t;a<s.length;a++){null!=(f=on(i,(d=s[a]).ci))&&(n[d.di]=Jt(f))}else if(i instanceof Object)for(var u=0,c=t;u<c.length;u++){var d,f;null!=(f=rn(i,(d=c[u]).ci))&&(n[d.di]=Jt(f))}return n}function rn(i,t){try{if(!t||t.length<3||!i)return;for(var n=t.substring(1,t.length-1).split("]["),e=i,r=0,o=n;r<o.length;r++){var a=o[r];if(!e||!(a in e)){e=void 0;break}e=e[a]}return"object"==typeof e&&(e=yi.stringify(e)),e}catch(i){}}function on(i,t){try{if(!t||t.length<3||!i)return;var n=t.substring(1,t.length-1).split("][");if(1!==n.length)return;return i.get(n[0])}catch(i){}}var an=function(i){function t(){var t=i.call(this,O,Kt,Vt)||this;return t.enable=!0,t}return n(t,i),t.qe=function(i,t){try{for(var n=nn(i.headers.get(w)),e=0,r=n;e<r.length;e++){var o=r[e];o.name===h&&(t.qn=Math.max(t.Cn-t.T-o.duration,0))}t.j=n}catch(i){Ei("Cannot get end elapse form response headers, will use other metric to calculate.")}},t.Ie=function(){var i="";if("Error"in window&&"function"==typeof window.Error)try{throw new Error(l)}catch(n){var t=n.stack;t&&(i=r(["window.fetch ([native code])"],t.split("\n").slice(4)).join("\n"))}return i},t.prototype.ne=function(i){if("fetch"in window&&"function"==typeof window.fetch){this.Oe=window.fetch;var n=this;window.fetch=function(i,e){var r=this;if(n.enable){var o,a;if("string"==typeof i)e&&(o=e.body),a=new Request(i,e);else{if(!i)return n.Oe.apply(r,arguments);a=i,o=i.body}if(n.Ce(a))return n.Oe.apply(r,arguments);var s=n.Me(a);return n.Pe(a,s),n.Oe.call(r,a).then((function(i){return i.clone().text().then((function(e){s.In=s.On=s.Cn=s.Mn=ki()-s.on,s.yn=i.status,s.kn=i.statusText||A,s.Tn=e.length,t.qe(i,s),n.Be(s,a,o,i,e),n.Yn.t(O,s),n.nn(s,a,i,o,e)})),i}),(function(i){s.In=s.On=s.Cn=s.Mn=ki()-s.on,s.kn=j,n.Be(s,a,o,null,null),n.Yn.t(O,s)}))}return n.Oe.apply(r,arguments)}}else Ei("Current browser not support fetch api, fetch request info will not report.")},t.prototype.ue=function(){this.enable=!1},t.prototype.ie=function(i){return i},t.prototype.Le=function(i){var t="";return i.forEach((function(i,n){t+=n+": "+i+"\r\n"})),t},t.prototype.nn=function(i,t,n,e,r){var o;if(this.D.X.Pt&&this.D.X.Pt.length)for(var a=0,s=this.D.X.Pt;a<s.length;a++){var u=s[a];if(u.ze||u.Fi!==this.D.Ut.getMetadata().Fi)break;if(u.Ae.test(i.mn)){var c=[this.D.X.K,u.Ae.__WGBP_reg_source__,(null===(o=this.D.Ut)||void 0===o?void 0:o.getMetadata().Fi)||"",i.mn,this.Le(t.headers),Jt(e),this.Le(n.headers),r];Ft((function(i){Nt().nn(i)}),this,[c]),u.ze=!0}}},t.prototype.Pe=function(i,t){var n;tn(t.mn)&&t.bn&&(i.headers.set(this.D.X.Ot+d,Ni(t.bn,t._n,(null===(n=this.D.Nt)||void 0===n?void 0:n.oi())?"1":"0")),i.headers.set(this.D.X.Ot+f,"appKey="+this.D.X.K))},t.prototype.Me=function(i){var n,r,o,a=Ot(i.url);return{Zt:O,on:ki(),bn:Hi(),_n:ji(),mn:a.it,gn:"fetch",yn:0,kn:"",Sn:(i.method||U).toUpperCase(),En:!0,Rn:0,Tn:0,an:e(e({},null===(n=this.D.Nt)||void 0===n?void 0:n.ii()),null===(r=this.D.Wt)||void 0===r?void 0:r.yt()),xn:t.Ie(),T:0,qn:-1,Jt:this.D.Wt.gt(),sn:null===(o=window.event)||void 0===o?void 0:o.ye}},t.prototype.Be=function(i,t,n,e,r){try{var o=this.D.X.si;if(!o)return;for(var a=o.filter((function(i){return i.ui===ei})),s=0,u=a;s<u.length;s++){var c=u[s];Ci(m=t.headers.get(c.ci))&&(i.an[c.di]=Mi(m,this.D.X.fi))}for(var d=o.filter((function(i){return i.ui===ri})),f=Ct(i.mn),w=0,h=d;w<h.length;w++){Ci(m=f[(c=h[w]).ci])&&(i.an[c.di]=Mi(m,this.D.X.fi))}if(n){var v=en(n,o.filter((function(i){return i.ui===oi})));for(var l in v)Ci(v[l])&&(i.an[l]=Mi(v[l],this.D.X.fi))}if(e)for(var p=o.filter((function(i){return i.ui===ai})),b=0,_=p;b<_.length;b++){var m,g=_[b];if(e.headers.has(g.ci))Ci(m=e.headers.get(g.ci))&&(i.an[g.di]=Mi(m,this.D.X.fi))}if(r){var y=en(r,o.filter((function(i){return i.ui===si})));for(var l in y)Ci(y[l])&&(i.an[l]=Mi(y[l],this.D.X.fi))}}catch(i){Ri(i)}},t.prototype.Ce=function(i){var t=this.D.X.Mt;if(t&&t.length>0)for(var n=Ot(i.url).et,e=0,r=t;e<r.length;e++){var o=r[e];if(n.indexOf(o)>=0)return!0}},t}(Dt),sn=function(i){function t(){return i.call(this,M,Kt,Vt)||this}return n(t,i),t.prototype.ne=function(i){"performance"in window&&"getEntriesByType"in window.performance&&"function"==typeof window.performance.getEntriesByType?this.je(i):Ei("Current browser not support performance api, resource load info will not report.")},t.prototype.ue=function(){this.He&&this.He.disconnect()},t.prototype.ie=function(i){var t,n,r={Zt:M,Pn:Hi(),an:e(e({},null===(t=this.D.Nt)||void 0===t?void 0:t.ii()),null===(n=this.D.Wt)||void 0===n?void 0:n.yt()),on:Math.floor(performance.timing.navigationStart+i.startTime),Jt:this.D.Wt.gt()};for(var o in X){var a=i[X[o]]||0;r[o]=Math.floor(a>0?a-i.startTime:a)}for(var o in $)r[o]=i[$[o]];return r},t.prototype.je=function(i){var t=this,n=window.performance.getEntriesByType("resource");"PerformanceObserver"in window&&"function"==typeof window.PerformanceObserver&&(this.He=new PerformanceObserver((function(n){for(var e=0,r=n.getEntries();e<r.length;e++){var o=r[e];t.Ne(o)&&t.Yn.t(i,o)}})),this.He.observe({entryTypes:["resource"]}));for(var e=0,r=n;e<r.length;e++){var o=r[e];this.Ne(o)&&this.Yn.t(i,o)}},t.prototype.Ne=function(i){return i instanceof PerformanceResourceTiming&&((0===i.name.indexOf("http://")||0===i.name.indexOf("https://"))&&Boolean(i.initiatorType)&&gi(t.Ue,i.initiatorType)<0)},t.Ue=[G,V,D],t}(Dt);function un(i){try{if(!("responseType"in i))return i.responseText;switch(i.responseType){case"":case"text":return i.responseText;case"json":return yi.stringify(i.response);case"arraybuffer":case"blob":return i.response.toString();case"document":return i.response.documentElement.outerHTML}}catch(i){Ri(i)}return""}var cn={HISTORY:[Yt],ERROR:[Xt],REQUEST:[function(i){function t(){var t=i.call(this,C,Kt,Vt)||this;return t.enable=!0,t.We={},t}return n(t,i),t.qe=function(i,t){if(i.getAllResponseHeaders&&"function"==typeof i.getAllResponseHeaders)try{var n=function(i){var t={};if(Oi(i))return t;for(var n=0,e=mi(i).split(/[\r\n]+/);n<e.length;n++){var r=e[n].split(": ");t[r.shift()]=r.join(": ")}return t}(i.getAllResponseHeaders()),e=n[w];if(Oi(e))return;for(var r=nn(e),o=0,a=r;o<a.length;o++){var s=a[o];if(s.name===h){t.qn=Math.max(t.Cn-t.T-s.duration,0);break}}t.j=r}catch(i){Ei("Cannot get end elapse form response headers, will use other metric to calculate.")}},t.Ie=function(){var i="";if("Error"in window&&"function"==typeof window.Error)try{throw new Error(l)}catch(n){var t=n.stack;t&&(i=r(["XMLHttpRequest","    at XMLHttpRequest.proto.send ([native code])"],t.split("\n").slice(5)).join("\n"))}return i},t.Le=function(i){var t="";if(!i)return t;for(var n in i)t+=n+": "+i[n]+"\r\n";return t},t.prototype.ne=function(i){if("XMLHttpRequest"in window&&"function"==typeof window.XMLHttpRequest){var t=this;this.Ge=XMLHttpRequest.prototype;var n=XMLHttpRequest.prototype.addEventListener||XMLHttpRequestEventTarget.prototype.addEventListener;this.Ve=Qt(this.Ge,"open",(function(){return function(i,e){try{t.Ce(i,e),!i.__WGBP_ignore__&&t.enable&&(t.De(n,i),t.Me(i,e))}catch(i){Ri(i)}return t.Ve.apply(i,e)}})),this.Fe=Qt(this.Ge,"setRequestHeader",(function(){return function(i,n){try{if(t.Ke(i)&&n.length>=2){var e=t.We[i.__WGBP_id__];e.Xe=e.Xe||{},e.Xe[n[0]]=n[1]}}catch(i){Ri(i)}return t.Fe.apply(i,n)}})),this.$e=Qt(this.Ge,"send",(function(){return function(i,n){try{if(t.Ke(i)){t.Pe(i);var e=t.We[i.__WGBP_id__];e.Qe=n[0],e.Ye.on=ki()}}catch(i){Ri(i)}return t.$e.apply(i,n)}}))}else Ei("Current browser not support XMLHttpRequest, xhr request info will not report.")},t.prototype.ue=function(){this.enable=!1},t.prototype.ie=function(i){return i},t.prototype.Ke=function(i){var t=i.__WGBP_id__;return this.enable&&!!t&&!!this.We[t]&&!!this.We[t].Ye},t.prototype.Me=function(i,n){var r,o,a,s=i.__WGBP_id__=Hi(),u=n[0],c=n[1],d=n[2],f=Ot(c);this.We[s]={Ye:{Zt:C,on:ki(),bn:s,_n:ji(),mn:f.it,gn:"xhr",yn:0,kn:"",Sn:(u||U).toUpperCase(),En:!(!1===d),Rn:0,Tn:0,an:e(e({},null===(r=this.D.Nt)||void 0===r?void 0:r.ii()),null===(o=this.D.Wt)||void 0===o?void 0:o.yt()),xn:t.Ie(),qn:-1,Jt:this.D.Wt.gt(),sn:null===(a=window.event)||void 0===a?void 0:a.ye,T:0}}},t.prototype.Pe=function(i){var t,n=this.We[i.__WGBP_id__].Ye;tn(n.mn)&&(i.setRequestHeader(this.D.X.Ot+d,Ni(n.bn,n._n,(null===(t=this.D.Nt)||void 0===t?void 0:t.oi())?"1":"0")),i.setRequestHeader(this.D.X.Ot+f,"appKey="+this.D.X.K))},t.prototype.De=function(i,t){var n=this;t.__WGBP_event_listening__||(t.__WGBP_event_listening__=!0,i.call(t,li,(function(i){n.Ke(t)&&(n.We[t.__WGBP_id__].Ye.kn=N)}),!0),i.call(t,vi,(function(i){n.Ke(t)&&(n.We[t.__WGBP_id__].Ye.kn=H)}),!0),i.call(t,hi,(function(i){n.Ke(t)&&n.Ze(t)}),!0),i.call(t,pi,(function(i){n.Ke(t)&&n.Je(t)})))},t.prototype.Ze=function(i){var t=this.We[i.__WGBP_id__].Ye;switch(i.readyState){case 2:t.In=ki()-t.on;break;case 3:t.On=ki()-t.on;break;case 4:t.Cn=ki()-t.on,!t.On&&(t.On=t.Cn),!t.In&&(t.In=t.On),!("onloadend"in XMLHttpRequest.prototype)&&this.Je(i)}},t.prototype.Je=function(i){var n=this.We[i.__WGBP_id__].Ye;n.Mn=ki()-n.on,n.Rn=i.timeout,n.yn=i.status,n.kn=n.kn||i.statusText,0!==n.yn||n.kn||(n.kn=j),n.Tn=function(i){try{if(!("responseType"in i))return i.responseText.length;switch(i.responseType){case"":case"text":return i.responseText.length;case"json":return yi.stringify(i.response).length;case"arraybuffer":return i.response.byteLength;case"blob":return i.response.size;case"document":return i.response.documentElement.outerHTML.length}}catch(i){Ri(i)}return 0}(i),t.qe(i,n),this.Be(i),this.Yn.t(C,n),this.nn(i),delete this.We[i.__WGBP_id__],delete i.__WGBP_id__},t.prototype.Be=function(i){try{var t=this.We[i.__WGBP_id__],n=t.Ye,e=this.D.X.si;if(!e||!e.length)return;for(var r=e.filter((function(i){return i.ui===ei})),o=0,a=r;o<a.length;o++){var s=a[o];Ci(_=t.Xe&&t.Xe[s.ci])&&(n.an[s.di]=Mi(_,this.D.X.fi))}for(var u=e.filter((function(i){return i.ui===ri})),c=Ct(n.mn),d=0,f=u;d<f.length;d++){Ci(_=c[(s=f[d]).ci])&&(n.an[s.di]=Mi(_,this.D.X.fi))}if(t.Qe){var w=e.filter((function(i){return i.ui===oi})),h=en(t.Qe,w);for(var v in h)Ci(h[v])&&(n.an[v]=Mi(h[v],this.D.X.fi))}for(var l=e.filter((function(i){return i.ui===ai})),p=0,b=l;p<b.length;p++){var _,m=b[p];Ci(_=i.getResponseHeader(m.ci))&&(n.an[m.di]=Mi(_,this.D.X.fi))}var g=function(i){return"response"in i?i.response:i.responseText}(i);if(g){var y=en(g,e.filter((function(i){return i.ui===si})));for(var v in y)Ci(y[v])&&(n.an[v]=Mi(y[v],this.D.X.fi))}}catch(i){Ri(i)}},t.prototype.nn=function(i){var n;try{var e=this.We[i.__WGBP_id__],r=e.Ye;if(200===i.status&&this.D.X.Pt&&this.D.X.Pt.length)for(var o=this.D.X.Pt,a=0,s=o;a<s.length;a++){var u=s[a];if(u.ze||u.Fi!==this.D.Ut.getMetadata().Fi)break;var c=u.Ae,d=c.__WGBP_reg_source__;if(c.test(r.mn)){var f=[this.D.X.K,d,(null===(n=this.D.Ut)||void 0===n?void 0:n.getMetadata().Fi)||"",r.mn,t.Le(e.Xe),Jt(e.Qe),i.getAllResponseHeaders(),un(i)];Ft((function(i){Nt().nn(i)}),this,[f]),u.ze=!0}}}catch(i){Ri(i)}},t.prototype.Ce=function(i,t){var n=this.D.X.Mt;if(n&&n.length>0){t[0];for(var e=t[1],r=(t[2],Ot(e).et),o=0,a=n;o<a.length;o++){var s=a[o];if(r.indexOf(s)>=0)return void(i.__WGBP_ignore__=!0)}}},t}(Dt),an],RESOURCE:[sn],OPERATION:[function(i){function t(){return i.call(this,P,Kt,Vt)||this}return n(t,i),t.prototype.ne=function(i){this.ir=this.ir.bind(this),this.tr=this.tr.bind(this),bi(fi,this.ir,!0),bi(wi,this.tr,!0)},t.prototype.ie=function(i){return i},t.prototype.ue=function(){_i(fi,this.ir,!0),_i(wi,this.tr,!0)},t.prototype.ir=function(i){var t,n,r=i.target,o=Hi();i.ye=o;var a={Zt:P,sn:o,on:ki(),Bn:0,Ln:i.isTrusted,zn:Pi(r),An:fi,jn:i.clientX+", "+i.clientY,Hn:r.tagName+":"+(r.type||r.tagName),Nn:Bi(r),Jt:this.D.Wt.gt(),an:e(e({},null===(t=this.D.Nt)||void 0===t?void 0:t.ii()),null===(n=this.D.Wt)||void 0===n?void 0:n.yt())};this.Yn.t(P,a)},t.prototype.tr=function(i){var t,n=i.target;if(n&&(n instanceof HTMLInputElement||n instanceof HTMLTextAreaElement)){var r=Hi();i.ye=r;var o={Zt:P,sn:r,on:ki(),Bn:0,Ln:i.isTrusted,zn:n.value,An:wi,jn:(null==n?void 0:n.clientLeft)+", "+(null==n?void 0:n.clientTop),Hn:n.tagName+":"+n.type,Nn:Bi(n),Jt:this.D.Wt.gt(),an:e(e({},null===(t=this.D.Nt)||void 0===t?void 0:t.ii()),this.D.Wt.yt())};this.Yn.t(P,o)}},t}(Dt)]},dn={};function fn(i){if(cn[i]&&!dn[i]){dn[i]=[];for(var t=0,n=cn[i];t<n.length;t++){var e=new(0,n[t]);dn[i].push(e)}xi("Add collect plugin "+i)}}function wn(i,t,n,e){var r=yi.parse(jt(i,!1)),o={K:t,Xi:n||"",Et:e,Tt:r.message,xt:r.enable,Rt:r.collectItems,qt:r.globalTrace,It:r.traceCrossOrigin,Ot:r.traceHeadersPrefix,Ct:r.filterUser,ai:r.importantUsers,Mt:r.excludeApiPath,si:[],Pt:[],Bt:r.sendQueueFlushInterval,Lt:r.sendQueueFlushInterval,zt:r.sendMetricRetryDelay,fi:r.maxTransLength};if(r.primaryInfoTrackOpts&&r.primaryInfoTrackOpts instanceof Array)for(var a=0,s=r.primaryInfoTrackOpts;a<s.length;a++){var u=s[a];o.si.push({di:u.tagKey,ui:u.trackPos,ci:u.trackKey})}if(r.dataTrackOpts)for(var c=0,d=r.dataTrackOpts;c<d.length;c++){var f=d[c];try{var w=new RegExp(f.url);w.__WGBP_reg_source__=f.url,o.Pt.push({Fi:f.deviceId,Ae:w,ze:f.trackState})}catch(i){Ri(i)}}return o}function hn(i){try{var t=document.getElementById(b);if(null===t)return void Ri('Browser probe script tag not found, please set id="wg-browser-probe" for script tag.');var n=t.getAttribute("data-appkey"),e=t.getAttribute("data-appversion"),r=t.getAttribute("data-serveraddr");if(n&&r){var o=localStorage.getItem(b+"_"+n);o&&i(wn(o,n,e,r)),function(i,t,n,e,r){try{var o=new XMLHttpRequest,a=""+n+S+"?t="+(new Date).getTime();o.__WGBP_ignore__=!0,o.open("post",a,!0),o.timeout=5e3,o.onerror=function(){Ri("Remote config load error, response status: "+o.status)},o.ontimeout=function(){Ri("Remote config load elapse over 5 seconds, timeout")},o.onloadend=function(){200===o.status?o.responseText?e(o.responseText):(Ri("Server return empty config, please check server address config and collector component status"),r()):(Ri("Remote config load error, response status: "+o.status),r())};var s=Ht(yi.stringify([i,t]));o.send(s)}catch(i){Ri("WebGate browser probe inject error, cause: "+i.message),r()}}(n,a,r,(function(t){localStorage.setItem(b+"_"+n,t),o||i(wn(t,n,e,r))}),(function(){localStorage.removeItem(b+"_"+n)}))}else Ri("Must set appkey and serveraddr attribute to your probe script tag.")}catch(i){Ri(i)}}var vn=window.WGBrowserProbe;vn||(window.WGBrowserProbe=vn={initFlag:!1,deviceSupport:!0,context:{pageId:""},init:function(){if(this.deviceSupport=!!(Zt&&window.performance&&window.performance.timing&&window.localStorage&&window.sessionStorage&&window.XMLHttpRequest),this.deviceSupport){if(this.initFlag)return void Ei("WebGate browser probe has already been injected.");this.initFlag=!0,hn((function(i){var t,n;((n=i).xt?n.K&&n.Et?(null!==n.Xi&&void 0!==n.Xi&&(n.Xi=mi(n.Xi),Boolean(n.Xi)||(n.Xi=p)),1):(Ri("Valid agent config fail, cause appKey or serverAddr is null."),0):(Ri(n.Tt),0))&&(qi(">>> "+window.location.href+"\n WebGate-Browser-Probe V"+a,!1),Lt.jt(i),t=new Wt,Gt.push(t),Pt||(Pt=new Ut),function(){for(var i=0,t=Lt.J.X.Rt;i<t.length;i++)fn(t[i])}())}))}else Ri("Current device is not support.")},addTag:function(i,t){var n;this.deviceSupport?this.initFlag?"string"==typeof i&&"string"==typeof t?""!==mi(i)&&""!==mi(t)?(i=mi(i),t=mi(t),i.length>200||i.indexOf(":")>=0||i.indexOf(";")>=0||t.length>200||t.indexOf(":")>=0||t.indexOf(";")>=0?Ri("Tag key or value must be less than 200 characters and cannot contain ':' and ';'"):null===(n=Lt.J.Nt)||void 0===n||n.ti(i,t)):Ri("Tag key or value can not be null!"):Ri("Tag key or value must be string!"):Ei("WebGate browser probe not init yet, please call init first."):Ei("Current device is not support, please update your browser.")},removeTag:function(i,t){var n;this.deviceSupport?this.initFlag?"string"==typeof i&&""!==mi(i)?(i=mi(i),null==t||"string"==typeof t?("string"==typeof t&&(t=mi(t)),null===(n=Lt.J.Nt)||void 0===n||n.ri(i,t)):Ri("Value must be one of null, undefined or string!")):Ri("Tag key can not be null and must be string!"):Ei("WebGate browser probe not init yet, please call init first."):Ei("Current device is not support, please update your browser.")}}),vn.init()}();
