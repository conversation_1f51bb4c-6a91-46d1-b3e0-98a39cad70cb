import * as types from './mutation-types'

const mutations = {
  [types.SET_LOCAL](state, locationTxt) {
    state.locationTxt = locationTxt
  },
  [types.SET_NAVBAR_DESK](state, obj){
    console.info(obj);
    let list = state.navbarDesk.slice();
    let item,tabList = [];//state.navbarDesk.slice();
    for(let i = 0; i < list.length; i++){
      if(obj[`total${list[i].id}`]){
        item = state.navbarDesk[i];
        item.num = obj[`total${list[i].id}`];
        tabList.push(item);
      }
    }
    console.info(tabList);
    state.navbarDesk = tabList;
  },
  [types.SET_NAVBAR_JIKE](state, obj){
    state.navbarJike = obj;
  },
  [types.SET_HOT_MENU_LIST](state, obj){
    state.hotMenuList = obj;
  },
  [types.SET_WEEK_JIKE](state, obj){
    state.weekJike = obj;
  },
  [types.SET_WEEK_DESK](state, obj){
    state.weekDesk = obj
  },
  [types.SET_WEEK_XIAOQU](state, obj){
    state.weekXiaoqu = obj
  },
  [types.SET_WEEK_COLLEGE](state, obj){
    state.weekCollege = obj
  },
    [types.SET_WEEK_CUSTOM](state, obj){
        state.weekCustom = obj
    }
}
export default mutations
