/*家客装维首页切换使用，灵犀助手home图标使用*/
import _axios from '@/base/nlAxios';
import Storage from '@/base/storage';

const danghuanCryptoJS = require('@/base/danghuan/encrypt.js');
let encrypt = danghuanCryptoJS.Encrypt;

const stationChangeModule = {
  namespaced: true,
  state: {
    currentStationId: '',
    LxZsBtnFlg: false,//是否是灵犀助手首页图标
    installMainFlg:false,//家客装维双首页，是否是装维随销首页
    jiaKeHomeFlg:false,//装维随销是否是家客装维首页
  },

  getters: {},

  mutations: {
    rewriteStation(state, value) {
      state.currentStationId = value;
    },
    rewriteBtn(state, value) {
      state.LxZsBtnFlg = value;
    },
    rewriteInstallMainFlg(state, value) {
      state.installMainFlg = value;
    },
    rewriteJiaKeHomeFlg(state, value) {
      state.jiaKeHomeFlg = value;
    }
  },
  actions: {
    //获取灵犀菜单
    getHomeBtnMenuList(context) {
      let userInfo = Storage.session.get('userInfo');

      let url = `/xsb/api-user/menu/h5getMenuByLevel?stationId=${userInfo.stationId}&level=13:0`;
      _axios.get(url).then((res) => {
        const { retCode, data } = res.data;

        // 判断 retCode 是否为 '0' 且 data 是数组
        if (retCode === '0' && Array.isArray(data)) {
          const menuList = data.find(item => item.privId === '13000001');

          // 判断 menuList 是否存在且 itemList 是数组
          if (menuList && Array.isArray(menuList.itemList)) {
            // 判断是否配置了灵犀助手
            const hasLxAssistant = menuList.itemList.some(item => item.privId === '4848');
            context.commit('rewriteBtn', hasLxAssistant);
          } else {
            context.commit('rewriteBtn', false);
          }
        } else {
          context.commit('rewriteBtn', false);
        }
      }).catch(err => {
        context.commit('rewriteBtn', false);
      });
    },
  }
};


export default stationChangeModule;
