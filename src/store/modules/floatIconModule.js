import _axios from '@/base/nlAxios';
import Storage from '@/base/storage';

//悬浮图标设置
const floatIconModule = {
  namespaced: true,
  state: {
    menuList: [],
    recommendIconList:[],//推荐图标
    isUpdateFlag: false,// 是否更新过菜单统计
    // 浮层各个词条点击量（页面展示使用）
    menuCount: {
      menuCountList:[], // 统计表
      menuRecordList:[]// 记录表
    },
    // 浮层顶部固定菜单
    topFixedList:[],
  },

  getters: {},

  mutations: {
    rewriteList(state, value) {
      state.menuList = value;
    },
    rewriteRecommendIconList(state, value) {
      state.recommendIconList = value;
    },
    // 更新菜单统计
    rewriteIsUpdateFlag(state, value) {
      state.isUpdateFlag = value;
    },
    // 更新菜单统计数据
    rewriteMenuCount(state,value) {
      state.menuCount = value;
    },
    // 更新顶部固定菜单
    rewriteTopFixedList(state,value) {
      state.topFixedList = value;
    },
  },

  actions: {
    //获取悬浮菜单
    getMenuList(context) {
      return new Promise((resolve, reject) => {
        let url = '/xsb/api-user/fastEntry/h5getFastEntry';
        let uinfo = Storage.session.get('userInfo');

        let params = {
          stationId: uinfo.stationId,
          unLoadFlg: true
        };

        _axios.post(url, params).then((res) => {
          let { retCode, data, retMsg } = res.data;
          if (retCode === '0' && Array.isArray(data)) {
            console.log('CounterStore=> action: asyncIncrement', data);
            context.commit('rewriteList', data);
            resolve(data);
          } else {
            context.commit('rewriteList', []);
            resolve([]);
          }
        }).catch(err => {
          reject(err);
          // console.log(err);
        });
      });

    }
  }
};


export default floatIconModule;
