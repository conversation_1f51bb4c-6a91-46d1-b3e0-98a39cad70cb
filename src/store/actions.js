import * as types from './mutation-types'



export const setNavBarNum = function({commit, state}, {data,key}) {
    if(key == 'desk'){
      commit(types.SET_NAVBAR_DESK,data);
    }else if(key == 'jike'){
      commit(types.SET_NAVBAR_JIKE,data);
    }
}

export const updateWeekArr = function({commit, state},{src,day,num,type}) {
  let list = {};
  if(src == 'desk'){//首页的
    list =  JSON.parse(JSON.stringify(state.weekDesk));
  }else if(src == 'jike'){
    list = JSON.parse(JSON.stringify(state.weekJike));
  } else if(src== 'xiaoqu'){
    list = JSON.parse(JSON.stringify(state.weekXiaoqu));
  } else if(src== 'college'){
    list = JSON.parse(JSON.stringify(state.weekCollege));
  } else if(src== 'custom'){
      list = JSON.parse(JSON.stringify(state.weekCustom));
  }


  for(let i = 0; i < list.length; i++){
    if(list[i].planDate == day){
      let oldNum = 0;
      if(src == 'jike' || src == 'xiaoqu' || src == 'college'){
        oldNum = parseInt(list[i].planNum) + num;
      }else if(src == 'desk'){
        oldNum = parseInt(list[i]['type'+type+'Num'])+ num;
      }



      if(src == 'jike' || src == 'xiaoqu' || src == 'college'){
        list[i].planNum = Math.max(oldNum,0);
      } else if(src == 'desk'){
        list[i]['type'+type+'Num'] = Math.max(oldNum,0);
        list[i]['type0Num'] = Math.max(parseInt(list[i].type0Num) + num,0);
      }
      break;
    }
  }
	
  if(src == 'desk'){//首页的
    commit(types.SET_WEEK_DESK, list);
  }else if(src == 'jike'){
    commit(types.SET_WEEK_JIKE, list);
  }else if(src == 'xiaoqu'){
    commit(types.SET_WEEK_XIAOQU, list);
  }else if(src == 'college'){
		commit(types.SET_WEEK_COLLEGE, list);
	}
}
