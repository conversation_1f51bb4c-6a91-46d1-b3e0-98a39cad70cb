/* import Vue from 'vue'
import Vuex from 'vuex'
Vue.use(Vuex);

//获取地址模块
const getLocal = {
    state: {
        localTxt: ''
    },
    getters: {
        getLocalTxt: state => {
          return state.localTxt;
        }
    },
    mutations: {
        setLocal(state, payload) {
            state.localTxt = payload;
        }
    }
};

let store = new Vuex.Store({
    modules: {
        getLocal
    }
});

export default store; */



import Vue from 'vue'
import Vuex from 'vuex'
import * as actions from './actions'
import state from './state'
import * as getters from './getters'
import mutations from './mutations'
import createLogger from 'vuex/dist/logger'
Vue.use(Vuex);


// https://webpack.js.org/guides/dependency-management/#requirecontext
const modulesFiles = require.context('./modules', true, /\.js$/);

// you do not need `import app from './modules/app'`
// it will auto require all vuex modules from modules file
const modules = modulesFiles.keys().reduce((modules, modulePath) => {
    // set './app.js' => 'app'
    const moduleName = modulePath.replace(/^\.\/(.*)\.\w+$/, '$1')
    const value = modulesFiles(modulePath)
    modules[moduleName] = value.default
    return modules

}, {})

const debug = process.env.NODE_ENV !== 'production'
export default new Vuex.Store({
    modules,
    actions,
    getters,
    state,
    mutations,
    strict: debug,
    plugins: debug ? [createLogger()] : []
  })
