<template>
  <div class="wrapper">
    <Header :tsTitleTxt="headTitle" :hasBackFlg="false"></Header>
    <div class="content-wrap" v-if="showAlert">
      <span class="product-title">{{alertContent}}</span>
    </div>
    <div v-if="isTimeout1==true">
      <div v-if='qrCodeFlag==true'>
        <div class="qrcode" ref="qrCodeUrl" ></div>
        <div class="text1">打开网格通app</div>
        <div class="text2">在[工具页]点击或者搜索[企微扫码登陆]菜单进行扫码登陆</div>
      </div>
      <div v-else>
        <div class="wanggeton">
          <img src="static/thirdsImg/wanggetong.png"/>
        </div>
        <div class="text1">登录网格通app</div>
        <div class="text2">在[工具页]点击或者搜索[企微扫码登陆]菜单进行扫码登陆</div>
      </div>
    </div>
    <div class="bottombg">
      <img src="static/thirdsImg/hailang.png">
    </div>
  </div>
</template>

<script>
  import Header from 'components/common/Header.vue';
  import {envjudge} from './utils.js';
  import {dateFormat} from '@/base/utils.js'
  import Storage from '@/base/storage';
  import html2canvas from 'html2canvas'
  import {getRealUrl} from '@/base/utils';
  // import QRCode from 'qrcodejs2';
  import QRCode from '@/base/qrcode'
  import axios from "axios";
  // import axios from "../../base/nlAxios";
  export default {
    components: {Header, envjudge},
    props: {},
    data() {
      return {
        headTitle: '网格通远程受理',
        showAlert: false,
        alertContent: '请在微信客户端打开',
        isTimeout1: true,//默认为false
        //isTimeout2:false,//默认为false
        qrCodeModel:null,
        qwGroupId: '',
        qwOperId: '',
        servNumber: '',
        qrCodeId: '',
        tokenStr: '',
        token:'',
        custNumber:'',
        custId:'',
        agentPhone:'',
        agentCustId:'',
        timer1: null,
        index: 1,
        qrcode:null,
        qrCodeFlag: true,
        envType: '', // 0: 企业微信 1: 托管平台
      }
    },
    methods: {
      //获取token
      getparameter(){
        //清除token、会话信息
        console.log('进入getparameter');
        Storage.session.remove('userInfo');
        Storage.session.remove('qwInfo');
        let tokenStr = this.$route.query.tokenStr;
         console.log('tokenStr='+tokenStr);
        if (tokenStr==null||tokenStr.length<=0){
          this.$alert("token不能为空")
          return
        }
          let url ='/xsb/api-user/qyQrCode/h5getAuthInfoByToken';
          this.$http.post(url, {"tokenStr":tokenStr}).then(res => {
            let {retCode, retMsg, data} = res.data;
            if (retCode == '0') {
              this.qwGroupId=data.qwGroupId;
              this.qwOperId=data.qwOperId;
              this.servNumber=data.servNumber;
              this.token=data.token;
              this.qrCodeModel = data;
              this.envType = data.envType || '0'; // 设置envType，默认为0
              
              // 根据envType判断是否需要在微信中打开
              let sourceStr = envjudge();
              if (this.envType === '0' && sourceStr !== 'com-wx-mobile' && sourceStr !== 'com-wx-pc') {
                this.showAlert = true;
                this.isTimeout1 = false;
                return;
              }

              if(typeof data.custNumber !== 'undefined' || data.custNumber != null || data.custNumber !== ''){
                this.custNumber = data.custNumber;
              }
              if(typeof data.custId !== 'undefined' || data.custId != null || data.custId !== ''){
                this.custId = data.custId;
              }
              if(typeof data.agentPhone !== 'undefined' || data.agentPhone != null || data.agentPhone !== ''){
                this.agentPhone = data.agentPhone;
              }
              if(typeof data.agentCustId !== 'undefined' || data.agentCustId != null || data.agentCustId !== ''){
                this.agentCustId = data.agentCustId;
              }
              axios.defaults.headers.aldregion = data.region+'_'+data.servNumber;//新增aldregion
              let userIofo = {"servNumber": this.servNumber}
              Storage.session.set('userInfo',userIofo);
              this.getOauth2Url();
              // this.getOperRegion()
            }else {
              // alert(2);
              this.$alert(retMsg+tokenStr || "获取二维码失败")
            }
          })



      },
      //定时器
      timer() {
        var index = this.index++;
        if (index % 5 == 0 && index < 300) {
          console.log(index + '小于300')
          this.getAppScanResult()
        } else if (index == 300) {
          this.index = 1
          console.log(this.index + '等于300')
          this.qrcode = null
          this.$messagebox({
            title: '提示',
            message: '二维码已过期请重新进入该页面',
            showCancelButton: false,
            showConfirmButton: true})
            .then(action => {
              if(action == 'confirm'){
                window.close();
              }
            }).catch(() => {});
          this.isTimeout1=true;
          // this.getOauth2Url()
        }
      },
      //生成二维码
      getOauth2Url() {
        if (this.qwGroupId == null|| this.qwGroupId.length<=0){
           this.$alert('企微群主id不能为空');
           return
        }
        if (this.qwOperId == null||this.qwOperId.length<=0){
          this.$alert('企微id不能为空');
          return
        }
        if (this.servNumber == null||this.servNumber.length<=0){
          this.$alert('手机号不能为空');
          return
        }
        var that= this;
        let url = '/xsb/api-user/qyQrCode/h5getQRCodeInfoByToken';
        let parameter = {"qwGroupId": this.qwGroupId, "qwOperId": this.qwOperId, "servNumber": this.servNumber, "token": this.token,"custNumber":this.custNumber,"custId":this.custId,"agentPhone":this.agentPhone,"agentCustId":this.agentCustId,"aldToken":this.qrCodeModel.aldToken,"region":this.qrCodeModel.region};
        this.$http.post(url, parameter).then(res => {
          let {retCode, retMsg, data} = res.data;
          if (retCode == '0') {
            if (data != '' && data != null) {
              if (data.status === '1') {
                let userInfo = JSON.parse(data.userInfo);
                Storage.session.set('userInfo',userInfo);
                Storage.session.set('tokenId',userInfo.tokenid);
                console.log('token='+userInfo.tokenid);
                axios.defaults.headers.tokenid = userInfo.tokenid;
                axios.defaults.headers.operaterPhone = userInfo.servNumber;//add by qhuang 2021-11-24登录token改造
                console.log(userInfo.imei);
                if(typeof userInfo.imei!== 'undefined' || userInfo.imei!= null || userInfo.imei!== ''){
                  axios.defaults.headers.imei = userInfo.imei;
                }else{
                  axios.defaults.headers.imei = 'null';
                }
                axios.defaults.headers.aldregion = userInfo.region+'_'+userInfo.servNumber;//新增aldregion
                axios.defaults.headers.validateType = 'strict';//严格校验token
                let qwInfo = {"qwGroupId":this.qwGroupId,"qwOperId":this.qwOperId}
                Storage.session.set('qwInfo',qwInfo);//操作员所属企业id和操作员微信id
                Storage.session.set('envType',this.envType);
                if(typeof data.custNumber !== 'undefined' || data.custNumber != null || data.custNumber !== ''){
                  Storage.session.set("qw_custNumber",data.custNumber); //目标客户手机号
                }
                if(typeof data.custId !== 'undefined' || data.custId != null || data.custId !== ''){
                  Storage.session.set("qw_custId",data.custId);//目标客户微信id
                }
                if(typeof data.agentPhone !== 'undefined' || data.agentPhone != null || data.agentPhone !== ''){
                  Storage.session.set("qw_agentPhone",data.agentPhone); //分销人手机号
                }
                if(typeof data.agentCustId !== 'undefined' || data.agentCustId != null || data.agentCustId !== ''){
                  Storage.session.set("qw_agentCustId",data.agentCustId);//分销人微信id
                }
                
                this.$router.push('/RemoteReceptionMenu');
              }
              else
              {
                this.qrCodeId = data.qrCodeId;
                // 非扫码不显示二维码
                if (this.qrCodeFlag) {
                  this.qrcode = new QRCode(this.$refs.qrCodeUrl, {
                    text: this.qrCodeId, // 需要转换为二维码的内容
                    width: 300,
                    height: 300,
                    colorDark: '#000000',
                    colorLight: '#ffffff',
                    correctLevel: 3
                  })
                }
                this.timer1 = setInterval(function () {
                  that.timer()
                }, 1000)
              }
            }

          } else {
            this.$alert(retMsg +'groupId='+this.qwGroupId+'operId='+this.qwOperId+'servNumber='+this.servNumber|| "获取二维码失败")
            this.isTimeout1 = false;
          }
        })
      },
      /*查询二维码状态 */
      getAppScanResult() {
        if (this.qrCodeId == null|| this.qrCodeId.length<=0){
          this.$alert('二维码id不能为空');
          return
        }
        let url = '/xsb/api-user/qyQrCode/h5getQRCodeInfo'
        this.$http.post(url, {"qrCodeId": this.qrCodeId, "token": this.token}).then(res => {
          let {retCode, retMsg, data} = res.data;
          if (retCode == '0') {
            if (data.status === '1') {
              clearInterval(this.timer1);
              this.timer1 = null;
              let userInfo = JSON.parse(data.userInfo);
              Storage.session.set('userInfo',userInfo);
              Storage.session.set('tokenId',userInfo.tokenid);
              console.log('token='+userInfo.tokenid);
              axios.defaults.headers.tokenid = userInfo.tokenid;
              axios.defaults.headers.operaterPhone = userInfo.servNumber;//add by qhuang 2021-11-24登录token改造
              axios.defaults.headers.imei = userInfo.imei;//add by qhuang 2021-11-24登录token改造
              axios.defaults.headers.aldregion = userInfo.region+'_'+userInfo.servNumber;//新增aldregion
              axios.defaults.headers.validateType = 'strict';//严格校验token
              let qwInfo = {"qwGroupId":this.qwGroupId,"qwOperId":this.qwOperId}
              Storage.session.set('qwInfo',qwInfo);
              Storage.session.set('envType',this.envType);
              if(typeof data.custNumber !== 'undefined' || data.custNumber != null || data.custNumber !== ''){
                Storage.session.set("qw_custNumber",data.custNumber);
              }
              if(typeof data.custId !== 'undefined' || data.custId != null || data.custId !== ''){
                Storage.session.set("qw_custId",data.custId);
              }
              if(typeof data.agentPhone !== 'undefined' || data.agentPhone != null || data.agentPhone !== ''){
                Storage.session.set("qw_agentPhone",data.agentPhone);
              }
              if(typeof data.agentCustId !== 'undefined' || data.agentCustId != null || data.agentCustId !== ''){
                Storage.session.set("qw_agentCustId",data.agentCustId);
              }
              this.$router.push('/RemoteReceptionMenu');
            }
            else if (data.status === '-1' || data.status === '-2') {
              let message  = data.status==-1?'用户取消授权请重新进入该页面获取二维码':'二维码已过期请重新进入该页面获取二维码'
              this.qrcode = null
              this.$messagebox({
                title: '友情提示',
                message: message,
                showCancelButton: false,
                showConfirmButton: true})
                .then(action => {
                  if(action == 'confirm'){
                    window.close();
                  }
                }).catch(() => {

              });
              this.isTimeout1=true;
            }
          }else {
            this.$alert(retMsg+this.qrCodeId+this.token || "获取二维码信息失败");
          }
        })
      },

      /**
       * 企微扫码开关
       */
      qrCodeSwitch(){
        let params = {
          'servNumber': this.servNumber,
          'busiType': 'qw_qrCode_switch',
          'region': '99'
        };
        this.$http.post('/xsb/ability/businessLimit/h5qryQrCodeAuthority', params).then(res=>{
          let { retCode } = res.data;
          if (retCode == '0') {
            this.qrCodeFlag = false;
          }
        })
      }
    },

    mounted() {
       let sourceStr = envjudge();
       console.log('sourceStr='+sourceStr);
       this.qrCodeSwitch();
       if (sourceStr === 'com-wx-mobile' || sourceStr === 'com-wx-pc') {
        console.log('在微信中打开')
        this.getparameter();
      } else {
        // 先调用getparameter获取envType
        console.log('不在微信中打开')
        this.getparameter();
        
      }
    },
    //销毁定时器
    beforeDestroy() {
      clearInterval(this.timer1)
      this.timer1 = null;
    },
  }
</script>

<style scoped lang="less">

.burr_f-haibao{
  width: 100%;
  height: 100%;
  font-size: 14px;
  color: #fff;
  text-align:center;
  position: absolute;
  z-index:999;
  .img {
    width:100%;height:100%;
  }
}



.account-btn{
  margin-top: 16px;
  border:none;
  font-size:16px;
  color:#007AFF;
  text-align: center;
  width:100%;
  background-color:#fff;
  height: 32px;
  border-radius: 20px;
  box-sizing: border-box;
  border: 1px solid #007AFF;
}

  .bottombg {
    //background-image: url(/static/thirdsImg/hailang.png);
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 42px;
    background-color: #ffffff;
  }


  .text1 {
    font-size: 20px;
    display: flex;
    justify-content: center;
    margin-top: 10px;
    font-family: PingFang SC-Regular, PingFang SC;
    /*line-height: 12px;*/
    /*-webkit-background-clip: text;*/
    /*-webkit-text-fill-color: transparent;*/
  }

  .text2 {
    font-size: 12px;
    display: flex;
    justify-content: center;
    margin-top: 10px;
    font-family: PingFang SC-Regular, PingFang SC;
    /*line-height: 12px;*/
    /*-webkit-background-clip: text;*/
    /*-webkit-text-fill-color: transparent;*/
  }

  .qrcode {
    /*width: 50px;*/
    /*height: 50px;*/
    /*position: absolute;*/
    margin-top: 50%;
    /*left: 40%;*/
    /*transform: translateY( -50% );*/
    display: flex;
    justify-content: center;
    /*display: inline-block;/*/

    img {
      width: 132px;
      height: 132px;
      background-color: #fff; //设置白色背景色
      padding: 6px; // 利用padding的特性，挤出白边
      box-sizing: border-box;
    }
  }

.wanggeton {
  margin-top: 55%;
  display: flex;
  justify-content: center;
  margin-bottom:6%;

  img {
    width: 102px;
    height: 102px;
  }
}


  .wrapper { //position: relative;
     height: 100%;
  }

  .content-wrap {
    margin-top: 44px;
    overflow: auto;
    margin-bottom: 76px;
  }

  .product-title {
    color: #232323;
    padding: 0 1rem;
    background-color: #fff;
    height: 48px;
    line-height: 48px;
    margin-bottom: 1rem;

    .left-lbl {
      float: left;
    }

    .right-name {
      float: right;
    }
  }

  .jq-wrapper {
    margin-top: 0.75rem;
    width: 100%;

    background: #fff;

    .title-line {
      height: 42px;
      padding: 0 1rem;
      font-size: 14px;
      color: #484848;
      font-weight: 600;
      line-height: 42px;

      .jianquan {
        color: #F0223B;
        margin-right: 0.5rem;
        font-size: 18px;
      }

    }

    .content-line {
      padding: 0 1rem;
      border-top: 1px solid #F3F3F3;
      height: 48px;
      line-height: 48px;
      color: #232323;
      font-size: 14px;
      box-sizing: border-box;

      .left-lbl {
        float: left;
        width: 30%;
      }

      .right-val {
        width: 70%;
        float: right;

        input {
          border: 0;
          height: 46px;
          width: 100%;
          color: #BBB;
          font-size: 14px;
          text-align: right;

          &:focus {
            outline: none;
          }
        }
      }
    }
  }

  .fix-btn {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 76px;
    background-color: #fff;
    padding: 1rem;
    box-sizing: border-box;

    .submit-btn {
      height: 44px;
      background: rgba(22, 129, 251, 1);
      border-radius: 22px;
      color: #fff;
      width: 100%;
      border: 0;
    }
  }

  .sign-wrapper {
    margin: 0.75rem;
  }

  .yzm-wrapper {
    background: #fff;

    .content-line {
      padding: 0 1rem;
      border-top: 1px solid #F3F3F3;
      height: 48px;
      line-height: 48px;
      color: #232323;
      font-size: 14px;
      box-sizing: border-box;

      .left-lbl {
        float: left;
        width: 30%;
      }

      .right-val, .left-val {
        width: 70%;
        float: right;
        text-align: right;

        input {
          border: 0;
          height: 46px;
          width: 100%;
          color: #BBB;
          font-size: 14px;
          text-align: right;

          &:focus {
            outline: none;
          }
        }
      }

      .left-val {
        float: left;

        input {
          text-align: left;
        }
      }

      .right-btn {
        float: right;
        margin-right: -1rem;
        width: 30%;
        text-align: center;
        height: 46px;
        line-height: 48px;
        color: #fff;
        background: rgba(22, 129, 251, 1);

        &.gray {
          background: #b1adad;
        }
      }
    }
  }
</style>
