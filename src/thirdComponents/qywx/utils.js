export function envjudge() {
  // 添加安全检查
  if (typeof window === 'undefined' || !window.navigator || !window.navigator.userAgent) {
      return 'other';
  }
  var isMobile = window.navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i); // 是否手机端
  var isWx = /micromessenger/i.test(navigator.userAgent); // 是否微信
  var isComWx = /wxwork/i.test(navigator.userAgent); // 是否企业微信
  if (isComWx && isMobile) { //手机端企业微信
    return 'com-wx-mobile'
  }
  else if (isComWx && !isMobile) { //PC端企业微信
    return 'com-wx-pc'
  }
  else if (isWx && isMobile) { // 手机端微信
    return 'wx-mobile';
  }
  else if (isWx && !isMobile) { // PC端微信
    return 'wx-pc';
  }
  else {
    return 'other';
  }
}


/**
 * 生成UUID
 * @param len * UUID长度,默认16
 * @param radix 进制，默认16
 * */
export function getUuid(prefix,len = 16, radix = 16) {//uuid长度以及进制
  const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('');
  const uuid = [];
  for (let i = 0; i < len; i++) uuid[i] = chars[0 | Math.random() * radix];
  return prefix + uuid.join('');
}
