<template>
  <div class='wrapper'>
    <Header :tsTitleTxt='headTitle' backType='custom' @emGoPrev='goPrev'></Header>
    <!-- 信息页 -->
    <div v-show="step=='1'" class='scroll-view'>
      <!-- 已购全家享低消产品 -->
      <div class='module-item white'>
        <div class='module-title line'>
          <div class='module-title-front'>
            <div class='iconfont tiaojianmingcheng'></div>
            <div class='title-txt' v-if='hasBought'>已购全家享低消产品</div>
            <div class='title-txt' v-else>全家享低消产品订购</div>
          </div>
        </div>
        <div v-if='prodList&&prodList.length>0' class='module-content' v-for='(prod,idx) in prodList' :key='idx'>
          <div class='module-content-i'>
            <div class='module-content-item' v-show='originalMeal && originalMeal.offername'>
              <div class='item-key'>资费套餐名称：</div>
              <div class='item-val'>{{ originalMeal.offername }}</div>
            </div>
            <div class='module-content-item' v-show='originalChangXiangMeal && originalChangXiangMeal.offername'>
              <div class='item-key'>畅享套餐名称：</div>
              <div class='item-val'>{{ originalChangXiangMeal.offername }}</div>
            </div>
            <div class='module-content-item' v-show='hasBought'>
              <div class='item-key'>生效时间：</div>
              <div class='item-val'>{{ prod.effectdate | changeDateTime }}</div>
            </div>
            <div class='module-content-item' v-show='hasBought'>
              <div class='item-key'>失效时间：</div>
              <div class='item-val'>{{ prod.expiredate | changeDateTime }}</div>
            </div>
            <div class='module-content-item' v-if='!hasBought'>
              <div class='item-key'>生效类型：</div>
              <select v-model='effectTime' class='option-select'>
                <option value=''>请选择</option>
                <option :value='effectType.id' v-for='effectType of effectTypeList' :key='effectType'>
                  {{ effectType.label }}
                </option>
              </select>
              <div class='item-val'></div>
            </div>
            <div class='module-content-item' v-else>
              <div class='item-key'>生效类型：</div>
              <div class='item-val'>次月</div>
            </div>
          </div>
        </div>
      </div>
      <!-- 低消成员信息 -->
      <div class='module-item blue'>
        <div class='module-title'>
          <div class='module-title-front'>
            <div class='iconfont chengyuan1'></div>
            <div class='title-txt'>低消成员信息</div>
          </div>
          <div class='module-title-beyond'>
            <span class='iconfont jiahao3'></span>
            <span v-if='diXiaoList && diXiaoList.length>0' class='btn-txt' @click="addMember('1','0')">新增</span>
            <span v-else class='btn-txt' @click="addMember('1','1')">新增主号</span>
          </div>
        </div>
        <div v-if='diXiaoList && diXiaoList.length>0' class='module-content' v-for='(member,idx) in diXiaoList'
             :key='idx'>
          <div class='module-content-i white' v-show="member.memacttype != 'D'">
            <span class='iconfont shanchu4 sub-card-footer-right-icon' v-show="member.isprima != '1'"
                  @click='deleteMember(member)'></span>
            <div class='module-content-item'>
              <div class='item-key'>成员号码：</div>
              <div class='item-val'>{{ member.servnumber }}</div>
            </div>
            <div class='module-content-item'>
              <div class='item-key'>户主标识：</div>
              <div class='item-val'>{{ member.isprima | typeParser }}</div>
            </div>
            <div class='module-content-item' v-show='member.memregion'>
              <div class='item-key'>归属地市：</div>
              <div class='item-val'>{{ member.memregion | regionParser }}</div>
            </div>
            <div class='module-content-item' v-show='member.startdate'>
              <div class='item-key'>生效时间：</div>
              <div class='item-val'>{{ member.startdate | changeDateTime }}</div>
            </div>
            <div class='module-content-item' v-show='member.enddate'>
              <div class='item-key'>失效时间：</div>
              <div class='item-val'>{{ member.enddate | changeDateTime }}</div>
            </div>
          </div>
        </div>
        <NoDataPage v-show='!diXiaoList || diXiaoList.length==0' tipTxt='暂无信息'></NoDataPage>
      </div>
      <!-- 畅享卡成员信息 -->
      <div class='module-item blue'>
        <div class='module-title'>
          <div class='module-title-front'>
            <div class='iconfont chengyuan1'></div>
            <div class='title-txt'>畅享卡成员信息</div>
          </div>
          <div class='module-title-beyond'>
            <span class='iconfont jiahao3'></span><span class='btn-txt' @click="addMember('2','0')">新增</span>
          </div>
        </div>
        <div v-if='fuKaList && fuKaList.length>0' class='module-content' v-for='(member,idx) in fuKaList' :key='idx'>
          <div class='module-content-i white' v-show="member.memacttype != 'D'">
            <span class='iconfont shanchu4 sub-card-footer-right-icon' @click='deleteMember(member)'></span>
            <div class='module-content-item'>
              <div class='item-key'>成员号码：</div>
              <div class='item-val'>{{ member.servnumber }}</div>
            </div>
            <div class='module-content-item'>
              <div class='item-key'>户主标识：</div>
              <div class='item-val'>{{ member.isprima | typeParser }}</div>
            </div>
            <div class='module-content-item' v-show='member.memregion'>
              <div class='item-key'>归属地市：</div>
              <div class='item-val'>{{ member.memregion | regionParser }}</div>
            </div>
            <div class='module-content-item' v-show='member.startdate'>
              <div class='item-key'>生效时间：</div>
              <div class='item-val'>{{ member.startdate | changeDateTime }}</div>
            </div>
            <div class='module-content-item' v-show='member.enddate'>
              <div class='item-key'>失效时间：</div>
              <div class='item-val'>{{ member.enddate | changeDateTime }}</div>
            </div>
          </div>
        </div>
        <NoDataPage v-show='!fuKaList || fuKaList.length==0' tipTxt='暂无信息'></NoDataPage>
      </div>
      <!-- 下一步 -->
      <NlButton enableTip='下一步' :defaultState='disableNextFlg' @click='nextStep'></NlButton>

    </div>
    <!-- 结算页 -->
    <div v-show="step=='2'" class='meal-warpper'>
      <!--        <div v-show='!deleteMode' class="smeal-kh">-->
      <div class='smeal-kh'>
        <div class='smeal-kh-tel'>群组成员近三个月账单（单位：元）</div>
      </div>
      <div v-if='memberFeeInfo && memberFeeInfo.length>0' class='smeal-kh' v-for='(userinfo,idx) in memberFeeInfo'
           :key='idx'>
        <div class='smeal-kh-tel'>{{ userinfo.service_number }}
          <div v-show="userinfo.type == '1'" class='st-rlis active' :class="{active:userinfo.type == '1'}">户主</div>
          <div v-show="userinfo.type == '2'" class='st-rlis'>成员</div>
        </div>
        <div v-if='userinfo.feelist && userinfo.feelist.feeinfo && userinfo.feelist.feeinfo.length > 0'
             class='smeal-type' v-for='(feeinfo,i) in userinfo.feelist.feeinfo' :key='i'>
          <div class='st-lefttxt'>{{ feeinfo.bill_month }}</div>
          <div class='st-rigtxt'>
            {{ feeinfo.fee | chgYuan }}
          </div>
        </div>
        <div class='smeal-type'>
          <div class='st-lefttxt'>平均消费</div>
          <div class='st-rigtxt' v-if='userinfo.avgfee'>
            {{ userinfo.avgfee | chgYuan }}
          </div>
          <div class='st-rigtxt' v-else>-</div>
        </div>
        <div class='smeal-type'>
          <div class='st-lefttxt'>新用户当月预计消费</div>
          <div class='st-rigtxt' v-if='userinfo.newuser_fee'>
            {{ userinfo.newuser_fee | chgYuan }}
          </div>
          <div class='st-rigtxt' v-else>-</div>
        </div>
      </div>

      <div class='smeal-kh' v-show="allAvgfee && allAvgfee!=''">
        <div class='smeal-kh-tel'>群组近三个月平均消费（含户主：{{ allAvgfee | chgYuan }}）
        </div>
      </div>

      <div class='smeals-title' @click='showDiXiao = !showDiXiao'>
        <span class='title-left'>低消套餐选择</span>
        <span class='title-right' v-if='!showDiXiao'><span class='iconfont shangzhankai-copy'></span>展开</span>
        <span class='title-right' v-else><span class='iconfont shangzhankai'></span>收起</span>
      </div>
      <div v-show='showDiXiao' class='top-wapper'>
        <div class='search-box'>
          <i class='iconfont sousuo'></i>
          <input class='box' ref='selectDiXiaoKeyWord' v-model='selectDiXiaoKeyWord' placeholder='请输入查询内容' />
          <i @click="clear('1')" v-show='selectDiXiaoKeyWord' class='iconfont shanchu'></i>
          <span @click="queryMeal(selectDiXiaoKeyWord,'1')" class='search-txt'>搜索</span>
        </div>
      </div>
      <div v-show='showDiXiao' class='smeals'>
        <ul class='smeals-ul' v-if='showSubProdList && showSubProdList.length > 0'>
          <li class='smeal-li' v-for='(item,index) in showSubProdList' :key='index'>
            <div class='smeal-li-radio iconfont'
                 :class='{checkboxround0:mealIndex != index,checkboxround1:mealIndex == index}'
                 @click="mealSelect(item,index,'1')">
            </div>
            <div class='smeal-li-lefttxt' @click="mealSelect(item,index,'1')">{{ item.offerName }}</div>
            <div class='smeal-rig'><span class='iconfont gantanhao-yuankuang icondesc'
                                         @click='mealDesc(item.offerId)'></span></div>
          </li>
        </ul>
        <NoDataPage v-show='!showSubProdList || showSubProdList.length==0' tipTxt='暂无信息'></NoDataPage>
      </div>

      <div @click='showChangXiang = !showChangXiang' class='smeals-title' v-show='changXiangFlag'>
        <span class='title-left'>畅享套餐选择</span>
        <span class='title-right' v-if='!showChangXiang'><span class='iconfont shangzhankai-copy'></span>展开</span>
        <span class='title-right' v-else><span class='iconfont shangzhankai'></span>收起</span>
      </div>
      <div class='smeals' v-show='changXiangFlag && showChangXiang'>
        <ul class='smeals-ul' v-if='showEnjoySubProdList && showEnjoySubProdList.length > 0'>
          <li class='smeal-li' v-for='(item,index) in showEnjoySubProdList' :key='index'>
            <div class='smeal-li-radio iconfont'
                 :class='{checkboxround0:changXiangMealIndex != index,checkboxround1:changXiangMealIndex == index}'
                 @click="mealSelect(item,index,'2')">
            </div>
            <div class='smeal-li-lefttxt' @click="mealSelect(item,index,'2')">{{ item.offerName }}</div>
            <div class='smeal-rig'><span class='iconfont gantanhao-yuankuang icondesc'
                                         @click='mealDesc(item.offerId)'></span></div>
          </li>
        </ul>
        <NoDataPage v-show='!showEnjoySubProdList || showEnjoySubProdList.length==0' tipTxt='暂无信息'></NoDataPage>
      </div>
      <NlButton enableTip='推送办理' @click='orderSubmit' :defaultState='disableSubmitFlg'></NlButton>

    </div>

  </div>
</template>

<script>
import NoDataPage from 'components/common/NoDataPage';
import Header from "components/common/Header.vue";
import Storage from '@/base/storage'
import { dateFormat, chgStrToDate } from '@/base/utils'
import { handleOrderReqmixin } from '@/base/handleOrderReqmixin.js'
import NlButton from 'components/common/NlButton';
import { decrptParam } from '@/base/encrptH5.js'


export default {
  mixins: [handleOrderReqmixin],
  components: {
    NoDataPage, Header, NlButton
  },
  data() {
    return {
      effectTime: '',
      headTitle: '全家享低消',
      step: '1',
      telnum: '',
      userInfo: {},
      jqData: {},
      srcFrom: '',
      linkName: '',
      linkPhone: '',
      mealIndex: null,     //低消套餐选择序号,
      changXiangMealIndex: null,     //畅享套餐选择序号,
      prodList: [],
      diXiaoList: [],
      fuKaList: [],
      subProdList: [],//符合条件的全部低消套餐
      showSubProdList: [],//展示的低消套餐
      subEnjoyProdList: [],//符合条件的全部畅享套餐
      showEnjoySubProdList: [],//展示的畅享套餐
      memberFeeInfo: [],
      allAvgfee: '',
      selectDiXiaoKeyWord: '',
      selectChangXiangKeyWord: '',
      currMeal: {},//当前选中的低消套餐
      currChangXiangMeal: {},//当前选中的畅享套餐
      originalMeal: {},//原有低消套餐
      originalChangXiangMeal: {},//原有畅享套餐
      effectTypeList: [{
        id: '2',
        label: '次月生效'
      }, {
        id: '0',
        label: '立即生效'
      }],
      azSubmitUrl: `/xsb/personBusiness/omnipotentvicecard/h5prodOrderAzFinal`,
      busiType: 'family_low_consumption',
      hasBought: false,
      mainInstId: '',
      subOrderOfferIds: [],
      params: {},//校验&&订购传参
      deleteMode: false,//只删除点下一步
      userAllProds: {},//用户所有已购有效产品
      showDiXiao: true,
      showChangXiang: false,
      changXiangFlag: true//畅享包开关
    }
  },
  methods: {
    //返回上一页
    goPrev() {
      if (this.step == '2') {
        this.step = '1';
        this.currMeal = {};
        this.currChangXiangMeal = {};
        this.mealIndex = null;
        this.changXiangMealIndex = null;
        this.deleteMode = false;
      } else {
        this.$router.push('/remoteReceptionMenu');
      }
    },
    nextStep() {
      if (this.step == '1') {
        this.step = '2';
        this.qryMemberFeeInfo();
      }
    },
    //2024.1.15 cyy 查开关
    qryChangXiangSwitch() {
      let param = {
        busiType: 'changxiang_meal' // 畅享包开关
      };
      this.$http.post('/xsb/ability/businessLimit/h5QryBusiPermission', param).then((res) => {
        let { retCode } = res.data;
        if (retCode == '0') {
          this.changXiangFlag = true;
        }
      })
    },
    onlyDelNextStep() {
      this.deleteMode = true;
      this.step = '2';
      console.log('delete next')
      this.selectMeal('');
    },
    deleteMember(member) {
      if (member.memauthchktype && member.memauthchktype != '') {
        //鉴过权说明是刚添加过的，可以直接删除
        if (member.memtype == '32') {
          this.diXiaoList = this.diXiaoList.filter((item) => {
            return item.servnumber != member.servnumber;
          });
        } else {
          this.fuKaList = this.fuKaList.filter((item) => {
            return item.servnumber != member.servnumber;
          });
        }
      } else {
        if (member.isprima == '1') {
          this.$alert('主号号码不可变更');
          return;
        }
        //删除原有的，需要标记
        member.memacttype = 'D';
        //低消
        if (member.memtype == '32') {
          let tmp = this.diXiaoList.filter((item) => {
            return item.servnumber != member.servnumber;
          });
          tmp.push(member);
          this.diXiaoList = tmp;
        } else {
          //副卡
          let tmp = this.fuKaList.filter((item) => {
            return item.servnumber != member.servnumber;
          });
          tmp.push(member);
          this.fuKaList = tmp;
        }
      }
      this.$forceUpdate();
    },
    addMember(opFlag, primaFlag) {
      //低消不能超过5个，副卡不能超过3个
      let diXiaoCount = 0;
      for (const diXiaoMem of this.diXiaoList) {
        if (diXiaoMem.memacttype != 'D') {
          diXiaoCount++;
        }
      }
      if (opFlag == '1' && diXiaoCount >= 5) {
        this.$alert('低消成员不能超过5个');
        return;
      }
      let fuKaCount = 0;
      for (const fuKaMem of this.fuKaList) {
        if (fuKaMem.memacttype != 'D') {
          fuKaCount++;
        }
      }
      if (opFlag == '2' && fuKaCount >= 3) {
        this.$alert('畅享卡成员不能超过3个');
        return;
      }
      //主号不用鉴权直接加
      if (primaFlag == '1') {
        let member = {
          servnumber: this.telnum,
          isprima: primaFlag,
          memregion: this.userInfo.region,
          startdate: '',
          enddate: '',
          memacttype: 'A',
          memtype: '32'
        }
        this.diXiaoList.push(member);
        return;
      }
      this.getUserData(opFlag, primaFlag);
    },
    getUserData(opFlag, primaFlag) {
      this.$messagebox.prompt('请输入手机号', '').then(({ value, action }) => {
        if (/^((1)+\d{10})$/.test(value)) {
          // 号码已添加
          let flg1 = this.diXiaoList.some((item) => {
            if (item.servnumber === value) {
              return true;
            }
          });
          let flg2 = this.fuKaList.some((item) => {
            if (item.servnumber === value) {
              return true;
            }
          });
          if (flg1 || flg2) {
            this.$alert('该号码已添加');
            return;
          }

          // 获取用户地市
          let url = '/xsb/personBusiness/customerView/h5QryCgetusercust';
          this.$http.post(url, { telnum: value }).then(res => {
            let resData = res.data;
            if (typeof (resData) != 'object') {//不是对象的话就是加密串
              resData = decrptParam(resData);
              resData = JSON.parse(resData);
            }
            if (resData.retCode == '0') {
              console.log(resData.data)
              let obj = resData.data

              let member = {
                servnumber: value,
                isprima: primaFlag,
                memregion: obj.userCity,
                startdate: '',
                enddate: '',
                memacttype: 'A',
                memauthchktype: 'AuthCheckR'
              }
              //低消
              if (opFlag == '1') {
                member.memtype = '32';
                if (primaFlag != '1') {
                }
                this.diXiaoList.push(member);
              } else {
                member.memtype = '31';
                //副卡
                this.fuKaList.push(member);
              }
            }
          }).catch(err => {

          })
        } else {
          this.$toast('请输入正确的手机号');
        }
      });
    },
    qryMemberFeeInfo() {
      let url = '/xsb/personBusiness/familyLowConsumption/h5queryAllFamilyFeeInfo';
      let userlist = {};
      let userinfo = [];
      let count = 0;
      for (const diXiaoMem of this.diXiaoList) {
        if (diXiaoMem.memacttype != 'D') {
          //最多查询5个
          if (count >= 5) {
            break;
          }
          userinfo.push({
            service_number: diXiaoMem.servnumber
          });
          count++;
        }
      }
      userlist.userinfo = userinfo;
      let param = {
        userlist: JSON.stringify(userlist)
      };
      console.info('fee param', param)
      this.$http.post(url, param).then(res => {
        let result = res.data;
        if ('0' == result.retCode) {
          let dataObj = result.data;
          console.info('fee', dataObj)
          this.memberFeeInfo = dataObj.userlist.userinfo || [];
          this.allAvgfee = dataObj.all_avgfee;
          for (let userInfo of this.memberFeeInfo) {
            if (userInfo.feelist && userInfo.feelist.feeinfo && userInfo.feelist.feeinfo.length > 0) {
              for (let fee of userInfo.feelist.feeinfo) {
                fee.bill_month = fee.bill_month.substring(0, 4) + '/' + fee.bill_month.substring(4, 6);
              }
              if (userInfo.feelist.feeinfo.length < 3) {
                if (userInfo.feelist.feeinfo.length == 2) {
                  //获取当月的上上个月，yyyyMM
                  let fee = {
                    bill_month: this.getMonth(2),
                    fee: ''
                  }
                  userInfo.feelist.feeinfo.push(fee);
                } else if (userInfo.feelist.feeinfo.length == 1) {
                  //获取当月的上上个月，yyyyMM
                  let fee = {
                    bill_month: this.getMonth(2),
                    fee: ''
                  }
                  userInfo.feelist.feeinfo.push(fee);
                  //获取当月的上上上个月，yyyyMM
                  fee = {
                    bill_month: this.getMonth(3),
                    fee: ''
                  }
                  userInfo.feelist.feeinfo.push(fee);
                }
              }
            } else {
              let feelist = {};
              userInfo.feelist = feelist;
              userInfo.feelist.feeinfo = [];
              //上月
              let fee = {
                bill_month: this.getMonth(1),
                fee: ''
              }
              userInfo.feelist.feeinfo.push(fee);
              //获取当月的上上个月，yyyy/MM
              fee = {
                bill_month: this.getMonth(2),
                fee: ''
              }
              userInfo.feelist.feeinfo.push(fee);
              //获取当月的上上上个月，yyyy/MM
              fee = {
                bill_month: this.getMonth(3),
                fee: ''
              }
              userInfo.feelist.feeinfo.push(fee);
            }
          }
          console.info('fee after change', this.memberFeeInfo);
          this.selectMeal(this.allAvgfee);
        } else {
          this.$alert(result.retMsg || '消费查询失败');
        }
      }).catch((response) => {

      });
    },
    //num为1表示上月 2表示上上月 …
    getMonth(num) {
      if (num <= 0) return;
      let today = new Date();
      let year = today.getFullYear();//当前年份
      let need_year = year - Math.floor(num / 12);//获取到想要查询月所在的年份
      let need_year_next = need_year;//获取到想要查询月下一个月所在的年份
      let month = today.getMonth() + 1;//获取到当前月

      //获取num 的余数  如果余数大于等于当前月份  则年份还要减1
      let num_remainder = num % 12;
      if (num_remainder >= month) {
        need_year--;
        need_year_next--;
        month += 12;
      }

      //获取到所需要的月份和所需要的月份的下一个月
      let need_month = month - num_remainder;
      let need_month_next = need_month + 1;

      //如果所需要月份的下一个月大于12 则需要调到下一年的一月
      if (need_month_next > 12) {
        need_month_next = 1;
        need_year_next++;
      }
      if (need_month === 0) need_month = 12;
      //所需月份小于10 则前面补0
      if (need_month < 10) need_month = '0' + need_month;
      if (need_month_next < 10) need_month_next = '0' + need_month_next;
      // return [`${need_year}/${need_month}`,`${need_year_next}/${need_month_next}`];
      return `${need_year}/${need_month}`;
    },
    queryMeal(keyword, type) {
      if (type == '1') {
        //在subProdList查询offerName包含keyword的套餐
        if (keyword == '') {
          this.showSubProdList = this.subProdList;
          return;
        }
        let tmp = [];
        for (const subProd of this.subProdList) {
          if (subProd.offerName.indexOf(keyword) != -1) {
            tmp.push(subProd);
          }
        }
        this.showSubProdList = tmp;
      } else if (type == '2') {
        //在subEnjoyProdList查询offerName包含keyword的套餐
        if (keyword == '') {
          this.showEnjoySubProdList = this.subEnjoyProdList;
          return;
        }
        let tmp = [];
        for (const subProd of this.subEnjoyProdList) {
          if (subProd.offerName.indexOf(keyword) != -1) {
            tmp.push(subProd);
          }
        }
        this.showEnjoySubProdList = tmp;
      }
      this.$forceUpdate();
    },
    clear(type) {
      if (type == '1') {
        this.selectDiXiaoKeyWord = '';
      } else if (type == '2') {
        this.selectChangXiangKeyWord = '';
      }
    },
    selectMeal(minConsumFee) {
      this.mealIndex = null;
      this.changXiangMealIndex = null;
      this.currMeal = {};
      this.currChangXiangMeal = {};
      let url = `/xsb/personBusiness/familyLowConsumption/h5fmyShareChildOfferQry`;
      let param = {};
      //获取offername里的数字

      let preOfferFee = '';
      if (this.originalMeal && this.originalMeal.offername && this.originalMeal.offername != '') {
        preOfferFee = this.originalMeal.offername.replace(/[^\d]/g, "");
      }

      console.info('preOfferFee', preOfferFee);
      if (minConsumFee && minConsumFee != '') {
        param = {
          offerId: '2400240301',
          minConsumFee: minConsumFee,
          preOfferFee: preOfferFee
        }
      } else {
        param = {
          offerId: '2400240301',
          preOfferFee: preOfferFee
        }
      }
      this.$http.post(url, param).then(res => {
        let result = res.data;
        if ('0' == result.retCode) {
          let dataObj = result.data;
          console.info('meal', dataObj)
          let allProdList = dataObj;
          let diXiaoProds = [];
          let changXiangProds = [];
          for (const item of allProdList) {
            if (item.classificationId == '699111') {
              diXiaoProds.push(item);
            } else if (item.classificationId == '699112') {
              changXiangProds.push(item);
            }
          }
          if (diXiaoProds.length != 0) {
            diXiaoProds.sort(this.sortBy('minConsumFee', 1));
          }
          if (changXiangProds.length != 0) {
            changXiangProds.sort(this.sortBy('offerName', 1));
          }
          this.subProdList = diXiaoProds;
          this.showSubProdList = diXiaoProds;
          this.subEnjoyProdList = changXiangProds;
          this.showEnjoySubProdList = changXiangProds;
          console.info('this.subOrderOfferIds', this.subOrderOfferIds)
          console.info('this.originalChangXiangMeal', this.originalChangXiangMeal)
        } else {
          this.$alert(result.retMsg || '套餐查询失败');
        }
      }).catch((response) => {

      });
    },
    //attr：根据该属性排序；rev：升序1或降序-1，不填则默认为1
    sortBy(attr, rev) {
      if (rev == undefined) {
        rev = 1;
      } else {
        (rev) ? 1 : -1;
      }
      return function(a, b) {
        a = a[attr];
        b = b[attr];
        //如果a和b是数字字符串，转换成数字后比较大小
        if (!isNaN(a) && !isNaN(b)) {
          a = parseFloat(a);
          b = parseFloat(b);
        }
        if (a < b) {
          return rev * -1
        }
        if (a > b) {
          return rev * 1
        }
        return 0;
      }
    },

    //选择套餐点击
    mealSelect(item, index, type) {
      if (type == '1') {
        if (item.offerId == this.currMeal.offerId) {
          this.mealIndex = null;
          this.currMeal = {};
          return;
        }
        this.mealIndex = index;
        this.currMeal = item;
      } else if (type == '2') {
        if (item.offerId == this.currChangXiangMeal.offerId) {
          this.changXiangMealIndex = null;
          this.currChangXiangMeal = {};
          return;
        }
        this.changXiangMealIndex = index;
        this.currChangXiangMeal = item;
      }
    },
    //套餐详情按钮
    mealDesc(offerId) {
      let prodIdList = [{ packageCode: offerId }];
      let url = '/xsb/personBusiness/businessOpenTv/h5QryProdDesc?prodIdList=' + JSON.stringify(prodIdList)
      this.$http
        .get(url)
        .then((res) => {
          if (res.data.retCode == '0') {
            this.$messagebox.alert(res.data.data.prodDesc, '套餐详情').then((action) => {
            })
          } else {
            this.$messagebox.alert(res.data.retMsg || '暂无描述', '套餐详情').then((action) => {
            })
          }
        })
        .catch((response) => {
          this.$alert('查询产品描述异常')
        })
    },
    //校验
    submit() {
      //订购校验
      //参数拼装
      let orderItem = [];
      let item = {};
      //开通
      if (!this.hasBought) {
        item = {
          itemid: '2400240301',
          instanceid: "",
          actiontype: "A",//开通A 变更M
          effectmode: this.effectTime, //生效时间0立即/2次月
          suborderitem: {},
          orderitemmember: []
        }
        for (let diXiaoMem of this.diXiaoList) {
          let member = {
            memservicenumber: diXiaoMem.servnumber,
            memtype: '32',
            memacttype: 'A',
            ishousehold: diXiaoMem.isprima
          }
          if (diXiaoMem.isprima != '1') {
            member.memauthchktype = diXiaoMem.memauthchktype;
          }
          item.orderitemmember.push(member);
        }
        for (let fuKaMem of this.fuKaList) {
          let member = {
            memservicenumber: fuKaMem.servnumber,
            memtype: '31',
            memacttype: 'A',
            ishousehold: fuKaMem.isprima,
            memauthchktype: fuKaMem.memauthchktype
          }
          item.orderitemmember.push(member);
        }
        let suborderitem = {};
        let orderitemList = [];
        if (this.currMeal.offerId) {
          orderitemList.push({
            itemid: this.currMeal.offerId,
            actiontype: 'A',
            effectmode: this.effectTime,
            instanceid: ''
          });
        }
        if (this.changXiangFlag) {
          if (this.currChangXiangMeal.offerId) {
            orderitemList.push({
              itemid: this.currChangXiangMeal.offerId,
              actiontype: 'A',
              effectmode: this.effectTime,
              instanceid: ''
            });
          }
        }
        suborderitem.orderitem = orderitemList;
        item.suborderitem = suborderitem;
      } else {
        //变更
        item = {
          itemid: '2400240301',
          instanceid: this.mainInstId,
          actiontype: "M",//开通A 变更M
          // effectmode: this.effectTime, //生效时间0立即/2次月
          suborderitem: {},
          orderitemmember: []
        }
        for (let diXiaoMem of this.diXiaoList) {
          if (diXiaoMem.memacttype && diXiaoMem.memacttype != '') {
            let member = {
              memservicenumber: diXiaoMem.servnumber,
              memtype: '32',
              memacttype: diXiaoMem.memacttype,
              ishousehold: diXiaoMem.isprima,
              memauthchktype: diXiaoMem.memauthchktype
            }
            item.orderitemmember.push(member);
          }
        }
        for (let fuKaMem of this.fuKaList) {
          if (fuKaMem.memacttype && fuKaMem.memacttype != '') {
            let member = {
              memservicenumber: fuKaMem.servnumber,
              memtype: '31',
              memacttype: fuKaMem.memacttype,
              ishousehold: fuKaMem.isprima,
              memauthchktype: fuKaMem.memauthchktype
            }
            item.orderitemmember.push(member);
          }
        }
        let suborderitem = {};
        let orderitemList = [];
        if (this.currMeal.offerId) {
          console.info('submit originalMeal', this.originalMeal)
          console.info('submit currMeal', this.currMeal)
          orderitemList.push({
            itemid: this.currMeal.offerId,
            actiontype: 'A',
            effectmode: this.effectTime,
            instanceid: ''
          });
          orderitemList.push({
            itemid: this.originalMeal.offerid,
            actiontype: 'D',
            effectmode: this.effectTime,
            instanceid: this.originalMeal.instanceid
          });
        }
        //开关打开
        if (this.changXiangFlag) {
          if (this.currChangXiangMeal.offerId) {
            console.info('submit originalChangXiangMeal', this.originalChangXiangMeal)
            console.info('submit currChangXiangMeal', this.currChangXiangMeal)
            orderitemList.push({
              itemid: this.currChangXiangMeal.offerId,
              actiontype: 'A',
              effectmode: this.effectTime,
              instanceid: ''
            });
            //之前开了的话删掉之前的，没开就只新增
            if (this.originalChangXiangMeal.offerid) {
              orderitemList.push({
                itemid: this.originalChangXiangMeal.offerid,
                actiontype: 'D',
                effectmode: this.effectTime,
                instanceid: this.originalChangXiangMeal.instanceid
              });
            }
          }
        }
        suborderitem.orderitem = orderitemList;
        item.suborderitem = suborderitem;
      }
      console.log('ITEM', item)
      //已选成员加工
      let memberList = [];
      let delmemberList = [];
      if (Array.isArray(item.orderitemmember) && item.orderitemmember.length > 0) {
        //去除主号
        let remoteItemmembe = item.orderitemmember.filter(fItem => fItem.ishousehold !== '1');

        //获取删除成员
        delmemberList = remoteItemmembe.map(item => {
          if (item.memacttype === 'D') {
            return {
              addtype: (item.memtype === '32' ? '低消' : '畅享卡') + '成员号码',
              servnumber: item.memservicenumber
            }
          }
        }).filter(odf => odf)

        //获取添加成员
        memberList = remoteItemmembe.map((item, index) => {
          if (item.memacttype !== 'D') {
            return {
              addtype: (item.memtype === '32' ? '低消' : '畅享卡') + '成员号码',
              primaryId: String(index),
              servnumber: item.memservicenumber
            }
          }
        }).filter(odf => odf)
      }

      //添加主套餐
      let subscribeInfos = [];
      if (this.currMeal.offerName) {
        subscribeInfos.push({
          offername: this.currMeal.offerName,
          offerId: this.currMeal.offerId,
          actiontype: item.actiontype,
          efftype: this.effectTime,
          // 保存手机号信息并缩减
          memberList: memberList
        });
      } else {
        if (this.originalMeal.offername) {
          subscribeInfos.push({
            offername: this.originalMeal.offername,
            offerId: this.originalMeal.offerid,
            actiontype: item.actiontype,
            efftype: '产品名称',
            // 保存手机号信息并缩减
            memberList: memberList
          });
        }
      }

      //添加畅享套餐
      if (this.currChangXiangMeal.offerName) {
        subscribeInfos.push({
          offername: this.currChangXiangMeal.offerName,
          offerId: this.currChangXiangMeal.offerId,
          efftype: this.effectTime || '已选产品'
        });
      } else {
        if (this.originalChangXiangMeal.offername) {
          subscribeInfos.push({
            offername: this.originalChangXiangMeal.offername,
            offerId: this.originalChangXiangMeal.offerid,
            efftype: '产品名称'
          });
        }
      }

      // 获取已选产品
      this.getObjdata({ "subscribeInfos": subscribeInfos, "unsubscribeInfos": delmemberList });

      orderItem.push(item);

      this.params = {
        telnum: this.telnum,
        channelid: '79',
        bizcode: 'ChangeProduct',
        authType: 'AuthCheckR',
        orderItem: JSON.stringify(orderItem),
        busiType: this.busiType,
        offerName: this.currMeal.offerName,
        offerId: this.currMeal.offerId
      }
      console.info('提交的jqData', Storage.session.get('jqData'))
      console.info('submit', this.params)
      let url = '/xsb/personBusiness/omnipotentvicecard/h5OrderCheck';
      this.$http.post(url, this.params).then((res) => {
        if (res.data.retCode == '0') {
          this.creatOrder();
        } else {
          let retMsg = res.data.retMsg || '订购校验服务异常';
          this.$alert(retMsg);
        }
      }).catch((respose) => {
        this.$alert('订购校验网络请求失败' + respose);
      })

    },
    creatOrder() {
      let params = this.params;
      params.isscmtdId = "Y";
      params.clientType = /android|harmony/gi.test(navigator.userAgent) ? 'ANDROID' : 'IOS';
      params.stationId = this.userInfo.stationId;//岗位编码
      params.latitude = Storage.get('latitude');//纬度
      params.longitude = Storage.get('longitude');//经度
      params.location = Storage.get('location');//位置信息;
      params.cardAuthSrl = this.jqData.cardAuthSrl;
      //爱知无纸化
      params.xsgOrderType = "0";
      this.orderParams = params;
      this.addMemberSubmit();
    },
    addMemberSubmit() {
      this.$messagebox({
        title: '温馨提示',
        message: `是否确认添加并推送办理?`,
        cancelButtonText: '取消',
        showCancelButton: true,
        confirmButtonText: '确定',
        closeOnClickModal: false
      }).then((action) => {
        if (action === 'confirm') {
          // 添加已选产品
          this.miniNoticeFlg = 1;
          this.paperlessMod = 1;

          this.offerInfos = {
            offerId: this.currMeal.offerId || this.originalMeal.offerid,
            offerName: this.currMeal.offerName || this.originalMeal.offername
          }

          //发起记录预受理订单请求
          this.$http.post('/xsb/personBusiness/fifthGHandle/h5AddHandleOrder',
            this.handleOrderReq('全家享低消', 'family_low_consumption', '/xsb/personBusiness/omnipotentvicecard/h5prodOrderAzFinal')
          ).then((res) => {
            let { retCode, retMsg } = res.data
            if (retCode === '0') {
              this.backCallHome();
            } else {
              this.$alert(retMsg || `推送办理失败`)
            }
          }).catch((response) => {
            this.$alert(`推送办理连接失败` + response)
          })
        }
      })
    },
    orderSubmit() {
      if (this.currMeal && this.currMeal.offerId && (!this.effectTime || this.effectTime == '')) {
        this.$toast('生效时间不能为空');
        return;
      }
      if (((!this.currMeal || !this.currMeal.offerId)) && (this.effectTime && this.effectTime != '') && !this.hasBought) {
        this.$toast('开通时低消套餐必选');
        return;
      }
      this.submit();
    },
    //查询已购
    qryHasProd() {
      let url = `/xsb/personBusiness/mainProductChange/h5QryOrderRelation?telnum=${this.telnum}`;
      this.$http.get(url).then(res => {
        let result = res.data;
        if ('0' == result.retCode) {
          let dataObj = result.data;
          console.info('dataObj', dataObj)
          let prods1 = dataObj.orderOffers;
          let prods2 = dataObj.unOrderOffers;
          this.userAllProds = Object.assign(prods1, prods2);
          let prod = this.userAllProds[2400240301];
          if (prod) {
            this.prodList.push(prod);
            this.mainInstId = prod.instanceid;
            //找已订购子商品
            for (let orderOffersKey in this.userAllProds) {
              if (this.userAllProds[orderOffersKey].bundleinstid == prod.instanceid && this.userAllProds[orderOffersKey].offerid != '2400240303' && this.userAllProds[orderOffersKey].offername.indexOf('畅享包') == -1) {
                this.originalMeal = dataObj.orderOffers[orderOffersKey];
                break;
              }
            }
            //开关打开
            if (this.changXiangFlag) {
              for (let orderOffersKey in this.userAllProds) {
                if (this.userAllProds[orderOffersKey].bundleinstid == prod.instanceid && this.userAllProds[orderOffersKey].offerid != '2400240303' && this.userAllProds[orderOffersKey].offername.indexOf('畅享包') != -1) {
                  this.originalChangXiangMeal = dataObj.orderOffers[orderOffersKey];
                  break;
                }
              }
            }
            this.hasBought = true;
            //变更只能次月
            this.effectTime = '2';
            this.qryMember();
            console.info('this.originalMeal', this.originalMeal)
            console.info('this.originalChangXiangMeal', this.originalChangXiangMeal)
          } else {
            let prod = {
              offername: '-',
              effectdate: '-',
              expiredate: '-'
            }
            this.prodList.push(prod);
            this.hasBought = false;
          }
        } else {
          this.$alert(result.retMsg || '订购关系查询失败');
        }
      }).catch((response) => {
      });
    },
    //查询成员
    qryMember() {
      let url = `/xsb/personBusiness/familyNetwork/h5QryMemberInfo`;
      let param = {
        telnum: this.telnum,
        pkgprodid: '2400240303'
      }
      this.$http.post(url, param).then(res => {
        let result = res.data;
        if ('0' == result.retCode) {
          let dataObj = result.data;
          console.info('mem', dataObj)
          let memberList = [];
          if (dataObj.mainGroup[0]) {
            if (dataObj.mainGroup[0].membeinfo && dataObj.mainGroup[0].membeinfo.length > 0) {
              memberList = dataObj.mainGroup[0].membeinfo;
            }
          } else {
            if (dataObj.subGroup[0]) {
              if (dataObj.subGroup[0].membeinfo && dataObj.subGroup[0].membeinfo.length > 0) {
                memberList = dataObj.subGroup[0].membeinfo;
              }
            }
          }
          for (let member of memberList) {
            if (member.memtype == '31') {
              //副卡
              this.fuKaList.push(member);
            } else if (member.memtype == '32') {
              //低消
              this.diXiaoList.push(member);
            }
          }
        } else {
          this.$alert(result.retMsg || '成员查询失败');
        }
      }).catch((response) => {

      });
    }
  },
  computed: {
    disableNextFlg() {
      let diXiaoChg = this.diXiaoList.some((item) => {
        return item.memacttype && item.memacttype != '';
      });
      let fuKaChg = this.fuKaList.some((item) => {
        return item.memacttype && item.memacttype != '';
      });
      //变更(成员 || 选择时间 )|| 开通（选择时间&&新增人员）
      if ((this.hasBought && (diXiaoChg || fuKaChg || (this.effectTime && this.effectTime != '')) || (!this.hasBought && (this.effectTime && this.effectTime != '') && this.diXiaoList.length > 0))) {
        return 'enable'
      } else {
        return 'disable'
      }
    },
    disableSubmitFlg() {
      //选择套餐或变更人员
      let diXiaoChg = this.diXiaoList.some((item) => {
        return item.memacttype && item.memacttype != '';
      });
      let fuKaChg = this.fuKaList.some((item) => {
        return item.memacttype && item.memacttype != '';
      });
      //变更
      if (this.hasBought) {
        //选了套餐 || 低消变更 || 副卡变更
        if (((this.currMeal.offerId && this.currMeal.offerId != '') || (this.currChangXiangMeal.offerId && this.currChangXiangMeal.offerId != '') || diXiaoChg || fuKaChg)) {
          return 'enable'
        } else {
          return 'disable'
        }
      } else {
        //开通
        //选了套餐 && 选择了生效时间
        if (((this.currMeal.offerId && this.currMeal.offerId != ''))
          && this.effectTime
          && this.effectTime != '') {
          return 'enable'
        } else {
          return 'disable'
        }
      }
    },
    onlyDelete() {
      let hasAddFlg = this.diXiaoList.some((item) => {
        return item.memacttype == 'A';
      });
      let hasDelFlg = this.diXiaoList.some((item) => {
        return item.memacttype == 'D';
      });
      let flg = !hasAddFlg && hasDelFlg;
      //只有删除成员
      return this.changXiangFlag && flg
    }
  },
  async created() {
    this.userInfo = Storage.session.get("userInfo");
    this.jqData = Storage.session.get('jqData');
    this.telnum = this.jqData.telnum;
    this.srcFrom = this.$route.query.srcFrom || 'csView';
    //查开关
    // this.qryChangXiangSwitch();
    await this.qryHasProd();

  },
  mounted() {

  },
  filters: {
    //改变时间格式
    changeDateTime(val) {
      if (val != undefined && val != '') {
        return dateFormat(chgStrToDate(val), ("yyyy/MM/dd hh:mm:ss"));
      }
    },
    //改变时间格式
    changeDateMonth(val) {
      if (val != undefined && val != '') {
        //前四位是年，后两位是月
        return val.substring(0, 4) + '/' + val.substring(4, 6);
      } else {
        return '-';
      }
    },
    typeParser(val) {
      if (val == '1') {
        return '户主'
      } else {
        return '成员'
      }
    },
    regionParser(val) {
      let regionList = [{ id: '11', label: '苏州' }, { id: '12', label: '淮安' }, {
        id: '13',
        label: '宿迁'
      }, { id: '14', label: '南京' }, { id: '15', label: '连云港' }
        , { id: '16', label: '徐州' }, { id: '17', label: '常州' }, { id: '18', label: '镇江' }, {
          id: '19',
          label: '无锡'
        }, { id: '20', label: '南通' },
        { id: '21', label: '泰州' }, { id: '22', label: '盐城' }, { id: '23', label: '扬州' }, {
          id: '99',
          label: '江苏省'
        }];
      let item = regionList.find(item => item.id == val);
      let name = item ? item.label : '-';
      return name;
    },
    //厘改成元
    chgYuan(val) {
      if (!val) {
        return '-';
      }
      try {
        let money = parseInt(val);
        let moneyStr = (money / 1000).toString() + ' 元';
        return moneyStr;
      } catch (error) {
        return '-';
      }
    }
  },
  watch: {}
}
</script>
<style lang='less' scoped>
@import '../../base/less/variable.less';

.html-wrap {
  background: #FAFAFA;
}

.wrapper {
  background: #FAFAFA;
  padding-top: 10px;

  .scroll-view {
    margin-top: 44px;
    height: calc(100% - 120px);
    margin-bottom: 76px;
    position: relative;
    z-index: 9;

    .module-item {
      border-radius: 5px;
      padding: 10px;
      display: flex;
      flex-direction: column;
      margin: 20px;

      .module-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 0 10px 0;

        .module-title-front {
          display: flex;
          align-items: center;
        }

        .module-title-beyond {
          padding: 5px;
          background-color: #0b7fff;
          border-radius: 8px;

          .jiahao3 {
            color: #ffffff;
            font-size: 14px;
          }

          .btn-txt {
            color: #ffffff;
            font-size: 14px;
            margin-left: 3px;
          }
        }

        .tiaojianmingcheng {
          color: #0b7fff;
          font-size: 20px;
          margin-right: 8px;
        }

        .chengyuan1 {
          color: #0b7fff;
          font-size: 25px;
          margin-right: 8px;
        }

        .title-txt {
          font-weight: bold;
          font-size: 16px;
        }
      }

      .line {
        border-bottom: 2px dashed #f0f0f0;

      }

      .module-content {
        display: flex;
        flex-direction: column;
        font-size: 14px;
        margin-top: 5px;

        .module-content-i {
          padding: 10px;
          border-radius: 5px;
          position: relative;

          .sub-card-footer-right-icon {
            font-size: 18px !important;
            transform: translateY(-50%);
            color: #0b7fff;
            position: absolute;
            top: 15px;
            right: 5px;
          }

          .module-content-item {
            display: flex;
            align-items: center;
            margin-top: 5px;
            margin-bottom: 5px;

            .option-select {
              width: 40%;
              outline: 0;
              font-size: 14px;
              background-color: white;
              border: 1px solid #cecece;
              border-radius: 3px;
              color: #8c9a9a;
              padding: 4px 2px;
            }

            .item-key {
            }

            .item-val {
              color: #959595;
            }
          }
        }

        .white {
          background: #fff;
        }

      }
    }

    .footer {
      background-color: white;
      width: 100%;
      text-align: center;
      z-index: 9999;
      line-height: 76px;
      height: 76px;
      position: fixed;
      bottom: 0;
      padding: 0 12px;
      box-sizing: border-box;

      .op-button {
        background: #fff;
        border-radius: 22px;
        height: 44px;
        width: 48%;
        font-size: 14px;
        color: #424242;
        outline: none;
        border: 1px solid rgba(204, 204, 204, 1);
      }

      .op-button-add {
        border-radius: 22px;
        height: 44px;
        width: 48%;
        font-size: 14px;
        outline: none;
        background: #e66d00;
        border: 1px solid #e66d00;
        color: #fff;
      }

      .op-button-next {
        border-radius: 22px;
        height: 44px;
        width: 48%;
        font-size: 14px;
        outline: none;
        background: #1681FB;
        border: 1px solid #1681FB;
        color: #fff;
      }

      .op-button-undo {
        border-radius: 22px;
        height: 44px;
        width: 48%;
        font-size: 14px;
        outline: none;
        background: rgb(210, 210, 210);
        border: 1px solid rgb(210, 210, 210);
        color: #fff;
      }
    }

    .white {
      background: #fff;
      box-shadow: 0px 3px 10px 0px rgba(86, 125, 244, 0.05);
    }

    .blue {
      background: #D6EAFF;
    }

    .next-btn {
      position: absolute;
      bottom: 0;
      width: 100%;
      background: #fff;
      padding: 10px;
      box-shadow: 0px -3px 10px 0px rgba(86, 125, 244, 0.05);

      .needsclick {
        background: #0b7fff;
        color: #fff;
        border-radius: 5px;
        padding: 10px;
        text-align: center;
        font-size: 16px;
      }
    }
  }

  .meal-warpper {
    margin-top: 34px;
    margin-bottom: 76px;
    height: calc(100% - 34px);
    width: 100%;
    overflow: hidden;
    background: #ECF0FA;
    display: flex;
    flex-direction: column;
  }
}

.smeal-kh {
  background: #fff;
  flex: 0 0 auto;
}

.smeal-kh-tel {
  font-size: 14px;
  font-weight: 800;
  margin: 10px 12px;
  height: 20px;
  color: rgba(40, 40, 40, 1);
  line-height: 20px;
}

.smeal-type {
  height: 25px;
  line-height: 25px;
}

.st-lefttxt {
  height: 25px;
  font-size: 14px;
  margin-left: 12px;
  font-weight: 400;
  float: left;
  color: rgba(35, 35, 35, 1);
  line-height: 25px;
}

.st-rigtxt {
  width: auto;
  float: right;
  height: 25px;
  line-height: 25px;
  margin-right: 12px;
  font-size: 14px;
  color: #2C2C2C;
}

.icondesc {
  font-size: 18px;
  color: #1681FB;
  margin-left: 4px;
}

.smeals {
  flex-grow: 1;
  background: #fff;
  overflow: auto;
  flex-shrink: 1;
}

.smeals-title {
  font-size: 16px;
  height: 40px;
  font-weight: 600;
  border-bottom: 1px solid #EBEBEB;
  text-indent: 12px;
  color: rgba(44, 44, 44, 1);
  line-height: 40px;
  margin-top: 10px;
  background: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .title-right {
    color: #0079FD;
    font-weight: normal;
    font-size: 14px;
    margin-right: 10px;
  }
}

.smeals-ul {
  display: block;
  height: auto;
  overflow: hidden;
}

.smeal-li {
  height: auto;
  border-bottom: 1px solid #EBEBEB;
  line-height: 20px;
  padding-top: 10px;
  padding-bottom: 10px;
  display: flex;
}

.smeal-li-lefttxt {
  color: rgba(44, 44, 44, 1);
  flex-grow: 1;
  margin-left: 4px;
  font-size: 14px;
}

.smeal-rig {
  width: 100px;
  flex-shrink: 0;
  text-align: right;
  margin-right: 16px;
  font-size: 14px;
  color: #A7A7A7;
}

.st-rlis {
  width: 64px;
  height: 22px;
  float: left;
  box-sizing: border-box;
  border-radius: 11px;
  border: 1px solid #D9D9D9;
  color: #525151;
  font-size: 12px;
  text-align: center;
  line-height: 22px;
  margin-left: 10px;
}

.st-rlis.active {
  background: rgba(206, 231, 255, 1);
  border: 1px solid rgba(79, 140, 238, 1);
  color: #007AFF;
}

.sn-btn {
  height: 76px;
  border-top: 1px solid #EDEDED;
  flex-shrink: 0;
  overflow: hidden;
  background: #fff;
}

.sn-submitbtn {
  height: 44px;
  background: rgba(22, 129, 251, 1);
  border-radius: 22px;
  margin-top: 16px;
  margin-left: 16px;
  margin-right: 16px;
  font-size: 14px;
  font-weight: 400;
  text-align: center;
  letter-spacing: 4px;
  color: rgba(255, 255, 255, 1);
  line-height: 44px;
}

.smeal-li-radio {
  width: 20px;
  height: 20px;
  color: #979797;
  margin-left: 12px;
}

.checkboxround1 {
  color: #1680F9;
}

.top-wapper {
  width: 100%;
  background-color: #fff;
  padding: 0.75rem;
  display: flex;
  align-items: center;
  box-sizing: border-box;

  .search-box {
    flex: auto;
    position: relative;
    margin-right: 0.5rem;

    .box {
      width: 100%;
      height: 30px;
      background: rgba(241, 241, 241, 1);
      border-radius: 1rem;
      box-sizing: border-box;
      outline: 0;
      padding-left: 2rem;
      padding-right: 1.5rem;
      overflow: hidden;
      font-size: 12px;

      &::placeholder {
        color: #B2B2B2;
      }
    }

    .sousuo {
      position: absolute;
      left: 0.75rem;
      top: 50%;
      transform: translateY(-50%);
      color: #B2B2B2;
      font-size: 20px;
    }

    .shanchu {
      position: absolute;
      right: 3.5rem;
      top: 50%;
      color: #B2B2B2;
      transform: translateY(-50%);
      font-size: 12px;
    }

    .search-txt {
      position: absolute;
      right: 0.75rem;
      top: 50%;
      color: #007AFF;
      transform: translateY(-50%);
      font-size: 12px;
    }
  }
}
</style>
