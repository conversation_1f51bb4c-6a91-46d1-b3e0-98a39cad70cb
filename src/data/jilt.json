{"h5QryOrdersInfo": {"retCode": "0", "err_msg": "Success", "data": [{"order_info_dt": {"order_id": "1111", "acc_nbr": "17856941469", "service_code": "2488", "booking_id": "18888", "order_type": "1", "offer_id": "8888", "offer_name": "测试业务", "offerremark": "云视讯", "market_sale_id": "88888", "market_sale_name": "测试营销", "amount": "10", "discount_amount": "2", "fact_amount": "8", "pay_status": "1", "delivery_type": "JDPS", "invoice_flag": "0", "create_time": "20200610235959", "remark": "客户留言", "site_name": "取货营业厅名称", "site_id": "999", "order_from": "100", "grab_staff": "1111", "prehold_staff": "2222", "status": "1", "child_channel": "12345678", "org_id": "88888888", "oper_id": "88"}, "order_info_o2o": {"is_oneclick": "1"}, "order_info_wh": {"goto_type": "?", "city_id": "?", "privid": "?", "oper_id": "?", "theoper_id": "?", "rewardlist": "?", "install_addr": "?", "calling_id": "?", "process_time": "20150101235959", "midway_opinion": [{"option_item": {"midway_type": "?", "req_time": "?", "deal_message": "?"}}, {"option_item": {"midway_type": "?", "req_time": "?", "deal_message": "?"}}]}}, {"order_info_dt": {"order_id": "2222", "acc_nbr": "27", "service_code": "2258", "booking_id": "18448", "order_type": "1", "offer_id": "4448", "offer_name": "测试业务2", "offerremark": "备注2", "market_sale_id": "77788", "market_sale_name": "测试营销2", "amount": "20", "discount_amount": "2", "fact_amount": "18", "pay_status": "1", "delivery_type": "OAO", "invoice_flag": "0", "create_time": "20200610235959", "remark": "客户留言233", "site_name": "取货营业厅名称2", "site_id": "944", "order_from": "266", "grab_staff": "444", "prehold_staff": "576", "status": "57", "child_channel": "12345678", "org_id": "88888888", "oper_id": "88"}, "order_info_o2o": {"is_oneclick": "1"}, "order_info_wh": {"goto_type": "?", "city_id": "?", "privid": "?", "oper_id": "?", "theoper_id": "?", "rewardlist": "?", "install_addr": "?", "calling_id": "?", "midway_opinion": [{"option_item": {"midway_type": "?", "req_time": "?", "deal_message": "?"}}, {"option_item": {"midway_type": "?", "req_time": "?", "deal_message": "?"}}]}}]}, "h5OrderCreate": {"retCode": "0", "retMsg": "创建订单成功", "data": null}, "h5GetThrowOrderOrgInfo": {"retCode": "0", "retMsg": "数据获取成功", "data": [{"crmId": "10001", "crmName": "张三", "orgId": "10010", "orgName": "中海大厦"}, {"crmId": "10002", "crmName": "里斯", "orgId": "10011", "orgName": "环宇城"}, {"crmId": "10003", "crmName": "王五", "orgId": "10012", "orgName": "华润苏果"}]}, "h5qryWorkList": {"retCode": "0", "retMsg": null, "data": {"resultCode": "0", "resultDesc": null, "taskInfolist": [{"taskId": "100001", "status": "2", "taskType": "1", "taskName": "xxxx作业任务1", "startDate": "2020/08/06 12:23:12", "endDate": "2020/08/06 12:23:12"}, {"taskId": "100002", "status": "1", "taskType": "2", "taskName": "xxxx作业任务2", "startDate": "2020/08/06 12:23:12", "endDate": "2020/08/06 12:23:12"}, {"taskId": "100003", "status": "3", "taskType": "3", "taskName": "xxxx作业任务3", "startDate": "2020/08/06 12:23:12", "endDate": "2020/08/06 12:23:12"}, {"taskId": "100004", "status": "4", "taskType": "4", "taskName": "xxxx作业任务4", "startDate": "2020/08/06 12:23:12", "endDate": "2020/08/06 12:23:12"}, {"taskId": "100005", "status": "5", "taskType": "5", "taskName": "xxxx作业任务5", "startDate": "2020/08/06 12:23:12", "endDate": "2020/08/06 12:23:12"}]}}, "h5qryWorkTotal": {"retCode": "0", "retMsg": "success", "data": {"resultCode": "0", "resultDesc": "success", "backlogTotal": "8", "backlogCheckl": "3", "backlogDeal": "5", "sendTotal": "4", "sendManrpt": "7", "sendOn": "5", "sendFinish": "4", "doFinish": "7"}}, "h5qryWorkDetail": {"retCode": "0", "retMsg": "success", "data": {"resultCode": "0", "resultDesc": "success", "taskId": "10001", "taskName": " xxxx作业任务1", "taskType": "1", "operName": "张三", "operCont": "xxxxxx内容", "operDate": "2020/08/06 12:23:12", "startDate": "2020/08/06 12:23:12", "endDate": "2020/08/06 12:23:12", "tempId": "100002", "executeInfo": [{"execOrgid": "1000250", "execName": "李四"}]}}, "h5qryWorkExecutionFlow": {"retCode": "0", "retMsg": "success", "data": {"resultCode": "0", "resultDesc": "success", "operList": [{"operName": "张三", "operOrgid": "1001", "operDate": "2020/08/06 12:23:12", "activeId": "1", "tempId": "T00001", "tempName": "考核反馈模板22", "titleList": [{"titleName": "是否达标", "putType": "1", "tempValue": "I001", "tempKey": "是"}]}]}}, "h5qryAttendanceRecord": {"resultCode": "0", "resultDesc": "success", "workinfo": [{"date": "20200804", "userInfo": [{"channelName": "虎踞路营业厅-营业员", "timermsisdn": "13951752221", "timerName": "张三", "userInfo": [{"workType": "0", "workTime": "0830", "workPoint": "虎踞路18号", "distance": "100", "timeError": "0", "locationError": "1"}, {"workType": "2", "workTime": "1730", "workPoint": "虎踞路81号", "distance": "100", "timeError": "0", "locationError": "1"}, {"workType": "2", "workTime": "0830", "workPoint": "虎踞路81号", "distance": "100", "timeError": "0", "locationError": "1"}, {"workType": "3", "workTime": "1730", "workPoint": "虎踞路81号", "distance": "100", "timeError": "0", "locationError": "1"}]}, {"channelName": "虎踞路营业厅-营业员", "timermsisdn": "13951752227", "timerName": "李四", "userInfo": [{"workType": "1", "workTime": "0830", "workPoint": "虎踞路81号", "distance": "100", "timeError": "0", "locationError": "1"}, {"workType": "2", "workTime": "1730", "workPoint": "中海大厦K", "distance": "100", "timeError": "0", "locationError": "1"}]}]}]}, "h5QryViewType": {"retCode": "0", "retMsg": "success", "data": {"key": 1, "viewTypeList": ["4", "3"]}}}