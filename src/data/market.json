{"marketList": {"retCode": "0", "retMsg": "", "data": {"myPackList": [{"region": "14", "operId": "14150759", "marketId": "202011281061", "marketName": "我的新包包1", "levelId": "300004022008", "activeId": "3002096745", "superType": "market", "issueType": "4", "levelName": "0充送2G（3个月）", "activeName": "2016镇江家庭融合套餐促销活动（8折）", "packType": "3", "marketDesc": "缴300元享宽带电视优惠2020年南京不限量套餐升档优惠2020年南京不限量套餐升档优惠"}, {"region": "14", "operId": "14150759", "marketId": "202012281062", "marketName": "我的新包包2", "levelId": "300004022008", "activeId": "3002096745", "superType": "market", "issueType": "4", "levelName": "0充送2G（3个月）", "activeName": "2016镇江家庭融合套餐促销活动（8折）", "packType": "3", "marketDesc": "缴300元享宽带电视优惠2020年南京不限量套餐升档优惠2020年南京不限量套餐升档优惠"}], "packList": [{"region": "14", "operId": null, "marketId": "202012311007", "marketName": "只有批次(高校)", "levelId": "300003850851", "activeId": "3002099443", "superType": "market", "issueType": "1", "levelName": "购机送新号", "activeName": "一级自有电渠飞享统一合约（25%话补）", "packType": "1", "marketDesc": "缴300元享宽带电视优惠2020年南京不限量套餐升档优惠2020年南京不限量套餐升档优惠"}, {"region": "14", "operId": null, "marketId": "202011191006", "marketName": "只有两个奖品包（高校 ）", "levelId": "3002096494", "activeId": "3002097500", "superType": "market", "issueType": "1", "levelName": "0充送200分钟（6个月）", "activeName": "2017全省4G套餐升档优惠", "packType": "3", "marketDesc": "缴300元享宽带电视优惠2020年南京不限量套餐升档优惠2020年南京不限量套餐升档优惠"}, {"region": "14", "operId": null, "marketId": "202011161004", "marketName": "没有奖品哦", "levelId": "300003850851", "activeId": "3002099443", "superType": "market", "issueType": "1", "levelName": "购机送新号", "activeName": "一级自有电渠飞享统一合约（25%话补）", "packType": "2", "marketDesc": "缴300元享宽带电视优惠2020年南京不限量套餐升档优惠2020年南京不限量套餐升档优惠"}, {"region": "14", "operId": null, "marketId": "202011211009", "marketName": "20201121包包", "levelId": "300004022008", "activeId": "3002096745", "superType": "market", "issueType": "1", "levelName": "0充送2G（3个月）", "activeName": "2016镇江家庭融合套餐促销活动（8折）", "packType": "3", "marketDesc": "缴300元享宽带电视优惠2020年南京不限量套餐升档优惠2020年南京不限量套餐升档优惠"}, {"region": "14", "operId": "14150759", "marketId": "202011161003", "marketName": "我自个的3包无奖品", "levelId": "300003850851", "activeId": "3002099443", "superType": "market", "issueType": "2", "levelName": "购机送新号", "activeName": "一级自有电渠飞享统一合约（25%话补）", "packType": "2", "marketDesc": "缴300元享宽带电视优惠2020年南京不限量套餐升档优惠2020年南京不限量套餐升档优惠"}, {"region": "99", "operId": null, "marketId": "201904021100", "marketName": "测试", "levelId": null, "activeId": "3002099979", "superType": "market", "issueType": "3", "levelName": null, "activeName": "2020年南京不限量套餐升档优惠", "packType": "1", "marketDesc": "缴300元享宽带电视优惠2020年南京不限量套餐升档优惠2020年南京不限量套餐升档优惠"}, {"region": "99", "operId": null, "marketId": "202012051083", "marketName": "TEST全省", "levelId": "300004021971", "activeId": "3002099449", "superType": "market", "issueType": "3", "levelName": "0充送300分钟（6个月）", "activeName": "购机评价送流量", "packType": "2", "marketDesc": "缴300元享宽带电视优惠2020年南京不限量套餐升档优惠2020年南京不限量套餐升档优惠"}, {"marketId": "202012041820", "operId": null, "region": "99", "levelId": "2000001683", "issueType": "3", "packType": "3", "rewardArr": [{"region": "14", "marketId": "202012041820", "rewardPakname": "产品包", "rewardName": "当月免费套餐", "machineId": "", "rewardPakid": "2013000001", "needSn": "1", "rewardId": "2011002057"}], "marketName": "标准资费包", "activeId": "2000001683", "superType": "band_school", "activeName": "宽带标准资费", "levelName": null, "marketDesc": "缴300元享宽带电视优惠2020年南京不限量套餐升档优惠2020年南京不限量套餐升档优惠"}, {"region": "99", "operId": null, "marketId": "201904021101", "marketName": "测试", "levelId": "300004021912", "activeId": "3002099423", "superType": "market", "issueType": "3", "levelName": "充100送100元和包券（4个月）", "activeName": "2020年小积分大转盘抽奖活动", "packType": "2", "marketDesc": "缴300元享宽带电视优惠2020年南京不限量套餐升档优惠2020年南京不限量套餐升档优惠"}, {"region": "99", "operId": null, "marketId": "202012110929", "marketName": "缴300元享宽带电视优惠", "levelId": "300004034561", "activeId": "3002101748", "superType": "market", "issueType": "3", "levelName": "缴300元享宽带电视优惠（12个月）", "activeName": "缴300元享宽带电视优惠", "packType": "3", "marketDesc": "缴300元享宽带电视优惠2020年南京不限量套餐升档优惠2020年南京不限量套餐升档优惠"}, {"marketId": "202012041822", "operId": null, "region": "99", "levelId": "2000009174", "issueType": "3", "packType": "3", "rewardArr": [{"region": "14", "marketId": "202012041822", "rewardPakname": "产品包", "rewardName": "10元提速包（20M提至50M）", "machineId": "", "rewardPakid": "2013000001", "needSn": "1", "rewardId": "2000007857"}, {"region": "14", "marketId": "202012041822", "rewardPakname": "产品包", "rewardName": "手机代付费宽带竣工套餐", "machineId": "", "rewardPakid": "2013000001", "needSn": "1", "rewardId": "2000002748"}], "marketName": "50元包月最低消费", "activeId": "2000009174", "superType": "band", "activeName": "50元包月（100M，手机最低消费8元）", "levelName": null, "marketDesc": "缴300元享宽带电视优惠2020年南京不限量套餐升档优惠2020年南京不限量套餐升档优惠"}], "bannerPackList": [{"region": "14", "operId": "14150759", "marketId": "202011161003", "marketName": "我自个的3包无奖品", "levelId": "300003850851", "activeId": "3002099443", "superType": "market", "issueType": "2", "levelName": "购机送新号", "activeName": "一级自有电渠飞享统一合约（25%话补）", "packType": "2", "marketDesc": "缴300元享宽带电视优惠2020年南京不限量套餐升档优惠2020年南京不限量套餐升档优惠"}, {"region": "14", "operId": "14150759", "marketId": "202011161003", "marketName": "我自个的3包无奖品", "levelId": "300003850851", "activeId": "3002099443", "superType": "market", "issueType": "2", "levelName": "购机送新号", "activeName": "一级自有电渠飞享统一合约（25%话补）", "packType": "2", "marketDesc": "缴300元享宽带电视优惠2020年南京不限量套餐升档优惠2020年南京不限量套餐升档优惠"}, {"region": "14", "operId": "14150759", "marketId": "202011161003", "marketName": "我自个的3包无奖品", "levelId": "300003850851", "activeId": "3002099443", "superType": "market", "issueType": "2", "levelName": "购机送新号", "activeName": "一级自有电渠飞享统一合约（25%话补）", "packType": "2", "marketDesc": "缴300元享宽带电视优惠2020年南京不限量套餐升档优惠2020年南京不限量套餐升档优惠"}, {"region": "14", "operId": "14150759", "marketId": "202011161003", "marketName": "我自个的3包无奖品", "levelId": "300003850851", "activeId": "3002099443", "superType": "market", "issueType": "2", "levelName": "购机送新号", "activeName": "一级自有电渠飞享统一合约（25%话补）", "packType": "2", "marketDesc": "缴费300元享宽带电视优惠2020年南京不限量套餐升档优惠2020年南京不限量套餐升档优惠"}]}}, "bandMustProd": {"retCode": "0", "retMsg": "", "data": [{"effecttype": "0", "ispackage": "0", "pkgprodid": "2013000001", "prodid": "2413000030", "prodname": "智能组网基础服务"}, {"effecttype": "0", "ispackage": "0", "pkgprodid": "2013000001", "prodid": "2413000031", "prodname": "智能组网基础服务2"}]}, "rongHeBanner": {"retCode": "0", "retMsg": "", "data": [{"marketId": "201904021100", "marketName": "测试", "superType": "market"}, {"marketId": "202012041820", "marketName": "标准资费包", "superType": "band"}, {"marketId": "201905011403", "marketName": "缴300元享宽带电视融合包", "superType": "r<PERSON><PERSON>"}]}, "rongHeList": {"retCode": "0", "retMsg": "", "data": [{"region": "14", "operId": "", "marketId": "201905011403", "marketName": "缴300元享宽带电视融合包", "prodList": [{"rewardArr": [{"region": "14", "marketId": "201905011403", "rewardPakname": "宽带电视优惠包(100M)", "rewardName": "宽带电视优惠包(100M)", "machineId": "", "rewardPakid": "8830017393", "needSn": "0", "rewardId": "2000009034"}], "levelId": "300004034561", "activeId": "3002101748", "levelName": "缴300元享宽带电视优惠（12个月）", "activeName": "缴300元享宽带电视优惠（12个月）", "memberType": "2"}, {"rewardArr": [{"region": "14", "marketId": "201905011403", "rewardPakname": "产品包", "rewardName": "当月免费套餐", "machineId": "", "rewardPakid": "2013000001", "needSn": "0", "rewardId": "2011002057"}, {"region": "14", "marketId": "201905011403", "rewardPakname": "产品包", "rewardName": "宽带标准资费", "machineId": "", "rewardPakid": "2013000001", "needSn": "0", "rewardId": "2000001683"}], "levelId": "2000001683", "activeId": "2013000001", "levelName": "宽带标准资费", "activeName": "宽带标准资费", "memberType": "6"}], "superType": "r<PERSON><PERSON>", "issueType": "1", "packType": "3", "marketDesc": "套餐费0元/月，按照0.05元/分，每次不足一分钟按一分钟计算，每月宽带使用费100元封顶。\n    预缴300元获赠180元话费，协议期1年内承诺开通\"宽带电视优惠包(100M)\"套餐。自宽带成功安装次月生效起，预缴的300元分12个月每月释放25元，赠送的180元"}, {"region": "14", "operId": "", "marketId": "201905011402", "marketName": "电视促销融合包", "prodList": [{"rewardArr": [], "levelId": "300004003303", "activeId": "3002099387", "levelName": "电视月返10元", "activeName": "电视促销活动", "memberType": "2"}, {"rewardArr": [{"region": "14", "marketId": "201905011402", "rewardPakname": "产品包", "rewardName": "当月免费套餐", "machineId": "", "rewardPakid": "2013000001", "needSn": "0", "rewardId": "2011002057"}, {"region": "14", "marketId": "201905011402", "rewardPakname": "产品包", "rewardName": "宽带标准资费", "machineId": "", "rewardPakid": "2013000001", "needSn": "0", "rewardId": "2000001683"}], "levelId": "2000001683", "activeId": "2013000001", "levelName": "宽带标准资费", "activeName": "宽带标准资费", "memberType": "6"}], "superType": "r<PERSON><PERSON>", "issueType": "1", "packType": "3", "marketDesc": "优惠提醒用户办理互联网电视套餐，赠送120元话费，每月返还10元，返还12个月。\n    套餐费0元/月，按照0.05元/分，每次不足一分钟按一分钟计算，每月宽带使用费100元封顶。"}, {"region": "14", "operId": "", "marketId": "201905011401", "marketName": "4G用户存量融合包", "prodList": [{"rewardArr": [{"region": "14", "marketId": "201905011401", "rewardPakname": "奖品包_50元及以上个人自选流量包（流量活动）", "rewardName": "4G自选套餐流量包50元（2020版）", "machineId": "", "rewardPakid": "4620047", "needSn": "0", "rewardId": "2000009634"}], "levelId": "300004000472", "activeId": "3002099189", "levelName": "充50送120（12个月）", "activeName": "4G用户存量保有活动", "memberType": "2"}, {"rewardArr": [{"region": "14", "marketId": "201905011401", "rewardPakname": "产品包", "rewardName": "当月免费套餐", "machineId": "", "rewardPakid": "2013000001", "needSn": "0", "rewardId": "2011002057"}, {"region": "14", "marketId": "201905011401", "rewardPakname": "产品包", "rewardName": "50元包月（100M，手机最低消费8元）", "machineId": "", "rewardPakid": "2013000001", "needSn": "0", "rewardId": "2000009174"}, {"region": "14", "marketId": "201905011401", "rewardPakname": "产品包", "rewardName": "手机代付费宽带竣工套餐", "machineId": "", "rewardPakid": "2013000001", "needSn": "0", "rewardId": "2000002748"}], "levelId": "2000009174", "activeId": "2013000001", "levelName": "50元包月（100M，手机最低消费8元）", "activeName": "50元包月（100M，手机最低消费8元）", "memberType": "6"}], "superType": "r<PERSON><PERSON>", "issueType": "1", "packType": "3", "marketDesc": "1.充50送120，50元立即到账，协议期12个月，每月释放10元，用户需保证协议期内开通20元及以上自选流量包（含不限量）不关闭。 2.充50送240，50元立即到账，协议期12个月，每月释放20元，用户需保证协议期内开通50元及以上自选流量包（含不限量）不关闭。\n    3.餐费50元,接入速率100M,不限时。套餐费按日分摊，月底补齐。手机月最低消费8元（不含宽带、互联网电视、IMS固话费），不足月底补齐。套餐资费有效期自开通之日起一年，到期后若无新调整则按年顺延。本套餐费/固定费平摊至每日收取。"}]}, "levelOrReward": {"retCode": "0", "retMsg": "", "data": {"packType": "3", "list": [{"region": null, "rewardPakname": "4G内容权益包二选一/咪咕视频会员15元", "rewardName": "4G内容权益包串号", "machineId": "", "rewardPakid": "8000085818", "needSn": "1", "rewardId": "2000009573"}, {"region": null, "rewardPakname": "4G内容权益包二选一/咪咕视频会员15元", "rewardName": "咪咕视频会员包月15元", "machineId": "", "rewardPakid": "8000085818", "needSn": "0", "rewardId": "2400000422"}, {"region": null, "rewardPakname": "测试奖品包1", "rewardName": "包1包下子产品1", "machineId": "", "rewardPakid": "1000001", "needSn": "0", "rewardId": "10000011"}]}}, "calSuperfee": {"retCode": "0", "retMsg": null, "data": [{"marketId": "202011191006", "rewardArr": [{"region": null, "rewardPakname": "4G内容权益包二选一/咪咕视频会员15元", "rewardName": "咪咕视频会员包月15元", "machineId": "", "rewardPakid": "8000085818", "needSn": "0", "rewardId": "2400000422"}], "isdevicefee": "0", "totalFee": 3000, "retCode": "0", "marketName": "只有两个奖品包", "levelId": "3002096494", "activeId": "3002097500", "superType": "market", "paytype": "0", "retMsg": "Success", "feelist": [{"charegename": "现金充值", "chargecode": "CashPay", "chargemoney": "3000"}, {"charegename": "专有帐户预存", "chargecode": "SpecialPay", "chargemoney": "0"}]}, {"marketId": "202012041822", "isdevicefee": "1", "crditFee": "0", "levelId": "2000009174", "rewardArr": [{"marketId": "202012041822", "region": "14", "rewardPakname": "产品包", "rewardName": "10元提速包（20M提至50M）", "machineId": "", "rewardPakid": "2013000001", "needSn": "1", "rewardId": "2000007857"}, {"marketId": "202012041822", "region": "14", "rewardPakname": "产品包", "rewardName": "手机代付费宽带竣工套餐", "machineId": "", "rewardPakid": "2013000001", "needSn": "1", "rewardId": "2000002748"}], "adress": "测试地址", "retCode": "0", "totalFee": 5000, "installDate": "2019-05-30", "marketName": "50元包月（100M，手机最低消费8元）", "activeId": "2000009174", "superType": "band", "paytype": "0", "feelist": [{"charegename": "宽带接入费", "chargecode": "3206", "chargemoney": "0"}, {"charegename": "家庭终端调试费", "chargecode": "DeviceTestFee", "chargemoney": "5000"}], "linkName": "测试"}]}, "QryTelnumList": {"retCode": "0", "retMsg": null, "data": {"telnumlistBo": [{"telnum": "13684736229", "telprice": "0"}, {"telnum": "13684736519", "telprice": "200"}, {"telnum": "13684736719", "telprice": "200"}, {"telnum": "13684736519", "telprice": "200"}, {"telnum": "13684736319", "telprice": "0"}, {"telnum": "13684736419", "telprice": "200"}, {"telnum": "13684736219", "telprice": "0"}]}}, "superCommit": {"retCode": "0", "retMsg": null, "data": [{"recoid": "200113047779136161", "retCode": "0", "marketName": "我的包没有奖品哦", "superType": "market", "retMsg": "Success", "telnum": "", "marketDate": "2019/05/29 17:45:57"}, {"recoid": "200113047779136162", "retCode": "0", "marketName": "50元包月", "superType": "band", "retMsg": "Success", "telnum": "", "marketDate": "2019/05/29 17:45:57"}, {"recoid": "200113047779136163", "retCode": "0", "marketName": "最低消费50元", "superType": "market", "retMsg": "Success", "telnum": "", "marketDate": "2019/05/29 17:45:57"}]}, "qryIotGood": {"retCode": "0", "retMsg": null, "data": [{"goodId": "2000006466", "goodName": "全国通用流量套餐（套外：0.29元/MB）", "goodDesc": "全国通用流量套餐（套外：0.29元/MB）", "goodPrice": "0", "region": null, "crmId": null, "state": null, "createTime": null, "operateTime": null, "parentId": null, "parentName": null, "type": "1", "sonGoods": [{"goodId": "200000646640", "goodName": "40元档次", "goodDesc": "40元档次", "goodPrice": "40", "region": null, "crmId": null, "state": null, "createTime": null, "operateTime": null, "parentId": "2000006466", "parentName": "全国通用流量套餐（套外：0.29元/MB）", "type": "2", "sonGoods": null}, {"goodId": "2000006466100", "goodName": "100元档次", "goodDesc": "100元档次", "goodPrice": "100", "region": null, "crmId": null, "state": null, "createTime": null, "operateTime": null, "parentId": "2000006466", "parentName": "全国通用流量套餐（套外：0.29元/MB）", "type": "2", "sonGoods": null}, {"goodId": "200000646680", "goodName": "80元档次", "goodDesc": "80元档次", "goodPrice": "80", "region": null, "crmId": null, "state": null, "createTime": null, "operateTime": null, "parentId": "2000006466", "parentName": "全国通用流量套餐（套外：0.29元/MB）", "type": "2", "sonGoods": null}, {"goodId": "200000646660", "goodName": "60元档次", "goodDesc": "60元档次", "goodPrice": "60", "region": null, "crmId": null, "state": null, "createTime": null, "operateTime": null, "parentId": "2000006466", "parentName": "全国通用流量套餐（套外：0.29元/MB）", "type": "2", "sonGoods": null}]}, {"goodId": "2600001228", "goodName": "物联网机卡分离", "goodDesc": "物联网机卡分离", "goodPrice": "0", "region": null, "crmId": null, "state": null, "createTime": null, "operateTime": null, "parentId": null, "parentName": null, "type": "0", "sonGoods": null}, {"goodId": "2000007727", "goodName": "通用流量用尽关停产品", "goodDesc": "通用流量用尽关停产品", "goodPrice": "0", "region": null, "crmId": null, "state": null, "createTime": null, "operateTime": null, "parentId": null, "parentName": null, "type": "0", "sonGoods": null}, {"goodId": "2000006486", "goodName": "全省通用流量套餐（套外：0.89元/MB）", "goodDesc": "全省通用流量套餐（套外：0.89元/MB）", "goodPrice": "0", "region": null, "crmId": null, "state": null, "createTime": null, "operateTime": null, "parentId": null, "parentName": null, "type": "1", "sonGoods": [{"goodId": "210000646640", "goodName": "40元档次", "goodDesc": "40元档次", "goodPrice": "40", "region": null, "crmId": null, "state": null, "createTime": null, "operateTime": null, "parentId": "2000006486", "parentName": "全国通用流量套餐（套外：0.29元/MB）", "type": "2", "sonGoods": null}, {"goodId": "2010006466100", "goodName": "100元档次", "goodDesc": "100元档次", "goodPrice": "100", "region": null, "crmId": null, "state": null, "createTime": null, "operateTime": null, "parentId": "2000006486", "parentName": "全国通用流量套餐（套外：0.29元/MB）", "type": "2", "sonGoods": null}, {"goodId": "200300646680", "goodName": "80元档次", "goodDesc": "80元档次", "goodPrice": "80", "region": null, "crmId": null, "state": null, "createTime": null, "operateTime": null, "parentId": "2000006486", "parentName": "全国通用流量套餐（套外：0.29元/MB）", "type": "2", "sonGoods": null}]}]}, "h5getRecomdActList": {"retCode": "0", "retMsg": "null", "data": {"list": [{"actId": "3002096589", "actName": "2016年南京宽带99包年系列优惠活动", "actDesc": "null", "type": "0"}, {"actId": "3002097347", "actName": "2017春季校园统一营销活动（主卡）", "actDesc": "null", "type": "0"}, {"actId": "3002096594", "actName": "2016年南京10M家庭宽带优惠活动", "actDesc": "null", "type": "0"}, {"actId": "300004021912", "actName": "钟的个人营销案", "actDesc": "null", "type": "1"}, {"actId": "3002096494", "actName": "钟娜娜的个人营销案", "actDesc": "null", "type": "1"}]}}, "h5getActListAll": {"retCode": "0", "retMsg": "nn", "data": {"total": 387, "list": [{"actId": "3002096589", "actName": "2016年南京宽带99包年系列优惠活动", "actDesc": "充值指定金额，协议期内开通指定套餐，获赠相应话费。", "type": "0"}, {"actId": "3002097347", "actName": "终端营销案001", "actDesc": "校园用户新办理副号（必开4G飞享套餐28元（校园）），主号连续13个月（协议期12个月及办理当月）每月获赠20元话费，主号必开60元及以上校园4G飞享套餐，副号费用由主号全额代付。", "type": "3"}, {"actId": "3002096594", "actName": "2016年南京10M家庭宽带优惠活动", "actDesc": "开通指定套餐，可使用12个月家庭宽带。", "type": "0"}, {"actId": "300004021912", "actName": "钟的个人营销案", "actDesc": "", "type": "1"}, {"actId": "3002096494", "actName": "钟娜娜的个人营销案", "actDesc": "null", "type": "1"}, {"actId": "3002099979", "actName": "2020年南京不限量套餐升档优惠", "actDesc": "已开通优惠提醒的特邀客户升至不限量自选套餐，协议期6个月内，每月送20-70元相应档次话费；升至158/198家庭不限量套餐，协议期12个月内，每月送30-70元相应档次话费。活动办理当月均有相应档次省内流量赠送。", "type": "2"}, {"actId": "3002099729", "actName": "终端营销案001", "actDesc": "月送话费10元，协议期为6/12个月，用户流量套餐价值30及以上。", "type": "3"}, {"actId": "3002099423", "actName": "2020年小积分大转盘抽奖活动", "actDesc": "“88积分”抽大奖：客户进入网厅、掌厅、短厅活动专区，可使用88积分兑换一次抽奖，当月连续兑换3次即赠送1次抽奖机会。跨月不累计，赠送的抽奖机会月底清零", "type": "2"}, {"actId": "3002099443", "actName": "一级自有电渠飞享统一合约（25%话补）", "actDesc": "用户购买手机，开通58/88/138/158/238/268/338/588飞享套餐，根据12/24个月协议期返还25%回报率的话费金额。", "type": "2"}, {"actId": "3002099450", "actName": "2020南京零售换机优惠活动", "actDesc": "客户购买指定4G+手机，充值100/200/300元金额话费号码承诺自活动生效当月起，协议期内须开通20/30/50元及以上4G自选套餐（含不限量及同等流量价值的家庭至尊套餐），且达到30/60/90最低消费，即可获赠相应档次的100/200/300购机优惠。", "type": "2"}]}}, "h5analysisActReward": {"retCode": "0", "retMsg": "搜索成功", "data": {"pageInfo": {"beginrownumber": "0", "totalrecord": "23", "curpage": null, "recordperpage": "50"}, "rewardlist": [{"rewardid": "JSYD-HUAWEI-SIM-AL00-05", "rewardname": "华为SIM-AL00[粉]", "ispkg": "0", "rewarddesc": null, "band": "hua<PERSON>", "color": "粉", "memory": "", "type": "SIM-AL00", "invId": "123456"}, {"rewardid": "JSYD-XTC-W1923AC-06", "rewardname": "小天才W1923AC[红]", "ispkg": "0", "rewarddesc": null, "band": null, "color": "红", "memory": "", "type": "小天才W1923AC", "invId": null}, {"rewardid": "JSYD-XTC-W1818AC-13", "rewardname": "小天才W1818AC[绿]", "ispkg": "0", "rewarddesc": null, "band": null, "color": "绿", "memory": "", "type": "小天才W1818AC", "invId": null}, {"rewardid": "JSYD-XIAOLAJIAO-20171225-05-4G-A", "rewardname": "小辣椒20171225[粉-4G-512M]", "ispkg": "0", "rewarddesc": null, "band": null, "color": "粉", "memory": "512M+4G", "type": "小辣椒20171225", "invId": null}, {"rewardid": "JSYD-HUAWEI-SIM-AL00-04", "rewardname": "华为SIM-AL00[蓝]", "ispkg": "0", "rewarddesc": null, "band": "hua<PERSON>", "color": "蓝", "memory": "", "type": "SIM-AL00", "invId": null}, {"rewardid": "JSYD-XTC-XTCZ5AQ-13", "rewardname": "小天才XTCZ5Aq[绿]", "ispkg": "0", "rewarddesc": null, "band": null, "color": "绿", "memory": "", "type": "小天才XTCZ5Aq", "invId": null}, {"rewardid": "JSYD-XTC-W1920AC-13", "rewardname": "小天才W1920AC[绿]", "ispkg": "0", "rewarddesc": null, "band": null, "color": "绿", "memory": "", "type": "小天才W1920AC", "invId": null}, {"rewardid": "JSYD-XTC-W1923AC-13", "rewardname": "小天才W1923AC[绿]", "ispkg": "0", "rewarddesc": null, "band": null, "color": "绿", "memory": "", "type": "小天才W1923AC", "invId": null}, {"rewardid": "JSYD-XTC-W1818AC-12", "rewardname": "小天才W1818AC[紫]", "ispkg": "0", "rewarddesc": null, "band": null, "color": "紫", "memory": "", "type": "小天才W1818AC", "invId": null}, {"rewardid": "JSYD-XTC-W2028AC-04", "rewardname": "小天才W2028AC[蓝]", "ispkg": "0", "rewarddesc": null, "band": null, "color": "蓝", "memory": "", "type": "小天才W2028AC", "invId": null}, {"rewardid": "JSYD-APPLE-A2008-10-16G-1", "rewardname": "苹果A2008[银-16G-1G]", "ispkg": "0", "rewarddesc": null, "band": "apple", "color": "银", "memory": "1G+16G", "type": "A2008", "invId": null}, {"rewardid": "JSYD-APPLE-A2008-11-16G-1", "rewardname": "苹果A2008[金-16G-1G]", "ispkg": "0", "rewarddesc": null, "band": "apple", "color": "金", "memory": "1G+16G", "type": "A2008", "invId": null}, {"rewardid": "JSYD-APPLE-A2007-10-16G-1", "rewardname": "苹果A2007[银-16G-1G]", "ispkg": "0", "rewarddesc": null, "band": "apple", "color": "银", "memory": "1G+16G", "type": "A2007", "invId": null}, {"rewardid": "JSYD-CMCC-CMCC-CW1A-05-4G-A", "rewardname": "中国移动CMCC-CW1A[粉-4G-512M]", "ispkg": "0", "rewarddesc": null, "band": null, "color": "粉", "memory": "512M+4G", "type": "中国移动CMCC-CW1A", "invId": null}, {"rewardid": "JSYD-XIAOLAJIAO-20171225-04-4G-A", "rewardname": "小辣椒20171225[蓝-4G-512M]", "ispkg": "0", "rewarddesc": null, "band": null, "color": "蓝", "memory": "512M+4G", "type": "小辣椒20171225", "invId": null}, {"rewardid": "JSYD-APPLE-A2007-03-16G-1", "rewardname": "苹果A2007[灰-16G-1G]", "ispkg": "0", "rewarddesc": null, "band": "apple", "color": "灰", "memory": "1G+16G", "type": "A2007", "invId": null}, {"rewardid": "JSYD-APPLE-A2007-11-16G-1", "rewardname": "苹果A2007[金-16G-1G]", "ispkg": "0", "rewarddesc": null, "band": "apple", "color": "金", "memory": "1G+16G", "type": "A2007", "invId": null}, {"rewardid": "JSYD-APPLE-A2008-03-16G-1", "rewardname": "苹果A2008[灰-16G-1G]", "ispkg": "0", "rewarddesc": null, "band": "apple", "color": "灰", "memory": "1G+16G", "type": "A2008", "invId": null}, {"rewardid": "JSYD-CMCC-CMCC_C3-05-4G-A", "rewardname": "中国移动CMCC_C3[粉-4G-512M]", "ispkg": "0", "rewarddesc": null, "band": null, "color": "粉", "memory": "512M+4G", "type": "中国移动CMCC_C3", "invId": null}, {"rewardid": "JSYD-CMCC-CMCC-CW1A-04-4G-A", "rewardname": "中国移动CMCC-CW1A[蓝-4G-512M]", "ispkg": "0", "rewarddesc": null, "band": null, "color": "蓝", "memory": "512M+4G", "type": "中国移动CMCC-CW1A", "invId": null}, {"rewardid": "JSYD-XTC-XTCZ5AQ-05", "rewardname": "小天才XTCZ5Aq[粉]", "ispkg": "0", "rewarddesc": null, "band": null, "color": "粉", "memory": "", "type": "小天才XTCZ5Aq", "invId": null}, {"rewardid": "JSYD-XTC-W2028AC-06", "rewardname": "小天才W2028AC[红]", "ispkg": "0", "rewarddesc": null, "band": null, "color": "红", "memory": "", "type": "小天才W2028AC", "invId": null}, {"rewardid": "JSYD-XTC-W1920AC-06", "rewardname": "小天才W1920AC[红]", "ispkg": "0", "rewarddesc": null, "band": null, "color": "红", "memory": "", "type": "小天才W1920AC", "invId": null}]}}, "h5getLevelList": {"retCode": "0", "retMsg": "nn", "data": [{"levelid": "3002096494", "levelname": "0充送200分钟（6个月）", "minlevel": "", "maxlevel": "", "iscashacount": "0", "levelinfo": "特邀客户承诺协议期6个月内不办理过户、销户、报停即可自活动办理次月起连续分6个月每月5日前赠送国内通话200分钟（不含港澳台），跨整月停机不赠送且赠送次数减少一次。赠送的通话分钟数到期自动关闭。限参加一次，冲突系统为准。"}, {"levelid": "300004021989", "levelname": "0充送1G（6个月）", "minlevel": "", "maxlevel": "", "iscashacount": "1", "levelinfo": "特邀客户承诺协议期6个月内不办理销户、过户、报停业务即可自活动办理次月起连续分6个月每月5日前获赠1GB全国通用套外流量(不含港澳台)。每月赠送流量月底自动清零(剩余流量不结转)，并仅在超出用户正常套餐后开始使用,限参加一次。若跨整月停机将导致流量不赠送，活动到期后赠送流量自动关闭。当月总流量达到100GB后，赠送流量不可继续使用,停止当月移动数据流量业务功能，次月恢复。请结合自身流量使用情况合理开通套餐及参加营销活动。冲突系统为准。"}, {"levelid": "300004022008", "levelname": "0充送2G（3个月）", "minlevel": "", "maxlevel": "", "iscashacount": "1", "levelinfo": "特邀客户承诺协议期3个月内不办理销户、过户、报停业务即可自活动办理次月起连续分3个月每月5日前获赠2GB全国通用流量（不含港澳台）。每月赠送流量在月底自动清零(剩余流量不结转)，并仅在超出用户正常套餐后开始使用,限参加一次。若跨整月停机将导致流量不赠送，活动到期后赠送流量自动关闭。当月总流量达到100GB后，赠送流量不可继续使用,停止当月移动数据流量业务功能，次月恢复。请结合自身流量使用情况合理开通套餐及参加营销活动。冲突系统为准。"}, {"levelid": "300004021912", "levelname": "充100送100元和包券（4个月）", "minlevel": "", "maxlevel": "", "iscashacount": "0", "levelinfo": "特邀客户自愿充值100元,并承诺协议期4个月内不办理过户、销户、报停即可获赠100元和包券，100元和包券立即到账，充值的100元话费,办理当月立即到账20元,剩余80元自活动办理次月起连续分4个月每月5日前释放20元（跨整月停机不释放且释放次数减少一次）。限参加一次。"}, {"levelid": "300004025291", "levelname": "0充送50元（4个月）", "minlevel": "", "maxlevel": "", "iscashacount": "0", "levelinfo": "特邀客户承诺协议期4个月内不办理过户、销户、报停即可获赠50元话费。其中10元在办理当月立即到账,剩余40元话费自活动办理次月起连续分4个月每月5日前赠送10元（跨整月停机不赠送且赠送次数减少一次）。赠送的话费不可转账取现且不提供发票打印。限参加一次。"}, {"levelid": "300004021899", "levelname": "0充送100元（4个月）", "minlevel": "", "maxlevel": "", "iscashacount": "0", "levelinfo": "特邀客户承诺协议期4个月内不办理过户、销户、报停即可获赠100元话费。其中20元在办理当月立即到账,剩余80元话费自活动办理次月起连续分4个月每月5日前赠送20元（跨整月停机不赠送且赠送次数减少一次）。赠送的话费不可转账取现且不提供发票打印。限参加一次。"}, {"levelid": "300004022026", "levelname": "0充送3G（2个月）", "minlevel": "", "maxlevel": "", "iscashacount": "0", "levelinfo": "特邀客户承诺协议期2个月内不办理销户、过户、报停业务即可自活动办理次月起连续分2个月每月5日前获赠3GB全国通用流量（不含港澳台）。每月赠送流量在月底自动清零(剩余流量不结转)，并仅在超出用户正常套餐后开始使用,限参加一次。若跨整月停机将导致流量不赠送，活动到期后赠送流量自动关闭。当月总流量达到100GB后，赠送流量不可继续使用,停止当月移动数据流量业务功能，次月恢复。请结合自身流量使用情况合理开通套餐及参加营销活动。冲突系统为准。"}, {"levelid": "300004021936", "levelname": "0充送100分钟（6个月）", "minlevel": "", "maxlevel": "", "iscashacount": "0", "levelinfo": "特邀客户承诺协议期6个月内不办理过户、销户、报停即可自营销案办理次月起连续分6个月每月5日前赠送国内通话100分钟（不含港澳台），跨整月停机不赠送且赠送次数减少一次。赠送的通话分钟数到期自动关闭。限参加一次，冲突系统为准。"}, {"levelid": "300004021971", "levelname": "0充送300分钟（6个月）", "minlevel": "", "maxlevel": "", "iscashacount": "0", "levelinfo": "特邀客户承诺协议期6个月内不办理过户、销户、报停即可自活动办理次月起连续分6个月每月5日前赠送国内通话300分钟（不含港澳台），跨整月停机不赠送且赠送次数减少一次。赠送的通话分钟数到期自动关闭。限参加一次，冲突系统为准。"}]}, "h5geRewardList": {"retCode": "0", "retMsg": "nn", "data": {"1000001": [{"rewardid": "1000001", "rewardname": "测试奖品包1", "rewardtype": "RwdGift_Pack", "selecttype": "SeleType_Choice", "rewardvalue": "0", "deductscoretype": "0", "deductscore": "0", "rewarddesc": "0", "needsn": "0", "haveapd": "0", "mincount": "1", "maxcount": "1", "rwdpkgtype": "RwdGift_Pack", "drawtype": "0", "maturedealtype": "0", "rwdpackageid": ""}, {"rewardid": "10000011", "rewardname": "包1包下子产品1", "rewardtype": "RwdGift_Prod", "selecttype": "SeleType_Choice", "rewardvalue": "0", "deductscoretype": "0", "deductscore": "0", "rewarddesc": "15元/月，成功订购后，每月可领取爱奇艺、优酷视频、腾讯视频、咪咕视频等视频VIP会员。该产品随主体套餐可选开通，成功开通后随开通短信指引去指定页面领取包内权益。", "needsn": "0", "haveapd": "0", "mincount": "1", "maxcount": "1", "rwdpkgtype": "0", "drawtype": "0", "maturedealtype": "0", "rwdpackageid": "1000001"}, {"rewardid": "10000012", "rewardname": "包1包下子产品2", "rewardtype": "RwdGift_Prod", "selecttype": "SeleType_Choice", "rewardvalue": "0", "deductscoretype": "0", "deductscore": "0", "rewarddesc": "10元/月，成功办理后，可领取咪咕视频钻石会员权益。该产品随主体套餐可选开通，成功开通后随开通短信指引去指定页面领取包内权益。", "needsn": "0", "haveapd": "0", "mincount": "1", "maxcount": "1", "rwdpkgtype": "0", "drawtype": "0", "maturedealtype": "0", "rwdpackageid": "1000001"}, {"rewardid": "10000013", "rewardname": "包1包下子产品3", "rewardtype": "RwdGift_Prod", "selecttype": "SeleType_Choice", "rewardvalue": "0", "deductscoretype": "0", "deductscore": "0", "rewarddesc": "15元/月，可以畅看咪咕视频上的VIP视频内容，同时还可以享受咪咕视频会员的功能权益和增值服务，具体权益请至咪咕视频客户端查看。", "needsn": "0", "haveapd": "0", "mincount": "1", "maxcount": "1", "rwdpkgtype": "0", "drawtype": "0", "maturedealtype": "0", "rwdpackageid": "1000001"}], "2000001": [{"rewardid": "2000001", "rewardname": "测试奖品包2", "rewardtype": "RwdGift_Pack", "selecttype": "SeleType_Choice", "rewardvalue": "0", "deductscoretype": "0", "deductscore": "0", "rewarddesc": "0", "needsn": "0", "haveapd": "0", "mincount": "1", "maxcount": "1", "rwdpkgtype": "RwdGift_Pack", "drawtype": "0", "maturedealtype": "0", "rwdpackageid": ""}, {"rewardid": "20000011", "rewardname": "包2包下子产品1", "rewardtype": "RwdGift_Prod", "selecttype": "SeleType_Choice", "rewardvalue": "0", "deductscoretype": "0", "deductscore": "0", "rewarddesc": "15元/月，成功订购后，每月可领取爱奇艺、优酷视频、腾讯视频、咪咕视频等视频VIP会员。该产品随主体套餐可选开通，成功开通后随开通短信指引去指定页面领取包内权益。", "needsn": "0", "haveapd": "0", "mincount": "1", "maxcount": "1", "rwdpkgtype": "0", "drawtype": "0", "maturedealtype": "0", "rwdpackageid": "2000001"}, {"rewardid": "20000012", "rewardname": "包2包下子产品2", "rewardtype": "RwdGift_Prod", "selecttype": "SeleType_Choice", "rewardvalue": "0", "deductscoretype": "0", "deductscore": "0", "rewarddesc": "10元/月，成功办理后，可领取咪咕视频钻石会员权益。该产品随主体套餐可选开通，成功开通后随开通短信指引去指定页面领取包内权益。", "needsn": "0", "haveapd": "0", "mincount": "1", "maxcount": "1", "rwdpkgtype": "0", "drawtype": "0", "maturedealtype": "0", "rwdpackageid": "2000001"}, {"rewardid": "20000013", "rewardname": "包2包下子产品3有串", "rewardtype": "RwdGift_Prod", "selecttype": "SeleType_Choice", "rewardvalue": "0", "deductscoretype": "0", "deductscore": "0", "rewarddesc": "15元/月，可以畅看咪咕视频上的VIP视频内容，同时还可以享受咪咕视频会员的功能权益和增值服务，具体权益请至咪咕视频客户端查看。", "needsn": "1", "haveapd": "0", "mincount": "1", "maxcount": "1", "rwdpkgtype": "0", "drawtype": "0", "maturedealtype": "0", "rwdpackageid": "2000001"}], "8000085818": [{"rewardid": "8000085818", "rewardname": "4G内容权益包二选一/咪咕视频会员15元", "rewardtype": "RwdGift_Pack", "selecttype": "SeleType_Choice", "rewardvalue": "0", "deductscoretype": "0", "deductscore": "0", "rewarddesc": "0", "needsn": "0", "haveapd": "0", "mincount": "1", "maxcount": "1", "rwdpkgtype": "RwdGift_Pack", "drawtype": "0", "maturedealtype": "0", "rwdpackageid": ""}, {"rewardid": "2000009573", "rewardname": "4G内容权益包15元", "rewardtype": "RwdGift_Prod", "selecttype": "SeleType_Choice", "rewardvalue": "0", "deductscoretype": "0", "deductscore": "0", "rewarddesc": "15元/月，成功订购后，每月可领取爱奇艺、优酷视频、腾讯视频、咪咕视频等视频VIP会员。该产品随主体套餐可选开通，成功开通后随开通短信指引去指定页面领取包内权益。", "needsn": "0", "haveapd": "0", "mincount": "1", "maxcount": "1", "rwdpkgtype": "0", "drawtype": "0", "maturedealtype": "0", "rwdpackageid": "8000085818"}, {"rewardid": "2000009572", "rewardname": "4G内容权益包10元", "rewardtype": "RwdGift_Prod", "selecttype": "SeleType_Choice", "rewardvalue": "0", "deductscoretype": "0", "deductscore": "0", "rewarddesc": "10元/月，成功办理后，可领取咪咕视频钻石会员权益。该产品随主体套餐可选开通，成功开通后随开通短信指引去指定页面领取包内权益。", "needsn": "0", "haveapd": "0", "mincount": "1", "maxcount": "1", "rwdpkgtype": "0", "drawtype": "0", "maturedealtype": "0", "rwdpackageid": "8000085818"}, {"rewardid": "2400000422", "rewardname": "咪咕视频会员包月15元", "rewardtype": "RwdGift_Prod", "selecttype": "SeleType_Choice", "rewardvalue": "0", "deductscoretype": "0", "deductscore": "0", "rewarddesc": "15元/月，可以畅看咪咕视频上的VIP视频内容，同时还可以享受咪咕视频会员的功能权益和增值服务，具体权益请至咪咕视频客户端查看。", "needsn": "0", "haveapd": "0", "mincount": "1", "maxcount": "1", "rwdpkgtype": "0", "drawtype": "0", "maturedealtype": "0", "rwdpackageid": "8000085818"}]}}, "h5qryFee": {"retCode": "0", "retMsg": "nn", "data": {"totalFee": 3000, "feeList": [{"chargecode": "CashPay", "chargemoney": "3000", "charegename": "现金充值"}, {"chargecode": "SpecialPay", "chargemoney": "0", "charegename": "专有帐户预存"}]}}, "h5QryMarketHeList": {"retCode": "0", "retMsg": null, "data": [{"offerId": "001", "offerName": "201909流量至尊宝优惠", "offerDesc": "满299元套餐送不限量流量套餐包", "becode": "14", "effectDate": "2019-08-29 00:00:00", "expireDate": "2020-08-29 00:00:00", "status": "1", "createDate": "2019-08-29 00:00:00"}]}, "h5QryLevelList": {"retCode": "0", "retMsg": null, "data": [{"levelId": "300004008510", "levelName": "测试下--和分期非终端类", "minLevel": null, "maxLevel": null, "levelInfo": null, "fee": null, "stagenum": null, "enterspenumber": "0", "overlaypkgList": null, "isHebaoXYGJ": "Y"}, {"levelId": "100000010000044700", "levelName": "测试下-副本", "minLevel": null, "maxLevel": null, "levelInfo": null, "fee": "200.00", "stagenum": null, "enterspenumber": "0", "overlaypkgList": null, "isHebaoXYGJ": "N"}]}, "h5QryRewardList": {"retCode": "0", "retMsg": null, "data": {"8000085818": [{"rewardid": "8000085818", "rewardname": "4G内容权益包二选一/咪咕视频会员15元", "rewardtype": "RwdGift_Pack", "selecttype": "SeleType_Choice", "rewardvalue": "0", "deductscoretype": "0", "deductscore": "0", "rewarddesc": "0", "needsn": "0", "haveapd": "0", "mincount": "1", "maxcount": "1", "rwdpkgtype": "RwdGift_Pack", "drawtype": "0", "maturedealtype": "0", "rwdpackageid": ""}, {"rewardid": "2000009573", "rewardname": "4G内容权益包15元", "rewardtype": "RwdGift_Prod", "selecttype": "SeleType_Choice", "rewardvalue": "0", "deductscoretype": "0", "deductscore": "0", "rewarddesc": "15元/月，成功订购后，每月可领取爱奇艺、优酷视频、腾讯视频、咪咕视频等视频VIP会员。该产品随主体套餐可选开通，成功开通后随开通短信指引去指定页面领取包内权益。", "needsn": "0", "haveapd": "0", "mincount": "1", "maxcount": "1", "rwdpkgtype": "0", "drawtype": "0", "maturedealtype": "0", "rwdpackageid": "8000085818"}, {"rewardid": "2000009576", "rewardname": "4G内容权益包15元1111", "rewardtype": "RwdGift_Prod", "selecttype": "SeleType_Choice", "rewardvalue": "0", "deductscoretype": "0", "deductscore": "0", "rewarddesc": "15元/月，成功订购后，每月可领取爱奇艺、优酷视频、腾讯视频、咪咕视频等视频VIP会员。该产品随主体套餐可选开通，成功开通后随开通短信指引去指定页面领取包内权益。", "needsn": "0", "haveapd": "0", "mincount": "1", "maxcount": "1", "rwdpkgtype": "0", "drawtype": "0", "maturedealtype": "0", "rwdpackageid": "8000085818"}, {"rewardid": "2000009578", "rewardname": "4G内容权益包15元1111", "rewardtype": "RwdGift_Prod", "selecttype": "SeleType_Choice", "rewardvalue": "0", "deductscoretype": "0", "deductscore": "0", "rewarddesc": "15元/月，成功订购后，每月可领取爱奇艺、优酷视频、腾讯视频、咪咕视频等视频VIP会员。该产品随主体套餐可选开通，成功开通后随开通短信指引去指定页面领取包内权益。", "needsn": "0", "haveapd": "0", "mincount": "1", "maxcount": "1", "rwdpkgtype": "0", "drawtype": "0", "maturedealtype": "0", "rwdpackageid": "8000085818"}], "1000001": [{"rewardid": "1000001", "rewardname": "测试奖品包1", "rewardtype": "RwdGift_Pack", "selecttype": "SeleType_Choice", "rewardvalue": "0", "deductscoretype": "0", "deductscore": "0", "rewarddesc": "0", "needsn": "0", "haveapd": "0", "mincount": "1", "maxcount": "1", "rwdpkgtype": "RwdGift_Pack", "drawtype": "0", "maturedealtype": "0", "rwdpackageid": ""}, {"rewardid": "10000011", "rewardname": "包1包下子产品1", "rewardtype": "RwdGift_Prod", "selecttype": "SeleType_Choice", "rewardvalue": "0", "deductscoretype": "0", "deductscore": "0", "rewarddesc": "15元/月，成功订购后，每月可领取爱奇艺、优酷视频、腾讯视频、咪咕视频等视频VIP会员。该产品随主体套餐可选开通，成功开通后随开通短信指引去指定页面领取包内权益。", "needsn": "0", "haveapd": "0", "mincount": "1", "maxcount": "1", "rwdpkgtype": "0", "drawtype": "0", "maturedealtype": "0", "rwdpackageid": "1000001"}]}}, "h5ApplyLoans": {"retCode": "0", "retMsg": null, "data": "true"}, "h5QryLoanOfferStatus": {"retCode": "0", "retMsg": null, "data": [{"oid": "1", "operCode": "1", "createDate": "2019-08-06 15:24:06", "levelOfferName": "档次名称1", "goodsTypeId": "001", "goodsBrand": "小米", "goodsName": "米9", "goodssn": "IMEI001", "goodsPrice": "2000", "loanMoney": "1500", "status": "1", "statusName": "审批中"}, {"oid": "2", "operCode": "2", "createDate": "2019-08-16 15:24:06", "levelOfferName": "档次名称2", "goodsTypeId": "002", "goodsBrand": "华为", "goodsName": "P20", "goodssn": "IMEI002", "goodsPrice": "3000", "loanMoney": "3000", "status": "2", "statusName": "审批完成"}, {"oid": "3", "operCode": "2", "createDate": "2019-08-16 15:24:06", "levelOfferName": "档次名称2", "goodsTypeId": "003", "goodsBrand": "oppo", "goodsName": "P20", "goodssn": "IMEI002", "goodsPrice": "3000", "loanMoney": "6000", "status": "3", "statusName": "未完成"}]}, "h5LoanHeBackout": {"retCode": "0", "retMsg": null, "data": "true"}, "h5ChargeLoanFee": {"retCode": "0", "retMsg": null, "data": {"totalAmount": null, "loanAmount": "2000", "leftAmount": "1000", "invoiceList": [{"offerName": "商品1", "chargeCode": "商品项1", "chargeName": "费用1", "amount": "3000"}, {"offerName": "商品2", "chargeCode": "商品项2", "chargeName": "费用2", "amount": "0"}]}}, "h5SubmitLoan": {"retCode": "0", "retMsg": null, "data": "145241564"}, "h5SubmitLoanOnline": {"retCode": "0", "retMsg": null, "data": "145241564"}, "h5QryActRewardPkg": {"retCode": "0", "retMsg": null, "data": {"rewardlist": [{"rewardid": "10001", "rewardname": "十元流量劵", "ispkg": "1", "rewarddesc": "中国移动十元流量劵"}, {"rewardid": "20001", "rewardname": "十元流量劵", "ispkg": "1", "rewarddesc": "中国移动十元流量劵"}, {"rewardid": "30001", "rewardname": "十元流量劵", "ispkg": "1", "rewarddesc": "中国移动十元流量劵"}, {"rewardid": "40001", "rewardname": "十元流量劵", "ispkg": "1", "rewarddesc": "中国移动十元流量劵"}, {"rewardid": "50001", "rewardname": "十元流量劵", "ispkg": "1", "rewarddesc": "中国移动十元流量劵"}, {"rewardid": "60001", "rewardname": "十元流量劵", "ispkg": "1", "rewarddesc": "中国移动十元流量劵"}], "pageinfo": {"beginrownumber": "0", "totalrecord": "100", "curpage": "4", "recordperpage": "10"}}}, "h5QryActReward": {"retCode": "0", "retMsg": null, "data": {"rewardlist": [{"rewardid": "100001", "rewardname": "必选十元流量劵", "ispkg": "0", "rewarddesc": "中国移动十元流量劵"}, {"rewardid": "100002", "rewardname": "十元流量劵SeleType_Choice", "ispkg": "0", "rewarddesc": "中国移动十元流量劵"}, {"rewardid": "100003", "rewardname": "十元流量劵", "ispkg": "0", "rewarddesc": "中国移动十元流量劵"}, {"rewardid": "100004", "rewardname": "十元流量劵", "ispkg": "0", "rewarddesc": "中国移动十元流量劵"}, {"rewardid": "100005", "rewardname": "十元流量劵SeleType_Default", "ispkg": "0", "rewarddesc": "中国移动十元流量劵"}, {"rewardid": "100006", "rewardname": "十元流量劵", "ispkg": "0", "rewarddesc": "中国移动十元流量劵"}, {"rewardid": "100007", "rewardname": "十元流量劵SeleType_Default", "ispkg": "0", "rewarddesc": "中国移动十元流量劵"}, {"rewardid": "100008", "rewardname": "十元流量劵SeleType_choice", "ispkg": "0", "rewarddesc": "中国移动十元流量劵"}, {"rewardid": "100009", "rewardname": "十元流量劵SeleType_choice", "ispkg": "0", "rewarddesc": "中国移动十元流量劵"}, {"rewardid": "100010", "rewardname": "十元流量劵", "ispkg": "0", "rewarddesc": "中国移动十元流量劵"}, {"rewardid": "100011", "rewardname": "十元流量劵SeleType_choice", "ispkg": "0", "rewarddesc": "中国移动十元流量劵"}, {"rewardid": "100012", "rewardname": "十元流量劵", "ispkg": "0", "rewarddesc": "中国移动十元流量劵"}, {"rewardid": "100013", "rewardname": "十元流量劵SeleType_choice", "ispkg": "0", "rewarddesc": "中国移动十元流量劵"}, {"rewardid": "100014", "rewardname": "十元流量劵", "ispkg": "0", "rewarddesc": "中国移动十元流量劵"}, {"rewardid": "100015", "rewardname": "十元流量劵SeleType_choice", "ispkg": "0", "rewarddesc": "中国移动十元流量劵"}, {"rewardid": "100016", "rewardname": "十元流量劵", "ispkg": "0", "rewarddesc": "中国移动十元流量劵"}], "pageinfo": {"beginrownumber": "0", "totalrecord": "32", "curpage": "4", "recordperpage": "10"}}}, "h5QryRwdDetailInfo": {"retCode": "0", "retMsg": null, "data": [{"rewardid": "100001", "rewardname": "中国移动十元话费代金券", "rewardtype": "RwdGift_fee 话费", "selecttype": "SeleType_Must", "rewardvalue": "1022", "deductscoretype": "扣减类型", "deductscore": "1000", "rewarddesc": "赠品说明", "needsn": "1", "haveapd": "1", "mincount": "1", "maxcount": "1", "rwdpkgtype": "RwdGift_fee 话费", "drawtype": "9", "maturedealtype": "AGREEPERIOD 按协议期"}, {"rewardid": "JSYD-HUAWEI-SIM-AL00-05", "rewardname": "中国移动十元话费代金券", "rewardtype": "RwdGift_fee 话费", "selecttype": "SeleType_Must", "rewardvalue": "1022", "deductscoretype": "扣减类型", "deductscore": "1000", "rewarddesc": "赠品说明", "needsn": "1", "haveapd": "1", "mincount": "1", "maxcount": "1", "rwdpkgtype": "RwdGift_fee 话费", "drawtype": "9", "maturedealtype": "AGREEPERIOD 按协议期"}, {"rewardid": "100002", "rewardname": "中国移动十元话费代金券", "rewardtype": "RwdGift_fee 话费", "selecttype": "SeleType_Choice", "needsn": "1", "mincount": "1", "maxcount": "1"}, {"rewardid": "100003", "rewardname": "中国移动十元话费代金券没有串号", "rewardtype": "RwdGift_fee 话费", "selecttype": "SeleType_Must", "needsn": "0", "mincount": "1", "maxcount": "1"}, {"rewardid": "100004", "rewardname": "中国移动十元话费代金券", "rewardtype": "RwdGift_fee 话费", "selecttype": "SeleType_Choice", "needsn": "1", "mincount": "1", "maxcount": "10"}]}, "h5QryUsableTerminalSn": {"retCode": "0", "retMsg": null, "data": {"usablereslist": [{"terminalsn": "ssss1111111"}, {"terminalsn": "ccccc2222"}, {"terminalsn": "hhhh33333"}, {"terminalsn": "zzz44444444"}, {"terminalsn": "dddd55555555555"}, {"terminalsn": "yyy66666666666"}, {"terminalsn": "asdff777777"}, {"terminalsn": "adfd8888888888"}], "orgid": "1000010"}}, "h5getprodlist": {"retCode": "0", "retMsg": "success", "data": [{"prodid": "2000009776", "prodName": "小区宽带480元包年（200M）", "pkgprodid": "2000009776", "ispackage": "0", "isNeed": "2", "prodDesc": "包年套餐费480元，包含一年不限时上网，480元包年套餐费按12个月均摊。每月收取40元，按日分摊，月底补齐。宽带接入速率200M。本套餐费/固定费平摊至每日收取。", "affecttype": "2", "feetype": "Y", "chargecode": "320500", "bandSpeed": "204800", "money": "48000"}, {"prodid": "2000009775", "prodName": "小区宽带360元包年（100M）", "pkgprodid": "2000009775", "ispackage": "0", "isNeed": "2", "prodDesc": "包年套餐费360元，包含一年不限时上网，360元包年套餐费按12个月均摊。每月收取30元，按日分摊，月底补齐。宽带接入速率100M。本套餐费/固定费平摊至每日收取。", "affecttype": "2", "feetype": "Y", "chargecode": "320500", "bandSpeed": "102400", "money": "36000"}]}, "h5prodCalFee": {"retCode": "0", "retMsg": "success", "data": {"ktParam": ""}}, "h5createNumber": {"retCode": "0", "retMsg": "success", "data": {"number": "123456789"}}, "h5prodchgsubmit": {"retCode": "0", "retMsg": "success", "data": {"orderid": "123456789"}}, "h5qryMarketPack": {"retCode": "0", "retMsg": null, "data": {"packList": [{"marketId": "1", "issueType": "1", "operId": "14150759", "marketName": "营销案包1", "packType": "3", "region": null, "activeId": "1001", "activeName": "批次1", "levelId": "10011", "levelName": "档次1", "superType": "market", "marketDesc": "测试包1", "rewardArr": [{"rewardId": "2400000422", "rewardName": "奖品12", "rewardPakId": "8000085818", "rewardPakName": "奖品包1", "region": null, "needSn": null}, {"rewardId": "2000009573", "rewardName": "奖品13", "rewardPakId": "8000085818", "rewardPakName": "奖品包1", "region": null, "needSn": null}, {"rewardId": "1001114", "rewardName": "奖品14", "rewardPakId": "100111", "rewardPakName": "奖品包1", "region": null, "needSn": null}, {"rewardId": "1001111", "rewardName": "奖品11", "rewardPakId": "100111", "rewardPakName": "奖品包1", "region": null, "needSn": null}, {"rewardId": "1001121", "rewardName": "奖品21", "rewardPakId": "100112", "rewardPakName": "奖品包2", "region": null, "needSn": null}, {"rewardId": "1001122", "rewardName": "奖品22", "rewardPakId": "100112", "rewardPakName": "奖品包2", "region": null, "needSn": null}, {"rewardId": "1001123", "rewardName": "奖品23", "rewardPakId": "100112", "rewardPakName": "奖品包2", "region": null, "needSn": null}, {"rewardId": "1001124", "rewardName": "奖品24", "rewardPakId": "100112", "rewardPakName": "奖品包2", "region": null, "needSn": null}, {"rewardId": "1001124", "rewardName": "奖品25", "rewardPakId": "100112", "rewardPakName": "奖品包2", "region": null, "needSn": null}]}, {"marketId": "2", "issueType": "2", "operId": "14150759", "marketName": "营销案包2", "packType": "2", "region": null, "activeId": "2001", "activeName": "批次2", "levelId": "20011", "levelName": "档次2", "superType": "market", "marketDesc": "测试包2", "rewardArr": null}]}}, "h5qryActiveList": {"retCode": "0", "retMsg": null, "data": [{"offerId": "3002104856", "offerName": "2019分期购互联网智能电视机活动自建20", "offerDesc": "2019分期购互联网智能电视机活动自建20", "becode": "14", "effectDate": "2019-11-18 00:00:00", "expireDate": "2019-11-23 00:00:00", "status": "1", "createDate": "2019-11-18 00:00:00"}, {"offerId": "3002104857", "offerName": "2019分期购互联网智能电视机活动自建20", "offerDesc": "2019分期购互联网智能电视机活动自建20", "becode": "14", "effectDate": "2019-11-18 00:00:00", "expireDate": "2019-11-23 00:00:00", "status": "1", "createDate": "2019-11-18 00:00:00"}]}, "h5apolloCreateOrder": {"retCode": "0", "retMsg": null, "data": {"orderId": "2020020111111111", "qrCode": "这里是base64流"}}, "h5qryApolloOrderDetail": {"retCode": "0", "retMsg": null, "data": {"orderId": "2020020111111111", "busiType": "mband_prod_kaitong", "queryJson": "", "source": "ald", "state": "0", "payMethod": "hfzf", "amount": "0", "hasFinish": "0", "orgId": "1488018465153416", "signId": "1233434343", "crmSrl": "123456789", "remark": "1", "createTime": "2020-02-04 00:00:00", "operTime": "2020-02-04 00:00:00"}}, "h5queryBreak": {"retCode": "0", "retMsg": "成功", "data": {"canRecendLevelList": [{"actName": "201703流量至尊包优惠201703流量至尊包优惠201703流量至尊包优惠201703流量至尊包优惠时", "actId": "001", "actInstId": "223371892", "levelName": "全家消费送宽带（2年期/98档）", "levelId": "34337788", "effectDate": "20200208000000", "expDate": "20200208000000", "createOrderId": "123456789", "levelInstId": "111111112"}, {"actName": "201704流量至尊包优惠", "actId": "001", "actInstId": "223371892", "levelName": "全家消费送宽带（2年期/98档）全家消费送宽带（2年期/98档）", "levelId": "34337788", "effectDate": "20200208000000", "expDate": "20200208000000", "createOrderId": "123456789", "levelInstId": "111111112"}, {"actName": "201705流量至尊包优惠", "actId": "001", "actInstId": "223371892", "levelName": "全家消费送宽带（2年期/98档）", "levelId": "34337788", "effectDate": "20200208000000", "expDate": "20200208000000", "createOrderId": "123456789", "levelInstId": "111111112"}, {"actName": "201706流量至尊包优惠", "actId": "001", "actInstId": "223371892", "levelName": "全家消费送宽带（2年期/98档）", "levelId": "34337788", "effectDate": "20200208000000", "expDate": "20200208000000", "createOrderId": "123456789", "levelInstId": "111111112"}, {"actName": "201707流量至尊包优惠", "actId": "001", "actInstId": "223371892", "levelName": "全家消费送宽带（2年期/98档）", "levelId": "34337788", "effectDate": "20200208000000", "expDate": "20200208000000", "createOrderId": "123456789", "levelInstId": "111111112"}, {"actName": "201708流量至尊包优惠", "actId": "001", "actInstId": "223371892", "levelName": "全家消费送宽带（2年期/98档）", "levelId": "34337788", "effectDate": "20200208000000", "expDate": "20200208000000", "createOrderId": "123456789", "levelInstId": "111111112"}]}}, "h5queryBatch": {"retCode": "0", "retMsg": "成功", "data": {"canendRecactList": [{"actId": "3002096589", "actName": "2016年南京宽带99包年系列优惠活动"}, {"actId": "3002097347", "actName": "终端营销案001"}, {"actId": "3002096594", "actName": "2016年南京10M家庭宽带优惠活动"}, {"actId": "300004021912", "actName": "钟的个人营销案"}, {"actId": "3002096494", "actName": "钟娜娜的个人营销案"}, {"actId": "3002099979", "actName": "2020年南京不限量套餐升档优惠"}, {"actId": "3002099729", "actName": "终端营销案001"}, {"actId": "3002099423", "actName": "2020年小积分大转盘抽奖活动"}, {"actId": "3002099443", "actName": "一级自有电渠飞享统一合约（25%话补）"}, {"actId": "3002099450", "actName": "2020南京零售换机优惠活动"}], "pageInfo": {"beginRowNumber": "1 ", "totalRecord": "100", "curPage": "1 ", "recordPerpage": "10"}}}, "h5queryGrade": {"retCode": "0", "retMsg": "成功", "data": {"canendReclevelList": [{"levelId": "3002096494", "levelName": "0充送200分钟（6个月）"}, {"levelId": "300004021989", "levelName": "0充送1G（6个月）"}, {"levelId": "300004022008", "levelName": "0充送2G（3个月）"}, {"levelId": "300004021912", "levelName": "充100送100元和包券（4个月）"}, {"levelId": "300004025291", "levelName": "0充送50元（4个月）"}, {"levelId": "300004021899", "levelName": "0充送100元（4个月）"}, {"levelId": "300004022026", "levelName": "0充送3G（2个月）"}, {"levelId": "300004021936", "levelName": "0充送100分钟（6个月）"}, {"levelId": "300004021971", "levelName": "0充送300分钟（6个月）"}], "pageInfo": {"beginRowNumber": "1 ", "totalRecord": "20", "curPage": "1 ", "recordPerpage": "10"}}}, "h5qryendlevelfee": {"retCode": "0", "retMsg": "查询成功", "data": {"feeList": [{"chargeMoney": "20000", "chargeCode": "CashPay", "chargeName": "现金充值"}, {"chargeMoney": "0", "chargeCode": "SpecialPay", "chargeName": "专有帐户预存"}]}}, "h5queryfmyrwdfee": {"retCode": "0", "retMsg": "查询成功", "data": {"feeList": [{"chargeMoney": "20000", "chargeCode": "CashPay", "chargeName": "现金充值"}, {"chargeMoney": "40000", "chargeCode": "SpecialPay", "chargeName": "专有帐户预存"}]}}, "h5endreclevelcommit": {"retCode": "0", "retMsg": "Success", "data": {"recoid": "00000001456789", "invoiceinfo": "0000000000000000", "outSrl": "2020021410197409"}}, "h5checkIdCard": {"data": {"certaddress": "江苏省丹阳市珥陵镇扶城村石庄4号", "certid": "301029200112098827", "certtype": "IdCard", "certtypename": "居民身份证", "fmyaddress": "", "name": "毛琳娜", "nettype": "GSM", "postalcode": "", "realstatecode": "4"}, "retCode": "0", "retMsg": "Success"}, "h5qryCustwBandInfo": {"data": [{"address": "淮安地区楚州区淮城镇华西路新区花园高层17号楼东单元602", "applyDate": "20200114", "bandCode": "手机代付费宽带", "bandSubsId": "80511000136", "invalidDate": "", "isMainBand": "1", "limitBandWidth": "102400", "loginTelnum": "15252915090", "status": "US10", "statusName": "正使用"}, {"address": "镇江地区丹阳访仙镇张集村无门牌10号牌楼", "applyDate": "20200925", "bandCode": "家庭宽带", "bandSubsId": "80511000133", "invalidDate": "", "isMainBand": "1", "limitBandWidth": "100M", "loginTelnum": "15252915090", "status": "US10", "statusName": "待激活"}, {"address": "镇江地区丹阳访仙镇张集村无门牌121号牌楼", "applyDate": "20200925", "bandCode": "家庭宽带1", "bandSubsId": "80511000138", "invalidDate": "", "isMainBand": "1", "limitBandWidth": "200M", "loginTelnum": "80511000137", "status": "US11", "statusName": "待激活"}], "retCode": "0", "retMsg": "Success"}, "h5installorderCalfee": {"data": [{"itemid": "2400000079", "itempriceinfo": [{"chargecode": "3206", "chargename": "宽带接入费", "chargetype": "Z", "chargetypename": "", "priceamount": "0"}], "totalamount": "0"}, {"itemid": "151000000000000016", "itempriceinfo": [{"chargecode": "DeviceTestFee", "chargename": "家庭终端调测费", "chargetype": "O", "chargetypename": "", "priceamount": "10000"}, {"chargecode": "DeviceTestFee", "chargename": "家庭终端调测费1", "chargetype": "O", "chargetypename": "", "priceamount": "10000"}], "totalamount": "20000"}], "retCode": "0"}, "h5offerListQry": {"retCode": "0", "retMsg": null, "data": [{"offerid": "2011002057", "offername": "当月免费套餐", "isbundle": "0", "selecttype": "T", "subofferinfolist": null}, {"offerid": "2400000979", "offername": "宽带接入费用", "isbundle": "0", "selecttype": "M", "subofferinfolist": null}, {"offerid": "2400001039", "offername": "一号多宽资费包", "isbundle": "1", "selecttype": "M", "subofferinfolist": {"subofferinfo": [{"offerid": "2000012299", "offername": "20元100M宽带电视包（一号多宽）"}, {"offerid": "2000012300", "offername": "30元200M宽带电视包（一号多宽）"}, {"offerid": "2000009874", "offername": "30元100M宽带电视包（一号多宽）"}, {"offerid": "2000009875", "offername": "40元200M宽带电视包（一号多宽）"}, {"offerid": "2000009034", "offername": "宽带电视优惠包(100M)"}, {"offerid": "2000009033", "offername": "宽带电视优惠包(50M)"}, {"offerid": "11111111111", "offername": null}, {"offerid": "22222222222222222", "offername": null}]}}]}, "h5qryBandSpeed": {"data": "0", "retCode": "0"}, "h5moreBandsubmit": {"retCode": "0", "retMsg": "success", "data": {"orderid": "123456789", "outSrl": "123456789"}}}