{"h5qryOrder": {"retCode": "0", "retMsg": "订单信息查询成功", "data": {"orderList": [{"installAddr": "大伦镇太平村", "deliveryType": "WLPS", "bookingId": "BES200112436050361407", "remark": "预约上门安装时间：4月24下午请师傅及时联系用户安装（请师傅代收50元宽带押金）", "status": "88", "offerName": "宽带新装,设备新装(设备类型:PON家庭网关新领)", "siteName": "姜堰香园社区宽带体验店", "orderId": "18110361007582729653", "discountAmount": "0", "payStatus": "1", "cityId": "21", "amount": "0", "invoiceFlag": "1", "serviceCode": "10002", "accNbr": "18261062547", "orderType": "1", "factAmount": "0", "createTime": "2020/03/06 00:00:00"}, {"installAddr": "大伦镇太平村", "deliveryType": "WLPS", "bookingId": "BES200112436241031407", "remark": "预约上门安装时间：4月24下午请师傅及时联系用户安装（请师傅代收50元互联网押金）", "status": "58", "offerName": "宽带保持,电视订购新装,设备新装", "siteName": "姜堰香园社区宽带体验店", "orderId": "18110261010142730030", "discountAmount": "0", "payStatus": "1", "cityId": "21", "amount": "0", "invoiceFlag": "1", "serviceCode": "10002", "accNbr": "18261062547", "orderType": "1", "factAmount": "0", "createTime": "2020/03/06 00:00:00"}]}}, "h5insertOrderAttach": {"retCode": "0", "retMsg": "成功"}}