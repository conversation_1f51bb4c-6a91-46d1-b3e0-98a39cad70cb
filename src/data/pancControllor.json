{"h5qryMenuFeeInfos": {"retCode": "0", "retMsg": "发送成功", "data": [{"serviceFee": "200", "panchannelId": "111", "menuId": "1001", "isValid": "1", "menuName": "信用购"}, {"serviceFee": "100", "panchannelId": "111", "menuId": "1002", "isValid": "1", "menuName": "宽带开通"}, {"serviceFee": "300", "panchannelId": "111", "menuId": "1003", "isValid": "1", "menuName": "选号入网"}]}, "h5QryPancList": {"retCode": "0", "retMsg": "发送成功", "data": [{"panChannelId": "1000000319104221", "panChannelName": "测试同步信息接口", "longitude": "1313", "dimension": "4646", "bossNumber": "***********", "crmCode": "14150759", "cityId": "14", "areaId": "456", "gridId": "789", "branchPostOffId": "789", "isValid": "1", "createrCode": "14150759", "createrName": "王涛", "createDate": "2020-02-20 17:33:18", "changerCode": "14150759", "changerName": "王涛", "changeDate": "2020-02-20 17:55:20", "remark": null, "localtionName": null, "businessAdd": "营业厅地址", "quaType": "1", "businessFullName": "营业执照全称", "businessNum": "15416346", "legalPapersType": null, "legalName": null, "legalIdNum": null, "checkState": "", "checkMsg": "理由"}, {"panChannelId": "1000000319104221", "panChannelName": "测试同步信息接口", "longitude": "1313", "dimension": "4646", "bossNumber": "***********", "crmCode": "14150759", "cityId": "14", "areaId": "456", "gridId": "789", "branchPostOffId": "789", "isValid": "2", "createrCode": "14150759", "createrName": "王涛", "createDate": "2020-02-20 17:33:18", "changerCode": "14150759", "changerName": "王涛", "changeDate": "2020-02-20 17:55:20", "remark": null, "localtionName": null, "businessAdd": "营业厅地址", "quaType": "1", "businessFullName": "营业执照全称", "businessNum": "15416346", "legalPapersType": null, "legalName": null, "legalIdNum": null, "checkState": "0", "checkMsg": "理由"}, {"panChannelId": "1000000319104221", "panChannelName": "测试同步信息接口", "longitude": "1313", "dimension": "4646", "bossNumber": "***********", "crmCode": "14150759", "cityId": "14", "areaId": "456", "gridId": "789", "branchPostOffId": "789", "isValid": "2", "createrCode": "14150759", "createrName": "王涛", "createDate": "2020-02-20 17:33:18", "changerCode": "14150759", "changerName": "王涛", "changeDate": "2020-02-20 17:55:20", "remark": null, "localtionName": null, "businessAdd": "营业厅地址", "quaType": "1", "businessFullName": "营业执照全称", "businessNum": "15416346", "legalPapersType": null, "legalName": null, "legalIdNum": null, "checkState": "1", "checkMsg": "理由"}, {"panChannelId": "1000000319104221", "panChannelName": "测试同步信息接口", "longitude": "1313", "dimension": "4646", "bossNumber": "***********", "crmCode": "14150759", "cityId": "14", "areaId": "456", "gridId": "789", "branchPostOffId": "789", "isValid": "2", "createrCode": "14150759", "createrName": "王涛", "createDate": "2020-02-20 17:33:18", "changerCode": "14150759", "changerName": "王涛", "changeDate": "2020-02-20 17:55:20", "remark": null, "localtionName": null, "businessAdd": "营业厅地址", "quaType": "1", "businessFullName": "营业执照全称", "businessNum": "15416346", "legalPapersType": null, "legalName": null, "legalIdNum": null, "checkState": "-1", "checkMsg": "理由"}, {"panChannelId": "1000000319104221", "panChannelName": "测试同步信息接口", "longitude": "1313", "dimension": "4646", "bossNumber": "***********", "crmCode": "14150759", "cityId": "14", "areaId": "456", "gridId": "789", "branchPostOffId": "789", "isValid": "0", "createrCode": "14150759", "createrName": "王涛", "createDate": "2020-02-20 17:33:18", "changerCode": "14150759", "changerName": "王涛", "changeDate": "2020-02-20 17:55:20", "remark": null, "localtionName": null, "businessAdd": "营业厅地址", "quaType": "1", "businessFullName": "营业执照全称", "businessNum": "15416346", "legalPapersType": null, "legalName": null, "legalIdNum": null, "checkState": "", "checkMsg": "理由"}]}, "h5qryMenuFee": {"retCode": "0", "retMsg": "发送成功", "data": [{"menuId": "123", "menuName": "信用购2", "serviceFee": "123", "flag": "1"}, {"menuId": "222", "menuName": "选号入网2", "serviceFee": "234", "flag": "0"}, {"menuId": "3333", "menuName": "智能组网2", "serviceFee": "132", "flag": "0"}]}, "h5qryPancUserInfo": {"retCode": "0", "retMsg": "发送成功", "data": [{"operatorId": "123123", "operatorName": "张三", "isValid": "1", "createDate": "2019/12/12"}, {"operatorId": "1231331", "operatorName": "李四", "isValid": "0", "createDate": "2019/12/12"}]}}