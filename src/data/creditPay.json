{"h5GetCalyxActiveAndLevel": {"retCode": "0", "retMsg": null, "data": [{"marketId": "Calyx002", "marketName": "花呗分期002", "activeId": "1002", "activeName": "花呗分期批次002", "levelId": "1022", "levelName": "花呗分期档次002", "packType": "3", "superType": "calyx", "marketDesc": "花呗分期包2", "fee": "123", "stagenum": "24", "scoureNum": "600", "region": "14", "operId": "14157059", "issueType": "1"}, {"marketId": "Calyx001", "marketName": "花呗分期001", "activeId": "1001", "activeName": "花呗分期批次001", "levelId": "1021", "levelName": "花呗分期档次001", "packType": "3", "superType": "calyx", "marketDesc": "花呗分期包1", "fee": "321", "stagenum": "24", "scoureNum": "600", "region": "14", "operId": "14150759", "issueType": "2"}]}, "h5GetCalyxReward": {"retCode": "0", "retMsg": "成功", "data": [{"rewardId": "3", "rewardName": "花呗奖品包3", "rewardPakid": "1235", "rewardPakname": "花呗奖品包3", "region": "14"}]}, "h5CalyxApplication": {"retCode": "0", "retMsg": null, "data": {"srlId": "2019120410000103", "pId": "11223344", "freezeamt": "100"}}, "h5BalanceFreeze": {"retCode": "0", "retMsg": null, "data": {"qrcode": "http://123", "paytype": "01"}}, "h5GetOrder": {"retCode": "0", "retMsg": null, "data": [{"srlid": "2019112910000102", "operid": "123", "telnum": "12312312312", "prepayid": "11223344", "createDate": "2019-11-29 16:05:13", "keyParameterJson": "{\"marketld\":\"Calyx002\",\"marketName\":\"花呗分期002\",\"activeld\":\"1002\",\"activeName\":\"花呗分期批次002\",\"levelld\":\"1022\",\"levelName\":\"花呗分期档次002\",\"stagenum\":\"24\",\"rewardld\":\"3\",\"rewardName\":\"花呗奖品包3\",\"fundType\":\"1\"}", "activeid": "123", "payurl": "http://123", "levelid": "123", "rewardid": "123", "scorenum": "123", "freezeamt": "123", "paytype": "01", "status": "2", "recid": "123"}, {"srlid": "2019120410000103", "operid": "123", "telnum": "12312312312", "prepayid": "11223344", "createDate": "2019-12-04 14:55:02", "keyParameterJson": "{\"marketld\":\"Calyx002\",\"marketName\":\"花呗分期002\",\"activeld\":\"1002\",\"activeName\":\"花呗分期批次002\",\"levelld\":\"1022\",\"levelName\":\"花呗分期档次002\",\"stagenum\":\"24\",\"rewardld\":\"3\",\"rewardName\":\"花呗奖品包3\",\"fundType\":\"2\"}", "activeid": "123", "payurl": "http://123", "levelid": "123", "rewardid": "123", "scorenum": "123", "freezeamt": "123", "paytype": "01", "status": "4", "recid": "123"}]}, "h5GetCalyxoffer": {"retCode": "0", "retMsg": null, "data": [{"offerId": "001", "offerName": "202108流量至尊宝优惠", "offerDesc": "满199元套餐送不限量流量套餐包", "becode": "14", "effectDate": "2019-08-29 00:00:00", "expireDate": "2020-08-29 00:00:00", "status": "1", "createDate": "2019-08-29 00:00:00"}, {"offerId": "002", "offerName": "202008流量至尊宝优惠", "offerDesc": "满299元套餐送不限量流量套餐包", "becode": "14", "effectDate": "2020-08-29 00:00:00", "expireDate": "2021-01-29 00:00:00", "status": "1", "createDate": "2021-01-29 00:00:00"}]}, "h5QryLevelList": {"retCode": "0", "retMsg": "成功", "data": [{"levelId": "3002096494", "levelName": "0充送200分钟（6个月）", "minLevel": null, "maxLevel": null, "levelInfo": "特邀客户承诺协议期6个月内不办理过户、销户、报停即可自活动办理次月起连续分6个月每月5日前赠送国内通话200分钟（不含港澳台），跨整月停机不赠送且赠送次数减少一次。赠送的通话分钟数到期自动关闭。限参加一次，冲突系统为准。", "freezeamt": "240000", "scorenum": "600"}, {"levelId": "300004021989", "levelName": "0充送1G（6个月）", "minLevel": null, "maxLevel": null, "levelInfo": "特邀客户承诺协议期6个月内不办理销户、过户、报停业务即可自活动办理次月起连续分6个月每月5日前获赠1GB全国通用套外流量(不含港澳台)。每月赠送流量月底自动清零(剩余流量不结转)，并仅在超出用户正常套餐后开始使用,限参加一次。若跨整月停机将导致流量不赠送，活动到期后赠送流量自动关闭。当月总流量达到100GB后，赠送流量不可继续使用,停止当月移动数据流量业务功能，次月恢复。请结合自身流量使用情况合理开通套餐及参加营销活动。冲突系统为准。", "freezeamt": "240000", "scorenum": "600"}, {"levelId": "300004022008", "levelName": "0充送2G（3个月）", "minLevel": "1", "maxLevel": "3", "levelInfo": "特邀客户承诺协议期3个月内不办理销户、过户、报停业务即可自活动办理次月起连续分3个月每月5日前获赠2GB全国通用流量（不含港澳台）。每月赠送流量在月底自动清零(剩余流量不结转)，并仅在超出用户正常套餐后开始使用,限参加一次。若跨整月停机将导致流量不赠送，活动到期后赠送流量自动关闭。当月总流量达到100GB后，赠送流量不可继续使用,停止当月移动数据流量业务功能，次月恢复。请结合自身流量使用情况合理开通套餐及参加营销活动。冲突系统为准。", "freezeamt": "240000", "scorenum": "600"}]}, "h5QryRewardList": {"retCode": "0", "retMsg": null, "data": {"8000085818": [{"rewardid": "8000085818", "rewardname": "4G内容权益包二选一/咪咕视频会员15元", "rewardtype": "RwdGift_Pack", "selecttype": "SeleType_Choice", "rewardvalue": "0", "deductscoretype": "0", "deductscore": "0", "rewarddesc": "0", "needsn": "0", "haveapd": "0", "mincount": "1", "maxcount": "1", "rwdpkgtype": "RwdGift_Pack", "drawtype": "0", "maturedealtype": "0", "rwdpackageid": ""}, {"rewardid": "2000009573", "rewardname": "4G内容权益包15元", "rewardtype": "RwdGift_Prod", "selecttype": "SeleType_Choice", "rewardvalue": "0", "deductscoretype": "0", "deductscore": "0", "rewarddesc": "15元/月，成功订购后，每月可领取爱奇艺、优酷视频、腾讯视频、咪咕视频等视频VIP会员。该产品随主体套餐可选开通，成功开通后随开通短信指引去指定页面领取包内权益。", "needsn": "0", "haveapd": "0", "mincount": "1", "maxcount": "1", "rwdpkgtype": "0", "drawtype": "0", "maturedealtype": "0", "rwdpackageid": "8000085818"}]}}}