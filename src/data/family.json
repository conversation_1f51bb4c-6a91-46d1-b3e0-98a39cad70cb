{"h5QryFamilyProd": {"retCode": "0", "retMsg": "", "data": [{"prodId": "2000007240", "prodName": "家庭网短信包,2元/月", "prodDesc": "月套餐费1元/家,包含家庭成员之间发送500条免费短信/月/人,订购首月按月折算.套餐资费有效期自开通之日起一年,到期后若无新调整则相应按年顺延.", "state": "1", "region": "99", "pkgprodId": ""}, {"prodId": "2000003574", "prodName": "省内家庭V网,15元/月", "prodDesc": "每个月15元，省内使用", "state": "1", "region": "99", "pkgprodId": ""}]}, "h5QryOrderProd": {"retCode": "0", "retMsg": "", "data": {"flag": "3", "hasFmyprod": "1", "isHousehold": "1", "familyProdBoList": [{"prodId": "2000003574", "prodName": "省内家庭V网,15元/月", "isPackage": "", "pkgprodId": "", "startDate": "20170801000000", "endDate": "21001231000000"}]}}, "familySuccess": {"retCode": "0", "retMsg": "", "data": "200192965459328595"}, "familyFail": {"retCode": "-1", "retMsg": "【家庭V网短号套餐注销FAMILY1074】error,未取得产品[14001]的定义!", "data": ""}, "h5QryProdAndMemberList": {"retCode": "0", "retMsg": "", "data": {"effectiveMemberList": [{"subsid": "1209200008987641", "fmyregion": "12", "memsubsid": "1209200008987641", "telnum": "15952322351", "ismain": "1", "isKey": "0", "isV": "1", "memarea": "12", "memareaName": "", "shortno": "", "paytype": "", "isbalancetomain": "", "isscoretomain": "", "memstartdate": "2016-04-19 15:50:32", "memenddate": "2100-12-31 00:00:00", "memtype": "1", "vendDate": ""}, {"subsid": "1209200008987641", "fmyregion": "12", "memsubsid": "1299101066548251", "telnum": "18252324877", "ismain": "0", "isKey": "1", "isV": "", "memarea": "12", "memareaName": "", "shortno": "758", "paytype": "1", "isbalancetomain": "0", "isscoretomain": "", "memstartdate": "2018-04-03 10:17:46", "memenddate": "2100-12-31 00:00:00", "memtype": "1", "vendDate": ""}, {"subsid": "1209200008987641", "fmyregion": "12", "memsubsid": "1209200007442419", "telnum": "15195515866", "ismain": "0", "isKey": "1", "isV": "1", "memarea": "12", "memareaName": "", "shortno": "767", "paytype": "0", "isbalancetomain": "0", "isscoretomain": "", "memstartdate": "2016-04-19 15:50:57", "memenddate": "2100-12-31 00:00:00", "memtype": "1", "vendDate": "2100-12-31 00:00:00"}, {"subsid": "1209200008987641", "fmyregion": "12", "memsubsid": "1299101101803317", "telnum": "18888128211", "ismain": "0", "isKey": "1", "isV": "", "memarea": "12", "memareaName": "", "shortno": "753", "paytype": "1", "isbalancetomain": "1", "isscoretomain": "0", "memstartdate": "2017-11-30 13:42:36", "memenddate": "2100-12-31 00:00:00", "memtype": "1", "vendDate": ""}, {"subsid": "1209200008987641", "fmyregion": "12", "memsubsid": "1209200011381340", "telnum": "15161725317", "ismain": "0", "isKey": "1", "isV": "", "memarea": "12", "memareaName": "", "shortno": "768", "paytype": "0", "isbalancetomain": "0", "isscoretomain": "", "memstartdate": "2016-06-06 16:56:27", "memenddate": "2100-12-31 00:00:00", "memtype": "1", "vendDate": ""}], "uneffectiveMemberList": [], "shotNumList": ["750", "751", "752", "754", "755", "756", "757", "759", "760", "761", "762", "764", "765", "766", "769"], "openedProdList": [{"prodId": "2000003574", "prodName": "省内家庭V网,5元/月", "prodDesc": "", "state": "1", "region": "99", "pkgprodId": "", "startDate": "", "endDate": ""}, {"prodId": "2000007240", "prodName": "家庭网短信包,1元/月", "prodDesc": "月套餐费1元/家,包含家庭成员之间发送400条免费短信/月/人,订购首月按月折算.套餐资费有效期自开通之日起一年,到期后若无新调整则相应按年顺延.", "state": "1", "region": "99", "pkgprodId": "", "startDate": "", "endDate": ""}]}}, "h5DoSearch": {"retCode": "0", "retMsg": null, "data": {"memsubsid": "1419200011771553", "memregion": "14"}}, "h5QryAlreadyOpened": {"retCode": "0", "retMsg": null, "data": {"identityCheckInfo": {"region": "14", "regionname": "南京", "userName": "戴波", "registerdate": "20051009113152", "retCode": "0", "retMsg": "【查询客户信息 BDS1001】success", "telnum": "15152819756", "realstatecode": "4", "certId": "320321198910152441"}, "isMband": "0", "isNetTv": "1", "prodNetList": [{"prodid": "xxx", "prodname": "", "startdate": "2018-07-01", "enddate": "2100-12-31", "ispakage": "0", "pakagename": "xxx", "prodtype": "I", "pakageid": "2013000001"}]}}}