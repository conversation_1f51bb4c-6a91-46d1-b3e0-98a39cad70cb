{"h5AddCustomMarketPkg": {"retCode": "0", "retMsg": null, "data": null}, "h5QryCustomMarketPkg": {"retCode": "0", "retMsg": null, "data": [{"marketId": "20200814101109", "marketName": "全家WIFI调测服务259元|全家WIFI调测费直降180元", "type": null, "crmId": "14150759", "createDate": null, "startDate": "2020-08-13 00:00:00", "endDate": "2099-08-01 00:00:00", "marketDesc": "智能组网+营销案", "source": "1", "status": null, "releaseType": "1", "customProdInfoList": [{"marketId": "20200814101109", "pkgId": "2013000030", "prodId": "2013000030", "prodName": "第一智能组网", "isPackage": "1", "effectType": "0", "prodLevel": 1, "type": "3", "parentId": "-1", "isMust": "1", "isNeed": null, "customProdInfoList": [{"marketId": "20200814101109", "pkgId": null, "prodId": "2000012327", "prodName": "全家WIFI调测服务258元", "isPackage": "1", "effectType": "0", "prodLevel": 2, "type": "1", "parentId": "2013000030", "isMust": "1", "isNeed": null, "customProdInfoList": []}, {"marketId": "20200814101109", "pkgId": null, "prodId": "2413000030", "prodName": "智能组网基础服务", "isPackage": "1", "effectType": "0", "prodLevel": 2, "type": "3", "parentId": "2013000030", "isMust": "1", "isNeed": null, "customProdInfoList": []}]}, {"marketId": "20200814101109", "pkgId": null, "prodId": "3002106626", "prodName": "2020全省全家WIFI调测费优惠活动", "isPackage": "1", "effectType": "0", "prodLevel": 1, "type": "1", "parentId": "-1", "isMust": "1", "isNeed": null, "customProdInfoList": [{"marketId": "20200814101109", "pkgId": null, "prodId": "300004089356", "prodName": "全家WIFI调测费直降至180元", "isPackage": "1", "effectType": "0", "prodLevel": 2, "type": "1", "parentId": "3002106626", "isMust": "1", "isNeed": null, "customProdInfoList": [{"marketId": "20200814101109", "pkgId": null, "prodId": "8830044852", "prodName": "全家WIFI调测服务258元", "isPackage": "1", "effectType": "0", "prodLevel": 3, "type": "1", "parentId": "300004089356", "isMust": "1", "isNeed": null, "customProdInfoList": [{"marketId": "20200814101109", "pkgId": null, "prodId": "2000012327", "prodName": "全家WIFI调测服务258元", "isPackage": "1", "effectType": "0", "prodLevel": 4, "type": "1", "parentId": "8830044852", "isMust": "1", "isNeed": null, "customProdInfoList": null}]}, {"marketId": "20200814101109", "pkgId": null, "prodId": "8830044903", "prodName": "业务包（38及以上主体套餐）", "isPackage": "1", "effectType": "0", "prodLevel": 3, "type": "1", "parentId": "300004089356", "isMust": "1", "isNeed": null, "customProdInfoList": [{"marketId": "20200814101109", "pkgId": null, "prodId": "2020120801", "prodName": "xxxx####", "isPackage": "1", "effectType": "0", "prodLevel": 4, "type": "1", "parentId": "8830044903", "isMust": "1", "isNeed": null, "customProdInfoList": null}]}]}]}]}, {"marketId": "20200814101109", "marketName": "全家WIFI调测服务258元|全家WIFI调测费直降180元", "type": null, "crmId": "14150759", "createDate": null, "startDate": "2020-08-13 00:00:00", "endDate": "2099-08-01 00:00:00", "marketDesc": "智能组网+营销案", "source": "1", "status": null, "releaseType": "1", "customProdInfoList": [{"marketId": "20200814101109", "pkgId": null, "prodId": "3002106626", "prodName": "2020全省全家WIFI调测费优惠活动", "isPackage": "1", "effectType": "0", "prodLevel": 1, "type": "1", "parentId": "-1", "isMust": "1", "isNeed": null, "customProdInfoList": [{"marketId": "20200814101109", "pkgId": null, "prodId": "300004089356", "prodName": "全家WIFI调测费直降至180元", "isPackage": "1", "effectType": "0", "prodLevel": 2, "type": "1", "parentId": "3002106626", "isMust": "1", "isNeed": null, "customProdInfoList": [{"marketId": "20200814101109", "pkgId": null, "prodId": "8830044852", "prodName": "全家WIFI调测服务258元", "isPackage": "1", "effectType": "0", "prodLevel": 3, "type": "1", "parentId": "300004089356", "isMust": "1", "isNeed": null, "customProdInfoList": [{"marketId": "20200814101109", "pkgId": null, "prodId": "2000012327", "prodName": "全家WIFI调测服务258元", "isPackage": "1", "effectType": "0", "prodLevel": 4, "type": "1", "parentId": "8830044852", "isMust": "1", "isNeed": null, "customProdInfoList": null}]}, {"marketId": "20200814101109", "pkgId": null, "prodId": "8830044903", "prodName": "业务包（38及以上主体套餐）", "isPackage": "1", "effectType": "0", "prodLevel": 3, "type": "1", "parentId": "300004089356", "isMust": "1", "isNeed": null, "customProdInfoList": [{"marketId": "20200814101109", "pkgId": null, "prodId": "2020120801", "prodName": "xxxx####", "isPackage": "1", "effectType": "0", "prodLevel": 4, "type": "1", "parentId": "8830044903", "isMust": "1", "isNeed": null, "customProdInfoList": null}]}]}]}, {"marketId": "20200814101109", "pkgId": "2013000030", "prodId": "2013000030", "prodName": "第一智能组网", "isPackage": "1", "effectType": "0", "prodLevel": 1, "type": "3", "parentId": "-1", "isMust": "1", "isNeed": null, "customProdInfoList": [{"marketId": "20200814101109", "pkgId": null, "prodId": "2000012327", "prodName": "全家WIFI调测服务258元", "isPackage": "1", "effectType": "0", "prodLevel": 2, "type": "1", "parentId": "2013000030", "isMust": "1", "isNeed": null, "customProdInfoList": []}, {"marketId": "20200814101109", "pkgId": null, "prodId": "2413000030", "prodName": "智能组网基础服务", "isPackage": "1", "effectType": "0", "prodLevel": 2, "type": "3", "parentId": "2013000030", "isMust": "1", "isNeed": null, "customProdInfoList": []}]}]}]}, "h5DelCustomMarketPkg": {"retCode": "0", "retMsg": null, "data": null}}