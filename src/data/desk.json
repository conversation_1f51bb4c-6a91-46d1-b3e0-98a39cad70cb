{"h5DeskCountInfo": {"retCode": "0", "retMsg": "", "data": {"countInfo": {"staffId": "1488018492933394", "todoNum": "5", "focuseNum": "10", "todayBalance": "110"}, "isHaveToDo": "0", "activeInfo": [{"type": "0", "picPath": "10053", "mainTitle": "测试1", "subTitle": "测试11", "goPath": "http://"}, {"type": "1", "picPath": "145", "mainTitle": "测试2", "subTitle": "测试22", "goPath": "http://"}, {"type": "0", "picPath": "10019", "mainTitle": "测试3", "subTitle": "测试34", "goPath": "http://"}]}}, "h5DeskListInfo": {"retCode": "0", "retMsg": {}, "data": {"pageNo": "1", "pageSize": "10", "total0": "8", "total1": "5", "total2": "3", "total3": "1", "todoList": [{"taskId": "1002", "type": "2", "taskName": "外呼任务", "groupId": "1111111", "workUrl": "http://outcall.do", "taskState": "0", "taskTime": "2018/03/24 12:00:00", "closedTime": "2019/02/24 12:30:12", "content": "补卡业务138", "workTypeCode": "1003", "emergent": "0"}, {"taskId": "1003", "type": "1", "isPacket": "0", "taskName": "集团拜访2", "groupId": "1111111", "workUrl": "http://", "taskState": "0", "distributeTime": "2019/03/25 12:30:12", "workTypeCode": "1001", "taskTime": "2019/03/21 12:30:12", "content": "补卡业务138", "emergent": "1"}, {"taskId": "1004", "type": "2", "taskName": "南京市建邺区嘉陵江东街18号新城科技园5", "groupId": "1111111", "workUrl": "http://", "taskState": "0", "taskTime": "2019/03/22 18:30:12", "workTypeCode": "1001", "closedTime": "2018/02/24 12:30:12", "content": "补卡业务138", "emergent": "1"}, {"taskId": "1005", "type": "2", "taskName": "上行下达2", "groupId": "22222", "workUrl": "http://", "workTypeCode": "1005", "taskState": "0", "taskTime": "2019/03/23 18:00:12", "closedTime": "2018/02/24 12:30:12", "content": "补卡业务138", "emergent": "0"}, {"taskId": "1006", "type": "2", "taskName": "上行下达3", "groupId": "22222", "workUrl": "http://", "workTypeCode": "1005", "taskState": "0", "taskTime": "2019/03/24 12:30:12", "closedTime": "2018/02/24 12:30:12", "content": "补卡业务138", "emergent": "0"}, {"taskId": "1007", "type": "2", "taskName": "上行下达3", "groupId": "22222", "workUrl": "http://", "taskState": "0", "workTypeCode": "1005", "taskTime": "2019/03/25 12:30:12", "closedTime": "2018/02/24 12:30:12", "content": "补卡业务138", "emergent": "0"}]}}, "h5DeskOtherInfo": {"retCode": "0", "retMsg": "", "data": {"hasUsuallyFunc": "0", "funcInfo": [{"opId": "fsop", "type": "1", "funcId": "100012", "funcName": "宽带开通（新）", "picPath": "100012", "goPath": null, "opParentid": "fsop", "hasPwd": "0", "authenType": "1", "hasCrmid": "1"}, {"opId": "fsop", "type": "1", "funcId": "100013", "funcName": "电视（新）", "picPath": "100013", "goPath": null, "opParentid": "fsop", "hasCrmid": "1"}, {"opId": "fsop", "type": "1", "funcId": "100236", "funcName": "家庭诊断", "picPath": "100236", "goPath": null, "opParentid": "fsop", "hasCrmid": "1"}, {"opId": "cmop", "type": "1", "funcId": "100014", "funcName": "营销案新", "picPath": "100014", "goPath": null, "opParentid": "cmop", "hasCrmid": "0"}], "noticeInfo": [{"noticeId": "1002", "title": "测试1", "type": "0", "content": "测试1", "releaseOper": "发布人1", "releaseTime": "2019/02/24 12:30:12"}, {"noticeId": "1003", "title": "测试2", "type": "0", "content": "测试1", "releaseOper": "发布人1", "releaseTime": "2019/02/24 12:30:12"}]}}, "h5UnDoList": {"retCode": "0", "retMsg": {}, "data": [{"taskId": "1001", "type": "1", "taskName": "集团拜访cesadfadfhi1", "groupId": "111111", "workUrl": "http://", "taskTime": "2019/02/24 12:30:12", "content": "集团拜访任务内容", "workTypeCode": "1001", "emergent": "0"}, {"taskId": "1002", "type": "1", "taskName": "南京市建邺区嘉陵南京市建邺区嘉陵江东街18号新城科技园5江东街18号新城科技园5", "groupId": "111111", "workUrl": "http://", "taskTime": "2019/02/24 12:30:12", "workTypeCode": "1002", "content": " 数字地图1002任务", "emergent": "1"}, {"taskId": "1003", "type": "1", "taskName": "南京市建邺区嘉陵南京市建邺区嘉陵江东街18号新城科技园5江东街18号新城科技园5", "groupId": "111111", "workUrl": "http://", "taskTime": "2019/02/24 12:30:12", "workTypeCode": "1001", "content": "外呼任务任务内容", "emergent": "1"}, {"taskId": "1006", "type": "1", "taskName": "南京市建邺区嘉陵南京市建邺区嘉陵江东街18号新城科技园5", "groupId": "111111", "workUrl": "http://", "taskTime": "2019/02/24 12:30:12", "workTypeCode": "1006", "content": "集团预警1006任务内容", "emergent": "1"}, {"taskId": "1005", "type": "1", "taskName": "南京市建邺区嘉陵南京市建邺区嘉陵江东街18号新城科技园5", "groupId": "111111", "workUrl": "http://", "taskTime": "2019/02/24 12:30:12", "workTypeCode": "1009", "content": "个人集团任务", "emergent": "1"}, {"taskId": "1001", "type": "1", "taskName": "集团拜访cesadfadfhi1", "groupId": "111111", "workUrl": "http://", "taskTime": "2019/02/24 12:30:12", "content": "集团拜访任务内容", "workTypeCode": "1001", "emergent": "0"}, {"taskId": "1002", "type": "1", "taskName": "南京市建邺区嘉陵南京市建邺区嘉陵江东街18号新城科技园5江东街18号新城科技园5", "groupId": "111111", "workUrl": "http://", "taskTime": "2019/02/24 12:30:12", "workTypeCode": "1001", "content": " 数字地图任务", "emergent": "1"}, {"taskId": "1003", "type": "1", "taskName": "南京市建邺区嘉陵南京市建邺区嘉陵江东街18号新城科技园5江东街18号新城科技园5", "groupId": "111111", "workUrl": "http://", "taskTime": "2019/02/24 12:30:12", "workTypeCode": "1001", "content": "外呼任务任务内容", "emergent": "1"}, {"taskId": "1005", "type": "1", "taskName": "南京市建邺区嘉陵南京市建邺区嘉陵江东街18号新城科技园5", "groupId": "111111", "workUrl": "http://", "taskTime": "2019/02/24 12:30:12", "workTypeCode": "1004", "content": "小区放装任务任务内容", "emergent": "1"}, {"taskId": "1005", "type": "1", "taskName": "南京市建邺区嘉陵南京市建邺区嘉陵江东街18号新城科技园5", "groupId": "111111", "workUrl": "http://", "taskTime": "2019/02/24 12:30:12", "workTypeCode": "1009", "content": "个人集团任务", "emergent": "1"}]}, "h5DelUnDo": {"retCode": "0", "retMsg": "成功"}, "h5UnDoNum": {"retCode": "0", "retMsg": "成功", "data": [{"planDate": "20190422", "type0Num": "34", "type1Num": "20", "type2Num": "14", "type3Num": "0"}, {"planDate": "20190423", "type0Num": "12", "type1Num": "20", "type2Num": "30", "type3Num": "50"}, {"planDate": "20190424", "type0Num": "20", "type1Num": "7", "type2Num": "3", "type3Num": "10"}, {"planDate": "20190425", "type0Num": "0", "type1Num": "0", "type2Num": "0", "type3Num": "0"}, {"planDate": "20190426", "type0Num": "18", "type1Num": "2", "type2Num": "3", "type3Num": "13"}, {"planDate": "20190427", "type0Num": "0", "type1Num": "0", "type2Num": "0", "type3Num": "0"}, {"planDate": "20190428", "type0Num": "0", "type1Num": "0", "type2Num": "0", "type3Num": "0"}]}, "menuList": {"retCode": "0", "retMsg": null, "data": [{"privName": "签到专区", "hasCrmid": "0", "levelId": 2, "opId": "fsop", "opParentid": "fsop", "itemList": [{"privName": "未开放", "hasCrmid": "0", "levelId": 3, "opId": "fsop", "opParentid": "fsop", "sort": 1, "privId": "1976", "parentId": "101", "picId": "1976", "hasPwd": "", "isHot": null}, {"privName": "打卡", "hasCrmid": "0", "levelId": 3, "opId": "fsop", "opParentid": "fsop", "sort": 1, "privId": "100069", "parentId": "101", "picId": "145", "hasPwd": "", "isHot": null}, {"privName": "位置签到", "hasCrmid": "0", "levelId": 3, "opId": "cmop", "opParentid": "cmop", "sort": 2, "privId": "1966", "parentId": "101", "picId": "1966", "hasPwd": "", "isHot": null}, {"privName": "当换以旧换新", "hasCrmid": "0", "levelId": 3, "opId": "cmop", "opParentid": "cmop", "sort": 2, "privId": "100216", "funcId": "100216", "parentId": "101", "picId": "1966", "hasPwd": "", "isHot": null}, {"privName": "家庭通信诊断", "hasCrmid": "0", "levelId": 3, "opId": "fsop", "opParentid": "fsop", "sort": 2, "privId": "100236", "funcId": "100236", "parentId": "101", "picId": "100236", "hasPwd": "", "authenType": "1", "isHot": null}, {"privName": "家庭通信诊断2", "hasCrmid": "0", "levelId": 3, "opId": "fsop", "opParentid": "fsop", "sort": 2, "privId": "100454", "funcId": "100454", "parentId": "101", "picId": "1966", "hasPwd": "", "authenType": "1", "isHot": null}], "sort": 1, "privId": "101", "parentId": "1", "picId": "10019", "isHot": null}, {"privName": "地图专区", "hasCrmid": "0", "levelId": 2, "opId": "fsop", "opParentid": "fsop", "itemList": [{"privName": "决策地图", "hasCrmid": "0", "levelId": 3, "opId": "cmopApp", "opParentid": "cmop", "sort": 1, "privId": "2974", "parentId": "102", "picId": "2974", "hasPwd": "1", "isHot": null}], "sort": 2, "privId": "102", "parentId": "1", "picId": "10019", "isHot": null}, {"privName": "辅助专区", "hasCrmid": "0", "levelId": 2, "opId": "fsop", "opParentid": "fsop", "itemList": [{"privName": "预占解除", "hasCrmid": "1", "levelId": 3, "opId": "fsop", "opParentid": "fsop", "sort": 1, "privId": "10023", "parentId": "103", "picId": "10019", "isHot": null}, {"privName": "免填单补录", "hasCrmid": "1", "levelId": 3, "opId": "fsop", "opParentid": "fsop", "sort": 2, "privId": "10024", "parentId": "103", "hasPwd": "1", "picId": "10024", "isHot": null}, {"privName": "超时补录", "hasCrmid": "1", "levelId": 3, "opId": "fsop", "opParentid": "fsop", "sort": 3, "privId": "10053", "parentId": "103", "picId": "10053", "isHot": null}, {"privName": "内部10086", "hasCrmid": "0", "levelId": 3, "opId": "cmopApp", "opParentid": "cmop", "sort": 4, "privId": "2847", "parentId": "103", "picId": "2847", "isHot": null}, {"privName": "公告查询", "hasCrmid": "0", "levelId": 3, "opId": "cmopApp", "opParentid": "cmop", "sort": 5, "privId": "2837", "parentId": "103", "picId": "2837", "isHot": null}], "sort": 3, "privId": "103", "parentId": "1", "picId": "10019", "isHot": null}]}, "groupList": {"retCode": "0", "retMsg": null, "data": [{"groupId": "20190301", "groupName": "新大陆集团1"}, {"groupId": "20190302", "groupName": "新大陆集团2"}, {"groupId": "20190303", "groupName": "福建新大陆软件工程有限公司福建新大陆软件福建新大陆软件福建新大陆软件"}, {"groupId": "20190304", "groupName": "现在卓越有限公司"}, {"groupId": "20190305", "groupName": "京东集团"}, {"groupId": "20190306", "groupName": "蚂蚁金服"}, {"groupId": "20190307", "groupName": "阿里巴巴"}, {"groupId": "20190308", "groupName": "阿里马马"}, {"groupId": "20190309", "groupName": "百度集团"}]}}