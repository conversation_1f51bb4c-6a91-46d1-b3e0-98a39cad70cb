{"h5WeekStatistic": {"retCode": "0", "retMsg": null, "data": [{"planDate": "20190923", "planNum": "0"}, {"planDate": "20190924", "planNum": "3"}, {"planDate": "20190925", "planNum": "1"}, {"planDate": "20190926", "planNum": "3"}, {"planDate": "20190927", "planNum": "6"}, {"planDate": "20190928", "planNum": "13"}, {"planDate": "20190929", "planNum": "3"}, {"planDate": "20190930", "planNum": "3"}, {"planDate": "20191001", "planNum": "7"}, {"planDate": "20191002", "planNum": "1"}, {"planDate": "20191004", "planNum": "35"}, {"planDate": "20191005", "planNum": "1"}, {"planDate": "20191006", "planNum": "3"}]}, "h5DecidedPlan": {"retCode": "0", "retMsg": null, "data": [{"villageId": "1001", "villageName": "朝阳新村", "villageActNum": "3", "meaasgePoints": "128", "permeability": "30%", "rushHour": "8:00~11:00", "startTime": "10", "isSendMessage": "1", "inHomeFirst": "1", "isVerify": "1", "planId": "20190422100157"}, {"villageId": "1002", "villageName": "凤凰西街1314号", "villageActNum": "3", "meaasgePoints": "128", "permeability ": "30%", "rushHour": "8:00~11:00", "startTime": "10", "isSendMessage": "1", "inHomeFirst": "1", "isVerify": "1", "planId": "20190422100157"}]}, "h5VillageList": {"retCode": "0", "retMsg": null, "data": [{"villageId": "20190305", "villageName": "中海大厦15A", "villageActNum": "3", "meaasgePoints": "128", "permeability": "30%", "rushHour": "8:00~11:00", "isPlan": "1"}, {"villageId": "20190306", "villageName": "凤凰西街520号", "villageActNum": "3", "meaasgePoints": "128", "permeability": "30%", "rushHour": "8:00~11:00", "isPlan": "1"}, {"villageId": "20190307", "villageName": "凤凰西街1319号", "villageActNum": "3", "meaasgePoints": "128", "permeability": "40%", "rushHour": "8:00~11:00", "isPlan": "1"}]}, "h5VillagePlanSync": {"retCode": "0", "retMsg": null, "data": null}, "h5VillageInfoDetail": {"retCode": "0", "retMsg": null, "data": [{"infoName": "集团地址", "infoValue": "中海大厦办公楼15A", "infoName1": "联系电话", "infoValue1": "025-86099090"}, {"activityType": 1, "activityId": "20190305", "activityName": "活动1", "businessType": "1", "businessName": "专线", "opertaionNum ": 200, "isOperation": 1}, {"activityType": 1, "activityId": "20190305", "activityName": "活动21", "businessType": "1", "businessName": "专线", "opertaionNum ": 200, "isOperation": 1}]}, "h5UserInfoPersonOrFamily": {"retCode": "0", "retMsg": null, "data": [{"msisdn": "***********", "age": 20, "sex": 1, "familyAddress": "环宇城西大门右拐再右拐"}, {"msisdn": "***********", "age": 20, "sex": 1, "familyAddress": "嘉陵江东街18号"}]}, "h5UserInfoVillage": {"retCode": "0", "retMsg": null, "data": [{"mustId": "222", "mustName": "异网", "mustType": "3", "mustValue": "1,2", "mustKey": "是,否"}, {"mustId": "222", "mustName": "异网", "mustType": "3", "mustValue": "1,2", "mustKey": "是,否"}]}, "h5GetPersonalActivityInfo": {"retCode": "0", "retMsg": null, "data": {"hasFamilyAddr": 0, "addressId": "231", "familyAddr": "翠萍小区2栋1034室", "isCall": 0, "businessInfo": {"mainBusiness": "128元4G套餐", "bill1": "78", "bill2": "78", "bill3": "78", "flow1": "2000M", "flow2": "200M", "flow3": "20000M"}, "activeResp": {"activeName": "128元4G套餐", "markTime": "2019-03-05 12:00:00", "result": "2", "reason": "需要考虑", "remark": ""}, "activityList": [{"stepId": "111", "activityId": "20190305", "activityName": "活动1", "businessType": "1", "businessName": "专线", "activityDesc": "这是活动介绍1", "messageInfo": "这是短信内容1", "consider": "考虑原因1∫考虑原因2", "refuse": "拒绝原因1∫拒绝原因2"}, {"stepId": "222", "activityId": "20190306", "activityName": "活动2", "businessType": "1", "businessName": "专线", "activityDesc": "这是活动介绍2", "messageInfo": "这是短信内容2", "consider": "考虑原因1∫考虑原因2", "refuse": "拒绝原因1∫拒绝原因2"}], "attrArray": [{"attrType": "1", "infoArray": [{"infoName": "当前是否安装互联网电视", "infoValue": "是"}, {"infoName": "当前已订购哪些互联网电视增值业务", "infoValue": "未订购"}]}, {"attrType": "2", "infoArray": [{"infoName": "当前是否安装宽带", "infoValue": "是"}, {"infoName": "宽带运营商", "infoValue": "移动"}]}]}}, "h5GetFamilyActivityInfo": {"retCode": "0", "retMsg": null, "data": {"addressId": "231", "familyAddress": "翠萍小区2栋1034室", "bandInfo": [{"dataSource": "2", "hasBand": 0, "bandOwner": 2, "bandOwnerRemark": "测试22", "bandAccount": "***********", "bandSpeed": "150", "overTime": "2020-10-11", "status": "1", "operType": "1", "collectUser": "王涛", "collectTime": "2021/03/15 12:00:00", "fdType": "3", "kdmustInfos": [{"mustId": "12", "mustName": "网费是否大于100", "mustType": "2", "mustValue": "1,2", "mustKey": "是,否", "isInput": "0", "mustUserValue": "1", "mustUserInput": "", "mustFlag": "1"}, {"mustId": "13", "mustName": "网费", "mustType": "1", "mustValue": "", "mustKey": "", "isInput": "1", "mustUserValue": "300yuan", "mustUserInput": "", "mustFlag": "1"}]}, {"dataSource": "2", "hasBand": 0, "bandOwner": 3, "bandOwnerRemark": "测试22", "bandAccount": "***********", "bandSpeed": "150", "overTime": "2020-10-11", "status": "1", "operType": "3", "collectUser": "", "collectTime": "", "fdType": "2", "kdmustInfos": [{"mustId": "12", "mustName": "网费是否大于100", "mustType": "2", "mustValue": "1,2", "mustKey": "是,否", "isInput": "0", "mustUserValue": "1", "mustUserInput": "", "mustFlag": "1"}, {"mustId": "13", "mustName": "网费", "mustType": "1", "mustValue": "", "mustKey": "", "isInput": "1", "mustUserValue": "300yuan", "mustUserInput": "", "mustFlag": "1"}]}, {"dataSource": "1", "hasBand": 0, "bandOwner": 5, "bandOwnerRemark": "测试22", "bandAccount": "***********", "bandSpeed": "150", "overTime": "2020-10-11", "status": "1", "operType": "3", "collectUser": "王涛", "collectTime": "2021/03/15 12:00:00", "fdType": "2", "kdmustInfos": [{"mustId": "12", "mustName": "网费是否大于100", "mustType": "2", "mustValue": "1,2", "mustKey": "是,否", "isInput": "0", "mustUserValue": "1", "mustUserInput": "", "mustFlag": "1"}, {"mustId": "13", "mustName": "网费", "mustType": "1", "mustValue": "", "mustKey": "", "isInput": "1", "mustUserValue": "300yuan", "mustUserInput": "", "mustFlag": "1"}]}, {"dataSource": "1", "hasBand": 0, "bandOwner": 4, "bandOwnerRemark": "测试22", "bandAccount": "***********", "bandSpeed": "150", "overTime": "2020-10-11", "status": "1", "operType": "3", "collectUser": "", "collectTime": "", "fdType": "2", "kdmustInfos": [{"mustId": "12", "mustName": "网费是否大于100", "mustType": "2", "mustValue": "1,2", "mustKey": "是,否", "isInput": "0", "mustUserValue": "1", "mustUserInput": "", "mustFlag": "1"}, {"mustId": "13", "mustName": "网费", "mustType": "1", "mustValue": "", "mustKey": "", "isInput": "1", "mustUserValue": "300yuan", "mustUserInput": "", "mustFlag": "1"}]}, {"dataSource": "1", "hasBand": 0, "bandOwner": 1, "bandOwnerRemark": "测试22", "bandAccount": "***********", "bandSpeed": "150", "overTime": "2020-10-11", "status": "2", "operType": "2", "collectUser": "王涛", "collectTime": "2021/03/15 12:00:00", "fdType": "1", "kdmustInfos": [{"mustId": "12", "mustName": "网费是否大于100", "mustType": "2", "mustValue": "1,2", "mustKey": "是,否", "isInput": "0", "mustUserValue": "1", "mustUserInput": "", "mustFlag": "1"}, {"mustId": "13", "mustName": "网费", "mustType": "1", "mustValue": "", "mustKey": "", "isInput": "1", "mustUserValue": "300yuan", "mustUserInput": "", "mustFlag": "1"}]}], "memberList": [{"dataSource": "1", "msisdn": "**********", "name": "张三3", "age": "22", "sex": "1", "userId": "123", "isStep": "0", "status": "2", "operType": "2", "collectUser": "", "collectTime": "", "mustInfos": [{"mustId": "10", "mustName": "身高大于170", "mustType": "3", "mustValue": "1,2", "mustKey": "是,否", "isInput": "1", "mustUserValue": "2", "mustUserInput": "", "mustFlag": "1"}, {"mustId": "11", "mustName": "体重", "mustType": "1", "mustValue": "", "mustKey": "", "isInput": "0", "mustUserValue": "63kg", "mustUserInput": "", "mustFlag": "1"}]}, {"dataSource": "2", "msisdn": "1367567890", "name": "张三5", "age": "35", "sex": "0", "userId": "124", "isStep": "1", "status": "1", "operType": "2", "collectUser": "王涛", "collectTime": "2021/03/15 12:00:00", "mustInfos": [{"mustId": "10", "mustName": "身高大于170", "mustType": "3", "mustValue": "1,2", "mustKey": "是,否", "isInput": "1", "mustUserValue": "2", "mustUserInput": "", "mustFlag": "1"}, {"mustId": "11", "mustName": "体重", "mustType": "1", "mustValue": "", "mustKey": "", "isInput": "0", "mustUserValue": "63kg", "mustUserInput": "", "mustFlag": "1"}]}, {"dataSource": "1", "msisdn": "***********", "name": "张三8", "age": "68", "sex": "0", "userId": "125", "isStep": "0", "status": "1", "operType": "3", "collectUser": "", "collectTime": "", "mustInfos": [{"mustId": "10", "mustName": "身高大于170", "mustType": "3", "mustValue": "1,2", "mustKey": "是,否", "isInput": "1", "mustUserValue": "2", "mustUserInput": "", "mustFlag": "1"}, {"mustId": "11", "mustName": "体重", "mustType": "1", "mustValue": "", "mustKey": "", "isInput": "0", "mustUserValue": "63kg", "mustUserInput": "", "mustFlag": "1"}]}], "activityList": [{"stepId": "111", "activityId": "20190305", "activityName": "活动1", "businessType": "1", "businessName": "专线", "activityDesc": "这是活动介绍1", "consider": "考虑原因1∫考虑原因2", "refuse": "拒绝原因1∫拒绝原因2"}, {"stepId": "222", "activityId": "20190305", "activityName": "活动1", "businessType": "1", "businessName": "专线", "activityDesc": "这是活动介绍2", "consider": "考虑原因1∫考虑原因2", "refuse": "拒绝原因1∫拒绝原因2"}], "customInfos": [{"customType": "1", "mustId": "10", "mustName": "身高大于170", "mustType": "3", "mustValue": "1,2", "mustKey": "是,否", "isInput": "1", "mustFlag": "1"}, {"customType": "1", "mustId": "11", "mustName": "体重", "mustType": "1", "mustValue": "", "mustKey": "", "isInput": "0", "mustFlag": "1"}, {"customType": "2", "mustId": "10", "mustName": "身高大于170", "mustType": "3", "mustValue": "1,2", "mustKey": "是,否", "isInput": "1", "mustFlag": "1"}, {"customType": "2", "mustId": "11", "mustName": "体重", "mustType": "1", "mustValue": "", "mustKey": "", "isInput": "0", "mustFlag": "1"}], "attrArray": [{"attrType": "1", "infoArray": [{"infoName": "宽带速率", "infoValue": "200"}, {"infoName": "宽带到期时间", "infoValue": "20210304"}, {"infoName": "小区宽带接入方式", "infoValue": "FTTH"}, {"infoName": "是否安装互联网电视", "infoValue": "是"}, {"infoName": "宽带套餐名称", "infoValue": "宽带标准资费"}, {"infoName": "宽带账号主体产品名称", "infoValue": "飞享（预付费）"}]}, {"attrType": "2", "infoArray": [{"infoName": "当前是否安装宽带", "infoValue": "是"}, {"infoName": "宽带运营商", "infoValue": "移动"}]}]}}, "h5SignInOut": {"retCode": "0", "retMsg": null, "data": null}, "h5VillageMustDo": {"retCode": "0", "retMsg": null, "data": null}, "h5VillageVisitSync": {"retCode": "0", "retMsg": null, "data": null}, "h5VillageCollectInfo": {"retCode": "0", "retMsg": null, "data": null}, "h5GetBusinessType": {"retCode": "0", "retMsg": null, "data": [{"businessName": "让渡", "businessType": "3"}, {"businessName": "信息采集", "businessType": "4"}]}, "h5CustomerInfo": {"retCode": "0", "retMsg": null, "data": {"primaryPkg": "主体套餐", "bill": [{"month": "1", "val": "7.89"}, {"month": "2", "val": "67.89"}, {"month": "3", "val": "10.89"}], "flux": [{"month": "1", "val": "20.07G"}, {"month": "2", "val": "900.76M"}, {"month": "3", "val": "18.64G"}]}}, "h5GetVillageInfoDetail": {"retCode": "0", "retMsg": null, "data": {"workId": "111111", "villageId": "1001", "villageName": "翠屏小区", "isSign": "0", "isPhoto": "0", "villageBaseInfo": [{"infoName": "集团地址", "infoValue": "嘉临江东街。。。。"}, {"infoName": "联系电话", "infoValue": "025-86099090"}], "activieList": [{"stepId": "11111", "activityType": "1", "activityId": "20190305", "activityName": "活动1", "businessType": "1", "businessName": "专线1", "opertaionNum": "12", "isOperation": "1"}, {"stepId": "222", "activityType": "2", "activityId": "20190306", "activityName": "活动2", "businessType": "1", "businessName": "专线2", "opertaionNum": "6", "isOperation": "1"}, {"stepId": "333", "activityType": "2", "activityId": "20190306", "activityName": "广告张贴", "businessType": "1", "businessName": "专线2", "opertaionNum": "0", "isOperation": "1"}, {"stepId": "333", "activityType": "1", "activityId": "20190307", "activityName": "广告张贴2", "businessType": "1", "businessName": "专线2", "opertaionNum": "0", "isOperation": "1"}, {"stepId": "333", "activityType": "3", "activityId": "20190308", "activityName": "广告张贴3", "businessType": "1", "businessName": "专线2", "opertaionNum": "0", "isOperation": "0"}]}}, "h5GetUserInfoPersOrFm": {"retCode": "0", "retMsg": null, "data": {"workId": "111111", "villageId": "1001", "activityId": "100023232", "messageInfo": "欢迎…….", "userInfoList": [{"userId": "1001", "msisdn": "***********", "age": "", "sex": "1", "addressId": "111", "familyAddress": "嘉陵江。。。。1"}, {"userId": "1002", "msisdn": "***********", "age": "21", "sex": "1", "addressId": "111", "familyAddress": "嘉陵江。。。。2"}, {"userId": "1003", "msisdn": "***********", "age": "22", "sex": "1", "addressId": "111", "familyAddress": "嘉陵江。。。。3"}, {"userId": "1004", "msisdn": "***********", "age": "23", "sex": "1", "addressId": "111", "familyAddress": "嘉陵江。。。。4"}, {"userId": "1005", "msisdn": "***********", "age": "24", "sex": "1", "addressId": "111", "familyAddress": "嘉陵江。。。。5"}, {"userId": "1006", "msisdn": "***********", "age": "25", "sex": "1", "addressId": "111", "familyAddress": "嘉陵江。。。。6"}, {"userId": "1007", "msisdn": "13675177897", "age": "26", "sex": "1", "addressId": "111", "familyAddress": "嘉陵江。。。。7"}, {"userId": "1008", "msisdn": "13675177898", "age": "27", "sex": "1", "addressId": "111", "familyAddress": "嘉陵江。。。。8"}, {"userId": "1009", "msisdn": "13675177899", "age": "28", "sex": "1", "addressId": "111", "familyAddress": "嘉陵江。。。。9"}]}}, "h5GetUserInfoVillage": {"retCode": "0", "retMsg": null, "data": {"streamSeq": "4320180606141834234", "workId": "111111", "villageId": "1001", "activityId": "100023232", "workFlag": "1", "isPhoto": "0", "mustInfoList": [{"mustId": "111", "mustName": "异网1", "mustType": 1, "isInput": 1, "mustValue": "", "mustKey": ""}, {"mustId": "222", "mustName": "异网2", "mustType": 2, "isInput": 0, "mustValue": "1,2,3", "mustKey": "A,B,C"}, {"mustId": "333", "mustName": "异网3", "mustType": 3, "isInput": 0, "mustValue": "1,2", "mustKey": "是,否"}, {"mustId": "444", "mustName": "异网4", "mustType": 4, "isInput": 0, "mustValue": "1,2", "mustKey": "是,否"}]}}, "h5GetVillageFullAmountInfo": {"retCode": "0", "retMsg": null, "data": [{"groupId": "1433243434", "groupName": "翠屏小区"}, {"groupId": "1433243435", "groupName": "翠屏小区一期"}]}, "h5GetVillageDetailInfo": {"retCode": "0", "retMsg": null, "data": {"villageViewHead": {"villageName": "宋都美誉", "villageType": "住宅", "villageIncome": "1000", "occupancy": "70%", "famliyCount": "3200", "address": "南京市江宁区双龙大道3000号"}, "villageDetailInfos": [{"firstMenuName": "小区基础信息", "firstMenuInfo": [{"secondMenuName": "小区名称", "secondMenuInfo": "宋都美誉"}, {"secondMenuName": "小区类型", "secondMenuInfo": "住宅"}, {"secondMenuName": "小区收入", "secondMenuInfo": "1000"}, {"secondMenuName": "入住率情况", "secondMenuInfo": "70%"}, {"secondMenuName": "乡镇名称", "secondMenuInfo": "江宁"}, {"secondMenuName": "区县名称", "secondMenuInfo": "江宁区"}, {"secondMenuName": "街道名称", "secondMenuInfo": "秣陵街道"}, {"secondMenuName": "小区经度", "secondMenuInfo": "E98.09"}, {"secondMenuName": "社区经理工号", "secondMenuInfo": "123221"}, {"secondMenuName": "社区经理名称", "secondMenuInfo": "范无忧"}, {"secondMenuName": "入住率情况", "secondMenuInfo": "70%"}, {"secondMenuName": "家庭个数", "secondMenuInfo": "3200"}]}, {"firstMenuName": "小区业务信息", "firstMenuInfo": [{"secondMenuName": "宽带到期个数", "secondMenuInfo": "1"}, {"secondMenuName": "小区移动宽带用户渗透（覆盖数）", "secondMenuInfo": "1"}, {"secondMenuName": "小区用户宽带活跃率", "secondMenuInfo": "1"}, {"secondMenuName": "是否宽带低渗透率小区", "secondMenuInfo": "1"}, {"secondMenuName": "净增宽带用户数", "secondMenuInfo": "1"}, {"secondMenuName": "平均用户宽带上网时长", "secondMenuInfo": "1"}, {"secondMenuName": "潜在宽带离网用户占比", "secondMenuInfo": "1"}, {"secondMenuName": "新增宽带用户数", "secondMenuInfo": "2"}]}, {"firstMenuName": "小区预警信息", "firstMenuInfo": [{"secondMenuName": "宽带负净增用户数", "secondMenuInfo": "3"}, {"secondMenuName": "宽带活跃大于3天的用户数", "secondMenuInfo": "3"}, {"secondMenuName": "续费率是否低于70%", "secondMenuInfo": "4"}]}]}}, "h5qryOperatorInfo": {"retCode": "0", "retMsg": null, "data": [{"operatorId": "1000001", "operatorName": "戴波"}, {"operatorId": "10002", "operatorName": "周文"}, {"operatorId": "1003", "operatorName": "张三"}, {"operatorId": "1004", "operatorName": "李四"}, {"operatorId": "1005", "operatorName": "王五"}, {"operatorId": "1006", "operatorName": "找六"}, {"operatorId": "1007", "operatorName": "李四"}, {"operatorId": "1008", "operatorName": "李四板仓街192号13发生的李四板仓街192号13发生的"}, {"operatorId": "1009", "operatorName": "找六"}, {"operatorId": "1010", "operatorName": "李四板仓街192号13发生的"}]}, "h5QryVillageList": {"retCode": "0", "retMsg": null, "data": [{"villageId": "30774", "villageName": "板仓街192号", "villageAddress": "板仓街192号", "villageType": "0", "addressId": "8105450", "constMgr": "1", "otherAgent": "1", "radiusFor": "1", "installCorpid": "18229", "planCategory": "1", "userType": "1", "villageX": "118.8181", "villageY": "32.0771", "fullName": "南京市建邺区嘉陵江东街18号南京市建邺区嘉陵江东街18号", "countryId": "1419"}, {"villageId": "30734", "villageName": "东南大学", "villageAddress": "东南大学192号", "villageType": "0", "addressId": "8105450", "constMgr": "1", "otherAgent": "1", "radiusFor": "1", "installCorpid": "18229", "planCategory": "1", "userType": "2", "villageX": "118.8181", "villageY": "32.0771", "fullName": "南京市建邺区嘉陵江东街18号", "countryId": "1419"}, {"villageId": "9430", "villageName": "凤凰街小区", "villageAddress": "凤凰街小区", "villageType": "0", "addressId": "2002796", "constMgr": "1", "otherAgent": "1", "radiusFor": "1", "installCorpid": "18229", "planCategory": "1", "userType": "1", "villageX": "118.7556", "villageY": "32.04587", "fullName": null, "countryId": "1419"}, {"villageId": "9684", "villageName": "十里牌安置小区", "villageAddress": "十里牌安置小区", "villageType": "0", "addressId": "2003479", "constMgr": "1", "otherAgent": "1", "radiusFor": "1", "installCorpid": "18230", "planCategory": "5", "userType": "1", "villageX": "119.08078", "villageY": "31.680729", "fullName": null, "countryId": "1424"}, {"villageId": "31877", "villageName": "热河南路五段小区", "villageAddress": "热河南路五段小区", "villageType": "0", "addressId": "8622341", "constMgr": "1", "otherAgent": "1", "radiusFor": "1", "installCorpid": "18229", "planCategory": "1", "userType": "1", "villageX": "118.7404", "villageY": "32.0831", "fullName": null, "countryId": "1419"}, {"villageId": "13226", "villageName": "金象园小区", "villageAddress": "金象园小区", "villageType": "0", "addressId": "2549976", "constMgr": "1", "otherAgent": "1", "radiusFor": "1", "installCorpid": "18229", "planCategory": "1", "userType": "1", "villageX": "118.80636", "villageY": "32.02236", "fullName": null, "countryId": "1419"}, {"villageId": "10070", "villageName": "海棠里小区", "villageAddress": "海棠里小区", "villageType": "0", "addressId": "2384617", "constMgr": "1", "otherAgent": "1", "radiusFor": "1", "installCorpid": "18229", "planCategory": "4", "userType": "1", "villageX": "118.73689", "villageY": "32.04266", "fullName": null, "countryId": "1419"}, {"villageId": "9672", "villageName": "经典花园小区", "villageAddress": "经典花园小区", "villageType": "0", "addressId": "2003449", "constMgr": "1", "otherAgent": "1", "radiusFor": "1", "installCorpid": "18230", "planCategory": "4", "userType": "1", "villageX": "119.04942", "villageY": "31.656267", "fullName": null, "countryId": "1424"}, {"villageId": "27606", "villageName": "宁中巷小区", "villageAddress": "宁中巷小区", "villageType": "0", "addressId": "6605817", "constMgr": "1", "otherAgent": "1", "radiusFor": "1", "installCorpid": "18229", "planCategory": "1", "userType": "1", "villageX": "118.84864", "villageY": "31.95917", "fullName": null, "countryId": "1423"}, {"villageId": "29053", "villageName": "永宁雅苑小区", "villageAddress": "永宁雅苑小区", "villageType": "0", "addressId": "7579113", "constMgr": "1", "otherAgent": "1", "radiusFor": "1", "installCorpid": "18229", "planCategory": "4", "userType": "1", "villageX": "118.8458", "villageY": "32.3568", "fullName": null, "countryId": "1422"}]}, "h5QryCareVillage": {"retCode": "0", "retMsg": null, "data": [{"villageId": "222553", "villageName": "顾家村101号", "villageAddress": "顾家村101号", "villageType": "0", "otherAgent": "1", "constMgr": "1", "radiusFor": "1", "installCorpid": "18231", "planCategory": "1", "userType": "1", "addressId": "11111", "fullName": "南京市建邺区嘉陵江东街18号", "countryId": "1419"}, {"villageId": "95012", "villageName": "河海大学江宁校区", "villageAddress": "河海大学江宁校区", "villageType": "1", "otherAgent": "1", "constMgr": "1", "radiusFor": "1", "installCorpid": "47", "planCategory": "7", "userType": "2", "addressId": "21699756", "fullName": "南京市建邺区嘉陵江东街18号南京市建邺区嘉陵江东街18号", "countryId": "1423"}, {"villageId": "95032", "villageName": "河海大学江宁校区", "villageAddress": "河海大学江宁校区", "villageType": "1", "otherAgent": "1", "constMgr": "1", "radiusFor": "1", "installCorpid": "47", "planCategory": "7", "userType": "2", "addressId": "21699756", "fullName": "南京市建邺区嘉陵江东街18号南京市建邺区嘉陵江东街18号", "countryId": "1423"}, {"villageId": "95412", "villageName": "河海大学江宁校区", "villageAddress": "河海大学江宁校区", "villageType": "1", "otherAgent": "1", "constMgr": "1", "radiusFor": "1", "installCorpid": "47", "planCategory": "7", "userType": "2", "addressId": "21699756", "fullName": "南京市建邺区嘉陵江东街18号南京市建邺区嘉陵江东街18号", "countryId": "1423"}]}, "h5QryInnerAddress0": {"retCode": "0", "retMsg": null, "data": [{"addressId": "103542915", "addressName": "901", "fullName": "无锡地区无锡市区惠山区锡澄路赛维拉三期37-38栋38 1001", "isCoverpoint": "0", "villageId": null, "businessStatus": "1", "status": "1"}, {"addressId": "103542884", "addressName": "903", "fullName": "无锡地区无锡市区惠山区锡澄路赛维拉三期37-38栋38 903", "isCoverpoint": "0", "villageId": null, "businessStatus": "1", "status": "1"}, {"addressId": "103542883", "addressName": "904", "fullName": "无锡地区无锡市区惠山区锡澄路赛维拉三期37-38栋38 904", "isCoverpoint": "0", "villageId": null, "businessStatus": "0", "status": "1"}, {"addressId": "103542884", "addressName": "905", "fullName": "无锡地区无锡市区惠山区锡澄路赛维拉三期37-38栋38 903", "isCoverpoint": "0", "villageId": null, "businessStatus": "0", "status": "1"}, {"addressId": "103542883", "addressName": "906", "fullName": "无锡地区无锡市区惠山区锡澄路赛维拉三期37-38栋38 904", "isCoverpoint": "0", "villageId": null, "businessStatus": "1", "status": "1"}, {"addressId": "103542884", "addressName": "907", "fullName": "无锡地区无锡市区惠山区锡澄路赛维拉三期37-38栋38 903", "isCoverpoint": "0", "villageId": null, "businessStatus": "1", "status": "1"}, {"addressId": "103542883", "addressName": "908", "fullName": "无锡地区无锡市区惠山区锡澄路赛维拉三期37-38栋38 904", "isCoverpoint": "0", "villageId": null, "businessStatus": "0", "status": "1"}, {"addressId": "103542884", "addressName": "909", "fullName": "无锡地区无锡市区惠山区锡澄路赛维拉三期37-38栋38 903", "isCoverpoint": "0", "villageId": null, "businessStatus": "1", "status": "1"}, {"addressId": "103542883", "addressName": "9010", "fullName": "无锡地区无锡市区惠山区锡澄路赛维拉三期37-38栋38 904", "isCoverpoint": "0", "businessStatus": "0", "villageId": null, "status": "1"}, {"addressId": "103572915", "addressName": "9001", "fullName": "无锡地区无锡市区惠山区锡澄路赛维拉三期37-38栋38 1001", "isCoverpoint": "0", "businessStatus": "0", "villageId": null, "status": "1"}, {"addressId": "103572884", "addressName": "9003", "fullName": "无锡地区无锡市区惠山区锡澄路赛维拉三期37-38栋38 903", "isCoverpoint": "0", "businessStatus": "0", "villageId": null, "status": "1"}, {"addressId": "103572883", "addressName": "9004", "fullName": "无锡地区无锡市区惠山区锡澄路赛维拉三期37-38栋38 904", "isCoverpoint": "0", "villageId": null, "businessStatus": "1", "status": "1"}, {"addressId": "103572884", "addressName": "9005", "fullName": "无锡地区无锡市区惠山区锡澄路赛维拉三期37-38栋38 903", "isCoverpoint": "0", "villageId": null, "businessStatus": "1", "status": "1"}, {"addressId": "103572883", "addressName": "9006", "fullName": "无锡地区无锡市区惠山区锡澄路赛维拉三期37-38栋38 904", "isCoverpoint": "0", "businessStatus": "1", "villageId": null, "status": "1"}, {"addressId": "103572884", "addressName": "9007", "fullName": "无锡地区无锡市区惠山区锡澄路赛维拉三期37-38栋38 903", "isCoverpoint": "0", "villageId": null, "businessStatus": "0", "status": "1"}, {"addressId": "103572883", "addressName": "9008", "fullName": "无锡地区无锡市区惠山区锡澄路赛维拉三期37-38栋38 904", "isCoverpoint": "0", "businessStatus": "1", "villageId": null, "status": "1"}, {"addressId": "103572884", "addressName": "9009", "fullName": "无锡地区无锡市区惠山区锡澄路赛维拉三期37-38栋38 903", "isCoverpoint": "0", "businessStatus": "1", "villageId": null, "status": "1"}, {"addressId": "103572883", "addressName": "8010", "fullName": "无锡地区无锡市区惠山区锡澄路赛维拉三期37-38栋38 904", "isCoverpoint": "0", "businessStatus": "0", "villageId": null, "status": "1"}, {"addressId": "101542915", "addressName": "701", "fullName": "无锡地区无锡市区惠山区锡澄路赛维拉三期37-38栋38 1001", "isCoverpoint": "0", "villageId": null, "businessStatus": "1", "status": "0"}, {"addressId": "102542884", "addressName": "703", "fullName": "无锡地区无锡市区惠山区锡澄路赛维拉三期37-38栋38 903", "isCoverpoint": "0", "businessStatus": "1", "villageId": null, "status": "1"}, {"addressId": "103342883", "addressName": "704", "fullName": "无锡地区无锡市区惠山区锡澄路赛维拉三期37-38栋38 904", "isCoverpoint": "0", "villageId": null, "businessStatus": "1", "status": "1"}, {"addressId": "104542884", "addressName": "705", "fullName": "无锡地区无锡市区惠山区锡澄路赛维拉三期37-38栋38 903", "isCoverpoint": "0", "villageId": null, "businessStatus": "0", "status": "1"}, {"addressId": "105542883", "addressName": "706", "fullName": "无锡地区无锡市区惠山区锡澄路赛维拉三期37-38栋38 904", "isCoverpoint": "0", "villageId": null, "businessStatus": "0", "status": "1"}, {"addressId": "106542884", "addressName": "707", "fullName": "无锡地区无锡市区惠山区锡澄路赛维拉三期37-38栋38 903", "isCoverpoint": "0", "businessStatus": "1", "villageId": null, "status": "1"}, {"addressId": "107542883", "addressName": "708", "fullName": "无锡地区无锡市区惠山区锡澄路赛维拉三期37-38栋38 904", "isCoverpoint": "0", "businessStatus": "1", "villageId": null, "status": "1"}, {"addressId": "108542884", "addressName": "709", "fullName": "无锡地区无锡市区惠山区锡澄路赛维拉三期37-38栋38 903", "isCoverpoint": "0", "businessStatus": "1", "villageId": null, "status": "1"}, {"addressId": "109542883", "addressName": "7010", "fullName": "无锡地区无锡市区惠山区锡澄路赛维拉三期37-38栋38 904", "isCoverpoint": "0", "businessStatus": "0", "villageId": null, "status": "1"}]}, "h5QryInnerAddress1": {"retCode": "0", "retMsg": null, "data": [{"addressId": "111111111", "addressName": "37-38栋38", "fullName": "无锡地区无锡市区惠山区锡澄路赛维拉三期37-38栋38 1001", "isCoverpoint": "1", "villageId": null, "businessStatus": "0", "status": "1"}, {"addressId": "103542384", "addressName": "37-38栋38", "fullName": "无锡地区无锡市区惠山区锡澄路赛维拉三期37-38栋38 903", "isCoverpoint": "1", "businessStatus": "0", "villageId": null, "status": "1"}, {"addressId": "103542883", "addressName": "47-49栋48", "fullName": "无锡地区无锡市区惠山区锡澄路赛维拉三期47-49栋38 904", "isCoverpoint": "1", "businessStatus": "0", "villageId": null, "status": "1"}, {"addressId": "103542384", "addressName": "47-48栋48", "fullName": "无锡地区无锡市区惠山区锡澄路赛维拉三期37-38栋38 903", "isCoverpoint": "1", "businessStatus": "0", "villageId": null, "status": "1"}, {"addressId": "103542883", "addressName": "47-49栋48", "fullName": "无锡地区无锡市区惠山区锡澄路赛维拉三期37-39栋38 904", "isCoverpoint": "1", "businessStatus": "0", "villageId": null, "status": "1"}]}, "h5QryInnerAddress2": {"retCode": "0", "retMsg": null, "data": [{"addressId": "1111111", "addressName": "无门牌", "fullName": "无锡地区无锡市区惠山区锡澄路赛维拉三期37-38栋38 1001", "isCoverpoint": "1", "businessStatus": "0", "villageId": null, "status": "1"}]}, "h5AddressSubmit": {"retCode": "0", "retMsg": null, "data": {"deviceBigList": [{"deviceClass": "HGW", "deviceClassName": "家庭网关", "getMode": "2"}], "deviceSmallList": [{"typeid": "151000000000000016", "typename": "家庭网关"}], "isSchoolVillage": false}}, "h5QryMBandAndExtraProd": {"retCode": "0", "retMsg": null, "data": {"mbandProdList": [{"prodId": "2000009174", "prodName": "50元包月（100M，手机最低消费8元）", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}, {"prodId": "2000009213", "prodName": "600元包月(1000M）", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}, {"prodId": "2000009211", "prodName": "150元包月(300M）", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}], "extraProdList": [{"prodId": "2000007857", "prodName": "10元提速包（20M提至50M）", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}, {"prodId": "2000009057", "prodName": "10元提速包（提至100M）", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}, {"prodId": "2000002748", "prodName": "竣工套餐", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}, {"prodId": "2011002057", "prodName": "当月免费", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}, {"prodId": "2400000979", "prodName": "宽带接入费", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}], "mustProdList": [{"prodId": "2413000030", "prodName": "智能组网基础服务", "pkgprodId": "1", "isPackage": "0", "effectType": "0"}, {"prodId": "2413000031", "prodName": "智能组网基础服务2", "pkgprodId": "1", "isPackage": "0", "effectType": "0"}, {"prodId": "2400000979", "prodName": "宽带接入费", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}]}}, "h5MbandCharge": {"retCode": "0", "retMsg": null, "data": {"linkName": "2", "adress": "嘉陵西街", "installDate": "2018-02-02 12:12", "totalFee": "5000", "businessName": "宽带开通", "chargeList": [{"chargecode": "3206", "chargemoney": "0", "charegename": "宽带接入费"}, {"chargecode": "DeviceTestFee", "chargemoney": "5000", "charegename": "家庭终端调试费"}], "crditFee": "0", "isDevicefee": "0"}}, "h5MbandSubmit": {"retCode": "0", "retMsg": "Success", "data": {"recoId": "200113294755317210", "totalFee": "5000", "chargeList": [{"chargecode": "3206", "chargemoney": "0", "charegename": "宽带接入费"}, {"chargecode": "DeviceTestFee", "chargemoney": "5000", "charegename": "家庭终端调试费"}]}}, "h5QryProdDesc": {"retCode": "0", "retMsg": null, "data": {"prodDesc": "指定用户宽带接入速率免费体验20M（最大可达），体验期三个月，从生效月1号开始计算，到期后自动终止，速率返回到用户当月有效套餐指定速率。"}}, "h5qryVillageInfoForCollection": {"retCode": "0", "retMsg": null, "data": [{"levelId": "3", "addressId": "1001", "addressName": "1013室", "collectStatus": "2", "subObjectId": "1001", "subObjectName": "新大陆", "cntBinding": "3", "objectType": "1"}, {"levelId": "2", "addressId": "1002", "addressName": "1022室", "collectStatus": "1", "subObjectId": "1001", "subObjectName": "新大陆1", "cntBinding": "2", "objectType": "2"}, {"levelId": "3", "addressId": "10022", "addressName": "102室", "collectStatus": "3", "subObjectId": "1001", "subObjectName": "新大陆5", "cntBinding": "0", "objectType": ""}, {"levelId": "3", "addressId": "1002", "addressName": "102室", "collectStatus": "3", "objectType": ""}, {"levelId": "3", "addressId": "1001", "addressName": "101室", "collectStatus": "2", "objectType": ""}, {"levelId": "2", "addressId": "1002", "addressName": "102室", "collectStatus": "1", "objectType": ""}, {"levelId": "3", "addressId": "1002", "addressName": "102室", "collectStatus": "3", "objectType": ""}, {"levelId": "3", "addressId": "1002", "addressName": "102室", "collectStatus": "3", "objectType": "3"}, {"levelId": "3", "addressId": "1001", "addressName": "201室", "collectStatus": "2", "objectType": ""}, {"levelId": "2", "addressId": "2002", "addressName": "202室", "collectStatus": "1", "objectType": "3"}, {"levelId": "3", "addressId": "2002", "addressName": "202室", "collectStatus": "3", "objectType": "2"}, {"levelId": "3", "addressId": "2002", "addressName": "202室", "collectStatus": "3", "objectType": "1"}]}, "h5QryMemberInfo": {"retCode": "0", "retMsg": "success", "data": {"mainGroup": [{"groupName": "全国亲情网", "pkgprodid": "2000009794", "bbossProdInstId": "201907080001", "region": "14", "familysubsid": "1210101240874015", "poidcode": "01", "poidlable": "组一", "membeinfo": [{"memsubsid": "1210101240874015", "servnumber": "15189807945", "isprima": "1", "memregion": "14", "shortnum": "751", "startdate": "20180915100101", "enddate": "21001211101010", "memlable": "7945"}, {"memsubsid": "1210101240874016", "servnumber": "15189807946", "isprima": "0", "memregion": "14", "shortnum": "", "startdate": "20180915100101", "enddate": "21001211101010", "memlable": "7946"}]}, {"groupName": "全国亲情网", "pkgprodid": "2000009794", "bbossProdInstId": "201907080002", "region": "14", "familysubsid": "1210101240874015", "poidcode": "02", "poidlable": "组二", "membeinfo": [{"memsubsid": "1210101240874015", "servnumber": "15189807945", "isprima": "1", "memregion": "14", "shortnum": "", "startdate": "20180915100101", "enddate": "21001211101010", "memLable": "7945"}, {"memsubsid": "1210101240874016", "servnumber": "15189807946", "isprima": "0", "memregion": "14", "shortnum": "", "startdate": "20180915100101", "enddate": "21001211101010", "memLable": "7946"}]}, {"groupName": "全国亲情网", "pkgprodid": "2000009794", "bbossProdInstId": "201907080003", "region": "14", "familysubsid": "1210101240874015", "poidcode": "03", "poidlable": "组三", "membeinfo": [{"memsubsid": "1210101240874015", "servnumber": "15189807945", "isprima": "1", "memregion": "14", "shortnum": "", "startdate": "20180915100101", "enddate": "21001211101010", "memLable": "7945"}, {"memsubsid": "1210101240874016", "servnumber": "15189807946", "isprima": "0", "memregion": "14", "shortnum": "", "startdate": "20180915100101", "enddate": "21001211101010", "memLable": "7946"}]}], "subGroup": [{"groupName": "全国亲情网", "pkgprodid": "2000009794", "bbossProdInstId": "201907080004", "region": "14", "familysubsid": "1210101240874016", "poidcode": "04", "poidlable": "组四", "membeinfo": [{"memsubsid": "1210101240874016", "servnumber": "15189807946", "isprima": "1", "memregion": "14", "shortnum": "761", "startdate": "20180915100101", "enddate": "21001211101010", "memLable": "7946"}, {"memsubsid": "1210101240874015", "servnumber": "15189807945", "isprima": "0", "memregion": "14", "shortnum": "", "startdate": "20180915100101", "enddate": "21001211101010", "memLable": "7945"}]}, {"groupName": "全国亲情网", "pkgprodid": "2000009794", "bbossProdInstId": "201907080005", "region": "14", "familysubsid": "1210101240874016", "poidcode": "05", "poidlable": "组五", "membeinfo": [{"memsubsid": "1210101240874016", "servnumber": "15189807946", "isprima": "1", "memregion": "14", "shortnum": "", "startdate": "20180915100101", "enddate": "21001211101010", "memLable": "7946"}, {"memsubsid": "1210101240874015", "servnumber": "15189807945", "isprima": "0", "memregion": "14", "shortnum": "", "startdate": "20180915100101", "enddate": "21001211101010", "memLable": "7945"}]}], "shortNumStatus": "1", "shortNum": "751", "poidcode": "01", "poidcodeStr": "01<br>02<br>03<br>04<br>05"}}, "h5QryMbandProd": {"retCode": "0", "retMsg": null, "data": {"mbandProdHasOreder": [{"prodId": "2000001683", "prodName": "宽带标准资费", "smallType": null, "parentProdId": null, "limitBandWidth": null, "startDate": "2018-08-01", "endDate": "2100-12-31", "isPakage": null, "pakageId": null, "effectType": null}], "mbandProdNoOrder": [{"prodId": "2000009213", "prodName": "600元包月(1000M）", "smallType": "4", "parentProdId": "2013000001", "limitBandWidth": "", "startDate": null, "endDate": null, "isPakage": "0", "pakageId": "2013000001", "effectType": "2"}, {"prodId": "2000009174", "prodName": "50元包月（100M，手机最低消费8元）", "smallType": "4", "parentProdId": "2013000001", "limitBandWidth": "100", "startDate": null, "endDate": null, "isPakage": "0", "pakageId": "2013000001", "effectType": "2"}, {"prodId": "2000009212", "prodName": "300元包月(500M）", "smallType": "4", "parentProdId": "2013000001", "limitBandWidth": "", "startDate": null, "endDate": null, "isPakage": "0", "pakageId": "2013000001", "effectType": "2"}]}}, "h5MbandChange": {"retCode": "0", "retMsg": null, "data": {"interfaceSeq": "2019071710074984", "orderId": "200113106363403222", "recoId": "200113106363403111", "orderResult": "0"}}, "h5GetProductList": {"retCode": "0", "retMsg": null, "data": [{"offerId": "2010141018", "offerName": "新动感地带必选套餐[必选包]", "becode": null, "status": null, "packageId": "2690012736", "createDate": null}, {"offerId": "2000002113", "offerName": "省内非常假期-宿迁区域话音包", "becode": null, "status": null, "packageId": "2690012734", "createDate": null}, {"offerId": "2000007807", "offerName": "0元提速20M", "becode": null, "status": null, "packageId": "2000007807", "createDate": null}, {"offerId": "2000001854", "offerName": "动感校园行E套餐", "becode": null, "status": null, "packageId": "2010141018", "createDate": null}]}, "h5OrderSubmit": {"retCode": "0", "retMsg": null, "data": {"crmOrderId": "200113106363403222", "orderId": "200113106363403111"}}}