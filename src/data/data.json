{"weekStatistic": {"retCode": "0", "retMsg": {}, "data": [{"planDate": "20190422", "planNum": "2"}, {"planDate": "20190423", "planNum": "2"}, {"planDate": "20190426", "planNum": "5"}, {"planDate": "20190430", "planNum": "7"}]}, "groupDetail": {"retCode": "0", "retMsg": "", "data": {"isSign": "0", "groupId": "231412341324", "groupName": "新大陆集团", "groupInfo": {"手机用户份额": "30%", "用户收入": "0", "集团地址": "江苏省南京市建业区嘉陵江东街18号 6栋17楼嘉陵江东街1"}, "activieList": [{"activityId": "11111", "activityName": "活动1", "businessType": "2", "businessName": "集团专线", "businessDesc": "营销话术adfadfdafdafda内容", "filePath": "", "consider": "考虑1∫考虑2", "refuse": "拒绝1∫拒绝2", "visitList": [{"visitId": "124312", "visitName": "二次拜访", "visitIntention": 1, "refuseReason": "不喜欢∫喜欢", "visitDesc": "备注", "visitDate": "2019/03/26 09:32"}, {"visitId": "124312", "visitName": "一次拜访", "visitIntention": 1, "refuseReason": "不喜欢∫喜欢", "visitDesc": "备注", "visitDate": "2019/03/25 09:32:09"}]}, {"activityId": "22222", "activityName": "活动***********", "businessType": "2", "businessName": "云视讯", "businessDesc": "互联网专线是依托中国移动的传输网络资源，通过传输电路连接CMNET的方式向集团客户提供独享带宽的互联网接入及访问服务", "filePath": "1111.docx", "consider": "考虑2∫考虑2", "refuse": "拒绝2∫拒绝2", "visitList": [{"visitId": "124312", "visitName": "二次拜访", "visitIntention": 2, "refuseReason": "不喜欢∫喜欢", "visitDesc": "备注", "visitDate": "2019/03/24 09:32"}, {"visitId": "124312", "visitName": "一次拜访", "visitIntention": 1, "refuseReason": "不喜欢∫喜欢", "visitDesc": "备注", "visitDate": "2019/03/27 09:32"}]}, {"activityId": "33333", "activityName": "活动1", "businessType": "2", "businessName": "集团专线3", "businessDesc": "营销话术adfadfdafdafda内容", "filePath": "222.docs", "consider": "考虑3∫考虑2", "refuse": "拒绝3∫拒绝3", "visitList": [{"visitId": "124312", "visitName": "二次拜访", "visitIntention": 1, "refuseReason": "不喜欢∫喜欢", "visitDesc": "备注", "visitDate": "2019/03/26 12:32"}, {"visitId": "124312", "visitName": "一次拜访", "visitIntention": 1, "refuseReason": "不喜欢∫喜欢", "visitDesc": "备注", "visitDate": "2019/03/24 11:32"}]}, {"activityId": "44444", "activityName": "活动***********", "businessType": "2", "businessName": "云视讯4", "businessDesc": "互联网专线是依托中国移动的传输网络资源，通过传输电路连接CMNET的方式向集团客户提供独享带宽的互联网接入及访问服务", "filePath": "", "consider": "考虑1∫考虑4", "refuse": "拒绝1∫拒绝4", "visitList": [{"visitId": "1222212", "visitName": "四次拜访", "visitIntention": 2, "refuseReason": "不喜欢∫喜欢", "visitDesc": "备注", "visitDate": "2018/05/09 09:32"}, {"visitId": "12554312", "visitName": "三次拜访", "visitIntention": 3, "refuseReason": "不喜欢∫喜欢", "visitDesc": "备注", "visitDate": "2018/05/09 09:32"}, {"visitId": "124312", "visitName": "二次拜访", "visitIntention": 2, "refuseReason": "不喜欢∫喜欢", "visitDesc": "备注", "visitDate": "2018/05/09 09:32"}, {"visitId": "124312", "visitName": "一次拜访", "visitIntention": 1, "refuseReason": "不喜欢∫喜欢", "visitDesc": "备注", "visitDate": "2018/05/09 09:32"}]}, {"activityId": "55555", "activityName": "活动***********", "businessType": "2", "businessName": "云视讯5", "businessDesc": "互联网专线是依托中国移动的传输网络资源，通过传输电路连接CMNET的方式向集团客户提供独享带宽的互联网接入及访问服务", "filePath": "", "consider": "考虑1∫考虑5", "refuse": "拒绝1∫拒绝5", "visitList": [{"visitId": "1222212", "visitName": "四次拜访", "visitIntention": 2, "refuseReason": "不喜欢∫喜欢", "visitDesc": "备注", "visitDate": "2018/05/09 09:32"}, {"visitId": "12554312", "visitName": "三次拜访", "visitIntention": 3, "refuseReason": "不喜欢∫喜欢", "visitDesc": "备注", "visitDate": "2018/05/09 09:32"}, {"visitId": "124312", "visitName": "二次拜访", "visitIntention": 2, "refuseReason": "不喜欢∫喜欢", "visitDesc": "备注", "visitDate": "2018/05/09 09:32"}, {"visitId": "124312", "visitName": "一次拜访", "visitIntention": 1, "refuseReason": "不喜欢∫喜欢", "visitDesc": "备注", "visitDate": "2018/05/09 09:32"}]}, {"activityId": "66666", "activityName": "活动***********", "businessType": "2", "businessName": "云视讯6", "businessDesc": "互联网专线是依托中国移动的传输网络资源，通过传输电路连接CMNET的方式向集团客户提供独享带宽的互联网接入及访问服务", "filePath": "", "consider": "考虑1∫考虑6", "refuse": "拒绝1∫拒绝6", "visitList": [{"visitId": "1222212", "visitName": "四次拜访", "visitIntention": 2, "refuseReason": "不喜欢∫喜欢", "visitDesc": "备注", "visitDate": "2018/05/09 09:32"}, {"visitId": "12554312", "visitName": "三次拜访", "visitIntention": 3, "refuseReason": "不喜欢∫喜欢", "visitDesc": "备注", "visitDate": "2018/05/09 09:32"}, {"visitId": "124312", "visitName": "二次拜访", "visitIntention": 2, "refuseReason": "不喜欢∫喜欢", "visitDesc": "备注", "visitDate": "2018/05/09 09:32"}, {"visitId": "124312", "visitName": "一次拜访", "visitIntention": 1, "refuseReason": "不喜欢∫喜欢", "visitDesc": "备注", "visitDate": "2018/05/09 09:32"}]}], "personActive": [{"activityId": "20190304", "stepId": "1111", "activityName": "活动1", "businessType": "1", "businessName": "集团专线"}, {"activityId": "20190305", "stepId": "2222", "activityName": "活动1", "businessType": "1", "businessName": "云视讯"}, {"activityId": "20190306", "stepId": "33333", "activityName": "活动1", "businessType": "1", "businessName": "集团专线2"}, {"activityId": "20190307", "stepId": "44444", "activityName": "活动1", "businessType": "1", "businessName": "云视讯2"}, {"activityId": "20190308", "stepId": "5555", "activityName": "活动5", "businessType": "1", "businessName": "集团专线3"}]}}, "groupInfoQuery": {"channelId": "2", "userId": "5413", "planDate": "2018/06/09", "planTime": "1", "StreamSeq": "***********", "groupData": [{"groupId": "6864", "groupName": "新大陆", "groupType": "A", "groupActnum": "12", "lastVisitTime": "2018/06/09", "visitNum": "2", "isPlan": 0}, {"groupId": "6866", "groupName": "中国移动", "groupType": "C", "groupActnum": "12", "lastVisitTime": "2018/06/19", "visitNum": "2", "isPlan": 1}]}, "viewPlan": [{"groupId": "6866", "groupName": "中国移动", "groupType": "C", "groupActnum": "12", "lastVisitTime": "2018/06/19", "visitNum": "2", "isPlan": 1}, {"groupId": "6867", "groupName": "中国移动2", "groupType": "A", "groupActnum": "12", "lastVisitTime": "2018/06/19", "visitNum": "2", "isPlan": 1}], "planInfoSync": {"resultCode": "0", "resultDesc": ""}, "h5GroupList": {"retCode": "0", "retMsg": null, "data": [{"planDate": "20190305", "planTime": "1", "groupId": "20190305", "groupName": "新大陆集团1", "groupType": "", "groupActnum": "3", "lastVisitTime": "20190305", "visitNum": "3", "isPlan": "1", "planId": "", "isMulticentre": "1"}, {"planDate": "20190306", "planTime": "1", "groupId": "20190305", "groupName": "新大陆集团2", "groupType": "", "groupActnum": "3", "lastVisitTime": "20190306", "visitNum": "3", "isPlan": "1", "planId": "", "isMulticentre": "0"}]}, "dictList": {"retCode": "0", "retMsg": "成功", "data": [{"dictId": "0001", "dictName": "业务名1"}, {"dictId": "0002", "dictName": "业务名2"}, {"dictId": "0003", "dictName": "业务名3"}]}, "h5getFilterStation": {"retCode": "0", "retMsg": null, "data": [{"dictName": "客户经理工号", "value": "9988022838662806", "valueName": "集客随销"}, {"dictName": "手机号", "value": "9988021245683406", "valueName": "校园营销"}, {"dictName": "携销配置提示语", "value": "9988021979165266", "valueName": "营业员配置"}, {"dictName": "携销配置提示语", "value": "0", "valueName": "岗位名称"}, {"dictName": "携销配置提示语", "value": "0", "valueName": "岗位名称"}, {"dictName": "携销配置提示语", "value": "9988021754066058", "valueName": "社区直销"}]}, "mustDoList": {"retCode": "0", "retMsg": null, "data": {"workFlag": "1", "mustList": [{"mustId": "32", "mustName": "拜访核心层", "mustType": "3", "mustValue": "0", "mustKey": "13906158811", "isInput": "1", "inputValue": null}, {"mustId": "118", "mustName": "拜访核心层2", "mustType": "3", "mustValue": "0", "mustKey": "13906158811", "isInput": "1", "inputValue": null}, {"mustId": "119", "mustName": "拜访核心层3", "mustType": "3", "mustValue": "0", "mustKey": "13906158811", "isInput": "1", "inputValue": null}, {"mustId": "120", "mustName": "工作内容", "mustType": "1", "mustValue": "", "mustKey": "", "isInput": "0", "inputValue": null}, {"mustId": "36", "mustName": "是否首席陪同（输入框填写领导全名）", "mustType": "4", "mustValue": "1,2", "mustKey": "是,否", "isInput": "1", "inputValue": null}, {"mustId": "44", "mustName": "关联人诉求摸排", "mustType": "4", "mustValue": "1,2,3,4,5,6,7", "mustKey": "单位信号,业务使用,4G网络,家宽问题,资费问题,其他,无", "isInput": "1", "inputValue": null}, {"mustId": "154", "mustName": "关联人诉求是否报障", "mustType": "3", "mustValue": "1,2,3,4,5,6,7", "mustKey": "已解决,已报障（4001110510）,已报障（84010086）,已报障（61234）,已报障（感知收集),反馈管理员,无诉求", "isInput": "0", "inputValue": null}, {"mustId": "121", "mustName": "商机", "mustType": "4", "mustValue": "1,2,3,4,5,6,7,8,9,10,11,12,13,14,15", "mustKey": "专线,视频彩铃,云MAS,云视迅,和对讲,大数据,物联网,云主机,云桌面,物联网,IDC,ICT,基础业务,其他,无", "isInput": "1", "inputValue": null}, {"mustId": "146", "mustName": "商机预计转换时间（格式例：4月）", "mustType": "1", "mustValue": "", "mustKey": "", "isInput": "0", "inputValue": null}, {"mustId": "147", "mustName": "商机预计月消费金额（元）", "mustType": "1", "mustValue": "", "mustKey": "", "isInput": "0", "inputValue": null}]}}, "businessList": {"retCode": "0", "retMsg": null, "data": [{"privName": "品质服务", "isNewFeature": null, "hasCrmid": "0", "hasPwd": "0", "opId": "pgop", "opParentid": "pgop", "sort": 1, "jqDateInfo": [], "parentId": "2", "authenType": -1, "levelId": 2, "featureType": null, "busiType": null, "privId": "20000052", "picId": null, "isHot": null}, {"privName": "新入网专区", "isNewFeature": 0, "hasCrmid": "0", "hasPwd": "1", "opId": "fsop", "opParentid": "fsop", "sort": 1, "jqDateInfo": [], "parentId": "2", "authenType": null, "levelId": 2, "featureType": null, "busiType": null, "itemList": [{"privName": "预配号入网", "isNewFeature": 0, "hasCrmid": "1", "hasPwd": "1", "opId": "fsop", "opParentid": "fsop", "sort": 1, "jqDateInfo": [], "parentId": "20000024", "authenType": -1, "levelId": 3, "featureType": null, "busiType": null, "privId": "10019", "picId": "10019", "isHot": null}, {"privName": "应急预配号", "isNewFeature": 1, "hasCrmid": "1", "hasPwd": "1", "opId": "fsop", "opParentid": "fsop", "sort": 1, "jqDateInfo": [], "parentId": "20000024", "authenType": -1, "levelId": 3, "featureType": "fea_urgent_true_name", "busiType": null, "privId": "100206", "picId": "10019", "isHot": null}, {"privName": "选号入网", "isNewFeature": 0, "hasCrmid": "1", "hasPwd": "1", "opId": "fsop", "opParentid": "fsop", "sort": 5, "jqDateInfo": [], "parentId": "20000024", "authenType": -1, "levelId": 3, "featureType": null, "busiType": null, "privId": "100026", "picId": "10028", "isHot": null}, {"privName": "携号转网", "isNewFeature": 0, "hasCrmid": "0", "hasPwd": "0", "opId": "fsop", "opParentid": "fsop", "sort": 7, "jqDateInfo": [], "parentId": "20000024", "authenType": -1, "levelId": 3, "featureType": "fea_transfer", "busiType": null, "privId": "100042", "picId": "100042", "isHot": null}, {"privName": "电渠抢单", "isNewFeature": 0, "hasCrmid": "0", "hasPwd": "0", "opId": "fsop", "opParentid": "fsop", "sort": 8, "jqDateInfo": [], "parentId": "20000024", "authenType": -1, "levelId": 3, "featureType": "fea_grab_sheet_new", "busiType": null, "privId": "100139", "picId": "3001", "isHot": null}, {"privName": "转网订单", "isNewFeature": 0, "hasCrmid": "0", "hasPwd": "0", "opId": "fsop", "opParentid": "fsop", "sort": 9, "jqDateInfo": [], "parentId": "20000024", "authenType": -1, "levelId": 3, "featureType": "fea_transfer_list", "busiType": null, "privId": "100044", "picId": "100044", "isHot": null}], "privId": "20000024", "picId": null, "isHot": null}, {"privName": "辅助专区", "isNewFeature": 0, "hasCrmid": "0", "hasPwd": "1", "opId": "fsop", "opParentid": "fsop", "sort": 2, "jqDateInfo": [], "parentId": "2", "authenType": null, "levelId": 2, "featureType": null, "busiType": null, "itemList": [{"privName": "我的工作", "isNewFeature": 0, "hasCrmid": "0", "hasPwd": "1", "opId": "cmopApp", "opParentid": "pgop", "sort": 1, "jqDateInfo": [], "parentId": "20000031", "authenType": 0, "levelId": 3, "featureType": null, "busiType": null, "privId": "2835", "picId": "2835", "isHot": null}, {"privName": "创建工作", "isNewFeature": 0, "hasCrmid": "0", "hasPwd": "1", "opId": "cmopApp", "opParentid": "pgop", "sort": 2, "jqDateInfo": [], "parentId": "20000031", "authenType": 0, "levelId": 3, "featureType": null, "busiType": null, "privId": "2836", "picId": "2836", "isHot": null}, {"privName": "公告查询", "isNewFeature": 0, "hasCrmid": "0", "hasPwd": "1", "opId": "cmopApp", "opParentid": "pgop", "sort": 3, "jqDateInfo": [], "parentId": "20000031", "authenType": 0, "levelId": 3, "featureType": null, "busiType": null, "privId": "2837", "picId": "2837", "isHot": null}], "privId": "20000031", "picId": null, "isHot": null}, {"privName": "基础业务", "isNewFeature": 0, "hasCrmid": "0", "hasPwd": "1", "opId": "fsop", "opParentid": "fsop", "sort": 2, "jqDateInfo": [], "parentId": "2", "authenType": null, "levelId": 2, "featureType": null, "busiType": null, "itemList": [{"privName": "主体产品变更", "isNewFeature": 0, "hasCrmid": "0", "hasPwd": "0", "opId": "fsop", "pagePath": "components/business/mainproductchange/MainProdMarket.vue", "opParentid": "fsop", "sort": 2, "jqDateInfo": [], "parentId": "20000025", "authenType": 1, "levelId": 3, "featureType": "fea_zhuti_prod", "busiType": null, "privId": "100061", "picId": "20402", "isHot": null}, {"privName": "增值产品", "isNewFeature": 0, "hasCrmid": "0", "hasPwd": "0", "opId": "fsop", "pagePath": "components/business/BandTvMarket/ValueAddOrderMarket.vue", "opParentid": "fsop", "sort": 3, "jqDateInfo": [], "parentId": "20000025", "authenType": 0, "levelId": 3, "featureType": null, "busiType": null, "privId": "100020", "picId": "100020", "isHot": null}, {"privName": "活动受理", "isNewFeature": 0, "hasCrmid": "0", "hasPwd": "0", "opId": "fsop", "pagePath": "components/market/MarketEntry.vue", "opParentid": "fsop", "sort": 4, "jqDateInfo": [], "parentId": "20000025", "authenType": 1, "levelId": 3, "featureType": "fea_market", "busiType": null, "privId": "100014", "picId": "20701", "isHot": null}, {"privName": "和分期", "isNewFeature": 0, "hasCrmid": "1", "hasPwd": "0", "opId": "fsop", "opParentid": "fsop", "sort": 6, "jqDateInfo": [], "parentId": "20000025", "authenType": -1, "levelId": 3, "featureType": "fea_hfq", "busiType": null, "privId": "100034", "picId": "100034", "isHot": null}, {"privName": "补换卡", "isNewFeature": 0, "hasCrmid": "0", "hasPwd": "0", "opId": "fsop", "opParentid": "fsop", "sort": 7, "jqDateInfo": [], "parentId": "20000025", "authenType": 0, "levelId": 3, "featureType": "fea_chcard", "busiType": null, "privId": "100041", "picId": "100041", "isHot": null}, {"privName": "终端营销案", "isNewFeature": 0, "hasCrmid": "0", "hasPwd": "0", "opId": "fsop", "opParentid": "fsop", "sort": 7, "jqDateInfo": [], "parentId": "20000025", "authenType": 0, "levelId": 3, "featureType": "fea_terminal", "busiType": null, "privId": "100043", "picId": "100043", "isHot": null}, {"privName": "中断接续营销案", "isNewFeature": 0, "hasCrmid": "0", "hasPwd": "0", "opId": "fsop", "opParentid": "fsop", "sort": 8, "jqDateInfo": [], "parentId": "20000025", "authenType": 0, "levelId": 3, "featureType": "fea_interrupt", "busiType": null, "privId": "100074", "picId": "100074", "isHot": null}, {"privName": "信用购", "isNewFeature": 0, "hasCrmid": "0", "hasPwd": "0", "opId": "fsop", "opParentid": "fsop", "sort": 8, "jqDateInfo": [], "parentId": "20000025", "authenType": -1, "levelId": 3, "featureType": "fea_creditloan", "busiType": null, "privId": "100046", "picId": "100046", "isHot": null}, {"privName": "活动促销", "isNewFeature": 1, "pagePath": "components/business/promotioncenter/PromotionEntry.vue", "hasCrmid": "0", "hasPwd": "0", "opId": "fsop", "opParentid": "fsop", "sort": 10, "jqDateInfo": [{"authId": "JQG", "authType": "00", "crmAuthType": "AuthCheckG", "authNum": "3", "authDesc": "身份证"}, {"authId": "JQFaceIden", "authType": "14", "crmAuthType": "AuthChkFaceIden", "authNum": "1", "authDesc": "刷脸鉴权"}, {"authId": "JQQR", "authType": "15", "crmAuthType": "AuthCheckQR", "authNum": "1", "authDesc": "数字鉴权"}, {"authId": "JQR", "authType": "07", "crmAuthType": "AuthCheckR", "authNum": "2", "authDesc": "验证码"}], "parentId": "20000025", "authenType": 1, "levelId": 3, "featureType": "fea_promotion", "busiType": "promotion_center", "privId": "100114", "picId": "100114", "isHot": null}, {"privName": "终端裸售", "isNewFeature": 1, "hasCrmid": "0", "hasPwd": "0", "opId": "fsop", "opParentid": "fsop", "sort": 11, "jqDateInfo": [], "parentId": "20000025", "authenType": -1, "levelId": 3, "featureType": "fea_zhongduan", "busiType": null, "privId": "100116", "picId": "100046", "isHot": null}, {"privName": "销户", "isNewFeature": 0, "hasCrmid": "0", "hasPwd": "0", "opId": "fsop", "opParentid": "fsop", "sort": 99, "jqDateInfo": [], "parentId": "20000025", "authenType": -1, "levelId": 3, "featureType": "fea_xiaohu", "busiType": null, "privId": "100228", "picId": "100228", "isHot": null}, {"privName": "销户重开", "isNewFeature": 0, "hasCrmid": "0", "hasPwd": "0", "opId": "fsop", "opParentid": "fsop", "sort": 100, "jqDateInfo": [], "parentId": "20000025", "authenType": -1, "levelId": 3, "featureType": "fea_xiaohureopen", "busiType": null, "privId": "100229", "picId": "100229", "isHot": null}, {"privName": "安防一键办理", "isNewFeature": 0, "hasCrmid": "0", "hasPwd": "0", "opId": "fsop", "opParentid": "fsop", "sort": 101, "jqDateInfo": [], "parentId": "20000025", "authenType": 1, "levelId": 3, "featureType": "fea_security", "busiType": null, "privId": "100226", "picId": "100226", "isHot": null}, {"privName": "组网一键办理", "isNewFeature": 0, "hasCrmid": "0", "hasPwd": "0", "opId": "fsop", "opParentid": "fsop", "sort": 102, "jqDateInfo": [], "parentId": "20000025", "authenType": 1, "levelId": 3, "featureType": "fea_network", "busiType": null, "privId": "100227", "picId": "10050", "isHot": null}, {"privName": "以卡换卡", "isNewFeature": 0, "hasCrmid": "0", "hasPwd": "0", "opId": "fsop", "opParentid": "fsop", "sort": 110, "jqDateInfo": [], "parentId": "20000025", "authenType": 0, "levelId": 3, "featureType": "fea_card_for_card", "busiType": null, "privId": "100247", "picId": "10023", "isHot": null}, {"privName": "群组权益管理", "isNewFeature": 0, "hasCrmid": "0", "hasPwd": "1", "opId": "fsop", "opParentid": "fsop", "sort": 111, "jqDateInfo": [], "parentId": "20000025", "authenType": 1, "levelId": 3, "featureType": "receive_equity", "busiType": null, "privId": "100269", "picId": "20202", "isHot": null}, {"privName": "群组权益开通", "isNewFeature": 1, "hasCrmid": "0", "hasPwd": "0", "opId": "fsop", "opParentid": "fsop", "sort": 113, "jqDateInfo": [], "parentId": "20000025", "authenType": 1, "levelId": 3, "featureType": "open_equity", "busiType": null, "privId": "100278", "picId": "20202", "isHot": null}, {"privName": "过户", "isNewFeature": 1, "hasCrmid": "0", "hasPwd": "0", "opId": "fsop", "opParentid": "fsop", "sort": 115, "jqDateInfo": [], "parentId": "20000025", "authenType": 1, "levelId": 3, "featureType": "transfer_user", "busiType": null, "privId": "100299", "picId": "100257", "isHot": null}], "privId": "20000025", "picId": null, "isHot": null}, {"privName": "宽带专区", "isNewFeature": 0, "hasCrmid": "0", "hasPwd": "1", "opId": "fsop", "opParentid": "fsop", "sort": 3, "jqDateInfo": [], "parentId": "2", "authenType": null, "levelId": 2, "featureType": null, "busiType": null, "itemList": [{"privName": "小区宽带", "isNewFeature": 0, "hasCrmid": "0", "hasPwd": "1", "opId": "fsop", "opParentid": "fsop", "sort": 3, "jqDateInfo": [], "parentId": "20000026", "authenType": -1, "levelId": 3, "featureType": "fea_xq_kuandai", "busiType": null, "privId": "100059", "picId": "10050", "isHot": null}, {"privName": "宽带开通", "isNewFeature": 0, "hasCrmid": "1", "hasPwd": "0", "opId": "fsop", "opParentid": "fsop", "sort": 6, "jqDateInfo": [{"authId": "JQ4", "authType": "13", "crmAuthType": "AuthCheckAB", "authNum": "4", "authDesc": "融合鉴权"}, {"authId": "JQ31", "authType": "00", "crmAuthType": "2", "authNum": "3", "authDesc": "身份证"}], "parentId": "20000026", "authenType": 1, "levelId": 3, "featureType": "fea_mband", "busiType": "mband_prod_kaitong", "privId": "100012", "picId": "10003", "isHot": null}, {"privName": "互联网电视", "isNewFeature": 0, "hasCrmid": "0", "hasPwd": "0", "opId": "fsop", "opParentid": "fsop", "sort": 6, "jqDateInfo": [], "parentId": "20000026", "authenType": 1, "levelId": 3, "featureType": "fea_nettv", "busiType": null, "privId": "100013", "picId": "10004", "isHot": null}, {"privName": "业务退订", "isNewFeature": 0, "hasCrmid": "0", "hasPwd": "0", "opId": "fsop", "opParentid": "fsop", "sort": 7, "jqDateInfo": [], "parentId": "20000026", "authenType": -1, "levelId": 3, "featureType": "fea_prodcancel", "busiType": null, "privId": "100054", "picId": "100054", "isHot": null}, {"privName": "和家固话", "isNewFeature": 0, "hasCrmid": "1", "hasPwd": "0", "opId": "fsop", "opParentid": "fsop", "sort": 8, "jqDateInfo": [], "parentId": "20000026", "authenType": 0, "levelId": 3, "featureType": "fea_hejia", "busiType": null, "privId": "100072", "picId": "20401", "isHot": null}, {"privName": "质差客户", "isNewFeature": 0, "hasCrmid": "1", "hasPwd": "0", "opId": "fsop", "opParentid": "fsop", "sort": 8, "jqDateInfo": [], "parentId": "20000026", "authenType": 0, "levelId": 3, "featureType": "<PERSON><PERSON><PERSON><PERSON>", "busiType": "bad_customer", "privId": "100311", "picId": "10003", "isHot": null}, {"privName": "一号多宽", "isNewFeature": 1, "hasCrmid": "0", "hasPwd": "0", "opId": "fsop", "opParentid": "fsop", "sort": 9, "jqDateInfo": [], "parentId": "20000026", "authenType": 0, "levelId": 3, "featureType": "fea_more_band", "busiType": null, "privId": "100137", "picId": "100137", "isHot": null}, {"privName": "一键办理", "isNewFeature": 0, "hasCrmid": "1", "hasPwd": "1", "opId": "fsop", "opParentid": "fsop", "sort": 12, "jqDateInfo": [], "parentId": "20000026", "authenType": 1, "levelId": 3, "featureType": "one_click_processing", "busiType": "one_click_processing", "privId": "100313", "picId": "10003", "isHot": null}, {"privName": "软终端", "isNewFeature": 0, "hasCrmid": "0", "hasPwd": "0", "opId": "fsop", "opParentid": "fsop", "sort": 18, "jqDateInfo": [], "parentId": "20000026", "authenType": 1, "levelId": 3, "featureType": "softterminal_flag", "busiType": null, "privId": "100215", "picId": "100215", "isHot": null}, {"privName": "10GPON千兆家庭网关更换", "isNewFeature": null, "hasCrmid": "0", "hasPwd": "0", "opId": "fsop", "opParentid": "fsop", "sort": 114, "jqDateInfo": [], "parentId": "20000026", "authenType": 1, "levelId": 3, "featureType": null, "busiType": null, "privId": "100306", "picId": "100306", "isHot": null}], "privId": "20000026", "picId": null, "isHot": null}, {"privName": "家庭专区", "isNewFeature": 0, "hasCrmid": "0", "hasPwd": "1", "opId": "fsop", "opParentid": "fsop", "sort": 4, "jqDateInfo": [], "parentId": "2", "authenType": null, "levelId": 2, "featureType": null, "busiType": null, "itemList": [{"privName": "多终端共享", "isNewFeature": 0, "hasCrmid": "1", "hasPwd": "0", "opId": "gongxiang", "opParentid": "fsop", "sort": 3, "jqDateInfo": [], "parentId": "20000027", "authenType": 0, "levelId": 3, "featureType": "fea_wanneng", "busiType": null, "privId": "100047", "picId": "100047", "isHot": null}, {"privName": "亲情网开通", "isNewFeature": 0, "hasCrmid": "0", "hasPwd": "0", "opId": "fsop", "opParentid": "fsop", "sort": 4, "jqDateInfo": [], "parentId": "20000027", "authenType": 0, "levelId": 3, "featureType": null, "busiType": null, "privId": "100016", "picId": "100016", "isHot": null}, {"privName": "亲情网维护", "isNewFeature": 0, "hasCrmid": "0", "hasPwd": "0", "opId": "fsop", "opParentid": "fsop", "sort": 6, "jqDateInfo": [], "parentId": "20000027", "authenType": 0, "levelId": 3, "featureType": null, "busiType": null, "privId": "100017", "picId": "100017", "isHot": null}], "privId": "20000027", "picId": null, "isHot": null}, {"privName": "物联网专区", "isNewFeature": 0, "hasCrmid": "0", "hasPwd": "1", "opId": "fsop", "opParentid": "fsop", "sort": 10, "jqDateInfo": [], "parentId": "2", "authenType": null, "levelId": 2, "featureType": null, "busiType": null, "itemList": [{"privName": "物联网卡激活", "isNewFeature": 0, "hasCrmid": "1", "hasPwd": "1", "opId": "fsop", "opParentid": "fsop", "sort": 1, "jqDateInfo": [], "parentId": "20000039", "authenType": -1, "levelId": 3, "featureType": "fea_iot_jh", "busiType": null, "privId": "100010", "picId": "10019", "isHot": null}, {"privName": "商品订购", "isNewFeature": 0, "hasCrmid": "1", "hasPwd": "1", "opId": "fsop", "opParentid": "fsop", "sort": 2, "jqDateInfo": [], "parentId": "20000039", "authenType": -1, "levelId": 3, "featureType": "offer_order", "busiType": null, "privId": "100008", "picId": "10010", "isHot": null}, {"privName": "代付费", "isNewFeature": 0, "hasCrmid": "1", "hasPwd": "1", "opId": "fsop", "opParentid": "fsop", "sort": 3, "jqDateInfo": [], "parentId": "20000039", "authenType": -1, "levelId": 3, "featureType": "dai_pay", "busiType": null, "privId": "100009", "picId": "20801", "isHot": null}], "privId": "20000039", "picId": null, "isHot": null}]}, "h5CustomerInfo": {"retCode": "0", "retMsg": null, "data": {"name": "戴波", "cityId": "14", "applyDate": "20051009113152", "custIcNo": "320281199512285797", "userid": "1419200007058281", "realstatecode": "4", "bandAddr": null, "balance": null, "consume": null, "integration": null, "fluxDetail": null}}, "h5SecondaryCard": {"retCode": "0", "retMsg": null, "data": {"busiType": "secondary_card", "flag": "1", "mainOffer": {"offerid": "**********", "offerName": "4G自选（预付费）"}, "attrList": [{"propcode": "602017092845", "propvalue": "13852297342"}, {"propcode": "602017092846", "propvalue": "13852298303"}, {"propcode": "602017092847", "propvalue": "13852298309"}]}}, "h5CustomerMoreInfo": {"retCode": "0", "retMsg": null, "data": {"name": null, "cityId": null, "applyDate": null, "custIcNo": null, "userid": null, "realstatecode": null, "bandAddr": "南京地区江宁东山街道天印大道书香名苑22栋4单元607室", "balance": "66.28", "charge": null, "integration": null, "fluxDetail": {"totalVal": "20.00G", "usedVal": "6.54G", "remainingVal": "13.46G", "commonTotalVal": "20.00G", "commonUsedVal": "6.54G", "commonRemainingVal": "13.46G", "specialTotalVal": "0.00M", "specialUsedVal": "0.00M", "specialRemainingVal": "0.00M", "commonFlowList": [{"gprsProductName": "任我用自选套餐流量宽带电视包120元(全国版)", "usedVal": "6.54G", "remainingVal": "13.46G", "totalVal": "20.00G"}], "specialFlowList": []}}}, "h5familyVNet": {"retCode": "0", "retMsg": null, "data": {"memberInfoList": [{"familysubsid": "1419200011771553", "region": "14", "memsubsid": "1419200011771553", "servnumber": "15951868901", "isprima": "1", "isV": "1", "isKey": "1", "paytype": "", "memregion": "14", "shortnum": "760", "startdate": "2017-08-01 00:00:00", "enddate": "", "memtype": "1"}, {"familysubsid": "1419200011771553", "region": "14", "memsubsid": "1419200011771553", "servnumber": "15951868902", "isprima": "0", "isV": "1", "isKey": "0", "paytype": null, "memregion": "14", "shortnum": "760", "startdate": "2017-08-01 00:00:00", "enddate": "", "memtype": "1"}, {"familysubsid": "1419200011771553", "region": "14", "memsubsid": "1419200011771553", "servnumber": "15951868907", "isprima": "0", "memregion": "14", "shortnum": "760", "isV": "1", "isKey": "1", "paytype": "1", "startdate": "2017-08-01 00:00:00", "enddate": "", "memtype": "1"}], "prodInfoList": [{"prodId": "2000003574", "prodName": "省内家庭V网,3元/月", "isPackage": null, "pkgprodId": null, "startDate": "20170801000000", "endDate": "21001231000000"}], "hasfmyprod": "1", "ishousehold": "1"}}, "h5alreadyOpened": {"retCode": "0", "retMsg": "success", "data": {"isMband": "1", "isNetTv": "1", "prodBrandList": [{"prodid": "**********", "prodname": "手机代付费宽带", "startdate": "2015-09-02", "enddate": "2019-05-02", "ispakage": "0", "pakagename": "手机代付费宽带", "prodtype": "I", "pakageid": "2013000001"}, {"prodid": "2000001683", "prodname": "宽带标准资费", "startdate": "2018-08-01", "enddate": "2100-12-31", "ispakage": "0", "pakagename": "手机代付费宽带", "prodtype": "I", "pakageid": "2013000001"}], "prodNetList": [{"prodid": "**********", "prodname": "互联网电视（省内央广银河-CP）", "startdate": "2015-09-02", "enddate": "2019-04-03", "ispakage": "0", "pakagename": "互联网电视（省内央广银河-CP）", "prodtype": "I", "pakageid": "**********"}, {"prodid": "**********", "prodname": "20元互联网电视", "startdate": "2018-08-01", "enddate": "2100-12-31", "ispakage": "0", "pakagename": "互联网电视（省内央广银河-CP）", "prodtype": "I", "pakageid": "**********"}]}}, "h5AccountBookDetail": {"retCode": "0", "retMsg": null, "data": {"acctid": "****************", "invalidDate": "**************", "cashBook": "199.00", "prepaidCardBook": "0.00", "marketCashBook": null, "discountBook": "50.09"}}, "h5GroupInfo": {"retCode": "0", "retMsg": null, "data": [{"custId": "****************", "groupName": "江苏新大陆软件有限公司", "corpscope": "C", "regStatus": "在网", "groupId": "***********", "address": "<PERSON><PERSON><PERSON>", "linkPhone": "**********", "region": "14"}, {"custId": "****************", "groupName": "南京医科大学附属医院第二医院", "corpscope": "D", "regStatus": "在网", "groupId": "***********", "address": "<PERSON><PERSON><PERSON>", "linkPhone": "**********", "region": "14"}, {"custId": "****************", "groupName": "华为有限公司", "corpscope": "C", "regStatus": "潜在", "groupId": "***********", "address": "<PERSON><PERSON><PERSON>", "linkPhone": "**********", "region": "14"}, {"custId": "****************", "groupName": "焦点集团教育部软件集成在线教育研究院有限公司", "corpscope": "C", "regStatus": "在网", "groupId": "***********", "address": "<PERSON><PERSON><PERSON>", "linkPhone": "**********", "region": "14"}, {"custId": "****************", "groupName": "Names", "corpscope": "D", "regStatus": "在网", "groupId": "51133665988", "address": "<PERSON><PERSON><PERSON>", "linkPhone": "**********", "region": "14"}, {"custId": "****************", "groupName": "Names", "corpscope": "C", "regStatus": "潜在", "groupId": "51134839277", "address": "<PERSON><PERSON><PERSON>", "linkPhone": "**********", "region": "14"}, {"custId": "****************", "groupName": "Names", "corpscope": "C", "regStatus": "在网", "groupId": "***********", "address": "<PERSON><PERSON><PERSON>", "linkPhone": "**********", "region": "14"}, {"custId": "****************", "groupName": "Names", "corpscope": "D", "regStatus": "在网", "groupId": "51133665988", "address": "<PERSON><PERSON><PERSON>", "linkPhone": "**********", "region": "14"}, {"custId": "****************", "groupName": "Names", "corpscope": "C", "regStatus": "潜在", "groupId": "51134839277", "address": "<PERSON><PERSON><PERSON>", "linkPhone": "**********", "region": "14"}]}, "h5GroupPkg": {"retCode": "0", "retMsg": null, "data": [{"prodid": "2000005644", "prodName": "19元包本地主叫1500分钟(09版集团套餐)", "servnum": "18216007795", "subsid": "1899300032426899"}, {"prodid": "2000005873", "prodName": "1元包本地主叫300分钟（13版集团套餐）", "servnum": "18216008938", "subsid": "1899300030845899"}, {"prodid": "2000005688", "prodName": "10元包本地主叫1500分钟(09版集团套餐)", "servnum": "18851406284", "subsid": "1899300032426900"}, {"prodid": "2000005664", "prodName": "19元包本地主叫1500分钟(09版集团套餐)", "servnum": "18216007795", "subsid": "1899300032426899"}, {"prodid": "2000005853", "prodName": "1元包本地主叫300分钟（13版集团套餐）", "servnum": "18216008938", "subsid": "1899300030845899"}, {"prodid": "2000005648", "prodName": "10元包本地主叫1500分钟(09版集团套餐)", "servnum": "18851406284", "subsid": "1899300032426900"}, {"prodid": "2000005634", "prodName": "19元包本地主叫1500分钟(09版集团套餐)", "servnum": "18216007795", "subsid": "1899300032426899"}, {"prodid": "2000005823", "prodName": "1元包本地主叫300分钟（13版集团套餐）", "servnum": "18216008938", "subsid": "1899300030845899"}, {"prodid": "2000005618", "prodName": "10元包本地主叫1500分钟(09版集团套餐)", "servnum": "18851406284", "subsid": "1899300032426900"}]}, "h5GroupBelongInf": {"retCode": "1", "retMsg": null, "data": {"groupName": "鲁海测试集团", "productName": "集团V网分组产品(集团V网分组产品(10元包本地主叫1500分钟(09版集团套餐)))", "startDate": "20180101000000", "endDate": "", "shortNum": "10026", "groupSubsId": "1899300032426899", "flag": "1", "productId": "2000005873", "groupId": "***********"}}, "h5MemberShortNum": {"retCode": "0", "retMsg": null, "data": ["60000", "60001", "60002", "60004", "60005"]}, "h5ChkShortnumStatus": {"retCode": "0", "retMsg": "success", "data": null}, "h5AddVNetMember": {"retCode": "0", "retMsg": null, "data": null}, "h5BusinessQuit": {"retCode": "0", "retMsg": null, "data": {"grpcustname": "Names", "grpprodname": "集团V网分组产品"}}, "h5OpenedPackage": {"retCode": "0", "retMsg": null, "data": [{"prodId": "2000005643", "prodName": "本地V网分组包"}, {"prodId": "101", "prodName": "10元包本地主叫1500分钟(09版集团套餐)"}, {"prodId": "102", "prodName": "10元包本地主叫1500分钟(09版集团套餐)"}, {"prodId": "103", "prodName": "10元包本地主叫1500分钟(09版集团套餐)"}, {"prodId": "104", "prodName": "10元包本地主叫1500分钟(09版集团套餐)"}, {"prodId": "105", "prodName": "10元包本地主叫1500分钟(09版集团套餐)"}, {"prodId": "106", "prodName": "10元包本地主叫1500分钟(09版集团套餐)"}, {"prodId": "107", "prodName": "10元包本地主叫1500分钟(09版集团套餐)"}]}, "h5ChangePacAndShort": {"retCode": "0", "retMsg": "办理成功", "data": null}, "h5CheckCampusVNet": {"retCode": "0", "retMsg": "【校园V网校验 FSOP2BDS1233】success", "data": null}, "h5ExitCampusVNet": {"retCode": "0", "retMsg": "【校园V网退出 FSOP2BDS1234】success", "data": null}, "h5IdentifyUser": {"retCode": "0", "retMsg": null, "data": {"region": "14", "regionname": "南京地区", "userName": "徐霜霜", "userbrand": "全球通", "userstatus": "US10", "userprod": "**********", "registerdate": "201309258000000", "userprodid": "**********", "viptypeid": "普通用户", "retCode": null, "retMsg": null, "telnum": null, "authtype": null, "realstatecode": "4", "certid": null, "certaddress": null, "certtypename": null}}, "h5SendVerifyCode": {"retCode": "0", "retMsg": null, "data": null}, "h5getCustomViewMenuByLevel": {"retCode": "0", "retMsg": null, "data": [{"privName": "宽带专区", "hasCrmid": "0", "levelId": 2, "opId": "fsop", "opParentid": "fsop", "itemList": [{"privName": "未开放", "hasCrmid": "0", "levelId": 3, "opId": "fsop", "opParentid": "fsop", "sort": 1, "privId": "1976", "parentId": "101", "picId": "1976", "hasPwd": "", "isHot": null}, {"privName": "宽带开通新", "hasCrmid": "0", "levelId": 3, "opId": "fsop", "opParentid": "fsop", "sort": 1, "isNewFeature": 1, "hasPwd": "0", "privId": "100012", "parentId": "101", "authenType": "1", "picId": "1966", "isHot": null}, {"privName": "选号入网", "hasCrmid": "0", "levelId": 3, "opId": "fsop", "opParentid": "fsop", "sort": 1, "isNewFeature": 1, "hasPwd": "0", "privId": "10028", "parentId": "101", "authenType": "0", "picId": "选号入网", "isHot": null}, {"privName": "互联网电视新", "hasCrmid": "0", "levelId": 3, "opId": "fsop", "opParentid": "fsop", "sort": 2, "privId": "100013", "parentId": "101", "picId": "100013", "isHot": null}, {"privName": "活动受理新", "hasCrmid": "0", "levelId": 3, "opId": "fsop", "opParentid": "fsop", "sort": 2, "privId": "100014", "parentId": "101", "picId": "100014", "isHot": null}], "sort": 1, "privId": "101", "parentId": "1", "picId": "10019", "isHot": null}, {"privName": "家庭专区", "hasCrmid": "0", "levelId": 2, "opId": "fsop", "opParentid": "fsop", "itemList": [{"privName": "家庭网开通", "hasCrmid": "1", "levelId": 3, "opId": "fsop", "opParentid": "fsop", "sort": 1, "privId": "20201", "parentId": "103", "picId": "10024", "isHot": null}, {"privName": "核心成员", "hasCrmid": "1", "levelId": 3, "opId": "fsop", "opParentid": "fsop", "sort": 2, "privId": "20202", "parentId": "103", "picId": "10024", "isHot": null}], "sort": 3, "privId": "103", "parentId": "1", "picId": "10019", "isHot": null}, {"privName": "副卡专区", "hasCrmid": "0", "levelId": 2, "opId": "fsop", "opParentid": "fsop", "itemList": [{"privName": "万能副卡", "hasCrmid": "1", "levelId": 3, "opId": "fsop", "opParentid": "fsop", "sort": 1, "privId": "20301", "parentId": "103", "picId": "10028", "isHot": null}], "sort": 3, "privId": "103", "parentId": "1", "picId": "10019", "isHot": null}, {"privName": "其他专区", "hasCrmid": "0", "levelId": 2, "opId": "fsop", "opParentid": "fsop", "itemList": [{"privName": "IMS固话", "hasCrmid": "0", "levelId": 3, "opId": "cmopApp", "opParentid": "cmop", "sort": 1, "privId": "20401", "parentId": "102", "picId": "999", "isHot": null}, {"privName": "主体产品变更", "hasCrmid": "0", "levelId": 3, "opId": "cmopApp", "opParentid": "cmop", "sort": 1, "privId": "20402", "parentId": "102", "picId": "999", "isHot": null}, {"privName": "增值产品变更", "hasCrmid": "0", "levelId": 3, "opId": "cmopApp", "opParentid": "cmop", "sort": 1, "privId": "20403", "parentId": "102", "picId": "999", "isHot": null}], "sort": 2, "privId": "102", "parentId": "1", "picId": "10019", "isHot": null}]}, "h5GroupDetailInfo": {"retCode": "0", "retMsg": null, "data": {"groupInfo": {"groupName": "腾讯房产有限公司", "level": "集团客户级别", "type": "集团类型", "scale": "集团规模", "address": "江苏省南京市新城科技园", "callNumList": [{"callName": "张三", "callNum": "15265365896"}, {"callName": "李四", "callNum": "13652569654"}]}, "detailInfo": [{"firstMenuName": "集团基础信息", "firstMenuInfo": [{"secondMenuName": "集团标识", "secondMenuInfo": "民营企业"}, {"secondMenuName": "集团名称", "secondMenuInfo": "江苏新大陆科技"}, {"secondMenuName": "客户经理工号(crm工号)", "secondMenuInfo": "37266189"}]}, {"firstMenuName": "业务发展信息", "firstMenuInfo": [{"secondMenuName": "产品1", "secondMenuInfo": ""}, {"secondMenuName": "产品2", "secondMenuInfo": [{"thirdMenuName": "产品", "thirdMenuInfo": "产品1(2001)|产品2(2002)|"}, {"thirdMenuName": "业务类型", "thirdMenuInfo": "2323423"}]}, {"secondMenuName": "产品3", "secondMenuInfo": [{"thirdMenuName": "产品", "thirdMenuInfo": "产品1(2001)|产品2(2002)|"}, {"thirdMenuName": "业务类型", "thirdMenuInfo": "wwwwwwwwwwwwwww"}]}, {"secondMenuName": "产品4", "secondMenuInfo": [{"thirdMenuName": "产品", "thirdMenuInfo": "产品1(2001)|产品2(2002)|"}, {"thirdMenuName": "业务类型", "thirdMenuInfo": "yyyyyyyyyyyyyyyy"}]}, {"secondMenuName": "产品5", "secondMenuInfo": [{"thirdMenuName": "产品", "thirdMenuInfo": "产品1(2001)|产品2(2002)|"}, {"thirdMenuName": "业务类型", "thirdMenuInfo": "eeeeeeeeeeeeee"}]}, {"secondMenuName": "产品6", "secondMenuInfo": [{"thirdMenuName": "产品", "thirdMenuInfo": "产品1(2001)|产品2(2002)|"}, {"thirdMenuName": "业务类型", "thirdMenuInfo": "xxxxxxxxxxxxxxxx"}]}, {"secondMenuName": "产品7", "secondMenuInfo": [{"thirdMenuName": "产品", "thirdMenuInfo": "产品1(2001)|产品2(2002)|"}, {"thirdMenuName": "业务类型", "thirdMenuInfo": "ccccccccccccccccc"}]}, {"secondMenuName": "产品8", "secondMenuInfo": [{"thirdMenuName": "产品", "thirdMenuInfo": "产品1(2001)|产品2(2002)|"}, {"thirdMenuName": "业务类型", "thirdMenuInfo": "hhhhhhhhhhhhhhh"}]}, {"secondMenuName": "产品9", "secondMenuInfo": [{"thirdMenuName": "产品", "thirdMenuInfo": "产品1(2001)|产品2(2002)|"}, {"thirdMenuName": "业务类型", "thirdMenuInfo": "nnnnnnnnnnnnnnnnnn"}]}, {"secondMenuName": "产品10", "secondMenuInfo": [{"thirdMenuName": "产品", "thirdMenuInfo": "产品1(2001)|产品2(2002)|"}, {"thirdMenuName": "业务类型", "thirdMenuInfo": "zzzzzzzzzzz"}]}]}, {"firstMenuName": "集团推荐信息", "firstMenuInfo": []}, {"firstMenuName": "集团预警信息", "firstMenuInfo": [{"secondMenuName": "成员", "secondMenuInfo": [{"thirdMenuName": "集团竞争对手物理成员数", "thirdMenuInfo": "1"}, {"thirdMenuName": "集团移动用户物理成员数", "thirdMenuInfo": "2"}]}, {"secondMenuName": "话务", "secondMenuInfo": [{"thirdMenuName": "集团总MOU", "thirdMenuInfo": "1"}, {"thirdMenuName": "集团人均MOU", "thirdMenuInfo": "2"}]}, {"secondMenuName": "话务1", "secondMenuInfo": [{"thirdMenuName": "集团总MOU", "thirdMenuInfo": "1"}, {"thirdMenuName": "集团人均MOU", "thirdMenuInfo": "2"}]}, {"secondMenuName": "话务2", "secondMenuInfo": [{"thirdMenuName": "集团总MOU", "thirdMenuInfo": "1"}, {"thirdMenuName": "集团人均MOU", "thirdMenuInfo": "2"}]}, {"secondMenuName": "话务3", "secondMenuInfo": [{"thirdMenuName": "集团总MOU", "thirdMenuInfo": "1"}, {"thirdMenuName": "集团人均MOU", "thirdMenuInfo": "2"}]}, {"secondMenuName": "话务4", "secondMenuInfo": [{"thirdMenuName": "集团总MOU", "thirdMenuInfo": "1"}, {"thirdMenuName": "集团人均MOU", "thirdMenuInfo": "2"}]}, {"secondMenuName": "其他", "secondMenuInfo": [{"thirdMenuName": "集团成员主体产品结束时间(历史)", "thirdMenuInfo": "20110101"}]}]}, {"firstMenuName": "集团关键人、联系人信息", "firstMenuInfo": [{"secondMenuName": "关键人1姓名", "secondMenuInfo": "aaa"}, {"secondMenuName": "关键人1手机号码", "secondMenuInfo": "***********"}]}]}}, "appVersionInfo": {"retCode": "0", "retMsg": "", "data": {"android": {"url": "http://183.207.195.94:8080/xsbh5/app/ald_2.20.13.apk", "desc": "1.上线新版本预配号入网<br>2.优化工作台滑动和日历展示", "versionCode": "1", "versionName": "2.2.2", "updateTime": "2019-04-29 11:34:23", "appSize": "23M", "updateRule": "1"}, "ios": {"url": "itms-services://?action=download-manifest&amp;url=https://ccg-1257785273.cos.ap-shanghai.myqcloud.com/ald_2.20.13.plist", "desc": "1.上线新版本预配号入网<br>2.优化工作台滑动和日历展示<br>3.优化工作台滑动和日历展示", "versionCode": "1", "versionName": "2.2.3", "updateTime": "2019-04-29 11:34:23", "appSize": "23M", "updateRule": "1"}, "image": "../../../static/img/cover1.png<br>../../../static/img/cover2.png"}}, "h5GetTelNumSegment": {"retCode": "0", "retMsg": "success", "data": ["159", "158", "152", "151", "150", "139", "138", "137", "136", "135", "134"]}, "h5QryOperatorInfo": {"retCode": "0", "retMsg": null, "data": {"opratorName": "冯瑜", "orgId": "14174470", "orgName": "宜兴经营部", "region": "101019003", "cityName": "南京", "countryId": "1423", "countryName": "江宁"}}, "h5QryTelnumList": {"retCode": "0", "retMsg": null, "data": {"telnumBoList": [{"telnum": "13886666321", "telprice": "0"}, {"telnum": "13886666322", "telprice": "200"}, {"telnum": "13886666323", "telprice": "100"}, {"telnum": "13886666324", "telprice": "200"}, {"telnum": "13886666325", "telprice": "100"}, {"telnum": "13886666326", "telprice": "200"}]}}, "h5QryHidenum": {"retCode": "0", "retMsg": null, "data": {"isExist": "1", "telprice": "100", "telnumStatus": "1"}}, "h5QryTaocan": {"retCode": "0", "retMsg": null, "data": {"taocanBoList": [{"pkgId": "套餐包id", "pkgName": "套餐包名称1", "pkgDesc": "1.套餐包描述信息|2.套餐2", "pkgFee": "10"}, {"pkgId": "套餐包id", "pkgName": "套餐包名称2", "pkgDesc": "套餐包描述信息", "pkgFee": "102"}, {"pkgId": "套餐包id", "pkgName": "套餐包名称3", "pkgDesc": "套餐包描述信息", "pkgFee": "106"}]}}, "h5CheckGiftCard": {"retCode": "0", "retMsg": null, "data": "15152815369"}, "h5QryBusiPermission": {"retCode": "0", "retMsg": "没有权限", "data": null}, "h5CheckPreferential": {"retCode": "0", "retMsg": null, "data": {"resCode": "02", "jmMoney": "22"}}, "h5CreateOrder": {"retCode": "0", "retMsg": "创建订单成功", "data": {"orderId": "11111111111111", "payUrl": "http://www.baidu.com"}}, "h5CreateOrders": {"retCode": "0", "retMsg": "创建订单成功", "data": "11111111111111"}, "h5GetOrderPayUrl": {"retCode": "0", "retMsg": "获取支付链接成功", "data": "http://p.12580.com/wps/service/WapFormTrans.xhtml?sessionid=20180929201809291033243416394899"}, "h5NoticeOrderCenter": {"retCode": "0", "retMsg": "订单通知成功", "data": null}, "idCardUpLoad": {"retCode": "0", "retMsg": null, "data": {"headId": "****************", "cardPicId": "****************"}}, "faceCheck": {"retCode": "0", "retMsg": "success", "data": {"netSrl": "**************", "pseq": ""}}, "h5PaySubmit": {"retCode": "0", "retMsg": null, "data": {"orderId": "1111", "payUrl": "http://p.12580.com/"}}, "h5GetOrderInfo": {"retCode": "0", "retMsg": null, "data": {"certType": "idCard", "busiType": "enter_net"}}, "h5OpenAccountSubmit": {"retCode": "0", "retMsg": null, "data": "11222"}, "h5SignUpLoad": {"retCode": "0", "retMsg": "", "data": {"signId": "2131313"}}, "h5QryCgetusercust": {"retCode": "0", "retMsg": "", "data": {"user_id": "****************", "userCity": "14", "userName": "杨春青", "userApplyDate": "**************", "custIcNo": "320722196509217359", "realstatecode": "4"}}, "h5QryChannelInfo": {"retCode": "0", "retMsg": null, "data": {"channelId": "14", "staffId": "************", "streamSeq": "1420180607141834234", "list": [{"groupId": "**********", "groupName": "虎踞路总店"}, {"groupId": "**********", "groupName": "翠屏小区门店"}]}}, "h5getActivity": {"retCode": "0", "retMsg": null, "data": [{"activeName": "存量未开和留言用户到厅推荐1", "activeDesc": "存量短信呼目标用户，推荐和留言（3元）短信呼（1元）或营销案（返话费）", "type": "2", "offerId": null, "offerName": null, "productType": "3", "productId": "**********", "productIdExp5": "短信呼(1元)", "productIdExp6": "", "productIdExp1": "", "canDo": "-1"}, {"activeName": "存量未开和留言用户到厅推荐2", "activeDesc": "存量短信呼目标用户，推荐和留言（3元）短信呼（1元）或营销案（返话费）存量短信呼目标用户，推荐和留言（3元）短信呼（1元）或营销案（返话费）", "type": "2", "offerId": null, "offerName": null, "productType": "2", "productId": "2200005001", "productIdExp5": "短信呼(1元)", "productIdExp6": "", "productIdExp1": "", "canDo": "0"}]}, "h5qryWeekPlan": {"retCode": "0", "retMsg": null, "data": {"channelId": "2", "streamSeq": "*******************", "userId": "16806792", "weekPlanData": [{"planDate": "20190125", "planNum": "3"}, {"planDate": "20190305", "planNum": "3"}]}}, "h5qryCollegeInfo": {"retCode": "0", "retMsg": null, "data": [{"collegeId": "1001", "collegeName": "南京大学", "collegeActNum": "11", "isPlan": 1}, {"collegeId": "1002", "collegeName": "东南大学", "collegeActNum": "11", "isPlan": 1}, {"collegeId": "1001", "collegeName": "南京大学", "collegeActNum": "11", "isPlan": 1}]}, "h5qryDecidedPlan": {"retCode": "0", "retMsg": null, "data": {"channelId": "2", "userId": "16806792", "planDate": "20190305", "streamSeq": "43C1000120180606141834234", "collegeData": [{"planId": "1111111", "collegeId": "1001", "collegeName": "南京大学", "collegeActNum": "3", "startTime": "10"}, {"planId": "1111112", "collegeId": "1002", "collegeName": "东南大学", "collegeActNum": "3", "startTime": "10"}]}}, "h5collegePlanSync": {"retCode": "0", "retMsg": "成功", "data": "*******************"}, "h5submitMakeupBusiness": {"retCode": "0", "retMsg": null, "data": null}, "h5qryBusinessType": {"retCode": "0", "retMsg": null, "data": [{"value": "true_name_record", "valueName": "资料补录"}, {"value": "mband_prod_kaitong", "valueName": "宽带产品开通"}, {"value": "enter_net", "valueName": "选号入网"}, {"value": "nettv_snygyh_prod_kaitong", "valueName": "央广银河电视开通"}, {"value": "nettv_prod_kaitong", "valueName": "百事通电视开通"}, {"value": "TERMINA_SELL", "valueName": "营销案"}, {"value": "nettv_snys_prod_kaitong", "valueName": "省内央视电视开通"}, {"value": "order_enter_net", "valueName": "电渠抢单"}]}, "h5qryMakeupBusiness": {"retCode": "0", "retMsg": null, "data": [{"orderId": "200150554936216763", "telnum": "***********", "source": "FSOP", "busiType": "order_enter_net", "valueName": "电渠抢单", "state": "已完成", "createTime": "2019/03/18 10:49", "cardId": "******************", "cardsign": "card"}]}, "h5GetTimeOutBusi": {"retCode": "0", "retMsg": null, "data": {"operId": "14150759", "region": "14", "msisdn": "***********", "state": null, "createTime": "2018-05-19 12:14:12", "isExist": "1", "busiType": "true_name_record", "crmOrderId": "182019010485406020", "operTime": "2019-01-04 11:03:42", "custIcNo": "320281199512285797"}}, "h5UpdateTimeOutBusi": {"retCode": "0", "retMsg": "", "data": null}, "h5TimeOutMakeUpSubmit": {"retCode": "0", "retMsg": "", "data": "11223344"}, "h5qryCollegeActivity": {"retCode": "0", "retMsg": null, "data": {"workId": "111111", "collegeId": "2", "userPhone": "***********", "userId": "1222233444444", "hasFamilyAddr": "0", "addressId": "111", "familyAddr": "嘉陵江府", "streamSeq": "*******************", "activityData": [{"stepId": "11111", "activityId": "20190305", "activityName": "活动1", "businessType": "1", "businessName": "专线", "activityDesc": "专线", "messageInfo": "专线", "consider": "考虑原因1∫考虑原因2", "refuse": "拒绝原因1∫拒绝原因2"}, {"stepId": "11111", "activityId": "20190305", "activityName": "活动2", "businessType": "1", "businessName": "专线", "activityDesc": "专线", "messageInfo": "专线", "consider": "考虑原因1∫考虑原因2", "refuse": "拒绝原因1∫拒绝原因2"}], "businessInfo": {"mainBusiness": "128元4G套餐", "bill1": "128", "bill2": "128", "bill3": "", "flow1": "100M", "flow2": "100M", "flow3": ""}}}, "h5QryCollegeList": {"retCode": "0", "retMsg": null, "data": [{"collegeId": "**********", "collegeName": "南京大学"}, {"collegeId": "**********", "collegeName": "东南大学"}]}, "h5qryCollegeInfoDetail": {"retCode": "0", "retMsg": null, "data": {"workId": "111111", "streamSeq": "*******************", "collegeId": "1001", "collegeName": "南京大学", "collageLat": "15.253", "collageLon": "25.233", "isSign": 0, "isPhoto": 0, "collegeInfo": [{"infoName": "集团地址", "infoValue": "嘉临江东街。。。。"}, {"infoName": "联系电话", "infoValue": "025-86099090"}], "activityData": [{"activityType": 1, "activityId": "20190305", "stepId": "11111", "boothId": "11111", "activityName": "活动1", "businessType": "1", "businessName": "专线", "operationNum": 200}, {"activityType": 1, "activityId": "20190305", "stepId": "11111", "boothId": "11111", "activityName": "活动2", "businessType": "1", "businessName": "专线", "operationNum": 200}]}}, "h5qryActivityUser": {"retCode": "0", "retMsg": null, "data": {"workId": "111111", "collegeId": "1001", "activityId": "100023232", "messageInfo": "欢迎…….", "streamSeq": "*******************", "data": [{"userId": "1112233344444", "msisdn": "***********", "age": 20, "sex": 1}, {"userId": "1112233344444", "msisdn": "***********", "age": 20, "sex": 1}]}}, "h5qryMustList": {"retCode": "0", "retMsg": null, "data": {"streamSeq": "*******************", "workFlag": "1", "isPhoto": "1", "mustList": [{"mustId": "222", "mustName": "异网", "mustType": "3", "mustValue": "1,2", "mustKey": "是,否"}]}}, "h5qryOperator": {"retCode": "0", "retMsg": null, "data": [{"operatorPhone": "1001", "operatorName": "戴波"}, {"operatorPhone": "1002", "operatorName": "周文"}]}, "h5QryFloorAddress": {"retCode": "0", "retMsg": null, "data": {"channelId": "2", "userId": "111122222", "parentId": "123", "workId": "22222", "addressDataBoList": [{"levelId": "4", "addressId": "1001", "addressName": "101室", "collectStatus": null}, {"levelId": "4", "addressId": "1002", "addressName": "102室", "collectStatus": null}]}}, "h5GetWriteData": {"retCode": "0", "retMsg": "success", "data": "12312312|121321312|121321312|121321312"}, "h5RtnWriteCardStatus": {"retCode": "0", "retMsg": "success", "data": null}, "h5TaskAuthorization": {"retCode": "0", "retMsg": null, "data": [{"operatorId": "1000001", "operatorName": "戴波"}, {"operatorId": "10002", "operatorName": "周文"}, {"operatorId": "1003", "operatorName": "张三"}, {"operatorId": "1004", "operatorName": "李四"}, {"operatorId": "1005", "operatorName": "王五"}, {"operatorId": "1006", "operatorName": "找六"}, {"operatorId": "1007", "operatorName": "李四"}, {"operatorId": "1008", "operatorName": "李四板仓街192号13发生的李四板仓街192号13发生的"}, {"operatorId": "1009", "operatorName": "找六"}, {"operatorId": "1010", "operatorName": "李四板仓街192号13发生的"}]}, "h5GetUserList": {"retCode": "0", "retMsg": null, "data": {"workId": "111111", "objectId": "1001", "activityId": "100023232", "stepId": "11", "messageInfo": "欢迎…….", "data": [{"userId": "1112233344444", "msisdn": "***********", "age": "20", "sex": "0"}, {"userId": "1112233344445", "msisdn": "***********", "age": "20", "sex": "1"}, {"userId": "1112233344446", "msisdn": "***********", "age": "20", "sex": "0"}, {"userId": "1112233344447", "msisdn": "***********", "age": "20", "sex": "1"}, {"userId": "1112233344448", "msisdn": "***********", "age": "20", "sex": "0"}, {"userId": "1112233344449", "msisdn": "***********", "age": "20", "sex": "1"}, {"userId": "1112233344450", "msisdn": "***********", "age": "20", "sex": "0"}, {"userId": "1112233344451", "msisdn": "***********", "age": "20", "sex": "1"}, {"userId": "1112233344452", "msisdn": "***********", "age": "20", "sex": "0"}, {"userId": "1112233344453", "msisdn": "***********", "age": "20", "sex": "1"}]}}, "h5GetUserDetail": {"retCode": "0", "retMsg": null, "data": {"hasFamilyAddr": 0, "addressId": "231", "familyAddr": "翠萍小区2栋1034室", "businessInfo": {"mainBusiness": "128元4G套餐", "bill1": "128", "bill2": "128", "bill3": "128", "flow1": "100M", "flow2": "100M", "flow3": "100M", "groupName": "新大陆集团"}, "activeResp": {"activeName": "128元4G套餐", "markTime": "2019-03-05 12:00:00", "result": "2", "reason": "需要考虑", "remark": ""}, "activityData": [{"stepId": "111", "activityId": "20190305", "activityName": "活动1", "businessType": "1", "businessName": "专线", "activityDesc": "这是活动介绍1", "messageInfo": "这是短信内容1", "consider": "考虑原因1∫考虑原因2", "refuse": "拒绝原因1∫拒绝原因2"}, {"stepId": "222", "activityId": "20190306", "activityName": "活动2", "businessType": "1", "businessName": "专线", "activityDesc": "这是活动介绍2", "messageInfo": "这是短信内容2", "consider": "考虑原因1∫考虑原因2", "refuse": "拒绝原因1∫拒绝原因2"}]}}, "h5getCustBaseInfo": {"retCode": "0", "retMsg": null, "data": {"userid": "14688111", "userName": "李四", "custIcNo": "320321199803032513", "userApplyDate": "20190916", "userCity": "南京", "certid": "320321199803032513", "custIcType": "32", "realstatecode": "4"}}, "h5qryOldCardInfo": {"retCode": "0", "retMsg": null, "data": {"simcardnum": "1233333333333333339", "cardtype": "kaleixing", "cardgroup": "1845", "islucknum": "0"}}, "h5checkAndcalfee": {"retCode": "0", "retMsg": null, "data": {"fee": "2000"}}, "h5supplementSim": {"retCode": "0", "retMsg": null, "data": {"orderId": "11111111", "netSrl": "222222"}}, "h5savewritecard": {"retCode": "0", "retMsg": null, "data": {}}, "h5qryPackByType": {"retCode": "0", "retMsg": null, "data": {"packList": [{"marketId": "1", "issueType": "1", "operId": null, "marketName": "和分期1", "packType": "3", "region": "14", "activeId": "1001", "activeName": "和分期批次1", "levelId": "100001", "levelName": "和分期档次", "superType": "<PERSON><PERSON><PERSON>", "marketDesc": "分期打包", "rewardArr": {"101": [{"rewardId": "1", "rewardName": "鸡翅买一送一", "rewardPakId": "101", "rewardPakName": "菜场优惠", "region": "14", "needSn": "0"}, {"rewardId": "2", "rewardName": "鸡蛋买一送二", "rewardPakId": "101", "rewardPakName": "菜场优惠", "region": "14", "needSn": "1"}], "102": [{"rewardId": "3", "rewardName": "鸡腿不要钱", "rewardPakId": "102", "rewardPakName": "菜场优惠", "region": "14", "needSn": "0"}, {"rewardId": "4", "rewardName": "猪肉不要钱", "rewardPakId": "102", "rewardPakName": "菜场优惠", "region": "14", "needSn": "0"}]}}, {"marketId": "3", "issueType": "1", "operId": null, "marketName": "和分期3", "packType": "3", "region": "14", "activeId": "1003", "activeName": "和分期批次3", "levelId": "100001", "levelName": "和分期档次", "superType": "<PERSON><PERSON><PERSON>", "marketDesc": "和分期打包", "rewardArr": {"101": [{"rewardId": "1", "rewardName": "鸡翅买一送一", "rewardPakId": "101", "rewardPakName": "菜场优惠", "region": "14", "needSn": "0"}, {"rewardId": "2", "rewardName": "鸡蛋买一送二", "rewardPakId": "101", "rewardPakName": "菜场优惠", "region": "14", "needSn": "1"}, {"rewardId": "1", "rewardName": "鸡翅买一送一", "rewardPakId": "101", "rewardPakName": "菜场优惠", "region": "14", "needSn": "0"}, {"rewardId": "2", "rewardName": "鸡蛋买一送二", "rewardPakId": "101", "rewardPakName": "菜场优惠", "region": "14", "needSn": "1"}, {"rewardId": "1", "rewardName": "鸡翅买一送一", "rewardPakId": "101", "rewardPakName": "菜场优惠", "region": "14", "needSn": "0"}, {"rewardId": "2", "rewardName": "鸡蛋买一送二", "rewardPakId": "101", "rewardPakName": "菜场优惠", "region": "14", "needSn": "1"}], "102": [{"rewardId": "3", "rewardName": "鸡腿不要钱", "rewardPakId": "102", "rewardPakName": "菜场优惠", "region": "14", "needSn": "0"}, {"rewardId": "4", "rewardName": "猪肉不要钱", "rewardPakId": "102", "rewardPakName": "菜场优惠", "region": "14", "needSn": "0"}]}}, {"marketId": "4", "issueType": "3", "operId": null, "marketName": "和分期4", "packType": "2", "region": "99", "activeId": "1004", "activeName": "和分期批次4", "levelId": "100001", "levelName": "和分期档次", "superType": "<PERSON><PERSON><PERSON>", "marketDesc": "和分期打包", "rewardArr": null}, {"marketId": "4", "issueType": "1", "operId": null, "marketName": "小牛电动车终端", "packType": "3", "region": "14", "activeId": "1001", "activeName": "终端批次1", "levelId": "100001", "levelName": "终端档次", "superType": "terminal", "marketDesc": "终端打包", "rewardArr": {"101": [{"rewardId": "1", "rewardName": "鸡翅买一送一", "rewardPakId": "101", "rewardPakName": "菜场优惠", "region": "14", "needSn": "0"}, {"rewardId": "2", "rewardName": "鸡蛋买一送二", "rewardPakId": "101", "rewardPakName": "菜场优惠", "region": "14", "needSn": "1"}], "102": [{"rewardId": "3", "rewardName": "鸡腿不要钱", "rewardPakId": "102", "rewardPakName": "菜场优惠", "region": "14", "needSn": "0"}, {"rewardId": "4", "rewardName": "猪肉不要钱", "rewardPakId": "102", "rewardPakName": "菜场优惠", "region": "14", "needSn": "0"}]}}, {"marketId": "5", "issueType": "1", "operId": null, "marketName": "oppo手机终端", "packType": "3", "region": "14", "activeId": "1003", "activeName": "终端批次3", "levelId": "100001", "levelName": "终端档次", "superType": "terminal", "marketDesc": "终端打包", "rewardArr": {"101": [{"rewardId": "1", "rewardName": "鸡翅买一送一", "rewardPakId": "101", "rewardPakName": "菜场优惠", "region": "14", "needSn": "0"}, {"rewardId": "2", "rewardName": "鸡蛋买一送二", "rewardPakId": "101", "rewardPakName": "菜场优惠", "region": "14", "needSn": "1"}, {"rewardId": "1", "rewardName": "鸡翅买一送一", "rewardPakId": "101", "rewardPakName": "菜场优惠", "region": "14", "needSn": "0"}, {"rewardId": "2", "rewardName": "鸡蛋买一送二", "rewardPakId": "101", "rewardPakName": "菜场优惠", "region": "14", "needSn": "1"}, {"rewardId": "1", "rewardName": "鸡翅买一送一", "rewardPakId": "101", "rewardPakName": "菜场优惠", "region": "14", "needSn": "0"}, {"rewardId": "2", "rewardName": "鸡蛋买一送二", "rewardPakId": "101", "rewardPakName": "菜场优惠", "region": "14", "needSn": "1"}], "102": [{"rewardId": "3", "rewardName": "鸡腿不要钱", "rewardPakId": "102", "rewardPakName": "菜场优惠", "region": "14", "needSn": "0"}, {"rewardId": "4", "rewardName": "中国移动十元流量劵", "rewardPakId": "102", "rewardPakName": "菜场优惠", "region": "14", "needSn": "0"}]}}, {"marketId": "6", "issueType": "3", "operId": null, "marketName": "和分期4", "packType": "2", "region": "99", "activeId": "1004", "activeName": "终端批次4", "levelId": "100001", "levelName": "终端档次", "superType": "terminal", "marketDesc": "终端打包", "rewardArr": null}]}}, "h5telnumgradeinfo": {"retCode": "0", "retMsg": null, "data": {}}, "h5checkApply": {"retCode": "0", "retMsg": "(2019101110113825)Crm接口提示:SUCCESS", "data": {"taskId": "123456789", "outSrl": "2019101110113825"}}, "h5qryOperatorInfo2": {"retCode": "0", "retMsg": "【查询操作员归属信息 FSOP2BDS1127】success", "data": {"opratorName": "冯瑜", "orgId": "14174470", "orgName": "宜兴经营部", "region": "101019003", "cityName": "南京", "countryId": "1423", "countryName": "江宁"}}, "h5qryTaocan1": {"retCode": "0", "retMsg": "", "data": {"taocanBoList": [{"pkgId": "10000000000000000043", "pkgName": "小燕子的测试包9", "pkgDesc": "测试", "pkgFee": "100"}, {"pkgId": "10000000000000000043", "pkgName": "小燕子的测试包9", "pkgDesc": "测试", "pkgFee": "0"}, {"pkgId": "10000000000000000043", "pkgName": "小燕子的测试包9", "pkgDesc": "测试", "pkgFee": "0"}]}}, "h5CheckGiftCard1": {"retCode": "0", "retMsg": null, "data": {}}, "h5transferSubmit": {"retCode": "0", "retMsg": null, "data": {}}, "h5qryCheckList": {"retCode": "0", "retMsg": null, "data": {"applyList": [{"srlId": "2019101110000016", "operId": "14150759", "region": "14", "telnum": "13585058303", "certType": "CIVIC_ID", "certId": "320181199999832", "authCode": "123321", "authCodeExpire": "2019-10-10 12:20", "taskId": "123456789", "currentStepId": null, "status": "4", "source": "1", "createDate": "2019-10-11 14:29:38", "operateDate": "2019-10-22 14:29:38", "sendState": "0", "failNum": null, "keyParameterJson": null, "userName": "梅"}, {"srlId": "2019101110000016", "operId": "14150759", "region": "14", "telnum": "13585058303", "certType": "CIVIC_ID", "certId": "320181199999833", "authCode": "123321", "authCodeExpire": "2019-10-10 12:20", "taskId": "123456789", "currentStepId": null, "status": "2", "source": "1", "createDate": "2019-10-11 14:29:38", "operateDate": "2019-10-11 14:29:38", "sendState": "0", "failNum": null, "keyParameterJson": null, "userName": "梅"}, {"srlId": "2019101110000016", "operId": "14150759", "region": "14", "telnum": "13585058303", "certType": "CIVIC_ID", "certId": "320181199999834", "authCode": "123321", "authCodeExpire": "2019-10-10 12:20", "taskId": "123456789", "currentStepId": null, "status": "3", "source": "2", "createDate": "2019-10-11 14:29:38", "operateDate": "2019-10-11 14:29:38", "sendState": "0", "failNum": null, "keyParameterJson": null, "userName": "梅"}, {"srlId": "2019101110000016", "operId": "14150759", "region": "14", "telnum": "13585058303", "certType": "CIVIC_ID", "certId": "320181199999835", "authCode": "123321", "authCodeExpire": "2019-10-10 12:20", "taskId": "123456789", "currentStepId": null, "status": "4", "source": "2", "createDate": "2019-10-11 14:29:38", "operateDate": "2019-10-11 14:29:38", "sendState": "0", "failNum": null, "keyParameterJson": null, "userName": "梅"}, {"srlId": "2019101110000016", "operId": "14150759", "region": "14", "telnum": "13585058303", "certType": "CIVIC_ID", "certId": "320181199999836", "authCode": "123321", "authCodeExpire": "2019-10-10 12:20", "taskId": "123456789", "currentStepId": null, "status": "5", "source": "2", "createDate": "2019-10-11 14:29:38", "operateDate": "2019-10-11 14:29:38", "sendState": "0", "failNum": null, "keyParameterJson": null, "userName": "梅"}, {"srlId": "2019101110000016", "operId": "14150759", "region": "14", "telnum": "13585058303", "certType": "CIVIC_ID", "certId": "320181199999837", "authCode": "123321", "authCodeExpire": "2019-10-10 12:20", "taskId": "123456789", "currentStepId": null, "status": "6", "source": "2", "createDate": "2019-10-11 14:29:38", "operateDate": "2019-10-11 14:29:38", "sendState": "0", "failNum": null, "keyParameterJson": null, "userName": "梅"}, {"srlId": "2019101110000016", "operId": "14150759", "region": "14", "telnum": "13585058303", "certType": "CIVIC_ID", "certId": "320181199999838", "authCode": "123321", "authCodeExpire": "2019-10-10 12:20", "taskId": "123456789", "currentStepId": null, "status": "7", "source": "2", "createDate": "2019-10-11 14:29:38", "operateDate": "2019-10-11 14:29:38", "sendState": "0", "failNum": null, "keyParameterJson": null, "userName": "梅"}, {"srlId": "2019101110000016", "operId": "14150759", "region": "14", "telnum": "13585058303", "certType": "CIVIC_ID", "certId": "320181199999839", "authCode": "123321", "authCodeExpire": "2019-10-10 12:20", "taskId": "123456789", "currentStepId": null, "status": "8", "source": "2", "createDate": "2019-10-11 14:29:38", "operateDate": "2019-10-11 14:29:38", "sendState": "0", "failNum": null, "keyParameterJson": null, "userName": "梅"}, {"srlId": "2019101110000016", "operId": "14150759", "region": "14", "telnum": "13585058303", "certType": "CIVIC_ID", "certId": "320181199999810", "authCode": "123321", "authCodeExpire": "2019-10-10 12:20", "taskId": "123456789", "currentStepId": null, "status": "9", "source": "1", "createDate": "2019-10-11 14:29:38", "operateDate": "2019-10-11 14:29:38", "sendState": "0", "failNum": null, "keyParameterJson": null, "userName": "梅"}]}}, "h5qryNationList": {"retCode": "0", "retMsg": "", "data": {"nationList": [{"id": "僜人", "label": "僜人"}, {"id": "蔡家人", "label": "蔡家人"}, {"id": "菜族人", "label": "菜族人"}, {"id": "老品人", "label": "老品人"}, {"id": "八甲人", "label": "八甲人"}, {"id": "夏尔巴人", "label": "夏尔巴人"}, {"id": "土克曼人", "label": "土克曼人"}, {"id": "摩梭人", "label": "摩梭人"}, {"id": "克木人", "label": "克木人"}, {"id": "穿青人", "label": "穿青人"}]}}, "h5GetBusiTypeList": {"retCode": "0", "retMsg": null, "data": [{"typeid": "SupplementInfo", "typename": "入网预约", "ordernum": null}, {"typeid": "packageApply", "typename": "套餐预约", "ordernum": null}, {"typeid": "mbandApply", "typename": "宽带预约", "ordernum": null}, {"typeid": "numberApply", "typename": "数字业务预约", "ordernum": null}, {"typeid": "tvApply", "typename": "互联网电视", "ordernum": null}, {"typeid": "MARKET", "typename": "4G套餐办理", "ordernum": null}, {"typeid": "<PERSON><PERSON><PERSON>", "typename": "和目", "ordernum": null}, {"typeid": "INCREMENT", "typename": "流量叠加包办理", "ordernum": null}]}, "h5searchOrgid": {"retCode": "0", "retMsg": null, "data": {"opratorName": "冯瑜", "orgId": "14174470", "orgName": "宜兴经营部", "region": "14", "cityName": null, "countryId": null, "countryName": null}}, "h5grabsheetNum": {"retCode": "0", "retMsg": "【查询抢单数量FSOP2BDS1222】success", "data": {"myTotalNumber": "15", "myUntreatedTotalNumber": "9", "myUntreatedEchannelNumber": "4", "myUntreatedOutcallNumber": "3", "myUntreatedIntelthreeNumber": "2", "myUntreatedIntelphoneNumber": "1", "untreatedEchannelNumber": "5", "untreatedOutcallNumber": "2", "untreatedIntelthreeNumber": "1", "untreatedIntelphoneNumber": "1", "untreatedTotalNumber": "9", "myUntreatedXhzwNumber": "9", "untreatedXhzwNumber": "9"}}, "h5grabsheetMessage": {"retCode": "0", "retMsg": "【抢单信息查询 FSOP2BDS1223】success", "data": [{"orderId": "190817709383700001619", "offerId": "1000100462", "offerName": "1000100462(2000009684)", "orderStatus": "72", "grabStatus": "2", "deliveryType": "WXWS", "createTime": "20190319093837", "remark": "1", "cityId": "18", "accNbrr": "", "serviceCode": "42000", "factAmount": "0", "shipAddr": "1", "deliveryPeriod": "", "grabStaff": "", "dealMessage": "未联系客户", "grabTime": "201903190938", "cancelTime": "", "appointmentTime": "201903190938", "contactPhone": "15903181134", "contactName": "1", "orderFrom": "120", "grabType": "05"}, {"orderId": "19081770938370000111100", "offerId": "1000100462", "offerName": "1000100462(2000009684)", "orderStatus": "72", "grabStatus": "3", "deliveryType": "WXWS", "createTime": "20190319093837", "remark": "1", "cityId": "18", "accNbrr": "", "serviceCode": "42000", "factAmount": "0", "shipAddr": "1", "deliveryPeriod": "", "grabStaff": "", "dealMessage": "<PERSON><PERSON><PERSON>", "grabTime": "201903190938", "cancelTime": "", "appointmentTime": "201903190938", "contactPhone": "15903181134", "contactName": "1", "orderFrom": "120", "grabType": "06"}, {"orderId": "19081770938370000111100", "offerId": "1000100462", "offerName": "1000100462(2000009684)", "orderStatus": "72", "grabStatus": "3", "deliveryType": "WXWS", "createTime": "20190319093837", "remark": "1", "cityId": "18", "accNbrr": "", "serviceCode": "42000", "factAmount": "0", "shipAddr": "1", "deliveryPeriod": "", "grabStaff": "", "dealMessage": "<PERSON><PERSON><PERSON>", "grabTime": "201903190938", "cancelTime": "", "appointmentTime": "201903190938", "contactPhone": "15903181134", "contactName": "1", "orderFrom": "120", "grabType": "05"}, {"orderId": "19081770938370000111100", "offerId": "1000100462", "offerName": "1000100462(2000009684)", "orderStatus": "72", "grabStatus": "2", "deliveryType": "WXWS", "createTime": "20190319093837", "remark": "1", "cityId": "18", "accNbrr": "", "serviceCode": "10000", "factAmount": "0", "shipAddr": "1", "deliveryPeriod": "", "grabStaff": "", "dealMessage": "<PERSON><PERSON><PERSON>", "grabTime": "201903190938", "cancelTime": "", "appointmentTime": "201903190938", "contactPhone": "15903181134", "contactName": "1", "orderFrom": "120", "grabType": "06"}, {"orderId": "1908177093837000011111", "offerId": "1000100462", "offerName": "1000100462(2000009684)", "orderStatus": "88", "grabStatus": "1", "deliveryType": "WXWS", "createTime": "20190319093837", "remark": "1", "cityId": "18", "accNbrr": "", "serviceCode": "10000", "factAmount": "0", "shipAddr": "1", "deliveryPeriod": "", "grabStaff": "", "dealMessage": "hahahh", "grabTime": "201903190938", "cancelTime": "", "appointmentTime": "201903190938", "contactPhone": "15903181134", "contactName": "1", "orderFrom": "120", "grabType": "02"}, {"orderId": "19081770938370000163", "offerId": "1000100462", "offerName": "1000100462(2000009684)", "orderStatus": "88", "grabStatus": "1", "deliveryType": "WXWS", "createTime": "20190319093837", "remark": "1", "cityId": "18", "accNbrr": "", "serviceCode": "10000", "factAmount": "0", "shipAddr": "1", "deliveryPeriod": "", "grabStaff": "", "dealMessage": "未联系客户", "grabTime": "201903190938", "cancelTime": "", "appointmentTime": "201903190938", "contactPhone": "15903181134", "contactName": "1", "orderFrom": "120", "grabType": "01"}, {"orderId": "191027720553800000641", "offerId": "1000100324", "offerName": "万能副卡", "orderStatus": "40", "grabStatus": "2", "deliveryType": "WXTS", "createTime": "20190409205539", "remark": "万能副卡", "cityId": "14", "accNbrr": "", "serviceCode": "10000", "factAmount": "0", "shipAddr": "1111119号", "deliveryPeriod": "", "grabStaff": "", "dealMessage": "客户拒绝", "grabTime": "", "cancelTime": "20190411205539", "appointmentTime": "", "contactPhone": "***********", "contactName": "王凡", "orderFrom": "103", "grabType": "05"}, {"orderId": "191027720553800000642", "offerId": "1000100324", "offerName": "万能副卡", "orderStatus": "72", "grabStatus": "3", "deliveryType": "WXTS", "createTime": "20190409205539", "remark": "万能副卡", "cityId": "14", "accNbrr": "", "serviceCode": "42000", "factAmount": "0", "shipAddr": "1111119号", "deliveryPeriod": "", "grabStaff": "", "dealMessage": "客户拒绝", "grabTime": "", "cancelTime": "20190411205539", "appointmentTime": "", "contactPhone": "***********", "contactName": "王凡", "orderFrom": "103", "grabType": "05"}, {"orderId": "191027720553800000641", "offerId": "1000100324", "offerName": "万能副卡", "orderStatus": "40", "grabStatus": "2", "deliveryType": "WXTS", "createTime": "20190409205539", "remark": "万能副卡", "cityId": "14", "accNbrr": "", "serviceCode": "10000", "factAmount": "0", "shipAddr": "1111119号", "deliveryPeriod": "", "grabStaff": "", "dealMessage": "客户拒绝", "grabTime": "", "cancelTime": "20190411205539", "appointmentTime": "", "contactPhone": "***********", "contactName": "王凡", "orderFrom": "103", "grabType": "05"}, {"orderId": "191027720553800000642", "offerId": "1000100324", "offerName": "万能副卡", "orderStatus": "72", "grabStatus": "3", "deliveryType": "WXTS", "createTime": "20190409205539", "remark": "万能副卡", "cityId": "14", "accNbrr": "", "serviceCode": "10000", "factAmount": "0", "shipAddr": "1111119号", "deliveryPeriod": "", "grabStaff": "", "dealMessage": "客户拒绝", "grabTime": "", "cancelTime": "20190411205539", "appointmentTime": "", "contactPhone": "***********", "contactName": "王凡", "orderFrom": "103", "grabType": "05"}, {"orderId": "191027720553800000642", "offerId": "1000100324", "offerName": "万能副卡", "orderStatus": "40", "grabStatus": "2", "deliveryType": "WXTS", "createTime": "20190409205539", "remark": "万能副卡", "cityId": "14", "accNbrr": "", "serviceCode": "42000", "factAmount": "0", "shipAddr": "1111119号", "deliveryPeriod": "", "grabStaff": "", "dealMessage": "客户拒绝", "grabTime": "", "cancelTime": "20190411205539", "appointmentTime": "", "contactPhone": "***********", "contactName": "王凡", "orderFrom": "103", "grabType": "05"}, {"orderId": "191027720553800000643", "offerId": "1000100324", "offerName": "万能副卡", "orderStatus": "40", "grabStatus": "2", "deliveryType": "WXTS", "createTime": "20190409205539", "remark": "万能副卡", "cityId": "14", "accNbrr": "", "serviceCode": "42000", "factAmount": "0", "shipAddr": "江苏省南京江宁区禄口街道高伏高家村19号", "deliveryPeriod": "", "grabStaff": "", "dealMessage": "客户拒绝", "grabTime": "", "cancelTime": "20190411205539", "appointmentTime": "", "contactPhone": "***********", "contactName": "王凡", "orderFrom": "103", "grabType": "04"}, {"orderId": "19103770908090000122", "offerId": "1000100324", "offerName": "万能副卡", "orderStatus": "40", "grabStatus": "0", "deliveryType": "WXTS", "createTime": "20190410090810", "remark": "万能副卡", "cityId": "14", "accNbrr": "", "serviceCode": "42000", "factAmount": "0", "shipAddr": "江苏省南京江宁区禄口街道高伏高家村19号", "deliveryPeriod": "", "grabStaff": "", "dealMessage": "改约时间", "grabTime": "", "cancelTime": "20190412090810", "appointmentTime": "", "contactPhone": "***********", "contactName": "王凡", "orderFrom": "103", "grabType": "05"}, {"orderId": "19103770909430000124", "offerId": "1000100324", "offerName": "万能副卡", "orderStatus": "40", "grabStatus": "2", "deliveryType": "WXTS", "createTime": "20190410090943", "remark": "万能副卡", "cityId": "14", "accNbrr": "", "serviceCode": "42000", "factAmount": "0", "shipAddr": "江苏省南京江宁区禄口街道高伏高家村19号", "deliveryPeriod": "", "grabStaff": "", "dealMessage": "", "grabTime": "", "cancelTime": "20190412090943", "appointmentTime": "", "contactPhone": "***********", "contactName": "王凡", "orderFrom": "103", "grabType": "04"}, {"orderId": "19070771620520000776", "offerId": "1000100462", "offerName": "1000100462(2000009684)", "orderStatus": "99", "grabStatus": "2", "deliveryType": "WXTS", "createTime": "20190308162052", "remark": "南京_18元超级王卡超级王卡超级王卡超级王卡超级王卡超级王卡超级王卡", "cityId": "18", "accNbrr": "", "serviceCode": "10000", "factAmount": "0", "shipAddr": "请选择", "deliveryPeriod": "", "grabStaff": "18092166", "dealMessage": "其他,已联系客户不在家", "grabTime": "20190410111908", "cancelTime": "20190410131908", "appointmentTime": "20190508162052", "contactPhone": "15150504727", "contactName": "王唯", "orderFrom": "103", "grabType": "04"}, {"orderId": "19049771419460000024", "offerId": "", "offerName": "test", "orderStatus": "88", "grabStatus": "3", "deliveryType": "WXWS", "createTime": "20190218141946", "remark": "订单备注信息", "cityId": "18", "accNbrr": "", "serviceCode": "10000", "factAmount": "0", "shipAddr": "我勒个去", "deliveryPeriod": "客户收货时间段描述", "grabStaff": "18092166", "dealMessage": "已联系客户", "grabTime": "20190218142018", "cancelTime": "20190320142018", "appointmentTime": "20190124172000", "contactPhone": "14751736538", "contactName": "石等伟", "orderFrom": "120", "grabType": "04"}, {"orderId": "19070771600200000742", "offerId": "1000100462", "offerName": "1000100462(2000009684)", "orderStatus": "40", "grabStatus": "2", "deliveryType": "WXTS", "createTime": "20190308160020", "remark": "南京_18元超级王卡超级王卡超级王卡超级王卡超级王卡超级王卡超级王卡", "cityId": "18", "accNbrr": "", "serviceCode": "10000", "factAmount": "0", "shipAddr": "请选择", "deliveryPeriod": "", "grabStaff": "18092166", "dealMessage": "", "grabTime": "20190308160051", "cancelTime": "20190308180051", "appointmentTime": "", "contactPhone": "15150504727", "contactName": "王唯", "orderFrom": "103", "grabType": "04"}]}, "h5dispatch": {"retCode": "0", "retMsg": "【查询可派单人员信息 FSOP2BDS1224】success", "data": [{"userName": "05qd1", "realName": "抢单01", "accNbr": "***********", "cityId": "19", "positionCode": "YG", "agencyCode": "********"}, {"userName": "18092166", "realName": "超级管理员", "accNbr": "***********", "cityId": "99", "positionCode": "GLY", "agencyCode": "********"}]}, "h5transfer": {"retCode": "0", "retMsg": "【查询代理商机构 FSOP2BDS1225】success", "data": [{"cityId": "14", "agencyName": "南京", "agencyCode": "********"}, {"cityId": "14", "agencyName": "南京鼓楼浙江行商团队", "agencyCode": "********"}, {"cityId": "14", "agencyName": "南京", "agencyCode": "********"}, {"cityId": "14", "agencyName": "南京鼓楼浙江行商团队", "agencyCode": "********"}]}, "h5accountExe": {"retCode": "0", "retMsg": "【抢单业务执行 FSOP2BDS1226】success", "data": null}, "h5qryOrderDeatil": {"retCode": "0", "retMsg": "success", "data": ""}, "h5QryDuringOrder": {"retCode": "0", "retMsg": "success", "data": {"orderlist": [{"orderid": "200107801743614130", "offerlist": [{"offerid": "**********", "offername": "互联网电视（省内央广银河-CP）"}]}, {"orderid": "200107801743614131", "offerlist": [{"offerid": "**********", "offername": "手机宽带"}]}]}}, "h5DuringOrderBack": {"retCode": "0", "retMsg": "success", "data": ""}, "h5QryBusinessType": {"retCode": "0", "retMsg": "success", "data": [{"offerId": "**********", "offerName": "手机代付费宽带"}, {"offerId": "**********，**********", "offerName": "互联网电视"}]}, "h5CreateOrderCard": {"retCode": "0", "retMsg": "success", "data": ""}, "h5GetConsumeBillSixMonths": {"retCode": "0", "retMsg": "success", "data": [{"billMonth": "201906", "totalAmount": "11.82", "fstDdiList": [{"defId": "AA", "defName": "套餐及固定费用", "amount": "8.4"}, {"defId": "AB", "defName": "语音通信费用", "amount": "3.42"}], "thirDdiList": [{"defId": "AA", "defName": "任我用套餐费", "amount": "7.88"}, {"defId": "AA", "defName": "4G自选套餐语音包", "amount": "0.52"}, {"defId": "AB", "defName": "基本通话费", "amount": "3.42"}], "pkgUserIdList": [{"pkgName": "亲情号码组合(30元最低消费)", "pkgSubName": "通话时长（主叫）", "value": "1分钟"}, {"pkgName": "任我用自选套餐流量宽带电视包120元(全国版)", "pkgSubName": "GPRS流量(kb)", "value": "198.81M"}]}, {"billMonth": "201907", "totalAmount": "11.82", "fstDdiList": [{"defId": "AA", "defName": "套餐及固定费用", "amount": "8.4"}, {"defId": "AB", "defName": "语音通信费用", "amount": "3.42"}], "thirDdiList": [{"defId": "AA", "defName": "任我用套餐费", "amount": "7.88"}, {"defId": "AA", "defName": "4G自选套餐语音包", "amount": "0.52"}, {"defId": "AB", "defName": "基本通话费", "amount": "3.42"}], "pkgUserIdList": [{"pkgName": "亲情号码组合(30元最低消费)", "pkgSubName": "通话时长（主叫）", "value": "1分钟"}, {"pkgName": "任我用自选套餐流量宽带电视包120元(全国版)", "pkgSubName": "GPRS流量(kb)", "value": "198.81M"}]}, {"billMonth": "201908", "totalAmount": "11.82", "fstDdiList": [{"defId": "AA", "defName": "套餐及固定费用", "amount": "8.4"}, {"defId": "AB", "defName": "语音通信费用", "amount": "3.42"}], "thirDdiList": [{"defId": "AA", "defName": "任我用套餐费", "amount": "7.88"}, {"defId": "AA", "defName": "4G自选套餐语音包", "amount": "0.52"}, {"defId": "AB", "defName": "基本通话费", "amount": "3.42"}], "pkgUserIdList": [{"pkgName": "亲情号码组合(30元最低消费)", "pkgSubName": "通话时长（主叫）", "value": "1分钟"}, {"pkgName": "任我用自选套餐流量宽带电视包120元(全国版)", "pkgSubName": "GPRS流量(kb)", "value": "198.81M"}]}, {"billMonth": "201909", "totalAmount": "11.82", "fstDdiList": [{"defId": "AA", "defName": "套餐及固定费用", "amount": "8.4"}, {"defId": "AB", "defName": "语音通信费用", "amount": "3.42"}], "thirDdiList": [{"defId": "AA", "defName": "任我用套餐费", "amount": "7.88"}, {"defId": "AA", "defName": "4G自选套餐语音包", "amount": "0.52"}, {"defId": "AB", "defName": "基本通话费", "amount": "3.42"}], "pkgUserIdList": [{"pkgName": "亲情号码组合(30元最低消费)", "pkgSubName": "通话时长（主叫）", "value": "1分钟"}, {"pkgName": "任我用自选套餐流量宽带电视包120元(全国版)", "pkgSubName": "GPRS流量(kb)", "value": "198.81M"}]}, {"billMonth": "201910", "totalAmount": "11.82", "fstDdiList": [{"defId": "AA", "defName": "套餐及固定费用", "amount": "8.4"}, {"defId": "AB", "defName": "语音通信费用", "amount": "3.42"}], "thirDdiList": [{"defId": "AA", "defName": "任我用套餐费", "amount": "7.88"}, {"defId": "AA", "defName": "4G自选套餐语音包", "amount": "0.52"}, {"defId": "AB", "defName": "基本通话费", "amount": "3.42"}], "pkgUserIdList": [{"pkgName": "亲情号码组合(30元最低消费)", "pkgSubName": "通话时长（主叫）", "value": "1分钟"}, {"pkgName": "任我用自选套餐流量宽带电视包120元(全国版)", "pkgSubName": "GPRS流量(kb)", "value": "198.81M"}]}, {"billMonth": "201911", "totalAmount": "11.82", "fstDdiList": [{"defId": "AA", "defName": "套餐及固定费用", "amount": "8.4"}, {"defId": "AB", "defName": "语音通信费用", "amount": "3.42"}], "thirDdiList": [{"defId": "AA", "defName": "任我用套餐费", "amount": "7.88"}, {"defId": "AA", "defName": "4G自选套餐语音包", "amount": "0.52"}, {"defId": "AB", "defName": "基本通话费", "amount": "3.42"}], "pkgUserIdList": [{"pkgName": "亲情号码组合(30元最低消费)", "pkgSubName": "通话时长（主叫）", "value": "1分钟"}, {"pkgName": "任我用自选套餐流量宽带电视包120元(全国版)", "pkgSubName": "GPRS流量(kb)", "value": "198.81M"}]}]}, "h5GetMemberVoiceFlux": {"retCode": "0", "retMsg": "success", "data": [{"telnum": "11111111111", "flux": {"totalVal": "20.00G", "usedVal": "6.54G", "remainingVal": "13.46G", "commonTotalVal": "20.00G", "commonUsedVal": "6.54G", "commonRemainingVal": "13.46G", "specialTotalVal": "0.00M", "specialUsedVal": "0.00M", "specialRemainingVal": "0.00M", "commonFlowList": [{"gprsProductName": "任我用自选套餐流量宽带电视包120元(全国版)", "usedVal": "6.54G", "remainingVal": "13.46G", "totalVal": "20.00G"}], "specialFlowList": []}}, {"telnum": "13952338044", "flux": {"totalVal": "20.00G", "usedVal": "6.54G", "remainingVal": "13.46G", "commonTotalVal": "20.00G", "commonUsedVal": "6.54G", "commonRemainingVal": "13.46G", "specialTotalVal": "0.00M", "specialUsedVal": "0.00M", "specialRemainingVal": "0.00M", "commonFlowList": [{"gprsProductName": "任我用自选套餐流量宽带电视包120元(全国版)", "usedVal": "6.54G", "remainingVal": "13.46G", "totalVal": "20.00G"}], "specialFlowList": []}}]}, "h5cancelNpin": {"retCode": "0", "retMsg": "success", "data": null}, "h5CheckInstallPkg": {"retCode": "0", "retMsg": "success", "data": null}, "h5PcSignUpLoad": {"data": "2019120210007932", "retCode": "0", "retMsg": "null"}, "h5QryPcSignResult": {"data": "true", "retCode": "0", "retMsg": "null"}, "InfoCollectionData": {"retCode": "0", "retMsg": null, "data": [{"mustId": "111", "mustName": "异网1", "mustType": "1", "isInput": "0", "mustValue": "", "mustKey": "", "mustUserValue": "qqq", "mustUserInput": "", "mustFlag": "1"}, {"mustId": "222", "mustName": "异网2", "mustType": "2", "isInput": "0", "mustValue": "1001,1052,1003", "mustKey": "AB,CB,CD", "mustUserValue": "1001,1003", "mustUserInput": "", "mustFlag": "1"}, {"mustId": "2221", "mustName": "异网21", "mustType": "2", "isInput": "0", "mustValue": "1,2,3", "mustKey": "A2,B1,C2", "mustUserValue": "", "mustUserInput": "", "mustFlag": "1"}, {"mustId": "2222", "mustName": "异网22", "mustType": "2", "isInput": "0", "mustValue": "1,2,3", "mustKey": "A11,B11,C11", "mustUserValue": "2", "mustUserInput": "", "mustFlag": "1"}, {"mustId": "333", "mustName": "异网3", "mustType": "3", "isInput": "0", "mustValue": "101,102,103,104", "mustKey": "是,否,333,444", "mustUserValue": "103", "mustUserInput": "", "mustFlag": "1"}, {"mustId": "444", "mustName": "异网4", "mustType": "4", "isInput": "1", "mustValue": "101,102", "mustKey": "是,否", "mustUserValue": "101", "mustUserInput": "", "mustFlag": "0"}, {"mustId": "5", "mustName": "异网5", "mustType": "5", "isInput": "0", "mustValue": "", "mustKey": "", "mustUserValue": "2019-12-27", "mustUserInput": "", "mustFlag": "1"}]}, "h5QryTransferNetStatus": {"retCode": "0", "retMsg": "success", "data": {"stepStatus": "11111", "npDesc": "22222", "status": "4"}}, "h5appHisVersionInfo": {"retCode": "0", "retMsg": null, "data": [{"appId": "APP0000000001", "updateTime": "2019-05-29 19:02:40", "versionCode": "6", "versionName": "2.0.6", "versionDesc": "1.上线新配预配号入网<br>2.优化工作台滑动和日历展示<br>3.新增日志页可查看业务和待久历史<br>4.新增日志页可查看业务和待久历史<br><span style=\"color:#F86E21;\">（需升级到2.0.30以上版本）</span><br>5.新增日志页可查看业务和待久历史<br>6.新增日志页可查看业务和待久历史<br>7.新增日志页可查看业务和待久历史<br>8.新增日志页可查看业务和待久历史查看业务和待久历史查看业务和待久历史", "currentIs": null, "sysSupport": "支持Android2.1及以上智能手机操作系统", "screenSupport": "480×800", "appSize": "1.9M", "language": "简体中文", "downloadFileName": "http://183.207.195.94:8080/xsbh5/app/ald_2.0.4.apk", "updateRule": "1", "type": "1", "image": "http://183.207.195.94:8080/xsbh5/app/pic1.png<br>http://183.207.195.94:8080/xsbh5/app/pic2.png", "versionNameReal": "2.0.7", "welcomeImageInfo": []}, {"appId": "APP0000000001", "updateTime": "2019-04-29 19:02:40", "versionCode": "6", "versionName": "2.0.8", "versionDesc": "1.上线新配预配号入网<br>2.优化工作台滑动和日历展示<br>3.新增日志页可查看业务和待久历史", "currentIs": null, "sysSupport": "支持Android2.1及以上智能手机操作系统", "screenSupport": "480×800", "appSize": "1.9M", "language": "简体中文", "downloadFileName": "http://183.207.195.94:8080/xsbh5/app/ald_2.0.4.apk", "updateRule": "1", "type": "1", "image": "http://183.207.195.94:8080/xsbh5/app/pic1.png<br>http://183.207.195.94:8080/xsbh5/app/pic2.png", "versionNameReal": "2.0.9", "welcomeImageInfo": []}, {"appId": "APP0000000001", "updateTime": "2019-04-29 19:02:40", "versionCode": "1", "versionName": "2.18.123", "versionDesc": "1.上线新配预配号入网<br>2.优化工作台滑动和日历展示<br>3.新增日志页可查看业务和待久历史", "currentIs": null, "sysSupport": "支持Android2.1及以上智能手机操作系统", "screenSupport": "480×800", "appSize": "1.9M", "language": "简体中文", "downloadFileName": "http://183.207.195.94:8080/xsbh5/app/ald_2.0.4.apk", "updateRule": "1", "type": "1", "image": "http://183.207.195.94:8080/xsbh5/app/pic1.png<br>http://183.207.195.94:8080/xsbh5/app/pic2.png", "versionNameReal": "2.19.122", "welcomeImageInfo": [{"picid": "20191205", "picpath": "http://***************:8080/xsbh5/static/welcomepage/20191204-1.jpg", "sort": "1"}, {"picid": "20191205", "picpath": "http://***************:8080/xsbh5/static/welcomepage/20191204-2.jpg", "sort": "2"}]}]}, "h5GetQrCode": {"retCode": "0", "retMsg": null, "data": "http://www.baidu.com"}, "h5GetPayInfoList": {"retCode": "0", "retMsg": "success", "total": "0", "data": {"pageData": [{"busiNumber": "15152819752", "state": -1, "orderid": "20200220105533257", "payType": "ALIPAY", "realFee": "1000", "createTime": "2020-03-01 00:01:00", "isInvoice": 0, "email": "", "payBusiType": "1"}, {"busiNumber": "15152819752", "state": 0, "orderid": "20200220105533257", "payType": "ALIPAY", "realFee": "1000", "createTime": "2020-03-01 00:01:00", "isInvoice": 0, "email": "", "payBusiType": "1"}, {"busiNumber": "15152819752", "state": 20, "orderid": "20200220105533257", "payType": "ALIPAY", "realFee": "1000", "createTime": "2020-03-01 00:01:00", "isInvoice": 0, "email": "", "payBusiType": "1"}, {"busiNumber": "15152819752", "state": 21, "orderid": "20200220105533257", "payType": "ALIPAY", "realFee": "1000", "createTime": "2020-03-01 00:01:00", "isInvoice": 0, "email": "", "payBusiType": "1"}]}}, "getYsxAppVersionInfo": {"retCode": "0", "retMsg": null, "data": {"appId": "APP0000000001", "updateTime": "2020-03-19 12:16:11", "versionCode": null, "versionName": null, "versionDesc": null, "currentIs": null, "sysSupport": "支持安卓操作系统", "screenSupport": null, "appSize": null, "language": "简体中文", "downloadFileName": "itms-services://?action=download-manifest&url=https://ccg-1257785273.cos.ap-shanghai.myqcloud.com/ysx_2.20.41.plist", "updateRule": "1", "type": "1", "image": null, "versionNameReal": null, "welcomeImageInfo": null}}, "h5qryUserList": {"retCode": "0", "retMsg": "Success", "data": {"userList": [{"userName": "张二", "telnumber": "18216009641", "userType": "2", "acctId": "1842432100033191"}, {"userName": "<PERSON>三", "telnumber": "18216009642", "userType": "3", "acctId": "1842432100033192"}, {"userName": "王四", "telnumber": "18216009643", "userType": "2", "acctId": "1842432100033193"}, {"userName": "金五", "telnumber": "18216009644", "userType": "3", "acctId": "1842432100033195"}]}}, "h5getUserFeeList": {"retCode": "0", "retMsg": "", "data": {"userFeeList": [{"acctId": "1842432100033191", "billMonth": "1", "billtelNum": "18216009644", "totalFee": "3076", "paidamt": "1", "lateFee": "1", "payFee": "3076"}, {"acctId": "1842432100033192", "billMonth": "1", "billtelNum": "18216009645", "totalFee": "307006", "paidamt": "1", "lateFee": "1", "payFee": "307006"}, {"acctId": "1842432100033193", "billMonth": "1", "billtelNum": "18216009646", "totalFee": "307006", "paidamt": "1", "lateFee": "1", "payFee": "307006"}]}}, "h5paymentCommit": {"retCode": "0", "retMsg": null, "data": {"formnum": "123456789"}}, "h5GetPayBusiTypeList": {"retCode": "0", "retMsg": null, "data": [{"id": "tel_recharge", "label": "话费充值"}, {"id": "mband_prod_kaitong", "label": "宽带开通"}, {"id": "arrears_pay", "label": "欠费缴费"}]}, "h5GetOneNetGroupInfo": {"retCode": "0", "retMsg": "成功", "data": {"beginRowNum": "0", "fetchRowNum": "10", "totalRowNum": "10", "groupSubsInfo": [{"subsId": "1234", "subsName": "福建新大陆集团", "status": "未激活", "createTime": "2020/03/03", "effDate": "2020/03/03", "expDate": "2020/03/03", "acctId": "789630", "prodType": "GrpPrdTypeYWTPkg", "account": "***********", "resetPwdList": [{"resetPwd": "5"}, {"resetPwd": "4"}]}, {"subsId": "5678", "subsName": "江苏新大陆公司", "status": "未激活", "createTime": "2020/06/06", "effDate": "2020/06/06", "expDate": "2020/06/06", "acctId": "036987", "account": "***********", "prodType": "GrpPrdTypeYWTPkg", "resetPwdList": [{"resetPwd": "4"}]}]}}, "h5ResetOneNetPwd": {"retCode": "0", "retMsg": "成功", "data": "****************"}, "h5QryOneNetComListInfo": {"retCode": "0", "retMsg": "11111", "data": {"totalRowNum": "10", "infoList": [{"groupId": "123", "groupName": "测试集团", "groupSubsId": "345", "groupSubsName": "测试员1", "prodName": "集团宽带", "bandAccountNum": "123456", "tvAccountNum": "123456", "createTime": "2020/02/13 11:02"}]}}, "h5OneNetTransInfoSearch": {"retCode": "0", "retMsg": "success", "data": {"queryType": "1", "infoList": [{"status": "1", "tvStatus": "2", "radiusStatus": "1", "crmStatusTime": "**************", "tvStatusTime": "**************", "radiusStatusTime": " **************"}]}}, "h5OneNetTransInfoSync": {"retCode": "0", "retMsg": "success", "data": "************"}, "h5groupRingInfo": {"retCode": "0", "retMsg": "Success.", "data": [{"produId": "**********", "groupSubId": "1", "groupSubName": "集团彩铃1.0"}, {"produId": "**********", "groupSubId": "2", "groupSubName": "集团彩铃 2.0"}, {"produId": "**********", "groupSubId": "3", "groupSubName": "集团彩铃 3.0"}]}, "h5qryUserRing": {"retCode": "0", "retMsg": "Success.", "data": [{"produId": "**********", "groupSubId": "3", "groupSubName": "集团彩铃 3.0", "groupId": "123456", "groupName": "苹果(中国)公司", "servnum": "***********", "memberProFree": "9", "paymethod": "1"}]}, "h5updateUserRing": {"retCode": "0", "retMsg": "success", "data": null}, "h5delUserRing": {"retCode": "0", "retMsg": "Success.", "data": null}, "h5GroupBusinessType": {"retCode": "0", "retMsg": "success", "data": [{"dictName": "让渡", "dictValue": "11"}, {"dictName": "低消保档150档", "dictValue": "12"}, {"dictName": "信息采集2", "dictValue": "13"}, {"dictName": "信息采集3", "dictValue": "14"}, {"dictName": "信息采集4", "dictValue": "15"}, {"dictName": "信息采集信息采集信息采集", "dictValue": "16"}, {"dictName": "信息采集5", "dictValue": "17"}, {"dictName": "信息采集6", "dictValue": "18"}, {"dictName": "信息采集信息采集", "dictValue": "19"}, {"dictName": "信息采集信息采集信息采集1", "dictValue": "20"}, {"dictName": "信息采集信息采集1", "dictValue": "21"}, {"dictName": "信息采集信息采集信息采集信息采集信息采集", "dictValue": "22"}]}, "h5QryCampusBook": {"retCode": "0", "retMsg": "success", "data": [{"prodId": "100", "prodName": "2020流量至尊包优惠", "privId": "1000", "privName": "全家消费送宽带全家消费送宽带全家消费送宽带全家消费送宽带全家消费送宽带", "campusId": "tzxq001", "campusName": "泰州学校001", "remark": "2020流量至尊包优惠2020流量至尊包优惠2020流量至尊包优惠2020流量至尊包优惠2020流量至尊包优惠2020流量至尊包优惠"}, {"prodId": "101", "prodName": "2020南京友好客户语音扩容", "privId": "1001", "privName": "语音扩容100分钟", "campusId": "tzxq001", "campusName": "泰州学校001", "remark": "2020流量至尊包优惠2020流量至尊包优惠2020流量至尊包优惠2020流量至尊包优惠2020流量至尊包优惠2020流量至尊包优惠"}, {"prodId": "102", "prodName": "2020年南京校园权益包", "privId": "1002", "privName": "至尊版校园二合一权益包", "campusId": "tzxq001", "campusName": "泰州学校001", "remark": "2020流量至尊包优惠2020流量至尊包优惠2020流量至尊包优惠2020流量至尊包优惠2020流量至尊包优惠2020流量至尊包优惠"}, {"prodId": "103", "prodName": "2020南京校园指定套餐优惠活动江宁校区的业务活动", "privId": "1003", "privName": "校园指定套餐优惠", "campusId": "tzxq001", "campusName": "泰州学校001", "remark": "2020流量至尊包优惠2020流量至尊包优惠2020流量至尊包优惠2020流量至尊包优惠2020流量至尊包优惠2020流量至尊包优惠"}, {"prodId": "104", "prodName": "2020流量至尊包优惠", "privId": "1004", "privName": "全家消费送宽带", "campusId": "tzxq001", "campusName": "泰州学校001", "remark": "2020流量至尊包优惠2020流量至尊包优惠2020流量至尊包优惠2020流量至尊包优惠2020流量至尊包优惠2020流量至尊包优惠"}, {"prodId": "105", "prodName": "2020南京友好客户语音扩容", "privId": "1005", "privName": "语音扩容100分钟", "campusId": "tzxq001", "campusName": "泰州学校001", "remark": "2020流量至尊包优惠2020流量至尊包优惠2020流量至尊包优惠2020流量至尊包优惠2020流量至尊包优惠2020流量至尊包优惠"}, {"prodId": "106", "prodName": "2020年南京校园权益包", "privId": "1006", "privName": "至尊版校园二合一权益包", "campusId": "tzxq001", "campusName": "泰州学校001", "remark": "2020流量至尊包优惠2020流量至尊包优惠2020流量至尊包优惠2020流量至尊包优惠2020流量至尊包优惠2020流量至尊包优惠"}, {"prodId": "107", "prodName": "2020南京校园指定套餐优惠活动江宁校区的业务活动", "privId": "1007", "privName": "校园指定套餐优惠", "campusId": "tzxq001", "campusName": "泰州学校001", "remark": "2020流量至尊包优惠2020流量至尊包优惠2020流量至尊包优惠2020流量至尊包优惠2020流量至尊包优惠2020流量至尊包优惠"}]}, "h5BusinessCampus": {"retCode": "0", "retMsg": "success", "data": {"operatingSrl": "***********", "outSrl": "123"}}, "h5getPancPackageListTop": {"retCode": "0", "retMsg": "查询信用购打包列表成功", "data": [{"marketId": "2020041614001", "marketName": "2019全品类30_80元档_畅享自选套餐流量包80元", "activeId": "3002105312", "activeName": "2019全品类产品信用购活动30-3C", "levelId": "300004078044", "levelName": "80元档（24个月）", "packType": "3", "superType": "credit_loan", "marketDesc": null, "fee": "52000", "stagenum": "24", "operId": null, "region": "14", "issueType": "1", "isFavourite": "1", "rewardList": [{"packId": "8830032681", "packList": [{"rewardId": "2000011802", "rewardName": "畅享自选套餐流量包80元", "rewardPakid": "8830032681", "rewardPakname": "和分期80", "region": null, "needsn": "0"}], "packName": "和分期80"}, {"packId": "15905341", "packList": [{"rewardId": "2000009573", "rewardName": "任我选会员15元", "rewardPakid": "15905341", "rewardPakname": "和分期100", "region": null, "needsn": "0"}], "packName": "和分期100"}]}, {"marketId": "2020020514041", "marketName": "20年全品类信用购58档_任我用自选套餐流量包120元", "activeId": "3002105764", "activeName": "2020年南京普通渠道全品类信用购（套餐合约）", "levelId": "300004082645", "levelName": "信用购58元档（24个月）", "packType": "3", "superType": "credit_loan", "marketDesc": null, "fee": "32000", "stagenum": "24", "operId": null, "region": "14", "issueType": "1", "isFavourite": null, "rewardList": []}, {"marketId": "2020020514040", "marketName": "20年全品类信用购58档_任我用家庭版498元(全国版)", "activeId": "3002105764", "activeName": "2020年南京普通渠道全品类信用购（套餐合约）", "levelId": "300004082645", "levelName": "信用购58元档（24个月）", "packType": "3", "superType": "credit_loan", "marketDesc": null, "fee": "32000", "stagenum": "24", "operId": null, "region": "14", "issueType": "1", "isFavourite": null, "rewardList": []}, {"marketId": "2020020514039", "marketName": "20年全品类信用购58档_任我用家庭版158元", "activeId": "3002105764", "activeName": "2020年南京普通渠道全品类信用购（套餐合约）", "levelId": "300004082645", "levelName": "信用购58元档（24个月）", "packType": "3", "superType": "credit_loan", "marketDesc": null, "fee": "32000", "stagenum": "24", "operId": null, "region": "14", "issueType": "1", "isFavourite": null, "rewardList": []}, {"marketId": "2020020514038", "marketName": "20年全品类信用购58档_4G飞享家庭套餐（至尊版）328元", "activeId": "3002105764", "activeName": "2020年南京普通渠道全品类信用购（套餐合约）", "levelId": "300004082645", "levelName": "信用购58元档（24个月）", "packType": "3", "superType": "credit_loan", "marketDesc": null, "fee": "32000", "stagenum": "24", "operId": null, "region": "14", "issueType": "1", "isFavourite": null, "rewardList": []}, {"marketId": "2020020514036", "marketName": "20年全品类信用购58档_4G自选套餐流量包300元（2017版）", "activeId": "3002105764", "activeName": "2020年南京普通渠道全品类信用购（套餐合约）", "levelId": "300004082645", "levelName": "信用购58元档（24个月）", "packType": "3", "superType": "credit_loan", "marketDesc": null, "fee": "32000", "stagenum": "24", "operId": null, "region": "14", "issueType": "1", "isFavourite": null, "rewardList": []}, {"marketId": "2020020514035", "marketName": "20年全品类信用购58档_4G自选套餐流量包50元（2018版）", "activeId": "3002105764", "activeName": "2020年南京普通渠道全品类信用购（套餐合约）", "levelId": "300004082645", "levelName": "信用购58元档（24个月）", "packType": "3", "superType": "credit_loan", "marketDesc": null, "fee": "32000", "stagenum": "24", "operId": null, "region": "14", "issueType": "1", "isFavourite": null, "rewardList": []}, {"marketId": "2020020514034", "marketName": "20年全品类信用购58档_4G自选套餐流量包300元（2018版）", "activeId": "3002105764", "activeName": "2020年南京普通渠道全品类信用购（套餐合约）", "levelId": "300004082645", "levelName": "信用购58元档（24个月）", "packType": "3", "superType": "credit_loan", "marketDesc": null, "fee": "32000", "stagenum": "24", "operId": null, "region": "14", "issueType": "1", "isFavourite": null, "rewardList": []}]}, "h5getRecommndActivity": {"retCode": "0", "retMsg": null, "data": {"regionList": [], "jfList": [{"module_type": "3", "data": [{"sub_mol_type": "4", "activity_id": "ty100000172768", "activity_name": "全国亲情网1分钱体验", "step_id": "20191023083859825227000", "market_term": "请推荐客户体验1分钱全国亲情网，24个月全国亲友通话不限量。", "sms_type": "26", "sms_content": "", "knowledge_path": "/icsp/kbs/c_kbs_provServiceKng.action?kngId=&productName=", "is_recom_prod": "1", "data_list": [{"product_type": "7", "product_id": "8902006492", "product_id_exp1": "", "product_id_exp2": "", "product_id_exp3": "0", "product_id_exp4": "", "priority": null, "is_item": null, "cnt_reject": null, "cnt_consider": null, "content1": null, "content2": null, "content3": null, "content4": null, "content5": null, "product_id_exp5": "全国亲情网优惠活动（三）", "product_id_exp6": "", "product_id_exp7": "", "canDo": null}], "isData": "0", "show": "0"}], "isData": "0"}, {"module_type": "6", "data": [{"sub_mol_type": "10", "activity_id": "ty100000174048", "activity_name": "【2019优惠计划】任我用家庭版128元(全国版)", "step_id": "20191028164806854526000", "market_term": "引导用户办理2019优惠计划，每月得5G咪咕视频流量，优惠至2019年底", "sms_type": "26", "sms_content": "", "knowledge_path": "/icsp/kbs/c_kbs_provServiceKng.action?kngId=&productName=", "is_recom_prod": "1", "data_list": [{"product_type": "3", "product_id": "2000011420", "product_id_exp1": "", "product_id_exp2": "", "product_id_exp3": "1", "product_id_exp4": "", "priority": null, "is_item": null, "cnt_reject": null, "cnt_consider": null, "content1": null, "content2": null, "content3": null, "content4": null, "content5": null, "product_id_exp5": "任我用家庭版128元(全国版)（2019优惠计划）", "product_id_exp6": "", "product_id_exp7": "", "canDo": null}], "isData": "0", "show": "0"}], "isData": "0"}, {"module_type": "13", "data": [{"sub_mol_type": "13", "activity_id": "ty100000168400", "activity_name": "和彩云超值购（前折7折）（2019四季度）", "step_id": "96f3600c05324b7c96dc212d06e75788", "market_term": "通过本活动开通和彩云基础版可享受一个月功能费7折优惠（1元*0.7=0.7元），活动到期恢复标准资费（1元/月），业务继续保留，请问您需要办理吗？", "sms_type": "26", "sms_content": "", "knowledge_path": "/icsp/kbs/c_kbs_provServiceKng.action?kngId=20190926150621558565&productName=", "is_recom_prod": "1", "data_list": [{"product_type": "2", "product_id": "3002104134", "product_id_exp1": "300004064639", "product_id_exp2": "", "product_id_exp3": "1", "product_id_exp4": "", "priority": null, "is_item": null, "cnt_reject": null, "cnt_consider": null, "content1": null, "content2": null, "content3": null, "content4": null, "content5": null, "product_id_exp5": "基础功能业务超值购", "product_id_exp6": "手机网盘（和彩云基础版）超值购", "product_id_exp7": "", "canDo": "1"}], "isData": "0", "show": "0"}], "isData": "0"}, {"module_type": "11", "data": [{"sub_mol_type": "14", "activity_id": "ty100000131156", "activity_name": "南京移动分享卡优先推荐客户", "step_id": "20190510174336054843000", "market_term": "推荐办理移动分享卡新入网", "sms_type": "26", "sms_content": "", "knowledge_path": "/icsp/kbs/c_kbs_provServiceKng.action?kngId=&productName=", "is_recom_prod": "0", "data_list": null, "isData": "0", "show": "0"}, {"sub_mol_type": "14", "activity_id": "ty100000176918", "activity_name": "18年入网任我用客户接盘维系-原28任我用复制", "step_id": "20191104102709446752000", "market_term": "您的前期办理的新入网优惠即将到期，您可享折后套餐费58元起接续优惠（套餐费直降50，享250分钟语音扩容包）。优惠期12个月。办理套餐：90元自选流量套餐+18元自选语音套餐及以上档次办理活动1：活动大类：2019年南京套餐优惠活动（12个月）活动小类：19套餐优惠-直降50（108档及以上）办理活动2：活动大类：2019年南京语音扩容优惠活动小类：语音扩容250分钟", "sms_type": "26", "sms_content": "", "knowledge_path": "/icsp/kbs/c_kbs_provServiceKng.action?kngId=&productName=", "is_recom_prod": "1", "data_list": [{"product_type": "7", "product_id": "8902009480", "product_id_exp1": "", "product_id_exp2": "", "product_id_exp3": "0", "product_id_exp4": "", "priority": null, "is_item": null, "cnt_reject": null, "cnt_consider": null, "content1": null, "content2": null, "content3": null, "content4": null, "content5": null, "product_id_exp5": "108档直降50活动+250分钟语音扩容", "product_id_exp6": "", "product_id_exp7": "", "canDo": null}], "isData": "0", "show": "0"}], "isData": "0"}]}}, "h5bdsQryPersonInfo": {"retCode": "0", "retMsg": "【查询客户信息 BDS1001】success", "data": {"userCity": "14", "userApplyDate": "20140822120641", "custIcType": "1", "user_id": "1843300013305556", "userName": "毛琳娜", "custContactAddr": "江苏省南京市建邺区新城科技园", "custName": "江苏新大陆科技有限公司", "custIcNo": "999999999"}}, "h5getGroupArchivesInitData": {"retCode": "0", "retMsg": null, "data": {"1091": [{"id": "81", "label": "G3版ESOP", "parentId": null, "son": null}, {"id": "6", "label": "营业前台受理", "parentId": null, "son": null}, {"id": "35", "label": "10086人工台", "parentId": null, "son": null}, {"id": "22", "label": "短信受理请求", "parentId": null, "son": null}, {"id": "4", "label": "网上营业厅", "parentId": null, "son": null}, {"id": "19", "label": "掌上营业厅", "parentId": null, "son": null}, {"id": "16", "label": "空中营业厅", "parentId": null, "son": null}, {"id": "68", "label": "集团网厅", "parentId": null, "son": null}, {"id": "79", "label": "行商", "parentId": null, "son": null}], "1090": [{"id": "B", "label": "采矿业", "parentId": null, "son": [{"id": "44", "label": "电力、热力的生产和供应业", "parentId": "B", "son": null}, {"id": "45", "label": "燃气生产和供应业", "parentId": "B", "son": null}, {"id": "46", "label": "水的生产和供应业", "parentId": "B", "son": null}, {"id": "47", "label": "房屋和土木工程建筑业", "parentId": "B", "son": null}, {"id": "48", "label": "建筑安装业", "parentId": "B", "son": null}, {"id": "49", "label": "建筑装饰业", "parentId": "B", "son": null}]}, {"id": "C", "label": "制造业", "parentId": null, "son": [{"id": "18", "label": "皮革、毛皮、羽毛(绒)及其制品业", "parentId": "C", "son": null}, {"id": "50", "label": "其他建筑业", "parentId": "C", "son": null}, {"id": "51", "label": "铁路运输业", "parentId": "C", "son": null}, {"id": "52", "label": "道路运输业", "parentId": "C", "son": null}, {"id": "53", "label": "城市公共交通业", "parentId": "C", "son": null}, {"id": "54", "label": "水上运输业", "parentId": "C", "son": null}, {"id": "55", "label": "航空运输业", "parentId": "C", "son": null}, {"id": "56", "label": "管道运输业", "parentId": "C", "son": null}, {"id": "57", "label": "装卸搬运和其他运输服务业", "parentId": "C", "son": null}, {"id": "58", "label": "仓储业", "parentId": "C", "son": null}, {"id": "59", "label": "邮政业", "parentId": "C", "son": null}, {"id": "60", "label": "电信和其他信息传输服务业", "parentId": "C", "son": null}, {"id": "61", "label": "计算机服务业", "parentId": "C", "son": null}, {"id": "62", "label": "软件业", "parentId": "C", "son": null}, {"id": "63", "label": "批发业", "parentId": "C", "son": null}, {"id": "641", "label": "互联网零售", "parentId": "C", "son": null}, {"id": "65", "label": "零售业", "parentId": "C", "son": null}, {"id": "66", "label": "住宿业", "parentId": "C", "son": null}, {"id": "67", "label": "餐饮业", "parentId": "C", "son": null}, {"id": "68", "label": "银行业", "parentId": "C", "son": null}, {"id": "69", "label": "证券业", "parentId": "C", "son": null}, {"id": "70", "label": "保险业", "parentId": "C", "son": null}, {"id": "71", "label": "其他金融活动", "parentId": "C", "son": null}, {"id": "72", "label": "房地产业", "parentId": "C", "son": null}, {"id": "73", "label": "租赁业", "parentId": "C", "son": null}, {"id": "74", "label": "商务服务业", "parentId": "C", "son": null}, {"id": "75", "label": "研究与试验发展", "parentId": "C", "son": null}, {"id": "76", "label": "专业技术服务业", "parentId": "C", "son": null}, {"id": "77", "label": "科技交流和推广服务业", "parentId": "C", "son": null}, {"id": "78", "label": "地质勘查业", "parentId": "C", "son": null}, {"id": "79", "label": "水利管理业", "parentId": "C", "son": null}]}, {"id": "D", "label": "电力、燃气及水的生产和供应业", "parentId": null, "son": [{"id": "80", "label": "环境管理业", "parentId": "D", "son": null}, {"id": "81", "label": "公共设施管理业", "parentId": "D", "son": null}, {"id": "82", "label": "居民服务业", "parentId": "D", "son": null}]}, {"id": "E", "label": "建筑业", "parentId": null, "son": [{"id": "83", "label": "其他服务业", "parentId": "E", "son": null}, {"id": "84", "label": "教育", "parentId": "E", "son": null}, {"id": "85", "label": "卫生", "parentId": "E", "son": null}, {"id": "86", "label": "社会保障业", "parentId": "E", "son": null}]}, {"id": "F", "label": "交通运输、仓储和邮政业", "parentId": null, "son": [{"id": "87", "label": "社会福利业", "parentId": "F", "son": null}, {"id": "88", "label": "新闻出版业", "parentId": "F", "son": null}, {"id": "89", "label": "广播、电视、电影和音像业", "parentId": "F", "son": null}, {"id": "90", "label": "文化艺术业", "parentId": "F", "son": null}, {"id": "91", "label": "体育", "parentId": "F", "son": null}, {"id": "92", "label": "娱乐业", "parentId": "F", "son": null}, {"id": "93", "label": "中国共产党机关", "parentId": "F", "son": null}, {"id": "94", "label": "国家机构", "parentId": "F", "son": null}, {"id": "95", "label": "人民政协和民主党派", "parentId": "F", "son": null}]}, {"id": "G", "label": "信息传输、计算机服务和软件业", "parentId": null, "son": [{"id": "96", "label": "群众团体、社会团体和宗教组织", "parentId": "G", "son": null}, {"id": "97", "label": "基层群众自治组织", "parentId": "G", "son": null}, {"id": "98", "label": "国际组织", "parentId": "G", "son": null}]}, {"id": "H", "label": "批发和零售业", "parentId": null, "son": [{"id": "0", "label": "未确定", "parentId": "H", "son": null}, {"id": "U001", "label": "综合管理机构", "parentId": "H", "son": null}]}, {"id": "I", "label": "住宿和餐饮业", "parentId": null, "son": [{"id": "2", "label": "林业", "parentId": "I", "son": null}, {"id": "U002", "label": "政治工作机构", "parentId": "I", "son": null}, {"id": "U003", "label": "动员工作机构", "parentId": "I", "son": null}]}, {"id": "J", "label": "金融业", "parentId": null, "son": [{"id": "U004", "label": "政法机构", "parentId": "J", "son": null}, {"id": "U005", "label": "直属院校", "parentId": "J", "son": null}, {"id": "U006", "label": "ZQ", "parentId": "J", "son": null}, {"id": "U007", "label": "LJ", "parentId": "J", "son": null}]}, {"id": "K", "label": "房地产业", "parentId": null, "son": [{"id": "1", "label": "农业", "parentId": "K", "son": null}, {"id": "U008", "label": "HJ", "parentId": "K", "son": null}]}, {"id": "L", "label": "租赁和商务服务业", "parentId": null, "son": [{"id": "U009", "label": "KJ", "parentId": "L", "son": null}, {"id": "U010", "label": "HJJ", "parentId": "L", "son": null}]}, {"id": "M", "label": "科学研究、技术服务和地质勘查业", "parentId": null, "son": [{"id": "U011", "label": "ZZ", "parentId": "M", "son": null}, {"id": "U012", "label": "LQ", "parentId": "M", "son": null}, {"id": "U013", "label": "WJ", "parentId": "M", "son": null}, {"id": "U014", "label": "融合事务管理机构", "parentId": "M", "son": null}]}, {"id": "N", "label": "水利、环境和公共设施管理业", "parentId": null, "son": [{"id": "19", "label": "木材加工及木、竹、藤、棕、草制品业", "parentId": "N", "son": null}, {"id": "U015", "label": "退役事务管理机构", "parentId": "N", "son": null}, {"id": "U016", "label": "融合企业", "parentId": "N", "son": null}]}, {"id": "O", "label": "居民服务和其他服务业", "parentId": null, "son": [{"id": "20", "label": "家具制造业", "parentId": "O", "son": null}, {"id": "21", "label": "造纸及纸制品业", "parentId": "O", "son": null}]}, {"id": "P", "label": "教育", "parentId": null, "son": [{"id": "22", "label": "印刷业和记录媒介的复制", "parentId": "P", "son": null}]}, {"id": "Q", "label": "卫生、社会保障和社会福利业", "parentId": null, "son": [{"id": "23", "label": "文教体育用品制造业", "parentId": "Q", "son": null}, {"id": "24", "label": "石油加工、炼焦及核燃料加工业", "parentId": "Q", "son": null}, {"id": "25", "label": "化学原料及化学制品制造业", "parentId": "Q", "son": null}]}, {"id": "R", "label": "文化、体育和娱乐业", "parentId": null, "son": [{"id": "26", "label": "医药制造业", "parentId": "R", "son": null}, {"id": "27", "label": "化学纤维制造业", "parentId": "R", "son": null}, {"id": "28", "label": "橡胶制品业", "parentId": "R", "son": null}, {"id": "29", "label": "塑料制品业", "parentId": "R", "son": null}, {"id": "30", "label": "非金属矿物制品业", "parentId": "R", "son": null}]}, {"id": "S", "label": "公共管理和社会组织", "parentId": null, "son": [{"id": "3", "label": "畜牧业", "parentId": "S", "son": null}, {"id": "4", "label": "渔业", "parentId": "S", "son": null}, {"id": "5", "label": "农、林、牧、渔服务业", "parentId": "S", "son": null}, {"id": "6", "label": "煤炭开采和洗选业", "parentId": "S", "son": null}, {"id": "7", "label": "石油和天然气开采业", "parentId": "S", "son": null}, {"id": "8", "label": "黑色金属矿采选业", "parentId": "S", "son": null}, {"id": "9", "label": "有色金属矿采选业", "parentId": "S", "son": null}, {"id": "10", "label": "非金属矿采选业", "parentId": "S", "son": null}, {"id": "11", "label": "其他采矿业", "parentId": "S", "son": null}, {"id": "12", "label": "农副食品加工业", "parentId": "S", "son": null}, {"id": "13", "label": "食品制造业", "parentId": "S", "son": null}, {"id": "14", "label": "饮料制造业", "parentId": "S", "son": null}, {"id": "15", "label": "烟草制品业", "parentId": "S", "son": null}, {"id": "16", "label": "纺织业", "parentId": "S", "son": null}, {"id": "17", "label": "纺织服装、鞋、帽制造业", "parentId": "S", "son": null}, {"id": "31", "label": "黑色金属冶炼及压延加工业", "parentId": "S", "son": null}, {"id": "32", "label": "有色金属冶炼及压延加工业", "parentId": "S", "son": null}, {"id": "33", "label": "金属制品业", "parentId": "S", "son": null}, {"id": "34", "label": "通用设备制造业", "parentId": "S", "son": null}, {"id": "35", "label": "专用设备制造业", "parentId": "S", "son": null}]}, {"id": "T", "label": "国际组织", "parentId": null, "son": [{"id": "36", "label": "交通运输设备制造业", "parentId": "T", "son": null}]}, {"id": "A", "label": "农、林、牧、渔业", "parentId": null, "son": [{"id": "37", "label": "电气机械及器材制造业", "parentId": "A", "son": null}, {"id": "40", "label": "通信设备、计算机及其他电子设备制造业", "parentId": "A", "son": null}, {"id": "41", "label": "仪器仪表及文化、办公用机械制造业", "parentId": "A", "son": null}, {"id": "42", "label": "工艺品及其他制造业", "parentId": "A", "son": null}, {"id": "43", "label": "废弃资源和废旧材料回收加工业", "parentId": "A", "son": null}]}, {"id": "U", "label": "国防", "parentId": null, "son": [{"id": "99", "label": "交通运输", "parentId": "U", "son": null}, {"id": "697", "label": "餐饮配送服务", "parentId": "U", "son": null}, {"id": "725", "label": "房地产中介服务", "parentId": "U", "son": null}, {"id": "P001", "label": "国防院校", "parentId": "U", "son": null}, {"id": "Q003", "label": "国防医院", "parentId": "U", "son": null}]}, {"id": "0", "label": "未确定", "parentId": null, "son": []}], "1088": [{"id": "corporation1", "label": "法人单位", "parentId": null, "son": null}, {"id": "corporation11", "label": "机关法人", "parentId": null, "son": null}, {"id": "corporation12", "label": "企业法人", "parentId": null, "son": null}, {"id": "corporation13", "label": "事业单位法人", "parentId": null, "son": null}, {"id": "corporation14", "label": "社会团体法人", "parentId": null, "son": null}, {"id": "corporation15", "label": "其它法人", "parentId": null, "son": null}, {"id": "individual1", "label": "个体经营户", "parentId": null, "son": null}, {"id": "clustering1", "label": "聚类客户", "parentId": null, "son": null}, {"id": "clustering11", "label": "部门聚类客户", "parentId": null, "son": null}, {"id": "clustering12", "label": "田园聚类客户", "parentId": null, "son": null}, {"id": "clustering13", "label": "校园聚类客户", "parentId": null, "son": null}, {"id": "clustering14", "label": "乡情聚类客户", "parentId": null, "son": null}, {"id": "clustering15", "label": "社区聚类客户", "parentId": null, "son": null}, {"id": "clustering16", "label": "其它聚类客户", "parentId": null, "son": null}, {"id": "other", "label": "其它", "parentId": null, "son": null}, {"id": "industry", "label": "产业活动单位", "parentId": null, "son": null}], "1099": [{"id": "ExpandChaType1", "label": "客户经理", "parentId": null, "son": null}, {"id": "ExpandChaType2", "label": "销售代理商(SA)", "parentId": null, "son": null}, {"id": "ExpandChaType3", "label": "电话客户经理", "parentId": null, "son": null}, {"id": "ExpandChaType4", "label": "10086集团热线", "parentId": null, "son": null}, {"id": "ExpandChaType5", "label": "服营厅", "parentId": null, "son": null}, {"id": "ExpandChaType6", "label": "门户网站", "parentId": null, "son": null}], "1087": [{"id": "1", "label": "国有", "parentId": null, "son": null}, {"id": "2", "label": "集体", "parentId": null, "son": null}, {"id": "3", "label": "私营", "parentId": null, "son": null}, {"id": "4", "label": "股份", "parentId": null, "son": null}, {"id": "5", "label": "外商投资", "parentId": null, "son": null}, {"id": "6", "label": "港澳台投资", "parentId": null, "son": null}, {"id": "7", "label": "合资", "parentId": null, "son": null}, {"id": "8", "label": "独资", "parentId": null, "son": null}, {"id": "9", "label": "政府部门", "parentId": null, "son": null}, {"id": "99", "label": "其它", "parentId": null, "son": null}, {"id": "10", "label": "行政事业类", "parentId": null, "son": null}, {"id": "11", "label": "自办营业厅", "parentId": null, "son": null}, {"id": "12", "label": "合作营业厅", "parentId": null, "son": null}], "1098": [{"id": "0", "label": "普通级", "parentId": null, "son": null}, {"id": "1", "label": "1A级", "parentId": null, "son": null}, {"id": "2", "label": "2A级", "parentId": null, "son": null}, {"id": "3", "label": "3A级", "parentId": null, "son": null}], "1086": [{"id": "14", "label": "南京地区", "parentId": null, "son": [{"id": "1424", "label": "溧水", "parentId": "14", "son": null}, {"id": "1425", "label": "江浦", "parentId": "14", "son": null}, {"id": "1426", "label": "高淳", "parentId": "14", "son": null}, {"id": "1419", "label": "南京市区", "parentId": "14", "son": null}, {"id": "1422", "label": "六合", "parentId": "14", "son": null}, {"id": "1423", "label": "江宁", "parentId": "14", "son": null}, {"id": "1430", "label": "玄武区", "parentId": "14", "son": null}, {"id": "1431", "label": "秦淮区", "parentId": "14", "son": null}, {"id": "1432", "label": "鼓楼区", "parentId": "14", "son": null}, {"id": "1433", "label": "建邺区", "parentId": "14", "son": null}, {"id": "1434", "label": "雨花台区", "parentId": "14", "son": null}, {"id": "1435", "label": "栖霞区", "parentId": "14", "son": null}]}, {"id": "12", "label": "淮安地区", "parentId": null, "son": [{"id": "1210", "label": "淮安市区", "parentId": "12", "son": null}, {"id": "1211", "label": "淮阴区", "parentId": "12", "son": null}, {"id": "1212", "label": "涟水", "parentId": "12", "son": null}, {"id": "1213", "label": "洪泽", "parentId": "12", "son": null}, {"id": "1214", "label": "楚州区", "parentId": "12", "son": null}, {"id": "1208", "label": "盱眙", "parentId": "12", "son": null}, {"id": "1209", "label": "金湖", "parentId": "12", "son": null}]}, {"id": "13", "label": "宿迁地区", "parentId": null, "son": [{"id": "1317", "label": "泗阳", "parentId": "13", "son": null}, {"id": "1318", "label": "泗洪", "parentId": "13", "son": null}, {"id": "1380", "label": "宿豫", "parentId": "13", "son": null}, {"id": "1315", "label": "宿城", "parentId": "13", "son": null}, {"id": "1316", "label": "沭阳", "parentId": "13", "son": null}]}, {"id": "11", "label": "苏州地区", "parentId": null, "son": [{"id": "1101", "label": "苏州市区", "parentId": "11", "son": null}, {"id": "1103", "label": "昆山", "parentId": "11", "son": null}, {"id": "1104", "label": "张家港", "parentId": "11", "son": null}, {"id": "1105", "label": "吴江", "parentId": "11", "son": null}, {"id": "1106", "label": "常熟", "parentId": "11", "son": null}, {"id": "1107", "label": "太仓", "parentId": "11", "son": null}, {"id": "1112", "label": "西区", "parentId": "11", "son": null}, {"id": "1113", "label": "城区", "parentId": "11", "son": null}, {"id": "1111", "label": "东区", "parentId": "11", "son": null}]}, {"id": "15", "label": "连云港地区", "parentId": null, "son": [{"id": "1527", "label": "新海", "parentId": "15", "son": null}, {"id": "1528", "label": "赣榆", "parentId": "15", "son": null}, {"id": "1529", "label": "东海", "parentId": "15", "son": null}, {"id": "1530", "label": "灌云", "parentId": "15", "son": null}, {"id": "1531", "label": "灌南", "parentId": "15", "son": null}, {"id": "1532", "label": "连云港市区", "parentId": "15", "son": null}, {"id": "1576", "label": "连云", "parentId": "15", "son": null}]}, {"id": "16", "label": "徐州地区", "parentId": null, "son": [{"id": "1632", "label": "沛县", "parentId": "16", "son": null}, {"id": "1633", "label": "徐州", "parentId": "16", "son": null}, {"id": "1634", "label": "丰县", "parentId": "16", "son": null}, {"id": "1635", "label": "睢宁", "parentId": "16", "son": null}, {"id": "1636", "label": "新沂", "parentId": "16", "son": null}, {"id": "1637", "label": "邳州", "parentId": "16", "son": null}, {"id": "1677", "label": "铜山", "parentId": "16", "son": null}, {"id": "1678", "label": "贾汪", "parentId": "16", "son": null}]}, {"id": "17", "label": "常州地区", "parentId": null, "son": [{"id": "1740", "label": "金坛", "parentId": "17", "son": null}, {"id": "1741", "label": "溧阳", "parentId": "17", "son": null}, {"id": "1742", "label": "武进", "parentId": "17", "son": null}, {"id": "1743", "label": "新北", "parentId": "17", "son": null}, {"id": "1744", "label": "城区", "parentId": "17", "son": null}, {"id": "1738", "label": "常州市区", "parentId": "17", "son": null}]}, {"id": "18", "label": "镇江地区", "parentId": null, "son": [{"id": "1842", "label": "镇江市区", "parentId": "18", "son": null}, {"id": "1843", "label": "丹阳", "parentId": "18", "son": null}, {"id": "1844", "label": "扬中", "parentId": "18", "son": null}, {"id": "1845", "label": "句容", "parentId": "18", "son": null}]}, {"id": "19", "label": "无锡地区", "parentId": null, "son": [{"id": "1946", "label": "无锡市区", "parentId": "19", "son": null}, {"id": "1948", "label": "江阴", "parentId": "19", "son": null}, {"id": "1949", "label": "宜兴", "parentId": "19", "son": null}, {"id": "1960", "label": "梁溪区", "parentId": "19", "son": null}, {"id": "1961", "label": "南长区", "parentId": "19", "son": null}, {"id": "1962", "label": "北塘区", "parentId": "19", "son": null}, {"id": "1963", "label": "滨湖区", "parentId": "19", "son": null}, {"id": "1964", "label": "惠山区", "parentId": "19", "son": null}, {"id": "1965", "label": "锡山区", "parentId": "19", "son": null}, {"id": "1966", "label": "新吴区", "parentId": "19", "son": null}, {"id": "1967", "label": "政企部", "parentId": "19", "son": null}]}, {"id": "20", "label": "南通地区", "parentId": null, "son": [{"id": "2050", "label": "南通市区", "parentId": "20", "son": null}, {"id": "2056", "label": "如皋", "parentId": "20", "son": null}, {"id": "2051", "label": "启东", "parentId": "20", "son": null}, {"id": "2052", "label": "海门", "parentId": "20", "son": null}, {"id": "2053", "label": "海安", "parentId": "20", "son": null}, {"id": "2054", "label": "如东", "parentId": "20", "son": null}, {"id": "2055", "label": "通州", "parentId": "20", "son": null}]}, {"id": "21", "label": "泰州地区", "parentId": null, "son": [{"id": "2160", "label": "兴化", "parentId": "21", "son": null}, {"id": "2161", "label": "泰兴", "parentId": "21", "son": null}, {"id": "2175", "label": "高港区", "parentId": "21", "son": null}, {"id": "2157", "label": "海陵", "parentId": "21", "son": null}, {"id": "2158", "label": "靖江", "parentId": "21", "son": null}, {"id": "2159", "label": "姜堰", "parentId": "21", "son": null}]}, {"id": "22", "label": "盐城地区", "parentId": null, "son": [{"id": "2262", "label": "射阳", "parentId": "22", "son": null}, {"id": "2263", "label": "东台", "parentId": "22", "son": null}, {"id": "2264", "label": "盐城市区", "parentId": "22", "son": null}, {"id": "2265", "label": "响水", "parentId": "22", "son": null}, {"id": "2266", "label": "滨海", "parentId": "22", "son": null}, {"id": "2267", "label": "建湖", "parentId": "22", "son": null}, {"id": "2268", "label": "大丰", "parentId": "22", "son": null}, {"id": "2269", "label": "阜宁", "parentId": "22", "son": null}]}, {"id": "23", "label": "扬州地区", "parentId": null, "son": [{"id": "2379", "label": "邗江区", "parentId": "23", "son": null}, {"id": "2370", "label": "仪征市", "parentId": "23", "son": null}, {"id": "2371", "label": "扬州市区", "parentId": "23", "son": null}, {"id": "2372", "label": "高邮市", "parentId": "23", "son": null}, {"id": "2373", "label": "宝应市", "parentId": "23", "son": null}, {"id": "2374", "label": "江都市", "parentId": "23", "son": null}]}], "1097": [{"id": "A", "label": "A", "parentId": null, "son": null}, {"id": "B", "label": "B", "parentId": null, "son": null}, {"id": "C", "label": "C", "parentId": null, "son": null}, {"id": "D", "label": "D", "parentId": null, "son": null}], "1085": [{"id": "0", "label": "否", "parentId": null, "son": null}, {"id": "1", "label": "是", "parentId": null, "son": null}], "1096": [{"id": "1", "label": "普通", "parentId": null, "son": null}, {"id": "2", "label": "银牌", "parentId": null, "son": null}, {"id": "3", "label": "金牌", "parentId": null, "son": null}, {"id": "4", "label": "VIP", "parentId": null, "son": null}], "1084": [{"id": "0", "label": "否", "parentId": null, "son": null}, {"id": "1", "label": "是", "parentId": null, "son": null}, {"id": "2", "label": "其它", "parentId": null, "son": null}], "1095": [{"id": "1", "label": "金牌级", "parentId": null, "son": null}, {"id": "4", "label": "标准级", "parentId": null, "son": null}, {"id": "3", "label": "铜牌级", "parentId": null, "son": null}, {"id": "2", "label": "银牌级", "parentId": null, "son": null}], "1083": [{"id": "1101", "label": "苏州市区", "parentId": "11", "son": null}, {"id": "1103", "label": "昆山", "parentId": "11", "son": null}, {"id": "1104", "label": "张家港", "parentId": "11", "son": null}, {"id": "1105", "label": "吴江", "parentId": "11", "son": null}, {"id": "1106", "label": "常熟", "parentId": "11", "son": null}, {"id": "1107", "label": "太仓", "parentId": "11", "son": null}, {"id": "1112", "label": "西区", "parentId": "11", "son": null}, {"id": "1113", "label": "城区", "parentId": "11", "son": null}, {"id": "1111", "label": "东区", "parentId": "11", "son": null}, {"id": "1210", "label": "淮安市区", "parentId": "12", "son": null}, {"id": "1211", "label": "淮阴区", "parentId": "12", "son": null}, {"id": "1212", "label": "涟水", "parentId": "12", "son": null}, {"id": "1213", "label": "洪泽", "parentId": "12", "son": null}, {"id": "1214", "label": "楚州区", "parentId": "12", "son": null}, {"id": "1208", "label": "盱眙", "parentId": "12", "son": null}, {"id": "1209", "label": "金湖", "parentId": "12", "son": null}, {"id": "1317", "label": "泗阳", "parentId": "13", "son": null}, {"id": "1318", "label": "泗洪", "parentId": "13", "son": null}, {"id": "1380", "label": "宿豫", "parentId": "13", "son": null}, {"id": "1315", "label": "宿城", "parentId": "13", "son": null}, {"id": "1316", "label": "沭阳", "parentId": "13", "son": null}, {"id": "1424", "label": "溧水", "parentId": "14", "son": null}, {"id": "1425", "label": "江浦", "parentId": "14", "son": null}, {"id": "1426", "label": "高淳", "parentId": "14", "son": null}, {"id": "1419", "label": "南京市区", "parentId": "14", "son": null}, {"id": "1422", "label": "六合", "parentId": "14", "son": null}, {"id": "1423", "label": "江宁", "parentId": "14", "son": null}, {"id": "1430", "label": "玄武区", "parentId": "14", "son": null}, {"id": "1431", "label": "秦淮区", "parentId": "14", "son": null}, {"id": "1432", "label": "鼓楼区", "parentId": "14", "son": null}, {"id": "1433", "label": "建邺区", "parentId": "14", "son": null}, {"id": "1434", "label": "雨花台区", "parentId": "14", "son": null}, {"id": "1435", "label": "栖霞区", "parentId": "14", "son": null}, {"id": "1527", "label": "新海", "parentId": "15", "son": null}, {"id": "1528", "label": "赣榆", "parentId": "15", "son": null}, {"id": "1529", "label": "东海", "parentId": "15", "son": null}, {"id": "1530", "label": "灌云", "parentId": "15", "son": null}, {"id": "1531", "label": "灌南", "parentId": "15", "son": null}, {"id": "1532", "label": "连云港市区", "parentId": "15", "son": null}, {"id": "1576", "label": "连云", "parentId": "15", "son": null}, {"id": "1632", "label": "沛县", "parentId": "16", "son": null}, {"id": "1633", "label": "徐州", "parentId": "16", "son": null}, {"id": "1634", "label": "丰县", "parentId": "16", "son": null}, {"id": "1635", "label": "睢宁", "parentId": "16", "son": null}, {"id": "1636", "label": "新沂", "parentId": "16", "son": null}, {"id": "1637", "label": "邳州", "parentId": "16", "son": null}, {"id": "1677", "label": "铜山", "parentId": "16", "son": null}, {"id": "1678", "label": "贾汪", "parentId": "16", "son": null}, {"id": "1740", "label": "金坛", "parentId": "17", "son": null}, {"id": "1741", "label": "溧阳", "parentId": "17", "son": null}, {"id": "1742", "label": "武进", "parentId": "17", "son": null}, {"id": "1743", "label": "新北", "parentId": "17", "son": null}, {"id": "1744", "label": "城区", "parentId": "17", "son": null}, {"id": "1738", "label": "常州市区", "parentId": "17", "son": null}, {"id": "1842", "label": "镇江市区", "parentId": "18", "son": null}, {"id": "1843", "label": "丹阳", "parentId": "18", "son": null}, {"id": "1844", "label": "扬中", "parentId": "18", "son": null}, {"id": "1845", "label": "句容", "parentId": "18", "son": null}, {"id": "1946", "label": "无锡市区", "parentId": "19", "son": null}, {"id": "1948", "label": "江阴", "parentId": "19", "son": null}, {"id": "1949", "label": "宜兴", "parentId": "19", "son": null}, {"id": "1960", "label": "梁溪区", "parentId": "19", "son": null}, {"id": "1961", "label": "南长区", "parentId": "19", "son": null}, {"id": "1962", "label": "北塘区", "parentId": "19", "son": null}, {"id": "1963", "label": "滨湖区", "parentId": "19", "son": null}, {"id": "1964", "label": "惠山区", "parentId": "19", "son": null}, {"id": "1965", "label": "锡山区", "parentId": "19", "son": null}, {"id": "1966", "label": "新吴区", "parentId": "19", "son": null}, {"id": "1967", "label": "政企部", "parentId": "19", "son": null}, {"id": "2050", "label": "南通市区", "parentId": "20", "son": null}, {"id": "2056", "label": "如皋", "parentId": "20", "son": null}, {"id": "2051", "label": "启东", "parentId": "20", "son": null}, {"id": "2052", "label": "海门", "parentId": "20", "son": null}, {"id": "2053", "label": "海安", "parentId": "20", "son": null}, {"id": "2054", "label": "如东", "parentId": "20", "son": null}, {"id": "2055", "label": "通州", "parentId": "20", "son": null}, {"id": "2160", "label": "兴化", "parentId": "21", "son": null}, {"id": "2161", "label": "泰兴", "parentId": "21", "son": null}, {"id": "2175", "label": "高港区", "parentId": "21", "son": null}, {"id": "2157", "label": "海陵", "parentId": "21", "son": null}, {"id": "2158", "label": "靖江", "parentId": "21", "son": null}, {"id": "2159", "label": "姜堰", "parentId": "21", "son": null}, {"id": "2262", "label": "射阳", "parentId": "22", "son": null}, {"id": "2263", "label": "东台", "parentId": "22", "son": null}, {"id": "2264", "label": "盐城市区", "parentId": "22", "son": null}, {"id": "2265", "label": "响水", "parentId": "22", "son": null}, {"id": "2266", "label": "滨海", "parentId": "22", "son": null}, {"id": "2267", "label": "建湖", "parentId": "22", "son": null}, {"id": "2268", "label": "大丰", "parentId": "22", "son": null}, {"id": "2269", "label": "阜宁", "parentId": "22", "son": null}, {"id": "2379", "label": "邗江区", "parentId": "23", "son": null}, {"id": "2370", "label": "仪征市", "parentId": "23", "son": null}, {"id": "2371", "label": "扬州市区", "parentId": "23", "son": null}, {"id": "2372", "label": "高邮市", "parentId": "23", "son": null}, {"id": "2373", "label": "宝应市", "parentId": "23", "son": null}, {"id": "2374", "label": "江都市", "parentId": "23", "son": null}], "1094": [{"id": "1", "label": "双跨", "parentId": null, "son": null}, {"id": "2", "label": "省级", "parentId": null, "son": null}, {"id": "3", "label": "市县级", "parentId": null, "son": null}, {"id": "4", "label": "普通", "parentId": null, "son": null}, {"id": "5", "label": "聚类", "parentId": null, "son": null}, {"id": "9", "label": "其他", "parentId": null, "son": null}], "1082": [{"id": "LocationType04", "label": "普通集团", "parentId": null, "son": null}, {"id": "LocationType07", "label": "离岸集团", "parentId": null, "son": null}, {"id": "LocationType08", "label": "行业集团", "parentId": null, "son": null}], "1093": [{"id": "0", "label": "特大型", "parentId": null, "son": null}, {"id": "1", "label": "大型", "parentId": null, "son": null}, {"id": "2", "label": "中型", "parentId": null, "son": null}, {"id": "3", "label": "小微型", "parentId": null, "son": null}, {"id": "large", "label": "大型集团", "parentId": null, "son": null}, {"id": "medium", "label": "中型集团", "parentId": null, "son": null}, {"id": "small", "label": "小型集团", "parentId": null, "son": null}], "1092": [{"id": "400103005", "label": "独立集团", "parentId": null, "son": null}, {"id": "400103000", "label": "顶级（总部）", "parentId": null, "son": null}, {"id": "400103001", "label": "一级分支", "parentId": null, "son": null}, {"id": "400103002", "label": "二级分支", "parentId": null, "son": null}, {"id": "400103003", "label": "三级分支", "parentId": null, "son": null}, {"id": "400103004", "label": "四级分支", "parentId": null, "son": null}], "1": [{"id": "2003932", "label": "测试一级", "parentId": null, "son": [{"id": "10000001", "label": "测试二级", "parentId": null, "son": null}, {"id": "2003932", "label": "测试一级", "parentId": null, "son": null}, {"id": "10000005", "label": "干扰数据", "parentId": null, "son": null}]}, {"id": "10000005", "label": "干扰数据", "parentId": null, "son": null}, {"id": "10000001", "label": "测试二级", "parentId": null, "son": null}], "1101": [{"id": "1", "label": "农业", "parentId": "K", "son": null}, {"id": "2", "label": "林业", "parentId": "I", "son": null}, {"id": "3", "label": "畜牧业", "parentId": "S", "son": null}, {"id": "4", "label": "渔业", "parentId": "S", "son": null}, {"id": "5", "label": "农、林、牧、渔服务业", "parentId": "S", "son": null}, {"id": "6", "label": "煤炭开采和洗选业", "parentId": "S", "son": null}, {"id": "7", "label": "石油和天然气开采业", "parentId": "S", "son": null}, {"id": "8", "label": "黑色金属矿采选业", "parentId": "S", "son": null}, {"id": "9", "label": "有色金属矿采选业", "parentId": "S", "son": null}, {"id": "10", "label": "非金属矿采选业", "parentId": "S", "son": null}, {"id": "11", "label": "其他采矿业", "parentId": "S", "son": null}, {"id": "12", "label": "农副食品加工业", "parentId": "S", "son": null}, {"id": "13", "label": "食品制造业", "parentId": "S", "son": null}, {"id": "14", "label": "饮料制造业", "parentId": "S", "son": null}, {"id": "15", "label": "烟草制品业", "parentId": "S", "son": null}, {"id": "16", "label": "纺织业", "parentId": "S", "son": null}, {"id": "17", "label": "纺织服装、鞋、帽制造业", "parentId": "S", "son": null}, {"id": "18", "label": "皮革、毛皮、羽毛(绒)及其制品业", "parentId": "C", "son": null}, {"id": "19", "label": "木材加工及木、竹、藤、棕、草制品业", "parentId": "N", "son": null}, {"id": "20", "label": "家具制造业", "parentId": "O", "son": null}, {"id": "21", "label": "造纸及纸制品业", "parentId": "O", "son": null}, {"id": "22", "label": "印刷业和记录媒介的复制", "parentId": "P", "son": null}, {"id": "23", "label": "文教体育用品制造业", "parentId": "Q", "son": null}, {"id": "24", "label": "石油加工、炼焦及核燃料加工业", "parentId": "Q", "son": null}, {"id": "25", "label": "化学原料及化学制品制造业", "parentId": "Q", "son": null}, {"id": "26", "label": "医药制造业", "parentId": "R", "son": null}, {"id": "27", "label": "化学纤维制造业", "parentId": "R", "son": null}, {"id": "28", "label": "橡胶制品业", "parentId": "R", "son": null}, {"id": "29", "label": "塑料制品业", "parentId": "R", "son": null}, {"id": "30", "label": "非金属矿物制品业", "parentId": "R", "son": null}, {"id": "31", "label": "黑色金属冶炼及压延加工业", "parentId": "S", "son": null}, {"id": "32", "label": "有色金属冶炼及压延加工业", "parentId": "S", "son": null}, {"id": "33", "label": "金属制品业", "parentId": "S", "son": null}, {"id": "34", "label": "通用设备制造业", "parentId": "S", "son": null}, {"id": "35", "label": "专用设备制造业", "parentId": "S", "son": null}, {"id": "36", "label": "交通运输设备制造业", "parentId": "T", "son": null}, {"id": "37", "label": "电气机械及器材制造业", "parentId": "A", "son": null}, {"id": "40", "label": "通信设备、计算机及其他电子设备制造业", "parentId": "A", "son": null}, {"id": "41", "label": "仪器仪表及文化、办公用机械制造业", "parentId": "A", "son": null}, {"id": "42", "label": "工艺品及其他制造业", "parentId": "A", "son": null}, {"id": "43", "label": "废弃资源和废旧材料回收加工业", "parentId": "A", "son": null}, {"id": "44", "label": "电力、热力的生产和供应业", "parentId": "B", "son": null}, {"id": "45", "label": "燃气生产和供应业", "parentId": "B", "son": null}, {"id": "46", "label": "水的生产和供应业", "parentId": "B", "son": null}, {"id": "47", "label": "房屋和土木工程建筑业", "parentId": "B", "son": null}, {"id": "48", "label": "建筑安装业", "parentId": "B", "son": null}, {"id": "49", "label": "建筑装饰业", "parentId": "B", "son": null}, {"id": "50", "label": "其他建筑业", "parentId": "C", "son": null}, {"id": "51", "label": "铁路运输业", "parentId": "C", "son": null}, {"id": "52", "label": "道路运输业", "parentId": "C", "son": null}, {"id": "53", "label": "城市公共交通业", "parentId": "C", "son": null}, {"id": "54", "label": "水上运输业", "parentId": "C", "son": null}, {"id": "55", "label": "航空运输业", "parentId": "C", "son": null}, {"id": "56", "label": "管道运输业", "parentId": "C", "son": null}, {"id": "57", "label": "装卸搬运和其他运输服务业", "parentId": "C", "son": null}, {"id": "58", "label": "仓储业", "parentId": "C", "son": null}, {"id": "59", "label": "邮政业", "parentId": "C", "son": null}, {"id": "60", "label": "电信和其他信息传输服务业", "parentId": "C", "son": null}, {"id": "61", "label": "计算机服务业", "parentId": "C", "son": null}, {"id": "62", "label": "软件业", "parentId": "C", "son": null}, {"id": "63", "label": "批发业", "parentId": "C", "son": null}, {"id": "641", "label": "互联网零售", "parentId": "C", "son": null}, {"id": "65", "label": "零售业", "parentId": "C", "son": null}, {"id": "66", "label": "住宿业", "parentId": "C", "son": null}, {"id": "67", "label": "餐饮业", "parentId": "C", "son": null}, {"id": "68", "label": "银行业", "parentId": "C", "son": null}, {"id": "69", "label": "证券业", "parentId": "C", "son": null}, {"id": "70", "label": "保险业", "parentId": "C", "son": null}, {"id": "71", "label": "其他金融活动", "parentId": "C", "son": null}, {"id": "72", "label": "房地产业", "parentId": "C", "son": null}, {"id": "73", "label": "租赁业", "parentId": "C", "son": null}, {"id": "74", "label": "商务服务业", "parentId": "C", "son": null}, {"id": "75", "label": "研究与试验发展", "parentId": "C", "son": null}, {"id": "76", "label": "专业技术服务业", "parentId": "C", "son": null}, {"id": "77", "label": "科技交流和推广服务业", "parentId": "C", "son": null}, {"id": "78", "label": "地质勘查业", "parentId": "C", "son": null}, {"id": "79", "label": "水利管理业", "parentId": "C", "son": null}, {"id": "80", "label": "环境管理业", "parentId": "D", "son": null}, {"id": "81", "label": "公共设施管理业", "parentId": "D", "son": null}, {"id": "82", "label": "居民服务业", "parentId": "D", "son": null}, {"id": "83", "label": "其他服务业", "parentId": "E", "son": null}, {"id": "84", "label": "教育", "parentId": "E", "son": null}, {"id": "85", "label": "卫生", "parentId": "E", "son": null}, {"id": "86", "label": "社会保障业", "parentId": "E", "son": null}, {"id": "87", "label": "社会福利业", "parentId": "F", "son": null}, {"id": "88", "label": "新闻出版业", "parentId": "F", "son": null}, {"id": "89", "label": "广播、电视、电影和音像业", "parentId": "F", "son": null}, {"id": "90", "label": "文化艺术业", "parentId": "F", "son": null}, {"id": "91", "label": "体育", "parentId": "F", "son": null}, {"id": "92", "label": "娱乐业", "parentId": "F", "son": null}, {"id": "93", "label": "中国共产党机关", "parentId": "F", "son": null}, {"id": "94", "label": "国家机构", "parentId": "F", "son": null}, {"id": "95", "label": "人民政协和民主党派", "parentId": "F", "son": null}, {"id": "96", "label": "群众团体、社会团体和宗教组织", "parentId": "G", "son": null}, {"id": "97", "label": "基层群众自治组织", "parentId": "G", "son": null}, {"id": "98", "label": "国际组织", "parentId": "G", "son": null}, {"id": "0", "label": "未确定", "parentId": "H", "son": null}, {"id": "U001", "label": "综合管理机构", "parentId": "H", "son": null}, {"id": "U002", "label": "政治工作机构", "parentId": "I", "son": null}, {"id": "U003", "label": "动员工作机构", "parentId": "I", "son": null}, {"id": "U004", "label": "政法机构", "parentId": "J", "son": null}, {"id": "U005", "label": "直属院校", "parentId": "J", "son": null}, {"id": "U006", "label": "ZQ", "parentId": "J", "son": null}, {"id": "U007", "label": "LJ", "parentId": "J", "son": null}, {"id": "U008", "label": "HJ", "parentId": "K", "son": null}, {"id": "U009", "label": "KJ", "parentId": "L", "son": null}, {"id": "U010", "label": "HJJ", "parentId": "L", "son": null}, {"id": "U011", "label": "ZZ", "parentId": "M", "son": null}, {"id": "U012", "label": "LQ", "parentId": "M", "son": null}, {"id": "U013", "label": "WJ", "parentId": "M", "son": null}, {"id": "U014", "label": "融合事务管理机构", "parentId": "M", "son": null}, {"id": "U015", "label": "退役事务管理机构", "parentId": "N", "son": null}, {"id": "U016", "label": "融合企业", "parentId": "N", "son": null}, {"id": "99", "label": "交通运输", "parentId": "U", "son": null}, {"id": "697", "label": "餐饮配送服务", "parentId": "U", "son": null}, {"id": "725", "label": "房地产中介服务", "parentId": "U", "son": null}, {"id": "P001", "label": "国防院校", "parentId": "U", "son": null}, {"id": "Q003", "label": "国防医院", "parentId": "U", "son": null}], "1089": [{"id": "OragCodeLicence", "label": "组织机构代码证", "parentId": null, "son": null}, {"id": "ArmyCodeLicence", "label": "军队代码", "parentId": null, "son": null}, {"id": "SocialCreditLicence", "label": "统一社会信用代码证书", "parentId": null, "son": null}, {"id": "NoEntreperOrgLicence", "label": "民办非企业单位登记证书", "parentId": null, "son": null}, {"id": "FoundatLegalLicence", "label": "基金会法人登记证书", "parentId": null, "son": null}, {"id": "LawOfficeLicence", "label": "律师事务所执业许可证", "parentId": null, "son": null}, {"id": "SolventServeLicence", "label": "有偿服务许可证", "parentId": null, "son": null}, {"id": "ReligiousActivLicence", "label": "宗教活动场所登记证", "parentId": null, "son": null}, {"id": "PePhoLicence", "label": "个人有效证件+店铺门头照", "parentId": null, "son": null}, {"id": "BussLicence", "label": "营业执照", "parentId": null, "son": null}, {"id": "CorpLicence", "label": "事业单位登记证", "parentId": null, "son": null}, {"id": "OrgaLicence", "label": "社团法人登记证", "parentId": null, "son": null}], "1100": [{"id": "MemberSort0300", "label": "集团主联系人", "parentId": null, "son": null}, {"id": "MemberSort0301", "label": "业务和信息化联系人", "parentId": null, "son": null}, {"id": "MemberSort0303", "label": "主管通信与信息化领导", "parentId": null, "son": null}, {"id": "MemberSort0304", "label": "办公室领导", "parentId": null, "son": null}, {"id": "MemberSort0305", "label": "通信部门领导", "parentId": null, "son": null}, {"id": "MemberSort0306", "label": "信息化部门领导", "parentId": null, "son": null}, {"id": "MemberSort0307", "label": "其他决策人", "parentId": null, "son": null}, {"id": "MemberSort0308", "label": "详单查询联系人", "parentId": null, "son": null}, {"id": "MemberSort0302", "label": "集团高层领导", "parentId": null, "son": null}, {"id": "MemberSort0309", "label": "电子发票接收人", "parentId": null, "son": null}]}}, "h5qrychildGridList": {"retCode": "0", "retMsg": "success", "data": [{"id": "区域1", "label": "名称1", "areaType": "99", "region": "14"}, {"id": "区域2", "label": "名称2", "areaType": "99", "region": "14"}, {"id": "区域3", "label": "名称3", "areaType": "99", "region": "14"}]}, "h5IdentificOrc": {"retCode": "0", "retMsg": "success", "data": {"ecInfo": {"uniscId": "123456789963", "name": "XXX单位", "address": "南京市鼓楼区清凉门大街中海大厦15A", "legalPersonName": "张三"}, "peInfo": {"name": "李四", "idCardNumber": "321111196511053452", "address": "江苏省南京市雨润大街"}, "rspcode": "1", "rspdesc": null}}, "h5createGroupCust": {"retCode": "0", "retMsg": "success", "data": {"rspcode": "1", "rspdesc": "描述", "custid": "集团客户编号", "custCode": "集团custCode"}}, "h5GroupArchivesupLoadImg": {"retCode": "0", "retMsg": "success", "data": "ALD_xxxx_xxxxx.jpg"}, "h5QryBalance": {"retCode": "0", "retMsg": "", "data": {"balance": "9000", "orgId": "123456"}}, "h5Recharge": {"retCode": "0", "retMsg": "", "data": null}, "h5promotionListRecommend": {"data": [{"actPlanName": "1", "promotionId": "9100000029034", "promotionName": "移动看家7天时间云存储优惠"}, {"actPlanName": "商业计划书12", "promotionId": "2021010500333", "promotionName": "20版套餐优惠三期（399档)"}], "retCode": "0"}, "h5promBatchCheck": {"retCode": "0", "retMsg": "success"}, "h5queryPromotionDetail": {"retCode": "0", "retMsg": "Success", "data": {"promotionId": "*************", "promotionName": "安防服务月包账户支持支持立即", "promotionDescription": "", "effDate": "**************", "expDate": "**************", "validityType": "1", "duration": "12", "assignEndDate": null, "extendIonInfo": [{"extendIonId": "ARPU", "extendIonValue": "2626"}, {"extendIonId": "accountBalancePay", "extendIonValue": "1"}, {"extendIonId": "isSupportFollow", "extendIonValue": "1"}], "actPlanId": "********", "actPlanName": "奥利给05959", "adaptationList": [{"adaptationId": "*************", "adaptationName": "智能组网安防类业务包", "adaptationType": "O", "offerMinNumber": "1", "offerMaxNumber": "1", "followPromEffect": "否", "followPromExpire": "是", "offerPackId": "**********", "promOfferList": [{"offeringId": "**********", "offeringName": "室外安防15元"}]}, {"adaptationId": "*************", "adaptationName": "业务可选包", "adaptationType": "O", "offerMinNumber": "", "offerMaxNumber": "", "followPromEffect": "是", "followPromExpire": "是", "offerPackId": null, "promOfferList": [{"offeringId": "**********", "offeringName": "组网主体产品测试"}, {"offeringId": "**********", "offeringName": "5G智享套餐（融合版）199元"}, {"offeringId": "**********", "offeringName": "通用流量包20元"}]}, {"adaptationId": "*************", "adaptationName": "业务可选包-新1", "adaptationType": "O", "offerMinNumber": "", "offerMaxNumber": "", "followPromEffect": "是", "followPromExpire": "否", "offerPackId": null, "promOfferList": [{"offeringId": "2400000434", "offeringName": "组网已开服务测试（积分商城）"}, {"offeringId": "2000003779", "offeringName": "通用流量包50元"}, {"offeringId": "2000009094", "offeringName": "通用流量包50元（2017版）"}, {"offeringId": "2000011520", "offeringName": "通用流量包50元（2019版）"}, {"offeringId": "2000009095", "offeringName": "通用流量包70元（2017版）"}, {"offeringId": "2200005006", "offeringName": "彩铃"}, {"offeringId": "**********", "offeringName": "短信呼(1元)"}, {"offeringId": "2000007200", "offeringName": "夜间专用流量包5元"}, {"offeringId": "2000007273", "offeringName": "夜间专用流量包10元"}, {"offeringId": "2000011971", "offeringName": "夜间专用流量包12G"}, {"offeringId": "2000009873", "offeringName": "夜间专用流量包5G"}]}, {"adaptationId": "11111111111", "adaptationName": "打包测试-新1", "adaptationType": "O", "offerMinNumber": "", "offerMaxNumber": "", "followPromEffect": "是", "followPromExpire": "否", "offerPackId": null, "promOfferList": [{"offeringId": "2000009210", "offeringName": "组网必选生效方式测试"}, {"offeringId": "20000037793", "offeringName": "打包测试1"}, {"offeringId": "20000090944", "offeringName": "打包测试（2017版）"}, {"offeringId": "20000115205", "offeringName": "打包测试（2019版）"}, {"offeringId": "20000090951", "offeringName": "打包测试（2017版）"}, {"offeringId": "22000050061", "offeringName": "打包测试彩铃"}, {"offeringId": "22000050020", "offeringName": "打包测试短信呼(1元)"}, {"offeringId": "20000072200", "offeringName": "打包测试5元"}, {"offeringId": "20000072273", "offeringName": "打包测试10元"}, {"offeringId": "20000131971", "offeringName": "打包测试12G"}, {"offeringId": "20000049873", "offeringName": "打包测试5G"}]}, {"adaptationId": "29385925828", "adaptationName": "终端促销", "adaptationType": "G", "offerMinNumber": "", "offerMaxNumber": "", "followPromEffect": "是", "followPromExpire": "否", "offerPackId": null, "promOfferList": [{"offeringId": "2390180030", "offeringName": "通用流量包50元（积分商城）"}, {"offeringId": "2000003779", "offeringName": "通用流量包50元"}, {"offeringId": "2000009094", "offeringName": "通用流量包50元（2017版）"}, {"offeringId": "2000011520", "offeringName": "通用流量包50元（2019版）"}, {"offeringId": "2000009095", "offeringName": "通用流量包70元（2017版）"}, {"offeringId": "2200005006", "offeringName": "彩铃"}, {"offeringId": "**********", "offeringName": "短信呼(1元)"}, {"offeringId": "2000007200", "offeringName": "夜间专用流量包5元"}, {"offeringId": "2000007273", "offeringName": "夜间专用流量包10元"}, {"offeringId": "2000011971", "offeringName": "夜间专用流量包12G"}, {"offeringId": "2000009873", "offeringName": "夜间专用流量包5G"}]}, {"adaptationId": "94589848949089485", "adaptationName": "终端促销44232", "adaptationType": "G", "offerMinNumber": "", "offerMaxNumber": "", "followPromEffect": "是", "followPromExpire": "否", "offerPackId": null, "promOfferList": null}], "effectType": "1,3", "promotionType": "113", "adaptationOtherList": []}}, "h5qryPromFee": {"retCode": "0", "retMsg": "Success", "data": [{"chargeCode": "CashPay", "chargeMoney": "1000", "chargeName": "现金充值"}, {"chargeCode": "SpecialPay", "chargeMoney": "2000", "chargeName": "专有帐户预存"}]}, "h5queryPromotionList": {"retCode": "0", "retMsg": "Success", "data": {"pageInfo": {"beginRowNumber": "0", "totalRecord": "33", "curPage": "0", "recordPerPage": "10"}, "promotionList": [{"beId": "99", "promotionId": "9100000279005", "promotionName": "安防服务月包", "promotionDescription": "", "effDate": "20210513145646", "expDate": "21130514145652", "actPlanId": "11111", "actPlanName": "22222"}, {"beId": "99", "promotionId": "9100000021001", "promotionName": "王建霞自动化专用003", "promotionDescription": "", "effDate": "********080249", "expDate": "**************", "actPlanId": "2000250-202001", "actPlanName": "无限制（允许变更）--0429商业计划书"}, {"beId": "99", "promotionId": "9100000021002", "promotionName": "最低消费享优惠85元档(12个月)", "promotionDescription": "", "effDate": "********080249", "expDate": "**************", "actPlanId": "2000250-202002", "actPlanName": "2020版南京最低消费享优惠活动"}, {"beId": "99", "promotionId": "9100000021003", "promotionName": "最低消费享优惠50元档(12个月)", "promotionDescription": "", "effDate": "********080249", "expDate": "**************", "actPlanId": "2000250-202003", "actPlanName": "2020版南京最低消费享优惠活动"}, {"beId": "99", "promotionId": "9100000021004", "promotionName": "集团优惠新40（90档）-12月", "promotionDescription": "", "effDate": "********080249", "expDate": "**************", "actPlanId": "2000250-202004", "actPlanName": "2020年南京集团套餐优惠活动新"}, {"beId": "99", "promotionId": "*************", "promotionName": "集团优惠新60（90档）-24月", "promotionDescription": "", "effDate": "********080249", "expDate": "**************", "actPlanId": "2000250-202005", "actPlanName": "2020年南京集团套餐优惠活动新"}]}}, "h5qryBldInfo": {"retCode": "0", "retMsg": "", "data": {"busiInfo": [{"total": "0", "money": null}], "yuEInfo": {"bananceInfo": [{"acc_no": "现金预存虚拟账号", "balance_type": "1", "bank_type": "99", "autotrans_flag": "0", "curr_amount": "0", "limit_amount": "1000"}, {"acc_no": "现金预存虚拟账号", "balance_type": "2", "bank_type": "99", "autotrans_flag": "0", "curr_amount": "90000", "limit_amount": "1000"}, {"acc_no": "现金预存虚拟账号", "balance_type": "3", "bank_type": "99", "autotrans_flag": "0", "curr_amount": "0", "limit_amount": "1000"}], "outSrl": "****************"}}}, "h5BusinessDetail": {"retCode": "0", "retMsg": "Success", "data": [{"stype": "2", "busiOppId": "*************", "objectId": "10001", "objectName": "水城一街区101", "infoData": [{"infoName": "宽带运营商", "infoValue": "联通"}, {"infoName": "宽带到期时间", "infoValue": "***********"}]}, {"stype": "3", "busiOppId": "*************", "objectId": "10002", "objectName": "水城一街区102", "infoData": [{"infoName": "宽带到期时间", "infoValue": "***********"}]}]}, "h5BusinessCollect": {"retCode": "0", "retMsg": "Success", "data": {"streamSeq": "*****************", "personTotalNum": "10", "groupTotalNum": "10", "groupYwNum": "10", "groupZdNum": "10", "groupGqNum": "10", "groupJjNum": "10", "familyTotalNum": "10", "familyYwNum": "10", "familyBwNum": "10", "familyGqNum": "10", "familyJjNum": "10", "familyZwNum": "10", "familyYqdNum": "10", "groupBwNum": "10"}}, "h5FamilyBusinessDetail": {"retCode": "0", "retMsg": null, "data": {"streamSeq": "*****************", "addressName": "水城一街区", "infoData": [{"infoName": "宽带运营商", "infoValue": "联通"}, {"infoName": "宽带账号", "infoValue": "***********"}, {"infoName": "宽带带宽", "infoValue": "100M"}, {"infoName": "宽带到期时间", "infoValue": "2020-07-01"}, {"infoName": "装维人员信息", "infoValue": "王涛***********"}], "stype": "2"}}, "h5FamilyBusinessSubmit": {"retCode": "0", "retMsg": "成功", "data": null}, "h5QryOrganizationRelation": {"retCode": "0", "retMsg": null, "data": {"resultCode": "0", "resultDesc": "success", "gridId": "88880001", "gridName": "xx网格", "gridManager": [{"operId": "11283", "operName": "张姝", "severNumber": "***********"}, {"operId": "11283", "operName": "张姝", "severNumber": "***********"}], "custManager": [{"custoperId": "", "custoperName": "张姝", "custseverNumber": "123123123"}, {"custoperId": "", "custoperName": "张姝", "custseverNumber": "123123123"}], "installMan": [], "channel": [{"chanId": "12455656", "chanName": "xxxx营业厅766", "channelList": []}, {"chanId": "332333", "chanName": "xxxx营业厅22", "channelList": [{"chanoperId": "1000003", "chanoperName": "张姝33", "chanoperNumber": "***********"}, {"chanoperId": "100005", "chanoperName": "张姝4", "chanoperNumber": "***********"}]}]}}, "h5OrganizationRelationSubmit": {"retCode": "0", "retMsg": "success", "data": null}, "h5GetSecondConfirmList": {"retCode": "0", "retMsg": "Success", "data": {"confirmList": [{"userId": "1112233344444", "msisdn": "***********", "age": 20, "sex": 1, "connectTime": "2019/7/26 14:40:26", "activeId": "10000000345", "activeName": "宽带续费", "stepId": "100003994jjf9nfnvv "}, {"userId": "1112233344444", "msisdn": "***********", "age": 20, "sex": 1, "connectTime": "2019/9/26 14:40:26", "activeId": "10000002345 ", "activeName": "宽带续费 ", "stepId": "100003994jjf9nfnvv "}]}}, "h5qryMrktFee": {"retCode": "0", "retMsg": "【统一算费接口-FSOP2BDS1115-2020081210320648】Success", "data": {"orderitem": [{"itemid": "151000000000000016", "itemname": "", "totalamount": "1000", "itempriceinfo": [{"chargecode": "DeptPay", "chargename": "设备押金费用项设备押金费用项设备押金费用项", "chargetype": "O", "chargetypename": "名称", "priceamount": "1000"}]}, {"itemid": "2013000001", "itemname": "", "totalamount": "2000", "itempriceinfo": [{"chargecode": "3206", "chargename": "宽带接入费", "chargetype": "Z", "chargetypename": "名称", "priceamount": "2000"}]}, {"itemid": "3002101748", "itemname": "", "totalamount": "1900", "itempriceinfo": [{"chargecode": "3206", "chargename": "宽带接入费", "chargetype": "Z", "chargetypename": "名称", "priceamount": "1900"}]}], "isdevicefee": "0", "payamount": "1000000"}}, "h5mrktCaseCheck": {"retCode": "0", "retMsg": null, "data": {"subscribeinfo": [{"offerid": "2400000431", "offername": "多终端共享服务1"}, {"offerid": "2400000431", "offername": "多终端共享服务2"}, {"offerid": "2400000431", "offername": "多终端共享服务3"}, {"offerid": "2400000431", "offername": "多终端共享服务4"}, {"offerid": "2400000431", "offername": "多终端共享服务"}, {"offerid": "2400000434", "offername": "副卡共享功能"}], "unsubscribeinfo": [{"offerid": "2400000433", "offername": "家庭短号呼叫", "instanceid": "00001", "isbundled": "N", "bundleinstid": ""}, {"offerid": "2400000437", "offername": "测试失效产品", "instanceid": "00002", "isbundled": "N", "bundleinstid": ""}]}}, "h5mrktCaseCommit": {"retCode": "0", "retMsg": "Success", "data": {"orderId": "11111111111111111111111111"}}, "h5QryHomeworkTempList": {"retCode": "0", "retMsg": "返回成功", "data": {"tempList": [{"tempId": " T00001", "tempName": "考核反馈模板111", "titleList": [{"titleId": "S00001", "titleName": "是否达标", "putType": "1", "tempValue": "", "tempKey": ""}, {"titleId": "S00002", "titleName": "是否达标", "putType": "2", "tempValue": "I001|I002|1003|1004", "tempKey": "A|B|C|D"}, {"titleId": "S00003", "titleName": "是否达标", "putType": "3", "tempValue": "I001|I002", "tempKey": "是|否"}, {"titleId": "S00004", "titleName": "请打卡", "putType": "6", "tempValue": "", "tempKey": ""}, {"titleId": "S00005", "titleName": "请拍照", "putType": "7", "tempValue": "", "tempKey": ""}], "tempType": "1"}, {"tempId": " T00002", "tempName": "考核反馈模板222", "titleList": [{"titleId": "S00001", "titleName": "是否达标", "putType": "1", "tempValue": "", "tempKey": ""}, {"titleId": "S00002", "titleName": "是否达标", "putType": "2", "tempValue": "I001|I002|1003|1004", "tempKey": "A|B|C|D"}, {"titleId": "S00003", "titleName": "是否达标", "putType": "3", "tempValue": "I001|I002", "tempKey": "是|否"}], "tempType": "2"}, {"tempId": " T00003", "tempName": "考核反馈模板333", "titleList": [{"titleId": "S00001", "titleName": "是否达标", "putType": "1", "tempValue": "", "tempKey": ""}, {"titleId": "S00002", "titleName": "是否达标1111111", "putType": "2", "tempValue": "I001|I002|1003|1004", "tempKey": "A|B|C|D"}, {"titleId": "S00003", "titleName": "是否达标", "putType": "3", "tempValue": "I001|I002", "tempKey": "是|否"}], "tempType": "2"}]}}, "h5NewHomeworkSubmit": {"retCode": "0", "retMsg": "成功", "data": null}, "h5QrySummaryInfo": {"retCode": "0", "retMsg": null, "data": {"interfinishNoday": "30", "interfinishnomon": "20", "interFinishnoover7": "565", "interFinishnoover30": "33", "tvfinishNoday": "44", "tvfinishnomon": "545", "tvFinishnoover7": "44", "tvFinishnoover30": "45", "faultreachday": "56", "faultnodealday": "567", "faultrepeatday": "545", "faultreachweek": "54", "faultnodealweek": "56", "faultrepeatweek": "56", "faultreachmon": "58", "faultnodealmon": "54", "faultrepeatmon": "58", "faultreachyear": "44", "faultnodealyear": "33", "faultrepeatyear": "233"}}, "h5QryZhuangWeiOperInfo": {"retCode": "0", "retMsg": null, "data": [{"classId": "2", "installType": "宽带", "interTV": "是", "insertTime": "20200706122312", "finishnocnt": "22", "orgname": "建邺区区域中心", "interAcc": "1389999999", "name": "张二三", "msisdn": "13951735555", "operatorName": "李四", "operatorCntno": "33", "productType": "仅宽带", "applyDate": "20200705122312", "channelCode": "洪泽共和带店加盟店", "finishDay": "20200705122312", "remarkCrm": "联系人：冒联系电话：151526919999安装时间：7.2上午", "safeguardordDate": "20200705122312", "interName": "阳光壹***", "addressName": "泰州地区泰州市区海陵***", "reportTime": "", "reportSource": "xxx", "finishTime": "20200705122312", "reasonDesc": "用户宽带账号xxxxxxx", "totalNum": "22", "operatorMsisdn": "15823121232"}, {"classId": "3", "installType": "宽带", "interTV": "是", "insertTime": "20200706122312", "finishnocnt": "22", "orgname": "建邺区区域中心", "interAcc": "1389999999", "name": "张三", "msisdn": "13951735555", "operatorName": "李四五", "operatorCntno": "33", "productType": "仅宽带", "applyDate": "20200705122312", "channelCode": "洪泽共和带店加盟店", "finishDay": "20200705122312", "remarkCrm": "联系人：冒联系电话：151526919999安装时间：7.2上午", "safeguardordDate": "20200705122312", "interName": "阳光壹***", "addressName": "泰州地区泰州市区海陵的一个小区的一个单元的一户***", "reportTime": "", "reportSource": "xxx", "finishTime": "20200705122312", "reasonDesc": "用户宽带账号xxxxxxx", "totalNum": "22", "operatorMsisdn": "15787465890"}]}, "h5QryAreak": {"retCode": "0", "retMsg": "Success", "data": [{"region": "2000250", "orgaid": "2000250", "organame": "全省", "parentid": null, "orderId": "99", "orgalevel": "0", "isLeaf": "0", "treeType": "1", "orgatype": null}]}, "h5QryScoreAndRank": {"retCode": "0", "retMsg": "Success", "data": {"orgId": "2000250", "orgaName": "江苏省", "orgaScore": "90.8", "orgaRank": "5", "isRightCopy": "0"}}, "h5qryExamState": {"retCode": "0", "retMsg": "success", "data": {"state": "1", "examId": "20200830"}}, "h5qryExamList": {"retCode": "0", "retMsg": null, "data": {"examList": [{"examId": "1002", "startTime": "2020-08-17 00:00:00", "endTime": "2022-09-01 00:00:00", "examName": "看看你了解谁", "state": "4", "knowledgeId": "99202003091005490014", "score": "100"}, {"examId": "1001", "startTime": "2020-08-01 00:00:00", "endTime": "2022-08-01 00:00:00", "examName": "如何用脚打字", "state": "1", "knowledgeId": "1001", "score": null}, {"examId": "1004", "startTime": "2021-08-01 00:00:00", "endTime": "2020-08-31 00:00:00", "examName": "如何不用脑思考", "state": "2", "knowledgeId": "1004", "score": null}, {"examId": "1003", "startTime": "2020-08-29 00:00:00", "endTime": "2020-08-30 00:00:00", "examName": "如何用耳朵吃饭", "state": "3", "knowledgeId": "1003", "score": null}, {"examId": "tes10000011", "startTime": "2020-08-28 14:51:00", "endTime": "2020-08-31 14:51:00", "examName": "如何用手说话", "state": "3", "knowledgeId": "10001，10002，10003，10004，10005，10006", "score": "80"}]}}, "h5qryExamDetail": {"retCode": "0", "retMsg": "success", "data": {"examId": "1002", "examName": "看看你了解谁", "totalScore": "100", "singleScore": 20, "multipeScore": 40, "judgeScore": 20, "totalNum": 4, "singleNum": 2, "multipleNum": 1, "judgeNum": 1, "questions": [{"examId": "1002", "questionId": "1", "questionType": "1", "questionSubject": "天青色等烟雨", "questionAnswer": null, "createTime": null, "operateTime": null, "score": 20, "items": [{"examId": null, "questionId": null, "itemId": "11", "itemNo": "A", "itemContent": "而我在等你", "createTime": null, "operateTime": null}, {"examId": null, "questionId": null, "itemId": "12", "itemNo": "B", "itemContent": "雨停了", "createTime": null, "operateTime": null}, {"examId": null, "questionId": null, "itemId": "13", "itemNo": "C", "itemContent": "听妈妈的话", "createTime": null, "operateTime": null}, {"examId": null, "questionId": null, "itemId": "14", "itemNo": "D", "itemContent": "给我的爱人来一杯莫吉托", "createTime": null, "operateTime": null}]}, {"examId": "1002", "questionId": "2", "questionType": "1", "questionSubject": "炊烟袅袅升起", "questionAnswer": null, "createTime": null, "operateTime": null, "score": 20, "items": [{"examId": null, "questionId": null, "itemId": "15", "itemNo": "A", "itemContent": "嚯嚯嚯嚯", "createTime": null, "operateTime": null}, {"examId": null, "questionId": null, "itemId": "16", "itemNo": "B", "itemContent": "隔江千万里", "createTime": null, "operateTime": null}, {"examId": null, "questionId": null, "itemId": "17", "itemNo": "C", "itemContent": "给我一首歌的时间", "createTime": null, "operateTime": null}, {"examId": null, "questionId": null, "itemId": "18", "itemNo": "D", "itemContent": "我就是力量的化身", "createTime": null, "operateTime": null}]}, {"examId": "1002", "questionId": "3", "questionType": "2", "questionSubject": "周杰伦的作品", "questionAnswer": null, "createTime": null, "operateTime": null, "score": 40, "items": [{"examId": null, "questionId": null, "itemId": "19", "itemNo": "A", "itemContent": "有一种悲伤", "createTime": null, "operateTime": null}, {"examId": null, "questionId": null, "itemId": "20", "itemNo": "B", "itemContent": "绿光", "createTime": null, "operateTime": null}, {"examId": null, "questionId": null, "itemId": "21", "itemNo": "C", "itemContent": "枫", "createTime": null, "operateTime": null}, {"examId": null, "questionId": null, "itemId": "22", "itemNo": "D", "itemContent": "搁浅", "createTime": null, "operateTime": null}]}, {"examId": "1002", "questionId": "4", "questionType": "3", "questionSubject": "周杰伦唱歌好听", "questionAnswer": null, "createTime": null, "operateTime": null, "score": 20, "items": []}]}}, "h5examCommit": {"retCode": "0", "retMsg": "success", "data": {"totalScore": "100"}}, "h5qryKnowledgeInfoById": {"retCode": "0", "retMsg": null, "data": {"knowledgeId": "222222222", "releaser": null, "releaseTime": "2020-02-24 15:00:01", "releaseTelnum": "***********", "releaseName": "王涛", "title": "南京地市优秀案例分享1", "content": "<p style=\"text-indent: 2em; line-height: 2em;\">\n    <span style=\"font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;\">2月19日0—24时，31个省（自治区、直辖市）和新疆生产建设兵团报告新增<strong>确诊病例394例</strong>，新增<strong>死亡病例114例</strong>（湖北108例，河北、上海、福建、山东、云南、陕西各1例），新增<strong>疑似病例1277例</strong>。</span>\n</p>\n<p style=\"text-indent: 2em; line-height: 2em;\">\n    <span style=\"font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;\">当日新增治愈出院病例1779例，解除医学观察的密切接触者25318人，重症病例减少113例。</span>\n</p>\n<p style=\"text-align:center\">\n    <span style=\"font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;\"><img src=\"http://221.178.251.46:8080/cmopApp/servlet/cmopDownloadAttachment?attachmentId=ac7d9ec3-ee8c-41ef-8617-97083fce6d03\" width=\"350\" height=\"120\"/></span>\n</p><p style=\"text-indent: 2em; line-height: 2em;\">\n    <span style=\"font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;\">截至2月19日24时，据31个省（自治区、直辖市）和新疆生产建设兵团报告，现有确诊病例56303例（其中重症病例11864例），累计治愈出院病例16155例，累计死亡病例2118例，累计报告确诊病例74576例（江西、河南、云南省各核减1例），现有疑似病例4922例。累计追踪到密切接触者589163人，尚在医学观察的密切接触者126363人。</span>\n</p>\n<p style=\"text-indent: 2em; line-height: 2em;\">\n    <span style=\"font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;\">湖北新增确诊病例349例（其中：武汉新增615例，仙桃等4市新增13例，荆门、咸宁等10市州对确诊病例中来源于原“临床诊断病例”者进行核酸检测，通过综合分析，将核酸检测结果为阴性的病例从确诊病例中核减，共订正核减279例），新增治愈出院病例1209例（武汉553例），新增死亡病例108例（武汉88例），现有确诊病例49665例（武汉37994例），其中重症病例11178例（武汉9689例）。累计治愈出院病例10337例（武汉5448例），累计死亡病例2029例（武汉1585例），累计确诊病例62031例（武汉45027例）。新增疑似病例880例（武汉483例），现有疑似病例3456例（武汉1940例）。</span>\n</p>\n<p style=\"text-indent: 2em; line-height: 2em;\">\n    <span style=\"font-family: 微软雅黑, &quot;Microsoft YaHei&quot;; font-size: 14px;\">累计收到港澳台地区通报确诊病例99例：香港特别行政区65例（出院5例，死亡2例），澳门特别行政区10例（出院6例），台湾地区24例(出院2例，死亡1例)。</span>\n</p>https://v.qq.com/x/page/u3059nv8tv7.html", "readNumber": "8", "likeNumber": "0", "typeCode": "100001", "fileName": "附件1,附件2", "fileUrl": "http://211.138.21.13:8090/cmop/,http://211.138.21.13:8090/cmop/", "showRegion": "14", "likeFlag": null}}, "h5GetPancOrderList": {"retCode": "0", "retMsg": null, "data": [{"privId": "100061", "busiTypeName": "主体产品变更", "targetNumber": "15557350068", "registerId": "***********", "registerTime": "2020/09/04", "busiType": "credit_loan", "state": "0", "registerName": "张恒", "remark": "测试"}, {"privId": "100042", "busiTypeName": "携号转网", "targetNumber": "15557350068", "registerId": "***********", "registerTime": "2020/09/04", "busiType": "credit_loan", "state": "0", "registerName": "张恒", "remark": "测试"}]}, "h5SearchMenuByName": {"retCode": "0", "retMsg": null, "data": [{"funcId": "997", "funcName": "超值版一网通", "opId": "jkddzx", "opParentid": "jkddzx", "levelId": 3, "picPath": "999", "hasCrmid": "0", "isHot": null, "status": null, "authenType": 0, "hasPwd": "1", "isNewFeature": 0, "featureType": null, "flag": null, "flagStatus": null, "remark": null, "location": null, "paths": ["业务/基础业务"]}, {"funcId": "100013", "funcName": "互联网电视", "opId": "fsop", "opParentid": "fsop", "levelId": 3, "picPath": "10004", "hasCrmid": "0", "isHot": null, "status": null, "authenType": 1, "hasPwd": "0", "isNewFeature": 0, "featureType": "fea_nettv", "flag": null, "flagStatus": null, "remark": null, "location": null, "paths": ["业务/宽带专区", "客户视图/有线业务"]}, {"funcId": "20201", "funcName": "家庭网开通", "opId": "ji<PERSON><PERSON>wang", "opParentid": "fsop", "levelId": 3, "picPath": "20201", "hasCrmid": "1", "isHot": null, "status": null, "authenType": 0, "hasPwd": "1", "isNewFeature": 0, "featureType": null, "flag": null, "flagStatus": null, "remark": null, "location": null, "paths": ["业务/家庭专区", "客户视图/家庭专区"]}, {"funcId": "100036", "funcName": "家庭网开通(新)", "opId": "ji<PERSON><PERSON>wang", "opParentid": "fsop", "levelId": 3, "picPath": "20201", "hasCrmid": "1", "isHot": null, "status": null, "authenType": 0, "hasPwd": "0", "isNewFeature": 0, "featureType": "fea_fnkt", "flag": null, "flagStatus": null, "remark": null, "location": null, "paths": ["业务/家庭专区", "客户视图/家庭专区"]}, {"funcId": "100039", "funcName": "家庭网退出(新)", "opId": "home<PERSON>ich<PERSON>", "opParentid": "fsop", "levelId": 3, "picPath": "100039", "hasCrmid": "0", "isHot": null, "status": null, "authenType": 0, "hasPwd": "1", "isNewFeature": 1, "featureType": "fea_fntc", "flag": null, "flagStatus": null, "remark": null, "location": null, "paths": ["客户视图/家庭专区"]}, {"funcId": "100038", "funcName": "家庭网退订(新)", "opId": "hometuiding", "opParentid": "fsop", "levelId": 3, "picPath": "100038", "hasCrmid": "0", "isHot": null, "status": null, "authenType": 0, "hasPwd": "1", "isNewFeature": 1, "featureType": "fea_fntd", "flag": null, "flagStatus": null, "remark": null, "location": null, "paths": ["客户视图/家庭专区"]}, {"funcId": "100007", "funcName": "家庭网维护", "opId": "vwang", "opParentid": "fsop", "levelId": 3, "picPath": "100007", "hasCrmid": "0", "isHot": null, "status": null, "authenType": 0, "hasPwd": "1", "isNewFeature": 0, "featureType": null, "flag": null, "flagStatus": null, "remark": null, "location": null, "paths": ["客户视图/家庭专区"]}, {"funcId": "100037", "funcName": "家庭网维护(新)", "opId": "vwang", "opParentid": "fsop", "levelId": 3, "picPath": "100007", "hasCrmid": "0", "isHot": null, "status": null, "authenType": 0, "hasPwd": "1", "isNewFeature": 0, "featureType": "fea_fnwh", "flag": null, "flagStatus": null, "remark": null, "location": null, "paths": ["客户视图/家庭专区"]}, {"funcId": "100089", "funcName": "家庭组网", "opId": "fsop", "opParentid": "fsop", "levelId": 3, "picPath": "100075", "hasCrmid": "0", "isHot": null, "status": null, "authenType": 0, "hasPwd": "0", "isNewFeature": 0, "featureType": null, "flag": "custom", "flagStatus": null, "remark": "家庭组网", "location": null, "paths": ["客户视图/有线业务"]}, {"funcId": "100016", "funcName": "亲情网开通", "opId": "fsop", "opParentid": "fsop", "levelId": 3, "picPath": "100016", "hasCrmid": "0", "isHot": null, "status": null, "authenType": 0, "hasPwd": "0", "isNewFeature": 0, "featureType": null, "flag": "custom", "flagStatus": null, "remark": "亲情网", "location": null, "paths": ["业务/家庭专区", "客户视图/家庭专区"]}, {"funcId": "100018", "funcName": "亲情网退出", "opId": "fsop", "opParentid": "fsop", "levelId": 3, "picPath": "100018", "hasCrmid": "0", "isHot": null, "status": null, "authenType": 0, "hasPwd": "0", "isNewFeature": 0, "featureType": null, "flag": "custom", "flagStatus": null, "remark": "亲情网", "location": null, "paths": ["客户视图/家庭专区"]}, {"funcId": "100017", "funcName": "亲情网维护", "opId": "fsop", "opParentid": "fsop", "levelId": 3, "picPath": "100017", "hasCrmid": "0", "isHot": null, "status": null, "authenType": 0, "hasPwd": "0", "isNewFeature": 0, "featureType": null, "flag": "custom", "flagStatus": null, "remark": "亲情网", "location": null, "paths": ["客户视图/家庭专区"]}, {"funcId": "100221", "funcName": "入网查询", "opId": "fsop", "opParentid": "fsop", "levelId": 3, "picPath": "100003", "hasCrmid": "0", "isHot": null, "status": null, "authenType": -1, "hasPwd": "0", "isNewFeature": 1, "featureType": "fea_certidnetnum", "flag": null, "flagStatus": null, "remark": null, "location": null, "paths": ["工具/辅助专区"]}, {"funcId": "100151", "funcName": "入网次数查询", "opId": "fsop", "opParentid": "fsop", "levelId": 3, "picPath": "100003", "hasCrmid": "0", "isHot": null, "status": null, "authenType": -1, "hasPwd": "0", "isNewFeature": 1, "featureType": "fea_enternetnum", "flag": null, "flagStatus": null, "remark": null, "location": null, "paths": ["工具/辅助专区"]}, {"funcId": "100022", "funcName": "网元同步", "opId": "fsoop", "opParentid": "fsoop", "levelId": 3, "picPath": "100022", "hasCrmid": "0", "isHot": null, "status": null, "authenType": -1, "hasPwd": "0", "isNewFeature": 0, "featureType": null, "flag": null, "flagStatus": null, "remark": null, "location": null, "paths": ["工具/辅助专区"]}, {"funcId": "100010", "funcName": "物联网卡激活", "opId": "fsop", "opParentid": "fsop", "levelId": 3, "picPath": "10019", "hasCrmid": "1", "isHot": null, "status": null, "authenType": -1, "hasPwd": "1", "isNewFeature": 0, "featureType": "fea_iot_jh", "flag": null, "flagStatus": null, "remark": null, "location": null, "paths": ["业务/物联网专区"]}, {"funcId": "100042", "funcName": "携号转网", "opId": "fsop", "opParentid": "fsop", "levelId": 3, "picPath": "100042", "hasCrmid": "0", "isHot": null, "status": null, "authenType": -1, "hasPwd": "1", "isNewFeature": 0, "featureType": "fea_transfer", "flag": null, "flagStatus": null, "remark": null, "location": null, "paths": ["业务/新入网专区"]}, {"funcId": "100026", "funcName": "选号入网", "opId": "fsop", "opParentid": "fsop", "levelId": 3, "picPath": "10028", "hasCrmid": "1", "isHot": null, "status": null, "authenType": -1, "hasPwd": "1", "isNewFeature": 0, "featureType": null, "flag": null, "flagStatus": null, "remark": null, "location": null, "paths": ["业务/新入网专区"]}, {"funcId": "10019", "funcName": "预配号入网", "opId": "fsop", "opParentid": "fsop", "levelId": 3, "picPath": "10019", "hasCrmid": "1", "isHot": null, "status": null, "authenType": -1, "hasPwd": "1", "isNewFeature": 0, "featureType": null, "flag": null, "flagStatus": null, "remark": null, "location": null, "paths": ["业务/新入网专区"]}, {"funcId": "100057", "funcName": "智能组网", "opId": "fsop", "opParentid": "fsop", "levelId": 3, "picPath": "100057", "hasCrmid": "0", "isHot": null, "status": null, "authenType": 1, "hasPwd": "0", "isNewFeature": 0, "featureType": null, "flag": "custom", "flagStatus": null, "remark": "智能组网", "location": null, "paths": ["客户视图/有线业务"]}, {"funcId": "100044", "funcName": "转网列表", "opId": "fsop", "opParentid": "fsop", "levelId": 3, "picPath": "100044", "hasCrmid": "0", "isHot": null, "status": null, "authenType": -1, "hasPwd": "1", "isNewFeature": 0, "featureType": null, "flag": null, "flagStatus": null, "remark": null, "location": null, "paths": ["业务/新入网专区"]}, {"funcId": "100227", "funcName": "组网一键办理", "opId": "fsop", "opParentid": "fsop", "levelId": 3, "picPath": "100227", "hasCrmid": "0", "isHot": null, "status": null, "authenType": 1, "hasPwd": "0", "isNewFeature": 1, "featureType": "fea_network", "flag": null, "flagStatus": null, "remark": null, "location": null, "paths": ["工具/辅助专区"]}]}, "h5getAladdinList": {"retCode": "0", "retMsg": "查询成功", "data": [{"taskId": "YYTBM1000001", "dispatchDate": "2020-08-20 15:30:20", "gridId": "10015695", "gridName": "xx中心网格", "businessHallId": "95642", "businessHallName": "xx路营业厅", "customPhone": "***********", "scoreTime": "2020-08-19 12:10:22", "dealId": "08654", "customAdvice": "移动营业厅空调能不能打低点啊", "wholeSatisfaction": "9", "environmentSatisfaction": "9", "waitSatisfaction": "9", "dealSatisfaction": "7", "serviceSatisfaction": "1", "skillSatisfaction": "8", "status": "1", "repairResult": "2", "unsatisfyReason": "2", "remark": "我也不知道备注些什么"}, {"taskId": "YYTBM1000002", "gridId": "10015695", "gridName": "xx中心网格", "businessHallId": "95543", "businessHallName": "xx大街营业厅", "customPhone": "***********", "scoreTime": "20200819", "dealId": "08654", "customAdvice": "移动营业厅空调能不能打低点啊，天气太热了", "wholeSatisfaction": "9", "environmentSatisfaction": "9", "waitSatisfaction": "9", "dealSatisfaction": "7", "serviceSatisfaction": "1", "skillSatisfaction": "8", "status": "1", "repairResult": "1", "unsatisfyReason": "2", "remark": "我也不知道备注些什么"}]}, "h5getSheetDictValue": {"retCode": "0", "retMsg": "成功", "data": [{"region": "14", "dictId": "1002", "dictValue": "短信内容：尊敬的客户您好！感谢您对我们工作的支持与理解，您可以点击xxxx领取2GB流量。【中国移动】", "remark": "短信"}, {"region": "14", "dictId": "1001", "dictValue": "您好，我是中国移动xx公司xxxx(营业厅名称)管理员，电话给您主要是为了做个营业厅服务方面的回访，请问现在方便吗？<br>1、客户表示不方便配合回访：结束语：真不好意思打扰到您了，稍后我会给您发送一条短信，您点击链接即可领取2G流量。祝您愉快！再见！<br>2、客户表示方便配合回访：非常感谢您对我们工作的支持，首先我代表我们营业厅向您表示歉意！结合客户低评项目，进一步询问客户不满原因；<br>", "remark": "开头语"}, {"region": "99", "dictId": "1003", "dictValue": "1", "dictName": "未解决问题", "remark": "不满意原因"}, {"region": "99", "dictId": "1003", "dictValue": "2", "dictName": "不认可补偿条件", "remark": "不满意原因"}, {"region": "99", "dictId": "1003", "dictValue": "3", "dictName": "对维护人员不满", "remark": "不满意原因"}, {"region": "99", "dictId": "1003", "dictValue": "4", "dictName": "其它", "remark": "不满意原因"}]}, "h5pseqRelieve": {"retCode": "0", "retMsg": "success", "data": {}}, "h5GetGroupEnterNet": {"retCode": "0", "retMsg": "success", "data": {}}, "h5countFuncClick": {"retCode": "0", "retMsg": null, "data": null}, "h5QryBusiRecommendList": {"retCode": "0", "retMsg": null, "data": {"resultCode": "0", "resultDesc": "success", "streamSeq": "*******************", "subObjectList": [{"subObjectId": "12222", "subObjectName": "威尼斯水城", "infoList": [{"infoName": "对象类型", "infoValue": "小区"}, {"infoName": "活动数", "infoValue": "3"}]}, {"subObjectId": "133332", "subObjectName": "江苏新大陆", "infoList": [{"infoName": "对象类型", "infoValue": "集团"}, {"infoName": "活动数", "infoValue": "3"}]}, {"subObjectId": "133332", "subObjectName": "江苏新大陆", "infoList": [{"infoName": "集团类型", "infoValue": "A"}]}]}}, "h5QryBusiRecommendDetail": {"retCode": "0", "retMsg": null, "data": {"resultCode": "0", "resultDesc": "success", "streamSeq": "*******************", "workId": "ALD123123", "objectId": "10202020", "objectName": "新大陆", "infoList": [{"infoName": "经度", "infoValue": "118.723362"}, {"infoName": "纬度", "infoValue": "31.963473"}, {"infoName": "人数", "infoValue": "31"}, {"infoName": "指数", "infoValue": "31%"}, {"infoName": "次数", "infoValue": "31.963473111111"}], "activieList": [{"stepId": "11111", "activityId": "20190306", "activityName": "活动1", "businessType": "1", "businessName": "专线"}, {"stepId": "11111", "activityId": "20190305", "activityName": "活动2", "businessType": "1", "businessName": "专线"}, {"stepId": "11111", "activityId": "20190307", "activityName": "活动3", "businessType": "1", "businessName": "专线"}, {"stepId": "11111", "activityId": "20190308", "activityName": "活动4", "businessType": "1", "businessName": "专线"}]}}, "h5getOutCallDeatils": {"retCode": "0", "retMsg": "获取外呼清单成功", "data": [{"channelId": "13", "workId": null, "objectId": null, "stepId": null, "userPhone": "***********", "createDate": "2020/02/03 23:47:34", "userId": null, "businessId": null}, {"channelId": "2", "workId": null, "objectId": null, "stepId": null, "userPhone": "***********", "createDate": "2020/02/03 22:47:34", "userId": null, "businessId": null}, {"channelId": "12", "workId": null, "objectId": null, "stepId": null, "userPhone": "***********", "createDate": "2020/02/03 21:47:34", "userId": null, "businessId": null}, {"channelId": "4444", "workId": "YYTBM1005978", "objectId": null, "stepId": null, "userPhone": "***********", "createDate": "2020/02/03 20:47:34", "userId": null, "businessId": null}]}, "h5qryToplCollegeBusiness": {"retCode": "0", "retMsg": null, "data": [{"busiCount": "19000", "busiName": "南京大学"}, {"busiCount": "16100", "busiName": "东南大学"}, {"busiCount": "15000", "busiName": "河海大学"}, {"busiCount": "14000", "busiName": "南京理工大学"}, {"busiCount": "13000", "busiName": "南京工业大学"}, {"busiCount": "11900", "busiName": "南京航天航空大学"}, {"busiCount": "11000", "busiName": "苏州大学"}, {"busiCount": "11000", "busiName": "扬州大学"}, {"busiCount": "7000", "busiName": "江苏大学"}, {"busiCount": "6000", "busiName": "徐州工程学院"}]}, "h5qryAllCollegeBusiness": {"retCode": "0", "retMsg": null, "data": {"points": [[120.***********, 30.236064370321], [120.***********, 30.***********], [120.***********, 30.236125905084], [120.***********, 30.236035316745], [120.1428734612, 30.236160551632], [120.***********, 30.***********], [120.***********, 30.236113748704], [120.1400398833, 30.235973050702]], "marker": [{"name": "广州火车站", "longitude": "113.264531", "latitude": "23.157003"}, {"name": "广州塔（赤岗塔）", "longitude": "113.330934", "latitude": "23.113401"}, {"name": "广州动物园", "longitude": "113.312213", "latitude": "23.147267"}, {"name": "天河公园", "longitude": "113.372867", "latitude": "23.134274"}]}}, "h5InterfaceCount": {"retCode": "0", "retMsg": null, "data": [{"busiType": "true_name_record", "busiName": "预配号补资料", "interfaces": [{"interfaceId": "FSOP2BDS1118", "interfaceName": "cc_cvscardStockChk", "sort": 1, "interfaceRemark": "便利店业主校验", "callTime": 0, "avgTime": 0}, {"interfaceId": "TRUENAME1001", "interfaceName": "chkappenduserinfo", "sort": 2, "interfaceRemark": "预配网入网补资料校验", "callTime": 0, "avgTime": 0}, {"interfaceId": "FSOP2BDS1135", "interfaceName": "bes_uni_presentcardqry", "sort": 4, "interfaceRemark": "用户营销方案信息查询接口", "callTime": 0, "avgTime": 0}, {"interfaceId": "FAMILYTEMP1002", "interfaceName": "cc_idcheck", "sort": 5, "interfaceRemark": "查询证件下信息", "callTime": 0, "avgTime": 0}, {"interfaceId": "FSOP2BDS1132", "interfaceName": "bes_uni_canorderpresentcard", "sort": 7, "interfaceRemark": "赠卡开户校验接口", "callTime": 0, "avgTime": 0}, {"interfaceId": "FAMILYTEMP1003", "interfaceName": "cc_idcampon", "sort": 8, "interfaceRemark": "证件状态预占变更", "callTime": 0, "avgTime": 0}, {"interfaceId": "FAMILYTEMP1001", "interfaceName": "cc_reality_verify", "sort": 9, "interfaceRemark": "实时查询验证", "callTime": 0, "avgTime": 0}, {"interfaceId": "TRUENAME1002", "interfaceName": "cmcvsg3_cusinforecord", "sort": 10, "interfaceRemark": "客户资料录入", "callTime": 19, "avgTime": 7198}, {"interfaceId": "FAMILYTEMP1004", "interfaceName": "cc_userinfosyn", "sort": 11, "interfaceRemark": "用户信息变更同步", "callTime": 0, "avgTime": 0}, {"interfaceId": "FSOP2BDS1133", "interfaceName": "bes_uni_orderpresentcardsyn", "sort": 12, "interfaceRemark": "赠卡开户信息同步接口", "callTime": 0, "avgTime": 0}]}, {"busiType": "TERMINA_SELL", "busiName": "营销案办理", "interfaces": [{"interfaceId": "FAMILY1024", "interfaceName": "qryfmylevel", "sort": 1, "interfaceRemark": "营销案查询档次接口", "callTime": 0, "avgTime": 0}, {"interfaceId": "FAMILY1025", "interfaceName": "qry<PERSON><PERSON><PERSON><PERSON>", "sort": 2, "interfaceRemark": "营销案查询奖品接口", "callTime": 0, "avgTime": 0}, {"interfaceId": "FAMILY1028", "interfaceName": "queryfmyrwdfee", "sort": 3, "interfaceRemark": "营销案算费接口", "callTime": 0, "avgTime": 0}, {"interfaceId": "FAMILY1029", "interfaceName": "fmyactrwdcommit", "sort": 4, "interfaceRemark": "营销案提交接口", "callTime": 0, "avgTime": 0}]}, {"busiType": "campus_v_net_add", "busiName": "校园V网", "interfaces": [{"interfaceId": "CRMSYS0002", "interfaceName": "SYS0002", "sort": 1, "interfaceRemark": "查询集团列表", "callTime": 0, "avgTime": 0}, {"interfaceId": "CRMG3ESOP1001", "interfaceName": "qrygrpvprodinfobycustid", "sort": 2, "interfaceRemark": "查询V网分组套餐", "callTime": 0, "avgTime": 0}, {"interfaceId": "FSOP2BDS1233", "interfaceName": "cc_vaddotherregmemchk", "sort": 3, "interfaceRemark": "校园V网校验", "callTime": 0, "avgTime": 0}, {"interfaceId": "CRMG3V0015", "interfaceName": "G3V0015", "sort": 4, "interfaceRemark": "成员短号查询", "callTime": 0, "avgTime": 0}, {"interfaceId": "FSOP2BDS1253", "interfaceName": "cc_chkshortnumstatusingrp", "sort": 5, "interfaceRemark": "短号校验", "callTime": 0, "avgTime": 0}, {"interfaceId": "CRMG3ESOP1005", "interfaceName": "custme<PERSON>d", "sort": 6, "interfaceRemark": "添加V网分组成员", "callTime": 0, "avgTime": 0}]}, {"busiType": "national_family_net_open_group", "busiName": "全国亲情网", "interfaces": [{"interfaceId": "FSOP2BDS1227", "interfaceName": "cc_qryksfamily", "sort": 1, "interfaceRemark": "亲情网组成员查询", "callTime": 0, "avgTime": 0}, {"interfaceId": "FSOP2BDS1228", "interfaceName": "cc_ksfamilyinstall", "sort": 2, "interfaceRemark": "亲情网组开通", "callTime": 0, "avgTime": 0}]}]}, "h5getGrabsheetOrderInfo": {"retCode": "0", "retMsg": "【抢单信息查询 FSOP2BDS1223】success", "data": [{"orderId": "19314491746070579147", "offerId": "**********", "offerName": "**********(2000011802)", "orderStatus": "88", "grabStatus": "2", "deliveryType": "WXTS", "createTime": "20191104174607", "remark": "徐州--个人畅享98（18+80）只有产品_行商派单16706105", "cityId": "16", "accNbrr": "15866251148", "serviceCode": "10000", "factAmount": "0", "shipAddr": "江苏省徐州丰县孙楼街道行商上门写卡开户(系统默认地址)", "deliveryPeriod": "", "grabStaff": "16706105", "dealMessage": "", "grabTime": "20191104174646", "cancelTime": "20191106174607", "appointmentTime": "2020-09-17 16:58:11", "contactPhone": "18652207286", "contactName": "王凯", "orderFrom": "109", "grabType": "01", "hasFirstCharge": null}, {"orderId": "19314491746070579148", "offerId": "**********", "offerName": "**********(2000011802)", "orderStatus": "88", "grabStatus": "2", "deliveryType": "WXTS", "createTime": "20191104174607", "remark": "徐州--个人畅享98（18+80）只有产品_行商派单16706105", "cityId": "16", "accNbrr": "15862547818", "serviceCode": "10000", "factAmount": "0", "shipAddr": "江苏省徐州丰县孙楼街道行商上门写卡开户(系统默认地址)", "deliveryPeriod": "", "grabStaff": "16706105", "dealMessage": "", "grabTime": "20191104174646", "cancelTime": "20191106174607", "appointmentTime": "2020-09-17 16:58:11", "contactPhone": "18652207286", "contactName": "王凯", "orderFrom": "130", "grabType": "05", "hasFirstCharge": null}]}, "h5CountBusiNum": {"data": {"busiRegionNameTotalList": [{"name": "苏州", "value": 64}, {"name": "淮安", "value": 10}, {"name": "宿迁", "value": 266}, {"name": "南京", "value": 1547}, {"name": "连云港", "value": 282}, {"name": "徐州", "value": 190}, {"name": "常州", "value": 6}, {"name": "镇江", "value": 234}, {"name": "无锡", "value": 1623}, {"name": "南通", "value": 50}, {"name": "泰州", "value": 111}, {"name": "盐城", "value": 397}, {"name": "扬州", "value": 0}], "maxBusiNum": 1623, "busiTotalNum": 4788, "minBusiNum": 0, "busiRegionTotalList": [64, 10, 266, 1547, 282, 190, 6, 234, 1623, 50, 111, 397, 0], "countBusiNumList": [{"busiNum": 4027, "busiName": "预配号入网", "busiType": "true_name_record"}, {"busiNum": 358, "busiName": "宽带新增", "busiType": "mband_prod_kaitong"}, {"busiNum": 308, "busiName": "营销案办理", "busiType": "TERMINA_SELL"}, {"busiNum": 9, "busiName": "校园V网", "busiType": "campus_v_net"}, {"busiNum": 86, "busiName": "全国亲情网", "busiType": "national_family_net"}], "busiRegionMap": {"泰州": {"totalNum": 111, "busiTypeNameNumInfoList": [{"busiTypeName": "预配号入网", "busiNum": 111, "busiType": "true_name_record"}, {"busiTypeName": "宽带新增", "busiNum": 0, "busiType": "mband_prod_kaitong"}, {"busiTypeName": "营销案办理", "busiNum": 0, "busiType": "TERMINA_SELL"}, {"busiTypeName": "校园V网", "busiNum": 0, "busiType": "campus_v_net"}, {"busiTypeName": "全国亲情网", "busiNum": 0, "busiType": "national_family_net"}]}, "徐州": {"totalNum": 190, "busiTypeNameNumInfoList": [{"busiTypeName": "预配号入网", "busiNum": 96, "busiType": "true_name_record"}, {"busiTypeName": "宽带新增", "busiNum": 94, "busiType": "mband_prod_kaitong"}, {"busiTypeName": "营销案办理", "busiNum": 0, "busiType": "TERMINA_SELL"}, {"busiTypeName": "校园V网", "busiNum": 0, "busiType": "campus_v_net"}, {"busiTypeName": "全国亲情网", "busiNum": 0, "busiType": "national_family_net"}]}, "宿迁": {"totalNum": 266, "busiTypeNameNumInfoList": [{"busiTypeName": "预配号入网", "busiNum": 255, "busiType": "true_name_record"}, {"busiTypeName": "宽带新增", "busiNum": 11, "busiType": "mband_prod_kaitong"}, {"busiTypeName": "营销案办理", "busiNum": 0, "busiType": "TERMINA_SELL"}, {"busiTypeName": "校园V网", "busiNum": 0, "busiType": "campus_v_net"}, {"busiTypeName": "全国亲情网", "busiNum": 0, "busiType": "national_family_net"}]}, "南通": {"totalNum": 50, "busiTypeNameNumInfoList": [{"busiTypeName": "预配号入网", "busiNum": 50, "busiType": "true_name_record"}, {"busiTypeName": "宽带新增", "busiNum": 0, "busiType": "mband_prod_kaitong"}, {"busiTypeName": "营销案办理", "busiNum": 0, "busiType": "TERMINA_SELL"}, {"busiTypeName": "校园V网", "busiNum": 0, "busiType": "campus_v_net"}, {"busiTypeName": "全国亲情网", "busiNum": 0, "busiType": "national_family_net"}]}, "镇江": {"totalNum": 234, "busiTypeNameNumInfoList": [{"busiTypeName": "预配号入网", "busiNum": 206, "busiType": "true_name_record"}, {"busiTypeName": "宽带新增", "busiNum": 28, "busiType": "mband_prod_kaitong"}, {"busiTypeName": "营销案办理", "busiNum": 0, "busiType": "TERMINA_SELL"}, {"busiTypeName": "校园V网", "busiNum": 0, "busiType": "campus_v_net"}, {"busiTypeName": "全国亲情网", "busiNum": 0, "busiType": "national_family_net"}]}, "常州": {"totalNum": 6, "busiTypeNameNumInfoList": [{"busiTypeName": "预配号入网", "busiNum": 6, "busiType": "true_name_record"}, {"busiTypeName": "宽带新增", "busiNum": 0, "busiType": "mband_prod_kaitong"}, {"busiTypeName": "营销案办理", "busiNum": 0, "busiType": "TERMINA_SELL"}, {"busiTypeName": "校园V网", "busiNum": 0, "busiType": "campus_v_net"}, {"busiTypeName": "全国亲情网", "busiNum": 0, "busiType": "national_family_net"}]}, "无锡": {"totalNum": 1623, "busiTypeNameNumInfoList": [{"busiTypeName": "预配号入网", "busiNum": 1606, "busiType": "true_name_record"}, {"busiTypeName": "宽带新增", "busiNum": 8, "busiType": "mband_prod_kaitong"}, {"busiTypeName": "营销案办理", "busiNum": 9, "busiType": "TERMINA_SELL"}, {"busiTypeName": "校园V网", "busiNum": 0, "busiType": "campus_v_net"}, {"busiTypeName": "全国亲情网", "busiNum": 0, "busiType": "national_family_net"}]}, "盐城": {"totalNum": 397, "busiTypeNameNumInfoList": [{"busiTypeName": "预配号入网", "busiNum": 397, "busiType": "true_name_record"}, {"busiTypeName": "宽带新增", "busiNum": 0, "busiType": "mband_prod_kaitong"}, {"busiTypeName": "营销案办理", "busiNum": 0, "busiType": "TERMINA_SELL"}, {"busiTypeName": "校园V网", "busiNum": 0, "busiType": "campus_v_net"}, {"busiTypeName": "全国亲情网", "busiNum": 0, "busiType": "national_family_net"}]}, "淮安": {"totalNum": 10, "busiTypeNameNumInfoList": [{"busiTypeName": "预配号入网", "busiNum": 9, "busiType": "true_name_record"}, {"busiTypeName": "宽带新增", "busiNum": 1, "busiType": "mband_prod_kaitong"}, {"busiTypeName": "营销案办理", "busiNum": 0, "busiType": "TERMINA_SELL"}, {"busiTypeName": "校园V网", "busiNum": 0, "busiType": "campus_v_net"}, {"busiTypeName": "全国亲情网", "busiNum": 0, "busiType": "national_family_net"}]}, "连云港": {"totalNum": 282, "busiTypeNameNumInfoList": [{"busiTypeName": "预配号入网", "busiNum": 282, "busiType": "true_name_record"}, {"busiTypeName": "宽带新增", "busiNum": 0, "busiType": "mband_prod_kaitong"}, {"busiTypeName": "营销案办理", "busiNum": 0, "busiType": "TERMINA_SELL"}, {"busiTypeName": "校园V网", "busiNum": 0, "busiType": "campus_v_net"}, {"busiTypeName": "全国亲情网", "busiNum": 0, "busiType": "national_family_net"}]}, "扬州": {"totalNum": 0, "busiTypeNameNumInfoList": [{"busiTypeName": "预配号入网", "busiNum": 0, "busiType": "true_name_record"}, {"busiTypeName": "宽带新增", "busiNum": 0, "busiType": "mband_prod_kaitong"}, {"busiTypeName": "营销案办理", "busiNum": 0, "busiType": "TERMINA_SELL"}, {"busiTypeName": "校园V网", "busiNum": 0, "busiType": "campus_v_net"}, {"busiTypeName": "全国亲情网", "busiNum": 0, "busiType": "national_family_net"}]}, "苏州": {"totalNum": 64, "busiTypeNameNumInfoList": [{"busiTypeName": "预配号入网", "busiNum": 0, "busiType": "true_name_record"}, {"busiTypeName": "宽带新增", "busiNum": 4, "busiType": "mband_prod_kaitong"}, {"busiTypeName": "营销案办理", "busiNum": 60, "busiType": "TERMINA_SELL"}, {"busiTypeName": "校园V网", "busiNum": 0, "busiType": "campus_v_net"}, {"busiTypeName": "全国亲情网", "busiNum": 0, "busiType": "national_family_net"}]}, "南京": {"totalNum": 1547, "busiTypeNameNumInfoList": [{"busiTypeName": "预配号入网", "busiNum": 1001, "busiType": "true_name_record"}, {"busiTypeName": "宽带新增", "busiNum": 212, "busiType": "mband_prod_kaitong"}, {"busiTypeName": "营销案办理", "busiNum": 239, "busiType": "TERMINA_SELL"}, {"busiTypeName": "校园V网", "busiNum": 9, "busiType": "campus_v_net"}, {"busiTypeName": "全国亲情网", "busiNum": 86, "busiType": "national_family_net"}]}}}, "retCode": "0", "retMsg": null}, "h5QryInfoCollectGroupList": {"retCode": "0", "retMsg": null, "data": {"resultCode": "0", "resultDesc": "success", "streamSeq": "*******************", "objectData": [{"objectId": "1111", "objectName": "新大陆1", "infoList": [{"infoName": "集团类型", "infoValue": "A"}]}, {"objectId": "2222", "objectName": "新大陆2", "infoList": [{"infoName": "集团类型", "infoValue": "B"}]}]}}, "h5BindActionSubmit": {"retCode": "0", "retMsg": null, "data": {"resultCode": "0", "resultDesc": "success", "streamSeq": "*******************"}}, "h5GetHostMonitorData": {"data": {"hostNameList": ["wui002", "wui001", "wui003"], "timeList": ["00:10", "00:20", "00:30", "00:40", "00:50", "01:00"], "memSeries": [{"symbol": "none", "stack": "总量", "areaStyle": {"color": null, "opacity": null}, "data": [60, 61, 62, 63, 64, 65], "name": "wui002", "type": "line", "smooth": true}, {"symbol": "none", "stack": "总量", "areaStyle": {"color": null, "opacity": null}, "data": [40, 41, 42, 43, 44, 45], "name": "wui001", "type": "line", "smooth": true}, {"symbol": "none", "stack": "总量", "areaStyle": {"color": null, "opacity": null}, "data": [80, 81, 82, 83, 84, 85], "name": "wui003", "type": "line", "smooth": true}], "cpuSeries": [{"symbol": "none", "stack": "总量", "areaStyle": {"color": null, "opacity": null}, "data": [50, 51, 52, 53, 54, 55], "name": "wui002", "type": "line", "smooth": true}, {"symbol": "none", "stack": "总量", "areaStyle": {"color": null, "opacity": null}, "data": [30, 31, 32, 33, 34, 35], "name": "wui001", "type": "line", "smooth": true}, {"symbol": "none", "stack": "总量", "areaStyle": {"color": null, "opacity": null}, "data": [70, 71, 72, 73, 74, 75], "name": "wui003", "type": "line", "smooth": true}], "hostMonitorAnalyList": [{"hostName": "wui003", "memRateCurrent": 85, "memRateMax": 85, "hostIp": "*************", "memRateMin": 80, "memRateAvg": 83, "cpuRateMax": 75, "cpuRateCurrent": 75, "cpuRateMin": 70, "cpuRateAvg": 73}, {"hostName": "wui002", "memRateCurrent": 65, "memRateMax": 65, "hostIp": "*************", "memRateMin": 60, "memRateAvg": 63, "cpuRateMax": 55, "cpuRateCurrent": 55, "cpuRateMin": 50, "cpuRateAvg": 53}, {"hostName": "wui001", "memRateCurrent": 45, "memRateMax": 45, "hostIp": "*************", "memRateMin": 40, "memRateAvg": 43, "cpuRateMax": 35, "cpuRateCurrent": 35, "cpuRateMin": 30, "cpuRateAvg": 33}]}, "retCode": "0", "retMsg": null}, "h5CountSchoolMarketBusiRegionsNum": {"data": {"busiTypeNameList": [{"busiTypeName": "预配号入网", "busiType": "true_name_record"}, {"busiTypeName": "宽带新增", "busiType": "mband_prod_kaitong"}, {"busiTypeName": "营销案办理", "busiType": "TERMINA_SELL"}, {"busiTypeName": "校园V网", "busiType": "campus_v_net"}, {"busiTypeName": "全国亲情网", "busiType": "national_family_net"}], "regionsBusiNumMap": {"national_family_net": [0, 0, 0, 86, 0, 0, 0, 0, 0, 0, 0, 0, 0], "TERMINA_SELL": [60, 0, 0, 239, 0, 0, 0, 0, 9, 0, 0, 0, 0], "true_name_record": [0, 9, 255, 1001, 282, 96, 6, 206, 1606, 50, 111, 397, 0], "mband_prod_kaitong": [4, 1, 11, 212, 0, 94, 0, 28, 8, 0, 0, 0, 0], "campus_v_net": [0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0]}}}, "h5logCollectAdd": {"retCode": "0", "retMsg": null, "data": ""}, "h5logCollectUpdate": {"retCode": "0", "retMsg": null, "data": ""}, "h5logCollectDate": {"retCode": "0", "retMsg": null, "data": ["20201009141237100063", "20201010141052100062", "20201011140537100061"]}, "h5logCollectContent": {"retCode": "0", "retMsg": "", "data": {"logId": "", "crmId": "14150759", "staffId": "1488018492933394", "logContent": "", "createTime": "2020-10-13 13:28:03", "modifyTime": "", "todayBacklog": "2", "todayHasDone": "1", "leftoverProblem": "5", "todayPercent": "0.3333", "totalPercent": "0.8", "todayVisit": "1", "reception": "1"}}, "h5QryLogBusiPermission": {"retCode": "1", "retMsg": null, "data": null}, "h5GroupPointInfoSync": {"retCode": "0", "retMsg": "", "data": {"requestSeq": "20201015174123", "responseTime": "20201015174123", "resultCode": "0000", "resultDesc": "成功", "auditUsers": [{"name": "丘处机", "mobile": "13675177912", "operatorId": "880032731", "operatorCrm": "14150759", "userType": "1"}, {"name": "王重阳", "mobile": "13675177912", "operatorId": "880032732", "operatorCrm": "14150759", "userType": "2"}]}}, "h5getDwActive": {"retCode": "0", "retMsg": null, "data": [{"module_type": "3", "data": [{"sub_mol_type": "4", "activity_id": "ty100000172768", "activity_name": "全国亲情网1分钱体验", "step_id": "20191023083859825227000", "market_term": "请推荐客户体验1分钱全国亲情网，24个月全国亲友通话不限量。", "sms_type": "26", "sms_content": "", "knowledge_path": "/icsp/kbs/c_kbs_provServiceKng.action?kngId=&productName=", "is_recom_prod": "1", "data_list": [{"product_type": "7", "product_id": "8902006492", "product_id_exp1": "", "product_id_exp2": "", "product_id_exp3": "0", "product_id_exp4": "", "priority": null, "is_item": null, "cnt_reject": null, "cnt_consider": null, "content1": null, "content2": null, "content3": null, "content4": null, "content5": null, "product_id_exp5": "全国亲情网优惠活动（三）", "product_id_exp6": "", "product_id_exp7": "", "canDo": null}], "isData": "0", "show": "0"}], "isData": "0"}, {"module_type": "6", "data": [{"sub_mol_type": "10", "activity_id": "ty100000174048", "activity_name": "【2019优惠计划】任我用家庭版128元(全国版)", "step_id": "20191028164806854526000", "market_term": "引导用户办理2019优惠计划，每月得5G咪咕视频流量，优惠至2019年底", "sms_type": "26", "sms_content": "", "knowledge_path": "/icsp/kbs/c_kbs_provServiceKng.action?kngId=&productName=", "is_recom_prod": "1", "data_list": [{"product_type": "3", "product_id": "2000011420", "product_id_exp1": "", "product_id_exp2": "", "product_id_exp3": "1", "product_id_exp4": "", "priority": null, "is_item": null, "cnt_reject": null, "cnt_consider": null, "content1": null, "content2": null, "content3": null, "content4": null, "content5": null, "product_id_exp5": "任我用家庭版128元(全国版)（2019优惠计划）", "product_id_exp6": "", "product_id_exp7": "", "canDo": null}], "isData": "0", "show": "0"}], "isData": "0"}, {"module_type": "13", "data": [{"sub_mol_type": "13", "activity_id": "ty100000168400", "activity_name": "和彩云超值购（前折7折）（2019四季度）", "step_id": "96f3600c05324b7c96dc212d06e75788", "market_term": "通过本活动开通和彩云基础版可享受一个月功能费7折优惠（1元*0.7=0.7元），活动到期恢复标准资费（1元/月），业务继续保留，请问您需要办理吗？", "sms_type": "26", "sms_content": "", "knowledge_path": "/icsp/kbs/c_kbs_provServiceKng.action?kngId=20190926150621558565&productName=", "is_recom_prod": "1", "data_list": [{"product_type": "2", "product_id": "3002104134", "product_id_exp1": "300004064639", "product_id_exp2": "", "product_id_exp3": "1", "product_id_exp4": "", "priority": null, "is_item": null, "cnt_reject": null, "cnt_consider": null, "content1": null, "content2": null, "content3": null, "content4": null, "content5": null, "product_id_exp5": "基础功能业务超值购", "product_id_exp6": "手机网盘（和彩云基础版）超值购", "product_id_exp7": "", "canDo": null}], "isData": "0", "show": "0"}], "isData": "0"}, {"module_type": "11", "data": [{"sub_mol_type": "14", "activity_id": "ty100000131156", "activity_name": "南京移动分享卡优先推荐客户", "step_id": "20190510174336054843000", "market_term": "推荐办理移动分享卡新入网", "sms_type": "26", "sms_content": "", "knowledge_path": "/icsp/kbs/c_kbs_provServiceKng.action?kngId=&productName=", "is_recom_prod": "0", "data_list": null, "isData": "0", "show": "0"}, {"sub_mol_type": "14", "activity_id": "ty100000176918", "activity_name": "18年入网任我用客户接盘维系-原28任我用复制", "step_id": "20191104102709446752000", "market_term": "您的前期办理的新入网优惠即将到期，您可享折后套餐费58元起接续优惠（套餐费直降50，享250分钟语音扩容包）。优惠期12个月。办理套餐：90元自选流量套餐+18元自选语音套餐及以上档次办理活动1：活动大类：2019年南京套餐优惠活动（12个月）活动小类：19套餐优惠-直降50（108档及以上）办理活动2：活动大类：2019年南京语音扩容优惠活动小类：语音扩容250分钟", "sms_type": "26", "sms_content": "", "knowledge_path": "/icsp/kbs/c_kbs_provServiceKng.action?kngId=&productName=", "is_recom_prod": "1", "data_list": [{"product_type": "7", "product_id": "8902009480", "product_id_exp1": "", "product_id_exp2": "", "product_id_exp3": "0", "product_id_exp4": "", "priority": null, "is_item": null, "cnt_reject": null, "cnt_consider": null, "content1": null, "content2": null, "content3": null, "content4": null, "content5": null, "product_id_exp5": "108档直降50活动+250分钟语音扩容", "product_id_exp6": "", "product_id_exp7": "", "canDo": null}], "isData": "0", "show": "0"}], "isData": "0"}]}, "h5qryBusiType": {"retCode": "0", "retMsg": null, "data": {"yiwangList": [{"ordertype": "1", "busitypename": "一网通", "type": "2"}, {"ordertype": "1", "busitypename": "新一网通产品", "type": "2"}], "sanxianListShigong": [{"ordertype": "2", "busitypename": "互联网专线", "type": "1"}, {"ordertype": "2", "busitypename": "数据专线C2C", "type": "1"}, {"ordertype": "2", "busitypename": "数据专线", "type": "1"}, {"ordertype": "2", "busitypename": "语音专线", "type": "1"}], "sanxianList": [{"ordertype": "1", "busitypename": "专线专网-互联网专线", "type": "1"}, {"ordertype": "1", "busitypename": "专线专网-数据专线(集中预约)", "type": "1"}, {"ordertype": "1", "busitypename": "新-数据专线(集中预约)", "type": "1"}, {"ordertype": "1", "busitypename": "新-数据专线", "type": "1"}, {"ordertype": "1", "busitypename": "专线专网-数据专线C2C(集中预约)", "type": "1"}, {"ordertype": "1", "busitypename": "新-数据专线C2C(集中预约)", "type": "1"}, {"ordertype": "1", "busitypename": "新-数据专线C2C", "type": "1"}, {"ordertype": "1", "busitypename": "专线专网-数据专线", "type": "1"}, {"ordertype": "1", "busitypename": "专线专网-语音专线(集中预约)", "type": "1"}, {"ordertype": "1", "busitypename": "新-语音专线(集中预约)", "type": "1"}, {"ordertype": "1", "busitypename": "新-语音专线", "type": "1"}, {"ordertype": "1", "busitypename": "专线专网-语音专线", "type": "1"}, {"ordertype": "1", "busitypename": "专线专网-互联网专线(集中预约)", "type": "1"}, {"ordertype": "1", "busitypename": "新-互联网专线(集中预约)", "type": "1"}, {"ordertype": "1", "busitypename": "新-互联网专线", "type": "1"}], "yiwangListShigong": [{"ordertype": "2", "busitypename": "一网通融合", "type": "2"}, {"ordertype": "2", "busitypename": "商务版一网通", "type": "2"}, {"ordertype": "2", "busitypename": "商务快线（动态）", "type": "2"}], "h5ListVNetPkg": {"retCode": "0", "retMsg": "success", "data": [{"prodid": "2000005873", "prodName": "1元包本地主叫300分钟（13版集团套餐）", "servnum": null, "subsid": "1899300034427905"}, {"prodid": "2000001586", "prodName": "1元包本地主叫200分钟(09版集团套餐)", "servnum": null, "subsid": "1899300032564944"}, {"prodid": "2000005597", "prodName": "20元包省内主叫1000分钟(07版集团套餐)", "servnum": null, "subsid": "1899300034125911"}, {"prodid": "2000006205", "prodName": "5元包全国主叫集团内800分钟(16版集团套餐)", "servnum": null, "subsid": "1899300034332922"}, {"prodid": "2000006206", "prodName": "10元包全国主叫集团内1500分钟(16版集团套餐)", "servnum": null, "subsid": "1899300034791920"}]}}}, "h5CountEnterpriceUnDoNum": {"retCode": "0", "retMsg": "success", "data": {"totalNum": "100", "todayNum": "10", "historyNum": "90", "itemList": [{"name": "上行下达", "num": "10"}]}}, "h5QryJtRelatedInfo": {"retCode": "0", "retMsg": "success", "data": {"dyjzsrpm": "10", "dnjzsrpm": "2", "dyxzjts": "1", "qfjts": "1", "tfcys": "1", "byjztsw": "1", "dysr": "1", "qfje": "1", "warnCnt": "1"}}, "h5QryJtMarketStatistics": {"retCode": "0", "retMsg": null, "data": [{"planId": "ALD2020111210003", "groupId": "1001", "groupName": "新大陆集团", "operId": "14150759", "operName": "王涛", "source": "2", "hasVisited": "1", "totalTaskNum": "3", "undoTaskNum": "3", "totalYwNum": "11", "taskList": [{"operId": "100003", "operName": "王五", "status": "0", "ywNum": "2"}, {"operId": "100002", "operName": "李四", "status": "0", "ywNum": "6"}, {"operId": "100001", "operName": "张三", "status": "0", "ywNum": "3"}]}, {"planId": "ALD2020111210001", "groupId": "1001", "groupName": "新大陆集团", "operId": "14150759", "operName": "王涛", "source": "2", "hasVisited": "2", "totalTaskNum": "0", "undoTaskNum": "0", "totalYwNum": "0", "taskList": null}]}, "h5getChkLast6digitsOfCertIdCfgNum": {"retCode": "0", "retMsg": "success", "data": "1"}, "h5chkLast6digitsOfCertId": {"retCode": "0", "retMsg": "success", "data": null}, "h5qryCustomerInfo": {"retCode": "0", "retMsg": "查询成功", "data": null}, "h5QryCustomerInfo": {"retCode": "0", "retMsg": "查询成功", "data": {"localCnt": "2", "localTotalCnt": "18", "feeInterval": "100元-150元"}}, "h5qryOfferInfo": {"retCode": "0", "retMsg": "查询成功", "data": {"offerBasicInfo": [{"commodityCode": "2000009174", "commodityName": "50元包月（100M，手机最低消费8元）", "efftDate": "20201112", "invalidDate": "20201230", "status": "1", "commodityDesc": "商品描述", "commodityType": "11", "mktCampaignCode": "营销资源标识", "subscribeRuleType": "1", "unsubscribeRuleType": "1", "minAmount": "1", "maxAmount": "100", "commodityCatalog": "目录标识"}, {"commodityCode": "2000009213", "commodityName": "600元包月(1000M）", "efftDate": "20201112", "invalidDate": "20201230", "status": "1", "commodityDesc": "商品描述", "commodityType": "11", "mktCampaignCode": "营销资源标识", "subscribeRuleType": "1", "unsubscribeRuleType": "1", "minAmount": "1", "maxAmount": "100", "commodityCatalog": "目录标识"}, {"commodityCode": "2000009211", "commodityName": "150元包月(300M）", "efftDate": "20201112", "invalidDate": "20201230", "status": "1", "commodityDesc": "商品描述", "commodityType": "11", "mktCampaignCode": "营销资源标识", "subscribeRuleType": "1", "unsubscribeRuleType": "1", "minAmount": "1", "maxAmount": "100", "commodityCatalog": "目录标识"}, {"commodityCode": "2000009211", "commodityName": "150元包月(300M）", "efftDate": "20201112", "invalidDate": "20201230", "status": "1", "commodityDesc": "商品描述", "commodityType": "11", "mktCampaignCode": "营销资源标识", "subscribeRuleType": "1", "unsubscribeRuleType": "1", "minAmount": "1", "maxAmount": "100", "commodityCatalog": "目录标识"}, {"commodityCode": "2000009211", "commodityName": "150元包月(300M）", "efftDate": "20201112", "invalidDate": "20201230", "status": "1", "commodityDesc": "商品描述", "commodityType": "11", "mktCampaignCode": "营销资源标识", "subscribeRuleType": "1", "unsubscribeRuleType": "1", "minAmount": "1", "maxAmount": "100", "commodityCatalog": "目录标识"}], "offerAttr": [{"attrId": "PPyy999999999", "commodityCode": "10000000001", "attrType": "01", "validStatusCode": "1", "attrName": "属性名称", "attrDesc": "属性描述", "attrValue": "商品属性值信息", "attrValueId": "PVyymmdd9999", "attrValueName": "属性值", "attrValueDesc": "属性值描述"}], "offerLabel": [{"tagId": "标签标识", "commodityCode": "商品标识", "parentTagId": "上级标签标识", "tagInfo": "标签内容"}], "offerComposite": [{"isBundleOffering": "2000007857", "bundleOfferingName": "10元提速包（20M提至50M）", "bundleOfferingStatus": "1"}, {"isBundleOffering": "2000009057", "bundleOfferingName": "10元提速包（提至100M）", "bundleOfferingStatus": "1"}, {"isBundleOffering": "2000002748", "bundleOfferingName": "竣工套餐", "bundleOfferingStatus": "1"}, {"isBundleOffering": "2000002748", "bundleOfferingName": "竣工套餐", "bundleOfferingStatus": "1"}, {"isBundleOffering": "2000002748", "bundleOfferingName": "竣工套餐", "bundleOfferingStatus": "1"}, {"isBundleOffering": "2000002748", "bundleOfferingName": "竣工套餐", "bundleOfferingStatus": "1"}]}}, "h5chkOrder": {"retCode": "0", "retMsg": "成功", "data": "123111111111111111"}, "h5chkCreateOrder": {"retCode": "0", "retMsg": "成功", "data": null}, "h5busiPreAcceptCreateOrder": {"retCode": "0", "retMsg": "成功", "data": null}, "h5qryLimitBandWidth": {"retCode": "0", "retMsg": "成功", "data": [{"prodId": "**********", "limitBandWidth": "50M"}]}, "h5CountSchoolMarketBusiNum": {"data": {"busiRegionNameTotalList": [{"name": "苏州", "value": 64}, {"name": "淮安", "value": 10}, {"name": "宿迁", "value": 266}, {"name": "南京", "value": 1547}, {"name": "连云港", "value": 282}, {"name": "徐州", "value": 190}, {"name": "常州", "value": 6}, {"name": "镇江", "value": 234}, {"name": "无锡", "value": 1623}, {"name": "南通", "value": 50}, {"name": "泰州", "value": 111}, {"name": "盐城", "value": 397}, {"name": "扬州", "value": 0}], "maxBusiNum": 1623, "busiTotalNum": 4788, "minBusiNum": 0, "busiRegionTotalList": [64, 10, 266, 1547, 282, 190, 6, 234, 1623, 50, 111, 397, 0], "countBusiNumList": [{"busiNum": 4027, "busiName": "预配号入网", "busiType": "true_name_record"}, {"busiNum": 358, "busiName": "宽带新增", "busiType": "mband_prod_kaitong"}, {"busiNum": 308, "busiName": "营销案办理", "busiType": "TERMINA_SELL"}, {"busiNum": 9, "busiName": "校园V网", "busiType": "campus_v_net"}, {"busiNum": 86, "busiName": "全国亲情网", "busiType": "national_family_net"}], "busiRegionMap": {"泰州": {"totalNum": 111, "busiTypeNameNumInfoList": [{"busiTypeName": "预配号入网", "busiNum": 111, "busiType": "true_name_record"}, {"busiTypeName": "宽带新增", "busiNum": 0, "busiType": "mband_prod_kaitong"}, {"busiTypeName": "营销案办理", "busiNum": 0, "busiType": "TERMINA_SELL"}, {"busiTypeName": "校园V网", "busiNum": 0, "busiType": "campus_v_net"}, {"busiTypeName": "全国亲情网", "busiNum": 0, "busiType": "national_family_net"}]}, "徐州": {"totalNum": 190, "busiTypeNameNumInfoList": [{"busiTypeName": "预配号入网", "busiNum": 96, "busiType": "true_name_record"}, {"busiTypeName": "宽带新增", "busiNum": 94, "busiType": "mband_prod_kaitong"}, {"busiTypeName": "营销案办理", "busiNum": 0, "busiType": "TERMINA_SELL"}, {"busiTypeName": "校园V网", "busiNum": 0, "busiType": "campus_v_net"}, {"busiTypeName": "全国亲情网", "busiNum": 0, "busiType": "national_family_net"}]}, "宿迁": {"totalNum": 266, "busiTypeNameNumInfoList": [{"busiTypeName": "预配号入网", "busiNum": 255, "busiType": "true_name_record"}, {"busiTypeName": "宽带新增", "busiNum": 11, "busiType": "mband_prod_kaitong"}, {"busiTypeName": "营销案办理", "busiNum": 0, "busiType": "TERMINA_SELL"}, {"busiTypeName": "校园V网", "busiNum": 0, "busiType": "campus_v_net"}, {"busiTypeName": "全国亲情网", "busiNum": 0, "busiType": "national_family_net"}]}, "南通": {"totalNum": 50, "busiTypeNameNumInfoList": [{"busiTypeName": "预配号入网", "busiNum": 50, "busiType": "true_name_record"}, {"busiTypeName": "宽带新增", "busiNum": 0, "busiType": "mband_prod_kaitong"}, {"busiTypeName": "营销案办理", "busiNum": 0, "busiType": "TERMINA_SELL"}, {"busiTypeName": "校园V网", "busiNum": 0, "busiType": "campus_v_net"}, {"busiTypeName": "全国亲情网", "busiNum": 0, "busiType": "national_family_net"}]}, "镇江": {"totalNum": 234, "busiTypeNameNumInfoList": [{"busiTypeName": "预配号入网", "busiNum": 206, "busiType": "true_name_record"}, {"busiTypeName": "宽带新增", "busiNum": 28, "busiType": "mband_prod_kaitong"}, {"busiTypeName": "营销案办理", "busiNum": 0, "busiType": "TERMINA_SELL"}, {"busiTypeName": "校园V网", "busiNum": 0, "busiType": "campus_v_net"}, {"busiTypeName": "全国亲情网", "busiNum": 0, "busiType": "national_family_net"}]}, "常州": {"totalNum": 6, "busiTypeNameNumInfoList": [{"busiTypeName": "预配号入网", "busiNum": 6, "busiType": "true_name_record"}, {"busiTypeName": "宽带新增", "busiNum": 0, "busiType": "mband_prod_kaitong"}, {"busiTypeName": "营销案办理", "busiNum": 0, "busiType": "TERMINA_SELL"}, {"busiTypeName": "校园V网", "busiNum": 0, "busiType": "campus_v_net"}, {"busiTypeName": "全国亲情网", "busiNum": 0, "busiType": "national_family_net"}]}, "无锡": {"totalNum": 1623, "busiTypeNameNumInfoList": [{"busiTypeName": "预配号入网", "busiNum": 1606, "busiType": "true_name_record"}, {"busiTypeName": "宽带新增", "busiNum": 8, "busiType": "mband_prod_kaitong"}, {"busiTypeName": "营销案办理", "busiNum": 9, "busiType": "TERMINA_SELL"}, {"busiTypeName": "校园V网", "busiNum": 0, "busiType": "campus_v_net"}, {"busiTypeName": "全国亲情网", "busiNum": 0, "busiType": "national_family_net"}]}, "盐城": {"totalNum": 397, "busiTypeNameNumInfoList": [{"busiTypeName": "预配号入网", "busiNum": 397, "busiType": "true_name_record"}, {"busiTypeName": "宽带新增", "busiNum": 0, "busiType": "mband_prod_kaitong"}, {"busiTypeName": "营销案办理", "busiNum": 0, "busiType": "TERMINA_SELL"}, {"busiTypeName": "校园V网", "busiNum": 0, "busiType": "campus_v_net"}, {"busiTypeName": "全国亲情网", "busiNum": 0, "busiType": "national_family_net"}]}, "淮安": {"totalNum": 10, "busiTypeNameNumInfoList": [{"busiTypeName": "预配号入网", "busiNum": 9, "busiType": "true_name_record"}, {"busiTypeName": "宽带新增", "busiNum": 1, "busiType": "mband_prod_kaitong"}, {"busiTypeName": "营销案办理", "busiNum": 0, "busiType": "TERMINA_SELL"}, {"busiTypeName": "校园V网", "busiNum": 0, "busiType": "campus_v_net"}, {"busiTypeName": "全国亲情网", "busiNum": 0, "busiType": "national_family_net"}]}, "连云港": {"totalNum": 282, "busiTypeNameNumInfoList": [{"busiTypeName": "预配号入网", "busiNum": 282, "busiType": "true_name_record"}, {"busiTypeName": "宽带新增", "busiNum": 0, "busiType": "mband_prod_kaitong"}, {"busiTypeName": "营销案办理", "busiNum": 0, "busiType": "TERMINA_SELL"}, {"busiTypeName": "校园V网", "busiNum": 0, "busiType": "campus_v_net"}, {"busiTypeName": "全国亲情网", "busiNum": 0, "busiType": "national_family_net"}]}, "扬州": {"totalNum": 0, "busiTypeNameNumInfoList": [{"busiTypeName": "预配号入网", "busiNum": 0, "busiType": "true_name_record"}, {"busiTypeName": "宽带新增", "busiNum": 0, "busiType": "mband_prod_kaitong"}, {"busiTypeName": "营销案办理", "busiNum": 0, "busiType": "TERMINA_SELL"}, {"busiTypeName": "校园V网", "busiNum": 0, "busiType": "campus_v_net"}, {"busiTypeName": "全国亲情网", "busiNum": 0, "busiType": "national_family_net"}]}, "苏州": {"totalNum": 64, "busiTypeNameNumInfoList": [{"busiTypeName": "预配号入网", "busiNum": 0, "busiType": "true_name_record"}, {"busiTypeName": "宽带新增", "busiNum": 4, "busiType": "mband_prod_kaitong"}, {"busiTypeName": "营销案办理", "busiNum": 60, "busiType": "TERMINA_SELL"}, {"busiTypeName": "校园V网", "busiNum": 0, "busiType": "campus_v_net"}, {"busiTypeName": "全国亲情网", "busiNum": 0, "busiType": "national_family_net"}]}, "南京": {"totalNum": 1547, "busiTypeNameNumInfoList": [{"busiTypeName": "预配号入网", "busiNum": 1001, "busiType": "true_name_record"}, {"busiTypeName": "宽带新增", "busiNum": 212, "busiType": "mband_prod_kaitong"}, {"busiTypeName": "营销案办理", "busiNum": 239, "busiType": "TERMINA_SELL"}, {"busiTypeName": "校园V网", "busiNum": 9, "busiType": "campus_v_net"}, {"busiTypeName": "全国亲情网", "busiNum": 86, "busiType": "national_family_net"}]}}}, "retCode": "0", "retMsg": null}, "h5GetGrabReportInfo": {"data": [{"myCountNumber": "100", "realName": "王涛", "myNoreceivedTotalNumber": "103", "myUnfinshTotalNumber": "102", "myIndeliveryTotalNumber": "104", "myFinshTotalNumber": "101", "userName": "14150759", "myNoactivedTotalNumber": "105"}, {"myCountNumber": "200", "realName": "张涛", "myNoreceivedTotalNumber": "203", "myUnfinshTotalNumber": "202", "myIndeliveryTotalNumber": "204", "myFinshTotalNumber": "201", "userName": "14150760", "myNoactivedTotalNumber": "205"}], "retCode": "0", "retMsg": null}, "h5CountGrabNumber": {"retCode": "0", "retMsg": null, "data": {"myOrderedNumber": "101", "myFinshTotalNumber": "30", "completeRate": "0.30"}}, "h5GetSuccessGrabList": {"data": [{"orderId": "20111915074436668691", "serviceCode": "10000", "orderStatus": "60", "remark": "", "cityId": "14", "grabTime": "20201124094304", "accNbrr": "***********", "factAmount": "0", "appointmentTime": "20190211092210", "grabType": "06", "deliveryPeriod": "", "dealMessage": "", "offerName": "移动花卡宝藏版19-6个月轻合约套卡", "contactName": "李延平", "deliveryType": "WXTS", "shipAddr": "秣陵街道东善桥东虹花苑141栋", "hasFirstCharge": "0", "grabStatus": "2", "createTime": "20201119150744", "cancelTime": "20201124150744", "offerId": "", "grabStaff": "********", "firstOrder": "1", "orderFrom": "130", "contactPhone": "***********"}, {"orderId": "20111915070436668463", "serviceCode": "10000", "orderStatus": "60", "remark": "", "cityId": "14", "grabTime": "20201124094250", "accNbrr": "***********", "factAmount": "0", "appointmentTime": "20190211092210", "grabType": "06", "deliveryPeriod": "", "dealMessage": "", "offerName": "移动花卡宝藏版19-6个月轻合约套卡", "contactName": "郑再添", "deliveryType": "WXTS", "shipAddr": "江苏经贸职业技术学院（桃李院）", "hasFirstCharge": "0", "grabStatus": "2", "createTime": "2*************", "cancelTime": "**************", "offerId": "", "grabStaff": "********", "firstOrder": "1", "orderFrom": "130", "contactPhone": "***********"}], "retCode": "0", "retMsg": null}, "h5SubmitGroupBusiness": {"retCode": "0", "retMsg": "成功", "data": {"streamSeq": "*****************"}}, "h5QryGroupBusiness": {"retCode": "0", "retMsg": "成功", "data": {"streamSeq": "*****************", "gridId": "**********", "gridName": "凤凰区域中心", "netData": [{"hasBand": "1", "bandOwner": "1", "bandAccount": "***********", "bandSpeed": "100", "overTime": "********"}], "otherData": [{"busiName": "一网通", "remark": "备注信息"}]}}, "h5QryMarketActivityList": {"retCode": "0", "retMsg": "成功", "data": {"streamSeq": "1220200606141834234", "activeData": [{"busiId": "**********", "busiName": "低消保档150档", "activeId": "**********", "activeName": "低消保档150档"}, {"busiId": "**********", "busiName": "低消保档150档", "activeId": "**********", "activeName": "宽带续费（次月）"}, {"busiId": "**********", "busiName": "信息采集2", "activeId": "1237942", "activeName": "宽带续费（次月）"}, {"busiId": "**********", "busiName": "集体公寓", "activeId": "24124", "activeName": "20号楼291"}, {"busiId": "**********", "busiName": "集体公寓", "activeId": "234", "activeName": "18号楼15A"}, {"busiId": "**********", "busiName": "专业卖场", "activeId": "625141", "activeName": "苏果乐园"}, {"busiId": "**********", "busiName": "医院病房", "activeId": "71921", "activeName": "一楼210"}]}}, "h5qryComplainCount": {"retCode": "0", "retMsg": null, "data": {"resultCode": "00", "resultDesc": "成功", "body": {"dataList": [{"dayComplaints": "11", "monthComplaints": "111", "allComplaints": "1111"}]}}}, "h5qryComplainList": {"retCode": "0", "retMsg": null, "data": {"resultCode": "00", "resultDesc": "成功", "body": {"dataList": [{"telNum": "13151253926", "name": "叶森为", "unit": "ceshi", "busiHall": "ceshi", "dealStatus": "ceshi", "dealTime": "202012091133", "complaintId": "ceshi"}, {"telNum": "13151253926", "name": "叶森为", "unit": "ceshi", "busiHall": "ceshi", "dealStatus": "ceshi", "dealTime": "202012091133", "complaintId": "ceshi"}, {"telNum": "13151253926", "name": "叶森为", "unit": "ceshi", "busiHall": "ceshi", "dealStatus": "ceshi", "dealTime": "ceshi", "complaintId": "ceshi"}, {"telNum": "13151253926", "name": "叶森为", "unit": "ceshi", "busiHall": "ceshi", "dealStatus": "ceshi", "dealTime": "ceshi", "complaintId": "ceshi"}, {"telNum": "13151253926", "name": "叶森为", "unit": "ceshi", "busiHall": "ceshi", "dealStatus": "ceshi", "dealTime": "202012091133", "complaintId": "ceshi"}, {"telNum": "13151253926", "name": "叶森为", "unit": "ceshi", "busiHall": "ceshi", "dealStatus": "ceshi", "dealTime": "202012091133", "complaintId": "ceshi"}, {"telNum": "13151253926", "name": "叶森为", "unit": "ceshi", "busiHall": "ceshi", "dealStatus": "ceshi", "dealTime": "202012091133", "complaintId": "ceshi"}, {"telNum": "13151253926", "name": "叶森为", "unit": "ceshi", "busiHall": "ceshi", "dealStatus": "ceshi", "dealTime": "202012091133", "complaintId": "ceshi"}]}}}, "h5qryComplainDetail": {"retCode": "0", "retMsg": null, "data": {"resultCode": "00", "resultDesc": "成功", "body": {"dataList": [{"unit": "ceshi1", "name": "叶森为", "telnum": "13154265594", "busiHall": "ceshi1", "region": "14", "areaId": "ceshi1", "areaName": "ceshi1", "orgId": "ceshi1", "orgName": "ceshi1", "complaintTime": "202012091133", "complaintContent": "ceshi1", "dealStatus": "ceshi1", "dealResult": "ceshi1"}]}}}, "h5qryMatchBandDetail": {"retCode": "0", "retMsg": null, "data": {"bandId": "**********", "bandNumber": "1", "mainProductNumber": "1"}}, "h5QryTotalBusiLog": {"retCode": "0", "retMsg": null, "data": {"total": "11", "money": "60000", "success": "2", "fail": "9", "typeList": [{"busiType": "TERMINA_SELL", "busiName": "营销案办理", "typeTotal": "1", "typeMoney": "10000", "typeSuccessNum": "1", "detailList": [{"busiType": "TERMINA_SELL", "createOper": "14150759", "region": "14", "createDate": "2020-12-08 15:29", "outSrl": "2020120210403333", "innerSrl": "20201202703339", "state": "0", "amount": "10000", "msisdn": "13776293321", "payType": "0", "payStatus": "1", "orderId": "200113047779136161", "busiName": "营销案办理", "offername": "2017春季校园统一营销活动（主卡）_茂礼哥的档次"}]}]}}, "h5QryVillageTaskInfo": {"retCode": "0", "retMsg": "success", "data": {"operList": [{"areaLivingName": "中海凤凰熙岸1期", "operName": "苹果", "operDate": "2020/11/06 14:25:09"}, {"areaLivingName": "中海凤凰熙岸1期", "operName": "菠萝", "operDate": "2020/11/20 14:25:09"}, {"areaLivingName": "香格里拉", "operName": "水蜜桃", "operDate": "2020/09/12 14:25:09"}, {"areaLivingName": "香格里拉", "operName": "甘蔗", "operDate": "2020/06/12 14:25:09"}, {"areaLivingName": "中海凤凰熙岸1期", "operName": "黄瓜", "operDate": "2020/11/12 14:25:09"}]}}, "h5qryImei": {"retCode": "0", "retMsg": null, "data": [{"phoneNumber": "***********", "imei": "1"}, {"phoneNumber": "***********", "imei": "12"}]}, "h5qryImeiNowadaysCount": {"retCode": "0", "retMsg": "查询IMEI号近30天内解锁次数成功", "data": 1}, "h5qryAccessNum": {"retCode": "0", "retMsg": null, "data": {"transferOwnerNum": "0", "createSubscriberNum": "0", "recoverSubsNum": "0", "infoPerfectionNum": "0", "certNum": "2", "supplementInfoNum": "2"}}, "h5qrySubsCertIdNum": {"retCode": "0", "retMsg": null, "data": {"onnetNum": "4", "threeMonthNum": "5", "samedaydiffchnlIn7days": null, "insideprovSubscount": "0", "outsideprovSubscount": ""}}, "h5queryProductInfo": {"retCode": "0", "retMsg": null, "data": {"total": 24, "pages": 1, "pageData": [{"offerId": "1000007020", "offerName": "测试"}, {"offerId": "2000007020", "offerName": "流量安心包（100MB档）流量安心包（100MB档）流量安心包（100MB档）流量安心包（100MB档）流量安心包（100MB档）"}]}}, "h5officeBusiCreateOrder": {"retCode": "0", "retMsg": "成功", "data": {"orderId": "2021011101010"}}, "h5officeBusiQueryInfo": {"retCode": "0", "retMsg": null, "data": {"total": 22, "pages": 1, "pageData": [{"staffId": null, "region": "14", "crmId": "14150759", "dwUser": null, "beId": null, "bizCode": null, "ownerType": null, "serviceNumber": "1578457845", "orderPayment": null, "addressInfo": null, "orderItem": null, "authChkType": "AuthCheckB", "orderId": "2021011101010", "state": "0", "failReason": "预约成功", "orderInfo": "[{\"offerId\":\"2000003877\",\"offerName\":\"通用流量包30元\"},{\"offerId\":\"2000003780\",\"offerName\":\"通用流量包70元\",}]", "orderSrl": "20210114100143100007", "createDate": "2021-01-1410:15:49", "modifyDate": null}, {"staffId": null, "region": "14", "crmId": "14150759", "dwUser": null, "beId": null, "bizCode": null, "ownerType": null, "serviceNumber": "1578457845", "orderPayment": null, "addressInfo": null, "orderItem": null, "authChkType": "AuthCheckB", "orderId": "2021011101010", "state": "1", "failReason": "成功", "orderInfo": "[{\"offerId\":\"2000003877\",\"offerName\":\"通用流量包30元\"},{\"offerId\":\"2000003780\",\"offerName\":\"通用流量包70元\",}]", "orderSrl": "20210114100143100007", "createDate": "2021-01-1410:15:49", "modifyDate": null}, {"staffId": null, "region": "14", "crmId": "14150759", "dwUser": null, "beId": null, "bizCode": null, "ownerType": null, "serviceNumber": "1578457845", "orderPayment": null, "addressInfo": null, "orderItem": null, "authChkType": "AuthCheckB", "orderId": "2021011101010", "state": "-1", "failReason": "新增失败新增失败新增失败新增失败新增失败新增失败新增失败新增失败新增失败新增失败新增失败新增失败", "orderInfo": "[{\"offerId\":\"2000003877\",\"offerName\":\"通用流量包30元\"},{\"offerId\":\"2000003780\",\"offerName\":\"通用流量包70元\",}]", "orderSrl": "20210114100143100007", "createDate": "2021-01-1410:15:49", "modifyDate": null}, {"staffId": null, "region": "14", "crmId": "14150759", "dwUser": null, "beId": null, "bizCode": null, "ownerType": null, "serviceNumber": "1578457845", "orderPayment": null, "addressInfo": null, "orderItem": null, "authChkType": "AuthCheckB", "orderId": "2021011101010", "state": "-2", "failReason": "删除失败删除失败删除失败删除失败删除失败删除失败删除失败删除失败删除失败删除失败", "orderInfo": "[{\"offerId\":\"2000003877\",\"offerName\":\"通用流量包30元\"},{\"offerId\":\"2000003780\",\"offerName\":\"通用流量包70元\",}]", "orderSrl": "20210114100143100007", "createDate": "2021-01-1410:15:49", "modifyDate": null}, {"staffId": null, "region": "14", "crmId": "14150759", "dwUser": null, "beId": null, "bizCode": null, "ownerType": null, "serviceNumber": "1578457845", "orderPayment": null, "addressInfo": null, "orderItem": null, "authChkType": "AuthCheckB", "orderId": "2021011101010", "state": "-3", "failReason": "同步失败同步失败同步失败同步失败同步失败同步失败同步失败同步失败", "orderInfo": "[{\"offerId\":\"2000003877\",\"offerName\":\"通用流量包30元\"},{\"offerId\":\"2000003780\",\"offerName\":\"通用流量包70元\",}]", "orderSrl": "20210114100143100007", "createDate": "2021-01-1410:15:49", "modifyDate": null}]}}, "h5officeBusiQueryOrder": {"retCode": "0", "retMsg": "成功", "data": null}, "h5officeBusiCancelOrder": {"retCode": "0", "retMsg": "成功", "data": null}, "h5qryNewCustom": {"retCode": "0", "retMsg": "Success", "data": {"result": "1", "customerBusi": {"versionList": {"s000000062691": "是", "s000000062701": "未知", "s000000062711": "是"}}, "inCityFkNum": "1", "inProvinceNum": "1", "inCityNum": "1", "resultMsg": "该客户为省内纯新增客户"}}, "h5queryNpinfo": {"retCode": "0", "retMsg": "success", "data": {"isYiDong": "1", "isInner": "1"}}, "h5GrabKaiHuActivity": {"retCode": "0", "retMsg": "【查询订单信息 FSOP2BDS1138】success", "data": [{"msisdn": "13405128848", "userType": "2", "activeId": "100000215806", "activeName": "2020终端合约70档（和分期/套餐直降）", "stepId": "31cc18cbe9ed420e8f1c52ccf1f45656", "marketTerm": "特定用户可参加和分期购机活动70档，适用营销案：2020年苏州和分期购机活动、2020年苏州和分期购智能终端活动、2020年苏州购机套餐优惠活动", "productData": [{"productType": "1", "productId": "**********", "productName": "2020年苏州购机套餐优惠活动"}, {"productType": "1", "productId": "**********", "productName": "2020年苏州和分期购机活动"}, {"productType": "1", "productId": "**********", "productName": "2020年苏州和分期购智能终端活动"}]}, {"msisdn": "13913204891", "userType": "1", "activeId": "100000215806", "activeName": "2020终端合约70档（和分期/套餐直降）", "stepId": "31cc18cbe9ed420e8f1c52ccf1f45656", "marketTerm": "特定用户可参加和分期购机活动70档，适用营销案：2020年苏州和分期购机活动、2020年苏州和分期购智能终端活动、2020年苏州购机套餐优惠活动", "productData": [{"productType": "1", "productId": "**********", "productName": "2020年苏州购机套餐优惠活动"}, {"productType": "1", "productId": "**********", "productName": "2020年苏州和分期购机活动"}, {"productType": "1", "productId": "**********", "productName": "2020年苏州和分期购智能终端活动"}]}, {"msisdn": "13913204891", "userType": "1", "activeId": "100000276861", "activeName": "流量赠送（如已限速需先开提速包）复制", "stepId": "20200525092337841245000", "marketTerm": "目标用户，到厅可结合其他业务营销，额外增加赠送10GB流量卖点。 注：如已限速需先开通提速包，不开提速包赠送流量无法使用。", "productData": [{"productType": "1", "productId": "3002105194", "productName": "2020年苏州幸运用户流量促销"}]}, {"msisdn": "13913204891", "userType": "1", "activeId": "100000298236", "activeName": "气象快讯弹窗7-9月", "stepId": "9efb68c862224534b8284b77f7b8e1eb", "marketTerm": "气象快讯1元/月，可提供本地24小时内天气预报和生活贴士信息。", "productData": [{"productType": "3", "productId": "2300930131", "productName": "移动快讯-气象快讯"}]}, {"msisdn": "13913204891", "userType": "1", "activeId": "100000300115", "activeName": "全品类信用购30-50元档", "stepId": "2020070303015092709000", "marketTerm": "特定用户推荐办理2019全品类产品信用购活动30-3C-50档，优惠金额320元。", "productData": [{"productType": "1", "productId": "3002105258", "productName": "2019全品类产品信用购活动30-3C"}]}, {"msisdn": "13913204891", "userType": "1", "activeId": "100000300312", "activeName": "全品类信用购20-50元档", "stepId": "20200703150213221235000", "marketTerm": "特定用户推荐办理2019全品类产品信用购活动20-50档，优惠金额210元。", "productData": [{"productType": "1", "productId": "3002105223", "productName": "2019全品类产品信用购活动20"}]}, {"msisdn": "13913204891", "userType": "1", "activeId": "100000302581", "activeName": "10元50分钟语音翻番包送50分钟", "stepId": "20200708094610584698000", "marketTerm": "客户承诺协议期内开通10元50分钟语音翻番包，每月享受50分钟国内主叫通话（不包含港澳台和国际长途），活动次月生效，协议期24个月。活动中享受的通话分钟数仅限当月使用，月底清零，不可结转，不可转赠他人使用。协议期内客户在用的4G自选语音套餐不得变更或关闭，活动到期语音翻番包不自动关闭，客户可自行选择是否继续使用。", "productData": [{"productType": "2", "productId": "300004050263", "productName": "办10元语音翻番包送50分钟（24个月）"}]}, {"msisdn": "13913204891", "userType": "1", "activeId": "100000321366", "activeName": "新全品类信用购（存量保拓20）-30元档", "stepId": "20200812170844526046000", "marketTerm": "特定用户推荐办理2020存量保拓信用购活动（直连20）-30档，优惠金额130元。", "productData": [{"productType": "1", "productId": "3100000183", "productName": "2020存量保拓信用购活动（直连20）"}]}, {"msisdn": "13913204891", "userType": "1", "activeId": "100000324326", "activeName": "同事网专享视频会员（B版）", "stepId": "20200820180207236011000", "marketTerm": "诚邀办理同事网视频会员（B版），咪咕视频会员5元优惠包（5元/月）+手机网盘（和彩云基础版）（1元/月），即折后3元/月，协议期12个月。累计节省36元。", "productData": [{"productType": "", "productId": "", "productName": ""}]}, {"msisdn": "13913204891", "userType": "1", "activeId": "100000324327", "activeName": "同事网专享权益会（A版）", "stepId": "20200820180212622203000", "marketTerm": "诚邀办理同事网专享权益会（A版），半价享受任我选权益汇（5元/月）+手机网盘（和彩云基础版）（1元/月），即折后3元/月，协议期12个月。累计节省36元。", "productData": [{"productType": "", "productId": "", "productName": ""}]}, {"msisdn": "13913204891", "userType": "1", "activeId": "100000327454", "activeName": "任我选会员", "stepId": "20200827110749984218000", "marketTerm": "每月10元即可享原价15元的爱优腾芒任一APP30天会员（每月任选一次），还有1G追剧流量畅用，活动12个月，到期不关", "productData": [{"productType": "2", "productId": "300004092969", "productName": "开任我选会员15元享首12个月优惠"}]}, {"msisdn": "13913204891", "userType": "1", "activeId": "100000336726", "activeName": "全球通客户5G专享-105元档C", "stepId": "20200908151003138105000", "marketTerm": "每月消费达到105元次月即可获得4000积分，价值40元话费，相当于最低消费105每月实际支付65元，活动当月生效，生效次月开始返还积分。积分有效期3年，随时可兑，发送短信HF30至10658999或登入掌厅搜索“积分”兑换", "productData": [{"productType": "2", "productId": "300004093354", "productName": "全球通客户5G专享-105元档C"}]}, {"msisdn": "15051600908", "userType": "2", "activeId": "100000172273", "activeName": "腾讯超值购B档次推荐-3元", "stepId": "20191021145819314472000", "marketTerm": "推荐办理腾讯视频包，连续12个月每月仅需3元得15G腾讯视频专享流量", "productData": [{"productType": "2", "productId": "300004069084", "productName": "15GB任我看腾讯视频包超值购B"}]}, {"msisdn": "15051600908", "userType": "2", "activeId": "100000195313", "activeName": "前台提醒-提速包优惠活动", "stepId": "20191213142426501541000", "marketTerm": "针对存量未开自动提速包的任我用客户，用户办理任我用优惠提速活动，开通任我用自动提速包，从开通当月起连续2个月，每月可享受减免提速包费用36元优惠（提速包费用36元以内全额减免，超过36元按提速包资费标准收取--3元/G，即每月可免费享受10GB提速流量）。", "productData": [{"productType": "2", "productId": "300004075961", "productName": "任我用提速优惠（2个月）"}]}, {"msisdn": "15051600908", "userType": "2", "activeId": "100000204035", "activeName": "【2020优惠计划】任我用自选套餐流量包70元(全国版)", "stepId": "20191230162032460149000", "marketTerm": "引导用户办理2020优惠计划，每月得20G咪咕视频流量，优惠至2020年底", "productData": [{"productType": "3", "productId": "2000011417", "productName": "任我用自选套餐流量包70元(全国版)（2019优惠计划）"}]}, {"msisdn": "15051600908", "userType": "2", "activeId": "100000216843", "activeName": "苏州2020年拍照保拓复制", "stepId": "20200119111023592796000", "marketTerm": "2020年拍照中高端用户，请优先维护，谨慎办理降收业务", "productData": [{"productType": "", "productId": "", "productName": ""}]}, {"msisdn": "15051600908", "userType": "2", "activeId": "100000230797", "activeName": "任我享20元6GB-6个月", "stepId": "20200225164614252846000", "marketTerm": "高饱和超套超限用户推荐办理6个月任我享活动，原价30元/月，现直降至20元/月，到期自动关闭。", "productData": [{"productType": "2", "productId": "300004073788", "productName": "任我享流量包6GB优惠（6个月）"}]}, {"msisdn": "15051600908", "userType": "2", "activeId": "100000267110", "activeName": "升5G任我用自选流量包120元直降30元-3个月", "stepId": "20200429181616701929000", "marketTerm": "推荐办理120元及以上任我用套餐，每月优惠30元。办理5G任我用自选套餐升档活动（3个月）-升5G任我用自选流量包120元直降30元", "productData": [{"productType": "2", "productId": "300004087083", "productName": "升5G任我用自选流量包120元直降30元（3个月）"}]}, {"msisdn": "15051600908", "userType": "2", "activeId": "100000276861", "activeName": "流量赠送（如已限速需先开提速包）复制", "stepId": "20200525092337841245000", "marketTerm": "目标用户，到厅可结合其他业务营销，额外增加赠送10GB流量卖点。 注：如已限速需先开通提速包，不开提速包赠送流量无法使用。", "productData": [{"productType": "1", "productId": "3002105194", "productName": "2020年苏州幸运用户流量促销"}]}, {"msisdn": "15051600908", "userType": "2", "activeId": "100000277581", "activeName": "语音升档扩容合约48元档", "stepId": "20200526104742500487000", "marketTerm": "客户需办理48元及以上4G自选语音包套餐，额外享受300分钟国内主叫通话（不包含港澳台和国际长途）。活动次月生效，协议期24个月。活动中享受的通话分钟数仅限当月使用，月底清零，不可结转，不可转赠他人使用。协议期内不可以退订或降档语音套餐包。活动到期后若双方均无异议将按照前述优惠政策自动延续24个月，共计延续2次。活动到期后，享受的语音扩容包自动关闭。", "productData": [{"productType": "2", "productId": "300004069102", "productName": "语音升档扩容合约48元档"}]}, {"msisdn": "15051600908", "userType": "2", "activeId": "100000284695", "activeName": "【5G开关辅导打开】5G终端未开5G开关用户", "stepId": "20200605141758592039000", "marketTerm": "5G终端未开5G开关用户，烦请引导开通5G开关，用户开通开关可给以连续2个月每月10G流量，助力5G网络体验。 路径：设置—移动网络—移动数据—启用5G（华为荣耀终端） 设置—SIM卡与流量管理—SIM卡信息与设置—启用5G（OPPO终端） 设置—移动网络—启用5G（VIVO终端） 设置—双卡与移动网络—启用5G网络（小米终端） 设置—连接—移动网络—启用5G网络（三星终端）", "productData": [{"productType": "2", "productId": "300004087011", "productName": "流量感恩活动10GB（2个月）"}]}, {"msisdn": "15051600908", "userType": "2", "activeId": "100000286754", "activeName": "【5G终端用户】服务引导并赠送流量", "stepId": "20200610175526746088000", "marketTerm": "5G终端用户，烦请告知用户您是移动5G手机终端用户，特赠送您当月10G流量，请您保持手机网络设置中的5G开关开通，体验移动最新5G网络体验。", "productData": [{"productType": "2", "productId": "300004076623", "productName": "感恩回馈-10GB"}]}, {"msisdn": "15051600908", "userType": "2", "activeId": "100000289595", "activeName": "车主服务", "stepId": "20200617171011084823000", "marketTerm": "邀您办理车主服务，每月10元享20元 / 20元享40元 车主权益（洗车、加油、停车、滴滴、年检、违章提醒等）", "productData": [{"productType": "", "productId": "", "productName": ""}]}, {"msisdn": "15051600908", "userType": "2", "activeId": "100000294745", "activeName": "2020终端合约199档（和分期/套餐直降）", "stepId": "20200629105432800609000", "marketTerm": "特定用户可参加和分期购机活动199档，适用营销案：2020年苏州和分期购机活动、2020年苏州和分期购智能终端活动、2020年苏州购机套餐优惠活动", "productData": [{"productType": "1", "productId": "**********", "productName": "2020年苏州购机套餐优惠活动"}, {"productType": "1", "productId": "**********", "productName": "2020年苏州和分期购机活动"}, {"productType": "1", "productId": "**********", "productName": "2020年苏州和分期购智能终端活动"}]}, {"msisdn": "15051600908", "userType": "2", "activeId": "100000294776", "activeName": "2020终端合约199档（含冲突等，分期/套餐）", "stepId": "20200629111232995633000", "marketTerm": "特定用户可参加和分期购机活动199档，适用营销案：2020年苏州和分期购机活动、2020年苏州和分期购智能终端活动、2020年苏州购机套餐优惠活动", "productData": [{"productType": "1", "productId": "**********", "productName": "2020年苏州购机套餐优惠活动"}, {"productType": "1", "productId": "**********", "productName": "2020年苏州和分期购机活动"}, {"productType": "1", "productId": "**********", "productName": "2020年苏州和分期购智能终端活动"}]}, {"msisdn": "15051600908", "userType": "2", "activeId": "100000298257", "activeName": "和彩云超值购（前折7折）（2020三季度）", "stepId": "3d1b3d07d61641118d9cd0803326cc37", "marketTerm": "通过本活动开通和彩云高级版可享受一个月功能费7折优惠（5元*0.7=3.5元），活动到期恢复标准资费（5元/月）业务继续保留，请问您需要办理吗？", "productData": [{"productType": "2", "productId": "300004065082", "productName": "手机网盘（和彩云高级版）超值购"}]}, {"msisdn": "15051600908", "userType": "2", "activeId": "100000300135", "activeName": "全品类信用购30-188元档", "stepId": "20200703104742338223000", "marketTerm": "特定用户推荐办理2019全品类产品信用购活动30-3C-188档，优惠金额1210元。", "productData": [{"productType": "1", "productId": "3002105258", "productName": "2019全品类产品信用购活动30-3C"}]}, {"msisdn": "15051600908", "userType": "2", "activeId": "100000300327", "activeName": "全品类信用购20-160元档", "stepId": "20200703152814536284000", "marketTerm": "特定用户推荐办理2019全品类产品信用购活动20-160元档，优惠金额690元。", "productData": [{"productType": "1", "productId": "3002105223", "productName": "2019全品类产品信用购活动20"}]}, {"msisdn": "15051600908", "userType": "2", "activeId": "100000303238", "activeName": "【5G终端用户】服务引导并赠送流量", "stepId": "20200709113430907309000", "marketTerm": "5G终端用户到厅，烦请告知用户您是移动5G手机终端用户，特赠送您两个月，每月各10G流量，请您保持手机网络设置中的5G开关开通，体验移动最新5G网络体验。", "productData": [{"productType": "2", "productId": "300004087011", "productName": "流量感恩活动10GB（2个月）"}]}, {"msisdn": "15051600908", "userType": "2", "activeId": "100000321972", "activeName": "新全品类信用购（存量保拓20）-169元档", "stepId": "20200814140453957604000", "marketTerm": "特定用户推荐办理2020存量保拓信用购活动（直连20）-169档，优惠金额710元。", "productData": [{"productType": "1", "productId": "3100000183", "productName": "2020存量保拓信用购活动（直连20）"}]}, {"msisdn": "15051600908", "userType": "2", "activeId": "100000327409", "activeName": "任我选会员流量包", "stepId": "20200827104012140237000", "marketTerm": "每月13.93元即可享原价19.9元的爱优腾芒任一APP30天会员（每月任选一次），还有15G追剧流量畅用，活动12个月，到期不关", "productData": [{"productType": "2", "productId": "300004092882", "productName": "开任我选会员流量包19.9元享首12个月7折优惠"}]}, {"msisdn": "15051600908", "userType": "2", "activeId": "100000327454", "activeName": "任我选会员", "stepId": "20200827110749984218000", "marketTerm": "每月10元即可享原价15元的爱优腾芒任一APP30天会员（每月任选一次），还有1G追剧流量畅用，活动12个月，到期不关", "productData": [{"productType": "2", "productId": "300004092969", "productName": "开任我选会员15元享首12个月优惠"}]}, {"msisdn": "15051600908", "userType": "2", "activeId": "100000327469", "activeName": "视频彩铃1元季享礼", "stepId": "20200827111434524434000", "marketTerm": "视频彩铃1元享三个月，让等待接听更可视更有趣！到期不关恢复6元", "productData": [{"productType": "", "productId": "", "productName": ""}]}, {"msisdn": "15051600908", "userType": "2", "activeId": "100000328224", "activeName": "5G任我用升档合约120档（12个月直降30元）", "stepId": "20200828085428642102000", "marketTerm": "推荐办理120元及以上任我用套餐，每月优惠30元。办理5G任我用自选套餐升档活动（12个月）-升5G任我用自选流量包120元直降30元", "productData": [{"productType": "2", "productId": "300004086723", "productName": "升5G任我用自选流量包120元直降30元"}]}, {"msisdn": "15051600908", "userType": "2", "activeId": "100000334572", "activeName": "保拓积分合约110-20（视频会员）", "stepId": "278d38b1c84d4e21b96e75ed39265f62", "marketTerm": "邀您办理开任我选会员送2000积分优惠，每月最低消费110元（不达标补齐），次月送价值20元积分，3个月。", "productData": [{"productType": "2", "productId": "300004093041", "productName": "全球通客户5G专享-110元档（视频会员）"}]}]}, "h5GetPackageInfoAllHomeWifi": {"retCode": "0", "retMsg": null, "data": [{"pkgId": "**********", "pkgName": "家庭安防服务月包"}, {"pkgId": "2013000088", "pkgName": "家庭安防服务年包"}, {"pkgId": "2013000030", "pkgName": "智能组网"}, {"pkgId": "2000012991", "pkgName": "30元游戏尊享套餐"}, {"pkgId": "2000012992", "pkgName": "100元教育尊享套餐"}]}, "h5QryOrderedInfo": {"retCode": "0", "retMsg": null, "data": {"mainOffer": {"instanceid": "68001495655035", "offerid": "**********", "offername": "4G自选（预付费）", "isprimary": "Y", "maininstid": "68001495655035", "ismcoffer": "N", "isbundled": "N", "bundleinstid": "", "effectdate": "20191206000000", "expiredate": "21001231000000", "status": "2", "createdate": "20191114191549", "createorderid": "200217878675498799", "modifydate": "20191206095218", "lastmodifyorderid": "200221676700439053", "saleschanneltype": "2", "saleschannelid": "19100001", "beid": "19", "sellobject": "", "attrlist": [{"propinstid": "90032867158909", "propcode": "C_O_MONTHLY_FEE", "iscomplex": "N", "propvalue": "0", "ppropinstid": "", "effectdate": "20191206000000", "expiredate": "21001231000000", "createdate": "20191114191549", "createorderid": "200217878675498799", "modifydate": "20191206095219", "lastmodifyorderid": "200221676700439053"}]}, "subsId": "1946101498369721", "orderedProdList": [{"offerId": "2400000434", "instanceId": "68001941015822", "attrList": [{"attrId": "NORMALFLAG", "value": "1"}]}, {"offerId": "2400000431", "instanceId": "68001941015823", "attrList": [{"attrId": "602017092845", "value": "15061849306"}, {"attrId": "s100300", "value": "1"}, {"attrId": "NORMALFLAG", "value": "1"}]}, {"offerId": "2400000308", "instanceId": "68001702989206", "attrList": null}, {"offerId": "14722004", "instanceId": "68001498005640", "attrList": [{"attrId": "PM_ReleaseRemind", "value": "Y"}]}, {"offerId": "14595140", "instanceId": "68001498005639", "attrList": null}, {"offerId": "14726006", "instanceId": "68001498005641", "attrList": null}, {"offerId": "2380000531", "instanceId": "68001498005638", "attrList": [{"attrId": "PM_FollowParentExpire", "value": "Y"}, {"attrId": "PM_SCORECLASS", "value": "0"}, {"attrId": "PM_SCORE_DEBIT_TYPE", "value": "0"}, {"attrId": "PM_SCORE_DEBIT_AMOUNT", "value": "0"}, {"attrId": "PM_FollowParentEffect", "value": "Y"}, {"attrId": "CREATED_BY_FROM", "value": "1"}, {"attrId": "FROM_PARENT_ORDER_ITEM_ID", "value": "95717877842"}, {"attrId": "FROM_PARENT_INST_ID", "value": "68001498005639"}, {"attrId": "900000000683", "value": "0.70"}]}, {"offerId": "14595139", "instanceId": "68001497874990", "attrList": null}, {"offerId": "14697004", "instanceId": "68001497874991", "attrList": [{"attrId": "PM_ReleaseRemind", "value": "Y"}]}, {"offerId": "14726006", "instanceId": "68001497874992", "attrList": null}, {"offerId": "2100000623", "instanceId": "68001497874989", "attrList": [{"attrId": "PM_FollowParentEffect", "value": "Y"}, {"attrId": "PM_FollowParentExpire", "value": "Y"}, {"attrId": "PM_SCORECLASS", "value": "0"}, {"attrId": "PM_SCORE_DEBIT_TYPE", "value": "0"}, {"attrId": "PM_SCORE_DEBIT_AMOUNT", "value": "0"}, {"attrId": "CREATED_BY_FROM", "value": "1"}, {"attrId": "FROM_PARENT_ORDER_ITEM_ID", "value": "95717872189"}, {"attrId": "FROM_PARENT_INST_ID", "value": "68001497874990"}, {"attrId": "900000000683", "value": "0.70"}]}, {"offerId": "6116019", "instanceId": "68001497554601", "attrList": null}, {"offerId": "12615000", "instanceId": "68001497554600", "attrList": null}, {"offerId": "13750013", "instanceId": "68001497554602", "attrList": [{"attrId": "PM_ReleaseRemind", "value": "Y"}]}, {"offerId": "13786013", "instanceId": "68001497554603", "attrList": null}, {"offerId": "2100000008", "instanceId": "68001495655054", "attrList": null}, {"offerId": "2400000280", "instanceId": "68001495655095", "attrList": null}, {"offerId": "2100000001", "instanceId": "68001495655053", "attrList": null}, {"offerId": "2400000121", "instanceId": "68001495655052", "attrList": null}, {"offerId": "2100000004", "instanceId": "68001495655051", "attrList": null}, {"offerId": "2100000003", "instanceId": "68001495655050", "attrList": null}, {"offerId": "2100000002", "instanceId": "68001495655049", "attrList": null}, {"offerId": "2100000007", "instanceId": "68001495655048", "attrList": null}, {"offerId": "2100000010", "instanceId": "68001495655047", "attrList": null}, {"offerId": "2200005026", "instanceId": "68001495655046", "attrList": null}, {"offerId": "2200005025", "instanceId": "68001495655045", "attrList": null}, {"offerId": "2100000009", "instanceId": "68001495655044", "attrList": null}, {"offerId": "2100000015", "instanceId": "68001495655043", "attrList": null}, {"offerId": "2100000013", "instanceId": "68001495655042", "attrList": null}, {"offerId": "2000009842", "instanceId": "68001495655040", "attrList": [{"attrId": "ToBOSS", "value": "Y"}, {"attrId": "C_O_MONTHLY_FEE", "value": "30000"}, {"attrId": "FROM_PARENT_ORDER_ITEM_ID", "value": "95732066342"}, {"attrId": "FROM_PARENT_INST_ID", "value": "68001501369303"}, {"attrId": "PM_FollowParentEffect", "value": "N"}, {"attrId": "PM_FollowParentExpire", "value": "N"}, {"attrId": "PM_SCORECLASS", "value": "0"}, {"attrId": "PM_SCORE_DEBIT_TYPE", "value": "0"}, {"attrId": "PM_SCORE_DEBIT_AMOUNT", "value": "0"}, {"attrId": "FROM_PARENT_ORDER_ITEM_ID", "value": "95715863100"}, {"attrId": "FROM_PARENT_INST_ID", "value": "68001497554600"}, {"attrId": "900000000685", "value": "10000"}, {"attrId": "900000000685", "value": "30000"}]}, {"offerId": "2400000295", "instanceId": "68001495655041", "attrList": null}, {"offerId": "2000007352", "instanceId": "68001495655038", "attrList": [{"attrId": "FROM_PARENT_ORDER_ITEM_ID", "value": "95715863102"}, {"attrId": "FROM_PARENT_INST_ID", "value": "68001497554601"}, {"attrId": "PM_FollowParentExpire", "value": "N"}, {"attrId": "PM_SCORECLASS", "value": "0"}, {"attrId": "PM_SCORE_DEBIT_TYPE", "value": "0"}, {"attrId": "PM_SCORE_DEBIT_AMOUNT", "value": "0"}, {"attrId": "PM_FollowParentEffect", "value": "N"}]}, {"offerId": "2400000291", "instanceId": "68001495655039", "attrList": null}, {"offerId": "2401000183", "instanceId": "68001495655036", "attrList": [{"attrId": "PassPort", "value": "1"}]}, {"offerId": "**********", "instanceId": "68001495655035", "attrList": [{"attrId": "C_O_MONTHLY_FEE", "value": "0"}]}, {"offerId": "2000001801", "instanceId": "68001495655037", "attrList": [{"attrId": "PM_SUITE_FLAG", "value": "Y"}]}]}}, "h5QrySmartMBandProd": {"retCode": "0", "retMsg": null, "data": {"mustProdList": [{"prodId": "**********", "prodName": "手机代付费宽带", "pkgprodId": "1", "isPackage": "0", "effectType": "0"}, {"prodId": "2011002057", "prodName": "当月免费套餐", "pkgprodId": "1", "isPackage": "0", "effectType": "0"}, {"prodId": "2413000030", "prodName": "智能组网基础服务", "pkgprodId": "1", "isPackage": "0", "effectType": "0"}, {"prodId": "2413000088", "prodName": "家庭安防基础商品", "pkgprodId": "1", "isPackage": "0", "effectType": "0"}], "mbandProdList": [{"prodId": "2000012775", "prodName": "全家WIFI调测费398元", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}, {"prodId": "2000009210", "prodName": "组网产品测试", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}, {"prodId": "2000011811", "prodName": "100元提速包（提至1000M）", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}, {"prodId": "2000011773", "prodName": "30元提速包（提至300M）", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}, {"prodId": "2000009211", "prodName": "150元包月(300M,手机最低消费8元）", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}, {"prodId": "2380000171", "prodName": "快游-移动专版（代网宿收费）", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}, {"prodId": "2000011769", "prodName": "20元提速包（200M提速至300M，家庭融合套餐）", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}, {"prodId": "2000001683", "prodName": "宽带标准资费", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}, {"prodId": "2000002705", "prodName": "12M互联网电视提速", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}, {"prodId": "2000009213", "prodName": "600元包月(1000M,手机最低消费8元）", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}, {"prodId": "2000007856", "prodName": "40元包月(100M,全家最低消费98元）", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}, {"prodId": "2000009212", "prodName": "300元包月(500M,手机最低消费8元）", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}, {"prodId": "2000009173", "prodName": "40元包月(50M,手机最低消费28元）", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}, {"prodId": "2000007763", "prodName": "60元包月(100M,手机最低消费28元）", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}, {"prodId": "2000009174", "prodName": "50元包月(100M,手机最低消费28元）", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}, {"prodId": "2000007855", "prodName": "30元包月(50M,全家最低消费98元）", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}, {"prodId": "2000007754", "prodName": "50元包月(50M,手机最低消费28元）", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}, {"prodId": "2000009057", "prodName": "10元提速包（提至100M）", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}, {"prodId": "2000011444", "prodName": "固话光纤线路", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}, {"prodId": "2000007272", "prodName": "30元包月（50M）", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}, {"prodId": "2000007772", "prodName": "90元包月(200M,手机最低消费28元)", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}, {"prodId": "2000007962", "prodName": "40元包月（100M）", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}, {"prodId": "2000009033", "prodName": "宽带电视优惠包(50M)", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}, {"prodId": "2000011619", "prodName": "30元宽带电视优惠包（个人最低消费58元）", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}, {"prodId": "2000007269", "prodName": "70元包月(50M,手机最低消费8元）", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}, {"prodId": "2000009056", "prodName": "40元包月(100M，家庭副卡用户办理）", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}, {"prodId": "2000007271", "prodName": "20元包月（20M）", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}, {"prodId": "2000009055", "prodName": "30元包月(50M，家庭副卡用户办理）", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}, {"prodId": "2000007270", "prodName": "90元包月(100M,手机最低消费8元）", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}, {"prodId": "2400000979", "prodName": "宽带接入费用", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}, {"prodId": "2000007857", "prodName": "10元提速包（20M提至50M）", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}, {"prodId": "2000007338", "prodName": "40元提速包（提至50M）", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}, {"prodId": "2000009054", "prodName": "20元包月(20M，家庭副卡用户办理）", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}, {"prodId": "2000009034", "prodName": "宽带电视优惠包(100M)", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}, {"prodId": "2000007773", "prodName": "90元提速包(提至200M)", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}, {"prodId": "2000009798", "prodName": "宽带电视优惠包（200M）", "pkgprodId": "1", "isPackage": "0", "effectType": "2"}]}}, "h5qryNetWorkProdPkg": {"retCode": "0", "retMsg": null, "data": [{"pkgId": "2013000030", "pkgName": "智能组网", "busiCode": "securityMonth"}, {"pkgId": "**********", "pkgName": "家庭安防服务月包", "busiCode": "securityYear"}, {"pkgId": "2013000088", "pkgName": "家庭安防服务年包", "busiCode": "securityYear"}]}, "h5smartNetworkQryProd": {"retCode": "0", "retMsg": null, "data": "宽带月套餐费40元，接入速率100M，不限时。套餐费按日分摊，月底补齐。"}, "h5GetBanbInfo": {"retCode": "0", "retMsg": null, "data": {"bandInfo": {"telnum": "13501183724", "rangeType": "6", "districtName": "新民1号点", "addrName": "镇江地区扬中城区新民1号点光交前100米1单元光交前附近1家", "factoryType": "1", "supplyType": "1", "radiusType": "1", "linkMan": "", "contactPhone": "", "gimsUserType": "1", "districtId": "217615", "addrId": "**********", "netWorkType": "1", "limitBandWidth": "20480", "isHaveDevice": null, "gimsAreaType": null}}}, "h5QryOfferingList": {"retCode": "0", "retMsg": null, "data": [{"subsId": "10001", "subsName": "10086语音专线", "accountId": "10002", "prodId": "10003", "prodCode": "10004", "effDate": "2021-02-05 16:32:01", "expDate": "2021-02-05 16:32:01", "createDate": "2021-02-05 16:32:01", "status": "正在使用"}]}, "h5QryTaskPkgPerformList": {"retCode": "0", "retMsg": "success", "data": {"planList": [{"planId": "10001", "planName": "中海营销", "planStartdate": "2021/03/05", "planPerson": "张三/张四/张五", "execStartdate": "2021/03/05", "execPerson": "张三/张四/张五", "finishStatus": "1"}, {"planId": "10002", "planName": "凤凰熙岸营销", "planStartdate": "2021/03/23", "planPerson": "李三/李四/李五", "execStartdate": "2021/03/23", "execPerson": "李三/李四/李五", "finishStatus": "2"}, {"planId": "10003", "planName": "凤凰西街营销", "planStartdate": "2021/04/01", "planPerson": "李三/李四/李五", "execStartdate": "2021/04/01", "execPerson": "王三/王四/王五", "finishStatus": "3"}, {"planId": "10004", "planName": "鼓楼区营销", "planStartdate": "2021/05/14", "planPerson": "李三/李四/李五", "execStartdate": "2021/05/14", "execPerson": "赵三/赵四/赵五", "finishStatus": "4"}, {"planId": "10005", "planName": "中海营销", "planStartdate": "2021/03/05", "planPerson": "张三/张四/张五", "execStartdate": "2021/03/05", "execPerson": "张三/张四/张五", "finishStatus": "1"}, {"planId": "10006", "planName": "凤凰熙岸营销", "planStartdate": "2021/03/23", "planPerson": "李三/李四/李五", "execStartdate": "2021/03/23", "execPerson": "李三/李四/李五", "finishStatus": "2"}, {"planId": "10007", "planName": "凤凰西街营销", "planStartdate": "2021/04/01", "planPerson": "李三/李四/李五", "execStartdate": "2021/04/01", "execPerson": "王三/王四/王五", "finishStatus": "3"}, {"planId": "10008", "planName": "鼓楼区营销", "planStartdate": "2021/05/14", "planPerson": "李三/李四/李五", "execStartdate": "2021/05/14", "execPerson": "赵三/赵四/赵五", "finishStatus": "4"}]}}, "h5QryTaskPkgList": {"retCode": "0", "retMsg": null, "data": {"resultCode": "0", "resultDesc": "成功", "gridId": "123", "taskList": [{"taskpId": "001", "taskpName": "测试包1", "taskpStartdate": "2021/03/30 00:00:00", "taskpEnddate": "2021/04/30 00:00:00", "taskpClassid": "1", "sceneList": [{"cntQ": "2", "cntP": "1", "cntO": "1", "itemClassid": "1", "taskPclassid": "热点营销", "taskSitename": "中海大厦", "describe": "大于三人的一场", "stargetv": "新入网2笔且宽带1笔"}, {"cntQ": "2", "cntP": "1", "cntO": "1", "itemClassid": "2", "taskPclassid": "热点营销", "taskSitename": "中海大厦", "describe": "大于三人的一场", "stargetv": "新入网2笔且宽带1笔"}, {"cntQ": "2", "cntP": "1", "cntO": "1", "itemClassid": "3", "taskPclassid": "热点营销", "taskSitename": "中海大厦", "describe": "大于三人的一场", "stargetv": "新入网2笔且宽带1笔"}, {"cntQ": "2", "cntP": "1", "cntO": "1", "itemClassid": "4", "taskPclassid": "热点营销", "taskSitename": "中海大厦", "describe": "大于三人的一场", "stargetv": "新宽带1笔"}]}, {"taskpId": "001", "taskpName": "测试包1", "taskpStartdate": "2021/03/30 00:00:00", "taskpEnddate": "2021/04/30 00:00:00", "taskpClassid": "1", "sceneList": [{"cntQ": "2", "cntP": "1", "cntO": "1", "itemClassid": "1", "taskPclassid": "热点营销", "taskSitename": "中海大厦", "describe": "大于三人的一场", "stargetv": "新入网2笔且宽带1笔"}, {"cntQ": "2", "cntP": "1", "cntO": "1", "itemClassid": "2", "taskPclassid": "热点营销", "taskSitename": "中海大厦", "describe": "大于三人的一场", "stargetv": "新入网2笔且宽带1笔"}, {"cntQ": "2", "cntP": "1", "cntO": "1", "itemClassid": "3", "taskPclassid": "热点营销", "taskSitename": "中海大厦", "describe": "大于三人的一场", "stargetv": "新入网2笔且宽带1笔"}, {"cntQ": "2", "cntP": "1", "cntO": "1", "itemClassid": "4", "taskPclassid": "热点营销", "taskSitename": "中海大厦", "describe": "大于三人的一场", "stargetv": "新入网2笔且宽带1笔"}]}]}}, "h5GroupInfoByFsop": {"retCode": "0", "retMsg": null, "data": [{"custId": "****************", "groupName": "江苏新大陆软件有限公司", "corpscope": "C", "regStatus": "在网", "groupId": "***********", "address": "<PERSON><PERSON><PERSON>", "linkPhone": "**********", "region": "14", "groupType": "C"}, {"custId": "****************", "groupName": "南京医科大学附属医院第二医院", "corpscope": "D", "regStatus": "在网", "groupId": "***********", "address": "<PERSON><PERSON><PERSON>", "linkPhone": "**********", "region": "14", "groupType": "D"}, {"custId": "****************", "groupName": "华为有限公司", "corpscope": "C", "regStatus": "潜在", "groupId": "***********", "address": "<PERSON><PERSON><PERSON>", "linkPhone": "**********", "groupType": "D"}, {"custId": "****************", "groupName": "焦点集团教育部软件集成在线教育研究院有限公司", "corpscope": "C", "regStatus": "在网", "groupId": "***********", "address": "<PERSON><PERSON><PERSON>", "linkPhone": "**********", "region": "14", "groupType": "A"}]}, "h5QryFrequentContacts": {"retCode": "0", "retMsg": "success", "data": [{"operatorId": "1003", "operatorName": "张三"}, {"operatorId": "1004", "operatorName": "李四"}, {"operatorId": "1005", "operatorName": "王五"}, {"operatorId": "1006", "operatorName": "赵六"}, {"operatorId": "1007", "operatorName": "孙七"}, {"operatorId": "1009", "operatorName": "周八"}]}, "h5SelectFrequentOperator": {"retCode": "0", "retMsg": "success", "data": [{"operatorId": "1003", "operatorName": "张三"}, {"operatorId": "1004", "operatorName": "李四"}, {"operatorId": "1005", "operatorName": "王五"}, {"operatorId": "1006", "operatorName": "赵六"}, {"operatorId": "1007", "operatorName": "孙七"}, {"operatorId": "1009", "operatorName": "周八"}]}, "h5UpdateFrequentOperator": {"retCode": "0", "retMsg": "success", "data": []}, "h5QryGroupList": {"retCode": "0", "retMsg": null, "data": [{"groupId": "10000011", "groupName": "测试AA9", "groupStatus": "2", "groupAddr": "无锡市北塘区人民西路98号3331", "distance": "0", "mpLevel": "1", "createName": "王涛9", "createDate": "2021-04-23 16:40:14"}, {"groupId": "10000012", "groupName": "测试AA8", "groupStatus": "1", "groupAddr": "无锡市北塘区人民西路98号3332", "distance": "54.8", "mpLevel": "1", "createName": "<PERSON>8", "createDate": "2021-04-22 16:40:14"}, {"groupId": "10000013", "groupName": "测试AA7", "groupStatus": "2", "groupAddr": "无锡市北塘区人民西路98号3333", "distance": "146.3", "mpLevel": "1", "createName": "王涛7", "createDate": "2021-04-21 16:40:14"}, {"groupId": "10000014", "groupName": "测试AA5", "groupStatus": "1", "groupAddr": "无锡市北塘区人民西路98号3334", "distance": "185.9", "mpLevel": "1", "createName": "<PERSON>涛6", "createDate": "2021-04-20 16:40:14"}]}, "h5GetGroupInfo": {"retCode": "0", "retMsg": "成功", "data": {"resultCode": "0", "resultDesc": "成功", "groupInfo": {"regionId": "14", "groupId": "19086123", "groupName": "江苏移不动公司", "runSituation": "1", "addrIsConsistent": "1", "regLocation": "注册地址", "officeAddr1": "办公地址1", "officeAddr2": "办公地址2", "officeAddr3": "办公地址3", "officeAddr4": "办公地址4", "officeAddr5": "办公地址5", "officeAddr6": "办公地址6", "officeAddr7": "办公地址7", "officeAddr8": "办公地址8", "officeAddr9": "办公地址9", "photoList": [{"photoFlag": "1", "photoUrl": "https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fcdn.duitang.com%2Fuploads%2Fitem%2F201410%2F20%2F20141020162058_UrMNe.jpeg&refer=http%3A%2F%2Fcdn.duitang.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=jpeg?sec=1622277860&t=7af90de752b2b9eb053669ba4234e112"}, {"photoFlag": "1", "photoUrl": "https://ss1.baidu.com/9vo3dSag_xI4khGko9WTAnF6hhy/zhidao/pic/item/c9fcc3cec3fdfc0373893c48d53f8794a5c22649.jpg"}, {"photoFlag": "2", "photoUrl": "https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fnimg.ws.126.net%2F%3Furl%3Dhttp%253A%252F%252Fdingyue.ws.126.net%252F2021%252F0427%252F76b8ab4bj00qs7otj0017c000c800g1c.jpg%26thumbnail%3D650x2147483647%26quality%3D80%26type%3Djpg&refer=http%3A%2F%2Fnimg.ws.126.net&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=jpeg?sec=1622277992&t=81d7b12e23aa66e28f34b337c632db22"}, {"photoFlag": "2", "photoUrl": "https://img2.baidu.com/it/u=3681880960,455182084&fm=26&fmt=auto&gp=0.jpg"}], "realStaffCnt": "31", "industryPhyCode": "采矿业", "cate1Code": "石油和天然气开采业", "cate2Code": "石油开采", "cate3Code": "其他石油开采", "memberList": [{"name": "王涛", "mobile": "***********", "roleType": "1", "memberType": "12"}, {"name": "涛涛", "mobile": "13901582506", "roleType": "2", "memberType": "25"}], "busiList": [{"busiName": "小企业", "telecomCarrier": "YD", "expireDate": "20210426", "monthlyFee": "200"}, {"busiName": "移不动企业", "telecomCarrier": "LT", "expireDate": "20210326", "monthlyFee": "100"}]}}}, "h5SyncGroupMpInfo": {"retCode": "0", "retMsg": "成功", "data": null}, "h5insetCommonInfo": {"retCode": "0", "retMsg": "插入订单信息表成功", "data": {"staffId": "1", "crmId": "14150759", "region": "14", "orgId": "12", "telnum": "***********", "orderId": "****************", "channelId": "900017"}}, "h5submitSecondAudit": {"retCode": "0", "retMsg": null, "data": {"creationCode": "1", "reason": "创建不成功原因"}}, "h5qrySecondAuditInfo": {"retCode": "0", "retMsg": "查询成功", "data": {"operatorInfo": {"opratorName": "肖凡", "orgId": "********", "orgName": "省公司镇江维护测试营业厅", "region": "**********", "cityName": "南京", "countryId": "1842", "countryName": "镇江市区", "channeltypecode": "**************"}, "bankList": [{"id": "00", "label": "移动公司"}, {"id": "01", "label": "邮储"}, {"id": "02", "label": "人民银行"}, {"id": "03", "label": "工商银行"}, {"id": "04", "label": "建设银行"}, {"id": "05", "label": "农业银行"}, {"id": "06", "label": "交通银行"}, {"id": "07", "label": "中国银行"}, {"id": "08", "label": "兴业银行"}, {"id": "09", "label": "农村信用社"}, {"id": "10", "label": "城市信用社"}, {"id": "11", "label": "农业发展银行"}, {"id": "12", "label": "其它A银行 "}, {"id": "13", "label": "其它B银行 "}, {"id": "14", "label": "信托投资公司"}, {"id": "15", "label": "合作银行"}, {"id": "16", "label": "商业银行"}, {"id": "17", "label": "中信实业银行"}, {"id": "18", "label": "华夏银行"}, {"id": "19", "label": "上海浦发银行"}, {"id": "20", "label": "光大银行"}, {"id": "21", "label": "财务公司"}, {"id": "22", "label": "民生银行"}, {"id": "23", "label": "招行"}, {"id": "24", "label": "深圳发展银行"}, {"id": "25", "label": "江阴浦发银行"}, {"id": "26", "label": "招行(信用卡)"}, {"id": "27", "label": "南通招行"}, {"id": "28", "label": "广东发展银行"}, {"id": "29", "label": "锡洲农村商业银行"}, {"id": "30", "label": "泰州农村合作银行"}, {"id": "31", "label": "汇丰银行"}, {"id": "32", "label": "邮政"}, {"id": "33", "label": "江苏银行"}, {"id": "34", "label": "建连银行"}, {"id": "35", "label": "友利银行"}], "payList": [{"id": "00", "label": "现金到账"}, {"id": "01", "label": "倒轧支票"}, {"id": "02", "label": "顺轧支票"}, {"id": "03", "label": "银联商务POS刷卡"}, {"id": "04", "label": "银行POS刷卡"}, {"id": "05", "label": "前台小键盘（江苏银联）"}, {"id": "06", "label": "自助银联卡（江苏银联）"}, {"id": "07", "label": "电子缴存"}, {"id": "08", "label": "银联商务MISPOS刷卡"}, {"id": "09", "label": "浦发POS刷卡"}]}}, "h5qrySecondAuditDetail": {"retCode": "0", "retMsg": null, "data": {"infoList": [{"createDate": "20201223140000", "settleOid": "95000241582997", "department": "********", "createOperId": "********", "shouldFee": "0", "aujastFee": "0", "factFee": "0", "status": "faWait", "auditOperId": "", "remark": "营业员自动扎帐"}, {"createDate": "**************", "settleOid": "**************", "department": "********", "createOperId": "********", "shouldFee": "125000", "aujastFee": "-32192", "factFee": "92808", "status": "faPass", "auditOperId": "********", "remark": "错误"}, {"createDate": "**************", "settleOid": "**************", "department": "********", "createOperId": "********", "shouldFee": "0", "aujastFee": "1000", "factFee": "1000", "status": "faBack", "auditOperId": "********", "remark": "测退回"}], "bankList": [{"bankType": "05", "bankAccount": "************", "bankId": "*********", "accountName": "响水农行"}, {"bankType": "03", "bankAccount": "************", "bankId": "*********", "accountName": "大丰工行营业部大丰工行营业部大丰工行营业部大丰工行营业部大丰工行营业部大丰工行营业部"}, {"bankType": "05", "bankAccount": "************", "bankId": "*********", "accountName": "滨海农行分理处"}, {"bankType": "03", "bankAccount": "************", "bankId": "*********", "accountName": "工商银行东台分行"}, {"bankType": "03", "bankAccount": "************", "bankId": "*********", "accountName": "射阳工商银行"}, {"bankType": "04", "bankAccount": "************", "bankId": "*********", "accountName": "建设银行阜宁支行"}, {"bankType": "03", "bankAccount": "************", "bankId": "*********", "accountName": "建湖工行营业部"}], "interfaceSeq": "****************"}}, "h5qryInvAudChk": {"retCode": "0", "retMsg": null, "data": {"isBlacklist": "0", "isMaterial": "1", "retMsg": "成功", "retCode": "0", "interfaceSeq": "****************", "orgId": "********"}}, "h5qrySimDataInfo": {"retCode": "0", "retMsg": null, "data": {"audit_day": "********", "city_id": "12", "city_name": "淮安", "area_id": "1210", "area_name": "淮安市", "agent_id": "********", "agent_name": "省公司淮安维护测试营业厅1-3", "simInfoList": [{"category_id": "rsclSgsm", "category_name": "实体卡", "res_type_id": "rsclSgsm_A3", "sim_type_name": "M2M MP1 USIM卡-2019", "begin_remain_num": "74596", "in_up": "0", "out_up": "0", "out_down": "0", "in_down": "0", "end_remain_num": "64494", "check_more_amount": "0", "check_less_amount": "0", "sale_out": "0", "sale_used": "0", "begin_onway": "0", "end_onway": "0", "invalid_num": "0", "other_in": "0", "other_out": "10102", "is_sold": "0", "audit_stock_amout": null, "remark": null}, {"category_id": "rsclWgsmB_00", "category_name": "空白卡", "res_type_id": "rsclWgsmB_00_91", "sim_type_name": "USIM卡-三切", "begin_remain_num": "0", "in_up": "0", "out_up": "0", "out_down": "0", "in_down": "0", "end_remain_num": "0", "check_more_amount": "0", "check_less_amount": "0", "sale_out": "0", "sale_used": "0", "begin_onway": "0", "end_onway": "0", "invalid_num": "0", "other_in": "0", "other_out": "0", "is_sold": "1", "audit_stock_amout": null, "remark": null}], "interfaceSeq": "2022010510910390"}}, "h5qryValuableDataInfo": {"retCode": "0", "retMsg": null, "data": {"audit_day": "20170808", "city_id": "12", "city_name": "淮安", "area_id": "1210", "area_name": "淮安市", "agent_id": "********", "agent_name": "省公司淮安维护测试营业厅1-3", "valuableDataInfoList": [{"card_status": "LOSE_CARD", "card_status_name": "挂失卡", "res_type_id": "rsclR.VC.31.1.100", "res_type_name": "1000元手机充值卡（100元）新", "card_value": "100", "begin_remain_num": "16", "in_up": "2", "out_down": "5", "out_up": "4", "sale_out": "7", "in_down": "3", "other_in": "8", "other_out": "9", "begin_onway": "12", "end_onway": "13", "invalid_num": "6", "end_remain_num": "0", "full_amount": "26523", "audit_stock_amout": "1", "check_more_amount": "14", "check_less_amount": "15", "remark": null, "audit_oper_id": "14150759"}, {"card_status": "UNLOSE_CARD", "card_status_name": "正常卡", "res_type_id": "rsclR.VC.31.1.100", "res_type_name": "1000元手机充值卡（100元）新", "card_value": "100", "begin_remain_num": "16", "in_up": "2", "out_down": "5", "out_up": "4", "sale_out": "7", "in_down": "3", "other_in": "8", "other_out": "9", "begin_onway": "12", "end_onway": "13", "invalid_num": "6", "end_remain_num": "0", "full_amount": "26522", "audit_stock_amout": "1", "check_more_amount": "14", "check_less_amount": "15", "remark": null, "audit_oper_id": "14150759"}, {"card_status": "UNLOSE_CARD", "card_status_name": "正常卡", "res_type_id": "rsclR.VC.31.100", "res_type_name": "手机充值卡-普通_100", "card_value": "100", "begin_remain_num": "221", "in_up": "0", "out_down": "0", "out_up": "0", "sale_out": "1", "in_down": "0", "other_in": "0", "other_out": "1", "begin_onway": "0", "end_onway": "0", "invalid_num": "0", "end_remain_num": "219", "full_amount": "5820", "audit_stock_amout": "218", "check_more_amount": "0", "check_less_amount": "0", "remark": "测试", "audit_oper_id": "14150759"}, {"card_status": "UNLOSE_CARD", "card_status_name": "正常卡", "res_type_id": "rsclR.VC.31.30", "res_type_name": "手机充值卡-普通_30", "card_value": "30", "begin_remain_num": "1", "in_up": "0", "out_down": "0", "out_up": "0", "sale_out": "1", "in_down": "0", "other_in": "0", "other_out": "1", "begin_onway": "0", "end_onway": "0", "invalid_num": "0", "end_remain_num": "1", "full_amount": "26526", "audit_stock_amout": "1", "check_more_amount": "0", "check_less_amount": "0", "remark": null, "audit_oper_id": "14150759"}, {"card_status": "UNLOSE_CARD", "card_status_name": "正常卡", "res_type_id": "rsclR.VC.31.50", "res_type_name": "手机充值卡-普通_50", "card_value": "50", "begin_remain_num": "1", "in_up": "0", "out_down": "0", "out_up": "0", "sale_out": "1", "in_down": "0", "other_in": "0", "other_out": "1", "begin_onway": "0", "end_onway": "0", "invalid_num": "0", "end_remain_num": "1", "full_amount": "26524", "audit_stock_amout": "1", "check_more_amount": "0", "check_less_amount": "0", "remark": null, "audit_oper_id": "14150759"}, {"card_status": "UNLOSE_CARD", "card_status_name": "正常卡", "res_type_id": "rsclR.VC.31.1.50", "res_type_name": "手机充值卡（50元）201512", "card_value": "50", "begin_remain_num": "1", "in_up": "0", "out_down": "0", "out_up": "0", "sale_out": "1", "in_down": "0", "other_in": "0", "other_out": "1", "begin_onway": "0", "end_onway": "0", "invalid_num": "0", "end_remain_num": "1", "full_amount": "26521", "audit_stock_amout": "1", "check_more_amount": "0", "check_less_amount": "0", "remark": "测试123", "audit_oper_id": "14150759"}], "interfaceSeq": "2022010510910391"}}, "h5QryNoNumberCustomer": {"retCode": "0", "retMsg": "【2021102110818879-FSOP2BDS1551-资料完善查询接口】CRM接口提示：Success", "data": {"custList": [{"custid": "129910004030710134", "custname": "黄嘉良黄嘉良", "certtype": "IdCard", "certid": "321183199207170510", "certadd": "江苏省南京市江苏省南京市", "certstartdate": "21000101", "certenddate": "21000101"}, {"custid": "129910004030710112", "custname": "黄嘉良", "certtype": "IdCard", "certid": "321183199207170510", "certadd": "江苏省南京市", "certstartdate": "20990801", "certenddate": "20990801"}]}}, "h5UpdNoNumberCustomer": {"retCode": "0", "retMsg": "【2021090710769575-FSOP2BDS1552-资料完善修改接口】CRM接口提示：success", "data": {"outSrl": "2021090710769575", "orderId": "2020092301010"}}, "h5qryServiceFeeCmop": {"retCode": "0", "retMsg": null, "data": {"daiweiId": "1", "daiweiName": null, "money": "123", "region": "14", "currentMonth": "1", "fee": "444", "score": "80", "starLevel": "4"}}, "h5ProdCommodityQuery": {"retCode": "0", "retMsg": null, "data": {"offerId": "100000010000029801100000010000029801100000010000029801", "offerName": "跨省家庭功能商品跨省家庭功能商品跨省家庭功能商品跨省家庭功能商品", "offerType": "0", "goodsInfo": "阿瓦撒旦发生大奥比岛喀什的黄金卡的哈商大是可见的哈桑了解卡莎宽松的环境阿克苏喝了口酒楼上的卢卡斯等哈数据库里贺卡送进来的卡拉是京东卡省的健康和啊数据库和大家说大苏打回家结婚三大件阿克苏大厦</", "offerStatus": "3"}}, "h5OnOrderQry": {"retCode": "0", "retMsg": null, "data": {"saleStep3": {"telnum": "13585058303", "offerCode": "2000009763", "activeId": "100000544434", "stepId": "20211123163834845373000", "marketingLink": "3"}, "saleStep1": {"telnum": "13585058303", "offerCode": "2000011811", "activeId": "100000544434", "stepId": "20211123163834845373000", "marketingLink": "1"}, "saleStep2": {"telnum": "13585058303", "offerCode": "2000011773", "activeId": "100000544434", "stepId": "20211123163834845373000", "marketingLink": "2"}}}, "h5MarketRealTimeMatchingPush": {"retCode": "0", "retMsg": null, "data": {"activityName": "关键时刻关怀-5G终端无流量客户", "activityId": "100000544434", "activityType": "9", "timeId": "0", "goodsID": "2000011811", "activityChannel": "67", "activityStartTime": "20210201", "activityEndTime": "20211130", "stepId": "20211123163834845373000", "interfaceSeq": "2022011210923186", "prodList": [{"productType": "3", "productId": "1001", "productIdExp1": "1", "productIdExp2": "1", "productIdExp3": "1", "productIdExp4": "1", "productIdExp5": "测试名称1", "productIdExp6": "1", "productIdExp7": "1"}, {"productType": "3", "productId": "1002", "productIdExp1": "1", "productIdExp2": "1", "productIdExp3": "1", "productIdExp4": "1", "productIdExp5": "测试名称2", "productIdExp6": "1", "productIdExp7": "1"}]}}, "h5getCustomerBusinessOrderList": {"retCode": "0", "retMsg": null, "data": [{"srvNumber": "***********", "processWOID": "BES202109231849_011", "acptChnlID": "0", "acptTime": "2021-09-23 19:08:47", "customID": "", "customType": "0", "productID": "宽带新装,设备(家庭网关新装)", "node": "4", "status": "2", "alarmStatus": "1", "processList": [{"processWOID": "BES202109231849_011", "processWONode": "0", "staffName": "测试", "staffNO": "23090015", "bizCode": "0000", "bizDesc": "", "feedbackTime": "2021-09-23 19:08:47"}, {"processWOID": "BES202109231849_011", "processWONode": "1", "staffName": "测试", "staffNO": "23090015", "bizCode": "0000", "bizDesc": "", "feedbackTime": "2021-09-23 19:08:47"}, {"processWOID": "BES202109231849_011", "processWONode": "2", "staffName": "刘大亮", "staffNO": "***********", "bizCode": "0000", "bizDesc": "", "feedbackTime": "2021-09-23 19:09:38"}, {"processWOID": "BES202109231849_011", "processWONode": "3", "staffName": "刘大亮", "staffNO": "***********", "bizCode": "0000", "bizDesc": "", "feedbackTime": "2021-09-23 19:09:43"}, {"processWOID": "BES202109231849_011", "processWONode": "4", "staffName": "测试", "staffNO": "23090015", "bizCode": "0000", "bizDesc": "", "feedbackTime": "2021-09-23 19:09:49"}]}, {"srvNumber": "***********", "processWOID": "BES202109231847_011", "acptChnlID": "0", "acptTime": "2021-09-23 18:48:33", "customID": "", "customType": "0", "productID": "宽带新装,设备(家庭网关新装)", "node": "4", "status": "2", "alarmStatus": "1", "processList": [{"processWOID": "BES202109231847_011", "processWONode": "0", "staffName": "测试", "staffNO": "23090015", "bizCode": "0000", "bizDesc": "", "feedbackTime": "2021-09-23 18:48:33"}]}, {"srvNumber": "***********", "processWOID": "BES202109231847_011", "acptChnlID": "0", "acptTime": "2021-09-23 18:48:33", "customID": "", "customType": "0", "productID": "宽带新装,设备(家庭网关新装)", "node": "4", "status": "2", "alarmStatus": "1", "processList": null}, {"srvNumber": "***********", "processWOID": "BES202109231847_011", "acptChnlID": "0", "acptTime": "2021-09-23 18:48:33", "customID": "", "customType": "0", "productID": "宽带新装,设备(家庭网关新装)", "node": "4", "status": "2", "alarmStatus": "1", "processList": []}, {"srvNumber": "***********", "processWOID": "BES202109231847_011", "acptChnlID": "0", "acptTime": "2021-09-23 18:48:33", "customID": "", "customType": "0", "productID": "宽带新装,设备(家庭网关新装)", "node": "4", "status": "2", "alarmStatus": "1", "processList": null}]}, "h5GetProductListCard": {"retCode": "0", "retMsg": null, "data": [{"offerId": "2620190326", "offerName": "授权认证", "becode": null, "status": null, "packageId": null, "createDate": null, "packageType": null, "idcardFlag": null}, {"offerId": "2620190327", "offerName": "授权认证2", "becode": null, "status": null, "packageId": null, "createDate": null, "packageType": null, "idcardFlag": null}, {"offerId": "2620190328", "offerName": "授权认证3", "becode": null, "status": null, "packageId": null, "createDate": null, "packageType": null, "idcardFlag": null}, {"offerId": "2620190329", "offerName": "授权认证4", "becode": null, "status": null, "packageId": null, "createDate": null, "packageType": null, "idcardFlag": null}, {"offerId": "2620190330", "offerName": "授权认证5", "becode": null, "status": null, "packageId": null, "createDate": null, "packageType": null, "idcardFlag": null}]}, "h5getCoverAddress": {"retCode": "0", "retMsg": "success", "data": {"netCoverInfo": [{"addressName": "虎踞路1", "fullName": "江苏省南京市鼓楼区虎踞路", "addressId": "1", "state": "1", "level": "1", "rank": "1"}, {"addressName": "华南路", "fullName": "江苏省南京市鼓楼区华南路", "addressId": "2", "state": "3", "level": "1", "rank": "1"}, {"addressName": "虎踞路2", "fullName": "江苏省南京市鼓楼区虎踞路", "addressId": "1", "state": "2", "level": "1", "rank": "10"}, {"addressName": "华南路", "fullName": "江苏省南京市鼓楼区华南路", "addressId": "2", "state": "3", "level": "1", "rank": "1"}, {"addressName": "虎踞路", "fullName": "江苏省南京市鼓楼区虎踞路", "addressId": "1", "state": "4", "level": "1", "rank": "1"}, {"addressName": "华南路", "fullName": "江苏省南京市鼓楼区华南路", "addressId": "2", "state": "3", "level": "1", "rank": "1"}, {"addressName": "虎踞路", "fullName": "江苏省南京市鼓楼区虎踞路", "addressId": "1", "state": "3", "level": "1", "rank": "1"}]}}, "h5QryBuildViewOppList": {"retCode": "0", "retMsg": "成功", "data": {"streamSeq": "20200606141834234", "cntDue": "20", "cntDiff": "18", "cntHome": "12", "groupInfo": [{"managerId": "14150759", "managerName": "王涛", "cntGroup": "12"}, {"managerId": "14150733", "managerName": "张三", "cntGroup": "10"}, {"managerId": "14150734", "managerName": "李四", "cntGroup": "12"}, {"managerId": "14150735", "managerName": "王五", "cntGroup": "21"}, {"managerId": "14150736", "managerName": "赵六", "cntGroup": "22"}, {"managerId": "14150737", "managerName": "钱七", "cntGroup": "42"}, {"managerId": "14150738", "managerName": "孙八", "cntGroup": "11"}, {"managerId": "14150739", "managerName": "吴九", "cntGroup": "1"}, {"managerId": "14150710", "managerName": "郑十", "cntGroup": "4"}, {"managerId": "14150711", "managerName": "富贵", "cntGroup": "6"}, {"managerId": "14150712", "managerName": "阿大", "cntGroup": "2"}]}}, "h5QueryBillNew": {"retCode": "0", "retMsg": "success", "data": {"bill": [{"acctid": "1423200004548218", "subsid": "1423200004953010", "cycle": "202105", "totalfee": "46500", "otherpay": "0", "grouppay": "0", "totalbusiamout": "0", "marketamount": "0", "realamount": "46500", "feedetail_list": [{"product_id": "2000011619", "feetypeid": "AFA1", "feename": "30元宽带电视优惠包（个人最低消费58元）", "org_fee": "19200", "dis_fee": "-12800", "fee": "6400", "type": "1", "leveldbiid": "AA", "leveldbiname": "套餐及固定费", "dsp_order": "1"}, {"product_id": "**********", "feetypeid": "AAN0", "feename": "短信呼(1元)", "org_fee": "1000", "dis_fee": "0", "fee": "1000", "type": "0", "leveldbiid": "AA", "leveldbiname": "套餐及固定费", "dsp_order": "1"}, {"product_id": "2000001586", "feetypeid": "AAAH", "feename": "1元包本地主叫200分钟(09版集团套餐)", "org_fee": "600", "dis_fee": "0", "fee": "600", "type": "0", "leveldbiid": "AA", "leveldbiname": "套餐及固定费", "dsp_order": "1"}, {"product_id": "2000007543", "feetypeid": "AAPV", "feename": "4G自选语音包8元B", "org_fee": "5200", "dis_fee": "0", "fee": "5200", "type": "1", "leveldbiid": "AA", "leveldbiname": "套餐及固定费", "dsp_order": "1"}, {"product_id": "2000011803", "feetypeid": "AAVK", "feename": "畅享自选套餐流量包100元", "org_fee": "65600", "dis_fee": "-32800", "fee": "32800", "type": "1", "leveldbiid": "AA", "leveldbiname": "套餐及固定费", "dsp_order": "1"}, {"product_id": "0", "feetypeid": "AB21", "feename": "基本通话费", "org_fee": "500", "dis_fee": "0", "fee": "500", "type": "0", "leveldbiid": "AB", "leveldbiname": "套餐外语音费", "dsp_order": "2"}]}], "busi": []}}, "h5QueryOfferingInfo": {"retCode": "0", "retMsg": "success", "data": {"offeringDesc": "月套餐费100元，包含当月30GB国内移动数据流量（不含港澳台），超出套餐后国内移动数据流量按照0.29元/MB计费(流量费用达到5元后停止收费，可免费使用流量至1GB），3GB以内按此规则收费（即每超出1GB按照5元/GB收费）；超过3GB后，按照0.29元/MB计费(流量费用达到3元后停止收费，可免费使用流量至1GB），其后继续使用按此规则收费（即每超出1GB按照3元/GB收费）。手机客户使用本套餐当月剩余移动数据流量，可结转至次月使用，结转流量次月月底失效，如变更套餐，该套餐当月剩余流量无法结转至下月使用。开通本套餐后，遵循套餐使用补充规则和手机上网安全约定。符合业务规则的本套餐用户可使用移动副卡/分享卡和多终端共享，收费标准见网掌营业厅资费公示。订购首月套餐费和套餐优惠按实际天数折算。本套餐费/固定费平摊至每日收取。", "offeringCode": "2000011803"}}, "h5GetUpgradeOrderInfoBo": {"retCode": "0", "retMsg": "", "data": [{"orderId": "121313", "createTime": "2020/0702 13:58", "serviceNumber": "***********", "offeringName": "50元宽带", "offeringId": "34324", "address": "江苏省南京市鼓楼区中海大厦", "groupName": "新大陆", "groupId": "23123", "contractName": "新大陆合同", "contractId": "154646", "agentLink": "***********", "state": "0", "devices": [{"deviceName": "家庭版", "deviceAmount": "50"}, {"deviceName": "网关版", "deviceAmount": "50"}]}]}, "h5QryTreeList": {"retCode": "0", "retMsg": "【BMMC484-树节点查询-2021121010880587】接口调用成功。", "data": {"body": {"firstKey": "11180507000242981", "dataList": [{"type": "2", "key": "11180507000242981", "level": "1", "isRealia": "1", "flag": "1", "childNum": "1", "childCnt": "1/5", "org": "首都银行", "label": "首都银行(中国)有限公司", "isLeaf": "false"}]}, "pageInfo": {"pageSize": "10", "currentPage": "15", "totalSize": "100", "totalPage": "10"}}}, "h5QryDataList": {"retCode": "0", "retMsg": "【BMMC484-树节点查询-2021121010880587】接口调用成功。", "data": {"basicInfo": {"orgName": "建邺沙洲网格", "cityName": "南京市", "regLocation": "江苏省南京市建邺区江东中路347号国金中心办公楼一期2103-2111室", "tel": "025-68584134", "managerName": "俞岚,周聪", "customerName": "首都银行", "countyName": "建邺区", "legalPersonName": "郑康为"}, "customerInfo": {"prod_1_value": "千里眼,车务通,云专线,云MAS,CDN", "prod_2_value": "和教育,一网通,ICT,公有云,语音专线", "prod_3_value": "语音专线", "prod_4_value": "语音专线", "prod_desc": null, "fee_1_value": "107687.42", "fee_2_value": "3000", "fee_3_value": "3000", "fee_4_value": "3000", "fee_desc": "集团收入规模", "mar_1_value": "10.81%", "mar_2_value": "20.00%", "mar_3_value": "33.33%", "mar_4_value": "100.00%", "mar_desc": "存量集团占清单集团的占比（百分比形式展示）", "max_1_name": "江苏江阴农村商业银行股份有限公司,江苏江阴农村商业银行股份有限公司（短信）,江苏江阴农村商业银行股份有限公司（数据）", "max_1_value": "585677.4697", "max_2_name": "首都银行（中国）有限公司", "max_2_value": "3000", "max_3_name": "首都银行（中国）有限公司", "max_3_value": "3000", "max_4_name": "首都银行（中国）有限公司", "max_4_value": "3000", "max_desc": "不同维度中，收入最高的企业", "rank_1_value": "1.0000", "rank_2_value": "1.0000", "rank_desc": "此集团在同级的收入排名"}, "trendIncom": [{"month": "2020/11", "value1": "0", "value2": "0"}, {"month": "2020/12", "value1": "0", "value2": "0"}, {"month": "2021/01", "value1": "0", "value2": "0"}, {"month": "2021/02", "value1": "0", "value2": "0"}, {"month": "2021/03", "value1": "0", "value2": "0"}, {"month": "2021/04", "value1": "0", "value2": "0"}, {"month": "2021/05", "value1": "0", "value2": "0"}, {"month": "2021/06", "value1": "3000", "value2": "3000"}, {"month": "2021/07", "value1": "3000", "value2": "3000"}, {"month": "2021/08", "value1": "3000", "value2": "3000"}, {"month": "2021/09", "value1": "3000", "value2": "3000"}, {"month": "2021/10", "value1": "3000", "value2": "3000"}], "top5": [{"prodName": "集团V网业务(单位成员组网,内部通话优惠)", "cnt": "2"}, {"prodName": "语音专线", "cnt": "1"}, {"prodName": "集团多媒体桌面电话", "cnt": "1"}, {"prodName": "基础口令", "cnt": "1"}, {"prodName": "移动云", "cnt": "1"}], "sameLevelLand": [{"month": "2020/11", "value": "0"}, {"month": "2020/12", "value": "0"}, {"month": "2021/01", "value": "0"}, {"month": "2021/02", "value": "0"}, {"month": "2021/03", "value": "0"}, {"month": "2021/04", "value": "0"}, {"month": "2021/05", "value": "0"}, {"month": "2021/06", "value": "0"}, {"month": "2021/07", "value": "6"}, {"month": "2021/08", "value": "6"}, {"month": "2021/09", "value": "6"}, {"month": "2021/10", "value": "6"}], "sameArrearsTrend": [{"month": "2021/10/31", "value": "0"}, {"month": "2021/11/01", "value": "0"}, {"month": "2021/11/02", "value": "0"}, {"month": "2021/11/03", "value": "3000"}, {"month": "2021/11/04", "value": "3000"}, {"month": "2021/11/05", "value": "3000"}, {"month": "2021/11/06", "value": "3000"}, {"month": "2021/11/07", "value": "3000"}, {"month": "2021/11/08", "value": "3000"}, {"month": "2021/11/09", "value": "3000"}, {"month": "2021/11/10", "value": "3000"}, {"month": "2021/11/11", "value": "3000"}, {"month": "2021/11/12", "value": "3000"}, {"month": "2021/11/13", "value": "3000"}, {"month": "2021/11/14", "value": "3000"}, {"month": "2021/11/15", "value": "3000"}, {"month": "2021/11/16", "value": "3000"}, {"month": "2021/11/17", "value": "3000"}, {"month": "2021/11/18", "value": "3000"}, {"month": "2021/11/19", "value": "3000"}, {"month": "2021/11/20", "value": "3000"}, {"month": "2021/11/21", "value": "3000"}, {"month": "2021/11/22", "value": "3000"}, {"month": "2021/11/23", "value": "0"}, {"month": "2021/11/24", "value": "0"}, {"month": "2021/11/25", "value": "0"}, {"month": "2021/11/26", "value": "0"}, {"month": "2021/11/27", "value": "0"}, {"month": "2021/11/28", "value": "0"}, {"month": "2021/11/29", "value": "0"}]}}, "h5QueryReport": {"retCode": "0", "retMsg": "【BMMC485-报表查询-2021120910876912】接口调用成功。", "data": {"body": {"dataList": [{"regionId": "1000250", "regionName": "南京市", "level": "2", "mea1": "1411", "mea2": "194", "mea3": "1217", "mea4": "6818199.2248", "mea5": "32", "mea6": "14", "mea7": "18", "mea8": "3786751.809", "mea9": "84", "mea10": "29", "mea11": "55", "mea12": "3006561.1144", "mea13": "207", "mea14": "47", "mea15": "160", "mea16": "17890.3014", "mea17": "1088", "mea18": "104", "mea19": "984", "mea20": "6996", "order_ID": "1"}]}}}}