{"h5qryOpenService": {"retCode": "0", "retMsg": "success", "data": {"prodList": [{"prodid": "2401000183", "prodname": "互联网基础功能", "startdate": "2014-02-21", "enddate": "2100-12-31", "ispakage": "0", "pakagename": "", "prodtype": "I", "pakageid": ""}, {"prodid": "2400000037", "prodname": "WLAN业务（WEB认证方式）", "startdate": "2011-01-07", "enddate": "2100-12-31", "ispakage": "0", "pakagename": "", "prodtype": "I", "pakageid": ""}], "marketList": [{"actid": "3002099189", "actname": "2017年南京4G用户存量保有活动", "levelid": "300004000653", "levelname": "充50送240（12个月）", "startdate": "2019-03-01", "enddate": "2020-03-01", "markettype": ""}, {"actid": "3002099189", "actname": "2017年南京4G用户存量保有活动", "levelid": "300004000653", "levelname": "充50送240（12个月）", "startdate": "2019-03-01", "enddate": "2020-03-01", "markettype": ""}, {"actid": "3002102003", "actname": "2019年南京市宽带电视优惠活动", "levelid": "300004037608", "levelname": "月返20_12个月_50元及以上", "startdate": "2019-03-01", "enddate": "2020-03-01", "markettype": ""}]}}, "h5qryOpenServiceDesc": {"retCode": "0", "retMsg": "success", "data": {"prodDesc": "1.充50送120，50元立即到账，协议期12个月，每月释放10元，用户需保证协议期内开通20元及以上自选流量包（含不限量）不关闭。2.充50送240，50元立即到账，协议期12个月，每月释放20元，用户需要保证协议期内开通50元及以上自选流量包（含不限量）不关闭。"}}}