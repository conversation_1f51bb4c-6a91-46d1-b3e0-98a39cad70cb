{"h5QryNormItems": {"retCode": "0", "retMsg": null, "data": {"msisdn": "13675177811", "orgaId": "14551234", "indexInfos": [{"indexCode": "10001", "indexName": "新增宽带"}, {"indexCode": "10002", "indexName": "新增互联网电视"}]}}, "h5QrySingleNormData": {"retCode": "0", "retMsg": null, "data": {"msisdn": "13675177811", "orgaId": "14551234", "targetValue": "20", "completeValue": "10", "completeRate": "0.50", "channelFinishedNum": "30", "outFinishedNum": "20", "personFinishedNum": "10", "rank": "2", "completeRankPro": "11111111111111111111", "completerankCity": "211111111111", "completerankSalv": "3", "normDetailInfos": [{"objectId": "11111", "objectName": "玄武区玄武区玄武区玄武区", "targetValue": "20", "completeValue": "10", "completeRate": "0.50", "completeRankPro": "3111111111111111", "completerankCity": "8", "completerankSalv": "7"}, {"objectId": "11112", "objectName": "江宁区", "targetValue": "20", "completeValue": "10", "completeRate": "0.50", "completeRankPro": "7", "completerankCity": "6", "completerankSalv": "4"}]}}, "h5QryTaskData": {"retCode": "0", "retMsg": null, "data": {"msisdn": "13675177811", "orgaId": "14551234", "finishedNum": "30", "unfinishedNum": "20", "leaveNum": "10", "staticObjects": [{"objectType": 1, "totalTaskNum": "100", "completeRate": "0.50"}, {"objectType": 2, "totalTaskNum": "100", "completeRate": "0.50"}, {"objectType": 3, "totalTaskNum": "100", "completeRate": "0.50"}, {"objectType": 4, "totalTaskNum": "100", "completeRate": "0.50"}], "gridDetailInfos": [{"objectId": "11111", "objectName": "玄武区", "targetValue": "20", "completeValue": "10", "completeRate": "0.50", "finishedNum": "20", "unfinishedNum": "10", "leaveNum": "5"}, {"objectId": "11112", "objectName": "江宁区", "targetValue": "20", "completeValue": "10", "completeRate": "0.50", "finishedNum": "20", "unfinishedNum": "10", "leaveNum": "5"}]}}, "h5QryTaskType": {"retCode": "0", "retMsg": null, "data": [{"typecode": "100000", "typename": "全部"}, {"typecode": "100001", "typename": "集团"}, {"typecode": "100002", "typename": "小区"}, {"typecode": "100003", "typename": "热点"}, {"typecode": "100004", "typename": "渠道"}]}, "h5QryOperWorkInfo": {"retCode": "0", "retMsg": null, "data": [{"workId": "1000001", "workTypeName": "集团拜访任务", "workTitle": "新大陆集团有限公司", "isUrgent": "1", "mustDate": "2020-03-11 10:00:00"}, {"workId": "1000002", "workTypeName": "小区放装任务", "workTitle": "万欣花园", "isUrgent": "2", "mustDate": "2020-03-12 10:00:00"}, {"workId": "1000003", "workTypeName": "小区放装任务", "workTitle": "万欣花园2", "isUrgent": "1", "mustDate": "2020-03-12 09:00:00"}]}, "h5QryGridAlarmInfo": {"retCode": "0", "retMsg": null, "data": {"alarmId": "10001", "ruleName": "xxxx", "operId": "<PERSON><PERSON><PERSON><PERSON>", "alarmDate": "20200402134221", "alarmInfo": "2020/04/20 南京地市公司的预警规则校讯通日新增用户数-校讯通日退订用户数大于校讯通日收费用户数，结果为异常"}}, "h5SubmitGridAlarmInfo": {"retCode": "0", "retMsg": null, "data": null}, "h5QryGridalarmOperInfo": {"retCode": "0", "retMsg": null, "data": [{"operId": "11284", "operName": "张三", "severNumber": "13901582505"}, {"operId": "11283", "operName": "张姝", "severNumber": "13901582505"}]}, "h5QryGridalarmProcess": {"retCode": "0", "resultDesc": "success", "data": [{"seqId": 202005221002003, "alarmId": 14551234, "nodeId": 0, "activeId": 0, "operMsisdnPut": 13675177812, "operNamePut": "张三", "operMsisdnGet": 13675177812, "operNameGet": "张三", "operDate": "20200522142345", "operContent": "请协助处理，谢谢"}, {"seqId": 202005221002003, "alarmId": 14551234, "nodeId": 0, "activeId": 1, "operMsisdnPut": 13675177812, "operNamePut": "张三", "operMsisdnGet": 13675177812, "operNameGet": "张三", "operDate": "20200522142345", "operContent": "请协助处理，谢谢"}, {"seqId": 202005221002003, "alarmId": 14551234, "nodeId": 0, "activeId": 2, "operMsisdnPut": 13675177812, "operNamePut": "李四", "operMsisdnGet": 13675177812, "operNameGet": "李四", "operDate": "20200522142345", "operContent": "请协助处理，谢谢"}]}, "h5QryGridReportList": {"retCode": "0", "retMsg": null, "data": [{"parentId": "0", "parentName": "0", "tableId": "1", "tableName": "外呼", "orderId": "1", "tableCyc": "1"}, {"parentId": "0", "parentName": "0", "tableId": "2", "tableName": "任务", "orderId": "1", "tableCyc": "1"}, {"parentId": "0", "parentName": "0", "tableId": "3", "tableName": "预警", "orderId": "1", "tableCyc": "1"}, {"parentId": "1", "parentName": "外呼", "tableId": "10001", "tableName": "外呼实时", "orderId": "1", "tableCyc": "3"}, {"parentId": "1", "parentName": "外呼", "tableId": "10002", "tableName": "外呼日报", "orderId": "2", "tableCyc": "1"}, {"parentId": "1", "parentName": "外呼", "tableId": "10003", "tableName": "外呼月报", "orderId": "3", "tableCyc": "2"}, {"parentId": "1", "parentName": "外呼", "tableId": "10004", "tableName": "外呼周报", "orderId": "4", "tableCyc": "1"}, {"parentId": "2", "parentName": "任务", "tableId": "10011", "tableName": "业务办理量", "orderId": "1", "tableCyc": "1"}, {"parentId": "2", "parentName": "任务", "tableId": "10012", "tableName": "到厅任务数", "orderId": "2", "tableCyc": "2"}, {"parentId": "3", "parentName": "预警", "tableId": "10021", "tableName": "预警数量", "orderId": "1", "tableCyc": "3"}]}, "h5QryGridReportTerm": {"retCode": "0", "retMsg": null, "data": [{"termId": "1001", "termName": "网格名称", "termType": "1", "termKey": "", "termValue": ""}, {"termId": "1002", "termName": "查询类型", "termType": "2", "termKey": "渠道|个人", "termValue": "1|2"}, {"termId": "1003", "termName": "统计时间", "termType": "3", "termKey": "yyyymmdd", "termValue": ""}]}, "h5QryGridReportTableTop": {"retCode": "0", "retMsg": null, "data": [{"headName": "地市", "headLevel": "1", "headColumns": "1", "headRows": "3", "isLeafHead": "1", "orderId": "1"}, {"headName": "账单收入（含税）", "headLevel": "1", "headColumns": "9", "headRows": "1", "isLeafHead": "0", "orderId": "2"}, {"headName": "账单收入（不含税）", "headLevel": "1", "headColumns": "9", "headRows": "1", "isLeafHead": "0", "orderId": "3"}, {"headName": "当日账单收入（万元）", "headLevel": "2", "headColumns": "1", "headRows": "2", "isLeafHead": "0", "orderId": "1"}, {"headName": "当月累计账单收入（万元）", "headLevel": "2", "headColumns": "1", "headRows": "2", "isLeafHead": "0", "orderId": "2"}, {"headName": "环比", "headLevel": "2", "headColumns": "1", "headRows": "2", "isLeafHead": "0", "orderId": "3"}, {"headName": "个人账户账单收入", "headLevel": "2", "headColumns": "3", "headRows": "1", "isLeafHead": "0", "orderId": "4"}, {"headName": "集团账户账单收入", "headLevel": "2", "headColumns": "3", "headRows": "1", "isLeafHead": "0", "orderId": "5"}, {"headName": "当日账单收入（万元）", "headLevel": "2", "headColumns": "1", "headRows": "2", "isLeafHead": "0", "orderId": "6"}, {"headName": "当月累计账单收入（万元）", "headLevel": "2", "headColumns": "1", "headRows": "2", "isLeafHead": "0", "orderId": "7"}, {"headName": "环比", "headLevel": "2", "headColumns": "1", "headRows": "2", "isLeafHead": "0", "orderId": "8"}, {"headName": "个人账户账单收入", "headLevel": "2", "headColumns": "3", "headRows": "1", "isLeafHead": "0", "orderId": "9"}, {"headName": "集团账户账单收入", "headLevel": "2", "headColumns": "3", "headRows": "1", "isLeafHead": "0", "orderId": "10"}, {"headName": "当日账单收入（万元）", "headLevel": "3", "headColumns": "1", "headRows": "1", "isLeafHead": "0", "orderId": "1"}, {"headName": "当月累计收入（万元）", "headLevel": "3", "headColumns": "1", "headRows": "1", "isLeafHead": "0", "orderId": "2"}, {"headName": "环比", "headLevel": "3", "headColumns": "1", "headRows": "1", "isLeafHead": "0", "orderId": "3"}, {"headName": "当日账单收入（万元）", "headLevel": "3", "headColumns": "1", "headRows": "1", "isLeafHead": "0", "orderId": "4"}, {"headName": "当月累计收入（万元）", "headLevel": "3", "headColumns": "1", "headRows": "1", "isLeafHead": "0", "orderId": "5"}, {"headName": "环比", "headLevel": "3", "headColumns": "1", "headRows": "1", "isLeafHead": "0", "orderId": "6"}, {"headName": "当日账单收入（万元）", "headLevel": "3", "headColumns": "1", "headRows": "1", "isLeafHead": "0", "orderId": "7"}, {"headName": "当月累计收入（万元）", "headLevel": "3", "headColumns": "1", "headRows": "1", "isLeafHead": "0", "orderId": "8"}, {"headName": "环比", "headLevel": "3", "headColumns": "1", "headRows": "1", "isLeafHead": "0", "orderId": "9"}, {"headName": "当日账单收入（万元）", "headLevel": "3", "headColumns": "1", "headRows": "1", "isLeafHead": "0", "orderId": "10"}, {"headName": "当月累计收入（万元）", "headLevel": "3", "headColumns": "1", "headRows": "1", "isLeafHead": "0", "orderId": "11"}, {"headName": "环比", "headLevel": "3", "headColumns": "1", "headRows": "1", "isLeafHead": "0", "orderId": "12"}]}, "h5QryGridReportTableInfo": {"retCode": "0", "retMsg": null, "data": [{"columnValue": "10000010|1|南京|100|10000|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%"}, {"columnValue": "10000011|1|苏州|100|10000|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%"}, {"columnValue": "10000012|0|常州|100|10000|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%"}, {"columnValue": "10000013|1|苏州|100|10000|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%"}, {"columnValue": "10000014|1|常州|100|10000|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%"}, {"columnValue": "10000015|1|常州|100|10000|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%"}, {"columnValue": "10000016|1|苏州|100|10000|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%"}, {"columnValue": "10000017|1|常州|100|10000|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%"}, {"columnValue": "10000018|1|苏州|100|10000|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%"}, {"columnValue": "10000019|1|常州|100|10000|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%"}, {"columnValue": "10000020|1|常州|100|10000|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%"}, {"columnValue": "10000021|1|连云港|100|10000|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%"}, {"columnValue": "10000022|1|盐城|100|10000|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%"}, {"columnValue": "10000023|1|常州|100|10000|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%"}, {"columnValue": "10000024|1|盐城|100|10000|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%"}, {"columnValue": "10000025|1|宿迁|100|10000|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%"}, {"columnValue": "10000026|1|连云港|100|10000|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%"}, {"columnValue": "10000027|1|徐州|100|10000|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%"}, {"columnValue": "10000028|1|镇江|100|10000|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%"}, {"columnValue": "10000029|0|无锡|100|10000|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%|2500|2500|25%"}]}, "h5QryScoreAndRankById": {"retCode": "0", "retMsg": null, "data": {"orgaId": "2000250", "orgaName": "江苏省", "orgaScore": "90.8", "orgaRank": "5", "isRightCopy": "0", "operType": null}}, "h5QryResource": {"retCode": "0", "retMsg": null, "data": [{"orgaResourceName": "集团", "orgaResourceValue": "10"}, {"orgaResourceName": "小区", "orgaResourceValue": "10"}, {"orgaResourceName": "渠道", "orgaResourceValue": "10"}, {"orgaResourceName": "个人", "orgaResourceValue": "10"}]}, "h5QryIndexAndBusi": {"retCode": "0", "retMsg": null, "data": {"gridIndexList": [{"orgaIndexName": "营收额度", "orgaIndexValue": "10万元", "orgaIndexQuaRate": "--", "orgaIndexYoY": "1.2", "orgaIndexMoM": "0.28"}, {"orgaIndexName": "新增用户数", "orgaIndexValue": "200人", "orgaIndexQuaRate": "0.2", "orgaIndexYoY": "--", "orgaIndexMoM": "0.35"}], "busiNumList": [{"businessName": "新增宽带", "targetValue": "2054154156户", "completeValue": "10", "completeRate": "0.55"}, {"businessName": "新增互联网电视", "targetValue": "20", "completeValue": "10", "completeRate": "0.06"}, {"businessName": "新增营销案", "targetValue": "20456", "completeValue": "10", "completeRate": "--"}], "serviceList": [{"serviceName": "服务1", "serviceValue": "10万元", "serviceRank": "1"}, {"serviceName": "服务2", "serviceValue": "10万元", "serviceRank": "2"}]}}, "h5QryTaskSum": {"retCode": "0", "retMsg": null, "data": {"finishedNum": "0", "unfinishedNum": "1", "leaveNum": "0"}}, "h5QryNormItems1": {"retCode": "0", "retMsg": null, "data": {"msisdn": "", "orgaId": "", "indexInfos": [{"indexCode": "1", "indexName": "个人", "parentIndex": "-1", "indexcyc": ""}, {"indexCode": "3", "indexName": "家庭", "parentIndex": "-1", "indexcyc": ""}, {"indexCode": "4", "indexName": "集团", "parentIndex": "-1", "indexcyc": ""}, {"indexCode": "10013", "indexName": "手机新增用户数", "parentIndex": "1", "indexcyc": "1"}, {"indexCode": "10024", "indexName": "4G终端销售量", "parentIndex": "1", "indexcyc": "1"}, {"indexCode": "100131", "indexName": "手机新增用户数（实时）", "parentIndex": "1", "indexcyc": "3"}, {"indexCode": "100241", "indexName": "4G终端销售量（实时）", "parentIndex": "1", "indexcyc": "3"}, {"indexCode": "100132", "indexName": "手机新增用户数（月）", "parentIndex": "1", "indexcyc": "2"}, {"indexCode": "100242", "indexName": "4G终端销售量（月）", "parentIndex": "1", "indexcyc": "2"}, {"indexCode": "10019", "indexName": "家庭宽带新增", "parentIndex": "3", "indexcyc": "1"}, {"indexCode": "10020", "indexName": "家庭互联网电视新增", "parentIndex": "3", "indexcyc": "1"}, {"indexCode": "10034", "indexName": "一网通-小微宽带新增", "parentIndex": "4", "indexcyc": "1"}, {"indexCode": "10035", "indexName": "一网通-互联网电视新增", "parentIndex": "4", "indexcyc": "1"}, {"indexCode": "10036", "indexName": "一网通-手机看店新增", "parentIndex": "4", "indexcyc": "1"}]}}, "h5qrySingleInfo": {"retCode": "0", "retMsg": null, "data": {"msisdn": "911", "orgaId": "10000000", "completeValue": "10000000", "rank": "200", "indexYoY": "0.56342", "indexMoM": "0.3832455", "detailInfos": [{"objectName": "一", "objectId": "2000250", "completeValue": "100000000000000", "rank": "200", "indexYoY": "0.56342", "indexMoM": "0.3832455"}, {"objectName": "二", "objectId": "2000250", "completeValue": "10000000", "rank": "20", "indexYoY": "0.56", "indexMoM": "0.38"}, {"objectName": "三", "objectId": "2000250", "completeValue": "10000000", "rank": "20", "indexYoY": "0.56", "indexMoM": "0.38"}]}}, "h5QrySingleNormData1": {"retCode": "0", "retMsg": null, "data": {"msisdn": "911", "orgaId": "10000000", "targetValue": "--", "completeValue": "10000000", "completeRate": "--", "rank": "200", "indexYoY": "0.56342", "indexMoM": "0.3832455", "completeRankPro": "1230", "completerankCity": "100", "completerankSalv": "14200", "normDetailInfos": [{"objectId": "1488018465153420", "objectName": "玄武秦淮分公司", "targetValue": "0.75", "completeValue": "1758个", "completeRate": "0.75", "completeRankPro": "77777777777", "completerankCity": "8", "completerankSalv": "8"}, {"objectId": "1488018465153418", "objectName": "雨花分公司", "targetValue": "0.75", "completeValue": "594个", "completeRate": "0.75", "completeRankPro": "7", "completerankCity": "8", "completerankSalv": "8"}]}}, "h5qryGridChannelDetails": {"retCode": "0", "retMsg": null, "data": {"agentLists": [{"agentId": "11", "agentName": "渠道1"}, {"agentId": "12", "agentName": "渠道2"}, {"agentId": "13", "agentName": "渠道13"}, {"agentId": "114", "agentName": "渠道14"}, {"agentId": "15", "agentName": "渠道15"}, {"agentId": "116", "agentName": "渠道16"}]}}, "h5qryGridChannelInfo": {"retCode": "0", "retMsg": null, "data": {"infoLists": [{"infoName": "渠道类型", "infoValue": "旗舰店"}, {"infoName": "经理号码", "infoValue": "13615296968"}, {"infoName": "经理名称", "infoValue": "刘涛"}, {"infoName": "渠道经度", "infoValue": "31.982877"}, {"infoName": "渠道纬度", "infoValue": "119.828272"}, {"infoName": "归属区县", "infoValue": "丹阳分公司"}, {"infoName": "归属网格", "infoValue": "云阳区域中心"}], "busLists": [{"busName": "号卡新增", "busValue": "116笔"}, {"busName": "宽带新增", "busValue": "35笔"}, {"busName": "终端办理", "busValue": "58笔"}, {"busName": "套餐办理", "busValue": "307笔"}], "otherLists": [{"otherName": "到厅用户数", "otherValue": "39人"}, {"otherName": "开户用户数", "otherValue": "28人"}, {"otherName": "酬金金额", "otherValue": "100元"}]}}}