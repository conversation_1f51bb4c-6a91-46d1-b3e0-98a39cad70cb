{"h5QryAccountInfoList": {"retCode": "0", "retMsg": null, "data": [{"accountName": "互联网专线账户", "accountId": "**************", "accountStatus": "正常", "accountBalances": "0.00", "accountArrears": "100.00"}, {"accountName": "语音专线账户", "accountId": "**************", "accountStatus": "正常", "accountBalances": "0.00", "accountArrears": "0.00"}, {"accountName": "互联网专线账户", "accountId": "**************", "accountStatus": "正常", "accountBalances": "0.00", "accountArrears": "100.00"}, {"accountName": "语音专线账户", "accountId": "**************", "accountStatus": "正常", "accountBalances": "0.00", "accountArrears": "200.00"}, {"accountName": "互联网专线账户", "accountId": "**************", "accountStatus": "正常", "accountBalances": "0.00", "accountArrears": "0.00"}, {"accountName": "语音专线账户", "accountId": "**************", "accountStatus": "正常", "accountBalances": "0.00", "accountArrears": "200.00"}, {"accountName": "互联网专线账户", "accountId": "**************", "accountStatus": "正常", "accountBalances": "0.00", "accountArrears": "100.00"}, {"accountName": "语音专线账户", "accountId": "**************", "accountStatus": "正常", "accountBalances": "0.00", "accountArrears": "200.00"}, {"accountName": "互联网专线账户", "accountId": "**************", "accountStatus": "正常", "accountBalances": "0.00", "accountArrears": "100.00"}]}, "h5QryPostPaid": {"retCode": "0", "retMsg": null, "data": [{"month": "202002", "arrearsAmount": "45.00", "discountAmount": "0.00", "substractAmount": "0.00", "falsify": "0.00", "needPay": "45.00"}, {"month": "202001", "arrearsAmount": "45.00", "discountAmount": "0.00", "substractAmount": "0.00", "falsify": "0.00", "needPay": "45.00"}, {"month": "202002", "arrearsAmount": "45.00", "discountAmount": "0.00", "substractAmount": "0.00", "falsify": "0.00", "needPay": "45.00"}, {"month": "202001", "arrearsAmount": "45.00", "discountAmount": "0.00", "substractAmount": "0.00", "falsify": "0.00", "needPay": "45.00"}, {"month": "202002", "arrearsAmount": "45.00", "discountAmount": "0.00", "substractAmount": "0.00", "falsify": "0.00", "needPay": "45.00"}, {"month": "202001", "arrearsAmount": "45.00", "discountAmount": "0.00", "substractAmount": "0.00", "falsify": "0.00", "needPay": "45.00"}]}, "h5QryPrePaid": {"retCode": "0", "retMsg": null, "data": [{"prePaidArrearsAmount": "45.00"}]}, "h5QryBillInfo": {"retCode": "0", "retMsg": null, "data": [{"month": "201909", "totalAmount": "500.00", "discountAmount": "100.00", "subtractAmount": "400.00", "falsify": "0.00", "arrearsAmount": "0.00", "balanceAmount": "0.00", "cancellationStatus": "已经销账", "collectionStatus": "--"}, {"month": "201910", "totalAmount": "510.00", "discountAmount": "0.00", "subtractAmount": "500.00", "falsify": "0.00", "arrearsAmount": "0.00", "balanceAmount": "0.00", "cancellationStatus": "已经销账", "collectionStatus": "--"}, {"month": "201911", "totalAmount": "200.00", "discountAmount": "0.00", "subtractAmount": "500.00", "falsify": "0.00", "arrearsAmount": "0.00", "balanceAmount": "0.00", "cancellationStatus": "已经销账", "collectionStatus": "--"}]}, "h5QryAccountFee": {"retCode": "0", "retMsg": null, "data": [{"feeName": "月租费", "feeValue": "400.00"}, {"feeName": "升档费", "feeValue": "100.00"}, {"feeName": "月租费", "feeValue": "400.00"}, {"feeName": "升档费", "feeValue": "100.00"}, {"feeName": "月租费", "feeValue": "400.00"}, {"feeName": "升档费", "feeValue": "100.00"}, {"feeName": "月租费", "feeValue": "400.00"}, {"feeName": "升档费", "feeValue": "100.00"}, {"feeName": "月租费", "feeValue": "400.00"}, {"feeName": "升档费", "feeValue": "100.00"}]}, "h5QryAccountBalance": {"retCode": "0", "retMsg": null, "data": [{"bookName": "充值卡账本", "bookBalance": "80.00"}, {"bookName": "现金充值账本", "bookBalance": "10.00"}, {"bookName": "现金缴费账本", "bookBalance": "0.00"}, {"bookName": "充值卡账本", "bookBalance": "80.00"}, {"bookName": "现金充值账本", "bookBalance": "10.00"}, {"bookName": "现金缴费账本", "bookBalance": "0.00"}, {"bookName": "充值卡账本", "bookBalance": "80.00"}, {"bookName": "现金充值账本", "bookBalance": "10.00"}, {"bookName": "现金缴费账本", "bookBalance": "0.00"}]}, "h5queryALDOrderTask": {"retCode": "0", "retMsg": "success", "data": {"orderNum": "2", "orderNum1": "4", "orderNum2": "10", "orderNum3": "3", "orderNum4": "0"}}, "h5orderQryList": {"retCode": "0", "retMsg": "success", "data": [{"orderId": "****************", "regionName": "雨花台公司", "siteName": "雨花SA组", "ctUserId": "1412728", "ctUserName": "支撑人员", "ctUserPhone": "***********", "createTime": "20160830132110", "opUserId": "1522881", "opUserName": "张华宇", "opUserPhone": "1820403938", "busiType": "集团WLAN", "actionType": "新增", "custName": "新大陆科技有限公司", "custId": "", "linkName": "勘察需求审核", "linkState": "待审核"}, {"orderId": "2382829199929283", "regionName": "雨花台公司2", "siteName": "雨花SA组2", "ctUserId": "14127282", "ctUserName": "支撑人员2", "ctUserPhone": "18204091882", "createTime": "20160830132112", "opUserId": "1522882", "opUserName": "张华宇2", "opUserPhone": "1820403932", "busiType": "集团套餐09版", "actionType": "新增2", "custName": "新大陆科技有限公司2", "custId": "", "linkName": "勘察需求审核2", "linkState": "待审核"}, {"orderId": "****************", "regionName": "雨花台公司", "siteName": "雨花SA组", "ctUserId": "1412728", "ctUserName": "支撑人员", "ctUserPhone": "***********", "createTime": "20160830132110", "opUserId": "1522881", "opUserName": "张华宇", "opUserPhone": "1820403938", "busiType": "云视讯", "actionType": "新增", "custName": "新大陆科技有限公司", "custId": "", "linkName": "勘察需求审核", "linkState": "待审核"}, {"orderId": "2382829199929283", "regionName": "雨花台公司2", "siteName": "雨花SA组2", "ctUserId": "14127282", "ctUserName": "支撑人员2", "ctUserPhone": "18204091882", "createTime": "20160830132112", "opUserId": "1522882", "opUserName": "张华宇2", "opUserPhone": "1820403932", "busiType": "集团套餐08版", "actionType": "新增2", "custName": "新大陆科技有限公司2", "custId": "", "linkName": "勘察需求审核2", "linkState": "待审核"}, {"orderId": "****************", "regionName": "雨花台公司", "siteName": "雨花SA组", "ctUserId": "1412728", "ctUserName": "支撑人员", "ctUserPhone": "***********", "createTime": "20160830132110", "opUserId": "1522881", "opUserName": "张华宇", "opUserPhone": "1820403938", "busiType": "集团套餐01版", "actionType": "新增", "custName": "新大陆科技有限公司", "custId": "", "linkName": "勘察需求审核", "linkState": "待审核"}, {"orderId": "2382829199929283", "regionName": "雨花台公司2", "siteName": "雨花SA组2", "ctUserId": "14127282", "ctUserName": "支撑人员2", "ctUserPhone": "18204091882", "createTime": "20160830132112", "opUserId": "1522882", "opUserName": "张华宇2", "opUserPhone": "1820403932", "busiType": "集团套餐02版", "actionType": "新增2", "custName": "新大陆科技有限公司2", "custId": "", "linkName": "勘察需求审核2", "linkState": "待审核"}]}, "h5QryGroupNum": {"retCode": "0", "retMsg": null, "data": {"typeANum": "10", "typeBNum": "10", "typeCNum": "10", "typeDNum": "10"}}, "h5QryGroupList": {"retCode": "0", "retMsg": null, "data": [{"groupId": "52310024440", "groupName": "泰州市公安局交通警察支队", "groupType": "C"}, {"groupId": "52330032057", "groupName": "泰州市海陵司法局", "groupType": "B"}, {"groupId": "52310000454", "groupName": "中国人民武装警察部队泰州支队", "groupType": "A"}, {"groupId": "52310001541", "groupName": "泰州市农业农村局", "groupType": "A"}, {"groupId": "52310000881", "groupName": "泰州司法局", "groupType": "C"}, {"groupId": "52310023023", "groupName": "泰州市司法局医药高新区分局", "groupType": "C"}, {"groupId": "52310001789", "groupName": "华泰人寿保险股份有限公司泰州中心支公司", "groupType": "C"}, {"groupId": "52330121002", "groupName": "泰州市嘉利投资管理有限公司", "groupType": "C"}, {"groupId": "52330788153", "groupName": "泰州市内司工委", "groupType": "C"}, {"groupId": "52334872572", "groupName": "中移全通系统集成有限公司", "groupType": "D"}, {"groupId": "52310000723", "groupName": "泰州公安局", "groupType": "C"}, {"groupId": "52330748154", "groupName": "泰州市机关工委", "groupType": "C"}, {"groupId": "***********", "groupName": "泰州市公安局", "groupType": "A"}, {"groupId": "***********", "groupName": "江苏省泰州市中级人民法院", "groupType": "B"}, {"groupId": "***********", "groupName": "泰州市消防支队", "groupType": "A"}]}, "h5QryGroupBaseInfo": {"retCode": "0", "retMsg": null, "data": {"groupId": "**************", "groupName": "新大陆科技", "groupType": "A", "groupAddress": "嘉陵江东街18号", "arrearsAccounts": "2", "groupMembers": "2", "canOrderProducts": "2"}}, "h5QryGroupKeyLinkman": {"retCode": "0", "retMsg": null, "data": [{"userName": "张三的名字", "userSex": "男", "userPhone": "***********"}, {"userName": "李四的名字", "userSex": "--", "userPhone": "***********"}, {"userName": "王二的名字", "userSex": "女", "userPhone": "***********"}]}, "h5QryGroupMemBaseInfo": {"retCode": "0", "retMsg": null, "data": {"memberName": "张三", "memberPhone": "***********"}}, "h5QryGroupEffServe": {"retCode": "0", "retMsg": null, "data": [{"productId": "************", "productName": "集团V网", "startTime": "2019-03-01 00:00:01", "endTime": "2100-03-01 00:00:01"}, {"productId": "************", "productName": "集团彩铃3.0", "startTime": "2019-03-01 00:00:01", "endTime": "2100-03-01 00:00:01"}, {"productId": "************", "productName": "集团铃2.0", "startTime": "2019-03-01 00:00:01", "endTime": "2100-03-01 00:00:01"}, {"productId": "************", "productName": "集团彩4.0", "startTime": "2019-03-01 00:00:01", "endTime": "2100-03-01 00:00:01"}]}, "h5QryGroupCurPayRel": {"retCode": "0", "retMsg": null, "data": [{"relationType": "1", "payAccount": "互联网专线账户", "accountId": "*****************", "payType": "全额代付", "payAmount": "--", "payStart": "2019-03-01 00:00:01", "payEnd": "2100-03-01 00:00:01"}, {"relationType": "2", "payAccount": "互联网专线账户", "accountId": "*****************", "payType": "--", "payAmount": "50.00", "payStart": "2019-03-01 00:00:01", "payEnd": "2100-03-01 00:00:01"}]}, "h5QryGroupPastPayRel": {"retCode": "0", "retMsg": null, "data": [{"payAccount": "互联网专线账户", "accountId": "*****************", "payType": "全额代付", "payAmount": "--", "payStart": "2019-03-01 00:00:01", "payEnd": "2100-03-01 00:00:01"}, {"payAccount": "互联网专线账户", "accountId": "*****************", "payType": "固定金额", "payAmount": "50.00", "payStart": "2017-03-01 00:00:01", "payEnd": "2019-02-28 00:00:01"}]}, "h5qryGroupProduct": {"retCode": "0", "retMsg": "成功", "data": {"total": 12, "totalPage": 1, "list": [{"productId": "*****************", "productName": "流量V网1.0", "state": "正在使用", "startTime": "2019-03-01 00:00:01", "endTime": "2100-03-01 00:00:01"}, {"productId": "*****************", "productName": "集团V网2.0集团V网集团V网集团V网集团V网集团V网集团V网集团V网集团V网", "state": "正在使用正在使用", "startTime": "2019-03-01 00:00:01", "endTime": "2100-03-01 00:00:01"}, {"productId": "*****************", "productName": "集团彩铃3.0", "state": "正在使用", "startTime": "2019-02-01 00:00:01", "endTime": "2100-03-01 00:00:01"}, {"productId": "*****************", "productName": "集团彩铃4.0", "state": "正在使用", "startTime": "2019-03-01 00:00:01", "endTime": "2100-03-01 00:00:01"}, {"productId": "13576879080909094", "productName": "集团V网5.0", "state": "正在使用", "startTime": "2019-02-01 00:00:01", "endTime": "2100-03-01 00:00:01"}, {"productId": "13576879080909095", "productName": "流量V网6.0", "state": "正在使用", "startTime": "2019-03-01 00:00:01", "endTime": "2100-03-01 00:00:01"}, {"productId": "13576879080909096", "productName": "集团V网7.0", "state": "正在使用", "startTime": "2019-03-01 00:00:01", "endTime": "2100-03-01 00:00:01"}, {"productId": "13576879080909097", "productName": "集团彩铃8.0", "state": "正在使用", "startTime": "2019-02-01 00:00:01", "endTime": "2100-03-01 00:00:01"}, {"productId": "13576879080909098", "productName": "集团彩铃9.0", "state": "正在使用", "startTime": "2019-03-01 00:00:01", "endTime": "2100-03-01 00:00:01"}, {"productId": "13576879080909099", "productName": "集团V网10.0", "state": "正在使用", "startTime": "2019-02-01 00:00:01", "endTime": "2100-03-01 00:00:01"}]}}, "h5qryGroupMarketing": {"retCode": "0", "retMsg": "成功", "data": {"total": 12, "totalPage": 1, "list": [{"marketId": "*****************", "marketName": "营销案营销案营销案营销案营销案营销案营销案营销案营销案营销案", "levelName": "300元档300元档300元档300元档300元档300元档300元档300元档300元档300元档", "state": "生效中", "startTime": "2019-03-01 00:00:01", "endTime": "2100-03-01 00:00:01"}, {"marketId": "*****************", "marketName": "营销案2", "levelName": "500元档", "state": "生效中", "startTime": "2019-01-01 00:00:01", "endTime": "2100-03-01 00:00:01"}, {"marketId": "*****************", "marketName": "营销案3", "levelName": "500元档", "state": "生效中", "startTime": "2019-01-01 00:00:01", "endTime": "2100-03-01 00:00:01"}, {"marketId": "*****************", "marketName": "营销案4", "levelName": "500元档", "state": "生效中", "startTime": "2019-01-01 00:00:01", "endTime": "2100-03-01 00:00:01"}]}}, "h5qryGroupAllData": {"retCode": "0", "retMsg": "成功", "data": {"total": 12, "totalPage": 2, "list": [{"totalMembers": "12", "addMembers": "9"}]}}, "h5qryGroupDetailData": {"retCode": "0", "retMsg": "成功", "data": {"total": 12, "totalPage": 1, "list": [{"memberName": "张三", "memberPhone": "***********", "memberSex": "男", "memberAge": "25", "startTime": "2018-04-01 12:00:05", "endTime": "2020-04-11 23:59:59"}, {"memberName": "李四", "memberPhone": "14751946779", "memberSex": "男", "memberAge": "25", "startTime": "2018-04-01 12:00:05", "endTime": "2022-12-31 23:59:59"}, {"memberName": "张三", "memberPhone": "***********", "memberSex": "男", "memberAge": "25", "startTime": "2018-04-01 12:00:05", "endTime": "2022-12-31 23:59:59"}, {"memberName": "李四", "memberPhone": "14751946779", "memberSex": "男", "memberAge": "25", "startTime": "2018-04-01 12:00:05", "endTime": "2022-12-31 23:59:59"}, {"memberName": "张三", "memberPhone": "***********", "memberSex": "男", "memberAge": "25", "startTime": "2018-04-01 12:00:05", "endTime": "2022-12-31 23:59:59"}, {"memberName": "李四", "memberPhone": "14751946779", "memberSex": "男", "memberAge": "25", "startTime": "2018-04-01 12:00:05", "endTime": "2022-12-31 23:59:59"}]}}, "h5qryGroupBasicData": {"retCode": "0", "retMsg": "成功", "data": {"total": 14, "totalPage": 1, "list": [{"chargeAmount": "30.76", "costMode": "月底最低消费", "totalPrivMoney": "0.00", "totalPrivPercent": "1.00", "totalMoneyretMember": "0.00", "memberPrivPercent": "0.00", "goodsMoney": "0.00", "goodsPercent": "0.00", "imeiMoney": "0.00", "imeiPercent": "0.00", "cardMoney": "0.00", "cardPercent": "0.00", "rwdPeriod": "12", "startDate": "2018/03/01 00:00:01", "endDate": "2018/03/01 00:00:01"}]}}, "h5QrySubList": {"retCode": "0", "retMsg": "成功", "data": [{"subId": "1001", "subName": "南京市公安局总局", "subAddress": "南京市玄武区玄武大道13号"}, {"subId": "1002", "subName": "南京市公安局鼓楼分局", "subAddress": "南京市鼓楼区清凉门大街100号"}]}, "h5QryBusiRecommendInfo": {"retCode": "0", "resultDesc": "成功", "data": [{"busiStepId": "10001", "busiStepName": "商机挖掘", "workDesc": "客户摸底，进行表单采集。再根据客户实际使用情况（PC购买时间、使用年限、规模、需求等）推荐云桌面", "isMust": "1", "lastTime": "2020-04-15 17:17:24"}, {"busiStepId": "10002", "busiStepName": "现场查看", "workDesc": "前往客户现场查看，结合收集的信息进行核实，并进行云桌面介绍及推荐。", "isMust": "0"}]}, "h5QryBusiRecommendDetail": {"retCode": "0", "resultDesc": "成功", "isEnd": "0", "data": {"busiStepInfo": [{"contentId": "10001", "contentName": "工作内容", "contentDesc": "客户摸底，进行表单采集。再根据客户实际使用情况（PC购买时间、使用年限、规模、需求等）推荐云桌面", "isCollect": "0"}, {"contentId": "10002", "contentName": "信息采集", "isCollect": "1", "mustInfo": [{"mustId": "5", "mustType": "2", "mustUserInput": "", "isInput": "0", "mustUserValue": "", "mustName": "test1", "mustValue": "0,1,2", "mustKey": "A,B,C", "mustFlag": "0"}, {"mustId": "6", "mustType": "1", "mustUserInput": "", "isInput": "0", "mustUserValue": "", "mustName": "test2", "mustValue": "", "mustKey": "", "mustFlag": "0"}, {"mustId": "7", "mustType": "4", "mustUserInput": "", "isInput": "0", "mustUserValue": "", "mustName": "test3", "mustValue": "0,1,2", "mustKey": "z,x,c", "mustFlag": "0"}]}], "fdInfos": [{"fdId": "1", "fdName": "成功"}, {"fdId": "2", "fdName": "考虑", "fdReason": "原因1∫原因2∫原因3"}], "doId": "2", "doReason": "原因2", "doRemark": "考虑考虑"}}}