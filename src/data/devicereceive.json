{"h5CheckGet": {"retCode": "0", "retMsg": null, "data": {"isDeliveryMan": 0, "usabDeviceList": [{"devileClass": "OTT_STB", "devileClassName": "电视机顶盒", "isMustChoice": "1"}, {"devileClass": "PON_STB", "devileClassName": "PON上行融合机顶盒", "isMustChoice": "1"}, {"devileClass": "HGW", "devileClassName": "家庭网关", "isMustChoice": "0"}]}}, "h5QryDeviceAndGetType": {"retCode": "0", "retMsg": null, "data": {"sinalnoRequired": "0", "terminalTypeList": [{"typeId": "151000000000000016", "typeName": "家庭网关"}], "drawSourceList": [{"drawSourceId": "2", "drawSourceName": "代维发放"}]}}, "h5DeviceGetFee": {"retCode": "0", "retMsg": null, "data": {"chargeList": [{"code": "DeviceTestFee", "name": "家庭终端调测费", "money": "100"}, {"code": "DeviceTestFee", "name": "接入费", "money": "100"}], "isDeviceFee": "0"}}, "h5DeviceGetCommit": {"retCode": "0", "retMsg": null, "data": null}}