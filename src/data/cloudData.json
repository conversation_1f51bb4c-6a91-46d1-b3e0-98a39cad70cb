{"h5getGroupContractDetail": {"retCode": "0", "retMsg": "success", "data": {"businessName": "移动云", "businessNumber": "14", "businessDescription": "移动云开通申请", "businessOpen": "", "xnzjsl": "1", "ipdzsl": "2", "xnzjhs": "4", "xnzjnc": "8", "xnzjcc": "128", "xnzjdk": "100", "remark": "暂无"}}, "h5yiDongYunCommit": {"retCode": "0", "retMsg": "", "data": null}, "h5getYiDongYunApplyList": {"retCode": "0", "retMsg": null, "data": [{"groupName": "集团名称", "contactUser": "集团联系人", "contactPhone": "集团联系人电话", "vmstorage": "500", "vmspec": "2", "state": "0", "contractInfo": {"serviceName": "云主机订购", "serviceQuantity": "2", "serviceSpecDesc": "云主机订购", "vmQuantity": null, "businessOpen": "主机数量；IP地址；带宽", "ipQuantity": "2", "vmCpu": "3", "vmMemory": "2", "vmStorage": null, "vmBandwidth": "10M", "additionalInfo": ""}, "customer": {"name": "集团名称", "accountManagerName": "客户经理", "accountManagerPhone": "客户经理电话", "contactUser": "集团联系人", "contactPhone": "集团联系人电话", "custCode": "集团编号"}, "requirement": {"vmQuantity": "1", "vmSpec": "2", "isPublicIp": "2", "vmBandwidth": "20", "hostBackup": "2", "dataDiskBackup": "1", "vmStorage": "500", "resourcePoolNode": "", "comment": "", "dealTime": "2021-3-25 18:00:00", "department": "阿里巴巴", "resourceInfo": ""}}]}}