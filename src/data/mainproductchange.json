{"h5GetMainProductList": {"retCode": "0", "retMsg": "success", "data": [{"offerid": "1", "offername": "4G飞享（预付费）"}, {"offerid": "2", "offername": "4G自选（预付费）"}]}, "h5GetPrimaryDownOffer": {"retCode": "0", "retMsg": "success", "data": {"mustList": [{"offerid": "101", "offername": "飞享必选包", "isbundled": "Y", "subOffers": [{"offerid": "101001", "offername": "任我用家庭版128元（全国版）"}, {"offerid": "101002", "offername": "任我用家庭版128元（全国版）（200M）"}, {"offerid": "101003", "offername": "任我用基础包（98档）"}]}, {"offerid": "102", "offername": "免月租费", "isbundled": "N", "subOffers": []}], "kxList": [{"offerid": "201", "offername": "可选商品1"}, {"offerid": "202", "offername": "可选商品2"}, {"offerid": "203", "offername": "可选商品3"}], "isAvailableResp": {}}}, "h5QryOrderRelation": {"retCode": "0", "retMsg": "success", "data": {}}, "h5GetMainProdInfo": {"retCode": "0", "retMsg": "success", "data": {"infoList": [{"offername": "任我用家庭版158元（全国版）", "offerid": "2000011595", "effectdate": "2021-01-01", "expiredate": "2021-03-01", "zhutiname": "4G飞享（预付费）", "zhutiid": "1000100301", "status": "0"}, {"offername": "5G智享套餐（融合版）199元", "offerid": "2000011961", "effectdate": "2021-03-01", "expiredate": "2100-12-01", "zhutiname": "5G智享（预付费）", "zhutiid": "1000100335", "status": "1"}]}}}