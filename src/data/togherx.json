{"h5ModifyPerson": {"retCode": "0", "retMsg": "录入行商人员成功", "data": null}, "h5QueryPerson": {"retCode": "0", "retMsg": null, "data": [{"dwUser": "daib", "insertCrmId": "14150759", "insertCrmName": "公爵", "isDefault": "1", "personSeq": "20210629084609100009", "id": "14150759", "label": "公爵"}, {"dwUser": "daib", "insertCrmId": "14150758", "insertCrmName": "子爵", "isDefault": "0", "personSeq": "20210629084819100010", "id": "14150758", "label": "子爵"}, {"dwUser": "daib", "insertCrmId": "14150758", "insertCrmName": "伯爵", "isDefault": "0", "personSeq": "20210629084836100011", "id": "14150758", "label": "伯爵"}, {"dwUser": "daib", "insertCrmId": "14150757", "insertCrmName": "亲王", "isDefault": "0", "personSeq": "20210702092212100017", "id": "14150757", "label": "亲王"}, {"dwUser": "daib", "insertCrmId": "14150757", "insertCrmName": "亲王", "isDefault": "0", "personSeq": "20210702092219100018", "id": "14150757", "label": "亲王"}]}, "h5InsertOrder": {"retCode": "0", "retMsg": "营销协同订单插入成功", "data": null}, "h5InsertFeedback": {"retCode": "0", "retMsg": null, "data": null}, "h5qryDictInfo": {"retCode": "0", "retMsg": null, "data": {"9100000783058": "1"}}, "h5queryPromotionTerminalList": {"retCode": "0", "retMsg": null, "data": {"pageInfo": {"beginRowNumber": "20", "totalRecord": "88", "curPage": "2", "recordPerPage": "20"}, "terminalOfferList": [{"offerId": "18517021", "offerName": "小米M2102J2SCGSC[蓝-128G-8G]", "offerCode": "JSYD-MI-M2102J2SCGSC-04-128G-8", "band": null, "color": null, "memory": null, "type": null, "invId": null}, {"offerId": "18588008", "offerName": "vivoV2048AGSC[蓝-128G-8G]", "offerCode": "JSYD-BBK-V2048AGSC-04-128G-8", "band": null, "color": null, "memory": null, "type": null, "invId": null}, {"offerId": "18473026", "offerName": "vivoV2068ASC[蓝-128G-6G]", "offerCode": "JSYD-BBK-V2068ASC-04-128G-6", "band": null, "color": null, "memory": null, "type": null, "invId": null}, {"offerId": "17928000", "offerName": "vivoV2031EASC[黑-128G-8G]", "offerCode": "JSYD-BBK-V2031EASC-01-128G-8", "band": null, "color": null, "memory": null, "type": null, "invId": null}, {"offerId": "19242004", "offerName": "vivoV2106AGSC[灰-128G-8G]", "offerCode": "JSYD-BBK-V2106AGSC-03-128G-8", "band": null, "color": null, "memory": null, "type": null, "invId": null}, {"offerId": "17626091", "offerName": "华为LIO-AN00mSC[紫-256G-8G]", "offerCode": "JSYD-HUAWEI-LIO-ANMSC-12-256G-8", "band": null, "color": null, "memory": null, "type": null, "invId": null}, {"offerId": "17626058", "offerName": "苹果A2400SC[绿-64G-4G]", "offerCode": "JSYD-APPLE-A2400SC-13-64G-4", "band": null, "color": null, "memory": null, "type": null, "invId": null}, {"offerId": "18401015", "offerName": "realmeRMX3121SC[灰-128G-6G]", "offerCode": "JSYD-REALME-RMX3121SC-03-128G-6", "band": null, "color": null, "memory": null, "type": null, "invId": null}, {"offerId": "18785010", "offerName": "vivoV2054AGSC[粉-256G-8G]", "offerCode": "JSYD-BBK-V2054AGSC-05-256G-8", "band": null, "color": null, "memory": null, "type": null, "invId": null}, {"offerId": "18028010", "offerName": "华为OCE-AN10GSC[黄-128G-8G]", "offerCode": "JSYD-HUAWEI-OCE-ANGSC-08-128G-8", "band": null, "color": null, "memory": null, "type": null, "invId": null}, {"offerId": "19040004", "offerName": "OPPOPEPM00GSC[蓝-256G-12G]", "offerCode": "JSYD-OPPO-PEPM00GSC-04-256G-12", "band": null, "color": null, "memory": null, "type": null, "invId": null}, {"offerId": "18028008", "offerName": "华为OCE-AN10GSC[黑-128G-8G]", "offerCode": "JSYD-HUAWEI-OCE-ANGSC-01-128G-8", "band": null, "color": null, "memory": null, "type": null, "invId": null}, {"offerId": "19117000", "offerName": "OPPOPEQM00GSC[蓝-128G-8G]", "offerCode": "JSYD-OPPO-PEQM00GSC-04-128G-8", "band": null, "color": null, "memory": null, "type": null, "invId": null}, {"offerId": "19039045", "offerName": "小米M2104K10ACSC[银-256G-8G]", "offerCode": "JSYD-MI-M2104K10ACSC-10-256G-8", "band": null, "color": null, "memory": null, "type": null, "invId": null}, {"offerId": "17836020", "offerName": "小米M2007J22CSC[青-128G-8G]", "offerCode": "JSYD-MI-M2007J22CSC-20-128G-8", "band": null, "color": null, "memory": null, "type": null, "invId": null}, {"offerId": "18531033", "offerName": "OPPOPEDM00SC[蓝-128G-8G]", "offerCode": "JSYD-OPPO-PEDM00SC-04-128G-8", "band": null, "color": null, "memory": null, "type": null, "invId": null}, {"offerId": "17280013", "offerName": "华为FRL-TN00SC[银-128G-6G]", "offerCode": "JSYD-HUAWEI-FRL-TN00SC-10-128G-6", "band": null, "color": null, "memory": null, "type": null, "invId": null}, {"offerId": "17388002", "offerName": "OPPOPEAM00SC[黑-128G-8G]", "offerCode": "JSYD-OPPO-PEAM00SC-01-128G-8", "band": null, "color": null, "memory": null, "type": null, "invId": null}, {"offerId": "15811020", "offerName": "华为TAS-AN00SC[紫-128G-8G]", "offerCode": "JSYD-HUAWEI-TAS-AN00SC-12-128G-8", "band": null, "color": null, "memory": null, "type": null, "invId": null}, {"offerId": "17856004", "offerName": "小米M2007J17CSC[绿-128G-8G]", "offerCode": "JSYD-MI-M2007J17CSC-13-128G-8", "band": null, "color": null, "memory": null, "type": null, "invId": null}]}}, "h5QueryOrderList": {"retCode": "0", "retMsg": null, "data": {"total": 8, "pages": 1, "pageData": [{"orderSeq": "20210628171600100008", "dwUser": "daib", "dwName": "孙悟空", "insertCrmId": "14150758", "region": "14", "customerName": "客户名称", "linkPhone": "***********", "busiType": "1", "address": null, "idea": null, "createTime": "2021-06-28 17:16:00", "feedbackSeq": "20210629094608100016", "feedbackState": "3", "feedbackRemark": null, "appointDate": "2021-06-29 22:25:44", "feedbackCreateTime": null}]}}, "h5QueryOrderDetail": {"retCode": "0", "retMsg": null, "data": {"orderSeq": "20210628171600100008", "dwUser": "daib", "dwName": "孙悟空", "insertCrmId": "14150759", "region": "14", "customerName": "客户名称", "linkPhone": "***********", "busiType": "1", "address": "1111111111111111111111111111111,,,qrqw", "idea": "小儿子买了手机，没有卡。要办个卡，最好加入亲情网。", "createTime": "2021-06-28 17:16:00", "feedbackSeq": "20210629094608100016", "feedbackState": "3", "feedbackRemark": "我全都要", "appointDate": "", "feedbackCreateTime": "2021-06-29 09:46:08"}}, "h5QryBusinessType": {"retCode": "0", "retMsg": null, "data": [{"offerId": "1", "offerName": "号卡销售", "operType": "1"}, {"offerId": "2", "offerName": "手机销售", "operType": "2"}, {"offerId": "3", "offerName": "宽带", "operType": "3"}]}, "h5queryDictInfo": {"retCode": "0", "retMsg": null, "data": {"1": "号卡销售", "2": "手机销售", "3": "宽带"}}, "h5getDictInfo": {"retCode": "0", "retMsg": null, "data": [{"id": "2", "label": "OPPO1", "valueName": "oppo"}, {"id": "3", "label": "vivo", "valueName": "vivo"}, {"id": "4", "label": "华为", "valueName": "hua<PERSON>"}, {"id": "5", "label": "苹果", "valueName": "apple"}, {"id": "6", "label": "realme", "valueName": "realme"}, {"id": "7", "label": "小米", "valueName": "xia<PERSON>"}, {"id": "8", "label": "三星", "valueName": "samsung"}, {"id": "9", "label": "一加", "valueName": "oneplus"}, {"id": "10", "label": "中兴", "valueName": "zte"}, {"id": "1", "label": "荣耀", "valueName": "honor"}]}}