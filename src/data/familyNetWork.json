{"h5GetOrderInfo": {"retCode": "0", "retMsg": null, "data": {"subsid": "1419101362793807", "offerlist": [{"instanceid": "68000273346006", "offerid": "1000100301", "offername": "4G飞享（预付费）", "isprimary": "Y", "maininstid": "68000273346006", "ismcoffer": "N", "isbundled": "N", "bundleinstid": "", "effectdate": "20190801000000", "expiredate": "21001231000000", "status": "2", "createdate": "20190717053402", "createorderid": "200195797050404798", "modifydate": "20191226150423", "lastmodifyorderid": "200224846106584798", "saleschanneltype": "6", "saleschannelid": "14002746", "beid": "14", "sellobject": "", "attrlist": null}]}}, "h5QryBandInfo": {"retCode": "0", "retMsg": null, "data": {"telnum": "13776626497", "rangeType": "1", "districtName": "书香名苑", "addrName": "南京地区江宁东山街道天印大道书香名苑22栋4单元607室", "factoryType": "1", "supplyType": "1", "radiusType": "1", "linkMan": "", "contactPhone": "", "gimsUserType": "1", "districtId": "9758", "addrId": "1041203842", "netWorkType": "1", "limitBandWidth": "102400"}}, "h5QryChooseProduct": {"retCode": "0", "retMsg": null, "data": [{"maxnumber": "1", "attrnum": "12", "memid": "6080001001001", "offername": "家庭组网基础服务", "isbundled": "N", "memorder": "3", "validunit": "T", "minnumber": "1", "selecttype": "M"}, {"maxnumber": "1", "attrnum": "0", "memid": "6080001001002", "offername": "基础服务", "isbundled": "N", "memorder": "4", "validunit": "T", "minnumber": "0", "selecttype": "O"}, {"maxnumber": "1", "attrnum": "0", "memid": "6080001001003", "offername": "尊享服务一", "isbundled": "N", "memorder": "4", "validunit": "T", "minnumber": "0", "selecttype": "O"}]}}