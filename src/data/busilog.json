{"h5qryTotalBusiLog": {"retCode": "0", "retMsg": "", "data": [{"total": "3", "money": "51"}]}, "h5qryTypeBusiLog": {"retCode": "0", "retMsg": null, "data": [{"busiType": "group_v_net_add", "busiName": "集团V网添加新成员", "typeTotal": "1", "typeMoney": "60"}, {"busiType": "mband_prod_kaitong", "busiName": "宽带开通", "typeTotal": "1", "typeMoney": "55"}]}, "h5qryOrderIdByTel": {"retCode": "0", "retMsg": "订单信息查询成功", "data": "18110261010142730030"}, "h5qryBusiLogList": {"retCode": "0", "retMsg": "查询成功", "data": [{"busiType": "mband_prod_kaitong", "createOper": "********", "region": "14", "createDate": "2020/3/10", "outSrl": "20203101111111", "innerSrl": "20203101111111", "state": "0", "amount": "100000", "msisdn": "13901582505", "payType": "0", "payStatus": "0", "orderId": "111111", "busiName": "宽带开通"}, {"busiType": "mband_prod_kaitong", "createOper": "********", "region": "14", "createDate": "2020/3/10", "outSrl": "20203101111111", "innerSrl": "20203101111111", "state": "0", "amount": "100000", "msisdn": "13901582505", "payType": "0", "payStatus": "0", "orderId": "111111", "busiName": "宽带开通"}]}, "h5orderdetail": {"retCode": "0", "retMsg": "订单信息查询成功", "data": {"customerInfo": {"install_addr": "浦欣家园", "cust_name": "毛玉明", "appointment_time": "", "contact_phone": "13585178362"}, "installMsgInfo": {"install_finish_time": "2018-07-31 22:47:35", "install_staff_name": "社会我哥", "install_staff_phone": "***********"}, "tacheMsgInfo1": {"tache_state_time": "2018-07-31 22:47:35", "tache_state_value": "成功", "tache_state_name": "订单提交"}, "tacheMsgInfo2": {"tache_state_time": "2018-07-31 22:47:35", "tache_state_value": "装机中", "tache_state_name": "安装服务"}, "tacheMsgInfo3": {"tache_state_time": "2018-07-31 22:47:35", "tache_state_value": "成功", "tache_state_name": "订单支付"}, "accountMsgInfo": {"account_sysnc_name": "开户成功、账号同步成功", "account_sysnc_time": "**************"}}}, "h5qryNewTotalBusiLog": {"retCode": "0", "retMsg": "", "data": {"total": "27", "money": "250.12", "success": "22", "fail": "5"}}, "h5qryNewTypeBusiLog": {"retCode": "0", "retMsg": null, "data": [{"busiType": "TERMINA_SELL", "busiName": "营销案办理", "typeTotal": "5", "typeMoney": "150"}, {"busiType": "mband_prod_kaitong", "busiName": "宽带产品开通宽带产品开通", "typeTotal": "2", "typeMoney": "99.92"}, {"busiType": "true_name_record", "busiName": "资料补录", "typeTotal": "1", "typeMoney": "0"}]}, "h5qryNewBusiLogList": {"retCode": "0", "retMsg": null, "data": [{"busiType": "mband_prod_kaitong", "createOper": "********", "region": "14", "createDate": "2020-04-30 19:05", "outSrl": "**************", "innerSrl": "**************", "state": "0", "amount": "3000", "msisdn": "***********", "payType": "1", "payStatus": "1", "orderId": null, "busiName": "宽带开通", "offername": null}, {"busiType": "mband_prod_kaitong", "createOper": "********", "region": "14", "createDate": "2018-06-08 15:02", "outSrl": "20180529547612", "innerSrl": "20180529689367", "state": "0", "amount": "3000", "msisdn": "16646455458", "payType": "2", "payStatus": "1", "orderId": null, "busiName": "宽带开通", "offername": null}, {"busiType": "mband_prod_kaitong", "createOper": "********", "region": "14", "createDate": "2018-06-08 15:01", "outSrl": "20180529547607", "innerSrl": "20180529689366", "state": "0", "amount": "1200", "msisdn": "18251896666", "payType": null, "payStatus": "1", "orderId": null, "busiName": "宽带开通", "offername": null}, {"busiType": "mband_prod_kaitong", "createOper": "********", "region": "14", "createDate": "2018-06-08 14:49", "outSrl": "20180529547602", "innerSrl": "20180529689365", "state": "0", "amount": "1600", "msisdn": "18251896292", "payType": null, "payStatus": "1", "orderId": null, "busiName": "宽带开通", "offername": null}]}, "h5qryNewOrderIdByTel": {"retCode": "0", "retMsg": "订单号查询成功", "data": "18110261010142730030"}, "h5newOrderDetail": {"retCode": "0", "retMsg": "订单信息查询成功", "data": {"customerInfo": {"install_addr": "浦欣家园", "cust_name": "毛玉明", "appointment_time": "", "contact_phone": "13585178362"}, "installMsgInfo": {"install_finish_time": "2018-07-31 22:47:35", "install_staff_name": "赵六", "install_staff_phone": "***********"}, "tacheMsgInfo1": {"tache_state_time": "2018-07-31 22:47:35", "tache_state_value": "成功", "tache_state_name": "订单提交"}, "tacheMsgInfo2": {"tache_state_time": "2018-07-31 22:47:35", "tache_state_value": "装机中", "tache_state_name": "安装服务"}, "tacheMsgInfo3": {"tache_state_time": "2018-07-31 22:47:35", "tache_state_value": "成功", "tache_state_name": "订单支付"}, "accountMsgInfo": {"account_sysnc_name": "开户成功、账号同步成功", "account_sysnc_time": "**************"}}}, "h5qryLeaderAuth": {"retCode": "-1", "retMsg": null, "data": [{"name": "全部", "crmId": null, "staffId": null, "label": "全部", "value": null}, {"name": "王露露", "crmId": null, "staffId": "****************", "label": "王露露", "value": "****************"}, {"name": "张润", "crmId": null, "staffId": "****************", "label": "张润", "value": "****************"}, {"name": "彭加梅", "crmId": null, "staffId": "****************", "label": "彭加梅", "value": "****************"}, {"name": "项东东", "crmId": null, "staffId": "****************", "label": "项东东", "value": "****************"}, {"name": "谭德新", "crmId": null, "staffId": "****************", "label": "谭德新", "value": "****************"}, {"name": "李金涛", "crmId": null, "staffId": "****************", "label": "李金涛", "value": "****************"}, {"name": "王红霞", "crmId": null, "staffId": "****************", "label": "王红霞", "value": "****************"}, {"name": "田福星", "crmId": null, "staffId": "1476001211175618", "label": "田福星", "value": "1476001211175618"}, {"name": "张龙", "crmId": null, "staffId": "1488021211048134", "label": "张龙", "value": "1488021211048134"}, {"name": "闫静静", "crmId": null, "staffId": "1488022590742402", "label": "闫静静", "value": "1488022590742402"}, {"name": "杨霞", "crmId": null, "staffId": "1488021821277642", "label": "杨霞", "value": "1488021821277642"}, {"name": "刘吉银", "crmId": null, "staffId": "1476001211175570", "label": "刘吉银", "value": "1476001211175570"}, {"name": "夏蕾", "crmId": null, "staffId": "1488021211030142", "label": "夏蕾", "value": "1488021211030142"}, {"name": "丁西凤", "crmId": null, "staffId": "1488021910323634", "label": "丁西凤", "value": "1488021910323634"}, {"name": "张志华", "crmId": null, "staffId": "1488020173152902", "label": "张志华", "value": "1488020173152902"}, {"name": "杜飞 ", "crmId": null, "staffId": "1488021211012746", "label": "杜飞 ", "value": "1488021211012746"}, {"name": "宋雪龄", "crmId": null, "staffId": "1488021211035254", "label": "宋雪龄", "value": "1488021211035254"}, {"name": "史晶晶", "crmId": null, "staffId": "880026788", "label": "史晶晶", "value": "880026788"}, {"name": "张艳", "crmId": null, "staffId": "1488021910446698", "label": "张艳", "value": "1488021910446698"}, {"name": "王耀", "crmId": null, "staffId": "1488022349926758", "label": "王耀", "value": "1488022349926758"}, {"name": "吴益东", "crmId": null, "staffId": "1488021821298830", "label": "吴益东", "value": "1488021821298830"}, {"name": "谭远征", "crmId": null, "staffId": "1488022590725222", "label": "谭远征", "value": "1488022590725222"}, {"name": "阚红艳", "crmId": null, "staffId": "1488021910428514", "label": "阚红艳", "value": "1488021910428514"}, {"name": "卞自贤", "crmId": null, "staffId": "1488021708988262", "label": "卞自贤", "value": "1488021708988262"}, {"name": "毛荣庆", "crmId": null, "staffId": "1488022868557882", "label": "毛荣庆", "value": "1488022868557882"}, {"name": "蒋成健", "crmId": null, "staffId": "1488022017892730", "label": "蒋成健", "value": "1488022017892730"}, {"name": "马亚洲", "crmId": null, "staffId": "1488022017946118", "label": "马亚洲", "value": "1488022017946118"}, {"name": "朱国强", "crmId": null, "staffId": "1488021821328306", "label": "朱国强", "value": "1488021821328306"}, {"name": "晏朗秦", "crmId": null, "staffId": "1488021821367850", "label": "晏朗秦", "value": "1488021821367850"}, {"name": "吴苏湘", "crmId": null, "staffId": "1488021709002058", "label": "吴苏湘", "value": "1488021709002058"}, {"name": "李玲", "crmId": null, "staffId": "1488022714659034", "label": "李玲", "value": "1488022714659034"}, {"name": "高强", "crmId": null, "staffId": "1488022349981718", "label": "高强", "value": "1488022349981718"}, {"name": "刘娜", "crmId": null, "staffId": "1488022590796590", "label": "刘娜", "value": "1488022590796590"}, {"name": "许红", "crmId": null, "staffId": "1488022017854698", "label": "许红", "value": "1488022017854698"}, {"name": "<PERSON>江", "crmId": null, "staffId": "1488019476681710", "label": "<PERSON>江", "value": "1488019476681710"}]}, "h5queryUserCj": {"retCode": "0", "retMsg": "success", "data": {"totalAmount": "1332", "amountDetail": [{"busiType": "true_name_record", "busiName": "资料补录", "amount": "1200"}, {"busiType": "mband_prod_kaitong", "busiName": "宽带产品开通", "amount": "132"}]}}, "h5qryTypeBusiLogByOrgId": {"retCode": "0", "retMsg": null, "data": [{"": "enter_net", "busiName": "选号入网", "typeTotal": "28", "typeMoney": "600"}, {"busiType": "mband_prod_change", "busiName": "宽带产品变更", "typeTotal": "1", "typeMoney": "0"}, {"busiType": "mband_prod_kaitong", "busiName": "宽带产品开通", "typeTotal": "30", "typeMoney": "1124"}, {"busiType": "true_name_record", "busiName": "资料补录", "typeTotal": "40", "typeMoney": "0"}, {"busiType": "wanneng_prod", "busiName": "万能副卡", "typeTotal": "3", "typeMoney": "0"}, {"busiType": "zengzhi_prod", "busiName": "111111111", "typeTotal": "4", "typeMoney": null}, {"busiType": "zhuti_prod", "busiName": "主体产品变更", "typeTotal": "11", "typeMoney": null}]}}