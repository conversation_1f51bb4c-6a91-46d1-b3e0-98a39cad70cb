{"h5QryYiwtongAccountInfo": {"retCode": "0", "retMsg": "成功", "data": {"subsId": "********", "serviceNumber": "*********", "subsName": "公司", "status": "2", "networkType": "1"}}, "h5QryTongfuGroupCheckResult": {"retCode": "0", "retMsg": "成功", "data": ""}, "h5QryVirtualGroupInfo": {"retCode": "0", "retMsg": "成功", "data": {"groupId": "********", "groupName": "公司", "status": "stcmNml"}}, "h5CreateVirtualGroup": {"retCode": "0", "retMsg": "success", "data": {"groupId": "***********"}}, "h5QryYiwtongOfferingInfo": {"retCode": "0", "retMsg": "success", "data": {"offeringInfoList": [{"offeringCode": "********06", "offeringName": "100M融合套餐", "offeringDesc": "100M融合套餐", "rightsList": [{"rightsId": "********06", "rightsName": "组网", "selectType": "Y"}]}, {"offeringCode": "*********", "offeringName": "电视100M融合套餐", "offeringDesc": "电视100M融合套餐", "rightsList": [{"rightsId": "********02", "rightsName": "电视", "selectType": "Y"}, {"rightsId": "********03", "rightsName": "和睦", "selectType": "N"}, {"rightsId": "********01", "rightsName": "和睦", "selectType": "N"}]}, {"offeringCode": "*********", "offeringName": "测试测试", "offeringDesc": "测试测试1", "rightsList": [{"rightsId": "********02", "rightsName": "电视", "selectType": "Y"}, {"rightsId": "********04", "rightsName": "电视", "selectType": "N"}]}]}}, "h5QryOfferingRightsInfo": {"retCode": "0", "retMsg": "success", "data": {"wbandInfo": {"rightsId": "********01", "rightsName": "宽带权益", "serviceNum": "*********", "limitBandWidth": "102400", "serviceType": "1"}, "nettvInfo": {"rightsId": "********02", "rightsName": "电视权益", "tvServiceNum": "*********", "tvMode": "CP"}, "hemuInfo": {"rightsId": "********03", "rightsName": "和目权益", "storage": "7", "loginNumber": "*********"}, "sppackageInfo": {"rightsId": "********04", "rightsName": "增值包权益", "additionalOfferType": [{"id": "1", "name": "类型一"}, {"id": "2", "name": "类型二"}]}}}, "h5QryInstallTimeOfTheDay": {"retCode": "0", "retMsg": "success", "data": {"installTime": "14:00"}}, "h5QryDeviceRelationInfo": {"retCode": "0", "retMsg": "success", "data": {"doCancelDeposit": "Y", "deviceRelationList": [{"isMustChoice": "Y", "deviceReceive": "3", "offeringId": "151000000000000101", "offeringName": "家庭网关", "price": "100"}], "noDeviceRelationList": [{"isMustChoice": "Y", "deviceReceive": "3", "offeringId": "151000000000000101", "offeringName": "魔百盒", "price": "200"}, {"isMustChoice": "N", "deviceReceive": "3", "offeringId": "151000000000000102", "offeringName": "魔百盒1", "price": "100"}, {"isMustChoice": "N", "deviceReceive": "3", "offeringId": "151000000000000103", "offeringName": "魔百盒2", "price": "240"}]}}, "h5QryXqAddressInfo": {"retCode": "0", "retMsg": "success", "data": {"uptownList": [{"uptownId": "3536", "uptownName": "幸福家园", "addressId": "2984631", "addressName": "苏州地区苏州市区姑苏区干将西路幸福家园1", "otherAgent": "1", "status": "1", "projectManager": "1", "rediusFor": "1", "userType": "1", "areaType": "1", "areaCode": "1101", "networkType": "1", "geoX": "22", "geoY": "24", "bandWidth": "102400"}, {"uptownId": "3536", "uptownName": "幸福家园", "addressId": "2984631", "addressName": "苏州地区苏州市区姑苏区干将西路幸福家园2", "otherAgent": "1", "status": "1", "projectManager": "1", "rediusFor": "1", "userType": "1", "areaType": "1", "areaCode": "1101", "networkType": "1", "geoX": "22", "geoY": "24", "bandWidth": "102400"}, {"uptownId": "3536", "uptownName": "幸福家园", "addressId": "2984631", "addressName": "苏州地区苏州市区姑苏区干将西路幸福家园3", "otherAgent": "1", "status": "1", "projectManager": "1", "rediusFor": "1", "userType": "1", "areaType": "1", "areaCode": "1101", "networkType": "1", "geoX": "22", "geoY": "24", "bandWidth": "102400"}, {"uptownId": "3536", "uptownName": "幸福家园", "addressId": "2984631", "addressName": "苏州地区苏州市区姑苏区干将西路幸福家园4", "otherAgent": "1", "status": "1", "projectManager": "1", "rediusFor": "1", "userType": "1", "areaType": "1", "areaCode": "1101", "networkType": "1", "geoX": "22", "geoY": "24", "bandWidth": "102400"}]}}, "h5QryLowerAddressInfo": {"retCode": "0", "retMsg": "success", "data": {"addressList": [{"addressId": "3584631", "addressName": "101室", "fullName": "苏州地区苏州市区姑苏区干将西路幸福家园2栋3单元101室", "addrLevel": "10", "parentId": "3584630", "areaCode": "1101", "isCoverpoint": "1", "uptownAddressId": "2984631", "addrRemark": "2984631"}, {"addressId": "3584631", "addressName": "102室", "fullName": "苏州地区苏州市区姑苏区干将西路幸福家园2栋3单元102室", "addrLevel": "10", "parentId": "3584630", "areaCode": "1101", "isCoverpoint": "0", "uptownAddressId": "2984631", "addrRemark": "2984631"}, {"addressId": "3584631", "addressName": "201室", "fullName": "苏州地区苏州市区姑苏区干将西路幸福家园2栋3单元201室", "addrLevel": "10", "parentId": "3584630", "areaCode": "1101", "isCoverpoint": "0", "uptownAddressId": "2984631", "addrRemark": "2984631"}, {"addressId": "3584631", "addressName": "202室", "fullName": "苏州地区苏州市区姑苏区干将西路幸福家园2栋3单元202室", "addrLevel": "10", "parentId": "3584630", "areaCode": "1101", "isCoverpoint": "0", "uptownAddressId": "2984631", "addrRemark": "2984631"}, {"addressId": "3584631", "addressName": "301室", "fullName": "苏州地区苏州市区姑苏区干将西路幸福家园2栋3单元301室", "addrLevel": "10", "parentId": "3584630", "areaCode": "1101", "isCoverpoint": "0", "uptownAddressId": "2984631", "addrRemark": "2984631"}]}}, "h5QryRegionAreaInfo": {"retCode": "0", "retMsg": "success", "data": {"areaInfoList": [{"id": "1423", "label": "江宁"}, {"id": "1422", "label": "六合"}]}}, "h5QryAddrDetailInfo": {"retCode": "0", "retMsg": "success", "data": {"addrDetailInfo": {"addressId": "121123845", "ponType": "10GPON", "reason": "111", "networkType": "1", "uptownId": "906194", "districtId": "906194", "districtName": "秦馀广场", "uptownAddressId": "121123823", "isEmpty": "1", "uptownName": "秦馀广场", "fullName": "苏州地区西区分公司新区凤凰峰路秦馀广场无门牌1栋1层101", "addressName": "101"}}}, "h5OrderCreateSubmit": {"retCode": "0", "retMsg": "success", "data": {"recId": "1111111123"}}, "h5QueryOneNetFlow": {"retCode": "0", "retMsg": "suuccess", "data": {"workFlowName": "流程名称", "orderStatus": "订单状态", "flowNodeResp": [{"tacheName": "环节名称", "tacheType": "环节类型", "createDate": "开始时间", "dealDate": "结束时间", "dealDesc": "环节处理描述", "opUserId": "工号", "opUserName": "姓名", "opUserPhone": "手机号", "nodeStatus": "节点状态"}]}}, "h5cancelOneNetOrder": {"retCode": "0", "retMsg": "success", "data": {}}}