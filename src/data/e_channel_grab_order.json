{"h5searchOrgid": {"retCode": "0", "retMsg": null, "data": {"opratorName": "冯瑜", "orgId": "14174470", "orgName": "宜兴经营部", "region": "14", "cityName": null, "countryId": null, "countryName": null}}, "h5grabsheetNum": {"retCode": "0", "retMsg": "【查询抢单数量FSOP2BDS1222】success", "data": {"myTotalNumber": "15", "myUntreatedTotalNumber": "10", "myUntreatedEchannelNumber": "4", "myUntreatedOutcallNumber": "3", "myUntreatedIntelthreeNumber": "2", "myUntreatedIntelphoneNumber": "1", "untreatedEchannelNumber": "5", "untreatedOutcallNumber": "2", "untreatedIntelthreeNumber": "1", "untreatedIntelphoneNumber": "1", "untreatedTotalNumber": "9"}}, "h5grabsheetMessage": {"retCode": "0", "retMsg": "【抢单信息查询 FSOP2BDS1223】success", "data": [{"orderId": "19081770938370000163", "offerId": "1000100462", "offerName": "1000100462(2000009684)", "orderStatus": "88", "grabStatus": "0", "deliveryType": "WXWS", "createTime": "20190319093837", "remark": "1", "cityId": "18", "accNbrr": "", "serviceCode": "10000", "factAmount": "0", "shipAddr": "1", "deliveryPeriod": "", "grabStaff": "", "dealMessage": "", "grabTime": "201903190938", "cancelTime": "", "appointmentTime": "201903190938", "contactPhone": "15903181134", "contactName": "1", "orderFrom": "120", "grabType": "02"}, {"orderId": "19102772055380000064", "offerId": "1000100324", "offerName": "万能副卡", "orderStatus": "40", "grabStatus": "0", "deliveryType": "WXTS", "createTime": "20190409205539", "remark": "万能副卡", "cityId": "14", "accNbrr": "", "serviceCode": "10000", "factAmount": "0", "shipAddr": "江苏省南京江宁区禄口街道高伏高家村19号", "deliveryPeriod": "", "grabStaff": "", "dealMessage": "", "grabTime": "", "cancelTime": "20190411205539", "appointmentTime": "", "contactPhone": "***********", "contactName": "王凡", "orderFrom": "103", "grabType": "05"}, {"orderId": "19103770908090000122", "offerId": "1000100324", "offerName": "万能副卡", "orderStatus": "40", "grabStatus": "0", "deliveryType": "WXTS", "createTime": "20190410090810", "remark": "万能副卡", "cityId": "14", "accNbrr": "", "serviceCode": "10000", "factAmount": "0", "shipAddr": "江苏省南京江宁区禄口街道高伏高家村19号", "deliveryPeriod": "", "grabStaff": "", "dealMessage": "", "grabTime": "", "cancelTime": "20190412090810", "appointmentTime": "", "contactPhone": "***********", "contactName": "王凡", "orderFrom": "103", "grabType": "05"}, {"orderId": "19103770909430000124", "offerId": "1000100324", "offerName": "万能副卡", "orderStatus": "40", "grabStatus": "0", "deliveryType": "WXTS", "createTime": "20190410090943", "remark": "万能副卡", "cityId": "14", "accNbrr": "", "serviceCode": "10000", "factAmount": "0", "shipAddr": "江苏省南京江宁区禄口街道高伏高家村19号", "deliveryPeriod": "", "grabStaff": "", "dealMessage": "", "grabTime": "", "cancelTime": "20190412090943", "appointmentTime": "", "contactPhone": "***********", "contactName": "王凡", "orderFrom": "103", "grabType": "05"}, {"orderId": "19070771620520000776", "offerId": "1000100462", "offerName": "1000100462(2000009684)", "orderStatus": "99", "grabStatus": "2", "deliveryType": "WXTS", "createTime": "20190308162052", "remark": "南京_18元超级王卡超级王卡超级王卡超级王卡超级王卡超级王卡超级王卡", "cityId": "18", "accNbrr": "", "serviceCode": "10000", "factAmount": "0", "shipAddr": "请选择", "deliveryPeriod": "", "grabStaff": "********", "dealMessage": "其他,已联系客户不在家", "grabTime": "20190410111908", "cancelTime": "20190410131908", "appointmentTime": "20190508162052", "contactPhone": "15150504727", "contactName": "王唯", "orderFrom": "103", "grabType": "04"}, {"orderId": "19049771419460000024", "offerId": "", "offerName": "test", "orderStatus": "88", "grabStatus": "3", "deliveryType": "WXWS", "createTime": "20190218141946", "remark": "订单备注信息", "cityId": "18", "accNbrr": "", "serviceCode": "10000", "factAmount": "0", "shipAddr": "我勒个去", "deliveryPeriod": "客户收货时间段描述", "grabStaff": "********", "dealMessage": "", "grabTime": "20190218142018", "cancelTime": "20190320142018", "appointmentTime": "20190124172000", "contactPhone": "14751736538", "contactName": "石等伟", "orderFrom": "120", "grabType": "04"}, {"orderId": "19070771600200000742", "offerId": "1000100462", "offerName": "1000100462(2000009684)", "orderStatus": "40", "grabStatus": "2", "deliveryType": "WXTS", "createTime": "20190308160020", "remark": "南京_18元超级王卡超级王卡超级王卡超级王卡超级王卡超级王卡超级王卡", "cityId": "18", "accNbrr": "", "serviceCode": "10000", "factAmount": "0", "shipAddr": "请选择", "deliveryPeriod": "", "grabStaff": "********", "dealMessage": "", "grabTime": "20190308160051", "cancelTime": "20190308180051", "appointmentTime": "", "contactPhone": "15150504727", "contactName": "王唯", "orderFrom": "103", "grabType": "04"}]}, "h5dispatch": {"retCode": "0", "retMsg": "【查询可派单人员信息 FSOP2BDS1224】success", "data": [{"userName": "05qd1", "realName": "抢单01", "accNbr": "***********", "cityId": "19", "positionCode": "YG", "agencyCode": "********"}, {"userName": "********", "realName": "超级管理员", "accNbr": "***********", "cityId": "99", "positionCode": "GLY", "agencyCode": "********"}]}, "h5transfer": {"retCode": "0", "retMsg": "【查询代理商机构 FSOP2BDS1225】success", "data": [{"cityId": "14", "agencyName": "南京", "agencyCode": "********"}, {"cityId": "14", "agencyName": "南京鼓楼浙江行商团队", "agencyCode": "********"}]}, "h5accountExe": {"retCode": "0", "retMsg": "【抢单业务执行 FSOP2BDS1226】success", "data": null}}