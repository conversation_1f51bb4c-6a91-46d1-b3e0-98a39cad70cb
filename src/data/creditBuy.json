{"h5Underwriters": {"retCode": "0", "retMsg": "", "data": [{"agentmerno": "01", "agentmername": "蜂云"}, {"agentmerno": "02", "agentmername": "蜂泰"}]}, "h5getOrgId": {"retCode": "0", "retMsg": "", "data": {"opratorName": "冯瑜", "orgId": "14174470", "orgName": "宜兴经营部", "region": "101019003", "cityName": "南京", "countryId": "1413", "countryName": "江宁"}}, "h5preCreditLoan": {"retCode": "0", "retMsg": "Success", "data": {"pid": "F18209771654510000001"}}, "h5creditLoan": {"retCode": "0", "retMsg": "Success", "data": {"paycode": "http://p.12580.com/wps/service/WapFormTrans.xhtml?sessionid=20180929201809291033243416394899", "freezamt": "2000.00", "loanamt": "2000.00", "mplordno": "50620171210094153004035918", "mplorddt": "20171210"}}, "h5getOrders": {"retCode": "0", "retMsg": "查询订单成功", "data": [{"srlid": "2019102410000007", "telnum": "18451114569", "prepayid": "F18209771654510000001", "status": "3", "createDate": "2019-10-24 14:39:41", "operateDate": "2019-10-25 14:39:41", "payurl": null, "activeid": null, "activename": "和分期批次3", "levelid": null, "levelname": "和分期档次", "rewardid": null, "rewardname": "鸡蛋买一送二", "agentmerno": null, "agentmername": "蜂云", "fee": "240000", "stagenum": "12", "depid": null, "depnm": null, "canclestate": "1", "couponCode": "23456", "couponStatus": "03"}, {"srlid": "2019102410000006", "telnum": "18451114569", "prepayid": "F18209771654510000001", "status": "1", "createDate": "2019-10-24 14:26:18", "payurl": null, "activeid": null, "activename": "和分期批次3", "levelid": null, "levelname": "和分期档次", "rewardid": null, "rewardname": "鸡蛋买一送二", "agentmerno": null, "agentmername": "蜂云", "fee": "240000", "stagenum": "12", "depid": null, "depnm": null, "couponCode": "23456", "couponStatus": "03"}, {"srlid": "2019102410000002", "telnum": "18451114569", "prepayid": "F18209771654510000001", "status": "2", "createDate": "2019-10-24 11:14:11", "payurl": "http://p.12580.com/wps/service/WapFormTrans.xhtml?sessionid=20180929201809291033243416394899", "activeid": null, "activename": "和分期批次3", "levelid": null, "levelname": "和分期档次", "rewardid": null, "rewardname": "鸡蛋买一送二", "agentmerno": null, "agentmername": "蜂云", "fee": "240000", "stagenum": "12", "depid": null, "depnm": null, "couponCode": "23456", "couponStatus": "03"}, {"srlid": "2019102410000002", "telnum": "18451114569", "prepayid": "F18209771654510000001", "status": "3", "createDate": "2019-10-24 11:14:11", "payurl": "http://p.12580.com/wps/service/WapFormTrans.xhtml?sessionid=20180929201809291033243416394899", "activeid": null, "activename": "和分期批次3", "levelid": null, "levelname": "和分期档次", "rewardid": null, "rewardname": "鸡蛋买一送二", "agentmerno": null, "agentmername": "蜂云", "fee": "240000", "stagenum": "12", "depid": null, "depnm": null}, {"srlid": "2019102410000002", "telnum": "18451114569", "prepayid": "F18209771654510000001", "status": "4", "createDate": "2019-10-24 11:14:11", "payurl": "http://p.12580.com/wps/service/WapFormTrans.xhtml?sessionid=20180929201809291033243416394899", "activeid": null, "activename": "和分期批次3", "levelid": null, "levelname": "和分期档次", "rewardid": null, "rewardname": "鸡蛋买一送二", "agentmerno": null, "agentmername": "蜂云", "fee": "240000", "stagenum": "12", "depid": null, "depnm": null}, {"srlid": "2019102410000002", "telnum": "18451114569", "prepayid": "F18209771654510000001", "status": "5", "createDate": "2019-10-24 11:14:11", "payurl": "http://p.12580.com/wps/service/WapFormTrans.xhtml?sessionid=20180929201809291033243416394899", "activeid": null, "activename": "和分期批次3", "levelid": null, "levelname": "和分期档次", "rewardid": null, "rewardname": "鸡蛋买一送二", "agentmerno": null, "agentmername": "蜂云", "fee": "240000", "stagenum": "12", "depid": null, "depnm": null}, {"srlid": "2019102410000002", "telnum": "18451114569", "prepayid": "F18209771654510000001", "status": "6", "createDate": "2019-10-24 11:14:11", "payurl": "http://p.12580.com/wps/service/WapFormTrans.xhtml?sessionid=20180929201809291033243416394899", "activeid": null, "activename": "和分期批次3", "levelid": null, "levelname": "和分期档次", "rewardid": null, "rewardname": "鸡蛋买一送二", "agentmerno": null, "agentmername": "蜂云", "fee": "240000", "stagenum": "12", "depid": null, "depnm": null}, {"srlid": "2019102410000002", "telnum": "18451114569", "prepayid": "F18209771654510000001", "status": "7", "createDate": "2019-10-24 11:14:11", "payurl": "http://p.12580.com/wps/service/WapFormTrans.xhtml?sessionid=20180929201809291033243416394899", "activeid": null, "activename": "和分期批次3", "levelid": null, "levelname": "和分期档次", "rewardid": null, "rewardname": "鸡蛋买一送二", "agentmerno": null, "agentmername": "蜂云", "fee": "240000", "stagenum": "12", "depid": null, "depnm": null}, {"srlid": "2019102410000002", "telnum": "18451114569", "prepayid": "F18209771654510000001", "status": "8", "createDate": "2019-10-24 11:14:11", "payurl": "http://p.12580.com/wps/service/WapFormTrans.xhtml?sessionid=20180929201809291033243416394899", "activeid": null, "activename": "和分期批次3", "levelid": null, "levelname": "和分期档次", "rewardid": null, "rewardname": "鸡蛋买一送二", "agentmerno": null, "agentmername": "蜂云", "fee": "240000", "stagenum": "12", "depid": null, "depnm": null}, {"srlid": "2019102410000002", "telnum": "18451114569", "prepayid": "F18209771654510000001", "status": "8", "createDate": "2019-10-24 11:14:11", "operateDate": "2019-10-25 14:39:41", "payurl": "http://p.12580.com/wps/service/WapFormTrans.xhtml?sessionid=20180929201809291033243416394899", "activeid": null, "activename": "和分期批次3", "levelid": null, "levelname": "和分期档次", "rewardid": null, "rewardname": "鸡蛋买一送二", "agentmerno": null, "agentmername": "蜂云", "fee": "240000", "stagenum": "12", "depid": null, "depnm": null, "canclestate": "1"}, {"srlid": "2019102410000002", "telnum": "18451114569", "prepayid": "F18209771654510000001", "status": "9", "createDate": "2019-10-24 11:14:11", "payurl": "http://p.12580.com/wps/service/WapFormTrans.xhtml?sessionid=20180929201809291033243416394899", "activeid": null, "activename": "和分期批次3", "levelid": null, "levelname": "和分期档次", "rewardid": null, "rewardname": "鸡蛋买一送二", "agentmerno": null, "agentmername": "蜂云", "fee": "240000", "stagenum": "12", "depid": null, "depnm": null}, {"srlid": "2019102410000002", "telnum": "18451114569", "prepayid": "F18209771654510000001", "status": "10", "createDate": "2019-10-24 11:14:11", "payurl": "http://p.12580.com/wps/service/WapFormTrans.xhtml?sessionid=20180929201809291033243416394899", "activeid": null, "activename": "和分期批次3", "levelid": null, "levelname": "和分期档次", "rewardid": null, "rewardname": "鸡蛋买一送二", "agentmerno": null, "agentmername": "蜂云", "fee": "240000", "stagenum": "12", "depid": null, "depnm": null}]}, "h5submit": {"retCode": "0", "retMsg": null, "data": null}, "h5upLoadImg": {"retCode": "0", "retMsg": "成功", "data": {"attachName": "FSOP_18451114593_8888888.jpg", "attachId": "2021111910007500"}}, "h5cancleReason": {"retCode": "0", "retMsg": null, "data": [{"reasonid": "10001", "reasondesc": "不想办了"}, {"reasonid": "10002", "reasondesc": "想换营销案"}, {"reasonid": "10003", "reasondesc": "选择错误"}, {"reasonid": "10004", "reasondesc": "其他"}]}, "h5cancelOrder": {"retCode": "0", "retMsg": "Sucess", "data": "1111111111111111111110"}, "h5getPackageList": {"retCode": "0", "retMsg": "Sucess", "data": [{"marketId": "10001", "marketName": "信用购88元档-畅享自选套餐流量包80元", "activeId": "3002105764", "activeName": "2020年南京普通渠道全品类信用购", "levelId": "300004082646", "levelName": "信用购88元档（24个月）0001", "packType": "3", "superType": null, "fee": "34000", "stagenum": "24", "operId": null, "region": "14", "issueType": "1", "rewardList": [{"packId": "8830032246", "packName": "畅享自选套餐流量包80元", "packList": [{"rewardName": "畅享自选套餐流量包80元", "needsn": "0", "rewardId": "2000011802", "rewardPakid": "8830032246", "rewardPakname": "奖品包_80元以上套餐包", "region": "14"}, {"rewardName": "畅享自选套餐流量包80元", "needsn": "0", "rewardId": "2000011802", "rewardPakid": "8830032246", "rewardPakname": "奖品包_80元以上套餐包", "region": "14"}, {"rewardName": "畅享自选套餐流量包80元", "needsn": "0", "rewardId": "2000011802", "rewardPakid": "8830032246", "rewardPakname": "奖品包_80元以上套餐包", "region": "14"}, {"rewardName": "畅享自选套餐流量包80元", "needsn": "0", "rewardId": "2000011802", "rewardPakid": "8830032246", "rewardPakname": "奖品包_80元以上套餐包", "region": "14"}]}]}, {"marketId": "10002", "marketName": "信用购88元档-畅享自选套餐流量包80元", "activeId": "3002105764", "activeName": "2020年南京普通渠道全品类信用购", "levelId": "300004082646", "levelName": "信用购88元档（24个月）0001", "packType": "3", "superType": null, "fee": "34000", "stagenum": "24", "operId": null, "region": "14", "issueType": "1", "rewardList": [{"packId": "8830032246", "packName": "畅享自选套餐流量包80元", "packList": [{"rewardName": "畅享自选套餐流量包80元", "needsn": "0", "rewardId": "2000011802", "rewardPakid": "8830032246", "rewardPakname": "奖品包_80元以上套餐包", "region": "14"}, {"rewardName": "畅享自选套餐流量包80元", "needsn": "1", "rewardId": "2000011802", "rewardPakid": "8830032246", "rewardPakname": "奖品包_80元以上套餐包", "region": "14"}, {"rewardName": "畅享自选套餐流量包80元", "needsn": "1", "rewardId": "2000011802", "rewardPakid": "8830032246", "rewardPakname": "奖品包_80元以上套餐包", "region": "14"}, {"rewardName": "畅享自选套餐流量包80元", "needsn": "0", "rewardId": "2000011802", "rewardPakid": "8830032246", "rewardPakname": "奖品包_80元以上套餐包", "region": "14"}]}]}, {"marketId": "10003", "marketName": "信用购88元档-畅享自选套餐流量包80元", "activeId": "3002105764", "activeName": "2020年南京普通渠道全品类信用购", "levelId": "300004082646", "levelName": "信用购88元档（24个月）0001", "packType": "3", "superType": null, "fee": "34000", "stagenum": "24", "operId": "14157059", "region": "14", "issueType": "1", "rewardList": [{"packId": "8830032246", "packName": "畅享自选套餐流量包80元", "packList": [{"rewardName": "畅享自选套餐流量包80元", "needsn": "0", "rewardId": "2000011802", "rewardPakid": "8830032246", "rewardPakname": "奖品包_80元以上套餐包", "region": "14"}, {"rewardName": "畅享自选套餐流量包70元", "needsn": "0", "rewardId": "2000011803", "rewardPakid": "8830032246", "rewardPakname": "奖品包_80元以上套餐包", "region": "14"}, {"rewardName": "畅享自选套餐流量包50元", "needsn": "0", "rewardId": "2000011804", "rewardPakid": "8830032246", "rewardPakname": "奖品包_80元以上套餐包", "region": "14"}, {"rewardName": "畅享自选套餐流量包80元", "needsn": "0", "rewardId": "2000011802", "rewardPakid": "8830032246", "rewardPakname": "奖品包_80元以上套餐包", "region": "14"}]}]}]}}