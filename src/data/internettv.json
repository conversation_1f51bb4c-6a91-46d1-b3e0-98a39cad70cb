{"h5QryTvProd": {"retCode": "0", "retMsg": null, "data": {"mustProdIds": "2413000030|2413000031|2400000079|2011002057|2413000001|", "prodList": [{"prodId": "2000011442", "prodName": "智能组网服务60元/月", "pkgprodId": "4556", "isPackage": "0", "effectType": "2"}, {"prodId": "2000011441", "prodName": "智能组网服务30元/月", "pkgprodId": "4556", "isPackage": "0", "effectType": "2"}, {"prodId": "2000011443", "prodName": "智能组网服务60元/月", "pkgprodId": "4556", "isPackage": "0", "effectType": "2"}, {"prodId": "2000011440", "prodName": "智能组网服务15元/月", "pkgprodId": "4556", "isPackage": "0", "effectType": "2"}, {"prodId": "2000001683", "prodName": "宽带标准资费", "pkgprodId": "4556", "isPackage": "0", "effectType": "2"}, {"prodId": "2000009213", "prodName": "600元包月(1000M）", "pkgprodId": "4556", "isPackage": "0", "effectType": "2"}, {"prodId": "2000009174", "prodName": "50元包月（100M，手机最低消费8元）", "pkgprodId": "4556", "isPackage": "0", "effectType": "2"}, {"prodId": "2380000171", "prodName": "快游-移动专版（代网宿收费）", "pkgprodId": "4556", "isPackage": "0", "effectType": "2"}, {"prodId": "2000009211", "prodName": "150元包月(300M）", "pkgprodId": "4556", "isPackage": "0", "effectType": "2"}, {"prodId": "2000009210", "prodName": "120元包月(200M）", "pkgprodId": "4556", "isPackage": "0", "effectType": "2"}, {"prodId": "2000009212", "prodName": "300元包月(500M）", "pkgprodId": "4556", "isPackage": "0", "effectType": "2"}, {"prodId": "2000009173", "prodName": "40元包月（50M，手机最低消费8元）", "pkgprodId": "4556", "isPackage": "0", "effectType": "2"}, {"prodId": "2000007763", "prodName": "60元每月（100M，手机最低消费8元）", "pkgprodId": "4556", "isPackage": "0", "effectType": "2"}, {"prodId": "2000007754", "prodName": "50元每月（50M，手机最低消费8元）", "pkgprodId": "4556", "isPackage": "0", "effectType": "2"}, {"prodId": "2000009799", "prodName": "60元包月(200M,手机最低消费8元）", "pkgprodId": "4556", "isPackage": "0", "effectType": "2"}, {"prodId": "2000009054", "prodName": "20元包月(20M，家庭副卡用户办理）", "pkgprodId": "4556", "isPackage": "0", "effectType": "2"}, {"prodId": "2000007857", "prodName": "10元提速包（20M提至50M）", "pkgprodId": "4556", "isPackage": "0", "effectType": "2"}, {"prodId": "2000007270", "prodName": "90元包月(100M）", "pkgprodId": "4556", "isPackage": "0", "effectType": "2"}, {"prodId": "2000009771", "prodName": "0元包月(50M,全家最低消费98元）", "pkgprodId": "4556", "isPackage": "0", "effectType": "2"}, {"prodId": "2000009055", "prodName": "30元包月(50M，移动副卡用户办理）", "pkgprodId": "4556", "isPackage": "0", "effectType": "2"}, {"prodId": "2000007772", "prodName": "90元包月(200M,手机最低消费28元)", "pkgprodId": "4556", "isPackage": "0", "effectType": "2"}, {"prodId": "2000007272", "prodName": "30元包月（50M）", "pkgprodId": "4556", "isPackage": "0", "effectType": "2"}, {"prodId": "2000009057", "prodName": "10元提速包（提至100M）", "pkgprodId": "4556", "isPackage": "0", "effectType": "2"}, {"prodId": "2000007962", "prodName": "40元包月（100M）", "pkgprodId": "4556", "isPackage": "0", "effectType": "2"}, {"prodId": "2000009033", "prodName": "宽带电视优惠包(50M)", "pkgprodId": "4556", "isPackage": "0", "effectType": "2"}, {"prodId": "2000011444", "prodName": "固话光纤线路", "pkgprodId": "4556", "isPackage": "0", "effectType": "2"}, {"prodId": "2000009773", "prodName": "20元包月(200M,全家最低消费98元）", "pkgprodId": "4556", "isPackage": "0", "effectType": "2"}, {"prodId": "2000007269", "prodName": "70元包月(50M）", "pkgprodId": "4556", "isPackage": "0", "effectType": "2"}, {"prodId": "2000009056", "prodName": "40元包月(100M，移动副卡用户办理）", "pkgprodId": "4556", "isPackage": "0", "effectType": "2"}, {"prodId": "2000009772", "prodName": "10元包月(100M,全家最低消费98元）", "pkgprodId": "4556", "isPackage": "0", "effectType": "2"}, {"prodId": "2000007773", "prodName": "90元提速包(提至200M)", "pkgprodId": "4556", "isPackage": "0", "effectType": "2"}, {"prodId": "2000009767", "prodName": "20元提速包（提至200M）", "pkgprodId": "4556", "isPackage": "0", "effectType": "2"}, {"prodId": "2000009798", "prodName": "宽带电视优惠包（200M）", "pkgprodId": "4556", "isPackage": "0", "effectType": "2"}, {"prodId": "2000009034", "prodName": "宽带电视优惠包(100M)", "pkgprodId": "4556", "isPackage": "0", "effectType": "2"}]}}, "h5QryTvOpenInfo": {"retCode": "0", "retMsg": null, "data": {"tvTypeList": [{"pkgProdId": "2013000011", "pkgProdName": "省内央视-CP"}, {"pkgProdId": "2013000012", "pkgProdName": "省内央广银河-CP"}, {"pkgProdId": "2013000010", "pkgProdName": "省内百视通-CP"}, {"pkgProdId": "2013000019", "pkgProdName": "百视通自购产品"}, {"pkgProdId": "2013000020", "pkgProdName": "CNTV自购产品"}, {"pkgProdId": "2013000021", "pkgProdName": "银河自购产品"}], "deviceSmallList": [{"typeid": "151000000000000016", "typename": "家庭网关"}], "mbandInfoDetail": {"addrName": "南京地区江宁东山街道天印大道书香名苑22栋4单元607室", "villageId": "9758", "villageName": "书香名苑", "villageAddress": "南京地区江宁东山街道天印大道书香名苑22栋4单元607室", "addressId": "1041203842", "otherAgent": "1", "constMgr": "1", "radiusFor": "1", "usertype": "1", "netWorkType": "1"}, "deviceClass": "OTT_STB", "canOpenTv": "true", "countryId": "1423"}}, "h5TvCharge": {"retCode": "0", "retMsg": null, "data": {"linkName": "0", "adress": "1", "installDate": "2019-06-28 15:35:58", "totalFee": "5000", "businessName": "倒是", "chargeList": [{"chargecode": "3206", "chargemoney": "0", "charegename": "宽带接入费"}, {"chargecode": "DeviceTestFee", "chargemoney": "5000", "charegename": "家庭终端调试费"}], "crditFee": "0", "isDevicefee": "1"}}, "h5TvSubmit": {"retCode": "0", "retMsg": "Success", "data": {"recoId": "200113294755317210", "totalFee": "4992", "tvSort": "1", "chargeList": [{"chargecode": "3206", "chargemoney": "0", "charegename": "宽带接入费"}, {"chargecode": "DeviceTestFee", "chargemoney": "4992", "charegename": "家庭终端调试费"}]}}, "h5QryProdDesc": {"retCode": "0", "retMsg": null, "data": {"prodDesc": "套餐包描述"}}, "h5CanOpenTv": {"retCode": "0", "retMsg": null, "data": "nettv_snygyh_prod_kaitong"}, "h5QryScoreAndRank": {"data": {"orgaId": "88020885368838", "orgaName": "沙洲街道", "orgaRank": "1", "orgaScore": "99.66"}, "retCode": "0"}, "h5QryResAndIndex": {"data": {"gridIndexList": [{"orgaIndexMoM": "0.7", "orgaIndexName": "营收额度", "orgaIndexQuaRate": "70", "orgaIndexValue": "10万元", "orgaIndexYoY": "1.2"}, {"orgaIndexMoM": "1.1", "orgaIndexName": "新增用户数", "orgaIndexQuaRate": "40", "orgaIndexValue": "200人", "orgaIndexYoY": "0.9"}], "gridResourceList": [{"orgaResourceName": "集团", "orgaResourceValue": "10"}, {"orgaResourceName": "小区", "orgaResourceValue": "10"}, {"orgaResourceName": "渠道", "orgaResourceValue": "10"}, {"orgaResourceName": "个人", "orgaResourceValue": "10"}, {"orgaResourceName": "个人", "orgaResourceValue": "10"}, {"orgaResourceName": "个人", "orgaResourceValue": "10"}]}, "retCode": "0"}, "h5QryMyTask": {"data": {"小区": {"finishedNum": "0", "leaveNum": "37", "unfinishedNum": "0"}, "集团": {"finishedNum": "0", "leaveNum": "67", "unfinishedNum": "0"}, "渠道": {"finishedNum": "0", "leaveNum": "2", "unfinishedNum": "0"}}, "retCode": "0"}, "h5QryTop3": {"data": [{"orgaRank": "1", "topOrgaName": "沙洲街道", "topOrgaScore": "99.66"}, {"orgaRank": "2", "topOrgaName": "校区_高校_高职校", "topOrgaScore": "96.96"}, {"orgaRank": "3", "topOrgaName": "沙洲网格2", "topOrgaScore": "96.76"}], "retCode": "0"}, "h5QryCustomMarketPkgPanc": {"retCode": "0", "retMsg": null, "data": [{"marketId": "20200814101109", "marketName": "全家WIFI调测服务258元22222222222222222222222222222222|全家WIFI调测费直降180元2222222222222222222222222", "type": null, "crmId": "14150759", "createDate": null, "startDate": "2020-08-13 00:00:00", "endDate": "2099-08-01 00:00:00", "marketDesc": "智能组网+营销案", "source": "1", "status": null, "releaseType": "1", "customProdInfoList": [{"marketId": "20200814101109", "pkgId": "20200814101109", "prodId": "yxa2013000030", "prodName": " 第一智能组网1111111111111111111", "isPackage": "1", "effectType": "0", "prodLevel": 1, "type": "3", "parentId": "-1", "isMust": "1", "isNeed": null, "customProdInfoList": [{"marketId": "20200814101109", "pkgId": "20200814101109", "prodId": "cp1.2000012327", "prodName": "全家WIFI调测服务258元111111111111111111", "isPackage": "1", "effectType": "0", "prodLevel": 2, "type": "1", "parentId": "2013000030", "isMust": "1", "isNeed": null, "customProdInfoList": []}, {"marketId": "20200814101109", "pkgId": "20200814101109", "prodId": "cp2.2413000030", "prodName": "智能组网基础服务1111111111111111111", "isPackage": "1", "effectType": "0", "prodLevel": 2, "type": "3", "parentId": "2013000030", "isMust": "1", "isNeed": null, "customProdInfoList": []}]}, {"marketId": "20200814101109", "pkgId": "20200814101109", "prodId": "pici3002106626", "prodName": "2020全省全家WIFI调测费优惠活动11111111111111111111", "isPackage": "1", "effectType": "0", "prodLevel": 1, "type": "1", "parentId": "-1", "isMust": "1", "isNeed": null, "customProdInfoList": [{"marketId": "20200814101109", "pkgId": "20200814101109", "prodId": "dangci300004089356", "prodName": "全家WIFI调测费直降至180元1111111111111111", "isPackage": "1", "effectType": "0", "prodLevel": 2, "type": "1", "parentId": "3002106626", "isMust": "1", "isNeed": null, "customProdInfoList": [{"marketId": "20200814101109", "pkgId": "20200814101109", "prodId": "jpb8830044852", "prodName": "奖品包全家WIFI调测服务258元11111111111111111111", "isPackage": "1", "effectType": "0", "prodLevel": 3, "type": "1", "parentId": "300004089356", "isMust": "1", "isNeed": null, "customProdInfoList": [{"marketId": "20200814101109", "pkgId": "20200814101109", "prodId": "jp2000012327", "prodName": "奖品全家WIFI调测服务258元11111111111111111", "isPackage": "1", "effectType": "0", "prodLevel": 4, "type": "1", "parentId": "8830044852", "isMust": "1", "isNeed": null, "customProdInfoList": null}, {"marketId": "20200814101109", "pkgId": "20200814101109", "prodId": "jp2000012327", "prodName": "奖品全家WIFI调测服务258元B1111111111111111111", "isPackage": "1", "effectType": "0", "prodLevel": 4, "type": "1", "parentId": "8830044852", "isMust": "1", "isNeed": null, "customProdInfoList": null}]}, {"marketId": "20200814101109", "pkgId": "20200814101109", "prodId": "jpb8830044852", "prodName": "全家WIFI调测服务258元222", "isPackage": "1", "effectType": "0", "prodLevel": 3, "type": "1", "parentId": "300004089356", "isMust": "1", "isNeed": null, "customProdInfoList": [{"marketId": "20200814101109", "pkgId": "20200814101109", "prodId": "jp2000012327", "prodName": "全家WIFI调测服务258元222", "isPackage": "1", "effectType": "0", "prodLevel": 4, "type": "1", "parentId": "8830044852", "isMust": "1", "isNeed": null, "customProdInfoList": null}, {"marketId": "20200814101109", "pkgId": "20200814101109", "prodId": "jp2000012327", "prodName": "全家WIFI调测服务258元222BB", "isPackage": "1", "effectType": "0", "prodLevel": 4, "type": "1", "parentId": "8830044852", "isMust": "1", "isNeed": null, "customProdInfoList": null}]}]}]}]}, {"marketId": "20200814101109", "marketName": "全家WIFI调测服务258元|全家WIFI调测费直降180元", "type": null, "crmId": "14150759", "createDate": null, "startDate": "2020-08-13 00:00:00", "endDate": "2099-08-01 00:00:00", "marketDesc": "智能组网+营销案", "source": "1", "status": null, "releaseType": "1", "customProdInfoList": [{"marketId": "20200814101109", "pkgId": "20200814101109", "prodId": "2013000030", "prodName": " 第一智能组网", "isPackage": "1", "effectType": "0", "prodLevel": 1, "type": "3", "parentId": "-1", "isMust": "1", "isNeed": null, "customProdInfoList": [{"marketId": "20200814101109", "pkgId": "20200814101109", "prodId": "2000012327", "prodName": "全家WIFI调测服务258元", "isPackage": "1", "effectType": "0", "prodLevel": 2, "type": "1", "parentId": "2013000030", "isMust": "1", "isNeed": null, "customProdInfoList": []}, {"marketId": "20200814101109", "pkgId": "20200814101109", "prodId": "2413000030", "prodName": "智能组网基础服务", "isPackage": "1", "effectType": "0", "prodLevel": 2, "type": "3", "parentId": "2013000030", "isMust": "1", "isNeed": null, "customProdInfoList": []}]}, {"marketId": "20200814101109", "pkgId": "20200814101109", "prodId": "3002106626", "prodName": "2020全省全家WIFI调测费优惠活动", "isPackage": "1", "effectType": "0", "prodLevel": 1, "type": "1", "parentId": "-1", "isMust": "1", "isNeed": null, "customProdInfoList": [{"marketId": "20200814101109", "pkgId": "20200814101109", "prodId": "300004089356", "prodName": "全家WIFI调测费直降至180元", "isPackage": "1", "effectType": "0", "prodLevel": 2, "type": "1", "parentId": "3002106626", "isMust": "1", "isNeed": null, "customProdInfoList": [{"marketId": "20200814101109", "pkgId": "20200814101109", "prodId": "8830044852", "prodName": "全家WIFI调测服务258元", "isPackage": "1", "effectType": "0", "prodLevel": 3, "type": "1", "parentId": "300004089356", "isMust": "1", "isNeed": null, "customProdInfoList": [{"marketId": "20200814101109", "pkgId": "20200814101109", "prodId": "2000012327", "prodName": "全家WIFI调测服务258元", "isPackage": "1", "effectType": "0", "prodLevel": 4, "type": "1", "parentId": "8830044852", "isMust": "1", "isNeed": null, "customProdInfoList": null}]}]}]}]}]}}