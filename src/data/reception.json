{"broadBandCheck": {"retCode": "0", "retMsg": null, "data": [{"marketId": "20200115100043", "marketName": "测试名称1.26", "type": null, "crmId": "14150759", "createDate": null, "startDate": null, "endDate": null, "marketDesc": null, "source": "1", "status": null, "releaseType": "1", "customProdInfoList": [{"marketId": "20200115100043", "pkgId": "2013000001", "prodId": "2000001683", "prodName": "宽带标准资费", "isPackage": "0", "effectType": "2", "prodLevel": 1, "type": null, "parentId": "-1", "isMust": "1", "isNeed": null, "customProdInfoList": null}, {"marketId": "20200115100043", "pkgId": "2013000001", "prodId": "2000007962", "prodName": "宽带接入费用", "isPackage": "0", "effectType": "0", "prodLevel": 1, "type": null, "parentId": "-1", "isMust": "2", "isNeed": null, "customProdInfoList": null}, {"marketId": "20200115100043", "pkgId": "2013000001", "prodId": "2000009057", "prodName": "30元提速包（提至100M）", "isPackage": "0", "effectType": "0", "prodLevel": 1, "type": null, "parentId": "-1", "isMust": "0", "isNeed": null, "customProdInfoList": null}, {"marketId": "20200115100043", "pkgId": "2013000001", "prodId": "2011002057", "prodName": "当月免费套餐", "isPackage": "0", "effectType": "0", "prodLevel": 1, "type": null, "parentId": "-1", "isMust": "0", "isNeed": null, "customProdInfoList": null}, {"marketId": "20200115100043", "pkgId": "2013000001", "prodId": "2413000001", "prodName": "手机代付费宽带", "isPackage": "0", "effectType": "0", "prodLevel": 1, "type": null, "parentId": "-1", "isMust": "2", "isNeed": null, "customProdInfoList": null}]}, {"marketId": "20200421100117", "marketName": "12", "type": null, "crmId": "14150759", "createDate": null, "startDate": "2020-04-20 00:00:00", "endDate": "2022-04-20 00:00:00", "marketDesc": "asdas", "source": "1", "status": null, "releaseType": "1", "customProdInfoList": [{"marketId": "20200115100043", "pkgId": "2013000001", "prodId": "2000001683", "prodName": "宽带标准资费", "isPackage": "0", "effectType": "2", "prodLevel": 1, "type": null, "parentId": "-1", "isMust": "1", "isNeed": null, "customProdInfoList": null}, {"marketId": "20200115100043", "pkgId": "2013000001", "prodId": "2000007962", "prodName": "宽带接入费用", "isPackage": "0", "effectType": "0", "prodLevel": 1, "type": null, "parentId": "-1", "isMust": "2", "isNeed": null, "customProdInfoList": null}, {"marketId": "20200115100043", "pkgId": "2013000001", "prodId": "2000009057", "prodName": "20元提速包（提至100M）", "isPackage": "0", "effectType": "0", "prodLevel": 1, "type": null, "parentId": "-1", "isMust": "0", "isNeed": null, "customProdInfoList": null}, {"marketId": "20200115100043", "pkgId": "2013000001", "prodId": "2011002057", "prodName": "当月免费套餐", "isPackage": "0", "effectType": "0", "prodLevel": 1, "type": null, "parentId": "-1", "isMust": "0", "isNeed": null, "customProdInfoList": null}, {"marketId": "20200115100043", "pkgId": "2013000001", "prodId": "2413000001", "prodName": "手机代付费宽带", "isPackage": "0", "effectType": "0", "prodLevel": 1, "type": null, "parentId": "-1", "isMust": "2", "isNeed": null, "customProdInfoList": null}]}]}, "marketing": {"retCode": "0", "retMsg": null, "data": [{"marketId": "20200217100084", "marketName": "测试", "type": null, "crmId": "14150759", "createDate": null, "startDate": "2020-02-16 11:28:42", "endDate": null, "marketDesc": null, "source": "1", "status": null, "releaseType": "2", "customProdInfoList": [{"marketId": "20200217100084", "pkgId": null, "prodId": "300004021912", "prodName": "钟的个人营销案", "isPackage": "0", "effectType": "0", "prodLevel": 1, "type": null, "parentId": "-1", "isMust": "0", "isNeed": null, "customProdInfoList": [{"marketId": "20200217100084", "pkgId": null, "prodId": "300004072849", "prodName": "自建自建", "isPackage": "0", "effectType": "0", "prodLevel": 2, "type": null, "parentId": "300004021912", "isMust": "0", "isNeed": null, "customProdInfoList": [{"marketId": "20200217100084", "pkgId": null, "prodId": "2000001", "prodName": "测试奖品包2", "isPackage": "0", "effectType": "0", "prodLevel": 3, "type": null, "parentId": "300004072849", "isMust": "0", "isNeed": null, "customProdInfoList": [{"marketId": "20200217100084", "pkgId": null, "prodId": "20000011", "prodName": "包2包下子产品1", "isPackage": "0", "effectType": "0", "prodLevel": 4, "type": null, "parentId": "2000001", "isMust": "0", "isNeed": null, "customProdInfoList": null}, {"marketId": "20200217100084", "pkgId": null, "prodId": "20000012", "prodName": "包2包下子产品2", "isPackage": "0", "effectType": "0", "prodLevel": 4, "type": null, "parentId": "2000001", "isMust": "0", "isNeed": null, "customProdInfoList": null}]}, {"marketId": "20200217100084", "pkgId": null, "prodId": "2000001", "prodName": "测试奖品包2", "isPackage": "0", "effectType": "0", "prodLevel": 3, "type": null, "parentId": "300004072849", "isMust": "0", "isNeed": null, "customProdInfoList": [{"marketId": "20200217100084", "pkgId": null, "prodId": "20000011", "prodName": "包2包下子产品1", "isPackage": "0", "effectType": "0", "prodLevel": 4, "type": null, "parentId": "2000001", "isMust": "0", "isNeed": null, "customProdInfoList": null}, {"marketId": "20200217100084", "pkgId": null, "prodId": "20000012", "prodName": "包2包下子产品2", "isPackage": "0", "effectType": "0", "prodLevel": 4, "type": null, "parentId": "2000001", "isMust": "0", "isNeed": null, "customProdInfoList": null}]}, {"marketId": "20200217100084", "pkgId": null, "prodId": "8000085818", "prodName": "4G内容权益包二选一/咪咕视频会员15元", "isPackage": "0", "effectType": "0", "prodLevel": 3, "type": null, "parentId": "300004072849", "isMust": "0", "isNeed": null, "customProdInfoList": [{"marketId": "20200217100084", "pkgId": null, "prodId": "2000009572", "prodName": "4G内容权益包10元", "isPackage": "0", "effectType": "0", "prodLevel": 4, "type": null, "parentId": "8000085818", "isMust": "0", "isNeed": null, "customProdInfoList": null}, {"marketId": "20200217100084", "pkgId": null, "prodId": "2000009573", "prodName": "4G内容权益包15元", "isPackage": "0", "effectType": "0", "prodLevel": 4, "type": null, "parentId": "8000085818", "isMust": "0", "isNeed": null, "customProdInfoList": null}]}, {"marketId": "20200217100084", "pkgId": null, "prodId": "8000085818", "prodName": "4G内容权益包二选一/咪咕视频会员15元", "isPackage": "0", "effectType": "0", "prodLevel": 3, "type": null, "parentId": "300004072849", "isMust": "0", "isNeed": null, "customProdInfoList": [{"marketId": "20200217100084", "pkgId": null, "prodId": "2000009572", "prodName": "4G内容权益包10元", "isPackage": "0", "effectType": "0", "prodLevel": 4, "type": null, "parentId": "8000085818", "isMust": "0", "isNeed": null, "customProdInfoList": null}, {"marketId": "20200217100084", "pkgId": null, "prodId": "2000009573", "prodName": "4G内容权益包15元", "isPackage": "0", "effectType": "0", "prodLevel": 4, "type": null, "parentId": "8000085818", "isMust": "0", "isNeed": null, "customProdInfoList": null}]}]}]}]}]}, "appreciation": {"retCode": "0", "retMsg": null, "data": [{"marketId": "20200408100101", "marketName": "xxx的营销案", "type": null, "crmId": "14150759", "createDate": null, "startDate": null, "endDate": null, "marketDesc": null, "source": "1", "status": null, "releaseType": "2", "customProdInfoList": [{"marketId": "20200408100101", "pkgId": "1000000001", "prodId": "1000000001", "prodName": "增值产品打包测试专用1", "isPackage": "0", "effectType": "2", "prodLevel": 1, "type": null, "parentId": "-1", "isMust": "1", "isNeed": null, "customProdInfoList": null}, {"marketId": "20200408100101", "pkgId": null, "prodId": "1000000002", "prodName": "增值产品打包测试专用2", "isPackage": "0", "effectType": "2", "prodLevel": 1, "type": null, "parentId": "-1", "isMust": "1", "isNeed": null, "customProdInfoList": null}, {"marketId": "20200408100101", "pkgId": null, "prodId": "1000000003", "prodName": "增值产品打包测试专用3", "isPackage": "0", "effectType": "2", "prodLevel": 1, "type": null, "parentId": "-1", "isMust": "1", "isNeed": null, "customProdInfoList": null}, {"marketId": "20200408100101", "pkgId": null, "prodId": "2000001854", "prodName": "动感校园行E套餐", "isPackage": "0", "effectType": "2", "prodLevel": 1, "type": null, "parentId": "-1", "isMust": "1", "isNeed": null, "customProdInfoList": null}, {"marketId": "20200408100101", "pkgId": null, "prodId": "2000002113", "prodName": "省内非常假期-宿迁区域话音包", "isPackage": "0", "effectType": "1", "prodLevel": 1, "type": null, "parentId": "-1", "isMust": "1", "isNeed": null, "customProdInfoList": null}, {"marketId": "20200408100101", "pkgId": null, "prodId": "2000003815", "prodName": "通用流量包280元", "isPackage": "0", "effectType": "2", "prodLevel": 1, "type": null, "parentId": "-1", "isMust": "1", "isNeed": null, "customProdInfoList": null}, {"marketId": "20200408100101", "pkgId": null, "prodId": "2000007381", "prodName": "通用流量包20元B", "isPackage": "0", "effectType": "2", "prodLevel": 1, "type": null, "parentId": "-1", "isMust": "1", "isNeed": null, "customProdInfoList": null}, {"marketId": "20200408100101", "pkgId": "2000007807", "prodId": "2000007807", "prodName": "0元提速20M", "isPackage": "0", "effectType": "2", "prodLevel": 1, "type": null, "parentId": "-1", "isMust": "1", "isNeed": null, "customProdInfoList": null}, {"marketId": "20200408100101", "pkgId": null, "prodId": "2010141018", "prodName": "新动感地带必选套餐[必选包]", "isPackage": "0", "effectType": "2", "prodLevel": 1, "type": null, "parentId": "-1", "isMust": "1", "isNeed": null, "customProdInfoList": null}, {"marketId": "20200408100101", "pkgId": null, "prodId": "2300420138", "prodName": "动感地带套餐[非必选]", "isPackage": "0", "effectType": "2", "prodLevel": 1, "type": null, "parentId": "-1", "isMust": "1", "isNeed": null, "customProdInfoList": null}]}, {"marketId": "20191218100094", "marketName": "测试", "type": null, "crmId": "14150759", "createDate": null, "startDate": "2019-12-18 20:32:31", "endDate": null, "marketDesc": null, "source": "1", "status": null, "releaseType": "2", "customProdInfoList": [{"marketId": "20191218100094", "pkgId": null, "prodId": "2000002113", "prodName": "省内超级假期-宿迁区域话音包", "isPackage": "0", "effectType": "0", "prodLevel": 1, "type": null, "parentId": "-1", "isMust": "1", "isNeed": null, "customProdInfoList": null}, {"marketId": "20191218100094", "pkgId": null, "prodId": "2010141018", "prodName": "新动感地带必选套餐[必选包]", "isPackage": "0", "effectType": "0", "prodLevel": 1, "type": null, "parentId": "-1", "isMust": "1", "isNeed": null, "customProdInfoList": null}]}, {"marketId": "20200416100102", "marketName": "泛渠道测试打包", "type": null, "crmId": "14150759", "createDate": null, "startDate": null, "endDate": null, "marketDesc": null, "source": "1", "status": null, "releaseType": "2", "customProdInfoList": [{"marketId": "20200416100102", "pkgId": null, "prodId": "2000002113", "prodName": "省内非常假期-宿迁区域话音包", "isPackage": "0", "effectType": "1", "prodLevel": 1, "type": null, "parentId": "-1", "isMust": "1", "isNeed": null, "customProdInfoList": null}, {"marketId": "20200416100102", "pkgId": null, "prodId": "2010141018", "prodName": "新动感地带必选套餐[必选包]", "isPackage": "0", "effectType": "0", "prodLevel": 1, "type": null, "parentId": "-1", "isMust": "1", "isNeed": null, "customProdInfoList": null}]}]}}