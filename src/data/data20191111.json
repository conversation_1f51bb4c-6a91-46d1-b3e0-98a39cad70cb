{"h5QryOrderInfoList": {"retCode": "0", "retMsg": null, "data": {"total_page": "4", "exc_order_num": "10", "complete_order_num": "12", "orderList": [{"order_id": "19303770923000000140", "data_type": "1", "order_status": "73", "order_status_name": "已写卡", "acc_nbr": "15800128592", "order_crate_time": "2019-10-24 09:23:01", "is_over_time": "N", "busi_create_time": "2019-10-24 09:23:00", "busi_create_channel": "", "acc_name": "受理人", "acc_date": "2019-10-24 09:23:00", "acc_channel": "受理渠道", "acc_phone": "13567567898", "goods_name": "1000100462(2000009684)", "busi_type_name": "号卡销售", "cust_name": "客户名称", "contact_phone": "15678976543", "cust_addr": "南京地区江宁秣陵街道玉树路万裕龙挺水岸106栋2单元1606", "area_id": "小区ID", "area_name": "小区名称", "area_code": "所属区域", "salesman_phone": "营业员联系电话", "opt_type_name": "操作类型", "group_code": "集团编码", "group_name": "集团名称", "group_addr": "集团客户地址", "approve_order_id": "审批单号"}, {"order_id": "19303770923000000142", "data_type": "2", "order_status": "57", "order_status_name": "待接收卡上发的写卡数据申请", "acc_nbr": "15800128592", "order_crate_time": "2019-10-24 09:23:00", "is_over_time": "N", "busi_create_time": "2019-10-24 09:23:00", "busi_create_channel": "", "acc_name": "受理人", "acc_date": "2019-10-24 09:23:00", "acc_channel": "受理渠道", "acc_phone": "13567567898", "goods_name": "1000100462(2000009684)", "busi_type_name": "号卡销售", "cust_name": "客户名称", "contact_phone": "15678976543", "cust_addr": "南京地区江宁秣陵街道玉树路万裕龙挺水岸106栋2单元1606", "area_id": "小区ID", "area_name": "小区名称", "area_code": "所属区域", "salesman_phone": "营业员联系电话", "opt_type_name": "操作类型", "group_code": "集团编码", "group_name": "集团名称", "group_addr": "集团客户地址", "approve_order_id": "审批单号"}, {"order_id": "19303770923000000240", "data_type": "1", "order_status": "72", "order_status_name": "已写卡", "acc_nbr": "15800128592", "order_crate_time": "2019-10-24 09:23:00", "is_over_time": "Y", "busi_create_time": "2019-10-24 09:23:00", "busi_create_channel": "", "acc_name": "受理人", "acc_date": "2019-10-24 09:23:00", "acc_channel": "受理渠道", "acc_phone": "13567567898", "goods_name": "1000100462(2000009684)", "busi_type_name": "号卡销售", "cust_name": "客户名称", "contact_phone": "15678976543", "cust_addr": "客户联系地址", "area_id": "小区ID", "area_name": "小区名称", "area_code": "所属区域", "salesman_phone": "营业员联系电话", "opt_type_name": "操作类型", "group_code": "集团编码", "group_name": "集团名称", "group_addr": "集团客户地址", "approve_order_id": "审批单号"}, {"order_id": "19303770923000000140", "data_type": "1", "order_status": "92", "order_status_name": "退款失败", "acc_nbr": "15800128592", "order_crate_time": "2019-10-24 09:23:00", "is_over_time": "N", "busi_create_time": "2019-10-24 09:23:00", "busi_create_channel": "", "acc_name": "受理人", "acc_date": "2019-10-24 09:23:00", "acc_channel": "受理渠道", "acc_phone": "13567567898", "goods_name": "1000100462(2000009684)", "busi_type_name": "号卡销售", "cust_name": "客户名称", "contact_phone": "15678976543", "cust_addr": "客户联系地址", "area_id": "小区ID", "area_name": "小区名称", "area_code": "所属区域", "salesman_phone": "营业员联系电话", "opt_type_name": "操作类型", "group_code": "集团编码", "group_name": "集团名称", "group_addr": "集团客户地址", "approve_order_id": "审批单号"}, {"order_id": "19303770923000000140", "data_type": "1", "order_status": "72", "order_status_name": "安装成功", "acc_nbr": "15800128592", "order_crate_time": "2019-10-24 09:23:00", "is_over_time": "N", "busi_create_time": "2019-10-24 09:23:00", "busi_create_channel": "", "acc_name": "受理人", "acc_date": "2019-10-24 09:23:00", "acc_channel": "受理渠道", "acc_phone": "13567567898", "goods_name": "1000100462(2000009684)", "busi_type_name": "号卡销售", "cust_name": "客户名称", "contact_phone": "15678976543", "cust_addr": "客户联系地址", "area_id": "小区ID", "area_name": "小区名称", "area_code": "所属区域", "salesman_phone": "营业员联系电话", "opt_type_name": "操作类型", "group_code": "集团编码", "group_name": "集团名称", "group_addr": "集团客户地址", "approve_order_id": "审批单号"}]}}, "h5QryOrderProcess": {"retCode": "0", "retMsg": null, "data": {"flow_info": {"flow_node": [{"work_flow_id": "f001", "work_item_id": "f001-1", "warning_date": "2019-08-08 12:12:12", "overtime_date": "2019-05-23 09:38:45", "tache_id": "1000", "tache_code": "CREATE_ORDER", "tache_name": "订单录入", "tache_type": "1", "create_date": "2019-05-06 09:38:45", "deal_date": "2019-05-10 09:38:45", "child_node_list": {"child_node": [{"work_item_id": "f001-1-1", "tache_id": "21005", "tache_code": "CREATE_ORDER_FLOW", "tache_name": "待审批", "tache_Type": "3", "tache_state_name": "完成", "deal_desc": "小王已阅", "support_phone": "18805152375", "duty_system": "订单中心", "create_date": "2019-05-07 09:38:45", "deal_date": "2019-05-08 02:38:45"}, {"work_item_id": "f001-1-2", "tache_id": "21007", "tache_code": "CREATE_ORDER_FLOW", "tache_name": "审核中", "tache_Type": "3", "tache_state_name": "完成", "deal_desc": "需要开会讨论一下", "support_phone": "18805152375", "duty_system": "增值部门", "create_date": "2019-05-08 03:38:45", "deal_date": "2019-05-08 05:38:45"}, {"work_item_id": "f001-1-3", "tache_id": "21006", "tache_code": "CREATE_ORDER_FLOW", "tache_name": "审核完成", "tache_Type": "3", "tache_state_name": "完成", "deal_desc": "一致同意，可以录入", "support_phone": "18805152375", "duty_system": "大众", "create_date": "2019-05-08 09:38:45", "deal_date": "2019-05-09 09:38:45"}]}}, {"work_flow_id": "f002", "work_item_id": "f001-2", "warning_date": "2019-05-18 12:12:12", "overtime_date": "2019-5-10 12:12:12", "tache_id": "1000", "tache_code": "CREATE_ORDER", "tache_name": "审批", "tache_type": "1", "create_date": "2019-05-11 09:38:45", "deal_date": "2019-05-13 09:38:45", "child_node_list": {"child_node": [{"work_item_id": "f002-1-1", "tache_id": "21005", "tache_state_name": "完成", "tache_code": "CREATE_ORDER_FLOW", "tache_name": "一级审核", "tache_Type": "3", "deal_desc": "组长小四审核中", "support_phone": "18805152375", "duty_system": "订单中心", "create_date": "2019-05-11 09:38:45", "deal_date": "2019-05-12 09:38:45"}, {"work_item_id": "f002-1-2", "tache_id": "21007", "tache_code": "CREATE_ORDER_FLOW", "tache_name": "二级审核", "tache_Type": "3", "tache_state_name": "完成", "deal_desc": "二级审核通过", "support_phone": "18805152375", "duty_system": "处理系统", "create_date": "2019-05-23 09:38:45", "deal_date": "2019-05-23 09:38:45"}, {"work_item_id": "f002-1-3", "tache_id": "21006", "tache_code": "CREATE_ORDER_FLOW", "tache_name": "终级审核", "tache_Type": "3", "tache_state_name": "审核中", "deal_desc": "同意，小二干得好", "support_phone": "18805152375", "duty_system": "订单中心", "create_date": "2019-05-23 09:38:45", "deal_date": "2019-05-23 09:38:45"}]}}, {"work_flow_id": "f003", "work_item_id": "f001-3", "warning_date": "2019-08-08 12:12:12", "overtime_date": "2019-11-14 12:12:12", "tache_id": "1000", "tache_code": "CREATE_ORDER", "tache_name": "勘查单发起", "tache_type": "1", "create_date": "2019-05-23 09:38:45", "deal_date": "", "child_flow_id": "branch001", "child_node_list": {"child_node": [{"work_item_id": "f002-1-1", "tache_id": "21005", "tache_state_name": "完成", "tache_code": "CREATE_ORDER_FLOW", "tache_name": "一级审核", "tache_Type": "3", "deal_desc": "组长小四审核通过", "support_phone": "18805152375", "duty_system": "订单中心", "create_date": "2019-05-11 09:38:45", "deal_date": "2019-05-12 09:38:45"}, {"work_item_id": "f002-1-2", "tache_id": "21007", "tache_code": "CREATE_ORDER_FLOW", "tache_name": "二级审核", "tache_Type": "3", "tache_state_name": "审核中", "deal_desc": "正在勘查中", "support_phone": "18805152375", "duty_system": "处理系统", "create_date": "2019-05-23 09:38:45", "deal_date": ""}]}}, {"work_flow_id": "f004", "work_item_id": "f001-2", "warning_date": "2019-05-18 12:12:12", "overtime_date": "2019-5-10 12:12:12", "tache_id": "1000", "tache_code": "CREATE_ORDER", "tache_name": "施工单发起", "tache_type": "1", "create_date": "", "deal_date": "", "child_flow_id": "branch002", "child_node_list": {"child_node": [{"work_item_id": "f002-1-1", "tache_id": "21005", "tache_state_name": "完成", "tache_code": "CREATE_ORDER_FLOW", "tache_name": "一级审核", "tache_Type": "3", "deal_desc": "组长小四审核中", "support_phone": "18805152375", "duty_system": "订单中心", "create_date": "", "deal_date": ""}, {"work_item_id": "f002-1-2", "tache_id": "21007", "tache_code": "CREATE_ORDER_FLOW", "tache_name": "二级审核", "tache_Type": "3", "tache_state_name": "完成", "deal_desc": "二级审核通过", "support_phone": "18805152375", "duty_system": "处理系统", "create_date": "", "deal_date": ""}, {"work_item_id": "f002-1-3", "tache_id": "21006", "tache_code": "CREATE_ORDER_FLOW", "tache_name": "终级审核", "tache_Type": "3", "tache_state_name": "审核中", "deal_desc": "同意，小二干得好", "support_phone": "18805152375", "duty_system": "订单中心", "create_date": "", "deal_date": ""}]}}, {"work_flow_id": "f006", "work_item_id": "f005-3", "warning_date": "2019-08-08 12:12:12", "overtime_date": "2019-11-14 12:12:12", "tache_id": "1000", "tache_code": "CREATE_ORDER", "tache_name": "计费生效确认", "tache_type": "1", "create_date": "", "deal_date": "", "child_node_list": {"child_node": []}}, {"work_flow_id": "f007", "work_item_id": "f005-3", "warning_date": "2019-08-08 12:12:12", "overtime_date": "2019-11-14 12:12:12", "tache_id": "1000", "tache_code": "CREATE_ORDER", "tache_name": "业务办理", "tache_type": "1", "create_date": "", "deal_date": "", "child_node_list": {"child_node": []}}, {"work_flow_id": "f008", "work_item_id": "f005-3", "warning_date": "2019-08-08 12:12:12", "overtime_date": "2019-11-14 12:12:12", "tache_id": "1000", "tache_code": "CREATE_ORDER", "tache_name": "业务稽核", "tache_type": "1", "create_date": "", "deal_date": "", "child_node_list": {"child_node": []}}, {"work_flow_id": "f009", "work_item_id": "f005-3", "warning_date": "2019-08-08 12:12:12", "overtime_date": "2019-11-14 12:12:12", "tache_id": "1000", "tache_code": "CREATE_ORDER", "tache_name": "客户经理确认", "tache_type": "1", "create_date": "", "deal_date": "", "child_node_list": {"child_node": []}}]}, "branch_flow_list": {"branch_flow": [{"flow_id": "b0001", "parent_flow_id": "branch001", "flow_info": {"flow_node": [{"work_flow_id": "实例流程ID", "work_item_id": "b0001-1", "warning_date": "预警时间2019-08-08 12:12:12", "overtime_date": "超时时间2019-08-08 12:12:12", "tache_id": "1000", "tache_code": "CREATE_ORDER", "tache_name": "勘查单创建", "tache_type": "1", "create_date": "2019-05-23 09:38:45", "deal_date": "2019-05-23 09:38:45", "child_node_list": {"child_node": [{"work_item_id": "f002-3-1", "tache_id": "21005", "tache_code": "CREATE_ORDER_FLOW", "tache_name": "勘查单发起申请", "tache_Type": "3", "tache_state_name": "审核中", "deal_desc": "主要的字段 ，处理人、处理人联系方式", "support_phone": "18805152375", "duty_system": "订单中心", "create_date": "2019-05-23 09:38:45", "deal_date": "2019-05-23 09:38:45"}, {"work_item_id": "f004-3-2", "tache_id": "21007", "tache_code": "CREATE_ORDER_FLOW", "tache_name": "勘查单审核", "tache_state_name": "完成中", "tache_Type": "3", "deal_desc": "主要的字段 ，处理人、处理人联系方式", "support_phone": "18805152375", "duty_system": "处理系统", "create_date": "2019-05-23 09:38:45", "deal_date": "2019-05-23 09:38:45"}, {"work_item_id": "f004-3-2", "tache_id": "21007", "tache_code": "CREATE_ORDER_FLOW", "tache_name": "勘查单发起审核通过", "tache_state_name": "进行中", "tache_Type": "3", "deal_desc": "主要的字段 ，处理人、处理人联系方式", "support_phone": "18805152375", "duty_system": "处理系统", "create_date": "2019-05-23 09:38:45", "deal_date": ""}]}}, {"work_flow_id": "实例流程ID", "work_item_id": "b0001-2", "warning_date": "预警时间2019-08-08 12:12:12", "overtime_date": "超时时间2019-08-08 12:12:12", "tache_id": "1000", "tache_code": "CREATE_ORDER", "tache_name": "勘查单审核", "tache_type": "1", "create_date": "2019-05-23 09:38:45", "deal_date": "2019-05-23 09:38:45", "child_node_list": {"child_node": [{"work_item_id": "f002-3-1", "tache_id": "21005", "tache_code": "CREATE_ORDER_FLOW", "tache_name": "待审核", "tache_Type": "3", "tache_state_name": "审核中", "tache_state": "100", "deal_desc": "主要的字段 ，处理人、处理人联系方式", "support_phone": "18805152375", "duty_system": "订单中心", "create_date": "2019-05-23 09:38:45", "deal_date": "2019-05-23 09:38:45"}, {"work_item_id": "f004-3-2", "tache_id": "21007", "tache_code": "CREATE_ORDER_FLOW", "tache_name": "审核", "tache_state": "100", "tache_state_name": "完成中", "tache_Type": "3", "deal_desc": "主要的字段 ，处理人、处理人联系方式", "support_phone": "18805152375", "duty_system": "处理系统", "create_date": "2019-05-23 09:38:45", "deal_date": "2019-05-23 09:38:45"}]}}, {"work_flow_id": "实例流程ID", "work_item_id": "b0001-3", "warning_date": "预警时间2019-08-08 12:12:12", "overtime_date": "超时时间2019-08-08 12:12:12", "tache_id": "1000", "tache_code": "CREATE_ORDER", "tache_name": "资源勘查", "tache_type": "1", "create_date": "2019-05-23 09:38:45", "deal_date": "", "child_node_list": {"child_node": [{"work_item_id": "f002-3-1", "tache_id": "21005", "tache_code": "CREATE_ORDER_FLOW", "tache_name": "勘查实地考查", "tache_Type": "3", "tache_state_name": "审核中", "deal_desc": "主要的字段 ，处理人、处理人联系方式", "support_phone": "18805152375", "duty_system": "订单中心", "create_date": "2019-05-23 09:38:45", "deal_date": ""}, {"work_item_id": "f004-3-2", "tache_id": "21007", "tache_code": "CREATE_ORDER_FLOW", "tache_name": "勘查实地记录", "tache_state_name": "完成中", "tache_Type": "3", "deal_desc": "主要的字段 ，处理人、处理人联系方式", "support_phone": "18805152375", "duty_system": "处理系统", "create_date": "", "deal_date": ""}]}}, {"work_flow_id": "实例流程ID", "work_item_id": "b0001-4", "warning_date": "预警时间2019-08-08 12:12:12", "overtime_date": "超时时间2019-08-08 12:12:12", "tache_id": "1000", "tache_code": "CREATE_ORDER", "tache_name": "勘查单归档", "tache_type": "1", "create_date": "", "deal_date": "", "child_node_list": {"child_node": [{"work_item_id": "f002-3-1", "tache_id": "21005", "tache_code": "CREATE_ORDER_FLOW", "tache_name": "待审核", "tache_Type": "3", "tache_state_name": "审核中", "deal_desc": "主要的字段 ，处理人、处理人联系方式", "support_phone": "18805152375", "duty_system": "订单中心", "create_date": "2019-05-23 09:38:45", "deal_date": "2019-05-23 09:38:45"}, {"work_item_id": "f004-3-2", "tache_id": "21007", "tache_code": "CREATE_ORDER_FLOW", "tache_name": "审核", "tache_state_name": "完成中", "tache_Type": "3", "deal_desc": "主要的字段 ，处理人、处理人联系方式", "support_phone": "18805152375", "duty_system": "处理系统", "create_date": "2019-05-23 09:38:45", "deal_date": "2019-05-23 09:38:45"}]}}, {"work_flow_id": "实例流程ID", "work_item_id": "b0001-5", "warning_date": "预警时间2019-08-08 12:12:12", "overtime_date": "超时时间2019-08-08 12:12:12", "tache_id": "1000", "tache_code": "CREATE_ORDER", "tache_name": "勘查单归档33", "tache_type": "1", "create_date": "", "deal_date": "", "child_node_list": {"child_node": [{"work_item_id": "f002-3-1", "tache_id": "21005", "tache_code": "CREATE_ORDER_FLOW", "tache_name": "待审核", "tache_Type": "3", "tache_state_name": "审核中", "deal_desc": "主要的字段 ，处理人、处理人联系方式", "support_phone": "18805152375", "duty_system": "订单中心", "create_date": "2019-05-23 09:38:45", "deal_date": "2019-05-23 09:38:45"}, {"work_item_id": "f004-3-2", "tache_id": "21007", "tache_code": "CREATE_ORDER_FLOW", "tache_name": "审核", "tache_state_name": "完成中", "tache_Type": "3", "deal_desc": "主要的字段 ，处理人、处理人联系方式", "support_phone": "18805152375", "duty_system": "处理系统", "create_date": "2019-05-23 09:38:45", "deal_date": "2019-05-23 09:38:45"}]}}]}}, {"flow_id": "b0002", "parent_flow_id": "branch002", "flow_info": {"flow_node": [{"work_flow_id": "实例流程ID", "work_item_id": "当前节点ID 210033", "warning_date": "预警时间2019-08-08 12:12:12", "overtime_date": "超时时间2019-08-08 12:12:12", "tache_id": "1000", "tache_code": "CREATE_ORDER", "tache_name": "施工单发起", "tache_type": "1", "create_date": "2019-05-23 09:38:45", "deal_date": "2019-05-23 09:38:45", "child_node_list": {"child_node": [{"work_item_id": "f002-3-1", "tache_id": "21005", "tache_code": "CREATE_ORDER_FLOW", "tache_name": "施工单创建", "tache_Type": "3", "tache_state_name": "审核中", "deal_desc": "处理描述：主要的字段 ，处理人、处理人联系方式", "support_phone": "18805152375", "duty_system": "处理系统 订单中心", "create_date": "", "deal_date": ""}, {"work_item_id": "f004-3-2", "tache_id": "21007", "tache_code": "CREATE_ORDER_FLOW", "tache_name": "施工单审核", "tache_state_name": "完成中", "tache_Type": "3", "deal_desc": "处理描述：主要的字段 ，处理人、处理人联系方式", "support_phone": "18805152375", "duty_system": "处理系统", "create_date": "", "deal_date": ""}, {"work_item_id": "f004-3-2", "tache_id": "21007", "tache_code": "CREATE_ORDER_FLOW", "tache_name": "资源施工", "tache_state_name": "进行中", "tache_Type": "3", "deal_desc": "处理描述：主要的字段 ，处理人、处理人联系方式", "support_phone": "18805152375", "duty_system": "处理系统", "create_date": "", "deal_date": ""}, {"work_item_id": "f004-3-3", "tache_id": "21007", "tache_code": "CREATE_ORDER_FLOW", "tache_name": "施工单归档", "tache_state_name": "", "tache_Type": "3", "deal_desc": "主要的字段 ，处理人、处理人联系方式", "support_phone": " 18805152375", "duty_system": "处理系统", "create_date": "", "deal_date": ""}]}, "child_flow_id": "分支流程ID flow1,flow2"}]}}]}}}, "h5QryStaffPkgInfo": {"retCode": "0", "retMsg": "success", "data": [{"pkgType": "staff_pack", "pkgInfo": [{"prodId": "2000003336", "prodName": "B套餐100元", "prodDesc": "月套餐费100元/月，包含国内主叫时长500分钟（超出部分主叫0.19元/分钟），国内被叫免费，赠送国内移动数据流量2GB、WLAN流量5G/月、短信500条、彩信50条。包含彩铃、139邮箱（5元版）,免来电显示、全球通标准版套餐费以及和彩印基础包功能费。每月赠送“O了”客户端发起的会议电话500分钟主叫时长（每路会议电话分别计费)。2015年10月起，手机客户使用本套餐当月剩余移动数据流量，可结转至次月使用，结转流量次月月底失效，如变更套餐，该套餐本月剩余流量无法结转至下月使用。非手机客户不适用。本套餐费/固定费在每月上旬一次性收取。"}, {"prodId": "2000003337", "prodName": " C套餐50元", "prodDesc": " 月套餐费50元/月，包含国内主叫时长300分钟（超出部分主叫0.19元/分钟），国内被叫免费，赠送国内移动数据流量300MB、WLAN流量5G/月、短信200条、彩信20条。包含彩铃、139邮箱（5元版）,免来电显示、全球通标准版套餐费以及和彩印基础包功能费。每月赠送“O了”客户端发起的会议电话500分钟主叫时长（每路会议电话分别计费)。2015年10月起，手机客户使用本套餐当月剩余移动数据流量，可结转至次月使用，结转流量次月月底失效，如变更套餐，该套餐本月剩余流量无法结转至下月使用。非手机客户不适用。本套餐费/固定费在每月上旬一次性收取。"}]}, {"pkgType": "attach_pack", "pkgInfo": [{"prodId": "2000007285", "prodName": "D套餐30元（语音叠加包）", "prodDesc": " 月套餐费30元，包含长、市、漫主叫时长300分钟（超出部分主叫0.19元/分钟），赠送国内点对点短信300条。本套餐费/固定费平摊至每日收取。"}, {"prodId": "2000007286", "prodName": "E套餐30元（流量叠加包）", "prodDesc": " 月套餐费30元，包含2GB国内移动数据流量。2015年10月起，手机客户使用本套餐当月剩余移动数据流量，可结转至次月使用，结转流量次月月底失效 ，如变更套餐，该套餐本月剩余流量无法结转至下月使用。非手机客户不适用。本套餐费/固定费平摊至每日收取。 "}, {"prodId": "2000007961", "prodName": " F套餐10元（家庭业务体验包）", "prodDesc": " 月费10元，开通的固话、互联网电视、和目套餐所产生的套餐费作优惠减免。本套餐费/固定费平摊至每日收取。"}, {"prodId": "2000009231", "prodName": "G套餐10元（新业务体验包）", "prodDesc": " 10元/月，包含咪咕超级会员（畅享版）权益和咪咕视频国内定向流量9GB，并且减免和多号、和彩云的包月费用。 "}]}, {"pkgType": "h_pack", "pkgInfo": [{"prodId": "2000012079", "prodName": "H套餐10元（新业务体验包）", "prodDesc": "月套餐费10元，可免两项新业务功能费，领取规则及可选业务种类以当月O了系统领取页面的展示为准。本套餐费/固定费平摊至每日收取。", "subProdItem": [{"prodId": "2400000771", "prodName": "个人云（移动网盘尊享版）", "prodDesc": "和彩云(移动网盘尊享版)，又名和彩云（家庭版），资费30元/月，包含和彩云铂金会员（4T空间等权益）、和家相册铂金会员（2T空间等权益）。江苏移动掌厅办理的路径:  http://wap.js.10086.cn/HCYUN.thtml "}, {"prodId": "2380000423", "prodName": "咪咕超级会员", "prodDesc": "包含咪咕超级会员（畅享版）权益（同G套餐10元（新业务体验包）），19.9元/月，可享受咪咕音乐、咪咕视频、咪咕阅读、咪咕游戏、咪咕圈圈的会员权益，具体请登录相关客户端查询。江苏移动掌厅办理的路径: http://wap.js.10086.cn/MGCJHY_CXB.html "}, {"prodId": "2000009573", "prodName": "任我选会员15元", "prodDesc": "15元/月，成功订购后，每月可领取爱奇艺、优酷、腾讯视频、咪咕视频、芒果TV、PP视频等视频VIP会员，每月可领取一项使用，成功开通后随开通短信指引去指定页面领取会员权益。套餐中包含专用于中国移动（2G/3G/4G网络）下 腾讯视频、爱奇艺、优酷、咪咕视频、芒果TV、PP视频、搜狐、酷我、喜马拉雅APP中部分内容的1GB定向全国移动数据流量。视频会员任我选内所含视频VIP会员种类等将根据业务发展的实际情况等做不定期增减，江苏移动将提前通过门户网站公告。视频会员任我选内可选视频VIP会员种类以当月业务办理及权益领取页面的展示为准。江苏移动掌厅办理的路径: http://wap.js.10086.cn/4G_NRQYB.html "}, {"prodId": "2000007708", "prodName": "IMS/和家固话", "prodDesc": "月套餐费8元包本地通话及国内长途（不含港澳台）共计300分钟；套餐之外通话本地及国内长途（不含港澳台）0.1元/分钟。套餐内免来电显示月功能费，免固话月租费。江苏移动掌厅办理的路径: http://wap.js.10086.cn/HOMEFIXEDLINEORDER.html"}, {"prodId": "2000009015", "prodName": "和目套餐", "prodDesc": "月套餐费12元/路。用户可享受7天历史视频回看功能。套餐按天扣费，开通首月不补齐，次月起月底补齐。江苏移动掌厅办理的路径:  https://wap.js.10086.cn/YCC.thtml"}]}]}, {"pkgType": "band_pack", "pkgInfo": [{"prodId": "2000007271", "prodName": " 20元包月（20M）", "bandWidth": "20M", "prodDesc": "宽带月套餐费20元，接入速率20M（最大可达），不限时。套餐费按日分摊，月底补齐。本套餐费/固定费平摊至每日收取。"}, {"prodId": "2000007272", "prodName": " 30元包月（50M）", "bandWidth": "50M", "prodDesc": " 宽带月套餐费30元，接入速率50M（最大可达），不限时。套餐费按日分摊，月底补齐。本套餐费/固定费平摊至每日收取。"}, {"prodId": "2000007962", "prodName": " 40元包月（100M）", "bandWidth": "100M", "prodDesc": "宽带月套餐费40元，接入速率100M，不限时。套餐费按日分摊，月底补齐。本套餐费/固定费平摊至每日收取。"}]}]}, "h5QryOpenedService": {"retCode": "0", "retMsg": "success", "data": [{"pkgType": "staff_pack", "isOpen": "1", "pkgInfo": [{"prodId": "2000003336", "prodName": "B套餐100元", "prodDesc": ""}, {"prodId": "2000003337", "prodName": "C套餐100元", "prodDesc": "", "startDate": "2019-12-01", "endDate": "2019-12-31"}], "openedBand": ""}, {"pkgType": "h_pack", "isOpen": "1", "pkgInfo": [{"prodId": "2000012079", "prodName": "H套餐10元（新业务体验包）", "prodDesc": "月套餐费10元，可免两项新业务功能费，领取规则及可选业务种类以当月O了系统领取页面的展示为准。本套餐费/固定费平摊至每日收取。", "startDate": "2019-12-01", "endDate": "2019-12-31", "subProdItem": [{"prodId": "2000007708", "prodName": "IMS/和家固话", "prodDesc": "月套餐费8元包本地通话及国内长途（不含港澳台）共计300分钟；套餐之外通话本地及国内长途（不含港澳台）0.1元/分钟。套餐内免来电显示月功能费，免固话月租费。江苏移动掌厅办理的路径: http://wap.js.10086.cn/HOMEFIXEDLINEORDER.html"}, {"prodId": "2000009015", "prodName": "和目套餐", "prodDesc": "月套餐费12元/路。用户可享受7天历史视频回看功能。套餐按天扣费，开通首月不补齐，次月起月底补齐。江苏移动掌厅办理的路径:  https://wap.js.10086.cn/YCC.thtml"}], "instanceid": "683452349093"}], "openedBand": ""}, {"pkgType": "attach_pack", "isOpen": "1", "pkgInfo": [{"prodId": "2000007286", "prodName": "E套餐30元（流量叠加包）", "bandWidth": "", "prodDesc": "", "startDate": "2019-12-01", "endDate": "2020-01-01"}, {"prodId": "2000009231", "prodName": "G套餐10元（新业务体验包）", "bandWidth": "", "prodDesc": "", "startDate": "2019-12-01", "endDate": "2019-12-31"}], "openedBand": ""}, {"pkgType": "band_pack", "isOpen": "1", "pkgInfo": [{"prodId": "2000007272", "prodName": " 30元包月（50M）", "bandWidth": "50M", "startDate": "2019-12-01", "endDate": "", "prodDesc": " 宽带月套餐费30元，接入速率50M（最大可达），不限时。套餐费按日分摊，月底补齐。本套餐费/固定费平摊至每日收取。"}], "openedBand": "0"}]}, "h5qryUserList": {"retCode": "0", "retMsg": null, "data": {"userList": [{"user_id": "1527200000128173", "msisdn": "***********", "sex": "男", "age": "68", "dou_1": "33.3369", "dou_2": "129.4912", "dou_3": "168.1221", "arpu_1": "109.700", "arpu_2": "108.000", "arpu_3": "123.300", "innet": "是", "product_id": "专业套餐（园丁套餐）", "product_name": "11111", "user_status": "正常", "business_id": "1051827360", "content1": "您好，您所办理的新入网营销案已到期/即将到期，现为您提供优惠政策宽带直降叠加包50直降40（6个月），可继续使用宽带业务（供参考）", "content2": "", "group_name": ""}, {"user_id": "1527200000128173", "msisdn": "***********", "sex": "男", "age": "68", "dou_1": "33.3369", "dou_2": "129.4912", "dou_3": "168.1221", "arpu_1": "109.700", "arpu_2": "108.000", "arpu_3": "123.300", "innet": "是", "product_id": "专业套餐（园丁套餐）", "product_name": "", "user_status": "正常", "business_id": "1051827360", "content1": "您好，您所办理的新入网营销案已到期/即将到期，现为您提供优惠政策宽带直降叠加包50直降40（6个月），可继续使用宽带业务（供参考）", "content2": "", "group_name": "新大陆集团"}], "dealNum": 0}}, "h5qryActiveList": {"retCode": "0", "retMsg": null, "data": {"activeList": [{"data_list": [{"product_type": "3", "product_id_exp5": "90返30（6个月）", "product_id_exp6": "", "product_id_exp7": ""}, {"product_type": "3", "product_id_exp5": "70返30（6个月）", "product_id_exp6": "", "product_id_exp7": ""}, {"product_type": "3", "product_id_exp5": "90直降40（6个月）", "product_id_exp6": "", "product_id_exp7": ""}, {"product_type": "3", "product_id_exp5": "32元大流量日租卡优惠（12个月）", "product_id_exp6": "", "product_id_exp7": ""}, {"product_type": "3", "product_id_exp5": "158返20（6个月）", "product_id_exp6": "", "product_id_exp7": ""}, {"product_type": "3", "product_id_exp5": "70直降40（6个月）", "product_id_exp6": "", "product_id_exp7": ""}, {"product_type": "3", "product_id_exp5": "120返30（6个月）", "product_id_exp6": "", "product_id_exp7": ""}, {"product_type": "3", "product_id_exp5": "90直降50（6个月）", "product_id_exp6": "", "product_id_exp7": ""}, {"product_type": "3", "product_id_exp5": "90直降60（6个月）", "product_id_exp6": "", "product_id_exp7": ""}], "activity_id": "100000159572", "activity_name": "新入网直降类接盘-有宽带", "step_id": "5cac2e7d15434fbeb9e1ccc8b0b28757", "usergroup_id": "EX1000218953", "usergroup_num": "47", "begin_date": "20190830", "end_date": "20191031", "market_term": "您好，您所办理的新入网营销案已到期/即将到期，现为您提供优惠政策接盘，可继续使用宽带业务（供参考）", "sms_content": "您好，您所办理的新入网营销案已到期/即将到期，现为您提供优惠政策接盘，可继续使用宽带业务【中国移动优惠提醒】", "content1": "", "content2": ""}], "businessId": "15003538", "sessionId": 20191203591738}}, "h5qryFaceInfo": {"retCode": "0", "retMsg": null, "data": {"operInfo": {"servNumber": "***********", "facePicId": "2019112710007894", "createTime": null, "modifyTime": null, "state": null, "operInfoJson": "{\"address\":\"江苏省如东县双甸镇石甸社区十七组47号\",\"certeffdate\":\"20121006\",\"certexpdate\":\"20221006\",\"certid\":\"320623198912286645\",\"custerm_misdn\":\"***********\",\"device_type\":\"5\",\"operId\":\"14150759\",\"operation_srl\":\"20191127484387\",\"phone_type\":\"Redmi K20 Pro\",\"region_id\":\"14\",\"staff_id\":\"1488018492933394\",\"username\":\"周文\"}"}, "faceCount": "1"}}, "h5CountOrder": {"retCode": "0", "retMsg": null, "data": {"total": 7, "unfinished": 4}}, "h5GetRecycleBusinessInfo": {"retCode": "0", "retMsg": "success", "data": {"recycleBusiness": "01", "pageUrl": "https://ald.igooma.cn", "freezeRatio": "1.0", "funderList": [{"id": "01", "label": "蜂云"}, {"id": "02", "label": "蜂泰"}], "orgId": "14123456", "orgName": "南京门店"}}, "h5CreateOrder": {"retCode": "0", "retMsg": "success", "data": {"recId": "123456789"}}, "h5GetPayUrl": {"retCode": "0", "retMsg": "success", "data": {"payUrl": "https://bang.360.cn/page/huishou-jsyd/?brand_id=10"}}, "h5QryOrderInfo": {"retCode": "0", "retMsg": "success", "data": [{"telnum": "***********", "orderId": "123456789", "evalAmount": "10000", "funder": "01", "recycleBusiness": "01", "recoverymodelname": "苹果手机", "oldIMEI": "123456789", "newIMEI": "123456789", "state": "-5", "createDate": "2020-02-01 16:09:11", "remark": "失败原因"}, {"telnum": "***********", "orderId": "***********", "evalAmount": "10000", "funder": "01", "recycleBusiness": "01", "recoverymodelname": "三星手机", "oldIMEI": "123456789", "newIMEI": "123456789", "state": "-10", "createDate": "2020-02-01 16:09:11", "remark": "失败原因"}, {"telnum": "***********", "orderId": "3203222000101586", "evalAmount": "34000", "funder": "02", "recycleBusiness": "01", "recoverymodelname": "二星手机", "oldIMEI": "123456789", "newIMEI": "123456789", "state": "1", "createDate": "2020-02-01 16:09:11", "remark": ""}, {"telnum": "***********", "orderId": "3203222000101586", "evalAmount": "34000", "funder": "02", "recycleBusiness": "01", "recoverymodelname": "小米手机", "oldIMEI": "123456789", "newIMEI": "123456789", "state": "2", "createDate": "2020-02-01 16:09:11", "remark": ""}, {"telnum": "***********", "orderId": "3203222000101586", "evalAmount": "34000", "funder": "02", "recycleBusiness": "01", "recoverymodelname": "三星手机", "oldIMEI": "123456789", "newIMEI": "123456789", "state": "3", "createDate": "2020-02-01 16:09:11", "remark": ""}, {"telnum": "***********", "orderId": "3203222000101586", "evalAmount": "34000", "funder": "01", "recycleBusiness": "01", "recoverymodelname": "三星手机", "oldIMEI": "123456789", "newIMEI": "123456789", "state": "4", "createDate": "2020-02-01 16:09:11", "remark": "失败原因"}, {"telnum": "***********", "orderId": "3203222000101586", "evalAmount": "65000", "funder": "01", "recoverymodelname": "三星手机", "recycleBusiness": "01", "oldIMEI": "928882hhsjs92", "newIMEI": "928882hhsjs92", "state": "5", "createDate": "2020-02-01 16:09:11", "remark": ""}]}, "h5qryLevelEffdate": {"retCode": "0", "retMsg": "", "data": {"effDate": "20180630000000", "newLevelId": "300004015037", "newLevelName": "接续分档1", "newActId": "3002100294", "newActName": "小燕子的宽带接续营销活动——1", "canRenew": "1"}}, "h5GetOrderInfoById": {"retCode": "0", "retMsg": "success", "data": {"telnum": "***********", "orderId": "123456789", "evalAmount": "10000", "funder": "01", "recycleBusiness": "01", "oldIMEI": "123456789", "newIMEI": "123456789", "state": "1", "createDate": "2020-02-01 16:09:11", "tcborderid": "123456", "recoverymodelname": "苹果手机", "pageUrl": "", "payUrl": "www.baidu.com", "custName": "王长文"}}, "h5upLoadImg": {"retCode": "0", "retMsg": "success", "data": {"attachId": "123456789", "attachName": "附件名称"}}, "h5BusiSubmit": {"retCode": "0", "retMsg": "success", "data": ""}, "h5QryOrderFreezeResult": {"retCode": "0", "retMsg": "success", "data": {"status": "1", "rspinfo": "资金授权查询失败，支付宝资金授权订单不存在"}}, "h5getReport": {"retCode": "0", "retMsg": "成功查询外呼报表", "data": {"headInfo": "地域编码|是否可下钻|时段|地域|外呼项目数|外呼目标客户数|已呼通|呼通率|未呼用户数", "dataList": [{"columnValue": "8502|1|14|南京市|7|33|10|30.30%|23"}], "streamSeq": "4320180606141834234"}}, "h5qryAmsBannerInfo": {"retCode": "0", "retMsg": null, "data": [{"privId": "100001", "privName": "智能组网", "privDesc": "wifi跟你走 让家更聪明", "picId": "1000011", "picName": "imbanner1.png"}, {"privId": "100002", "privName": "宽带开通", "privDesc": "无忧快速办理", "picId": "1000021", "picName": "imbanner2.png"}]}, "amsMenuList": {"retCode": "0", "retMsg": null, "data": [{"privName": "业务专区", "isNewFeature": 0, "hasCrmid": "0", "hasPwd": "0", "opId": "fsop", "opParentid": "fsop", "sort": 1, "parentId": "4", "authenType": null, "levelId": 2, "featureType": null, "itemList": [{"privName": "智能组网", "isNewFeature": 0, "hasCrmid": "0", "hasPwd": "1", "opId": "fsop", "opParentid": "fsop", "sort": 1, "parentId": "20000047", "authenType": null, "levelId": 3, "featureType": null, "privId": "100057", "picId": "100057", "isHot": null}, {"privName": "互联网电视", "isNewFeature": 0, "hasCrmid": "0", "hasPwd": "0", "opId": "fsop", "opParentid": "fsop", "sort": 2, "parentId": "20000047", "authenType": 0, "levelId": 3, "featureType": "fea_nettv", "privId": "100013", "picId": "10004", "isHot": null}, {"privName": "宽带开通", "isNewFeature": 0, "hasCrmid": "1", "hasPwd": "1", "opId": "fsop", "opParentid": "fsop", "sort": 3, "parentId": "20000047", "authenType": 0, "levelId": 3, "featureType": "fea_mband", "privId": "100012", "picId": "10003", "isHot": null}, {"privName": "活动受理", "isNewFeature": 0, "hasCrmid": "0", "hasPwd": "0", "opId": "huodong", "opParentid": "fsop", "sort": 4, "parentId": "20000047", "authenType": 0, "levelId": 3, "featureType": "fea_market", "privId": "100014", "picId": "20701", "isHot": null}, {"privName": "预配号入网", "isNewFeature": 0, "hasCrmid": "1", "hasPwd": "1", "opId": "fsop", "opParentid": "fsop", "sort": 5, "parentId": "20000047", "authenType": -1, "levelId": 3, "featureType": null, "privId": "10019", "picId": "10019", "isHot": null}], "privId": "20000047", "picId": null, "isHot": null}, {"privName": "辅助专区", "isNewFeature": 0, "hasCrmid": "0", "hasPwd": "0", "opId": "fsop", "opParentid": "fsop", "sort": 2, "parentId": "4", "authenType": null, "levelId": 2, "featureType": null, "itemList": [{"privName": "业务日志查询(新)", "isNewFeature": 0, "hasCrmid": "0", "hasPwd": "1", "opId": "fsop", "opParentid": "fsop", "sort": 1, "parentId": "20000049", "authenType": -1, "levelId": 3, "featureType": "fea_ywrz", "privId": "100040", "picId": "137", "isHot": null}, {"privName": "已开服务(新)", "isNewFeature": 0, "hasCrmid": "0", "hasPwd": "1", "opId": "fsop", "opParentid": "fsop", "sort": 2, "parentId": "20000049", "authenType": 0, "levelId": 3, "featureType": "fea_yk", "privId": "100035", "picId": "10010", "isHot": null}, {"privName": "免填单补录(新)", "isNewFeature": 0, "hasCrmid": "1", "hasPwd": "1", "opId": "fsop", "opParentid": "fsop", "sort": 3, "parentId": "20000049", "authenType": -1, "levelId": 3, "featureType": null, "privId": "100028", "picId": "10024", "isHot": null}, {"privName": "活动推荐", "isNewFeature": 0, "hasCrmid": "0", "hasPwd": "0", "opId": "fsop", "opParentid": "fsop", "sort": 4, "parentId": "20000049", "authenType": 0, "levelId": 3, "featureType": null, "privId": "100060", "picId": "100060", "isHot": null}, {"privName": "预占解除(新)", "isNewFeature": 0, "hasCrmid": "1", "hasPwd": "1", "opId": "fsop", "opParentid": "fsop", "sort": 6, "parentId": "20000049", "authenType": -1, "levelId": 3, "featureType": null, "privId": "100030", "picId": "10023", "isHot": null}], "privId": "20000049", "picId": null, "isHot": null}]}, "h5judgeRewardItem": {"retCode": "0", "retMsg": null, "data": {"rewardList": ["10000011", "10000013"], "itemList": [{"label": "南京校园", "id": "p200002=1413943"}]}}, "h5QryKnowledge": {"retCode": "0", "retMsg": "", "data": [{"knowledgeId": "100001", "releaser": "14150759", "releaseTime": "2020-02-1500:00:00", "releaseTelnum": null, "releaseName": "王涛", "title": "测试", "content": "测试内容1测试内容2测试内容3", "readNumber": "100", "likeNumber": "20", "typeCode": "999999", "fileName": null, "fileUrl": "a,b,c,d,e,f,g,h,i,j,k", "showRegion": "1"}, {"knowledgeId": "100002", "releaser": "14150759", "releaseTime": "2020-02-1500:00:00", "releaseTelnum": null, "releaseName": "王涛", "title": "测试", "content": "测试内容1测试内容2测试内容3", "readNumber": "100", "likeNumber": "20", "typeCode": "100002", "fileName": null, "fileUrl": "a,b", "showRegion": "1"}, {"knowledgeId": "100003", "releaser": "14150759", "releaseTime": "2020-02-1500:00:00", "releaseTelnum": null, "releaseName": "王涛", "title": "测试", "content": "测试内容1测试内容2测试内容3", "readNumber": "100", "likeNumber": "20", "typeCode": "100003", "fileName": null, "fileUrl": "a,b", "showRegion": "1"}, {"knowledgeId": "100004", "releaser": "14150759", "releaseTime": "2020-02-1500:00:00", "releaseTelnum": null, "releaseName": "王涛", "title": "测试", "content": "测试内容1测试内容2测试内容3", "readNumber": "100", "likeNumber": "20", "typeCode": "100004", "fileName": null, "fileUrl": "a,b", "showRegion": "1"}, {"knowledgeId": "100001", "releaser": "14150759", "releaseTime": "2020-02-1500:00:00", "releaseTelnum": null, "releaseName": "王涛", "title": "测试", "content": "<p> 泰州分公司为进一步拓展市场营销机会，提升网格活力，近期开展了网格营销机会挖掘专项活动。</p> <p>鼓励各网格上报营销优秀案例并汇编下发；网格根据自身实际选择合适营销模式进行复制。当前已下发《先锋营营销场景推广业务拓展情况周报》，新增挖掘公交公司合作、年底订货会、燃气检测等15个成功的市场营销模板。下阶段泰州分公司计划将市场机会的挖掘纳入区域中心考评中，提升各区域发挥挖掘市场营销机会的主动性。 </p> <p> <br/> </p>", "readNumber": "100", "likeNumber": "20", "typeCode": "100005", "fileName": null, "fileUrl": "a,b", "showRegion": "1"}, {"knowledgeId": "100001", "releaser": "14150759", "releaseTime": "2020-02-1500:00:00", "releaseTelnum": null, "releaseName": "王涛", "title": "测试", "content": "测试内容1测试内容2测试内容3", "readNumber": "100", "likeNumber": "20", "typeCode": "100003", "fileName": null, "fileUrl": "a,b", "showRegion": "1"}, {"knowledgeId": "100001", "releaser": "14150759", "releaseTime": "2020-02-1500:00:00", "releaseTelnum": null, "releaseName": "王涛", "title": "测试", "content": "测试内容1测试内容2测试内容3", "readNumber": "100", "likeNumber": "20", "typeCode": "100002", "fileName": null, "fileUrl": "a,b", "showRegion": "1"}]}, "h5GetHasCollectionInfo": {"retCode": "0", "retMsg": null, "data": [{"infoId": "1", "businessType": "数据专线", "operatorName": "联通", "endDate": "2020-11-30"}, {"infoId": "2", "businessType": "集团专线", "operatorName": "电信", "endDate": "2020-12-31"}, {"infoId": "2", "businessType": "集团专线", "operatorName": "电信", "endDate": "2020-12-31"}, {"infoId": "2", "businessType": "集团专线", "operatorName": "电信", "endDate": "2020-12-31"}, {"infoId": "2", "businessType": "集团专线", "operatorName": "电信", "endDate": "2020-12-31"}, {"infoId": "2", "businessType": "集团专线", "operatorName": "电信", "endDate": "2020-12-31"}, {"infoId": "2", "businessType": "集团专线", "operatorName": "电信", "endDate": "2020-12-31"}, {"infoId": "2", "businessType": "集团专线", "operatorName": "电信", "endDate": "2020-12-31"}, {"infoId": "2", "businessType": "集团专线", "operatorName": "电信", "endDate": "2020-12-31"}, {"infoId": "2", "businessType": "集团专线", "operatorName": "电信", "endDate": "2020-12-31"}]}, "h5GetTextType": {"retCode": "0", "retMsg": "", "data": [{"typeid": "100001", "typename": "案例", "color": "#F3A016"}, {"typeid": "100002", "typename": "培训", "color": "#1681FB"}, {"typeid": "999999", "typename": "培训+考试", "color": "#7387EB"}, {"typeid": "100003", "typename": "答疑", "color": "#71C43A"}, {"typeid": "100004", "typename": "新闻", "color": "#F50000"}]}, "h5qryLocation": {"locationInfo": {"staffId": "1488018492933394", "phoneNumber": "***********", "imei": "865664034915980", "regionId": "14", "longitude": "118.75221", "latitude": "32.04542", "location": "江苏省南京市鼓楼区北圩路39号靠近南京银行(清凉门支行)", "seq": null, "stationId": null}, "retCode": "0", "retMsg": ""}, "h5QryEvalCompleted": {"retCode": "0", "retMsg": "", "data": {"completed": "1", "payUrl": "www.baidu.com"}}, "h5getNumState": {"retCode": "0", "retMsg": "", "data": [{"phoneNumber": "15862651185", "lockType": "修改密码连续错误5次锁定"}, {"phoneNumber": "15862651185", "lockType": "验证码连续错误5次锁定"}, {"phoneNumber": "15862651185", "lockType": "登录密码连续错误5次锁定"}]}, "h5delectKey": {"retCode": "0", "retMsg": "", "data": null}, "h5CheckPassword": {"retCode": "0", "retMsg": null, "data": {"servNumber": "***********", "password": "", "operatorName": "梅志斌", "region": "14", "regionName": null, "orgId": "1488018465153416", "orgName": "市场经营部", "staffId": "1488018492933394", "longitude": null, "latitude": null, "location": null, "imei": null, "stationId": null, "stationName": null, "device": null, "crmId": null, "tokenid": null, "oa": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stations": null, "viewInfoList": null, "hasFaceInfo": null, "faceSwitch": null}}}