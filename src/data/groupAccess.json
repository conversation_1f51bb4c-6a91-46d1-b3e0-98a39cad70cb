{"h5CheckGroupEnterNet": {"retCode": "0", "retMsg": "success", "data": {"custName": "李四", "certAddr": "江苏省南京市鼓楼区中海大厦", "residentAddr": "江苏省南京市江宁区", "gender": "1", "groupCustname": "新大陆", "certEffdate": "**************", "certExpdate": "**************", "country": "China"}}, "h5StoreGroupEnterNetInfo": {"retCode": "0", "retMsg": "success", "data": {"recId": "202101201309"}}, "h5ListGrpEnterNetRecord": {"retCode": "0", "retMsg": "success", "data": [{"ordered": "123456", "region": "14", "operId": "14150759", "groupCertId": "********", "groupCertType": "BusinessLicence", "groupCertName": "测试集团", "groupCertAddr": "测试的集团地址", "grouppic": "FSOP_123_licence.jpg", "proxypic": "FSOP_456_proxy.jpg", "keyParameterJson": {"jbEnterNetSfz": {"sfzObj": {"sfzName": "哈哈", "sfzId": "********"}, "cardPicId": "123", "headId": "123", "netSrl": "123", "certType": "IdCard", "pseq": "123"}, "userEnterNetSfz": {"sfzObj": {"sfzName": "哈哈", "sfzId": "********"}, "cardPicId": "123", "headId": "123", "netSrl": "123", "certType": "IdCard", "pseq": "123"}, "grpEnterNetSfz": {"pseq": "123", "netSrl": "123"}}, "state": "0", "createTime": "2020/07/06 11:07:51", "aldOrderId": "1234567"}]}, "h5QryTaocan": {"retCode": "0", "retMsg": "success", "data": {"taocanBoList": [{"pkgId": "123456", "pkgName": "测试包", "pkgDesc": "测试包描述", "pkgFee": "10"}, {"pkgId": "123456", "pkgName": "测试包", "pkgDesc": "测试包描述", "pkgFee": "10"}]}}, "openAccountSubmit": {"retCode": "0", "retMsg": "success", "data": {"recId": "********"}}, "h5GetGroupCustInfo": {"retCode": "0", "retMsg": "success", "data": {"custName ": "王五", "certAddr ": "江苏省南京市鼓楼区中海大厦", "residentAddr ": "江苏省南京市江宁区", "gender ": "1", "groupCustname ": "新大陆", "certEffdate": "**************", "certExpdate": "**************", "country": "China"}}, "seconSMSQuery": {"retCode": "0", "retMsg": "success", "data": [{"serviceNumber": "111", "groupName": "新大陆", "offeringName": "短信", "createDate": "2020/7/6", "statusDate": "2020/7/6", "confirmStatus": "1", "confirmLogSeq": "********"}, {"serviceNumber": "22", "groupName": "新大陆2", "offeringName": "短信", "createDate": "2020/7/6", "statusDate": "2020/7/6", "confirmStatus": "12", "confirmLogSeq": "********"}]}, "seconSMSDeal": {"retCode": "0", "retMsg": "success", "data": null}, "groupVnetQuery": {"retCode": "0", "retMsg": "success", "data": [{"longCode": "111111", "shortCode": "111", "confirmLogSeq": "12345446", "vpnnumber": "111"}, {"longCode": "222", "shortCode": "111", "confirmLogSeq": "12345446", "vpnnumber": "111"}]}, "vnetNEQuery": {"retCode": "0", "retMsg": "success", "data": [{"longCode": "123456", "shortCode": "111", "vpnnumber": "111"}]}, "vnetOrderDeal": {"retCode": "0", "retMsg": "success", "data": null}, "groupRingSelfQuery": {"retCode": "0", "retMsg": "success", "data": [{"telNumber": "111111", "confirmLogSeq": "111", "ring": "111"}]}, "ringSignQuery": {"retCode": "0", "retMsg": "success", "data": [{"telNumber": "15866247865", "state": "1", "ring": "111"}]}, "workOrderReset": {"retCode": "0", "retMsg": "success", "data": null}}