{"h5QryProductInfo": {"retCode": "0", "retMsg": "success", "data": {"prodName": "亲情网套餐", "prodDesc": "1、一个全国亲情网中只能有一个主号，一个号码仅限加入一个全国亲情网。<br>2、亲情网成员可以是省内移动号码，也可以是省外移动号码；成员数2-19个（含组网人及成员），2个及以上客户方可组网。"}}, "h5QryMemberInfo": {"retCode": "0", "retMsg": "success", "data": {"mainGroup": [{"groupName": "群组一", "pkgprodid": "111111111", "bbossProdInstId": "222222222", "region": "14", "poidcode": "01", "poidlable": "11", "membeinfo": [{"memsubsid": "5555", "servnumber": "15189807945", "isprima": "1", "memregion": "14", "shortnum": "751", "startdate": "2019-05-27", "enddate": "2019-05-27", "memLable": "hello world"}, {"memsubsid": "5555", "servnumber": "15189807946", "isprima": "0", "memregion": "14", "shortnum": "752", "startdate": "2019-05-27", "enddate": "2019-05-27", "memLable": "hello world"}]}, {"groupName": "群组二", "pkgprodid": "111111111", "bbossProdInstId": "222222222", "region": "14", "poidcode": "02", "poidlable": "11", "membeinfo": [{"memsubsid": "5555", "servnumber": "15189807945", "isprima": "1", "memregion": "14", "shortnum": "", "startdate": "2019-05-27", "enddate": "2019-05-27", "memLable": "hello world"}, {"memsubsid": "5555", "servnumber": "15189807947", "isprima": "0", "memregion": "14", "shortnum": "", "startdate": "2019-05-27", "enddate": "2019-05-27", "memLable": "hello world"}]}], "subGroup": [{"groupName": "群组三", "pkgprodid": "111111111", "bbossProdInstId": "222222222", "region": "14", "poidcode": "03", "poidlable": "11", "membeinfo": [{"memsubsid": "5555", "servnumber": "15189807948", "isprima": "1", "memregion": "14", "shortnum": "762", "startdate": "2019-05-27", "enddate": "2019-05-27", "memLable": "hello world"}, {"memsubsid": "5555", "servnumber": "15189807945", "isprima": "0", "memregion": "14", "shortnum": "", "startdate": "2019-05-27", "enddate": "2019-05-27", "memLable": "hello world"}]}], "shortNumStatus": "1", "shortNum": "751", "poidcode": "01", "shortNumCollect": ["751", "752", "762"], "poidcodeStr": "01<br>02<br>03"}}, "h5SubscribeFamilyNet": {"retCode": "0", "retMsg": "success", "data": null}, "h5MaintainMember": {"retCode": "0", "retMsg": "success", "data": null}, "h5UnSubscribeFamilyNet": {"retCode": "0", "retMsg": "success", "data": null}, "h5SwitchShortNum": {"retCode": "0", "retMsg": "success", "data": null}, "h5QryValidShortNum": {"retCode": "0", "retMsg": "success", "data": {"five": ["750", "753", "754", "755", "756", "757", "758", "759"], "six": ["760", "761", "763", "764", "765", "766", "767", "768", "769"]}}}