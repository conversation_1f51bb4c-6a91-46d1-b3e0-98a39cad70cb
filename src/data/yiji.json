{"h5GetBroadBandInfo": {"retCode": "0", "retMsg": null, "data": {"userName": "公爵", "addrName": "南京书香名苑2栋4单元607室", "addrId": "123456789", "userType": "1", "userTypeName": "家庭用户", "linkMan": "", "contactPhone": "", "districtName": "书香名苑", "isHaveDevice": "1", "netWorkType": "0"}}, "h5GetGimsMoveInfo": {"retCode": "0", "retMsg": null, "data": [{"networkType": "1", "sameBuilding": "0", "equipRange": "2", "callFor": "1106", "netTypeName": "FTTH", "ngnFor": ""}]}, "h5MoveMachineFee": {"retCode": "0", "retMsg": null, "data": {"chargeList": [{"chargecode": "Handle734", "chargemoney": "8000", "charegename": "同楼移机手续费"}, {"chargecode": "DeviceTestFee", "chargemoney": "5000", "discountmoney": "5000", "charegename": "家庭终端调测费"}], "totalFee": 14000, "linkman": "方文山", "addrname": "建邺区嘉陵江东街18号", "appointdate": "2019/08/09", "crditFee": 0, "rangetype": "1", "fee": ""}}, "h5MoveMachineSubmit": {"retCode": "0", "retMsg": "success", "data": "200183668776421902"}, "h5whitelistQuery": {"retCode": "0", "retMsg": "success", "data": ""}, "h5qryShoppingCartItemList": {"retCode": "0", "retMsg": null, "data": {"totalCount": "12", "shopCartItemInfoList": [{"itemId": "1172072", "entityType": "O", "entityId": "2400000075", "entityCode": "YX139-BAN-5Y-BAO", "entityName": "139邮箱", "entityDesc": "139邮箱", "actionType": "1", "effType": "", "effDate": "", "expType": "", "expDate": "", "busineesCode": null, "quantity": "1", "scenarioType": "OfferPkgEntry", "accessType": "601", "channelName": "阿拉盯", "createDate": "20221031163442", "createProleId": "518220000268345", "operName": "yz20160511", "createDeptId": "23100003", "status": "1"}, {"itemId": "1172070", "entityType": "O", "entityId": "2400000075", "entityCode": "YX139-BAN-5Y-BAO", "entityName": "139邮箱", "entityDesc": "139邮箱", "actionType": "1", "effType": "", "effDate": "", "expType": "", "expDate": "", "busineesCode": null, "quantity": "1", "scenarioType": "OfferEntry", "accessType": "601", "channelName": "CRM营业厅", "createDate": "20221031163032", "createProleId": "518220000268345", "operName": "yz20160511", "createDeptId": "23100003", "status": "1"}, {"itemId": "1169001", "entityType": "O", "entityId": "2413000029", "entityCode": "2413000029", "entityName": "智能固话", "entityDesc": "智能固话（智能音箱）", "actionType": "1", "effType": "I", "effDate": "", "expType": "", "expDate": "", "busineesCode": null, "quantity": "1", "scenarioType": "OfferEntry", "accessType": "601", "channelName": "CRM营业厅", "createDate": "20221029205154", "createProleId": "518220000268345", "operName": "yz20160511", "createDeptId": "23100003", "status": "1"}, {"itemId": "1155037", "entityType": "O", "entityId": "1000100305", "entityCode": "1000100305", "entityName": "4G自选（预付费）", "entityDesc": "", "actionType": "1", "effType": "", "effDate": "20221101000000", "expType": "", "expDate": "", "busineesCode": null, "quantity": "1", "scenarioType": "MainOfferChg", "accessType": "601", "channelName": "CRM营业厅", "createDate": "20221027194656", "createProleId": "518220000268345", "operName": "yz20160511", "createDeptId": "23100003", "status": "3"}, {"itemId": "1155021", "entityType": "O", "entityId": "1000100305", "entityCode": "1000100305", "entityName": "4G自选（预付费）", "entityDesc": "", "actionType": "1", "effType": "", "effDate": "20221101000000", "expType": "", "expDate": "", "busineesCode": null, "quantity": "1", "scenarioType": "MainOfferChg", "accessType": "601", "channelName": "CRM营业厅", "createDate": "20221027194600", "createProleId": "518220000268345", "operName": "yz20160511", "createDeptId": "23100003", "status": "1"}, {"itemId": "1155019", "entityType": "O", "entityId": "2000001241", "entityCode": "2000001241", "entityName": "1元河北区域话音包", "entityDesc": "套餐费1元/月，在河北省内通话时，国内主叫（不含港澳台）0.5元/分钟", "actionType": "1", "effType": "I", "effDate": "", "expType": "", "expDate": "", "busineesCode": null, "quantity": "1", "scenarioType": "OfferEntry", "accessType": "601", "channelName": "CRM营业厅", "createDate": "20221027194310", "createProleId": "518220000268345", "operName": "yz20160511", "createDeptId": "23100003", "status": "1"}, {"itemId": "1155020", "entityType": "O", "entityId": "2000001264", "entityCode": "2000001264", "entityName": "动感校园行B", "entityDesc": "优惠区域内主叫0.15元/分钟，优惠区域外国内主叫0.3元/分钟，国内被叫免费。", "actionType": "1", "effType": "I", "effDate": "", "expType": "", "expDate": "", "busineesCode": null, "quantity": "1", "scenarioType": "OfferEntry", "accessType": "601", "channelName": "CRM营业厅", "createDate": "20221027194310", "createProleId": "518220000268345", "operName": "yz20160511", "createDeptId": "23100003", "status": "1"}, {"itemId": "1155018", "entityType": "O", "entityId": "2200005006", "entityCode": "2200005006", "entityName": "个人彩铃", "entityDesc": "5元/月", "actionType": "1", "effType": "N", "effDate": "", "expType": "", "expDate": "", "busineesCode": null, "quantity": "1", "scenarioType": "OfferEntry", "accessType": "601", "channelName": "CRM营业厅", "createDate": "20221027194304", "createProleId": "518220000268345", "operName": "yz20160511", "createDeptId": "23100003", "status": "1"}, {"itemId": "1155013", "entityType": "O", "entityId": "4000000081", "entityCode": "8160000003", "entityName": "主体商品变更促销包", "entityDesc": "", "actionType": "1", "effType": "I", "effDate": "", "expType": "", "expDate": "", "busineesCode": null, "quantity": "1", "scenarioType": "OfferPkgEntry", "accessType": "601", "channelName": "CRM营业厅", "createDate": "20221027194231", "createProleId": "518220000268345", "operName": "yz20160511", "createDeptId": "23100003", "status": "1"}, {"itemId": "1155008", "entityType": "O", "entityId": "2400000075", "entityCode": "YX139-BAN-5Y-BAO", "entityName": "139邮箱", "entityDesc": "139邮箱", "actionType": "1", "effType": "", "effDate": "", "expType": "", "expDate": "", "busineesCode": null, "quantity": "1", "scenarioType": "OfferPkgEntry", "accessType": "601", "channelName": "CRM营业厅", "createDate": "20221027194017", "createProleId": "518220000268345", "operName": "yz20160511", "createDeptId": "23100003", "status": "1"}]}}, "h5shoppingCartBusiValidate": {"retCode": "0", "retMsg": null, "data": {"itemFeeList": [{"entityType": "O", "entityId": "209807", "entityName": "移动梦网0", "itemPriceInfoList": [{"itemPriceInfo": {"chargeCode": "test_20181101", "chargeName": "测试_营业费用0", "priceAmount": "100000"}}, {"itemPriceInfo": {"chargeCode": "test_20181102", "chargeName": "测试_营业费用1", "priceAmount": "50000"}}]}, {"entityType": "O", "entityId": "209808", "entityName": "移动梦网2", "itemPriceInfoList": [{"itemPriceInfo": {"chargeCode": "test_20181103", "chargeName": "测试_营业费用3", "priceAmount": "200000"}}, {"itemPriceInfo": {"chargeCode": "test_20181104", "chargeName": "测试_营业费用4", "priceAmount": "1000000"}}]}], "totalPrice": "1350000"}}, "h5delShoppingCartItem": {"retCode": "0", "retMsg": null, "data": null}, "h5qryShoppingCartItemInfo": {"retCode": "0", "retMsg": null, "data": {"retCode": "0", "retDesc": "Success", "itemId": "1192027", "entityType": "O", "entityId": "2400000075", "entityCode": "YX139-BAN-5Y-BAO", "entityName": "139邮箱", "entityDesc": "139邮箱", "actionType": "1", "effType": "", "effDate": "", "expType": "", "expDate": "", "busineesCode": null, "quantity": "1", "scenarioType": "OfferPkgEntry", "accessType": "601", "channelName": "CRM营业厅", "createDate": "20221101201156", "createProleId": "518220000268345", "operName": "yz20160511", "createDeptId": "23100003", "status": "1", "itemRelaList": null, "subItemList": [{"subItem": {"itemId": "1192028", "entityType": "O", "entityId": "2400000003", "entityCode": "2400000003", "entityName": "20元版139邮箱", "entityDesc": "20元版139邮箱", "actionType": "1", "effType": "I", "effDate": "", "expType": "", "expDate": "", "busineesCode": null, "quantity": "1", "scenarioType": "", "accessType": "601", "channelName": "CRM营业厅", "createDate": "20221101201156", "createProleId": "518220000268345", "operName": "yz20160511", "createDeptId": "23100003", "status": "1", "itemRelaList": null, "subItemList": [], "busiInfo": null}}, {"subItem": {"itemId": "1192028", "entityType": "O", "entityId": "2400000003", "entityCode": "2400000003", "entityName": "20元版139邮箱", "entityDesc": "20元版139邮箱", "actionType": "1", "effType": "I", "effDate": "", "expType": "", "expDate": "", "busineesCode": null, "quantity": "1", "scenarioType": "", "accessType": "601", "channelName": "CRM营业厅", "createDate": "20221101201156", "createProleId": "518220000268345", "operName": "yz20160511", "createDeptId": "23100003", "status": "1", "itemRelaList": null, "subItemList": [], "busiInfo": null}}], "busiInfo": {"offerList": [{"offer": {"offerId": "2400000075", "offeringInstId": "", "opCode": "1", "effMode": "", "effDate": "", "expMode": "", "expDate": "", "propList": {"prop": [{"propCode": "Custaffecttype", "propValue": "1", "subAttrList": null}, {"propCode": "PRIMARYPRODUCT", "propValue": "0", "subAttrList": null}, {"propCode": "MemAdNotChkProdValid", "propValue": "1", "subAttrList": null}, {"propCode": "C_O_BUSINESS_CATEGOTY", "propValue": "2", "subAttrList": null}, {"propCode": "PM_PROM_ALLOW_OVERREGION", "propValue": "N", "subAttrList": null}, {"propCode": "C_O_APPLY_ENTITY_TYPE", "propValue": "S", "subAttrList": null}, {"propCode": "C_O_GROUP_FLAG", "propValue": "0", "subAttrList": null}, {"propCode": "C_O_NETWORK_TYPE", "propValue": "1", "subAttrList": null}, {"propCode": "C_O_SELL_OBJECT", "propValue": "I", "subAttrList": null}, {"propCode": "C_O_OFFERING_TYPE", "propValue": "3", "subAttrList": null}, {"propCode": "C_O_BITYPE", "propValue": "", "subAttrList": null}, {"propCode": "ToBOSS", "propValue": "", "subAttrList": null}]}, "subOfferList": [{"subOffer": {"offerId": "2400000003", "offeringInstId": "", "opCode": "1", "effMode": "I", "effDate": "", "propList": {"prop": [{"propCode": "C_O_APPLY_ENTITY_TYPE", "propValue": "S", "subAttrList": null}, {"propCode": "priceValue", "propValue": "20000", "subAttrList": null}, {"propCode": "DOMAIN", "propValue": "CMCC", "subAttrList": null}, {"propCode": "SRPFLAG", "propValue": "0", "subAttrList": null}, {"propCode": "Custaffecttype", "propValue": "1", "subAttrList": null}, {"propCode": "ToBOSS", "propValue": "", "subAttrList": null}, {"propCode": "C_O_GROUP_FLAG", "propValue": "0", "subAttrList": null}, {"propCode": "C_O_NETWORK_TYPE", "propValue": "1", "subAttrList": null}, {"propCode": "PM_PROM_ALLOW_OVERREGION", "propValue": "N", "subAttrList": null}, {"propCode": "C_O_SELL_OBJECT", "propValue": "I", "subAttrList": null}, {"propCode": "C_O_OFFERING_TYPE", "propValue": "3", "subAttrList": null}, {"propCode": "C_O_BITYPE", "propValue": "", "subAttrList": null}, {"propCode": "IsFake", "propValue": "2000", "subAttrList": null}, {"propCode": "PRIMARYPRODUCT", "propValue": "0", "subAttrList": null}, {"propCode": "INTERNETPRODSERVRESET", "propValue": "120000001", "subAttrList": null}, {"propCode": "KNOWLEGEID", "propValue": "10090527161754590980", "subAttrList": null}, {"propCode": "MemAdNotChkProdValid", "propValue": "1", "subAttrList": null}, {"propCode": "C_O_BUSINESS_CATEGOTY", "propValue": "4", "subAttrList": null}, {"propCode": "BUSINESS_DESC", "propValue": "20元版139邮箱的业务介绍", "subAttrList": null}, {"propCode": "C_O_NETWORK_TYPE", "propValue": "1", "subAttrList": null}, {"propCode": "businessType", "propValue": "MBOX", "subAttrList": null}, {"propCode": "ToBOSS", "propValue": "", "subAttrList": null}, {"propCode": "businessPlatform", "propValue": "MBOX", "subAttrList": null}, {"propCode": "SPCODE", "propValue": "910194", "subAttrList": null}, {"propCode": "businessCode", "propValue": "+MAILVIP", "subAttrList": null}]}}}]}}], "promotionList": null}}}}