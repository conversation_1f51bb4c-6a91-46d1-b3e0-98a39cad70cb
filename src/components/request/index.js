// 相关网络请求接口
import axios from '@/base/nlAxios.js'
import { Toast } from 'mint-ui'
import { aesEncryptAllParams } from '@/base/AesEncrptUtil'
import { getRandomStr } from '@/base/utils'
/*
* 提取公共的post请求处理逻辑
* url: 请求地址
* params: 请求参数
* isShow: 是否展示错误提示信息
* message: 错误提示信息
* */
const handleAxiosPost = (url, params, isShow = true, message) => {
  return new Promise((resolve, reject) => {
    axios.post(url, params).then(res => {
      const { retCode, retMsg, data } = res.data
      if (retCode === '0') {
        data ? resolve(data) : resolve(res.data)
      } else {
        showMessage(retMsg || message || '查询统计数据失败', isShow)
        resolve(null)
      }
    }).catch(err => {
      showMessage(err || message || '查询统计数据失败', isShow)
      resolve(null)
    })
  })
}
/*
* 提取公共的post请求处理逻辑
* url: 请求地址
* params: 请求参数
* isShow: 是否展示错误提示信息
* message: 错误提示信息
* */
const handleAxiosPost2 = (url, params, isShow = true, message) => {
  return new Promise((resolve, reject) => {
    axios.post(url, params).then(res => {
      const { retCode, retMsg, data } = res.data
      if (retCode === '0') {
        data ? resolve(data) : resolve(res)
      } else {
        if (retCode === '303') {
          resolve(retCode)
        } else {
          showMessage(retMsg || message || '查询统计数据失败', isShow)
          resolve(null)
        }
      }
    }).catch(err => {
      showMessage(err || message || '查询统计数据失败', isShow)
      resolve(null)
    })
  })
}
/*
* 提取公共的get请求处理逻辑
* url: 请求地址
* isShow: 是否展示错误提示信息
* message: 错误提示信息
* */
const handleAxiosGet = (url, isShow = true, message) => {
  return new Promise((resolve, reject) => {
    axios.get(url).then(res => {
      const { retCode, retMsg, data } = res.data
      if (retCode === '0') {
        resolve(data)
      } else {
        showMessage(retMsg || message || '操作失败', isShow)
        resolve(null)
      }
    }).catch(err => {
      showMessage(err || message || '操作失败', isShow)
      reject(new Error(err))
    })
  })
}
const showMessage = (message, isShow) => {
  if (isShow) {
    if (message.includes('crm工号') || message.includes('CRM工号')) {
      Toast({message:'当前岗位没有CRM工号，请联系地市工号管理员配置', className: 'request-toast'})
    } else {
      Toast({message,className: 'request-toast'})
    }
  }
}
/*公共的fetch请求*/
const fetchApi = async (url, params, uinfo, method = 'POST') => {
  let data = aesEncryptAllParams(params)
  let data2 = JSON.stringify(params) // post请求测试数据
  let data3 = new URLSearchParams(params).toString()//get请求测试数据
  const requestOptions = {
    method: method,
    headers: {
      'tokenid': uinfo.tokenid,
      'aldregion': uinfo.region + '_' + uinfo.servNumber,
      'operaterPhone': uinfo.servNumber,
      'imei': uinfo.imei,
      'Content-Type': 'application/json',
      'c03be90046a7e7f': 'GwRT4HjrxC9Davw',
      'Accept': 'text/event-stream'
    }
    // redirect: 'follow'
    // credentials: 'include' // 表示允许携带跨域的认证信息，根据实际情况调整
  }
  if (method === 'POST') {
    requestOptions['body'] = data
  } else {
    url = `${url}?${data3}`
  }
  return await fetch(url, requestOptions)
}
/*获取tokenn*/
export const h5genToken = (opId, prviId, uinfo) => {
  let client = /android|harmony/gi.test(navigator.userAgent) ? 'ANDROID' : 'IOS'
  let url = `/xsb/api-user/tokenAuthen/h5genToken.do?opId=${opId}&prviId=${prviId}&clientType=${client}`
  url += `&regionId=${uinfo.region}&operId=${uinfo.staffId}&stationId=${uinfo.stationId}&phoneNumber=${uinfo.servNumber}`
  return handleAxiosGet(url)
}
/**************************************************通用接口***************************************************/
/*ai相关接口*/
export const h5UnifiedCapability = (data) => {
  let url = `/xsb/chatTools/agent/h5UnifiedCapability`
  return handleAxiosPost(url, data)
}
/*ai相关接口SSE*/
export const h5UnifiedCapabilitySse = (param, uinfo) => {
  // 接口地址 /xsb/chatTools/agent/h5RoamPackageAgent?rdom=${getRandomStr()}
  const url = `/xsb/sse/chatTools/agent/h5UnifiedCapabilitySse?rdom=${getRandomStr()}`
  return fetchApi(url, param, uinfo)
}
/**************************************************通用接口***************************************************/
/*
*获取政企融合视图菜单列
* */
export const h5getMenuByLevel = (stationId, leave, isShow = false) => {
  let url = `/xsb/api-user/menu/h5getMenuByLevel?level=${leave}:0&stationId=${stationId}`
  return handleAxiosGet(url, isShow)
}
/*
*获取政企融合视图菜单列
* */
export const h5getMenuByLevelAllLevel = (stationId, allLevel, isShow = false) => {
    let url = `/xsb/api-user/menu/h5getMenuByLevel?level=${allLevel}&stationId=${stationId}`
    return handleAxiosGet(url, isShow)
}
/*
*常用功能和公告信息
* */
export const h5DeskOtherInfo = (stationId, area) => {
  let url = '/xsb/api-user/desk/h5DeskOtherInfo?unLoadFlg=true&stationId=' + stationId + '&area=' + area
  return handleAxiosGet(url)
}
/*
*查询操作人员归属信息
* */
export const h5QryScoreAndRank = (servNumber, message = '查询当前网格基本信息接口调用出错') => {
  let url = `/xsb/personBusiness/gridViewSecond/h5QryScoreAndRank?telnum=${servNumber}`
  return handleAxiosGet(url, true, message)
}
/*
*查询区域层级
* */
export const h5QryAreak = (orgId, message = `当前人员未查询到网格信息，请到渠道运营平台配置`) => {
  const url = `/xsb/personBusiness/gridViewSecond/h5QryAreak?orgaid=${orgId}`
  return handleAxiosGet(url, message)
}
/*
* 查询区域列表
* */
export const h5AreaListQuery = (params, message) => {
  let url = `/xsb/gridCenter/gridMap/h5AreaListQuery`
  return handleAxiosPost(url, params, message)
}
/*
*转派提交
* */
export const h5TransferSubmit = (param, message) => {
  let url = '/xsb/gridCenter/gridHomework/h5TransferSubmit'
  return handleAxiosPost(url, param, message)
}

/*
*查询沿街商铺指标信息
* */
export const h5gridMapShopTotalInfo = (param, message) => {
  const url = '/xsb/gridCenter/gridMap/h5gridMapShopTotalInfo'
  return handleAxiosPost(url, param, message)
}
/*
*查询酒店指标信息
* */
export const h5hotelIndicators = ({ orgId, crmId }, message) => {
  let url = '/xsb/gridCenter/gridMap/h5hotelIndicators?orgaId=' + orgId + '&crmId=' + crmId
  return handleAxiosGet(url, message)
}
/*
*查询楼宇和园区指标信息
* */
export const h5buildingIndex = ({ orgId, crmId }, message) => {
  const url = '/xsb/gridCenter/gridMap/h5buildingIndex?orgaId=' + orgId + '&crmId=' + crmId
  return handleAxiosGet(url, message)
}
/*
*查询网格长信息
* */
export const h5GridStaffListPublicQuery = (param, message) => {
  let url = `/xsb/gridCenter/gridHomework/h5GridStaffListPublicQuery`
  return handleAxiosPost(url, param, message)
}
/*
*查询网格信息
* */
export const h5digitalAssistant = (param, message) => {
  let url = `/xsb/gridCenter/groupMarket/h5digitalAssistant`
  return handleAxiosPost(url, param, message)
}


/*
* 获取摸排任务列表
* */
export const h5GridTabCustMgrTaskToDoQuery = (param, message) => {
  const url = `/xsb/gridCenter/gridMap/h5GridTabCustMgrTaskToDoQuery`
  return handleAxiosPost(url, param, message)
}
/*
* 查询网格长视图信息
* */
export const h5GridTabGridViewQuery = (param, message) => {
  const url = `/xsb/gridCenter/gridMap/h5GridTabGridViewQuery`
  return handleAxiosPost(url, param, message)
}
/**************************************************分割线*************************************************************/
// 查询当前有没有签到过
export const h5GridTodayUnSignOutQuery = (params) => {
  let url = '/xsb/gridCenter/gridMap/h5GridTodayUnSignOutQuery'
  return handleAxiosPost(url, params)
}
// 签到提交
export const h5GridSignInSubmit = (params) => {
  let url = '/xsb/gridCenter/gridMap/h5GridSignInSubmit'
  return handleAxiosPost(url, params)
}
// 签出提交
export const h5GridSignOutSubmit = (params) => {
  let url = '/xsb/gridCenter/gridMap/h5GridSignOutSubmit'
  return handleAxiosPost(url, params)
}
/**************************************************分割线*************************************************************/
/*国漫套餐推荐回复*/
export const h5RoamPackageAgent = (param, uinfo) => {
  // 接口地址 /xsb/chatTools/agent/h5RoamPackageAgent?rdom=${getRandomStr()}
  const url = `/xsb/sse/chatTools/agent/h5RoamPackageAgent?rdom=${getRandomStr()}`
  return fetchApi(url, param, uinfo)
}
/*获取ai推荐菜单列表*/
export const h5queryTopAgentInfo = (param) => {
  const url = '/xsb/chatTools/agent/h5queryTopAgentInfo'
  return handleAxiosPost(url, param)
}
/**/
export const h5agentKeyConvert = (tokenStr) => {
  let url = `/xsb/chatTools/agent/h5agentKeyConvert?tokenStr=${tokenStr}`
  return handleAxiosGet(url)
}
/*记录AI使用次数*/
export const h5AgentLogRecord = (data) => {
  let url = `/xsb/chatTools/agent/h5AgentLogRecord`
  return handleAxiosPost(url, data, false)
}
/**************************************************风险清单分割线*********************************************************/
/*省公司部门人员合规风险清单查询接口*/
export const h5ComplianceInfoQuery = (data) => {
  let url = `/xsb/gridCenter/userHome/h5ComplianceInfoQuery`
  return handleAxiosPost(url, data)
}
/*省公司部门人员阅知结果通知接口*/
export const h5ComplianceInfoSync = (data) => {
  let url = `/xsb/gridCenter/userHome/h5ComplianceInfoSync`
  return handleAxiosPost(url, data)
}
/**************************************************客户画像分析分割线******************************************************/
/*异动用户业务信息查询接口*/
export const h5CustMaintainBusiInfoQuey = (data) => {
  let url = `/xsb/gridCenter/warningOrder/h5CustMaintainBusiInfoQuey`
  return handleAxiosPost(url, data, false)
}
/*客户洞察*/
export const h5AiPackageInsight = (param, uinfo) => {
  const url = `/xsb/sse/chatTools/agent/h5AiPackageInsight?rdom=${getRandomStr()}`
  return fetchApi(url, param, uinfo)
}
/*客户洞察2.0*/
export const h5AiPackageInsightNew = (param, uinfo) => {
  const url = `/xsb/sse/chatTools/agent/h5AiPackageInsightNew?rdom=${getRandomStr()}`
  return fetchApi(url, param, uinfo)
}
/*客户三看(查流量通话)*/
export const h5queryLabelByTel = (telnum) => {
  let url = '/xsb/personBusiness/recordlogbusiaction/h5queryLabelByTel?telnum=' + telnum
  return handleAxiosGet(url, false)
}
/*查宽带和电视*/
export const h5alreadyOpened = (telnum) => {
  let url = '/xsb/personBusiness/customerView/h5alreadyOpened?telnum=' + telnum
  return handleAxiosGet(url, false)
}
/**************************************************户型图分割线******************************************************/
/*登录接口*/
export const h5GetSessionId = (data) => {
  let url = `/xsb/chatTools/wifiCover/h5GetSessionId`
  return handleAxiosPost(url, data)
}
/*获取城市列表接口*/
export const h5QueryCity = (data) => {
  let url = `/xsb/chatTools/wifiCover/h5QueryCity`
  return handleAxiosPost(url, data)
}

/*获取WIFI热力图接口*/
export const h5QueryWifiCover = (data) => {
  let url = `/xsb/chatTools/wifiCover/h5QueryWifiCover`
  return handleAxiosPost2(url, data)
}
/*搜索户型图接口*/
export const h5QueryFloorPlan = (data) => {
  let url = `/xsb/chatTools/wifiCover/h5QueryFloorPlan`
  return handleAxiosPost2(url, data)
}
/*刷新sessionId*/
export const h5UpdateSessionId = (data) => {
  let url = `/xsb/chatTools/wifiCover/h5UpdateSessionId`
  return handleAxiosPost(url, data)
}
/**************************************************浮层菜单点击量分割线****************************************************/
/*查询菜单统计数据
* operMsisdn：手机号码
* */
export const h5QueryMenuRecordCount = (data) => {
  let url = `/xsb/chatTools/menuRecord/h5QueryMenuRecordCount`
  return handleAxiosPost(url, data, false)
}
/*保存菜单点击记录
* operMsisdn：手机号码
* menuId
* menuName
* menuType 菜单类型 1:菜单 2.智能体 3.词条 4.外围菜单
* region
* */
export const h5AddMenuRecord = (data) => {
  let url = `/xsb/chatTools/menuRecord/h5AddMenuRecord`
  return handleAxiosPost(url, data)
}
/*更新菜单统计数据
* operMsisdn：手机号码
* menuCountList
*   recordId 记录ID
*   operMsisdn：手机号码
*   menuId
*   menuName
*   menuType 菜单类型 1:菜单 2.智能体 3.词条 4.外围菜单
*   menuRoute 菜单类型
*   clickCount  点击次数
* */
export const h5UpdateMenuCount = (data) => {
  let url = `/xsb/chatTools/menuRecord/h5UpdateMenuCount`
  return handleAxiosPost(url, data)
}
/**************************************************换绑集团分割线****************************************************/
export const h5BuildingCompanyAction = (data) => {
  let url = `/xsb/personBusiness/buildingView/h5BuildingCompanyAction`
  return handleAxiosPost(url, data)
}
/**************************************************菜单反馈分割线****************************************************/
/*菜单反馈
* operMsisdn：手机号码
* regionId：地市
* keyWord：对话内容
* menuList;
*   menuId
*   menuName
*   menuType 菜单类型 1:菜单 2.智能体 3.词条 4.外围菜单
*   feedbackType 反馈类型0:踩1:赞
* */
export const h5MenuSearchFeedBack = (data) => {
  let url = `/xsb/chatTools/menuRecord/h5MenuSearchFeedBack`
  return handleAxiosPost(url, data,true,'反馈失败！')
}
