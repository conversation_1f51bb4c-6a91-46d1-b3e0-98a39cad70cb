<template>
  <div class="wrapper">正在拉起....
    <br>
    <br>
    <br>
    <br>
    <br>
    <button @click="goBackWork()">点击返回</button>
  </div>
</template>

<script>
import ClientJs from '@/base/clientjs'
import Storage from '@/base/storage'

let openFlg = false;//记录从这个页面拉起外围页面的flag标识
export default {
  components: {},
  data() {
    return {
        //生产地址
        subWgtPro : Storage.get('webUrl')+'/xsbh5/subWgt/index.html'//子项目地址


    };
  },
  computed: {},
  methods: {
    goBackWork(){
      window.history.go(-1);
    }
  },
  created() {
    if(process.env.NODE_ENV === 'development'){
      this.subWgtPro = 'http://localhost:5173/child/wgt/'
      // this.subWgtPro = 'http://***************:8080/xsbh5/subWgt/index.html'
    }
    let routeQry = this.$route.query;//获取ald_priv_url表里配置的菜单对应的子项目里的参数对象
    let path = routeQry.path;//子项目的路由地址

    let proUrl = this.subWgtPro;
    if(path){
        proUrl += '#/'+path;//拼接子项目的路由地址
    }
    let fullPath = this.$route.fullPath;//获取全路径的参数字符串
    let subQuery = '';//子项目的路由参数
    let qIndex = fullPath.indexOf('?');
    if(~qIndex){//截取路径中的参数
      subQuery = fullPath.substr(qIndex);//取？后面的参数,连？一起
    }
    proUrl += subQuery;
		// 前端切换岗位时并不会更新客户端的岗位信息和Crmid信息，新页面拉起的时候取不到userInfo信息，还是取客户端拉起时候的信息，故拼接传递
	  let userInfo = Storage.session.get('userInfo');
	  if (userInfo && userInfo.crmId && userInfo.stationId) {
		  let paramSeparator = proUrl.indexOf('?')!== -1? '&' : '?';
		  proUrl += `${paramSeparator}crmId=${userInfo.crmId}&stationId=${userInfo.stationId}`;
	  }
	  // if (process.env.NODE_ENV === 'development') {
		  // window.location.href = proUrl
      // ClientJs.openWebKitNew(proUrl, {})
	  // } else {
    ClientJs.openWebKitNew(proUrl, {})
	  // }
    console.info('拉起subWgt页面')
    openFlg = true;
    // this.opegjinWebKitWithStatus(proUrl,{},'ALD拉起王子应用')
  },
  mounted() {
    //网格通子应用返回主项目时，使用客户端方法 closeWebKitWithCallBack
    window['closeWgtWebkitCb'] = (res)=>{
      // console.info('----in closeWgtWebkitcb')
        openFlg = false;
        //同时回到上层父页面（菜单、工具、业务等）
        window.history.go(-1);
    };
    document.addEventListener("visibilitychange", function () {
      let vState = document.visibilityState
      // console.info('---yan-vState',vState)
        if (vState === 'hidden') {  // 当页面由前端运行在后端时，出发此代码
            console.log('我被隐藏了')
        }
        if (vState === 'visible' && openFlg == true) {   // 当页面由隐藏至显示时
          window.history.go(-1);
        }
    });
  },
}
</script>
<style lang='less' scoped>
//@import url(); 引入公共css类

</style>
