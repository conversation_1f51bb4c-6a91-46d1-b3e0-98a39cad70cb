<!--tab-->
<template>
  <div class="flex text-sm" :class="{
    'line-div margin-top':tabType==='line',
    'card-div':tabType==='card',
  }">
    <p class="margin-right-sm padding-bottom-xs" :style="value===item.value?propStyle:{}" v-for="(item,index) in list" :key="index"
       @click="tabChange(item.value)"
       :class="{'active':value===item.value}">{{ item.text }}
    </p>
  </div>
</template>

<script>
export default {
  name: 'tabCom',
  props: {
    value: {
      type: [String, Number]
    },
    // tab列表
    list: {
      type: Object,
      default: () => [
        { text: '积分', value: 1 },
        { text: '薪酬', value: 2 }
      ]
    },
    tabType: {
      type: String,
      default: 'line'
    },
    propStyle: {}
  },
  data() {
    return {
      // switchValue: 1
    }
  },
  methods: {
    tabChange(e) {
      if (e === this.value) return
      this.$emit('input', e)
    }
  }
}
</script>

<style scoped lang="less">
@import "../../base/less/public";

.line-div {
  p {
    &.active {
      border-bottom: 3px solid #1681fb;
      color: #1681fb;
      font-weight: bold;
    }
  }
}

.card-div {
  p {
    padding: 8px 10px;
    background: #F1F1F1;

    &.active {
      background: #1D6CFF;
      color: white;
      font-weight: bold;
    }
  }
}
</style>
