<template>
  <div class="loading-message-box-wrapper" v-if="visible">
    <div class="loading-message-box-mask" @click="handleMaskClick"></div>
    <div class="loading-message-box">
      <div class="loading-message-box-header" v-if="title">
        <div class="loading-message-box-title">{{ title }}</div>
      </div>
      <div class="loading-message-box-content">
        <div class="loading-message-box-message" v-html="message"></div>
      </div>
      <div class="loading-message-box-btns">
        <button
          v-if="showCancelButton"
          class="loading-message-box-btn loading-message-box-cancel"
          @click="handleAction('cancel')"
          :disabled="loading">
          {{ cancelButtonText }}
        </button>
        <button
          v-if="showConfirmButton"
          class="loading-message-box-btn loading-message-box-confirm"
          @click="handleAction('confirm')"
          :disabled="loading">
          <span v-if="loading" class="loading-spinner"></span>
          {{ loading ? loadingText : confirmButtonText }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LoadingMessageBox',
  props: {
    title: {
      type: String,
      default: '温馨提示'
    },
    message: {
      type: String,
      default: ''
    },
    showConfirmButton: {
      type: Boolean,
      default: true
    },
    showCancelButton: {
      type: Boolean,
      default: false
    },
    confirmButtonText: {
      type: String,
      default: '确定'
    },
    cancelButtonText: {
      type: String,
      default: '取消'
    },
    loadingText: {
      type: String,
      default: '处理中...'
    },
    closeOnClickModal: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      visible: false,
      loading: false,
      debounceTimer: null
    }
  },
  methods: {
    show() {
      this.visible = true;
      this.loading = false;
      return this;
    },
    hide() {
      this.visible = false;
      this.loading = false;
      this.clearDebounceTimer();
    },
    setLoading(loading) {
      this.loading = loading;
    },
    handleMaskClick() {
      if (this.closeOnClickModal && !this.loading) {
        this.handleAction('cancel');
      }
    },
    handleAction(action) {
      // 防抖处理
      if (this.debounceTimer) {
        return;
      }

      if (action === 'confirm' && this.loading) {
        return; // 如果正在加载中，不允许再次点击确认按钮
      }

      // 设置防抖定时器
      this.debounceTimer = setTimeout(() => {
        this.debounceTimer = null;
      }, 500);

      this.$emit('action', action);
    },
    clearDebounceTimer() {
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer);
        this.debounceTimer = null;
      }
    }
  },
  beforeDestroy() {
    this.clearDebounceTimer();
  }
}
</script>

<style scoped>
.loading-message-box-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-message-box-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.loading-message-box {
  position: relative;
  background: white;
  border-radius: 8px;
  width: 320px;
  min-height: 135px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  animation: messageBoxFadeIn 0.3s ease-out;
  transform: translateY(-120px);
}

@keyframes messageBoxFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-140px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(-120px);
  }
}

.loading-message-box-header {
  padding: 20px 20px 15px;
  text-align: center;
}

.loading-message-box-title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
}

.loading-message-box-content {
  padding: 0 20px 20px;
  text-align: center;
}

.loading-message-box-message {
  font-size: 16px;
  color: #333;
  line-height: 1.5;
}

.loading-message-box-btns {
  display: flex;
  border-top: 1px solid #ddd;
}

.loading-message-box-btn {
  flex: 1;
  height: 50px;
  border: none;
  background: none;
  font-size: 17px;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  font-weight: normal;
}

.loading-message-box-btn:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.loading-message-box-cancel {
  color: #888;
  border-right: 1px solid #ddd;
}

.loading-message-box-cancel:hover:not(:disabled) {
  background-color: #f8f8f8;
}

.loading-message-box-confirm {
  color: #007AFF;
  font-weight: normal;
}

.loading-message-box-confirm:hover:not(:disabled) {
  background-color: #f0f8ff;
}

.loading-spinner {
  width: 14px;
  height: 14px;
  border: 2px solid #e0e0e0;
  border-top: 2px solid #007AFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
