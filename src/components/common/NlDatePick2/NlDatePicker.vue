<template>
  <div v-show="value"  @click="cancle()">
      <h4 class="choose-title-wrap wrapper-medias">
        <span class="choose-btn-cancel" @click.stop="cancle()">取消</span>
        <span class="choose-component-title">{{title}}</span>
        <span class="choose-btn-confirm" @click.stop="confirm()">确定</span>
      </h4>
      <div class="ipt-box-wrap" v-if="!onlyOne">
        <input type="text" :class="{'focus-clz':focusOne=='start'}" ref="startIpt" readonly
              @click.stop="focusStart" v-model="startDate"/>
        <span class="mid-span">至</span>
        <input type="text"
              :class="{'focus-clz':focusOne=='end','error':ifLess}" ref="endIpt"
              @click.stop="focusEnd"
              readonly
              v-model="endDate"
              placeholder="结束时间"/>
      </div>

      <mt-datetime-picker
            :type="dateType"
            ref="startDate"
            :yearFormat='yearFormat'
            :monthFormat='monthFormat'
            :dateFormat='dateFormat'
            :startDate="tsMinDate"
            :endDate="tsMaxDate"
            v-model="dateval"
            @input="change">
      </mt-datetime-picker>
  </div>
</template>

<script>
import {dateFormat} from '@/base/utils'
export default {
  props:{
    value: {
      type: Boolean,
      default: false
    },
      title: {
          type: String,
          default: '请选择日期'
      }  ,
    onlyOne:false,
    dateType:'',//日期类型 'datetime', 'date', 'time'
    format:'',//日期格式
    tsStartDate:'',//开始日期
    tsEndDate:'',//结束日期
    focusWhich:'',//聚集在哪个输入框
    tsMinDate:{
      type: Date,
      default() {
        return new Date(new Date().getFullYear(), 0, 1);
      }
    },//最小时间  默认为10年前
    tsMaxDate:{
        type: Date,
        default() {
            return new Date(new Date().getFullYear()+ 30, 0, 1);
        }
    } // 最大时间, 默认30年后
  },
  data(){
    return {
      dateval:'',
      focusOne:'start',//当前聚焦的输入框
      startDate:'',
      endDate:'',
      yearFormat:'{value}年',
      monthFormat:'{value}月',
      dateFormat:'{value}日'
    }
  },
  computed:{
    ifLess(){
      if(this.onlyOne){
        return false;
      }
      let endDate = this.endDate;
      //结束日期不得小于开始日期
      if(endDate && endDate < this.startDate){
        return true;
      }
      return false;
    }
  },
  watch:{
    value(val){
      if(val){
        this.$refs.startDate.open();
        this.startDate = this.tsStartDate;
        this.endDate = this.tsEndDate;
        this.dateval = this.startDate;
        this.focusOne = this.focusWhich;
        if(this.focusWhich == 'end'){
          this.dateval = this.endDate || this.startDate;
        }
        if(this.dateType === 'datetime'){//如果是日期加时间同时展示就不展示“年”、“月”、“日”
          this.yearFormat = '{value}';
          this.monthFormat = '{value}';
          this.dateFormat = '{value}';
          //this.dateval = new Date(this.startDate);
        }
      }
    }
  },
  methods:{
    cancle() {
      this.focusOne = 'start';
      this.close();
    },
    //点击开始时间
    focusStart(){
      this.focusOne = 'start';
      this.$refs.startIpt.focus();
      this.dateval = this.startDate;
    },
    //结束开始时间
    focusEnd(){
      this.focusOne = 'end';
      this.$refs.endIpt.focus();
      this.dateval = this.endDate || this.startDate;//结束日期
    },
    //点击确认事件
    confirm() {
      if(!this.onlyOne && this.endDate < this.startDate && !!this.endDate){//结束日期可以为空
        this.$toast('结束日期不得小于开始日期')
      } else {
        this.callback({
          startDate: this.startDate,
          endDate: this.endDate
        });
        this.focusOne = 'start';
        this.close();
      }
    },
    change(val){
      let newD = val;
      if(this.dateType != 'time'){//如果是时分 不需要格式化
        newD = dateFormat(val,this.format);
      }
      if(this.focusOne == 'start' || this.onlyOne){
        this.startDate = newD;
      } else {
        this.endDate = newD;
      }
    }
  },
  created(){},
  mounted() {
    this.dateval = this.startDate;
  },
}
</script>
<style lang="less" scoped>

.choose-title-wrap {
    height: 60px;
    display: flex;
    justify-content: space-between;
    padding: 0 16px;
    box-sizing: border-box;
    align-items: center;
    -webkit-align-items: center;
    flex-shrink: 0;
    z-index: 3000;
    -webkit-flex-shrink: 0;
    position:absolute;
    left:0;
    right:0;
    bottom:252px;
    background: #fff;
    box-shadow:0px 0px 0px 0px rgba(221,221,221,1);
    transition: .2s ease-out;
    border-radius: 20px 20px 0 0 ;

}
/deep/ .mint-popup.mint-datetime.mint-popup-bottom {
    border-radius: 20px 20px 0 0 ;
    overflow: hidden;
}
.ipt-box-wrap{
    position:absolute;
    left:0;
    right:0;
    height: 40px;
    z-index: 3000;
    bottom:212px;
    background: #fff;
    display: flex;
    padding:0 2rem;
    .mid-span{
        flex:0 0 20%;
        text-align:center;
        line-height: 40px;
    }
    input{
        border:none;
        flex:1 0 30vw;
        width: 30vw;
        text-align: center;
        border-bottom:1px solid #C1C1C1;
        &:focus{
            border-color:#007AFF;
        }
    }
    .focus-clz{
        border-color:#007AFF;
    }
    .error{
        border-color:red!important;
    }
}
.choose-component-title {
    font-size: 18px;
    font-weight:500;
    color: #000;
}

.choose-btn-confirm {
    font-size: 16px;
    color: #108EE9;
    text-align: right;
}

.choose-btn-cancel {
    font-size: 16px;
    color: #A8A7A7;
}
.top-gray-bg {
    z-index:2001;
    position: fixed;
    left: 0;
    top: 0;
    bottom:293px;
    width: 100%;
    opacity: 0.5;
    background: #000;
}

/deep/ .picker-selected {
    color: #0b7fff;
}

</style>
<style>
.picker-toolbar .mint-datetime-action{
    display: none !important;
}
</style>
