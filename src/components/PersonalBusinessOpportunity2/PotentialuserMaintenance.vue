<template>
    <div class='qz-wrap'>
        <Header tsTitleTxt="商机信息录入" backType="custom" v-show='editType == "add"'  @emGoPrev="goPrev"/>
        <Header tsTitleTxt="商机信息查看" backType="custom" v-show='editType == "view"'  @emGoPrev="goPrev"/>
        <Header tsTitleTxt="商机信息补充" backType="custom" v-show='editType == "supplement"'  @emGoPrev="goPrev"/>
        <div class='content'>


            <div class='content-item'>
                <div class='first-title'>
                    <div class='left-info'>
                        <img src='../../assets/img/sjlr3.png' />
                        基础信息
                    </div>
                </div>
                <div class='more-line'>
                    <div class='fadd-info'>
                        <ul class='family-add'>
                            <li class='add-item add-item-tel' :class='canTel? "add-itemtel" : ""'>
                                <span class="add-left">用户号码<span style='color: red'>*</span></span>
                                <input type="tel"  v-show='!routeBuopId'  :maxlength="11" v-model="telNum" @input="inputNum" placeholder="请输入" class="add-input"  :style="telNum && telNum.length > 10 ?  add == 1? 'padding-right: 0px' : 'padding-right: 18px' : ''"/>
                                <span class='phone1' :style="telNum && telNum.length > 10 ?  add == 1? 'padding-right: 0' : 'padding-right: 0px' : ''" v-show=' routeBuopId'>{{maskphone(telNum)}}</span>
                                <span class='mini' v-show='telNum && telNum.length == 11'> <a class='dianhuaa'  v-show='+canTel' :href="'tel:'+telNum"><span class='iconfont dianhua'></span></a></span>
                                <span class='tip' v-show=' mItem.oregionId == 1 && telNum && telNum.length >= 11 && cntsub && !hasNoOver'>存量号码，请重新输入</span>
                                <span class='tip' v-show=' telNum.length >= 11 && hasNoOver'>{{chongfu}}</span>
                                <span class='tip' v-show=' telNum.length >= 11 && !isPhone'>请输入正确的手机号</span>
                            </li>

                            <li class='add-item'>
                                <span class='add-left'>用户姓名<span style='color: red'>*</span></span>
                                <input v-model='busiUserName' :disabled='!showbtn' placeholder='可仅输入姓' class='add-input' maxlength='10' />
                            </li>

                            <li class='add-item'>
                                <span class='add-left'>归属地市<span style='color: red'>*</span></span>
                                <span class='add-right' @click='(showbtn && changeCounty0())'>{{ belongRegion.label ? belongRegion.label : '请选择'}}<i v-show='showbtn != 0' v-if='!(showbtn && add != 1 && buchong != 1 && belongRegion.label)' class='iconfont jiantou-copy-copy'></i></span>
                            </li>
                            <li class='add-item'>
                                <span class='add-left'>区县乡镇<span style='color: red'>*</span></span>
                                <span class='add-right' @click='(showbtn && changeCounty())'>{{ custName.townName ? custName.townName : '请选择'}}<i v-show='showbtn != 0' v-if='!(showbtn && add != 1 && buchong != 1 && custName.townName)' class='iconfont jiantou-copy-copy'></i></span>
                            </li>
                            <li class='add-item'>
                                <span class='add-left'>详细地址<span v-show='showbtn'  class='small-font' >(建议填写单位集团和小区信息)</span></span>
                                <input v-model='villageName' placeholder='请输入' :disabled='!showbtn' class='add-input' maxlength='50' />
                            </li>
                            <li class='add-item' v-show='!cntsub && showbtn && showSms() && editObject.messageVerify != "true" && showbtn'>
                                <span class='add-left'>短信验证码</span>
                                <div>
                                    <input v-model='yzmCode' style='max-width: 144px;' placeholder='输入接收到的验证码' class='add-input' maxlength='4' />
                                    <div class='add-right2'>
                                        <span class='shibie' :class='timer? "grey-btn" : "" ' :disabled='countdown > 0' @click='startCountdown()'>  {{ countdown > 0 ? `${countdown} 秒后重试` : '发送验证码'}}</span>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class='content-item'>
                <div class='first-title'>
                    <div class='left-info'>
                        <img src='../../assets/img/sjlr3.png' />
                        其他信息
                    </div>
                    <div class='right-info' @click='showMoreContent = !showMoreContent'>
                        <span class='iconfont jiantou2' v-show='showMoreContent'></span>
                        <span class='iconfont jiantouxiangshang' v-show='!showMoreContent'></span>
                    </div>
                </div>
                <div class='more-line' v-show='!showMoreContent'>
                    <div class='fadd-info'>
                        <ul class='family-add'>
                            <li class='add-item'>
                                <span class='add-left'>用户需求描述<span v-show='showbtn' class='small-font2' >(建议填写)</span></span>
                                <div>
                                    <div class='add-right2' v-show='showbtn'>
                                        <span @touchstart='handlerTouchstart' @touchend='handlerTouchend'
                                              class='iconfont luyin'>语音录入</span>
                                    </div>
                                </div>
                            </li>
                            <div class='reserve3-box'>
                                <div v-show='showbtn != 0'>
                                    <textarea v-model='reserve3' :disabled='!showbtn' maxlength='500' placeholder='例:用户是xx集团用户，想办移动吉祥号码，月消费100元左右，需要宽带...' class='pop-textarea' />
                                    <span class='num'> {{ reserve3.length }}/500</span>
                                </div>
                                <div v-show='showbtn == 0 && reserve3'>
                                    <span class='info'> {{ reserve3 }}</span>
                                </div>
                            </div>




                            <div class='box-img'>
                                <div class="fadd-info" style='padding: 0'>
                                    <ul class="family-add">
                                        <li class="add-item" >
                                            <span class="add-left">掌厅图片<span class='iconfont wenhao1' @click='showinfo = !showinfo'></span></span>
                                            <div class='dialog-info' v-show='showinfo' @click='showinfo = !showinfo'>
                                                <div>请上传含以下信息的用户掌厅图片，系统将进行AI识别并自动填充到相关字段中，最多3张图片</div>
                                                <div>1、含用户号码、用户姓氏信息的掌厅图片</div>
                                                <div>2、含用户宽带地址信息的掌厅图片</div>
                                                <div>3、含上月账单的应收费用的掌厅图片</div>
                                            </div>
                                            <span v-show='showbtn' @click='showbtn && getvideo()' class="add-input iconfont xiangji hasvideo" />
                                        </li>
                                        <div class='img-box' v-for='(item,idx) in thebase64' v-show='item && item.indexOf("blob") < 0'>
                                            <span v-show='showbtn' class='iconfont guanbi2' @click='clearimg(idx)'></span>
                                            <img :src="'data:image/jpeg;base64,'+ item" @click='openImg(item)'/>

                                        </div>
                                        <div class='img-box' v-for='(item,idx) in thebase64' v-show='item && item.indexOf("blob") >= 0'>
                                            <span v-show='showbtn' class='iconfont guanbi2' @click='clearimg(idx)'></span>
                                            <img :src="item" @click='openImg(item)'/>
                                        </div>
                                        <div class='big-img' v-if='showIMG'>
                                            <span class='iconfont guanbi2' @click='showIMG = null'></span>
                                            <img  v-if='showIMG && showIMG.indexOf("blob") < 0' :src="'data:image/jpeg;base64,'+ showIMG" />
                                            <img  v-if='showIMG && showIMG.indexOf("blob") >= 0' :src="showIMG" />
                                        </div>
                                    </ul>

                                </div>
                            </div>
                            <div style='clear: both'></div>

                            <li class="add-item">
                                <span class="add-left">宽带安装地址</span>
                                <input v-model="bandInstall" placeholder="请输入" :disabled='!showbtn' class="add-input" maxlength="50"/>
                            </li>
                            <li class="add-item">
                                <span class="add-left">月消费</span>
                                <input v-model="monthConsum" type='number' placeholder="请输入" :disabled='!showbtn' class="add-input" maxlength="20"/>
                            </li>

                            <li class='add-item'>
                                <span class='add-left'>身份证号</span>
                                <div>
                                    <input v-model='busiCertId' @input='inputSfz' style='width: 144px'
                                           placeholder='请输入' :disabled='!showbtn' class='add-input' maxlength='18' />
                                    <div class='add-right2' v-show='showbtn != 0'>
                                        <span class='shibie' @click='sfzOcrFun()'>OCR识别 <i
                                            class='iconfont zu'></i></span>
                                    </div>
                                </div>
                            </li>
                            <li class='add-item'>
                                <span class='add-left'>客户来源</span>
                                <span class='add-right' @click=" showbtn &&changeLY('2')">{{ laiyuan ? laiyuan : '请选择'
                                    }}<i class='iconfont jiantou-copy-copy' v-show='showbtn != 0'></i></span>
                            </li>
                            <li class='add-item'>
                                <span class='add-left'>是否有合约捆绑</span>
                                <span class='add-right' v-show='!showbtn'>{{ isContBund ? '是' : '否' }}</span>
                                <mt-switch v-show='showbtn' v-model='isContBund' :disabled='!showbtn'></mt-switch>
                            </li>
                            <li class='add-item'>
                                <span class='add-left'>合约到期时间</span>
                                <span class='add-right'
                                      @click=" showbtn &&changeTime('2')">{{ busiExpireDate ? busiExpireDate : '请选择'
                                    }}<i v-show='showbtn != 0' class='iconfont jiantou-copy-copy'></i></span>
                            </li>
                            <li class="add-item" >
                                <span class="add-left">预约服务时间</span>
                                <span class="add-right" @click=" showbtn &&changeTime2()">{{ serviceDate?serviceDate: '请选择' }}<i v-show='showbtn != 0' class="iconfont jiantou-copy-copy"></i></span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class='content-item'
                 v-show='isDperson.indexOf("是")>-1 ||provNewIcCnt.indexOf("是")>-1 || lowConsume.indexOf("是")>-1 || suanzhanglist.length > 0 || huawuList.length > 0 || sjlist.length > 0'>
                <div class='first-title'>
                    <div class='left-info'>
                        <img src='../../assets/img/sjlr3.png' />
                        系统匹配信息
                    </div>
                    <div class='right-info'></div>
                </div>
                <div class='more-line'>
                    <div class='fadd-info'>
                        <div class='user-tag'>
                            <li class='add-item'
                                v-show='isDperson.indexOf("是")>-1 ||provNewIcCnt.indexOf("是")>-1 || lowConsume.indexOf("是")>-1'>
                                <span class='add-left'>用户标签信息</span>
                                <div class='tasg'>
                                    <div class='tag-item' v-show="provNewIcCnt.indexOf('是')>-1">纯新增</div>
                                    <div class='tag-item' v-show="isDperson.indexOf('是')>-1">D类用户</div>
                                    <div class='tag-item' v-show="lowConsume.indexOf('是')>-1">低消</div>
                                </div>
                            </li>
                            <div class='title1' v-if='huawuList.length > 0'>互通话务信息</div>
                            <div class='huaw' v-if='huawuList.length > 0'>
                                <div class='item' v-for='item in huawuList'>
                                    <div class='value'>{{ getStr(item.statMonth) }}</div>
                                    <div class='title' v-show='item.voiceDuration'>{{ item.voiceDuration }}分钟</div>
                                    <div class='title' v-show='!item.voiceDuration'>--</div>
                                </div>
                            </div>

                            <div class='title1' v-show='sjlist && sjlist.length'>历史商机 </div>
                            <div class='sj-lists' v-for='item in sjlist' :key='item.accountDate'>
                                <div class='item'>
                                    <div>{{ item.accountDate }} {{ item.buopStatus == '6' ? '已结单' : '' }}</div>
                                    <div>创建：{{ item.createOper }} ({{item.createTel}})
                                        {{ item.createDate }}
                                    </div>
                                    <div v-show='item.followTel'>最终跟进：{{ item.followBy }} <span
                                        v-show='item.followTel'>({{ item.followTel }})</span>
                                        {{ item.accountDate }} {{ item.buopStatus == '6' ? '反馈结单' : '' }}
                                    </div>
                                    <div v-show='item.accountType'>结单类型：{{ item.accountType }}</div>
                                    <div v-show='item.failReason'>失败原因：{{ item.failReason }} <span
                                        v-show='item.failReason == "其他"'>:</span>{{ item.failReasonDesc }}
                                    </div>
                                    <div v-show='item.followResult'>跟进结果：{{getfollowResult(item.followResult)}} {{ item.newCardTel
                                        }}
                                    </div>

                                </div>

                            </div>
                        </div>

                    </div>
                </div>


            </div>

        </div>
        <div class='hide-box' v-show='changeCountyFlg' @click='cancelCounty'></div>
        <div class="task-search-box wrapper-medias" v-show='changeCountyFlg'>
            <div class="top">
                <div class="lie">
                    <div class="choose-item" v-for='item in countyObjListNew' :class='chooseQX1 == item.countryId ? "active" : ""' @click='chooseCountryName(item)'>
                        <span class="font-wenzi">{{item.countryName }}</span>
                        <span class="iconfont youjiantou1" ></span>
                    </div>
                </div>
                <div class="lie" >
                    <div class="choose-item" v-for='item in countyObjListNew2' :class='chooseQX3 == item.townId ? "active" : ""' @click='chooseCountryName2(item)'>
                        <span class="font-wenzi">{{item.townName}}</span>
                    </div>
                </div>
            </div>
            <div class="bottom">
                <div class="chongzhi" @click='cancelCounty'>取 消</div>
                <div class="queren" @click='sureCounty'>确 认</div>
            </div>
        </div>
        <FamilyVideo v-if='showCameraFlgPhotoOrVideoNew' :showCameraFlgPhotoOrVideoNew='showCameraFlgPhotoOrVideoNew' @emClose='personChooseClose' @emSign='personChooseClose' />
        <personchoose ref='executeRef' v-if='orgId && showExecute' :belongRegion='belongRegion.id' :custName='custName.id' @emCloseInit='closeExecute' @emSubmit='getExecute' :orgaId='orgId' :headerName="'选择服务专家'" :showExecute='1' />

        <div class='op-button-box' v-show='showbtn'>
            <button class='op-button ' @click='submitJudge(1)'>保 存</button>
            <button class='op-button ' :class="cntsub || hasNoOver? '' : 'active'" @click='submitJudge(99)'>提 交</button>
        </div>
        <div class='dialog-box'>
            <img src='../../assets/img/chatloading.gif' />
            <div class='recwave-box1'>正在录音识别中...</div>
        </div>

        <div class='dislog' v-show='popFollowReason'>
            <div class='title'>商机保存成功！请选择该商机的跟进方式:</div>
            <div class='reson-box'>
                <div class='chooseone' @click='choosereson = 1'>
                    <span class='iconfont duihao1' v-show='choosereson == 1'></span>
                    <div class='yuan'></div>提交商机池，由分公司指派专人跟进服务
                </div>
                <div class='chooseone' @click='choosereson = 2'>
                    <span class='iconfont duihao1' v-show='choosereson == 2'></span>
                    <div class='yuan'></div>自行指定专家
                </div>
            </div>
            <div class='reson-box2'>
                <div class='chooseone' @click='submitFlollowPopSure'>确定</div>
            </div>
        </div>
        <div class='hide-box' v-show='popFollowReason'></div>
    </div>
</template>

<script>
import Header from 'components/common/Header.vue'
import Storage from '@/base/storage'
import NlDropdown from 'components/common/NlDropdown/dropdownForBusinessOpportunity.js'
import RecorderManger from '@/base/chatUtil/RecorderManager.js'
import ClientJs from '@/base/clientjs'
import { dateFormat } from '@/base/utils'
import NlDatePicker from 'components/common/NlDatePick2/datePicker.js'
import FamilyVideo from './FamilyVideo'
import ImageObj from '@/base/request/ImageServReq'
import storage from '../../base/storage'
import personchoose from './personchoose.vue'
import Vconsole from 'vconsole'

export default {
    name: 'weihu',
    components: { personchoose, FamilyVideo, Header },
    data() {
        return {
            editType: '',// 访问页面类型 supplement补充 / view查看 / change维护 / add新增
            showinfo: false,
            onlyKey: '',
            buopId: '',
            orgId: '1000250',
            popFollowReason: false, // 商机提交成功！请选择该商机的跟进方式
            choosereson: '1',
            showExecute: false,
            showIMG: false,
            add: null,
            yzmCode: '',
            countdown: 0,
            timer: null,
            routeBillid: '',
            routeBuopId: '',
            showDalog: false,
            cntsub: false,
            showCameraFlgPhotoOrVideoNew: false,
            tabactive: 1,
            thisvideo: [],
            isContBund: false,
            mItem: { oregionId: '', operaId: '' },
            remark: '',//其他信息
            ywProdList: [],
            ywProdListChoose: [],
            elseMTname: '',//其他套餐名称
            elseMTname2: '',//外省套餐名称
            rec: '',
            speakingFlg: false,
            iosPermissionFlg: '',
            homeType: '',
            telNum: '',
            memberInfoList: '',
            homeId: '',
            editObject: {},
            editMemberList: [],
            recwave: {
                input: function() {
                }
            },
            busiUserName: '',
            laiyuan: '',
            villageName: '',
            belongRegion: {},
            custName: {},
            busiCertId: '',
            busiExpireDate: '',
            showbtn: true,
            provNewIcCnt: '',
            lowConsume: '',
            suanzhanglist: [],
            sjlist: [],
            hasNoOver: false,
            isDperson: '',
            huawuList: [],
            userinfo: {},
            showMoreContent: false,
            thebase64: [],
            countyObjList: [],
            changeCountyFlg: false,
            countyObjListNew: [],
            countyObjListNew2: [], // 归属列表
            chooseQX1: '', // 归属列表
            chooseQX11: '', // 归属列表
            chooseQX2: '', // 归属列表
            chooseQX22: '', // 归属列表
            chooseQX3: '', // 归属列表
            chooseQX33: '', // 归属列表
            reserve3: '',
            savetype: '',
            buchong: '',
            subID: '',
            canTel: false,
            khdbase: '',
            yysText: '',
            serviceDate: '',
            bandInstall: '',
            monthConsum: '',
            isPhone: true,
            regionArr: [
                {"region":'11',"longRegion":"1000512"}, //苏州
                {"region":'12',"longRegion":"1000517"}, //淮安
                {"region":'13',"longRegion":"1000527"}, //宿迁
                {"region":'14',"longRegion":"1000250"}, //南京
                {"region":'15',"longRegion":"1000518"}, //连云港
                {"region":'16',"longRegion":"1000516"}, //徐州
                {"region":'17',"longRegion":"1000519"}, //常州
                {"region":'18',"longRegion":"1000511"}, //镇江
                {"region":'19',"longRegion":"1000510"}, //无锡
                {"region":'20',"longRegion":"1000513"}, //南通
                {"region":'21',"longRegion":"1000523"}, //泰州
                {"region":'22',"longRegion":"1000515"}, //盐城
                {"region":'23',"longRegion":"1000514"}, //扬州
                {"region":'99',"longRegion":"1000250"} //江苏省-取南京
            ],
            chongfu: ''
        }
    },
    methods: {
        getoStatus(val) {
            if (val == '1') {
                this.yysText = '移动'
                return '移动'
            } else if (val == '2') {
                this.yysText = '联通'
                return '联通'
            } else if (val == '3') {
                this.yysText = '电信'
                return '电信'
            } else if (val == '4') {
                this.yysText = '中国网通'
                return '中国网通'
            } else if (val == '5') {
                this.yysText = '中国铁通'
                return '中国铁通'
            } else if (val == '6') {
                this.yysText = '中国卫通'
                return '中国卫通'
            } else if (val == '7') {
                this.yysText = '中宇通信'
                return '中宇通信'
            } else if (val == '8') {
                this.yysText = '其他'
                return '其他'
            } else if (val == '9') {
                this.yysText = '中国广电'
                return '中国广电'
            }
        },
        getfollowResult(val) {
            if (val == '1') {
                return '新办卡'
            }else if (val == '2') {
                return '归位'
            }else if (val == '3') {
                return '低消迎回'
            }else if (val == '4') {
                return '失败结单'
            }else if (val == '5') {
                return '其他'
            }else{
                return val
            }
        },
        closeExecute() {
            this.showExecute = false
            this.showExecute2 = false
        },
        getExecute(item2, Status) {
            // 指派
            this.chooseitem1Person = item2
            this.showExecute = false


            let url2 = '/xsb/gridCenter/familyDiagnosis/h5UpdateBusiStatus'
            let userInfo = this.userinfo
            let param2 = {
                'busiSource':  2,
                'transferSource': 2,
                'busiType': 1,
                "buopStatus": 7,
                'crmId': this.userinfo.crmId,
                "buopNewStatus": item2 ? 2 : 5,
                'region': this.userinfo.region,
                'buopId': this.subID,  //商机id
                'busiTel': this.telNum, //商机号码
                'followOldBy': '',  //上一跟进人
                'followOldTel': '',//上一跟进人电话
                'followBy': '',//当前跟进人（当前操作人）
                'followTel': '',//当前跟进人电话（当前操作人电话）
                'busiReleaseReason': '', //商机释放原因
                'busiTransferReason': '',//商机转派原因
                'modifyOper': this.userinfo.operatorName,//修改人（当前操作人）
                'modifyTel': this.userinfo.servNumber,//修改人（当前操作人电话）
                'operateType': item2 ? 2 : 3,
                'transferPerson': item2 ? item2.execName : '',//转派或者指派人
                'transferTel': item2 ? item2.execMsisdn : ''//转派或者指派人电话
            }

            console.log('指派/转派---人', item2)
            console.log('指派/转派---请求', param2)

            this.$http.post(url2, param2).then((res) => {
                let { retCode, retMsg } = res.data
                if (retCode == '0') {
                    this.$toast('操作成功')
                    setTimeout(() => {
                        history.go(-1)
                    }, 1000)
                } else {
                    this.$alert(retMsg || `记录商机失败`)
                }
            })
        },
        submitFlollowPopSure() {
            let arr = [
                { 'region': '11', 'longRegion': '1000512' }, //苏州
                { 'region': '12', 'longRegion': '1000517' }, //淮安
                { 'region': '13', 'longRegion': '1000527' }, //宿迁
                { 'region': '14', 'longRegion': '1000250' }, //南京
                { 'region': '15', 'longRegion': '1000518' }, //连云港
                { 'region': '16', 'longRegion': '1000516' }, //徐州
                { 'region': '17', 'longRegion': '1000519' }, //常州
                { 'region': '18', 'longRegion': '1000511' }, //镇江
                { 'region': '19', 'longRegion': '1000510' }, //无锡
                { 'region': '20', 'longRegion': '1000513' }, //南通
                { 'region': '21', 'longRegion': '1000523' }, //泰州
                { 'region': '22', 'longRegion': '1000515' }, //盐城
                { 'region': '23', 'longRegion': '1000514' }, //扬州
                { 'region': '99', 'longRegion': '2000250' } //江苏省
            ]
            for (let i = 0; i < arr.length; i++) {
                if (arr[i].region == this.belongRegion.id) {
                    if (arr[i].longRegion == '2000250') {
                        this.orgId= '1000250'
                    } else {
                        this.orgId= arr[i].longRegion
                    }
                }
            }
            if (this.choosereson == 1) {
                // 释放 提交商机池状态为“待接单”
                this.updateState()
            } else {
                this.showExecute = true
            }



        },
        updateState(){
            console.log(this.codetype)
            let url = '/xsb/gridCenter/familyDiagnosis/h5UpdateBusiStatus'
            let param = {
                'busiType': 1,
                'busiSource':  2,
                'transferSource': 2,
                'countyId': this.custName.id,
                "crmId": Storage.session.get('userInfo').crmId,
                "region":  Storage.session.get('userInfo').region,
                "buopId": this.subID ,  //商机id
                "busiTel": this.telNum, //商机号码
                "buopNewStatus": 5,
                "followOldBy": '',  //上一跟进人
                "followOldTel": '',//上一跟进人电话
                "followBy": Storage.session.get('userInfo').operatorName,//当前跟进人（当前操作人）
                "followTel": Storage.session.get('userInfo').servNumber,//当前跟进人电话（当前操作人电话）
                "buopStatus": 7,//商机状态
                "busiReleaseReason":"", //商机释放原因
                "busiTransferReason":"",//商机转派原因
                "modifyOper":Storage.session.get('userInfo').operatorName,//修改人（当前操作人）
                "modifyTel":Storage.session.get('userInfo').servNumber,//修改人（当前操作人电话）
                "operateType":  '3',
                "transferPerson":'',//转派或者指派人
                "transferTel": ''//转派或者指派人电话
            }
            console.log(param)
            this.$http.post(url, param).then((res) => {
                if (res.data.retCode == 0) {
                    this.$toast('释放成功')
                    history.go(-1)
                } else {
                    this.$toast(res.data.retMsg)
                }
            })
        },
        showSms() {
            if (this.telNum && this.telNum['0'] == 1 && this.telNum.length == 11) {
                return true
            }
            return false
        },
        startCountdown() {
            if (this.timer) return

            if (!this.telNum) {
                this.$toast('请输入用户号码')
                return
            }
            if (this.telNum['0'] == 1) {
                var regnum = /^[\d|\.]*$/
                if (!regnum.test(this.telNum)) {
                    this.$toast('请输入正确的用户号码')
                    return
                }
                if (this.telNum.length != 11) {
                    this.$toast('请输入正确的用户号码')
                    return
                }
                var regnum2 = /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/
                if (!regnum2.test(this.telNum)) {
                    this.$toast('请输入正确的用户号码')
                    return
                }
            }
            this.$messagebox({
                title: '温馨提示',
                showCancelButton: true,
                message: '确定给' + `<span style="color: #0b7ffe">${this.telNum}</span>` + '发送验证码短信吗？',
                confirmButtonText: '确定'
            }).then((ressend) => {
                if (ressend === 'confirm') {
                    this.sendSms()
                    // 这里可以添加点击按钮后需要执行的逻辑
                }
            })


        },
        sendSms() {

            let url = '/xsb/gridCenter/sendCode/h5SendVerification'
            let param = {
                'crmId': this.userinfo.crmId,
                'region': this.userinfo.region,
                'telnum': this.telNum
            }
            this.$http.post(url, param).then((res) => {
                console.log('h5SendVerification', res)
                if (res.data.retCode == 0) {
                    this.countdown = 60 // 设置倒计时时间，如60秒
                    this.timer = setInterval(() => {
                        if (this.countdown > 0) {
                            this.countdown -= 1
                        } else {
                            clearInterval(this.timer)
                            this.timer = null
                        }
                    }, 1000)
                    this.$toast('验证码发送成功')
                    return
                } else {
                    this.$alert(res.data.retMsg || '验证码发送失败')
                }
            }).catch((response) => {
                this.$alert(response || '验证码发送失败')

            })
        },
        getSmsCheckVerification(save) {
            if (!this.yzmCode) {
                this.$toast('请输入短信验证码')
                return
            }
            this.yzmCode = this.yzmCode + ''
            if (this.yzmCode.length != 4) {
                this.$toast('请输入4位的短信验证码')
                return
            }
            let url = '/xsb/gridCenter/sendCode/h5CheckVerification'
            let param = {
                'crmId': this.userinfo.crmId,
                'region': this.userinfo.region,
                'sendCode': this.yzmCode,
                'telnum': this.telNum
            }
            this.$http.post(url, param).then((res) => {
                if (res.data.retCode == 0) {
                    this.submitFunction(1, save)
                } else {
                    this.$alert(res.data.retMsg || '验证码校验失败')
                }

            }).catch((response) => {
                this.$alert(response || '验证码校验失败')
            })
        },
        personChooseClose() {
            this.showCameraFlgPhotoOrVideoNew = false
            this.thisvideo = Storage.session.get('getsjVideo') ? Storage.session.get('getsjVideo') : ''   // YW用户信息-视频
            this.thebase64 = Storage.session.get('ywUserInfoPhotobase')
        },
        getHis() {
            let that = this
            if (!this.$route.query.buopId) return
            let param = {
                'crmId': this.userinfo.crmId,
                'region': this.userinfo.region,
                'buopId': this.$route.query.buopId,
                'busiTel': this.$route.query.busiTel
            }

            // this.getJudgment()
            this.homeId = this.$route.query.homeId
            this.$http.post('/xsb/gridCenter/familyDiagnosis/h5PersonBusiQuery', param).then((res) => {
                if (res.data.retCode == '0') {
                    this.editObject = res.data.data[0]
                    // 手机号
                    this.telNum = this.editObject.busiTel ? this.editObject.busiTel : ''
                    // 从路由取号码
                    if (!this.editObject.busiTel) {
                        this.telNum = this.$route.query.busiTel
                    }
                    // 服务日期
                    this.serviceDate = this.editObject.serviceDate? this.editObject.serviceDate : ''

                    // 号码校验
                    this.inputNum()

                    // 历史商机
                    this.getSj()

                    // 话务信息
                    this.getDandHuawu()

                    // 归属省内外+运营商
                    this.mItem.oregionId = this.editObject.userAffiliation ? this.editObject.userAffiliation : ''
                    this.mItem.operaId = this.editObject.busiOperatorName ? this.editObject.busiOperatorName : ''

                    console.log('this.mItem',this.mItem)
                    // 姓名
                    this.busiUserName = this.editObject.busiUserName

                    // 来源
                    this.laiyuan = this.editObject.userSource

                    // 合约捆绑
                    if (this.editObject.isContBund == 'true' || this.editObject.isContBund == true) {
                        this.isContBund = true
                    } else {
                        this.isContBund = false
                    }

                    // 详细地址
                    this.villageName = this.editObject.villageName

                    // 归属区县
                    this.custName = {
                        label : this.editObject.custName,
                        id : this.editObject.countyId,
                        townId:  this.editObject.streetId,// 街道-id
                        townName: this.editObject.streetName, // 街道-名

                        countryId: this.editObject.countyId, // 区县-id
                        countryName: this.editObject.custName,// 区县-名

                        gridId: this.editObject.gridId, // 网格-id
                        gridName: this.editObject.gridName, // 网格-名
                    }

                    // 合约到期时间
                    this.busiExpireDate = this.editObject.busiExpireDate

                    // 用户需求描述
                    this.reserve3 = this.editObject.userDemand ? this.editObject.userDemand : ''

                    // 宽带安装地址
                    this.bandInstall = this.editObject.bandInstall ? this.editObject.bandInstall : ''

                    // 月消费
                    this.monthConsum = this.editObject.monthConsum ? this.editObject.monthConsum : ''

                    // 预约服务时间
                    this.serviceDate = this.editObject.serviceDate ? this.editObject.serviceDate : ''

                    // 身份证
                    if(this.editObject.busiCertId) {
                        this.busiCertId = this.editObject.busiCertId
                        this.qrySubsCertIdNum(this.busiCertId)
                    }
                    let arr = [
                        { 'region': '11', 'longRegion': '1000512','label': '苏州' }, //苏州
                        { 'region': '12', 'longRegion': '1000517','label': '淮安' }, //淮安
                        { 'region': '13', 'longRegion': '1000527','label': '宿迁' }, //宿迁
                        { 'region': '14', 'longRegion': '1000250','label': '南京' }, //南京
                        { 'region': '15', 'longRegion': '1000518','label': '连云港' }, //连云港
                        { 'region': '16', 'longRegion': '1000516','label': '徐州' }, //徐州
                        { 'region': '17', 'longRegion': '1000519','label': '常州' }, //常州
                        { 'region': '18', 'longRegion': '1000511','label': '镇江' }, //镇江
                        { 'region': '19', 'longRegion': '1000510','label': '无锡' }, //无锡
                        { 'region': '20', 'longRegion': '1000513','label': '南通' }, //南通
                        { 'region': '21', 'longRegion': '1000523','label': '泰州' }, //泰州
                        { 'region': '22', 'longRegion': '1000515','label': '盐城' }, //盐城
                        { 'region': '23', 'longRegion': '1000514','label': '扬州' }, //扬州
                    ]
                    for (let i = 0; i < arr.length; i++) {
                        if (arr[i].region == this.editObject.areaCode) {
                            // 归属区县
                            this.belongRegion = {
                                label : arr[i].label,
                                id : this.editObject.areaCode,
                            }
                            this.orgId= arr[i].longRegion
                        }
                    }
                    this.qryCountyList(this.belongRegion.id) // 地市列表
                    this.getStreetList(this.belongRegion.id) // 地市列表


                    // 图片
                    this.thisvideo = this.editObject.ztVideoUrl.split(',')
                    console.log('this.thisvideo--字符串',this.thisvideo)
                    if (this.thisvideo) {
                        for (let i = 0 ; i < this.thisvideo.length;i++) {
                            ImageObj.getImgUrl(this.thisvideo[i], {}, '/xsb/personBusiness/queryCalculate/h5DownBillFollowUpPic').then(res => {
                                this.thebase64.push(res)
                                console.info('this.thebase64-blob',res)
                            })
                        }

                    }

                    this.$forceUpdate()
                }
            })
        },
        //获取照片
        async accessPhoto() {
            if (window.vwtSelf) {
                console.log("安卓");
                window.vwtSelf.takePhoto(JSON.stringify({
                    abID: "1",
                    cbFuncName: "accessPhotoCallback"
                }));
                console.log(window.vwtSelf.takePhoto("accessPhotoCallback"));
            } else if (WebViewJavascriptBridge) {
                console.log("ios");
                WebViewJavascriptBridge.callHandler("takePhoto", JSON.stringify({
                    abID: "1",
                    cbFuncName: "onGetImage1"
                }));
            }
        },
        //获取照片-OCR识别身份证
        async sfzOcrFun() {
            if (!this.showbtn) return

            if (window.vwtSelf) {
                console.log("安卓");
                window.vwtSelf.takePhoto(JSON.stringify({
                    abID: "1",
                    cbFuncName: "accessPhotoCallback2"
                }));
                console.log(window.vwtSelf.takePhoto("accessPhotoCallback2"));
            } else if (WebViewJavascriptBridge) {
                console.log("ios");
                WebViewJavascriptBridge.callHandler("takePhoto", JSON.stringify({
                    abID: "1",
                    cbFuncName: "onGetImage2"
                }));
            }
        },


        getvideo() {
            if (this.thebase64.length < 3) {
                this.accessPhoto()
            } else {
                this.$toast('最多可拍三张')
            }
        },
        openImg(item){
            this.showIMG = false

            setTimeout(() => {
                this.showIMG = item.replace(new RegExp('\n', 'g'), '\\n')

            },500)
        },
        // OCR识别身份证


        //主副号判断服务
        getJudgment() {
            if (!this.telNum && !this.$route.query.busiTel) {
                return
            }
            let param = {
                'msisdn': this.telNum ? this.telNum : this.$route.query.busiTel
            }
            let url = '/xsb/personBusiness/queryCalculate/h5QryJudgmentNum'
            this.$http.post(url, param).then(res => {
                let { retCode, data, retMsg } = res.data
                if (retCode == '0') {
                    this.homeId = data.result.homeId
                    // this.getMemberInfo()
                } else {
                }
            })
        },
        //查询家庭成员信息
        getMemberInfo(type, index) {
            let userInfo = this.userinfo
            let param = {
                'msisdnListStr': JSON.stringify([]),
                'inMsisdn': this.telNum,
                'homeId': this.homeId,
                'operatorMsisdn': userInfo.servNumber,
                'operatorName': userInfo.operatorName,
                'operatorCrm': userInfo.crmId
            }
            let url = '/xsb/personBusiness/queryCalculate/h5qryFamilyMembersInfo'
            this.$http.post(url, param).then(res => {
                let { retCode, retMsg, data } = res.data
                if (retCode == '0') {
                    if (data && data.length > 0) {
                        this.editMemberList = data
                        for (let i = 0; i < this.editMemberList.length; i++) {

                            if (this.editMemberList[i].msisdn == this.telNum) {
                                this.editObject = this.editMemberList[i]
                            }
                        }
                    } else {
                        this.$alert(retMsg || '查询家庭成员信息失败')
                    }
                }
            })
        },
        getImgInfo(base64) {
            let param = {
                'crmId': this.userinfo.crmId,
                'region': this.userinfo.region,
                'image_data': base64,
                'unEncrpt': true
            }
            let url = `/xsb/chatTools/marEmpower/h5UniverOcrRecogn`
            this.$http.post(url, param).then((res) => {
                if (res.data.retCode == '0') {
                    let obj = {
                        voiceTxt: res.data.data['result_data'].join(',').replace(/,/g, '')
                    }
                    if (obj.voiceTxt) {
                        this.getRecordResultCbFn(obj)
                    } else {
                        this.$toast('OCR识别失败')

                    }
                }
            })
        },
        getImgInfo2(base64) {
            let param = {
                'crmId': this.userinfo.crmId,
                'region': this.userinfo.region,
                'imageBase64String': base64,
                'imagePage': 'f',
                'unEncrpt': true

            }
            let url = `/xsb/gridCenter/familyDiagnosis/h5Idcardocr`
            this.$http.post(url, param).then((res) => {
                if (res.data.retCode == '0') {
                    this.busiCertId = res.data.data.id_number
                    this.busiUserName = res.data.data.name

                    this.qrySubsCertIdNum(this.busiCertId)

                }
            })
        },
        startRecord() {
            this.rec.startRecorder()
        },
        stopRecord() {
            this.rec.endRecorder();
        },
        handlerTouchstart(e) {
            if (!this.showbtn) return
            if (!this.rec) {
                if (/android|harmony/gi.test(navigator.userAgent)) {
                    this.rec = new RecorderManger({}, this.recwave, this.getRecordResultCbFn)
                } else {
                    this.rec = new RecorderManger({}, this.recwave, this.getRecordResultCbFn)
                }
            } else {
                e.preventDefault()
                this.rec.startRecorder()
                document.querySelector('.dialog-box').style.top = '42px'
            }


        },
        handlerTouchend() {
            if (!this.rec) return
            this.rec.endRecorder()
            document.querySelector('.dialog-box').style.top = '-300px'

        },
        inputSfz() {
            this.busiCertId = this.busiCertId + ''
            if (this.busiCertId.length == 18) {
                console.info(this.validateIdCard(this.busiCertId))
                if (this.validateIdCard(this.busiCertId)) {
                    this.qrySubsCertIdNum(this.busiCertId)

                } else {
                    this.$toast('请输入正确的身份证号')
                }
            }
        },
        //录音回调
        getRecordResultCbFn(obj) {
            console.log('obj', obj)
            if (!obj.voiceTxt) return
            if (obj.code == '-1') {
                this.$toast(obj.voiceTxt)
                return
            }
            if (obj.voiceTxt && obj.voiceTxt.length > 500) {
                let result = obj.voiceTxt.substring(0, 100)
                this.reserve3 = result

            } else {
                this.reserve3 = obj.voiceTxt

            }

        },
        //判断解析，可以为小数2位，正整数
        validates(val, tips) {
            if (!val) return
            if (!/^((?:-?0)|(?:-?[1-9]\d{0,19}))(?:\.\d{1,2})?$/.test(val)) {
                this.$toast(`${tips}必须为长度不超过20的数字,最多包含2位小数`)
                return false
            }
            if (val.indexOf('-') > -1) {
                this.$toast(`${tips}必须为长度不超过20的数字,最多包含2位小数`)
                return false
            }
            return true
        },
        //判断为正整数，可以为0.但是不能以0开头
        valiNum(val, tips) {
            if (!/(^[1-9]([0-9]*)$|^[0-9]$)+/.test(val)) {
                this.$toast(`${tips}必须为正整数,长度不超过20位`)
                return false
            }
            return true
        },
        goPrev() {
            this.goBack()
            Storage.session.set('ywUserInfoPhoto', '')
            Storage.session.set('ywUserInfoPhotobase', '')

            setTimeout(() => {
                history.go(-1)
            }, 500)
            if (this.rec) {
                this.rec && this.rec.recorder.close()
                this.rec = null
                this.luyinFlag = false
            }


        },
        inputNum() {
            this.cntsub = false
            this.hasNoOver = false
            this.isPhone = true

            console.log('this.telNum',this.telNum)

            if (this.telNum) {
                this.hasNoOver = false
                this.cntsub = false
                let str = this.telNum + ''
                if (str['0'] == '0') {
                    if (str.length >= 11) {
                        this.$toast('请输入正确的手机号码')
                        this.telNum = ''
                        this.isPhone = false

                    }
                } else {
                    this.suanzhanglist = []
                    this.huawuList = []
                    this.sjlist = []
                    this.isDperson = ''
                    if (str.length >= 11) {

                        this.panduanYorO(this.telNum)


                    }
                }
            }
            // this.yzmCode = ''
            // this.timer = null
            // this.countdown = 0
        },
        panduanHas() {
            if (!this.$route.query.add) {
                return
            }
            if (!this.telNum) return
            let param11 = {
                'busiTel': this.telNum,       //商机号码
                'region': this.userinfo.region,    //地市
                'regionId': this.belongRegion.id ? this.belongRegion.id : this.userInfo.region == 99 ?14  : this.userInfo.region,    //地市
                'busiType': 1

            }
            console.info('判定商机号码是否已存在?', param11)

            this.$http.post('/xsb/gridCenter/familyDiagnosis/h5UnfinishedBusiQuery', param11).then((res) => {
                if (res.data.retCode == -1) {
                    this.hasNoOver = true
                    // 您可以办理成功后联系管理员补录。
                    let result = res.data.retMsg.replace(/您可以办理成功后联系管理员补录。/, "");
                    this.chongfu = result
                    this.$toast(res.data.retMsg)
                    return
                } else {
                    this.hasNoOver = false
                    this.getDandHuawu()
                    this.getSj()
                }
            })
        },
        panduanYorO(value) {
            if (this.editType == 'add') {
                this.cntsub = false
                if (value['0'] == 0) {

                    this.mItem.oregionId = 2
                    this.mItem.operaId = 2
                    this.cntsub = false
                    this.panduanHas()


                } else {

                    var regnum2 = /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/
                    if (!regnum2.test(value)) {
                        this.isPhone = false

                        return
                    }

                    let urlnew = `/xsb/gridCenter/familyDiagnosis/h5QryUserCityByBoss`
                    let paramenw = {
                        'crmId': this.userinfo.crmId,
                        'region': this.userinfo.region,
                        'servicenumber': value,  // 手机号码
                        'is_show_csp': '1'              // 是否展示运营商   0：否   1：是
                    }
                    this.$http.post(urlnew, paramenw).then((resnew) => {
                        if (resnew.data.retCode == '0') {

                            this.getoStatus(resnew.data.data.csp_type)
                            if (resnew.data.data.province_name.indexOf('江苏') >= 0) {
                                if (resnew.data.data.csp_type == 1) {
                                    this.cntsub = true
                                    this.mItem.oregionId = '1'
                                    this.mItem.operaId = resnew.data.data.csp_type
                                    this.getoStatus(this.mItem.operaId)
                                    this.$messagebox({
                                        title: '温馨提示',
                                        message: '存量号码，请重新输入，或按业务商机进行录入。',
                                        showCancelButton: true,
                                        confirmButtonText: '去业务商机',
                                        cancelButtonText: '知道了',
                                    }).then((action) => {
                                        if (action == 'confirm') {
                                            this.$router.push({
                                                path: '/BusinessOpportMaintenance2',
                                                query: {
                                                    showbtn: 1,
                                                    editType: 'add',
                                                    tel: this.telNum

                                                }
                                            })
                                        }
                                    });
                                } else {
                                    this.mItem.oregionId = '2' // 外省
                                    this.mItem.operaId = resnew.data.data.csp_type
                                    this.panduanHas()
                                    this.getoStatus(this.mItem.operaId)

                                }
                            } else {
                                this.mItem.oregionId = '3' // 外省
                                this.mItem.operaId = resnew.data.data.csp_type
                                this.panduanHas()

                                this.getoStatus(this.mItem.operaId)
                            }
                        } else {
                            this.$alert(resnew.data.retMsg || '未识别出号码归属，请重新输入号码')

                        }
                    }).catch(() => {
                        this.$toast('未识别出号码归属，请重新输入号码')

                    })


                }
            } else {

            }


        },
        changeTime() {
            if (!this.showbtn) return

            let date = ''
            let _this = this

            NlDatePicker({
                onlyOne: true,
                format: 'yyyy-MM-dd',
                title: '合约到期时间',

                tsMinDate: new Date(new Date().getTime() - 1000 * 24 * 60 * 60)

            }, (retVal) => {
                console.info(retVal)
                _this.busiExpireDate = retVal.startDate + ' 23:59:59'
            })
        },
        changeTime2(){
            if (!this.showbtn) return

            let _this = this;

            NlDatePicker({
                onlyOne:true,
                dateType: 'datetime',
                format: 'yyyy-MM-dd hh:mm',
                tsMinDate:new Date(new Date().getTime()),
                title: '预约服务时间',

            }, (retVal) => {
                _this.serviceDate = retVal.startDate + ':00' ;
                console.info(_this.serviceDate)

            });
        },
        changeLY() {
            if (!this.showbtn) return

            var self = this
            NlDropdown({
                confirmBtn: false,
                title: '客户来源',

                datalist: [
                    { id: '', label: '请选择' },
                    { id: '进厅', label: '进厅' },
                    { id: '公开卖场', label: '公开卖场' },
                    { id: '沿街商铺', label: '沿街商铺' },
                    { id: '小区', label: '小区' },
                    { id: '集团', label: '集团' },
                    { id: '楼宇', label: '楼宇' },
                    { id: '村组', label: '村组' },
                    { id: '银发市场', label: '银发市场' },
                    { id: '在线', label: '在线' },
                    { id: '互联网', label: '互联网' },
                ]
            }, function(retVal) {
                self.laiyuan = retVal.label
            })
        },
        //客户关怀查询接口
        qrySubsCertIdNum(cardNum) {
            if (cardNum) {
                let url = `/xsb/personBusiness/customerCare/h5QryCustomerInfo?certNo=${cardNum}`
                this.$http.get(url).then(res => {
                    let { retCode, retMsg, data } = res.data
                    console.info('客户关怀查询接口provNewIcCnt', res.data.data.provNewIcCnt)
                    console.info('客户关怀查询接口lowConsume', res.data.data.lowConsume)
                    this.provNewIcCnt = res.data.data.provNewIcCnt ? res.data.data.provNewIcCnt : ''
                    this.lowConsume = res.data.data.lowConsume ? res.data.data.lowConsume : ''
                })

            }
        },
        getDandHuawu() {
            let url = '/xsb/gridCenter/familyDiagnosis/h5QueryMsisdnDura'
            let params = {
                'msisdn': this.telNum,  //输入的商机号码
                'sysId': 'H00000000072',
                'crmId': this.userinfo.crmId,
                'region': this.userinfo.region
            }
            this.$http.post(url, params).then((res) => {
                if (res.data.retCode == '0') {
                    let body = res.data.data.body
                    this.isDperson = body.isDTypeLable == 1 ? '是' : '否'
                    if (body.dataList && body.dataList.length) {
                        this.huawuList = body.dataList

                    }

                }
            })
        },
        debounce(func, delay) {
            let timeoutId
            console.log(func)
            return function(...args) {
                // 清除之前的定时器
                if (timeoutId) {
                    clearTimeout(timeoutId)
                }
                // 设置新的定时器
                timeoutId = setTimeout(() => {
                    func.apply(this, args) // 或者使用func(...args)
                }, delay)
            }
        },
        OCRfun(base64){
            this.submitPic(base64)
        },
        submitPic(val) {
            let param2 = {
                photoStr: val,
                comeFrom: 'familyDiagnosis'
            }
            param2['unEncrpt'] = true;
            this.$http.post('/xsb/personBusiness/queryCalculate/h5SubmitPhoto', param2).then((res) => {
                this.thisvideo.push(res.data.data)
                let param1 = {
                    'crmId': Storage.session.get('userInfo').crmId,
                    'region': Storage.session.get('userInfo').region,
                    "operatorTel": Storage.session.get('userInfo').servNumber,  //操作人手机号
                    "images":JSON.stringify([val]),
                    "imageName" : res.data.data,
                    "streamSeq": new Date().getTime(),
                    "params": JSON.stringify([
                        {
                            "key": "busiTel",
                            "desc": "11位的电话号码，类型为字符串，中间四位可能显示为****",
                            "exp": "***********"
                        },
                        {
                            "key": "busiUserName",
                            "desc": "用户姓名，类型为字符串，除姓之外其余可能显示为*",
                            "exp": "王涛"
                        },
                        {
                            "key": "bandInstall",
                            "desc": "宽带安装地址，类型为字符串",
                            "exp": "中海大厦15A"
                        },
                        {
                            "key": "monthConsum",
                            "desc": "月消费，类型为数字，一般含两位小数",
                            "exp": "157.00"
                        }
                    ]),
                    "prompt": "分析文本并提取以下信息如果没有则传空",
                    "modelName": "QWEN2.5-14B",
                    'unEncrpt' : true

                }
                console.log('ocr---',param1)
                let url1 = `/xsb/gridCenter/familyDiagnosis/h5AiIdentify`
                this.$http.post(url1, param1).then((res) => {
                    if (res.data.data) {

                        let obj = res.data.data.data
                        if(obj.busiTel) {
                            this.telNum = obj.busiTel
                            this.inputNum()
                        }
                        if(obj.busiUserName) {
                            this.busiUserName = obj.busiUserName.replace(/\*/g, "")


                        }
                        if(obj.bandInstall) {
                            this.bandInstall = obj.bandInstall
                        }
                        if(obj.monthConsum) {
                            this.monthConsum = obj.monthConsum

                        }
                    }
                })
            })

        },
        maskphone(phone) {
            if(!phone) return

            let thisObj = this.$route.query.item ? JSON.parse(this.$route.query.item) : {}


            if (thisObj.createTel == Storage.session.get('userInfo').servNumber || thisObj.followTel == Storage.session.get('userInfo').servNumber) {
                return phone; // 185****5678
            } else{
                var reg = /^(1[3-9][0-9])\d{4}(\d{4}$)/;
                // 判断手机号是否能够通过正则校验
                let isMobile=reg.test(phone);
                //将手机号中间4位用*展示
                phone = phone.replace(reg, '$1****$2');
                return phone; // 185****5678
            }
        },
        maskphone2(phone) {
            if(!phone) return

            var reg = /^(1[3-9][0-9])\d{4}(\d{4}$)/;
            // 判断手机号是否能够通过正则校验
            let isMobile=reg.test(phone);
            //将手机号中间4位用*展示
            phone = phone.replace(reg, '$1****$2');
            return phone; // 185****5678
        },
        async submitJudge(save) {
            if (!this.isPhone) {
                this.$toast('请输入正确的手机号码')
                return
            }
            var regnum0 = /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/
            if (!regnum0.test(this.telNum)) {
                this.$toast('请输入正确的手机号')
                return
            }
            if (this.hasNoOver) {
                this.$toast(this.chongfu ||'该商机号已存在，请重新输入用户号码')
                return
            }
            if (this.cntsub) {
                this.$messagebox({
                    title: '温馨提示',
                    message: '存量号码，请重新输入，或按业务商机进行录入。',
                    showCancelButton: true,
                    confirmButtonText: '去业务商机',
                    cancelButtonText: '知道了',
                }).then((action) => {
                    if (action == 'confirm') {
                        this.$router.push({
                            path: '/BusinessOpportMaintenance2',
                            query: {
                                showbtn: 1,
                                editType: 'add',
                                tel: this.telNum
                            }
                        })
                    }
                });
                return
            }
            if (!this.telNum) {
                this.$toast('请输入用户号码')
                return
            }


            let regnum = /^[\d|\.]*$/
            if (this.telNum['0'] == 0) {
                if (!regnum.test(this.telNum)) {
                    this.$toast('请输入数字')
                    return
                }
                const reg = /^0\d{2,3}\d{7,8}$/
                if (!reg.test(this.telNum)) {
                    this.$toast('请输入正确的用户号码')
                    return
                }
            } else if (this.telNum['0'] == 1) {
                if (!regnum.test(this.telNum)) {
                    this.$toast('请输入数字')
                    return
                }
                if (this.telNum.length != 11) {
                    this.$toast('请输入正确的用户号码')
                    return
                }
                var regnum2 = /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/
                if (!regnum2.test(this.telNum)) {
                    this.$toast('请输入正确的手机号')
                    return
                }
            } else {
                this.$toast('请输入正确的用户号码')
                return
            }


            if(save == 99) {
                console.log('this.mItem', this.mItem)
                if (!this.mItem.operaId) {
                    this.$toast('请选择归属运营商')
                    return
                }
                if (!this.busiUserName) {
                    this.$toast('请输入用户姓名')
                    return
                }
                if (!this.belongRegion.id) {
                    this.$toast('请选择归属地市')
                    return
                }
                if (!this.custName.townId) {
                    this.$toast('请选择区县乡镇')
                    return
                }
            }


            if (this.busiCertId && save != 13) {
                console.info(this.validateIdCard(this.busiCertId))
                if (!this.validateIdCard(this.busiCertId)) {
                    this.$toast('请输入正确的身份证号')
                    return
                }
            }



            if (this.yzmCode) {
                this.getSmsCheckVerification(save)
            } else {
                if (this.editObject.messageVerify == 'true') {
                    this.submitFunction(1, save)
                } else {
                    this.submitFunction(null, save)
                }

            }

        },
        //提交
        async submitFunction(codetype, savetype) {
            console.log('提交codetype,提交savetype',codetype,savetype)

            if (savetype != 13) {
                let regnum = /^[\d|\.]*$/
                if (this.telNum['0'] == 0) {
                    if (!regnum.test(this.telNum)) {
                        this.$toast('请输入数字')
                        return
                    }
                    const reg = /^0\d{2,3}\d{7,8}$/
                    if (!reg.test(this.telNum)) {
                        this.$toast('请输入正确的用户号码')
                        return
                    }
                } else if (this.telNum['0'] == 1) {
                    if (!regnum.test(this.telNum)) {
                        this.$toast('请输入数字')
                        return
                    }
                    if (this.telNum.length != 11) {
                        this.$toast('请输入正确的用户号码')
                        return
                    }
                    var regnum2 = /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/
                    if (!regnum2.test(this.telNum)) {
                        this.$toast('请输入正确的手机号')
                        return
                    }
                } else {
                    this.$toast('请输入正确的用户号码')
                    return
                }

                if(savetype == 99) {
                    console.log('this.mItem', this.mItem)
                    if (!this.mItem.operaId) {
                        this.$toast('请选择归属运营商')
                        return
                    }
                    if (!this.busiUserName) {
                        this.$toast('请输入用户姓名')
                        return
                    }
                    if (!this.belongRegion.id) {
                        this.$toast('请选择归属地市')
                        return
                    }
                    if (!this.custName.id) {
                        this.$toast('请选择归属区县')
                        return
                    }
                }

                if (this.busiCertId) {
                    console.info(this.validateIdCard(this.busiCertId))
                    if (!this.validateIdCard(this.busiCertId)) {
                        this.$toast('请输入正确的身份证号')
                        return
                    }
                }


            }
            let userInfo = this.userinfo

            if (codetype) {
                this.yzmCode = ''
            }

            this.savetype = savetype
            if (this.editType == 'add' || this.editType == 'supplement') {
                console.info('新增')
                let param11 = {
                    'busiTel': this.telNum,       //商机号码
                    'region': this.belongRegion.id,    //地市
                    'regionId': this.belongRegion.id,    //地市
                    'busiType': 1
                }
                console.info('判定商机号码是否已存在?', param11)
                this.$http.post('/xsb/gridCenter/familyDiagnosis/h5UnfinishedBusiQuery', param11).then((reszz) => {
                    if (reszz.data.retCode == -1 && this.editType == 'add') {
                        this.hasNoOver = true
                        // 您可以办理成功后联系管理员补录。
                        let result = reszz.data.retMsg.replace(/您可以办理成功后联系管理员补录。/, "");
                        this.chongfu = result
                        this.$toast(reszz.data.retMsg)
                        return
                    } else {
                        let paramHome = {
                            'msisdn': this.telNum
                        }
                        console.info('不存在:查询当前号码是否已有归属家庭?', paramHome)
                        let urlHome = '/xsb/personBusiness/queryCalculate/h5QryJudgmentNum'
                        this.$http.post(urlHome, paramHome).then(res => {
                            let { retCode, data, retMsg } = res.data
                            if (retCode == '0') {
                                this.homeId = data.result.homeId
                                let userInfo = this.userinfo
                                console.info('this.homeId', this.homeId)
                                console.info('this.operaId', this.operaId)
                                if (this.homeId) {
                                    let url2 = '/xsb/gridCenter/familyDiagnosis/h5PersonBusiAdd'
                                    let userInfo = this.userinfo
                                    let param2 = {
                                        "busiSource": 2,
                                        'oaId': this.$route.query.oaId,
                                        'buopStatus': 7,
                                        'serviceDate': this.serviceDate,
                                        'busiType': 1,
                                        "bandInstall": this.bandInstall,
                                        "monthConsum": this.monthConsum,
                                        'region': userInfo.region == 99 ? '14' : userInfo.region,   // 地市
                                        'busiTel': this.telNum,   //商机号码
                                        'buopId': this.$route.query.buopId,//商机id
                                        'userAffiliation': this.mItem.oregionId,  // 用户归属
                                        'busiOperatorName': this.mItem.operaId,  //运营商
                                        'createTel': userInfo.servNumber,   //当前操作人号码
                                        'createOper': userInfo.operatorName,  //当前操作人
                                        'homeId': this.homeId,  //家庭号
                                        'releBillId': '',  //关联的算账单号
                                        'busiUserName': this.busiUserName,  // 用户姓名
                                        'busiCertId': this.busiCertId,// 身份证号码
                                        "ztVideoUrl": this.thisvideo.join(','),// 掌厅图片
                                        'userSource': this.laiyuan,// 客户来源
                                        'isContBund': this.isContBund, // 是否有合约捆绑
                                        'busiExpireDate': this.busiExpireDate ? this.busiExpireDate : '',// 合约到期时间
                                        'reserve1': this.telNum['0'] == 1 ? true : '',
                                        'custName': this.custName.label,// 归属区县字段
                                        'countyId': this.custName.id,// 归属区县id,
                                        "gridId": this.custName.gridId,
                                        "gridName": this.custName.gridName,
                                        "streetId": this.custName.townId,
                                        "streetName": this.custName.townName,
                                        "regionId": this.belongRegion.id,
                                        'reserve3': this.reserve3, // 用户需求描述
                                        'villageName': this.villageName,// 详细地址字段
                                        'userDemand': this.reserve3,
                                        'crmId': userInfo.crmId,
                                        'messageVerify': codetype ? true : false,// 2025.1.20 新增字段，是否短信验证
                                        'operateType': this.savetype == '99' ? '1' : '13'
                                    }

                                    console.log('add-----',param2)
                                    this.$http.post(url2, param2).then((res2) => {
                                        let { retCode, retMsg, data } = res2.data
                                        if (res2.data.data) {
                                            this.subID = res2.data.data
                                        } else {
                                            this.subID = this.$route.query.buopId
                                        }

                                        if (this.savetype == '99') {
                                            this.popFollowReason = true
                                            this.codetype = codetype

                                        } else {
                                            if (this.savetype == '99') {
                                                this.$toast('提交成功')
                                            } else {
                                                this.$toast('保存成功')
                                            }
                                            setTimeout(() => {
                                                history.go(-1)
                                                if (this.rec) {
                                                    this.rec && this.rec.recorder.close()
                                                    this.rec = null
                                                }
                                            }, 1000)
                                        }
                                        console.info('有家庭----记录新商机', param2)

                                        if (retCode == '0') {
                                            let param = {
                                                'operatorMsisdn': userInfo.servNumber,
                                                'operatorName': userInfo.operatorName,
                                                'operatorCrm': userInfo.crmId,
                                                'changeType': '3',
                                                'regMsisdn': this.telNum,//登记主号码即潜在成员登记哪个号码下
                                                'homeID': this.homeId,//归属家庭
                                                'msisdn': this.telNum,//号码
                                                'ascriptionType': this.mItem.oregionId,//号码归属1：本网2：异网
                                                'teleType': this.mItem.operaId,//运营商1：移动2：电信3：联通
                                                'userID': this.userinfo.servNumber,//变更人
                                                'changeTime': dateFormat(new Date(), 'yyyy/MM/dd hh:mm:ss'),//变更时间
                                                'otherInfo': this.remark || ''
                                            }
                                            let url = '/xsb/personBusiness/queryCalculate/h5QryFamilyMemberChange'
                                            console.info('有家庭----更新已有的成员信息', param)

                                            this.$http.post(url, param).then(res000 => {
                                                if (res000.data.retCode == '0') {
                                                    // history.go(-1)

                                                } else {
                                                    this.$alert(`添加潜在成员失败`)
                                                }
                                            })

                                        } else {
                                            this.$alert(`添加潜在成员失败`)
                                        }
                                    })
                                } else {
                                    let param = {
                                        'operatorMsisdn': userInfo.servNumber,
                                        'operatorName': userInfo.operatorName,
                                        'operatorCrm': userInfo.crmId,
                                        'changeType': '1',
                                        'regMsisdn': this.telNum,//登记主号码即潜在成员登记哪个号码下
                                        'homeID': this.homeId,//归属家庭
                                        'msisdn': this.telNum,//号码
                                        'ascriptionType': this.mItem.oregionId,//号码归属1：本网2：异网
                                        'teleType': this.mItem.operaId,//运营商1：移动2：电信3：联通
                                        'userID': this.userinfo.servNumber,//变更人
                                        'changeTime': dateFormat(new Date(), 'yyyy/MM/dd hh:mm:ss'),//变更时间
                                        'otherInfo': this.remark || ''
                                    }
                                    let url = '/xsb/personBusiness/queryCalculate/h5QryFamilyMemberChange'
                                    console.info('没有家庭----保存成员信息', param)

                                    this.$http.post(url, param).then(res => {
                                        let { retCode, retMsg } = res.data
                                        if (retCode == '0') {
                                            let url3 = '/xsb/personBusiness/queryCalculate/h5QryJudgmentNum'
                                            this.$http.post(url3, { 'msisdn': this.telNum }).then(res3 => {
                                                if (res3.data.retCode == '0') {
                                                    this.homeId = res3.data.data.result.homeId
                                                    let urladd = '/xsb/gridCenter/familyDiagnosis/h5PersonBusiAdd'
                                                    let paramadd = {
                                                        "busiSource": 2,
                                                        "oaId": this.$route.query.oaId,
                                                        "buopStatus": 7,
                                                        "serviceDate": this.serviceDate,
                                                        "busiType": 1,
                                                        "bandInstall": this.bandInstall,
                                                        "monthConsum": this.monthConsum,
                                                        'region': userInfo.region == 99 ? '14' : userInfo.region,   // 地市
                                                        'busiTel': this.telNum,   //商机号码
                                                        'buopId': this.$route.query.buopId,//商机id
                                                        'userAffiliation': this.mItem.oregionId,  // 用户归属
                                                        'busiOperatorName': this.mItem.operaId,  //运营商
                                                        'createTel': userInfo.servNumber,   //当前操作人号码
                                                        'createOper': userInfo.operatorName,  //当前操作人
                                                        'homeId': this.homeId,  //家庭号
                                                        'releBillId': '',  //关联的算账单号
                                                        'busiUserName': this.busiUserName,  // 用户姓名
                                                        'busiCertId': this.busiCertId,// 身份证号码
                                                        "ztVideoUrl": this.thisvideo.join(','),// 掌厅图片
                                                        'userSource': this.laiyuan,// 客户来源
                                                        'isContBund': this.isContBund, // 是否有合约捆绑
                                                        'busiExpireDate': this.busiExpireDate ? this.busiExpireDate : '',// 合约到期时间
                                                        'reserve1': this.telNum['0'] == 1 ? true : '',
                                                        'custName': this.custName.label,// 归属区县字段
                                                        'countyId': this.custName.id,// 归属区县id,
                                                        "gridId": this.custName.gridId,
                                                        "gridName": this.custName.gridName,
                                                        "streetId": this.custName.townId,
                                                        "streetName": this.custName.townName,
                                                        'regionId': this.belongRegion.id,// 归属区县id,
                                                        'reserve3': this.reserve3, // 用户需求描述
                                                        'villageName': this.villageName,// 详细地址字段
                                                        'userDemand': this.reserve3,
                                                        'crmId': this.userinfo.crmId,
                                                        'messageVerify': codetype ? true : false, // 2025.1.20 新增字段，是否短信验证
                                                        'operateType': this.savetype == '99' ? '1' : '13'
                                                    }

                                                    console.info('没有家庭----查到homeid后记录新商机', paramadd)
                                                    this.$http.post(urladd, paramadd).then((resadd) => {
                                                        if (resadd.data.retCode == '0') {
                                                            if (resadd.data.data) {
                                                                this.subID = resadd.data.data
                                                            } else {
                                                                this.subID = this.$route.query.buopId
                                                            }
                                                            console.info('没有家庭----查询号码归属家庭', param)
                                                            if (this.savetype == '99') {
                                                                this.popFollowReason = true
                                                                this.codetype = codetype

                                                            } else {
                                                                this.$toast('提交成功')
                                                                setTimeout(() => {
                                                                    history.go(-1)
                                                                }, 1000)
                                                            }
                                                        } else {
                                                            this.$alert(resadd.data.retMsg || `添加潜在成员失败`)
                                                        }
                                                    })
                                                }
                                            })

                                        } else {
                                            this.$alert(retMsg || `添加潜在成员失败`)
                                        }
                                    })
                                }

                            } else {

                            }
                        })

                    }
                })
            }


        },

        //取消清空
        goBack() {
            if (this.editFlag) {
                this.telNum = this.editObject.msisdn ? this.editObject.msisdn : ''
                this.mItem.oregionId = this.editObject.belongType ? this.editObject.belongType : ''
                this.mItem.operaId = this.editObject.teleType ? this.editObject.teleType : ''
                this.mItem.mTname = this.editObject.pkName ? this.editObject.pkName : ''
                this.mItem.mPrice = this.editObject.pkFee ? this.editObject.pkFee : ''


                this.$forceUpdate()
            } else {
                this.mItem = { oregionId: '1', operaId: '1' }
                this.remark = ''
            }
            this.elseMTname2 = ''
            this.elseMTname = ''
        },
        changePhone(item) {
            let phoneNumber = item.memMsisdn
            if (!phoneNumber) return
            phoneNumber = phoneNumber + ''
            if (phoneNumber.length == 10) {
                return phoneNumber.replace(/(\d{3})\d{3}(\d{4})/, '$1***$2')
            }
            if (phoneNumber.length == 11) {
                return phoneNumber.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
            }
            if (phoneNumber.length == 12) {
                return phoneNumber.replace(/(\d{3})\d{5}(\d{4})/, '$1*****$2')
            }
        },
        getSj() {
            let that = this
            let url = '/xsb/gridCenter/familyDiagnosis/h5QueryHistoryBusi'
            let params = {
                'busiTel': this.telNum,  //输入的商机号码
                busiType: 1
            }
            this.$http.post(url, params).then((res) => {
                console.info('sj-param', params)
                console.info('sj-res', res)
                if (res.data.retCode == '0') {
                    this.sjlist = res.data.data
                    return

                    let arr = res.data.data
                    for (let i = 0; i < arr.length; i++) {
                        if (arr[i].buopId == this.$route.query.buopId) {
                            arr.splice(i, 1)
                        }
                    }
                    this.sjlist = arr

                } else {
                    this.sjlist = []
                }
            })
        },
        /**
         * 身份证合法性校验
         */
        validateIdCard(idCard) {
            let vcity = {
                11: '北京', 12: '天津', 13: '河北', 14: '山西', 15: '内蒙古',
                21: '辽宁', 22: '吉林', 23: '黑龙江', 31: '上海', 32: '江苏',
                33: '浙江', 34: '安徽', 35: '福建', 36: '江西', 37: '山东', 41: '河南',
                42: '湖北', 43: '湖南', 44: '广东', 45: '广西', 46: '海南', 50: '重庆',
                51: '四川', 52: '贵州', 53: '云南', 54: '西藏', 61: '陕西', 62: '甘肃',
                63: '青海', 64: '宁夏', 65: '新疆', 71: '台湾', 81: '香港', 82: '澳门', 91: '国外'
            }
            //是否为空
            if (idCard === '') return false
            //校验长度，类型
            if (this.isCardNo(idCard) === false) return false
            //检查省份
            if (this.checkProvince(idCard, vcity) === false) return false
            //校验生日
            if (this.checkBirthday(idCard) === false) return false
            //检验位的检测
            if (this.checkParity(idCard) === false) return false
            return true
        },
        /**
         * 检查号码是否符合规范，包括长度，类型
         */
        isCardNo(card) {
            //身份证号码为15位或者18位，15位时全为数字，18位前17位为数字，最后一位是校验位，可能为数字或字符X
            let reg = /(^\d{15}$)|(^\d{17}(\d|X|x)$)/
            if (reg.test(card) === false) {
                return false
            }
            return true
        },
        /**
         * 取身份证前两位,校验省份
         * @param {*} card
         * @param {*} vcity
         * @returns
         */
        checkProvince(card, vcity) {
            let province = card.substr(0, 2)
            if (vcity[province] == undefined) {
                return false
            }
            return true
        },
        /**
         * 检查生日是否正确
         * @param {*} card
         * @returns
         */
        checkBirthday(card) {
            var len = card.length
            //身份证15位时，次序为省（3位）市（3位）年（2位）月（2位）日（2位）校验位（3位），皆为数字
            if (len == '15') {
                var re_fifteen = /^(\d{6})(\d{2})(\d{2})(\d{2})(\d{3})$/
                var arr_data = card.match(re_fifteen)
                var year = arr_data[2]
                var month = arr_data[3]
                var day = arr_data[4]
                var birthday = new Date('19' + year + '/' + month + '/' + day)
                return this.verifyBirthday('19' + year, month, day, birthday)
            }
            //身份证18位时，次序为省（3位）市（3位）年（4位）月（2位）日（2位）校验位（4位），校验位末尾可能为X
            if (len == '18') {
                var re_eighteen = /^(\d{6})(\d{4})(\d{2})(\d{2})(\d{3})([0-9]|X|x)$/
                var arr_data = card.match(re_eighteen)
                var year = arr_data[2]
                var month = arr_data[3]
                var day = arr_data[4]
                var birthday = new Date(year + '/' + month + '/' + day)
                return this.verifyBirthday(year, month, day, birthday)
            }
            return false
        },
        /**
         * 校验日期
         * @param {*} year
         * @param {*} month
         * @param {*} day
         * @param {*} birthday
         * @returns
         */
        verifyBirthday(year, month, day, birthday) {
            let now = new Date()
            let now_year = now.getFullYear()
            //年月日是否合理
            if (birthday.getFullYear() == year && (birthday.getMonth() + 1) == month && birthday.getDate() == day) {
                //判断年份的范围（0岁到100岁之间)
                let time = now_year - year
                if (time >= 0 && time <= 100) return true
                return false
            }
            return false
        },
        /**
         * 校验位的检测
         * @param {*} card
         * @returns
         */
        checkParity(card) {
            //15位转18位
            card = this.changeFivteenToEighteen(card)
            let len = card.length
            if (len == '18') {
                let arrInt = new Array(7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2)
                let arrCh = new Array('1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2')
                let cardTemp = 0, i, valnum
                for (i = 0; i < 17; i++) {
                    cardTemp += card.substr(i, 1) * arrInt[i]
                }
                valnum = arrCh[cardTemp % 11]
                if (valnum == card.substr(17, 1).toLocaleUpperCase()) return true
                return false
            }
            return false
        },
        /**
         * 15位转18位身份证号
         * @param {} card
         * @returns
         */
        changeFivteenToEighteen(card) {
            if (card.length == '15') {
                let arrInt = new Array(7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2)
                let arrCh = new Array('1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2')
                let cardTemp = 0, i
                card = card.substr(0, 6) + '19' + card.substr(6, card.length - 6)
                for (i = 0; i < 17; i++)
                    cardTemp += card.substr(i, 1) * arrInt[i]
                card += arrCh[cardTemp % 11]
                return card
            }
            return card
        },
        getStr(value) {
            if (!value) return
            let val = value + ''
            val = val.slice(-2)
            if (val['0'] == 0) {
                val = val.slice(-1)
            }
            val = val + '月'
            return val
        },
        getStreetList(id){
            let regId = ''
            console.log(this.regionArr)
            for (let i = 0 ;i < this.regionArr.length;i++){
                if (this.regionArr[i].region == id){
                    regId = this.regionArr[i].longRegion
                }
            }
            let url = '/xsb/gridCenter/familyDiagnosis/h5QryCountryTownList'
            let params = {
                "areaId": regId
            }
            this.$http.post(url, params).then((res) => {
                console.info('1-param',params)
                console.info('1-res',res)
                if (res.data.retCode == 0) {
                    this.countyObjListNew = res.data.data
                } else {
                    this.countyObjListNew = []
                }

            })
        },
        chooseCountryName(item){
            this.countyObjListNew2 = {}
            this.chooseQX1 = item.countryId
            this.chooseQX11 = item.countryName
            this.chooseQX2 = ''
            this.chooseQX3 = ''
            for (let i = 0 ; i < this.countyObjListNew.length;i++) {
                if (item.countryId == this.countyObjListNew[i].countryId) {
                    this.countyObjListNew2 = this.countyObjListNew[i].townList
                }
            }
        },
        chooseCountryName2(item){
            this.chooseQX2 = item.gridId
            this.chooseQX22 = item.gridName
            this.chooseQX3 = item.townId
            this.chooseQX33 = item.townName
        },
        cancelCounty(){
            this.changeCountyFlg = false
            this.chooseQX1 = ''
            this.chooseQX11 = ''
            this.chooseQX2 = ''
            this.chooseQX22 = ''
            this.chooseQX3 = ''
            this.countyObjListNew2=[]
            this.chooseQX33 = ''
        },
        sureCounty(){
            if (!this.chooseQX3) {
                this.$toast('请选择区县乡镇')
                return
            }
            this.custName = {
                id: this.chooseQX1, // 区县-id
                label:  this.chooseQX11,//  区县-名

                townId:  this.chooseQX3,// 街道-id
                townName: this.chooseQX33, // 街道-名

                countryId: this.chooseQX1, // 区县-id
                countryName: this.chooseQX11,// 区县-名

                gridId: this.chooseQX2, // 网格-id
                gridName: this.chooseQX22, // 网格-名
            }
            this.changeCountyFlg = false
            console.log('this.custName',this.custName)
        },
        //地市列表查询接口
        async qryCountyList(regionId) {
            return
            if (regionId == 99) {
                regionId = 14
            }
            let url = `/xsb/personBusiness/listGroupTarget/h5qryCountyList?regionId=` + regionId
            console.log(url)
            await this.$http.post(url).then(res => {
                if (res.data.retCode == '0') {
                    let data = res.data.data
                    this.countyObjList = data
                    for (let i = 0; i < this.countyObjList.length; i++) {
                        if (this.countyObjList[i].id == '88020041152678' || this.countyObjList[i].id == '88020360015762' || this.countyObjList[i].id == '88021088781110' || this.countyObjList[i].id == '88021088778966' || this.countyObjList[i].id == '88021089325506' || this.countyObjList[i].id == '88021089323054' || this.countyObjList[i].id == '88021089318250' || this.countyObjList[i].id == '88021089196266' || this.countyObjList[i].id == '88021058080998' || this.countyObjList[i].id == '88021089181934' || this.countyObjList[i].id == '88021089178670') {
                            this.countyObjList.splice(i, 1)
                        }
                    }
                } else {
                    this.$alert(res.data.retMsg || '查询区县列表失败')
                }
            }).catch(res => {
                this.$alert('查询区县列表网络异常:' + res)
            })
        },
        changeCounty0() {
            NlDropdown({
                confirmBtn: false,
                datalist: [
                    { 'id': '11', 'label': '苏州'}, //苏州
                    { 'id': '12', 'label': '淮安'}, //淮安
                    { 'id': '13', 'label': '宿迁'}, //宿迁
                    { 'id': '14', 'label': '南京'}, //南京
                    { 'id': '15', 'label': '连云港'}, //连云港
                    { 'id': '16', 'label': '徐州'}, //徐州
                    { 'id': '17', 'label': '常州'}, //常州
                    { 'id': '18', 'label': '镇江'}, //镇江
                    { 'id': '19', 'label': '无锡'}, //无锡
                    { 'id': '20', 'label': '南通'}, //南通
                    { 'id': '21', 'label': '泰州'}, //泰州
                    { 'id': '22', 'label': '盐城'}, //盐城
                    { 'id': '23', 'label': '扬州'}, //扬州
                ]
            }, (retVal) => {
                this.belongRegion = retVal
                this.custName = {}
                this.panduanHas()
                this.qryCountyList(this.belongRegion.id)
                this.getStreetList(this.belongRegion.id) // 地市列表
                this.cancelCounty()

            })
        },
        changeCounty() {
            this.changeCountyFlg = true
            this.chooseQX1 = this.custName.countryId
            this.chooseQX11 = this.custName.countryName
            this.chooseQX2 = this.custName.gridId
            this.chooseQX22 = this.custName.gridName
            this.chooseQX3 = this.custName.townId
            this.chooseQX33 = this.custName.townName
            for (let i = 0 ; i < this.countyObjListNew.length;i++) {
                if (this.custName.countryId == this.countyObjListNew[i].countryId) {
                    this.countyObjListNew2 = this.countyObjListNew[i].townList

                }
            }
            console.log('this.chooseQX3',this.chooseQX3)

            return
            NlDropdown({
                confirmBtn: false,
                datalist: this.countyObjList
            }, (retVal) => {
                this.custName = retVal

            })
        },
        clearimg(idx) {
            this.thisvideo.splice(idx,1)
            this.thebase64.splice(idx,1)
        },
        setupWebViewJavascriptBridge(callback) {
            return
            if (window.WebViewJavascriptBridge) {
                // eslint-disable-next-line no-undef
                return callback(WebViewJavascriptBridge);
            }
            if (window.WVJBCallbacks) {
                return window.WVJBCallbacks.push(callback);
            }
            window.WVJBCallbacks = [callback];
            const WVJBIframe = document.createElement("iframe");
            WVJBIframe.style.display = "none";
            WVJBIframe.src = "wvjbscheme://__BRIDGE_LOADED__";
            document.documentElement.appendChild(WVJBIframe);
            setTimeout(function() {
                document.documentElement.removeChild(WVJBIframe);
            }, 0)
        }
    },
    created() {
        this.userinfo = Storage.session.get('userInfo')
        this.routeBillid = this.$route.query.billid
        this.routeBuopId = this.$route.query.buopId
        this.showbtn = this.$route.query.showbtn == 1
        this.add = this.$route.query.add
        this.buchong = this.$route.query.buchong
        this.onlyKey = new Date().getTime()
        this.editType = this.$route.query.editType
        if (this.editType == 'add') {
            let arr =  [
                { 'region': '11', 'id': '11' , 'label': '苏州'}, //苏州
                { 'region': '12', 'id': '12' , 'label': '淮安'}, //淮安
                { 'region': '13', 'id': '13' , 'label': '宿迁'}, //宿迁
                { 'region': '14', 'id': '14' , 'label': '南京'}, //南京
                { 'region': '15', 'id': '15' , 'label': '连云港'}, //连云港
                { 'region': '16', 'id': '16' , 'label': '徐州'}, //徐州
                { 'region': '17', 'id': '17' , 'label': '常州'}, //常州
                { 'region': '18', 'id': '18' , 'label': '镇江'}, //镇江
                { 'region': '19', 'id': '19' , 'label': '无锡'}, //无锡
                { 'region': '20', 'id': '20' , 'label': '南通'}, //南通
                { 'region': '21', 'id': '21' , 'label': '泰州'}, //泰州
                { 'region': '22', 'id': '22' , 'label': '盐城'}, //盐城
                { 'region': '23', 'id': '23' , 'label': '扬州'}, //扬州
                { 'region': '99', 'id': '99' , 'label': '南京'}, //扬州
            ]
            this.belongRegion = {
                label : '',
                id: this.userinfo.region == 99 ? 14 : this.userinfo.region
            }
            for (let i = 0 ;i < arr.length;i++){
                if (arr[i].region== this.userinfo.region){
                    this.belongRegion.label = arr[i].label
                }
            }
            this.qryCountyList(this.belongRegion.id) // 地市列表
            this.getStreetList(this.belongRegion.id) // 地市列表

        }


        // this.qryCountyList(this.userinfo.region)
        this.getHis()


        if(this.$route.query.qrCode) {
            this.telNum = this.$route.query.phoneNum
            this.inputNum()
        }
        if (this.showbtn && this.add != 1 && this.buchong != 1) {
            let item = JSON.parse(this.$route.query.item)
            if (item.followTel == this.userinfo.servNumber) {
                this.canTel = true
            }
        }

    },
    mounted() {
        this.rec = new RecorderManger({}, this.recwave, this.getRecordResultCbFn)
        document.addEventListener('visibilitychange', function() {
            let vState = document.visibilityState
            if (window.location.href.indexOf('PotentialuserMaintenance') >= 0) {
                if (vState === 'hidden') {  // 当页面由前端运行在后端时，出发此代码
                    if (that.rec) {
                        that.rec && that.rec.recorder.close()
                        that.rec = null
                        that.luyinFlag = false
                    }
                }
            }
        })


        const that = this
        try {
            this.setupWebViewJavascriptBridge(function(bridge) {
                //iOS客户端-选择图片回调--掌厅拍照
                bridge.registerHandler('onGetImage1', function(data, responseCallback) {
                    let info = JSON.parse(data).result + ''
                    if (info) {
                        let info2 = info.split(',')[1];
                        console.log('ios选择图片回调:' +info2 )
                        if (that.thebase64.length < 3) {
                            that.thebase64.push(info2)
                            that.OCRfun(info2)
                        } else {
                            that.$toast('最多可拍三张')
                        }
                    }
                })


                //iOS客户端-选择图片回调2--身份证
                bridge.registerHandler('onGetImage2', function(data, responseCallback) {
                    let info = JSON.parse(data).result + ''

                    if (info) {
                        let info2 = info.split(',')[1];
                        console.log('ios选择图片回调:' +info2 )
                        that.getImgInfo2(info2)
                        that.showDalog = false
                    }
                })
            })



            //Android-选择图片回调--掌厅拍照
            window.accessPhotoCallback = function(info) {
                if (info.result[0]) {
                    info.result[0] = info.result[0].split(',')[1];
                    console.log('android选择图片回调:' + JSON.stringify(info))
                    if (that.thebase64.length < 3) {
                        that.thebase64.push(info.result[0])
                        that.OCRfun(info.result[0])
                    } else {
                        that.$toast('最多可拍三张')
                    }
                }
            }
            //Android-选择图片回调2--身份证
            window.accessPhotoCallback2 = function(info) {
                console.log('android选择图片回调:' + JSON.stringify(info))
                if (info.result[0]) {
                    info.result[0] = info.result[0].split(',')[1];
                    console.log('android选择图片回调:' + JSON.stringify(info))

                    that.getImgInfo2(info.result[0])
                    that.showDalog = false
                }
            }
        } catch (error) {
            console.error(error)
        }
    }

}
</script>

<style scoped lang='less'>
.qz-wrap {
    padding: 44px 0 80px;
    box-sizing: border-box;
    height: 100vh;
    width: 100vw;
    background: #F2F2F2;
    overflow: auto;
    z-index: 999999;

    .content {
        padding: 0px 10px 64px;
        box-sizing: border-box;

    }

    .top-tel {
        width: 100%;
        line-height: 44px;
        background: #daebff;
        color: #0b7ffe;
        font-size: 16px;
        text-align: center;
        font-weight: 700;

        a {
            color: #0b7ffe;
            text-decoration: none;
        }

        .dianhua013x {
            margin-right: 5px;
        }
    }

    .tab-change {
        display: flex;

        .tab-item {
            flex: 1;
            text-align: center;
            font-size: 14px;
            line-height: 38px;
            border-bottom: 1px solid #bbb;

            &.active {
                color: #0b7ffe;
                font-weight: 700;
                border-bottom: 1px solid #0b7ffe;
            }
        }
    }

}

.fadd-info {
    box-sizing: border-box;
    padding: 0 15px;

    h2 {
        img {
            width: 14px;
            height: 14px;
            display: inline-block;
            vertical-align: -2px;
            margin-right: 4px;
        }

        color: #3A3A3A;
        font-size: 14px;
    }

    .fa-tips {
        font-weight: normal;
        margin-top: 8px;
        font-size: 12px;
        color: red;
        line-height: 14px;
        display: block;
    }


    .add-item2 {
        line-height: 40px;
        text-align: center;
        position: relative;

        .button {
            width: 80px;
            height: 40px;
            text-align: center;
            color: #fff;
            display: inline-block;
            border-radius: 6px;
            background: #0b7fff;
            margin: 5px 10px;
            overflow: hidden;

            .iconfont {
                font-size: 18px;
            }

            .recwave-box {
                width: 80px;
                height: 48px;
            }
        }
    }

    .add-itemtel {
        padding-right: 25px;
        box-sizing: border-box;

        .dianhua {
            font-size: 20px !important;
            right: 0;
            top: 0px;
        }

        .mini {
        }
    }

    .add-itemdizhi {
        border-top: 1px solid #e5e4e4;
    }

    .add-item {
        height: 40px;
        line-height: 40px;
        //border-bottom: 1px solid #F0F0F0;
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        font-size: 12px;

        .zhangting {
            color: #bbb;
            position: absolute;
            font-size: 12px;
            right: 15px;
            bottom: 0;
        }

        .mini {
            color: #0b7ffe;
            position: absolute;
            font-size: 12px;
            right: 0;
            bottom: 0;
        }

        .dianhua {
            color: #0b7ffe;
            font-size: 20px !important;

        }

        .dianhuaa {
            font-size: 20px !important;
            outline: none;
            right: 0;
            top: 0px;
            text-decoration: 0;
            vertical-align: bottom;
        }

        .add-left {
            color: #232323;
            font-size: 14px;
            position: relative;
            .small-font {
                color: #999;
                font-size: 10px;
                position: absolute;
                left: 0;
                bottom: -16px;
                width: 140px;

            }
            .small-font2 {
                color: #999;
                font-size: 10px;
            }
        }

        .add-input {
            font-size: 12px;
            color: #232323;
            outline: none;
            text-align: right;

            &::placeholder {
                color: #BBBBBB;

            }
        }

        .add-input2 {
            padding-right: 120px;
        }

        .add-right {
            flex: 1;
            text-align: right;
            color: #232323;

            i {
                color: #CBCBCB;
                font-size: 14px;
                vertical-align: middle;
                display: inline-block;
            }
        }

        .add-right2 {
            text-align: right;
            display: inline-block;
            color: #232323;

            i {
                color: #CBCBCB;
                font-size: 14px;
                vertical-align: middle;
                display: inline-block;
            }

            .luyin {
                font-size: 12px;
                color: #0b7ffe;
            }
        }
    }
}

.pop-textarea {
    width: 100%;
    resize: none;
    height: 100px;
    line-height: 22px;
    font-size: 12px;
    border: 1px solid #bbb;
    padding: 5px;
    box-sizing: border-box;
    border-radius: 4px;
    outline: none;
}

.finfo-item {
    margin-top: 14px;

    .fitem-p {
        width: 100%;
        line-height: 36px;
        height: 36px;
        background: linear-gradient(270deg, #FFFFFF 0%, #EBEFFF 50%, #FFFFFF 100%);
        border-radius: 4px;
        font-size: 14px;
        color: #3A3A3A;
        text-align: center;
        font-weight: bold;

        i {
            color: #8F8F8F;
            font-size: 22px;
            display: inline-block;
            margin-right: 4px;
            vertical-align: -2px;
        }
    }

    .fitem-list {
        display: flex;
    }

    .fitem-sub {
        margin-top: 14px;
        flex: 1;
        text-align: center;

        .sub-input {
            background-size: 47px 23px !important;
            width: 84px;
            text-align: center;
            height: 26px;
            border-radius: 12px;
            font-size: 14px;
            outline: none;
        }

        .sub-text {
            margin-top: 4px;
            color: #3A3A3A;
            font-size: 14px;
            display: block;
        }

        .textarea-info {
            width: 88%;
            height: 90px;
            resize: none;
            border: none;
            outline: none;
            background: rgb(249, 249, 249);
            font-size: 14px;
            padding: 6px 10px;
            overflow: auto;
        }
    }
}

.op-button-box {
    background-color: #fff;
    text-align: center;
    padding: 10px 12px;
    display: flex;
    justify-content: space-between;
    position: fixed;
    bottom: 0;
    left: 0;
    background: #fff;
    width: 100vw;
    box-sizing: border-box;
    box-shadow: 0px 0px 7px 5px rgba(0, 0, 0, 0.2);
    z-index: 333;


    .op-button {
        border-radius: 22px;
        height: 44px;
        line-height: 44px;
        flex: 1;
        font-size: 14px;
        outline: none;
        border: none;
        background: rgba(204, 204, 204, 1);
        color: #fff;
        margin: 0 10px;

        &.active {
            background: #1681FB;
            color: #fff;
        }
    }
}

.dialog-box {
    position: fixed;
    top: -300px;
    left: 50%;
    transform: translateX(-50%);
    width: 70vw;
    padding: 40px 20px;
    box-sizing: border-box;
    background: #f5f6f6;
    border-radius: 8px;
    text-align: center;
    z-index: 777;

    img {
        width: 85%;

        margin-bottom: 30px;
    }

    div {
        font-size: 12px;
        color: #ccc;
    }

}

.shibie {
    padding: 6px 4px;
    border-radius: 4px;
    background: #0b7fff;
    color: #fff;
    font-size: 11px;

    .luyin {
        color: #fff;


    }

    .zu {
        font-size: 12px !important;
    }

    &.grey-btn {
        background: #929292;
        color: #0A0A0A;
    }

}

.chooseDialog {
    position: fixed;
    z-index: 99;
    background: #fff;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    padding: 12px;
    border-radius: 8px;
    box-shadow: 2px 2px 6px 0px #747576;
    width: 260px;

    .title {
        font-size: 18px;
        line-height: 26px;
        color: #0b7fff;
        border-bottom: 2px solid #1681FB;
        overflow: hidden;

        .guanbi {
            float: right;
            color: red;
            font-size: 20px;
            line-height: 26px;

        }
    }

    .info {
        font-size: 14px;
        line-height: 22px;
        margin-top: 8px;
    }

    .btnlist {
        margin-top: 8px;
        display: unset !important;
        width: 100%;

        div {
            display: block;
            background: #1681FB;
            text-align: center;
            padding: 12px 15px;
            box-sizing: border-box;
            margin-top: 10px;
            border-radius: 5px;
            color: #fff;
            font-size: 15px;
            width: 100%;
        }
    }
}

.user-tag {
    .title {
        color: #0b7ffe;
        font-size: 16px;
        font-weight: 700;
    }

    .add-left {
        color: #232323;
        font-size: 12px;

    }

    .tasg {
        display: flex;

        .tag-item {
            font-size: 10px;
            background: #FFD9AE;
            color: #ff8b19;
            border-radius: 3px;
            margin: 0 2px;
            line-height: 20px;
            padding: 0 5px;


        }
    }
}

.title1 {
    margin-top: 10px;
    font-size: 14px;
    .showmore {
        font-size: 12px;
        float: right;
        color: #0b7fff;
        .youjiantou2 {
            vertical-align: text-bottom;
        }
    }
}

.table-box {
    padding: 12px 0;
    box-sizing: border-box;

    margin-bottom: 50px;

    .table-wrap {
        width: 100%;
        font-size: 12px;
        text-align: left;
    }

    .head-line {
        height: 30px;
        vertical-align: middle;
        line-height: 30px;
        background-color: #f3f4f9;
    }

    .type-css {
        padding-left: 30px;
    }

    thead {
        text-align: center;

    }

    .table-body tr {
        height: 30px;
        line-height: 30px;
        font-size: 14px;
        text-align: center;

        th {

        }

        td:first-child {
            color: #38393a;


        }

        td {
            color: #0f87ff;

            .youjiantou {
                font-size: 10px;
            }
        }
    }

    .table-body tr:nth-child(2n) {
        background-color: #f3f4f9;
    }
}

.right-pai {
    position: absolute;
    right: 30px;
    color: #0b7ffe;
}

.hasvideo {
    color: #0b7ffe !important;
}

.huaw {
    display: flex;
    width: 100%;
    box-sizing: border-box;

    .item {
        text-align: center;
        display: inline-block;
        width: calc(33% - 6px);
        margin: 6px 3px;
        background: #f2f2f2;
        padding: 6px;
        border-radius: 6px;
        box-sizing: border-box;

        .title, .value {
            font-size: 14px;
            line-height: 26px;
        }
    }
}

.content-item {
    margin-top: 10px;
    width: 100%;
    box-sizing: border-box;
    border-radius: 8px;
    margin-bottom: 20px;
    //box-shadow:4px 4px 12px 0px rgba(184,184,184,0.5);
    background: #fff;

    .first-title {
        border-radius: 8px 8px 0 0px;
        height: 36px;

        //background: linear-gradient(to bottom left,#D2E8FF ,#fff);

        .left-info {
            float: left;
            font-size: 15px;
            font-weight: 700;
            line-height: 30px;
            padding: 3px 10px;
            box-sizing: border-box;
            height: 36px;
            color: #257CDB;

            img {
                vertical-align: middle;
                display: inline-block;
                height: 22px;
            }

        }

        .right-info {
            float: right;
            padding: 3px 10px 3px 0;
            line-height: 30px;
            font-style: italic;
            color: #a1c7f1;
            font-weight: 700;
            font-size: 18px;

            .iconfont {
                color: #257CDB !important;
            }
        }
    }

    .more-line {
        box-sizing: border-box;
        border-radius: 0px 0px 8px 8px;
        padding-bottom: 15px;
        overflow: hidden;
        .fadd-info {
            box-sizing: border-box;
            padding: 0 15px;

        }
    }
}

.mint-switch {
    transform: scale(0.8);
}

.img-box {
    overflow: hidden;
    position: relative;
    width: calc(33% - 10px);
    display: inline-block;
    margin: 0 5px;
    float: right;
    box-shadow: 2px 2px 6px 0px rgba(0, 0, 0, 0.2);

    .guanbi2 {
        position: absolute;
        right: 5px;
        top: 5px;
        color: red;
    }

    img {
        width: 120px;
        height: 80px;
        float: right;
    }
}

.reserve3-box {
    position: relative;

    .num {
        font-size: 12px;
        position: absolute;
        right: 5px;
        bottom: 8px;
        color: #bbb;
    }

    .info {
        display: inline-block;
        padding: 5px;
        box-sizing: border-box;
        font-size: 12px;
        line-height: 20px;
        border: 1px solid #c6c5c5;
        width: 100%;
        border-radius: 6px;

    }
}

.big-img {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: #fff;
    z-index: 99999999999;
    overflow: auto;

    img {
        width: 100%;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);

    }

    .guanbi2 {
        position: absolute;
        right: 15px;
        top: 15px;
        font-size: 24px;
        color: red;
        z-index: 99999;
    }
}

/deep/ .mint-switch-input:checked + .mint-switch-core {
    border-color: #0b7fff !important;
    background-color: #0b7fff !important;
}

.shibie .zu {
    font-size: 10px !important;
    vertical-align: middle;
    margin-top: -3px;
}

.dislog {
    position: fixed;
    width: 100%;
    padding: 6px 0 0;
    bottom: 0;
    left: 0;
    z-index: 555;
    background: #fff;
    text-align: center;
    box-shadow: 0px 0px 7px 5px rgba(0, 0, 0, 0.02);
    margin-top: -40%;

    .title {
        font-size: 14px;
        font-weight: 700;
        line-height: 34px;

    }

    .reson-box {
        margin-top: 5px;

        .chooseone {
            width: 100%;
            font-size: 14px;
            line-height: 32px;
            position: relative;
            color: #929292;
            text-align: left;
            padding: 0 30px;
            box-sizing: border-box;

            .duihao1 {
                position: absolute;
                top: -1px;
                color: green;
            }
        }

        .yuan {
            width: 14px;
            height: 14px;
            display: inline-block;
            border-radius: 100%;
            vertical-align: middle;
            border: 1px solid #929292;
            margin-right: 5px;
            text-align: left;
        }
    }

    .reson-box2 {
        display: flex;
        margin-top: 25px;
        padding: 0 15px;
        margin-bottom: 25px;

        .chooseone {
            flex: 1;
            font-size: 16px;
            line-height: 32px;
            position: relative;
            line-height: 36px;
            background: #0b7ffe;
            color: #fff;
            border-radius: 6px;

            &:first-child {
                border-right: 1px solid #bbb;
            }

            &:last-child {
                color: #fff;
            }
        }
    }
}

.hide-box {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, .5);
    z-index: 444;
}

.tip {
    color: red;
    position: absolute;
    font-size: 10px;
    right: 0;
    bottom: -3px;
    line-height: 15px;
}

.phone1 {
    padding-right: 100px;
}

.sj-lists {
    width: 100%;
    padding-left: 15px;
    box-sizing: border-box;
    font-size: 12px;
    line-height: 24px;
    margin-top: 10px;

    .item {
        position: relative;

        &::before {
            content: '';
            width: 5px !important;
            height: 5px !important;
            position: absolute;
            background: #fff;
            border: 3px solid #0b7ffe;
            border-radius: 100%;
            left: -15px;
            top: 6px;
            z-index: 222;
        }

        &::after {
            content: '';
            width: 1px !important;
            height: 90% !important;
            position: absolute;
            background: #bbb;
            left: -10px;
            top: 6px;
            z-index: 222;
            border: 0;

        }
    }

}

.sj-lists:first-child {
    &::after {
        content: '';
        width: 0px !important;
        height: 90% !important;
        position: absolute;
        background: #bbb;
        left: -10px;
        top: 6px;
        z-index: 222;
        border: 0;

    }
}

.box-img {

}
.left-info2,.add-item {
    position: relative;
    font-size: 12px  !important;
    color: red !important;
    font-weight: 400  !important;
    .wenhao1 {
        font-size: 14px;
        margin-left: 0px;
        vertical-align: bottom;
        color: #747474;
    }
    .dialog-info {
        position: absolute;
        font-size: 12px;
        width: calc(100vw - 50px);
        left: 0px;
        top: 36px;
        box-sizing: border-box;
        background: #c0deff;
        padding: 10px;
        border-radius: 7px;
        z-index: 99;
        line-height: 20px;
        color: #02337f;
        font-weight: 400;
        box-shadow: 2px 2px 6px 0px rgba(0, 0, 0, 0.2);

    }
    .dialog-info::after {
        content: '';
        position: absolute;
        width: 0;
        height: 0;
        border-style: solid;
        border-width:0 6px 12px 6px;
        border-color: transparent transparent  #c0deff transparent;
        top: -9px;
        left: 58px;
    }

}
.add-item-tel.add-item {
    color: #000 !important;
}
.hide-box {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, .5);
    z-index: 444;
}
.task-search-box {
    width: 100%;
    height: 360px;
    padding: 15px 0;
    overflow: hidden;
    background: #fff;
    position: fixed;
    left: 0;
    bottom: 0px;
    border: 1px solid #E5E5E5;
    z-index: 99999988;
    box-sizing: border-box;

    .top {
        width: 100%;
        height: 280px;
        overflow: hidden;
        display: flex;

        .lie {
            flex: 1;
            border-right: 1px solid #eee;
            height: 280px;
            box-sizing: border-box;
            padding: 0 5px;
            overflow: auto;

            .choose-item {
                font-size: 14px;
                padding: 0 15px 0 5px;
                color: #888;
                overflow: hidden;
                position: relative;

                margin: 5px 0;

                .font-wenzi {
                    width: calc(100% - 7px);
                    //overflow: hidden;
                    //white-space: nowrap;
                    //text-overflow: ellipsis;
                    display: inline-block;
                    line-height: 36px;
                    white-space: nowrap;
                    overflow: auto;
                }

                .font-wenzi3 {
                    width: 100%;
                }

                .youjiantou1 {
                    font-size: 16px;
                    line-height: 36px;
                    float: right;
                    width: 16px;
                    position: absolute;
                    right: 5px;
                    top: 50%;
                    margin-top: -2px;
                    transform: translateY(-50%);
                }
            }

            .active {
                background: #D9EBFF;
                color: #0b7fff;
                border-radius: 4px;

                .font-wenzi {
                    color: #0b7fff;

                }
            }
        }

        .lie:first-child {
            flex: unset;
            width: 33%;
        }

        .lie:last-child {
            border: none;
        }

    }

    .bottom {
        width: 100%;
        margin-top: 11px;
        text-align: center;
        display: flex;
        div {
            flex: 1;
        }
        .chongzhi {
            display: inline-block;
            margin: 5px 25px;
            line-height: 32px;
            border-radius: 32px;
            background: #fff;
            border: 2px solid #0b7fff;
            box-sizing: border-box;
            color: #0b7fff;
            font-size: 14px;
        }

        .queren {
            display: inline-block;
            margin: 5px 25px;
            line-height: 36px;
            border-radius: 36px;
            background: #0b7fff;
            color: #fff;
            font-size: 14px;

        }
    }
}
</style>
