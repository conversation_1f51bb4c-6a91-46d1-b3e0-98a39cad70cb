<template>
  <div v-if="value">
    <div class='menu-dialog-bg'>
      <div class='menu-box' v-show="!feedbackShow">
        <div class='send-message'>
          <div class='content'>{{ sendMessageTxt }}</div>
        </div>
        <img src='static/img/be-sure.png' alt='' class='h-s' />
        <ul>
          <li v-for='(item,index) in maMenuList' :key='index' @click='switchMenu(item)'>
            <img src='static/img/ai-l-0.png' alt=''>
            <span>{{ item.sceneName || item.privName }}</span>
          </li>
        </ul>
        <div class="feedback-btn" @click='feedbackShow = true'>
          我要反馈
          <i class="iconfont pinglun3"></i>
        </div>
        <div class='close-btn' @click='valueChange'>关闭</div>
      </div>
      <div v-show="feedbackShow" class='feedback-box'>
        <img src="../../../assets/img/aiGroup/float/feedback-top.png" />
        <div class='feedback-content'>
          <div class="title">我的问题</div>
          <div class='send-message'>
            <div class='content'>{{ sendMessageTxt }}</div>
          </div>
          <div class="title">助手的回答</div>
          <div class="menu-list">
            <div class="menu-item" v-for="(item,index) in maMenuList" :key="index">
              <div class="text-007AFF">{{ item.sceneName || item.privName }}</div>
              <div>
                <i :class="item.feedbackType==1 ? 'text-007AFF' : ''" @click="feedbackChange(index,1)"
                   class="iconfont xingzhuangww rotate-icon"></i>
                <i :class="item.feedbackType==0 ? 'text-007AFF' : ''" @click="feedbackChange(index,0)"
                   class="iconfont xingzhuangww"></i>
              </div>
            </div>
          </div>
        </div>
        <div class="handle-box">
          <div @click="submitBtn(0)">取消</div>
          <div class='right-btn text-007AFF' @click="submitBtn(1)">提交</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { h5MenuSearchFeedBack } from '../../request'
import Storage from '@/base/storage'

export default {
  name: 'menuDialog',
  components: {},
  props: {
    // 控制弹框显示隐藏
    value: {
      type: Boolean,
      default: false
    },
    // 发送的文字
    sendMessageTxt: {
      type: String,
      default: ''
    },
    // 推荐列表
    maMenuList: {
      type: Array,
      default: () => ([])
    }
  },
  data() {
    return {
      feedbackShow: false // 反馈盒子
    }
  },
  computed: {},
  methods: {
    switchMenu(item) {
      this.$emit('switchMenu', item)
    },
    valueChange() {
      this.$emit('input', false)
    },
    feedbackChange(index, type) {
      let cType = this.maMenuList[index]['feedbackType']
      // 取消反馈点击
      if (cType == type) {
        this.maMenuList[index]['feedbackType'] = null
      } else {
        this.maMenuList[index]['feedbackType'] = type
      }
      this.$forceUpdate()
    },
    async submitBtn(flag) {
      if (flag) {
        let userInfo = Storage.session.get('userInfo')
        let menuList = this.maMenuList.filter(item => item.feedbackType==0 || item.feedbackType==1).map(item => {
          return {
            menuId: item.sceneId || item.privId,
            menuName: item.sceneName || item.privName,
            menuType: item.type,
            feedbackType: item.feedbackType
          }
        })
        let params = {
          operMsisdn: userInfo.servNumber,
          keyWord: this.sendMessageTxt,
          regionId: userInfo.region,
          menuList
        }
        console.info('菜单反馈请求参数：', params)
        const res = await h5MenuSearchFeedBack(params)
        if (res) {
          console.info('菜单反馈返回：', res)
          this.$toast({ message: `反馈成功！`, className: 'ai-toast-o-r', duration: 1000 })
        } else {
          return
        }
      }
      this.maMenuList.forEach(item => {
        item.feedbackType = null
      })
      this.feedbackShow = false
    }
  },
  async mounted() {

  }
}
</script>


<style scoped lang="less">
.menu-dialog-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.5) 90%, rgba(0, 0, 0, 0) 100%);
  z-index: 1000;

  .menu-box, .feedback-box {
    position: fixed;
    left: 0;
    box-sizing: border-box;
    font-size: 14px;
  }

  .send-message {

    max-width: 90%;
    width: fit-content;

    .content {
      padding: 8px 13px;
      line-height: 19px;
      word-break: break-word;
      background: #007AFF;
      color: white;
      border-radius: 28px;
    }
  }

  .menu-box {
    width: 100%;
    padding: 0 23px;
    bottom: 100px;

    .send-message {
      margin-left: auto;
    }

    .h-s {
      height: 15px;
      margin: 23px 0 15px 3px;
    }

    ul {

      li {
        background: #fff;
        width: fit-content;
        border-radius: 28px;
        padding: 6px 13px;
        margin-bottom: 8px;
        font-size: 14px;
        color: #1A72D1;
        line-height: 19px;

        img {
          width: 18px;
        }
      }
    }

    .close-btn {
      width: 80px;
      height: 33px;
      background: #3B414A;
      border-radius: 24px;
      font-size: 14px;
      color: #FFFFFF;
      line-height: 33px;
      text-align: center;
      margin: 28px auto 0;
    }

    .feedback-btn {
      font-size: 12px;
      border-radius: 136px;
      border: 1px solid #FFFFFF;
      color: #FFFFFF;
      display: inline-block;
      padding: 4px 10px;
      margin-top: 5px;

      .pinglun3 {
        font-size: 10px;
      }
    }
  }

  .feedback-box {
    bottom: 30px;
    background: #FFFFFF;
    margin: 0 20px;
    border-radius: 16px;

    img {
      width: 100%;
      position: relative;
      top: -26px;
    }

    .feedback-content {
      position: relative;
      top: -10px;
      padding: 0 20px;

      .title {
        font-weight: 600;
        padding-bottom: 10px;
      }

      .send-message {
        margin-bottom: 20px;
      }

      .menu-list {
        max-height: 100px;
        overflow-y: auto;

        .menu-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          line-height: 1.6;

          .rotate-icon {
            transform: rotate(180deg);
            display: inline-block;
            margin-right: 10px;
          }
        }
      }
    }

    .handle-box {
      display: flex;
      text-align: center;
      border-top: 1px solid #E1E1E1;

      div {
        padding: 16px 0;
        width: 50%;
        font-weight: 600;
      }

      .right-btn {
        border-left: 1px solid #E1E1E1;
      }
    }
  }
}

.text-007AFF {
  color: #007AFF;
}

.ai-toast-o-r {
  z-index: 999999;
}
</style>

