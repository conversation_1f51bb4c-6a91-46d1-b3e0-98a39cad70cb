<template>
    <div class='input-area'>
        <div class='gif' v-show='!inputSwitch&&isRecording&&vioceTxt'>
            {{ vioceTxt }}
        </div>
        <div class='ai' v-show='!inputSwitch'>
            <i class="iconfont ai-keyboard" @click='inputSwitch = true'></i>
            <div class='glass-bg ' :class='{"is-recording": isRecording}'>
                <div
                    class='speak'
                    @touchstart.prevent='startRecording'
                    @touchmove.prevent='handleMove'
                    @touchend.prevent='stopRecording'
                >
                    <div class='box-recording' v-show='isRecording'>
                        <div class="text-recording">
                            <div v-show="!displayResult.length" class='loading-txt' v-if='!displayResult.length'>
                                请勿松手，正在识别中...
                                <span class='iconfont jiazai'></span>
                            </div>
                            <div class="text-white" v-html="displayResult"></div>
                        </div>
                        <div class="btn-recording"></div>
                    </div>
                    <div class='speak-s'>
                        <i class="iconfont yuyin"></i>
                        {{ buttonText }}
                    </div>
                </div>
            </div>
        </div>
        <!-- 键盘-->
        <div class='foot-input' v-show='inputSwitch'>
            <em class='iconfont luyin' @click='inputSwitch = false'></em>
            <textarea
                v-model='inputText'
                placeholder='请输入...'
                @keyup.enter.exact.prevent='sendMessage(inputText)'
                ref='textareaRef'
                rows='1'
            />
            <div class='send' :class='{"active":haveContentFlg}' @click='sendMessage(inputText)'>
                <em class='iconfont lujing23r'></em>
            </div>
        </div>
    </div>
</template>
<script>
import Recorder from 'recorder-core'
import 'recorder-core/src/engine/mp3'
import 'recorder-core/src/engine/mp3-engine'
import 'recorder-core/src/extensions/waveview'
import Storage from '@/base/storage'
import { Toast as showToast } from 'mint-ui'

export default {
    name: 'aiBottomCom',
    components: {},
    model: {
        prop: 'inputText',
        event: 'change'
    },
    props: {
        placeholder: {
            type: String,
            default: '请输入...'
        },
        // 输入文字
        inputText: {
            type: String,
            default: ''
        },
        // 输入/文字转化
        inputSwitch: {
            type: Boolean,
            default: false
        },
        // 是否正在加载
        loading: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            vioceTxt: '',
            isRecognizing: false,// 是否打开录音权限
            recorder: null,
            socket: null,
            wave: null,
            prodPath: '/tuling-proxy-v3',
            // 音频参数
            requestParams: {
                aue: 'raw',
                rst: 'json',
                rse: 'utf8',
                domain: 'iat',
                eos: '16000',
                rate: 16,
                dwa: 'wpgs'     // 动态修正
            },
            // 自定义参数
            customParams: [],
            webUrl: Storage.get('webUrl'),
            // 识别结果
            resultText: '',       // 最终识别文本
            pendingText: '',       // 最终识别文本
            displayResult: '',    // 用于显示的结果（带临时标记）
            lastFinalText: '',    // 上次确认的最终文本
            isRecording: false, // 是否正在说话
            currentPos: null,// 当前触摸位置
            buttonRect: null,// 按钮位置信息
            // WPGS高级修正相关
            resultSegments: [],   // 存储每次返回的结果片段，用于rg范围替换
            segmentCounter: 0,   // 结果片段计数器
            confirmedText: '',    // 单次录音中已确认的所有文本片段
            // 60秒自动断开相关
            recordingTimer: null, // 录音计时器
            maxRecordingTime:  60 * 1000 // 最大录音时间60秒（毫秒）
        }
    },
    computed: {
        shouldCancel() {
            if (!this.currentPos || !this.buttonRect) return false
            // 计算触点是否超出按钮区域
            return !this.isInButtonArea(this.currentPos)
        },
        buttonText() {
            if (this.isRecording) {
                return this.shouldCancel ? '松开取消' : '正在说话'
            } else {
                return '长按说话'
            }
        },
        haveContentFlg() {
            return this.inputText.trim().length > 0
        }
    },
    methods: {
        // 处理移动
        handleMove(event) {
            if (!this.isRecording) return
            this.currentPos = this.getEventPosition(event)
        },
        // 开始录音
        startRecording(event) {
            this.isRecording = true
            // 获取初始位置
            this.currentPos = this.getEventPosition(event)
            // 获取按钮位置信息
            this.buttonRect = event.target.getBoundingClientRect()
            // 实际录音逻辑
            this.startRecognize()
            // 启动60秒自动断开计时器
            this.startRecordingTimer()
        },
        // 结束录音
        stopRecording() {
            this.isRecording = false
            this.vioceTxt = ''
            // 清除60秒自动断开计时器
            this.clearRecordingTimer()
            console.info('录音识别结果', this.resultText)
            if (this.resultText && this.resultText.trim().length) {
                this.$emit('sendMessage', this.resultText)
            }
            // 立即清空所有录音相关状态，避免下次录音显示上次内容
            this.clearRecordingState()
            // 结束录音
            this.stopRecognize()
        },
        // 清空所有录音相关状态
        clearRecordingState() {
            // 清空文本相关状态
            this.resultText = ''
            this.displayResult = ''
            this.pendingText = ''
            this.lastFinalText = ''
            this.vioceTxt = ''

            // 清空WPGS相关状态
            this.confirmedText = ''
            this.resultSegments = []
            this.segmentCounter = 0

            // 清除计时器
            this.clearRecordingTimer()

            console.info('✅ 已清空所有录音状态')
        },
        // 判断是否在按钮区域内
        isInButtonArea(pos) {
            const { left, top, width, height } = this.buttonRect
            const buffer = 20 // 增加容错区域

            return (
                pos.x >= left - buffer &&
                pos.x <= left + width + buffer &&
                pos.y >= top - buffer &&
                pos.y <= top + height + buffer
            )
        },
        // 获取事件坐标
        getEventPosition(event) {
            if (event.touches) {
                return {
                    x: event.touches[0].clientX,
                    y: event.touches[0].clientY
                }
            } else {
                return {
                    x: event.clientX,
                    y: event.clientY
                }
            }
        },
        // 发送对话
        sendMessage(inputText) {
            this.$emit('sendMessage', inputText)
        },
        // 生成鉴权URL
        getWebSocketUrl() {
            // 正式地址
            if (!this.webUrl.length) {
                this.webUrl = Storage.get('webUrl')
            }
            console.info('当前中心地址', this.webUrl)
            // return 'wss://spare.jsalading.cn/tuling-proxy-v3'
            let url = this.webUrl.replace('https', 'wss')
            return url + this.prodPath
        },

        // 开始识别
        async startRecognize() {
            // 初始化录音对象
            if (!this.recorder || !this.isRecognizing) {
                this.initReCom()
            }
            this.recorder.start()
            try {
                // 重置所有录音相关状态
                this.clearRecordingState()
                // 初始化WebSocket
                const url = this.getWebSocketUrl()
                console.info('连接地址', url)
                this.socket = new WebSocket(url)
                console.info('this.socket: ', this.socket)
                this.socket.onopen = () => {
                    console.log('WebSocket连接成功')
                    // 发送开始参数
                    const startParams = {
                        sessionParam: {
                            endFlag: false
                        }
                    }
                    console.info('开始请求参数', startParams)
                    this.socket.send(JSON.stringify(startParams))
                }

                this.socket.onmessage = (event) => {
                    const response = JSON.parse(event.data)
                    this.processResponse(response)
                }

                this.socket.onclose = (event) => {
                    console.log('WebSocket已关闭', event)
                }

                this.socket.onerror = (error) => {
                    console.error('WebSocket错误', error)
                }
                let isIos = !!navigator.userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) //ios终端
                if (isIos) {
                    console.info('ios震动方法调用')
                    if (window.webkit && window.webkit.messageHandlers) {
                        window.webkit.messageHandlers.SoundVibration.postMessage('1')
                    }
                } else {
                    console.info('Android震动方法调用')
                    if (window.WebViewFunc && window.WebViewFunc.SoundVibration) {
                        window.WebViewFunc.SoundVibration()
                    }
                }
            } catch (error) {
                console.error('初始化失败', error)
            }
        },
        initReCom() {
            this.recorder = Recorder({
                type: 'mp3',
                sampleRate: 16000,  // 必须与API要求一致
                bitRate: 16,
                onProcess: (buffers, powerLevel, bufferDuration, bufferSampleRate) => {
                    // 实时发送音频数据
                    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
                        this.sendAudioData(buffers[buffers.length - 1])
                    }
                }
            })
            // 打开麦克风
            this.recorder.open(() => {
                    console.log('麦克风权限已打开')
                    this.isRecognizing = true
                },
                (msg, isUserNotAllow) => {
                    //用户拒绝了录音权限，或者浏览器不支持录音
                    showToast({
                        className: 'screen-toast',
                        iconClass: 'iconfont gantanhao-yuankuang',
                        message: (isUserNotAllow ? '请先允许使用麦克风' : msg),
                        duration: 3000
                    })
                    console.error('麦克风权限打开失败', (isUserNotAllow ? '用户拒绝' : msg))
                    this.isRecognizing = false
                    if (this.socket) this.socket.close()
                }
            )
        },
        // 发送音频数据到WebSocket
        sendAudioData(buffer) {
            // 转换为PCM格式
            const pcmData = this.float32ToInt16(buffer)
            const base64Data = this.arrayBufferToBase64(pcmData.buffer)

            // 构建基础参数
            const sessionParam = this.requestParams
            // 添加自定义参数
            this.customParams.forEach(param => {
                if (param.key && param.value) {
                    sessionParam[param.key] = param.value
                }
            })

            // 发送数据（status=1表示中间数据）
            const ingParams = {
                endFlag: false,
                sessionParam: sessionParam,
                samples: base64Data
            }
            this.socket.send(JSON.stringify(ingParams))
        },
        // 重点修改：适配讯飞WPGS高级实时修正机制 - 支持pgs(apd/rpl)和rg范围替换
        processResponse(response) {
            console.info('🔄 接口返回', response)
            if (response.errorCode != 0) {
                console.error('❌ API错误:', response.errorMsg || '未知错误')
                return
            }
            if (!response.result) return

            try {
                const resultObj = JSON.parse(response.result)
                console.info('📋 识别结果详情', resultObj)

                // 提取当前片段的文本
                let currentSegment = ''
                if (resultObj.ws && Array.isArray(resultObj.ws)) {
                    resultObj.ws.forEach(wordSeg => {
                        if (wordSeg.cw && Array.isArray(wordSeg.cw)) {
                            wordSeg.cw.forEach(char => {
                                if (char.w) currentSegment += char.w
                            })
                        }
                    })
                }

                // WPGS高级修正处理
                if (resultObj.pgs) {
                    this.handleWpgsCorrection(resultObj, currentSegment)
                } else {
                    // 兼容原有逻辑（基于ls字段）
                    this.handleBasicCorrection(resultObj, currentSegment)
                }
                // 更新显示和结果
                this.updateDisplayAndResult()

            } catch (e) {
                console.error('❌ 解析结果失败', e, '原始result:', response.result)
            }
        },

        // 处理WPGS高级修正逻辑
        handleWpgsCorrection(resultObj, currentSegment) {
            this.segmentCounter++
            console.info(`🔢 片段计数: ${this.segmentCounter}`)

            if (resultObj.pgs === 'apd') {
                // 追加模式：直接追加到已确认文本
                this.confirmedText += currentSegment
                this.pendingText = '' // 清空未确认文本

                // 记录这个片段
                this.resultSegments.push({
                    index: this.segmentCounter,
                    text: currentSegment,
                    isConfirmed: true
                })

                console.info('➕ APD追加模式:', currentSegment)
                console.info('📝 累计确认文本:', this.confirmedText)

            } else if (resultObj.pgs === 'rpl') {
                // 替换模式：需要根据rg字段替换指定范围
                const replaceRange = resultObj.rg || []
                if (replaceRange.length === 2) {
                    const [startIndex, endIndex] = replaceRange
                    console.info(`🔄 RPL替换模式: 替换片段 ${startIndex}-${endIndex}`)

                    this.handleRangeReplacement(startIndex, endIndex, currentSegment)
                } else {
                    console.warn('⚠️ RPL模式但rg字段格式错误:', replaceRange)
                    // 降级为追加模式
                    this.confirmedText += currentSegment
                }

            } else {
                console.warn('⚠️ 未知pgs值:', resultObj.pgs)
                // 降级为基础处理
                this.handleBasicCorrection(resultObj, currentSegment)
            }
        },

        // 处理范围替换逻辑
        handleRangeReplacement(startIndex, endIndex, newText) {
            console.info(`🎯 执行范围替换: [${startIndex}, ${endIndex}] -> "${newText}"`)

            // 重新构建文本：保留替换范围外的片段，替换范围内的用新文本
            let newConfirmedText = ''
            let newSegments = []

            // 添加替换范围之前的片段
            this.resultSegments.forEach(segment => {
                if (segment.index < startIndex) {
                    newConfirmedText += segment.text
                    newSegments.push(segment)
                }
            })

            // 添加新的替换片段
            newConfirmedText += newText
            newSegments.push({
                index: this.segmentCounter,
                text: newText,
                isConfirmed: true
            })

            // 添加替换范围之后的片段
            this.resultSegments.forEach(segment => {
                if (segment.index > endIndex) {
                    newConfirmedText += segment.text
                    newSegments.push({ ...segment, index: segment.index - (endIndex - startIndex) })
                }
            })

            this.confirmedText = newConfirmedText
            this.resultSegments = newSegments
            this.pendingText = ''

            console.info('✅ 替换完成，新的确认文本:', this.confirmedText)
        },

        // 处理基础修正逻辑（兼容原有ls字段）
        handleBasicCorrection(resultObj, currentSegment) {
            if (resultObj.ls) {
                // 已确定结果：追加到已确认文本
                this.confirmedText += currentSegment
                this.pendingText = ''

                this.resultSegments.push({
                    index: ++this.segmentCounter,
                    text: currentSegment,
                    isConfirmed: true
                })

                console.info('✅ LS确认模式:', currentSegment)
            } else {
                // 未确定结果：替换未确认文本
                this.pendingText = currentSegment
                console.info('⏳ LS临时模式:', currentSegment)
            }
        },

        // 更新显示和结果文本
        updateDisplayAndResult() {
            // 实时显示：已确认文本 + 未确认文本（未确认用特殊样式标记）
            this.displayResult = this.confirmedText +
                (this.pendingText ? `<span style="color:#FF9800;background-color:#FFF3E0;padding:1px 3px;border-radius:2px;border-left:3px solid #FF9800;">${this.pendingText}</span>` : '')

            // 完整结果文本（用于最终发送）
            this.resultText = this.confirmedText + this.pendingText

            console.info('🖥️ 显示结果:', this.displayResult)
        },
        // 停止识别
        stopRecognize() {
            if (!this.isRecognizing) return

            // 发送结束信号
            if (this.socket && this.socket.readyState === WebSocket.OPEN) {
                const endParams = {
                    'endFlag': true
                }
                this.socket.send(JSON.stringify(endParams))
                // 延迟关闭WebSocket，确保服务器收到结束信号
                this.socket.close()
            }
        },

        // 工具方法：Float32转Int16
        float32ToInt16(buffer) {
            const int16Buffer = new Int16Array(buffer.length)
            for (let i = 0; i < buffer.length; i++) {
                int16Buffer[i] = Math.max(-32768, Math.min(32767, buffer[i] * 32767))
            }
            return int16Buffer
        },

        // 工具方法：ArrayBuffer转Base64
        arrayBufferToBase64(buffer) {
            let binary = ''
            const bytes = new Uint8Array(buffer)
            for (let i = 0; i < bytes.byteLength; i++) {
                binary += String.fromCharCode(bytes[i])
            }
            return window.btoa(binary)
        },

        // 启动录音计时器（60秒自动断开）
        startRecordingTimer() {
            // 清除之前可能存在的计时器
            this.clearRecordingTimer()

            console.info('🕐 启动60秒录音计时器')
            this.recordingTimer = setTimeout(() => {
                console.info('🕐 录音已达到60秒，自动停止')
                this.autoStopRecording()
            }, this.maxRecordingTime)
        },

        // 清除录音计时器
        clearRecordingTimer() {
            if (this.recordingTimer) {
                clearTimeout(this.recordingTimer)
                this.recordingTimer = null
            }
        },

        // 自动停止录音（60秒到达时调用）
        autoStopRecording() {
            if (!this.isRecording) return

            console.info('🔴 60秒自动停止录音')
            this.isRecording = false
            this.vioceTxt = ''

            // 清除计时器
            this.clearRecordingTimer()

            // 如果有识别结果则发送
            if (this.resultText && this.resultText.trim().length) {
                this.$emit('sendMessage', this.resultText)
            }

            // 清空录音状态
            this.clearRecordingState()
            // 结束录音
            this.stopRecognize()
        }
    }
    ,
    beforeDestroy() {
        // 组件销毁时清理计时器
        this.clearRecordingTimer()
        console.info('🔧 组件销毁，已清理录音计时器')
    },
    async mounted() {}
}
</script>


<style scoped lang="less">
.input-area {
    padding: 10px 21px 10px;
    position: relative;

    .foot-input {
        background-color: #fff;
        display: flex;
        align-items: flex-end;
        padding: 10px 15px 10px;
        border-radius: 24px;
        gap: 6px;
        position: relative;
        overflow: hidden;
        box-shadow: 0px 8px 3px 0px rgba(153, 153, 153, 0.08);


        .luyin {
            font-size: 20px;
            color: #444;
            transform: translateY(-5px);
        }

        textarea {
            flex: 1;
            border: none;
            resize: none;
            max-height: 100px;
            outline: none;
            line-height: 22px;
            padding: 4px 2px;
            box-sizing: border-box;
        }

        .send {
            width: 30px;
            height: 30px;
            background: rgb(240, 239, 247);
            border-radius: 50%;
            line-height: 27px;

            em {
                font-size: 22px;
                color: rgb(199, 198, 206);
                transform: rotate(-135deg);
                display: inline-block;
                margin-left: 5px;
            }
        }

    }

    .ai {
        display: flex;
        align-items: center;
        padding: 0 0 0 20px;
        gap: 6px;
        position: relative;
        box-shadow: 0px 8px 3px 0px rgba(153, 153, 153, 0.08);
        background: #287AEE;
        border-radius: 12px 12px 12px 12px;
        height: 50px;

        &::after {
            width: 1px;
            height: 22px;
            position: absolute;
            left: 67px;
            top: 14px;
            content: '';
            background: rgba(255, 255, 255, 0.28);;
        }

        .glass-bg {
            flex: 1;
        }

        .speak {
            width: 100%;
            height: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #fff;

            .speak-s {
                display: flex;
                align-items: center;
            }

            img {
                margin-right: 12px;
                width: 33px;
            }
        }

        img {
            width: 25px;
        }
    }

    .gif {
        max-width: 80%;
        position: fixed;
        left: 50%;
        bottom: 110px;
        transform: translateX(-50%);
        background: #FFFFFF;
        border-radius: 12px 12px 12px 12px;
        padding: 10px;
        font-size: 14px;
        color: #3D3D3D;
        line-height: 19px;
        z-index: 89;
        width: max-content;
    }

    .active {
        background-color: #1273E8 !important;

        em {
            color: #fff !important;
        }
    }
}

.input-area .ai .is-recording {
    position: fixed;
    width: 100%;
    left: 0;
    bottom: 0;
    right: 0;
    top: 0;
    background: rgba(0, 0, 0, 0.7);
    z-index: 88;

    .speak-s {
        position: fixed;
        bottom: 30px;
        display: flex;
        align-items: center;
    }
}

.input-area .ai .isOutOfRange {
    background-color: #f50000;
}

.box-recording {
    width: 100%;
}

.text-recording {
    position: absolute;
    bottom: 160px;
    width: 100%;
    box-sizing: border-box;
    padding: 0 20px;
    font-size: 14px;
    color: #fff;
    .text-white {
        text-align: center;
        font-size: 14px;
        line-height: 1.5;
        padding: 0 10px;
    }
    .loading-txt {
        font-size: 14px;
        line-height: 1.4;
        background: none;
        display: flex;
        justify-content: center;
        align-items: center;
    }
}

.btn-recording {
    width: 100%;
    height: 120px;
    border: none;
    position: absolute;
    bottom: 0;
    /* 使用圆锥渐变模拟漩涡效果，添加动画让其旋转 */
    background: conic-gradient(from 0deg at center, #4e72e9, #21eedc, #44b0d6);
    background-size: 200% 200%;
    animation: swirl 5s linear infinite;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    cursor: pointer;
    overflow: hidden;
    transition: all 0.3s ease;
    filter: blur(20px);
    border-radius: 150px 150px 0 0;

    &::before {
        /* 科技弥散效果 */
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(45deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0));
        opacity: 0;
        animation: diffuse 3s infinite;
        pointer-events: none;
    }
}

@keyframes swirl {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 100% 100%;
    }
}

@keyframes diffuse {
    0% {
        opacity: 0;
        transform: rotate(0deg);
    }
    50% {
        opacity: 1;
        transform: rotate(180deg);
    }
    100% {
        opacity: 0;
        transform: rotate(360deg);
    }
}

.yuyin {
    font-size: 30px;
}

.ai-keyboard {
    font-size: 25px;
    color: #fff;
}

.jiazai {
    font-size: 18px;
    animation: rotate1 1.8s linear infinite;
}

@keyframes rotate1 {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

</style>

