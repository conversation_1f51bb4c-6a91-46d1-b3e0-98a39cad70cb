<template>
    <div class="topWrap"  @scroll="scrollWorkStageNew($event)" ref="workStageDiv">
        <!--头部信息-->
        <!--信息展示区 v-show="isBigHead"-->
        <div v-show="!isShowProvincial">
            <div :class="[isBigHead ? 'show':'hide']" class="work-info-wrap" ref="topDiv">
                <!-- 提示-->
                <div class="workStageNew-wrap" v-show="weekPlanXinFlag" >
                    <!-- 头-->
                    <div class="workStage-head">
                        <img class='workStage-cloud' src='../../../static/img/workStroge/workStroge-bg.png' alt=''>
                        <!-- 消息-->
                        <div class="workStage-remind" v-if='isShowTip && weekPlanXinFlag' >
                <span class="workStage-latestXin" @click="goNoticeDetail"> <LatestXin
                    :tsNoticeInfo="noticeInfo"></LatestXin></span>
                            <em class="iconfont guanbi" @click="deleteTip"></em>
                        </div>

                        <!-- 人员信息-->
                        <div class='workStage-frist'>
                            <div class='workFrist-left' v-show="(showCompete && chooseCompete) || weekPlanType =='1'">
                                <div class='workFrist-name'>{{ userInfoData.operatorName | tuoMingName(myInfoTuoMingFlag)}}</div>
                                <div class='workFrist-role' v-show="!showCompete || !chooseCompete"><span>{{ userIdentity }}</span></div>
                                <div class='workFrist-role' v-if="showCompete && chooseCompete"><span>{{ uinfo.stationName }}</span></div>
                            </div>
                            <div class='workFrist-left-SheQu'  :class="{'compete-role': showCompete}" v-show="(weekPlanType =='2' || weekPlanType =='3' || weekPlanType =='4' || weekPlanType =='5') && (!showCompete || !chooseCompete)">
                                <div class='workFrist-name'>{{ userInfoData.operatorName | tuoMingName(myInfoTuoMingFlag)}}</div>
                                <div class='workFrist-role'><em class='iconfont zhanghaoqiehuan'></em><span>{{ userInfoData.stationName }}</span></div>
                            </div>

                            <div class='workFrist-right'>
                                <div class="top-icon">
                                    <i class="iconfont feiyong" id="incentiveClick" @click="goIncentiveDetail"></i>
                                    <!-- 定位-->
                                    <img class='ocr-button' id='workStageClickOCR' src="static/img/scan.png" @click="openOcr" />
                                    <div class="iconfont a-zu5638101x" id='workStageClickPosition' @click="showDingWei">
                                        <div v-show="showDingWeiFlag">
                                            <div class="workFrist-dizhiContent">
                                                <div class="workFrist-jiantou"></div>
                                            </div>
                                            <div class="workFrist-qiPao">
                                                <div class="workFrist-dizhi">{{ getLocation }}</div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- 日志-->
                                    <i class="iconfont a-zu5638111x" id='workStageClickLog' @click="gobusilogseach"></i>
                                    <!-- 搜索-->
                                    <i class="iconfont sousuo1" id='workStageClickSearch' @click="qryAct"></i>
                                </div>
                                <!--                  <div class="index-change" v-show="weekPlanType=='1' && isShowChangeBtu && !showCompete">
                    <span class="region">本地</span>
                    <span class="provincial" @click="backProvincial">省统</span>
                  </div>-->
                                <i style="display: none;" id="competeClick"></i>
                                <div class="index-change" v-show="showCompete">
                    <span class="change-btu" :class="{'orange-bg': chooseCompete}" id='workStageControl' @click="changeCompete(true)">
                      <img src="../../../static/img/provincecount/<EMAIL>" alt="" v-show="chooseCompete"/>
                      <img src="../../../static/img/provincecount/<EMAIL>" alt="" v-show="!chooseCompete"/>
                      <span class="btu-name" :class="{'choose-name': chooseCompete}">掌控值</span>
                    </span>
                                    <span class="change-btu" :class="{'blue-bg': !chooseCompete}"  id='workStageBoard' @click="changeCompete(false)">
                      <span class="iconfont btu-icon renshiyewu" :class="{'choose-icon': !chooseCompete}"/>
                      <span class="btu-name" :class="{'choose-name': !chooseCompete}">指标看板</span>
                    </span>
                                </div>
                            </div>
                        </div>

                        <!--              <div class="switch-shiTu" v-show='weekSheQuStationFlag && (!showCompete || !chooseCompete)' :class="{zhiBiaoTive:weekPlanType =='1'}">-->
                        <!--                  <div class="switch-shequ" :class="{active:weekPlanType !='1' && weekPlanType !='4'}" @click='weekSheQuStationCheck(1)'>社区</div>-->
                        <!--                  <div class="switch-zhibiao" :class="{active:weekPlanType =='1'}" @click='weekSheQuStationCheck(2)'>指标</div>-->
                        <!--                  <div class="switch-zhibiao" :class="{active:weekPlanType =='4'}" @click='weekSheQuStationCheck(4)'>商机</div>-->
                        <!--              </div>-->


                        <div class='tab-head' v-show='weekSheQuStationFlag && (!showCompete || !chooseCompete)'>
                            <div class='tabs tabs1' id='workStageWarning1' v-show='kanban.viewFlag == 1' :class="{active:weekPlanType =='5'}" @click='weekSheQuStationCheck(5)'>预警</div>
                            <div class="tabs tabs0" id='workStageCommunity1' :class="{active:weekPlanType !='1' && weekPlanType !='4' && weekPlanType !='5'}" @click='weekSheQuStationCheck(1)'>社区</div>
                            <div class='tabs tabs0' id='workStageNorm1' :class="{active:weekPlanType =='1'}" @click='weekSheQuStationCheck(2)'>指标</div>
                            <div class='tabs tabs2' id='workStageBusiness1' v-show='kanban.viewFlag == 1' :class="{active:weekPlanType =='4'}" @click='weekSheQuStationCheck(4)'>商机</div>
                        </div>
                        <div class='tab-box' >
                            <div class='tab-head' v-show='!(weekSheQuStationFlag && (!showCompete || !chooseCompete)) && kanban.viewFlag == 1 && !chooseCompete'>
                                <div class='tabs tabs1' id='workStageWarning2' :class="tabhead == 3? 'active': '' " @click='tabhead = 3'>预警</div>
                                <div class='tabs tabs0' id='workStageNorm2' :class="tabhead == 1? 'active': '' " @click='tabhead = 1;weekSheQuStationCheck(2)'>指标</div>
                                <div class='tabs tabs2' id='workStageBusiness2' :class="tabhead == 2? 'active': '' " @click='tabhead = 2'>商机</div>
                            </div>


                            <div class='tab-head' v-show='chooseCompete && !showCompete && !weekSheQuStationFlag && kanban.viewFlag == 1'>
                                <div class='tabs tabs1' id='workStageWarning3' :class="tabhead == 3? 'active': '' " @click='tabhead = 3'>预警</div>
                                <div class='tabs tabs0' id='workStageNorm3' :class="tabhead == 1? 'active': '' " @click='tabhead = 1;weekSheQuStationCheck(2)'>指标</div>
                                <div class='tabs tabs2' id='workStageBusiness3' :class="tabhead == 2? 'active': '' " @click='tabhead = 2'>商机</div>
                            </div>
                            <div v-if='weekSheQuStationFlag && (!showCompete || !chooseCompete)'>
                                <div class='tab-conteng' v-show='weekPlanType != 4'>
                                    <div class="workcs-banner" v-show="!showCompete || !chooseCompete">
                                        <div class="workcs-bannernei"  v-show="weekPlanType =='1' && zhiBiaoList.length >0 ">
                                            <workSlide ref="workSlide"
                                                       :autoPlay="false"
                                                       :loop="true"
                                                       :showDot="true"
                                                       :dataList="zhiBiaoList">
                                                <div v-for="(items,indexs) in zhiBiaoList" :key="indexs" style="position: relative;">
                                                    <div class="workcs-banner-list">
                                                        <!--                        <div class="workcs-banner-cs-wangge" @click="openSelectTree(items)">{{ items.orgNameChild }}</div>-->
                                                        <div class="compete-banner-head-left"  @click="openSelectTree(items)">
                                                            <span class="iconfont cengji"/>
                                                            <span class="name">{{ items.orgNameChild }}</span>
                                                        </div>
                                                        <div class="compete-banner-head-right">
                                                            <span class="label" @click="backProvincial" v-show="weekPlanType=='1' && isShowChangeBtu && (!chooseCompete || !showCompete)"><span class="iconfont xingzhuanggangwei"/>省统</span>
                                                            <span class="more-btu" @click="goWorkReportDetail">更多</span>
                                                        </div>
                                                        <!--                        <div class="workcs-banner-cs-gengduo" @click="goWorkReportDetail">更多</div>-->
                                                        <div class="workcs-banner-cs-box">
                                                            <div class="workcs-banner-cs-indiList">
                                                                <div class="workcs-banner-cs" @click="openShuJuTip(item)"
                                                                     v-for="(item,index) in items.indiList" :key="index">
                                                                    <div class="workcs-banner-cs-name">{{ item.indNameShort }}
                                                                        <span class="workcs-banner-cs-unit1" v-show="item.indiValue && item.indiValue !='null'">({{ item.unit }})</span>
                                                                    </div>
                                                                    <div class="workcs-banner-cs-num">{{ item.indiValue | checkNull }}
                                                                        <!--                                <span class="workcs-banner-cs-unit" v-show="item.indiValue && item.indiValue !='null'">{{ item.unit }}</span>-->
                                                                    </div>
                                                                    <!--                              <div class="workcs-banner-cs-Finsh">-->
                                                                    <!--                                <div class="iconfont benyue"><span class="workcs-banner-Finsh-zi" :class="{'fail':~checkInList(item)}">{{ item.indiFinish | checkNull }}</span></div>-->
                                                                    <!--                              </div>-->
                                                                    <div class="workcs-banner-cs-Finsh1">
                                                                        <div class="workcs-banner-Finsh-biao" >标</div>
                                                                        <div class="workcs-banner-Finsh-zi1" >{{ item.indiTarget | checkNull }}</div>
                                                                        <div class="workcs-banner-Finsh-yue" >月</div>
                                                                        <div class="workcs-banner-Finsh-zi1">{{ item.indiFinish | checkNull }}</div>
                                                                    </div>
                                                                </div>
                                                                <!--                  <div class="workcs-banner-touming"></div>-->
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </workSlide>
                                        </div>
                                        <div class="workcs-bannernei"  v-show="weekPlanType =='1' && zhiBiaoList.length ==0">
                                            <div  style="position: relative;">
                                                <div class="workcs-banner-list">
                                                    <div class="workcs-banner-cs-wangge" >--</div>
                                                    <div class="workcs-banner-cs-gengduo">--</div>
                                                    <div class="workcs-banner-cs-box">
                                                        <div class="workcs-banner-cs-indiList">
                                                            <div class="workcs-banner-cs" v-for='(item,index) in 9' :key='index'>
                                                                <div class="workcs-banner-cs-name">--
                                                                    <span class="workcs-banner-cs-unit1">--</span>
                                                                </div>
                                                                <div class="workcs-banner-cs-num">--
                                                                </div>
                                                                <div class="workcs-banner-cs-Finsh1">
                                                                    <div class="workcs-banner-Finsh-biao" >标</div>
                                                                    <div class="workcs-banner-Finsh-zi1" >--</div>
                                                                    <div class="workcs-banner-Finsh-yue" >月</div>
                                                                    <div class="workcs-banner-Finsh-zi1">--</div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="statistics-box" v-show="weekPlanType =='2'" @click="gobusilogseach">
                                            <div class="statistics-dangyue">{{ dangYueTip }}</div>

                                            <div class='statistics'>
                                                <div class='statistics-item'>
                                                    <div>比算新增</div>
                                                    <p>{{ sheQuList.biSuanNum }}<span></span></p></div>
                                                <div class='statistics-item'>
                                                    <div>宽带新增</div>
                                                    <p>{{ sheQuList.kuanDaiNum | quZheng}}<span>笔</span></p></div>
                                                <div class='statistics-item'>
                                                    <div>电视</div>
                                                    <p>{{ sheQuList.dianShiNum | quZheng}}<span>笔</span></p></div>
                                                <div class='statistics-item'>
                                                    <div>组网</div>
                                                    <p>{{ sheQuList.zuWangNum | quZheng}}<span>笔</span></p></div>
                                                <div class='statistics-item'>
                                                    <div>安防</div>
                                                    <p>{{ sheQuList.anFangNUm | quZheng}}<span>笔</span></p></div>
                                                <div class='statistics-item'>
                                                    <div>号卡</div>
                                                    <p>{{ sheQuList.haoKaNum | quZheng}}<span>笔</span></p></div>
                                                <div class='statistics-item'>
                                                    <div>电视会员</div>
                                                    <p>{{ sheQuList.dianShiVipNum | quZheng}}<span>笔</span></p></div>
                                                <div class='statistics-item'>
                                                    <div>权益</div>
                                                    <p>{{ sheQuList.quanYiNum | quZheng}}<span>笔</span></p></div>
                                            </div>
                                        </div>

                                        <div class="statistics-box" v-show="weekPlanType =='3'" @click="gobusilogseach">
                                            <div class="statistics-dangyue">{{ dangYueTip }}</div>

                                            <div class='statistics' style="height: 130px;">
                                                <div class='statistics-item'>
                                                    <div>宽带新增</div>
                                                    <p>{{ sheQuList.kuanDaiNum | quZheng}}<span>笔</span></p></div>
                                                <div class='statistics-item'>
                                                    <div>电视</div>
                                                    <p>{{ sheQuList.dianShiNum | quZheng}}<span>笔</span></p></div>
                                                <div class='statistics-item'>
                                                    <div>组网</div>
                                                    <p>{{ sheQuList.zuWangNum | quZheng}}<span>笔</span></p></div>
                                                <div class='statistics-item'>
                                                    <div>安防</div>
                                                    <p>{{ sheQuList.anFangNUm| quZheng}}<span>笔</span></p></div>
                                                <div class='statistics-item'>
                                                    <div>电视会员</div>
                                                    <p>{{ sheQuList.dianShiVipNum | quZheng}}<span>笔</span></p></div>
                                                <div class='statistics-item'>
                                                    <div>号卡</div>
                                                    <p>{{ sheQuList.haoKaNum | quZheng}}<span>笔</span></p></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-else>
                                <div class='tab-conteng' v-show='tabhead == 1'>
                                    <div class="workcs-banner" v-show="!showCompete || !chooseCompete">
                                        <div class="workcs-bannernei"  v-show="weekPlanType =='1' && zhiBiaoList.length >0 ">
                                            <workSlide ref="workSlide"
                                                       :autoPlay="false"
                                                       :loop="true"
                                                       :showDot="true"
                                                       :dataList="zhiBiaoList">
                                                <div v-for="(items,indexs) in zhiBiaoList" :key="indexs" style="position: relative;">
                                                    <div class="workcs-banner-list">
                                                        <!--                        <div class="workcs-banner-cs-wangge" @click="openSelectTree(items)">{{ items.orgNameChild }}</div>-->
                                                        <div class="compete-banner-head-left"  @click="openSelectTree(items)">
                                                            <span class="iconfont cengji"/>
                                                            <span class="name">{{ items.orgNameChild }}</span>
                                                        </div>
                                                        <div class="compete-banner-head-right">
                                                            <span class="label" @click="backProvincial" v-show="weekPlanType=='1' && isShowChangeBtu && (!chooseCompete || !showCompete)"><span class="iconfont xingzhuanggangwei"/>省统</span>
                                                            <span class="more-btu" @click="goWorkReportDetail">更多</span>
                                                        </div>
                                                        <!--                        <div class="workcs-banner-cs-gengduo" @click="goWorkReportDetail">更多</div>-->
                                                        <div class="workcs-banner-cs-box">
                                                            <div class="workcs-banner-cs-indiList">
                                                                <div class="workcs-banner-cs" @click="openShuJuTip(item)"
                                                                     v-for="(item,index) in items.indiList" :key="index">
                                                                    <div class="workcs-banner-cs-name">{{ item.indNameShort }}
                                                                        <span class="workcs-banner-cs-unit1" v-show="item.indiValue && item.indiValue !='null'">({{ item.unit }})</span>
                                                                    </div>
                                                                    <div class="workcs-banner-cs-num">{{ item.indiValue | checkNull }}
                                                                        <!--                                <span class="workcs-banner-cs-unit" v-show="item.indiValue && item.indiValue !='null'">{{ item.unit }}</span>-->
                                                                    </div>
                                                                    <!--                              <div class="workcs-banner-cs-Finsh">-->
                                                                    <!--                                <div class="iconfont benyue"><span class="workcs-banner-Finsh-zi" :class="{'fail':~checkInList(item)}">{{ item.indiFinish | checkNull }}</span></div>-->
                                                                    <!--                              </div>-->
                                                                    <div class="workcs-banner-cs-Finsh1">
                                                                        <div class="workcs-banner-Finsh-biao" >标</div>
                                                                        <div class="workcs-banner-Finsh-zi1" >{{ item.indiTarget | checkNull }}</div>
                                                                        <div class="workcs-banner-Finsh-yue" >月</div>
                                                                        <div class="workcs-banner-Finsh-zi1">{{ item.indiFinish | checkNull }}</div>
                                                                    </div>
                                                                </div>
                                                                <!--                  <div class="workcs-banner-touming"></div>-->
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </workSlide>
                                        </div>
                                        <div class="workcs-bannernei"  v-show="weekPlanType =='1' && zhiBiaoList.length ==0">
                                            <div  style="position: relative;">
                                                <div class="workcs-banner-list">
                                                    <div class="workcs-banner-cs-wangge" >--</div>
                                                    <div class="workcs-banner-cs-gengduo">--</div>
                                                    <div class="workcs-banner-cs-box">
                                                        <div class="workcs-banner-cs-indiList">
                                                            <div class="workcs-banner-cs" v-for='(item,index) in 9' :key='index'>
                                                                <div class="workcs-banner-cs-name">--
                                                                    <span class="workcs-banner-cs-unit1">--</span>
                                                                </div>
                                                                <div class="workcs-banner-cs-num">--
                                                                </div>
                                                                <div class="workcs-banner-cs-Finsh1">
                                                                    <div class="workcs-banner-Finsh-biao" >标</div>
                                                                    <div class="workcs-banner-Finsh-zi1" >--</div>
                                                                    <div class="workcs-banner-Finsh-yue" >月</div>
                                                                    <div class="workcs-banner-Finsh-zi1">--</div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="statistics-box" v-show="weekPlanType =='2'" @click="gobusilogseach">
                                            <div class="statistics-dangyue">{{ dangYueTip }}</div>

                                            <div class='statistics'>
                                                <div class='statistics-item'>
                                                    <div>比算新增</div>
                                                    <p>{{ sheQuList.biSuanNum }}<span></span></p></div>
                                                <div class='statistics-item'>
                                                    <div>宽带新增</div>
                                                    <p>{{ sheQuList.kuanDaiNum | quZheng}}<span>笔</span></p></div>
                                                <div class='statistics-item'>
                                                    <div>电视</div>
                                                    <p>{{ sheQuList.dianShiNum | quZheng}}<span>笔</span></p></div>
                                                <div class='statistics-item'>
                                                    <div>组网</div>
                                                    <p>{{ sheQuList.zuWangNum | quZheng}}<span>笔</span></p></div>
                                                <div class='statistics-item'>
                                                    <div>安防</div>
                                                    <p>{{ sheQuList.anFangNUm | quZheng}}<span>笔</span></p></div>
                                                <div class='statistics-item'>
                                                    <div>号卡</div>
                                                    <p>{{ sheQuList.haoKaNum | quZheng}}<span>笔</span></p></div>
                                                <div class='statistics-item'>
                                                    <div>电视会员</div>
                                                    <p>{{ sheQuList.dianShiVipNum | quZheng}}<span>笔</span></p></div>
                                                <div class='statistics-item'>
                                                    <div>权益</div>
                                                    <p>{{ sheQuList.quanYiNum | quZheng}}<span>笔</span></p></div>
                                            </div>
                                        </div>

                                        <div class="statistics-box" v-show="weekPlanType =='3'" @click="gobusilogseach">
                                            <div class="statistics-dangyue">{{ dangYueTip }}</div>

                                            <div class='statistics' style="height: 130px;">
                                                <div class='statistics-item'>
                                                    <div>宽带新增</div>
                                                    <p>{{ sheQuList.kuanDaiNum | quZheng}}<span>笔</span></p></div>
                                                <div class='statistics-item'>
                                                    <div>电视</div>
                                                    <p>{{ sheQuList.dianShiNum | quZheng}}<span>笔</span></p></div>
                                                <div class='statistics-item'>
                                                    <div>组网</div>
                                                    <p>{{ sheQuList.zuWangNum | quZheng}}<span>笔</span></p></div>
                                                <div class='statistics-item'>
                                                    <div>安防</div>
                                                    <p>{{ sheQuList.anFangNUm| quZheng}}<span>笔</span></p></div>
                                                <div class='statistics-item'>
                                                    <div>电视会员</div>
                                                    <p>{{ sheQuList.dianShiVipNum | quZheng}}<span>笔</span></p></div>
                                                <div class='statistics-item'>
                                                    <div>号卡</div>
                                                    <p>{{ sheQuList.haoKaNum | quZheng}}<span>笔</span></p></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class='tab-conteng' v-show='tabhead == 2 || weekPlanType == 4'>
                                <div class='workcs-banner'>
                                    <div class='workcs-bannernei'>
                                        <div class='workcs-banner-list workcs-banner-list2'>
                                            <div class="compete-banner-head-left" >
                                                <span class="iconfont cengji"/>
                                                <span class="name">{{kanban.rangeName}}</span>
                                            </div>
                                            <div class="compete-banner-head-right">
                                                <span class="more-btu" >{{kanban.month}}</span>
                                            </div>
                                            <div class="workcs-banner-cs-box workcs-banner-cs-box2">
                                                <div class="workcs-banner-cs-indiList">
                                                    <div class="workcs-banner-cs" >
                                                        <div class="workcs-banner-cs-name">当天新增</div>
                                                        <div class="workcs-banner-cs-num">{{kanban2.mea1}}</div>
                                                    </div>
                                                    <div class="workcs-banner-cs" >
                                                        <div class="workcs-banner-cs-name">本月新增</div>
                                                        <div class="workcs-banner-cs-num">{{kanban2.mea2}}</div>
                                                    </div>
                                                    <div class="workcs-banner-cs" >
                                                        <div class="workcs-banner-cs-name">累计新增</div>
                                                        <div class="workcs-banner-cs-num">{{kanban2.mea3}}</div>
                                                    </div>
                                                    <div class="workcs-banner-cs" >
                                                        <div class="workcs-banner-cs-name">当月转化</div>
                                                        <div class="workcs-banner-cs-num">{{kanban2.mea4}}</div>
                                                    </div>
                                                    <div class="workcs-banner-cs" >
                                                        <div class="workcs-banner-cs-name">累计转化</div>
                                                        <div class="workcs-banner-cs-num">{{kanban2.mea5}}</div>
                                                    </div>
                                                    <div class="workcs-banner-cs" >
                                                        <div class="workcs-banner-cs-name">当月解耦新增</div>
                                                        <div class="workcs-banner-cs-num">{{kanban2.mea7}}</div>
                                                    </div>
                                                </div>
                                                <div class="compete-bottom" @click='goPersonalBusinessOpportunity()'>
                                                    <div class="compete-bottom-left">
                                                        <span class="left-btu"><span class="iconfont lujing2"></span>商机报表</span>
                                                    </div>
                                                </div>
                                                <div class="compete-bottom" @click='goPersonalBusinessOpportunity(1)'>
                                                    <div class="compete-bottom-left">
                                                        <span class="left-btu"><span class="iconfont lujing2"></span>解耦商机报表</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                            <div class='tab-conteng' v-show='(tabhead == 3 && weekPlanType != 5) || (tabhead != 3 && weekPlanType == 5)'>
                                <div class='workcs-banner'>
                                    <div class='workcs-bannernei'>
                                        <div class='workcs-banner-list workcs-banner-list3'>
                                            <div class="compete-banner-head-left" >
                                                <span class="iconfont cengji"/>
                                                <span class="name">{{kanban.rangeName}}</span>
                                            </div>
                                            <div class="compete-banner-head-right">
                                                <span class="more-btu" >{{kanban3.monthStr ? kanban3.monthStr + '月' : '--月'}}</span>
                                            </div>
                                            <div class="workcs-banner-cs-box workcs-banner-cs-box2">
                                                <div class="workcs-banner-cs-indiList">
                                                    <div class="workcs-banner-cs workcs-banner-cs-orange" >
                                                        <div class="workcs-banner-cs-name">预警工单量</div>
                                                        <div class="workcs-banner-cs-num">{{kanban3.yjOrderCnt}}</div>
                                                        <div class="workcs-banner-cs-num2" v-show='kanban3.yjOrderHb != "0.00PP"' :class='kanban3.yjOrderHb1 == "red" ? "red" : ""'>
                                                            <span v-show='kanban3.yjOrderHb1 != "red"' class='iconfont arrowTop'></span>
                                                            <span v-show='kanban3.yjOrderHb1 == "red"' class='iconfont arrowBottom'></span>
                                                            {{kanban3.yjOrderHb}}
                                                        </div>
                                                    </div>
                                                    <div class="workcs-banner-cs workcs-banner-cs-orange" >
                                                        <div class="workcs-banner-cs-name">客户保有率</div>
                                                        <div class="workcs-banner-cs-num">{{kanban3.customerStayPercent}}</div>
                                                        <div class="workcs-banner-cs-num2" v-show='kanban3.customerStayHb != "0.00PP"' :class='kanban3.customerStayHb1 == "red" ? "red" : ""'>
                                                            <span v-show='kanban3.customerStayHb1 != "red"' class='iconfont arrowTop'></span>
                                                            <span v-show='kanban3.customerStayHb1 == "red"' class='iconfont arrowBottom'></span>
                                                            {{kanban3.customerStayHb}}
                                                        </div>

                                                    </div>
                                                    <div class="workcs-banner-cs workcs-banner-cs-orange" >
                                                        <div class="workcs-banner-cs-name">价值保有率</div>
                                                        <div class="workcs-banner-cs-num">{{kanban3.valueStayPercent}}</div>
                                                        <div class="workcs-banner-cs-num2" v-show='kanban3.valueStayHb != "0.00PP"' :class='kanban3.valueStayHb1 == "red" ? "red" : ""'>
                                                            <span v-show='kanban3.valueStayHb1 != "red"' class='iconfont arrowTop'></span>
                                                            <span v-show='kanban3.valueStayHb1 == "red"' class='iconfont arrowBottom'></span>
                                                            {{kanban3.valueStayHb}}
                                                        </div>

                                                    </div>
                                                    <div class="workcs-banner-cs" >
                                                        <div class="workcs-banner-cs-name">工单结单率</div>
                                                        <div class="workcs-banner-cs-num">{{kanban3.orderFinishPercent}}</div>
                                                        <div class="workcs-banner-cs-num2" v-show='kanban3.orderFinishHb != "0.00PP"' :class='kanban3.orderFinishHb1 == "red" ? "red" : ""'>
                                                            <span v-show='kanban3.orderFinishHb1 != "red"' class='iconfont arrowTop'></span>
                                                            <span v-show='kanban3.orderFinishHb1 == "red"' class='iconfont arrowBottom'></span>
                                                            {{kanban3.orderFinishHb}}
                                                        </div>

                                                    </div>
                                                    <div class="workcs-banner-cs" >
                                                        <div class="workcs-banner-cs-name">工单成功率</div>
                                                        <div class="workcs-banner-cs-num">{{kanban3.orderSuccessPercent}}</div>
                                                        <div class="workcs-banner-cs-num2" v-show='kanban3.orderSuccessHb != "0.00PP"'  :class='kanban3.orderSuccessHb1 == "red" ? "red" : ""'>
                                                            <span v-show='kanban3.orderSuccessHb1 != "red"' class='iconfont arrowTop'></span>
                                                            <span v-show='kanban3.orderSuccessHb1 == "red"' class='iconfont arrowBottom'></span>
                                                            {{kanban3.orderSuccessHb}}
                                                        </div>

                                                    </div>
                                                    <div class="workcs-banner-cs" >
                                                        <div class="workcs-banner-cs-name">工单异动率</div>
                                                        <div class="workcs-banner-cs-num">{{kanban3.orderUnusualPercent}}</div>
                                                        <div class="workcs-banner-cs-num2" v-show='kanban3.orderUnusualHb != "0.00PP"' :class='kanban3.orderUnusualHb1 == "red" ? "red" : ""'>
                                                            <span v-show='kanban3.orderUnusualHb1 != "red"' class='iconfont arrowTop'></span>
                                                            <span v-show='kanban3.orderUnusualHb1 == "red"' class='iconfont arrowBottom'></span>
                                                            {{kanban3.orderUnusualHb}}
                                                        </div>

                                                    </div>
                                                    <div class="workcs-banner-cs" >
                                                        <div class="workcs-banner-cs-name">工单见面率</div>
                                                        <div class="workcs-banner-cs-num">{{kanban3.orderMeetPercent}}</div>
                                                        <div class="workcs-banner-cs-num2" v-show='kanban3.orderMeetHb != "0.00PP"'  :class='kanban3.orderMeetHb1 == "red" ? "red" : ""'>
                                                            <span v-show='kanban3.orderMeetHb1 != "red"' class='iconfont arrowTop'></span>
                                                            <span v-show='kanban3.orderMeetHb1 == "red"' class='iconfont arrowBottom'></span>
                                                            {{kanban3.orderMeetHb}}
                                                        </div>

                                                    </div>
                                                    <div class="workcs-banner-cs workcs-banner-cs-orange" >
                                                        <div class="workcs-banner-cs-name">有效加约率</div>
                                                        <div class="workcs-banner-cs-num">{{kanban3.validGaiusPercent}}</div>
                                                        <div class="workcs-banner-cs-num2" v-show='kanban3.validGaiusHb != "0.00PP"' :class='kanban3.validGaiusHb1 == "red" ? "red" : ""'>
                                                            <span v-show='kanban3.validGaiusHb1 != "red"' class='iconfont arrowTop'></span>
                                                            <span v-show='kanban3.validGaiusHb1 == "red"' class='iconfont arrowBottom'></span>
                                                            {{kanban3.validGaiusHb}}
                                                        </div>

                                                    </div>
                                                </div>

                                                <div class="compete-bottom" @click="goShengJiOrder('4')">
                                                    <div class="compete-bottom-left">
                                                        <span class="left-btu">查看脉动感知工单<span class="iconfont jiantou-copy-copy"></span></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>

                        </div>
                        <!-- 首页数据视图-->
                        <!--竞对比-->
                        <div class="workcs-banner" v-show="showCompete && chooseCompete && tabhead == 1">
                            <div class="workcs-bannernei" v-show="competeData && competeData.length > 0">
                                <workSlide ref="workSlide" :autoPlay="false" :loop="true" :showDot="true" :dataList="competeData">
                                    <div v-for="(item, index) in competeData" :key="index" style="position: relative;">
                                        <div class="compete-banner-list">
                                            <div class="compete-banner-head-left">
                                                <span class="iconfont cengji"/>
                                                <span class="name">{{ item.orgName }}</span>
                                            </div>
                                            <div class="compete-banner-content-box">
                                                <ul class="content-ul">
                                                    <li class="content-li">
                                                        <div class="compete-name">规模掌控值</div>
                                                        <div class="compete-value">{{ item.userScaleRatio }}</div>
                                                    </li>
                                                    <li class="container"></li>
                                                    <li class="content-li">
                                                        <div class="compete-name sub-option">
                                                            <span class="iconfont jingzhengduishou sub-compete-icon"/>
                                                            <span class="sub-compete-name">进攻规模</span>
                                                        </div>
                                                        <div class="compete-value sub-option">
                                                            <span class="sub-compete-value" v-show="item.attackScale">{{ item.attackScale }}</span>
                                                            <span class="sub-compete-value" v-show="!item.attackScale">--</span>
                                                        </div>
                                                    </li>
                                                    <li class="content-li">
                                                        <div class="compete-name sub-option">
                                                            <span class="iconfont fangshou sub-compete-icon"/>
                                                            <span class="sub-compete-name">失守规模</span>
                                                        </div>
                                                        <div class="compete-value sub-option">
                                                            <span class="sub-compete-value" v-show="item.defenseScale">{{ item.defenseScale }}</span>
                                                            <span class="sub-compete-value" v-show="!item.defenseScale">{{ item.defenseScale }}</span>
                                                        </div>
                                                    </li>
                                                    <li class="content-li">
                                                        <div class="compete-name">价值掌控值</div>
                                                        <div class="compete-value">{{ item.valueFeeRatio }}</div>
                                                    </li>
                                                    <li class="container"></li>
                                                    <li class="content-li">
                                                        <div class="compete-name sub-option">
                                                            <span class="iconfont jingzhengduishou sub-compete-icon"/>
                                                            <span class="sub-compete-name">进攻用户价值</span>
                                                        </div>
                                                        <div class="compete-value sub-option">
                                <span class="sub-compete-value">{{ item.attactScaleFee | unitConversion(isNewCompete) }}
                                 <span class="sub-compete-unit">万</span>
                                </span>
                                                        </div>
                                                    </li>
                                                    <li class="content-li">
                                                        <div class="compete-name sub-option">
                                                            <span class="iconfont fangshou sub-compete-icon"/>
                                                            <span class="sub-compete-name">失守用户价值</span>
                                                        </div>
                                                        <div class="compete-value sub-option">
                                <span class="sub-compete-value">{{ item.defenseScaleFee | unitConversion(isNewCompete) }}
                                  <span class="sub-compete-unit">万</span>
                                </span>
                                                        </div>
                                                    </li>
                                                </ul>
                                                <div class="compete-bottom">
                                                    <div class="compete-bottom-left">
                                                        <button class="left-btu" @click="viewStatement" id="competeStatementClick"><span class="iconfont lujing2"></span>查看报表</button>
                                                    </div>
                                                    <img class="compete-bottom-img" src="../../../static/img/provincecount/<EMAIL>" alt=""/>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </workSlide>
                            </div>
                            <div class="workcs-bannernei" v-show="showCompete && (!competeData || competeData.length === 0)">
                                <div style="position: relative;">
                                    <div class="compete-banner-list">
                                        <div class="compete-banner-head-left">
                                            <span class="iconfont cengji"/>
                                            <span class="name">--</span>
                                        </div>
                                        <div class="compete-banner-content-box">
                                            <ul class="content-ul">
                                                <li class="content-li">
                                                    <div class="compete-name">规模掌控值</div>
                                                    <div class="compete-value">--</div>
                                                </li>
                                                <li class="container"></li>
                                                <li class="content-li">
                                                    <div class="compete-name sub-option">
                                                        <span class="iconfont jingzhengduishou sub-compete-icon"/>
                                                        <span class="sub-compete-name">进攻规模</span>
                                                    </div>
                                                    <div class="compete-value sub-option">
                                                        <span class="sub-compete-value">--</span>
                                                    </div>
                                                </li>
                                                <li class="content-li">
                                                    <div class="compete-name sub-option">
                                                        <span class="iconfont fangshou sub-compete-icon"/>
                                                        <span class="sub-compete-name">失守规模</span>
                                                    </div>
                                                    <div class="compete-value sub-option">
                                                        <span class="sub-compete-value">--</span>
                                                    </div>
                                                </li>
                                                <li class="content-li">
                                                    <div class="compete-name">价值掌控值</div>
                                                    <div class="compete-value">--</div>
                                                </li>
                                                <li class="container"></li>
                                                <li class="content-li">
                                                    <div class="compete-name sub-option">
                                                        <span class="iconfont jingzhengduishou sub-compete-icon"/>
                                                        <span class="sub-compete-name">进攻用户价值</span>
                                                    </div>
                                                    <div class="compete-value sub-option">
                                                        <span class="sub-compete-value">--</span>
                                                    </div>
                                                </li>
                                                <li class="content-li">
                                                    <div class="compete-name sub-option">
                                                        <span class="iconfont fangshou sub-compete-icon"/>
                                                        <span class="sub-compete-name">失守用户价值</span>
                                                    </div>
                                                    <div class="compete-value sub-option">
                                                        <span class="sub-compete-value">--</span>
                                                    </div>
                                                </li>
                                            </ul>
                                            <div class="compete-bottom">
                                                <div class="compete-bottom-left">
                                                    <span class="left-btu"><span class="iconfont lujing2"></span>查看报表</span>
                                                </div>
                                                <img class="compete-bottom-img" src="../../../static/img/provincecount/<EMAIL>" alt=""/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>

                    <div class="workStage-content">
                        <!-- 登入-->
                        <!--              <div class='login'><input type="tel" onkeyup="value=value.replace(/[^\d]/g,'')" v-model="telnum"-->
                        <!--                                        maxlength="11" placeholder='请输入客户号码'>-->
                        <!--                <div @click="csViewLogin" class='log-in'>登入</div>-->
                        <!--              </div>-->

                        <!--              <div></div>-->
                        <div class='login-AI'>
                            <!-- 登入-->
                            <div class='login'><input type="tel" onkeyup="value=value.replace(/[^\d]/g,'')" v-model="telnum"
                                                      maxlength="11" placeholder='请输入客户号码'>
                                <div @click="csViewLogin" class='log-in' id='workStageToCsView'>登入</div>
                            </div>

                            <!--网格助手-->
                            <div class='ai-assistant' @click='goAiAssistant()' id='aiMarketAssistant'>
                                <img class='ai-img' src='../../../static/img/workStage/ai-assistant.png' alt=''/>
                                <span class='ai-title'>网格助手</span>
                            </div>
                        </div>

                        <!-- 任务专区-->
                        <div class='title' v-show="!showXTZQ()"><p>大商机</p>
                            <div @click="goTask()" id='myTaskWorkSatgeBtn'>我的任务</div>
                            <!--                    <div @click="goTask()">更多</div>-->
                        </div>

                        <div class='service-content' v-show="!showXTZQ()">
                            <ul class='recommendation-list'>
                                <!--                  <li  @click="openTask(item)"
                                            v-show="workStrogeCountList && workStrogeCountList.length>0"
                                            v-for="(item,index) in workStrogeCountList" :key="index">
                                            <img :src="getTaskPicture(index)" alt=''>
                                            <span>{{item.label|getTaskName}} <div class="task-num">{{item.num}}
                                              <span  class="task-num-fenge">/</span>
                                              {{item.passPer}}</div></span>
                        &lt;!&ndash;                    <div><em class='iconfont you'></em></div>&ndash;&gt;
                                          </li>-->

                                <li @click="goShengJiOrder('2')" id='earlyWarningWorkSatgeBtn'>
                                    <!--                        <li @click="goTask('1', sjyjTaskItem)">-->
                                    <img src="static/img/serviceBar/sjyjshow.png" alt=''>
                                    <div class="task-content">
                                        <!--                            <div class="task-title">{{ sjyjTaskItem.label }}</div>-->
                                        <div class="task-title">预警</div>
                                        <div class="task-value">
                                            <div class="value-option">
                                                <div class="value">{{ sjyjTaskItem.taskCount }}</div>
                                                <div class="value-unit">数量</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div><em class='iconfont you'></em></div>
                                </li>

                                <li @click="goBusiOppo()" id='businessOpportunityWorkSatgeBtn'>
                                    <img src="static/img/serviceBar/contract.png" alt=''>
                                    <div class="task-content">
                                        <div class="task-title">商机</div>
                                        <div class="task-value">
                                            <div class="value-option">
                                                <div class="value" v-show="busiOppoTaskCount != 0">{{ busiOppoTaskCount }}</div>
                                                <div class="value" v-show="busiOppoTaskCount == 0">0</div>
                                                <div class="value-unit">数量</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div><em class='iconfont you'></em></div>
                                </li>
                            </ul>
                            <div v-show="workStrogeCountList.length == 0 && !weekPlanXinFlag" class="recommendation-img">
                                <img src="../../assets/img/default-nodata.png" />
                                <p>暂无待办任务～</p>
                            </div>
                        </div>


                        <!--常用功能-->
                        <div class='title' v-show="showYWBL()"><p>业务办理</p>
                            <div @click="editHotMenu()">编辑</div>
                        </div>

                        <div class="cs-usufun" v-show="showYWBL()">
                            <section class="Common-functional-modules  change-height" >
                                <GridMenuList v-show="commonFnList && commonFnList.length>0" :menuList="commonFnList" comeFrom="/workStage"></GridMenuList>
                                <p class="empty-menu-p" v-show="!commonFnList||commonFnList.length==0">“点击右上角“编辑”按钮，轻松定制您的专属菜单！让您最常用的功能触手可及”</p>
                            </section>
                        </div>

                        <!--协同专区-->
                        <div class='title' v-show="showXTZQ()"><p>协同专区</p>
                        </div>
                        <div class="cs-usufun" v-show="showXTZQ()">
                            <section class="Common-functional-modules  change-height" >
                                <GridMenuList v-show="xieTongFnList && xieTongFnList.length>0" :menuList="xieTongFnList" comeFrom="/workStage"></GridMenuList>
                            </section>
                        </div>



                        <!-- 培训专区-->
                        <div class='title' v-show="!showYWBL()"><p>AI学堂</p>
                            <div @click="toMyStudy">更多</div>
                        </div>
                        <div class='peixun-content' v-show="!showYWBL()" @click="toMyStudy">
                            <img src='../../../static/img/workStroge/workStroge-xuexi.png' alt=''>
                            <div class="peixun-shichang">
                                <div class="peixun-shichang1">学习时长</div>
                                <div class="peixun-shichang2">{{studyMsg.totalTime}}<span class="peixun-shichang3">分钟</span></div>
                            </div>
                            <div class="peixun-shuxian"></div>
                            <div class="peixun-xiaozi">
                                <div class="peixun-xiaozi1">总积分</div>
                                <div class="peixun-xiaozi2">{{studyMsg.intergral}}<span class="peixun-xiaozi3">分</span></div>
                            </div>
                            <div class="peixun-xiaozi">
                                <span class="peixun-xiaozi1">全省排名</span>
                                <span class="peixun-xiaozi2">{{studyMsg.rankId}}<span class="peixun-xiaozi3">/{{studyMsg.allNum}}</span></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-show="weekPlanXinFlag && weekMoreShowFlag" class="workStage-banner-touming"></div>

                <div v-show="!weekPlanXinFlag">
                    <!-- 菜单搜索框-->
                    <div class="menu-search-container" v-show="!weekPlanXinFlag">
                        <div class="menu-search-box">
                            <i @click="qryAct" class="iconfont sousuo"></i>
                            <input class="menu-box" id="inputBox1" ref="queryKey" v-model="queryKey" @keyup.13="qryAct" @focus="foucusInput" @blur="blueInput"  placeholder="输入菜单名检索"/>
                            <i @click="clear" v-show="queryKey" class="iconfont shanchu"></i>
                            <span  class="menu-search-txt" @click="qryAct">搜索</span>
                        </div>
                    </div>

                    <!--关键信息区域-->
                    <div class="main-info-box" >
                        <!--左侧登录信息 @click="showStation()"-->
                        <div class="login-info-box">
                            <div class="login-info"><span>{{userInfoData.operatorName}}</span></div>
                            <a class="log-btn busi-log" @click="gobusilogseach"><span class="iconfont rizhi"></span><span>日志</span><span class="iconfont jiantou-copy-copy"></span></a>
                        </div>
                        <!--右侧关键数据展示区域 style="z-index:-1"-->
                        <div class="date-show-right">
                            <ul class="date-show-box">
                                <li>
                                    <span class="data-text">{{workBenchHeadData.todoNum ||'--'}}</span>
                                    <h4 class="data-title">待办</h4>
                                </li>
                                <li>
                                    <span class="data-text">{{workBenchHeadData.focuseNum ||'--'}}</span>
                                    <h4 class="data-title">关注</h4>
                                </li>
                                <li @click="goBusinessIncome()">
                                    <span class="data-text">{{formatMoney(workBenchHeadData.todaySum) ||'--'}}<span class="data-title"></span></span>
                                    <h4 class="data-title">金额(元)</h4>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <hr v-show="showStationFlg" class="station-line"/>
                    <!--地址区域-->
                    <div class="location-box" @click="getClientTrueLocal()">
                        <span class="iconfont dingweiweizhi"></span>
                        <span class="location-text">{{getLocation}}</span>
                        <span class="click-area" @click.stop="addrFull=!addrFull"><i class="iconfont diandiandian"></i></span>
                        <div class="add-full-wrapper" v-show="addrFull" @click.stop="addrFull=!addrFull">
                            <span class="dao-sanjiao"></span>
                            {{getLocation}}
                        </div>
                    </div>
                    <!--信息区域-->
                    <div class="info-box" @click="goNoticeDetail()">
                        <span class="iconfont Shape1"></span>
                        <LatestNews :tsNoticeInfo="noticeInfo"></LatestNews>
                        <a class="more-btn more-size">更多</a>
                    </div>
                    <!--底部装饰线条-->
                    <div class="decoration-line"></div>
                </div>
            </div>
            <!--       <div v-show="!weekPlanXinFlag">-->

            <div v-show="!weekPlanXinFlag && !isBigHead" class="simple-head-wrap" ref="smallHead">
                <p class="login-name">{{userInfoData.operatorName}}</p>
                <div class="mid-num">
                    <span>{{workBenchHeadData.todoNum}}</span>
                    <span>{{workBenchHeadData.focuseNum}}</span>
                    <span>{{workBenchHeadData.todayBalance}}</span>
                </div>
                <p class="up-arrow" @click="openHead()">
                    <span><i class="iconfont jiantou"></i></span>
                </p>
                <div class="decoration-line"></div>
            </div>

            <!--        <div v-show="!weekPlanXinFlag" class="work-content-wrap">
            <div v-show="isBigHead" class="mid-wrap" ref="menuDiv">
                &lt;!&ndash;客户视图 或者集团视图&ndash;&gt;
                <div class="telnum-wrap">
                    <div class="input-wrap" v-show="currentView.viewId==4">
                        <span class="title " @click="showView()">{{currentView.txt}} <span class="iconfont jiantou2"
                                                                                           v-show="viewInfoList && viewInfoList.length > 1"></span><i
                                class="line"></i></span>
                        <input type="tel" onkeyup="value=value.replace(/[^\d]/g,'')" v-model="telnum" maxlength="11"
                               placeholder="请输入客户手机号"/>
                        <a class="btn" @click="csViewLogin">登录</a>
                    </div>
                    <div class="input-wrap" v-show="currentView.viewId !=4 ">
                        <span class="title" @click="showView()">{{currentView.txt}}<span class="iconfont jiantou2"
                                                                                         v-show="viewInfoList && viewInfoList.length > 1"></span></span>
                        <input type="text" @focus="goSearch(currentView.viewId)" placeholder="请输入名称"/>
                        <a class="btn" @click="goSearch(currentView.viewId)">
                            <span class="iconfont qudaoshituditu" @click.stop="goMap"></span>搜索
                        </a>
                    </div>
                </div>

                &lt;!&ndash;知识点,没有待办的时候展示&ndash;&gt;
                <ActiveInfo :tsActiveInfoData="activeInfoData"
                            v-show="workBenchHeadData.todoNum==0 && activeInfoData && activeInfoData.length > 0"></ActiveInfo>
            </div>
            <div class="weekplan-wrap" >
                <WeekPlan :weeks="1" comeFrom="desk" :type="typeNum" @selectDay="queryList"></WeekPlan>
            </div>

            &lt;!&ndash; v-show="showTodo" &ndash;&gt;
            <section class="todo-wrap" style="padding: 0">
                <div class="navbar-wrapper" :class="{active:typeNum == '4'}" ref="navDiv">
                    <nl-nav-bar page-src="desk" @chooseTab="taskFilter">
                        &lt;!&ndash; :class="{'active':emergentFlag==0}" &ndash;&gt;
                        <a class="emergent-btn" v-show="emergentFlag==1 && showEmergent==true" @click="emergentCk()">紧急</a>
                        <a class="emergent-btn" v-show="emergentFlag==0 && showEmergent==true" @click="emergentCk()">全部</a>
                    </nl-nav-bar>
                </div>
                <TodoList :tsTypeNum="typeNum"
                          :tsQueryDay="queryDay"
                          :tsEmergentFlag="emergentFlag"
                          :tsHeight="todoHeight"
                          :tsPeopleType="peopleType"
                          @emClick="selectItem"
                          @collapseHead="collapseHead">
                </TodoList>

            </section>
        </div>-->
            <!--      </div>-->
            <div class="searchBTn2"  v-show="foucusFlag && isIos">
                <img src="../../assets/img/huashu.png" @touchstart='handlerTouchstart' @touchend='handlerTouchend'/>
            </div>
            <div class="searchBTn"  v-show="foucusFlag && !isIos">
                <img src="../../assets/img/huashu1.png" @touchstart='handlerTouchstart' @touchend='handlerTouchend'/>
            </div>
            <div class="loading-chat" :style="isIos? 'top: 50%; bottom: unset;transform: translate(-50%, -50%);': ''">
                <div class="img"><img src="../../assets/img/chatloading.gif" /></div>
                <div>正在录音识别中...</div>
            </div>
            <div class="hide-box" @click="closeLuyin"></div>
            <div class="view-wrapper" :class="{'show':viewListFlg}">
                <p class="dao-sanjiao"></p>
                <ul class="view-ul">
                    <li class="view-li" @click="changeView(viewClassMap[item])" v-for="item in viewInfoList" :key="item">
                        <p class="icon-wrap">
                        <span class="icon-img" :class="viewClassMap[item] && viewClassMap[item].bgClz"><i class="iconfont"
                                                                                                          :class="viewClassMap[item]&&viewClassMap[item].iconClz"></i></span>
                        </p>
                        <span class="icon-name">{{viewClassMap[item] && viewClassMap[item].txt}}</span>
                    </li>
                </ul>
            </div>
            <div class="panc-tips" v-show="tips">
                <div class="tips-head">
                    <h3 class="head-text">温馨提示</h3>
                    <span class="close-seds">{{time}}s后自动关闭</span>
                    <i class="iconfont guanbi" @click="close"></i>
                </div>
                <div class="tips-main">
                    <p class="tips-txt">{{pushMsg}}</p>
                </div>
            </div>
            <!-- <div class="AIchat" @click="goAIchat" v-if="AiRole">
            <div class="text" v-if="firstAiFlag">
                <img src="../../assets/img/aicontent.png"/>
            </div>
            <div class="pic">
                <img src="../../assets/img/you.gif"/>
            </div>
        </div> -->
            <!--      <HomePageView v-show='isShowDetail'
                    :weekPlanXinFlag="weekPlanXinFlag && weekPlanType !='3'"
                    :isShowDetail.sync="isShowDetail"
                    :typeNum="typeNum"
                    :typeFilter="typeFilter"
                    :emergentFlag="emergentFlag"
                    :queryDay="queryDay"
                    :tsHeight="todoHeight"
                    :tsPeopleType="peopleType"
                    @workStrogeQueryCount="workStrogeQueryCount"
                    @selectItem="selectItem"/>-->

            <mt-popup v-model="isShowTreeBox" position="bottom" :closeOnClickModal="false">
                <div class="select-tree-box">
                    <div class="select-top">
                        <span class="cancel" @click="cancelSelect">取消</span>
                        <span>请选择</span>
                        <span class="confirm" @click="closeSelectBox">确认</span>
                        <!--            <span class="iconfont guanbi1" @click="closeSelectBox"></span>-->
                    </div>
                    <div class="content-space">
                        <div class="select-content">
                            <div class="content-option" v-for="(item, index) in selectOrgTree" :key="index" @click="clickLevel(item)">
                                <div class="option-left">
                                    <div class="left-line">
                                        <div class="line" v-show="index !== 0"></div>
                                        <div class="icon" :class="{'background': item.orgIdChild !== '0'}"></div>
                                    </div>
                                    <span class="option-name" :class="{'select-level': item.treeLevel === selectOrgInfo.treeLevel}">{{ item.orgNameChild }}</span>
                                </div>
                                <span class="iconfont youjiantou1" v-show="index !== 0"></span>
                            </div>
                        </div>
                        <div class="option-list-content" v-show="orgInfoList && orgInfoList.length > 0">
                            <div class="option-list-title">{{ selectOrgInfo.treeLevel | getNextName }}</div>
                            <span class="option-item" :class="{'select-option': item.orgIdChild === selectOrgInfo.orgIdChild}"
                                  v-for="(item, index) in orgInfoList" :key="index"
                                  @click="selectOption(item)">
                {{ item.orgNameChild }}
              </span>
                        </div>
                    </div>
                </div>
            </mt-popup>

            <WorkStageShuJuTip v-show='isShowShuJuTip'
                               :showShuJuTipInfo="showShuJuTipInfo"
                               @closeShuJuTip="closeShuJuTip"/>
        </div>

        <ProvinceCountPage v-show="isShowProvincial"
                           ref="provinceCountPage"
                           :userInfo="uinfo"
                           :statDate="statDate"
                           :roleName="userIdentity"
                           :opNowPath="opNowPath"
                           :provincialIndexList="provincialIndexList"
                           :provincialIndexJson="provincialIndexJson"
                           :myInfoTuoMingFlag ="myInfoTuoMingFlag"
                           @IndexDataTime="IndexDataTime"/>
        <!-- 零菜单-->
        <inlet-menu v-if='showOcr' :showPicture='true' srcFrom='work'></inlet-menu>
        <!-- 掌上家客首页-->
        <div class='out-switch-page' v-if='jiaKeHomeFlg'>
            <outH5MidPage fromPage='homePage'></outH5MidPage>
        </div>
    </div>
</template>

<script>
import NlNavBar from 'components/common/NlNavBar.vue'
import LatestNews from 'components/common/LatestNews.vue'
import LatestXin from 'components/desk/WorkStageXin/LatestXin.vue'
import workSlide from 'components/desk/WorkStageXin/WorkSlide.vue'
import WorkStageShuJuTip from 'components/desk/WorkStageXin/WorkStageShuJuTip.vue'
import TodoList from './TodoListNew.vue'
import ActiveInfo from 'components/common/ActiveInfo.vue'
import Storage from '@/base/storage'
import StationList from 'components/desk/StationList'
import WeekPlan from 'components/common/WeekPlan'
import WeekPlanXin from 'components/desk/WorkStageXin/WeekPlanXin.vue'
import {dateFormat} from '@/base/utils'
import {clientLocalMixin, needNumMixin,tokenFromAldMixin} from '@/base/mixin'
import {mapMutations} from 'vuex'
import ClientJs from '@/base/clientjs'
import {iEncrpt} from '@/base/encrptH5.js'
import Authenct from 'components/common/uniteauth/index.js'
import HomePageView from './HomePageView.vue'
import RecorderManger from '@/base/chatUtil/RecorderManager.js'
import GridMenuList from 'components/common/GridMenuList.vue'
import ProvinceCountPage from './ProvinceCountPage.vue'
import InletMenu from 'components/business/ZeroMenu/InletMenu.vue'
import outH5MidPage from '@/components/desk/jiaKeInstallMain/outH5MidPage.vue'
import { menuChain } from '@/base/mixins/menuChainMixin'
import {menuLogRecord, checkScreenShot} from '@/base/request/commonReq.js'

const midDomHeight = 55;//常用功能110、客户号码输入框的高度55
// 城市长编码
const LONG_CITY_LIST = [
    {id: '1000250', shortId: '14', label: '南京'},
    {id: '1000510', shortId: '19', label: '无锡'},
    {id: '1000511', shortId: '18', label: '镇江'},
    {id: '1000512', shortId: '11', label: '苏州'},
    {id: '1000513', shortId: '20', label: '南通'},
    {id: '1000514', shortId: '23', label: '扬州'},
    {id: '1000515', shortId: '22', label: '盐城'},
    {id: '1000516', shortId: '16', label: '徐州'},
    {id: '1000517', shortId: '12', label: '淮安'},
    {id: '1000518', shortId: '15', label: '连云港'},
    {id: '1000519', shortId: '17', label: '常州'},
    {id: '1000523', shortId: '21', label: '泰州'},
    {id: '1000527', shortId: '13', label: '宿迁'},
    {id: '2000250', shortId: '99', label: '省'}
]

export default {
    mixins: [clientLocalMixin, needNumMixin,tokenFromAldMixin,menuChain],
    data() {
        return {
            tabhead: '1',
            busiOppoTaskCount:0,
            isIos: false,
            foucusFlag: false,
            luyinFlag: false,
            rec: null,
            recwave: {input: function(){}},
            isBigHead: true,//默认展示完整头部信息
            //常用功能数据和热门数据
            commonFnList: [],
            isHaveToDo: 1,//0代表有 1代表无
            //工作台和活动推荐信息
            workBenchHeadData: {},

            //公告信息
            noticeInfo: [],

            //热门或者常用文字
            hotOrUsualTxt: '',

            //活动推荐数据
            activeInfoData: [],

            //个人信息
            userInfoData: {},
            showEmergent: true,
            //获取待办分类点击类型num
            typeNum: 0,
            queryDay: '',//查询日期，默认为今天
            //紧急按钮切换
            emergentFlag: 1,     //0 紧急，1 全部
            todoHeight: 300,
            touchStartY: 0,      //开始滑动y坐标的值
            touchEndY: 0,        //结束滑动y坐标的值
            showStationFlg: false,//默认不展示岗位列表
            curStationId: '',//当前岗位
            // stationsArr:[],//组装岗位列表
            telnum: '',//客户电话号码
            viewListFlg: false,//是否展示视图列表
            currentView: {viewId: '4', txt: '客户视图', bgClz: 'kehu-bg', iconClz: 'kehu'},
            viewClassMap: {
                "1": {viewId: '1', txt: '集客视图', bgClz: 'jituan-bg', iconClz: 'jituan'},
                "2": {viewId: '2', txt: '小区视图', bgClz: 'xiaoqu-bg', iconClz: 'xiaoquzhaofang'},
                "3": {viewId: '3', txt: '渠道视图', bgClz: 'qudao-bg', iconClz: 'qudao1'},
                "4": {viewId: '4', txt: '客户视图', bgClz: 'kehu-bg', iconClz: 'kehu'},
                "5": {viewId: '5', txt: '高校视图', bgClz: 'school-bg', iconClz: 'xuexiao1'},
                "6": {viewId: '6', txt: '楼宇视图', bgClz: 'building-bg', iconClz: 'louyu'},
                "7": {viewId: '7', txt: '清单集团视图', bgClz: 'listGroup-bg', iconClz: 'bianzu8'}
            },
            addrFull: false,//是否展示全地址
            uinfo : {},
            queryKey:'',//搜索关键字
            pushMsg: '', //甩单提示
            tips: false,
            time: 5,
            versionList: {
                "s000000077351": "", //该参数有4个枚举值，分别是“一级升级投诉用户”、“二级升级投诉用户”、“三级升级投诉用户”、“非升级投诉用户”；其中只有传“非升级投诉用户”时，不弹出弹窗。
            },
            peopleType: 'xs',// 双向协同人员类型: 行商人员:xs 装维人员:zw
            // firstAiFlag: false,
            // AiRole: false

            userIdentity:"",//身份
            isShowTip:false,//是否展示消息
            showDingWeiFlag:false,//是否展示地址
            weekPlanXinFlag:true,//展示新的首页
            weekPlanType:"1",//1.默认本地新首页 ,2.直销首页 3.随销首页
            weekSheQuStation:['9988021754066058','10000601','10009102','10009101'],//社区店岗位
            zhiBiaoList:[],//指标数据
            reportFormTitleList:[],//报表头
            reportFormDataList:[],//报表体
            zhiBiaoRoleType:"",//角色类型编号
            studyMsg:"", //学习信息
            workStrogeCountList:[],//任务数量
            isShowDetail: false,
            typeFilter: {},
            isShowShuJuTip:false,//是否展示数据详情
            showShuJuTipInfo: {},//打开数据详情
            isShowTreeBox: false,
            selectItem1: {},
            orgInfoList: [],
            statDate: "",
            chanList: [],
            selectOrgTree: [],
            selectOrgInfo: {},
            resultType: 1,
            weekMoreShowFlag:false,//更多
            roleId: "",

            dangYueTip:'当月',
            sheQuList: {
                "biSuanNum":'--',
                "kuanDaiNum":'0',
                "dianShiNum":'0',
                "zuWangNum":'0',
                "anFangNUm":'0',
                "haoKaNum":'0',
                "dianShiVipNum":'0',
                "quanYiNum":'--'
            },
            xieTongFnList:[],//协同专区菜单

            indexFlag: "2",           //指标数据查询标识 1：省统  2：本地
            provincialIndexList: [],  //省统指标数据
            provincialIndexJson: {
                chbnItem: {indexValue: ""},
                chnItem: {indexValue: ""},
                bItem: {indexValue: ""},
                txCountItem: {indexValue: ""},
                kdCountItem: {indexValue: ""},
                pzCountItem: {indexValue: ""},
                kdAddItem: {indexValue: ""},
                tvAddItem: {indexValue: ""},
                fttrAddItem: {indexValue: ""},
                channelItem: {indexValue: ""},
                effectChannelItem: {indexValue: ""}
            },
            isShowProvincial: false,  //是否展示省统页面
            opNowPath: [],            //操作员当前所在路径
            isShowChangeBtu: false,
            wcTaskItem: {}, //外场任务
            yjTaskItem: {}, //预警任务
            rwgjTaskItem: {}, //任务跟进任务
            tzTaskItem: {},   //通知任务
            sjyjTaskItem: {},   //省级预警任务
            //商机任务
            warnProdValue: "",  //预警产能信息
            homePageInfoObj: {},   //新首页信息
            // showPicFlag: false,//是否展示扫一扫ocr功能
            showOcr: false,//打开零菜单功能
            isSecond: "0",
            weekSheQuStationFlag:false,//社区版可切换
            myInfoTuoMingFlag:true,//,脱敏


            showCompete: false, //是否展示竞比对数据
            chooseCompete: true, //是否选择竞对比
            competeData: [], //竞比对数据
            isNewCompete: true, //是否是新的竞比对
            kanban: {},
            kanban2: {},
            kanban3: {},
        }
    },
    components: {
        ProvinceCountPage,
        HomePageView,
        NlNavBar,
        LatestNews,
        TodoList,
        ActiveInfo,
        StationList,
        WeekPlan,
        LatestXin,
        workSlide,
        WeekPlanXin,
        WorkStageShuJuTip,
        GridMenuList,
        InletMenu,
        outH5MidPage
    },
    mounted() {
        if (!(/android|harmony/gi.test(navigator.userAgent))) {
            this.isIos  = true
        }
        let h = window.screen.availHeight || window.document.body.offsetHeight;
        //console.info(h,this.$refs.topDiv.clientHeight,this.$refs.navDiv.clientHeight,midDomHeight)
        this.todoHeight = h - this.$refs.topDiv.clientHeight - 60 - 80 - 22 - 24 - midDomHeight;//头部的高度 底部的高度 日历的高度
        window['clientCallback'] = (result) => {
            this.$router.push('/emptyReload');//刷新页面
        }
        window['versionBack'] = (res) =>{
            var obj = eval('(' + res + ')');
            Storage.set('clientVersion',obj.verison);
        }

        let AIflag = localStorage.getItem('AIflag')
        if (AIflag) {
            this.firstAiFlag = false
        } else {
            this.firstAiFlag = true
            localStorage.setItem('AIflag','1')
        }

        window['recordLoaction'] = (result) => {
            let locationParam = result;
            this.judeRecordLoaction(locationParam);
        };


        // 录音操作
        window['getAudioPermissfun'] = (res) => {
            this.rec = new RecorderManger({}, '', this.getRecordResultCbFn);
            sessionStorage.setItem('hasSetAudio',1)


        }
        const firstHeight = window.innerHeight
        const that = this
        window.addEventListener('resize', () => {
            console.info('屏幕高度变化了：', window.innerHeight,firstHeight);
            if (window.location.href.indexOf('workStage') >= 0) {
                if (firstHeight == window.innerHeight) {
                    document.getElementById("inputBox1").blur();
                    that.luyinFlag = false
                }
            }

        });
        document.addEventListener("visibilitychange", function() {
            let vState = document.visibilityState
            if (window.location.href.indexOf('workStage') >= 0) {
                if (vState === 'hidden') {  // 当页面由前端运行在后端时，出发此代码
                    console.info('我被隐藏了')
                    if (that.rec) {
                        that.rec && that.rec.recorder.close();
                        that.rec = null
                        that.luyinFlag = false
                        document.getElementById("inputBox1").blur();
                    }
                }
                if (vState === 'visible' ) {   // 当页面由隐藏至显示时
                    console.info('我被显示了')
                }
            }
        });
    },
    created() {
        // 记录菜单链一级菜单链 edit by qhuang 7.15
        this.initFirstMenuLevel(this.CONSTVAL.MENU_CHAIN.WORK_STAGE);

        this.uinfo = Storage.session.get('userInfo');
        // this.getAiRole()//注释by qhuang

        // add by qhuang 公告查询
        this.qryNotice();
        this.queryDay = dateFormat(new Date(), "yyyyMMdd");
        this.getUserInfo();
        this.getData();
        this.viewInfoLists();
        this.orderTip();
        this.handleStationSwitch()
        //通过手机号获取userId,再调用能运接口获取用户最近一次通话和最近一次流量使用的地点(具备按地市开启功能)
        // this.qryLoginUserLocation();
        //清除调用链业务菜单流水，防止被错误采集 add by qhuang
        this.$pointLesslog && this.$pointLesslog.setChainBusiSeq('');
        // 当前操作员是否为装维人员
        if (this.uinfo && ['10000601','10009102'].includes(this.uinfo.stationId)) {
            this.peopleType = 'zw';
        }
        this.queryThisLevel();
        if (this.$route.query.indexFlag && this.$route.query.indexFlag==="1") {
            this.indexFlag = "1";
            this.isShowProvincial = true;
            this.IndexDataTime("1");
        } else {
            this.indexFlag = "2";
        }
        //指标数据查询
        this.IndexDataPermission();
        //查询是否脱敏
        let myInfoTuoMingSum = Storage.session.get('myInfoTuoMingFlag');
        if(myInfoTuoMingSum){
            if(myInfoTuoMingSum =='1'){
                this.myInfoTuoMingFlag = true;
            }else{
                this.myInfoTuoMingFlag = false;
            }
        }else{
            this.myInfoTuoMingQuery();
        }
    },
    beforeRouteEnter(to, from, next) {
        next(vm => {
            let l = Storage.get('location');
            if (l) {
                //vm.$store.commit('setLocal',l);
                vm.$store.commit('SET_LOCAL', Storage.get('location'));
            }
        });
    },
    filters:{
        tuoMingName(val,myInfoTuoMingFlag) {
            if (myInfoTuoMingFlag && val) {
                let name = val[0];
                for (let i = 1; i < val.length; i++) {
                    name += "*";
                }
                return name;
            }else{
                return val;
            }
        },
        //取整
        quZheng(val){
            if(val && val !='--'){
                return parseInt(val);
            }else{
                return 0;
            }
        },
        //校验任务名长度
        getTaskName(label){
            if (label){
                if (label.length > 6){
                    return label.substring(0,5)+'...';
                }else{
                    return label;
                }
            }
        },
        checkNull(indiFinish) {
            if (indiFinish && indiFinish !="null") {
                return indiFinish;
            } else {
                return "--";
            }
        },

        getNextName(val) {
            if (val === 0) {
                return "请选择地市";
            } else if (val === 1) {
                return "请选择区/县"
            } else if (val === 2) {
                return "请选择网格";
            } else if (val === 3) {
                return "请选择网格经理";
            } else if (val === 4) {
                return "请选择渠道"
            }
        },

        getNum(val) {
            if (val && val !== 'null') {
                return val;
            } else {
                return '--';
            }
        },

        unitConversion(value, isNewCompete) {
            if (value && value !== 'null') {
                if (isNewCompete && Number(value) && !isNaN(Number(value))) {
                    // 先将元转换为万元
                    let tenThousand = Number(value) / 10000
                    return parseFloat(tenThousand.toFixed(2)) + "";
                }
                return value;
            }
            return "--"
        }
    },
    computed: {
        //视图列表
        // viewInfoList() {
        //     let list = this.userInfoData.viewInfoList;
        //     if (list && list.length > 0) {
        //         this.currentView = this.viewClassMap[list[0]];
        //     }
        //     return list;
        // },
        //获取vuex中存的定位信息
        getLocation() {
            let self = this;
            let loc = this.$store.getters.locationTxt;
            if (!loc && loc != 'empty') {//初始化进来的时候  取不到定位信息
                setTimeout(() => {
                    if(!Storage.get('location') || Storage.get('location')=='empty'){//没有获取到定位
                        this.getLocationFromAldParent();//从服务端获取
                    } else {
                        self.$store.commit('SET_LOCAL', Storage.get('location'));
                    }
                }, 1000)
            }
            if(loc == 'empty'){
                return '请点击刷新'
            } else {
                return loc;
            }
        },
        showTodo() {
            if (this.workBenchHeadData && this.workBenchHeadData.todoNum != 0) {
                return true;
            }
            return false;
        },
        jiaKeHomeFlg(){//装维随销双首页
            let currentStationId = this.$store.state.stationChangeModule.currentStationId;
            return this.$store.state.stationChangeModule.jiaKeHomeFlg && ('10000601' === currentStationId);
        }
    },
    //keep-alive 的钓子函数
    activated() {
        this.getLocation();//更新定位信息
        this.getUserInfo();
        this.getData();
        this.viewInfoLists();
        this.foucusFlag = false
        if (!(/android|harmony/gi.test(navigator.userAgent))) {
            this.isIos  = true
        }
        this.initFirstMenuLevel(this.CONSTVAL.MENU_CHAIN.WORK_STAGE);
    },
    deactivated(){
        this.rec && this.rec.recorder.close();
        this.rec = null
        document.removeEventListener('visibilitychange', () => {});
    },
    beforeCreate() {
        this.uinfo = Storage.session.get('userInfo');
        let uinfo = this.uinfo;
        if(uinfo && (this.uinfo.stationId==this.CONSTVAL.STATION_KHJL)){
            console.info('=====ifZqstRoute=======')
            this.$router.replace('/workStageNew');
        } else if (uinfo && (~this.CONSTVAL.SKZQ_ACCESS_STATIONS.indexOf(uinfo.stationId))) {
            this.$router.replace('/skzqHome')
        } else if (uinfo && uinfo.stationId == this.CONSTVAL.STATION_GZT_KHJL) {
            this.$router.replace('/zqWorkStage')
        }
    },
    methods: {
        getPreviousDayDate() {
            // 创建一个新的Date对象，表示当前日期
            const today = new Date();

            // 创建一个新的Date对象，表示前一天的日期
            // 通过减去一天的毫秒数（24小时 * 60分钟 * 60秒 * 1000毫秒）
            const previousDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() - 2);

            // 格式化日期为 yyyymmdd
            const formattedDate = previousDay.getFullYear().toString().padStart(4, '0') +
                (previousDay.getMonth() + 1).toString().padStart(2, '0') + // 月份是从0开始的，所以要加1
                previousDay.getDate().toString().padStart(2, '0');

            return formattedDate;
        },
        getPreviousDayDate2() {
            // 创建一个新的Date对象，表示当前日期
            const today = new Date();

            // 创建一个新的Date对象，表示前一天的日期
            // 通过减去一天的毫秒数（24小时 * 60分钟 * 60秒 * 1000毫秒）
            const previousDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() - 2);

            // 格式化日期为 yyyymmdd
            const formattedDate = (previousDay.getMonth() + 1).toString().padStart(2, '0') + '.' + previousDay.getDate().toString().padStart(2, '0');

            return formattedDate;
        },
        getYJinfo(orgId){
            let param2 = {
                orgId : orgId,
                unLoadFlg:true,//屏蔽加载圈
            }
            this.$http.post('/xsb/gridCenter/familyDiagnosis/h5QryWarnBoardInfo',param2).then((res) => {
                let body = res.data;
                if (body.retCode === '0') {
                    if (res.data.data) {
                        this.kanban3 = res.data.data
                    } else {
                        return
                    }

                    if(this.kanban3.startDate) {
                        console.log('this.kanban3.startDate',this.kanban3.startDate)
                        let monthStr = this.kanban3.startDate.substring(4, 6);
                        monthStr = parseInt(monthStr, 10)
                        this.kanban3.monthStr = monthStr
                    }
                    if (this.kanban3.yjOrderHb.indexOf('-') > -1) {
                        this.kanban3.yjOrderHb1 = 'red'
                        let result = this.kanban3.yjOrderHb.substring(1)
                        this.kanban3.yjOrderHb = result.replace('%', 'PP')
                    }
                    if (this.kanban3.customerStayHb.indexOf('-') > -1) {
                        this.kanban3.customerStayHb1 = 'red'
                        let result = this.kanban3.customerStayHb.substring(1)
                        this.kanban3.customerStayHb = result.replace('%', 'PP')
                    }
                    if (this.kanban3.valueStayHb.indexOf('-') > -1) {
                        this.kanban3.valueStayHb1 = 'red'
                        let result = this.kanban3.valueStayHb.substring(1)
                        this.kanban3.valueStayHb = result.replace('%', 'PP')
                    }
                    if (this.kanban3.orderFinishHb.indexOf('-') > -1) {
                        this.kanban3.orderFinishHb1 = 'red'
                        let result = this.kanban3.orderFinishHb.substring(1)
                        this.kanban3.orderFinishHb = result.replace('%', 'PP')
                    }
                    if (this.kanban3.orderSuccessHb.indexOf('-') > -1) {
                        this.kanban3.orderSuccessHb1 = 'red'
                        let result = this.kanban3.orderSuccessHb.substring(1)
                        this.kanban3.orderSuccessHb = result.replace('%', 'PP')
                    }
                    if (this.kanban3.orderUnusualHb.indexOf('-') > -1) {
                        this.kanban3.orderUnusualHb1 = 'red'
                        let result = this.kanban3.orderUnusualHb.substring(1)
                        this.kanban3.orderUnusualHb = result.replace('%', 'PP')
                    }
                    if (this.kanban3.orderMeetHb.indexOf('-') > -1) {
                        this.kanban3.orderMeetHb1 = 'red'
                        let result = this.kanban3.orderMeetHb.substring(1)
                        this.kanban3.orderMeetHb = result.replace('%', 'PP')
                    }
                    if (this.kanban3.validGaiusHb.indexOf('-') > -1) {
                        this.kanban3.validGaiusHb1 = 'red'
                        let result = this.kanban3.validGaiusHb.substring(1)
                        this.kanban3.validGaiusHb = result.replace('%', 'PP')
                    }
                    this.kanban3.yjOrderHb = this.kanban3.yjOrderHb.replace('%', 'PP')
                    this.kanban3.customerStayHb = this.kanban3.customerStayHb.replace('%', 'PP')
                    this.kanban3.valueStayHb = this.kanban3.valueStayHb.replace('%', 'PP')
                    this.kanban3.orderFinishHb = this.kanban3.orderFinishHb.replace('%', 'PP')
                    this.kanban3.orderSuccessHb = this.kanban3.orderSuccessHb.replace('%', 'PP')
                    this.kanban3.orderUnusualHb =this.kanban3.orderUnusualHb.replace('%', 'PP')
                    this.kanban3.orderMeetHb =this.kanban3.orderMeetHb.replace('%', 'PP')
                    this.kanban3.validGaiusHb =this.kanban3.validGaiusHb.replace('%', 'PP')
                }
            })
        },
        getSjInfo(){

            let param2 = {
                "region": this.uinfo.region,
                "unLoadFlg":true,//屏蔽加载圈
                "operatorTel": this.uinfo.servNumber
            }
            this.$http.post('/xsb/gridCenter/familyDiagnosis/h5QryCalculateBusiCoard',param2).then((res) => {
                let body = res.data;
                if (body.retCode === '0') {
                    this.kanban.viewFlag = body.data.viewFlag
                    const date = new Date();
                    const month = date.getMonth() + 1;
                    this.kanban.month = date.getMonth() + 1;
                    this.kanban.month2 = date.getMonth() + 1;
                    this.kanban.rangeName = body.data.rangeName

                    if(this.kanban.viewFlag == 1) {
                        if (this.weekSheQuStationFlag && (!this.showCompete || !this.chooseCompete)) {
                            this.tabhead = ''
                            this.weekSheQuStationCheck(5)
                            console.info(11111111111111111)
                        } else {
                            this.tabhead = 3
                            console.info(***********)

                        }
                        setTimeout(() => {
                            if (this.showCompete) {
                                this.changeCompete(true)
                                this.tabhead = 1
                                console.info(3333333333333333)

                            }
                        },100)
                        let orgin = ''

                        let url2 = `/xsb/personBusiness/gridViewSecond/h5QryScoreAndRank?telnum=${this.uinfo.servNumber}&unLoadFlg=true`;
                        this.$http.get(url2).then((res2) =>{
                            if (res2.data.retCode == "0") {
                                let data = res2.data.data;
                                orgin = data.orgaId2
                                this.getYJinfo(orgin)

                                this.kanban.rangeName = data.orgaName2

                                let today = this.getPreviousDayDate()
                                this.kanban.month  = this.getPreviousDayDate2()
                                let param3 = {
                                    unLoadFlg:true,//屏蔽加载圈
                                    "dataType": 0,
                                    "crmId": this.uinfo.crmId,
                                    "region": this.uinfo.region,
                                    "startDate": today,   //看板数据时间YYYYMMDD
                                    "queryType": "current",   // 查询类型：current  当前层级      parent  查询父级
                                    "orgId": orgin,              // current  当前层级 传这个
                                    "parentId": ''     //parent   查询父级  查这个
                                }
                                console.log('kanban--',param3)
                                this.$http.post('/xsb/gridCenter/familyDiagnosis/h5QryBusiBoardInfo', param3).then((res3) => {
                                    let body = res3.data
                                    console.info('body.body',body)

                                    if (body.retCode === '0') {
                                        this.kanban2 = body.data[0] ? body.data[0] : {}
                                        console.info('this.kanban2',this.kanban2)
                                    }
                                })
                            } else {
                                this.$alert(res.data.retMsg || "查询当前网格基本信息接口调用出错");
                            }
                        })




                    }
                }
            }).catch((response) => {
            });
        },
        goPersonalBusinessOpportunity(val){
            this.$router.push({
                path: '/PersonalBusinessOpportunity',
                query : {
                    kanbanType : val
                }
            })
        },
        //查询是否脱敏
        myInfoTuoMingQuery() {
            let param = {
                unLoadFlg: true
            }
            this.$http.post('/xsb/paperless/locationLimit/h5myInfoTuoMingQuery', param).then(res => {
                let { retCode, retMsg, data } = res.data
                if (retCode == '0') {
                    this.myInfoTuoMingFlag = true;
                    Storage.session.set('myInfoTuoMingFlag','1');
                } else {
                    this.myInfoTuoMingFlag = false;
                    Storage.session.set('myInfoTuoMingFlag','2');
                }
            }).catch((err) => {
                this.myInfoTuoMingFlag = false;
                Storage.session.set('myInfoTuoMingFlag','2');
            })
        },
        editHotMenu() {
            //常用菜单编辑
            let _this = this;
            this.$router.push({
                path: '/personMenuEdit',
                query: {
                    'personHotMenus': _this.commonFnList,
                    'srcFrom':'workStage'
                }
            });
        },
        openOcr(){
            //埋点
            this.updateMenuChain(this.CONSTVAL.MENU_CHAIN.FUNCTION_ENTRY, 2);
            this.updateMenuChain(this.CONSTVAL.MENU_CHAIN.SCAN_CODE, 3);
            menuLogRecord({})
            // 菜单截屏权限控制
            checkScreenShot(this.CONSTVAL.MENU_CHAIN.SCAN_CODE);
            let param = {
                'area': 'work',
                'privId': 'z-0',
                'stationId': this.uinfo.stationId,
                unLoadFlg: true //屏蔽加载圈
            }
            this.$http.post('/xsb/api-user/commonFunc/h5commCollect', param)
            if(!this.uinfo.crmId){
                this.$toast('当前岗位CRM工号为空，请联系地市管理员')
                return
            }
            this.showOcr = !this.showOcr
        },
        closeLuyin () {
            document.getElementsByClassName("loading-chat")[0].style.display = 'none';
            document.getElementsByClassName("hide-box")[0].style.display = 'none';
        },
        getRecordResultCbFn(obj){
            console.info('getRecordResultCbFn',obj)
            // 长按松开时候关闭实例
            if (obj.code != undefined) {
                this.rec && this.rec.recorder.close();
                this.rec = null
                document.getElementsByClassName("loading-chat")[0].style.display = 'none';
                document.getElementsByClassName("hide-box")[0].style.display = 'none';
            }
            if (obj.code == '-1') {
                this.$toast(obj.voiceTxt)
                return
            }

            if (obj.voiceTxt) {
                this.queryKey =  obj.voiceTxt.replace(/[\.。,，;]/g, '')
                this.debounce(this.qryAct(),1500)
            }
        },
        debounce(fn, wait) {
            let timeout = null;
            return function() {
                let context = this;
                let args = arguments;
                if (timeout) clearTimeout(timeout);
                let callNow = !timeout;
                timeout = setTimeout(() => {
                    timeout = null;
                }, wait);
                if (callNow) fn.apply(context, args);
            };
        },
        handlerTouchstart(e){
            this.luyinFlag = true
            document.getElementById("inputBox1").focus();
            document.getElementsByClassName("loading-chat")[0].style.display = 'block';
            document.getElementsByClassName("hide-box")[0].style.display = 'block';
            e.preventDefault();
            this.rec.startRecorder();
        },
        handlerTouchend(){
            this.luyinFlag = false
            document.getElementById("inputBox1").blur();
            this.rec.endRecorder()
        },
        foucusInput(){
            if (!this.rec) {
                if (/android|harmony/gi.test(navigator.userAgent)) {
                    if (sessionStorage.getItem('hasSetAudio')) {
                        this.rec = new RecorderManger({}, '', this.getRecordResultCbFn);
                    } else {
                        ClientJs.getAudioPermiss('getAudioPermissfun');
                    }
                } else {
                    this.rec = new RecorderManger({}, '', this.getRecordResultCbFn);
                }
            }
            setTimeout(() => {
                this.foucusFlag = true
            },1000)
        },
        blueInput(){
            if (this.luyinFlag) {
                document.getElementById("inputBox1").focus();
                this.foucusFlag = true
            } else {
                this.foucusFlag = false
            }
        },
        scrollWorkStageNew(){
            if(this.weekPlanXinFlag){
                // let dom =  e.target;
                let totalHeight = this.$refs.workStageDiv.scrollHeight;
                let ulHeight = this.$refs.workStageDiv.clientHeight;
                let scrollTop = this.$refs.workStageDiv.scrollTop;
                // console.log(totalHeight);
                // console.log(ulHeight);
                // console.log(scrollTop);
                if(ulHeight + scrollTop+1 >= totalHeight){
                    // this.$toast("滚动到底部了");
                    this.weekMoreShowFlag =false;
                } else{
                    this.weekMoreShowFlag =true;
                }
            }
        },

        //打开数据详情
        openShuJuTip(item){
            this.showShuJuTipInfo=item;
            this.isShowShuJuTip=true;
        },
        convertToPercentage(num) {
            if(!num)  return '--'
            return (num * 100).toFixed(2) + '%';
        },
        //关闭数据详情
        closeShuJuTip(){
            this.showShuJuTipInfo={};
            this.isShowShuJuTip=false;
        },
        //跳转我的学习
        toMyStudy() {
            this.updateMenuChain(this.CONSTVAL.MENU_CHAIN.AI_SCHOOL, 2);
            this.updateMenuChain(this.CONSTVAL.MENU_CHAIN.LEARNING_DURATION, 3);
            menuLogRecord({})
            // 菜单截屏权限控制
            checkScreenShot(this.CONSTVAL.MENU_CHAIN.LEARNING_DURATION);

          this.$router.push({
                path: '/studyHome',
                query: {
                    activeTab: 1,
                    srcFrom: 'workStage'
                }
            })
        },
        //前往任务
        openTask(item){
            this.typeFilter = {}
            if (item.worktypecode) {
                this.typeFilter = item;
                this.typeFilter.id = item.worktypecode;
            } else {
                this.typeFilter.id = "9999"
                this.typeFilter.label = "全部"
            }
            this.isShowDetail = true;
        },
        //获取任务
        workStrogeQueryCount(countLis){
            if(countLis && countLis.length>0){
                if(countLis.length==1 || countLis.length==3){
                    let daiBanNum=0;
                    let passBanNum=0;
                    for(let i =0;i < countLis.length;i++){
                        daiBanNum = daiBanNum+ parseInt(countLis[i].num);
                        passBanNum = passBanNum +parseInt(countLis[i].passNum);
                    }
                    let passBanNumPer =Math.round((passBanNum/(daiBanNum+passBanNum))* 100)+'%';
                    countLis[countLis.length] = {
                        "label": "全部任务",
                        "num": daiBanNum,
                        "passNum": passBanNum,
                        "passPer": passBanNumPer,
                    };
                    this.workStrogeCountList = countLis;
                }else{
                    this.workStrogeCountList = countLis.slice(0,4);
                }
            }
            setTimeout(()=>{
                this.scrollWorkStageNew();
            },1000);
        },
        //获取任务图片
        getTaskPicture(val){
            if (val == 0) {
                return 'static/img/serviceBar/new-ka.png';
            } else if (val == 1) {
                return 'static/img/serviceBar/contract.png';
            } else if (val == 2) {
                return 'static/img/serviceBar/phone.png';
            } else if (val == 3) {
                return 'static/img/serviceBar/all.png';
            } else {
                return '';
            }
        },
        //比较是否达标
        checkInList(item){
            let indiFinish=0;
            if(item.indiFinish && item.indiFinish!="null"){
                indiFinish= parseFloat(item.indiFinish);
            }
            let indiTarget=0;
            if(item.indiTarget && item.indiTarget!="null"){
                indiTarget= parseFloat(item.indiTarget);
            }
            if(indiFinish < indiTarget){
                return 1;
            }else{
                return -1;
            }

        },
        //学习信息
        getStudyMes() {
            let showRole = "0";
            if(this.uinfo.dwUser) {
                showRole = "1";
            }
            let url = "/xsb/personBusiness/studyCenter/h5QryOperStudyInfo";
            let params = {
                gridNum: this.uinfo.orgId,
                indicatorStr: "",
                startDate: "",
                showRole: showRole,
                unLoadFlg: true
            }
            this.$http.post(url, params).then(res => {
                let {retCode,retMsg,data} = res.data;
                if (retCode == '0') {//成功的情况
                    this.studyMsg = data;
                } else {
                    this.$toast(retMsg || '请求失败');
                }
            }).catch(err => {
                this.$toast(err)
            })
        },
        //指标数据查询(日期是否写死)
        IndexDataTime(indexFlag){
            if(indexFlag) {
                this.indexFlag = indexFlag;
            }
            if (this.indexFlag === "2") {
                this.getTaskCodeDetail();
                //查询预警产能信息
                this.qryWarnJobProdInfo();
            }
            if (indexFlag && indexFlag === '2') {
                this.isShowProvincial = false;
                if (this.zhiBiaoList && this.zhiBiaoList.length > 0) {
                    return;
                }
            }
            this.qryIndexDataInfo();
            /*let url='/xsb/personBusiness/daiWeiSynergy/h5getDictInfo?dictId=2020';
            this.$http.get(url,{unLoadFlg:true}).then(res => {
              let statDateTmp="";
              let checkWang="";
              if(res.data.retCode == '0'){
                let data = res.data.data;
                if(data && data.length>0){
                  statDateTmp=data[0].valueName;
                  checkWang=data[0].label;
                }
              }
              //this.statDate = statDateTmp;
              //地市指标数据（小时）查询服务
              this.qryIndexDataInfo(statDateTmp,checkWang);
            }).catch(() => {
              this.qryIndexDataInfo("","");
            }).finally(()=>{
              this.scrollWorkStageNew();
            });*/
        },

        getTaskCodeDetail() {
            let param = {
                'levelType': "1",
                'qryTypeCode': ""
            }
            this.level2List = []

            this.$http.post('/xsb/personBusiness/myTask/h5QryTaskCodeDetail', param).then((res) => {
                if (res.data.retCode === '0') {
                    this.levelListAll = res.data.data;
                    for (let item of this.levelListAll) {
                        //this.$set(item, 'taskCount', "--");
                        if (item.id == "1") {
                            Object.assign(this.wcTaskItem, item);
                            if (!this.wcTaskItem.taskCount) {
                                this.$set(this.wcTaskItem, 'taskCount', "--");
                            }
                        }
                        if (item.id == "2") {
                            Object.assign(this.yjTaskItem, item);
                            if (!this.yjTaskItem.taskCount) {
                                this.$set(this.yjTaskItem, 'taskCount', "--");
                            }
                        }
                        if (item.id == "4") {
                            Object.assign(this.rwgjTaskItem, item);
                            if (!this.rwgjTaskItem.taskCount) {
                                this.$set(this.rwgjTaskItem, 'taskCount', "--");
                            }
                        }
                        if (item.id == "6") {
                            Object.assign(this.tzTaskItem, item);
                            if (!this.tzTaskItem.taskCount) {
                                this.$set(this.tzTaskItem, 'taskCount', "--");
                            }
                        }
                        if(item.id == "0"){
                            Object.assign(this.sjyjTaskItem, item);
                            if (!this.sjyjTaskItem.taskCount) {
                                this.$set(this.sjyjTaskItem, 'taskCount', "0");
                            }
                        }
                    }
                    this.getTaskCount()
                }

                this.getListMy();
            })
        },

        getTaskCount() {
            let url = `/xsb/personBusiness/myTask/h5qryTaskByState`;
            let param = {
                queryType: "1",
                page: 1,
                size: 15
            }
            this.$http.post(url, param).then((res) => {
                let { retCode, data } = res.data;
                if (retCode === '0') {
                    if (data && data.length > 0) {
                        for (let countItem of data) {
                            if (countItem.id == this.wcTaskItem.id) {
                                this.$set(this.wcTaskItem, 'taskCount', countItem.taskCount ? countItem.taskCount : "--");
                            }
                            if (countItem.id == this.yjTaskItem.id) {
                                this.$set(this.yjTaskItem, 'taskCount', countItem.taskCount ? countItem.taskCount : "--");
                            }
                            if (countItem.id == this.rwgjTaskItem.id) {
                                this.$set(this.rwgjTaskItem, 'taskCount', countItem.taskCount ? countItem.taskCount : "--");
                            }
                            if (countItem.id == this.tzTaskItem.id) {
                                this.$set(this.tzTaskItem, 'taskCount', countItem.taskCount ? countItem.taskCount : "--");
                            }
                            if(countItem.id == this.sjyjTaskItem.id){
                                this.$set(this.sjyjTaskItem, 'taskCount', countItem.taskCount ? countItem.taskCount : "0");
                            }
                        }
                        let taskInfoObj = {};
                        taskInfoObj.wcTaskItem = this.wcTaskItem;
                        taskInfoObj.yjTaskItem = this.yjTaskItem;
                        taskInfoObj.rwgjTaskItem = this.rwgjTaskItem;
                        taskInfoObj.tzTaskItem = this.tzTaskItem;
                        taskInfoObj.sjyjTaskItem = this.sjyjTaskItem;
                        this.homePageInfoObj.taskInfo = taskInfoObj;
                        Storage.session.set('homePageInfoObj', this.homePageInfoObj);
                    }
                }
            })
        },
        goShengJiOrder(tab){
            //埋点
            this.updateMenuChain(this.CONSTVAL.MENU_CHAIN.BIG_BUSINESS_OPPORTUNITY, 2);
            this.updateMenuChain(this.CONSTVAL.MENU_CHAIN.WARNING, 3);
            menuLogRecord({})
            // 菜单截屏权限控制
            checkScreenShot(this.CONSTVAL.MENU_CHAIN.WARNING);


          this.$router.push({
                path: '/warnOrderList',
                query:{
                    srcFrom:'workStage',
                    queryType:tab
                }
            })
        },
        goBusiOppo(){
            //埋点
            this.updateMenuChain(this.CONSTVAL.MENU_CHAIN.BIG_BUSINESS_OPPORTUNITY, 2);
            this.updateMenuChain(this.CONSTVAL.MENU_CHAIN.BUSINESS_OPPORTUNITY, 3);
            menuLogRecord({})

            this.$router.push({
                path: '/PersonalBusinessOpportunity',
                query:{
                    srcFrom:'workStage'
                }
            })
        },

        getListMy() {
            let self = this;
            let url = '/xsb/gridCenter/familyDiagnosis/h5MyBusiQuery'
            let params = {
                'crmId': Storage.session.get('userInfo').crmId,
                'region': Storage.session.get('userInfo').region,
                'operateTel': Storage.session.get('userInfo').servNumber, //当前操作人号码
                'operateName': Storage.session.get('userInfo').operatorName,  //当前操作人
                'busiSource':'',
                'busiType':'1',
                'busiTel': '',  //商机号码
                'buopStatus': '',//商机状态
                'createStartTime': '',//创建开始时间
                'createEndTime': '',//创建结束时间
                'size':10,
                'page':1
            }
            self.$http.post(url, params).then((res) => {
                //商机数量赋值
                if( res.data.retCode == '0'){
                    self.busiOppoTaskCount = res.data.data.total ? res.data.data.total : 0;
                    Storage.session.set('busiOppoTaskCount', self.busiOppoTaskCount);
                }
            })
        },

        goTask(type, item){
            //埋点
            this.updateMenuChain(this.CONSTVAL.MENU_CHAIN.BIG_BUSINESS_OPPORTUNITY, 2);
            this.updateMenuChain(this.CONSTVAL.MENU_CHAIN.MORE, 3);
            menuLogRecord({})
            // 菜单截屏权限控制
            checkScreenShot(this.CONSTVAL.MENU_CHAIN.MORE);

            let workTypeCode = []
            if (type === "1") {
                let itemChild = item.children;
                if (itemChild && itemChild.length > 0) {
                    for (let secondItem of itemChild) {
                        if (secondItem.children && secondItem.children.length > 0) {
                            for (let thirdItem of secondItem.children) {
                                workTypeCode.push(thirdItem.id)
                            }
                            //break;
                        } else {
                            workTypeCode.push(secondItem.id)
                        }
                    }
                }
            }
            this.$router.push({
                name: "MyTaskList",
                query: {
                    state: '1',
                    srcFrom: "workStage",
                    workTypeCode: workTypeCode.join(',')
                }
            });
        },
        weekSheQuStationCheck(flag){

            if(flag ==1){
                if(this.uinfo.stationId=='9988021754066058' || this.uinfo.stationId=='10009101'){
                    this.weekPlanType='2'; //直销
                    this.qrySheQuBanLi();
                }else if(this.uinfo.stationId=='10000601' || this.uinfo.stationId=='10009102'){
                    this.weekPlanType='3';//随销
                    this.qrySheQuBanLi();
                }
            }else{
                console.log(11111111111111111111)
                this.weekPlanType='1';
                //地市指标数据（小时）查询服务
                let homePageInfoObj = Storage.session.get('homePageInfoObj');
                //获取缓存数据，先进行预加载，后续进行数据覆盖
                if (homePageInfoObj && typeof homePageInfoObj === 'object') {
                    this.homePageInfoObj = homePageInfoObj
                    //this.weekPlanXinFlag = this.homePageInfoObj.weekPlanXinFlag;  //是否展示新首页
                    this.weekPlanType = this.homePageInfoObj.weekPlanType;        //新首页标识 1.默认本地新首页 ,2.直销首页 3.随销首页
                    //角色信息 判断是否切换岗位，若未切换岗位角色信息取缓存数据
                    if (this.homePageInfoObj.stationId && this.homePageInfoObj.stationId === this.uinfo.stationId) {
                        this.userIdentity = this.homePageInfoObj.userIdentity;
                        this.roleId = this.homePageInfoObj.roleId;
                        this.zhiBiaoRoleType = this.homePageInfoObj.zhiBiaoRoleType;
                        this.jobId = this.homePageInfoObj.jobId;
                        this.chanList = this.homePageInfoObj.chanList;
                        this.isSecond = this.homePageInfoObj.isSecond;
                        //指标数据
                        if (this.homePageInfoObj.zhiBiaoList && this.homePageInfoObj.zhiBiaoList.length > 0) {
                            this.zhiBiaoList = this.homePageInfoObj.zhiBiaoList;
                        }
                    }
                    //任务专区
                    if (this.homePageInfoObj.taskInfo) {
                        let taskInfo = this.homePageInfoObj.taskInfo;
                        this.wcTaskItem = taskInfo.wcTaskItem;
                        this.yjTaskItem = taskInfo.yjTaskItem;
                        this.rwgjTaskItem = taskInfo.rwgjTaskItem;
                        this.tzTaskItem = taskInfo.tzTaskItem;
                        this.sjyjTaskItem = taskInfo.sjyjTaskItem;
                        if(Storage.session.get('busiOppoTaskCount')){
                            this.busiOppoTaskCount = Storage.session.get('busiOppoTaskCount')
                        }

                        //预警产能
                        if (this.homePageInfoObj.warnProdValue) {
                            this.warnProdValue = this.homePageInfoObj.warnProdValue;
                        }
                    }
                    //this.weekPlanXinFlag = true;

                    this.weekPlanType='1';
                    //地市指标数据（小时）查询服务
                    this.IndexDataTime();

                    //this.homePageInfoObj.weekPlanXinFlag = this.weekPlanXinFlag;
                    this.homePageInfoObj.weekPlanType = this.weekPlanType;
                    Storage.session.set('homePageInfoObj', this.homePageInfoObj);
                }else{
                    this.IndexDataTime();
                }
            }
            if (flag == 4) {
                this.tabhead = ''
                this.weekPlanType = 4
            }
            if (flag == 5) {
                this.tabhead = ''
                this.weekPlanType = 5
            }
        },

        //指标数据查询
        IndexDataPermission(){
            let homePageInfoObj = Storage.session.get('homePageInfoObj');
            //获取缓存数据，先进行预加载，后续进行数据覆盖
            if (homePageInfoObj && typeof homePageInfoObj === 'object') {
                this.homePageInfoObj = homePageInfoObj
                //this.weekPlanXinFlag = this.homePageInfoObj.weekPlanXinFlag;  //是否展示新首页
                this.weekPlanType = this.homePageInfoObj.weekPlanType;        //新首页标识 1.默认本地新首页 ,2.直销首页 3.随销首页
                //角色信息 判断是否切换岗位，若未切换岗位角色信息取缓存数据
                if (this.homePageInfoObj.stationId && this.homePageInfoObj.stationId === this.uinfo.stationId) {
                    this.userIdentity = this.homePageInfoObj.userIdentity;
                    this.roleId = this.homePageInfoObj.roleId;
                    this.zhiBiaoRoleType = this.homePageInfoObj.zhiBiaoRoleType;
                    this.jobId = this.homePageInfoObj.jobId;
                    this.chanList = this.homePageInfoObj.chanList;
                    this.isSecond = this.homePageInfoObj.isSecond;
                    //指标数据
                    if (this.homePageInfoObj.zhiBiaoList && this.homePageInfoObj.zhiBiaoList.length > 0) {
                        this.zhiBiaoList = this.homePageInfoObj.zhiBiaoList;
                    }
                }
                //任务专区
                if (this.homePageInfoObj.taskInfo) {
                    let taskInfo = this.homePageInfoObj.taskInfo;
                    this.wcTaskItem = taskInfo.wcTaskItem;
                    this.yjTaskItem = taskInfo.yjTaskItem;
                    this.rwgjTaskItem = taskInfo.rwgjTaskItem;
                    this.tzTaskItem = taskInfo.tzTaskItem;
                    this.sjyjTaskItem = taskInfo.sjyjTaskItem;
                    if(Storage.session.get('busiOppoTaskCount')){
                        this.busiOppoTaskCount = Storage.session.get('busiOppoTaskCount')
                    }
                    //预警产能
                    if (this.homePageInfoObj.warnProdValue) {
                        this.warnProdValue = this.homePageInfoObj.warnProdValue;
                    }
                }
            }
            let queryBusiType ='workStage_zhiBiao';
            //判断是否社区店版
            if(this.weekSheQuStation.includes(this.uinfo.stationId)){
                this.weekSheQuStationFlag=true;
                queryBusiType ='workStage_sheQu';
                //地市指标数据（小时）查询服务
                //this.weekPlanXinFlag = true;
                Storage.session.set('workStageZhiBiao', this.uinfo.stationId);

                let date = new Date();
                let month = date.getMonth() + 1;
                this.dangYueTip =month +"月";

                if(this.uinfo.stationId=='9988021754066058' || this.uinfo.stationId=='10009101'){
                    this.weekPlanType='2'; //直销
                    this.qrySheQuBanLi();
                }else if(this.uinfo.stationId=='10000601' || this.uinfo.stationId=='10009102'){
                    this.weekPlanType='3';//随销
                    this.qrySheQuBanLi();
                    this.xieTongFnList=[{"opId":"fsop","type":"1","funcId":"100415","funcName":"双向协同","picPath":"20201","goPath":null,"opParentid":"fsop","hasCrmid":"-1","hasPwd":"0","isNewFeature":0,"featureType":"","authenType":-1},{"opId":"fsop","type":"1","funcId":"100360","funcName":"激励明细 ","picPath":"100360","goPath":null,"opParentid":"fsop","hasCrmid":"-1","hasPwd":"0","isNewFeature":0,"featureType":"","authenType":-1},{"opId":"fsop","type":"1","funcId":"100346","funcName":"激励账户配置 ","picPath":"100346","goPath":null,"opParentid":"fsop","hasCrmid":"-1","hasPwd":"0","isNewFeature":0,"featureType":"","authenType":-1}];
                }

                this.getTaskCodeDetail();
                //查询预警产能信息
                this.qryWarnJobProdInfo();

                //this.homePageInfoObj.weekPlanXinFlag = this.weekPlanXinFlag;
                this.homePageInfoObj.weekPlanType = this.weekPlanType;
                Storage.session.set('homePageInfoObj', this.homePageInfoObj);
            }else{
                this.weekPlanType='1';
                //地市指标数据（小时）查询服务
                this.IndexDataTime();
                this.homePageInfoObj.weekPlanType = this.weekPlanType;
                Storage.session.set('homePageInfoObj', this.homePageInfoObj);
                /*let url = `/xsb/ability/businessLimit/h5QryBusiPermission`;
              let param = {
                busiType: queryBusiType,
                unLoadFlg: true
              }
              this.$http.post(url, param).then(res => {
                if(res.data.retCode == '0'){
                  this.weekPlanXinFlag = true;

                  this.weekPlanType='1';
                  //地市指标数据（小时）查询服务
                  this.IndexDataTime();

                  this.homePageInfoObj.weekPlanXinFlag = this.weekPlanXinFlag;
                  this.homePageInfoObj.weekPlanType = this.weekPlanType;
                  Storage.session.set('homePageInfoObj', this.homePageInfoObj);
                } else {
                  this.weekPlanXinFlag = false;
                }
              }).catch(() => {
                this.weekPlanXinFlag = false;
              })*/
            }
            this.getSjInfo()

        },
        //查询直销业务办理量
        qrySheQuBanLi(){
            if(Storage.session.get(this.uinfo.stationId+"sheQuList")){
                this.sheQuList= Storage.session.get(this.uinfo.stationId+"sheQuList");
                //this.scrollWorkStageNew();
            }else{
                let url = "/xsb/gridCenter/directSales/h5QrySheQuBanLi";
                if(this.weekPlanType =='3'){
                    url = "/xsb/gridCenter/directSales/h5QrySheQuBanLiSx";
                }
                let params = {
                    unLoadFlg:true,
                    localTime: dateFormat(new Date(), "yyyyMM"),
                }
                this.$http.post(url, params).then(res => {
                    if(res.data.retCode == "0"){
                        this.sheQuList=res.data.data;
                        Storage.session.set(this.uinfo.stationId+"sheQuList", this.sheQuList);
                    }else {
                        this.$toast('业务办理量失败');
                    }
                }).catch((response) => {
                    this.$toast('业务办理量失败'+response);
                }).finally(()=>{
                    this.scrollWorkStageNew();
                });
            }
        },
        //前往报表
        goWorkReportDetail(){
            let orgaNameBiao="本店";
            if(this.zhiBiaoRoleType!='6'&& this.zhiBiaoRoleType!='7'&& this.zhiBiaoRoleType!='8'){
                orgaNameBiao=this.zhiBiaoList[0].orgNameChild;
            }
            this.$router.push({
                path: '/workReportDetail',
                query: {
                    srcFrom: "/workStage",//来源
                    orgaName:orgaNameBiao,
                    reportFormTitleList: JSON.stringify(this.reportFormTitleList),
                    reportFormDataList: JSON.stringify(this.reportFormDataList),
                    resultType: this.resultType,
                    statDate: this.statDate,
                    roleId: this.roleId,
                    zhiBiaoRoleType: this.zhiBiaoRoleType,
                    selectOrgTree: JSON.stringify(this.selectOrgTree),
                    //selectOrgInfo: JSON.stringify(this.selectOrgInfo),
                }
            })
        },

        getEndOrgName(val) {
            if (val === 0) {
                return "请选择地市";
            } else if (val === 1) {
                return "请选择区/县"
            } else if (val === 2) {
                return "请选择网格";
            } else if (val === 3) {
                return "请选择网格经理";
            } else if (val === 4) {
                return "请选择渠道";
            }
        },

        backProvincial() {
            this.indexFlag = "1";
            if (!this.provincialIndexList || !this.provincialIndexList.length > 0) {
                this.qryIndexDataInfo();
            } else {
                this.isShowProvincial = true;
                this.$refs.provinceCountPage.initProvincialData();
            }
        },

        queryThisLevel() {
            let competeData = Storage.session.get('competeData');
            if (competeData && competeData[0].orgId) {
              this.competeData = competeData;
              this.showCompete = true;
              return;
            }
            let url = `/xsb/paperless/competecontrast/h5queryThisLevel`;
            if (this.isNewCompete) {
                url = `/xsb/paperless/competecontrast/h5queryThisLevelNew`;
            }
            this.$http.post(url).then(res => {
              let { retCode, data } = res.data;
              if (retCode === '0') {
                if (data) {
                  this.showCompete = true;
                  this.competeData.push(data);
                  Storage.session.set('competeData', this.competeData);
                  document.getElementById('competeClick').click();
                } else {
                  this.showCompete = false;
                }
              } else {
                this.showCompete = false;
              }
            }).catch(() => {
                this.showCompete = false;
            })
        },

        showYWBL(){
            if(this.weekSheQuStation.includes(this.uinfo.stationId)) {
                return true
            } else {
                return false
            }
        },
        showXTZQ(){
            if(this.uinfo.stationId=='10000601' || this.uinfo.stationId=='10009102'){
                return true
            }
            return false
        },
        changeCompete(val) {

            //埋点
            this.updateMenuChain(this.CONSTVAL.MENU_CHAIN.FUNCTION_ENTRY, 2);
            this.chooseCompete = val;
            if (val == true) {
                if(this.weekSheQuStation.includes(this.uinfo.stationId)) {
                    if(this.uinfo.stationId=='9988021754066058' || this.uinfo.stationId=='10009101'){
                        this.weekPlanType='2'; //直销
                    }else if(this.uinfo.stationId=='10000601' || this.uinfo.stationId=='10009102'){
                        this.weekPlanType='3';//随销
                    }
                } else {
                    this.weekPlanType = '1'
                }
                this.updateMenuChain(this.CONSTVAL.MENU_CHAIN.PK_CONTROL_VALUE, 3);
                // 菜单截屏权限控制
                checkScreenShot(this.CONSTVAL.MENU_CHAIN.PK_CONTROL_VALUE);
            } else {
                if (this.kanban.viewFlag == 1) {
                    this.tabhead = '3'
                } else {
                    this.weekPlanType = '3'

                }
                this.updateMenuChain(this.CONSTVAL.MENU_CHAIN.KPI_DASHBOARD, 3);
                // 菜单截屏权限控制
                checkScreenShot(this.CONSTVAL.MENU_CHAIN.KPI_DASHBOARD);
            }
            menuLogRecord({})

            this.tabhead = 1
            if (this.weekSheQuStationFlag && (!this.showCompete || !this.chooseCompete)) {
                console.log('进来了第一个给')

                if (this.kanban.viewFlag == 1) {
                    this.weekSheQuStationCheck(5)
                } else {
                    this.weekSheQuStationCheck(1)
                }
            }else if (!(this.weekSheQuStationFlag && (!this.showCompete || !this.chooseCompete))  && !this.chooseCompete) {
                console.log('进来了第2个给')

                if (this.kanban.viewFlag == 1) {
                    this.tabhead = '3'
                } else {
                    this.tabhead = '1'
                    this.weekSheQuStationCheck(2)
                }

            } else {

            }

        },

        viewStatement() {
            //埋点
            this.updateMenuChain(this.CONSTVAL.MENU_CHAIN.FUNCTION_ENTRY, 2);
            this.updateMenuChain(this.CONSTVAL.MENU_CHAIN.VIEW_REPORT, 3);
            menuLogRecord({})
            // 菜单截屏权限控制
            checkScreenShot(this.CONSTVAL.MENU_CHAIN.VIEW_REPORT);


          if (this.competeData && this.competeData.length > 0) {
                this.$router.push({
                    path: '/competeReportDetail',
                    query: {
                        srcFrom: "/workStage",//来源
                        competeInfo: this.competeData[0],
                        selectOrgTree: this.competeData,
                    }
                })
            }
        },

        //点击指标数据左上角打开组装机构树
        openSelectTree (item) {
            //初始化组织机构树，下钻时数据为空，返回初始化数据
            this.selectOrgTreeInit = this.selectOrgTree;
            //判断是否是当前层级 seLevel为“0”标识当前层级，为“1”时表示下一层级
            if (item.seLevel !== "0") {
                //若为下一层级，把所选的指标对象的组织机构信息接到当前组织机构树后面
                let itemOrgInfo = {};
                itemOrgInfo.orgIdChild = item.orgIdChild;
                itemOrgInfo.orgNameChild = item.orgNameChild;
                itemOrgInfo.parentOrgId = this.selectOrgTree[this.selectOrgTree.length - 1].orgIdChild;
                itemOrgInfo.treeLevel = this.selectOrgTree[this.selectOrgTree.length - 1].treeLevel + 1;
                this.selectOrgTree.push(itemOrgInfo);
            }
            //判断当前是否已经下钻到渠道
            if ((this.selectOrgTree[this.selectOrgTree.length - 1].treeLevel < 4) && (this.selectOrgTree[this.selectOrgTree.length - 1].orgIdChild !== "0")) {
                //若未下钻到渠道，在组机构树后面添加默认值
                let endOrgInfo = {};
                endOrgInfo.orgIdChild = "0";
                endOrgInfo.orgNameChild = this.getEndOrgName(this.selectOrgTree[this.selectOrgTree.length - 1].treeLevel + 1);
                endOrgInfo.parentOrgId = this.selectOrgTree[this.selectOrgTree.length - 1].orgIdChild;
                endOrgInfo.treeLevel = this.selectOrgTree[this.selectOrgTree.length - 1].treeLevel + 1;
                this.selectOrgTree.push(endOrgInfo);
            }
            this.selectOrgInfo = this.selectOrgTree[this.selectOrgTree.length - 1];
            this.resultType = this.selectOrgTree[this.selectOrgTree.length - 1].treeLevel;
            this.isShowTreeBox = true;

            if (this.selectOrgTree[this.selectOrgTree.length - 1].orgIdChild === "0") {
                this.qryOrgAndChild(this.selectOrgTree[this.selectOrgTree.length - 2].orgIdChild, this.resultType);
            } else {
                this.qryOrgAndChild(this.selectOrgTree[this.selectOrgTree.length - 1].orgIdChild, this.resultType);
            }
        },

        clickLevel(item) {
            this.selectOrgInfo = item;
            this.resultType = item.treeLevel;
            if (this.selectOrgTree[0].orgIdChild === item.orgIdChild){
                this.selectOrgTree = this.selectOrgTree.slice(0, 1);
                if ((this.selectOrgTree[this.selectOrgTree.length - 1].treeLevel < 4) && (this.selectOrgTree[this.selectOrgTree.length - 1].orgIdChild !== "0")) {
                    let endOrgInfo = {};
                    endOrgInfo.orgIdChild = "0";
                    endOrgInfo.orgNameChild = this.getEndOrgName(this.selectOrgTree[this.selectOrgTree.length - 1].treeLevel + 1);
                    endOrgInfo.parentOrgId = this.selectOrgTree[this.selectOrgTree.length - 1].orgIdChild;
                    endOrgInfo.treeLevel = this.selectOrgTree[this.selectOrgTree.length - 1].treeLevel + 1;
                    this.selectOrgTree.push(endOrgInfo);
                }
                this.orgInfoList = []
            } else {
                let orgId = "";
                for (let i = 1; i < this.selectOrgTree.length; i++) {
                    if (this.selectOrgTree[i].orgIdChild === item.orgIdChild) {
                        orgId = this.selectOrgTree[i - 1].orgIdChild;
                        break;
                    }
                }
                this.qryOrgAndChild(orgId, item.treeLevel.toString())
            }
        },

        selectOption(item) {
            this.selectOrgInfo = item;
            this.selectOrgInfo.treeLevel = this.resultType;
            let index = -1;
            for (let i = 0 ; i < this.selectOrgTree.length; i++) {
                if (this.selectOrgTree[i].treeLevel === this.resultType) {
                    this.selectOrgInfo.parentOrgId = this.selectOrgTree[i].parentOrgId;
                    index = i;
                    break;
                }
            }
            if (index > -1) {
                this.selectOrgTree = this.selectOrgTree.slice(0, index);
                this.selectOrgTree.push(this.selectOrgInfo);

                if ((this.selectOrgTree[this.selectOrgTree.length - 1].treeLevel < 4) && (this.selectOrgTree[this.selectOrgTree.length - 1].orgIdChild !== "0")) {
                    let endOrgInfo = {};
                    endOrgInfo.orgIdChild = "0";
                    endOrgInfo.orgNameChild = this.getEndOrgName(this.selectOrgTree[this.selectOrgTree.length - 1].treeLevel + 1);
                    endOrgInfo.parentOrgId = this.selectOrgTree[this.selectOrgTree.length - 1].orgIdChild;
                    endOrgInfo.treeLevel = this.selectOrgTree[this.selectOrgTree.length - 1].treeLevel + 1;
                    this.selectOrgTree.push(endOrgInfo);
                }
            }
            if (this.selectOrgTree[this.selectOrgTree.length - 1].orgIdChild !== "0") {
                this.closeSelectBox();
            }
        },

        cancelSelect() {
            this.isShowTreeBox = false;
            this.selectOrgTree = this.selectOrgTreeInit;
            this.selectOrgInfo = this.selectOrgTreeInit[this.selectOrgTreeInit.length - 1];
            this.resultType = this.selectOrgTreeInit[this.selectOrgTreeInit.length - 1].treeLevel;
        },

        closeSelectBox() {
            this.isShowTreeBox = false;
            if (this.selectOrgTree[this.selectOrgTree.length - 1].orgIdChild === "0") {
                this.selectOrgInfo = this.selectOrgTree[this.selectOrgTree.length - 2];
            } else {
                this.selectOrgInfo = this.selectOrgTree[this.selectOrgTree.length - 1];
            }
            this.qryIndexChildDataInfo();
        },

        /**
         * 组织机构层级查询
         * @param orgId 组织机构编码/渠道编码
         * @param resultType 返回子节点组织机构类型（默认是1）
         */
        qryOrgAndChild(orgId, resultType) {
            //this.orgInfoList = [];
            let url = `/xsb/api-user/homePageView/h5qryOrgAndChild`;
            let param = {
                statDate: this.statDate,
                msisdn: this.uinfo.servNumber,
                orgId: orgId,
                roleType: this.zhiBiaoRoleType,
                resultType: resultType.toString()
            };
            if(!this.statDate) {
                param.statDate = dateFormat(new Date(), "yyyyMMdd");
            }
            this.$http.post(url, param).then(res => {
                let { retCode, retMsg, data } = res.data;
                if (retCode === "0") {
                    if (data) {
                        this.orgInfoList = data;
                    } else {
                        this.orgInfoList = []
                    }
                } else {
                    this.$alert(retMsg || "组织机构层级查询失败");
                }
            }).catch(reason => {
                this.$alert("组织机构层级查询异常，error: " + reason);
            })
        },

        qryIndexChildDataInfo() {
            let url = `/xsb/api-user/homePageView/h5qryIndexChildDataInfo`;
            let param = {
                statDate: this.statDate,
                msisdn: this.uinfo.servNumber,
                objectId: this.selectOrgInfo.orgIdChild,
                roleType: this.zhiBiaoRoleType,
                roleId: this.roleId,
                indexFlag: "2",
                isQryChild: "1"
            }
            this.$http.post(url, param).then(res => {
                let { retCode, retMsg, data } = res.data;
                if (retCode === "0") {
                    if (data.indexDataList && data.indexDataList.length > 0) {
                        this.zhiBiaoList=data.indexDataList;
                        this.reportFormTitleList =data.reportFormTitleList;
                        this.reportFormDataList =data.reportFormDataList;
                    } else {
                        this.selectOrgInfo = this.selectOrgTreeInit[this.selectOrgTreeInit.length - 1];
                        this.resultType = this.selectOrgTreeInit[this.selectOrgTreeInit.length - 1].treeLevel;
                        this.$alert("当前指标数据为空");
                    }
                } else {
                    this.$alert(retMsg || "查询首页指标数据失败");
                }
            }).catch(reason => {
                this.$alert("查询首页指标数据异常，error: " + reason);
            })
        },

        qryIndexDataInfo(){
            let provincialRole = ["0", "1", "2"]
            if (!this.statDate) {
                this.statDate = dateFormat(new Date(), "yyyyMMdd");
            }
            //获取当前操作员所在位置
            let cityOrgInfo = LONG_CITY_LIST.find(item => Storage.get('location').includes(item.label));

            let url = "/xsb/api-user/homePageView/h5qryIndexDataInfo";
            let params = {
                unLoadFlg:true,
                roleId: this.uinfo.stationId,
                objectId:this.uinfo.crmId,
                msisdn:this.uinfo.servNumber,
                statDate: this.statDate,
                latitude:Storage.get('latitude'),
                longitude:Storage.get('longitude'),
                indexFlag: this.indexFlag
            }
            if (this.indexFlag === "1" && this.chanList && this.chanList.length > 0) {
                params.gridId = this.chanList[0].orgId;
            }
            if (cityOrgInfo) {
                params.cityOrgId = cityOrgInfo.id;
            }

            if (this.zhiBiaoRoleType) {
                params.roleType = this.zhiBiaoRoleType;
                params.jobId = this.jobId;
                params.chanList = this.chanList;
                params.roleName = this.userIdentity;
            }
            if (this.userInfoData.crmId) {
                //学习信息
                this.getStudyMes();
            }
            //防止重复调用
            if (this.isSecond !== "1" || this.zhiBiaoRoleType) {
                this.$http.post(url, params).then(res => {
                    let { retCode, retMsg, data } = res.data;
                    let indexSessionData = [];    //缓存数据， 存放指标第一条数据
                    this.isSecond = "1";
                    if(retCode === "0"){
                        this.userIdentity = data.roleName;
                        this.roleId=data.roleId;
                        this.zhiBiaoRoleType=data.roleType;
                        this.jobId=data.jobId;
                        if (provincialRole.includes(this.zhiBiaoRoleType)) {
                            this.isShowChangeBtu = true;
                        }
                        this.chanList = data.chanList;
                        this.resultType = parseInt(this.zhiBiaoRoleType) - 1;
                        let selectOrgTree = data.selectOrgTree;
                        if (this.indexFlag === "1") {
                            this.provincialIndexList = data.provincialIndexList;
                            if (this.provincialIndexList && this.provincialIndexList.length > 0) {
                                this.isShowProvincial = true;
                                this.provincialIndexJson = {};
                                for (let item of this.provincialIndexList) {
                                    if (item.indexNumber === "10001") {
                                        this.provincialIndexJson.chbnItem = item;
                                    }
                                    if (item.indexNumber === "10002") {
                                        this.provincialIndexJson.chnItem = item;
                                    }
                                    if (item.indexNumber === "10003") {
                                        this.provincialIndexJson.bItem = item;
                                    }
                                    if (item.indexNumber === "10004") {
                                        this.provincialIndexJson.txCountItem = item;
                                    }
                                    if (item.indexNumber === "10005") {
                                        this.provincialIndexJson.kdCountItem = item;
                                    }
                                    if (item.indexNumber === "10006") {
                                        this.provincialIndexJson.pzCountItem = item;
                                    }
                                    if (item.indexNumber === "10007") {
                                        this.provincialIndexJson.kdAddItem = item;
                                    }
                                    if (item.indexNumber === "10008") {
                                        this.provincialIndexJson.tvAddItem = item;
                                    }
                                    if (item.indexNumber === "10009") {
                                        this.provincialIndexJson.fttrAddItem = item;
                                    }
                                    if (item.indexNumber === "10010") {
                                        this.provincialIndexJson.channelItem = item;
                                    }
                                    if (item.indexNumber === "10011") {
                                        this.provincialIndexJson.effectChannelItem = item;
                                    }
                                }
                                this.$refs.provinceCountPage.initProvincialData();
                            } else {
                                this.isShowProvincial = false;
                                this.indexFlag = "2";
                                this.$toast("当前省统指标数据为空");
                            }
                            this.opNowPath = selectOrgTree;
                        } else {
                            this.selectOrgTree = selectOrgTree;
                            this.isShowProvincial = false;
                            this.zhiBiaoList = data.indexDataList;
                            if(this.zhiBiaoList && this.zhiBiaoList.length>0){
                                //this.weekPlanXinFlag = true;
                                Storage.session.set('workStageZhiBiao', this.uinfo.stationId);
                                if (!this.selectOrgTree) {
                                    this.selectOrgTree = []
                                    let firstOrgInfo = {}
                                    firstOrgInfo.orgIdChild = this.zhiBiaoList[0].orgIdChild;
                                    firstOrgInfo.orgNameChild = this.zhiBiaoList[0].orgNameChild;
                                    firstOrgInfo.parentOrgId = "-1";
                                    firstOrgInfo.treeLevel = this.resultType;
                                    this.selectOrgTree.push(firstOrgInfo);
                                }
                                indexSessionData.push(this.zhiBiaoList[0]);
                            }
                            this.homePageInfoObj.zhiBiaoList = indexSessionData;
                            this.reportFormTitleList =data.reportFormTitleList;
                            this.reportFormDataList =data.reportFormDataList;
                        }

                        this.homePageInfoObj.userIdentity = this.userIdentity;
                        this.homePageInfoObj.roleId = this.roleId;
                        this.homePageInfoObj.jobId = this.jobId;
                        this.homePageInfoObj.chanList = this.chanList;
                        this.homePageInfoObj.zhiBiaoRoleType = this.zhiBiaoRoleType;
                    } else {
                        //初始化缓存数据
                        this.homePageInfoObj.zhiBiaoList = [];
                        this.homePageInfoObj.userIdentity = "";
                        this.homePageInfoObj.roleId = "";
                        this.homePageInfoObj.jobId = "";
                        this.homePageInfoObj.chanList = [];
                        this.homePageInfoObj.zhiBiaoRoleType = "";
                    }
                    this.homePageInfoObj.stationId = this.uinfo.stationId;
                    this.homePageInfoObj.isSecond = this.isSecond;
                    Storage.session.set('homePageInfoObj', this.homePageInfoObj);
                }).finally(()=>{
                    this.scrollWorkStageNew();
                });
            }
        },

        //获取预警产能信息 cntBusImport
        qryWarnJobProdInfo() {
            let url = `/xsb/api-user/homePageView/h5qryWarnJobProdInfo?msisdn=${this.uinfo.servNumber}`;
            this.$http.get(url).then(res => {
                let { retCode, retMsg, data } = res.data;
                if (retCode === "0") {
                    this.warnProdValue = data.cntBusImport ? data.cntBusImport : "--";
                    this.homePageInfoObj.warnProdValue = this.warnProdValue;
                    Storage.session.set('homePageInfoObj', this.homePageInfoObj);
                } else {
                    this.$toast(retMsg || "获取预警产能信息失败");
                }
            });
        },
        //展示定位
        showDingWei(){
            this.showDingWeiFlag=!this.showDingWeiFlag;
            if(this.showDingWeiFlag){
                this.getClientTrueLocal();
            }
        },
        //关闭提示
        deleteTip() {
            this.isShowTip = false;
            Storage.session.set('noShowNotice', true)
        },

        // goAIchat(){
        //     let subWgtUrl = "/subWgt?path=aiChatPage&gobackFlag=ald"
        //     this.$router.push(subWgtUrl)
        // },
        // getAiRole() {
        //     let userInfo = Storage.session.get('userInfo');
        //     let url = `/xsb/api-user/menu/h5getMenuByLevel?stationId=${userInfo.stationId}&level=9:0`;
        //     this.$http.get(url).then(res => {
        //         let {retCode,data,retMsg} = res.data;
        //         if (retCode == '0') {
        //             if (data && data.length > 0) {
        //                 this.AiRole = true
        //             } else {
        //                 this.AiRole = false
        //             }
        //         } else {
        //             this.AiRole = false
        //         }
        //     }).catch((response) => {
        //         //this.$alert('甩单信息获取网络连接失败');
        //     });
        // },
        //泛渠道甩单提示
        orderTip() {
            let url = `/xsb/personBusiness/pancOrderedList/h5qryOrderPush?crmCode=${this.userInfoData.crmId}`;
            this.$http.get(url).then(res => {
                let {retCode,data,retMsg} = res.data;
                if (retCode == '0') {
                    let {retCode,data,retMsg} = res.data;
                    //this.$alert(data);
                    this.pushMsg = data;
                    this.tips = true;
                    this.timeDown();
                } else {
                    this.tips = false;
                }
            }).catch((response) => {
                //this.$alert('甩单信息获取网络连接失败');
            });
        },

        //从移动基站获取时间及位置
        qryLoginUserLocation(){
            //此接口需开启地市权限才会调用，否则不调用
            let param={
                busiType:'base_station_get_login_info',
            };
            let loginUnusual = Storage.session.get('loginUnusual');
            this.$http.post('/xsb/ability/businessLimit/h5QryBusiPermission',param).then((res)=>{
                if(res.data.retCode == '0' && !loginUnusual){
                    let url = `/xsb/personBusiness/myTask/h5qryLoginUserLocation`;
                    this.$http.get(url).then(res => {
                        Storage.session.set('loginUnusual', '1');
                        //这个接口只需要调用即可，不需要考虑返回
                    }).catch((response) => {
                    });
                }
            });
        },
        //倒计时
        timeDown() {
            let result = setInterval(() => {
                --this.time;
                if (this.time <= 0) {
                    clearInterval(result);
                    this.tips = false;
                }
            }, 1000);
        },
        //关闭弹框
        close() {
            this.tips = false;
            this.time = 0;
        },
        viewInfoLists() {
            let url = "/xsb/api-user/viewType/h5QryViewType";
            let params = {
                unLoadFlg:true,
                stationId: this.userInfoData.stationId
            }
            this.$http.post(url, params).then(res => {
                if(res.data.retCode == "0"){
                    if(res.data.data.key == '0') {
                        this.viewInfoList = this.userInfoData.viewInfoList;
                    }else if (res.data.data.key == '1') {
                        this.viewInfoList = res.data.data.viewTypeList;
                    }
                    if (this.viewInfoList && this.viewInfoList.length > 0) {
                        this.currentView = this.viewClassMap[this.viewInfoList[0]];
                    }
                }else {
                    // this.$alert(res.data.retMsg || "请求失败")
                }
            });
        },
        // add by qhuang 公告查询
        qryNotice() {
            let url = `/xsb/api-user/noticeInfo/h5getNoticeInfo?type=0&orgId=${this.uinfo.orgId}`;
            this.$http.get(url,{unLoadFlg:true}).then(res => {
                let {retCode, data} = res.data;
                if (retCode == '0') {
                    let len = data ? data.length : 0;
                    let showNotice = Storage.session.get('showNotice');
                    if (len > 0 && !showNotice) {
                        let {content, title} = data[0];
                        this.$alert(content, title);
                        Storage.session.set('showNotice', '1');
                    }
                }
            });
        },
        //待办列表上拉，将头部信息缩起来
        collapseHead() {
            this.isBigHead = false;
            let h = window.screen.availHeight || window.document.body.offsetHeight;
            this.todoHeight = h - this.$refs.smallHead.clientHeight - 60 - 80 - this.$refs.navDiv.clientHeight - 24;//头部的高度 底部的高度 日历的高度
            //console.info('this.$refs.topDiv.clientHeight==='+this.$refs.smallHead.clientHeight)
        },
        openHead() {
            this.isBigHead = true;
            let h = window.screen.availHeight || window.document.body.offsetHeight;

            this.todoHeight = h - this.$refs.topDiv.clientHeight - 60 - 80 - this.$refs.navDiv.clientHeight - 24 - midDomHeight;//头部的高度 底部的高度 日历的高度

        },
        //点击周日期后查询待办列表
        queryList(dayObj, isToday) {
            //console.info(dayObj,isToday);
            this.queryDay = dayObj.planDate;
            if (dayObj.type0Num == 0) {//没数据不展示紧急筛选
                this.showEmergent = false;
            } else {
                this.showEmergent = true;
            }
            this.emergentFlag = 1;//恢复全部 不筛选成紧急
        },
        taskFilter(d) {
            this.typeNum = d.id;
        },
        doSubmitWh(tel, item) {
            let self = this;
            if (!/^((1)+\d{10})$/.test(tel)) {
                this.$toast({position: 'bottom', message: '请输入正确的手机号码'});
                return;
            }
            let url = "/xsb/ability/ictoutcall/h5login";
            this.$http.post(url, { callerNumber: tel }).then(res => {
                let { retCode, retMsg, data } = res.data;
                if (retCode == "0") {
                    let whParams=JSON.parse(item.workUrl);
                    whParams.outcallSessionId=data;
                    whParams.callerNumber=tel;
                    console.info(whParams);
                    this.$router.push({
                        path: '/whUserList',
                        query: {
                            whParams: whParams,
                            nokeep:'1'
                        }
                    })
                } else {
                    this.$alert(retMsg || "外呼登陆失败，请稍后再试");
                }
            }).catch(response => {
                this.$alert("发送外呼登陆请求失败");
            });
        },
        //增加点击量
        addClickNum(item){
            let param = {
                workTypeCode:item.workTypeCode,
                workTypeName:item.content,
                workId:item.taskId,
                regionId:item.regionId,
                countyId:item.countyId,
                areaId:item.areaId,
                sendDate:item.sendDate
            }
            this.$http.post('/xsb/personBusiness/myTask/h5AddTaskClick', param).then((res) => {
                if (res.data.retCode == '0') {
                    console.info(res.data.retMsg)
                }
            })
        },
        selectItem(item) {

            //增加点击量
            this.addClickNum(item);

            let uinfo = this.userInfoData;
            let workTypeCode = item.workTypeCode;
            let self = this;
            if (workTypeCode === '1001') {//1001 集团拜访任务
                //增加地市开关
                let groupSwitchParam = {
                    switchType: 'archive_group_flag'
                }
                self.$http.post('/xsb/personBusiness/gridSand/h5GridMarketSwitch', groupSwitchParam).then((res) => {
                    if (res.data.retCode == 0) {
                        let timeInfo = item.taskTime + '-' + item.limitDate;
                        // let subWgtUrl = '/subWgt?path=archiveGroup&groupId='+item.groupId+'&workId='+item.taskId+'&gobackFlag=ald&workType=1&workTypeCode='+ workTypeCode+'&timeInfo='+timeInfo;
                        let subWgtUrl = '/subWgt?path=archiveGroup&groupId='+item.groupId+'&workId='+item.taskId+'&gobackFlag=ald&workType=1&workTypeCode='+ workTypeCode;
                        self.$router.push(subWgtUrl)
                    }else {
                        self.$router.push({
                            path: `/groupDetailNew`,
                            query: {
                                workIdFlag:true,
                                taskId: item.taskId,
                                flag: '1',
                                groupId:item.groupId
                            }
                        })
                    }
                }).catch(() => {
                    self.$router.push({
                        path: `/groupDetailNew`,
                        query: {
                            workIdFlag:true,
                            taskId: item.taskId,
                            flag: '1',
                            groupId:item.groupId
                        }
                    })
                })
                /* this.$router.push({
                         name:'PlanDetail',
                         query: {
                           groupId:item.groupId,
                           taskId:item.taskId
                         }
                     })*/
            } else if (workTypeCode === '1009') {//100 集团任务(个人)
                // this.$router.push({
                //     path: `/groupDetailNew`,
                //     query: {
                //         workIdFlag:true,
                //         taskId: item.taskId,
                //         flag: '2',
                //         groupId:item.groupId
                //     }
                // })
                this.$router.push({
                    path: `/groupDetailNew`,
                    query: {
                        workIdFlag: true,
                        taskId: item.taskId,
                        flag: '2',
                        groupId: item.groupId,
                        theTime: item.taskTime + '-' + item.limitDate,
                        channelId: '12'

                    }
                })

            } else if (workTypeCode === '1003') {//1003 外呼任务
                this.$messagebox.prompt('请输入手机号', '温馨提示').then(({value, action}) => {
                    self.doSubmitWh(value, item)
                }).catch(() => {
                });
            } else if (workTypeCode === '1012') {//1012 热点自定义场景
                let crmId = this.userInfoData.crmId;
                if (crmId && crmId != 'null' && crmId != 'undefined') {
                    this.$router.push({
                        path: `/customSceneDetail`,
                        query: {
                            workIdFlag:true,
                            workId: item.taskId,
                            objectId: item.groupId,
                            channelId: "53"
                        }
                    })
                }else {
                    this.$alert("当前岗位没有CRM工号，不能执行任务")
                }
            }else if(workTypeCode === '1059'){//1059沿街商铺任务
                this.$router.push({
                    path: `/shopsNew`,
                    query: {
                        workIdFlag:true,
                        workId: item.taskId,
                        objectId: item.groupId,
                        channelId:"95"
                    }
                })
            }else if(workTypeCode === '1060'){//1060泛住宿任务
                this.$router.push({
                    path: `/shopsViewDetail`,
                    query: {
                        workIdFlag:true,
                        workId: item.taskId,
                        objectId: item.groupId,
                        channelId:"96"
                    }
                })
            }else if(workTypeCode === '1063'){//1063乡村任务
                this.$router.push({
                    path: `/shopsNew`,
                    query: {
                        workIdFlag:true,
                        workId: item.taskId,
                        objectId: item.groupId,
                        channelId:"97"
                    }
                })
            }else if (workTypeCode === '1004') {//1004 小区放装任务
                let crmId = this.userInfoData.crmId;
                if (crmId && crmId != 'null' && crmId != 'undefined') {
                    //if ('' != Storage.session.get('userInfo').crmId && null != Storage.session.get('userInfo').crmId && 'undefind' != Storage.session.get('userInfo').crmId) {
                    Storage.session.set('villageWorkId'+item.taskId,item.taskTime + '-' + item.limitDate);
                    this.$router.push({
                        path: '/villageDetail',
                        query: {
                            workIdFlag:true,
                            workId: item.taskId,
                            villageId: item.groupId
                        }
                    })
                } else {
                    this.$alert("当前岗位没有CRM工号，不能执行小区任务")
                }
            }else if (workTypeCode === '1018') {//1004 小区放装任务
                let crmId = this.userInfoData.crmId;
                if (crmId && crmId != 'null' && crmId != 'undefined') {
                    //if ('' != Storage.session.get('userInfo').crmId && null != Storage.session.get('userInfo').crmId && 'undefind' != Storage.session.get('userInfo').crmId) {
                    this.$router.push({
                        path: '/gridWarn',
                        query: {
                            alarmId: item.groupId
                        }
                    })
                } else {
                    this.$alert("当前岗位没有CRM工号，不能执行当前任务")
                }
            } else if (workTypeCode === '1008') {//高校任务
                this.$router.push({
                    path: '/CollegeDetail',
                    query: {
                        workId: item.taskId,
                        collegeId: item.groupId
                    }
                })
            } else if (workTypeCode === '1006' || workTypeCode === '1007'|| workTypeCode === '1026'
                || workTypeCode === '1085' || workTypeCode === '1086') {//集团预警任务编码  1085	商机任务 1086	摸排任务) {//集团预警任务编码
                this.goToken('jtyjDb', uinfo, item.workUrl);
            } else if (workTypeCode === '1010') { // 考虑用户二次确认
                this.$router.push({
                    path: '/secondConfirm',
                    query: {
                        workId: item.taskId
                    }
                })
            } else if (workTypeCode === '1011') { // 携号转网上行下达
                this.$router.push({
                    path: '/portNumChangeNet',
                    query: {
                        workId: item.taskId
                    }
                });
            } else if (workTypeCode === '1013') { // 查询IMS固话批次开通状态
                this.$router.push('/grpIMSBNTaskList');
                // 地市 1032为无锡地市
            } else if (workTypeCode === '1014'|| workTypeCode === '1015'|| workTypeCode === '1023'|| workTypeCode === '1032') {
                ClientJs.openWebKit(encodeURIComponent(item.workUrl), '', '0', '', '', '', '', '', '', '', '');
            } else if(workTypeCode === '1022'){  //工单处理
                this.$router.push({
                    path: '/workOrders',
                    query: {
                        workId: item.taskId
                    }
                })
            } else if(workTypeCode === '1024'){  //楼宇视图
                this.$router.push({
                    path: '/buildingViewDetail',
                    query: {
                        objectId : item.groupId,
                        workId : item.taskId
                    }
                })
            }else if(workTypeCode === '1058'){ //物理楼宇视图
                this.$router.push({
                    path: '/buildingViewDetail',
                    query: {
                        objectId : item.groupId,
                        workId : item.taskId,
                        channelId:'92'
                    }
                })
            } else if(workTypeCode === '1025'){  //清单集团视图
                this.$router.push({
                    path: '/listGroupDetail',
                    query: {
                        workIdFlag:true,
                        taskId: item.taskId,
                        groupId:item.groupId
                    }
                })
            }else if(workTypeCode === '1030'){ //政企客户经理-集团拜访任务
                this.$alert("请切换到政企客户经理岗位进行计划制定！");
            } else if(workTypeCode === '1031'){  //移动云
                this.$router.push({
                    path: '/maskTask',
                    query: {
                        workId: item.taskId,
                        title: item.content,
                        workTypeCode: item.workTypeCode,
                        planDate: this.queryDay
                    }
                })
            }else if(workTypeCode === '1035'){  //移动云
                this.$router.push({
                    path: '/workHandle',
                    query: {
                        taskId: item.groupId,
                        comeFrom: 'workStage'
                    }
                })
            }else if(workTypeCode === '1033' || workTypeCode === '1034'){  //批量集团拜访任务
                this.$router.push({
                    path: '/maskTask',
                    query: {
                        workId: item.taskId,
                        title: item.content,
                        workTypeCode: item.workTypeCode,
                        planDate: this.queryDay
                    }
                })
            }else if(workTypeCode === '1041'){  //网格任务包
                this.$router.push({
                    path: '/taskPackDetail',//详情页面路径
                    query:{
                        taskpId: item.groupId,
                        workId: item.taskId
                    }
                })
            }else if(workTypeCode === '1038') {
                this.$router.push({
                    path: '/opportunityMes',
                    query: {
                        workId: item.taskId,
                        objectId : item.groupId,
                        taskName: item.taskName
                    }
                })
            }else if(workTypeCode === '1040') {   //清单集团摸排任务
                this.$router.push({
                    path: '/newCorporationDetails',
                    query: {
                        groupId:item.groupId,
                        gobackFlag: 'localView'
                    }
                })
            }else if(workTypeCode === '1042') {   //商机提醒任务（楼宇）
                this.$router.push({
                    path: '/buildViewOppMes',
                    query: {
                        workId: item.taskId,
                        objectId : item.groupId,
                        taskName: item.taskName
                    }
                })
            }else if(workTypeCode == '1051'){
                let subId = "";
                if(item.groupId && item.groupId.indexOf("-") != "-1") {
                    subId = item.groupId.substring(0, item.groupId.indexOf("-"));
                }
                this.$router.push({
                    path: '/taskTitle',
                    query: {
                        workId: item.taskId,
                        objectId : item.groupId,
                        taskName: item.taskName,
                        workTypeCode: item.workTypeCode,
                        taskTime: item.taskTime,
                        limitDate: item.limitDate,
                        subId: subId
                    }
                })
            } else if(workTypeCode === '1066') {   //道路任务
                this.$router.push({
                    path: '/corridorViewDetail',
                    query: {
                        workId: item.taskId,
                        objectId : item.groupId,
                        taskName: item.taskName,
                        returnFlag : "WorkStage"
                    }
                })
            } else if (workTypeCode === '1065') { // 双向协同
                this.$router.push('twoWayUserOrderList?peopleType='+this.peopleType+'&telNum='+item.content+'&srcFrom=workStage');
            } else {
                this.selectItemContinue(item);
            }
        },
        selectItemContinue(item){
            let uinfo = this.userInfoData;
            let workTypeCode = item.workTypeCode;
            let self = this;
            if (workTypeCode === '1044'){//集团预警任务
                this.$router.push({
                    path: '/groupWarningTaskDetail',
                    query: {
                        objectId : item.groupId,
                        objectName:item.taskName,
                    }
                })
            }else if(workTypeCode === '1039') {   //二次维护功能（连云港）
                this.$router.push({
                    path: '/secondMaintenanceList',
                    query: {
                        title: item.content,
                        workId: item.taskId,
                        objectId : item.groupId,
                        taskName: item.taskName
                    }
                })
            }else if(workTypeCode === '1045') {   //栅格任务
                let crmId = this.userInfoData.crmId;
                if (crmId && crmId != 'null' && crmId != 'undefined') {
                    this.$router.push({
                        path: '/customSceneDetail',
                        query: {
                            workIdFlag:true,
                            workId: item.taskId,
                            objectId: item.groupId,
                            channelId: "78"
                        }
                    })
                }else {
                    this.$alert("当前岗位没有CRM工号，不能执行任务")
                }
            }else if(workTypeCode === '1046') {   //营销协同预约
                this.$router.push({
                    path: '/workOrderFeedback',
                    query: {
                        orderSeq:item.groupId,
                        workId:item.taskId
                    }
                })
            }else if(workTypeCode === '1047') {   //算账单跟进任务
                this.$router.push({
                    path: '/familyDiagnosisDeskTop',
                    query: {
                        strList:item.groupId,
                        comeFrom : 'workStage'
                    }
                })
            }else if(workTypeCode === '1048') {   //稽核回单
                this.$router.push({
                    path: '/returnAuditFeedback',
                    query: {
                        taskId:item.taskId
                    }
                })
            }else if(workTypeCode === '1049') {   //学习任务
                this.$router.push({
                    path: '/ArticleList',
                    query: {
                        comeFrom:'workStage'
                    }
                })
            }else if(workTypeCode === '1050') {   //个人商机提醒任务
                this.$router.push({
                    path: '/personalOppMes',
                    query: {
                        workId: item.taskId,
                        objectId : item.groupId,
                        channelId : "-3"
                    }
                })
            }else if(workTypeCode === '1052') {  //网格实时预警工单
                console.log(item)
                this.$router.push({
                    path: '/realTimeWarnOrder',
                    query: {
                        taskId: item.taskId,
                        workTypeCode: item.workTypeCode,
                        taskName: item.taskName,
                        taskTime: item.taskTime,
                        type: '1'
                    }
                })
            }else if(workTypeCode === '1053') {   //网格实时预警工单
                this.$router.push({
                    path: '/realTimeWarnOrder',
                    query: {
                        taskId: item.taskId,
                        workTypeCode: item.workTypeCode,
                        taskName: item.taskName,
                        taskTime: item.taskTime,
                        type: '2'
                    }
                })
            }else if(workTypeCode === '1055') {//政企知识库
                this.openKnowledge(item);
            }else if(workTypeCode === '1056') {//训战任务
                this.$router.push({
                    name: 'TrainTaskDetails',
                    query: {
                        objectId : item.groupId,//任务标签\文章标签
                    }
                })
            }else if(workTypeCode === '1057') {//问题诊断书
                this.$router.push({
                    name: 'ProblemDiagnosisList',
                })
            }else if(workTypeCode === '1061'){
                ClientJs.openWebKit(encodeURIComponent(item.workUrl), '', '0', '', '', '', '', '', '', '', '');
            } else if (workTypeCode === '1062') {//物理楼宇商机任务
                this.$router.push({
                    name: 'PhyBuildingOpportunityDetails',
                    query:{
                        workId: item.taskId,
                        objectId : item.groupId,
                        returnFlag : "WorkStage"
                    }
                })
            } else if (workTypeCode === '1067') {
                //道路商机提醒任务
                this.$router.push({
                    name: 'corridorBusiness',
                    query:{
                        workId: item.taskId,
                        objectId : item.groupId,
                        returnFlag : "WorkStage"
                    }
                })
            }else if(workTypeCode  === '1069'){//摸排分配任务

                let subWgtUrl = '/subWgt?path=blankGroupFilingTasks&workId=' + item.taskId
                this.$router.push(subWgtUrl)

            }else if(workTypeCode  === '1070'){//空白集团建档

                let subWgtUrl = '/subWgt?path=blankGroup&groupId=' + item.groupId + '&workId=' + item.taskId + '&gobackFlag=myTaskList&workTypeCode=' + workTypeCode
                this.$router.push(subWgtUrl)
            }else if(workTypeCode  === '1076'){//空白酒店建档

                let subWgtUrl = '/subWgt?path=hotelAndShopsView&objectId='+item.groupId+'&workId='+item.taskId+"&channelId=96&gobackFlag=ald&workTypeCode="+workTypeCode
                this.$router.push(subWgtUrl)
            }else if(workTypeCode  === '1077'){//空白商铺建档

                let subWgtUrl = '/subWgt?path=hotelAndShopsView&objectId='+item.groupId+'&workId='+item.taskId+"&channelId=95&gobackFlag=ald&workTypeCode="+workTypeCode
                this.$router.push(subWgtUrl)
            }else if(workTypeCode  === '1071'||workTypeCode  === '1078'||workTypeCode  === '1079'){//淮安宽带预警维护
                let workUrl = '&url=' +item.workUrl
                this.tokenFromAld('haydApp','-1',uinfo,'',workUrl);
            }else if(workTypeCode  === '1072'){//物理楼宇任务（新）

                let subWgtUrl = '/subWgt?path=buildingView&objectId='+item.groupId+'&workId='+item.taskId+"&channelId=92&gobackFlag=ald"
                this.$router.push(subWgtUrl)
            }else if(workTypeCode  === '1073'){//道路任务(新)

                let subWgtUrl = '/subWgt?path=roadView&objectId='+item.groupId+'&workId='+item.taskId+"&channelId=104&gobackFlag=ald"
                this.$router.push(subWgtUrl)
            }else if(workTypeCode  === '1074'){//泛住宿任务（新）

                let subWgtUrl = '/subWgt?path=hotelAndShopsView&objectId='+item.groupId+'&workId='+item.taskId+"&channelId=96&gobackFlag=ald"
                this.$router.push(subWgtUrl)
            }else if(workTypeCode  === '1075'){//商铺任务（新）

                let subWgtUrl = '/subWgt?path=hotelAndShopsView&objectId='+item.groupId+'&workId='+item.taskId+"&channelId=95&gobackFlag=ald"
                this.$router.push(subWgtUrl)
            }else if(workTypeCode === '1088'){//营销提醒
                this.$router.push({
                    path: '/demoPage',
                    query: {
                        title:item.content,
                        groupNameAndRemark:item.taskName,
                        workId:item.taskId,
                        groupId:item.groupId,
                        channelId:"109"//营销提醒
                    }
                })
            } else if(workTypeCode === '1089'){//营销建议书
                this.$router.push({
                    path: '/demoPage',
                    query: {
                        title:item.content,
                        groupNameAndRemark:item.taskName,
                        workId:item.taskId,
                        groupId:item.groupId,
                        channelId:"108"//营销建议书
                    }
                })
            }else if(workTypeCode === '1090'){
                this.$router.push({
                    path: '/groupWarnOrder',
                    query: {
                        groupId:item.groupId,
                        workId:item.taskId,
                        srcCorm:"workStage"
                    }
                })
            }else if(workTypeCode === '1091'){
                let orderType = '';//预警类型 1：携出异动  2：宽带异动,3、降档/异动
                let type = item.taskId.split('-');
                console.info("type",type)
                if(type[0] == 'a'){
                    orderType = '1';
                }else if(type[0] == 'b'){
                    orderType = '2';
                }else if(type[0] == 'c'){
                    orderType = '3';
                }else {
                    return;
                }
                this.$router.push({
                    path: '/personMovementAlert',
                    query: {
                        workId:item.taskId,
                        orderId:item.groupId,
                        orderType:orderType,
                        srcCorm:"workStage"
                    }
                })

            }else if (workTypeCode === '1092') {//扬州弱项任务包（网格）
                let workUrl = '&url=' + item.workUrl
                this.tokenFromAld('yzApp', '-1', uinfo, '', workUrl)
            }else if (workTypeCode === '1093') {//连云港网格调度任务
                let workUrl = '&url=' + item.workUrl
                this.tokenFromAld('lygApp', '-1', uinfo, '', workUrl)
            } else {//1002 数字地图任务  1005 上行下达任务 1006 新入网不合规号码整改 1064中小集团摸排审批
                this.goToken('cmopDb', uinfo, item.workUrl);
            }
        },
        //跳数字地图
        goMap() {

            /*let uinfo = this.userInfoData;
                let webUrl = Storage.get('webUrl');
                let client = /android|harmony/gi.test(navigator.userAgent) ? 'ANDROID' : 'IOS'
                let url = `${webUrl}/iportal/tokenAuthen/genToken.do?opId=cmopApp&prviId=3133&phoneNumber=${uinfo.servNumber}`;
                url += `&regionId=${uinfo.region}&operId=${uinfo.staffId}&stationid=${uinfo.stationId}&client_type=${client}`;
                this.$http.get(url).then((response) => {
                    let data = response.data;
                    let opUrl = data.opUrl;
                    console.info(opUrl);
                    ClientJs.openWebKit(encodeURIComponent(opUrl), '', '0', '', '', '', '', '', '', '', '');
                }).catch((response) => {
                })*/
            let uinfo = Storage.session.get("userInfo");
            this.tokenFromAld('cmopApp','3133',uinfo,'','');
        },
        //token拉起
        goToken(opId, uinfo, workUrl) {
            // let client = /android|harmony/gi.test(navigator.userAgent) ? 'ANDROID' : 'IOS'
            // let url = `/iportal/tokenAuthen/genToken.do?opId=${opId}&prviId=`;
            // url += `&regionId=${uinfo.region}&operId=${uinfo.staffId}&stationid=${uinfo.stationId}&client_type=${client}`;

            // this.$http.get(url).then((response) => {
            //     let data = response.data;
            //     let opUrl = data.opUrl + '&url=' + workUrl;
            //     ClientJs.openWebKit(encodeURIComponent(opUrl), '', '0', '', '', '', '', '', '', '', '');
            // }).catch((response) => {
            // });



            let client = /android|harmony/gi.test(navigator.userAgent)?'ANDROID':'IOS';
            let  url = `/xsb/api-user/tokenAuthen/h5genToken.do?opId=${opId}&prviId=-1&clientType=${client}`;
            url += `&regionId=${uinfo.region}&operId=${uinfo.staffId}&stationId=${uinfo.stationId}&phoneNumber=${uinfo.servNumber}`;
            this.$http.get(url).then((response)=>{
                let {retCode,retMsg,data} = response.data;
                if(retCode == '0'){
                    let opUrl = data.opUrl + '&url=' + workUrl;
                    console.log(opUrl)
                    ClientJs.openWebKit(encodeURIComponent(opUrl), '', '0', '', '', '', '', '', '', '', '');
                } else {
                    this.$alert(retMsg || '拉起token失败');
                }

            }).catch((response)=>{
            })
        },
        getData() {
            var _this = this;
            //工作台/推荐活动
            this.$http.get('/xsb/api-user/desk/h5DeskCountInfo?stationId='+this.curStationId)
                .then((res) => {
                    let body = res.data;
                    if (body.retCode === '0') {
                        this.workBenchHeadData = body.data.countInfo;
                        this.isHaveToDo = body.data.isHaveToDo;//0代表有 1代表无
                        this.activeInfoData = body.data.activeInfo;
                    }
                }).catch((response) => {
            });

            //常用功能和公告信息
            this.$http.get('/xsb/api-user/desk/h5DeskOtherInfo?stationId=' + this.curStationId+'&area=business')
                .then((res) => {
                    if (res.data.retCode == '0') {
                        let data = res.data.data;
                        //判断常用，还是热门 hasUsuallyFunc:0(为常用)，1(为热门)
                        data.hasUsuallyFunc == '0' ? this.hotOrUsualTxt = '常用功能' : this.hotOrUsualTxt = '热门功能';

                        _this.commonFnList = data.funcInfo;
                        _this.setHotMenuList(data.funcInfo);
                        //公告信息
                        let url = `/xsb/api-user/noticeInfo/h5getNoticeInfo?type=1&orgId=${this.uinfo.orgId}`;
                        this.$http.get(url).then(res => {
                            if (res.data.retCode == '0') {
                                let data = res.data.data;
                                _this.noticeInfo = data?data:[];
                                //20240516 qp 新客户视图展示公告
                                let flag = Storage.session.get('noShowNotice');
                                if(!flag && data){
                                    this.isShowTip = true;
                                }
                            }
                        });
                    } else {
                        _this.commonFnList = [];
                        _this.setHotMenuList([]);
                    }
                }).catch((response) => {
            });
        },

        //获取个人信息
        getUserInfo() {
            let uinfo = Storage.session.get('userInfo');
            this.curStationId = uinfo.stationId;
            this.userInfoData = uinfo;
            this.checkStationTelNum(this.curStationId, uinfo.relTelnum)
        },
        // 跳转业务日志查询
        gobusilogseach() {
            //埋点
            this.updateMenuChain(this.CONSTVAL.MENU_CHAIN.FUNCTION_ENTRY, 2);
            this.updateMenuChain(this.CONSTVAL.MENU_CHAIN.BUSINESS_LOG_QUERY, 3);
            menuLogRecord({})
            // 菜单截屏权限控制
            checkScreenShot(this.CONSTVAL.MENU_CHAIN.BUSINESS_LOG_QUERY);


          this.$router.push({
                path: '/busiLogIndex',
                query: {
                    isToday: '1',
                    srcFrom:'/workStage'
                }
            })
        },
        goIncentiveDetail() {
            //埋点
            this.updateMenuChain(this.CONSTVAL.MENU_CHAIN.FUNCTION_ENTRY, 2);
            this.updateMenuChain(this.CONSTVAL.MENU_CHAIN.AMOUNT, 3);
            menuLogRecord({})

            this.$router.push({
                path: '/incentiveDetail',
                query: {
                    srcFrom:'/workStage'
                }
            })
        },
        //紧急按钮点击
        emergentCk() {
            if (this.emergentFlag == 0) {
                this.emergentFlag = 1;
            } else {
                this.emergentFlag = 0;
            }
        },

        /*             //点击用户姓名，展示岗位信息
                        showStation(){
                            this.showStationFlg = true;
                        },
                        hideStation(){
                            this.showStationFlg = false;
                        },
                        //切换岗位
                        chgStation(item){
                            this.showStationFlg = false;
                            this.curStationId = item.stationId;
                            //更新岗位相关的待办、菜单
                        }, */

        //客户视图登录按钮点击
        csViewLogin() {
            let idCardWayFlg = true;//默认有身份证鉴权
            let callBackFn = (obj)=>{
                if (obj.result == '1') {
                    //  修改0329 增加弹窗-弹窗日志记录接口 手机号
                    this.qryNewCustom(obj.telnum)
                }
            }
            //crm为空用
            if(Storage.session.get('userInfo').crmId ) {
                let csviewJqFlg = Storage.session.get('csview_jq_flg');
                if (csviewJqFlg) {//使用新版本的
                    this.showAuthent(idCardWayFlg, callBackFn, csviewJqFlg, Storage.session.get('csview_jq_data'))
                } else {
                    //查询用新鉴权还是老版本鉴权框的开关
                    this.$http.post('/xsb/ability/businessLimit/h5qryCsviewAuth').then((res) => {
                        let {retCode, data} = res.data;
                        let jqDataArr = retCode == 0 && data && data.jqDateInfo;//客户视图配置的鉴权类型
                        let flg = 'oldJq';//默认旧的鉴权方式
                        if (jqDataArr && jqDataArr.length > 0) {//用新版的鉴权窗
                            flg = 'newJq';
                            Storage.session.set('csview_jq_data', jqDataArr);
                        }
                        Storage.session.set('csview_jq_flg', flg);
                        this.showAuthent(idCardWayFlg, callBackFn, flg, jqDataArr)
                    });
                }
            }else{
                this.$alert("crm工号不能为空");
            }
        },
        showAuthent(idCardWayFlg,callBackFn,csviewJqFlg,jqDataArr){
            Authenct({
                popFlag: true,
                jqTypeData:jqDataArr,
                idCardWay: idCardWayFlg,            //是否支持身份证鉴权方式
                telnum: this.telnum,
            },  callBackFn);
        },
        // 0320 弹窗接口
        qryNewCustom(telNum){
            //记录用户位置信息
            ClientJs.getLocation('','recordLoaction');
            // 记录客户视图菜单链
            this.updateMenuChain('ke-hu-shi-tu', 2);
            this.$http.get(`/xsb/personBusiness/recordlogbusiaction/h5queryLabelByTel?telnum=${telNum}`).then((res) => {
                let { retCode, retMsg, data } = res.data;
                if(retCode == '0'){
                    if(data != ''&& data != null && data.versionList ){
                        this.versionList = data.versionList;
                        //弹窗提示  type: 'confirm'
                        if(this.versionList.s000000077351!='非升级投诉用户'){
                            this.$messagebox({
                                title: '温馨提示',
                                message: '您好！请在业务服务过程中按照标准化服务要求认真执行。业务受理过程需经过客户密码确认，引导客户清晰理解业务协议内容，并提醒客户在签字板上进行必要、工整的签字。严格遵守营销准则，注意使用完整营销话术，避免出现过度营销行为。',
                                showCancelButton: false,
                                confirmButtonText: '确认'
                            }).then((action) => {
                                this.alterLog(telNum);
                                this.$router.push({
                                    name: 'CsViewIn',
                                    query: {
                                        telnum: telNum
                                    }
                                });
                            });
                        }else{
                            this.$router.push({
                                name: 'CsViewIn',
                                query: {
                                    telnum: telNum
                                }
                            });
                        }
                    }else{
                        this.$router.push({
                            name: 'CsViewIn',
                            query: {
                                telnum: telNum
                            }
                        });
                    }
                }else{
                    //  this.$alert(retMsg || '弹窗标签查询接口返回失败');
                    this.$router.push({
                        name: 'CsViewIn',
                        query: {
                            telnum: telNum
                        }
                    });
                }
            }).catch((res) => {
                this.$router.push({
                    name: 'CsViewIn',
                    query: {
                        telnum: telNum
                    }
                });
            });
        },
        judeRecordLoaction(locationParam){
            let userInfo = Storage.session.get('userInfo');
            let param = {
                servNumber:userInfo.servNumber,
                type:"2",
                configTypeName :"ability-gps-config-",
            }
            this.$http.post("/xsb/ability/submenuAuthen/h5QryConfigAbility",param).then(res =>{
                if(res.data.retCode == '0'){
                    if(res.data.data == '1'){
                        console.info("ability-gps-config-"+userInfo.region,"有权限")
                        this.recordLoaction(locationParam);
                    }else {
                        console.info("ability-gps-config-"+userInfo.region,"没权限")
                    }
                }
            })
        },

        recordLoaction(locationParam){
            let userInfo = Storage.session.get('userInfo');
            let param = {
                phone:userInfo.servNumber,
                name:userInfo.operatorName,
                x : locationParam.longitude,
                y : locationParam.latitude,
                location : locationParam.address,
                type:"1",//客户端
                distance:"0",
                locationError :"0",
                timeError :"0",
            }
            this.$http.post("/xsb/personBusiness/attendanceCard/h5RecordLocation",param)
        },
        // 0320 弹窗日志记录
        alterLog(telNum){
            let param = {
                "telNum":telNum,
                "beId":this.uinfo.region,
                "menuId":"101",
                "note":"客户视图",
                "operType":"SensitiveUserNotification"
            }
            //查询是否需要免填单
            this.$http.post('/xsb/personBusiness/recordlogbusiaction/h5popupLogRecord',param).then((res)=>{
                if(res.data.retCode != '0'){
                    //  this.$alert(res.data.retMsg);
                }
            })
        },
        //拉起统一知识库页面
        openKnowledge(item){
            let url=`/xsb/api-user/knowledge/h5GetTicketUrl`;
            let p={
                "staff_id":this.uinfo.staffId,
                "phone_number":this.uinfo.servNumber,
                "station_id":this.uinfo.stationId,
            };
            let params={
                "opId":'knowledge',
                "regionId":this.uinfo.region,
                "prviId":'1055',
                "operId":this.uinfo.crmId,
                "clientType":/android|harmony/gi.test(navigator.userAgent)?'ANDROID':'IOS',
                "param":JSON.stringify(p)
            };
            this.$http.post(url,params).then((res)=>{
                if(res.data.retCode=='0'){
                    let knowledgeUrl=res.data.data;
                    //根据待办的url跳转
                    let url = "busiurl=http://183.207.195.94:8080" + item.workUrl;
                    knowledgeUrl = knowledgeUrl.split("busiurl=")[0] + url;
                    console.log(knowledgeUrl)
                    ClientJs.openWebKit(encodeURIComponent(knowledgeUrl),'政企知识库','1', '0', '', '', '', '', '', '', '');
                    this.closeTask(item.taskId)
                }else {
                    this.$alert(res.data.retMsg || '获取知识库URL失败')
                }
            }).catch((res)=>{
                this.$alert('获取知识库URL路径异常')
            })
        },
        //拉起后关闭待办
        closeTask(workId){
            let url=`/xsb/api-user/desk/h5UpdateTaskStatus`;
            let params = {
                "workTypeCode":'1055',
                "workId":workId,
                "status":'2',
            };
            this.$http.post(url,params);
        },
        goPersonView(ndata, privId) {
            //let uinfo = Storage.session.get('userInfo');
            var realstatecode = ndata.realstatecode;
            var region = this.checkStr(ndata.region);
            var regionname = this.checkStr(ndata.regionname);
            var registerdate = this.checkStr(ndata.registerdate);
            var userbrand = this.checkStr(ndata.userbrand);
            var username = this.checkStr(ndata.username);
            var userstatus = this.checkStr(ndata.userstatus);
            var viptypeid = this.checkStr(ndata.viptypeid);
            var userprod = this.checkStr(ndata.userprod);


            var url = Storage.get('webUrl') + "/h5personView.do?nextStep=h5cc_cgetusercust&telnum=" + this.telnum + "&operid=" + this.userInfoData.crmId;
            url += "&realstatecode=" + realstatecode + "&region=" + region + "&regionname=" + regionname;
            url += "&registerdate=" + registerdate + "&userbrand=" + userbrand + "&username=" + username;
            url += "&userstatus=" + userstatus + "&viptypeid=" + viptypeid + "&source=3" + "&userprod=" + userprod;
            url += "&stationId=" + this.userInfoData.stationId + "&phoneNumber=" + this.userInfoData.servNumber + "&staffId=" + this.userInfoData.staffId + "&privId=" + privId;
            url = iEncrpt(url);
            console.info(url);
            ClientJs.openWebKit(url, '', '0', '', '', '', '', '', '', '', '');
        },

        checkStr(str, defaultval) {
            if (typeof(str) == "undefined" || str == null || str == "null") {
                if (defaultval == undefined) {
                    str = "";
                } else {
                    str = defaultval;
                }
            }
            return str;
        },
        //展示视图列表
        showView() {
            if (this.viewInfoList && this.viewInfoList.length > 1) {//视图个数大于1
                this.viewListFlg = !this.viewListFlg;
            }
        },
        //切换视图
        changeView(item) {
            this.viewListFlg = false;
            this.currentView = item;
        },
        //进入搜索页面
        goSearch(vid) {
            if (vid === '1') {//集客视图
                this.$router.push('/SearchJt?src=jike');
            } else if (vid === '2') {//小区视图
                this.$router.push('/SearchJt?src=xq');
            } else if (vid === '3') {//渠道视图
                this.$router.push('/SearchJt?src=qd');
            } else if (vid === '5') {//高校视图
                this.$router.push('/SearchJt?src=gx');
            } else if (vid === '6') {//楼宇视图
                this.$router.push('/SearchJt?src=ly');
            } else if (vid === '7') {//清单集团视图
                this.$router.push('/SearchJt?src=lg');
            }
        },
        formatMoney(item){
            return item?(Number(item)/100).toFixed(): '0';
        },
        goBusinessIncome(){
            this.$router.push('/BusinessIncome?sumDay=' + this.workBenchHeadData.todaySum + '&sumMonth=' + this.workBenchHeadData.monthSum);
        },
        goNoticeDetail(){
            this.$router.push('/NoticeDetail');
        },
        //调用客户端获取当前版本
        checkCurVersion(){
            if(Storage.get('clientVersion')){
                return;
            }
            if(/android|harmony/gi.test(navigator.userAgent)){
                var res = window.WebViewFunc.getAppVersion();
                var obj = eval('(' + res + ')');
                // this.version = obj.verison;
                Storage.set('clientVersion',obj.verison);
            }else{
                //window.location="clientrequest:getAppVersion::versionBack";
                ClientJs.getIosOrBrowserAppVersion();
            }
        },
        //搜索菜单
        qryAct(){
            this.$router.push({
                path: '/searchMenu',
                query: {
                    queryKey: this.queryKey,
                    srcFrom:'workStage'
                }
            })
        },
        //清除搜索框中数据
        clear() {
            this.queryKey = '';
        },

        // 当前岗位开关控制
        async handleStationSwitch() {
            const uinfo = Storage.session.get('userInfo');
            const res = await this.$http.post('/xsb/api-user/menu/h5GetStationSwitch', {
                stationId: uinfo.stationId
            })
            console.info(res, '当前岗位开关控制');

            if (res) {
                const data = res.data

                if (data && data.retCode == '0') {
                    // 有权限
                    Storage.session.set('stationSwitch', 1)
                } else {
                    // 无权限
                    Storage.session.set('stationSwitch', 0)
                }
            }
        },

        ...mapMutations({
            setHotMenuList: 'SET_HOT_MENU_LIST'
        }),
        //去往网格助手页面
        goAiAssistant(){
            //埋点
            this.updateMenuChain(this.CONSTVAL.MENU_CHAIN.FUNCTION_ENTRY, 2);
            this.updateMenuChain(this.CONSTVAL.MENU_CHAIN.GRID_ASSISTANT, 3);
            menuLogRecord({})
            // 菜单截屏权限控制
            checkScreenShot(this.CONSTVAL.MENU_CHAIN.GRID_ASSISTANT);


          this.$router.push('/aiMarketAssistant')
        },

        // aiSwitch(){
        //     let groupSwitchParam = {
        //         switchType: 'ai_assistant'
        //     }
        //     this.$http.post('/xsb/personBusiness/gridSand/h5GridMarketSwitch', groupSwitchParam).then((res2) => {
        //         if (res2.data.retCode == 0) {
        //             this.aiFlag = true
        //         } else {
        //             this.aiFlag = false
        //         }
        //     })
        // }

    },

    watch: {
        isShowTreeBox(val) {
            if (val) {
                let container = document.getElementsByClassName("topWrap")[0];
                container.style.overflow = "hidden";
            } else {
                window.setTimeout(function() {
                    let container = document.getElementsByClassName("topWrap")[0];
                    container.style.overflow = "auto";
                },400);
            }
        },

        /*isShowProvincial(val) {
            if (val) {
              this.$refs.provinceCountPage.initProvincialData();
            }
          }*/
    }
}
</script>

<style scoped lang="less">
@import "../../base/less/variable.less";
@import "../../base/less/mixin.less";

.menu-search-container{
    width:100%;
    background-color:#fff;
    padding-top: 0.75rem;
    padding-left: 0.75rem;
    padding-right: 0.75rem;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    height:50px;
    .menu-search-box{
        flex:auto;
        position:relative;
        margin-right: 0.5rem;
        .menu-box{
            width:100%;
            height:30px;
            background:rgba(241,241,241,1);
            border-radius:1rem;
            box-sizing: border-box;
            outline: 0;
            padding-left:2rem;
            padding-right: 5.5rem;
            overflow: hidden;
            font-size: 12px;
            &::placeholder{
                color: #B2B2B2;
            }
        }
        .sousuo{
            position:absolute;
            left:0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color:#B2B2B2;
            font-size:20px;
        }
        .shanchu{
            position:absolute;
            right:3rem;
            top: 50%;
            color:#B2B2B2;
            transform: translateY(-50%);
            font-size:12px;
        }
        .menu-search-txt{
            position:absolute;
            right:0.75rem;
            top: 50%;
            color:#007AFF;
            transform: translateY(-50%);
            font-size:12px;
        }
    }
}

.click-area {
    position: absolute;
    right: 1rem;
    top: 0;
    bottom: 0;
    color: #585858;
    width: 30px;
    background: #fff;
    z-index: 10;
    text-align: center;
}

.add-full-wrapper {
    position: absolute;
    top: 100%;
    right: 0.5rem;
    left: 3rem;
    background: rgba(227, 237, 255, 1);
    box-shadow: 0px 4px 8px 0px rgba(85, 85, 85, 0.14);
    border-radius: 8px;
    border: 1px solid rgba(188, 213, 255, 1);
    font-size: 12px;
    color: rgba(66, 108, 203, 1);
    min-height: 36px;
    line-height: 16px;
    z-index: 200;
    box-sizing: border-box;
    padding: 6px 0.75rem;
    .dao-sanjiao {
        border: 1px solid rgba(188, 213, 255, 1);
        border-bottom: none;
        border-right: none;
        background: rgba(227, 237, 255, 1);
        width: 12px;
        height: 12px;
        transform: rotate(45deg);
        position: absolute;
        top: -6px;
        right: 1rem;
        z-index: 2;
    }
}

.content-wrap-hide {
    transform: translateY(-100px);
}

.right-arrow-wrap {
    position: absolute;
    right: 5px;
    top: 50%;
    width: 10px;
    transform: translateY(-50%) rotateY(180deg);
}

.login-info-box {
    position: relative;
    z-index: 999;
    background-color: #fff;
    .busi-log{
        background: white;
        color: #424242;
        box-shadow: 0px 0px 0px 0px;
        padding: 4px 0px;
        .rizhi{
            color: #5C5858;
        }
        .jiantou-copy-copy{
            font-size: 4px;
            color: #979797;
        }
    }
}

.station-line {
    height: 1px;
    background-color: #F5F5F5;
    border: 0;
    margin-left: 16px;
}

.view-wrapper {
    min-height: 114px;
    position: absolute;
    top: 255px;
    left: 16px;
    right: 16px;
    z-index: -1;
    box-sizing: border-box;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 10px 30px 0px rgba(78, 103, 143, 0.23);
    border-radius: 20px;
    border: 1px solid rgba(213, 233, 255, 1);
    padding: 16px 0px;
    transition: all 0.4s linear;
    opacity: 0;
    &.show {
        opacity: 1;
        z-index: 99;
    }
    .dao-sanjiao {
        border: 1px solid rgba(213, 233, 255, 1);
        border-bottom: none;
        border-right: none;
        background: rgba(255, 255, 255, 1);
        width: 16px;
        height: 16px;
        border-radius: 6px;
        transform: rotate(45deg);
        position: absolute;
        top: -8px;
        left: 38px;
        z-index: 2;
    }
    .view-ul {
        height: 100%;
        z-index: 10;
        .view-li {
            float: left;
            width: 25%;
            text-align: center;
            .icon-wrap {
                text-align: center;
                line-height: 50px;
                .icon-img {
                    width: 50px;
                    height: 50px;
                    border-radius: 50%;
                    color: #fff;
                    display: inline-block;
                    text-align: center;
                    i {
                        font-size: 28px;
                    }
                    &.kehu-bg {
                        background: linear-gradient(137deg, rgba(35, 222, 224, 1) 0%, rgba(46, 143, 250, 1) 100%);
                        box-shadow: 0px 4px 8px 0px rgba(39, 195, 233, 0.61);
                    }
                    &.jituan-bg {
                        background: linear-gradient(137deg, rgba(224, 195, 35, 1) 0%, rgba(250, 134, 46, 1) 100%);
                        box-shadow: 0px 4px 8px 0px rgba(230, 181, 38, 0.54);
                    }
                    &.xiaoqu-bg {
                        background: linear-gradient(137deg, rgba(245, 116, 185, 1) 0%, rgba(250, 46, 161, 1) 100%);
                        box-shadow: 0px 4px 8px 0px rgba(255, 176, 219, 1);
                    }
                    &.qudao-bg {
                        background: linear-gradient(135deg, rgba(69, 237, 126, 1) 0%, rgba(52, 171, 95, 1) 100%);
                        box-shadow: 0px 4px 6px 0px rgba(62, 208, 112, 0.68);
                    }
                    &.school-bg {
                        background: linear-gradient(141deg, rgba(255, 163, 163, 1) 0%, rgba(249, 86, 86, 1) 100%);
                        box-shadow: 0px 4px 8px 0px rgba(242, 122, 122, 0.61);
                    }
                    &.building-bg {
                        background-image: linear-gradient(137deg, #F5B07B 0%, #A26C57 100%);
                        box-shadow: 0 4px 8px 0 rgba(207,181,181,0.61);
                    }
                    &.listGroup-bg{
                        background-image: linear-gradient(137deg, rgb(35,200,230) 0%, rgb(40,160,245) 100%);
                        box-shadow: 0 4px 8px 0 rgba(168,230,245,0.61);
                    }
                }
            }
            .icon-name {
                display: block;
                margin-top: 12px;
                font-size: 10px;
                color: rgba(88, 88, 88, 1);
            }
        }
        li:nth-child(5) {
            margin-top: 6px;
        }
        li:nth-child(6) {
            margin-top: 6px;
        }
    }
}
.more-size{
    font-size: 12px;
    color: #6B89AD !important;
}
.panc-tips{
    animation: fades-in 1.5s both linear;
    -webkit-animation: fades-in 1.5s both linear;
    width:70%;
    position:fixed;
    left:12px;
    z-index:1000;
    bottom:12px;
    background:rgba(255,255,255,1);
    box-shadow:0px 2px 8px 0px rgba(0,0,0,0.24);
    border-radius:7px;
    border:1px solid rgba(106,203,254,1);
    .tips-head{
        border-radius: 7px 7px 0 0;
        line-height:35px;
        height:35px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding:0 12px 0 16px;
        background:linear-gradient(180deg,rgba(0,178,255,1) 0%,rgba(0,122,255,1) 100%);
        .head-text{
            display:inline-block;
            font-size:16px;
            color:#fff;
            margin-right:4px;
        }
        .close-seds{
            display:inline-block;
            text-align:center;
            height:22px;
            line-height: 22px;
            font-size:12px;
            color:#fff;
            padding: 0 8px;
            background:rgba(94,186,255,1);
            border-radius:12px;
        }
        .guanbi{
            display: inline-block;
            color: #fff;
            margin-left: 6px;
            vertical-align: middle;
        }
    }
    .tips-main{
        padding:12px 16px 18px 12px;
        .tips-pub{
            &.mar{
                margin-top:14px;
            }
            .pub{
                display:inline-block;
                font-size:12px;
            }
            .shijian1{
                .pub;
                color:#FF5400;
            }
            .zb{
                .pub;
                color:#0092FF;
            }
            .time-info{
                display:inline-block;
                color:#5B5B5B;
                font-size:14px;
                margin-bottom:8px;
            }
        }
        .tips-txt{
            color:#8F8F8F;
            font-size:12px;
        }
    }
}
.todo-wrap .navbar-wrapper{
    font-size: 14px;
    line-height: 40px !important;
    padding: 0 16px;
    background: #FAFAFA;
}
.todo-wrap .navbar-wrapper.active{
    margin-bottom: 15px;
}
.AIchat {
    position:fixed;
    left:84%;
    bottom:173px;
    text-align:center;
    z-index: 999;
    width: 44px;
    overflow: unset;
    .text {
        position: absolute;
        bottom: 40px;
        right: 0;
        img {
            height: 80px;
            width: auto;
        }
    }
    .pic {
        position: absolute;
        right: 0;
        bottom: 0;
        img {
            width: 50px;
            height: 50px;
        }
    }
}
.workStageNew-wrap{
    margin-bottom: -6px;
}

.workStage-head{
    width: 100%;
    //height: 365px;
    background: linear-gradient(180deg, #3F6FFE 0%, #FDFDFD  60%);
    position: relative;
    padding: 30px 0 0;
    box-sizing: border-box;
    .workStage-cloud{
        position: absolute;
        right: 0;
        top: 0;
        width: 210px;
        height: auto;
        pointer-events: none;
    }
}
.workStage-remind {
    width: 100%;
    height: 30px;
    background: #E87656;
    font-size: 12px;
    color: #FFFFFF;
    line-height: 18px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 0.78125rem 0 0.78125rem;
    box-sizing: border-box;
    margin-top: -30px;

    .workStage-latestXin {
        line-height: 24px;
        height: 24px;
        overflow: hidden;
        width: 90%;
    }
}

.workStage-frist{
    display: flex;
    justify-content: space-between;
    padding: 15px 20px 11px 20px;
    .workFrist-left{
        display: flex;
        align-items: flex-start;
        flex-direction: column;
        .workFrist-name{
            font-size: 29px;
            color: #FFFFFF;
            letter-spacing: 2px;
            font-weight: bold;
            white-space: nowrap;
        }
        .workFrist-role{
            display: flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.13);
            border-radius: 4px 4px 4px 4px;
            font-weight: 400;
            font-size: 14px;
            color: #FFFFFF;
            padding: 6px;
            //margin: 0 16px 0 13px;
            margin-top: 3px;
        }
    }
    .workFrist-left-SheQu{
        //align-items: flex-start;
        //flex-direction: column;
        &.compete-role {
            display: block;
        }
        .workFrist-name{
            font-size: 29px;
            color: #FFFFFF;
            letter-spacing: 2px;
            font-weight: bold;
            white-space: nowrap;
        }
        .workFrist-role{
            display: flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.13);
            border-radius: 4px 4px 4px 4px;
            font-weight: 400;
            font-size: 14px;
            color: #FFFFFF;
            padding: 6px;
            //margin: 0 16px 0 13px;
            margin-top: 3px;
        }
    }
    .workFrist-right{
        .top-icon {
            display: flex;
            align-items: center;
            justify-content: end;
        }
        .ocr-button{
            margin-right: 14px;
            width: 20px;
        }

        .index-change {
            margin-top: 14px;
            display: flex;
            align-items: center;
            border-radius: 4px;
            background-color: #FFFFFF;
            float: right;
            .region {
                padding: 4px 8px;
                font-size: 14px;
                color: #FFFFFF;
                background-color: #0B5CFF;
                border-top-left-radius: 4px;
                border-bottom-left-radius: 4px;
            }
            .provincial {
                padding: 4px 8px;
                font-size: 14px;
                color: #7D8AA7;
                background-color: #FFFFFF;
                border-top-right-radius: 4px;
                border-bottom-right-radius: 4px;
            }
            .change-btu {
                padding: 4px 8px;
                border-radius: 4px;
                display: flex;
                align-items: center;
                justify-content: center;
                white-space: nowrap;
                &.orange-bg {
                    background-color: #E56018;
                }
                &.blue-bg {
                    background-color: #007AFF;
                }
                img {
                    width: 28px;
                }
                .btu-icon {
                    color: #4C4C4C;
                    font-size: 14px;
                    margin-right: 4px;
                    &.choose-icon {
                        color: #FFFFFF;
                    }
                }
                .btu-name {
                    color: #4C4C4C;
                    font-size: 14px;
                    &.choose-name {
                        color: #FFFFFF;
                    }
                }
            }
        }
        .a-zu5638101x{
            margin-right: 14px;
            font-size: 20px;
            color: #FFFFFF;
            .workFrist-dizhiContent{
                position: absolute;
                z-index: 10;
                .workFrist-jiantou{
                    width: 0;
                    height: 0;
                    border-left: 8px solid transparent;
                    border-right: 8px solid transparent;
                    border-bottom: 10px solid #3DC66F;
                }
            }
            .workFrist-qiPao{
                position: absolute;
                z-index: 100;
                margin-top: 7px;
                left: 50px;
                width: 70%;
                line-break: anywhere;
                .workFrist-dizhi{
                    background: #3DC66F;
                    padding: 10px 14px;
                    border-radius: 6px;
                    color: white;
                    font-size: 15px;
                }
            }

        }
        .feiyong {
            font-size: 20px;
            color: #FFFFFF;
            margin-right: 12px;
        }

        .a-zu5638111x{
            font-size: 20px;
            color: #FFFFFF;
            margin-right: 12px;
        }
        .sousuo1{
            font-size: 20px;
            color: #FFFFFF;
        }
    }
}
.switch-shiTu{
    margin-left: 19px;
    display: flex;
    border-radius: 6px 6px 0 0;
    line-height: 35px;

    &.zhiBiaoTive{
        margin-left: 20px;
        margin-bottom: -4px;
    }
    .switch-shequ{
        display: inline-block;
        text-align: center;
        background: linear-gradient(-119deg, transparent 8px, #E1E9FF 0) top right;
        height: 35px;
        width: 70px;
        align-items: center;
        color: #626262;
        font-weight: 500;
        justify-content: center;
        border-top-left-radius: 6px;
        font-size: 16px;
        letter-spacing: 0.5px;
        &.active{
            background: linear-gradient(-119deg, transparent 8px, #FFFFFF 0) top right;
            font-weight: 900;
            color: #5f87ff;
        }
    }
    .switch-zhibiao{
        height: 35px;
        text-align: center;

        width: 70px;
        display: inline-block;
        align-items: center;
        color: #626262;
        font-weight: 500;
        justify-content: center;
        font-size: 16px;
        letter-spacing: 0.5px;
        &.active{
            font-weight: 900;
            color: #5f87ff;
        }
    }

}
.workcs-banner{
    .workcs-bannernei{
        padding-bottom: 30px;
        overflow: hidden;
        .workcs-banner-list{
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            //background: linear-gradient(180deg, #FFFFFF 0%, #E1E9FF 100%);
            background: #F0F5FF;
            border-radius: 0 10px 10px 10px;
            padding: 40px 0 16px 0;
            margin: 0px 16px 4px 16px;
            height: 210px;
            justify-content: space-between;
            position: relative;
            .workcs-banner-cs-wangge{
                text-align: center;
                font-size: 16px;
                position: absolute;
                left: 2px;
                top: 2px;
                padding: 8px 20px 8px 10px;
                background-color: #ffffff;
                border-radius: 10px 0 26px 0;
                font-weight: 500;
            }
            .workcs-banner-cs-gengduo{
                color: #305fe2;
                right: 2px;
                text-align: center;
                font-size: 16px;
                position: absolute;
                top: 2px;
                padding: 8px 10px 8px 14px;
                background-color: #ffffff;
                border-radius: 0 10px 0 28px;
                font-weight: 500;
            }
            //.workcs-banner-touming{
            //  position: sticky;
            //  left: 0;
            //  bottom: -2px;
            //  height: 70px;
            //  width: 100%;
            //  background: linear-gradient(180deg, rgba(240, 245, 255, 0.1) 0%, rgba(240, 245, 255, 0.9) 100%);
            //  pointer-events: none;
            //}
            .workcs-banner-cs-box{
                height:250px;
                overflow: auto;
                margin-top: 5px;
                width: 100%;
            }

            .workcs-banner-cs-indiList{
                width: 100%;
                display: flex;
                //justify-content: space-between;
                flex-wrap: wrap;
            }

            .workcs-banner-cs{
                margin-bottom: 16px;
                text-align: center;
                position: relative;
                width: calc((100% - 4px)/3);
                &:nth-child(3n+1), &:nth-child(3n+2) {
                    &:after {
                        position: absolute;
                        content: '';
                        width: 1px;
                        height: 34px;
                        background-color: #dce0e7;
                        right: 0;
                        top: 50%;
                        transform: translateY(-50%);
                    }
                }
                .workcs-banner-cs-name{
                    font-size: 13px;
                    color: #6F7D99;
                    .workcs-banner-cs-unit1{
                        font-weight: 400;
                        font-size: 9px;
                        color: #1B1B1B;
                        display: inline-block;
                    }
                }
                .workcs-banner-cs-num{
                    margin-top: 6px;
                    font-weight: bold;
                    font-size: 20px;
                    color: #282828;
                    .workcs-banner-cs-unit{
                        font-weight: 400;
                        font-size: 9px;
                        color: #1B1B1B;
                        display: inline-block;
                    }
                }
                .workcs-banner-cs-Finsh{
                    font-size: 12px;
                    margin-top: 4px;
                    .benyue{
                        color: #ccc;
                        font-size: 12px;
                    }
                    .workcs-banner-Finsh-zi{
                        margin-left: 4px;
                        color: #3DC66F;
                        font-size: 12px;
                        &.fail{
                            color: #FF7A93;
                        }
                    }
                }

                .workcs-banner-cs-Finsh1{
                    font-size: 12px;
                    margin-top: 4px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    .workcs-banner-Finsh-biao{
                        font-size: 6px;
                        background: #0883FF;
                        padding: 3px;
                        color: #FFFFFF;
                    }
                    .workcs-banner-Finsh-zi1{
                        font-size: 12px;
                        color: #282828;
                        margin-left: 3px;
                    }
                    .workcs-banner-Finsh-yue{
                        font-size: 6px;
                        background: #2AA994;
                        padding: 3px;
                        color: #FFFFFF;
                        margin-left: 3px;
                    }

                }
            }
        }
        .workcs-banner-list2 {
            height: 170px;
            .compete-bottom {
                display: inline-flex;
                align-items: center;
                margin-top: 15px;
                .compete-bottom-left {
                    width: 40%;
                    text-align: center;
                    .left-btu {
                        margin: 20px 0 20px 16px;
                        padding: 8px 12px;
                        color: #007AFF;
                        white-space: nowrap;
                        font-size: 16px;
                        border: 1px solid #007AFF;
                        border-radius: 8px;
                        background: none;
                        .lujing2 {
                            font-size: 18px;
                            color: #007AFF;
                            margin-right: 4px;
                        }
                    }
                }
                .compete-bottom-img {
                    width: 60%;
                    margin-left: 20px;
                }
            }
            .compete-bottom2 {
                display: inline-flex;
                align-items: center;
                margin-top: 15px;
                .compete-bottom-left {
                    width: 40%;
                    text-align: center;
                    .left-btu {
                        margin: 20px 0 20px 16px;
                        padding: 8px 12px;
                        color: #837f7f;
                        white-space: nowrap;
                        font-size: 16px;
                        border: 1px solid #837f7f;
                        border-radius: 8px;
                        background: none;
                        .lujing2 {
                            font-size: 18px;
                            color: #837f7f;
                            margin-right: 4px;
                        }
                    }
                }
                .compete-bottom-img {
                    width: 60%;
                    margin-left: 20px;
                }
            }

        }
        .workcs-banner-list3 {
            height: 250px;
            .compete-bottom {
                display: inline-flex;
                align-items: center;
                margin-top: 15px;
                .compete-bottom-left {
                    width: 40%;
                    text-align: center;
                    .left-btu {
                        margin: 20px 0 20px 16px;
                        padding: 8px 12px;
                        color: #007AFF;
                        white-space: nowrap;
                        font-size: 16px;
                        border: 1px solid #007AFF;
                        border-radius: 8px;
                        background: none;
                        .lujing2 {
                            font-size: 18px;
                            color: #007AFF;
                            margin-right: 4px;
                        }
                        .jiantou-copy-copy{
                            font-size: 16px;
                            color: #007AFF;
                            //margin-left: 4px;
                        }
                    }
                }
                .compete-bottom-img {
                    width: 60%;
                    margin-left: 20px;
                }
            }
            .compete-bottom2 {
                display: inline-flex;
                align-items: center;
                margin-top: 15px;
                .compete-bottom-left {
                    width: 40%;
                    text-align: center;
                    .left-btu {
                        margin: 20px 0 20px 16px;
                        padding: 8px 12px;
                        color: #837f7f;
                        white-space: nowrap;
                        font-size: 16px;
                        border: 1px solid #837f7f;
                        border-radius: 8px;
                        background: none;
                        .lujing2 {
                            font-size: 18px;
                            color: #837f7f;
                            margin-right: 4px;
                        }
                    }
                }
                .compete-bottom-img {
                    width: 60%;
                    margin-left: 20px;
                }
            }

        }
        .compete-banner-head-left {
            position: absolute;
            left: 0px;
            top: 0px;
            padding: 6px 14px 6px 6px;
            background-color: #ffffff;
            border-radius:0px 0 10px 0;
            font-weight: 500;
            display: flex;
            max-width: 50%;

            align-items: center;
            .cengji {
                color: #007AFF;
                font-size: 14px;
                margin-right: 4px;
            }
            .name {
                color: #2F2F2F;
                font-size: 14px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap
            }
        }
        .compete-banner-head-right {
            right: 2px;
            text-align: center;
            position: absolute;
            top: 2px;
            display: flex;
            align-items: center;
            justify-content: end;
            .label {
                display: flex;
                align-items: center;
                justify-content: end;
                color: #007AFF;
                white-space: nowrap;
                font-size: 14px;
                .xingzhuanggangwei {
                    color: #007AFF;
                    font-size: 14px;
                    margin-right: 4px;
                }
            }
            .more-btu {
                padding: 6px 12px 6px 12px;
                background-color: #ffffff;
                border-radius: 0 10px 0 10px;
                font-weight: 500;
                color: #007AFF;
                font-size: 14px;


                white-space: nowrap;
            }
        }
        .compete-banner-list {
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            background: #F0F5FF;
            border-radius: 10px;
            padding: 40px 0 0 0;
            margin: 0px 16px 0 16px;
            justify-content: space-between;
            position: relative;
            .compete-banner-content-box {
                overflow: hidden;
                margin-top: 5px;
                width: 100%;
                .content-ul {
                    width: 100%;
                    display: flex;
                    flex-wrap: wrap;
                    .content-li {
                        margin-bottom: 16px;
                        text-align: center;
                        position: relative;
                        width: calc((100% - 4px)/3);
                        .compete-name {
                            font-size: 14px;
                            font-weight: normal;
                            color: #3A3A3A;
                            line-height: 22px;
                            white-space: nowrap;
                            text-align: center;
                            &.sub-option {
                                padding-left: 14px;
                                display: flex;
                                align-items: center;
                                overflow: hidden;
                            }
                            .sub-compete-icon {
                                font-size: 14px;
                                color: #007AFF;
                            }
                            .sub-compete-name {
                                font-size: 14px;
                                color: #202020;
                                font-weight: normal;
                                line-height: 18px;
                            }
                        }
                        .compete-value {
                            font-weight: 600;
                            font-size: 24px;
                            color: #F18622;
                            line-height: 31px;
                            white-space: nowrap;
                            text-align: center;
                            &.sub-option {
                                padding-left: 14px;
                                display: flex;
                                align-items: center;
                                //overflow: hidden;
                            }
                            .sub-compete-value {
                                font-size: 24px;
                                color: #007AFF;
                            }
                            .sub-compete-unit {
                                font-size: 14px;
                                color: #007AFF;
                                margin-left: -4px;
                            }
                        }
                    }
                    .container {
                        position: relative;
                        height: 24px;
                        margin-top: 14px;
                    }
                    .container::before {
                        content: ''; /* 伪元素需要内容属性，即使它是空的 */
                        position: absolute; /* 绝对定位 */
                        top: 0; /* 竖线的顶部位置 */
                        left: 50%; /* 从容器的左侧偏移50%，但还需要进一步调整 */
                        transform: translateX(-50%); /* 向左偏移自身宽度的一半，确保竖线居中 */
                        height: 100%; /* 竖线的高度与容器相同 */
                        width: 1px; /* 竖线的宽度 */
                        background-color: #CDCCCC; /* 竖线的颜色 */
                    }
                }
                .compete-bottom {
                    display: flex;
                    align-items: center;
                    margin-top: -30px;
                    .compete-bottom-left {
                        width: 40%;
                        text-align: center;
                        .left-btu {
                            margin: 20px 0 20px 16px;
                            padding: 8px 12px;
                            color: #007AFF;
                            white-space: nowrap;
                            font-size: 16px;
                            border: 1px solid #007AFF;
                            border-radius: 8px;
                            background: none;
                            .lujing2 {
                                font-size: 18px;
                                color: #007AFF;
                                margin-right: 4px;
                            }
                        }
                    }
                    .compete-bottom-img {
                        width: 60%;
                        margin-left: 20px;
                    }
                }
            }
        }
    }
}

.statistics-box {
    background: linear-gradient(180deg, #FFFFFF 0%, #E1E9FF 100%);
    border-radius: 0 20px 20px 20px;
    margin: 0 17px 11px 16px;
    z-index: 1;
    position: relative;
    .statistics-dangyue{
        text-align: center;
        color: #305fe2;
        font-size: 10px;
        position: absolute;
        right: 0;
        padding: 6px 10px;
        background-color: #dceaff;
        border-radius: 0 20px 0 20px;
    }

    .statistics {
        width: 100%;
        height: 200px;
        display: flex;
        flex-wrap: wrap;
        padding: 0 10px 5px;
        box-sizing: border-box;

        .statistics-item {
            width: 33.333%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: relative;
            margin: 11px 0 0 0;

            &:nth-child(1), &:nth-child(2), &:nth-child(4), &:nth-child(5), &:nth-child(7) {
                &:after {
                    position: absolute;
                    content: '';
                    width: 1px;
                    height: 24px;
                    background-color: #E6E8EC;
                    right: 0;
                    top: 50%;
                    transform: translateY(-50%);
                }
            }

            > div {
                font-weight: 400;
                font-size: 13px;
                color: #323232;
                line-height: 18px;
                //letter-spacing: 1px;

                &.active {
                    height: 21px;
                    background: #335FDA;
                    border-radius: 242px 242px 242px 242px;
                    line-height: 21px;
                    text-align: center;
                    font-size: 12px;
                    color: #FFFFFF;
                    width: 75px;
                }
            }

            p {
                font-weight: 400;
                font-size: 22px;
                color: #282828;
                line-height: 31px;

                span {
                    font-size: 12px;
                    color: #1B1B1B;
                    display: inline-block;
                    margin-left: 2px;
                }

                &.active {
                    color: #335FDA;

                    span {
                        color: #335FDA;
                    }

                }

            }
        }
    }
}

.workStage-content{
    padding: 0 20px 20px;
    box-sizing: border-box;
    background: linear-gradient( 180deg, #FDFDFD 0%, #ECF0FA 60%);
    .login-AI {
        display: flex;
        .login {
            width: 66%;
            height: 41px;
            background: #E4EBFF;
            border-radius: 14px 14px 14px 14px;
            display: flex;
            align-items: center;
            padding: 0 12px 0 16px;
            box-sizing: border-box;
            font-size: 14px;
            color: #717171;

            input {
                border: none;
                outline: none;
                width: 0;
                flex: 1;
                background: #E4EBFF;
            }

            .log-in {
                width: 53px;
                height: 26px;
                background: linear-gradient(180deg, #3E70FE 0%, #5982FE 100%);
                border-radius: 12px 12px 12px 12px;
                font-size: 14px;
                color: #FFFFFF;
                line-height: 26px;
                text-align: center;

            }
        }
        .ai-assistant {
            justify-content: center;
            align-items: center;
            display: flex;
            width: 30%;
            height: 40px;
            margin-left:15px;
            background: linear-gradient(180deg, #3E70FE 0%, #5982FE 100%);
            border-radius: 14px 14px 14px 14px;

            .ai-img {
                width: 17px;
                height: 15px;
                margin-right: 3px;
            }

            .ai-title {
                font-weight: 400;
                font-size: 13px;
                color: #FFFFFF;
                line-height: 18px;
                text-align: center;
                font-style: normal;
                text-transform: none;
            }
        }
    }
    //.login {
    //  margin-top: 9px;
    //  width: 100%;
    //  height: 41px;
    //  background: #E4EBFF;
    //  border-radius: 14px 14px 14px 14px;
    //  display: flex;
    //  align-items: center;
    //  padding: 0 12px 0 16px;
    //  box-sizing: border-box;
    //  font-size: 14px;
    //  color: #717171;
    //
    //  input {
    //    border: none;
    //    outline: none;
    //    width: 0;
    //    flex: 1;
    //    background: #E4EBFF;
    //  }
    //
    //  .log-in {
    //    width: 53px;
    //    height: 26px;
    //    background: linear-gradient(180deg, #3E70FE 0%, #5982FE 100%);
    //    border-radius: 12px 12px 12px 12px;
    //    font-size: 14px;
    //    color: #FFFFFF;
    //    line-height: 26px;
    //    text-align: center;
    //
    //  }
    //}
    .cs-usufun {
        height: auto;
        margin-top: 0;
        .Common-functional-modules.change-height {
            height: auto !important;
            min-height: 90px;
            border-radius: 8px;
            background: none;

            .empty-menu-p {
                height: 84px;
                line-height: 30px;
                color: #8FA7C1;
                font-size: 12px;
                border: 1px solid #ccc;
                padding: 24px;
                text-align: center;
                border-radius: 8px;
            }
        }
    }

    .title {
        display: flex;
        align-items: center;
        margin-top: 17px;
        margin-bottom: 8px;
        height: 24px;
        p {
            font-size: 17px;
            color: #181818;
            padding-left: 14px;
            flex: 1;
            line-height: 24px;
            position: relative;
            &::before {
                position: absolute;
                content: '';
                left: 0;
                top: 50%;
                transform: translateY(-50%);
                width: 8px;
                height: 16px;
                background: #4B74FF;
                border-radius: 110px 110px 110px 110px;
            }
        }

        > div {
            font-size: 14px;
            color: #646464;
            position: relative;
            padding-right: 5px;
            display: flex;
            align-items: center;


            &::after {
                position: absolute;
                right: -6px;
                font-family: 'iconfont' !important;
                content: '\e658';
                font-size: 10px;
                font-style: normal;
                bottom: 50%;
                transform: translateY(50%);
            }
        }
    }

    .service-content {
        //padding: 0 20px 20px;
        box-sizing: border-box;
        .recommendation-img{
            height: 200px;
            border-radius: 8px;
            background-color: #fff;
            margin: 8px 16px;
            color:#737373;
            font-size:14px;
            text-align:center;
            display:flex;
            flex-direction:column;
            justify-content:center;
            align-items:center;
            img{
                width:50vw;
            }
        }

        .recommendation-list {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;


            li {
                width: 48%;
                height: 80px;
                background: #FFFFFF;
                box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.05);
                border-radius: 14px 14px 14px 14px;
                padding: 0 14px 0 15px;
                display: flex;
                align-items: center;
                box-sizing: border-box;
                margin-bottom: 11px;
                position: relative;
                &.disabled {
                    opacity: 0.4;
                }

                .recommendation {
                    position: absolute;
                    right: 0;
                    top: 0;
                    width: 45px;
                    height: 19px;
                    background: #FC7F04;
                    border-radius: 0 12px 0 12px;
                    font-size: 13px;
                    color: #FFFFFF;
                    line-height: 17px;
                }

                img {
                    width: 26px;
                    margin-right: 7px;
                }

                /*&:nth-child(1) img {
              width: 38px;
            }

            &:nth-child(3) img {
              width: 35px;
            }*/

                span {
                    flex: 1;
                    font-size: 14px;
                    color: #323232;
                    line-height: 19px;
                    white-space: nowrap;
                }
                .task-num{
                    font-size: 24px;
                    color: #323232;
                    margin-top: 4px;
                    white-space: nowrap;
                    .task-num-fenge{
                        color: #D8D8D8;
                        font-size: 18px;
                    }
                }

                /*> div {
              position: relative;
              width: 16px;
              height: 16px;
              background: #E0E8FF;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;

              em {
                color: #5882F5;
                font-size: 16px;
              }

            }*/
                .task-content {
                    margin: 0 10px;
                    width: 100%;
                    .task-title {
                        font-size: 14px;
                        color: #323232;
                        line-height: 18px;
                        white-space: nowrap;
                        //font-weight: bold;
                    }
                    .task-value {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        .value-option {
                            margin-top: 6px;
                            .value {
                                font-size: 20px;
                                color: #323232;
                                font-weight: bold;
                            }
                            .value-unit {
                                font-size: 10px;
                                color: #636363;
                                white-space: nowrap;
                            }
                        }
                    }
                }
                em {
                    color: #5882F5;
                    font-size: 16px;
                    background-color: #dbe4ff;
                    border-radius: 8px;
                }
            }
        }
    }
    .peixun-content{
        background: #FFFFFF;
        box-shadow: 0 2px 6px 0 rgba(0,0,0,0.05);
        border-radius: 14px 14px 14px 14px;
        padding: 18px 6px 12px 6px;
        align-items: center;
        display: flex;
        flex-direction: row;
        justify-content: space-around;
        img{

        }
        .peixun-shichang{
            display: flex;
            flex-direction: column;
            text-align: center;
            .peixun-shichang1{
                font-weight: 400;
                font-size: 13px;
                color: #323232;
                line-height: 19px;
            }
            .peixun-shichang2{
                font-weight: 400;
                font-size: 21px;
                color: #323232;
                line-height: 29px;
            }
            .peixun-shichang3{
                font-weight: 400;
                font-size: 10px;
                color: #9E9E9E;
                line-height: 14px;
            }
        }
        .peixun-shuxian{
            width: 2px;
            height: 37px;
            background-color: #ECECEC;
            margin-left: 12px;
            margin-right: 12px;
        }
        .peixun-xiaozi{
            display: flex;
            flex-direction: column;
            text-align: center;
            .peixun-xiaozi1{
                font-weight: 400;
                font-size: 11px;
                color: #151515;
                line-height: 16px;
            }
            .peixun-xiaozi2{
                font-weight: 400;
                font-size: 16px;
                color: #151515;
                line-height: 22px;
            }
            .peixun-xiaozi3{
                font-weight: 400;
                font-size: 10px;
                color: #9E9E9E;
                line-height: 14px;
            }
        }

    }
}
.workStage-banner-touming{
    position: sticky;
    left: 0;
    bottom: -1px;
    height: 30px;
    width: 100%;
    background: linear-gradient(180deg, rgba(240, 245, 255, 0.1) 0%, rgba(236, 240, 250, 1) 60%);
    pointer-events: none;
    margin-bottom: -8px;
    margin-top: -4px
}
/deep/ .mint-popup-bottom {
    position: fixed;
    top: 20%;
    overflow: auto;
    bottom: 0;
    transform: translate3d(-50%, 0, 0);
    width: 100%;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}
.select-tree-box {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    .select-top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px;
        font-size: 18px;
        font-weight: bold;
        .guanbi1 {
            font-size: 24px;
            color: #666666;
        }
        .cancel {
            font-size: 16px;
            font-weight: bold;
            color: #777777;
        }
        .confirm {
            font-size: 18px;
            font-weight: bold;
            color: #007AFE;
        }
    }
    .content-space {
        overflow: auto;
        .select-content {
            padding: 16px;
            .content-option {
                display: flex;
                align-items: end;
                justify-content: space-between;
                .option-left {
                    display: flex;
                    align-items: end;
                    .left-line {
                        margin-right: 16px;
                        .line {
                            height: 36px;
                            margin-left: 2.7px;
                            border-left: 2px solid #007aff;
                        }
                        .icon {
                            width: 6px;
                            height: 6px;
                            border-radius: 4px;
                            border: 1px solid #007aff;
                            &.background {
                                background-color: #007aff;
                            }
                        }
                    }
                    .option-name {
                        font-size: 16px;
                        &.select-level {
                            color: #007aff;
                        }
                    }
                }
                .youjiantou1 {
                    font-size: 16px;
                    padding-bottom: 2px;
                }
            }
        }
        .option-list-content {
            padding: 12px 12px;
            border-top: 1px solid #F5F5F5;
            display: flex;
            flex-wrap: wrap;
            .option-list-title {
                width: 100%;
                font-size: 16px;
                font-weight: bold;
                color: #1c1c1c;
                margin-bottom: 12px;
            }
            .option-item {
                padding: 4px 10px;
                border-radius: 12px;
                background-color: #F5F5F5;
                color: #989898;
                white-space: nowrap;
                font-size: 14px;
                margin: 6px 12px 6px 0;
                &.select-option {
                    background-color: #e2e8fa;
                    color: #8ba2f3;
                }
            }
        }
    }
}
.hide-box {
    position: fixed;
    left: 0;
    top: 0;
    width: 100vw;
    height: 100vh;
    z-index: 88888;
    background: rgba(0,0,0,0.6);
    display: none;

}
.searchBTn {
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 999999;
    width: 100%;
    text-align: center;
    height: 76px;
    background: #fff;

    img {
        display: inline-block;
        position: absolute;
        left: 50%;
        top: -66px;

        width: 110px;
        margin: 0 auto;
        -webkit-transform: translateX(-50%);
        transform: translateX(-50%);
    }
}
.loading-chat {
    position: fixed;
    bottom: 150px;
    left: 50%;
    transform: translateX(-50%);
    width: 70vw;
    padding: 54px 20px;
    box-sizing: border-box;
    background: #f5f6f6;
    border-radius: 8px;
    text-align: center;
    z-index: 999999;
    display: none;
    img {
        width: 85%;
        margin-bottom: 30px;
    }
    div {
        font-size: 12px;
        color: #ccc;
    }

}
.searchBTn2 {
    position: fixed;
    top: 50px;
    left: 0;
    z-index: 999999;
    width: 100%;
    text-align: center;
    height: 60px;

    img {
        display: inline-block;
        position: absolute;
        left: 50%;
        top: 36px;
        width: 110px;
        margin: 0 auto;
        transform: translateX(-50%);
    }
}
.tab-head {

    border-radius: 6px;
    color: #000;
    font-size: 14px;
    padding-left: 16px;
    font-size: 0;
    border-radius: 10px 10px 0 0;
    margin-bottom: -1px;
    .tabs {
        display: inline-block;
        background: #dce3fe;
        line-height: 32px;
        font-size: 14px;
        width: 50px;
        text-align: center;
    }
    .tabs0 {
        border-radius: 0px 0 0 0 ;
    }
    .tabs1 {
        border-radius: 10px 0 0 0 ;
    }
    .tabs2 {
        border-radius: 0 10px 0 0 ;
    }
    .active {
        background: #fefdfd;
    }
}

.out-switch-page{
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    bottom: 59px;
    z-index: 999;
    background: #fff;
    display: flex;
    flex-direction: column;
}
.workcs-banner-cs-num2 {
    font-size: 12px;
    color: #72b707;
    .iconfont {
        font-size: 12px;
        margin-right: -5px;
    }
}
.workcs-banner-cs-num2.red {
    font-size: 12px;
    color: red;
}
.workcs-banner-cs-orange {
    .workcs-banner-cs-name,.workcs-banner-cs-num {
        color: #ff8503 !important;
    }
}
</style>
