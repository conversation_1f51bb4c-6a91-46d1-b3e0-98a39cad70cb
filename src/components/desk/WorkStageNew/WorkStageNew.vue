<template>
  <div class="work-stage">
    <div class="overlay" v-show="showDropDown && groupList && groupList.length > 0" @click="closeGroupList"></div>
    <div class="remind" v-show="isShowTip">
      <em class="iconfont ico-notice"></em>
      <span @click="goNoticeDetail"><LatestNews :tsNoticeInfo="noticeInfo"></LatestNews></span>
      <em class="iconfont jiahao2 ratote" @click="deleteTip"></em>
    </div>
    <img v-show='isShowPoint' src='@/assets/img/point-guide.png' alt='' class='point-to'>
    <!-- 搜索框-->
    <div class="search" :class="{ 'tip-space': !isShowTip }"  @click="focus">
      <img class='scan' src='static/img/zqWorkStage/scan-pic.png' @click.stop='openScan' />
      <span class='split-span'>|</span>
      <span class='input-span'>搜索业务菜单、管辖主体等</span>
      <div class='ai-tools'>
        <span class='iconfont paizhao' @click.stop='openOcr' />
        <div class='search-button' @click="focus()">搜索</div>
      </div>
    </div>
    <div class="content-box-work">
      <!--热点-->
      <div class="hot">
        <ul>
          <li v-for="(item,index) in todoTaskList" :key="index" @click="goOtherTask(item)">
            <p>{{ item.num }}</p>
            <span>{{ item.showWorkTypeName }}</span>
          </li>
        </ul>
        <div class="hot-more" @click="goOtherTask()">更多<em class="iconfont youjiantou2"></em></div>
      </div>
      <!--视图-->
      <div class="views">
        <div class="view-type">
          <p :class="viewsChoose == 0 ? 'active' : ''" @click="chooseW(0)">客户视图</p>
          <p :class="{ active: viewsChoose == 1 }" @click="chooseW(1)">集客视图</p>
        </div>
        <!--客户视图登录-->
        <div class="connection" v-show="viewsChoose == 0">
          <div class="phone-auth">
            <div class="ph-num">
              <em class="iconfont shouji"></em>
              <input type="tel" maxlength="11" placeholder="请输入11位手机号码" v-model="telnum" />
            </div>
            <div class="ph-send" @click="sendValiCodeCheck">{{ noSendFlag ? sCountDown + 's' : '发送验证码' }}</div>
          </div>
          <div class="ph-num">
            <em class="iconfont xin-xianxing"></em>
            <input type="number" placeholder="请输入验证码" v-model="valCode" />
          </div>
          <div class="more-other">
            <div class="more-box">
              <div class="more-type">
                <p @click="csViewLogin">更多方式<em class="iconfont youjiantou2"></em></p>
              </div>
            </div>
            <div class="login-box">
              <div class="login" @click="jqSure">登录</div>
            </div>
          </div>
        </div>
        <!-- 政企视图登录  tabindex="-1" @blur="closeGroupList" -->
        <div class="business-view" v-show="viewsChoose == 1" >
          <div class="phone-auth" >
            <div ref="refer" class="ph-num" :class="{ 'dropDwon-keep': showDropDown }">
              <em class="iconfont suoshujituan"></em>
              <input type="text" placeholder="请输入集团名称" v-model="groupName" />
            </div>
            <div class="ph-send" @click="searchgGroupCust">搜索</div>
          </div>
          <div class="record">
            <em class="iconfont shijian2"></em>
            <p v-for="(item, index) in historyGroup" @click="goGroupDetail(item)" :key="index">{{item.groupName}}</p>
          </div>
          <dropDown
          v-show="showDropDown && groupList && groupList.length > 0"
          :groupList="groupList"
          :pageNum="pageNum"
          :rootLevel='1'
          @getGroupList="getGroupList"
          v-bind:style="{ width: dropDownWidth }"></dropDown>
        </div>
      </div>
      <div ref="menuRef">
      <!-- 常用功能 -->
      <div  v-for="(item, index) in menuList" :key="index">
        <div class="common-functions" v-if="item.picId == 'type1'">
          <ul>
            <li class='govern-versus' v-for="(subItem, index) in item.itemList" :key="index" @click="skip(subItem)">
              <img src="static/workStageTool/100374.png" v-real-img="'static/workStageTool/' + subItem.picId + '.png'" alt="" />
              <p>{{ subItem.privName }}</p>
            </li>
          </ul>
        </div>
      </div>
      </div>
      <!--组件-->
      <div class="common-area">
        <common-use v-for="(item, index) in blendMenuList.slice(0, 2)" :key="index" :menu="item" :rootLevel='1'></common-use>
      </div>
      <!--融合受理-->
      <div class="fuse" ref="fuseDom">
        <div class="fuse-title">
          <p><span>融合受理</span></p>
        </div>
<!--        <div class="fuse-list">-->
<!--          <ul>-->
<!--            <li v-for="(item, index) in intePackages" :key="index" @click="goHandlePackage(item)">-->
<!--              <div class="li-label" :class="{ orange: index === 1 }">-->
<!--                <p>{{ item.attrValDesc }}</p>-->
<!--              </div>-->
<!--              <div class="li-content">{{ item.attrName }}</div>-->
<!--            </li>-->
<!--          </ul>-->
<!--          <div class="fuse-more" @click="goHandlePackage()">更多<em class="iconfont youjiantou2"></em></div>-->
<!--        </div>-->
        <div class="fuse-common">
          <common-use v-for="(item, index) in blendMenuList.slice(2, 4)" :key="index" :menu="item" :rootLevel='1'></common-use>
        </div>
      </div>
      <!-- 轮播图-->
<!--      <div class="wc-bannerblock" style="">-->
<!--        <div class="wc-banner">-->
<!--          <slide ref="slide" class="bs-slide" :autoPlay="true" :loop="true" :showDot="true" :dataList="intePackages" :interval="4000">-->
<!--            <div v-for="(item, index) in intePackages" :key="index">-->
<!--              <div class="baner-item-wrap" @click="goHandlePackage(item)">-->
<!--                <img src="static/img/workStage/banner-1.png" alt="" />-->
<!--                <span>{{ item.attrName }}</span>-->
<!--              </div>-->
<!--            </div>-->
<!--          </slide>-->
<!--        </div>-->
<!--      </div>-->
      <!--辅助工具-->
      <div class='tool-div'>
        <div class="tool" v-for="(item, index) in menuList" :key="index">
          <div v-if="item.picId == 'type3'">
            <div class="title">
              <p>{{ item.privName }}</p>
              <span @click="goTool">更多<em class="iconfont youjiantou2"></em></span>
            </div>
            <ul>
              <li class='govern-versus' v-for="(subItem, index) in item.itemList" :key="index" @click="skip(subItem)">
                <img src="static/workStageTool/100407.png" v-real-img="'./static/workStageTool/' + subItem.picId + '.png'" alt="" />
                <p>{{ subItem.privName }}</p>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <!--移动图标-->
    <div v-show="shopCarShow" ref="fixedSmartXin" class="fixed-button fixed-smart-xin" @touchmove="moveBtn($event, 'fixedSmartXin')" @click="goShoppingCart">
      <em class="iconfont gouwuche"></em>
    </div>
    <span v-show="false" class="location-text">{{getLocation}}</span>
    <!-- 零菜单-->
    <inlet-menu v-if='showOcr' :showPicture='true' srcFrom='workN'></inlet-menu>
  </div>
</template>
<script>
import commonUse from './CommonUse'
import ApplyDialog from './ApplyDialog'
import dropDown from './dropDown'
import Slide from 'components/common/Slide.vue'
import Authenct from 'components/common/uniteauth/index.js'
import Storage from '@/base/storage'
import { dateFormat, chgStrToDate } from '@/base/utils'
import NlApplyDialog from './dialog.js'
import { needNumMixin, smartMixin, tokenFromAldMixin, clientLocalMixin } from '@/base/mixin'
import LatestNews from 'components/common/LatestNews.vue'
import { skipCommom } from './commonBusiCk.js'
import { decrptParam } from '@/base/encrptH5.js'
import { menuChain } from '@/base/mixins/menuChainMixin'
import ClientJs from '@/base/clientjs'
import InletMenu from 'components/business/ZeroMenu/InletMenu.vue'
import {h5getMenuByLevel} from '@/base/request/commonReq.js'

export default {
  components: { commonUse, ApplyDialog, Slide, Authenct, LatestNews, dropDown, InletMenu},
  mixins: [needNumMixin, smartMixin, tokenFromAldMixin, skipCommom,clientLocalMixin,menuChain],
  data() {
    return {
      viewsChoose: 0,
      judge: false, //控制图标弹窗
      isShowTip: false, //控制提示是否展示
      windowWidth: '',
      windowHeigth: '',
      bannerData: [
        { href: 'javascript:;', src: 'static/img/workStage/banner-1.png' },
        { href: 'javascript:;', src: 'static/img/workStage/banner-1.png' },
      ],
      uinfo: {}, //操作员信息
      menuList: [], //菜单列表
      blendMenuList: [], //当前二级菜单下的三级菜单
      todoTaskList: [], //未完成待办
      queryDay: '', //当天日期
      srcFrom: 'workStageNew',
      noticeInfo: [],
      shopCarShow: false, //购物车隐藏
      intePackages: [],
      canClickFlg: true, //是否能点击
      telnum: '',
      valCode: '', //验证码
      telnumRule: {
        rule: /^((1)+\d{10})$/,
        msg: '请输入正确的手机号',
      },
      codeRule: {
        rule: /\S/, //验证码验证规则
        msg: '请输入正确的验证码',
      },
      currentItem: {
        authDesc: '短信验证码',
        authId: 'JQR',
        authNum: '2',
        authType: '07',
        crmAuthType: 'AuthCheckR',
        sort: '2',
      },
      showDropDown: false,
      dropDownWidth: 250,
      groupName: '',
      groupList: [],
      noSendFlag: false, //是否能发送验证码
      sCountDown: 30, //重新发送倒计时时间（默认30s）
      historyGroup:[],//常用集团
      pageNum:1,
      deskSwitch:false,
      showOcr:false,
      isShowPoint:false,
    }
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      let l = Storage.get('location');
      if (l) {
        vm.$store.commit('SET_LOCAL', Storage.get('location'));
      }
    });
  },
  computed: {
    //获取vuex中存的定位信息
    getLocation() {
    let self = this;
    let loc = this.$store.getters.locationTxt;
    if (!loc && loc != 'empty') {//初始化进来的时候  取不到定位信息
      setTimeout(() => {
        if(!Storage.get('location') || Storage.get('location')=='empty'){//没有获取到定位
          this.getLocationFromAldParent();//从服务端获取
        } else {
          self.$store.commit('SET_LOCAL', Storage.get('location'));
        }
      }, 1000)
    }
    if(loc == 'empty'){
      return '请点击刷新'
    } else {
      return loc;
    }
    },
  },
  methods: {
    chooseW(num) {
      this.viewsChoose = num
      if(this.viewsChoose == 1 && this.historyGroup.length < 1){
        this.getGroupList()
      }
    },
    openOcr() {
      let param = {
        'area': 'workN',
        'privId': 'z-0',
        'stationId': this.uinfo.stationId,
        unLoadFlg: true //屏蔽加载圈
      }
      this.$http.post('/xsb/api-user/commonFunc/h5commCollect', param)
      if (!this.uinfo.crmId) {
        this.$toast('当前岗位CRM工号为空，请联系地市管理员')
        return
      }
      this.showOcr = !this.showOcr
    },
    openScan(){
      ClientJs.scanQRcode('scanCallBack')
    },
    searchgGroupCust() {

      if (!this.groupName) {
        return
      }
      this.groupList = [];
      this.pageNum = 1;
      this.getGroupList()
      this.showDropDown = true
      let _this = this
      this.$nextTick(function () {
        _this.dropDownWidth = _this.$refs.refer.style.width
      })
    },
    // 搜索
    focus() {
      // console.log('获取焦点了')
      // this.$router.push({
      //   path: '/searchHistory',
      //   query: {
      //     menuList: this.menuList,
      //     srcFrom: 'WorkStageNew'
      //   }

      // })
      this.openView()
    },
    deleteTip() {
      this.isShowTip = false
      Storage.session.set('noShowNotice', true)
    },
    goOtherTask(item) {
      let param = {}
      if (item) {
        param = {
          state: 2,
          isMore: false,
          workTypeName: item.workTypeName,
          workTypeCode: item.workTypeCode,
          srcFrom: this.srcFrom,
        }
      } else {
        param = {
          state: 2,
          isMore: true,
          srcFrom: this.srcFrom,
        }
      }
      this.$router.push({ path: '/myTaskList', query: param })
    },
    goGroupDetail(item){
        let self = this

        // 记录菜单链
      // this.updateMenuChain('ji-tuan-shi-tu', 2);
      this.updateMenuChain(this.CONSTVAL.MENU_CHAIN.GROUP_VIEW,2);
      let workId = '';
      let url = `/xsb/personBusiness/villageMarket/h5qryWorkIdForSearchJt?villageId=${item.groupId}`;
      this.$http.get(url).then((res) => {
        if(res.data.retCode == '0'){
          workId = res.data.data;
        }

          let groupSwitchParam = {
              switchType: 'archive_group_flag'
          }

          self.$http.post('/xsb/personBusiness/gridSand/h5GridMarketSwitch', groupSwitchParam).then((res2) => {
              if (res2.data.retCode == 0) {
                  let subWgtUrl = '/subWgt?path=archiveGroup&groupId=' + item.custId + '&workId=' + workId + '&gobackFlag=ald&workType=1';
                  self.$router.push(subWgtUrl)
              } else {
                  self.$router.push({
                      path: `/groupDetailNew`,
                      query: {
                          workIdFlag: true,
                          taskId: workId,
                          flag: '0',
                          groupId: item.custId,
                          whereFrom: 'workStageNew',
                          // 菜单链默认父节点级别
                          dataRootLevel: 2
                      }
                  });
              }
          })



      }).catch((response) => {
      });
    },
    //获取政企融合视图菜单列表
    async getMenuList() {
      let data = await h5getMenuByLevel(this.uinfo.stationId, `8:0`);
      if (data.retCode == '0') {
        this.menuList = data.data;
        this.menuList.forEach((item) => {
          if (item.picId == 'type2' || item.picId == 'type3') {
            this.blendMenuList.push(item)
          }
        })
        this.$nextTick(() => {
          if(this.$refs.menuRef.offsetHeight == '51') {
            this.$refs.fixedSmartXin.style.top = this.getMonitorWallTop('fuseDom') + 30 + 'px'
          }else{
            this.$refs.fixedSmartXin.style.top = this.getMonitorWallTop('fuseDom') - 10 + 'px'
          }
          console.log(this.getMonitorWallTop('fuseDom'))
          this.shopCarShow = true
        })
      } else {
        this.shopCarShow = true
        this.menuList = []
        this.blendMenuList = []
        this.$alert(data.retMsg || '获取菜单错误，请确认岗位')
      }
      // let url = `/xsb/api-user/menu/h5getMenuByLevel?stationId=${this.uinfo.stationId}&level=8:0`
      // this.$http
      //   .get(url)
      //   .then((res) => {
      //     let { retCode, retMsg, data } = res.data
      //     if (retCode == '0') {
      //       this.menuList = data
      //       this.menuList.forEach((item) => {
      //         if (item.picId == 'type2' || item.picId == 'type3') {
      //           this.blendMenuList.push(item)
      //         }
      //         // if (item.picId == 'type0') {
      //         //   this.showOcr = true
      //         // }
      //       })
      //       this.$nextTick(() => {
      //         if(this.$refs.menuRef.offsetHeight == '51') {
      //           this.$refs.fixedSmartXin.style.top = this.getMonitorWallTop('fuseDom') + 30 + 'px'
      //         }else{
      //            this.$refs.fixedSmartXin.style.top = this.getMonitorWallTop('fuseDom') - 10 + 'px'
      //         }
      //         console.log(this.getMonitorWallTop('fuseDom'))
      //         this.shopCarShow = true
      //       })
      //     } else {
      //       this.shopCarShow = true
      //       this.menuList = []
      //       this.blendMenuList = []
      //       this.$alert(retMsg || '获取菜单错误，请确认岗位')
      //     }
      //   })
      //   .catch((response) => {
      //     this.shopCarShow = true
      //   })
    },
    getTodoTaskList() {
      let url = `/xsb/api-user/desk/h5GetHotTaskCount?queryDay=${this.queryDay}`
      this.$http
        .get(url)
        .then((res) => {
          let { retCode, retMsg, data } = res.data
          if (retCode == '0') {
            this.todoTaskList = data
            this.todoTaskList.forEach((item) => {
              if(item.workTypeName.length > 4){
                 item.showWorkTypeName = item.workTypeName.replace('任务', '')
              }else{
                item.showWorkTypeName = item.workTypeName;
              }

            })
          } else {
            this.$alert(retMsg || '查询待办任务失败')
          }
        })
        .catch((response) => {
          this.$alert(response || '查询待办任务异常')
        })
    },
    skip(item) {
      // 记录菜单链
      this.updateMenuChain(item.privId, 2);
      this.goBusinessPage(item)
    },
    //公告查询
    qryNotice() {
      let _this = this
      let url = `/xsb/api-user/noticeInfo/h5getNoticeInfo?type=1&orgId=${this.uinfo.orgId}`
      this.$http.get(url, { unLoadFlg: true }).then((res) => {
        let { retCode, data } = res.data
        if (retCode == '0') {
          let flag = Storage.session.get('noShowNotice')
          if (data && data.length > 0 && !flag) {
            let noticeData = data ? data : []
            if (noticeData) {
              let nowDate = new Date()
              noticeData.forEach((item) => {
                let releaseDate = chgStrToDate(item.releaseTime, 'yyyy-MM-dd hh-mm-ss')
                let hours = parseInt(nowDate - releaseDate) / 1000 / 60 / 60
                if (hours <= 24) {
                  _this.noticeInfo.push(item)
                }
              })
            }
          }
        }
        if (this.noticeInfo && this.noticeInfo.length > 0) {
          this.isShowTip = true
        }
      })
    },
    goTool() {
      this.$router.push('/tools')
    },
    moveBtn(event, refdom) {
      event.preventDefault()
      let touch = event.targetTouches[0] || event.changedTouches[0]
      let left = touch.pageX
      let top = touch.pageY
      if (left < 0) {
        //移动图标不超出最左边
        left = 0
      } else if (left + 50 > this.windowWidth) {
        //50为移动图标的宽度
        left = this.windowWidth - 50
      }
      if (top < 0) {
        //移动图标不超出最上边
        top = 0
      } else if (top + 50 + 60 > this.windowHeigth) {
        //50为移动图标的高度
        top = this.windowHeigth - 50 - 60 //60为底部菜单的高度
      }
      let btnDom = this.$refs[refdom]
      btnDom.style.left = left + 'px'
      btnDom.style.top = top + 'px'
    },
    // 获取div到窗口x轴距离
    getMonitorWallTop(signBox) {
      let ele = this.$refs[signBox]
      let L = ele.offsetTop
      while (ele.offsetParent !== null) {
        ele = ele.offsetParent
        L += ele.offsetTop
        return L
      }
    },
    //查询产品属性字典
    qryAttrCfg() {
      let param = {
        productId:'999'
      }
      let url = `/xsb/personBusiness/cloudOrder/h5GetProductCfg`
      return this.$http
        .post(url,param)
        .then((res) => {
          let result = res.data
          if ('0' == result.retCode) {
            this.intePackages = result.data.attrCfgList
            if (this.intePackages && this.intePackages.length > 4) {
              this.intePackages.splice(4, this.intePackages.length - 4)
            }
          } else {
            this.$alert(res.retMsg || '查询产品属性字典失败')
          }
        })
        .catch((response) => {
          this.$alert(response || '查询产品属性字典异常')
        })
    },
    goHandlePackage(item) {
      this.$router.push({
        name: 'ValueNetcomList',
        query: {
          packageId: item ? item.attrId : '000',
          srcFrom: 'workStageNew',
        },
      })
    },
    goShoppingCart() {
      Authenct({
        popFlag: true,
        hasPwd: 0,
        idCardWay:  false
      }, obj => {
        this.$router.push({
          path: '/shoppingCartManage',
          query: {
            srcFrom: 'workStageNew',
          }
        });
      });
    },
    //客户视图登录按钮点击
    csViewLogin() {
      let idCardWayFlg = true //默认有身份证鉴权
      let callBackFn = (obj) => {
        if (obj.result == '1') {
          //  修改0329 增加弹窗-弹窗日志记录接口 手机号
          this.qryNewCustom(obj.telnum)
        }
      }
      //crm为空用
      if (Storage.session.get('userInfo').crmId) {
        let csviewJqFlg = Storage.session.get('csview_jq_flg')
        if (csviewJqFlg) {
          //使用新版本的
          this.showAuthent(idCardWayFlg, callBackFn, csviewJqFlg, Storage.session.get('csview_jq_data'))
        } else {
          //查询用新鉴权还是老版本鉴权框的开关
          this.$http.post('/xsb/ability/businessLimit/h5qryCsviewAuth').then((res) => {
            let { retCode, data } = res.data
            let jqDataArr = retCode == 0 && data && data.jqDateInfo //客户视图配置的鉴权类型
            let flg = 'oldJq' //默认旧的鉴权方式
            if (jqDataArr && jqDataArr.length > 0) {
              //用新版的鉴权窗
              flg = 'newJq'
              Storage.session.set('csview_jq_data', jqDataArr)
            }
            Storage.session.set('csview_jq_flg', flg)
            this.showAuthent(idCardWayFlg, callBackFn, flg, jqDataArr)
          })
        }
      } else {
        this.$alert('crm工号不能为空')
      }
    },
    showAuthent(idCardWayFlg, callBackFn, csviewJqFlg, jqDataArr) {
      Authenct({
        popFlag: true,
        jqTypeData:jqDataArr,
        idCardWay: idCardWayFlg,            //是否支持身份证鉴权方式
        telnum: this.telnum,
      },  callBackFn);
    },
    // 0320 弹窗接口
    qryNewCustom(telNum) {
      // 记录菜单链
      this.updateMenuChain('ke-hu-shi-tu', 2);
         //记录用户位置信息
        ClientJs.getLocation('','recordLoaction');

      Storage.session.set('workStageNew', true)
      this.$http
        .get(`/xsb/personBusiness/recordlogbusiaction/h5queryLabelByTel?telnum=${telNum}`)
        .then((res) => {
          let { retCode, retMsg, data } = res.data
          if (retCode == '0') {
            if (data != '' && data != null && data.versionList) {
              this.versionList = data.versionList
              //弹窗提示  type: 'confirm'
              if (this.versionList.s000000077351 != '非升级投诉用户') {
                this.$messagebox({
                  title: '温馨提示',
                  message:
                    '您好！请在业务服务过程中按照标准化服务要求认真执行。业务受理过程需经过客户密码确认，引导客户清晰理解业务协议内容，并提醒客户在签字板上进行必要、工整的签字。严格遵守营销准则，注意使用完整营销话术，避免出现过度营销行为。',
                  showCancelButton: false,
                  confirmButtonText: '确认',
                }).then((action) => {
                  this.alterLog(telNum)
                  this.$router.push({
                    name: 'CsViewIn',
                    query: {
                      telnum: telNum,
                      dataRootLevel: 2
                    },
                  })
                })
              } else {
                this.$router.push({
                  name: 'CsViewIn',
                  query: {
                    telnum: telNum,
                    dataRootLevel: 2
                  },
                })
              }
            } else {
              this.$router.push({
                name: 'CsViewIn',
                query: {
                  telnum: telNum,
                  dataRootLevel: 2
                },
              })
            }
          } else {
            //  this.$alert(retMsg || '弹窗标签查询接口返回失败');
            this.$router.push({
              name: 'CsViewIn',
              query: {
                telnum: telNum,
                dataRootLevel: 2
              },
            })
          }
        }).catch((res) => {
          this.$router.push({
            name: 'CsViewIn',
            query: {
              telnum: telNum,
              dataRootLevel: 2
            },
          })
        })
    },
      judeRecordLoaction(locationParam){
          let userInfo = Storage.session.get('userInfo');
          let param = {
              servNumber:userInfo.servNumber,
              type:"2",
              configTypeName :"ability-gps-config-",
          }
          this.$http.post("/xsb/ability/submenuAuthen/h5QryConfigAbility",param).then(res =>{
              if(res.data.retCode == '0'){
                  if(res.data.data == '1'){
                      console.info("ability-gps-config-"+userInfo.region,"有权限")
                      this.recordLoaction(locationParam);
                  }else {
                      console.info("ability-gps-config-"+userInfo.region,"没权限")
                  }
              }
          })
      },
      recordLoaction(locationParam){
        let userInfo = Storage.session.get('userInfo');
        let param = {
            phone:userInfo.servNumber,
            name:userInfo.operatorName,
            x : locationParam.longitude,
            y : locationParam.latitude,
            location : locationParam.address,
            type:"1",//客户端
            distance:"0",
            locationError :"0",
            timeError :"0",
        }
          this.$http.post("/xsb/personBusiness/attendanceCard/h5RecordLocation",param)
      },

    // 0320 弹窗日志记录
    alterLog(telNum) {
      let param = {
        telNum: telNum,
        beId: this.uinfo.region,
        menuId: '101',
        note: '客户视图',
        operType: 'SensitiveUserNotification',
      }
      //查询是否需要免填单
      this.$http.post('/xsb/personBusiness/recordlogbusiaction/h5popupLogRecord', param).then((res) => {
        if (res.data.retCode != '0') {
          //  this.$alert(res.data.retMsg);
        }
      })
    },
    //判断是否有短信验证码权限 qp
    sendValiCodeCheck(){
      let userInfo = Storage.session.get('userInfo');
      if (userInfo.crmId) {
        if (userInfo.authCheckGBlackFlag == undefined || userInfo.authCheckGBlackFlag == null) {
          let params = {};
          //服务密码白名单接口
          let url = '/xsb/ability/businessLimit/h5whitelistQuery';
          let authCheckBFlag = false;//服务密码白名单
          let authCheckGBlackFlag = true;//身份证鉴权黑名单
          let videoCollectBlackFlag = false;//视频采集黑名单
          let uniteAuthFlag = false;//统一鉴权框开关
          this.$http.post(url, params).then(res => {
            let {retCode, data} = res.data;
            if (retCode == '0') {
              authCheckBFlag = data.authCheckBFlag;
              authCheckGBlackFlag = data.authCheckGBlackFlag;
              videoCollectBlackFlag = data.videoCollectBlackFlag;
              uniteAuthFlag = data.uniteAuthFlag;
            }
          }).finally(()=>{
            userInfo.authCheckBFlag = authCheckBFlag;
            userInfo.authCheckGBlackFlag = authCheckGBlackFlag;
            userInfo.videoCollectBlackFlag = videoCollectBlackFlag;
            userInfo.uniteAuthFlag = uniteAuthFlag;
            Storage.session.set('userInfo', userInfo);
            if (userInfo.authCheckGBlackFlag) {
              //短信验证码鉴权
              this.sendValiCode()
            }else{
              this.$alert("当前工号（"+userInfo.crmId+"）为短信验证码黑名单，只能二代证鉴权，请点击更多方式切换");
            }
          });
        } else {
          if (userInfo.authCheckGBlackFlag) {
            //短信验证码鉴权
            this.sendValiCode()
          }else{
            this.$alert("当前工号（"+userInfo.crmId+"）为短信验证码黑名单，只能二代证鉴权，请点击更多方式切换");
          }
        }
      }else{
        this.$alert("当前工号（"+userInfo.crmId+"）为短信验证码黑名单，只能二代证鉴权，请点击更多方式切换");
      }

    },

    //发送验证码点击
    sendValiCode() {
      let telnum = this.telnum
      //判断有没有输入正确手机号
      if (!this.validateVal(telnum, this.telnumRule)) {
        return
      }
      //在倒计时内，不允许重复请求
      if (!this.noSendFlag) {
        let url = `/xsb/personBusiness/customerView/h5SendVerifyCode?telnum=${telnum}`
        this.$http
          .get(url)
          .then((res) => {
            console.info(res)
            if (res.data.retCode == '0') {
              this.$toast('短信发送成功，注意查收')
              this.noSendFlag = true
              this.timerValCode = setInterval(() => {
                this.sCountDown--
                if (this.sCountDown <= 0) {
                  this.noSendFlag = false
                  this.sCountDown = 30 //恢复初始时间
                  clearInterval(this.timerValCode)
                }
              }, 1000)
            } else {
              this.$toast({
                message: res.data.retMsg || '短信发送失败',
                position: 'bottom',
              })
            }
          })
          .catch((response) => {
            console.info(response)
          })
      }
    },
    //鉴权弹框确定按钮点击
    jqSure() {
      if (!this.canClickFlg) {
        return
      }
      let telnum = this.telnum
      //验证用户的手机号是否正确
      if (!this.validateVal(telnum, this.telnumRule)) {
        this.canClickFlg = true
        return false
      }

      this.canClickFlg = false
      //当前选中的tab
      let curAuthItem = this.currentItem
      let obj = {
        jqType: curAuthItem.authNum, //0:服务密码，1:验证码，2:身份证 3：融合鉴权 4.刷脸鉴权
        result: '1', //鉴权结果，1为成功
        crmAuthType: curAuthItem.crmAuthType,
        authNum: curAuthItem.authNum,
      }
      let authType = curAuthItem.authType //提交给CRM的authtype

      //非数字鉴权方式
      let url = `/xsb/personBusiness/customerView/h5IdentifyUser?telnum=${telnum}&idCardType=${authType}&idCardNum=&randomNum=${this.valCode}`
      let validateFlg = this.validateVal(this.valCode, this.codeRule)

      if (validateFlg) {
        //如果输入框内容校验通过
        this.doIdentifyUser(url, obj)
      } else {
        this.canClickFlg = true
      }
    },
    //请求服务端鉴权
    doIdentifyUser(url, obj) {
      //authtype 验证类型 06服务密码登录 07验证码 00二代证;
      this.popFlag = false
      let authType = this.currentItem.authType
      //处理主号副号问题（万能副卡、亲情网、核心成员）
      if (this.isPrimaryId) {
        //传了主号的情况
        let userIdentifyInfoStr = {
          addType: '2', //2副号鉴权
          primaryId: this.isPrimaryId, //主号
          subId: this.telnum, //副号
        }
        url = url + '&userIdentifyInfo=' + JSON.stringify(userIdentifyInfoStr)
      }

      this.$http
        .get(url, { unLoadFlg: false })
        .then((res) => {
          let resData = res.data
          if (typeof resData != 'object') {
            //不是对象的话就是加密串
            resData = decrptParam(res.data) //解密
            resData = JSON.parse(resData) //转换成对象
          }
          let { retCode, retMsg, data } = resData
          this.canClickFlg = true
          if (retCode == '0') {
            let realstatecode = data.realstatecode
            //港澳台胞证的
            let certType2List = ['HKMCPassport', 'DriverIC', 'Passport', 'StudentID', 'TaiBaoZheng', 'UnionSocietyCredit']

            let flag2 = certType2List.indexOf(data.certType) //判断证件类型
            //部分证件需要实名制审核，其他证件只需已登记状态
            if (realstatecode === '4' || (realstatecode === '1' && flag2 > -1)) {
              //需要实名制审核通过
              obj.telnum = this.telnum
              obj.authtype = authType //验证类型 06服务密码登录 07验证码 00二代证;
              obj.userName = data.userName
              obj.dynamicKey = data.dynamicKey
              obj.idCardWay = this.idCardWay //是否需要身份证鉴权 add 20200413
              //判断鉴权类型，如果不是身份证鉴权则不存流水
              if (authType == '00') {
                //00二代证
                obj.cardAuthSrl = this.cardAuthSrl //是否需要身份证鉴权 add 20200413
              }
              obj.userCity = data.region //用户地市 add 20220729 qp
              obj.userId = data.user_id //用户标识 add 20221224 qp
              // this.clearFormData();
              // console.info(obj)
              //设置调用链需要采集的手机号
              this.$pointLesslog && this.$pointLesslog.setChainTelnum(this.telnum);
              Storage.session.set('jqData', obj)
              this.qryNewCustom(obj.telnum)
            } else {
              this.goToolCheck()
            }
          } else {
            this.$toast({
              message: retMsg || '鉴权失败',
              position: 'bottom',
            })
            this.popFlag = true
          }
        })
        .catch((err) => {
          this.$alert('鉴权异常：' + err)
        })
    },
    goToolCheck() {
      this.$messagebox.alert('该号码状态是未审核，请使用实名制后再进行业务受理', '温馨提示').then((action) => {
        this.$router.push('/tools')
      })
    },
    //验证手机号 、密码是否正确
    validateVal(val, obj) {
      let result = true
      let rule = obj.rule //验证规则
      //验证输入的值是否符合rule
      if (rule && !rule.test(val)) {
        this.errorMsg = obj.msg //报错提醒内容
        result = false
      } else {
        this.errorMsg = ''
      }
      return result
    },
    getGroupList(pageNum) {
      let qryGroupType = ''
      let param = {}
      let groupName = this.groupName;
      if (!groupName) {
        qryGroupType = 'A'
        groupName = ''
        param = {unLoadFlg:true}
      }
      this.pageNum = pageNum ? pageNum : this.pageNum
      let url = `/xsb/personBusiness/groupVNet/h5GroupInfoByFsop?groupName=${groupName}&currentPage=${this.pageNum}&corpscope=${qryGroupType}`
      this.$http
        .get(url,param)
        .then((res) => {
          let { retCode, data, retMsg } = res.data
          if (retCode == '0') {
            let groupListTemp = data;
            if (!groupListTemp || groupListTemp.length < 1) {
              this.moreData = false;
              if(!qryGroupType){
                  if (this.pageNum > 1) {
                    this.$toast('没有更多数据')
                  } else {
                    this.$toast('查无集团，请确认集团名称是否填写无误')
                  }
              }
            } else {
              if (qryGroupType) {
                let count = 2
                for (let i in groupListTemp) {
                  if (count >= 1) {
                    this.historyGroup.push(groupListTemp[i])
                    count--
                  }
                }
              } else {
                this.moreData = true
                if (this.pageNum == 1) {
                  this.groupList = []
                  this.groupList = groupListTemp
                } else {
                  this.groupList.push(...groupListTemp)
                }
              }
            }
          } else {
            this.moreData = false
            this.groupList = []
            if(!qryGroupType){
              this.$alert(retMsg || '搜索集团失败')
            }
          }
        })
        .catch((response) => {
          this.moreData = false
          this.groupList = []
          this.$alert(`搜索集团异常网络连接失败${response}`)
        })
    },
    //公告管理页面
    goNoticeDetail(){
      this.$router.push('/noticeDetail')
    },
    closeGroupList(){
      this.showDropDown = false;
      this.groupList = [];
      this.pageNum = 1;
    },
    // 当前岗位开关控制
    async handleStationSwitch() {
      const res = await this.$http.post('/xsb/api-user/menu/h5GetStationSwitch', {
        stationId: this.uinfo.stationId
      })
      console.info(res, '当前岗位开关控制');

      if (res) {
        const data = res.data

        if (data && data.retCode == '0') {
          // 有权限
          Storage.session.set('stationSwitch', 1)
        } else {
          // 无权限
          Storage.session.set('stationSwitch', 0)
        }
      }
    },
    searchOrder(oneId,sharaCrmId){
      let beginDateVal = dateFormat( new Date(new Date().getTime() - 30 * 24 * 60 * 60 * 1000), 'yyyyMMdd')
      let endDateVal = dateFormat(new Date(new Date().getTime() + 24 * 60 * 60 * 1000), 'yyyyMMdd')
      let param = {
        'beginTime': beginDateVal,
        'endTime': endDateVal,
        'pageIndex': '1',
        'pageSize': '2',
        'searchType': '2',
        'oneId': oneId,
        'sharaCrmId':sharaCrmId
      }
      let url = `/xsb/personBusiness/jikeOrder/h5qryJikeOrderDetail`
      this.$http.post(url, param).then((res) => {
        let { retCode, retMsg, data } = res.data
        if (retCode == '0') {
          //没有返回数据
          if (!data.orderDetailInfoList || data.orderDetailInfoList.length < 1) {
            this.$toast('二维码订单信息查询失败，订单时间可能超过一个月无法查询')
          }else{
            let order = data.orderDetailInfoList[0]
            if (data.orderDetailInfoList.length > 1) {
              order.busiTypeName = data.orderDetailInfoList[1].busiTypeName
              order.linkName = data.orderDetailInfoList[1].linkName
              order.linkStateName = data.orderDetailInfoList[1].linkStateName
              order.opUserId = data.orderDetailInfoList[1].opUserId
              order.opUserName = data.orderDetailInfoList[1].opUserName
              order.opUserPhone = data.orderDetailInfoList[1].opUserPhone
            }
            console.log(order)
            this.$router.push({
              path:'ZeroMenuShare',
              query:{
                order: order,
                sharaCrmId: sharaCrmId
              }
            })
          }
        } else {
          this.$toast(retMsg || '二维码订单信息查询失败')
        }
      }).catch((response) => {
        this.$toast('二维码订单信息网络异常，请重试')
      })
    },
    getHomeType() {
      let param = {
        busiName: 'skzq-flag',
        busiKey: 'accessKey',
        defaultRegionValue: ''
      }
      let url = `/xsb/gridCenter/groupBroadBand/h5getBusiTypeZkFlag`
      return this.$http.post(url, param).then(res => {
        let { retCode } = res.data
        if (retCode == '0') {
          Storage.session.set('skzq-flag', '1')
          return true
        } else {
          Storage.session.set('skzq-flag', '2')
          return false
        }
      }).catch(res => {
        return false
      })
    },
    async initData() {
      if(this.$route.query.srcFrom == 'skzqHome'){
        this.getMenuList();
        this.getTodoTaskList();
        this.qryNotice();
        this.handleStationSwitch()
      }else{
        let skzqFlag =  Storage.session.get('skzq-flag') == '1' || (~Storage.session.get('skzq-flag') && await this.getHomeType())
        if (skzqFlag) {
          this.$router.push('/skzqHome')
          return
        }
        this.getMenuList();
        this.getTodoTaskList();
        this.qryNotice();
        this.handleStationSwitch()
      }
    },
    openView(){

      window.location="clientRequest:openWebKit::"+
        JSON.stringify({
          url: 'http://192.168.1.102:8080/xsbh5.html#/workStageNew',
          title: '',
          titleFlag: '0',
          screenType:   '1' ,
          param: '',
          hasRightBtn: '',
          rightIcon: '',
          rightJs: '',
          hasRightBtn1: '',
          rightIcon1: '',
          rightJs1: '',
        })
      sessionStorage.setItem('ViewMode',sessionStorage.getItem('ViewMode') ? '' : '1')
    }
  },
  async created() {
      // 关闭截屏权限
    ClientJs.screenShot(0);
    this.uinfo = Storage.session.get('userInfo')
    // 记录菜单链
    // this.updateMenuChain('shou-ye-new', 1);
    this.initFirstMenuLevel(this.CONSTVAL.MENU_CHAIN.SHOUYE_NEW);
    this.queryDay = dateFormat(new Date(), 'yyyyMMdd')
    //窗口宽度
    this.windowWidth = window.innerWidth || (document.body && document.body.clientWidth)
    //窗口高度
    this.windowHeigth = window.innerHeight || (document.body && document.body.clientHeight)
    this.initData()

    //悬浮按钮展示
    let deskSwitch = Storage.get('deskSwitchShow');
    if (deskSwitch == void 0) {
      deskSwitch = true;
      Storage.set('deskSwitchShow', deskSwitch)
    }
    this.deskSwitch = deskSwitch;
    //清除调用链业务菜单流水，防止被错误采集
    this.$pointLesslog && this.$pointLesslog.setChainBusiSeq('');
    if (Storage.session.get('isOpenZero')) {
      Storage.session.remove('isOpenZero')
      this.isShowPoint = true
      this.openOcr()
    }
  },
  mounted() {
    //集客大厅关闭页面主动请求此回调方法 add by qhuang
    window['closeCallBackFn'] = (res)=>{}

      window['recordLoaction'] = (result) => {
          let locationParam = result;
          this.judeRecordLoaction(locationParam);
      };
    window['scanCallBack'] = (result) => {
      console.log('code',result)
      if(result){
        if(~result.indexOf('http')) {
          this.$toast("网格通app为考虑安全问题，暂不允许打开其他外源链接")
        }else if(~result.indexOf('|') && /^[0-9]+$/.test(result.split('|')[0])){
          let oneId = result.split('|')[0]
          let shareCrmId = result.split('|')[1] || ''
          this.searchOrder(oneId,shareCrmId)
        }else {
          this.$toast("二维码内容识别有误，无法查询信息")
        }
      }else{
        this.$toast("二维码内容识别为空，请检查二维码是否完整")
      }
    }
    // 截屏权限回调方法
    window['screenShotCallbackfunc'] = (result) => {
      console.log('截屏方法结果', result);
    };
  },
  activated(){
    // 关闭截屏权限
    ClientJs.screenShot(0);
  },
  watch: {},
}
</script>
<style lang='less' scoped>
input {
  border: none;
  outline: none;
  background-color: transparent;
}

.work-stage {
  flex-grow: 1;
  background: linear-gradient(225deg, #dde5e5 0%, #f4f5f7 30%);
  overflow-y: auto;

  .remind {
    width: 100%;
    height: 30px;
    background: #fff9e5;
    font-size: 12px;
    color: #b76610;
    line-height: 18px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 0.78125rem 0 0.78125rem;
    box-sizing: border-box;

    .ratote {
      transform: rotate(45deg);
    }

    .ico-notice {
      font-size: 12px;
    }

    .latestNews {
      width: 80%;
    }
  }

  .tip-space {
    margin-top: 20px !important;
  }
  .search {
    height: 40px;
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid #000000;
    display: flex;
    align-items: center;
    padding-left: 0.361rem;
    width: calc(88% - 1.2rem);
    padding: 0 0.6rem;

    margin: auto;

    em {
      font-size: 30px;
      color: #444;
    }

    .input-span {
      font-size: 15px;
      color: #787878;
      //padding-left: 0.601rem;
        width: 100%;
    }
    .scan {
      width: 17px;
      height: 17px;
    }
    .split-span {
      font-size: 16px;
      color: #c8c8c8;
      margin: 0 8px 0 7px;
    }
  }
  .ai-tools {
    display: flex;
    align-items: center;

    .voice {
      width: 14px;
      height: 17px;
    }
    .paizhao{
      color: #007aff;
      padding: 5px 8px;
      font-size: 19px;
    }
    .search-button{
      width: 24px;
      background-color: #007AFF;
      padding: 6px 8px;
      font-size: 12px;
      border-radius: 8px;
      color: #fff;
      text-align: center;
      font-family: 'Microsoft YaHei', Arial, sans-serif;
    }

    .scan {
      width: 17px;
      height: 17px;
    }

  }
}

.content-box-work {
  display: flex;
  flex-direction: column;
  padding: 0 0.78125rem 0 0.901rem;
  box-sizing: border-box;
  background: transparent;

  .hot {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;

    ul {
      display: flex;
      flex: 1;

      li {
        display: flex;
        flex: 1;
        align-items: baseline;

        p {
          font-size: 31px;
          margin-right: 4px;
          font-family: math;
          color: #007aff;
        }

        span {
          font-size: 12px;
          color: #7d7d7d;
        }
      }
    }

    .hot-more {
      color: #9f9c9c;
      font-size: 13px;

      em {
        color: #c8c8c8;
        font-size: 13px;
      }
    }
  }

  .views {
    //height: 142px;
    background: #ffffff;
    box-shadow: 0 5px 7px 0 rgba(0, 0, 0, 0.03);
    border-radius: 6px 6px 6px 6px;
    border-image: linear-gradient(360deg, rgba(243, 243, 243, 1), rgba(255, 255, 255, 1)) 1 1;
    padding-top: 10px;
    position: relative;
    //overflow: hidden;

    .view-type {
      display: flex;
      font-weight: bold;
      margin-bottom: 10px;
      padding-left: 0.962rem;

      p {
        font-size: 14px;
        color: #2f2f2f;

        &.active {
          color: #007aff;
        }

        &:nth-child(1) {
          margin-right: 0.962rem;
        }
      }
    }

    .connection {
      padding: 0 0.7rem 5px;
      position: relative;
      height: 104px;

      .more-other {
        position: absolute;
        bottom: 0;
        right: -8px;
        display: flex;

        .more-box {
          width: 4.988rem;
          height: 28px;
          box-sizing: border-box;
          padding: 2px 2px 0 2px;
          transform: skewX(-30deg);
          margin-right: 10px;
          border-radius: 6px 19px 0 0;
          background-image: linear-gradient(180deg, rgba(173, 173, 173, 1), rgba(140, 144, 149, 0));

          .more-type {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 6px 17px 0 0;
            display: flex;
            align-items: center;
            justify-content: center;

            p {
              transform: skewX(30deg);
              font-size: 12px;
              color: #676767;
            }

            em {
              font-size: 12px;
            }
          }
        }

        .login-box {
          width: 80px;
          height: 30px;
          border-radius: 9px 6px 6px 0;
          box-sizing: border-box;
          padding-right: 0.78125rem;
          background: linear-gradient(0deg, #007aff 0%, rgba(0, 122, 255, 0.2) 100%);
          color: #fff;
          font-size: 15px;
          display: flex;
          justify-content: center;
          align-items: center;
          transform: skewX(-24deg);
          margin-right: 12.5px;
          border: 1px solid #007aff;

          .login {
            transform: skewX(24deg);
            letter-spacing: 1.2px;
          }
        }
      }
    }

    .business-view {
      // padding: 0 0.962rem 5px;
      padding: 0 0.7rem 5px;
      height: 104px;

      .record {
        display: flex;
        padding: 10px 0;
        align-items: center;
        font-size: 13px;

        p {
          font-size: 12px;
          color: #4d8acc;
          padding: 8px 0.601rem;
          background: #e0efff;
          border-radius: 2.224rem;
          margin-left: 0.841rem;
          max-width: 40%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        em {
          font-size: 20px;
          color: #d7d2d2;
        }
      }
    }

    .phone-auth {
      display: flex;
      justify-content: space-between;

      .ph-send {
        height: 36px;
        //width: 6.37rem;
        color: #007aff;
        font-size: 14px;
        border-radius: 8px;
        border: 1.5px solid #007aff;
        padding: 0 1.022rem;
        display: flex;
        align-items: center;
        margin-left: 0.901rem;
        white-space: nowrap;
        justify-content: space-evenly;
        width: 20%;
        z-index: 999;
      }
    }

    .ph-num {
      flex: 1;
      background-color: #e0efff;
      border-left: 4px solid #007aff;
      height: 42px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      margin-bottom: 10px;

      .dropDwon-keep {
        border-end-start-radius: 0px;
        border-end-end-radius: 0px;
      }

      em {
        color: #007aff;
        font-size: 16px;
        display: inline-block;
        margin: 0 0.301rem 0 0.1rem;
      }

      input {
        font-size: 14px;
        letter-spacing: 0.5px;
        width: 10.413rem;

        &::placeholder {
          color: #007aff;
          opacity: 1; //兼容火狐默认给占位符设置了透明度
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }

  .common-functions {
    > p {
      color: #3d3b3b;
      letter-spacing: 1px;
      margin-top: 15px;
      font-weight: bold;
    }

    ul {
      display: flex;
      justify-content: space-around;
      padding: 17px 0 21px;

      li {
        display: flex;
        flex-direction: column;
        align-items: center;

        img {
          width: 2.5rem;
        }

        p {
          margin-top: 1px;
          font-size: 12px;
          color: #585858;
        }
      }
    }
  }
  .box {
    display: flex;
    flex-wrap: wrap;
    position: relative;
  }

  .common-area {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    .common-use {
      width: calc(50% - 0.3rem);
    }
  }

  .fuse {
    border-radius: 10px;
    background-color: #fff;
    padding: 40px 0.481rem 0 0.481rem;
    display: flex;
    position: relative;
    margin: 10px 0;

    .fuse-list {
      flex: 1;
      margin-right: 0.6rem;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      margin-bottom: 12px;

      > ul {
        flex: 1;
        display: flex;
        justify-content: space-evenly;
        flex-direction: column;

        li {
          display: flex;
          height: 38px;
          align-items: center;

          .li-label {
            border: 1px solid #007aff;
            border-radius: 4px;
            color: #007aff;
            margin-right: 3px;
            font-size: 8px;

            &.orange {
              border-color: #e97330;
              color: #e97330;
            }

            p {
              transform: scale(0.8);
            }
          }

          .li-content {
            flex: 1;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            color: #474747;
            font-size: 13px;
          }
        }
      }

      .fuse-more {
        width: 100%;
        background: #eff7ff;
        height: 25px;
        border-radius: 8px 8px 8px 8px;
        color: #558ad8;
        text-align: center;
        line-height: 25px;
        font-size: 12px;

        em {
          font-size: 13px;
        }
      }
    }

    .fuse-common {
      //width: calc(50% - 0.1rem);
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      .common-use {
        background-color: #eff7ff;
      }
    }

    .fuse-title {
      position: absolute;
      left: 0;
      top: -7px;
      width: 127px;
      height: 36px;
      color: #ffffff;
      border-radius: 4px;
      overflow: hidden;

      p {
        width: 100%;
        height: 100%;
        background: rgba(0, 122, 255, 0.6);
        box-shadow: 1px -1px 4px 0px rgba(24, 132, 250, 0.28);
        transform: skewX(27deg);
        margin-left: -12px;
        line-height: 36px;
        text-align: center;

        span {
          transform: skewX(-27deg);
          display: inline-block;
        }
      }
    }
  }

  .tool {
    >div{
      padding-bottom: 10px;
    }
    .title {
      display: flex;
      margin-bottom: 10px;
      justify-content: space-between;

      p {
        font-size: 16px;
        font-weight: bold;
        color: #000000;
      }

      span {
        font-size: 13px;
        color: #9f9c9c;

        em {
          font-size: 13px;
        }
      }
    }

    ul {
      display: flex;
      flex-wrap: wrap;

      li {
        width: 25%;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 10px 0;

        img {
          width: 1.8rem;
        }

        p {
          font-size: 12px;
          color: #000000;
        }
      }
    }
  }

  .wc-bannerblock {
    height: auto;
    overflow: hidden;
    flex-shrink: 0;
    margin-bottom: 10px;

    .wc-banner {
      height: auto;
      overflow: hidden;
      margin-top: 8px;
      padding-bottom: 30px;
    }

    .bs-slide {
      .baner-item-wrap {
        border-radius: 8px;
        position: relative;
        span {
          position: absolute;
          top: 29%;
          left: 7%;
          color: #fff;
          font-size: 122%;
          font-family: zcoolqingkehuangyouti-Regular, zcoolqingkehuangyouti;
          font-weight: 400;
          line-height: 23px;
          background: linear-gradient(107deg, #007aff 0%, rgba(0, 122, 255, 0.41) 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }

      .banner-title {
        color: white;
        font-size: 20px;
        font-weight: bold;
        position: absolute;
        top: 50%;
        left: 20px;
        transform: translateY(-50%);
        white-space: nowrap;
      }
    }
  }
}

.noop-li {
  float: left;
}

.fixed-button {
  position: fixed;
  left: 84%;
  top: 0px;
  border-radius: 50%;
  background: #007aff;
  color: #fff;
  text-align: center;
  z-index: 999;
  width: 44px;
  height: 44px;
  line-height: 44px;
  box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.1), 0px 1px 3px 0px rgba(0, 0, 0, 0.1), 0px 5px 5px 0px rgba(0, 0, 0, 0.09), 0px 12px 7px 0px rgba(0, 0, 0, 0.05),
    0px 21px 8px 0px rgba(0, 0, 0, 0.01), 0px 32px 9px 0px rgba(0, 0, 0, 0);

  .gouwuche {
    font-size: 35px;
    background: linear-gradient(180deg, #ffffff 30%, #007aff 100%);
    box-shadow: -7px 4px 5px 0px rgba(15, 96, 184, 0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 888;
  opacity: 0;
}
    .AIchat {
        position: fixed;
        bottom: 80px;
        right: 10px;
        z-index: 555;
        .text {
            position: absolute;
            bottom: 40px;
            right: 0;
            img {
                height: 80px;
                width: auto;
            }
        }
        .pic {
            position: absolute;
            right: 0;
            bottom: 0;
            img {
                width: 50px;
                height: 50px;
            }
        }
    }
.tool-div{
  margin-top: 15px;
}
.point-to {
  top: 91px;
  right: 3.735rem;
  transform: rotate(-115deg) rotateY(20deg) rotateZ(45deg);
  width: 92px;
  position: fixed;
  z-index: 1999;
}
</style>
