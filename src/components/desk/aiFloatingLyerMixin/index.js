import DBStorage from '@/base/indexDb.js'
import Storage from '@/base/storage'
import { h5AddMenuRecord, h5QueryMenuRecordCount, h5UpdateMenuCount } from '../../request'
import { chgStrToDate, dateFormat } from '../../../base/utils'
// 更新相关混合
export const aiFloatingLyerMixin = {
  data() {
    return {
      userInfo: null,// 用户信息
      dbStorage: {},// 数据库实例
      // aiFloatHits: [],// 浮层各个词条点击量（页面展示使用）
      menuCountList: [],// 统计菜单（全部统计使用）
      menuRecordList: [],// 记录菜单（全部统计使用）
      // menuType 1:菜单 2.智能体 3.词条 4.外围菜单 menuId = sceneId  menuName = sceneName
      aiPageArr1: [
        // { menuName: 'DeepSeek', menuId: '109', menuType: 2, onlyKey: '109_DeepSeek', isFixed: true },// 直接跳转"DeepSeek"智能体
        { menuName: '订单查询', menuId: '108', menuType: 3, onlyKey: '108_订单查询', isFixed: true }, // 跳转"我的集团公测"智能体
        { menuName: '问查算比', menuId: '47', menuType: 2, onlyKey: '47_问查算比', isFixed: true },// 直接跳转"问查算比"智能体
        // { menuName: '服务', menuId: '3', menuType: 2, onlyKey: '3_服务', isFixed: true },// 直接跳转"服务"智能体
        // { menuName: '推荐V网成员', sceneId: '2', menuType: 3 },// 跳转"营销"智能体
        // { menuName: '方案生成', sceneId: '108', menuType: 3 },// 跳"我的集团公测"智能体
        { menuName: 'AI选号', menuId: '2202', menuType: 2, ydHide: true, onlyKey: '2202_AI选号', isFixed: true }, // 是否异地登录隐藏
        { menuName: '国漫套餐', menuId: '2242', menuType: 2, onlyKey: '2242_国漫套餐', isFixed: true },
        { menuName: '客户洞察', menuId: '2244', menuType: 2, onlyKey: '2244_客户洞察', isFixed: true },
        { menuName: 'AI商机', menuId: '2243', menuType: 2, onlyKey: '2243_AI商机', isFixed: true },
        // {menuRoute:'invoiceDialogue',name:'AI开票'},
        { menuName: '宽带户型热力图', menuId: '2245', menuType: 2, onlyKey: '2245_宽带户型热力图', isFixed: true },
        // { menuRoute: 'dayPractice', menuName: '投诉专区助手', menuType: 2 },
        { menuName: '营业厅知识助手',  menuId: '2249',menuType: 2, onlyKey: '2249_营业厅知识助手', isFixed: true }
      ],
      cachedData: null// 当前本地存储数据
      // taskTimer: null // 定时任务
    }
  },
  async mounted() {
    this.userInfo = Storage.session.get('userInfo')
    //初始化数据示例
    this.dbStorage = DBStorage.getInstance({
      busiType: 'aiFloatHits'
    })
  },
  computed: {
    aiPageArr() {
      if (this.$store.state.floatIconModule.topFixedList &&  this.$store.state.floatIconModule.topFixedList.length) {
        return this.$store.state.floatIconModule.topFixedList
      }
      return this.aiPageArr1
    }
  },
  methods: {
    // 获取本地数据库或者从接口获取菜单统计数据
    async getDBData() {
      // 用当前登陆人手机号当作唯一的键存在DB数据库
      let cachedData = await this.dbStorage.getItem(this.userInfo.servNumber)
      // 本地没有存过数据
      if (!cachedData || !cachedData.menuCountList) {
        // 当前用户手机号本地没有存过
        const res = await this.h5QueryMenuRecordCount() // 统计表
        // 封装处理函数
        const processList = (list, servNumber) => {
          return list.map(item => ({
            ...item,
            onlyKey: `${item.menuId}_${item.menuName}`,
            operMsisdn: this.userInfo.servNumber
          }))
        }
        // 处理唯一的键值
        res.countList = processList(res.countList)
        res.recordList = processList(res.recordList)
        cachedData = {
          id: this.userInfo.servNumber,
          menuCountList: res.countList,
          menuRecordList: res.recordList
        }
        this.dbStorage.addItem(cachedData)
      }
      this.cachedData = cachedData
      this.menuCountList = this.cachedData.menuCountList
      this.menuRecordList = this.cachedData.menuRecordList
      return JSON.parse(JSON.stringify(cachedData))
    },
    // 记录ai浮层点击量
    async aiFloatHitsCount(item) {
      console.info('aiFloatHitsCount', item)
      let menuName = item.menuName ? item.menuName : item.sceneName
      let menuId = item.sceneId ? item.sceneId : item.menuId
      let onlyKey = `${menuId}_${menuName}`
      let curMenuItem = [...this.menuCountList, ...this.aiPageArr].find(aItem => aItem.onlyKey === onlyKey)
      if (curMenuItem) {
        // 30天点击量
        curMenuItem.clickCount = curMenuItem.clickCount ? curMenuItem.clickCount + 1 : 1
      } else {
        curMenuItem = {
          menuId: menuId,
          menuName: menuName,
          menuType: item.menuType,
          clickCount: 1,
          onlyKey: onlyKey
        }
      }
      console.info('curMenuItem：', curMenuItem)
      // 每条都要加上手机号
      curMenuItem['operMsisdn'] = this.userInfo.servNumber
      // 添加时间戳（超过30天删除该条数据使用）
      curMenuItem['createDate'] = dateFormat(new Date(), 'yyyyMMddhhmmss')
      // 通过searchMenu搜索菜单拉起的菜单
      if (item.routeParams) {
        curMenuItem['menuRoute'] = item.menuRoute
        curMenuItem['routeParams'] = JSON.stringify(item.routeParams)
      }
      // 没有统计过该菜单
      if (!this.menuCountList.some((item => item.onlyKey === curMenuItem.onlyKey))) {
        // 没有统计过该菜单
        this.menuCountList.push(curMenuItem)
      }
      // 添加到记录表中
      this.menuRecordList.push(curMenuItem)
      let addItem = {
        id: this.userInfo.servNumber, menuCountList: this.menuCountList, menuRecordList: this.menuRecordList
      }
      console.info('添加本地数据库：', addItem)
      // 添加到本地数据库
      await this.dbStorage.setItem(addItem)
      // 保存菜单点击
      await this.h5AddMenuRecord(curMenuItem)
    },
    async h5QueryMenuRecordCount() {
      const res = await h5QueryMenuRecordCount({ operMsisdn: this.userInfo.servNumber })
      console.info('h5QueryMenuRecordCount返回：', res)
      if (res) {
        return res
      } else {
        return {
          countList: [],
          recordList: []
        }
      }
    },
    // 保存菜单点击记录（一条）
    async h5AddMenuRecord(item) {
      item['operMsisdn'] = this.userInfo.servNumber
      console.info('保存菜单点击记录请求参数', item)
      const res = await h5AddMenuRecord(item)
      console.info('保存菜单点击记录返回数据', res)
      if (res) {
      }
    },
    // 更新菜单统计数据（列表）
    async h5UpdateMenuCount() {
      let params = {
        operMsisdn: this.userInfo.servNumber, menuCountList: this.menuCountList
      }
      console.info('更新菜单统计数据请求参数', params)
      const res = await h5UpdateMenuCount(params)
      console.info('更新菜单统计数据返回数据', res)
    },
    getTopThreeByType(menuData1) {
      if (!menuData1) return this.aiPageArr
      // 过滤：找出 aiPageArr固定菜单 里不存在相同 onlyKey 的数据
      const menuData = menuData1.filter(item => {
        // 判断 aiPageArr 中是否有 onlyKey 与当前 item.onlyKey 相同的元素
        return !this.aiPageArr.some(aiItem => aiItem.onlyKey === item.onlyKey)
      })
      // 1. 基于系统当前时间，计算最近 30 天的起始时间
      const currentDate = new Date() // 获取实际当前时间
      const thirtyDaysAgo = new Date(currentDate)
      thirtyDaysAgo.setDate(currentDate.getDate() - 30) // 当前时间往前推 30 天

      // 2. 筛选最近 30 天的数据
      const recentData = menuData.filter((item) => {
        return new Date(chgStrToDate(item.createDate)) >= thirtyDaysAgo
      })

      // 3. 按 menuType 分组统计点击量（1、2、3），直接用对象存储分组统计结果
      const typeStats = { 1: {}, 2: {}, 3: {} }
      recentData.forEach((item) => {
        const menuType = Number(item.menuType)
        const onlyKey = item.onlyKey
        if (![1, 2, 3].includes(menuType)) return
        if (!typeStats[menuType][onlyKey]) {
          typeStats[menuType][onlyKey] = {
            menuId: item.menuId,
            menuName: item.menuName,
            menuType,
            clickCount: item.clickCount ? item.clickCount : 0,
            menuRoute: item.menuRoute,
            onlyKey: item.menuId + '_' + item.menuName,
            routeParams: item.routeParams ? JSON.parse(item.routeParams) : null,
            createDate: item.createDate
          }
        }
        typeStats[menuType][onlyKey].clickCount++
      })

      // 4. 按点击量降序排序，取前三，利用数组 flatMap 简化合并
      const topThree = [2, 3, 1].flatMap((type) => {
        return Object.values(typeStats[type])
          .sort((a, b) => b.clickCount - a.clickCount)
          .slice(0, 3)
      })

      console.info('计算出的统计列表', topThree)

      // 5. 合并固定 aiPageArr 并去重
      const merged = [...this.aiPageArr, ...topThree]
      const map = new Map()
      merged.forEach((item) => map.set(item.onlyKey, { ...map.get(item.onlyKey), ...item }))
      const result = Array.from(map.values())

      console.info('全部推荐列表', result)
      return result
    },
    // 删除前端数据库内menuRecordList30天前的数据
    async delete30DaysAgoData() {
      // 1. 计算30天前的时间
      const now = new Date()
      const thirtyDaysAgo = new Date(now)
      thirtyDaysAgo.setDate(now.getDate() - 30)

      // 2. 筛选30天内的数据
      this.menuRecordList = this.menuRecordList.filter(item => {
        const createDate = chgStrToDate(item.createDate)
        // 若解析失败，默认视为过期数据（或根据业务调整）
        return createDate && createDate >= thirtyDaysAgo
      })

      // 3. 更新数据库
      const addItem = {
        id: this.userInfo.servNumber,
        menuCountList: this.menuCountList,
        menuRecordList: this.menuRecordList
      }
      // 重新添加30天内的数据
      console.info('添加30天内记录：', addItem)
      await this.dbStorage.setItem(addItem)
    },

    // 查询本月营销案击次数
    async getMarketReminderHitsCount() {
        try {
            const id = 'MarketReminder_' + this.userInfo.crmId;
            const data = await this.dbStorage.getItem(id);
            if (!data) return 0;
            const now = dateFormat(new Date(), 'yyyyMM');
            if (now != data.date) return 0;
            return data.count;
        } catch (e) {
            console.error('getJTopHitsCount error', e);
            return 0;
        }
    },
    // 增加本月营销案点击次数
    async addMarketReminderHitsCount(hitsCount) {
        try {
            const item = {
                id: 'MarketReminder_' + this.userInfo.crmId,
                count: hitsCount,
                date: dateFormat(new Date(), 'yyyyMM')
            }
            await this.dbStorage.setItem(item)
        } catch (e) {
            console.error('getJTopHitsCount error', e);
        }
    }
  }
}
