<template>
  <div class='float-box' :style='{"height":clientHeight+"px"}'>
    <div class='float-content'>
      <div class='close' @click='closeFloat'><em class='iconfont guanbi'></em>关闭</div>
      <div class='head'>
        <div class='speak'>
<!--          <img src='static/img/ai-lxzs.gif' alt='' />-->
          <img src='static/img/xy-bg-2.png' alt='' />
          <p>您的常用功能</p>
        </div>
        <div class='title-content'>
          <p>Hi 我叫灵犀</p>
          <span>心有灵犀，精准触达</span>
        </div>
      </div>
      <div class='tip-content'>
        <div class='tip-box'>
          <div v-if="!(item.ydHide && offsiteLoginFlg)" v-for="(item,index) in aiFloatHits" :key="index" class='tip' @click='openAiPage(item)'>
            <span :class="{
              'isFixed':item.isFixed,// 固定推荐样式
              'color-2':item.menuType==2 || item.menuType==3, // 智能体、词条
              'color-1':item.menuType==1,// 菜单
            }">{{item.menuName}}</span>
          </div>
        </div>

        <div class='remind' v-if='remindFlg || gdFlg'>您有
          <span @click='goJtopAppPage("stayOver2DaysCnt")'>{{workReminder.stayOver2DaysCnt}}</span>
          条审批工单滞留超2日，今日通过
          <span @click='goJtopAppPage("todayApproveCnt")'>{{workReminder.todayApproveCnt}}</span>  条，拒绝
          <span @click='goJtopAppPage("todayRejectCnt")'>{{workReminder.todayRejectCnt}}</span>  条
        </div>
        <div class='remind' v-if='marketReminderShow'>
           您本月有
           <span @click="openMarketPage('1')">{{ marketReminder.currentMonthExpireCount }}</span>
           条营销案即将到期，次月有
           <span @click="openMarketPage('2')">{{ marketReminder.nextMonthExpireCount }}</span>
           条到期
        </div>
        <div class='title-p'>
          <p>我的智能体</p>
          <div @click='preSetFlg=true'><em class='iconfont shezhi'></em><span>偏好设置</span></div>
        </div>
        <slide ref='slideAi'
               class='bs-slide float-slide'
               :autoPlay='false'
               :loop='false'
               :dataList='recommendIconList'
        >
          <div v-for='(item,index) in recommendIconList' :key='index'>
            <ul class='baner-item-wrap'>
              <li v-for='(i,d) in item' :key='d' :id='`li-${d}`'>
                <div ref='divDom'>
                  <img v-if="!i.isMore" @click='goToPage(i)' :src='i.sceneIcon' />
                  <img v-else @click='preSetFlg=true' src="../../assets/img/aiGroup/float/more-iocn.png" />
                </div>
                <span class='banner-title'>{{ i.sceneName }}</span>
              </li>
            </ul>
          </div>
        </slide>
        <!-- 输入区域 -->
        <ai-bottom-com ref="aiBottomRef" :inputText.sync="inputText" :inputSwitch.sync="inputSwitch" :loading="loading"
                         @sendMessage="sendMessage"></ai-bottom-com>
      </div>
    </div>

    <preference-settings v-if='preSetFlg' v-model:show='preSetFlg'></preference-settings>


    <menu-dialog :maMenuList="maMenuList" :sendMessageTxt="sendMessageTxt" v-model="menuDialogFlg" @switchMenu="switchMenu"></menu-dialog>
  </div>
</template>

<script>
import Slide from 'components/common/Slide.vue'
import ClientJs from '@/base/clientjs'
import Storage from '@/base/storage'
import PreferenceSettings from './PreferenceSettings.vue'
import { BASE64 } from '@/base/coding'
import { menuChain } from '@/base/mixins/menuChainMixin'
import { dateFormat } from '@/base/utils'
import { menuLogRecord } from '@/base/request/commonReq.js'
import { tokenFromAldMixin } from '@/base/mixin'
import { aiFloatingLyerMixin } from './aiFloatingLyerMixin/index'
import aiBottomCom from './components/aiBottomCom.vue'
import menuDialog from './components/menuDialog.vue'
const danghuanCryptoJS = require('@/base/danghuan/encrypt.js');
let encrypt = danghuanCryptoJS.Encrypt;

export default {
  name: 'AiFloatingLayer',
  mixins: [menuChain,tokenFromAldMixin,aiFloatingLyerMixin],
  components: { Slide, PreferenceSettings,aiBottomCom,menuDialog},
  props:{
    clientHeight:{
      type:Number,
      default:300
    },
    workReminder:{
      type:Boolean,
      default:function(){
        return {}
      }
    },
    show: {
        type: Boolean,
        default: false
    }
  },
  data() {
    return {
      recommendList: [],
      isRecording: false,
      inputSwitch: false,
      loading: false,
      inputText: '',
      preSetFlg: false,
      vioceTxt: '',
      maMenuList: [],//可供选择的菜单
      menuDialogFlg: false,
      sendMessageTxt:"",
      offsiteLoginFlg: Storage.session.get('offsiteLoginFlg'),  //异地登录标识
      marketReminderShow: false, // 营销案提醒是否展示
      marketReminder: {currentMonthExpireCount: '0', nextMonthExpireCount: '0'}, // 营销案提醒
      marketReminderMaxClickCount: 5, // 营销案最大点击次数
    }
  },
  methods: {
    openAiPage(item){
      // 内部菜单
      if(item.menuType == 1){
        this.goInsidePage(item)
      }else{
        item.sceneId = item.menuId ? item.menuId : item.sceneId
        // 外部菜单
        this.goToPage(item)
      }
    },
    closeFloat() {
      // 关闭录音权限
      // if(this.$refs.aiBottomRef.recorder){
      //     console.info('清空录音权限')
      //     this.$refs.aiBottomRef.recorder.close()
      //     this.$refs.aiBottomRef.recorder = null
      // }
      this.$emit('closeAiDialog')
      console.info('是否更新过菜单',this.isUpdateFlag)
      // 没更新菜单
      if(!this.isUpdateFlag){
        // 保存菜单统计
        this.h5UpdateMenuCount()
        // 更新缓存值
        this.$store.commit('floatIconModule/rewriteIsUpdateFlag', true)
        // 删除30天前的数据
        this.delete30DaysAgoData()
      }
    },
    // 外部菜单（星邺）
    async goToPage(item) {
      const accessToken = await this.getAccessToken();
      if (!accessToken) return;
      const name = item.menuName ? item.menuName : item.sceneName;
      const keywordContent = item.menuType == 3 ? name : "";
      this.openOtherPage(item, accessToken, keywordContent);
    },
    // 获取浮层单点登录Token
    async getAccessToken() {
        // 从缓存中取Token
        const expires = Storage.session.get('ai_expires_in')
        const current = (new Date().getTime()) / 1000
        if (expires && current < expires.time) {
            // 缓存没有过期
            return expires.accessTk
        }
        const aldToken = await this.getAldToken();
        if (!aldToken) return;
        const url = `/xsb/chatTools/agent/h5agentKeyConvert?tokenStr=${decodeURIComponent(aldToken)}`
        const res = await this.$http.get(url)
        let { retCode, data, retMsg } = res.data
        if (retCode == '0') {
            const accessToken = data.access_token
            const currentTime = (new Date().getTime()) / 1000
            const expiresObj = {
                accessTk: accessToken,
                time: currentTime + 600 //有效期10分钟
            }
            Storage.session.set('ai_expires_in', expiresObj)
            return accessToken;
        } else {
            this.$alert(retMsg || '拉起access_token失败')
            return null;
        }
    },
    // 获取认证Token
    async getAldToken() {
        let client = /android|harmony/gi.test(navigator.userAgent) ? 'ANDROID' : 'IOS'
        let url = `/xsb/api-user/tokenAuthen/h5genToken.do?opId=starye&prviId=4848&clientType=${client}`
        url += `&regionId=${this.uinfo.region}&operId=${this.uinfo.staffId}&stationId=${this.uinfo.stationId}&phoneNumber=${this.uinfo.servNumber}`
        const res = await this.$http.get(url)
        let { retCode, data, retMsg } = res.data
        if (retCode == '0') {
            return data.parameter;
        } else {
            this.$alert(retMsg || '拉起token失败')
            return null;
        }
    },
    // 内部菜单
    goInsidePage(item) {
      this.sceneItem = item.routeParams? item.routeParams : item;
      console.info('sceneItem',this.sceneItem)
      this.sceneItem.breadIds = window.breadIds;
      this.selectStationId = this.sceneItem.stationId;
      this.selectStationName = this.sceneItem.stationName;
      this.sceneItem.funcId = this.sceneItem.privId;
      this.sceneItem.funcName = this.sceneItem.privName;
      let userInfo = Storage.session.get('userInfo');
      // 判断岗位是否相同
      if(this.selectStationId !== userInfo.stationId){//切换岗位
        this.$messagebox({
          title: '温馨提示',
          message: `正在为您切换岗位到${this.selectStationName}，去访问${this.sceneItem.privName},可能会影响当前页面,是否切换？`,
          showCancelButton: true,
          confirmButtonText: '确认',
          cancelButtonText: '取消',
        }).then((action) => {
          if(action === 'confirm'){
            this.changeStation();
          }
        })
      }else{
        this.goSearchPage();
      }
    },
    async addMenuLogRecord() {
      // 菜单点击日志采集
      let menuParam = {
        stationId: this.uinfo.stationId,
        serverNumber: this.uinfo.servNumber,
        clickTime: dateFormat(new Date(), ('yyyy-MM-dd hh:mm:ss'))
      }
      // 后续绑定数据使用
      let menuData = await menuLogRecord(menuParam)
    },
    agentKeyConvert(item, tokenStr,keywordContent) {
      let url = `/xsb/chatTools/agent/h5agentKeyConvert?tokenStr=${decodeURIComponent(tokenStr)}`
      this.$http.get(url).then((res) => {
        let { retCode, retMsg, data } = res.data
        if (retCode == '0') {
          let access_token = data.access_token
          let currentTime = (new Date().getTime()) / 1000

          let expiresObj = {
            accessTk: access_token,
            time: currentTime + 600 //有效期10分钟
          }

          Storage.session.set('ai_expires_in', expiresObj)
          this.openOtherPage(item, access_token,keywordContent)

        }else{
          this.$alert(retMsg || '拉起access_token失败')
        }
      }).catch(err=>[
        this.$alert(err || '网络连接失败')
      ])
    },
    openOtherPage(item, access_token,keywordContent) {
      let floatObjectSsoParams = {
        access_token: access_token,
        sceneId: item.sceneId,
        keywordContent: keywordContent
      }
      console.info('拉起外部菜单请求参数',floatObjectSsoParams)
      let url = `${Storage.get('webUrl')}/aiagent/aigc/#/mobilechat/index?floatObjectSsoParams=${encodeURIComponent(BASE64.encode(JSON.stringify(floatObjectSsoParams)))}`
      console.log(url)

      // 更新菜单链
      this.updateMenuChain(`scene_${item.sceneId}`, 2)
      this.addMenuLogRecord()
      // 记录ai浮层菜单统计
      this.aiFloatHitsCount(item)
      this.closeFloat()
      ClientJs.openWebKit(encodeURIComponent(url), '', '0', '', '', '', '', '', '', '', '')
    },
    /*输入发送*/
    sendMessage(inputText) {
      if (!inputText.trim() || this.loading) return
      this.getMenuId(inputText.trim());
      setTimeout(() => {
        this.inputText = ''
      }, 300)
    },
    getMenuId(voiceTxt) {
      this.sendMessageTxt = voiceTxt;
      let url = '/xsb/chatTools/agent/h5SearchMenuAgent';
      let userInfo = Storage.session.get('userInfo');
      let params = {
        "stationId":userInfo.stationId,
        "staffId":userInfo.staffId,
        "keyword":voiceTxt,
        "phoneNumber":userInfo.servNumber,
      }
      console.log(params)
      this.$http.post(url,params).then(res => {
        let { retCode, retMsg, data } = res.data
        console.log('h5SearchMenuAgent返回',data)
        if (retCode == '0') {
          //判断有几个菜单
          if(Array.isArray(data) && data.length >0){
            let chooseItem = data.find(d => d.similarity > 0.8);
            if(chooseItem){
              this.switchMenu(chooseItem)
            }else{
              this.menuDialogFlg = true;
              this.maMenuList = data;
            }
          }else{
            this.$alert('请重新输入或配置包含该菜单的岗位！')
          }
        }else{
            this.$alert(retMsg || '请重新输入或配置包含该菜单的岗位！')
        }
      }).catch(err=>[
        this.$alert(err || '网络异常')
      ])

    },
    switchMenu(item){
      this.menuDialogFlg = false;
      // 2:智能体菜单 3：智能体词条
      if(item.type === '2' || item.type === '3'){//拉起星耶慧杰
        // 智能体类型是2
        item.menuType = item.type
        this.goToPage(item);
      } else{//跳转自有菜单
        this.goInsidePage(item)
      }
    },
     changeStation() {
      if (this.isSpecialStation()) {
        let promptTxt = '请输入客户经理工号';
        if (this.selectStationId === this.CONSTVAL.STATION_SCHOOL){
          promptTxt = '请输入手机号';
        }
        //弹窗校验
        this.showNamePrompt('',promptTxt)
      } else {
        this.switchStation();
      }
    },
    showNamePrompt(initialValue = '',promptTxt) {
      this.$messagebox.prompt(promptTxt, '', { inputValue: initialValue}).then(async({ value, action }) => {
        if (action === 'confirm') {
          if (this.selectStationId === this.CONSTVAL.STATION_SCHOOL){
            if (!this.isValidPhoneNumber(value)) {
              this.showNamePrompt(value,'请输入正确的手机号')
              return;
            }
            this.relTelnum = value;
            await this.checkStudentPhone();
          }else{
            this.relTelnum = value;
            await this.getRelTelnumInfo();
          }
        }
      })
    },
    goSearchPage(){
      this.$router.push({
        path: '/searchMenu',
        query: {
          queryAiKey: this.sceneItem
        }
      })
      setTimeout(() => {
        this.closeFloat()
      }, 300)
      // 记录ai浮层菜单统计
      let item = {
        menuType: 1,
        menuId: this.sceneItem.funcId,// 菜单id
        menuName: this.sceneItem.funcName,// 菜单名称
        menuRoute:`/searchMenu`,
        routeParams:this.sceneItem // 路由跳转携带参数
      }
      this.aiFloatHitsCount({... this.sceneItem,...item})
    },
    isSpecialStation() {
      return this.selectStationId === this.CONSTVAL.STATION_JKSX || this.selectStationId === this.CONSTVAL.STATION_SCHOOL;
    },

    isValidPhoneNumber(phone) {
      return phone === this.uinfo.servNumber || /^((1)+\d{10})$/.test(phone);
    },

    async getRelTelnumInfo() {
      const xiexiaoName = this.selectStationId === this.CONSTVAL.STATION_JKSX ? "客户经理工号" : "协销工号";
      try {
        const res = await this.$http.get(`/xsb/personBusiness/chooseTelEnterNet/h5QryRelTelnumInfo?relTelnum=${this.relTelnum}`);
        const data = res.data;
        if (data.retCode === '0') {
          const opratorName = this.maskName(data.data.opratorName);
          await this.confirmSwitchStation(xiexiaoName, opratorName);
        } else {
          await this.confirmSwitchStation(xiexiaoName, null);
        }
      } catch (err) {
        await this.confirmSwitchStation(xiexiaoName, null);
      }
    },
    async checkStudentPhone() {
      try {
        // 检查是否需要校验学生手机号
        const permissionParam = { busiType: 'studentPhoneCheck_jq' };
        const permissionResponse = await this.$http.post('/xsb/ability/businessLimit/h5QryBusiPermission', permissionParam);

        // 如果不需要校验，直接切换岗位
        if (permissionResponse.data.retCode === '0') {
            this.switchStation();
            return;
        }

        // 校验学生手机号
        const verifyParam = {
            regionId: this.uinfo.region,
            operId: this.uinfo.crmId,
            phone: this.relTelnum
        };
        const verifyResponse = await this.$http.post('/xsb/api-user/user/h5verifyStudentPhone', verifyParam);

        if (verifyResponse.data.retCode === '0') {
            this.switchStation();
        } else {
            this.$alert(verifyResponse.data.retMsg || '学生手机号校验失败');
        }
    } catch (error) {
        console.error('校验学生手机号时发生错误:', error);
        this.$alert('网络错误，请稍后重试');
    }
    },
    maskName(name) {
      if (!name) return '';
      return name.length > 2 ? name[0] + '*'.repeat(name.length - 2) + name.slice(-1) : '*' + name.slice(-1);
    },

    async confirmSwitchStation(xiexiaoName, opratorName) {
      const message = opratorName ? `当前输入的${xiexiaoName}【${this.relTelnum}】姓名是：${opratorName}，请确认是否继续？` : `当前输入的${xiexiaoName}【${this.relTelnum}】查询不到人员信息，请确认是否继续？`;
      const action = await this.$messagebox({
        title: '温馨提示',
        message,
        showConfirmButton: true,
        closeOnClickModal: false,
        showCancelButton: true,
        closeOnPressEscape: false,
        confirmButtonText: '是',
        cancelButtonText: '否',
      });
      if (action === 'confirm') {
        this.switchStation();
      } else {
        this.selectStationId = this.uinfo.stationId;
        this.selectStationName = this.uinfo.stationName;
      }
    },

    //调用服务端切换岗位
    switchStation(){
      let oldStationId = this.uinfo.stationId;
      let url = `/xsb/api-user/user/h5switchStation?stationId=${this.selectStationId}&imei=${this.uinfo.imei}`;
      this.$http.get(url).then((res) => {
        let data = res.data;
        if(data.retCode == '0'){
          //2023.10.27 cyy 如果原来是装维随销、装维随销（新）,代维信息清空
          if (oldStationId == '10000601' || oldStationId == '10009102') {
            this.uinfo.dwUser = '';
            this.uinfo.dwMobile = '';
            this.uinfo.dwName = '';
          }
          this.uinfo.stationId = this.selectStationId;
          this.uinfo.stationName = this.selectStationName;
          this.uinfo.crmId = data.data.crmId;
          this.uinfo.relTelnum = this.relTelnum;
          this.uinfo.viewInfoList = data.data.viewInfoList;
          Storage.session.set('userInfoNew',this.uinfo);
          Storage.session.set('userInfo',this.uinfo);
          //2025.04.24 wjx 增加调用客户端更新userInfo的岗位编码
          this.updateStationInfo(this.selectStationId)
          //2023.10.18 cyy 对于装维随销、装维随销（新）岗位，塞入装维人员信息
          if(this.uinfo.stationId == '10000601' || this.uinfo.stationId == '10009102'){
            this.qryZwPerson();
          }
          if(this.uinfo.stationId==this.CONSTVAL.STATION_BLD){//便利店业主跳到首页
            this.$router.push("/bldMaintain");
          }else if(this.uinfo.stationId==this.CONSTVAL.STATION_GB){//政企室经理
            this.$router.push("/adminGovernment");
          }else if(this.uinfo.stationId==this.CONSTVAL.STATION_AGB){//政企客户经理
            this.$router.push("/governmentIndex");
          }
          this.handleStationSwitch()

          /*保存切换的stationId*/
          this.$store.commit('stationChangeModule/rewriteStation',this.uinfo.stationId);
          //更新悬浮按钮权限
          this.$store.dispatch('floatIconModule/getMenuList');
          //更新home办理灵犀助手权限
          this.$store.dispatch('stationChangeModule/getHomeBtnMenuList');

          this.goSearchPage();
        }else{
          this.$alert(data.retMsg || '切换岗位服务异常');
        }
      })
    },
    updateStationInfo(newStationId){
      if(newStationId){
        ClientJs.updateStationId(newStationId);
      }else{
        console.log('未获取到最新岗位编码')
      }
    },
    // 当前岗位开关控制
    async handleStationSwitch() {
      const res = await this.$http.post('/xsb/api-user/menu/h5GetStationSwitch', { stationId: this.uinfo.stationId })
      if (res) {
        const data = res.data

        if (data && data.retCode == '0') {
          // 有权限
          Storage.session.set('stationSwitch', 1)
        } else {
          // 无权限
          Storage.session.set('stationSwitch', 0)
        }
      }
    },
    qryZwPerson() {
      let param = {
        xs_mobile: this.uinfo.servNumber,//行商手机号
        regionId: this.uinfo.region,//地市
      };
      let url = '/xsb/personBusiness/zwOrder/h5queryZwPerson';
      this.$http.post(url, param).then((res) => {
        if (res.data.retCode == '0') {
          if(res.data.data && res.data.data.zwryList &&  res.data.data.zwryList.length > 0) {
            this.uinfo.dwUser = res.data.data.zwryList[0]?res.data.data.zwryList[0].zw_id:'';
            this.uinfo.dwMobile = res.data.data.zwryList[0]?res.data.data.zwryList[0].zw_mobile:'';
            this.uinfo.dwName = res.data.data.zwryList[0]?res.data.data.zwryList[0].zw_name:'';
            Storage.session.set('userInfo',this.uinfo);
            Storage.session.set('userInfoNew',this.uinfo);
          }
        }
      }).catch((err) => {
      })
    },
    //跳转到瑞月的审批工单页面
    goJtopAppPage(flag){
      this.closeFloat();
      let workUrl = `http://jtop.js.cmcc:9015/jtop/uniflow/task/schAllFlow.do?flag=${flag}`
      this.tokenFromAld('jtyjDb', '-1', this.uinfo, '', `&url=${workUrl}`)
    },
    // 获取本月营销案点击次数
    async getMarketReminderClickCount() {
        if (!this.uinfo.crmId) return this.marketReminderMaxClickCount + 1;
        const hitsCount = await this.getMarketReminderHitsCount();
        return hitsCount;
    },
    // 判断是否展示营销案提醒
    async isMarketReminderShow() {
       const hitsCount = await this.getMarketReminderClickCount();
       // 本月点击次数大于5不展示
       if (hitsCount > this.marketReminderMaxClickCount) return false;
       if (this.gdFlg) return true;
       const remind = this.marketReminder;
       if (remind.currentMonthExpireCount == '0' && remind.nextMonthExpireCount == '0') {
           // 本月和次月营销案到期个数都为0不展示
           return false;
       }
       return true;
    },
    // 查询营销案提醒个数
    async queryMarketReminder() {
        const accessToken = await this.getAccessToken();
        if (!accessToken) return;
        console.info("accessToken:  " + accessToken);
        const param = {
            crmId: this.uinfo.crmId,
            region: this.uinfo.region,
            custManagerStaff: this.uinfo.crmId,
            authorization: 'bearer ' + accessToken
        }
        const url = `/xsb/chatTools/agent/h5MarketingReminder`
        const res = await this.$http.post(url, param)
        let { retCode, data } = res.data
        if (retCode == '0' && data) {
            this.marketReminder = {
                currentMonthExpireCount: data.currentMonthExpireCount || '0',
                nextMonthExpireCount: data.nextMonthExpireCount || '0'
            };
        }
    },
    // 打卡营销案页面
    async openMarketPage(flag) {
        const accessToken = await this.getAccessToken();
        if (!accessToken) return;
        const content = flag == '1' ? '查询我的当月营销案' : '查询我的次月营销案';
        this.openOtherPage({sceneId: '108'}, accessToken, content)
        const hitsCount = await this.getMarketReminderClickCount();
        if (hitsCount <= this.marketReminderMaxClickCount) {
            await this.addMarketReminderHitsCount(hitsCount + 1);
        }
    },
  },
  computed: {
    recommendIconList() {
      let recommendIconList = JSON.parse(JSON.stringify(this.$store.state.floatIconModule.recommendIconList))
      let lastItem = recommendIconList[recommendIconList.length - 1]
      // 添加一个查看更多
      if(lastItem){
        if(lastItem.length < 8){
          lastItem.push({sceneName:"查看更多",isMore:true})
        }else{
          this.$set(recommendIconList, recommendIconList.length, [{sceneName:"查看更多",isMore:true}])
        }
      }
      return recommendIconList
    },
    remindFlg(){
      for(let key in this.workReminder){
        if(this.workReminder[key] > 0){
          return true;
        }
      }
      return false;
    },
    gdFlg(){
      return encrypt(this.uinfo.servNumber) === '89C670F18BEF038E57670496B7877705'
    },
    // 是否更新过统计菜单
    isUpdateFlag(){
      return this.$store.state.floatIconModule.isUpdateFlag
    },
    // 菜单统计列表
    aiFloatHits(){
      return this.getTopThreeByType(this.$store.state.floatIconModule.menuCount.menuRecordList)
    }
  },
  async mounted() {
    this.uinfo = Storage.session.get('userInfo')
    const hitsCount = await this.getMarketReminderClickCount();
    if (hitsCount <= this.marketReminderMaxClickCount) {
        // 查询营销案提醒个数
        await this.queryMarketReminder();
        // 判断是否展示营销案提醒
        this.marketReminderShow = await this.isMarketReminderShow();
    }
    window['getAiAudioFun'] = (res) => {
       // 已经获取过权限了
       localStorage.setItem('getAiAudioFun', '1')
       console.info('执行了获取录音权限客户端')
       this.$refs.aiBottomRef.initReCom()
    }
  },
  watch:{
    preSetFlg(val) {
      if (!val && this.$refs.slideAi && this.$refs.slideAi.slide) {

        try {

          let x = this.$refs.slideAi.slide.currentPage.x;
          let dom = document.querySelector('.float-slide .slide-group');

          const computedStyle = window.getComputedStyle(dom);
          const transform = computedStyle.transform || computedStyle.webkitTransform;

          if (transform && transform.startsWith('matrix')) {
            const matrix = new DOMMatrix(transform);

            if (x !== matrix.e) {
              dom.style.transform = `translateX(${x}px)`;
            }
          }
        } catch (error) {

        }
      }
    },
    async show(val) {
      if (val) {
        // 打卡浮层判断是否展示营销案提醒
        this.marketReminderShow = await this.isMarketReminderShow();
        // 判断有没有获取过录音权限
        if (/android|harmony/gi.test(navigator.userAgent)) {
          let str1 = localStorage.getItem('getAiAudioFun')
          if (!str1) {
            console.info('获取录音权限')
            ClientJs.getAudioPermiss('getAiAudioFun')
          }
        } else {
          this.$refs.aiBottomRef.initReCom()
        }
      }
    }
  }
}
</script>

<style scoped lang='less'>
.float-box {
  width: 100vw;
  height: 100vh;
  position: relative;
}

.float-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  border-radius: 20px 20px 0 0;

  .close {
    width: 62px;
    height: 30px;
    background: rgba(62, 62, 62, 0.74);
    border-radius: 78px 78px 78px 78px;
    font-size: 13px;
    color: #FFFFFF;
    position: absolute;
    right: 20px;
    top: -40px;
    line-height: 30px;
    text-align: center;
    z-index: 8;

    em {
      font-size: 14px;
    }
  }

  .head {
    width: 100%;
    height: 95px;
    background: transparent url(../../../static/img/ai-0566310.png) no-repeat;
    background-size: 100% 100%;
    position: relative;
    border-radius: 18px 18px 0 0;

    .title-content {
      position: absolute;
      left: 160px;
      bottom: 19px;
      width: 184px;
      height: 53px;
      background: transparent url(../../../static/img/ai-qp.png) no-repeat;
      background-size: 100% 100%;
      padding-left: 37px;
      box-sizing: border-box;
      padding-top: 6px;


      p {
        font-size: 14px;
        color: #757575;
        line-height: 19px;
      }

      span {
        font-size: 14px;
        color: #141414;
        line-height: 22px;
        font-weight: bold;
      }
    }

    .speak {
      position: absolute;
      left: 0;
      bottom: 0;

      img{
        //width: 157px;
        position: absolute;
        bottom: 0;
        //left: 0;
        width: 130px;
        left: 10px;
      }
      p{
        background: linear-gradient(180deg, #007AFF 0%, #387BDF 100%);
        border-radius: 8px 8px 0px 0px;
        border-image: linear-gradient(176deg, rgba(255, 255, 255, 0.10000000149011612), rgba(255, 255, 255, 1), rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.28999999165534973), rgba(255, 255, 255, 0.3428803086280823), rgba(0, 0, 0, 0)) 1 1;
        position: absolute;
        white-space: nowrap;
        padding: 0px 8px;
        bottom: 0;
        left: 24px;
        color: #FFFFFF;
        line-height: 22px;
        font-size: 14px;
      }
    }
  }

  .tip-content {
    padding-top: 14px;
    background: linear-gradient(141deg, #DAE6F7 0%, #F1F3FE 100%);

    .tip-box{
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      padding: 0  14px 0 22px;

      .tip {
        box-shadow: 0px 4px 10px 0px rgba(0,0,0,0.06);
        display: flex;
        align-items: center;
        padding: 5px 11px;
        width: fit-content;
        background: linear-gradient(360deg, #FFFFFF 0%, #F4F4F4 99%);
        margin-bottom: 8px;
        margin-right: 8px;
        border-radius: 22px;
        font-size: 13px;
        line-height: 18px;
      }
      .color-1{
        color: #19AF6B
      }
      .color-2{
        color: #207CE1;
      }
      .isFixed{
        color: #262626;
      }
    }

    .remind{
      background: #FFE3E3;
      box-shadow: 0px 4px 10px 0px rgba(0,0,0,0.06);
      border-radius: 87px 87px 87px 87px;
      border-top: 1px solid #fff;
      font-size: 12px;
      color: #464242;
      line-height: 17px;
      margin: 4px 22px;
      padding: 7px 11px;

      span{
        font-weight: bold;
        color: #FC2424;
      }
    }

    .title-p {
      margin: 10px 19px 0;
      display: flex;
      align-items: center;
      justify-content: space-between;

      p {
        font-weight: bold;
        font-size: 16px;
        color: #141414;
        line-height: 22px;
      }

      > div {
        width: 86px;
        height: 25px;
        background: #FFFFFF;
        border-radius: 163px 163px 163px 163px;
        border: 1px solid #007AFF;
        font-size: 13px;
        color: #007AFF;
        line-height: 18px;
        display: flex;
        align-items: center;
        justify-content: center;

        em {
          color: #007AFF;
          font-size: 14px;
          margin-right: 2px;
        }
      }

    }

    .bs-slide {
      width: 100%;
      padding-bottom: 15px;

      /deep/ .dots{
        bottom: 0;
      }

      /deep/ .dot {
        width: 18px;
        height: 6px;
        background: rgba(255, 255, 255, 0.92);
        border-radius: 99px 99px 99px 99px;
        border: none;


        &.active {
          background: #007AFF;
          border: none;
        }
      }

      .baner-item-wrap {
        display: flex;
        flex-wrap: wrap;
        padding: 0 10px;

        li {
          width: 25%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          margin-top: 16px;

          > div {
            width: 60px;
            height: 60px;
            background: #fff;
            border-radius: 8px 8px 8px 8px;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          span {
            font-size: 12px;
            color: #585858;
            line-height: 17px;
            overflow: hidden;
            display: inline-block;
            width: 100%;
            text-overflow: ellipsis;
            white-space: nowrap;
            text-align: center;
          }
        }

        img {
          width: 30px;
          max-height: 30px;
        }
      }

      /deep/ .dot {
        width: 18px;
        height: 6px;
        background: rgba(255, 255, 255, 0.92);
        border-radius: 99px 99px 99px 99px;
        border: none;

        &.active {
          background: #007AFF;
        }
      }
    }

    /deep/ .mint-swipe {
      height: 220px;
    }

    /deep/ .mint-swipe-indicator {
      width: 18px;
      height: 6px;
      background: rgba(255, 255, 255, 0.92);
      border-radius: 99px 99px 99px 99px;
      opacity: 1;

      &.is-active {
        background: #007AFF;
      }
    }
  }

  .input-area {
    padding: 10px 21px 10px;
    position: relative;

    .foot-input {
      background-color: #fff;
      display: flex;
      align-items: flex-end;
      padding: 10px 15px 10px;
      border-radius: 24px;
      gap: 6px;
      position: relative;
      overflow: hidden;
      box-shadow: 0px 8px 3px 0px rgba(153, 153, 153, 0.08);


      .luyin {
        font-size: 20px;
        color: #444;
        transform: translateY(-5px);
      }

      textarea {
        flex: 1;
        border: none;
        resize: none;
        max-height: 100px;
        outline: none;
        line-height: 22px;
        padding: 4px 2px;
        box-sizing: border-box;
      }

      .send {
        width: 30px;
        height: 30px;
        background: rgb(240, 239, 247);
        border-radius: 50%;
        line-height: 27px;

        em {
          font-size: 22px;
          color: rgb(199, 198, 206);
          transform: rotate(-135deg);
          display: inline-block;
          margin-left: 5px;
        }
      }

    }

    .ai {
      display: flex;
      align-items: center;
      padding: 0 0 0 20px;
      gap: 6px;
      position: relative;
      box-shadow: 0px 8px 3px 0px rgba(153, 153, 153, 0.08);
      background: #287AEE;
      border-radius: 12px 12px 12px 12px;
      height: 50px;

      &::after {
        width: 1px;
        height: 22px;
        position: absolute;
        left: 67px;
        top: 14px;
        content: '';
        background: rgba(255, 255, 255, 0.28);;
      }

      .glass-bg {
        flex: 1;
      }

      .speak {
        width: 100%;
        height: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #fff;

        .speak-s{
          display: flex;
          align-items: center;
        }

        img {
          margin-right: 12px;
          width: 33px;
        }
      }

      img {
        width: 25px;
      }
    }

    .gif {
      max-width: 80%;
      position: fixed;
      left: 50%;
      bottom: 110px;
      transform: translateX(-50%);
      background: #FFFFFF;
      border-radius: 12px 12px 12px 12px;
      padding: 10px;
      font-size: 14px;
      color: #3D3D3D;
      line-height: 19px;
      z-index: 89;
      width: max-content;
    }

    .active {
      background-color: #1273E8 !important;

      em {
        color: #fff !important;
      }
    }
  }

  .input-area .ai .is-recording {
    position: fixed;
    width: 100%;
    left: 0;
    bottom: 0;
    right: 0;
    top: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 88;

    .speak-s{
      position: fixed;
      bottom: 30px;
      display: flex;
      align-items: center;
    }
  }

  .input-area .ai .isOutOfRange {
    background-color: #f50000;
  }
}

.btn-recording{
  width: 100%;
  height: 120px;
  border: none;
  position: absolute;
  bottom: 0;
  /* 使用圆锥渐变模拟漩涡效果，添加动画让其旋转 */
  background: conic-gradient(from 0deg at center, #4e72e9, #21eedc, #44b0d6);
  background-size: 200% 200%;
  animation: swirl 5s linear infinite;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  cursor: pointer;
  overflow: hidden;
  transition: all 0.3s ease;
  filter: blur(20px);
  border-radius: 150px 150px 0 0;

  &::before{
    /* 科技弥散效果 */
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0));
    opacity: 0;
    animation: diffuse 3s infinite;
    pointer-events: none;
  }
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.6;
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
}

@keyframes swirl {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 100% 100%;
  }
}

@keyframes diffuse {
  0% {
    opacity: 0;
    transform: rotate(0deg);
  }
  50% {
    opacity: 1;
    transform: rotate(180deg);
  }
  100% {
    opacity: 0;
    transform: rotate(360deg);
  }
}
</style>
<style>
.ai-toast-o-r,.screen-toast {
  z-index: 100001;
}
</style>
