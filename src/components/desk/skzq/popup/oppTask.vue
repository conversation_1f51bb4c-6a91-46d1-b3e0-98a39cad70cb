<!--商机任务弹框-->
<template>
  <div class='dialog-bcg' :style="{width:modal?'100vw':0}">
    <mt-popup v-model='modal' style='width: 80%;border-radius: 10px'>
      <div class='padding'>
        <div class='text-center relative'>
          <span class='text-bold text-xl text-3D3D3D'>商机任务</span>
          <span class='iconfont guanbi absolute' @click='onCancel'></span>
        </div>
        <ul class='menu-grid'>
          <li v-for='(item,index) in menuList' :key='index'
              class='menu-item'
              :class="`menu-item-${index}`"
              @click='onConfirm(item)'>
            <img class='menu-icon' :src='item.picUrl' alt=''>
            <div class='menu-priv-name menu-priv-title'>{{ item.privName }}</div>
            <div class='menu-priv-name menu-priv-remark'>{{ item.remark }}</div>
          </li>
        </ul>
      </div>
    </mt-popup>
  </div>

</template>
<script>
import { skipCommom } from 'components/desk/WorkStageNew/commonBusiCk.js'
import { menuChain } from '@/base/mixins/menuChainMixin'

export default {
  mixins: [skipCommom, menuChain],

  name: 'sceneChange',
  components: {},
  model: {
    prop: 'modal',
    event: 'change'
  },
  props: {
    // 控制弹框显示隐藏
    modal: {
      type: Boolean,
      default: false
    },
    // 选中列表
    checkList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      // 选项列表
      menuList: [
        {
          privId: '3625',
          picUrl: 'static/img/manage-logo.png',
          privName: 'TOB商机',
          opId: 'jtopApp',
          opParentid: 'pgop',
          remark: '集团维度'
        },
        {
          privId: '100545',
          picUrl: 'static/img/person-logo.png',
          privName: 'TOC商机',
          opId: 'fsop',
          opParentid: 'fsop',
          remark: '个人维度'
        }
      ]
    }
  },
  watch: {
    modal(val) {
      this.$emit('change', val)
    }
  },
  methods: {
    // 关闭弹框
    onCancel() {
      this.$emit('change', false)
    },
    // 确认
    onConfirm(item) {
      this.updateMenuChain(item.privId)
      this.goBusinessPage(item)
    }
  }
}
</script>


<style scoped lang='less'>
@import "../../../../base/less/public";

.dialog-bcg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  //backdrop-filter: blur(3px);
  z-index: 999;
  overflow: hidden;
}

/deep/ .v-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1001;
  overflow: hidden;
  background-color: rgba(0, 0, 0, .4);
}

.absolute {
  position: absolute;
  right: 0;
  top: -2px;
}

// 菜单网格布局
.menu-grid {
  padding-top: 16px;
  padding-bottom: 8px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  justify-content: space-around;
  gap: 16px;
}

// 菜单项基础样式
.menu-item {
  width: 100%;
  aspect-ratio: 1/1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.10);
  border-radius: 10px;
  padding: 0;
  cursor: pointer;
  transition: box-shadow 0.2s;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  // 第一个菜单项 - 橙色背景
  &.menu-item-0 {
    background: rgb(252, 244, 239);
  }

  // 第二个菜单项 - 蓝色背景
  &.menu-item-1 {
    background: rgb(239, 245, 253);
  }
}

// 菜单图标
.menu-icon {
  width: 60%;
  height: auto;
  max-height: 60%;
  object-fit: contain;
}

// 菜单权限名称基础样式
.menu-priv-name {
  text-align: center;
  font-family: unset;
}

// 菜单标题样式（第一个menu-priv-name）
.menu-priv-title {
  margin-top: 8px;
  font-weight: 500;
  font-size: 17px;
  color: #203a5a;
}

// 菜单备注样式（第二个menu-priv-name）
.menu-priv-remark {
  margin-top: 6px;
  font-size: 12px;
  color: #8fb2e7;
}

// 为不同位置的li设置不同的menu-priv-name颜色
.menu-item-0 {
  .menu-priv-title {
    color: #7c2d12; // 第一个li的标题颜色 - 深橙色（配橙色背景）
  }

  .menu-priv-remark {
    color: #f6ad55; // 第一个li的备注颜色 - 亮橙色
  }
}

.menu-item-1 {
  .menu-priv-title {
    color: #1a365d; // 第二个li的标题颜色 - 深蓝色（配蓝色背景）
  }

  .menu-priv-remark {
    color: #4299e1; // 第二个li的备注颜色 - 亮蓝色
  }
}
</style>
