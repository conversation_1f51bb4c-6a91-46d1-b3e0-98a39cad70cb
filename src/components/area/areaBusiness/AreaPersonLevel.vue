<template>
  <div class="wrapper">
    <Header :tsTitleTxt="titleTxt"></Header>
    <div class="content-wrap">
      <div class="main-prod" v-show="orderMProdList.length>0">
        <div class="mprod-title" @click="clickFlag = !clickFlag">
          <span class="icon-dinggou iconfont dinggoudan"></span>
          <h4 class="mprod-title-text">已订购主体套餐</h4>
          <span class="iconfont jt"
                :class="[clickFlag ? 'jiantouxiangshang' : 'jiantou']"></span>
        </div>
        <ul class="main-order" v-show="!clickFlag">
          <li class="main-item" v-for="(item,index) in orderMProdList" :key="index">
            <div class="item-box">
              <div class="main-prod-name">{{item.zhutiname}}</div>
              <div class="mprod-name">
                <span class="mprod-name-label">资费</span>
                <span class="mprod-name-text">{{item.offername}}</span>
              </div>
              <div class="mprod-time">
                <span>有效期：</span>
                <span>{{item.effectdate}}至{{item.expiredate}}</span>
              </div>
            </div>
          </li>
        </ul>
      </div>
      <div class="top-msg"><i class="iconfont taocan"></i>{{actName}}</div>
      <ul class="ul-wrapper" v-if="levelList && levelList.length > 0">
        <li class="li-item"
            :key="idx"
            @click="goRewardPage(item)"
            v-for="(item,idx) in levelList">
          <p class="txt">
            {{item.levelname}}
            <span>{{item.levelid}}</span>
          </p>
          <i class="iconfont arrow jiantou-copy-copy"></i>
        </li>
      </ul>
      <div class="ul-wrapper" v-if="!loading && levelList.length <= 0">
        <NoDataPage tipTxt="该批次下暂无档次"></NoDataPage>
      </div>
    </div>
    <div class="op-button-box wrapper-medias">
      <button class="op-button" @click="goBack">上一步</button>
    </div>
  </div>
</template>

<script>
  import Header from 'components/common/Header.vue'
  import NoDataPage from 'components/common/NoDataPage.vue'
  import Storage from '@/base/storage'
  export default {
    components:{Header,NoDataPage},
    props:{},
    data(){
      return {
        actId:'',//批次编号
        actName:'',//批次名称
        levelList:[],//档次列表
        loading:false,//是否正在加载中
        srcFrom:'',
        orderMProdList: [], //已订购的主体套餐
        clickFlag:true,//false按钮向下
      }
    },
    methods:{
      qryLevel(){
        this.loading = true;
        let url = `/xsb/personBusiness/personMarket/h5getLevelList?actId=${this.actId}`;
        this.$http.get(url).then(res => {
          if(res.data.retCode == '0'){
            this.loading = false;
            this.levelList = res.data.data;
          } else {
            this.$alert(res.data.retMsg || '查询批次失败');
          }
        })
      },
      //跳奖品页面
      goRewardPage(item){
        let marketType = this.$route.query.marketType;//营销案类型
        let path = '/personReward';
        if(marketType == '3'){//终端营销案
          path = '/zhongduanReward';
          this.$router.push({
            path,
            query:{
              actId: this.actId,
              actName: this.actName,
              levelId:item.levelid,
              levelName:item.levelname,
              srcFrom:this.srcFrom
            }
          })
        } else {
          let levelId = item.levelid;//档次编码
          let levelName = item.levelname;//档次名称
          let needSub=item.enterspenumber;//需要副号
          let actId = this.actId;
          let actName = this.actName;
          let effDate = '';//生效时间
          this.goPersonReward(path,actId,actName,levelId,levelName,effDate,item.iscashacount,needSub);
        }
        //回滚 20200212
        // } else {
        //   //营销案是否可接续查询 20200205
        //   let url = '/xsb/personBusiness/personMarket/h5qryLevelEffdate';
        //   let jqData = Storage.session.get('jqData');
        //   let telnum = jqData && jqData.telnum;//手机号
        //   let levelId = item.levelid;//档次编码
        //   let levelName = item.levelname;//档次名称
        //   let actId = this.actId;
        //   let actName = this.actName;
        //   let effDate = '';//生效时间
        //   url += `?telnum=${telnum}&levelId=${levelId}`;
        //   this.$http.get(url).then(res=>{
        //     return res;
        //   }).then(levelRes => {
        //     let {retCode,data} = levelRes.data;
        //     if(retCode === '0'){//接口返回正常
        //       let canRenew = data.canRenew;//是否可以接续 1可以，0不可以
        //       //TODO
        //       canRenew = '0';
        //       if(canRenew == '1'){//可以接续
        //         levelId = data.newLevelId;//新的批次编码
        //         levelName = data.newLevelName;//新的批次名称
        //         effDate = data.effDate;//生效时间
        //         actName = data.newActName || this.actName;
        //         actId = data.newActId || this.actId;
        //       }
        //     }
        //     this.goPersonReward(path,actId,actName,levelId,levelName,effDate);
        //   }).catch(err =>{
        //     //接口异常 就当作不可接续的营销案正常走
        //     this.goPersonReward(path,actId,actNamelevelId,levelName,effDate);
        //   })
        // }
      },
      //跳转到个人营销案的奖品页面
      goPersonReward(path,actId,actName,levelId,levelName,effDate,iscashacount,needSub){
        this.$router.push({
          path,
          query:{
            actId: actId,
            actName: actName,
            levelId:levelId,//批次编码
            levelName:levelName,//批次名称
            effDate:effDate,//生效时间
            isCashaCount:iscashacount,//是否支持话费支付，1支持 0不支持 add by qhuang 20200421
            orderMProdList: JSON.stringify(this.orderMProdList),
            needSub:needSub,//副号
            srcFrom:this.srcFrom
          }
        })
      },
      goBack(){
        this.$router.back();
      }
    },
    created(){
      if(this.$route.query.orderMProdList) {
        this.orderMProdList = JSON.parse(this.$route.query.orderMProdList);
      }
      let qry = this.$route.query;
      this.actId = qry.actId;
      this.actName = qry.actName;
      this.srcFrom= qry.srcFrom;
      let jqData  = {
        authtype: '',
        jqType: '',
        result: '0',
        telnum: qry.serverNumber,
        userName: ''
      };
      Storage.session.set('jqData',jqData);
      this.qryLevel();
    },
    computed:{
      titleTxt(){
        let marketType = this.$route.query.marketType;//营销案类型
        console.info(this.$route.query);
        if(marketType == '3'){
          return '终端营销案';
        }
        return '个人营销案';
      },
    }
  }
</script>
<style lang="less" scoped>
  .content-wrap{
    margin-top:44px;
    border-top:1px solid #EAEAEA;
    background-color:#fff;
    margin-bottom:76px;
    .top-msg{
      height:48px;
      font-size:14px;
      line-height:48px;
      color:#409758;
      position:fixed;
      //top:44px;
      padding-left: 12px;
      width: 100%;
      background:#fff;
      z-index:99;
      box-shadow:3px 3px 14px 0px rgba(215,215,215,0.5);
    }
    .ul-wrapper{
      margin-top:48px;
      width:100%;
      .li-item{
        height:54px;
        display:table;
        position:relative;
        width:100%;
        box-sizing: border-box;
        padding: 0 12px;
        border-bottom:1px solid #F0F0F0;
        .txt{
          display:table-cell;
          vertical-align:middle;
          line-height:20px;
          color:#434343;
          font-size: 14px;
          padding-right: 18px;
          span{
            color:#727272;
            font-size:12px;
            display:block;
          }
        }

        .arrow{
          position:absolute;
          right:12px;
          top:50%;
          color:#C7C7CC;
          transform: translateY(-50%);
        }
      }
    }
  }
  .op-button-box {
    background-color: white;
    width: 100%;
    margin-top: 12px;
    text-align: center;
    line-height: 76px;
    position: fixed;
    bottom: 0px;
    padding: 0 12px;
    box-sizing: border-box;
    .op-button {
      background: #1681FB;
      border-radius: 22px;
      height: 44px;
      width: 100%;
      font-size: 14px;
      color: #FFFFFF;
      outline: none;
      border: none;
    }
  }
  /*包含已订购信息*/
  .main-prod {
    background: #fff;
    border-bottom: 4px solid #EAEAEA;
    box-shadow: 3px 3px 14px 0px rgba(215, 215, 215, 0.5);
  }
  .mprod-title {
    padding: 8px 12px;
    border-top: 1px solid #EBEBEB;
    border-bottom: 1px solid #EBEBEB;
    position:relative;
  }
  .icon-dinggou {
    color:#007aff;
    height:22px;
    line-height:22px;
    font-size:20px;
    vertical-align: middle;
  }
  .mprod-title-text {
    display: inline-block;
    height:22px;
    font-size:16px;
    color:rgba(67,67,67,1);
    line-height:22px;
    vertical-align: middle;
  }
  .main-order {
    padding: 0 12px 12px 12px;
  }
  .main-item {
    padding: 1px;
    box-shadow:0px 4px 8px 0px rgba(227,227,227,0.5);
    border-radius:8px;
    margin-top: 12px;
    border-image:linear-gradient(135deg, rgba(43,115,202,1), rgba(174,195,222,1)) 1 1;
    background:linear-gradient(136deg,rgba(3,128,255,1) 0%,rgba(19,204,238,1) 100%) rgba(255,255,255,1);
  }
  .item-box {
    padding: 11px;
    border-radius:8px;
    background: #fff;
  }
  .main-prod-name {
    height:22px;
    font-size:16px;
    font-weight:450;
    color:rgba(67,67,67,1);
    line-height:22px;
  }
  .mprod-name {
    font-size:14px;
    font-weight:400;
    color:#505050;
    line-height:22px;
    display: flex;
    flex-direction: row;

  }
  .mprod-name-label {
    font-weight:400;
    color:rgba(255,255,255,1);
    flex-grow: 0;
    flex-shrink: 0;
    background: rgba(245,166,35,1);
    border-radius: 4px;
    text-align: center;
    margin-right: 6px;
    padding: 0 4px;
    height: 22px;
    line-height: 22px;
    display: block;
    font-size: 12px;
  }
  .mprod-name-text {
    font-size:14px;
    font-weight:400;
    color:rgba(67,67,67,1);
    flex-grow: 1;
    flex-shrink: 1;
    padding: 0px 4px;
  }
  .mprod-time {
    height:20px;
    font-size:14px;
    font-weight:400;
    color:rgba(119,119,119,1);
    line-height:20px;
  }
  .jt {
    color: #1680F9;
    position: absolute;
    right: 30px;
    top:13px;
  }
</style>
