<template>
  <div>
    <Header tsTitleTxt="阿拉盯模拟拉起外围页面" backType="custom" @emGoPrev="goPrev"></Header>
    <div class="content-div">

      <h2>{{ doingSth }}</h2>
      <h4>地市专区</h4>
      <button @click="showAllCityList('cityOpList')">点击拉起地市专区</button>
      <br>
      <h4>地市报表专区</h4>
      <button @click="showAllCityList('cityReportOpList')">点击拉起地市报表专区</button>
      <br>
      <h4>掌上家客</h4>
      <button @click="goFrmPage('jiake','')">点击拉起掌上家客</button>
      <br>
      <h4>其它渠道</h4>
      <input class="op-ipt" placeholder="请输入opId" v-model="otherOp"/>
      <button @click="goFrmPage(otherOp,'')">点击其它外围</button>
      <button @click="showAllCityList('otherOpList')">点击选择其它外围</button>
      <br>
      


    </div>
  </div>
</template>
<script>

import Header from 'components/common/Header.vue'
import NlDropdown from 'components/common/NlDropdown/dropdown.js'
//地市专区opId
const cityOpList = [
  {id:'ntgrid',label:'南通地市专区'},
  {id:'lygApp',label:'连云港地市专区'},
  {id:'yzApp',label:'扬州地市专区'},
  {id:'sqApp',label:'宿迁地市专区'},
  {id:'zjApp',label:'镇江地市专区',title:'地市专区'},
  {id:'tzApp',label:'泰州地市专区'},
  {id:'wxImpRpt',label:'无锡地市专区'},
  {id:'czApp',label:'常州地市专区',title:''},
  {id:'haydApp',label:'淮安地市专区'},
  {id:'njapp',label:'南京地市专区'},
  {id:'ycApp',label:'盐城地市专区'},
  {id:'poineer',label:'苏州地市专区'},
  {id:'tzApp',label:'泰州地市专区'},
]

//地市报表专区opId
const cityReportOpList = [
  {id:'zjappkh',label:'镇江地市报表专区'},
  {id:'czappkh',label:'常州地市报表专区'},
  {id:'xzappkh',label:'徐州地市报表专区'},
  {id:'sqappkh',label:'宿迁地市报表专区'},
  {id:'wxappkh',label:'无锡地市报表专区'},
  {id:'ycappkh',label:'盐城地市报表专区'},
  {id:'lygappkh',label:'连云港地市报表专区'},
  {id:'yzappkh',label:'扬州地市报表专区'},
  {id:'szappkh',label:'苏州地市报表专区'},
  {id:'haappkh',label:'淮安地市报表专区'},
  {id:'tzappkh',label:'泰州地市报表专区'},
]

const otherOpList =[
  {id:'dms',label:'dms爱订货'},
  {id:'jike',label:'集客APP'},
  {id:'jsisi',label:'麒麟平台对接产品推荐'},
  {id:'qilin',label:'麒麟平台'},
]
export default {
  components: { Header },
  data() {
    return {
      data: {},
      doingSth:'',//正在拉起某个页面
      needParam:''
    }
  },
  methods: {
    goPrev() {
      history.go(-1)
    },
    goFrmPage(opSrc,pTitle){
      if(!opSrc) return;
      this.$router.push({
            path: 'gateWayBox',
            query: {
              opSrc:opSrc,
              hasBottom:0,
              pTitle:pTitle,
            }
      })
    },
    //地市专区列表或者地市报表专区列表
    showAllCityList(dataListName) {
      let dataList = [];
      switch (dataListName) {
        case 'cityOpList':
          dataList = cityOpList;
          break;
        case 'cityReportOpList':
          dataList = cityReportOpList;
          break;
        default:
          dataList = otherOpList;
          break;
      }
      
      NlDropdown({
        confirmBtn: false,
        datalist: dataList
      }, (retVal) => {
        this.doingSth = '正在拉起页面：' + retVal.label;
        this.goFrmPage(retVal.id,retVal.title)
      });
    }
  },
  created() {
    
  },
  mounted() {


  },
}
</script>
<style lang="less" scoped>

.content-div {
  margin-top: 50px;
  height: calc(100vh - 200px);
  overflow: scroll;
  padding:10px 16px 200px 16px;

  button{
    border: 0;
    background-color: #c194ea;
    color: white;
    border-radius: 4px;
    height: 30px;
    line-height: 1;
    padding:0 20px;
    margin:8px 10px;
  }
  .op-ipt{
    border: 0;
    margin:8px 10px;
    padding:0 20px;
    height: 30px;
    line-height: 1;
    border-radius: 4px;
    width:60vw;
  }
}
</style>
