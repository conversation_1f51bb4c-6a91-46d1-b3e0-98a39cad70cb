<template>
  <div>
    <Header tsTitleTxt="模拟拉起阿拉盯页面"  backType="custom" @emGoPrev="goPrev"></Header>
    <div class="content">
      <div>路径：{{ipUrl}}</div>
      <div class="biu" @click="go()">集团建档</div>
    </div>
  </div>
</template>
<script>
  import Header from "components/common/Header.vue";
  import ClientJs from '@/base/clientjs.js';
  import {BASE64} from "../../../base/coding";



  export default {
    components: { Header},
    data() {
      return {
        data:{},
        ipUrl:'',
        array:[]
      }
    },
    methods: {
      goPrev(){
        history.go(-1);
      },
      go(){
        let token='/cDIvmEZK4zmQ/L2lZ0gM7Ii5yNC0Iz4k1pA+8nYoB0bKtOWohnCmNu3eLiToCwBJfHqc8GbjJGppxNxl8LmkA=='
        token=encodeURIComponent(token)
        let entname="新大陆"
        entname=encodeURIComponent(entname)
        let url = `${this.ipUrl}/xsbh5/index.html#/transferPage?gobackFlag=webview&TOKEN=${token}&ENTNAME=${entname}&UNISCID=111`;
        ClientJs.openWebKit(url, '阿拉盯', '1', '0', '', '', '', '', '', '', '')
         // window.location.href = url;
      },
    },
    created(){
      this.ipUrl = 'http://*************:8080'
      ClientJs.getSysInfo('getUserInfoCB');
    },
    mounted(){
      window['getUserInfoCB'] = (result) => {
        let res = result.userInfo;
        let uinfo = JSON.parse(res);
        initTokenAfterBack(this.$http,uinfo);
        this.ipUrl = result.serverUrl;
      }
    }
  }
</script>
<style lang="less" scoped>
  .biu{
    margin-top: 20px;
  }
  .content{
    margin-top: 50px;
  }
</style>
