import { BASE64 } from '@/base/coding'
import ClientJs from '@/base/clientjs.js'
import { skipCommom } from 'components/desk/WorkStageNew/commonBusiCk.js'
import Storage from '@/base/storage.js'
import { decrptParam } from '@/base/encrptH5.js'
import { iEncrpt, iEncrptParamMap } from '@/base/encrptH5.js'
import { Indicator } from 'mint-ui'
import { dateFormat } from '@/base/utils'
import {menuLogRecord} from '@/base/request/commonReq.js'
import { menuChain } from '@/base/mixins/menuChainMixin'
import { oneClickOrderJs } from 'components/area/areaBusiness/oneClickOrder.js'

export const commonPageJs = {
  mixins: [skipCommom, menuChain, oneClickOrderJs],
  data() {
    return {
      body: '',
      ipUrl: '',
      params: '',
      uinfo: '',
      levelList: [],
      currentPage:'',
    }
  },
  created() {
    //登录人信息
    this.uinfo = Storage.session.get('userInfo')
    // 查询有没有权限接口
  },
  methods: {
    async goPage() {
      // 菜单点击日志采集
      // 更新菜单链
      this.updateMenuChain('common-page', 1)
      // 更新菜单链
      this.updateMenuChain(this.body && this.body.menuId || 'undefined', 2)

      let menuParam = {
        stationId: this.uinfo.stationId,
        serverNumber: this.uinfo.servNumber,
        clickTime: dateFormat(new Date(), ('yyyy-MM-dd hh:mm:ss')),
        source: this.body && this.body.sourceSys || 'other'
      }
      menuLogRecord(menuParam)

      let data = this.body
      let param0 = {
        switchType: 'jianquan_laqi'
      }
      this.$http.post('/xsb/personBusiness/gridSand/h5GridMarketSwitch', param0).then((res0) => {
        if (res0.data.retCode == '0') {//成功的情况
          let param1 = {
            'crmStatus': this.uinfo.crmStatus,
            'crmId': this.uinfo.crmId,
            'region': this.uinfo.region,
            'servNumber': this.uinfo.servNumber,
            'stationId': this.uinfo.stationId,
            'subMenuId': data.menuId
          }
          let url1 = `/xsb/ability/submenuAuthen/h5SubMenuAuthen`
          this.$http.post(url1, param1).then(async (res1) => {
            console.log('param1', param1)
            console.log('res1', res1)
            if (res1.data.retCode == '0') {
              console.log(data)
              let flag = await this.qryAccessAuth(data)
              if(flag){
                  this.goPageContinue(res1, true)
              }
            } else {
              this.$messagebox.alert(res1.data.retMsg, '温馨提示').then((res) => {
                ClientJs.closeCallBack()
              })
            }
          })

        } else {
          this.goPageContinue(null, false)
        }
      })

    },
    //应用权限校验
    qryAccessAuth(param) {
        let openParam = {
            busiName: 'auth-enabled',
            busiKey: 'accessKey',
            defaultRegionValue: ''
        }
        return this.$http.post('/xsb/gridCenter/groupBroadBand/h5getBusiTypeZkFlag', openParam).then(res => {
            let { retCode, retMsg } = res.data
            if (retCode == '0') {
                let url = `/xsb/ability/accessAuth/h5qryAccessAuth`
                return this.$http.post(url, param).then(res => {
                    let { retCode, retMsg } = res.data
                    if (retCode == '0') {
                        console.log('当前应用appId访问网格通系统权限获取成功：' + retMsg)
                        return true
                    } else {
                        this.$alert(retMsg || `当前应用appId访问网格通系统权限获取失败`)
                        return false
                    }
                }).catch(res => {
                    this.$alert('集团客户创建网络异常:' + res)
                    return false
                })
            } else {
                console.log('当前应用appId访问网格通系统权限获取成功：认证总开关关闭')
                return true
            }
        })
    },
    async goPageContinue(res1, flag) {
      let data = this.body
      let route = ''
      if (flag) {
        route = res1.data.data
      } else {
        route = data.pageRoute
      }
      let param = ''
      if (data.pageType == '3') {
        if (data.menuId == '4952' || data.menuId == '4953' || data.menuId == '4956' || data.menuId == '4957' || data.menuId == '4958') {
          let targetCenter = await this.getCrmTargetCenter()
          this.$set(data.paramMap,'targetCenter',targetCenter)
          this.skipOutPage(data)
          return
        }else{
          this.skipOutPage(data)
          return
        }
      }
      //若页面类型为2(具体受理页面)，则解析paramMap参数
      if (data.pageType == '2' || data.pageType == '4') {
        let paramMap = data.paramMap
        //若具体受理页面与父页面不一致，以子页面为跳转路由
        if (paramMap.childPageRoute && route.replace(/\//g, '') != paramMap.childPageRoute) {
          route = paramMap.childPageRoute
        }
        switch (data.menuId) {
          case '100114':
            route = '/promotionReward'
            param = `&serverNumber=${paramMap.serverNumber}&promotionId=${paramMap.promotionId}`
            break
          case '100014':
            route = '/AreaPersonLevel'
            param = `&serverNumber=${paramMap.serverNumber}&actId=${paramMap.actId}&actName=${paramMap.actName}`
            break
          case '100235':
            param = `&param=${JSON.stringify(paramMap).trim()}`
            break
          case '100431':
            route = '/bcvnetadd'
            param = `&msisdn=${paramMap.custPhone}&handleMode=0&type=01`
            break
          case '100236':
            this.validNumber(paramMap.serverNumber) //去调用接口查号码信息
            param = false
            break
          case 'other1005':
            this.getType(paramMap.knowledgeNo)
            param = false
            break
          case '100502':
            this.initOneClickOrderInfo(data)
            param = false
            break
          default:
            route = route.startsWith('/') ? route : `/${route}`
            let keys = Object.keys(paramMap)
            param = '&' + keys.map((key) => `${key}=${paramMap[key]}`).join('&')
            break
        }

        let top17List = ['khulzs1008', '100012', '100227', '100041', '100013', '100061', '100020', '100047', '100017', '100226', '100016', 'khulzs1009']
        console.info('111', top17List.indexOf(data.menuId))
        if (top17List.indexOf(data.menuId) > -1 && paramMap.serverNumber) {
          Storage.session.remove('jqData')
          await this.qryCgetusercust(paramMap.serverNumber, data.menuId)
        }
        console.info('sssss')

      }
      if (param === false) {
        return
      }
      //入网业务需要查看当前操作员是否做过身份证认证
      let enterNetAuthLilt = ['10019', '100026', '100042', '100044', '100139']
      let enterNetAuthRouters = ['/yuPeiHao', '/realNameCertify?srcFlag=simcard', '/carryingApply', '/carryingList', '/grabSheetNew?doGradSheetRefresh=1']
      let privName = ['预配号入网', '选号入网', '携号转网', '携号转网工单', '电渠抢单']
      if (enterNetAuthLilt.indexOf(data.menuId) > -1 || enterNetAuthRouters.indexOf(route) > -1) {
        let item = {}
        for (let i = 0; i < enterNetAuthRouters.length; i++) {
          if (enterNetAuthRouters[i].indexOf(route) > -1) {
            item = {
              'privName': privName[i],
              'isNewFeature': 0,
              'hasCrmid': '1',
              'hasPwd': '0',
              'opId': 'fsop',
              'opParentid': 'fsop',
              'remark': 'ald_web:2023/03/15',
              'sort': 3,
              'jqDateInfo': null,
              'parentId': '20000125',
              'authenType': -1,
              'levelId': 3,
              'featureType': null,
              'busiType': null,
              'privId': enterNetAuthLilt[i],
              'picId': '100383',
              'isHot': null
            }
            this.checkEnterNet(item)
            return
          }
        }
        return
      }

      let url = ''
      //subwgt页面理由
      if (data.pageType == '4') {
        document.addEventListener('visibilitychange', function() {
          let vState = document.visibilityState
          console.log(vState)
          if (vState === 'hidden') {  // 当页面由前端运行在后端时，出发此代码
            console.log('我被隐藏了')
          }
          if (vState === 'visible') {   // 当页面由隐藏至显示时
            ClientJs.closeCallBack()
          }
        })
        //生产地址
        url = `${this.ipUrl}/xsbh5/subWgt/index.html#${route}?srcFrom=webview&gobackFlag=webview&source=region` + param//子项目地址
        //测试地址
        // url = `http://**************:5173/child/wgt/#${route}?srcFrom=webview&gobackFlag=webview&source=region`+ param
        ClientJs.openWebKitNew(url, {})

      } else {
        //生产地址
        // url = `${this.ipUrl}/xsbh5/index.html#${route}?srcFrom=webview&gobackFlag=webview&source=region` + param
        //测试地址
        url = `${this.ipUrl}/xsbh5.html#${route}?srcFrom=webview&gobackFlag=webview&source=region` + param
        Indicator.close();

        window.location.href = url
      }
    },

    async qryCgetusercust(telnum, menuId) {
      let url = '/xsb/personBusiness/customerView/h5QryCgetusercust'
      await this.$http.post(url, { telnum: telnum }).then(res => {
        let resData = res.data
        if (typeof (resData) != 'object') {//不是对象的话就是加密串
          resData = decrptParam(res.data)
          resData = JSON.parse(resData)
        }
        let { retCode, data } = resData
        if (retCode == '0') {
          let realstatecode = data.realstatecode
          //港澳台胞证的
          let certType2List = ['HKMCPassport', 'DriverIC', 'Passport', 'StudentID', 'TaiBaoZheng', 'UnionSocietyCredit']
          let flag2 = certType2List.indexOf(data.certType)//判断证件类型
          //需要实名制审核通过
          if ((realstatecode === '4') || (realstatecode === '1' && flag2 > -1)) {
            let obj = {
              jqType: '0',   //0:服务密码，1:验证码，2:身份证
              result: '0',             //鉴权结果，1为成功
              authtype: '',
              userName: data.userName,
              telnum: telnum,
              idCardWay: true,
              userCity: data.userCity,//用户地市
              userId: data.user_id //用户标识
            }
            Storage.session.set('jqData', obj)
            console.info('qryCgetusercust', data)
            if (menuId == '100012') {
              this.checkBandTv(obj.telnum, menuId, 'band')
            }
            if (menuId == '100013') {
              this.checkBandTv(obj.telnum, menuId, 'tv')
            }
          } else {
            this.$messagebox.alert('该号码状态是未审核，请使用实名制后再进行业务受理', '温馨提示').then((res) => {
              ClientJs.closeCallBack()
            })
          }
        } else {
          this.$messagebox.alert('暂无此号码', '温馨提示').then((res) => {
            ClientJs.closeCallBack()
          })
        }
      })
    },
    getType(knowledgeNo) {
      let url = `/xsb/personBusiness/studyCenter/h5GetTextType`
      this.$http.get(url)
        .then((res) => {
          if (res.data.retCode == '0') {
            let levelAll = res.data.data

            for (let i = 0; i < levelAll.length; i++) {
              if (levelAll[i].typeid == '9900004') {
                let url2 = '/xsb/personBusiness/studyCenter/h5QryRealTimeKnowledgeType'
                let params2 = {
                  operatorMobile: this.uinfo.servNumber
                }
                this.$http.post(url2, params2).then((res2) => {
                  let typeLevel2 = levelAll.filter(items => {
                    return items.typelevel == '2' && items.parentid == '9900004'
                  })
                  this.levelList = typeLevel2
                  console.log(this.levelList)
                  this.ALLtabs = JSON.parse(JSON.stringify(typeLevel2))
                  for (let j = 0; j < this.levelList.length; j++) {
                    this.getVideo(this.levelList[j].typeid, j, knowledgeNo)
                  }
                })
              }
            }
          } else {
            this.$alert('查询类别失败,' + res.data.retMsg)
          }
        })
    },
    getVideo(val, idx, knowledgeNo) {
      let url = `/xsb/personBusiness/studyCenter/h5QryFiveKnow`
      let param = {
        'typeCode': val,
        'servNumbe': this.uinfo.servNumber,
        'pageNum': 1,
        'pageSize': 3,
        'sortType': 1,
        'qryText': this.text
      }
      this.$http.post(url, param).then(res => {
        if (res.data.retCode == '0') {
          let tempData = res.data.data.pageData
          for (let i = 0; i < tempData.length; i++) {
            if (tempData[i].knowledgeId == knowledgeNo) {
              for (let j = 0; j < tempData[i].fileName.split(',').length; j++) {
                if (tempData[i].fileName.split(',')[j].indexOf('5G图片') >= 0) {
                  let imgUrl = tempData[i].fileUrl.split(',')[j]
                  let geturl = Storage.get('webUrl') + iEncrpt('/xsb/gridCenter/servlet/h5imageShowServletNew?comeFrom=fiveKnowInfo&attachId=' + imgUrl)
                  tempData[i].img5GUrl = geturl
                }
                if (tempData[i].fileName.split(',')[j].indexOf('5G视频') >= 0) {
                  let videoUrl = tempData[i].fileUrl.split(',')[j]
                  let geturl = Storage.get('webUrl') + iEncrpt('/xsb/gridCenter/servlet/h5imageShowServletNew?comeFrom=fiveKnowInfo&attachId=' + videoUrl)
                  tempData[i].video5GUrl = geturl
                }
              }
              this.goVideoDetail(tempData[i])
            }
          }
          this.levelList[idx].children = tempData
          this.newList = JSON.parse(JSON.stringify(this.levelList))

        } else {
          this.$alert(res.data.retMsg)
        }
      })
    },
    goVideoDetail(item) {
      this.$router.push({
        path: '5GVideoDetail',
        query: {
          tiaozhuan: item.route,
          knowledgeId: item.knowledgeId,
          title: item.title,
          releaserName: item.releaseName,
          releaseTime: item.releaseTime,
          content: item.content,
          videoUrl: item.video5GUrl,
          img5GUrl: item.img5GUrl,
          fileUrl: item.fileUrl,
          fileName: item.fileName,
          readNumber: item.readNumber,
          likeNumber: item.likeNumber,
          likeFlag: item.likeFlag,
          typeCode: item.typeCode,
          commentNumber: item.commentNumber,
          giveGoodFlag: item.giveGoodFlag,
          giveGoodNumber: item.giveGoodNumber,
          srcFrom: 'webview',
          gobackFlag: 'webview',
          authorNames: item.authorNames,
          anthorYxdy: item.anthorYxdy,
          authorArea: item.authorArea
        }
      })
    },
    appendToUrl(baseUrl, params) {
      const queryParams = Object.keys(params)
        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
        .join('&')
      return `${baseUrl}?${queryParams}`
    },
    skipOutPage(param) {
      let client = /android/gi.test(navigator.appVersion) ? 'ANDROID' : 'IOS'
      let opId = param.paramMap.childPageRoute || param.channelId
      //外围渠道依然用行商的token拉起
      let url = ''
      url = `/xsb/api-user/tokenAuthen/h5genToken.do?opId=${opId}&prviId=${param.menuId}&clientType=${client}`
      url += `&regionId=${this.uinfo.region}&operId=${this.uinfo.staffId}&stationId=${this.uinfo.stationId}&phoneNumber=${this.uinfo.servNumber}`
      this.$http
        .get(url)
        .then((response) => {
          let data = response.data
          let opUrl = ''
          opUrl = data.data.opUrl
          //针对拼接子url进行转码，包含redirectUrl值的进行处理，这种分割方式包括前缀，redirectUrl=及后面数据
          const splitPattern = /(redirectUrl=)/;
          const result = opUrl.split(splitPattern);
          console.log(result)
          if (result && result.length > 2) {
            const splitPattern2 = /(&token=)/;
            const result2 = result[2].split(splitPattern2);
            if (result2 && result2[2] && result2[2].length > 2) {
              let keys = Object.keys(param.paramMap)
              let extendParam = ''
              if (keys && keys.length > 0) {
                keys.forEach(item => {
                  if(item != 'pageName'){
                  extendParam += `&${item}=${param.paramMap[item]}`
                  }
                })
              }
              opUrl = result[0] + result[1] + encodeURIComponent(result2[0] + extendParam) + result2[1] + result2[2]

              console.log(opUrl)
              let title = param.paramMap && param.paramMap.pageName ? param.paramMap.pageName : '阿拉盯'
              console.log(opUrl)
              Indicator.close();

              console.log(title,opUrl)
              if(this.currentPage != 'commonPage'){
                console.log(title,opUrl)
                ClientJs.openWebKit(opUrl, title , '1', '0', '', '', '', '', '', '', '')
              }else{
                window.location.href = opUrl
              }
              return
            }
          }
          let keys = Object.keys(param.paramMap)
          if (keys && keys.length > 0) {
            keys.forEach(item => {
              if(item != 'pageName'){
                  opUrl += `&${item}=${param.paramMap[item]}`
              }
            })
          }
          console.log(opUrl)
          let title = param.paramMap && param.paramMap.pageName ? param.paramMap.pageName : '阿拉盯'
          Indicator.close();
          if(this.currentPage != 'commonPage'){
            console.log(title,opUrl)
            ClientJs.openWebKit(opUrl, title , '1', '0', '', '', '', '', '', '', '')
          }else{
            window.location.href = opUrl
          }
        })
        .catch((response) => {
          this.$alert(response)
        })
    },
    //入网业务需要查看当前操作员是否做过身份证认证
    checkEnterNet(item) {
      //判断鉴权
      let url = `/xsb/ability/businessLimit/h5qryEnterAuthority?privId=${item.privId}`
      this.$http
        .get(url)
        .then((response) => {
          let { retCode, retMsg, data } = response.data
          if (retCode == '0') {
            if (data.isLimit == '1') {
              this.$messagebox({
                title: '提示',
                message: '操作员未做身份认证或认证信息已失效，请点击【确定】进行读证认证操作',
                showCancelButton: true,
                showConfirmButton: true
              }).then((action) => {
                if (action == 'confirm') {
                  this.$router.push({
                    path: '/enternetAuth',
                    query: {
                      item: item,
                      srcFrom: 'business'
                    }
                  })
                }
              })
            } else {
              this.goBusinessPage(item)
            }
          } else {
            this.$alert(retMsg || '入网鉴权异常')
            return false
          }
        })
        .catch((response) => {
          this.$alert(response)
        })

    },
    validErrorTip(message) {
      this.$messagebox({
        title: '提示',
        message: message,
        showConfirmButton: true,
        closeOnClickModal: false
      }).then((action) => {
        ClientJs.closeCallBack('FSOP')
      })
    },
    //拉空白卡管理页面
    getCrmTargetCenter(){
      //首先查询session中是否有值，有值直接使用不需要调用接口，否则再调用接口获取
      let targetCenter = sessionStorage.getItem('blankCardCenter');
      //如果targetCenter为空则调用下面接口
      if(!targetCenter){
        let url=`/xsb/gridCenter/grabSheet/h5GetBlankCardUrl`;
        return this.$http.post(url).then((res)=>{
          if(res.data.retCode=='0'){
            targetCenter = res.data.data
            //将值存入session中
            sessionStorage.setItem('blankCardCenter', targetCenter)
            return targetCenter
            // let urlParam = `&targetCenter=` + targetCenter
            // this.tokenFromAld('blankCardManager', item.privId, this.uinfo, '', urlParam)
          }else {
            this.$alert(res.data.retMsg || '获取跳转页面URL失败')
          }
        }).catch((res)=>{
          this.$alert('获取跳转页面URL路径异常')
        })
      }else{
        return targetCenter
      }
    },
    validNumber(value) {
      let _this = this
      if (!value || value == '') {
        this.validErrorTip('用户号码为空，请检查号码')
        return
      } else if (value['0'] == 1) {
        const regnum = /^[\d|\.]*$/
        if (!regnum.test(value)) {
          this.validErrorTip(`用户号码需为纯数字，请检查号码`)
          return
        }
        if (value.length != 11) {
          this.validErrorTip(`用户服务号码【${value}】错误，请检查号码`)
          return
        }
      } else if (value['0'] == 0) {
        const reg = /^0\d{2,3}\d{7,8}$/
        if (!reg.test(value)) {
          this.validErrorTip(`用户固定电话【${value}】错误，请检查号码`)
          return
        }
      } else if (value['0'] != 0 && value['0'] != 1) {
        this.validErrorTip(`用户号码【${value}】不正确，请检查号码`)
        return
      }
      let url = `/xsb/personBusiness/transferNet/h5queryNpinfoTwo?telnum=${value}`
      this.$http
        .get(url)
        .then((res) => {
          if ('0' == res.data.retCode) {
            if (res.data.data.curYidong == '-1') {
              let url = `/xsb/ability/configmanage/h5qryAllVersionsConfigByFeature?serviceName=person&featureKey=person-yidongnumsegment`
              this.$http.get(url).then((res1) => {
                if (res1.data.retCode == '0') {
                  let retList = res1.data.data['1']['key']
                  let startKeyList = retList.split(',')
                  let num = 0
                  startKeyList.forEach((startKey) => {
                    if (value.startsWith(startKey)) {
                      this.$router.push({
                        path: 'familyDiagnosis',
                        query: {
                          isYiDong: '1',
                          telnum: value,
                          isMobel: value.split('')[0] == 0 ? 'fixedTel' : 'mobileTel',
                          srcFrom: 'webview',
                          gobackFlag: 'webview',
                          source: 'region'
                        }
                      })
                    } else {
                      num++
                      if (num == startKeyList.length) {
                        this.$router.push({
                          path: 'familyDiagnosis',
                          query: {
                            isYiDong: '0',
                            telnum: value,
                            isMobel: value.split('')[0] == 0 ? 'fixedTel' : 'mobileTel',
                            srcFrom: 'webview',
                            gobackFlag: 'webview',
                            source: 'region'
                          }
                        })
                      }
                    }
                  })
                } else {
                  this.validErrorTip('查询移动号段异常，' + res1.data.retMsg)
                }
              })
            } else if (res.data.data.curYidong == '1') {
              this.$router.push({
                path: 'familyDiagnosis',
                query: {
                  isYiDong: '1',
                  telnum: value,
                  isMobel: value.split('')[0] == 0 ? 'fixedTel' : 'mobileTel',
                  srcFrom: 'webview',
                  gobackFlag: 'webview',
                  source: 'region'
                }
              })
            } else {
              this.$router.push({
                path: 'familyDiagnosis',
                query: {
                  isYiDong: '0',
                  telnum: value,
                  isMobel: value.split('')[0] == 0 ? 'fixedTel' : 'mobileTel',
                  srcFrom: 'webview',
                  gobackFlag: 'webview',
                  source: 'region'
                }
              })
            }
          } else {
            _this.validErrorTip(res.data.retMsg || '携转信息获取失败，请稍后重试')
          }
        })
        .catch((response) => {
          _this.validErrorTip(response || '携转信息获取超时')
        })
    },
    //判断电视宽带开通情况
    checkBandTv(tel, privid, tvBandFlg) {
      let url = `/xsb/personBusiness/personInfo/h5QryAlreadyOpened?privid=${privid}&telnum=${tel}`
      this.$http.get(url).then(res => {
        let { retCode, retMsg, data } = res.data
        if (retCode == '0') {
          let isMband = data.isMband // 是否开通宽带 0：否，1：是
          let isNetTv = data.isNetTv // 是否开通互联网电视 0：否，1：是
          if (tvBandFlg == 'band') {//宽带
            if (isMband == '1') {//开通过宽带 跳产品变更
              this.$router.push('/bandChange')
            } else {
              this.$router.push('/kuandaiKaiTong')
            }
          } else if (tvBandFlg == 'tv') {//电视
            if (isNetTv == '1') {
              //this.$alert('已开通过互联网电视！');
              let prodNetList = data.prodNetList
              //跳第二台电视开通
              this.$router.push('/internetTvOpen?tvSort=2&alreadProd=' + JSON.stringify(prodNetList[0]))
            } else if (isNetTv == '2') {
              //this.$alert('已开通过2台互联网电视！');
              let prodNetList = data.prodNetList
              let prodInfo = {}
              for (let item of prodNetList) {
                if (item.pakageid == '2013000101' || item.pakageid == '2013000102') {
                  prodInfo = item
                  break
                }
              }
              //跳第三台电视开通
              this.$router.push('/internetTvOpen?tvSort=3&alreadProd=' + JSON.stringify(prodInfo))
              /*let param = {
                busiType: 'three_tv_open'
              }
              this.$http.post('/xsb/ability/businessLimit/h5QryBusiPermission', param).then(res => {
                if (res.data.retCode == '0') {
                  let prodNetList = data.prodNetList
                  let prodInfo = {}
                  for (let item of prodNetList) {
                    if (item.pakageid == '2013000101' || item.pakageid == '2013000102') {
                      prodInfo = item
                      break
                    }
                  }
                  //跳第三台电视开通
                  this.$router.push('/internetTvOpen?tvSort=3&alreadProd=' + JSON.stringify(prodInfo))
                } else {
                  this.$messagebox.alert('已开通过2台互联网电视', '温馨提示').then((res) => {
                    ClientJs.closeCallBack()
                  })
                }
              })*/
            } else if (isNetTv == '3') {
              this.$messagebox.alert('已开通过3台互联网电视！', '温馨提示').then((res) => {
                ClientJs.closeCallBack()
              })
            } else {
              if (isMband == '0') {//没开通宽带
                this.$messagebox.alert('请先开通宽带！', '温馨提示').then((res) => {
                  ClientJs.closeCallBack()
                })
              } else {
                this.$router.push('/internetTvOpen?tvSort=1')
              }
            }
          } else if (tvBandFlg === 'guhua') {
            if (isMband == '1') {//开通过宽带
              this.$router.push('/guHuaChooseTel')
            } else {
              this.$messagebox.alert('请先开通宽带！', '温馨提示').then((res) => {
                ClientJs.closeCallBack()
              })
            }
          }
        } else {
          this.$messagebox.alert(retMsg || '查询开通宽带电视信息异常', '温馨提示').then((res) => {
            ClientJs.closeCallBack()
          })
        }
      }).catch(res => {
        this.$alert('查询开通宽带电视信息网络异常:' + res)
      })
    }


  },
  mounted() {
    //其他系统关闭H5回传参数
    window['closeCommWebkitCb'] = (result) => {
      // Indicator.open("加载中");
      console.log((result))
      console.log('12222222', this.currentPage)
      if (result) {
        this.params = decodeURIComponent(BASE64.decode(result))
        this.body = JSON.parse(this.params)
        console.log(this.body)
        if(this.body.pageType != '0'){
          Indicator.open("正在同步电子协议...");
          this.goPage()
        } else if (this.currentPage == 'groupFiling') {
          Indicator.close()
          this.goPrev()
        } else if (this.body.pageType == '0' && this.isTest) {
          this.$router.push('/userEvaluate')
          Indicator.close()
        } else if (this.body.pageType == '0' && this.body.paramMap && this.body.paramMap.currentPrivId == '4955') {
          if(this.body.paramMap.oneid){
              this.initOneClickOrderInfo()
          }else{
            Indicator.close()
          }
        } else {
          Indicator.close()
        }
      }
    };
    window['getUserInfoAndGo'] = (result) => {
        let res = result.userInfo
        this.uinfo = JSON.parse(res)
        initTokenAfterBack(this.$http, this.uinfo) //edit by qhuang at 2021/11/29
        let ipUrl = result.serverUrl
        ipUrl = 'http://*************:8080'
        let jsonString = JSON.stringify(this.body);
        console.info("jsonString",jsonString);
        let body = BASE64.encode(encodeURIComponent(jsonString, 'utf-8'))
        // ipUrl = `${ipUrl}/xsbh5/index.html#/commonPage?srcFrom=webview&gobackFlag=webview&body=${body}`;
        ipUrl = `${ipUrl}/xsbh5.html#/commonPage?srcFrom=webview&gobackFlag=webview&body=${body}`
        console.log(ipUrl)
        ClientJs.openWebKit(ipUrl, '阿拉盯', '1', '0', '', '', '', '', '', '', '')
    }
  }
}
