import { BASE64 } from '@/base/coding'
import ClientJs from '@/base/clientjs.js'
import { skipCommom } from 'components/desk/WorkStageNew/commonBusiCk.js'
import Storage from '@/base/storage.js'

export const oneClickOrderJs = {
  mixins: [skipCommom],
  data() {
    return {
      uinfo: {},
      serverUrl: '',
      orderLinkType: { id: '', label: '', state: '' },//查询工单环节状态 addCart-加购；realName-实名；confirmCart-结算；commitContract-合同生成；recharge-充值缴费;sign-签名；archived-归档
      orderLinkTypeList: [
        { id: 'addCart', label: '加购' },
        { id: 'realName', label: '实名' },
        { id: 'confirmCart', label: '结算' },
        { id: 'commitContract', label: '合同生成' },
        { id: 'recharge', label: '缴费' },
        { id: 'sign', label: '签名' },
        { id: 'archived', label: '归档' }],
      customerInfo: { customerId: '', customerName: '' },
      oneId: '',
      orderId: '',
      contractId: '',
      busiType: ''

    }
  },
  created() {
    //登录人信息
    this.uinfo = Storage.session.get('userInfo')
    ClientJs.getSysInfo('getUserInfoCB1')
  },
  methods: {
    initOneClickOrderInfo(info) {
      console.log(info)
      let addParam = {
        busiType: 'oneClickOrderTob',
        busiTypeName: '一键订购TOB业务'
      }
      if (this.uinfo) {
        if (this.uinfo.stationId == this.CONSTVAL.STATION_KHJL) {
          addParam.sceneType = '01'
        } else if (this.uinfo.stationId == this.CONSTVAL.STATION_SKZQ_KHJL) {
          addParam.sceneType = '02'
        } else if (this.uinfo.stationId == this.CONSTVAL.STATION_YYYHWYXN || this.uinfo.stationId == this.CONSTVAL.STATION_HUWAI) {
          addParam.sceneType = '03'
        } else {
          addParam.sceneType = '99'
        }
      }
      if (info) {
        if (info.menuId == '100502') {
          //加购新增到工单表,集团编码、购物车编码和产品名称
          addParam.customerId = info.paramMap.custCode
          addParam.addCartItemCode = info.paramMap.addCartItemIds
          addParam.addCartItemName = info.paramMap.productNames
          addParam.latestLinkId = this.orderLinkTypeList[0].id //confirmCart
          addParam.latestLinkName = this.orderLinkTypeList[0].label //结算
          addParam.latestLinkState = '1'
          this.recordOneClickOrder(addParam)
        } else if (info.menuId == '4955' && info.paramMap.channelType == '01') {
          //加购新增到工单表
          addParam.customerId = info.paramMap.custCode //集团编码
          addParam.oneId = info.paramMap.oneid //oneId
          addParam.accountId = info.paramMap.accountId //集团账户编码
          addParam.priceAmount = info.paramMap.priceAmount //订单金额
          addParam.traceId = this.traceId
          addParam.latestLinkId = this.orderLinkTypeList[2].id //confirmCart
          addParam.latestLinkName = this.orderLinkTypeList[2].label //结算
          addParam.latestLinkState = '1'
          this.recordOneClickOrder(addParam)
        }else if(info.pageType == '0' && info.paramMap.oneid){
          addParam.oneId = info.paramMap.oneid //oneId
          if(addParam.paramMap.currentPrivId == '4955'){
            addParam.latestLinkId = this.orderLinkTypeList[5].id //sign
            addParam.latestLinkName = this.orderLinkTypeList[5].label //签名
            addParam.latestLinkState = addParam.paramMap.state
            if (addParam.latestLinkState != '0') {
              this.recordOneClickOrder(addParam)
            }
          }
        }
      } else {

      }
    },
    recordOneClickOrder(addParam) {
      let url = `/xsb/gridCenter/zqGroup/h5recordOneClickInfo`
      this.$http.post(url, addParam).then(res => {
        let { retCode, retMsg, data } = res.data
        if (retCode == '0') {
          this.orderLinkType = {
            id: addParam.latestLinkId,
            label: addParam.latestLinkName,
            state: addParam.latestLinkState
          }
          console.log(this.orderLinkType)

          this.traceId = data
          if (this.orderLinkType) {
            let nextLink = {}
            if (this.orderLinkType.state === '1') {
              this.orderLinkTypeList.forEach((item, index) => {
                if (item.id == this.orderLinkType.id) {
                  nextLink = this.orderLinkTypeList[index + 1]
                  this.goNextLink(nextLink,addParam)

                }
              })
            } else {
              this.orderLinkTypeList.forEach((item, index) => {
                if (item.id == this.orderLinkType.id) {
                  nextLink = item
                  this.goNextLink(nextLink,addParam)
                }
              })
            }
          }
        } else {
          this.$alert(retMsg || `当前应用appId访问网格通系统权限获取失败`)
          return false
        }
      }).catch(res => {
        this.$alert('集团客户创建网络异常:' + res)
        return false
      })
    },
    goNextLink(nextLink, orderInfo) {
      let commonPageParam = {}
      if (nextLink.id === 'realName') {
        commonPageParam = {
          pageType: '2',
          menuId: '100502',
          pageRoute: '/groupRealNameList',
          paramMap: {
            custCode: orderInfo.customerId,
            channelId: '02'//一键订购来源
          }
        }
      } else if (nextLink.id === 'confirmCart') {
        commonPageParam = {
          pageType: '3',
          menuId: '5904',
          pageRoute: 'tob_business_confirm',
          paramMap: {
            custCode: orderInfo.customerId,
            addCartItemIds: orderInfo.addCartItemIds,
            channelId: '02'//一键订购来源
          }
        }
      } else if (nextLink.id === 'commitContract') {
        commonPageParam = {
          pageType: '3',
          menuId: '4955',
          pageRoute: 'jkddmNewContract',
          paramMap: {
            pageName: '合同确认',
            childPageRoute: 'jkddmNewContract',
            oneid: orderInfo.oneId
          }
        }
      } else if (nextLink.id === 'recharge') {
        commonPageParam = {
          pageType: '2',
          menuId: '100265',
          pageRoute: '/groupRecharge',
          paramMap: {
            custCode: orderInfo.customerId,
            accountId: orderInfo.accountId,
            priceAmount: orderInfo.priceAmount
          }
        }
      } else if (nextLink.id === 'sign') {
        if (!orderInfo.workUrl) {
          this.$router.push('/oneClickOrderQuery')
        } else {
          ClientJs.openWebKit(orderInfo.workUrl, '', '0', '', '', '', '', '', '', '', '')
        }
      } else if (nextLink.id === 'archived') {
        this.$router.push('/oneClickOrderQuery')
      }
      let jsonString = JSON.stringify(commonPageParam)
      let base64Param = BASE64.encode(encodeURIComponent(jsonString, 'utf-8'))
      let url = `${this.serverUrl}/xsbh5/index.html#/commonPage?srcFrom=webview&gobackFlag=webview&body=${base64Param}`
      // let.url = `${this.ipUrl}/xsbh5.html#/commonPage?srcFrom=webview&gobackFlag=webview&body=${base64Param}`
      console.log(url)
      ClientJs.openWebKit(url, '', '0', '0', '', '', '', '', '', '', '')
    }
  },
  mounted() {
    window['getUserInfoCB1'] = (result) => {
      this.serverUrl = result.serverUrl
      // this.serverUrl = 'http://*************:8080'
    }
  }
}
