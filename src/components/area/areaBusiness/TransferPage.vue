<template>
  <div class="no-data-wrap">
    <Header  backType='custom' @emGoPrev="goPrev"></Header>
    <img src='@/assets/img/default-nodata.png'/>
    <p>页面正在加载中,请稍等...</p>
  </div>
</template>
<script>

import Header from 'components/common/Header.vue'
import ClientJs from '@/base/clientjs.js'
export default {

  components: {Header},
  data() {
    return {
      isAndroid:/android|harmony/gi.test(navigator.userAgent),
      body: {},
      token:'',
      uinfo:'',
      // ipUrl:''
      ipUrl:'http://**************:8080'
    }
  },
  created(){
    this.getUrlParams();
    ClientJs.getSysInfo('getUserInfoCB');
  },
  methods: {
    getUrlParams(){
      this.token = decodeURIComponent(this.$route.query.TOKEN)
      this.body.entname=decodeURIComponent(this.$route.query.ENTNAME)
      this.body.uniscid=decodeURIComponent(this.$route.query.UNISCID)
      this.body=JSON.stringify(this.body)
    },
    goPage(){
      //本地
      // let url = `${this.ipUrl}/xsbh5.html#/groupFiling?srcFrom=businessView&body=${this.body}`;
      //生产
      let url = `${this.ipUrl}/xsbh5/index.html#/groupFiling?gobackFlag=webview&srcFrom=businessView&body=${this.body}`;
      window.location.href = url;
    },
    checkToken(param){
      let tokenPo = {
        token: param
      }
      // let url = 'apiM/xsb/personBusiness/learnGarden/h5checkToken'
      //生产
      let url = 'xsb/personBusiness/learnGarden/h5checkToken'
      this.$http
        .post(url,tokenPo)
        .then(res => {
          let resData = res.data
          if (resData.retCode == '0') {
            this.goPage()
          } else {
            this.$alert(resData.retMsg || "鉴权失败")
          }
        })
        .catch(response => {
          this.$alert(response||"请求异常")
        })
    },
    goPrev(){
      history.back();
    },
  },
  mounted(){
    window['getUserInfoCB'] = (result) => {
      //获取服务器地址
      this.ipUrl = result.serverUrl;
      let res = result.userInfo;
      this.uinfo = JSON.parse(res);
      initTokenAfterBack(this.$http,this.uinfo);
      //替换端口号
      // let array=this.ipUrl.split(':');
      // array[array.length-1]='8080';
      // this.ipUrl=array.join(':');
      //校验token
      this.checkToken(this.token)
    }
  }
}
</script>

<style scoped lang="less">
.no-data-wrap{
  height: 240px;
  border-radius: 8px;
  background-color: #fff;
  margin: 8px 16px;
  color:#737373;
  font-size:16px;
  text-align:center;
  display:flex;
  flex-direction:column;
  justify-content:center;
  align-items:center;
  margin-top: 20%;
  img{
    width:50vw;
  }
}
</style>

