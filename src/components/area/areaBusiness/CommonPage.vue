<template>
    <div class='no-data-wrap'>
        <img src='../../../assets/img/default-nodata.png' alt=''/>
        <p>页面正在加载中,请稍等...</p>
    </div>
</template>
<script>
    import { BASE64 } from '@/base/coding'
    import ClientJs from '@/base/clientjs.js'
    import { commonPageJs } from 'components/area/areaBusiness/commonPage.js'


    export default {
        mixins: [commonPageJs],
        data() {
            return {
            }
        },
        methods: {},
        created() {
            this.currentPage = 'commonPage'
            this.params = decodeURIComponent(BASE64.decode(this.$route.query.body))
            this.body = JSON.parse(this.params)
            console.log(this.body)
            ClientJs.getSysInfo('getUserInfoCB')
        },
        mounted() {
            window['getUserInfoCB'] = (result) => {
                let res = result.userInfo
                this.uinfo = JSON.parse(res)
                // this.uinfo = {"servNumber": "***********",
                //   "operatorName": "无名2",
                //   "region": "14",
                //   "regionName": "南t",
                //   "staffId": "9988021754066058",
                //   "stationId": "9988020036385338",
                //   "stationName": "客户经理",
                //   "crmId": "14150759",            //21115457
                //   "oa":"20394123",//OA账号
                //   "imei":"123",
                //   "roleInfo":[],
                //   "imei":"-1",
                //   "orgId":"1488018465153416",
                //   "panChannelInfo":{"panchannelid":"1"},
                //   "tokenid":"QFOxpuDlWRM=",       //1 V8x7/bXcqAw= 2 QFOxpuDlWRM=
                //   "viewInfoList":['4','1','2','3','5','6'],//视图 1：集客视图 2：小区视图 3：渠道视图 4：个人视图
                //   "stations": [
                //     {
                //       "stationId": "10011406",
                //       "stationName": "工作台-客户经理"
                //     },
                //     {
                //       "stationId": "10011806",
                //       "stationName": "商客经理"
                //     },
                //     {
                //       "stationId": "10012007",
                //       "stationName": "商客管理员"
                //     },
                //     {
                //       "stationId": "10009001",
                //       "stationName": "营业员户外营销"
                //     },
                //     {
                //       "stationId": "1811489",
                //       "stationName": "地市渠道管理员"
                //     },
                //     {
                //       "stationId": "1488018492933394",
                //       "stationName": "社区直销人员(新)"
                //     },
                //     {
                //       "stationId": "9988020036385338",
                //       "stationName": "客户经理"
                //     },
                //     {
                //       "stationId": "9988021754066058",
                //       "stationName": "社区直销人员"
                //     },
                //     {
                //       "stationId": "10013601",
                //       "stationName": "副网格长"
                //     },
                //     {
                //       "stationId": "10013401",
                //       "stationName": "楼园代理"
                //     },
                //   ]}

                // Storage.session.set('userInfo',this.uinfo);
                // Storage.session.set('tokenId',this.uinfo.tokenid);
                // this.$http.defaults.headers.tokenid = this.uinfo.tokenid;
                initTokenAfterBack(this.$http, this.uinfo) //edit by qhuang at 2021/11/29
                console.info(res)
                this.ipUrl = result.serverUrl
                this.ipUrl = 'http://*************:8080'
                this.goPage()
            }

        }
    }
</script>

<style scoped lang='less'>
    .no-data-wrap {
        height: 240px;
        border-radius: 8px;
        background-color: #fff;
        margin: 8px 16px;
        color: #737373;
        font-size: 16px;
        text-align: center;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-top: 20%;

        img {
            width: 50vw;
        }
    }
</style>
