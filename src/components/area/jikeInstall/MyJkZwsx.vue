<template>
    <div class="my-md">
        <div class="my-top">
            <div class="my-top-name">我的</div>
            <div class="my-top-warning" @touchstart="getStartTime" @touchend.prevent="showMenuList">
            </div>
        </div>
        <div class="marcard-wrap" :class="{'bg':stationFlg}">
            <div class="my-card" :style="{background:'url(static/img/cardline.png) no-repeat center center,linear-gradient(147deg,#5196FF 0%, #2A68FF 100%)'}">
                <div class="my-card-inner">
                    <div class="my-cd-name">{{userInfo.dwName | starName}}</div>
                    <div class="my-cd-station">
                        {{userInfo.stationName}}

                    </div>
                    <div class="my-cd-local">
                        <p>{{getLocation}}</p>
                    </div>
                    <div class="my-cd-num">{{userInfo.crmId|starCrmId}}/{{userInfo.servNumber | starTel}}</div>
                </div>
                <div class="my-cd-planbtn" @click="getClientTrueLocal()">
                    <i class="iconfont zhongzhi"></i>
                    <span class="my-cp-txt">刷新位置</span>
                </div>
            </div>
            <div class="student-wrapper">
                <img src="static/img/my-student.png" class="student-img" @click="showVconsole">
                <span class="student-tel">装维大使：{{userInfo.dwMobile | starTel}}</span>
            </div>
        </div>
        <div class="my-fn">
            <ul>
                <li class="my-fn-lis" @click="userLoginOut()">
                    <div class="my-fl-icon">
                        <span class="iconfont dengchu iconzhuxiao"></span>
                    </div>
                    <div class="my-fl-txt">注销登录</div>
                    <div class="my-fl-target">
                        <span class="iconfont youjiantou iconrjt"></span>
                    </div>
                </li>

            </ul>
        </div>
    </div>
</template>

<script>
    import ClientJs from '@/base/clientjs'
   // import {iEncrpt,iEncrptParam} from '@/base/encrptH5.js'
    import Storage from '@/base/storage'
    import Authenct from 'components/common/Authenticate/Authenticate'
    import NlDropdown from 'components/common/NlDropdown/dropdown.js'
    import {clientLocalMixin} from '@/base/mixin'
    import Vconsole from 'vconsole'

    export default {
        mixins: [clientLocalMixin],
        data() {
            return {
                pickerValue: '',
                userInfo:{},
                menuCount:0,
                stationFlg:false,//默认不展示岗位列表
                stationsList:[],//岗位列表
                selectStationId:'',//选中的岗位ID
                selectStationName:'',//选中的岗位名称
                relTelnum:'',//岗位关联的手机号或者工号
                emptyTel:false,//是否输入岗位关联的手机号或者工号
                zkTypeList:[
                            {id:'happyIndex',label:'无忧购机',needAuth:false},
                            {id:'happyList',label:'无忧购机订单列表',needAuth:false},
                            {id:'kuandaiKaiTong',label:'宽带开通',needAuth:false},
                            {id:'fullOrderList',label:'订单全流程',needAuth:false},
                            {id:'marketEntry?type=terminal',label:'终端营销案',needAuth:true,hasPwd:'0'},
                            {id:'token_okr',label:'okr'},
                            // {id:'familyKaiTong',label:'家庭网开通',needAuth:true,hasPwd:'0'},
                            // {id:'familyTuiDing',label:'家庭网退订',telnum:'15850671704',needAuth:true,hasPwd:'0'},
                            // {id:'familyTuiChu',label:'家庭网退出',telnum:'15906115107',needAuth:true,hasPwd:'0'},
                            // {id:'familyWeiHu',label:'家庭网维护',telnum:'15850671704',needAuth:true},
                            ],
                startTime:''//点击时间
            }
        },
        mounted(){
            this.getUserInfo();
            new Vconsole();
            let vconDom = document.getElementById('__vconsole');
            vconDom.classList.add('hide');
        },

        methods: {
            showVconsole(){
                const nowTime = new Date().getTime();
                if(nowTime - this.lastClickTime < 3000){
                    this.menuCount ++;
                } else {
                    this.menuCount = 0;
                }
                this.lastClickTime = nowTime;
                if(this.menuCount >= 5) {
                    let vconDom = document.getElementById('__vconsole');
                    vconDom.classList.toggle('hide');
                    this.menuCount = 0;
                }
            },
            getStartTime(){
                this.startTime = new Date().getTime();
            },
            showMenuList(){

                const nowTime = new Date().getTime();
                if(nowTime - this.lastClickTime < 3000){
                    this.menuCount ++;
                } else {
                    this.menuCount = 0;
                }
                this.lastClickTime = nowTime;


                if(this.menuCount >= 3 || nowTime - this.startTime > 1000 ) {
                    this.menuCount = 0;
                    let self = this;
                    NlDropdown({
                        confirmBtn: false,
                        datalist: self.zkTypeList
                        }, (retVal) => {

                        if(retVal.needAuth){
                            console.info(retVal)
                            Authenct({
                                popFlag:true,
                                hasPwd:retVal.hasPwd,
                                telnum:retVal.telnum,
                                idCardWay:false
                            },function (obj){
                                self.$router.push('/' + retVal.id);
                            });
                        }else {


                            let uinfo = this.userInfo;
                            if(retVal.id == 'token_okr'){
                                if(!uinfo.oa){
                                    this.$alert('请联系管理员配置OA账号');
                                    return;
                                }
                                let client = /android|harmony/gi.test(navigator.userAgent) ? 'ANDROID' : 'IOS'
                                let url = `/xsb/api-user/tokenAuthen/h5genToken.do?opId=okr&prviId=0&phoneNumber=${uinfo.servNumber}`;
                                url += `&regionId=${uinfo.region}&operId=${uinfo.staffId}&stationId=${uinfo.stationId}&clientType=${client}`;
                                this.$http.get(url).then((response) => {
                                    let {retCode,retMsg,data} = response.data;
                                    console.info(data);
                                    if (retCode === '0') {
                                        let opUrl = data.opUrl;
                                        ClientJs.openWebKit(encodeURIComponent(opUrl), '', '0', '', '', '', '', '', '', '', '');
                                    } else {
                                        this.$alert(retMsg || '拉起TOKEN_OKR失败')
                                    }
                                }).catch((response) => {
                                });
                            }else {
                                self.$router.push('/' + retVal.id)
                            }
                        }
                    });
                }
            },
            //切换岗位
            showStationList(){
                this.stationFlg = !this.stationFlg;
            },
            userLoginOut(){
                this.$messagebox.confirm('确认注销登录吗？','温馨提示')
                .then((action) => {
                    ClientJs.userLoginOut();
                }).catch(() => {});
            },
            getUserInfo(){
                this.userInfo = Storage.session.get('userInfo');
                this.stationsList = this.userInfo.stations;
                this.selectStationId = this.userInfo.stationId;
                this.selectStationName = this.userInfo.stationName;
            },

            selectStation(item){
                this.selectStationId = item.stationId;
                this.selectStationName = item.stationName;
            },
            //岗位关联手机号的输入验证置false
            clearEmptyFlg(){
                this.emptyTel = false;
            },
            //岗位列表中的确认按钮
            changeStation(){
                //如果是集客随销 或者 校园营销
                if(this.selectStationId == this.CONSTVAL.STATION_JKSX || this.selectStationId == this.CONSTVAL.STATION_SCHOOL){
                    if(!this.relTelnum){
                        let msg = '请输入客户经理工号';
                        if(this.selectStationId == this.CONSTVAL.STATION_SCHOOL){
                            msg = '请输入手机号';
                        }
                        this.emptyTel = true;
                        this.$alert(msg);
                        return;
                    }
                    if(this.selectStationId == this.CONSTVAL.STATION_SCHOOL){
                        if(this.relTelnum == this.userInfo.servNumber){//输入的手机号和登录的手机号一样
                            //不做处理
                        } else if(/^((1)+\d{10})$/.test(this.relTelnum)){//
                            //请求服务端，调用CRM接口判断此号码是否有效
                            this.checkStudentPhone()
                            return;
                        } else {
                            this.$alert('请输入正确的手机号');
                            return;
                        }
                    }
                } else {
                    this.relTelnum = '';
                }
                this.switchStation();
            },
            //调用服务端切换岗位
            switchStation(){
                this.stationFlg = false;
                let oldStationId = this.userInfo.stationId;
                let url = `/xsb/api-user/user/h5switchStation?stationId=${this.selectStationId}&imei=${this.userInfo.imei}`;
                this.$http.get(url).then((res) => {
                    let data = res.data;
                    if(data.retCode == '0'){
                        //2023.10.27 cyy 如果原来是装维随销、装维随销（新）,代维信息清空
                        if (oldStationId == '10000601' || oldStationId == '10009102') {
                          this.userInfo.dwUser = '';
                          this.userInfo.dwMobile = '';
                          this.userInfo.dwName = '';
                        }
                        this.userInfo.stationId = this.selectStationId;
                        this.userInfo.stationName = this.selectStationName;
                        this.userInfo.crmId = data.data.crmId;
                        this.userInfo.relTelnum = this.relTelnum;
                        this.userInfo.viewInfoList = data.data.viewInfoList;
                        Storage.session.set('userInfoNew',this.userInfo);
                        Storage.session.set('userInfo',this.userInfo);
                        //2023.10.18 cyy 塞入装维人员信息
                        if(this.userInfo.stationId == '10000601' || this.userInfo.stationId == '10009102'){
                          this.qryZwPerson();
                        }
                    }else{
                        this.$alert(data.retMsg || '切换岗位服务异常');
                    }
                })
            },
            //2023.10.18 cyy 查询装维人员信息
            qryZwPerson() {
              let param = {
                xs_mobile: this.userInfo.servNumber,//行商手机号
                regionId: this.userInfo.region,//地市
              };
              let url = '/xsb/personBusiness/zwOrder/h5queryZwPerson';
              this.$http.post(url, param).then((res) => {
                if (res.data.retCode == '0') {
                  if (res.data.data && res.data.data.zwryList && res.data.data.zwryList.length>0) {
                    this.userInfo.dwUser = res.data.data.zwryList[0]?res.data.data.zwryList[0].zw_id:'';
                    this.userInfo.dwMobile = res.data.data.zwryList[0]?res.data.data.zwryList[0].zw_mobile:'';
                    this.userInfo.dwName = res.data.data.zwryList[0]?res.data.data.zwryList[0].zw_name:'';
                    Storage.session.set('userInfo',this.userInfo);
                    Storage.session.set('userInfoNew',this.userInfo);
                  }
                }
              }).catch((err) => {
              })
            },
            //请求服务端，调用CRM接口判断此号码是否有效
            checkStudentPhone(){
                //是否校验学生手机号开关
                let param = {
                    busiType:'studentPhoneCheck_jq'
                }
                let self = this;
                this.$http.post('/xsb/ability/businessLimit/h5QryBusiPermission',param).then(res => {
                    //注意 retCode=0 不校验号码
                    if(res.data.retCode != '0'){
                        param = {
                            regionId:this.userInfo.region,
                            operId:this.userInfo.crmId,
                            phone:this.relTelnum
                        }
                        //校验学生手机号
                        self.$http.post('/xsb/api-user/user/h5verifyStudentPhone',param).then(res => {
                            if(res.data.retCode == '0'){
                                self.switchStation();
                            } else {
                                self.$alert(res.data.retMsg || '学生手机号校验失败')
                            }
                        })
                    } else {
                        self.switchStation();
                    }
                })
            },
            //跳关于
            goAbout(){
                 this.$router.push({name: 'About'});
            },
            test(){
                let vconDom = document.getElementById('__vconsole');
                vconDom.classList.remove('hide');
                this.$router.push('/realNameCertify');
            },
            //跳智能小信
            goSmartXin(){
                let uInfo = this.userInfo;
                let loginCode = uInfo.crmId;// 账号(crm账号)：loginCode =123455
                if(!loginCode){
                    // this.$alert('crm工号为空,请尝试切换岗位');
                    // return;
                    loginCode = uInfo.servNumber;
                }
                let loginName = encodeURI(uInfo.operatorName);// 操作员姓名：loginName =张三
                let channel = 'xs';// 行商渠道标识: channel=xs
                let region = uInfo.region;// 对应的地市：region =14
                let mobile = uInfo.servNumber;// 手机号：mobile =12812312312
                let indicate = '';// 验证码：indicate =xxxxxxxxxxxxx

                //验证码为loginCode + mobile + region + key 进行md5进行签名（key值可以设值xs)。
                indicate = loginCode + mobile + region + channel;
                this.$http.get('/xsb/ability/encrypt/h5XXMd5Encrypt?param=' + indicate).then(res=>{
                    let {retCode,retMsg,data} = res.data;
                    if(retCode == '0'){
                        indicate = data;//md5加密后的值
                        //亚信智能小信的链接
                        let url = `${Storage.get('webUrl')}/xt/XsMobile/loginMobile?loginCode=${loginCode}&loginName=${loginName}`;
                        url += `&mobile=${mobile}&region=${region}&channel=${channel}&indicate=${indicate}`;
                        console.info(url);
                        //window.location.href = url;
                        ClientJs.openWebKit(url, '', '0', '', '', '', '', '', '', '', '');
                    } else {
                        this.$alert(retMsg || 'MD5算法失败')
                    }
                });

            }
        },
        computed:{
            //获取定位信息
            getLocation(){
                return Storage.get('location') || this.userInfo.regionName;
            },
            //集客随销 9988022838662806   校园营销  9988021245683406
            relTelTitle(){
                if(this.selectStationId == this.CONSTVAL.STATION_JKSX){//集客随销
                    return '客户经理工号(必填)';
                } else if(this.selectStationId == this.CONSTVAL.STATION_SCHOOL) {
                    return '手机号(必填)';
                } else {
                    return '';
                }
            },

        },
        created(){},
        components: {},
        filters:{
            //电话号码中间几位模糊化
            starTel(val){
                // 应付集团验收 start at 2023.10.10
                let uinfo = Storage.session.get('userInfo');
                let dwUserRole = uinfo.dwUserRole;
                if(dwUserRole != 'dw_1004'){//应付集团验收
                    return val;
                }
                // 应付集团验收 end by qhuang
                if(!val){
                    return '***';
                } else  {
                    let reg = /^(\d{3})\d*(\d{4})$/;
                    return val.replace(reg,'$1****$2')
                }
            },
            //工号中间几位模糊化
            starCrmId(val){
                // 应付集团验收 start at 2023.10.10
                let uinfo = Storage.session.get('userInfo');
                let dwUserRole = uinfo.dwUserRole;
                if(dwUserRole != 'dw_1004'){//应付集团验收
                    return val;
                }
                // 应付集团验收 end by qhuang
                if(!val){
                    return '1***3';
                } else  {
                    let reg = /^(\d{2})\d*(\d{2})$/;
                    return val.replace(reg,'$1****$2')
                }
            },
            starName(val){
                // 应付集团验收 start at 2023.10.10
                let uinfo = Storage.session.get('userInfo');
                let dwUserRole = uinfo.dwUserRole;
                if(dwUserRole != 'dw_1004'){//应付集团验收
                    return val;
                }
                // 应付集团验收 end by qhuang
                if(!val){
                    return '***';
                } else  {
                    return val.substr(0,1) + new Array(val.length).join('*');
                }
            }
        }
    }
</script>

<style scoped lang="less">
    @import "../../../base/less/variable.less";
    @import "../../../base/less/mixin.less";
    .my-md {
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        overflow: auto;
        background: #fff;
        flex-direction: column;
    }

    .my-top {
        height: 32px;
        line-height: 32px;
        margin-top: 20px;
        margin-bottom: 25px;
        flex:0 1;
    }

    .my-top-name {
        height: 32px;
        float: left;
        font-size: 23px;
        margin-left: 16px;
        font-weight: 600;
        color: rgba(88, 88, 88, 1);
    }

    .my-top-warning {
        width: 40px;
        height: 30px;
        float: right;
        margin-top: 0px;
        margin-right: 16px;
        position: relative;
        line-height: 22px;
        text-align: center;
    }

    .iconwarning {
        color: #585858;
        font-size: 22px;
    }

    .my-wnum {
        width: 16px;
        height: 16px;
        display: block;
        top: -1px;
        right: -5px;
        text-align: center;
        line-height: 16px;
        font-size: 12px;
        color: #fff;
        background: #FF7070;
        border-radius: 50%;
        position: absolute;
    }
    .marcard-wrap{
        &.bg{
            box-shadow:0px 4px 18px 0px rgba(193,193,193,0.5);
            border-radius:0px 0px 16px 16px;
        }
    }
    .my-card {
        margin:0 1rem;
        height: 150px;
        /* background: linear-gradient(147deg, rgba(81, 150, 255, 1) 0%, rgba(42, 104, 255, 1) 100%); */
        box-shadow: 3px 3px 12px 0px rgba(122, 192, 255, 1);
        border-radius: 16px;
        opacity: 0.95;
        position: relative;
        /* background: url(../../assets/img/cardline.png) no-repeat center center,
                    linear-gradient(147deg,#5196FF 0%, #2A68FF 100%); */
        background-size: 100%;
    }

    .my-card-inner {
        position: absolute;
        top: 0px;
        left: 1.375rem;
        right: 1rem;
        bottom: 0;
        overflow: hidden;
        z-index: 2;
    }

    .my-cd-name {
        height: 32px;
        line-height: 32px;
        font-size: 24px;
        color: #fff;
        margin-top: 1.4rem;
    }
    .my-cd-station {
        font-size:16px;
        font-weight:600;
        color:#fff;
        line-height: 28px;
        height: 32px;
        i{
            padding-left:0.5rem;
        }
    }
    .my-cd-local {
        max-height: 32px;
        display:table;
        font-size:14px;
        color: #fff;
        p{
            display:table-cell;
            line-height: 16px;
            vertical-align: middle;
        }
    }

    .my-cd-num {
        height: 24px;
        display: inline-block;
        padding: 1px 8px;
        box-sizing: border-box;
        line-height: 24px;
        font-size: 16px;
        margin-top: 6px;
        color: #fff;
        background: linear-gradient(104deg, #3376ff 0%, #2268ff 100%);
        border-radius: 12px;
    }

    .my-fn {
        height: auto;
        margin: 15px;
        overflow: auto;
        flex-grow: 1;
    }

    .my-fn ul {
        display: block;
        overflow: hidden;
    }

    .my-fn-lis {
        height: 50px;
        line-height: 50px;
        border-bottom: 1px solid #F0F0F0;
        display: flex;
        overflow: hidden;

    }

    .my-fl-icon {
        width: 20px;
        height: 20px;
        margin-top: 15px;
        line-height: 20px;
        .bianzu2,.anniu{
            font-size: 20px;
        }

    }

    .my-fl-txt {
        flex-grow: 1;
        flex-shrink: 0;
        font-size: 14px;
        color: #585858;
        margin-left: 15px;
    }

    .my-fl-target {
        width: 20px;
        height: 20px;
        margin-top: 15px;
        line-height: 20px;
        color: #A6AAB2;
    }
    .my-fl-target2{
        margin-top:10px;
        .iconfont{
            font-size: 24px;
        }
    }
    .iconrizhi {
        color: #585858;
        font-size: 24px;
    }

    .iconyxlj {
        color: #585858;
        font-size: 20px;
    }

    .iconzhuxiao {
        color: #585858;
        font-size: 20px;
    }

    .my-cd-planbtn {
        width: 86px;
        position: absolute;
        right: 1rem;
        top: 14px;
        color:#fff;
        text-align: center;
        z-index: 3;
    }
    .plan-model{
        position: absolute;
        right: 1rem;
        top: 3.6rem;
        width:212px;
        background:#fff;
        height:60px;
        border-top-left-radius:35px;
        border-bottom-right-radius:35px;
        z-index: 3;
        padding:6px 0;
        box-sizing: border-box;
        .sanjian{
            width:8px;
            height:8px;
            background:#fff;
            position:absolute;
            top:-3px;
            right:40px;
            transform:rotate(45deg);
        }
        .icon-ul{
            width:100%;
            li{
                width:25%;
                float:left;
                height:48px;
                text-align: center;
                .txt{
                    color:#5B5B5B;
                    font-size:10px;
                    display:block;
                    margin-top:2px;
                }
            }
        }
    }
    .student-wrapper{
        margin-top:-30px;
        margin-left:2rem;
        margin-right:2rem;
        height:68px;
        background:rgba(255,242,227,1);
        box-shadow:0px 6px 5px 0px rgba(246,238,225,1);
        border-radius:8px;
        border:1px solid;
        border-image:linear-gradient(117deg, rgba(255,255,255,1), rgba(255,216,172,1)) 1 1;
        .student-img{
            padding:0 0.6rem 0 1rem;
            height:24px;
            vertical-align: middle;
        }
        .student-tel{
            line-height:96px;
            color:#AF822B;
            font-size:14px;
        }
    }
    .iconrwjh {
        color: #3473FE;
        font-size: 16px;
        display: block;
        height: 20px;
        line-height: 20px;
        float: left;
        margin-top: 6px;
        margin-left: 10px;
    }

    .my-cp-txt {
        font-size: 12px;
        color: #fff;
        height: 20px;
        line-height: 20px;
        padding-left: 3px;
    }

    .rel-tel-wrapper{
        .title{
            font-size:14px;
            color:#F43A45;
            text-align: center;
            margin:1rem 1rem 0.5rem 1rem;
        }
        .ipt-wrapper{
            height:38px;
            background:rgba(255,255,255,1);
            margin:0 1rem;
            input{
                height:38px;
                width:100%;
                box-sizing: border-box;
                border-radius:4px;
                border:1px solid rgba(233,233,233,1);
                padding-left:0.5rem;
                &:focus{
                    outline: none;
                }
            }
            &.empty-tel{
                input{
                    border:1px solid #F43A45;
                }
            }
        }
    }

.icon-wrapper{
    width:34px;
    height:34px;
    border-radius:50%;
    color:#fff;
    line-height: 34px;
    text-align: center;
    display:inline-block;


    .iconfont{
        font-size:20px;
    }
    &.school-div{
        background:linear-gradient(141deg,rgba(255,163,163,1) 0%,rgba(252,118,118,1) 100%);
        box-shadow:0px 4px 8px 0px rgba(242,122,122,0.61);
    }
    &.jituan-div{
        background:linear-gradient(137deg,rgba(255,227,72,1) 0%,rgba(255,156,81,1) 100%);
        box-shadow:0px 4px 8px 0px rgba(230,181,38,0.54);
    }
    &.xiaoqu-div{
        background:linear-gradient(145deg,rgba(255,157,209,1) 0%,rgba(255,91,184,1) 100%);
        box-shadow:0px 4px 8px 0px rgba(240,115,183,0.58);
    }
    &.hot-div{
        background:linear-gradient(145deg,rgba(95,234,187,1) 0%,rgba(49,187,98,1) 100%);
        box-shadow:0px 4px 6px 0px rgba(108,206,141,0.68);
    }
}
.my-txt{
    font-size: 14px;
    color: #1681fb;
    padding-right: 4px;
}
</style>
