<!-- 装维随销首页 -->
<template>
  <div class="wrapper">
    <div class="top-wrap">
      <div class="top-bg" :style="{backgroundImage:'url(static/img/imtopbg.png)'}">
        <h1 class="titletext">集客随销首页</h1>
      </div>
      <div class="top-card">
        <div class="yd-icon">
          <img src="static/img/ydlogo.png">
        </div>
        <div class="name-div">
          <h2 class="nametext">{{userInfo.dwName | starName}}</h2>
          <h4 class="posttext">{{userInfo.stationName}}</h4>
          <!--                    <div class="cash-div" @click="selectMonth()" v-show="currentMonth">-->
          <!--                    <div class="month-div" v-show="currentMonth">{{currentMonth}}月-->
          <!--                      <span class="iconfont f11 f1lSize"></span>-->
          <!--                    </div>-->
          <!--                    <div class="money-div" v-show="money">-->
          <!--                         <div>实发</div>-->
          <!--                         <div><span class="money-span">{{money}}</span><span class="yuan-span">元</span> </div>-->
          <!--                    </div>-->
          <!--                </div>-->
        </div>
        <ul class="oper-ul">
          <li class="oper-li">
            <p class="title">{{userInfo.crmId|starCrmId}}/{{userInfo.servNumber|starTel}}</p>
            <p class="desc">CRM工号/阿拉盯帐号</p>
          </li>
          <li class="oper-li">
            <p class="title">{{userInfo.dwUser|starCrmId}}/{{userInfo.dwMobile|starTel}}</p>
            <p class="desc">装维工号/手机号</p>
          </li>
        </ul>
      </div>
    </div>
    <mt-datetime-picker
      ref="picker"
      type="date"
      v-model="dateVal"
      year-format="{value} 年"
      month-format="{value} 月"
      @confirm="handleConfirm"
      :start-date="startDate"
      :end-date="endDate"
    >
    </mt-datetime-picker>
    <h1 class="first-title">活动专区</h1>
    <div class="banner-wrap">
      <div class="banner-item"
           v-for="bitem in bannerList"
           :key="bitem.privId"
           @click="businessCkPre(bitem)"
           :style="'background-image:url(static/img/' + bitem.picName + ')'">
        <h1 class="title">{{bitem.privName}}</h1>
        <span class="span-lbl " :class="bitem.privId=='100121'?'greenbg':'bluebg'">{{bitem.privDesc}}</span>
      </div>
    </div>

    <ul class="cs-bnul">
      <li class="cs-bnli" v-for="(item,index) in menuList" :key="index">
        <h1 class="title" v-show="item.itemList && item.itemList.length>0">{{item.privName}}</h1>
        <div class="cs-bnli-b" :class="{'first-menu':index==0}">
          <div class="csb-lis" v-for="(itemInner,indexInner) in item.itemList" :key="indexInner">
                        <span class="cs-lis-icon" @click="businessCkPre(itemInner)">
                            <img src="static/business/00000.png"
                                 v-real-img="'./static/zwsxImg/'+itemInner.picId+'.png'">
                        </span>
            <span class="cs-lis-txt">{{itemInner.privName}}</span>
          </div>
        </div>
      </li>
    </ul>

  </div>
</template>

<script>
  import Storage from '@/base/storage'
  import Authenct from 'components/common/Authenticate/Authenticate'
  import ClientJs from '@/base/clientjs'
  import {clientLocalMixin} from '@/base/mixin.js'
  import {h5getMenuByLevel} from '@/base/request/commonReq.js'


  export default {
    mixins: [clientLocalMixin],
    components: {},
    data() {
      return {
        startDate: new Date(new Date() - 365 * 24 * 60 * 60 * 1000),
        endDate: new Date(new Date().setMonth((new Date().getMonth() - 1))),
        monthNum: '',
        dateVal: new Date(new Date() - 365 * 24 * 60 * 60 * 1000),
        menuList: [],
        userInfo: {},
        bannerList: [{
          "privId": "0",
          "privName": "营销活动",
          "privDesc": "集客装维随销营销活动",
          "picId": "1000011",
          "picName": "imbanner3.png"
        }],
        money: undefined,
        currentMonth: undefined,
        serviceInfoList: [],
        serviceInfoValue: [],
      };
    },
    created() {
      this.userInfo = Storage.session.get('userInfo');
      this.getBusiness();
      // this.getBannerData();//获取轮播图
      this.checkLocation();//判断地理位置
      //this.initCashMode();//初始化实收
    },
    methods: {
      //选择月份
      // selectMonth(){
      //         this.$refs.picker.open();
      //         var pickerSlot = document.getElementsByClassName('picker-slot');
      //         pickerSlot[2].style.display = 'none'
      // },
      handleConfirm() {
        var d = new Date(this.dateVal);
        let getY = d.getFullYear();
        let getM = (d.getMonth() + 1) < 10 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1;
        this.monthNum = getY + '' + getM;
        //this.initCashMode();
      },
      //判断地理位置
      checkLocation() {
        //如果位置、纬度、经度为空则取服务端的地理位址
        if (!Storage.get('location') || !Storage.get('latitude') || !Storage.get('longitude')) {
          this.getLocationFromAldParent();
        }
      },
      // //获取banner图片
      // getBannerData(){
      //     let url = `/xsb/personBusiness/amsBanner/h5qryAmsBannerInfo?roleId=${this.userInfo.dwUserRole}`;
      //     this.$http.get(url).then(res => {
      //         let {retCode,retMsg,data} = res.data;
      //         if(retCode == '0'){
      //             console.info(data);
      //             this.bannerList = data
      //         } else {
      //             this.$toast(retMsg);
      //         }
      //     });
      // },
      //点击菜单图标
      businessCkPre(item) {
        if (item.privId == '0') {
          this.$alert("暂未开放");
          return;
        }
        let isNewFeature = item.isNewFeature;// 0：不用控制，1：控制
        if (isNewFeature == '1') {
          let featureType = item.featureType;//业务类型
          let param = {
            busiType: featureType
          }
          this.$http.post('/xsb/ability/businessLimit/h5QryBusiPermission', param).then(res => {
            //注意 retCode=0 有权限点击该菜单
            if (res.data.retCode == '0') {
              this.businessCk(item);
            } else {
              this.$alert(res.data.retMsg || '暂无权限')
            }
          })
        }else if(isNewFeature == '2'){
            //crm操作状态判断 ,若type有值则同时判断权限
            this.checkCrmStatus(item);
        } else {
          this.businessCk(item);
        }
      },

        //操作员工号校验
        async checkCrmStatus(item){
            let self = this;
            let uinfo = Storage.session.get("userInfo");
            if(!uinfo.crmId){
                this.$alert('当前岗位CRM工号为空');
                return false;
            }
            if(uinfo.crmStatus){
                if(uinfo.crmStatus != '1'){
                    this.$alert('当前操作员CRM工号'+uinfo.crmId+'状态无效，禁止办理业务');
                    return false;
                }
            }else{
                let orgRes= await self.qryOrgId();
                if(orgRes.data.retCode == '0'){
                    let orgData = orgRes.data.data;
                    if(orgData.orgId==""||orgData.orgId==undefined||orgData.orgId==null){
                        this.$alert('获取组织机构编号失败,暂无法办理业务');
                        return false;
                    }else{
                        uinfo.crmOrgId =orgData.orgId;
                        uinfo.crmStatus=orgData.status;//crm生效状态
                        uinfo.crmOrgName =orgData.orgName;
                        Storage.session.set('userInfo',uinfo);
                        if(uinfo.crmStatus != '1'){
                            this.$alert('当前操作员CRM工号【'+uinfo.crmId+'】状态无效，禁止办理业务');
                            return false;
                        }
                    }
                }else{
                    this.$alert(orgRes.data.retMsg || '获取组织机构编号失败,暂无法办理业务');
                    return false;
                }
            }

            let featureType = item.featureType;//业务类型
            if(featureType){
                let param = {
                    busiType:featureType
                }
                this.$http.post('/xsb/ability/businessLimit/h5QryBusiPermission',param).then(res => {
                    //注意 retCode=0 有权限点击该菜单
                    if(res.data.retCode == '0'){
                        self.enterAuthCheck(item);
                    } else {
                        self.$alert(res.data.retMsg || '暂无权限')
                    }
                })
            }else{
                self.enterAuthCheck(item);
            }

        },

        //获取组织机构编码
        async qryOrgId(){
            return this.$http.get('/xsb/personBusiness/chooseTelEnterNet/h5QryOperatorInfo');
        },

      //增值产品
      getProductZzInfo(privId, privName) {
        this.$router.push({
          path: 'valueAddOrderDaiwei',
          query: {
            privId: privId,
            privName: privName
          }
        });
      },
      //判断电视宽带开通情况
      checkBandTv(tel, privid, tvBandFlg) {
        let url = `/xsb/personBusiness/personInfo/h5QryAlreadyOpened?privid=100012&telnum=${tel}`;
        this.$http.get(url).then(res => {
          let {retCode, retMsg, data} = res.data;
          if (retCode == '0') {
            let isMband = data.isMband; // 是否开通宽带 0：否，1：是
            let isNetTv = data.isNetTv; // 是否开通互联网电视 0：否，1：是
            if (privid === '100012') {//宽带
              if (isMband == '1') {//开通过宽带 跳产品变更
                this.$router.push('/bandChange');
              } else {
                this.$router.push('/kuandaiKaiTong');
              }
            } else if (privid === '100013' || privid === '100032') {//电视
              if (isNetTv == '1') {
                //this.$alert('已开通过互联网电视！');
                let prodNetList = data.prodNetList;
                //跳第二台电视开通
                this.$router.push('/internetTvOpen?tvSort=2&alreadProd=' + JSON.stringify(prodNetList[0]));
              } else if (isNetTv == '2') {
                //this.$alert('已开通过2台互联网电视！');
                let prodNetList = data.prodNetList;
                let prodInfo = {};
                for (let item of prodNetList) {
                  if (item.pakageid == "2013000101" || item.pakageid == "2013000102") {
                    prodInfo = item;
                    break;
                  }
                }
                //跳第三台电视开通
                this.$router.push('/internetTvOpen?tvSort=3&alreadProd='+JSON.stringify(prodInfo));
                /*let param = {
                  busiType: "three_tv_open",
                }
                this.$http.post('/xsb/ability/businessLimit/h5QryBusiPermission', param).then(res => {
                  if(res.data.retCode == '0'){
                    let prodNetList = data.prodNetList;
                    let prodInfo = {};
                    for (let item of prodNetList) {
                      if (item.pakageid == "2013000101" || item.pakageid == "2013000102") {
                        prodInfo = item;
                        break;
                      }
                    }
                    //跳第三台电视开通
                    this.$router.push('/internetTvOpen?tvSort=3&alreadProd='+JSON.stringify(prodInfo));
                  } else {
                    this.$alert('已开通过2台互联网电视！');
                  }
                })*/
              } else if(isNetTv == '3'){
                this.$alert('已开通过3台互联网电视！');
              } else {
                if (isMband == '0') {//没开通宽带
                  this.$alert('请先开通宽带！');
                } else {
                  this.$router.push('/internetTvOpen?tvSort=1');
                }
              }
            } else if (privid === '100057') {
              if (isMband == '1') {//开通过宽带
                this.$router.push('/smartNetWork');
              } else {
                this.$alert('请先开通宽带');
              }
            } else if (privid === '100089') {
              if (isMband == '1') {//开通过宽带
                this.$router.push('/familyNetWork');
              } else {
                this.$alert('请先开通宽带');
              }
            }
          } else {
            this.$alert(retMsg || '查询开通宽带电视信息异常');
          }
        }).catch(res => {
          this.$alert('查询开通宽带电视信息网络异常:' + res);
        });
      },
      businessCk(item) {
        let webUrl = Storage.get('webUrl');
        let client = /android|harmony/gi.test(navigator.userAgent) ? 'ANDROID' : 'IOS'
        //外围渠道依然用行商的token拉起
        let url = `${webUrl}/iportal/tokenAuthen/genToken.do?opId=${item.opId}&prviId=${item.privId}&client_type=${client}`;
        url += `&regionId=${this.userInfo.region}&operId=${this.userInfo.staffId}&stationid=${this.userInfo.stationId}&phoneNumber=${this.userInfo.servNumber}`;
        //100013 电视开通 100012 宽带开通新 100057智能组网
        if (item.privId === '100012'
          || item.privId === '100013'
          || item.privId === '100032'
          || item.privId === '100057') {
          Authenct({
            popFlag: true,
            hasPwd: item.hasPwd,
            idCardWay: item.authenType == '1' ? true : false
          }, obj => {
            this.checkBandTv(obj.telnum, item.privId);
          });
          //增值产品
        } else if (item.privId === '100107'
          || item.privId === '100108'
          || item.privId === '100109'
          || item.privId === '100110'
          || item.privId === '100111'
          || item.privId === '100112') {
          Authenct({
            popFlag: true,
            hasPwd: item.hasPwd,
            idCardWay: item.authenType == '1' ? true : false
          }, obj => {
            this.getProductZzInfo(item.privId, item.privName);
          });
          //业务预约 及 预约记录
        } else if (item.privId === '100120' || item.privId === '100121') {
          if (item.privId === '100120') {
            this.$router.push('/salesappointment');
          }
          if (item.privId === '100121') {
            this.$router.push('/amsbusilog');
          }

        } else if (item.privId === '100072') {//新版和家固话
          let self = this;//判断是否开通宽带
          Authenct({
            popFlag: true,
            hasPwd: item.hasPwd,
            idCardWay: item.authenType == '1' ? true : false
          }, function (obj) {
            self.getGuHuaInfo(obj.telnum);
          });
        }else if(item.privId==='100233'){
          this.$router.push('/valueNetcomList?srcFrom=jiKeinstallMaintain')
        } else {
          if (item.opParentid != 'fsop') {//外围渠道依然用行商的token拉起
            this.$http.get(url).then((response) => {
              let data = response.data;
              let opUrl = data.opUrl;
              console.info(opUrl);
              ClientJs.openWebKit(encodeURIComponent(opUrl), '', '0', '', '', '', '', '', '', '', '');
            }).catch((response) => {
            })
          } else {
            this.goTokenAld(item);
          }
        }
      },
      //阿拉盯拉起菜单
      goTokenAld(item) {
        let client = /android|harmony/gi.test(navigator.userAgent) ? 'ANDROID' : 'IOS';
        let url = `/xsb/api-user/tokenAuthen/h5genToken.do?opId=${item.opId}&prviId=${item.privId}&clientType=${client}`;
        url += `&regionId=${this.userInfo.region}&operId=${this.userInfo.staffId}&stationId=${this.userInfo.stationId}&phoneNumber=${this.userInfo.servNumber}`;
        this.$http.get(url).then((response) => {
          let {retCode, retMsg, data} = response.data;
          if (retCode == '0') {
            let opUrl = data.opUrl;
            //校验token码
            this.$http.get(opUrl).then((res) => {
              let {retCode, retMsg, data} = res.data;
              if (retCode == '0') {//令牌鉴权成功
                console.info(data.url);
                let pageUrl = data.url;
                if (item.authenType == '-1') {//不用鉴权
                  this.$router.push(pageUrl);
                } else {
                  Authenct({
                    popFlag: true,
                    hasPwd: item.hasPwd,
                    idCardWay: item.authenType == '1' ? true : false
                  }, obj => {

                    this.$router.push(pageUrl);
                  });
                }
              } else {
                this.$alert(retMsg || '令牌token鉴权失败')
              }
            });
          } else {
            this.$alert(retMsg || '拉起token失败');
          }

        }).catch((response) => {
        })
      },
      //获取业务模块数据
      async getBusiness() {
        let data = await h5getMenuByLevel(`jk${this.userInfo.dwUserRole}`, '4:0');
        if (data.retCode == '0') {
          this.menuList = data.data;
        } else {
          this.menuList = [];
          this.$alert(data.retMsg || '获取工具菜单错误，请确认岗位');
        }
        // let url = `/xsb/api-user/menu/h5getMenuByLevel?stationId=jk${this.userInfo.dwUserRole}&level=4:0`;
        // this.$http.get(url).then((res) => {
        //   if (res.data.retCode == '0') {
        //     this.menuList = res.data.data;
        //   } else {
        //     this.menuList = [];
        //     this.$alert(res.data.retMsg || '获取工具菜单错误，请确认岗位');
        //   }
        // }).catch((response) => {
        //   this.$alert('获取菜单错误,' + response);
        // })
      },
      // //获取实收
      // initCashMode(){
      //     if(!this.monthNum){
      //         var d =new Date(new Date().setMonth((new Date().getMonth()-1)));
      //         let getY = d.getFullYear();
      //         let getM = (d.getMonth() + 1) < 10 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1;
      //         this.monthNum = getY +''+ getM;
      //     }
      //     let url = `/xsb/personBusiness/installServiceFee/h5qryServiceFeeCmop?mothNum=${this.monthNum}`;
      //     this.$http.get(url).then(res => {
      //         let {retCode,retMsg,data} = res.data;
      //         if(retCode == '0'){
      //                   this.money = data.money;
      //                   let month= data.currentMonth;
      //                   this.currentMonth = parseInt(month.substring(month.length - 2));
      //         } else {
      //             this.$toast(retMsg);
      //         }
      //     });
      //  },
      getGuHuaInfo(tel) {
        let url = `/xsb/personBusiness/guhua/h5getGuHuaInfo?telnum=${tel}`;
        this.$http.get(url).then(res => {
          let {retCode, retMsg, data} = res.data;
          if (retCode == '0') {
            if (data.bandFlag == '0') {
              this.$alert("请先开通宽带");
              return false;
            }
            if (data.imsFlag == '1') {
              this.$alert("已开通IMS固话，无法开通和家固话");
            } else if (data.imsNewFlag == '1' && data.hejiaFlag == '1') {
              this.$alert("已开通和家固话，无法重复开通");
            } else {
              this.$router.push({
                path: '/hejiaGuHuaChooseTel', query: {
                  hasHejia: data.hejiaFlag,
                  hasIms: data.imsNewFlag,
                }
              });
            }
          } else {
            this.$alert(retMsg || '查询是否已开通过和家固话信息失败');
          }
        }).catch(res => {
          this.$alert("查询是否已开通过和家固话信息异常:" + res);
        });
      },
    },
    filters: {
      //电话号码中间几位模糊化
      starTel(val){
        // 应付集团验收 start at 2023.10.10
        let uinfo = Storage.session.get('userInfo');
        let dwUserRole = uinfo.dwUserRole;
        if(dwUserRole != 'dw_1004'){//应付集团验收
            return val;
        }
        // 应付集团验收 end by qhuang
          if(!val){
              return '***';
          } else  {
              let reg = /^(\d{3})\d*(\d{4})$/;
              return val.replace(reg,'$1****$2')
          }
      },
       //工号中间几位模糊化
      starCrmId(val){
        // 应付集团验收 start at 2023.10.10
        let uinfo = Storage.session.get('userInfo');
        let dwUserRole = uinfo.dwUserRole;
        if(dwUserRole != 'dw_1004'){//应付集团验收
            return val;
        }
        // 应付集团验收 end by qhuang
        if(!val){
              return '1***3';
        } else  {
            let reg = /^(\d{2})\d*(\d{2})$/;
            return val.replace(reg,'$1****$2')
        }
      },
      starName(val){
          // 应付集团验收 start at 2023.10.10
          let uinfo = Storage.session.get('userInfo');
          let dwUserRole = uinfo.dwUserRole;
          if(dwUserRole != 'dw_1004'){//应付集团验收
              return val;
          }
          // 应付集团验收 end by qhuang
          if(!val){
              return '***';
          } else  {
              return val.substr(0,1) + new Array(val.length).join('*');
          }
      }
    }
  };
</script>

<style lang="less" scoped>
  .wrapper {
    width: 100%;
    background: rgba(255, 255, 255, 1);
    flex-grow: 1;
    overflow: auto;
  }

  .top-wrap {
    position: relative;
    width: 100%;
    height: 174px;
    box-sizing: border-box;

    .top-bg {
      height: 120px;
      background-size: 100% 100%;
    }

    .titletext {
      height: 27px;
      font-size: 20px;
      color: rgba(255, 255, 255, 1);
      line-height: 27px;
      padding: 14px 0 0 18px;
    }
  }

  .top-card {
    margin: -67px 18px 0 18px;
    padding: 12px 16px;
    box-sizing: border-box;
    height: 114px;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 5px 13px 0px rgba(164, 164, 164, 0.22);
    border-radius: 6px;
    border: 1px solid rgba(239, 239, 239, 1);
    overflow: hidden;
    position: relative;

    .yd-icon {
      position: absolute;
      left: 16px;
      top: 18px;
      width: 30px;

      img {
        width: 100%;
      }
    }

    .name-div {
      padding-left: 38px;

      .nametext {
        font-size: 18px;
        font-weight: 600;
        color: rgba(70, 70, 70, 1);
        line-height: 25px;
      }

      .posttext {
        font-size: 14px;
        font-weight: 400;
        color: rgba(70, 70, 70, 1);
        line-height: 20px;
      }
    }

    .oper-ul {
      margin-top: 18px;
      height: 28px;

      .oper-li {
        display: flex;
        justify-content: space-between;

        .title {
          font-size: 12px;
          font-weight: 400;
          color: rgba(70, 70, 70, 1);
          line-height: 12px;
        }

        .desc {
          font-size: 10px;
          color: rgba(201, 204, 207, 1);
          margin-top: 4px;
        }
      }
    }
  }

  .first-title {
    margin-top: 16px;
    margin-left: 16px;
    height: 25px;
    font-size: 18px;
    font-weight: 600;
    color: rgba(60, 60, 60, 1);
  }

  .banner-wrap {
    margin: 10px 16px 0 0;
    height: 70px;
    overflow: hidden;
    display: flex;

    .banner-item {
      flex: 1;
      margin-left: 16px;
      height: 69px;
      background-size: 100% 69px;

      .title {
        height: 20px;
        font-size: 14px;
        padding: 12px 0 0 12px;
        font-weight: 600;
        color: rgba(255, 255, 255, 1);
        line-height: 20px;
        text-shadow: 0px 2px 0px rgba(255, 255, 255, 0.22);
      }

      .span-lbl {
        display: inline-block;
        padding: 2px 4px;
        background: rgba(255, 255, 255, 1);
        border-radius: 5px;
        color: #fff;
        font-size: 10px;
        margin-left: 12px;
        margin-top: 4px;
      }

      .greenbg {
        background: #35A148;
        line-height: 15px
      }

      .bluebg {
        background: #0E8FD1;
        line-height: 15px
      }
    }
  }

  .cs-bnul {
    display: block;
    height: auto;
    overflow: hidden;
    margin: 16px 18px 0 16px;

    .cs-bnli {
      display: block;
      height: auto;
      overflow: hidden;
      margin-top: 16px;

      .cs-bnli-b {
        height: auto;
        overflow: hidden;
      }

      .first-menu {
        background: rgba(250, 250, 250, 1);
        border-radius: 8px;
        border: 1px solid rgba(241, 241, 241, 1);
      }
    }

    .title {
      height: 25px;
      font-size: 18px;
      font-weight: 600;
      color: rgba(60, 60, 60, 1);
      line-height: 25px;
      margin-bottom: 16px;
    }

  }

  .csb-lis {
    height: auto;
    width: 25%;
    overflow: hidden;
    margin-top: 10px;
    float: left;
    margin-bottom: 10px;
  }

  .cs-lis-icon {
    width: 40px;
    height: 40px;
    display: block;
    margin: 0 auto;

    img {
      width: 100%;
    }
  }

  .cs-lis-txt {
    height: 18px;
    font-size: 12px;
    font-weight: 400;
    margin-top: 4px;
    color: #646464;
    line-height: 18px;
    display: block;
    text-align: center;
  }

  .cash-div {
    position: absolute;
    right: 16px;
    top: 18px;

    .month-div {
      display: inline-block;
      position: relative;
      color: #F26000;
      border: 1px solid #F26000;
      padding: 2px 5px;
      font-size: 12px;
      bottom: 10px;
      margin-right: 4px;

      .f1lSize {
        font-size: 10px;
        transform: scale(0.5);
        display: inline-block;
      }
    }

    //.money-div{
    //     display: inline-block;
    //     padding-left: 9px;
    //     border-left: 1px solid #E4E4E4;
    //     font-size: 12px;
    //    .shishou{
    //       letter-spacing: 1px;
    //       line-height: 14px;
    //       font-weight: 600;
    //    }
    //    .yuan-span{
    //        font-size: 11px;
    //    }
    //    .money-span{
    //      color: #E03A19;
    //      font-weight: 600;
    //     font-size: 20px;
    //    }
    //}
  }


</style>
