<!--学习文档-->

<template>
  <div class="wrapper">
    <Header tsTitleTxt="学习文档" backType="custom" @emGoPrev="goPrev"></Header>
    <ul class="cs-bnul">
      <li class="cs-bnli" v-for="(item,index) in menuList" :key="index">
        <h1 class="title" v-show="item.itemList && item.itemList.length>0">
<!--          {{item.privName}}-->
          <span style="float: right; font-size: 13px;color: #007AFF" v-if="item.itemList&&item.itemList.length>8 &&(item.showTitleFlag)" @click="showOpen(item)">全部<i class="iconfont youjiantou" style="font-size: 13px"></i></span>
          <span style="float: right; font-size: 13px;color: #007AFF" v-if="item.itemList&&item.itemList.length>8 &&(!item.showTitleFlag)" @click="showOpen(item)">收起<i class="iconfont youjiantou" style="font-size: 13px"></i></span>
        </h1>
        <div class="cs-bnli-b" :class="{'first-menu':index==0}">
          <div class="csb-lis"  v-for="(itemInner,indexInner) in item.itemList" v-if="indexInner< item.showNumber" :key="indexInner">
                        <span class="cs-lis-icon" @click="businessCkPre(itemInner)">
                            <img src="static/business/00000.png"
                                 v-real-img="'./static/zwsxImg/'+itemInner.picId+'.png'">
                        </span>
            <span class="cs-lis-txt">{{itemInner.privName}}</span>
          </div>
        </div>
      </li>
    </ul>

  </div>
</template>

<script>
import Header from 'components/common/Header.vue'
import Storage from '@/base/storage'
import Authenct from 'components/common/Authenticate/Authenticate'
import ClientJs from '@/base/clientjs'
import { getRealUrl } from "@/base/utils"
import AuthenctKuanDai from 'components/common/AuthenticateKuanDai/Authenticate'

export default {
  components: {Header},
  data() {
    return {
      menuList: [],
      userInfo:{},
    };
  },
  created() {
    this.userInfo = Storage.session.get('userInfo');
    this.getBusiness();
  },
  methods: {
    goPrev(){
      history.go(-1)
    },
    //点击菜单图标
    businessCkPre(item){
      let isNewFeature = item.isNewFeature;// 0：不用控制，1：控制
      if(isNewFeature == '1'){
        let featureType = item.featureType;//业务类型
        let param = {
          busiType:featureType
        }
        this.$http.post('/xsb/ability/businessLimit/h5QryBusiPermission',param).then(res => {
          //注意 retCode=0 有权限点击该菜单
          if(res.data.retCode == '0'){
            this.businessCk(item);
          } else {
            this.$alert(res.data.retMsg || '暂无权限')
          }
        })
      }else if(isNewFeature == '2'){
          //crm操作状态判断 ,若type有值则同时判断权限
          this.checkCrmStatus(item);
      }   else {
        this.businessCk(item);
      }
    },

      //操作员工号校验
      async checkCrmStatus(item){
          let self = this;
          let uinfo = Storage.session.get("userInfo");
          if(!uinfo.crmId){
              this.$alert('当前岗位CRM工号为空');
              return false;
          }
          if(uinfo.crmStatus){
              if(uinfo.crmStatus != '1'){
                  this.$alert('当前操作员CRM工号'+uinfo.crmId+'状态无效，禁止办理业务');
                  return false;
              }
          }else{
              let orgRes= await self.qryOrgId();
              if(orgRes.data.retCode == '0'){
                  let orgData = orgRes.data.data;
                  if(orgData.orgId==""||orgData.orgId==undefined||orgData.orgId==null){
                      this.$alert('获取组织机构编号失败,暂无法办理业务');
                      return false;
                  }else{
                      uinfo.crmOrgId =orgData.orgId;
                      uinfo.crmStatus=orgData.status;//crm生效状态
                      uinfo.crmOrgName =orgData.orgName;
                      Storage.session.set('userInfo',uinfo);
                      if(uinfo.crmStatus != '1'){
                          this.$alert('当前操作员CRM工号【'+uinfo.crmId+'】状态无效，禁止办理业务');
                          return false;
                      }
                  }
              }else{
                  this.$alert(orgRes.data.retMsg || '获取组织机构编号失败,暂无法办理业务');
                  return false;
              }
          }

          let featureType = item.featureType;//业务类型
          if(featureType){
              let param = {
                  busiType:featureType
              }
              this.$http.post('/xsb/ability/businessLimit/h5QryBusiPermission',param).then(res => {
                  //注意 retCode=0 有权限点击该菜单
                  if(res.data.retCode == '0'){
                      self.businessCk(item);
                  } else {
                      self.$alert(res.data.retMsg || '暂无权限')
                  }
              })
          }else{
              self.businessCk(item);
          }

      },

      //获取组织机构编码
      async qryOrgId(){
          return this.$http.get('/xsb/personBusiness/chooseTelEnterNet/h5QryOperatorInfo');
      },

    //增值产品
    getProductZzInfo(privId,privName){
      this.$router.push({
        path: 'valueAddOrderDaiwei',
        query: {
          privId: privId,
          privName: privName,
          srcFrom:  'installMaintain'
        }
      });
    },
    //
    businessCk(item){
      let webUrl = Storage.get('webUrl');
      let client = /android|harmony/gi.test(navigator.userAgent)?'ANDROID':'IOS'
      //外围渠道依然用行商的token拉起
      let url = `${webUrl}/iportal/tokenAuthen/genToken.do?opId=${item.opId}&prviId=${item.privId}&client_type=${client}`;
      url += `&regionId=${this.userInfo.region}&operId=${this.userInfo.staffId}&stationid=${this.userInfo.stationId}&phoneNumber=${this.userInfo.servNumber}`;
      //100013 电视开通 100012 宽带开通新 100057智能组网
      if(item.privId === '100012'
        || item.privId === '100013'
        || item.privId === '100032'
        || item.privId === '100057'){
        //互联网电视支持宽带账号 20220112 qp
        if(item.privId === '100013'){
          AuthenctKuanDai({
            popFlag:true,
            hasPwd:item.hasPwd,
            idCardWay:item.authenType == '1' ? true : false
          }, obj => {
           // this.checkBandTv(obj.telnum,item.privId);
          });
        }else {
          Authenct({
            popFlag: true,
            hasPwd: item.hasPwd,
            idCardWay: item.authenType == '1' ? true : false
          }, obj => {
          //  this.checkBandTv(obj.telnum, item.privId);
          });
        }
        //增值产品
      }else if(item.privId === '100107'
        || item.privId === '100108'
        || item.privId === '100109'
        || item.privId === '100110'
        || item.privId === '100111'
        || item.privId === '100112'){
        Authenct({
          popFlag:true,
          hasPwd:item.hasPwd,
          idCardWay:item.authenType == '1' ? true : false
        }, obj => {
          this.getProductZzInfo(item.privId,item.privName);
        });
        //业务预约 及 预约记录
      }else if(item.privId === '100120' || item.privId === '100121' ){
        if(item.privId === '100120'){
          this.$router.push('/salesappointment?srcFrom=installMaintain');
        }
        if(item.privId === '100121'){
          this.$router.push('/amsbusilog?srcFrom=installMaintain');
        }

      }else if(item.privId === '100072'){//新版和家固话
        let self = this;//判断是否开通宽带
        Authenct({
          popFlag:true,
          hasPwd:item.hasPwd,
          idCardWay:item.authenType == '1' ? true : false
        },function (obj){
          self.getGuHuaInfo(obj.telnum);
        });
      }else if(item.privId ==='100213'){ //杭研WIFI热力图功能
        let url = `${getRealUrl('hotWifi')}`;
        ClientJs.openWebKit(encodeURIComponent(url), 'WIFI热力图', '1', '', '', '', '', '', '', '', '');
      }else if(item.privId === '100281'
        || item.privId === '100282'
        || item.privId === '100283'
        || item.privId === '100284'
        || item.privId === '100285'
        || item.privId === '100286'
        || item.privId === '100292' ){  // 数据业务专区 - 爱车权益优惠
        if(item.authenType == '-1'){//学习园地
          this.getProductData(item.privId);
        } else {    //增值产品+促销活动
          Authenct({
            popFlag:true,
            hasPwd:item.hasPwd,
            idCardWay:item.authenType == '1' ? true : false
          }, obj => {
            this.getProductData(item.privId);
          });
        }
      } else {
        if(item.opParentid != 'fsop'){//外围渠道依然用行商的token拉起
          this.$http.get(url).then((response)=>{
            let data = response.data;
            let opUrl = data.opUrl;
            console.info(opUrl);
            ClientJs.openWebKit(encodeURIComponent(opUrl), '', '0', '', '', '', '', '', '', '', '');
          }).catch((response)=>{
          })
        } else {
          this.goTokenAld(item);
        }
      }
    },
    //阿拉盯拉起菜单
    goTokenAld(item){
      let client = /android|harmony/gi.test(navigator.userAgent)?'ANDROID':'IOS';
      let  url = `/xsb/api-user/tokenAuthen/h5genToken.do?opId=${item.opId}&prviId=${item.privId}&clientType=${client}`;
      url += `&regionId=${this.userInfo.region}&operId=${this.userInfo.staffId}&stationId=${this.userInfo.stationId}&phoneNumber=${this.userInfo.servNumber}`;
      this.$http.get(url).then((response)=>{
        let {retCode,retMsg,data} = response.data;
        if(retCode == '0'){
          let opUrl = data.opUrl;
          //校验token码
          this.$http.get(opUrl).then((res)=>{
            let {retCode,retMsg,data} = res.data;
            if(retCode == '0'){//令牌鉴权成功
              console.info(data.url);
              let pageUrl = data.url;
              if(pageUrl && ~pageUrl.indexOf('?')){
                pageUrl += '&srcFrom=installMaintain'
              } else {
                pageUrl += '?srcFrom=installMaintain'
              }
              if(item.authenType == '-1'){//不用鉴权
                this.$router.push(pageUrl);
              } else {
                Authenct({
                  popFlag:true,
                  hasPwd:item.hasPwd,
                  idCardWay:item.authenType == '1' ? true : false
                },obj => {
                  if(item.privId == '100135'){
                    this.addRecommdPoint();
                  }
                  this.$router.push(pageUrl);
                });
              }
            } else {
              this.$alert(retMsg || '令牌token鉴权失败')
            }
          });
        } else {
          this.$alert(retMsg || '拉起token失败');
        }

      }).catch((response)=>{
      })
    },
    //获取业务模块数据
    getBusiness(){
      let url = `/xsb/api-user/menu/h5SearchMenuByMenuId?menuId=100293`;
      this.$http.get(url).then((res)=>{
        if(res.data.retCode == '0'){
          this.menuList = res.data.data;
          this.menuList.forEach((item) => {
            this.$set(item, 'showTitleFlag', true); //默认收起下拉
            this.$set(item, 'showNumber', 8); //默认收起下拉
          });
          console.log('菜单数据',this.menuList);
        } else {
          this.menuList = [];
          this.$alert(res.data.retMsg || '获取工具菜单错误，请确认岗位');
        }
      })
        .catch((response)=>{
        this.$alert('获取菜单错误,' +　response);
      })
    },

    getGuHuaInfo(tel){
      let url=`/xsb/personBusiness/guhua/h5getGuHuaInfo?telnum=${tel}`;
      this.$http.get(url).then(res =>{
        let {retCode,retMsg,data} = res.data;
        if(retCode=='0'){
          if(data.bandFlag=='0'){
            this.$alert("请先开通宽带");
            return false;
          }
          if(data.imsFlag=='1'){
            this.$alert("已开通IMS固话，无法开通和家固话");
          }else if(data.imsNewFlag=='1'&&data.hejiaFlag=='1'){
            this.$alert("已开通和家固话，无法重复开通");
          }else{
            this.$router.push({path:'/hejiaGuHuaChooseTel',query:{
                hasHejia:data.hejiaFlag,
                hasIms:data.imsNewFlag,
                srcFrom:'installMaintain'
              }});
          }
        }else{
          this.$alert(retMsg||'查询是否已开通过和家固话信息失败');
        }
      }).catch(res=>{
        this.$alert("查询是否已开通过和家固话信息异常:"+res);
      });
    },
    //活动推荐积分
    addRecommdPoint(){
      let param = {
        "daiweiId": this.userInfo.dwUser,
        "daiweiName": this.userInfo.dwName,
        "isRecommend":"1",
        "servnumber": this.userInfo.dwMobile
      }
      let url = '/xsb/api-user/amsLogin/h5updBusiPoint';
      this.$http.post(url,param).then(res => {
        if(res.data.retCode != '0'){
          this.$alert(res.data.retMsg || '活动推荐积分更新失败')
        }
      })
    },
    //展示收起
    showOpen(item){
      item.showTitleFlag = !item.showTitleFlag;
      if(item.showTitleFlag){
        this.$set(item, 'showNumber', 8); //默认展示8个
      }else{
        this.$set(item, 'showNumber', item.itemList.length); //展示全部
      }
    },
    //获取数据业务专区的产品信息
    getProductData(privId){
      let url = `/xsb/personBusiness/amsBanner/h5getAmsProductInfo?privId=${privId}`;
      this.$http.get(url).then(res => {
        let {retCode,retMsg,data} = res.data;
        if(retCode == '0'){
          this.productList = data;
          if(this.productList && this.productList.length > 0){
            let info = this.productList[0];
            if(info.type == '1'){   //订购增值产品
              this.$router.push({
                path: 'valueAddedProduct',
                query: {
                  infoList: this.productList,
                  srcFrom:  'installMaintain'
                }
              });
            }else if(info.type == '2'){   //跳转促销活动
              this.$router.push({
                path: 'promotionPici',
                query: {
                  srcFrom: 'installMaintain',
                  isDabao: 'N',
                  privId: info.privId,
                  privName: info.productName
                }
              });
            }else if(info.type == '3'){   //跳转学习园地
              let content = info.content1;
              if(info.content2){
                content += info.content2;
              }
              if(info.content3){
                content += info.content3;
              }
              this.$router.push({
                name: 'StudyCenterDetail',
                query: {
                  privName: info.privName,
                  knowledgeId: info.privId,
                  title: info.title,
                  releaserName: info.releaseName,
                  releaseTime: info.releaseTime,
                  content: content,
                  fileUrl: info.fileUrl,
                  fileName: info.fileName,
                  srcFrom: 'installMaintain'
                }
              });
            }
          }
        } else {
          this.$toast(retMsg);
        }
      });
    },
  },
  filters: {}
};
</script>

<style lang="less" scoped>
.wrapper {
  width: 100%;
  background: rgba(255, 255, 255, 1);
  flex-grow: 1;
  overflow: auto;
}
.cs-bnul{
  display: block;
  height: auto;
  overflow: hidden;
  margin:16px 18px 0 16px;
  .cs-bnli{
    display: block;
    height: auto;
    overflow: hidden;
    margin-top:50px;
    .cs-bnli-b{
      height: auto;
      overflow: hidden;
    }
    .first-menu{
      background: rgba(250, 250, 250, 1);
      border-radius: 8px;
      border: 1px solid rgba(241, 241, 241, 1);
    }
  }

  .title{
    height: 25px;
    font-size: 18px;
    font-weight: 600;
    color: rgba(60, 60, 60, 1);
    line-height: 25px;
    margin-bottom: 16px;
  }

}
.csb-lis{
  height: auto;
  width: 25%;
  overflow: hidden;
  margin-top:10px;
  float:left;
  margin-bottom: 10px;
}
.cs-lis-icon{
  width: 40px;
  height: 40px;
  display: block;
  margin:0 auto;
  img{
    width: 100%;
  }
}

.cs-lis-txt{
  height:18px;
  font-size:12px;
  font-weight:400;
  margin-top:4px;
  color:#646464;
  line-height:18px;
  display: block;
  text-align: center;
}



</style>
