<template>
    <div class='pbo-wrap' :class='topType2 == 0? "pbo-wrap2" : ""'>
        <Header tsTitleTxt='AI商机助理' backType='custom' tsBtnTxt='商机录入' @emBtnCk='goEnterPage(null)'
                @emGoPrev='goPrev' />

        <mt-popup
            v-model='showDialogFlag'
            position='bottom'>
            <div class='chooseDialog' v-if='showDialogFlag'>
                <div class='title'>请选择本次录入的商机类型 <span @click='showDialogFlag = false' class='iconfont guanbi'></span></div>

                <div class='btnlist'>
                    <div @click="goEnterPage('1')">融合服务</div>
                    <div @click="goEnterPage('3')">团单商机</div>
                    <div @click="goEnterPage('2')">业务商机</div>
                    <div v-show='countyIdList && countyIdList.length > 0' class='item-btn' @click="goEnterPage('4')">融合服务<p>（直办）</p></div>
                </div>
            </div>
        </mt-popup>
        <FormHP v-show='hpFlg' :tableInfo='tableInfo' :tableName='tableName' @changeHP='changeHP' @getKBtable='getKBtable' @changeFanwei='changeFanwei'/>

        <div class='search-box'>
            <div class='tab-sj'>
                <div class='item' v-show='kanban.viewFlag == 1' :class="topType2 == 0? 'active': ''" @click='topType2 = 0'>商机看板</div>
                <div class='item' :class="topType2 == 1? 'active': ''" @click='topType2 = 1'>我的商机</div>
                <div class='item' :class="topType2 == 2? 'active': ''" @click='topType2 = 2'>商机池({{ emptylist.length }})</div>
            </div>

            <!--我的商机搜索框-->
            <div class='search-input' v-show='topType2 == 1'>
                <div class='search-input1'><input type='text' v-model='teltext' :placeholder="'请输入商机主体、创建人号码等'" /></div>
                <span class='iconfont sousuo1'></span>
                <span class='sou' id='aiShangjiSearch' @click='pageNum = 1;getListMy()'>搜索</span>
                <span class='iconfont guanbi1' @click="teltext = '';pageNum = 1;getListMy()"></span>
            </div>
            <!--商机池搜索框-->
            <div class='search-input' v-show='topType2 == 2'>
                <div class='search-input1'><input type='text' v-model='teltext2' :placeholder="'请输入号码'" /></div>
                <span class='iconfont sousuo1'></span>
                <span class='sou' id='shangjiChiSearch' @click='getListEmpty()'>搜索</span>
                <span class='iconfont guanbi1' @click="teltext2 = '';getListEmpty()"></span>
            </div>

            <!--我的商机下拉-->
            <div class='drop-drown' v-show='topType2 == 1'>
                <div id='aiShangjiFusion' class='item' @click='changeSearchType0()'>{{ type0.label }}<span class='iconfont xiala'></span></div>
                <div id='aiShangjiStatus' class='item' @click='changeSearchType1()'>{{ type1.label }}<span class='iconfont xiala'></span></div>
                <div id='aiShangjiSource' class='item' v-show='type0.id != 3' @click='changecomFormType1'>{{ comFormType1 ? comFormType1 : '商机来源' }}<span class='iconfont xiala'></span></div>
                <div id='aiShangjiRecordTime' class='item' @click='changeSearchTime1'>{{ chooseTime1 ? '创建时间' : '创建时间' }}<span class='iconfont xiala'></span></div>
            </div>

            <!--商机池下拉-->
            <div class='drop-drown' v-show='topType2 == 2'>
                <div id='shangjiChiAllType' class='item' @click='changeSearchType3()'>{{ type3.label }}<span class='iconfont xiala'></span></div>
                <div id='shangjiChiSource' class='item' @click='changecomFormType2'>{{ comFormType2 ? comFormType2 : '商机来源' }}<span class='iconfont xiala'></span></div>
                <div id='shangjiChiCreateTime' class='item' @click='changeSearchTime2'>{{ chooseTime2 ? '创建时间' : '创建时间' }}<span class='iconfont xiala'></span></div>
                <div id='shangjiChiEnterTime' class='item' @click='changeSearchTime3'>{{ chooseTime3 ? '入池时间' : '入池时间' }}<span class='iconfont xiala'></span></div>
            </div>

            <div class='search-btns' v-show='topType2 == 1'>
                <div class='item' id='aiShangjiContractExpire' :style='type0.id == 3? "width: 33.33%" : ""' @click='changecomFormType3'>{{ comFormType3 ? comFormType3 : '合约到期' }}<span class='iconfont xiala'></span></div>
                <div class='item' id='aiShangjiIsDirect' :style='type0.id == 3? "width: 33.33%" : ""' @click='changecomFormType4'>{{ comFormType4 ? comFormType4 : '是否直办' }}<span class='iconfont xiala'></span></div>
            </div>

            <div class='search-btns' v-show='topType2 == 2'>
                <span class='title'>合约到期</span>
                <div class='btns'>
                    <span id='shangjiChiExp1M' @click='changeHY2(1)' :class='HYtime2 == "1"? "active" : ""'>近一个月</span>
                    <span id='shangjiChiExp2M' @click='changeHY2(2)' :class='HYtime2 == "2"? "active" : ""'>近两个月</span>
                    <span id='shangjiChiExp3M' @click='changeHY2(3)' :class='HYtime2 == "3"? "active" : ""'>近三个月</span>
                </div>
            </div>
        </div>


        <div class='just-box'>
            <!--看板数据-->
            <div class='kanban-box' v-show='topType2 == 0'>
                <div class='text'>
                    <div class='left'><span class='iconfont a-zu564625'></span><span class='label'>报表类型:</span><div class='time' @click='changeKBType'>{{ kanban.type }} <span class='iconfont youjiantou2'></span></div>
                    </div>
                    <div class='left'><span class='iconfont yuyue1'></span><span class='label'>统计日期:</span><div class='time' @click='changeKBtime'>{{ formatDate(kanban.today) }} <span class='iconfont youjiantou2'></span></div>
                    </div>
                    <div class='right'><span class='iconfont shaixuan-mianxing'></span><span class='label'>统计范围:</span>
                        <span v-for='(item,idx) in tableName' @click='changeFanwei(item,idx)'>{{item ? item.orgName : ''}} <span v-show='idx != tableName.length-1' class='iconfont youjiantou2'></span></span>
                        <span v-if='tableInfo.length > 0' class='hp-btn' @click='changeHP'><span class='qiehuan1 iconfont'></span>横屏</span>
                    </div>
                </div>


                <!-- 基本格式 -->
                <div class='table-box'>
                    <div v-show='!hpFlg' class="right-tbl-wrapper " ref="rightDiv"  v-if='tableInfo.length > 0'>
                        <table class="left-tbl">
                            <thead>
                            <tr class="first-tr">
                                <th class="f-th">组织</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr class="other-tr" v-for='(item,idx) in tableInfo'  :class='idx == tableInfo.length-1 ? "color-table" :""'>
                                <td @click='item.orgLevel < 4 && idx != tableInfo.length-1 && getKBtable(item.orgId) ' :class='item.orgLevel < 4 ? "active" : ""' class="other-td">{{item.orgName}}</td>
                            </tr>
                            </tbody>
                        </table>

                        <table class="right-tbl">
                            <thead>
                            <tr class="first-tr">
                                <th class="f-th">当天新增</th>
                                <th class="f-th">本月新增</th>
                                <th class="f-th">累计新增</th>
                                <th class="f-th">当月转化</th>
                                <th class="f-th">累计转化</th>
                                <th class="f-th">累计转化率</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr class="other-tr" v-for='(item,idx) in tableInfo' :class='idx == tableInfo.length-1 ? "color-table" :""'>
                                <td class="other-td">{{item.mea1 ? item.mea1 : '/'}}</td>
                                <td class="other-td">{{item.mea2 ? item.mea2 : '/'}}</td>
                                <td class="other-td">{{item.mea3 ? item.mea3 : '/'}}</td>
                                <td class="other-td">{{item.mea4 ? item.mea4 : '/'}}</td>
                                <td class="other-td">{{item.mea5 ? item.mea5 : '/'}}</td>
                                <td class="other-td">{{item.mea6 ? convertToPercentage(item.mea6) : '/'}}</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <no-data-page v-if='tableInfo.length == 0'/>
                </div>


            </div>
            <div class='all-num all-num2' v-show='topType2 == 1'>总数: {{ totalMy }}</div>
            <div class='all-num all-num2' v-show='topType2 == 2'>总数: {{ emptylist.length }}</div>

            <!--我的商机列表-->
            <div class='lists-box lists-box2 lists-box3' v-show='topType2 == 1'>
                <mt-loadmore
                    :top-method="loadTopAccredit"
                    :bottom-method="loadBottomAccredit"
                    :bottom-all-loaded="tsAllLoaded"
                    :auto-fill="false"
                    topPullText="刷新"
                    topDropText="释放更新"
                    topLoadingText="刷新中···"
                    bottomPullText="加载更多"
                    bottomDropText="释放加载"
                    bottomLoadingText="加载中···"
                    ref="loadmoref">
                    <div style='overflow: hidden' >
                        <div class='list-item'  v-for='(item,idx) in mylist' :key='idx' >
                            <div class='line1'>
                                <span class='tel' :class='item.busiType == 3? "tel-group" : ""'>{{ item.busiTel }}
                                    <div class='xiansuo' v-show='item.isMessageVerify != "true" && item.busiType == 1'>线索</div>
                                    <div class='top-box' :style='item.isMessageVerify != "true" ? "margin-left: 29px;": ""'>
<!--                                        <div class='go-top'>置顶<span class='iconfont shangsheng'></span></div>-->
<!--                                        <div class='end-top'>取消置顶<span class='iconfont xiajiang'></span></div>-->
                                    </div>
                                </span>
                                <span class='tag tag2' v-show='item.buopStatus == 2'>跟进中</span>
                                <span class='tag tag0' v-show='item.buopStatus == 5'>待接单</span>
                                <span class='tag tag3' v-show='item.buopStatus == 6'>已结单</span>
                                <span class='tag tag0' v-show='item.buopStatus == 7'>待补充</span>
                                <span class='tag tag0' v-show='item.buopStatus == 8'>待指派</span>
                                <span class='tag tag0' v-show='item.buopStatus == 9'>超时结单</span>
                                <span class='tag tag7' v-show='item.buopStatus == 10'>待人工稽核</span>
                                <span class='tag tag5' v-show='item.buopStatus == 11'>人工稽核失败</span>
                                <span class='tag tag33' v-show='item.buopStatus == 12'>成功结单</span>
                                <span class='tag tag6' v-show='item.buopStatus == 13'>失败结单</span>
                                <span class='tag tag7' v-show='item.buopStatus == 15'>审核待补充</span>
                                <span class='tag tag7' v-show='item.buopStatus == 16'>补单待审核</span>
                            </div>
                            <div class='tags-box' v-show='item.busiType == 1 || item.busiType == 2'>
                                <div class='tags tags1' v-show='item.busiType'>{{item.busiType == 1 ? '融合服务' : '业务商机'}}</div>
                                <div class='tags tags1' v-show='item.busiSource'>{{getLaiyuan(item.busiSource)}}</div>
                                <div class='tags' v-show='item.busiExpireTime' :class='getDateStatus(item.busiExpireTime) && getDateStatus(item.busiExpireTime).indexOf("-")>0? "tags2" : "tags3"'>{{getDateStatus(item.busiExpireTime)}}</div>
                                <div class='tags' v-show='item.serviceDate' :class='getDateStatus2(item.serviceDate) && getDateStatus2(item.serviceDate).indexOf("-")>0? "tags2" : "tags3"'>{{getDateStatus2(item.serviceDate)}}</div>
                                <div class='tags' v-show='item.businServiceDate' :class='getDateStatus2(item.businServiceDate) && getDateStatus2(item.businServiceDate).indexOf("-")>0? "tags2" : "tags3"'>{{getDateStatus2(item.businServiceDate)}}</div>
                            </div>

                            <div class='line2' v-show='item.countyName'>
                                <span class='title'>归属区县：</span>
                                <span class='value'>{{ item.countyName }}</span>
                            </div>
                            <div class='line2' v-show=' item.assistOper && item.assistTel'>
                                <span class='title'>协销人员：</span>
                                <span class='value'>{{ item.assistOper }}<span v-show='item.assistTel'>({{ item.assistTel}})</span></span>
                            </div>
                            <div class='line2' v-show='!item.assistOper && item.assistTel'>
                                <span class='title'>协销人员：</span>
                                <span class='value'><span v-show='item.assistTel'>{{ item.assistTel}}</span></span>
                            </div>
                            <div class='line2' v-show='item.createOper && (item.createTel != item.followTel)'>
                                <span class='title'>创建人：</span>
                                <span class='value'>{{ item.createOper }}<span v-show='item.createTel'>({{ item.createTel }})</span></span>
                            </div>

                            <div class='line2' v-show='item.createTime'>
                                <span class='title'>创建时间：</span>
                                <span class='value'>{{ item.createTime }}</span>
                            </div>
                            <div class='line2' v-show='item.reportDate'>
                                <span class='title'>到岗时间：</span>
                                <span class='value'>{{item.reportDate}}</span>
                            </div>
                            <div class='line2' v-show='item.followTel &&  (item.createTel != item.followTel)'>
                                <span class='title'>当前跟进人：</span>
                                <span class='value'>{{ item.followBy ? item.followBy : '--' }}<span v-show='item.followTel'>({{ item.followTel }})</span></span>
                            </div>
                            <div class='line2' v-show='item.createOper && item.followTel &&  (item.createTel == item.followTel)'>
                                <span class='title'>创建/跟进人：</span>
                                <span class='value'>{{ item.followBy ? item.followBy : '--' }}<span v-show='item.followTel'>({{ item.followTel }})</span></span>
                            </div>

                            <!--个人--操作按钮-->
                            <div class='box-btn' v-show='item.busiType == 1'>
                                <div class='control-item' @click='personJieDan(item)'
                                     v-show='showpersonJieDan(item) && item.buopStatus != 12  && item.buopStatus != 10 && item.buopStatus != 13 && item.buopStatus != 15 && item.buopStatus != 16 && item.buopStatus != 7'>
                                    <span class='iconfont yuyue'></span>结单
                                </div>
                                <div class='control-item' @click='goPersonWeihu(item,0)'
                                     v-show='showPersonChakan(item) && item.buopStatus != 10 && item.buopStatus != 15 && item.buopStatus != 16'><span
                                    class='iconfont xingzhuang5'></span>查看
                                </div>
                                <div class='control-item' @click='goPersonSuanzhang(item,1)'
                                     v-show='item.buopStatus == 2 && userInfo.servNumber == item.followTel'><span
                                    class='iconfont a-yonghusuanfei3x'></span>算帐
                                </div>

                                <!--个人-跟进中 -->
                                <div v-show='item.buopStatus == 2 '>
                                    <!--当前操作员==跟进人-->
                                    <div v-if='userInfo.servNumber == item.followTel'>
                                        <div class='control-item' @click='personZhuanPai(item)'><span
                                            class='iconfont zhanghaoqiehuan'></span>转派
                                        </div>

                                        <div class='control-item' @click='goPersonWeihu(item,1)'><span
                                            class='iconfont tools'></span>维护
                                        </div>
                                    </div>
                                </div>

                                <!--个人-待补充 当前操作员==跟进人-->
                                <div v-show='item.buopStatus == 7 '>
                                    <!--当前操作员==跟进人-->
                                    <div v-if='userInfo.servNumber == item.followTel'>
                                        <div class='control-item' @click='goPersonWeihu(item,1,1)'><span class='iconfont a-zu5638111x'></span>补充</div>
                                    </div>
                                </div>

                                <!--个人-审核待补充 当前操作员==跟进人-->
                                <div v-show='item.buopStatus == 15 '>
                                    <!--当前操作员==跟进人-->
                                    <div v-if='userInfo.servNumber == item.followTel'>
                                        <div class='control-item' @click='goPersonWeihu(item,1,1,"审核待补充")'><span class='iconfont a-zu5638111x'></span>补充</div>
                                    </div>
                                    <div v-if='userInfo.servNumber != item.followTel'>
                                        <div class='control-item' @click='goPersonWeihu(item,0,null,"审核待补充")'><span class='iconfont a-zu5638111x'></span>查看</div>
                                    </div>
                                </div>

                                <!--个人-补单待审核 当前操作员==跟进人-->
                                <div v-show='item.buopStatus == 16'>
                                    <div class='control-item' @click='goPersonWeihu(item,0,null,"审核待补充")'><span class='iconfont a-zu5638111x'></span>查看</div>
                                </div>


                                <!--个人-待人工稽核 || 个人-失败结单-->
                                <div v-show='item.buopStatus == 10 || item.buopStatus == 13'>
                                    <div class='control-item' @click='goPersonWeihu(item,0)'><span
                                        class='iconfont xingzhuang5'></span>查看
                                    </div>
                                </div>

                                <!--个人-人工稽核失败-->
                                <div v-show='item.buopStatus == 11'>
                                    <!--个人-当前操作员==跟进人-->
                                    <div v-if='userInfo.servNumber == item.followTel'>
                                        <div class='control-item' @click='goPersonWeihu(item,1)'><span
                                            class='iconfont tools'></span>维护
                                        </div>
                                        <div class='control-item' @click='personZhuanPai(item)'><span
                                            class='iconfont zhanghaoqiehuan'></span>转派
                                        </div>
                                        <div class='control-item' @click='personJieDan(item)'><span
                                            class='iconfont yuyue'></span>结单
                                        </div>
                                    </div>
                                    <!--个人-当前操作员!=跟进人-->
                                    <div v-if='userInfo.servNumber != item.followTel'>
                                        <div class='control-item' @click='goPersonWeihu(item,0)'><span
                                            class='iconfont xingzhuang5'></span>查看
                                        </div>
                                    </div>
                                </div>

                                <!--个人-成功结单-->
                                <div v-show='item.buopStatus == 12'>
                                    <div class='control-item' @click='goPersonWeihu(item,0)'><span
                                        class='iconfont xingzhuang5'></span>查看
                                    </div>
                                </div>
                            </div>

                            <!--业务--操作按钮-->
                            <div class='box-btn' v-show='item.busiType == 2'>
                                <!--当前操作员==跟进人-->
                                <div
                                    v-if='userInfo.servNumber == item.followTel && item.buopStatus != 7  && item.buopStatus != 10 && item.buopStatus != 13 && item.buopStatus != 12 && item.buopStatus != 15 && item.buopStatus != 16'>
                                    <div class='control-item' @click='yewuJieDan(item)'><span
                                        class='iconfont xingzhuang5'></span>结单
                                    </div>
                                    <div class='control-item' @click='personZhuanPai(item,"view")'><span
                                        class='iconfont xingzhuang5'></span>转派
                                    </div>
                                    <div class='control-item' @click='goYewuView(item,"change")'><span
                                        class='iconfont xingzhuang5'></span>维护
                                    </div>
                                </div>

                                <!--当前操作员==跟进人-->
                                <div v-if='userInfo.servNumber != item.followTel && item.buopStatus != 7 && item.buopStatus != 10 && item.buopStatus != 13 && item.buopStatus != 12 && item.buopStatus != 15 && item.buopStatus != 16'>
                                    <div class='control-item' @click='goYewuView(item,"view")'><span
                                        class='iconfont xingzhuang5'></span>查看
                                    </div>
                                </div>


                                <div v-if='item.buopStatus == 7 '>
                                    <div class='control-item' v-if='userInfo.servNumber == item.followTel ' @click='yewuJieDan(item)'><span class='iconfont xingzhuang5'></span>结单</div>
                                    <div class='control-item' v-if='userInfo.servNumber == item.followTel ' @click='goYewuView(item,"supplement")'><span class='iconfont xingzhuang5'></span>补充</div>
                                    <div class='control-item' v-if='userInfo.servNumber != item.followTel && userInfo.servNumber == item.createTel' @click='goYewuView(item,"view")'><span class='iconfont xingzhuang5'></span>查看</div>
                                </div>


                                <!--业务-审核待补充 当前操作员==跟进人-->
                                <div v-show='item.buopStatus == 15 '>
                                    <!--当前操作员==跟进人-->
                                    <div v-if='userInfo.servNumber == item.followTel'>
                                        <div class='control-item'  @click='goYewuView(item,"supplement","审核待补充")'><span class='iconfont xingzhuang5'></span>补充</div>

                                    </div>
                                    <div v-if='userInfo.servNumber != item.followTel'>
                                        <div class='control-item' @click='goYewuView(item,"view","审核待补充")'><span class='iconfont xingzhuang5'></span>查看</div>
                                    </div>
                                </div>
                                <!--业务-补单待审核 当前操作员==跟进人-->
                                <div v-show='item.buopStatus == 16 '>
                                    <div class='control-item' @click='goYewuView(item,"view","审核待补充")'><span class='iconfont xingzhuang5'></span>查看</div>

                                </div>

                                <!--结单/待人工稽核状态--->
                                <div v-show='item.buopStatus == 10 || item.buopStatus == 13 || item.buopStatus == 12'>
                                    <div class='control-item' @click='goYewuView(item,"view")'><span
                                        class='iconfont xingzhuang5'></span>查看
                                    </div>
                                </div>
                            </div>
                            <!--团单--操作按钮-->
                            <div class='box-btn' v-show='item.busiType == 3'>
                                <!--当前操作员==跟进人-->
                                <div v-if='userInfo.servNumber == item.followTel '>
                                    <div v-show='item.buopStatus == 6 || item.buopStatus == 9 '>
                                        <div class='control-item' @click='goTuandanView(item)'><span
                                            class='iconfont xingzhuang5'></span>查看
                                        </div>
                                    </div>
                                    <div v-show='item.buopStatus == 2'>
                                        <div class='control-item' @click='goTuandanView(item)'><span
                                            class='iconfont xingzhuang5'></span>查看
                                        </div>
                                        <div class='control-item' @click='goTuandanZhuan(item)'><span
                                            class='iconfont xingzhuang5'></span>转派
                                        </div>
                                        <div class='control-item' @click='goTuandanJieDan(item)'><span
                                            class='iconfont xingzhuang5'></span>结单
                                        </div>
                                    </div>
                                    <div v-show='item.buopStatus == 8'>
                                        <div class='control-item' @click='goTuandanView(item)'><span
                                            class='iconfont xingzhuang5'></span>查看
                                        </div>
                                        <div class='control-item' @click='goTuandanZhi(item)'><span
                                            class='iconfont xingzhuang5'></span>指派
                                        </div>
                                        <div class='control-item' @click='goTuandanRenling(item)'><span
                                            class='iconfont xingzhuang5'></span>认领
                                        </div>
                                        <div class='control-item' @click='goTuandanJieDan(item)'><span
                                            class='iconfont xingzhuang5'></span>结单
                                        </div>
                                    </div>
                                </div>

                                <!--当前操作员==创建人, and 当前操作员不是当前商机所属区县的团单调度员-->
                                <div v-else-if='userInfo.servNumber == item.createTel && countryId.indexOf(item.countyId) < 0'>
                                    <div class='control-item' @click='goTuandanView(item)'><span
                                        class='iconfont xingzhuang5'></span>查看
                                    </div>
                                </div>

                                <!--当前操作员==创建人, and 当前操作员是当前商机所属区县的团单调度员-->
                                <div v-else-if='userInfo.servNumber == item.createTel && countryId && countryId.indexOf(item.countyId) > -1'>

                                    <div v-show='item.buopStatus == 6 || item.buopStatus == 9 || item.buopStatus == 2'>
                                        <div class='control-item' @click='goTuandanView(item)'><span
                                            class='iconfont xingzhuang5'></span>查看
                                        </div>
                                    </div>

                                    <div v-show='item.buopStatus == 8'>
                                        <div class='control-item' @click='goTuandanView(item)'><span class='iconfont xingzhuang5'></span>查看</div>
                                        <div class='control-item' @click='goTuandanZhi(item)'><span class='iconfont xingzhuang5'></span>指派</div>
                                        <div class='control-item' @click='goTuandanRenling(item)'><span class='iconfont xingzhuang5'></span>认领</div>
                                        <div class='control-item' @click='goTuandanJieDan(item)'><span class='iconfont xingzhuang5'></span>结单</div>
                                    </div>
                                </div>

                                <div v-else>
                                    <div v-show='item.buopStatus == 9'>
                                        <div class='control-item' @click='goTuandanView(item)'><span
                                            class='iconfont xingzhuang5'></span>查看
                                        </div>
                                    </div>
                                    <div v-show='item.buopStatus == 8'>
                                        <div class='control-item' @click='goTuandanView(item)'><span
                                            class='iconfont xingzhuang5'></span>查看
                                        </div>
                                        <div class='control-item' @click='goTuandanZhi(item)'><span
                                            class='iconfont xingzhuang5'></span>指派
                                        </div>
                                        <div class='control-item' @click='goTuandanRenling(item)'><span
                                            class='iconfont xingzhuang5'></span>认领
                                        </div>
                                        <div class='control-item' @click='goTuandanJieDan(item)'><span
                                            class='iconfont xingzhuang5'></span>结单
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </mt-loadmore>

                <no-data-page v-if='mylist && mylist.length == 0' />

            </div>
            <!--商机池-->
            <div class='lists-box lists-box2' v-show='topType2 == 2'>
                <div class='list-item' v-for='(item,idx) in emptylist' :key='idx' >
                    <div class='line1'>
                        <span class='tel'><img
                            src='../../assets/img/grsj.png' />{{item.createTel == userInfo.servNumber? item.busiTel : maskPhoneNumber(item.busiTel) }}
                                    <div class='xiansuo' v-show='item.isMessageVerify != "true"  && item.busiType == 1'>线索</div>
                        </span>
                        <span class='tag tag2' v-show='item.buopStatus == 2'>{{ more24Hour(item) ? '待指派' : '跟进中' }}</span>
                        <span class='tag tag0' v-show='item.buopStatus == 5'>{{ more24Hour(item) ? '待指派' : '待接单' }}</span>
                        <span class='tag tag3' v-show='item.buopStatus == 6'>{{ more24Hour(item) ? '待指派' : '已结单' }}</span>
                        <span class='tag tag0' v-show='item.buopStatus == 7'>{{ more24Hour(item) ? '待指派' : '待补充' }}</span>
                        <span class='tag tag7' v-show='item.buopStatus == 15'>审核待补充</span>
                        <span class='tag tag7' v-show='item.buopStatus == 16'>补单待审核</span>
                    </div>

                    <div class='tags-box' v-show='item.busiType == 1 || item.busiType == 2'>
                        <div class='tags tags1' v-show='item.busiType'>{{item.busiType == 1 ? '融合服务' : '业务商机'}}</div>
                        <div class='tags tags1' v-show='item.busiSource'>{{getLaiyuan(item.busiSource)}}</div>
                        <div class='tags' v-show='item.busiExpireTime' :class='getDateStatus(item.busiExpireTime) && getDateStatus(item.busiExpireTime).indexOf("-")>0? "tags2" : "tags3"'>{{getDateStatus(item.busiExpireTime)}}</div>
                        <div class='tags' v-show='item.serviceDate' :class='getDateStatus2(item.serviceDate) && getDateStatus2(item.serviceDate).indexOf("-")>0? "tags2" : "tags3"'>{{getDateStatus2(item.serviceDate)}}</div>
                        <div class='tags' v-show='item.businServiceDate' :class='getDateStatus2(item.businServiceDate) && getDateStatus2(item.businServiceDate).indexOf("-")>0? "tags2" : "tags3"'>{{getDateStatus2(item.businServiceDate)}}</div>

                    </div>


<!--                    <div class='line2' v-show='item.busiSource'>-->
<!--                        <span class='title'>商机来源：</span>-->
<!--                        <span class='value'>{{ getLaiyuan(item.busiSource) }}</span>-->
<!--                    </div>-->
                    <div class='line2' v-show='item.countyName'>
                        <span class='title'>归属区县：</span>
                        <span class='value'>{{ item.countyName }}</span>
                    </div>
                    <div class='line2' v-show='item.businCountyName'>
                        <span class='title'>归属区县：</span>
                        <span class='value'>{{ item.businCountyName }}</span>
                    </div>
                    <div class='line2' v-show='item.reportDate'>
                        <span class='title'>到岗时间：</span>
                        <span class='value'>{{item.reportDate}}</span>
                    </div>
                    <div class='line2' v-show=' item.assistOper && item.assistTel'>
                        <span class='title'>协销人员：</span>
                        <span class='value'>{{ item.assistOper }}<span v-show='item.assistTel'>({{ item.assistTel}})</span></span>
                    </div>
                    <div class='line2' v-show='!item.assistOper && item.assistTel'>
                        <span class='title'>协销人员：</span>
                        <span class='value'><span v-show='item.assistTel'>{{ item.assistTel}}</span></span>
                    </div>
                    <div class='line2' v-show='item.createOper && item.createTel &&  (item.createTel != item.followTel)'>
                        <span class='title'>创建人：</span>
                        <span class='value'>{{ item.createOper }}<span v-show='item.createTel'>({{ item.createTel}})</span></span>
                    </div>
                    <div class='line2' v-show='!item.createOper && item.createTel &&  (item.createTel != item.followTel)'>
                        <span class='title'>创建人：</span>
                        <span class='value'><span v-show='item.createTel'>{{ item.createTel}}</span></span>
                    </div>
                    <div class='line2' v-show='item.createTel && item.followTel &&  (item.createTel == item.followTel)'>
                        <span class='title'>创建/跟进人：</span>
                        <span class='value'>{{ item.followBy ? item.followBy : '--' }}<span v-show='item.followTel'>({{ item.followTel }})</span></span>
                    </div>
                    <div class='line2' v-show='item.createTime'>
                        <span class='title'>创建时间：</span>
                        <span class='value'>{{ item.createTime }}</span>
                    </div>
                    <div class='line2' v-show='item.followBy && item.followTel &&  (item.createTel != item.followTel)'>
                        <span class='title'>当前跟进人：</span>
                        <span class='value'>{{ item.followBy }}<span v-show='item.followTel'>({{ item.followTel }})</span></span>
                    </div>
                    <div class='line2' v-show='!item.followBy && item.followTel &&  (item.createTel != item.followTel)'>
                        <span class='title'>当前跟进人：</span>
                        <span class='value'><span v-show='item.followTel'>{{ item.followTel }}</span></span>
                    </div>
                    <div class='line2' v-show='item.releaseTime'>
                        <span class='title'>入池时间：</span>
                        <span class='value'>{{ item.releaseTime }}</span>
                    </div>
                    <div class='box-btn'>






                        <div class='box-btn' v-show='item.busiType == 1'>
                            <div class='control-item' @click='goPersonWeihu(item,1)' v-show='showPersonFlag(item)'><span
                                class='iconfont a-zu5638111x'></span>维护
                            </div>
                            <div class='control-item' @click='personJieDan(item)'
                                 v-show='countyId && countyId.indexOf(item.countyId) > -1'><span
                                class='iconfont yuyue'></span>结单
                            </div>
                            <div class='control-item' @click='PersonZhipai(item)'
                                 v-show='countyId && countyId.indexOf(item.countyId) > -1'><span
                                class='iconfont yuyue'></span>指派
                            </div>
                            <div class='control-item' @click='PersonRenling(item,1)'><span
                                class='iconfont a-yonghusuanfei3x'></span>认领
                            </div>
                            <div class='control-item' @click='goPersonWeihu(item,0)' v-show='!showPersonFlag(item) '>
                                <span class='iconfont xiangqing1'></span>查看
                            </div>
                        </div>



                        <div class='box-btn' v-show='item.busiType == 2'>
                            <div class='control-item' @click='goYewuView(item,"change")' v-show='showPersonFlag(item)'>
                                <span class='iconfont a-zu5638111x'></span>维护
                            </div>
                            <div class='control-item' @click='yewuJieDan(item)'
                                 v-show='countyId && countyId.indexOf(item.countyId) > -1'><span
                                class='iconfont yuyue'></span>结单
                            </div>
                            <div class='control-item' @click='PersonZhipai(item)' v-show='countyId && countyId.indexOf(item.countyId) > -1'><span
                                class='iconfont xingzhuang5'></span>指派
                            </div>
                            <div class='control-item' @click='YWRenling(item)'><span
                                class='iconfont xingzhuang5'></span>认领
                            </div>
                            <div class='control-item' @click='goYewuView(item,"view")' v-show='!showPersonFlag(item) '>
                                <span class='iconfont xiangqing1'></span>查看
                            </div>
                        </div>
                    </div>
                </div>
                <no-data-page v-if='emptylist && emptylist.length == 0' />
            </div>
        </div>


        <!--个人-商机指派-->
        <personchoose ref='executeRef' v-if='orgId2 && showExecute' @emCloseInit='closeExecute' @emSubmit='assignSubmit'
                      :orgaId='orgId2'  :headerName="'商机指派'" :objParam='zhipaiparam' :showExecute='2' />
        <!--个人-商机转派-->
        <personChooseNew ref='executeRef' v-if='orgId && showExecute2' @emCloseInit='closeExecute' @emSubmit='reassignSubmit'
                      :orgaId='orgId' :objParam='personZhuanpaiParam' :headerName="'商机转派'" :showExecute='1' />


        <!--团单-商机指派-->
        <person-tuan-dan-zhi-pai ref='executeRef' v-if='orgId && showExecute3' :countryId='countryId'
                                 @emCloseInit='closeExecute' @emSubmit='getExecute3'
                                 :chooseresonTDZzhipai='chooseresonTDZzhipai' :orgaId='orgId'
                                 :headerName="'团单商机指派'" :showExecute='2' />
        <!--团单-商机转派-->
        <person-tuan-dan-zhuanpai ref='executeRef' v-if='orgId && showExecute4' :showExecute4='showExecute4'
                                  :countryId='showExecute4.countyId' @emCloseInit='closeExecute' @emSubmit='getExecute4'
                                  :orgaId='orgId' :headerName="'团单商机转派'" />
        <!--团单-结单-->
        <TDoverForm ref='executeRef' v-if='orgId && showExecute5' :overItem='overItem' @goback='closeExecute' />


        <div class='dislog dislog2' v-show='showTuanDanZhiPaiReson'>
            <div class='title'>请选择指派类型</div>
            <div class='reson-box'>
                <div class='chooseone' @click='chooseresonTDZzhipai = 1'><span class='iconfont duihao1'
                                                                               v-show='chooseresonTDZzhipai == 1'></span>
                    <div class='yuan'></div>
                    指给区县内客户经理
                </div>
                <div class='chooseone' @click='chooseresonTDZzhipai = 2'><span class='iconfont duihao1'
                                                                               v-show='chooseresonTDZzhipai == 2'></span>
                    <div class='yuan'></div>
                    指给其他区县的调度员
                </div>
            </div>
            <div class='reson-box2'>
                <div class='chooseone' @click='showTuanDanZhiPaiReson = false'>取消</div>
                <div class='chooseone' @click='openDiaodu'>确定</div>
            </div>
        </div>
        <div class='hide-box' v-show='showTuanDanZhiPaiReson'></div>


        <div class='jiedan-box' v-show='personJiedanFlag'>
            <div class='title'>{{ formInfo.busiTel }}结单反馈</div>
            <div class='action-step-detail'>
                <span class='action-step-text'>结单类型<span class='red-info'>*</span></span>
                <div class='action-step-detail-btnlist'>
                    <span v-show='formInfo.buopStatus != 7' :class="formInfo.followtype == '成功结单'? 'active-span' : ''" @click="changeOwnFeedback('成功结单')">成功结单</span>
                    <span :class="formInfo.followtype == '失败结单'? 'active-span' : ''" @click="changeOwnFeedback('失败结单')">失败结单</span>
                </div>
            </div>
            <div class='action-step-detail' v-show="formInfo.followtype == '成功结单'">
                <span class='action-step-text'>结果描述 <span style='color: red'>*</span></span>
            </div>
            <div class='action-step-detail' v-show="formInfo.followtype == '失败结单'">
                <span class='action-step-text'>失败原因 <span style='color: red'>*</span></span>
                <div class='action-step-detail-btnlist'>
                    <span :class="formInfo.filereson == '无效商机'? 'active-span' : ''"
                          @click="changeOwnfileresult('无效商机')">无效商机</span>
                    <span :class="formInfo.filereson == '其他'? 'active-span' : ''"
                          @click="changeOwnfileresult('其他')">其他</span>
                </div>
            </div>
            <div class='action-step-detail' v-show="formInfo.followresult == '新办卡'">
                <span class='action-step-text'>新卡号码 <span class='red-info'>*</span></span>
                <div class='action-step-detail'>
                    <div>
                        <input v-model='formInfo.newMsisdn' class='addr-input' maxlength='11' type='tel'
                               placeholder='请输入新卡号码' />
                    </div>
                </div>
            </div>
            <div class='action-step-detail' v-show="formInfo.followresult == '新办卡'">
                <span class='action-step-text'>掌厅图片 <span class='red-info'>*</span></span>
                <div @click='takePhoto'>
                    <span style='color: #bbb'>含姓名信息的商机号码掌厅截图 </span><span class='iconfont xiangji'></span>
                </div>
            </div>

            <div v-show="formInfo.followresult == '新办卡'">
                <div class='img-box' v-for='(item,idx) in formInfo.thebase64' v-show='item && item.indexOf("blob") < 0'>
                    <span class='iconfont guanbi2' @click='clearimg(idx)'></span>
                    <img :src="'data:image/jpeg;base64,'+ item" />
                </div>
                <div class='img-box' v-for='(item,idx) in formInfo.thebase64'
                     v-show='item && item.indexOf("blob") >= 0'>
                    <span class='iconfont guanbi2' @click='clearimg(idx)'></span>
                    <img :src='item' />
                </div>
            </div>

            <div class='action-step-detail' v-show="formInfo.filereson == '其他' && formInfo.followtype == '失败结单'">
                <span class='action-step-text'>原因明细 <span class='red-info'>*</span></span>
                <div class='action-step-detail'>
                    <div>
                        <input v-model='formInfo.resontext' class='addr-input' maxlength='100' type='text'
                               placeholder='请输入原因明细' />
                    </div>
                </div>
            </div>
            <div class='detail-remark' v-show="formInfo.followtype == '成功结单'">
                <div class='pop-textarea-border'>
                    <textarea v-model='formInfo.detailRemark' maxlength='100' placeholder='请输入'
                              class='pop-textarea'></textarea>
                    <span
                        class='pop-textarea-num'><span>{{ formInfo.detailRemark.length }}</span><span>/100</span></span>
                </div>
            </div>
            <div class='btn' @click='statementAccount'>
                结 单
            </div>
        </div>
        <div class='hide-box2' v-show='personJiedanFlag' @click='cancerlpop'></div>
        <div class='dislog' v-show='showreson3'>
            <div class='title'>请选择原因</div>
            <div class='reson-box'>
                <div class='chooseone' @click='choosereson = 1'><span class='iconfont duihao1'
                                                                      v-show='choosereson == 1'></span>
                    <div class='yuan'></div>
                    需要专家支援
                </div>
                <div class='chooseone' @click='choosereson = 2'><span class='iconfont duihao1'
                                                                      v-show='choosereson == 2'></span>
                    <div class='yuan'></div>
                    客户拒绝办理
                </div>
            </div>
            <div class='reson-box2'>
                <div class='chooseone' @click='showreson3 = false'>取消</div>
                <div class='chooseone' @click='sure3'>确定</div>
            </div>
        </div>
        <div class='hide-box' v-show='showreson3'></div>
    </div>
</template>

<script>
import Header from 'components/common/Header.vue'
import Storage from '@/base/storage.js'
import NlDropdown from 'components/common/NlDropdown/dropdownForBusinessOpportunity.js'
import NlDatePicker from 'components/common/NlDatePick2/datePicker.js'
import NoDataPage from '../common/NoDataPage'
import personchoose from './personchoose.vue'
import personChooseNew from './personChooseNew.vue'
import { chgStrToDate, dateFormat } from '@/base/utils'
import ClientJs from '@/base/clientjs'
import ImageObj from '@/base/request/ImageServReq'
import PersonTuanDanZhiPai from './personTuanDanZhiPai.vue'
import PersonTuanDanZhuanpai from './personTuanDanZhuanpai.vue'
import TDoverForm from './TDoverForm.vue'
import PersonOver from './PersonOver.vue'
import FormHP from './FormHP.vue'

export default {
    name: 'PersonalBusinessOpportunity',
    components: {
        PersonOver,
        TDoverForm,
        PersonTuanDanZhuanpai,
        PersonTuanDanZhiPai,
        NoDataPage,
        Header,
        FormHP,
        personchoose,
        personChooseNew
    },
    data() {
        return {
            todayDate: '',// 系统时间
            // 业务结单表单
            formInfo: {
                busiTel: '',
                followtype: '',
                followresult: '',
                thebase64: [],
                filereson: '',
                detailRemark: '',
                resontext: '',
                thebase642: []
            },


            totalMy: '',//个人商机总数
            pageNum: 1,
            tsAllLoaded: false,


            totalRowNum: '',

            oldForm: {},
            personJiedanFlag: false,
            mylist: [],
            emptylist: [],
            countyId: [],
            suanzhanglist: [],
            teltext: '',
            teltext2: '',
            teltext3: '',
            topType2: 0,
            userInfo: {},
            type0: { id: '', label: '全部类型' },
            type1: { id: '', label: '商机状态' },
            type2: { id: '', label: '商机状态' },
            type3: { id: '', label: '全部类型' },
            inputtype2: { id: '1', label: '商机号码' },
            startTime1: '',
            endTime1: '',
            startTime2: '',
            endTime2: '',
            startTime3: '',
            endTime3: '',
            chooseTime1: '',
            chooseTime2: '',
            chooseTime3: '',
            createTime4: '',
            followTime4: '',
            showExecute: false, // 个人-指派
            showExecute2: false, // 个人-转派
            showExecute3: false, // 团单-指派
            showExecute4: false, // 团单-转派
            showExecute5: false, // 团单-结单
            overItem: {},// 团单-结单选择的
            orgId: '',
            gangwei: '',
            chooseidPerson: '',
            chooseidPerson2: '',
            chooseidPerson3: '',
            chooseidPerson4: '',
            chooseidTuandan1: '', // 指派,团单商机id
            chooseitem2: '',
            personZhuanpaiParam: {},
            chooseitem1Person: {},
            chooseitem2Person: {},
            choosereson: '',
            chooseresonTDZzhipai: {},
            showTuanDanZhiPaiReson: false,  // 团单-指派弹窗
            showreson3: false, // 释放弹窗
            zhipaiparam: {},
            orgId2: '',



            comFormType1: '', // 商机来源-个人
            comFormType2: '', // 商机来源-商机池
            comFormType3: '', // 合约到期-个人-文本
            HYtime1: '', //合约到期-个人-id
            HYtime2: '', // 合约到期-商机池-id
            comFormType4: '', // 是否直办-个人-文本
            isStaright: '',// 是否直办-个人-id
            countyIdList: [],


            showDialogFlag: false,
            kanban: {
                type: '商机报表'
            },
            IsCountryTuanOper: false,
            countryId: '',
            tableInfo: [], // 表格数据
            tableName: [], // 归属层级列表
            tableId: '', // 表格查询的地址id
            hpFlg: false,
        }
    },
    mounted() {




        window['PalmHallPic'] = (info) => {
            if (info.fileImage) {
                if (this.formInfo.thebase642.length >= 3) {
                    this.$toast('最多可拍三张')
                    return
                }

                this.formInfo.thebase64.push(info.fileImage)
                let param2 = {
                    photoStr: info.fileImage,
                    comeFrom: 'familyDiagnosis'
                }

                param2['unEncrpt'] = true
                this.$http.post('/xsb/personBusiness/queryCalculate/h5SubmitPhoto', param2).then((res) => {
                    //选择的图片全部成功上传到服务器
                    this.formInfo.thebase642.push(res.data.data)
                    console.log('this.formInfo.thebase642', this.formInfo.thebase642)

                })
            }
        }

        window['getUserInfoCB'] = (result) => {
            let res = result.userInfo
            const uinfo = JSON.parse(res)
            initTokenAfterBack(this.$http, uinfo) //重置token
            this.init()
        }

        const gobackFlag = this.$route.query.gobackFlag
        if (gobackFlag == 'webview') {
            ClientJs.getSysInfo('getUserInfoCB')
        } else {
            this.init()
        }
    },
    methods: {
        init () {
            this.topType2 = Storage.session.get('SJTab')
            if (Storage.session.get('SJtype0')) {
                this.type0 = Storage.session.get('SJtype0')
            }
            if (Storage.session.get('SJtype1')) {
                this.type1 = Storage.session.get('SJtype1')
            }
            if (Storage.session.get('SJtype2')) {
                this.type2 = Storage.session.get('SJtype2')
            }
            if (Storage.session.get('SJcomFormType1')) {
                this.comFormType1 = Storage.session.get('SJcomFormType1')
            }
            if (Storage.session.get('SJcomFormType2')) {
                this.comFormType2 = Storage.session.get('SJcomFormType2')
            }
            if (Storage.session.get('SJchooseTime1')) {
                this.chooseTime1 = Storage.session.get('SJchooseTime1')
            }
            if (Storage.session.get('SJchooseTime2')) {
                this.chooseTime2 = Storage.session.get('SJchooseTime2')
            }
            if (Storage.session.get('SJchooseTime3')) {
                this.chooseTime3 = Storage.session.get('SJchooseTime3')
            }

            this.userInfo = Storage.session.get('userInfo')
            this.gangwei = Storage.session.get('userInfo').stationId == '9988020401021550'
            this.pageNum = 1
            this.getListMy()
            this.getListEmpty()
            this.getSjInfo()
            this.getTodayDate()
            this.getIsCountryTuanOper()
            this.getConstrolCounntyId()
            let arr = [
                { 'region': '11', 'longRegion': '1000512' }, //苏州
                { 'region': '12', 'longRegion': '1000517' }, //淮安
                { 'region': '13', 'longRegion': '1000527' }, //宿迁
                { 'region': '14', 'longRegion': '1000250' }, //南京
                { 'region': '15', 'longRegion': '1000518' }, //连云港
                { 'region': '16', 'longRegion': '1000516' }, //徐州
                { 'region': '17', 'longRegion': '1000519' }, //常州
                { 'region': '18', 'longRegion': '1000511' }, //镇江
                { 'region': '19', 'longRegion': '1000510' }, //无锡
                { 'region': '20', 'longRegion': '1000513' }, //南通
                { 'region': '21', 'longRegion': '1000523' }, //泰州
                { 'region': '22', 'longRegion': '1000515' }, //盐城
                { 'region': '23', 'longRegion': '1000514' }, //扬州
                {"region":'99',"longRegion":"2000250"} //江苏省-取南京
            ]
            for (let i = 0; i < arr.length; i++) {
                if (arr[i].region == Storage.session.get('userInfo').region) {
                    if (arr[i].longRegion == '2000250') {
                        this.orgId = '1000250'
                    } else {
                        this.orgId = arr[i].longRegion
                    }
                }
            }
        },
        changeHP(){
            this.hpFlg = !this.hpFlg
        },
        formatDate(dateString) {
            if (!dateString) return
            // 首先创建一个Date对象
            const date = new Date(dateString.slice(0, 4), dateString.slice(4, 6) - 1, dateString.slice(6));

            // 格式化为 YYYY-MM-DD
            const formattedDate = date.getFullYear() + '-' +
                String(date.getMonth() + 1).padStart(2, '0') + '-' +
                String(date.getDate()).padStart(2, '0');

            return formattedDate;
        },
        // 是否区县团单调度员
        getIsCountryTuanOper() {
            let params = {
                'crmId': Storage.session.get('userInfo').crmId,
                'telNum': Storage.session.get('userInfo').servNumber,
                'regionId': Storage.session.get('userInfo').region == 99 ? 14 : Storage.session.get('userInfo').region
            }
            this.$http.post('/xsb/gridCenter/familyDiagnosis/h5IsCountryTuanOper', params).then((res) => {
                if (res.data.retCode == '0') {
                    if (res.data.data.isCountryTuanOper == 1) {
                        this.IsCountryTuanOper = true
                        let arr = res.data.data.countryList
                        let arr2 = []
                        for (let i = 0; i <arr.length;i++) {
                            arr2.push(arr[i].countryId)
                        }
                        this.countryId = arr2
                        console.log('res.data.data.countryList[0].countryId', res.data.data.countryList[0].countryId)
                    } else {
                        this.IsCountryTuanOper = false
                    }
                } else {
                    this.IsCountryTuanOper = false

                }
            })
        },
        // 团单-查看展现
        showTuanDanView(item) {
            // 当前操作员仅为创建人的情况下，无论工单状态仅可以“查看”商机
            if (this.userInfo.servNumber != item.followTel && this.userInfo.servNumber != item.createTel) {
                return true
            }
            // 当前操作员为跟进人的情况下，“已结单”仅支持“查看”
            if (this.userInfo.servNumber == item.followTel && item.buopStatus == 6) {
                return true
            }
            // 当前操作员为跟进人的情况下，“跟进中”支持“查看”
            if (this.userInfo.servNumber == item.followTel && item.buopStatus == 2) {
                return true
            }
            // 当前操作员为跟进人的情况下，“待指派”支持“查看”
            if (this.userInfo.servNumber == item.followTel && item.buopStatus == 8) {
                return true
            }
            // 当前操作员为跟进人的情况下，“待指派”支持“查看”
            if (this.IsCountryTuanOper && !item.followTel && item.buopStatus == 8) {
                return true
            }
        },
        // 团单-查看
        goTuandanView(item) {
            this.$router.push({
                path: '/groupOrdersBusiness',
                query: {
                    editType: 'view',
                    item: JSON.stringify(item),
                    buopId: item.buopId,
                    busiTel: item.busiTel,
                    buopStatus: item.buopStatus
                }
            })
        },
        // 业务-查看/维护
        goYewuView(item, type,text) {
            if (item.buopStatus == 15 || item.buopStatus == 16) {
                text = '审核待补充'
            }
            this.$router.push({
                path: '/BusinessOpportMaintenance',
                query: {
                    editType: type,
                    editType2:text == '审核待补充'? '1' : '',
                    showbtn: type == 'change' || type == 'supplement' ? 1 : 0,
                    itemObj: JSON.stringify(item)
                }
            })
        },
        // 团单-转派展现
        showTuanDanZhuanPai(item) {
            // 当前操作员仅为创建人的情况下，无论工单状态仅可以“查看”商机
            if (this.userInfo.servNumber != item.followTel && this.userInfo.servNumber != item.createTel) {
                return false
            }
            // 当前操作员为跟进人的情况下，“跟进中”支持“查看”
            if (this.userInfo.servNumber == item.followTel && item.buopStatus == 2) {
                return true
            }
        },
        // 团单-转派
        goTuandanZhuan(item) {
            this.showExecute4 = item
        },

        // 团单-指派展现
        showTuanDanZhiPai(item) {
            // 当前操作员仅为创建人的情况下，无论工单状态仅可以“查看”商机
            if (this.userInfo.servNumber != item.followTel && this.userInfo.servNumber != item.createTel) {
                return false
            }
            // “待指派”支持“查看”、“认领”、“指派”、“结单”
            if (this.userInfo.servNumber == item.followTel && item.buopStatus == 8) {
                return true
            }
            // 当前操作员为跟进人的情况下，“待指派”支持“指派”
            if (this.IsCountryTuanOper && !item.followTel && item.buopStatus == 8) {
                return true
            }
        },
        // 团单-指派-打开弹窗
        goTuandanZhi(item) {
            this.showTuanDanZhiPaiReson = item

        },
        // 团单-指派-调度员
        openDiaodu() {
            this.showExecute3 = true

        },
        // 团单-指派
        getExecute3(item) {
            let choosePersonObj = item
            console.log(choosePersonObj)
            let itemObj = this.showTuanDanZhiPaiReson
            this.showTuanDanZhiPaiReson = false
            this.showExecute3 = false
            let url = '/xsb/gridCenter/familyDiagnosis/h5UpdateBusiStatus'
            let params = {
                'transferSource': 1,
                'busiType': itemObj.busiType,
                'crmId': this.userInfo.crmId,
                'region': this.userInfo.region,
                'buopId': itemObj.buopId,  //商机id
                'busiTel': itemObj.busiTel, //商机号码
                'followOldBy': itemObj.followOldBy,  //上一跟进人
                'followOldTel': itemObj.followOldTel,//上一跟进人电话
                'followBy': itemObj.followBy,//当前跟进人（当前操作人）
                'followTel': itemObj.followTel,//当前跟进人电话（当前操作人电话）
                'buopStatus': itemObj.buopStatus,//商机状态
                'modifyOper': this.userInfo.operatorName,//修改人（当前操作人）
                'modifyTel': this.userInfo.servNumber,//修改人（当前操作人电话）
                'operateType': '7',//商机操作类型
                'transferPerson': this.chooseresonTDZzhipai == 1 ? choosePersonObj.managerName : '',//转派或者指派人
                'transferTel': this.chooseresonTDZzhipai == 1 ? choosePersonObj.managerTelnum : choosePersonObj.telnum,//转派或者指派人电话
                'transferRole': this.chooseresonTDZzhipai == 1 ? '2' : '1'  //转派角色
            }
            // {
            //     "managerName": "尹光霞",
            //     "managerTelnum": "13901581869"
            // }
            // {
            //     "regionId": "14",
            //     "regionName": "南京分公司",
            //     "countryId": "1488018465153410",
            //     "countryName": "浦口分公司",
            //     "telnum": "13901583375"
            // }
            this.$http.post(url, params).then((res) => {
                let { retCode, retMsg } = res.data
                if (retCode == '0') {
                    this.$toast('操作成功')
                    this.pageNum = 1
                    this.getListMy()
                } else {
                    this.$alert(retMsg || `记录商机失败`)
                }
            })


        },
        // 转派/回退
        getExecute4(item) {
            let choosePersonObj = item
            this.showExecute4 = false
            this.showTuanDanZhiPaiReson = false
            this.$toast('操作成功')
            this.pageNum = 1
            this.getListMy()
        },
        // 团单-认领展现
        showTuanDanRenling(item) {
            // 当前操作员仅为创建人的情况下，无论工单状态仅可以“查看”商机
            if (this.userInfo.servNumber != item.followTel && this.userInfo.servNumber != item.createTel) {
                return false
            }
            // 当前操作员为跟进人的情况下，“待指派”支持“认领”
            if (this.IsCountryTuanOper && !item.followTel && item.buopStatus == 8) {
                return true
            }
            // 当前操作员为跟进人的情况下，“待指派”支持“指派”
            if (this.IsCountryTuanOper && !item.followTel && item.buopStatus == 8) {
                return true
            }
        },
        // 团单-认领
        goTuandanRenling(item) {
            this.$messagebox({
                title: '温馨提示',
                showCancelButton: true,
                message: '该团单商机将流转到您岗上',
                confirmButtonText: '确定'
            }).then((ressend) => {
                if (ressend === 'confirm') {
                    let url = '/xsb/gridCenter/familyDiagnosis/h5UpdateBusiStatus'
                    let params = {
                        'transferSource': 1,
                        'busiType': item.busiType,
                        'crmId': Storage.session.get('userInfo').crmId,
                        'region': Storage.session.get('userInfo').region,
                        'buopId': item.buopId,  //商机id
                        'busiTel': item.busiTel, //商机号码
                        'followOldBy': item.followOldBy,  //上一跟进人
                        'followOldTel': item.followOldTel,//上一跟进人电话
                        'followBy': item.followBy,//当前跟进人（当前操作人）
                        'followTel': item.followTel,//当前跟进人电话（当前操作人电话）
                        'buopStatus': item.buopStatus,//商机状态
                        'busiReleaseReason': '', //商机释放原因
                        'busiTransferReason': '',//商机转派原因
                        'modifyOper': Storage.session.get('userInfo').operatorName,//修改人（当前操作人）
                        'modifyTel': Storage.session.get('userInfo').servNumber,//修改人（当前操作人电话）
                        'operateType': '8',//商机操作类型
                        'transferPerson': '',//转派或者指派人
                        'transferTel': ''//转派或者指派人电话
                    }
                    this.$http.post(url, params).then((res) => {
                        if (res.data.retCode == '0') {
                            this.$toast('认领成功')
                            this.pageNum = 1
                            this.getListMy()
                            this.getListEmpty()
                        } else {
                            this.$toast('认领失败' || res.data.restMsg)

                        }
                    })
                }
            })
        },
        // 团单-结单展现
        showTuanDanJieDan(item) {
            // 当前操作员仅为创建人的情况下，无论工单状态仅可以“查看”商机
            if (this.userInfo.servNumber != item.followTel && this.userInfo.servNumber != item.createTel) {
                return false
            }
            // 当前操作员为跟进人的情况下，“待指派”支持“结单”
            if (this.IsCountryTuanOper && !item.followTel && item.buopStatus == 8) {
                return true
            }
            // 当前操作员为跟进人的情况下，“待指派”支持“指派”
            if (this.IsCountryTuanOper && !item.followTel && item.buopStatus == 8) {
                return true
            }
        },
        // 团单-结单
        goTuandanJieDan(item) {
            this.overItem = item
            this.showExecute5 = true
        },
        getPreviousDayDate() {
            // 创建一个新的Date对象，表示当前日期
            const today = new Date();

            // 创建一个新的Date对象，表示前一天的日期
            // 通过减去一天的毫秒数（24小时 * 60分钟 * 60秒 * 1000毫秒）
            const previousDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() - 2);

            // 格式化日期为 yyyymmdd
            const formattedDate = previousDay.getFullYear().toString().padStart(4, '0') +
                (previousDay.getMonth() + 1).toString().padStart(2, '0') + // 月份是从0开始的，所以要加1
                previousDay.getDate().toString().padStart(2, '0');

            return formattedDate;
        },
        // 商机看板信息
        getSjInfo() {
            let param = {
                'region': this.userInfo.region,
                'operatorTel': this.userInfo.servNumber
            }
            this.$http.post('/xsb/gridCenter/familyDiagnosis/h5QryCalculateBusiCoard', param).then((res) => {
                let body = res.data
                if (body.retCode === '0') {
                    this.kanban = body.data
                    if(this.$route.query.kanbanType) {
                        this.kanban.type = '解耦商机报表'
                    } else {
                        this.kanban.type = '商机报表'
                    }
                    if (this.kanban.viewFlag == 1) {
                        this.topType2 = 0
                    } else {
                        this.topType2 = 1
                    }


                    if (Storage.session.get('SJTab') != null) {
                        this.topType2 = Storage.session.get('SJTab')
                    }

                    if (this.kanban.viewFlag == 1) {

                        this.kanban.today = this.getPreviousDayDate()
                        let url2 = `/xsb/personBusiness/gridViewSecond/h5QryScoreAndRank?telnum=${this.userInfo.servNumber}`;
                        this.$http.get(url2).then((res2) =>{
                            if (res2.data.retCode == "0") {
                                let data = res2.data.data;
                                this.tableId = data.orgaId2
                                this.getKBtable(data.orgaId2,null,data.orgaName2)
                            } else {
                                this.$alert(res.data.retMsg || "查询当前网格基本信息接口调用出错");
                            }
                        })

                    }
                }
            }).catch(() => {
                this.topType2 = 1

            })
        },
        convertToPercentage(num) {
            if(!num)  return '--'
            return (num * 100).toFixed(2) + '%';
        },
        getKBtable(orgId,flg,name){

            let param = {
                "dataType": this.kanban.type == '解耦商机报表' ? '1' : '0',
                "crmId": this.userInfo.crmId,
                "region": this.userInfo.region,
                "startDate": this.kanban.today,   //看板数据时间YYYYMMDD
                "queryType": "parent",   // 查询类型：current  当前层级      parent  查询父级
                "orgId": '',              // current  当前层级 传这个
                "parentId": orgId? orgId : this.tableId     //parent   查询父级  查这个
            }
            console.log('表格',param)
            this.$http.post('/xsb/gridCenter/familyDiagnosis/h5QryBusiBoardInfo', param).then((res) => {
                let body = res.data
                if (body.retCode === '0') {
                    this.tableInfo = body.data
                    if (!flg || this.tableName.length == 0) {
                        if (this.tableInfo[this.tableInfo.length-1]) {
                            this.tableName.push(this.tableInfo[this.tableInfo.length-1])
                        } else {
                            this.tableName=[{orgId:this.tableId ,orgName: name }]
                        }
                        console.log('this.tableName',this.tableName)
                    }

                }
            })
        },
        changeFanwei(item,idx){
            this.tableName = this.tableName.slice(0, idx)
            this.getKBtable(item.orgId)
        },
        changeKBType() {
            let self = this
            NlDropdown({
                confirmBtn: false,
                datalist: [
                    { id: '1', label: '商机报表' },
                    { id: '2', label: '解耦商机报表' },
                ]
            }, function(retVal) {
                self.kanban.type = retVal.label
                self.getKBtable(null,'type')

            })
        },
        changeKBtime(){
            let date = '';
            let _this = this;
            NlDatePicker({
                onlyOne:true,
                format:'yyyyMMdd',
                title: '统计日期',
                tsMaxDate:new Date(new Date().getTime()-1000*24*60*60),
            }, (retVal) => {
                console.info(retVal)
                this.kanban.today = retVal.startDate
                console.log('this.tableName',this.tableName)
                console.log('this.this.tableName[this.tableName.length-1]',this.tableName[this.tableName.length-1])
                if (this.tableName[this.tableName.length-1]) {
                    this.getKBtable(this.tableName[this.tableName.length-1].orgId,'time')

                } else {
                    console.log(77777777777777)
                    this.getKBtable(null,'time')

                }
            });
        },
        // 判断是否超过24h
        more24Hour(item) {
            let startTime = new Date(item.releaseTime)
            let endTime = new Date()
            const diffTime = Math.abs(endTime - startTime) // 计算时间差
            const diffHours = diffTime / (1000 * 60 * 60) // 将毫秒转换为小时

            if (diffHours > 24 && this.countyId && this.countyId.indexOf(item.countyId) >= 0) {
                return true
            }
        },

        // 返回
        goPrev() {
            const gobackFlag = this.$route.query.gobackFlag
            if (gobackFlag == 'webview'){
                ClientJs.closeCallBack('FSOP');
            } else if (gobackFlag == 'ald') {
              history.go(-1);
            } else {
                let srcFrom = this.$route.query.srcFrom || ''
                if (srcFrom == 'workStage') {
                    this.$router.push('/')
                } else {
                    if (srcFrom == 'toolsNew') { //跳回工具箱
                        this.$router.push('/toolsNew')
                    } else {
                        this.$router.push('/tools')
                    }
                }
            }
        },
        // 商机录入
        goEnterPage(val) {
            if (val == 1) {
                this.$router.push({
                    path: '/PotentialuserMaintenance',
                    query: {
                        showbtn: 1,
                        add: 1,
                        editType: 'add'
                    }
                })
            } else if (val == 2) {
                this.$router.push({
                    path: '/BusinessOpportMaintenance',
                    query: {
                        showbtn: 1,
                        editType: 'add'

                    }
                })
            } else if (val == 3) {
                this.$router.push({
                    path: '/groupOrdersBusiness',
                    query: {
                        editType: 'add'
                    }
                })
            }  else if (val == 4) {
                let url1 = `/xsb/personBusiness/gridSand/h5GridMarketSwitch`
                let param = {
                    switchType: 'zhiban_flg'
                }
                this.$http.post(url1,param).then((res) => {
                    if(res.data.retCode == 0) {
                        this.$router.push({
                            path: '/PersonAddAndOverNew',
                        })
                    } else {
                        this.$router.push({
                            path: '/PersonAddAndOver',
                        })
                    }
                })
            } else {
                this.showDialogFlag = !this.showDialogFlag
            }


        },

        showPersonFlag(item) {
            if ((item.buopStatus == 1 && this.userInfo.servNumber == item.createTel) || (item.buopStatus == 2 && this.userInfo.servNumber == item.followTel)) {
                return true
            }
            return false
        },
        showpersonJieDan(item) {
            if (item.buopStatus != 6 && item.buopStatus != 11 && this.userInfo.servNumber == item.followTel) {
                return true
            }
            return false
        },
        showPersonChakan(item) {
            if (!this.showPersonFlag(item) && item.buopStatus != 7 && item.buopStatus != 11 && item.buopStatus != 12 && item.buopStatus != 13) {
                return true
            }
            if (item.buopStatus == 7 && this.userInfo.servNumber != item.followTel) {
                return true
            }
            return false
        },
        showPersonBuChong(item) {
            if (item.buopStatus == 7 && this.userInfo.servNumber == item.followTel) {
                return true
            }
            return false
        },
        getLaiyuan(val) {

            if (!val) return
            let arr = [
                { id: '', label: '全部来源' },
                { id: '2', label: 'O了' },
                { id: '1', label: '阿拉盯' },
                { id: '3', label: '在线' },
                { id: '4', label: '互联网' },
                { id: '5', label: '泛渠道' },
                { id: '6', label: '权益平台' },
            ]
            for (let i = 0;i < arr.length; i++) {
                if (arr[i].id == val) {
                    return arr[i].label
                }
            }
        },
        changecomFormType1() {
            let self = this
            NlDropdown({
                confirmBtn: false,
                title: '商机来源',
                defaultValue: { id: '', label: '全部来源' },
                datalist: [
                    { id: '', label: '全部来源' },
                    { id: '1', label: 'O了' },
                    { id: '2', label: '阿拉盯' },
                    { id: '3', label: '在线' },
                    { id: '4', label: '互联网' },
                    { id: '5', label: '泛渠道' },
                    { id: '6', label: '权益平台' },
                ]
            }, function(res) {
                self.comFormType1 = res.label
                self.pageNum = 1
                self.getListMy()
            })
        },
        changecomFormType2() {
            let self = this
            NlDropdown({
                confirmBtn: false,
                title: '商机来源',
                defaultValue: { id: '', label: '全部来源' },
                datalist: [
                    { id: '', label: '全部来源' },
                    { id: '1', label: 'O了' },
                    { id: '2', label: '阿拉盯' },
                    { id: '3', label: '在线' },
                    { id: '4', label: '互联网' },
                    { id: '5', label: '泛渠道' },
                    { id: '6', label: '权益平台' },

                ]
            }, function(res) {
                self.comFormType2 = res.label
                self.getListEmpty()
            })
        },
        changecomFormType3() {
            let self = this
            NlDropdown({
                confirmBtn: false,
                title: '合约到期',
                defaultValue: { id: '', label: '全部时间' },
                datalist: [
                    { id: '', label: '全部时间' },
                    { id: '1', label: '近一个月' },
                    { id: '2', label: '近两个月' },
                    { id: '3', label: '近三个月' },

                ]
            }, function(res) {
                self.comFormType3 = res.label
                self.HYtime1 =   res.id
                self.pageNum = 1
                self.getListMy()
            })
        },
        changecomFormType4() {
            let self = this
            NlDropdown({
                confirmBtn: false,
                title: '是否直办',
                defaultValue: { id: '', label: '全部是否直办' },
                datalist: [
                    { id: '', label: '全部是否直办' },
                    { id: '1', label: '是' },
                    { id: '2', label: '否' },

                ]
            }, function(res) {
                self.comFormType4 = res.label
                self.isStaright = res.id
                self.pageNum = 1
                self.getListMy()
            })
        },
        changeSearchType0() {
            let self = this
            NlDropdown({
                confirmBtn: false,
                title: '商机类型',
                defaultValue: { id: '', label: '全部类型' },
                datalist: [
                    { id: '', label: '全部类型' },
                    { id: '1', label: '融合服务' },
                    { id: '3', label: '团单商机' },
                    { id: '2', label: '业务商机' }
                ]
            }, function(res) {
                self.type0 = res
                if (self.type0.id == 3) {
                    self.comFormType1 = '商机来源'
                }
                self.type1 = { id: '', label: '商机状态' }
                self.pageNum = 1
                self.getListMy()

            })
        },
        changeSearchType1() {
            // 商机状态
            let self = this
            let arr = []
            if (self.type0.id == 1) {
                // 个人
                arr = [
                    { id: '', label: '全部状态' },
                    { id: '2', label: '跟进中' },
                    { id: '5', label: '待接单' },
                    { id: '6', label: '已结单' },
                    { id: '7', label: '待补充' },
                    { id: '10', label: '待人工稽核' },
                    { id: '11', label: '人工稽核失败' },
                    { id: '12', label: '成功结单' },
                    { id: '13', label: '失败结单' },
                    { id: '15', label: '审核待补充' },
                    { id: '16', label: '补单待审核' },
                ]
            } else if (self.type0.id == 3) {
                // 团单
                arr = [
                    { id: '', label: '全部状态' },
                    { id: '8', label: '待指派' },
                    { id: '2', label: '跟进中' },
                    { id: '6', label: '已结单' },
                    { id: '9', label: '超时结单' },
                    { id: '15', label: '审核待补充' },
                    { id: '16', label: '补单待审核' },
                ]
            } else {
                // 业务
                arr = [
                    { id: '', label: '全部状态' },
                    { id: '5', label: '待接单' },
                    { id: '2', label: '跟进中' },
                    { id: '7', label: '待补充' },
                    { id: '12', label: '成功结单' },
                    { id: '13', label: '失败结单' },
                    { id: '10', label: '待人工稽核' },
                    { id: '15', label: '审核待补充' },
                    { id: '16', label: '补单待审核' },
                ]
            }
            NlDropdown({
                confirmBtn: false,
                title: '商机状态',
                defaultValue: { id: '', label: '全部状态' },
                datalist: arr
            }, function(res) {
                self.type1 = res
                self.pageNum = 1
                self.getListMy()
            })
        },
        changeSearchType2() {
            let self = this
            NlDropdown({
                confirmBtn: false,
                title: '商机状态',
                defaultValue: { id: '', label: '全部状态' },
                datalist: [
                    { id: '', label: '全部状态' },
                    { id: '5', label: '待接单' },
                    { id: '7', label: '待补充' }
                ]
            }, function(res) {
                self.type2 = res
                self.getListEmpty()
            })
        },
        changeSearchType3() {
            let self = this
            NlDropdown({
                confirmBtn: false,
                title: '商机类型',
                defaultValue: { id: '', label: '全部类型' },
                datalist: [
                    { id: '', label: '全部类型' },
                    { id: '1', label: '融合服务' },
                    { id: '2', label: '业务商机' }
                ]
            }, function(res) {
                self.type3 = res
                self.getListEmpty()

            })
        },
        changeSearchTime1() {
            let self = this
            NlDatePicker({
                dateType: 'date',
                startDate: self.startTime,
                endDate: self.endTime,
                title: '创建时间',
                tsMinDate: chgStrToDate('2024/06/01', false)
            }, (retVal) => {
                //获取返回回调
                if (retVal.startDate == '' || retVal.endDate == '') {
                    console.info(retVal.startDate)
                    if (retVal.startDate) {
                        self.startTime1 = retVal.startDate.replace(/\//g, '-') + ' 00:00:00'
                    } else {
                        self.startTime1 = ''
                    }
                    if (retVal.endDate) {
                        self.endTime1 = retVal.endDate.replace(/\//g, '-') + ' 23:59:59'

                    } else {
                        self.endTime1 = ''
                    }
                    self.chooseTime1 = retVal.startDate + '~' + retVal.endDate
                    self.pageNum = 1
                    self.getListMy()
                } else {
                    console.info(retVal.startDate)
                    self.startTime1 = retVal.startDate.replace(/\//g, '-') + ' 00:00:00'
                    self.endTime1 = retVal.endDate.replace(/\//g, '-') + ' 23:59:59'
                    self.chooseTime1 = retVal.startDate + '~' + retVal.endDate
                    self.pageNum = 1
                    self.getListMy()

                }
            })
        },
        changeSearchTime2() {
            let self = this
            NlDatePicker({
                dateType: 'date',
                startDate: self.startTime,
                endDate: self.endTime,
                title: '创建时间',
                tsMinDate: chgStrToDate('2024/06/01', false)

            }, (retVal) => {
                //获取返回回调
                console.info(retVal.startDate)
                self.startTime2 = retVal.startDate ? retVal.startDate.replace(/\//g, '-') + ' 00:00:00' : ''
                self.endTime2 = retVal.endDate ? retVal.endDate.replace(/\//g, '-') + ' 23:59:59' : ''
                self.chooseTime2 = retVal.startDate + '~' + retVal.endDate
                self.getListEmpty()

            })
        },
        changeSearchTime3() {
            let self = this
            NlDatePicker({
                dateType: 'date',
                startDate: self.startTime,
                endDate: self.endTime,
                title: '入池时间',
                tsMinDate: chgStrToDate('2024/06/01', false)

            }, (retVal) => {
                //获取返回回调
                console.info(retVal.startDate)
                self.startTime3 = retVal.startDate ? retVal.startDate.replace(/\//g, '-') + ' 00:00:00' : ''
                self.endTime3 = retVal.endDate ? retVal.endDate.replace(/\//g, '-') + ' 23:59:59' : ''
                self.chooseTime3 = retVal.startDate + '~' + retVal.endDate
                self.getListEmpty()
            })
        },
        changeHY2(val){
            if (val == this.HYtime2) {
                this.HYtime2 = ''

            } else {
                this.HYtime2 = val
            }
            this.getListEmpty()
        },
        loadTopAccredit(){
            console.info('刷新')
            this.pageNum = 1
            this.mylist = []
            this.$refs.loadmoref.onTopLoaded();
            this.getListMy()
        },
        loadBottomAccredit(){
            console.info('滚动+1')
            this.pageNum++
            this.$refs.loadmoref.onBottomLoaded();
            this.getListMy()
        },
        // 我的商机
        getListMy() {
            let url = '/xsb/gridCenter/familyDiagnosis/h5MyBusiQuery'
            let params = {
                'page': this.pageNum,
                'size': 10,
                'queryFlag': 1,
                'crmId': Storage.session.get('userInfo').crmId,
                'region': Storage.session.get('userInfo').region,
                'operateTel': Storage.session.get('userInfo').servNumber, //当前操作人号码
                'operateName': Storage.session.get('userInfo').operatorName,  //当前操作人
                'busiSource': this.comFormType1 == '阿拉盯' ? '1' : this.comFormType1 == 'O了' ? '2' : this.comFormType1 == '在线' ? '3' : this.comFormType1 == '互联网'? "4" : this.comFormType1 == '泛渠道' ? "5" : this.comFormType1 == '权益平台' ? 6 :''  ,
                'busiType': this.type0.id,
                'isStaright': this.isStaright,
                'busiExpireTime': this.HYtime1,
                'busiTel': this.teltext,  //商机号码
                'buopStatus': this.type1.id,//商机状态
                'createStartTime': this.startTime1,//创建开始时间
                'createEndTime': this.endTime1//创建结束时间
            }
            this.$http.post(url, params).then((res) => {
                console.info('我的商机列表--请求', params)
                console.info('我的商机列表--返回', res)
                if (res.data.retCode == '0') {
                    this.totalMy = res.data.data.total
                    if (this.pageNum == 1) {
                        this.mylist = res.data.data.pageData
                        this.tsAllLoaded = false
                        document.querySelector('.lists-box3').scrollTop = 0
                    }else {
                        let oldArr = JSON.parse(JSON.stringify(this.mylist))
                        let newArr = oldArr.concat(res.data.data.pageData)
                        this.mylist = newArr
                    }
                    if (res.data.data.pageData.length < 10) {
                        this.tsAllLoaded = true
                    }
                } else {
                    this.mylist = []
                }
            })
        },
        // 商机池
        getListEmpty() {
            this.emptylist = []
            let params = {
                'crmId': Storage.session.get('userInfo').crmId,
                'operatorTel': Storage.session.get('userInfo').servNumber,
                'regionId': Storage.session.get('userInfo').region == 99 ? 14 : Storage.session.get('userInfo').region,
                'busiTel': this.inputtype2.id == 1 ? this.teltext2 : '',//商机号码
                'createTel': this.inputtype2.id == 2 ? this.teltext2 : '',//创建人号码
                'createStartTime': this.startTime2,//创建开始时间
                'createEndTime': this.endTime2,//创建结束时间
                'releaseStartTime': this.startTime3,//释放开始时间
                'releaseEndTime': this.endTime3,//释放结束时间
                'busiExpireTime': this.HYtime2,
                'buopStatus': this.type2.id,//商机状态
                'busiType': this.type3.id,
                'busiSource': this.comFormType2 == '阿拉盯' ? '1' : this.comFormType2 == 'O了' ? '2' : this.comFormType2 == '在线' ? '3' : this.comFormType2 == '互联网'? "4" : this.comFormType2 == '泛渠道' ? '5' : this.comFormType1 == '权益平台' ? 6 : ''  ,

            }
            console.info('商机池列表--请求', params)
            this.$http.post('/xsb/gridCenter/familyDiagnosis/h5BusiPoolQuery', params).then((res) => {
                if (res.data.retCode == '0') {
                    console.info('商机池列表--返回', res)
                    this.emptylist = res.data.data ? res.data.data.busiPoolList : []
                    if (res.data.data.countyId && res.data.data.countyId.length > 0) {
                        this.countyId = res.data.data.countyId
                    }
                } else {
                    this.emptylist = []
                }
            })

        },
        getDateStatus(inputDate) {
            if (!inputDate) return
            // 当前日期
            const now = new Date();
            const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

            // 解析输入日期
            const dateParts = inputDate.split(' ')[0].split('-');
            const timeParts = inputDate.split(' ')[1].split(':') || ['00', '00', '00'];
            const targetDate = new Date(
                parseInt(dateParts[0]),
                parseInt(dateParts[1]) - 1,
                parseInt(dateParts[2]),
                parseInt(timeParts[0]),
                parseInt(timeParts[1]),
                parseInt(timeParts[2])
            );
            const targetDay = new Date(targetDate.getFullYear(), targetDate.getMonth(), targetDate.getDate());

            // 计算日期差
            const timeDiff = targetDay.getTime() - today.getTime();
            const dayDiff = timeDiff / (1000 * 60 * 60 * 24);

            // 判断状态
            if (dayDiff < 0) {
                return '合约已到期';
            } else if (dayDiff === 0) {
                return '今天合约到期';
            } else if (dayDiff <= 6) {  // 本周内（今天+6天）
                return '本周合约到期';
            } else if (dayDiff <= 30) {  // 一个月内
                return '近一个月合约到期';
            } else {
                return `${dateParts[0]}-${dateParts[1]}-${dateParts[2]}合约到期`;
            }
        },
        getDateStatus2(inputDate) {

            if (!inputDate) return
            // 当前日期
            const now = new Date();
            const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

            // 解析输入日期
            const dateParts = inputDate.split(' ')[0].split('-');
            const timeParts = inputDate.split(' ')[1].split(':') || ['00', '00', '00'];
            const targetDate = new Date(
                parseInt(dateParts[0]),
                parseInt(dateParts[1]) - 1,
                parseInt(dateParts[2]),
                parseInt(timeParts[0]),
                parseInt(timeParts[1]),
                parseInt(timeParts[2])
            );
            const targetDay = new Date(targetDate.getFullYear(), targetDate.getMonth(), targetDate.getDate());

            // 计算日期差
            const timeDiff = targetDay.getTime() - today.getTime();
            const dayDiff = timeDiff / (1000 * 60 * 60 * 24);

            // 判断状态
            if (dayDiff < 0) {
                return '预约已超期';
            } else if (dayDiff === 0) {
                return '今天预约服务';
            } else if (dayDiff <= 6) {  // 本周内（今天+6天）
                return '本周预约服务';
            } else if (dayDiff <= 30) {  // 一个月内
                return '近一个月预约服务';
            } else {
                return `${dateParts[0]}-${dateParts[1]}-${dateParts[2]}预约服务`;
            }
        },
        changePhone(item) {
            if (item.memMsisdn) {
                if (item.isClearShow == 1) {
                    return item.memMsisdn
                }
                let reg = /^(\d{3})\d*(\d{4})$/
                return item.memMsisdn.replace(reg, '$1****$2')
            }
            return ''
        },

        getSuanzhangdan() {
            let url = '/xsb/personBusiness/queryCalculate/h5RecordQuery'
            let params = {
                'billType': '1', //账单类型(就传1，1--表示查询全部)
                'createDate': this.createTime4.replace(/\//g, ''),
                'followDate': this.followTime4.replace(/\//g, ''),
                'msisdn': this.teltext3,  //输入的商机号码
                'page': '1',  //当前页码
                'size': '20',  //每页长度
                operatorMsisdn: Storage.session.get('userInfo').servNumber,
                operatorName: Storage.session.get('userInfo').operatorName
            }
            this.$http.post(url, params).then((res) => {
                if (res.data.retCode == '0') {
                    console.info('suanzhanglist', res.data.data)
                    this.suanzhanglist = res.data.data
                } else {
                    this.suanzhanglist = []
                }
            })
        },

        maskPhoneNumber(phoneNumber) {
            if (!phoneNumber) return
            phoneNumber = phoneNumber + ''
            var pattern = /[\u4e00-\u9fa5]/
            if (pattern.test(phoneNumber)) {
                return phoneNumber
            } else {
                if (phoneNumber.length == 11) {
                    return phoneNumber.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
                } else {
                    return phoneNumber
                }
            }

        },
        formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return
            const year = dateTimeStr.substring(0, 4)
            const month = dateTimeStr.substring(4, 6) - 1 // JS中月份是从0开始的
            const day = dateTimeStr.substring(6, 8)
            const hour = dateTimeStr.substring(8, 10)
            const minute = dateTimeStr.substring(10, 12)
            const second = dateTimeStr.substring(12, 14)

            const date = new Date(Date.UTC(year, month, day, hour, minute, second))
            let str = date.toISOString().substring(0, 19).replace(/T/g, ' ')
            return str // 获取日期和时间部分，去掉时间区
        },
        personJieDan(item) {
            let url1 = `/xsb/personBusiness/gridSand/h5GridMarketSwitch`
            let param = {
                switchType: 'over_flg'
            }
            this.$http.post(url1,param).then((res) => {
                if(res.data.retCode == 0) {
                    this.$router.push({
                        path: '/PersonOverNew',
                        query: {
                            shangJiObj: JSON.stringify(item)
                        }
                    })
                } else {
                    this.$router.push({
                        path: '/PersonOver',
                        query: {
                            shangJiObj: JSON.stringify(item)
                        }
                    })
                }
            })

        },
        yewuJieDan(item) {
            this.personJiedanFlag = true
            this.formInfo.buopId = item.buopId
            this.formInfo.busiTel = item.busiTel
            this.formInfo.followOldBy = item.followBy
            this.formInfo.followOldTel = item.followTel
            this.formInfo.buopStatus = item.buopStatus
            this.formInfo.busiSource = item.busiSource
            this.formInfo.isMessageVerify = item.isMessageVerify
            this.formInfo.busiChannel = item.busiChannel
            this.oldForm = item
            console.log(item)
        },
        clearimg(idx) {
            this.formInfo.thebase64.splice(idx, 1)
            this.formInfo.thebase642.splice(idx, 1)
        },
        getTodayDate() {
            let url1 = `/xsb/gridCenter/familyDiagnosis/h5GetCurrentTime`
            this.$http.post(url1).then((res) => {
                if(res.data.retCode == 0) {
                    this.todayDate = res.data.data
                    // this.todayDate = this.safeDateParse(this.todayDate)
                    console.log('this.todayDate',this.todayDate)
                } else {
                    this.todayDate = new Date().getTime()
                }
            })
        },
        getPast15Days() {
            const dates = [];
            let now = new Date(this.todayDate);
            if (isNaN(now.getTime())) {
                now = new Date()
            }
            for (let i = 16; i >= 0; i--) {
                const pastDate = new Date(now);
                pastDate.setDate(now.getDate() - i);

                const year = pastDate.getFullYear();
                const month = String(pastDate.getMonth() + 1).padStart(2, '0');
                const day = String(pastDate.getDate()).padStart(2, '0');
                const hours = String(pastDate.getHours()).padStart(2, '0');
                const minutes = String(pastDate.getMinutes()).padStart(2, '0');
                const seconds = String(pastDate.getSeconds()).padStart(2, '0');

                if (dates.length == 1) {
                    dates.push(`${year}-${month}-${day} 00:00:00`);

                } else  {
                    dates.push(`${year}-${month}-${day} 23:59:59`);

                }
            }

            return dates;
        },
        getConstrolCounntyId(){
            let param = {
                'telNum': Storage.session.get('userInfo').servNumber,

            }
            let url = `/xsb/gridCenter/familyDiagnosis/h5QryIsCountyManager`
            this.$http.post(url, param).then((res) => {
                if (res.data.retCode == 0) {
                    this.countyIdList = res.data.data
                }
            })
        },
        async get15DaysFlg(){
            let dateArr = this.getPast15Days()
            console.log('dateArr',dateArr)
            if(!Storage.session.get('userInfo').crmId) {
                this.$toast('当前crmId为空,请切换到有crmId的岗位')
                return
            }
            let param1 = {
                "crmId": Storage.session.get('userInfo').crmId,
                "region": Storage.session.get('userInfo').region,
                "serviceNumber":  this.formInfo.busiTel, // 用户号码，11位手机号码，例如: "17823001821"
                "begin": dateArr[1], // 开始时间，格式：yyyy-mm-dd hh24:mi:ss
                "end": dateArr[16], // 结束时间，格式：yyyy-mm-dd hh24:mi:ss
                "busiCode": "", // 受理类型，见附录1，例如："CreateSubscriber"，如果不传则查询所有受理类型
                "operId": Storage.session.get('userInfo').crmId, // 跟进人手机号
                "status": "OS90", // 受理状态，OS20：处理中；OS90：完成；不传该参数表示查询所有状态
                "beId": Storage.session.get('userInfo').region, // 地区编码，取自字典组OC.AREA.TYPE，见附录4
                "incBackProcess": "Y", // 是否包含后台业务，Y：包含；N：不包含
                "incRollback": "", // 是否显示回退业务，Y：是；N：否
                "relaRequest": "", // 是否查询相关受理，Y：是；N：否
                "noPrintInvoice": "", // 是否查询未打印发票业务，Y：是；N：否
                "queryLogDetail": "", // 是否包含下级单位的业务，Y：是；N：否
                "beginRowNum": "0", // 开始行数，首次查询填0，后续填(当前页数-1)*每页行数
                "fetchRowNum": "10", // 每页显示行数，固定值，翻页查询时不能变
                "curPage": "1", // 当前页，首次查询填1
                "totalRowNum": "0", // 总行数，首次查询填0，第二次以后填返回的总记录数
            }
            console.log('h5GetOrderLogList',param1)
            let url1 = `/xsb/gridCenter/familyDiagnosis/h5GetOrderLogList`
            return this.$http.post(url1, param1)
        },

        async changeOwnFeedback(val) {
            if(val == '成功结单') {
                let day4Rresuly = await this.get15DaysFlg()
                console.log('day4Rresuly',day4Rresuly)
                if(day4Rresuly.data.retCode == 0) {
                    if (day4Rresuly.data.data.totalRowNum > 0) {
                        this.totalRowNum = day4Rresuly.data.data.totalRowNum
                    } else {
                        this.$toast('结单失败，结单人在15天内未给商机用户办理过业务')
                        return

                    }
                } else {
                    this.$toast('结单失败，结单人在15天内未给商机用户办理过业务')
                    return
                }
            }
            if (this.formInfo.isMessageVerify == 'false') {
                this.formInfo.followtype = '失败结单'
            } else {
                this.formInfo.followtype = val
            }
            this.formInfo.followresult = ''
            this.formInfo.newMsisdn = this.oldForm.newCardTel
            this.formInfo.detailRemark = ''
            this.formInfo.resontext = ''
            // 图片
            console.log('this.oldForm.ztVideoUrl', this.oldForm.ztVideoUrl)
            this.oldForm.ztVideoUrl = this.oldForm.ztVideoUrl.split(',')
            console.log('this.oldForm.ztVideoUrl--字符串', this.oldForm.ztVideoUrl)
            if (this.oldForm.ztVideoUrl) {
                for (let i = 0; i < this.oldForm.ztVideoUrl.length; i++) {
                    console.info(' this.oldForm.ztVideoUrl[i]', this.oldForm.ztVideoUrl[i])

                    ImageObj.getImgUrl(this.oldForm.ztVideoUrl[i], {}, '/xsb/personBusiness/queryCalculate/h5DownBillFollowUpPic').then(res => {
                        this.formInfo.thebase64.push(res)
                        this.formInfo.thebase642.push(this.oldForm.ztVideoUrl[i])
                        console.info(' this.formInfo.thebase64', res)
                        console.info(' this.formInfo.thebase64', this.formInfo.thebase64)
                    })
                }

            }

        },
        changeOwnFeedresult(val) {
            this.formInfo.followresult = val
            this.formInfo.detailRemark = ''
            this.formInfo.detailRemark = ''
            this.formInfo.resontext = ''
            console.log('this.formInfo---', this.formInfo)
        },
        changeOwnfileresult(val) {
            this.formInfo.filereson = val
            this.formInfo.newMsisdn = ''
            this.formInfo.thebase64 = []
            this.formInfo.thebase642 = []
            this.formInfo.detailRemark = ''
            this.formInfo.resontext = ''
        },
        // 个人掌厅拍照
        takePhoto() {
            ClientJs.openCameraAndShow('1', 'PalmHallPic')
        },
        // 个人结单
        statementAccount() {
            console.log(' this.formInfo.thebase642 ', this.formInfo.thebase642)
            if (!this.formInfo.followtype) {
                this.$toast('请选择结单类型')
                return
            } else {
                if (this.formInfo.followtype == '成功结单') {
                    if (!this.formInfo.detailRemark) {
                        this.$toast('请输入结单描述')
                        return
                    }
                }
                if (this.formInfo.followtype == '失败结单') {
                    this.formInfo.followresult = ''
                    this.formInfo.newMsisdn = ''
                    this.formInfo.thebase64 = []
                    this.formInfo.thebase642 = []
                    if (!this.formInfo.filereson) {
                        this.$toast('请选择失败原因')
                        return
                    } else {
                        if (this.formInfo.filereson == '其他') {
                            if (!this.formInfo.resontext) {
                                this.$toast('请输入原因明细')
                                return
                            }
                        }
                    }
                }
            }
            if (this.formInfo.newMsisdn) {
                var regnum2 = /^(13[0-9]|14[********]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/
                if (!regnum2.test(this.formInfo.newMsisdn)) {
                    this.$alert('请输入正确的手机号')
                    return
                }
            }
            let url = '/xsb/gridCenter/familyDiagnosis/h5BusinAccount'
            let param = {
                'region': Storage.session.get('userInfo').region,
                'crmId': Storage.session.get('userInfo').crmId,
                'buopId': this.formInfo.buopId,
                'busiTel': this.formInfo.busiTel,
                'busiSource': this.formInfo.busiSource,
                'busiChannel': this.formInfo.busiChannel,
                'accountType': this.formInfo.followtype == '成功结单' ? 1 : 2,// 结单类型
                'accountDesc': this.formInfo.detailRemark, // 结单描述
                'failReason': this.formInfo.filereson,// 失败原因
                'failReasonDesc': this.formInfo.resontext,// 失败原因描述
                'modifyOper': Storage.session.get('userInfo').operatorName,
                'modifyTel': Storage.session.get('userInfo').servNumber,
                'followOldBy': this.formInfo.followOldBy,
                'followOldTel': this.formInfo.followOldTel,
                'buopStatus': this.formInfo.buopStatus,
                'totalRowNum': this.totalRowNum,
                'buopNewStatus': this.formInfo.followtype == '成功结单' ? '12' : '13'
            }
            console.info(this.formInfo)
            console.info(param)
            this.$http.post(url, param).then((res) => {
                if (res.data.retCode == '0') {
                    this.$toast('提交成功')
                    this.formInfo.followtype = ''
                    this.formInfo.followresult = ''
                    this.formInfo.newMsisdn = ''
                    this.formInfo.thebase64 = []
                    this.formInfo.detailRemark = ''
                    this.formInfo.resontext = ''
                    this.personJiedanFlag = false
                    this.getListMy()
                    this.getListEmpty()
                    return
                } else {
                    this.$toast('提交失败')
                    return
                }
            })

        },
        cancerlpop() {
            this.formInfo.followtype = ''
            this.formInfo.followresult = ''
            this.formInfo.newMsisdn = ''
            this.formInfo.thebase64 = []
            this.formInfo.detailRemark = ''
            this.formInfo.resontext = ''
            this.formInfo.filereson = ''
            this.personJiedanFlag = false
        },
        goPersonWeihu(item, val, val2,text) {
            console.info('-----------------', item)
            // val1----是否显示按钮
            // val2----操作类型
            // text----审核待补充
            if (item.buopStatus == 15 || item.buopStatus == 16) {
                text = '审核待补充'
            }
            this.$router.push({
                path: '/PotentialuserMaintenance',
                query: {
                    editType:val2 ? 'supplement' : val == 0 ? 'view' : 'change',
                    editType2:text == '审核待补充'? '1' : '',
                    buopId: item.buopId,
                    busiTel: item.busiTel,
                    billid: item.releBillId,
                    homeId: item.homeId,
                    showbtn: val,
                    item: JSON.stringify(item)
                }
            })
        },
        goPersonSuanzhang(item) {
            console.info('去算账点击的item==》', item)
            if (item.releBillId) {

                let url = '/xsb/personBusiness/queryCalculate/h5RecordQuery'
                let params = {
                    operatorMsisdn: '',
                    operatorName: '',
                    billType: '2',
                    billId: item.releBillId,
                    msisdn: '',
                    createDate: '',
                    followDate: '',
                    page: 1,
                    size: 1,
                    flag: '1'

                }
                console.info('去算账通过billId查详情', params)

                this.$http.post(url, params).then((res) => {
                    console.info('去算账通过billId查详情', params, res)

                    let { retCode, retMsg, data } = res.data
                    if (retCode == '0') {//成功的情况
                        if (data && data.length) {
                            let items1 = data[0]
                            console.log({
                                respResult: 1,
                                strList: items1.regMsisdn + '|' + items1.billId,
                                items: JSON.stringify(items1),
                                goform: 1,
                                itemObj: JSON.stringify(item),
                                busiTel: item.busiTel,
                                releBillId: item.releBillId,
                                buopId: item.buopId

                            })
                            this.$router.push({
                                path: '/familyDiagnosisDeskTop2',
                                query: {
                                    respResult: 1,
                                    strList: items1.regMsisdn + '|' + items1.billId,
                                    items: JSON.stringify(items1),
                                    goform: 1,
                                    itemObj: JSON.stringify(item),
                                    busiTel: item.busiTel,
                                    releBillId: item.releBillId,
                                    buopId: item.buopId

                                }
                            })
                        } else {
                            this.$router.push({
                                path: '/familyDiagnosis2',
                                query: {
                                    telnum: item.busiTel,
                                    busiTel: item.busiTel,
                                    buopId: item.buopId,
                                    goform: 1,
                                    itemObj: JSON.stringify(item),
                                    releBillId: item.releBillId,
                                    isYiDong: '0'

                                }
                            })
                        }
                    } else {
                        this.$alert(retMsg || '请求失败')
                    }
                })
            } else {
                this.$router.push({
                    path: '/familyDiagnosis2',
                    query: {
                        telnum: item.busiTel,
                        busiTel: item.busiTel,
                        buopId: item.buopId,
                        itemObj: JSON.stringify(item),
                        isYiDong: '0',
                        releBillId: item.releBillId,

                        goform: 1

                    }
                })
            }

        },
        PersonZhipai(item) {
            let arr = [
                { 'region': '11', 'longRegion': '1000512' }, //苏州
                { 'region': '12', 'longRegion': '1000517' }, //淮安
                { 'region': '13', 'longRegion': '1000527' }, //宿迁
                { 'region': '14', 'longRegion': '1000250' }, //南京
                { 'region': '15', 'longRegion': '1000518' }, //连云港
                { 'region': '16', 'longRegion': '1000516' }, //徐州
                { 'region': '17', 'longRegion': '1000519' }, //常州
                { 'region': '18', 'longRegion': '1000511' }, //镇江
                { 'region': '19', 'longRegion': '1000510' }, //无锡
                { 'region': '20', 'longRegion': '1000513' }, //南通
                { 'region': '21', 'longRegion': '1000523' }, //泰州
                { 'region': '22', 'longRegion': '1000515' }, //盐城
                { 'region': '23', 'longRegion': '1000514' }, //扬州
                { 'region': '99', 'longRegion': '1000250' } //江苏省
            ]
            for (let i = 0; i < arr.length; i++) {
                if (arr[i].region == Storage.session.get('userInfo').region) {
                    if (arr[i].longRegion == '2000250') {
                        this.orgId = '1000250'
                    } else {
                        this.orgId = arr[i].longRegion
                    }
                }
            }
            this.zhipaiparam = item
            this.orgId2 = item.countyId
            this.showExecute = true
            this.chooseidPerson = item.busiTel

            this.chooseidPerson2 = item.buopId


            console.log(3333333333333333)

            console.info(item)
            console.info(this.showExecute)
            console.info(this.orgId2)

        },
        personZhuanPai(item) {
            this.personZhuanpaiParam = item
            this.showExecute2 = true
            this.chooseidPerson3 = item.busiTel
            this.chooseidPerson4 = item.buopId
        },
        closeExecute(val) {
            this.showExecute = false
            this.showExecute2 = false
            this.showExecute3 = false
            this.showExecute4 = false
            this.showExecute5 = false
            this.showTuanDanZhiPaiReson = false
            if (val) {
                history.go(0)
            }
        },
        assignSubmit(item) {
            // 指派
            this.chooseitem1Person = item
            this.showExecute = false
            this.assignSure()
            this.pageNum=1
            this.getListMy()

        },
        reassignSubmit(item,type) {
            if (type) {
                // 跨地市转派
                console.log('跨地市转派---',item,type)
                let shangjiObj = this.personZhuanpaiParam
                console.info('跨地市转派---shangjiObj---', shangjiObj)
                this.showExecute2 = false
                this.acrossReassignSure(shangjiObj,item,type)

            } else {
                // 正常转派/商机池
                this.chooseitem2Person = item
                this.showExecute2 = false
                this.reassignSure()
            }
        },
        acrossReassignSure(SJitem,areaItem,type){
            // 跨地市转派
            //SJitem--商机对象  areaItem--地区对象  type--类型
            // 跨地市转派
            let url = '/xsb/gridCenter/familyDiagnosis/h5CrossRegionTresfer'
            let params = {
                "region": Storage.session.get('userInfo').region == 99 ? 14 : Storage.session.get('userInfo').region,
                "buopId": SJitem.buopId,
                "operateType": 2,
                "busiType":  SJitem.busiType,
                "busiTel":  SJitem.busiTel,
                "followOldBy": SJitem.followBy,
                "followOldTel": SJitem.followTel,
                "buopOldStatus": SJitem.buopStatus,
                "buopNewStatus": type == 'city' ? SJitem.buopStatus : '5',
                "modifyOper": Storage.session.get('userInfo').operatorName,
                'modifyTel': Storage.session.get('userInfo').servNumber,//修改人（当前操作人电话）
                "regionId": "",
                "countyName": "",
                "countyId": ""
            }
            if (type == 'city') {
                params.regionId = areaItem.city.shortId
            } else {
                params.regionId = areaItem.city.shortId
                params.countyName = areaItem.branch.label
                params.countyId = areaItem.branch.id
            }
            console.log('跨地市转派-请求',params)
            this.$http.post(url, params).then((res) => {
                if (res.data.retCode == '0') {
                    this.$toast('操作成功')
                    this.pageNum=1
                    this.getListMy()
                    this.getListEmpty()
                    this.choosereson = ''

                } else {
                    this.$toast('商机操作失败' || res.data.retMsg)
                }
            })

        },
        assignSure() {
            // 指派-确定
            let item2 = this.chooseitem1Person
            let item = this.zhipaiparam
            console.info(item)
            console.info(item2)
            let url0 = '/xsb/gridCenter/familyDiagnosis/h5QueryCurrentPosiBusiTel'
            let params0 = {
                'crmId': Storage.session.get('userInfo').crmId,
                'region': Storage.session.get('userInfo').region == 99 ? 14 : Storage.session.get('userInfo').region,
                'buopTel': item.busiTel,  //商机id
                'operateTel': item2.execMsisdn,
                'busiType': item.busiType

            }
            console.info('指派----》', params0)

            this.$http.post(url0, params0).then((res0) => {
                console.info('res0.data.data---------------', res0.data.data)
                if (res0.data.data == true) {
                    let url = '/xsb/gridCenter/familyDiagnosis/h5UpdateBusiStatus'
                    let params = {
                        'transferSource': 1,
                        'busiType': item.busiType,
                        'countyId': item.countyId,
                        'crmId': Storage.session.get('userInfo').crmId,
                        'region': Storage.session.get('userInfo').region == 99 ? 14 : Storage.session.get('userInfo').region,
                        'buopId': item.buopId,  //商机id
                        'busiTel': item.busiTel, //商机号码
                        'followOldBy': item.followOldBy,  //上一跟进人
                        'followOldTel': item.followOldTel,//上一跟进人电话
                        'followBy': item.followBy,//当前跟进人（当前操作人）
                        'followTel': item.followTel,//当前跟进人电话（当前操作人电话）
                        'buopStatus': item.buopStatus,//商机状态
                        'busiReleaseReason': '', //商机释放原因
                        'busiTransferReason': this.choosereson == 1 ? '' : '',//商机转派原因
                        'modifyOper': Storage.session.get('userInfo').operatorName,//修改人（当前操作人）
                        'modifyTel': Storage.session.get('userInfo').servNumber,//修改人（当前操作人电话）
                        'operateType': '7',//商机操作类型
                        'transferPerson': item2.execName,//转派或者指派人
                        'transferTel': item2.execMsisdn//转派或者指派人电话
                    }

                    console.log('h5UpdateBusiStatus', params)
                    this.$http.post(url, params).then((res) => {
                        if (res.data.retCode == '0') {
                            this.$toast('操作成功')

                            this.getListMy()
                            this.getListEmpty()
                            this.choosereson = ''

                        } else {
                            this.$toast('指派失败' || res.data.retMsg)

                        }
                    })
                } else {
                    this.$toast('有同商机号码商机,无法操作')
                }
            })
        },
        reassignSure() {
            //转派-释放
            let item = this.personZhuanpaiParam
            let item2 = this.chooseitem2Person
            console.info('item---', item)
            console.info('item2---', item2)
            let url0 = ''
            if (item2) {
                url0 = '/xsb/gridCenter/familyDiagnosis/h5QueryCurrentPosiBusiTel'
            } else {
                url0 = '/xsb/gridCenter/familyDiagnosis/h5QueryBusiPoolBusiTel'

            }
            let params0 = {
                'crmId': Storage.session.get('userInfo').crmId,
                'region': Storage.session.get('userInfo').region == 99 ? 14 : Storage.session.get('userInfo').region,
                'buopTel': item.busiTel,  //商机id
                'operateTel': item2 ? item2.execMsisdn : '',
                'busiType': item.busiType

            }
            console.info(params0)
            this.$http.post(url0, params0).then((res0) => {
                if (res0.data.data == true) {
                    let url = '/xsb/gridCenter/familyDiagnosis/h5UpdateBusiStatus'
                    let params = {
                        'transferSource': 1,
                        'busiType': item.busiType,
                        'countyId': item.countyId,
                        'crmId': Storage.session.get('userInfo').crmId,
                        'region': Storage.session.get('userInfo').region == 99 ? 14 : Storage.session.get('userInfo').region,
                        'buopId': item.buopId,  //商机id
                        'busiTel': item.busiTel, //商机号码
                        'followOldBy': item.followOldBy,  //上一跟进人
                        'followOldTel': item.followOldTel,//上一跟进人电话
                        'followBy': item.followBy,//当前跟进人（当前操作人）
                        'followTel': item.followTel,//当前跟进人电话（当前操作人电话）
                        'buopStatus': item.buopStatus,//商机状态
                        'busiReleaseReason': '', //商机释放原因
                        'busiTransferReason': '',
                        'modifyOper': Storage.session.get('userInfo').operatorName,//修改人（当前操作人）
                        'modifyTel': Storage.session.get('userInfo').servNumber,//修改人（当前操作人电话）
                        'operateType': item2 ? 2 : 3,//商机操作类型
                        'transferPerson': item2 ? item2.execName : '',//转派或者指派人
                        'transferTel': item2 ? item2.execMsisdn : ''//转派或者指派人电话
                    }
                    console.info('转派=====》', params)
                    this.$http.post(url, params).then((res) => {
                        if (res.data.retCode == '0') {
                            this.$toast('操作成功')
                            this.pageNum = 1
                            this.getListMy()
                            this.getListEmpty()
                            this.choosereson = ''

                        } else {
                            this.$toast('商机操作失败' || res.data.retMsg)

                        }
                    })
                } else {
                    this.$toast('有同商机号码商机,无法操作')

                }
            })
        },
        sure3() {
            // 释放
            if (!this.choosereson) {
                this.$toast('请选择原因')
                return
            }
            let item = this.chooseitem2
            console.info('item---', item)
            let url0 = '/xsb/gridCenter/familyDiagnosis/h5QueryBusiPoolBusiTel'
            let params0 = {
                'crmId': Storage.session.get('userInfo').crmId,
                'region': Storage.session.get('userInfo').region,
                'buopTel': item.busiTel,  //商机id
                'busiType': item.busiType

            }
            console.info(params0)
            this.$http.post(url0, params0).then((res0) => {
                if (res0.data.data == true) {
                    let url = '/xsb/gridCenter/familyDiagnosis/h5UpdateBusiStatus'
                    let params = {
                        'busiType': item.busiType,
                        'transferSource': 1,
                        'crmId': Storage.session.get('userInfo').crmId,
                        'region': Storage.session.get('userInfo').region,
                        'buopId': item.buopId,  //商机id
                        'busiTel': item.busiTel, //商机号码
                        'followOldBy': item.followOldBy,  //上一跟进人
                        'followOldTel': item.followOldTel,//上一跟进人电话
                        'followBy': item.followBy,//当前跟进人（当前操作人）
                        'followTel': item.followTel,//当前跟进人电话（当前操作人电话）
                        'buopStatus': item.buopStatus,//商机状态
                        'busiReleaseReason': this.choosereson == 1 ? '需要专家支援' : '客户拒绝办理',//商机转派原因 //商机释放原因
                        'busiTransferReason': '',//商机转派原因
                        'modifyOper': Storage.session.get('userInfo').operatorName,//修改人（当前操作人）
                        'modifyTel': Storage.session.get('userInfo').servNumber,//修改人（当前操作人电话）
                        'operateType': '3',//商机操作类型
                        'transferPerson': '',//转派或者指派人
                        'transferTel': ''//转派或者指派人电话
                    }
                    this.$http.post(url, params).then((res) => {
                        if (res.data.retCode == '0') {
                            this.getListMy()
                            this.getListEmpty()
                            this.showreson3 = false
                            this.choosereson = ''
                        } else {
                            this.$toast('商机释放失败')

                        }
                    })
                } else {
                    this.$toast('商机池中同地市下有同商机号码商机')
                }
            })
        },
        PersonRenling(item) {
            this.$messagebox({
                title: '温馨提示',
                showCancelButton: true,
                message: '该商机将流转到您岗上，您可以在"我的商机"中查看及跟进。',
                confirmButtonText: '确定'
            }).then((ressend) => {
                if (ressend === 'confirm') {
                    let url0 = '/xsb/gridCenter/familyDiagnosis/h5QueryCurrentPosiBusiTel'
                    let params0 = {
                        'crmId': Storage.session.get('userInfo').crmId,
                        'region': Storage.session.get('userInfo').region,
                        'buopTel': item.busiTel,  //商机id
                        'operateTel': Storage.session.get('userInfo').servNumber,
                        'busiType': item.busiType

                    }
                    console.info('认领-----》', params0)
                    this.$http.post(url0, params0).then((res0) => {
                        if (res0.data.data == true) {
                            let url = '/xsb/gridCenter/familyDiagnosis/h5UpdateBusiStatus'
                            let params = {
                                'transferSource': 1,
                                'busiType': item.busiType,
                                'crmId': Storage.session.get('userInfo').crmId,
                                'region': Storage.session.get('userInfo').region,
                                'buopId': item.buopId,  //商机id
                                'busiTel': item.busiTel, //商机号码
                                'followOldBy': item.followOldBy,  //上一跟进人
                                'followOldTel': item.followOldTel,//上一跟进人电话
                                'followBy': item.followBy,//当前跟进人（当前操作人）
                                'followTel': item.followTel,//当前跟进人电话（当前操作人电话）
                                'buopStatus': item.buopStatus,//商机状态
                                'busiReleaseReason': '', //商机释放原因
                                'busiTransferReason': '',//商机转派原因
                                'modifyOper': Storage.session.get('userInfo').operatorName,//修改人（当前操作人）
                                'modifyTel': Storage.session.get('userInfo').servNumber,//修改人（当前操作人电话）
                                'operateType': '8',//商机操作类型
                                'transferPerson': '',//转派或者指派人
                                'transferTel': ''//转派或者指派人电话
                            }
                            console.log('renling-------',params)
                            this.$http.post(url, params).then((res) => {
                                if (res.data.retCode == '0') {
                                    this.$toast('认领成功')

                                    this.getListMy()
                                    this.getListEmpty()

                                } else {
                                    this.$toast('认领失败' || res.data.restMsg)

                                }
                            })
                        } else {
                            this.$toast('有同商机号码商机,无法操作')
                        }
                    })
                }
            })


        },
        YWRenling(item) {
            this.$messagebox({
                title: '温馨提示',
                showCancelButton: true,
                message: '该商机将流转到您岗上，您可以在"我的商机"中查看及跟进。',
                confirmButtonText: '确定'
            }).then((ressend) => {
                if (ressend === 'confirm') {
                    let url0 = '/xsb/gridCenter/familyDiagnosis/h5QueryCurrentPosiBusiTel'
                    let params0 = {
                        'crmId': Storage.session.get('userInfo').crmId,
                        'region': Storage.session.get('userInfo').region,
                        'buopTel': item.busiTel,  //商机id
                        'operateTel': Storage.session.get('userInfo').servNumber,
                        'busiType': item.busiType

                    }
                    console.info('认领-----》', params0)
                    this.$http.post(url0, params0).then((res0) => {
                        if (res0.data.data == true) {
                            let url = '/xsb/gridCenter/familyDiagnosis/h5UpdateBusiStatus'
                            let params = {
                                'transferSource': 1,
                                'busiType': item.busiType,
                                'crmId': Storage.session.get('userInfo').crmId,
                                'region': Storage.session.get('userInfo').region,
                                'buopId': item.buopId,  //商机id
                                'busiTel': item.busiTel, //商机号码
                                'followOldBy': item.followOldBy,  //上一跟进人
                                'followOldTel': item.followOldTel,//上一跟进人电话
                                'followBy': item.followBy,//当前跟进人（当前操作人）
                                'followTel': item.followTel,//当前跟进人电话（当前操作人电话）
                                'buopStatus': item.buopStatus,//商机状态
                                'busiReleaseReason': '', //商机释放原因
                                'busiTransferReason': '',//商机转派原因
                                'modifyOper': Storage.session.get('userInfo').operatorName,//修改人（当前操作人）
                                'modifyTel': Storage.session.get('userInfo').servNumber,//修改人（当前操作人电话）
                                'operateType': '8',//商机操作类型
                                'transferPerson': '',//转派或者指派人
                                'transferTel': ''//转派或者指派人电话
                            }
                            this.$http.post(url, params).then((res) => {
                                if (res.data.retCode == '0') {
                                    this.$toast('认领成功')

                                    this.getListMy()
                                    this.getListEmpty()

                                } else {
                                    this.$toast('认领失败' || res.data.restMsg)

                                }
                            })
                        } else {
                            this.$toast('有同商机号码商机,无法操作')
                        }
                    })
                }
            })


        },

        huishouBtn(item) {
            // 回收
            let url = '/xsb/gridCenter/familyDiagnosis/h5UpdateBusiStatus'
            let params = {
                'transferSource': 1,
                'busiType': item.busiType,
                'crmId': Storage.session.get('userInfo').crmId,
                'region': Storage.session.get('userInfo').region,
                'buopId': item.buopId,  //商机id
                'busiTel': item.busiTel, //商机号码
                'followOldBy': item.followOldBy,  //上一跟进人
                'followOldTel': item.followOldTel,//上一跟进人电话
                'followBy': item.followBy,//当前跟进人（当前操作人）
                'followTel': item.followTel,//当前跟进人电话（当前操作人电话）
                'buopStatus': item.buopStatus,//商机状态
                'busiReleaseReason': '', //商机释放原因
                'busiTransferReason': '',//商机转派原因
                'modifyOper': Storage.session.get('userInfo').operatorName,//修改人（当前操作人）
                'modifyTel': Storage.session.get('userInfo').servNumber,//修改人（当前操作人电话）
                'operateType': '4',//商机操作类型
                'transferPerson': '',//转派或者指派人
                'transferTel': ''//转派或者指派人电话
            }
            this.$http.post(url, params).then((res) => {
                if (res.data.retCode == '0') {
                    this.getListMy()
                    this.getListEmpty()

                } else {

                }
            })
        },

        jujueOrJieshou(item, idx) {
            // idx  6-拒绝   5-接受  按钮
            let url = '/xsb/gridCenter/familyDiagnosis/h5UpdateBusiStatus'
            let params = {
                'busiType': item.busiType,
                'transferSource': 1,
                'crmId': Storage.session.get('userInfo').crmId,
                'region': Storage.session.get('userInfo').region,
                'buopId': item.buopId,  //商机id
                'busiTel': item.busiTel, //商机号码
                'followOldBy': item.followOldBy,  //上一跟进人
                'followOldTel': item.followOldTel,//上一跟进人电话
                'followBy': item.followBy,//当前跟进人（当前操作人）
                'followTel': item.followTel,//当前跟进人电话（当前操作人电话）
                'buopStatus': item.buopStatus,//商机状态
                'busiReleaseReason': '',//商机转派原因 //商机释放原因
                'busiTransferReason': '',//商机转派原因
                'modifyOper': Storage.session.get('userInfo').operatorName,//修改人（当前操作人）
                'modifyTel': Storage.session.get('userInfo').servNumber,//修改人（当前操作人电话）
                'operateType': idx,//商机操作类型
                'transferPerson': '',//转派或者指派人
                'transferTel': ''//转派或者指派人电话
            }

            console.info('jujueOrJieshou----', params.operateType)
            this.$http.post(url, params).then((res) => {
                if (res.data.retCode == '0') {
                    this.getListMy()
                    this.getListEmpty()
                } else {
                    this.$toast('商机操作失败')

                }
            })
        }
    },
    beforeRouteLeave(to,from,next) {
        Storage.session.set('SJTab',this.topType2);
        Storage.session.set('SJtype0',this.type0);
        Storage.session.set('SJtype1',this.type1);
        Storage.session.set('SJtype2',this.type2);
        Storage.session.set('SJcomFormType1',this.comFormType1);
        Storage.session.set('SJcomFormType2',this.comFormType2);
        Storage.session.set('SJchooseTime1',this.chooseTime1);
        Storage.session.set('SJchooseTime2',this.chooseTime2);
        Storage.session.set('SJchooseTime3',this.chooseTime3);
        next()
    }
}
</script>

<style scoped lang='less'>
.pbo-wrap {
    padding: 50px 15px;
    height: 100vh;
    overflow: auto;
    box-sizing: border-box;
    background: #F5F6F7;

    .tab-header {
        position: fixed;
        left: 0;
        top: 44px;
        width: 100vw;
        line-height: 44px;
        z-index: 333;
        display: flex;

        .tab-item {
            flex: 1;
            font-size: 15px;
            text-align: center;
            border-bottom: 1px solid #bbb;

            &.active {
                color: #0b7ffe;
                font-weight: 700;
                border-bottom: 1px solid #0b7ffe;
                background: #f4feff;
            }
        }
    }

    .search-box {
        position: fixed;
        left: 0;
        top: 45px;
        width: 100vw;
        box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.2);
        padding: 5px 10px 5px;
        box-sizing: border-box;
        z-index: 333;
        background: linear-gradient(180deg, #F7FBFF 0%, #FFFFFF 100%);
        box-shadow: 0px 3px 10px 0px rgba(86, 125, 244, 0.05);
        border-radius: 0px 0px 0px 0px;

        .tab-header2 {
            width: 100%;
            line-height: 30px;
            z-index: 333;
            display: flex;

            .tab-item {
                flex: 1;
                font-size: 14px;
                text-align: center;
                border-bottom: 1px solid #bbb;

                &.active {
                    color: #0b7ffe;
                    border-bottom: 1px solid #0b7ffe;
                }
            }
        }

        .tab-sj {
            width: 100%;
            margin: 0 auto;
            display: flex;
            margin-bottom: 20px;

            .item {
                flex: 1;
                text-align: center;
                line-height: 28px;
                font-size: 16px;
                border-radius: 9px;
                position: relative;

                &.active {
                    color: #0b7ffe;

                    &::before {
                        content: '';
                        position: absolute;
                        width: 110px;
                        height: 4px;
                        background: #0b7ffe;
                        left: 50%;
                        bottom: -5px;
                        border-radius: 4px;
                        transform: translateX(-50%);
                    }
                }
            }

        }

        .search-input {
            margin: 0 auto;
            display: flex;
            border: 1px solid #9DC2EB;
            border-radius: 6px;
            margin-top: 15px;
            overflow: hidden;
            position: relative;
            width: calc(100% - 20px);

            .sousuo1 {
                position: absolute;
                left: 10px;
                top: 9px;
            }

            .sou {
                position: absolute;
                right: 5px;
                font-size: 14px;
                color: #0b7ffe;
                top: 10px;
            }

            .one-line {
                position: absolute;
                left: 100px;
                top: 7px;
                width: 1px;
                height: 16px;
                background: #929292;
            }

            .ssss {
                left: 6px !important;
                top: 7px;
            }

            .guanbi1 {
                position: absolute;
                right: 36px;
                top: 9px;
            }

            .search-down {
                width: 100px;
                text-align: center;
                font-size: 12px;
                line-height: 30px;
                background: #f4feff;
                color: #007AFF;
                box-shadow: 0px 0px 7px 5px rgba(0, 0, 0, 0.1);
            }

            .search-input1 {
                flex: 1;
            }

            .xiala {
                font-size: 8px !important;
            }

            input {
                width: calc(100% - 15px);
                height: 36px;
                padding: 0 70px 0 30px;
                box-sizing: border-box;
                font-size: 14px;
                outline: 0;
            }
        }

        .drop-drown {
            width: 100%;
            display: flex;
            margin-top: 15px;
            padding-left: 15px;
            color: #007AFF;


            .item {
                width: 40%;
                text-align: left;
                font-size: 12px;

                .xiala {
                    font-size: 10px;
                    margin-left: 5px;
                }
            }
        }
    }

    .lists-box {
        left: 0;
        top: 215px;
        width: 100vw;
        padding: 0px 20px 80px;
        box-sizing: border-box;
        z-index: 66;
        background: #F5F6F7;
        overflow-y: scroll;
        flex-grow: 1;
        -webkit-overflow-scrolling: touch;
        position: fixed;
        margin-bottom: 76px;
        height: calc(100vh - 215px);

        .list-item {
            padding: 10px;
            border-radius: 8px;
            position: relative;
            margin-bottom: 20px;
            background: #fff;
            border: 2px solid #fff;
            overflow: hidden;
            box-shadow: 0px 0px 8px 0px rgba(167, 167, 167, 0.2312);

            .control {
                background: #fff;
                font-size: 12px;
                line-height: 26px;
                color: #0b7ffe;
                padding: 6px;
                border-radius: 6px;
                width: 80px;
                text-align: center;
                position: absolute;
                right: 33px;
                top: 10px;
                box-shadow: 0px 0px 7px 5px rgba(0, 0, 0, 0.1);
                z-index: 99999;

                .youjiantou {
                    font-size: 10px;
                    vertical-align: top;
                    line-height: 26px;
                }
            }

            .gengduo {
                font-size: 18px;
                color: #0b7ffe;
                position: absolute;
                right: 10px;
                top: 10px;
            }

            .line1 {
                margin-bottom: 8px;

                img {
                    width: 20px;
                    vertical-align: middle;
                    margin-right: 4px;
                }

                .tel {
                    font-size: 18px;
                    vertical-align: middle;
                    font-weight: 700;
                    display: inline-block;
                    .xiansuo {
                        display: inline-block;
                        position: absolute;
                        font-weight: 400;
                        background: #ffd4d4;
                        padding: 2px 4px;
                        color: red;
                        top: 5px;
                        border-radius: 3px;
                        font-size: 10px;
                    }
                }

                .tel-group {
                    font-size: 16px;
                    width: calc(100% - 37px);

                }

                .tag {
                    font-size: 12px;
                    vertical-align: middle;
                    margin-left: 5px;
                    padding: 2px 3px;
                    border-radius: 3px;
                    position: absolute;
                    right: 0;
                    top: 0;
                    width: 53px;
                    height: 20px;
                    border-radius: 0 0 0 16px;
                    padding-left: 10px;
                    padding-top: 4px;
                    box-sizing: border-box;
                }

                .tag0 {
                    background: #e7e4e4;
                    color: #434343;
                }

                .tag1 {
                    background: #FFEBDB;
                    color: #FC7F04;
                }

                .tag2 {
                    background: #d2e0ff;
                    color: #3F7AFF;
                }

                .tag3 {
                    background: green;
                    color: #fff;
                }

                .tag33 {
                    background: green;
                    color: #fff;
                    width: 66px !important;

                }

                .tag4 {
                    background: #ffdabd;
                    width: 66px !important;
                    color: #d96c00;
                }

                .tag7 {
                    background: #ffdabd;
                    width: 77px !important;
                    color: #d96c00;
                }

                .tag5 {
                    background: #ffc0c0;
                    width: 88px !important;
                    color: red;
                }

                .tag6 {
                    background: #ffc0c0;
                    width: 66px !important;
                    color: red;
                }
            }

            .line2 {
                font-size: 14px;
                line-height: 24px;
                padding-left: 5px;

                .title {
                    color: #727272;
                    font-size: 14px;

                }

                .value {
                    color: #404040;
                }
            }

        }
    }

    .lists-box2 {
        top: 240px;

    }

}

.lists {
    width: 100%;
    position: fixed;
    top: 166px;
    height: 100%;
    background: #F9F9F9;
    overflow: auto;

    .list {
        position: relative;
        margin: 16px 12px;
        background-color: #fff;
        padding: 15px;
        border-radius: 8px;
        box-shadow: 0px 4px 8px 0px rgba(204, 204, 204, 0.5);
        font-size: 14px;

        .createTimeShow {
            margin: 10px 0;
        }

        .title {
            font-size: 14px;
            color: #868686;
        }

        .content {
            font-size: 14px;
            color: #1D1D1D;
        }

        .tel {
            font-weight: 600;
            color: #232323;
        }

        .audit-flag {
            display: flex;
            align-items: center;
            position: absolute;
            top: 5px;
            right: 10px;

            .iconfont {
                margin-right: 3px;
            }

            .wenhao1-font-size {
                font-size: 14px;
            }

            .checkbox2-font-size {
                font-size: 15px;
            }
        }

        .audit-yet-color {
            color: #4B7902;
        }

        .audit-wait-color {
            color: #B8741A;
        }

        .audit-fail-color {
            color: red;
        }

        .tels {
            display: flex;
            align-items: center;
            flex-wrap: wrap;

            .chengyuan2 {
                font-size: 20px;
            }

            .highLight {
                color: #318DFF;
            }

            .lowLight {
                color: #999;
            }
        }

        .indexShow {
            position: absolute;
            top: 0;
            left: 0;
            width: 25px;
            height: 15px;
            background-color: #3E9BFF;
            color: #fff;
            text-align: center;
            line-height: 15px;
            border-radius: 8px 0 8px 0;
            font-size: 12px;
        }
    }

    .jiedan {
        background: #ECECEC;
    }

}

.dislog {
    position: fixed;
    width: 85%;
    padding: 6px 0 0;
    border-radius: 4px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 555;
    background: #fff;
    text-align: center;
    box-shadow: 0px 0px 7px 5px rgba(0, 0, 0, 0.02);
    margin-top: -40%;

    .title {
        font-size: 14px;
        font-weight: 700;
        line-height: 34px;

    }

    .reson-box {
        display: flex;
        margin-top: 5px;

        .chooseone {
            flex: 1;
            font-size: 14px;
            line-height: 32px;
            position: relative;
            color: #929292;

            .duihao1 {
                position: absolute;
                top: -1px;
                color: green;
            }
        }

        .yuan {
            width: 14px;
            height: 14px;
            display: inline-block;
            border-radius: 100%;
            vertical-align: middle;
            border: 1px solid #929292;
            margin-right: 5px;
            text-align: left;
        }
    }

    .reson-box2 {
        display: flex;
        border-top: 1px solid #bbb;
        margin-top: 25px;

        .chooseone {
            flex: 1;
            font-size: 16px;
            line-height: 32px;
            position: relative;
            line-height: 36px;

            &:first-child {
                border-right: 1px solid #bbb;
            }

            &:last-child {
                color: #0b7ffe;
            }
        }
    }
}

.dislog2 {
    width: 92%;
}

.hide-box, .hide-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, .5);
    z-index: 444;
}

.hide-box2 {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, .5);
    z-index: 444;
}

.just-box {
    height: 0;
}

.box-btn {
    width: 100%;
    overflow: hidden;

    .control-item {
        float: right;
        margin: 5px;
        width: calc(25% - 10px);
        background: #007AFF;
        text-align: center;
        color: #fff;
        padding: 6px 0;
        font-size: 12px;
        border-radius: 4px;

        .iconfont {
            font-size: 12px !important;
            margin-right: 3px;
        }
    }

    .control-item2 {
        background: red;
    }
}

.jiedan-box {
    position: fixed;
    left: 0;
    bottom: 0;
    padding: 15px;
    background: #fff;
    box-sizing: border-box;
    width: 100vw;
    z-index: 998;

    .title {
        text-align: center;
        font-size: 18px;
        font-weight: 700;

    }

    .title2 {
        text-align: left;
        font-size: 14px;
        font-weight: 700;
        margin: 10px 0;

    }

    .btn0 {
        background: #929292;
    }

    .btn {
        margin: 15px 0;
    }
}

.pop-textarea-border {
    border: 1px solid #bbb;
    position: relative;

}

.detail-remark {
    margin-top: 10px;
}

textarea {
    width: 100%;
    height: 60px;
    font-weight: 400;
    color: black;
    resize: none;
    outline: none;
    border: none;
    margin-top: 10px;
    background: #fff;
    padding: 5px;
    box-sizing: border-box;
    border: 1px solid #bbb;
    font-size: 12px;
}

.pop-textarea {
    font-size: 12px;
    height: 80px;
    overflow: hidden;
    outline: none;
    border: 0;
    resize: none;
    margin-top: 0;
}

.pop-textarea-num {
    position: absolute;
    bottom: 5px;
    right: 10px;
    font-size: 12px;
    color: #7d7d7d;
}

textarea::-webkit-input-placeholder, input::-webkit-input-placeholder { /* Chrome/Safari/Opera */
    color: rgb(158, 155, 155);
    font-size: 14px;
}

input::-moz-placeholder { /* Firefox 19+ */
    color: rgb(158, 155, 155);
    font-size: 14px;

}

input:-ms-input-placeholder { /* IE 10+ 注意这里只有一个冒号 */
    color: rgb(158, 155, 155);
    font-size: 14px;

}

.btn {
    width: 100%;
    border-radius: 7px;
    line-height: 36px;
    margin: 25px 0 10px;
    background: #0b7ffe;
    color: #fff;
    text-align: center;
    font-size: 14px;
}

.action-step-detail {
    line-height: 36px;
    display: flex;
    justify-content: space-between;
    font-size: 14px;

    .action-step-text {
        white-space: nowrap;
    }

    input {
        text-align: right;
        outline: none;
        font-size: 12px;
    }

    .luyin {
        font-size: 14px;
        color: #0b7ffe;
    }

    .chooseTextStyle {
        color: #9E9B9B;
        line-height: 24px
    }

    .action-step-detail-btnlist {
        text-align: right !important;

        span {
            display: inline-block;
            margin: 0 2px;
            padding: 0px 3px;
            line-height: 20px;
            border: 1px solid #ebe9e9;
            background: #ebe9e9;
            font-size: 13px;
            border-radius: 3px;
            min-width: 56px;
            text-align: center;
            box-sizing: border-box;
        }
    }

    .xiangji {
        font-size: 14px;
        color: rgb(16, 142, 233);
        margin-right: 3px;
    }

    .jiantou-copy-copy {
        font-size: 14px;
        color: #9E9B9B;
    }

    .red-info {
        color: red;
    }

}

.img {
    overflow: hidden;
    width: 100%;

    img {
        float: right;
        width: 140px;
        height: 70px;

    }
}

.active-span {
    background: #0b7ffe !important;
    color: #fff;
}

.img-box {
    width: calc(33% - 10px);
    display: inline-block;
    position: relative;
    overflow: hidden;
    margin-right: 10px;

    img {
        width: 100%;
        height: 80px;
        float: right;
    }

    .guanbi2 {
        position: absolute;
        right: 10px;
        top: 5px;
        color: red;
    }
}

.mint-popup-bottom {
    width: 100vw;
}

.chooseDialog {
    z-index: 456;
    background: #fff;
    padding: 12px;
    box-shadow: 2px 2px 6px 0px #747576;
    width: 100%;
    box-sizing: border-box;

    .guanbi {
        float: right;
    }

    .title {
        font-size: 18px;
        line-height: 26px;
        color: #000;
        text-align: center;
    }

    .info {
        font-size: 14px;
        line-height: 22px;
        margin-top: 8px;
    }

    .btnlist {
        margin-top: 8px;
        display: block !important;
        width: 100%;
        margin-bottom: 50px;
        margin-top: 15px;

        div {
            display: inline-block;
            background: #1681FB;
            text-align: center;
            padding: 0 15px;
            box-sizing: border-box;
            margin-top: 15px;
            border-radius: 5px;
            color: #fff;
            font-size: 15px;
            width: calc(33% - 16px);
            margin-right: 10px;
            line-height: 54px;
            p {
                margin-top: -3px;
            }
        }
        .item-btn {
            padding-top: 5px;
            height: 54px;
            line-height: 25px;
        }
    }
}

.pbo-wrap2 {
    padding-left: 0 !important;
    padding-right: 0 !important;
}

.kanban-box {
    background: #fff;
    height: calc(100vh - 150px);
    padding: 0 15px;
    box-sizing: border-box;
    width: 100vw;
    overflow-x: hidden;
    overflow-y: auto;
    position: fixed;
    top: 110px;
    .text {
        font-weight: 700;
        overflow: hidden;
        font-size: 14px;
        padding: 0 5px;
        box-sizing: border-box;
        line-height: 30px;

        .label {
            color: #929292;
        }

        .left {
            //float: left;
            line-height: 40px;
            overflow: hidden;
            border-bottom: 1px solid #eee;

            .iconfont {
                color: #0b7ffe;
                margin-right: 3px;
                font-size: 14px;
            }
            .time {
                float: right;
                font-weight: 400;
            }
        }

        .right {
            //float: right;
            border-bottom: 1px solid #eee;
            line-height: 24px;
            padding: 10px 0;
            color: #0b7fff;
            .label {
                color: #929292;

            }
            .iconfont {
                margin-right: 3px;
                font-size: 15px;

            }
        }


    }

    .tags {
        display: flex;
        margin-top: 15px;

        .tag-item {
            flex: 1;
            border-radius: 6px;
            margin: 5px 10px;
            line-height: 28px;
            text-align: center;
            background: #83beff;
            padding: 5px;
            box-sizing: border-box;
            font-size: 12px;
            color: #fff;

            .value {
                font-size: 22px;
            }
        }
    }
}

.all-num {
    font-size: 14px;
    font-weight: 700;
    line-height: 40px;
    position: fixed;
    top: 174px;
    left: 20px;
    z-index: 33;
}
.all-num2 {
    font-size: 14px;
    font-weight: 700;
    line-height: 40px;
    position: fixed;
    top: 205px;
    left: 20px;
    z-index: 33;
}
.right-tbl-wrapper {
    overflow: auto;
    width: calc(100vw - 30px);
    overflow-x: hidden;
    display: flex;
    position: absolute;
    background-color:#fff;











    .right-tbl {
        width: 100vw;
        overflow: auto;
    }
    .right-tbl,.left-tbl{
        display: inline-block;
        margin-bottom: 109px;

        .first-tr {
            height: 33px;
            line-height: 32px;
            font-size: 12px;
            //background:rgba(255,246,229,1);
            //box-shadow:5px 2px 6px 0px rgba(228,202,164,0.5);
            color: #B26A20;
            .f-th {
                padding: 0 6px;
                white-space: nowrap;
                box-sizing: border-box;
                border: 1px solid #eee;
                vertical-align: middle;
                text-align: center;
                font-weight: 400;
            }
        }
        .other-tr {
            height: 30px;
            line-height: 30px;
            font-size: 12px;
            color: #232323;
            text-align: center;
            border-bottom: 1px solid #eee;
            .other-td {
                padding: 0 6px;
                white-space: nowrap;
                border-right: 1px solid #eee;
            }
            .other-td.active {
                color: #1681FB;
            }
        }
    }

    .left-tbl .other-tr:nth-child(2n) ,.right-tbl .other-tr:nth-child(2n) {
        background-color: #eee;
    }
    .color-table {
        font-weight: 700;
        .other-td {
            color: #000 !important;

        }
    }
}

.hp-btn {
    font-weight: 400;
    margin-left: 20px;
    width: 62px;
    line-height: 20px;
    border: 1px solid #0b7fff;
    font-size: 12px;
    display: inline-block;
    text-align: center;
}
.right-tbl-wrapper2 {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    height: 100%;
    -webkit-transform: rotate(270deg);
    transform: rotate(90deg);
    z-index: 999;
    position: absolute;
    /* padding: 30px; */
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    /* width: 100vh; */
    /* height: 100vw; */
    top: 110px;
    left: -113px;
    /* right: 0; */
    /* right: -193px; */
    /* left: 0; */
    /* bottom: 0; */
    height: calc(100vw - 18px);
    width: calc(100vh - 311px);
    overflow: auto;
    .left-tbl {
        overflow: auto;
        /* padding: 30px; */
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        margin-bottom: 0;

    }
    .first-tr ,.other-tr{
    }
    .other-tr {
        max-width: 200px;
        overflow: auto;
        display: block;
        text-align: center;
    }
}
.search-btns {
    font-size: 12px;
    line-height: 36px;
    padding-left: 14px;
    margin-top: 5px;
    .title {
        font-size: 12px;

    }
    .item {
        display: inline-block;
        width: 25%;
        color: #007AFF;
        .xiala {
            font-size: 10px;
            margin-left: 5px;
        }
    }
    .btns {
        display: inline-block;
        margin-left: 10px;
        span {
            font-size: 10px;
            border-radius: 4px;
            padding: 2px 8px;
            margin: 0 5px;
            display: inline-block;
            background: #e3e0e0;
            line-height: 14px;
        }
        .active {
            background: #0b7fff;
            color: #fff;
        }
    }
}
//.left-tbl .other-tr {
//    max-width: 200px;
//    overflow: auto;
//    display: block;
//    text-align: center;
//    td {
//        width: calc(100% - 14px);
//        display: inline-block;
//    }
//}
.tags-box {
    margin: 10px 0;
    .tags {
        background: #fff;
        line-height: 12px;
        font-size: 10px;
        padding: 2px 7px;
        border: 1px solid #717171;
        border-radius: 3px;
        color: #717171;
        display: inline-block;
    }
    .tags2 {
        border:1px solid #0b7fff;
        color: #0b7fff;
    }
    .tags3 {
        border:1px solid red;
        color: red;
    }
}
.top-box {
    display: inline-block;
    div {
        display: inline-block;
        font-weight: 400;
        font-size: 12px;
        margin-left: 5px;
        .iconfont {
            font-weight: 400;
            font-size: 12px !important;
        }
    }
    .go-top {
        color: #007AFF;
    }
    .end-top {
        color: #FD9936;
    }
}
</style>

