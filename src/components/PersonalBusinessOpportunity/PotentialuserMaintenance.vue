<template>
    <div class='qz-wrap'>
        <Header tsTitleTxt="商机信息录入" backType="custom" v-show='editType == "add"'  @emGoPrev="goPrev"/>
        <Header tsTitleTxt="商机信息维护" backType="custom" v-show='editType == "change"'  @emGoPrev="goPrev"/>
        <Header tsTitleTxt="商机信息补充" backType="custom" v-show='editType == "supplement"'  @emGoPrev="goPrev"/>
        <Header tsTitleTxt="商机信息查看" backType="custom" v-show='editType == "view"'  @emGoPrev="goPrev"/>
        <div class='content'>
            <div class='content-item content-item2' v-show='editType2'>
                <div class='reason'>
                    <span>审核不通过原因：</span>{{busiInputReviewOpinion}}
                </div>
            </div>

            <div class='content-item'>

                <div class='first-title'>
                    <div class='left-info left-info2'>
                        部分信息可基于掌厅图片进行AI识别填充<span class='iconfont wenhao' @click='showinfo = !showinfo'></span>
                        <div class='dialog-info' v-show='showinfo'  @click='showinfo = !showinfo'>
                            <div>请上传含以下信息的用户掌厅图片，系统将进行AI识别并自动填充到相关字段中，最多3张图片：</div>
                            <div >1、含用户号码、用户姓氏信息的掌厅图片</div>
                            <div>2、含用户宽带地址信息的掌厅图片</div>
                            <div>3、含上月账单的应收费用的掌厅图片</div>
                        </div>
                    </div>
                </div>
                <div class='more-line'>
                    <div class="fadd-info">
                        <ul class="family-add">
                            <li class="add-item" >
                                <span class="add-left">掌厅图片</span>
                                <span v-show='showbtn' @click='showbtn && getvideo()' class="add-input iconfont xiangji hasvideo" />

                            </li>
                            <div class='img-box' v-for='(item,idx) in thebase64' v-show='item && item.indexOf("blob") < 0'>
                                <span v-show='showbtn' class='iconfont guanbi2' @click='delThisImg(idx)'></span>
                                <img :src="'data:image/jpeg;base64,'+ item" @click='openImg(item)'/>

                            </div>
                            <div class='img-box' v-for='(item,idx) in thebase64' v-show='item && item.indexOf("blob") >= 0'>
                                <span v-show='showbtn' class='iconfont guanbi2' @click='delThisImg(idx)'></span>
                                <img :src="item" @click='openImg(item)'/>
                            </div>

                            <div class='big-img' v-if='showIMG'>
                                <span class='iconfont guanbi2' @click='showIMG = null'></span>
                                <img  v-if='showIMG && showIMG.indexOf("blob") < 0' :src="'data:image/jpeg;base64,'+ showIMG" />
                                <img  v-if='showIMG && showIMG.indexOf("blob") >= 0' :src="showIMG" />
                            </div>
                        </ul>
                    </div>
                </div>
            </div>

            <div class='content-item'>
                <div class='first-title'>
                    <div class='left-info'>
                        <img src="../../assets/img/sjlr3.png"/>
                        基础信息
                    </div>
                </div>
                <div class='more-line'>
                    <div class="fadd-info">
                        <ul class="family-add">
                            <li class="add-item " :class='canTel? "add-itemtel" : ""' >
                                <span class="add-left">用户号码<span style='color: red'>*</span></span>
                                <input type="tel"  v-show='!buopId'  :maxlength="11" v-model="telNum" @input="inputNum" placeholder="请输入" class="add-input"  :style="telNum && telNum.length > 10 ?  editType == 'add'? 'padding-right: 0px' : 'padding-right: 18px' : ''"/>
                                <span class='phone1' :style="telNum && telNum.length > 10 ?  editType == 'add'? 'padding-right: 0' : 'padding-right: 0px' : ''" v-show='buopId'>{{maskphone(telNum)}}</span>
                                <span class='mini' v-show='telNum && telNum.length >= 11'> <a class='dianhuaa'  v-show='+canTel' :href="'tel:'+telNum"><span class='iconfont dianhua'></span></a></span>
                                <span class='tip' v-show=' mItem.oregionId == 1 && telNum && telNum.length == 11 && cntsub && !hasNoOver'>存量号码，请重新输入</span>
                                <span class='tip' v-show=' telNum.length >= 11 && hasNoOver'>{{chongfu}}</span>
                                <span class='tip' v-show=' telNum.length >= 11 && !isPhone'>请输入正确的手机号</span>
                            </li>
                            <li class="add-item">
                                <span class="add-left">用户姓名<span style='color: red'>*</span></span>
                                <input v-model="busiUserName" :disabled='!showbtn' placeholder="可仅输入姓" class="add-input" maxlength="10"/>
                            </li>
                            <li class="add-item" v-show='showbtn'>
                                <span class="add-left">区县乡镇<span style='color: red'>*</span></span>
                                <span class="add-right" @click='(showbtn && changeCounty())'><span class='name'>{{ custName.townName? custName.townName: '请选择'}}</span><i  v-if='editType == "add" || editType == "change" || editType == "supplement"' class="iconfont jiantou-copy-copy"></i></span>
                            </li>
                            <li class="add-item" v-show='!showbtn'>
                                <span class="add-left">区县乡镇<span style='color: red'>*</span></span>
                                <span class="add-right" ><span class='name'>{{ custName.townName? custName.townName: custName.countryName ?custName.countryName: '请选择' }}</span><i  v-if='editType == "add" || editType == "change" || editType == "supplement"' class="iconfont jiantou-copy-copy"></i></span>
                            </li>
                            <li class="add-item">
                                <span class="add-left">详细地址<span v-show='showbtn' class='small-font' >(建议填写单位集团和小区信息)</span></span>
                                <input v-model="villageName" placeholder="请输入" :disabled='!showbtn' class="add-input" maxlength="50"/>
                            </li>
                            <li class="add-item" v-show='!cntsub  && showbtn && showSms() && editObject.messageVerify != "true"'>
                                <span class="add-left">短信验证码<span v-show='potentialuser_maintenance_flag' style='color: red'>*</span></span>
                                <div>
                                    <input v-model="yzmCode" style='max-width: 144px;' placeholder="输入接收到的验证码" class="add-input" maxlength="4"/>
                                    <div class="add-right2">
                                        <span class="shibie" :class='timer? "grey-btn" : "" ' :disabled="countdown > 0" @click="startCountdown()">  {{ countdown > 0 ? `${countdown} 秒后重试` : '发送验证码' }}</span>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class='hide-box' v-show='changeCountyFlg' @click='cancelCounty'></div>
            <div class="task-search-box wrapper-medias" v-show='changeCountyFlg'>
                <div class="top">
                    <div class="lie">
                        <div class="choose-item" v-for='item in countyObjListNew' :class='chooseQX1 == item.countryId ? "active" : ""' @click='chooseCountryName(item)'>
                            <span class="font-wenzi">{{item.countryName }}</span>
                            <span class="iconfont youjiantou1" ></span>
                        </div>
                    </div>
                    <div class="lie" >
                        <div class="choose-item" v-for='item in countyObjListNew2' :class='chooseQX3 == item.townId ? "active" : ""' @click='chooseCountryName2(item)'>
                            <span class="font-wenzi">{{item.townName}}</span>
                        </div>
                    </div>
                </div>
                <div class="bottom">
                    <div class="chongzhi" @click='cancelCounty'>取 消</div>
                    <div class="queren" @click='sureCounty'>确 认</div>
                </div>
            </div>
            <div class='content-item'>
                <div class='first-title'>
                    <div class='left-info'>
                        <img src="../../assets/img/sjlr3.png"/>
                        其他信息
                    </div>
                    <div class='right-info' @click='showMoreContent = !showMoreContent'>
                        <span class='iconfont jiantou2' v-show='showMoreContent'></span>
                        <span class='iconfont jiantouxiangshang'  v-show='!showMoreContent'></span>
                    </div>
                </div>
                <div class='more-line' v-show='!showMoreContent'>
                    <div class="fadd-info">
                        <ul class="family-add">
                            <li class="add-item" >
                                <span class="add-left">用户需求描述<span v-show='showbtn' class='small-font2' >(建议填写)</span></span>
                                <div>
                                    <div class="add-right2" v-show='showbtn'>
                                        <span  @touchstart='handlerTouchstart' @touchend='handlerTouchend' class='iconfont luyin'>语音录入</span>
                                    </div>
                                </div>
                            </li>
                            <div class='reserve3-box'>
                                <div v-show='showbtn != 0'>
                                    <textarea v-model="reserve3" :disabled='!showbtn' maxlength='500' placeholder="例:用户是xx集团用户，想办移动吉祥号码，月消费100元左右，需要宽带..." class="pop-textarea" />
                                    <span class='num'> {{reserve3.length}}/500</span>
                                </div>
                                <div v-show='showbtn == 0 && reserve3'>
                                    <span class='info'> {{ reserve3 }}</span>
                                </div>
                            </div>

                            <li class="add-item" v-show='intentionTel'>
                                <span class="add-left">意向号码</span>
                                <input v-model="intentionTel" placeholder="请输入" :disabled='true' class="add-input" maxlength="50"/>
                            </li>


                            <li class="add-item">
                                <span class="add-left">宽带安装地址</span>
                                <input v-model="bandInstall" placeholder="请输入" :disabled='!showbtn' class="add-input" maxlength="50"/>
                            </li>
                            <li class="add-item">
                                <span class="add-left">月消费</span>
                                <input v-model="monthConsum" type='text' placeholder="请输入" :disabled='!showbtn' class="add-input" maxlength="20"/>
                            </li>
                            <li class="add-item" >
                                <span class="add-left">身份证号</span>
                                <div>
                                    <input v-model="busiCertId" @input='inputSfz' style='width: 144px' placeholder="请输入" :disabled='!showbtn' class="add-input" maxlength="18"/>
                                    <div class="add-right2" v-show='showbtn != 0'>
                                        <span class="shibie"  @click="updateImg2()">OCR识别 <i class="iconfont zu"></i></span>
                                    </div>
                                </div>
                            </li>
                            <li class="add-item">
                                <span class="add-left">客户来源</span>
                                <span class="add-right" @click=" showbtn &&changeLY('2')">{{ laiyuan? laiyuan : '请选择' }}<i class="iconfont jiantou-copy-copy" v-show='showbtn != 0'></i></span>
                            </li>
                            <li class="add-item" >
                                <span class="add-left">是否有合约捆绑</span>
                                <span class="add-right" v-show='!showbtn'>{{ isContBund?'是': '否' }}</span>
                                <mt-switch v-show='showbtn' v-model="isContBund" :disabled='!showbtn'></mt-switch>
                            </li>
                            <li class="add-item" >
                                <span class="add-left">合约到期时间</span>
                                <span class="add-right" @click=" showbtn &&changeTime()">{{ busiExpireDate?busiExpireDate: '请选择' }}<i v-show='showbtn != 0' class="iconfont jiantou-copy-copy"></i></span>
                            </li>
                            <li class="add-item" >
                                <span class="add-left">预约服务时间</span>
                                <span class="add-right" @click=" showbtn &&changeTime2()">{{ serviceDate?serviceDate: '请选择' }}<i v-show='showbtn != 0' class="iconfont jiantou-copy-copy"></i></span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class='content-item'  v-show='isDperson.indexOf("是")>-1 ||provNewIcCnt.indexOf("是")>-1 || lowConsume.indexOf("是")>-1 || huawuList.length > 0 || hisBuopList.length > 0'>
                <div class='first-title'>
                    <div class='left-info'>
                        <img src="../../assets/img/sjlr3.png"/>
                        系统匹配信息
                    </div>
                    <div class='right-info'></div>
                </div>
                <div class='more-line' >
                    <div class='fadd-info'>
                        <div class='user-tag' >
                            <li class="add-item" v-show='isDperson.indexOf("是")>-1 ||provNewIcCnt.indexOf("是")>-1 || lowConsume.indexOf("是")>-1'>
                                <span class="add-left">用户标签信息</span>
                                <div class='tasg'>
                                    <div class='tag-item' v-show="provNewIcCnt.indexOf('是')>-1">纯新增</div>
                                    <div class='tag-item' v-show="isDperson.indexOf('是')>-1">D类用户</div>
                                    <div class='tag-item' v-show="lowConsume.indexOf('是')>-1">低消</div>
                                </div>
                            </li>
                            <div class='title1'  v-if='huawuList.length > 0'>互通话务信息</div>
                            <div class='huaw' v-if='huawuList.length > 0'>
                                <div class='item' v-for='item in huawuList'>
                                    <div class='value'>{{ getStr(item.statMonth) }}</div>
                                    <div class='title' v-show='item.voiceDuration'>{{ item.voiceDuration }}分钟</div>
                                    <div class='title' v-show='!item.voiceDuration'>--</div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>


            </div>
            <div class='content-item' v-show='list1.length > 0 || list2.length > 0 '>
                <div class='first-title'>
                    <div class='left-info'>
                        <img src="../../assets/img/sjlr3.png"/>
                        轨迹信息
                    </div>
                    <div class='right-info' @click='goHisMore(1)' v-show='list1.length > 1 || list2.length > 1 '>查看更多 <span class='iconfont youjiantou2'></span></div>

                </div>
                <div class='guiji-box'>
                    <div class='guiji-item' v-show='list1.length > 0'>
                        <div class='guiji-title'>
                            <div class='left'>当前商机跟进记录</div>
                        </div>
                        <div class='guiji-content'  v-for="(item,index) in list1" v-show='index == 0' :key="index">
                            <div class='first-time'>{{changeTimeNew(item.createDate) }} ,{{item.createOper }} <span v-show='item.createTel'>({{item.createTel }})</span></div>
                            <div class='other-info' v-show="item.operateType">
                                <div class='tip-title'>操作类型：</div>
                                <div class='tip-value'>{{item.operateType | setStatus}}</div>
                            </div>
                            <div class='other-info' v-show="item.operateNewStatus">
                                <div class='tip-title'>商机状态：</div>
                                <div class='tip-value'>{{item.operateNewStatus | sjStatus}}</div>
                            </div>
                            <div class='other-info' v-show='item.operateType == 17' >
                                <div class='tip-title'>稽核结果：</div>
                                <div class='tip-value'>{{item.examineStatus}}</div>
                            </div>
                            <div class='other-info' v-show="item.operateType == 2">
                                <div class='tip-title tip-title3'>转派接收人：</div>
                                <div class='tip-value tip-value2'>{{item.folloeNewBy}}({{item.folloeNewTel}})</div>
                            </div>
                            <div class='other-info' v-show="item.operateType == 7">
                                <div class='tip-title tip-title3'>指派接收人：</div>
                                <div class='tip-value tip-value2'>{{item.folloeNewBy}}({{item.folloeNewTel}})</div>
                            </div>
                            <div class='other-info' v-show=' item.operateType == 11 && item.followResult && !item.failReason' >
                                <div class='tip-title'>结单信息：</div>
                                <div class='tip-value'>{{item.followResult | followResult}} <span v-show='item.newCardTel'>,{{item.newCardTel}}</span></div>
                            </div>

                            <div class='other-info' v-show="item.accountDesc ">
                                <div class='tip-title'>结单描述：</div>
                                <div class='tip-value'>{{item.accountDesc }} - 成功办理笔数: {{item.successAccountSum}}</div>
                            </div>
                        </div>
                    </div>
                    <div class='guiji-item' v-show='list2.length > 0'>
                        <div class='guiji-title'>
                            <div class='left'>商机历史轨迹</div>
                        </div>
                        <div class='guiji-content' v-for="(item,index) in list2" v-show='index == 0'  :key="index">
                            <div class='first-time'>{{item.accountDate}} ,{{item.buopStatus | sjStatus}}</div>


                            <div class='other-info' >
                                <div class='tip-title'>创&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;建：</div>
                                <div class='tip-value '>{{item.createOper}} ({{item.createTel}})  {{ item.createDate}}</div>
                            </div>
                            <div class='other-info' v-show="item.followBy">
                                <div class='tip-title'>最终跟进：</div>
                                <div class='tip-value'>{{ item.followBy }} <span v-show='item.followTel'>({{ item.followTel }})</span> , {{ item.accountDate }} {{ item.buopStatus == '6' ? '反馈结单' : '' }}</div>
                            </div>
                            <div class='other-info' v-show="item.accountType">
                                <div class='tip-title'>结单类型：</div>
                                <div class='tip-value'>{{item.accountType}}</div>
                            </div>
                            <div class='other-info' v-show="item.failReason">
                                <div class='tip-title'>失败原因：</div>
                                <div class='tip-value'>{{item.failReason}} <span v-show='item.failReason == "其他"'>:</span>{{item.failReasonDesc}}</div>
                            </div>
                            <div class='other-info' v-show='item.followResult'>
                                <div class='tip-title'>跟进结果：</div>
                                <div class='tip-value'>{{getfollowResult(item.followResult)}} {{item.newCardTel }}</div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>

        </div>
        <personchoose ref="executeRef"  v-if='orgId && showExecute' :objParam='objParam' @emCloseInit="closeExecute" @emSubmit="getExecute" :orgaId="orgId" :headerName="'选择服务专家'" :showExecute="1"/>

        <div class="op-button-box" v-show='showbtn && editType2 != 1'>
            <button class="op-button" @click="submitJudge('13')" v-show='editType == "supplement"|| editType == "add"'>保存</button>
            <button class="op-button " :class="cntsub || hasNoOver? '' : 'active'" @click="submitJudge(99)">提 交</button>
        </div>
        <div class="op-button-box" v-show='showbtn && editType2 == 1'>
            <button class="op-button " :class="cntsub || hasNoOver? '' : 'active'" @click="submitJudge(99)">提 交</button>
        </div>
        <div class="dialog-box">
            <img src="../../assets/img/chatloading.gif"/>
            <div class="recwave-box1">正在录音识别中...</div>
        </div>

        <div class='dislog' v-show='showChooseReason'>
            <div class='title'>商机提交成功！请选择该商机的跟进方式:</div>
            <div class='reson-box'>
                <div class='chooseone' @click='chooseResonNum = 1'><span class='iconfont duihao1' v-show='chooseResonNum == 1'></span> <div class='yuan'></div>自己跟进处理</div>
                <div class='chooseone' @click='chooseResonNum = 2'><span class='iconfont duihao1' v-show='chooseResonNum == 2'></span> <div class='yuan'></div>转专家支撑</div>
            </div>
            <div class='reson-box2' >
<!--                <div class='chooseone' @click='showChooseReason = false'>取消</div>-->
                <div class='chooseone' @click='folltypeSub'>确定</div>
            </div>
        </div>
        <div class='hide-box' v-show='showChooseReason'></div>
    </div>
</template>

<script>
import Header from 'components/common/Header.vue'
import Storage from '@/base/storage'
import NlDropdown from 'components/common/NlDropdown/dropdownForBusinessOpportunity.js'
import RecorderManger from '@/base/chatUtil/RecorderManager.js'
import ClientJs from '@/base/clientjs'
import {dateFormat,chgStrToDate} from '@/base/utils'
import NlDatePicker from "components/common/NlDatePick2/datePicker.js";
import FamilyVideo from './FamilyVideo'
import ImageObj from '@/base/request/ImageServReq';
import personchoose from './personchoose.vue'

export default {
    name: 'weihu',
    components: { personchoose, FamilyVideo, Header},
    data() {
        return {
            // 路由相关信息
            orgId: '', // 执行人归属id
            countdown: 0, // 验证码倒计时
            timer: null, // 验证码定时器
            homeId: '', // 家庭id
            userInfo: {}, // 用户基本信息
            countyObjList: [], // 归属列表
            changeCountyFlg: false,
            countyObjListNew: [], // 归属列表
            countyObjListNew2: [], // 归属列表
            chooseQX1: '', // 归属列表
            chooseQX11: '', // 归属列表
            chooseQX2: '', // 归属列表
            chooseQX22: '', // 归属列表
            chooseQX3: '', // 归属列表
            chooseQX33: '', // 归属列表
            savetype: '', // 点击保存/提交
            editType: '',// 访问页面类型 supplement补充 / view查看 / change维护 / add新增
            editType2: '',//审核待补充专用判断标识
            objParam: {},
            sjlist: [],
            busiInputReviewOpinion: '', // 审核原因
            intentionTel: '', // 意向号码


            // 控制开关
            showinfo: false, // AI识别填充提示窗开关
            showChooseReason: false, // 选择该商机的跟进方式弹窗
            chooseResonNum: '1', // 跟进方式弹窗选择
            showExecute: false, // 人员选择弹窗
            showIMG: false, // 展示大图
            cntsub: false, // 按钮提交控制
            isPhone: true, // 是否正确手机号
            showbtn: true, // 是否展示提交按钮
            hasNoOver: false, // 是否有未完结
            showMoreContent: false, // 其他信息展开收起
            potentialuser_maintenance_flag: true, // 验证码开关
            canTel: false, // 是否可以拨号



            // 商机明细信息
            buopId: '', // 商机id
            telNum: '', // 号码
            yzmCode: '', // 商机验证码
            serviceDate: '', // 服务时间
            busiUserName: '', // 用户姓名
            isContBund: false, // 合约捆绑
            mItem: { oregionId: '', operaId: '' }, // 归属
            thisvideo: [], // 掌厅图片
            thebase64: [], // base64的掌厅图片
            laiyuan: '', // 来源
            villageName: '', // 详细地址
            belongRegion: {}, // 归属地市
            custName: {}, // 区县乡镇
            busiCertId: '', // 身份证
            busiExpireDate: '', // 合约到期时间
            reserve3: '', // 用户需求描述
            bandInstall: '', // 宽带安装地址
            monthConsum: '', // 月消费
            editObject: {}, // 查询的商机信息


            // 录
            rec: '',
            recwave: { input: function() {} },


            //展示信息-其他信息
            provNewIcCnt: '', // 纯新增 值:是/否
            lowConsume: '', // 低消 值:是/否
            isDperson: '', // D类 值:是/否
            hisBuopList: [], // 历史商机列表
            huawuList: [], // 互通话务信息

            regionArr: [
                {"region":'11',"longRegion":"1000512"}, //苏州
                {"region":'12',"longRegion":"1000517"}, //淮安
                {"region":'13',"longRegion":"1000527"}, //宿迁
                {"region":'14',"longRegion":"1000250"}, //南京
                {"region":'15',"longRegion":"1000518"}, //连云港
                {"region":'16',"longRegion":"1000516"}, //徐州
                {"region":'17',"longRegion":"1000519"}, //常州
                {"region":'18',"longRegion":"1000511"}, //镇江
                {"region":'19',"longRegion":"1000510"}, //无锡
                {"region":'20',"longRegion":"1000513"}, //南通
                {"region":'21',"longRegion":"1000523"}, //泰州
                {"region":'22',"longRegion":"1000515"}, //盐城
                {"region":'23',"longRegion":"1000514"}, //扬州
                {"region":'99',"longRegion":"1000250"} //江苏省-取南京
            ],
            list1: [], // 商机跟进记录
            list2: [], // 商机历史轨迹
            chongfu: ''
        }
    },
    filters: {
        starTel(val) {
            if (!val) {
                return '***'
            } else {
                let reg = /^(\d{3})\d*(\d{4})$/
                return val.replace(reg, '$1****$2')
            }
        },
        timeFiler(val){
            if(val){
                return dateFormat(chgStrToDate(val), "yyyy-MM-dd hh:mm:ss");
            }else {
                return '';
            }
        },
        followResult(val){
            if (val == 1) {
                return '新办卡'
            }
            if (val == 2) {
                return '归位'
            }
            if (val == 3) {
                return '低消迎回'
            }
            if (val == 4) {
                return '失败结单'
            }
            if (val == 5) {
                return '其他'
            }
        },
        setStatus(val){
            if (val == 1) {
                return '商机创建'
            }
            if (val == 2) {
                return '商机转派'
            }
            if (val == 3) {
                return '商机释放'
            }
            if (val == 4) {
                return '商机回收'
            }
            if (val == 5) {
                return '商机接收'
            }
            if (val == 6) {
                return '商机拒绝'
            }
            if (val == 7) {
                return '商机指派'
            }
            if (val == 8) {
                return '商机认领'
            }
            if (val == 9) {
                return '商机维护'
            }
            if (val == 10) {
                return '去算帐'
            }
            if (val == 11) {
                return '商机结单'
            }
            if (val == 12) {
                return '算帐单解绑'
            }
            if (val == 13) {
                return '商机保存'
            }
            if (val == 14) {
                return 'API同步-新增'
            }
            if (val == 15) {
                return 'API同步-修改'
            }
            if (val == 16) {
                return 'API同步-册除"'
            }
            if (val == 17) {
                return '人工稽核结果接收'
            }
            if (val == 18) {
                return '商机挂起'
            }
            if (val == 19) {
                return '商机调度'
            }
            if (val == 20) {
                return '地市管理员删除'
            }
            if (val == 21) {
                return '商机单录入审核结果接收'
            }
            if (val == 22) {
                return '商机补充提交'
            }
        },
        sjStatus(val) {
            if (val == 2) {
                return '跟进中'
            }
            if (val == 4) {
                return '转派中'
            }
            if (val == 5) {
                return '待接单'
            }
            if (val == 6) {
                return '已结单'
            }
            if (val == 7) {
                return '待补充'
            }
            if (val == 8) {
                return '待指派'
            }
            if (val == 9) {
                return '超时结单'
            }
            if (val == 10) {
                return '待人工稽核'
            }
            if (val == 11) {
                return '人工稽核失败'
            }
            if (val == 12) {
                return '成功结单'
            }
            if (val == 13) {
                return '失败结单'
            }
            if (val == 14) {
                return '同步结单'
            }
            if (val == 15) {
                return '审核待补充'
            }
            if (val == 16) {
                return '补单待审核'
            }
        }
    },
    methods: {
        getStreetList(id){
            if (id == 99) {
                id = 14
            }
            let regId = ''
            for (let i = 0 ;i < this.regionArr.length;i++){
                if (this.regionArr[i].region == id){
                    regId = this.regionArr[i].longRegion
                }
            }
            let url = '/xsb/gridCenter/familyDiagnosis/h5QryCountryTownList'
            let params = {
                "areaId": regId
            }
            this.$http.post(url, params).then((res) => {
                console.info('1-param',params)
                console.info('1-res',res)
                if (res.data.retCode == 0) {
                    this.countyObjListNew = res.data.data
                } else {
                    this.countyObjListNew = []
                }

            })
        },
        chooseCountryName(item){
            this.countyObjListNew2 = {}
            this.chooseQX1 = item.countryId
            this.chooseQX11 = item.countryName
            this.chooseQX2 = ''
            this.chooseQX3 = ''
            for (let i = 0 ; i < this.countyObjListNew.length;i++) {
                if (item.countryId == this.countyObjListNew[i].countryId) {
                    this.countyObjListNew2 = this.countyObjListNew[i].townList
                }
            }
            console.log('----------',item)
            console.log('----chooseQX1------',this.chooseQX1)
        },
        chooseCountryName2(item){
            this.chooseQX2 = item.gridId
            this.chooseQX22 = item.gridName
            this.chooseQX3 = item.townId
            this.chooseQX33 = item.townName
        },
        cancelCounty(){
            this.changeCountyFlg = false
            this.chooseQX1 = ''
            this.chooseQX11 = ''
            this.chooseQX2 = ''
            this.chooseQX22 = ''
            this.chooseQX3 = ''
            this.chooseQX33 = ''
            this.countyObjListNew2=[]
        },
        sureCounty(){
            if (!this.chooseQX3) {
                this.$toast('请选择区县乡镇')
                return
            }
            console.log('----chooseQX1--chooseQX1----',this.chooseQX1)

            this.custName = {
                id: this.chooseQX1, // 区县-id
                label:  this.chooseQX11,//  区县-名

                townId:  this.chooseQX3,// 街道-id
                townName: this.chooseQX33, // 街道-名

                countryId: this.chooseQX1, // 区县-id
                countryName: this.chooseQX11,// 区县-名

                gridId: this.chooseQX2, // 网格-id
                gridName: this.chooseQX22, // 网格-名
            }
            this.changeCountyFlg = false
            console.log('this.custName',this.custName)
        },
        goHisMore(val){
            this.$router.push({
                path: '/hisInfoList',
                query: {
                    tab: val ,
                    type: 1,
                    telNum: this.telNum,
                    buopId: this.buopId
                }
            })
        },
        changeTimeNew(val){
            let newStr = val.slice(0, -2)
            return newStr
        },
        // 跟进结果
        getfollowResult(val) {
            if (val == '1') {
                return '新办卡'
            }else if (val == '2') {
                return '归位'
            }else if (val == '3') {
                return '低消迎回'
            }else if (val == '4') {
                return '失败结单'
            }else if (val == '5') {
                return '其他'
            }else{
                return val
            }
        },
        // 历史商机列表
        getGJList(){
            if (!this.buopId) return
            let url = '/xsb/gridCenter/familyDiagnosis/h5QryBusiOppoTrajectory'
            let params = {
                "buopId": this.buopId,  //输入的商机号码
                "busiType": 1
            }
            this.$http.post(url, params).then((res) => {
                console.info('1-param',params)
                console.info('1-res',res)
                if (res.data.retCode == '0') {
                    this.list1 = res.data.data

                } else {
                    this.list1 = res.data.data
                }
            })
        },
        // 历史商机列表
        gethisBuopList(){
            if(!this.telNum) return
            let url = '/xsb/gridCenter/familyDiagnosis/h5QueryHistoryBusi'
            let params = {
                "busiTel": this.telNum,  //输入的商机号码
                "busiType":1,
                "buopId": this.buopId,  //输入的商机号码

            }
            this.$http.post(url, params).then((res) => {
                console.info('2-param',params)
                console.info('2-res',res)
                if (res.data.retCode == '0') {
                    this.list2 = res.data.data

                } else {
                    this.list2 = res.data.data
                }
            })
        },
        // 指派
        closeExecute(){
            this.showExecute = false
            this.showExecute2 = false
        },
        // 指派
        getExecute(item2){
            // 指派
            this.chooseitem1Person = item2
            this.showExecute = false
            let url2 = '/xsb/gridCenter/familyDiagnosis/h5UpdateBusiStatus'
            let param2 = {
                'transferSource': 1,
                'busiType': 1,
                'countyId': this.custName.id,
                "crmId": Storage.session.get('userInfo').crmId,
                "region":  Storage.session.get('userInfo').region,
                "buopId":this.buopId,  //商机id
                "busiTel": this.telNum, //商机号码
                "followOldBy": '',  //上一跟进人
                "followOldTel": '',//上一跟进人电话
                "followBy": Storage.session.get('userInfo').operatorName,//当前跟进人（当前操作人）
                "followTel": Storage.session.get('userInfo').servNumber,//当前跟进人电话（当前操作人电话）
                "buopStatus": item2 ? 2 : 5,//商机状态
                "busiReleaseReason":"", //商机释放原因
                "busiTransferReason":"",//商机转派原因
                "modifyOper":Storage.session.get('userInfo').operatorName,//修改人（当前操作人）
                "modifyTel":Storage.session.get('userInfo').servNumber,//修改人（当前操作人电话）
                "operateType":  item2 ? 2: 3,
                "transferPerson":item2 ? item2.execName: '',//转派或者指派人
                "transferTel": item2 ? item2.execMsisdn: ''//转派或者指派人电话

            }
            console.log('指派/释放',item2)
            console.log('指派/释放',param2)

            this.$http.post(url2, param2).then((res) => {
                let { retCode, retMsg } = res.data
                if (retCode == '0') {
                    this.$toast('操作成功')
                    setTimeout(() => {
                        this.goBack();
                    },1000)
                }else {
                    this.$alert(retMsg || `记录商机失败`)
                }
            })
        },
        // 跟进方式确定选择
        folltypeSub(){
            let arr = [
                {"region":'11',"longRegion":"1000512"}, //苏州
                {"region":'12',"longRegion":"1000517"}, //淮安
                {"region":'13',"longRegion":"1000527"}, //宿迁
                {"region":'14',"longRegion":"1000250"}, //南京
                {"region":'15',"longRegion":"1000518"}, //连云港
                {"region":'16',"longRegion":"1000516"}, //徐州
                {"region":'17',"longRegion":"1000519"}, //常州
                {"region":'18',"longRegion":"1000511"}, //镇江
                {"region":'19',"longRegion":"1000510"}, //无锡
                {"region":'20',"longRegion":"1000513"}, //南通
                {"region":'21',"longRegion":"1000523"}, //泰州
                {"region":'22',"longRegion":"1000515"}, //盐城
                {"region":'23',"longRegion":"1000514"}, //扬州
                {"region":'99',"longRegion":"2000250"} //江苏省
            ]
            for (let i = 0 ;i <arr.length;i++){
                if (arr[i].region== this.belongRegion.id){
                    if (arr[i].longRegion == '2000250') {
                        this.orgId = '1000250'
                    } else {
                        this.orgId = arr[i].longRegion
                    }
                }
            }
            if (this.chooseResonNum == 1) {
                // 自己跟进
                this.goBack();
            } else {
                this.objParam.countyId = this.custName.id
                this.objParam.region = this.belongRegion.id
                this.objParam.busiSource = this.editObject.busiSource ? this.editObject.busiSource : '1'
                this.showExecute = true
            }
        },
        // 展示短信验证码
        showSms(){
            if (this.telNum && this.telNum['0'] == 1 && this.telNum.length == 11) {
                return true
            }
            return false
        },
        // 验证码倒计时
        startCountdown() {
            if (this.timer) return;

            if (!this.telNum) {
                this.$toast('请输入用户号码')
                return
            }
            if (this.telNum['0'] == 1) {
                var regNum = /^[\d|\.]*$/
                if (!regNum.test(this.telNum)) {
                    this.$toast('请输入正确的用户号码')
                    return
                }
                if (this.telNum.length != 11) {
                    this.$toast('请输入正确的用户号码')
                    return
                }
                var regNum2 = /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/
                if (!regNum2.test(this.telNum)) {
                    this.$toast('请输入正确的用户号码')
                    return
                }
            }
            this.$messagebox({
                title: '温馨提示',
                showCancelButton: true,
                message: '确定给' + `<span style="color: #0b7ffe">${this.telNum}</span>` + '发送验证码短信吗？',
                confirmButtonText: '确定'
            }).then((ressend) => {
                if (ressend === 'confirm') {
                    // 这里可以添加点击按钮后需要执行的逻辑
                    this.sendSms()
                }
            })
        },
        // 发送验证码
        sendSms(){
            let url = '/xsb/gridCenter/sendCode/h5SendVerification'
            let param = {
                "crmId": Storage.session.get('userInfo').crmId,
                "region":  Storage.session.get('userInfo').region,
                "telnum": this.telNum
            }
            this.$http.post(url, param).then((res) => {
                console.log('h5SendVerification',res)
                if (res.data.retCode == 0) {
                    this.countdown = 60; // 设置倒计时时间，如60秒
                    this.timer = setInterval(() => {
                        if (this.countdown > 0) {
                            this.countdown -= 1;
                        } else {
                            clearInterval(this.timer);
                            this.timer = null;
                        }
                    }, 1000);
                    this.$toast('验证码发送成功')
                    return
                } else {
                    this.$alert(res.data.retMsg || '验证码发送失败');
                }
            }).catch((response) => {
                this.$alert(response || '验证码发送失败');
            })
        },
        // 验证码校验
        getSmsCheckVerification(save){
            if (!this.yzmCode) {
                this.$toast('请输入短信验证码')
                return
            }
            this.yzmCode = this.yzmCode + ''
            if (this.yzmCode.length != 4) {
                this.$toast('请输入4位的短信验证码')
                return
            }
            let url = '/xsb/gridCenter/sendCode/h5CheckVerification'
            let param = {
                "crmId": Storage.session.get('userInfo').crmId,
                "region":  Storage.session.get('userInfo').region,
                "sendCode": this.yzmCode,
                "telnum": this.telNum
            }
            this.$http.post(url, param).then((res) => {
                if (res.data.retCode == 0) {
                    this.submitFunction(1,save)
                } else {
                    this.$alert(res.data.retMsg || '验证码校验失败');
                }
            }).catch((response) => {
                this.$alert(response || '验证码校验失败');
            })
        },
        // 商机明细
        getBusinessDetail(){
            if (!this.buopId) return
            let param = {
                'crmId': Storage.session.get('userInfo').crmId,
                'region': Storage.session.get('userInfo').region,
                "buopId": this.$route.query.buopId,
                "busiTel": this.$route.query.busiTel,
            }

            this.homeId = this.$route.query.homeId
            this.$http.post('/xsb/gridCenter/familyDiagnosis/h5PersonBusiQuery', param).then((res) => {
                if (res.data.retCode == '0') {
                    this.editObject= res.data.data[0]
                    // 手机号
                    this.telNum = this.editObject.busiTel ? this.editObject.busiTel : ''
                    // 从路由取号码
                    if (!this.editObject.busiTel) {
                        this.telNum = this.$route.query.busiTel
                    }
                    // 服务日期
                    this.serviceDate = this.editObject.serviceDate? this.editObject.serviceDate : ''

                    // 号码校验
                    this.inputNum()

                    // 历史商机
                    this.gethisBuopList()

                    // 话务信息
                    this.getDandHuawu()

                    // 归属省内外+运营商
                    this.mItem.oregionId = this.editObject.userAffiliation ? this.editObject.userAffiliation : ''
                    this.mItem.operaId = this.editObject.busiOperatorName ? this.editObject.busiOperatorName : ''

                    // 姓名
                    this.busiUserName = this.editObject.busiUserName

                    // 来源
                    this.laiyuan = this.editObject.userSource

                    // 合约捆绑
                    if (this.editObject.isContBund == 'true' || this.editObject.isContBund == true) {
                        this.isContBund = true
                    } else {
                        this.isContBund = false
                    }

                    // 详细地址
                    this.villageName = this.editObject.villageName

                    // 归属区县
                    this.custName = {
                        label : this.editObject.custName,
                        id : this.editObject.countyId,
                        townId:  this.editObject.streetId,// 街道-id
                        townName: this.editObject.streetName, // 街道-名

                        countryId: this.editObject.countyId, // 区县-id
                        countryName: this.editObject.custName,// 区县-名

                        gridId: this.editObject.gridId, // 网格-id
                        gridName: this.editObject.gridName, // 网格-名
                    }
                    let arr = [
                        { 'region': '11', 'longRegion': '1000512','label': '苏州' }, //苏州
                        { 'region': '12', 'longRegion': '1000517','label': '淮安' }, //淮安
                        { 'region': '13', 'longRegion': '1000527','label': '宿迁' }, //宿迁
                        { 'region': '14', 'longRegion': '1000250','label': '南京' }, //南京
                        { 'region': '15', 'longRegion': '1000518','label': '连云港' }, //连云港
                        { 'region': '16', 'longRegion': '1000516','label': '徐州' }, //徐州
                        { 'region': '17', 'longRegion': '1000519','label': '常州' }, //常州
                        { 'region': '18', 'longRegion': '1000511','label': '镇江' }, //镇江
                        { 'region': '19', 'longRegion': '1000510','label': '无锡' }, //无锡
                        { 'region': '20', 'longRegion': '1000513','label': '南通' }, //南通
                        { 'region': '21', 'longRegion': '1000523','label': '泰州' }, //泰州
                        { 'region': '22', 'longRegion': '1000515','label': '盐城' }, //盐城
                        { 'region': '23', 'longRegion': '1000514','label': '扬州' }, //扬州
                    ]
                    for (let i = 0; i < arr.length; i++) {
                        if (arr[i].region == this.editObject.areaCode) {
                            // 归属区县
                            this.belongRegion = {
                                label : arr[i].label,
                                id : this.editObject.areaCode,
                            }
                            this.orgId= arr[i].longRegion
                        }
                    }
                    this.qryCountyList(this.belongRegion.id) // 地市列表
                    this.getStreetList(this.belongRegion.id) // 地市列表

                    // 合约到期时间
                    this.busiExpireDate = this.editObject.busiExpireDate

                    // 用户需求描述
                    this.reserve3 = this.editObject.userDemand ? this.editObject.userDemand : ''

                    // 宽带安装地址
                    this.bandInstall = this.editObject.bandInstall ? this.editObject.bandInstall : ''

                    // 月消费
                    this.monthConsum = this.editObject.monthConsum ? this.editObject.monthConsum : ''

                    // 预约服务时间
                    this.serviceDate = this.editObject.serviceDate ? this.editObject.serviceDate : ''


                    // 审核原因
                    this.busiInputReviewOpinion = this.editObject.busiInputReviewOpinion ? this.editObject.busiInputReviewOpinion : ''
                    // 意向号码
                    this.intentionTel = this.editObject.intentionTel ? this.editObject.intentionTel : ''

                    // 身份证
                    if(this.editObject.busiCertId) {
                        this.busiCertId = this.editObject.busiCertId
                        this.qrySubsCertIdNum(this.busiCertId)
                    }


                    // 图片

                    this.thisvideo = this.editObject.ztVideoUrl.split(',')
                    if (this.thisvideo) {
                        for (let i = 0 ; i < this.thisvideo.length;i++) {
                            ImageObj.getImgUrl(this.thisvideo[i], {}, '/xsb/personBusiness/queryCalculate/h5DownBillFollowUpPic').then(res => {
                                this.thebase64.push(res)
                            })
                        }

                    }

                    this.$forceUpdate()


                }
            })
        },
        // 大图
        openImg(item){
            this.showIMG = item
        },
        // 掌厅图片拍照
        getvideo(){
            ClientJs.openCameraAndShow("1","PalmHalCameral")
        },
        // 身份证OCR识别
        updateImg2() {
            if (!this.showbtn) return
            ClientJs.openCameraAndShow('1', 'sfzCanmera')
        },
        //主副号判断服务
        getJudgment(){
            if(!this.telNum && !this.busiTel) {
                return
            }
            let param = {
                "msisdn":this.telNum?this.telNum: this.$route.query.busiTel,
            };
            let url='/xsb/personBusiness/queryCalculate/h5QryJudgmentNum';
            this.$http.post(url,param).then(res => {
                let {retCode,data,retMsg} = res.data;
                if(retCode == '0') {
                    this.homeId = data.result.homeId;
                }else{
                }
            })
        },
        getImgInfo(base64) {
            let param = {
                'crmId': Storage.session.get('userInfo').crmId,
                'region': Storage.session.get('userInfo').region,
                'image_data': base64,
                'unEncrpt' : true

            }
            let url = `/xsb/chatTools/marEmpower/h5UniverOcrRecogn`
            this.$http.post(url, param).then((res) => {
                if (res.data.retCode == '0') {
                    let obj = {
                        voiceTxt: res.data.data['result_data'].join(',').replace(/,/g, "")
                    }
                    if (obj.voiceTxt) {
                        this.getRecordResultCbFn(obj)
                    } else {
                        this.$toast('OCR识别失败')

                    }
                }
            })
        },
        getImgInfo2(base64) {
            let param = {
                'crmId': Storage.session.get('userInfo').crmId,
                'region': Storage.session.get('userInfo').region,
                "imageBase64String": base64,
                "imagePage": "f",
                'unEncrpt' : true

            }
            let url = `/xsb/gridCenter/familyDiagnosis/h5Idcardocr`
            this.$http.post(url, param).then((res) => {
                if (res.data.retCode == '0') {
                    this.busiCertId = res.data.data.id_number
                    this.busiUserName = res.data.data.name

                    this.qrySubsCertIdNum(this.busiCertId)

                }
            })
        },
        handlerTouchstart(e) {
            if (!this.showbtn) return
            e.preventDefault()
            this.rec.startRecorder()
            console.log(1111111111111111)
            document.querySelector('.dialog-box').style.top = '42px'
        },
        handlerTouchend() {
            this.rec.endRecorder()
            document.querySelector('.dialog-box').style.top = '-300px'

        },
        inputSfz(){
            this.busiCertId = this.busiCertId+''
            if (this.busiCertId.length == 18) {
                console.info(this.validateIdCard(this.busiCertId))
                if (this.validateIdCard(this.busiCertId)) {
                    this.qrySubsCertIdNum(this.busiCertId)

                } else {
                    this.$toast('请输入正确的身份证号')
                }
            }
        },
        //录音回调
        getRecordResultCbFn(obj) {
            console.log('obj',obj)
            if (!obj.voiceTxt) return
            if (obj.code == '-1') {
                this.$toast(obj.voiceTxt)
                return
            }
            if (obj.voiceTxt && obj.voiceTxt.length > 500) {
                let result = obj.voiceTxt.substring(0, 100);
                this.reserve3 = result

            } else {
                this.reserve3 = obj.voiceTxt

            }

        },
        //判断解析，可以为小数2位，正整数
        validates(val, tips) {
            if(!val) return
            if (!/^((?:-?0)|(?:-?[1-9]\d{0,19}))(?:\.\d{1,2})?$/.test(val)) {
                this.$toast(`${tips}必须为长度不超过20的数字,最多包含2位小数`)
                return false
            }
            if (val.indexOf('-') > -1) {
                this.$toast(`${tips}必须为长度不超过20的数字,最多包含2位小数`)
                return false
            }
            return true
        },
        //判断为正整数，可以为0.但是不能以0开头
        valiNum(val, tips) {
            if (!/(^[1-9]([0-9]*)$|^[0-9]$)+/.test(val)) {
                this.$toast(`${tips}必须为正整数,长度不超过20位`)
                return false
            }
            return true
        },
        // 返回
        goPrev() {
            setTimeout(() => {
                const gobackFlag = this.$route.query.gobackFlag
                if(gobackFlag == 'webview'){
                    ClientJs.closeCallBack('FSOP');
                } else {
                    if (this.$route.query.qrCode == 1) {
                        this.$router.push({
                            path: '/aiMarketAssistant',
                            query: {
                                qrCodeVisible : true,
                            }
                        })
                    } else {
                        history.go(-1)
                    }
                }
            },500)

            if (this.rec) {
                this.rec && this.rec.recorder.close();
                this.rec = null
                this.luyinFlag = false
            }
        },
        // 返回上一页面
        goBack() {
            const gobackFlag = this.$route.query.gobackFlag
            if(gobackFlag == 'webview'){
                ClientJs.closeCallBack('FSOP');
            } else {
                history.go(-1)
            }
        },
        // 手机号输入
        inputNum() {
            this.cntsub = false
            this.hasNoOver = false
            this.isPhone = true

            if (this.telNum) {
                let str = this.telNum + ''
                if (str['0'] == '0') {
                    if (str.length >= 11) {
                        this.isPhone = false
                        this.$toast('请输入正确的手机号码')
                        this.telNum = ''
                    }
                } else {
                    this.huawuList = []
                    this.hisBuopList = []
                    this.isDperson = ''
                    this.sjlist = []

                    if (str.length >= 11) {
                        this.panduanYorO(this.telNum)
                    }
                }
            }
        },
        // 是否已存在
        panduanHas(){
            if (this.editType != 'add') {
                return
            }
            let param11 = {
                "busiTel": this.telNum,       //商机号码
                "region": Storage.session.get('userInfo').region,    //地市
                "regionId": this.belongRegion.id ? this.belongRegion.id : Storage.session.get('userInfo').region == 99 ?14  : Storage.session.get('userInfo').region  ,    //地市
                "busiType": 1
            }
            console.info('判定商机号码是否已存在?',param11)

            this.$http.post('/xsb/gridCenter/familyDiagnosis/h5UnfinishedBusiQuery', param11).then((res) =>{
                if (res.data.retCode == -1){
                    this.hasNoOver = true
                    // 您可以办理成功后联系管理员补录。
                    let result = res.data.retMsg.replace(/您可以办理成功后联系管理员补录。/, "");
                    this.chongfu = result
                    this.$toast(res.data.retMsg)
                    return
                } else {
                    this.hasNoOver = false
                    this.getDandHuawu()
                    this.gethisBuopList()

                }
            })
        },
        // 判断归属
        panduanYorO(value) {

            if (this.editType == 'add') {
                this.cntsub = false
                if (value['0'] == 0) {

                    this.mItem.oregionId = 2
                    this.mItem.operaId = 2
                    this.cntsub = false
                    this.panduanHas()
                } else {
                    var regNum2 = /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/
                    if (!regNum2.test(value)) {
                        this.isPhone = false
                        this.$toast('请输入正确的手机号码')
                        return
                    }
                    let urlnew = `/xsb/gridCenter/familyDiagnosis/h5QryUserCityByBoss`
                    let paramenw ={
                        "crmId": Storage.session.get('userInfo').crmId,
                        "region":  Storage.session.get('userInfo').region,
                        "servicenumber":value,  // 手机号码
                        "is_show_csp": "1"              // 是否展示运营商   0：否   1：是
                    }
                    this.$http.post(urlnew, paramenw).then((resnew) => {
                        if (resnew.data.retCode == '0') {
                            if (process.env.NODE_ENV === 'development') {
                                resnew.data.data.csp_type = 4
                            }
                            if (resnew.data.data.province_name.indexOf('江苏') >= 0) {
                                if(resnew.data.data.csp_type == 1) {
                                    this.cntsub = true
                                    this.mItem.oregionId = '1' // 本网
                                    this.mItem.operaId = resnew.data.data.csp_type
                                    this.$messagebox({
                                        title: '温馨提示',
                                        message: '存量号码，请重新输入，或按业务商机进行录入。',
                                        showCancelButton: true,
                                        confirmButtonText: '去业务商机',
                                        cancelButtonText: '知道了',
                                    }).then((action) => {
                                        if (action == 'confirm') {
                                            this.$router.push({
                                                path: '/BusinessOpportMaintenance',
                                                query: {
                                                    showbtn: 1,
                                                    editType: 'add',
                                                    tel: this.telNum
                                                }
                                            })
                                        }
                                    });
                                } else {
                                    this.mItem.oregionId = '2' // 省内异网
                                    this.mItem.operaId = resnew.data.data.csp_type
                                    this.panduanHas()
                                    this.getGJList()
                                    this.gethisBuopList()
                                }
                            } else{
                                this.mItem.oregionId = '3' // 外省
                                this.mItem.operaId = resnew.data.data.csp_type
                                this.panduanHas()
                                this.getGJList()
                                this.gethisBuopList()
                            }
                        } else {
                            this.$alert(resnew.data.retMsg || '未识别出号码归属，请重新输入号码')

                        }
                    }).catch(() => {
                        this.$toast('未识别出号码归属，请重新输入号码')

                    })
                }
            } else {
                this.panduanHas()
                this.getGJList()
                this.gethisBuopList()
            }


        },
        // 合约到期时间
        changeTime(){
            if (!this.showbtn) return

            let date = '';
            let _this = this;

            NlDatePicker({
                onlyOne:true,
                format:'yyyy-MM-dd',
                title: '合约到期时间',

                tsMinDate:new Date(new Date().getTime()-1000*24*60*60),

            }, (retVal) => {
                console.info(retVal)
                _this.busiExpireDate = retVal.startDate + ' 23:59:59';
            });
        },
        // 预约服务时间
        changeTime2(){
            if (!this.showbtn) return

            let _this = this;

            NlDatePicker({
                onlyOne:true,
                dateType: 'datetime',
                format: 'yyyy-MM-dd hh:mm',
                title: '预约服务时间',

                tsMinDate:new Date(new Date().getTime()),

            }, (retVal) => {
                _this.serviceDate = retVal.startDate + ':00' ;
                console.info(_this.serviceDate)

            });
        },
        // 客户来源
        changeLY(){
            if (!this.showbtn) return

            var self = this
            NlDropdown({
                confirmBtn: false,
                title: '客户来源',
                datalist: [
                    {id: '',label: "请选择"},
                    {id: '进厅',label: "进厅"},
                    {id: '公开卖场',label: "公开卖场"},
                    {id: '沿街商铺',label: "沿街商铺"},
                    {id: '小区',label: "小区"},
                    {id: '集团',label: "集团"},
                    {id: '楼宇',label: "楼宇"},
                    {id: '村组',label: "村组"},
                    {id: '银发市场',label: "银发市场"},
                    { id: '在线', label: '在线' },
                    { id: '互联网', label: '互联网' },
                ]
            },function (retVal) {
                self.laiyuan = retVal.label
            });
        },
        //客户关怀查询接口
        qrySubsCertIdNum(cardNum){
            if(cardNum) {
                let url = `/xsb/personBusiness/customerCare/h5QryCustomerInfo?certNo=${cardNum}`;
                this.$http.get(url).then(res => {
                    let {retCode,retMsg,data} = res.data;
                    console.info('客户关怀查询接口provNewIcCnt',res.data.data.provNewIcCnt)
                    console.info('客户关怀查询接口lowConsume',res.data.data.lowConsume)
                    this.provNewIcCnt = res.data.data.provNewIcCnt ? res.data.data.provNewIcCnt : ''
                    this.lowConsume = res.data.data.lowConsume? res.data.data.lowConsume : ''
                })

            }
        },
        // 话务信息
        getDandHuawu(){
            let url = '/xsb/gridCenter/familyDiagnosis/h5QueryMsisdnDura'
            let params = {
                "msisdn": this.telNum,  //输入的商机号码
                "sysId": "H00000000072",
                'crmId': Storage.session.get('userInfo').crmId,
                'region': Storage.session.get('userInfo').region,
            }
            this.$http.post(url, params).then((res) => {
                if (res.data.retCode == '0') {
                    let body = res.data.data.body
                    this.isDperson = body.isDTypeLable == 1? '是' : '否'
                    if (body.dataList && body.dataList.length ) {
                        this.huawuList = body.dataList

                    }
                }
            })
        },
        submitJudge(save){
            // save --99提交 13保存
            if (!this.isPhone) {
                this.$toast('请输入正确的手机号码')
                return
            }
            var regNum = /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/
            if (!regNum.test(this.telNum)) {
                this.$toast('请输入正确的手机号')
                return
            }
            if (this.hasNoOver) {
                this.$toast(this.chongfu || '该商机号已存在，请重新输入用户号码')
                return
            }
            if (this.cntsub) {
                this.$messagebox({
                    title: '温馨提示',
                    message: '存量号码，请重新输入，或按业务商机进行录入。',
                    showCancelButton: true,
                    confirmButtonText: '去业务商机',
                    cancelButtonText: '知道了',
                }).then((action) => {
                    if (action == 'confirm') {
                        this.$router.push({
                            path: '/BusinessOpportMaintenance',
                            query: {
                                showbtn: 1,
                                editType: 'add',
                                tel: this.telNum
                            }
                        })
                    }
                });
                return
            }
            if (!this.telNum) {
                this.$toast('请输入用户号码')
                return
            }


            // 保存
            if (save == 99) {
                let regNum = /^[\d|\.]*$/
                if (this.telNum['0'] == 0) {
                    if (!regNum.test(this.telNum)) {
                        this.$toast('请输入正确的用户号码')
                        return
                    }
                    const reg = /^0\d{2,3}\d{7,8}$/
                    if (!reg.test(this.telNum)) {
                        this.$toast('请输入正确的用户号码')
                        return
                    }
                } else if (this.telNum['0'] == 1) {
                    if (!regNum.test(this.telNum)) {
                        this.$toast('请输入数字')
                        return
                    }
                    if (this.telNum.length != 11) {
                        this.$toast('请输入正确的用户号码')
                        return
                    }
                    var regNum2 = /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/
                    if (!regNum2.test(this.telNum)) {
                        this.$toast('请输入正确的手机号')
                        return
                    }
                } else {
                    this.$toast('请输入正确的用户号码')
                    return
                }


                if (!this.mItem.operaId) {
                    this.$toast('请选择归属运营商')
                    return
                }
                if (!this.busiUserName) {
                    this.$toast('请输入用户姓名')
                    return
                }
                if (!this.custName.id) {
                    this.$toast('请选择归属区县')
                    return
                }


                if (this.busiCertId && save != 13) {
                    console.info(this.validateIdCard(this.busiCertId))
                    if (!this.validateIdCard(this.busiCertId)) {
                        this.$toast('请输入正确的身份证号')
                        return
                    }
                }
            }



            // 维护---点提交
            if (this.showbtn && this.editType =='change') {
                if(this.potentialuser_maintenance_flag ) {
                    if (!this.editObject.messageVerify || this.editObject.messageVerify == 'false') {
                        if(this.yzmCode) {
                            this.getSmsCheckVerification(save)
                            return
                        } else  {
                            this.$toast('请输入验证码')
                            return

                        }
                    }
                }
            }


            // 补充---点提交
            if(save == 99  &&  this.editType == 'supplement') {
                if(this.potentialuser_maintenance_flag ) {
                    if (!this.editObject.messageVerify || this.editObject.messageVerify == 'false') {
                        if(this.yzmCode) {
                            this.getSmsCheckVerification(save)
                            return
                        } else  {
                            this.$toast('请输入验证码')
                            return
                        }
                    }
                }
            }


            if (this.yzmCode) {
                this.getSmsCheckVerification(save)
            } else {
                if  (this.editObject.messageVerify == 'true'){
                    this.submitFunction(1,save)
                } else {
                    this.submitFunction(null,save)
                }
            }


        },
        // 图片提交ocr
        submitPic(val) {
            let param2 = {
                photoStr: val,
                comeFrom: 'familyDiagnosis'
            }
            param2['unEncrpt'] = true;
            this.$http.post('/xsb/personBusiness/queryCalculate/h5SubmitPhoto', param2).then((res) => {
                this.thisvideo.push(res.data.data)
                let param1 = {
                    'crmId': Storage.session.get('userInfo').crmId,
                    'region': Storage.session.get('userInfo').region,
                    "operatorTel": Storage.session.get('userInfo').servNumber,  //操作人手机号
                    "images":val,
                    "imageName" : res.data.data,
                    "streamSeq": new Date().getTime(),
                    "params": JSON.stringify([
                        {
                            "key": "busiTel",
                            "desc": "你需要分析提供的图片中的内容并提取其中的手机号码信息。图片形式可能为页面截图也可能为拍摄的图片，号码中间4位可能显示为*，存在多个手机号的时候只需要返回一个手机号，请优先给出最先识别出的那一个号码或者“主号码”、“用户号码”、“我的号码”、“联系电话”等字样后的那一个号码。请以 11 位手机号码的格式输出结果，若图片中不存在手机号码，则返回空字符串。注意请直接返回字符串，不要添加任何其他文本或说明。",
                            "exp": "13901582505"
                        },
                        {
                            "key": "busiUserName",
                            "desc": "你需要分析提供的图片中的内容并提取其中的姓名信息。图片形式可能为页面截图也可能为拍摄的图片，姓名中可能包含1个或多个*，存在多个姓名的时候只需要返回最先识别出来的那一个姓名。请以字符串格式输出结果，若图片中不存在姓名，则返回空字符串。注意请直接返回字符串，不要添加任何其他文本或说明。",
                            "exp": "王涛"
                        },
                        {
                            "key": "bandInstall",
                            "desc": "你需要分析提供的图片中的内容并提取其中的地址信息。图片形式可能为页面截图也可能为拍摄的图片，地址中可能包含1个或多个*，存在多个地址的时候只需要返回一个地址，请优先给出最先识别出的那一个地址。请以字符串格式输出结果，例如：中海大厦15A，若图片中不存在地址，则返回空字符串。注意请直接返回字符串，不要添加任何其他文本或说明。",
                            "exp": "中海大厦15A"
                        },
                        {
                            "key": "monthConsum",
                            "desc": "你需要分析提供的图片中的内容并提取其中的金额信息。图片形式可能为页面截图也可能为拍摄的图片，金额类型一般为包含两位小数的数字，存在多个金额的时候只需要最先识别出来的那一个金额信息。请以字符串格式输出结果，例如：157.00，若图片中不存在金额，则返回空字符串。注意请直接返回字符串，不要添加任何其他文本或说明。",
                            "exp": "157.00"
                        }
                    ]),
                    "prompt": "分析文本并提取以下信息如果没有则传空",
                    "modelName": "QWEN2.5-14B",
                    'unEncrpt' : true,
                    "identifySource": 1
                }
                console.log('ocr---',param1)
                let url1 = `/xsb/gridCenter/familyDiagnosis/h5AiIdentify`
                this.$http.post(url1, param1).then((res) => {

                    if (res.data.data) {

                        let obj = res.data.data.data
                        if(this.editType == 'add') {
                            if(obj.busiTel && obj.busiTel.indexOf('*') < 0) {
                                this.telNum = obj.busiTel
                                this.inputNum()
                            }
                        }

                        if(obj.busiUserName) {
                            this.busiUserName = obj.busiUserName.replace(/\*/g, "")

                        }
                        if(obj.bandInstall) {
                            this.bandInstall = obj.bandInstall
                        }
                        if(obj.monthConsum) {
                            this.monthConsum = obj.monthConsum

                        }
                    }
                })
            })

        },
        // 号码脱敏
        maskphone(phone){
            if(!phone) return


            let thisObj = this.$route.query.item ? JSON.parse(this.$route.query.item) : {}
            if (thisObj.createTel == this.userInfo.servNumber || thisObj.followTel == this.userInfo.servNumber) {

                return phone; // 185****5678
            } else{
                var reg = /^(1[3-9][0-9])\d{4}(\d{4}$)/;
                // 判断手机号是否能够通过正则校验
                let isMobile=reg.test(phone);
                //将手机号中间4位用*展示
                phone = phone.replace(reg, '$1****$2');
                return phone; // 185****5678
            }


        },
        // 号码脱敏
        maskphone2(phone){
            if(!phone) return
            var reg = /^(1[3-9][0-9])\d{4}(\d{4}$)/;
            // 判断手机号是否能够通过正则校验
            let isMobile = reg.test(phone);
            //将手机号中间4位用*展示
            phone = phone.replace(reg, '$1****$2');
            return phone; // 185****5678
        },
        //提交
        async submitFunction(codetype,savetype) {
            console.log('提交codetype,提交savetype',codetype,savetype)
            if (savetype != 13) {
                let regNum = /^[\d|\.]*$/
                if (this.telNum['0'] == 0) {
                    if (!regNum.test(this.telNum)) {
                        this.$toast('请输入数字')
                        return
                    }
                    const reg = /^0\d{2,3}\d{7,8}$/
                    if (!reg.test(this.telNum)) {
                        this.$toast('请输入正确的用户号码')
                        return
                    }
                } else if (this.telNum['0'] == 1) {
                    if (!regNum.test(this.telNum)) {
                        this.$toast('请输入数字')
                        return
                    }
                    if (this.telNum.length != 11) {
                        this.$toast('请输入正确的用户号码')
                        return
                    }
                    var regNum2 = /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/
                    if (!regNum2.test(this.telNum)) {
                        this.$toast('请输入正确的手机号')
                        return
                    }
                } else {
                    this.$toast('请输入正确的用户号码')
                    return
                }

                console.log('this.mItem',this.mItem)
                if (!this.mItem.operaId) {
                    this.$toast('请选择归属运营商')
                    return
                }
                if (!this.busiUserName) {
                    this.$toast('请输入用户姓名')
                    return
                }
                if (!this.custName.townId) {
                    this.$toast('请选择区县乡镇')
                    return
                }
                if (this.potentialuser_maintenance_flag && !this.yzmCode && this.editType == 'add') {
                    this.$toast('请输入验证码')
                    return
                }

                if (this.busiCertId ) {
                    console.info(this.validateIdCard(this.busiCertId))
                    if (!this.validateIdCard(this.busiCertId)) {
                        this.$toast('请输入正确的身份证号')
                        return
                    }
                }


            }

            let userInfo = Storage.session.get('userInfo')
            if(codetype) {
                this.yzmCode = ''
            }
            this.savetype = savetype
            if(this.editType == 'change') {
                console.log('维护------更新商机信息')
                let weihuObj = JSON.parse(this.$route.query.item)
                let param2 = {
                    "region": userInfo.region,   // 地市
                    "busiTel": this.telNum?this.telNum: this.$route.query.busiTel,   //商机号码
                    "buopId": this.$route.query.buopId,//商机id
                    "userAffiliation": this.mItem.oregionId,  // 用户归属
                    "busiOperatorName": this.mItem.operaId,  //运营商
                    "createTel": userInfo.servNumber,   //当前操作人号码
                    "createOper":  userInfo.operatorName,  //当前操作人
                    "homeId":  '',  //家庭号
                    "releBillId": "",  //关联的算账单号
                    "busiUserName": this.busiUserName,  // 用户姓名
                    "busiCertId": this.busiCertId,// 身份证号码
                    "ztVideoUrl": this.thisvideo.join(','),// 掌厅图片
                    "userSource": this.laiyuan,// 客户来源
                    "isContBund": this.isContBund, // 是否有合约捆绑
                    "busiExpireDate": this.busiExpireDate? this.busiExpireDate: '',// 合约到期时间
                    "custName": this.custName.label,// 归属区县字段
                    "regionId": this.belongRegion.id ? this.belongRegion.id : userInfo.region == 99 ?14  :userInfo.region,// 区县id
                    "countyId":  this.custName.id,// 归属区县id,
                    "gridId": this.custName.gridId,
                    "gridName": this.custName.gridName,
                    "streetId": this.custName.townId,
                    "streetName": this.custName.townName,
                    "reserve2":  this.custName.id,// 归属区县id,
                    "reserve3": this.reserve3, // 用户需求描述
                    "userDemand": this.reserve3, // 用户需求描述
                    "villageName": this.villageName,// 详细地址字段
                    "modifyOper":Storage.session.get('userInfo').operatorName,//修改人（当前操作人）
                    "modifyTel":Storage.session.get('userInfo').servNumber,//修改人（当前操作人电话）
                    "followBy": weihuObj.followBy,//当前跟进人（当前操作人）
                    "followTel": weihuObj.followTel,//当前跟进人电话（当前操作人电话）
                    "buopStatus": weihuObj.buopStatus, //状态
                    "messageVerify": this.editObject.messageVerify == 'true' ? true : codetype ? true: false, // 是否短信严重
                    "serviceDate": this.serviceDate, // 服务时间
                    "bandInstall": this.bandInstall,
                    "monthConsum": this.monthConsum,
                }
                console.info('维护-更新商机信息-->',param2)

                this.$http.post('/xsb/gridCenter/familyDiagnosis/h5PersonBusiUpdate', param2).then((res) => {
                    let { retCode, retMsg } = res.data
                    if (retCode == '0') {
                        let param = {
                            'operatorMsisdn': userInfo.servNumber,
                            'operatorName': userInfo.operatorName,
                            'operatorCrm': userInfo.crmId,
                            'changeType': '3',
                            'regMsisdn':this.telNum,//登记主号码即潜在成员登记哪个号码下
                            'homeID': this.$route.query.homeId,//归属家庭
                            'msisdn': this.telNum,//号码
                            'ascriptionType': this.mItem.oregionId,//号码归属1：本网2：异网
                            'teleType': this.mItem.operaId,//运营商1：移动2：电信3：联通
                            'userID': Storage.session.get('userInfo').servNumber,//变更人
                            'changeTime': dateFormat(new Date(), 'yyyy/MM/dd hh:mm:ss'),//变更时间
                            "modifyOper":Storage.session.get('userInfo').operatorName,
                            "modifyTel": Storage.session.get('userInfo').servNumber,
                            "region":  Storage.session.get('userInfo').region,
                            "countyId": this.custName.id,
                            "custName": this.custName.label,
                            "gridId": this.custName.gridId,
                            "gridName": this.custName.gridName,
                            "streetId": this.custName.townId,
                            "streetName": this.custName.townName,
                            "regionId": this.belongRegion.id ? this.belongRegion.id : userInfo.region == 99 ? 14  :userInfo.region,// 区县id
                            "villageName": this.villageName,
                            "ztVideoUrl": this.thisvideo.join(','),// 掌厅图片
                        }
                        let url = '/xsb/personBusiness/queryCalculate/h5QryFamilyMemberChange'
                        console.info('维护--更新已有的成员信息-->',param)

                        this.$http.post(url, param).then(res => {
                            let { retCode, retMsg } = res.data
                            if (retCode == '0') {
                                this.$toast('提交成功')
                                setTimeout(() => {
                                    this.goBack();
                                },1000)
                            } else {
                                this.$alert(retMsg )
                            }
                        })

                    }else {
                        this.$alert(retMsg )
                    }
                })
            } else {
                console.info('新增/补充---进来了')
                let param11 = {
                    "busiTel": this.telNum,       //商机号码
                    "region": Storage.session.get('userInfo').region,    //地市
                    "regionId": this.belongRegion.id ? this.belongRegion.id : Storage.session.get('userInfo').region,    //地市
                    "busiType": 1
                }
                console.info('判定商机号码是否已存在?',param11)

                this.$http.post('/xsb/gridCenter/familyDiagnosis/h5UnfinishedBusiQuery', param11).then((reszz) => {
                    if(reszz.data.retCode == -1 && this.editType == 'add') {
                        this.hasNoOver = true
                         // 您可以办理成功后联系管理员补录。
                        let result = reszz.data.retMsg.replace(/您可以办理成功后联系管理员补录。/, "");
                        this.chongfu = result
                        this.$toast(reszz.data.retMsg)
                        return
                    } else {
                        if (this.homeId) {
                            console.log('有homeid,点保存/提交----进来了')
                            let url2 = '/xsb/gridCenter/familyDiagnosis/h5PersonBusiAdd'
                            let param2 = {
                                "buopStatus": this.editType2 ? 16 : this.savetype == 13 ? 7 : 2,
                                "serviceDate": this.serviceDate,
                                "busiSource": this.editObject.busiSource ? this.editObject.busiSource : '1',
                                "oaId": userInfo.oaId,
                                "busiType": '1',
                                "bandInstall": this.bandInstall,
                                "monthConsum": this.monthConsum,
                                "busiTel": this.telNum,   //商机号码
                                "buopId": this.$route.query.buopId,//商机id
                                "userAffiliation": this.mItem.oregionId,  // 用户归属
                                "busiOperatorName": this.mItem.operaId,  //运营商
                                "createTel": userInfo.servNumber,   //当前操作人号码
                                "createOper":  userInfo.operatorName,  //当前操作人
                                "homeId":  this.homeId,  //家庭号
                                "releBillId": '',  //关联的算账单号
                                "busiUserName": this.busiUserName,  // 用户姓名
                                "busiCertId": this.busiCertId,// 身份证号码
                                "ztVideoUrl": this.thisvideo.join(','),// 掌厅图片
                                "userSource": this.laiyuan,// 客户来源
                                "isContBund": this.isContBund, // 是否有合约捆绑
                                "busiExpireDate": this.busiExpireDate? this.busiExpireDate: '',// 合约到期时间
                                "reserve1": this.telNum['0'] == 1 ? true : '',
                                "custName": this.custName.label,// 归属区县字段
                                "countyId":  this.custName.id,// 归属区县id,
                                "gridId": this.custName.gridId,
                                "gridName": this.custName.gridName,
                                "streetId": this.custName.townId,
                                "streetName": this.custName.townName,
                                "regionId": this.belongRegion.id ? this.belongRegion.id : userInfo.region == 99 ?14  :userInfo.region,// 区县id
                                "reserve3": this.reserve3, // 用户需求描述
                                "villageName": this.villageName,// 详细地址字段
                                "userDemand": this.reserve3,
                                "crmId": Storage.session.get('userInfo').crmId,
                                "messageVerify": codetype ? true: false, // 2025.1.20 新增字段，是否短信验证
                                "operateType": this.editType2 ? 22 : this.savetype == 99 ? '1' : '13'
                            }

                            this.$http.post(url2, param2).then((res2) => {
                                let { retCode, retMsg } = res2.data
                                this.buopId = res2.data.data
                                if (this.savetype == '99' && this.editType != 'supplement') {
                                    this.showChooseReason = true
                                } else {
                                    if (this.savetype == '99' ) {
                                        this.$toast('提交成功')
                                    } else {
                                        this.$toast('保存成功')
                                    }
                                    setTimeout(() => {
                                        this.goBack();
                                    },1000)
                                }
                                console.info('有家庭----记录新商机',param2)

                                if (retCode == '0') {
                                    let param = {
                                        'operatorMsisdn': userInfo.servNumber,
                                        'operatorName': userInfo.operatorName,
                                        'operatorCrm': userInfo.crmId,
                                        'changeType': '3',
                                        'regMsisdn':this.telNum,//登记主号码即潜在成员登记哪个号码下
                                        'homeID': this.homeId,//归属家庭
                                        'msisdn': this.telNum,//号码
                                        'ascriptionType': this.mItem.oregionId,//号码归属1：本网2：异网
                                        'teleType': this.mItem.operaId,//运营商1：移动2：电信3：联通
                                        'userID': Storage.session.get('userInfo').servNumber,//变更人
                                        'changeTime': dateFormat(new Date(), 'yyyy/MM/dd hh:mm:ss'),//变更时间

                                    }
                                    let url = '/xsb/personBusiness/queryCalculate/h5QryFamilyMemberChange'
                                    console.info('有家庭----更新已有的成员信息',param)

                                    this.$http.post(url, param).then(resUpdateHome => {
                                        if (resUpdateHome.data.retCode == '0') {
                                            // history.go(-1)

                                        } else {
                                            this.$alert(resUpdateHome.data.retMsg)
                                        }
                                    })

                                }else {
                                    this.$alert(retMsg)
                                }
                            })
                            return
                        } else {
                            console.log('没有homeid,点保存/提交----进来了')
                            let paramHome = {
                                "msisdn":this.telNum,
                            };
                            console.info('不存在homeid:查询当前号码是否已有归属家庭?',paramHome)
                            let urlHome='/xsb/personBusiness/queryCalculate/h5QryJudgmentNum';
                            this.$http.post(urlHome,paramHome).then(res => {
                                let {retCode,data,retMsg} = res.data;
                                if(retCode == '0') {
                                    this.homeId = data.result.homeId;
                                    let userInfo = Storage.session.get('userInfo')
                                    console.info('this.homeId',this.homeId)
                                    console.info('this.operaId',this.operaId)
                                    if (this.homeId) {
                                        let url2 = '/xsb/gridCenter/familyDiagnosis/h5PersonBusiAdd'
                                        let param2 = {
                                            "buopStatus": this.editType2 ? 16 : this.savetype == 13 ? 7 : 2,
                                            "busiSource": this.editObject.busiSource ? this.editObject.busiSource : '1',
                                            "oaId": userInfo.oaId,
                                            "serviceDate": this.serviceDate,
                                            "busiType": '1',
                                            "bandInstall": this.bandInstall,
                                            "monthConsum": this.monthConsum,
                                            "busiTel": this.telNum,   //商机号码
                                            "buopId": this.$route.query.buopId,//商机id
                                            "userAffiliation": this.mItem.oregionId,  // 用户归属
                                            "busiOperatorName": this.mItem.operaId,  //运营商
                                            "createTel": userInfo.servNumber,   //当前操作人号码
                                            "createOper":  userInfo.operatorName,  //当前操作人
                                            "homeId":  this.homeId,  //家庭号
                                            "releBillId": '',  //关联的算账单号
                                            "busiUserName": this.busiUserName,  // 用户姓名
                                            "busiCertId": this.busiCertId,// 身份证号码
                                            "ztVideoUrl": this.thisvideo.join(','),// 掌厅图片
                                            "userSource": this.laiyuan,// 客户来源
                                            "isContBund": this.isContBund, // 是否有合约捆绑
                                            "busiExpireDate": this.busiExpireDate? this.busiExpireDate: '',// 合约到期时间
                                            "reserve1": this.telNum['0'] == 1 ? true : '',
                                            "custName": this.custName.label,// 归属区县字段
                                            "countyId":  this.custName.id,// 归属区县id,
                                            "gridId": this.custName.gridId,
                                            "gridName": this.custName.gridName,
                                            "streetId": this.custName.townId,
                                            "streetName": this.custName.townName,
                                            "regionId": this.belongRegion.id ? this.belongRegion.id : userInfo.region == 99 ?14  :userInfo.region,// 区县id
                                            "reserve3": this.reserve3, // 用户需求描述
                                            "villageName": this.villageName,// 详细地址字段
                                            "userDemand": this.reserve3,
                                            "crmId": Storage.session.get('userInfo').crmId,
                                            "messageVerify": codetype? true: false, // 2025.1.20 新增字段，是否短信验证
                                            "operateType": this.editType2 ? 22 : savetype == 99 ? '1' : '13'
                                        }
                                        this.$http.post(url2, param2).then((res2) => {
                                            let { retCode, retMsg } = res2.data
                                            this.buopId = res2.data.data

                                            if (this.savetype == '99' && this.editType != 'supplement') {
                                                this.showChooseReason = true
                                            } else {
                                                if (this.savetype == '99' ) {
                                                    this.$toast('提交成功')
                                                } else {
                                                    this.$toast('保存成功')
                                                }
                                                setTimeout(() => {
                                                    this.goBack();
                                                },1000)
                                            }
                                            console.info('有家庭----记录新商机',param2)

                                            if (retCode == '0') {
                                                let param = {
                                                    'operatorMsisdn': userInfo.servNumber,
                                                    'operatorName': userInfo.operatorName,
                                                    'operatorCrm': userInfo.crmId,
                                                    'changeType': '3',
                                                    'regMsisdn':this.telNum,//登记主号码即潜在成员登记哪个号码下
                                                    'homeID': this.homeId,//归属家庭
                                                    'msisdn': this.telNum,//号码
                                                    'ascriptionType': this.mItem.oregionId,//号码归属1：本网2：异网
                                                    'teleType': this.mItem.operaId,//运营商1：移动2：电信3：联通
                                                    'userID': Storage.session.get('userInfo').servNumber,//变更人
                                                    'changeTime': dateFormat(new Date(), 'yyyy/MM/dd hh:mm:ss'),//变更时间
                                                }
                                                let url = '/xsb/personBusiness/queryCalculate/h5QryFamilyMemberChange'
                                                console.info('有家庭----更新已有的成员信息',param)

                                                this.$http.post(url, param).then(resUpdateHome => {
                                                    if (resUpdateHome.data.retCode == '0') {

                                                    } else {
                                                        this.$alert(resUpdateHome.data.retMsg)
                                                    }
                                                })
                                            }else {
                                                this.$alert(retMsg)
                                            }
                                        })

                                    } else {
                                        let param = {
                                            'operatorMsisdn': userInfo.servNumber,
                                            'operatorName': userInfo.operatorName,
                                            'operatorCrm': userInfo.crmId,
                                            'changeType': '1',
                                            'regMsisdn': this.telNum,//登记主号码即潜在成员登记哪个号码下
                                            'homeID': this.homeId,//归属家庭
                                            'msisdn': this.telNum,//号码
                                            'ascriptionType': this.mItem.oregionId,//号码归属1：本网2：异网
                                            'teleType': this.mItem.operaId,//运营商1：移动2：电信3：联通
                                            'userID': Storage.session.get('userInfo').servNumber,//变更人
                                            'changeTime': dateFormat(new Date(), 'yyyy/MM/dd hh:mm:ss'),//变更时间

                                        }
                                        let url = '/xsb/personBusiness/queryCalculate/h5QryFamilyMemberChange'
                                        console.info('没有家庭----保存成员信息',param)

                                        this.$http.post(url, param).then(res => {
                                            let { retCode, retMsg } = res.data
                                            if (retCode == '0') {
                                                let url3='/xsb/personBusiness/queryCalculate/h5QryJudgmentNum';
                                                this.$http.post(url3,{"msisdn":this.telNum,}).then(res3 => {
                                                    if(res3.data.retCode == '0') {
                                                        this.homeId = res3.data.data.result.homeId;
                                                        let urlAdd = '/xsb/gridCenter/familyDiagnosis/h5PersonBusiAdd'
                                                        let paramAdd = {
                                                            "buopStatus": this.editType2 ? 16 : this.savetype == 13 ? 7 : 2,
                                                            "busiSource": this.editObject.busiSource ? this.editObject.busiSource : '1',
                                                            "oaId": userInfo.oaId,
                                                            "serviceDate": this.serviceDate,
                                                            "busiType": '1',
                                                            "bandInstall": this.bandInstall,
                                                            "monthConsum": this.monthConsum,
                                                            "busiTel": this.telNum,   //商机号码
                                                            "buopId": this.$route.query.buopId,//商机id
                                                            "userAffiliation": this.mItem.oregionId,  // 用户归属
                                                            "busiOperatorName": this.mItem.operaId,  //运营商
                                                            "createTel": userInfo.servNumber,   //当前操作人号码
                                                            "createOper":  userInfo.operatorName,  //当前操作人
                                                            "homeId":  this.homeId,  //家庭号
                                                            "releBillId": '',  //关联的算账单号
                                                            "busiUserName": this.busiUserName,  // 用户姓名
                                                            "busiCertId": this.busiCertId,// 身份证号码
                                                            "ztVideoUrl": this.thisvideo.join(','),// 掌厅图片
                                                            "userSource": this.laiyuan,// 客户来源
                                                            "isContBund": this.isContBund, // 是否有合约捆绑
                                                            "busiExpireDate": this.busiExpireDate? this.busiExpireDate: '',// 合约到期时间
                                                            "reserve1": this.telNum['0'] == 1 ? true : '',
                                                            "custName": this.custName.label,// 归属区县字段
                                                            "countyId":  this.custName.id,// 归属区县id,
                                                            "gridId": this.custName.gridId,
                                                            "gridName": this.custName.gridName,
                                                            "streetId": this.custName.townId,
                                                            "streetName": this.custName.townName,
                                                            "regionId": this.belongRegion.id ? this.belongRegion.id : userInfo.region == 99 ?14  :userInfo.region,// 区县id
                                                            "reserve3": this.reserve3, // 用户需求描述
                                                            "villageName": this.villageName,// 详细地址字段
                                                            "userDemand": this.reserve3,
                                                            "crmId": Storage.session.get('userInfo').crmId,
                                                            "messageVerify": codetype? true: false, // 2025.1.20 新增字段，是否短信验证
                                                            "operateType":this.editType2 ? 22 : savetype == 99 ? '1' : '13'
                                                        }

                                                        console.info('没有家庭----查到homeid后记录新商机',paramAdd)
                                                        this.$http.post(urlAdd, paramAdd).then((resAdd) => {
                                                            if (resAdd.data.retCode == '0') {
                                                                this.buopId = resAdd.data.data
                                                                console.info('没有家庭----查询号码归属家庭',param)
                                                                if (this.savetype == '99' && this.editType != 'supplement') {
                                                                    this.showChooseReason = true
                                                                } else {
                                                                    this.$toast('提交成功')
                                                                    setTimeout(() => {
                                                                        this.goBack();
                                                                    },1000)
                                                                }
                                                            } else {
                                                                this.$alert(resAdd.data.retMsg || `添加潜在成员失败`)
                                                            }
                                                        })
                                                    }
                                                })

                                            } else {
                                                this.$alert(retMsg || `添加潜在成员失败`)
                                            }
                                        })
                                    }

                                }
                            })
                        }
                    }
                })
            }
        },
        // 互通话务信息几月份文字处理
        getStr(value) {
            if (!value) return
            let val = value + ''
            val = val.slice(-2);
            if (val['0'] == 0) {
                val = val.slice(-1);
            }
            val = val + '月'
            return val
        },
        // 短信开关
        SMSswitch() {
            let SwitchParam = {
                switchType: 'potentialuser_maintenance_flag'
            }

            console.log(1111111111,SwitchParam)
            this.$http.post('/xsb/personBusiness/gridSand/h5GridMarketSwitch', SwitchParam).then((res) => {
                if (res.data.retCode == 0) {
                    this.potentialuser_maintenance_flag = true
                } else {
                    this.potentialuser_maintenance_flag = false
                }
            })
        },
        //地市列表查询接口
        qryCountyList(regionId){
            return
            if(regionId == 99) {
                regionId = 14
            }
            let url = `/xsb/personBusiness/listGroupTarget/h5qryCountyList?regionId=`+ regionId;
            this.$http.post(url).then(res =>{
                if(res.data.retCode == '0'){
                    let data = res.data.data;
                    this.countyObjList = data;
                    for (let i = 0 ;i < this.countyObjList.length;i++) {
                        if (this.countyObjList[i].id == '88020041152678' || this.countyObjList[i].id == '88020360015762' || this.countyObjList[i].id == '88021088781110' || this.countyObjList[i].id == '88021088778966' || this.countyObjList[i].id == '88021089325506' || this.countyObjList[i].id == '88021089323054' || this.countyObjList[i].id == '88021089318250' || this.countyObjList[i].id == '88021089196266' || this.countyObjList[i].id == '88021058080998' || this.countyObjList[i].id == '88021089181934' || this.countyObjList[i].id == '88021089178670') {
                            this.countyObjList.splice(i,1)
                        }
                    }
                } else {
                    this.$alert(res.data.retMsg || '查询区县列表失败');
                }
            }).catch(res =>{
                this.$alert('查询区县列表网络异常:'+ res);
            });
        },
        // 归属区县
        changeCounty(){
            this.changeCountyFlg = true
            this.chooseQX1 = this.custName.countryId
            console.log('this.custName',this.custName.countryId)
            console.log('this.chooseQX1',this.chooseQX1)

            this.chooseQX11 = this.custName.countryName
            this.chooseQX2 = this.custName.gridId
            this.chooseQX22 = this.custName.gridName
            this.chooseQX3 = this.custName.townId
            this.chooseQX33 = this.custName.townName
            for (let i = 0 ; i < this.countyObjListNew.length;i++) {
                if (this.custName.countryId == this.countyObjListNew[i].countryId) {
                    this.countyObjListNew2 = this.countyObjListNew[i].townList

                }
            }
            console.log('this.chooseQX3',this.chooseQX3)
            console.log('this.custName',this.custName)

            return
            NlDropdown({
                confirmBtn: false,
                datalist: this.countyObjList
            }, (retVal) => {
                this.custName = retVal
            })
        },
        // 删图
        delThisImg(idx){
            if(this.thisvideo) {
                this.thisvideo.splice(idx,1)
            }
            this.thebase64.splice(idx,1)
        },
        /**
         * 身份证合法性校验
         */
        validateIdCard(idCard){
            let vcity={ 11:"北京",12:"天津",13:"河北",14:"山西",15:"内蒙古",
                21:"辽宁",22:"吉林",23:"黑龙江",31:"上海",32:"江苏",
                33:"浙江",34:"安徽",35:"福建",36:"江西",37:"山东",41:"河南",
                42:"湖北",43:"湖南",44:"广东",45:"广西",46:"海南",50:"重庆",
                51:"四川",52:"贵州",53:"云南",54:"西藏",61:"陕西",62:"甘肃",
                63:"青海",64:"宁夏",65:"新疆",71:"台湾",81:"香港",82:"澳门",91:"国外"
            };
            //是否为空
            if(idCard === '')   return false;
            //校验长度，类型
            if(this.isCardNo(idCard) === false)  return false;
            //检查省份
            if(this.checkProvince(idCard,vcity) === false)   return false;
            //校验生日
            if(this.checkBirthday(idCard) === false) return false;
            //检验位的检测
            if(this.checkParity(idCard) === false)   return false;
            return true;
        },
        /**
         * 检查号码是否符合规范，包括长度，类型
         */
        isCardNo(card){
            //身份证号码为15位或者18位，15位时全为数字，18位前17位为数字，最后一位是校验位，可能为数字或字符X
            let reg = /(^\d{15}$)|(^\d{17}(\d|X|x)$)/;
            if(reg.test(card) === false){
                return false;
            }
            return true;
        },
        /**
         * 取身份证前两位,校验省份
         * @param {*} card
         * @param {*} vcity
         * @returns
         */
        checkProvince(card,vcity){
            let province = card.substr(0,2);
            if(vcity[province] == undefined){
                return false;
            }
            return true;
        },
        /**
         * 检查生日是否正确
         * @param {*} card
         * @returns
         */
        checkBirthday(card){
            var len = card.length;
            //身份证15位时，次序为省（3位）市（3位）年（2位）月（2位）日（2位）校验位（3位），皆为数字
            if(len == '15'){
                var re_fifteen = /^(\d{6})(\d{2})(\d{2})(\d{2})(\d{3})$/;
                var arr_data = card.match(re_fifteen);
                var year = arr_data[2];
                var month = arr_data[3];
                var day = arr_data[4];
                var birthday = new Date('19'+year+'/'+month+'/'+day);
                return this.verifyBirthday('19'+year,month,day,birthday);
            }
            //身份证18位时，次序为省（3位）市（3位）年（4位）月（2位）日（2位）校验位（4位），校验位末尾可能为X
            if(len == '18'){
                var re_eighteen = /^(\d{6})(\d{4})(\d{2})(\d{2})(\d{3})([0-9]|X|x)$/;
                var arr_data = card.match(re_eighteen);
                var year = arr_data[2];
                var month = arr_data[3];
                var day = arr_data[4];
                var birthday = new Date(year+'/'+month+'/'+day);
                return this.verifyBirthday(year,month,day,birthday);
            }
            return false;
        },
        /**
         * 校验日期
         * @param {*} year
         * @param {*} month
         * @param {*} day
         * @param {*} birthday
         * @returns
         */
        verifyBirthday(year,month,day,birthday){
            let now = new Date();
            let now_year = now.getFullYear();
            //年月日是否合理
            if(birthday.getFullYear() == year && (birthday.getMonth() + 1) == month && birthday.getDate() == day) {
                //判断年份的范围（0岁到100岁之间)
                let time = now_year - year;
                if(time >= 0 && time <= 100)    return true;
                return false;
            }
            return false;
        },
        /**
         * 校验位的检测
         * @param {*} card
         * @returns
         */
        checkParity(card){
            //15位转18位
            card = this.changeFivteenToEighteen(card);
            let len = card.length;
            if(len == '18'){
                let arrInt = new Array(7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2);
                let arrCh = new Array('1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2');
                let cardTemp = 0, i, valnum;
                for(i = 0; i < 17; i ++) {
                    cardTemp += card.substr(i, 1) * arrInt[i];
                }
                valnum = arrCh[cardTemp % 11];
                if (valnum == card.substr(17, 1).toLocaleUpperCase()) return true;
                return false;
            }
            return false;
        },
        /**
         * 15位转18位身份证号
         * @param {} card
         * @returns
         */
        changeFivteenToEighteen(card){
            if(card.length == '15'){
                let arrInt = new Array(7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2);
                let arrCh = new Array('1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2');
                let cardTemp = 0, i;
                card = card.substr(0, 6) + '19' + card.substr(6, card.length - 6);
                for(i = 0; i < 17; i ++)
                    cardTemp += card.substr(i, 1) * arrInt[i];
                card += arrCh[cardTemp % 11];
                return card;
            }
            return card;
        },

        init() {
            this.userInfo = Storage.session.get('userInfo')
            this.buopId = this.$route.query.buopId
            this.showbtn = this.$route.query.showbtn == 1
            this.editType = this.$route.query.editType
            this.editType2 = this.$route.query.editType2

            if (this.$route.query.phoneNum) {
                // 从二维码那边跳过来带的参数
                this.telNum = this.$route.query.phoneNum
            } else  {
                this.telNum = this.$route.query.busiTel ? this.$route.query.busiTel : ''
            }

            if(this.$route.query.editType == 'add') {
                // orgId判断,查询执行人地市列表
                for (let i = 0 ;i < this.regionArr.length;i++){
                    if (this.regionArr[i].region== this.userInfo.region){
                        this.orgId= this.regionArr[i].longRegion
                    }
                }
                this.qryCountyList(this.userInfo.region)
                this.getStreetList(this.userInfo.region)

            }
            this.inputNum() // 手机号输入
            this.SMSswitch()// 短信开关
            this.getBusinessDetail() // 商机明细
            // 轨迹
            this.getGJList()
            this.gethisBuopList()

            if (/android|harmony/gi.test(navigator.userAgent)) {
                ClientJs.getAudioPermiss('getAudioPermissfun')
            } else {
                this.rec = new RecorderManger({}, this.recwave, this.getRecordResultCbFn)
            }

            if (this.showbtn && this.editType != 'add' && this.editType != 'supplement') {
                let item  = JSON.parse(this.$route.query.item)
                if (item.followTel == Storage.session.get('userInfo').servNumber) {
                    this.canTel = true
                }
            }
        }

    },
    created() {
        window['getUserInfoCB'] = (result) => {
            let res = result.userInfo
            const uinfo = JSON.parse(res)
            initTokenAfterBack(this.$http, uinfo) //重置token
            this.init()
        }

        const gobackFlag = this.$route.query.gobackFlag
        if (gobackFlag == 'webview') {
            ClientJs.getSysInfo('getUserInfoCB')
        } else {
            this.init()

        }
    },

    mounted() {

        // 掌厅
        window['PalmHalCameral'] = (info) => {
            if(info.fileImage){
                if (this.thebase64.length < 3) {
                    this.thebase64.push(info.fileImage)
                    this.submitPic(info.fileImage)
                } else {
                    this.$toast('最多可拍三张')
                }
            }
        };



        // 身份证
        window['sfzCanmera'] = (info) => {
            this.getImgInfo2(info.fileImage)
        }


        // 语音

        window['getAudioPermissfun'] = (res) => {
            this.rec = new RecorderManger({}, this.recwave, this.getRecordResultCbFn)
        }
        const that = this
        document.addEventListener("visibilitychange", function() {
            let vState = document.visibilityState
            if (window.location.href.indexOf('PotentialuserMaintenance') >= 0) {
                if (vState === 'hidden') {  // 当页面由前端运行在后端时，出发此代码
                    if (that.rec) {
                        that.rec && that.rec.recorder.close();
                        that.rec = null
                        that.luyinFlag = false
                    }
                }
            }
        });


    }

}
</script>

<style scoped lang='less'>
.qz-wrap {
    padding: 44px 0 80px;
    box-sizing: border-box;
    height: 100vh;
    width: 100vw;
    background: #F2F2F2;
    overflow: auto;
    z-index: 999999;

    .content {
        padding: 0px 10px 64px;
        box-sizing: border-box;

    }
    .top-tel {
        width: 100%;
        line-height: 44px;
        background: #daebff;
        color: #0b7ffe ;
        font-size: 16px;
        text-align: center;
        font-weight: 700;
        a {
            color: #0b7ffe;
            text-decoration: none;
        }

        .dianhua013x {
            margin-right: 5px;
        }
    }
    .tab-change {
        display: flex;
        .tab-item {
            flex: 1;
            text-align: center;
            font-size: 14px;
            line-height: 38px;
            border-bottom: 1px solid #bbb;
            &.active{
                color: #0b7ffe;
                font-weight: 700;
                border-bottom: 1px solid #0b7ffe;
            }
        }
    }

}

.fadd-info {
    box-sizing: border-box;
    padding: 0 15px;
    h2 {
        img {
            width: 14px;
            height: 14px;
            display: inline-block;
            vertical-align: -2px;
            margin-right: 4px;
        }

        color: #3A3A3A;
        font-size: 14px;
    }

    .fa-tips {
        font-weight: normal;
        margin-top: 8px;
        font-size: 12px;
        color: red;
        line-height: 14px;
        display: block;
    }


    .add-item2 {
        line-height: 40px;
        text-align: center;
        position: relative;
        .button {
            width: 80px;
            height: 40px;
            text-align: center;
            color: #fff;
            display: inline-block;
            border-radius: 6px;
            background: #0b7fff;
            margin: 5px 10px;
            overflow: hidden;

            .iconfont {
                font-size: 18px;
            }

            .recwave-box {
                width: 80px;
                height: 48px;
            }
        }
    }

    .add-itemtel {
        padding-right: 25px;
        box-sizing: border-box;
        .dianhua {
            font-size: 20px !important;
            right: 0;
            top: 0px;
        }

        .mini {
        }
    }
    .add-itemdizhi {
        border-top:  1px solid #e5e4e4;
    }
    .add-item {
        height: 40px;
        line-height: 40px;
        //border-bottom: 1px solid #F0F0F0;
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        font-size: 12px;
        .zhangting {
            color: #bbb;
            position: absolute;
            font-size: 12px;
            right: 15px;
            bottom: 0;
        }
        .mini {
            color: #0b7ffe;
            position: absolute;
            font-size: 12px;
            right: 0;
            bottom: 0;
        }
        .dianhua {
            color: #0b7ffe;
            font-size: 20px !important;

        }
        .dianhuaa {
            font-size: 20px !important;
            outline: none;
            right: 0;
            top: 0px;
            text-decoration: 0;
            vertical-align: bottom;
        }
        .add-left {
            color: #232323;
            font-size: 14px;
            position: relative;
            .small-font {
                color: #999;
                font-size: 10px;
                position: absolute;
                left: 0;
                bottom: -16px;
                width: 140px;
            }
            .small-font2 {
                color: #999;
                font-size: 10px;
            }
        }

        .add-input {
            font-size: 12px;
            color: #232323;
            outline: none;
            text-align: right;
            line-height: 30px;
            &::placeholder {
                color: #BBBBBB;

            }
        }
        .add-input2 {
            padding-right: 120px;
        }

        .add-right {
            flex: 1;
            text-align: right;
            color: #232323;

            i {
                color: #CBCBCB;
                font-size: 14px;
                vertical-align: middle;
                display: inline-block;
            }
            .name {
                width: 150px;
                display: inline-block;
                vertical-align: bottom;
                white-space: nowrap;
                overflow: auto;
            }
        }
        .add-right2 {
            text-align: right;
            display: inline-block;
            color: #232323;

            i {
                color: #CBCBCB;
                font-size: 14px;
                vertical-align: middle;
                display: inline-block;
            }
            .luyin {
                font-size: 12px;
                color: #0b7ffe;
            }
        }
    }
}
.pop-textarea {
    width: 100%;
    resize: none;
    height: 100px;
    line-height: 22px;
    font-size: 12px;
    border: 1px solid #bbb;
    padding: 5px;
    box-sizing: border-box;
    border-radius: 4px;
    outline: none;
}
.finfo-item {
    margin-top: 14px;

    .fitem-p {
        width: 100%;
        line-height: 36px;
        height: 36px;
        background: linear-gradient(270deg, #FFFFFF 0%, #EBEFFF 50%, #FFFFFF 100%);
        border-radius: 4px;
        font-size: 14px;
        color: #3A3A3A;
        text-align: center;
        font-weight: bold;

        i {
            color: #8F8F8F;
            font-size: 22px;
            display: inline-block;
            margin-right: 4px;
            vertical-align: -2px;
        }
    }

    .fitem-list {
        display: flex;
    }

    .fitem-sub {
        margin-top: 14px;
        flex: 1;
        text-align: center;

        .sub-input {
            background-size: 47px 23px !important;
            width: 84px;
            text-align: center;
            height: 26px;
            border-radius: 12px;
            font-size: 14px;
            outline: none;
        }

        .sub-text {
            margin-top: 4px;
            color: #3A3A3A;
            font-size: 14px;
            display: block;
        }

        .textarea-info {
            width: 88%;
            height: 90px;
            resize: none;
            border: none;
            outline: none;
            background: rgb(249, 249, 249);
            font-size: 14px;
            padding: 6px 10px;
            overflow: auto;
        }
    }
}

.op-button-box {
    background-color: #fff;
    text-align: center;
    padding: 10px 12px;
    display: flex;
    justify-content: space-between;
    position: fixed;
    bottom: 0;
    left: 0;
    background: #fff;
    width: 100vw;
    box-sizing: border-box;
    box-shadow: 0px 0px 7px 5px rgba(0, 0, 0, 0.2);
    z-index: 333;


    .op-button {
        border-radius: 22px;
        height: 44px;
        line-height: 44px;
        flex: 1;
        font-size: 14px;
        outline: none;
        border: none;
        background: rgba(204, 204, 204, 1);
        color: #fff;
        margin: 0 10px;

        &.active {
            background: #1681FB;
            color: #fff;
        }
    }
}

.dialog-box {
    position: fixed;
    top: -300px;
    left: 50%;
    transform: translateX(-50%);
    width: 70vw;
    padding: 40px 20px;
    box-sizing: border-box;
    background: #f5f6f6;
    border-radius: 8px;
    text-align: center;
    z-index: 777;
    img {
        width: 85%;

        margin-bottom: 30px;
    }
    div {
        font-size: 12px;
        color: #ccc;
    }

}
.shibie {
    padding: 6px 4px;
    border-radius: 4px;
    background: #0b7fff;
    color: #fff;
    font-size: 11px;
    .luyin {
        color: #fff;


    }
    .zu {
        font-size: 12px !important;
    }
    &.grey-btn {
        background: #929292;
        color: #0A0A0A;
    }

}
.chooseDialog {
    position: fixed;
    z-index: 99;
    background: #fff;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    padding: 12px;
    border-radius: 8px;
    box-shadow: 2px 2px 6px 0px #747576;
    width: 260px;

    .title {
        font-size: 18px;
        line-height: 26px;
        color: #0b7fff;
        border-bottom: 2px solid #1681FB;
        overflow: hidden;
        .guanbi {
            float: right;
            color: red;
            font-size: 20px;
            line-height: 26px;

        }
    }

    .info {
        font-size: 14px;
        line-height: 22px;
        margin-top: 8px;
    }

    .btnlist {
        margin-top: 8px;
        display: unset !important;
        width: 100%;

        div {
            display: block;
            background: #1681FB;
            text-align: center;
            padding: 12px 15px;
            box-sizing: border-box;
            margin-top: 10px;
            border-radius: 5px;
            color: #fff;
            font-size: 15px;
            width: 100%;
        }
    }
}
.user-tag {
    .title {
        color: #0b7ffe;
        font-size: 16px;
        font-weight: 700;
    }
    .add-left {
        color: #232323;
        font-size: 12px;

    }
    .tasg {
        display: flex;
        .tag-item {
            font-size: 10px;
            background: #FFD9AE;
            color: #ff8b19;
            border-radius: 3px;
            margin: 0 2px;
            line-height: 20px;
            padding: 0 5px;


        }
    }
}
.title1 {
    margin-top: 10px;
    font-size: 14px;
    overflow: hidden;
    .showmore {
        font-size: 12px;
        float: right;
        color: #0b7fff;
        .youjiantou2 {
            vertical-align: text-bottom;
        }
    }
}
.table-box {
    padding: 12px 0;
    box-sizing: border-box;

    margin-bottom: 50px;
    .table-wrap{
        width: 100%;
        font-size: 12px;
        text-align: left;
    }
    .head-line {
        height: 30px;
        vertical-align: middle;
        line-height: 30px;
        background-color: #f3f4f9;
    }

    .type-css {
        padding-left: 30px;
    }
    thead {
        text-align: center;

    }
    .table-body tr {
        height: 30px;
        line-height: 30px;
        font-size: 14px;
        text-align: center;
        th {

        }

        td:first-child {
            color: #38393a;


        }
        td {
            color: #0f87ff;
            .youjiantou {
                font-size: 10px;
            }
        }
    }

    .table-body tr:nth-child(2n) {
        background-color: #f3f4f9;
    }
}
.right-pai {
    position: absolute;
    right: 30px;
    color: #0b7ffe;
}
.hasvideo {
    color: #0b7ffe !important;
}
.huaw {
    display: flex;
    width: 100%;
    box-sizing: border-box;
    .item {
        text-align: center;
        display: inline-block;
        width: calc(33% - 6px);
        margin: 6px 3px;
        background: #f2f2f2;
        padding: 6px;
        border-radius: 6px;
        box-sizing: border-box;
        .title,.value {
            font-size: 14px;
            line-height: 26px;
        }
    }
}
.content-item {
    margin-top: 10px;
    width: 100%;
    box-sizing: border-box;
    border-radius: 8px;
    margin-bottom: 20px;
    //box-shadow:4px 4px 12px 0px rgba(184,184,184,0.5);
    background: #fff;

    .first-title {
        border-radius: 8px 8px 0 0px;
        height: 36px;

        //background: linear-gradient(to bottom left,#D2E8FF ,#fff);

        .left-info {
            float: left;
            font-size: 15px;
            font-weight: 700;
            line-height: 30px;
            padding: 3px 10px;
            box-sizing: border-box;
            height: 36px;
            color: #257CDB;

            .wenhao1 {
                color: #bbb;
            }
            img {
                vertical-align: middle;
                display: inline-block;
                height: 22px;
            }

        }
        .left-info2 {
            position: relative;
            font-size: 12px;
            color: red;
            font-weight: 400;
            .wenhao {
                font-size: 14px;
                margin-left: 5px;
                vertical-align: bottom;
            }
            .dialog-info {
                position: absolute;
                font-size: 12px;
                width: calc(100vw - 40px);
                left: 10px;
                top: 36px;
                box-sizing: border-box;
                background: #e2f0ff;
                padding: 10px;
                border-radius: 7px;
                z-index: 99;
                line-height: 20px;
                color: #02337f;
                font-weight: 400;
                box-shadow: 2px 2px 6px 0px rgba(0, 0, 0, 0.2);

            }
            .dialog-info::after {
                content: '';
                position: absolute;
                width: 0;
                height: 0;
                border-style: solid;
                border-width:0 6px 12px 6px;
                border-color: transparent transparent  #e2f0ff transparent;
                top: -9px;
                left: 223px;
            }

        }
        .right-info {
            float: right;
            padding: 3px 10px 3px 0;
            line-height: 30px;
            color: #0b7fff;
            font-weight: 700;
            font-size: 14px;
            .iconfont {
                color: #257CDB !important;
            }
        }
    }
    .more-line {
        box-sizing: border-box;
        border-radius: 0px 0px 8px 8px;
        padding-bottom: 15px;
        overflow: hidden;
        width: 100%;
        .fadd-info {
            box-sizing: border-box;
            padding: 0 15px;

        }
    }
}
.content-item2 {
    background: #dcefff;
    padding: 10px;
    .reason {
        font-size: 12px;
        line-height: 24px;
        color: red;
        span {
            font-weight: 700;
            color: #000;
            display: block;
        }
    }

}
.mint-switch {
    transform: scale(0.8);
}
.img-box {
    overflow: hidden;
    position: relative;
    width: calc(33% - 10px);
    display: inline-block;
    margin: 0 5px;
    float: right;
    box-shadow: 2px 2px 6px 0px rgba(0, 0, 0, 0.2);

    .guanbi2 {
        position: absolute;
        right: 5px;
        top: 5px;
        color: red;
    }
    img {
        width: 100%;
        height: 100px;
        float: right;

    }
}
.reserve3-box {
    position: relative;
    .num {
        font-size: 12px;
        position: absolute;
        right: 5px;
        bottom: 8px;
        color: #bbb;
    }
    .info {
        display: inline-block;
        padding: 5px ;
        box-sizing: border-box;
        font-size: 12px;
        line-height: 20px;
        border: 1px solid #c6c5c5;
        width: 100%;
        border-radius: 6px;
    }
}
.big-img {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: #fff;
    z-index: 99999999999;
    overflow: auto;
    img {
        width: 100% !important;
        height: auto;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        box-shadow: 2px 2px 6px 0px rgba(0, 0, 0, 0.2);


    }
    .guanbi2 {
        position: absolute;
        right: 15px;
        top: 15px;
        font-size: 24px;
        color: red;
        z-index: 99999;
    }
}
/deep/ .mint-switch-input:checked + .mint-switch-core {
    border-color: #0b7fff !important;
    background-color: #0b7fff !important;
}
.shibie .zu {
    font-size: 10px !important;
    vertical-align: middle;
    margin-top: -3px;
}
.dislog {
    position: fixed;
    width: 100%;
    padding: 6px 0 0;
    bottom: 0;
    left: 0;
    z-index: 555;
    background: #fff;
    text-align: center;
    box-shadow: 0px 0px 7px 5px rgba(0, 0, 0, 0.02);
    margin-top: -40%;

    .title {
        font-size: 14px;
        font-weight: 700;
        line-height: 34px;

    }

    .reson-box {
        margin-top: 5px;

        .chooseone {
            width: 100%;
            font-size: 14px;
            line-height: 32px;
            position: relative;
            color: #929292;
            text-align: left;
            padding: 0 30px;
            box-sizing: border-box;

            .duihao1 {
                position: absolute;
                top: -1px;
                color: green;
            }
        }

        .yuan {
            width: 14px;
            height: 14px;
            display: inline-block;
            border-radius: 100%;
            vertical-align: middle;
            border: 1px solid #929292;
            margin-right: 5px;
            text-align: left;
        }
    }

    .reson-box2 {
        display: flex;
        margin-top: 25px;
        padding: 0 15px;
        margin-bottom: 25px;

        .chooseone {
            flex: 1;
            font-size: 16px;
            line-height: 32px;
            position: relative;
            line-height: 36px;
            background: #0b7ffe;
            color: #fff;
            border-radius: 6px;

            &:first-child {
                border-right: 1px solid #bbb;
            }

            &:last-child {
                color: #fff;
            }
        }
    }
}
.hide-box {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0,0,0,.5);
    z-index: 444;
}
.tip {
    color: red;
    position: absolute;
    font-size: 10px;
    right: 0;
    bottom: -3px;
    line-height: 15px;
}
.phone1 {
    padding-right: 100px;
}
.sj-lists {
    width: 100%;
    padding-left: 15px;
    box-sizing: border-box;
    font-size: 12px;
    line-height: 24px;
    margin-top: 10px;
    .item {
        position: relative;
        &::before {
            content: '';
            width: 5px !important;
            height: 5px !important;
            position: absolute;
            background: #fff;
            border: 3px solid #0b7ffe;
            border-radius: 100%;
            left: -15px;
            top: 6px;
            z-index: 222;
        }
        &::after {
            content: '';
            width: 1px !important;
            height: 90% !important;
            position: absolute;
            background: #bbb;
            left: -10px;
            top: 6px;
            z-index: 220;
            border: 0;

        }
    }

}
.sj-lists:first-child {
    &::after {
        content: '';
        width: 0px !important;
        height: 90% !important;
        position: absolute;
        background: #bbb;
        left: -10px;
        top: 6px;
        z-index: 222;
        border: 0;

    }
}
.add-left-red {
    color: red !important;
}
.guiji-item {
    padding: 10px ;
    box-sizing: border-box;
    border-bottom: 1px solid #d7d7d7;
    .guiji-content {
        padding-left: 18px;
        position: relative;
        &::after {
            content: '';
            width: 10px;
            height: 10px;
            border-radius: 100%;
            background: #4087f2;
            left: 0;
            top: 8px;
            position: absolute;
        }
    }
    .guiji-title {
        font-size: 14px;
        line-height: 30px;
        overflow: hidden;
        .left {
            float: left;
            color: #4087f2;
            font-weight: 700;
        }
        .right {
            float: right;
            font-size: 14px;
            color: #4087f2;
        }
    }
    .first-time {
        font-weight: 700;
        font-size: 14px;
        line-height: 28px;

    }
    .other-info {
        font-size: 12px;
        line-height: 28px;
        word-break: break-all;
        overflow: hidden;
        div {
            display: inline;
        }
        .tip-title {
            color: #888;
            width: 63px;
            float: left;
        }
        .tip-value {
            width: calc(100% - 66px);
            float: left;
        }
        .tip-title2 {
            color: #888;
            width: 90px;
            float: left;
        }
        .tip-value2 {
            width: calc(100% - 92px);
            float: left;
        }
        .tip-title3 {
            color: #888;
            width: 72px;
            float: left;
        }
    }
}
.list-key{
    font-size: 12px;
    color: #323232;

    .key-info{
        display: inline-block;
        border-radius: 3px;
        background: #ccdefc;
        color: #4087f2;
        border: 1px solid #4087f2;
        box-sizing: border-box;
        font-size: 12px;
        padding: 7px 5px;
        margin-right: 5px;
        margin-bottom: 5px;
    }
}
.hide-box {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, .5);
    z-index: 444;
}
.task-search-box {
    width: 100%;
    height: 360px;
    padding: 15px 0;
    overflow: hidden;
    background: #fff;
    position: fixed;
    left: 0;
    bottom: 0px;
    border: 1px solid #E5E5E5;
    z-index: 99999988;
    box-sizing: border-box;

    .top {
        width: 100%;
        height: 280px;
        overflow: hidden;
        display: flex;

        .lie {
            flex: 1;
            border-right: 1px solid #eee;
            height: 280px;
            box-sizing: border-box;
            padding: 0 5px;
            overflow: auto;

            .choose-item {
                font-size: 14px;
                padding: 0 15px 0 5px;
                color: #888;
                overflow: hidden;
                position: relative;

                margin: 5px 0;

                .font-wenzi {
                    width: calc(100% - 7px);
                    //overflow: hidden;
                    //white-space: nowrap;
                    //text-overflow: ellipsis;
                    display: inline-block;
                    line-height: 36px;
                    white-space: nowrap;
                    overflow: auto;
                }

                .font-wenzi3 {
                    width: 100%;
                }

                .youjiantou1 {
                    font-size: 16px;
                    line-height: 36px;
                    float: right;
                    width: 16px;
                    position: absolute;
                    right: 5px;
                    top: 50%;
                    margin-top: -2px;
                    transform: translateY(-50%);
                }
            }

            .active {
                background: #D9EBFF;
                color: #0b7fff;
                border-radius: 4px;

                .font-wenzi {
                    color: #0b7fff;

                }
            }
        }

        .lie:first-child {
            flex: unset;
            width: 33%;
        }

        .lie:last-child {
            border: none;
        }

    }

    .bottom {
        width: 100%;
        margin-top: 11px;
        text-align: center;
        display: flex;
        div {
            flex: 1;
        }

        .chongzhi {
            display: inline-block;
            margin: 5px 25px;
            line-height: 32px;
            border-radius: 32px;
            background: #fff;
            border: 2px solid #0b7fff;
            box-sizing: border-box;
            color: #0b7fff;
            font-size: 14px;
        }

        .queren {
            display: inline-block;
            margin: 5px 25px;
            line-height: 36px;
            border-radius: 36px;
            background: #0b7fff;
            color: #fff;
            font-size: 14px;
        }
    }
}

</style>
