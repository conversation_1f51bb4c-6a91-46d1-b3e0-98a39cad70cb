<template>
    <div class='td-wrap1'>
        <Header tsTitleTxt="融合服务直办(新)" backType='custom' @emGoPrev='goback' />
        <div class="chooseDialog" v-show='ocrChoose'>
            <div class="title">提示</div>
            <div class="info">请选择拍摄类型</div>
            <div class="btnlist">
                <!--                <div @click="showOCRCamera('photo')">拍照片</div>-->
                <div @click="showOCRCamera('video')">拍视频</div>
                <div @click="showOCRCamera('album')">相册选择</div>
            </div>
        </div>
        <div class="chooseDialogBgc" v-if="ocrChoose" @click="ocrChoose = false"></div>

        <div class='content'>
            <div class='content-item'>
                <div class='first-title'>
                    <div class='left-info'>
                        <img src="../../assets/img/sjlr3.png"/>
                        基础信息
                    </div>
                </div>
                <div class='more-line'>
                    <div class='fadd-info'>
                        <ul class='family-add'>
                            <li class='add-item add-item-tel'>
                                <span class="add-left">用户号码<span style='color: red'>*</span></span>
                                <input type="tel" :maxlength="11" v-model="telNum" @input="inputBusiTel" placeholder="请输入" class="add-input"  />
                                <span class='tip' v-show='telNum && telNum.length == 11 && busiTelIsJSCMCC'>存量号码，请重新输入</span>

                            </li>
                            <li class='add-item'>
                                <span class='add-left'>用户姓名<span style='color: red'>*</span></span>
                                <input v-model='busiUserName' placeholder='可仅输入姓' class='add-input' maxlength='10' />
                            </li>
                            <li class='add-item'>
                                <span class='add-left'>归属区县<span style='color: red'>*</span></span>
                                <span class='add-right' v-show='countyIdList.length > 1' @click='changeCounty'>{{county.countyId ? county.countyName : '请选择'}} <span class='iconfont jiantou-copy-copy'></span></span>
                                <span class='add-right' v-show='countyIdList.length == 1'>{{ county.countyName }} </span>
                            </li>
                            <li class='add-item'>
                                <span class='add-left'>商机跟进人<span style='color: red'>*</span></span>
                                <span class='add-right' @click='choosePer'>{{chooseUserInfo.execName ? chooseUserInfo.execName : '请选择'}} <span class='iconfont jiantou-copy-copy'></span></span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class='content-item'>
                <div class='first-title'>
                    <div class='left-info'>
                        <img src="../../assets/img/sjlr3.png"/>
                        结单情况
                    </div>
                </div>
                <div class='more-line'>
                    <div class='fadd-info'>
                        <ul class='family-add'>
                            <li class='add-item'>
                                <span class='add-left'>结单类型 <span class='red'>*</span></span>
                                <div class='reserve3-box'>
                                    <div class='btn-item'  @click='get180DaysResult(1)' :class='overType == 1? "active" : ""'>新办卡</div>
                                    <div class='btn-item'  @click='get180DaysResult(2)' :class='overType == 2? "active" : ""'>归位</div>
                                </div>
                            </li>
                            <!--新办卡-->
                            <div v-if='overType == 1'>
                                <li class='add-item' >
                                    <span class='add-left'>短信验证码 <span class='red' v-show='!nameAndTelFlg'>*</span></span>

                                    <div v-show='isMessageVerify != "true"'>
                                        <input v-model='yzmCode' style='max-width: 144px;' placeholder='输入接收到的验证码' class='add-input' maxlength='4' />
                                        <div class='add-right2'>
                                            <span class='shibie' :class='timer? "grey-btn" : "" ' :disabled='countdown > 0' @click='startCountdown()'>  {{ countdown > 0 ? `${countdown} 秒后重试` : '发送验证码'}}</span>
                                        </div>
                                    </div>
                                    <div v-show='isMessageVerify == "true"'>
                                        <span class='iconfont checkboxround1'>已验证</span>
                                    </div>
                                </li>
                                <li class='add-item'>
                                    <span class='add-left'>新卡号码 <span class='red'>*</span></span>
                                    <input v-model='newMsisdn' @input='inputNewTel' placeholder='请输入新卡号码' class='add-input' :maxlength='11' />
                                </li>
                                <li class='add-item'>
                                    <span class='add-left'>掌厅截图  <span class='red' v-show='!nameAndTelFlg'>*</span></span>
                                    <span class='red-font'>请上传含姓名、号码后四位的商机号码掌厅截图，图片清晰大小不超过1M</span>
                                    <span class='add-right add-right4'><i class='iconfont xiangji' @click='showOCRCamera0'></i></span>
                                </li>
                                <div class='img-boxs' v-show='OCRimgs.length > 0'>
                                    <div class='img-box' v-for='(item,idx) in OCRimgs'>
                                        <span class='iconfont guanbi2' @click='clearOCRimg(idx)'></span>
                                        <img v-if='item.img && item.img.indexOf("blob") < 0' :src="'data:image/jpeg;base64,'+ item.img" />
                                        <img v-if='item.img && item.img.indexOf("blob") >= 0' :src="item.img" />
                                    </div>
                                </div>
                                <li class='add-item'>
                                    <span class='add-left'>掌厅视频  </span>
                                    <span class='add-right add-right4'>含姓名、号码的商机号掌厅视频<i class='iconfont xiangji' @click='showOCRCamera1'></i></span>
                                </li>
                                <div class='img-boxs2' v-show='OCRVideo.length > 0'>
                                    <div class='img-box' v-for='(item,idx) in OCRVideo'>
                                        <span class='iconfont guanbi2' @click='clearOCRVideo(idx)'></span>
                                        <video  controls v-if='item && item.video.indexOf("blob") < 0' >
                                            <source type="video/mp4" :src="'data:video/mp4;base64,'+ item.video"/>
                                        </video>
                                        <video controls v-if='item && item.video.indexOf("blob") >= 0' >
                                            <source type="video/mp4" :src="item.video"/>
                                        </video>
                                    </div>
                                </div>
                                <li class='add-item' v-if='OCRname'>
                                    <span class='add-left'>用户姓</span>
                                    <span class='add-right'>{{ OCRname }}</span>
                                </li>
                                <li class='add-item' v-if='OCRtel'>
                                    <span class='add-left'>商机号后四位</span>
                                    <span class='add-right'>{{ OCRtel }}</span>
                                </li>
                            </div>
                        </ul>
                    </div>
                </div>
            </div>

        </div>


        <ZBperson v-if='personFlg && orgId' :headerName="'商机跟进人选择'" :orgaId="orgId" showExecute='1'  @emCloseInit="closeExecute" @emSubmit="getExecute"/>
        <div class='op-button-box' >
            <button class='op-button active' @click='submitForm(2)'>结 单</button>
        </div>
    </div>
</template>

<script>
import Header from 'components/common/Header.vue'
import ClientJs from '@/base/clientjs'
import Storage from '@/base/storage'
import LCvideoPhoto from './LC-videoPhoto.vue'
import XHvideoPhoto from './XH-videoPhoto.vue'
import ImageObj from '@/base/request/ImageServReq';
import {dateFormat} from '@/base/utils'
import ZBperson from './ZBperson.vue'
import NlDropdown from 'components/common/NlDropdown/dropdown.js'
import personchoose from './personchoose.vue'

export default {
    OCRname: 'TDoverForm',
    components: { personchoose, ZBperson, Header, LCvideoPhoto, XHvideoPhoto },
    data() {
        return {
            ocrChoose: false,// 掌厅OCR 选择
            OCRVideo: [],// 掌厅OCR 视频
            OCRimgs: [], // 掌厅OCR 图片
            OCRname: '', // 掌厅OCR 姓名
            OCRtel: '', // 掌厅OCR 手机号
            mItem: {},

            overType: '', // 结单类型

            telNum: '',
            newMsisdn: '',// 新卡号码/低效
            busiTelIsJSCMCC: false ,// 商机号是否江苏移动
            busiUserName: '', //用户姓名
            days15flg: false,

            followSituation: '', // 持续跟进情况

            showLicaiFlg: false, // 理财拍照组件
            LCphotos: '', // 理财上传资料
            LCvideos: '', // 理财上传资料
            priceBefore: '', // 理财前套餐价值
            priceAfter: '',// 理财后套餐价值


            showXiaohuFlg: false, //销户拍照组件
            XHphotos: '', // 销户上传资料
            XHvideos: '', // 销户上传资料


            faileReson: '', // 失败原因

            remark: '', // 备注
            homeId: '',

            userInfo: {},
            userApplyDate: '', // 新卡开户time
            userApplyName: '', // 新卡开户time
            userId: '',// 新卡用户id
            userApplyTel: '', // 新卡开户time
            isJSCMCC: false ,// 新卡是否江苏移动
            accountId: '',
            isAllowExist: false,



            createImg: [],
            flg180Days: false,
            orgId: '',
            personFlg: false,
            countyIdList: [],
            county: {},
            chooseUserInfo: {},

            todayDate: '',
            nameAndTelFlg: false,


            isMessageVerify: false, // 是否验证过
            timer: '',// 验证码倒计时
            countdown: '',// 验证码倒计时
            yzmCode: '', // 验证码
            handler: {}, // 新卡办理人
        }
    },
    mounted() {
        this.userInfo = Storage.session.get('userInfo')
        this.getConstrolCounntyId()
        this.getTodayDate()
        // 掌厅
        window['getOcrPicCb0'] = (info) => {
            if (info.fileImage) {
                if (this.OCRimgs.length < 3) {
                    this.OCRfun(info.fileImage)
                } else {
                    this.$toast('最多拍摄三张')
                }
            }
        }

        // 录像
        window['getOcrVideoCb'] = (res) => {
            if (typeof res == "string") {
                var info = res.replace(/\+/g, "%2B");
                res = JSON.parse(info.replace(new RegExp('\n', 'g'), '\\n'));
            }
            if (res.videoB) {
                this.ocrChoose = false
                this.uploadVideo(res.videoB)
            }
        };
        // 相册
        window['getOcrAlbum'] = (res) => {
            if (typeof res == "string") {
                var info = res.replace(/\+/g, "%2B");
                res = JSON.parse(info.replace(new RegExp('\n', 'g'), '\\n'));
            }
            if (res.imageB) {
                this.$toast('请选择视频上传')
                this.ocrChoose = false

            }
            if (res.videoB) {
                this.ocrChoose = false
                this.uploadVideo(res.videoB)
            }
        };
    },
    methods: {
        goback() {
            history.go(-1)
        },
        maskPhoneNumber(phoneNumber) {
            if (!phoneNumber) return
            phoneNumber = phoneNumber + ''
            var pattern = /[\u4e00-\u9fa5]/
            if (pattern.test(phoneNumber)) {
                return phoneNumber

            } else {
                if (phoneNumber.length == 11) {
                    return phoneNumber.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
                } else {
                    return phoneNumber
                }
            }
        },
        getConstrolCounntyId(){
            let param = {
                'telNum': Storage.session.get('userInfo').servNumber,
            }
            let url = `/xsb/gridCenter/familyDiagnosis/h5QryIsCountyManager`
            this.$http.post(url, param).then((res) => {
                if (res.data.retCode == 0) {
                    this.countyIdList = res.data.data
                    if(this.countyIdList.length == 1) {
                        this.county = this.countyIdList[0]
                        this.orgId = this.countyIdList[0].countyId
                        console.log('this.orgId',this.orgId)
                    }
                }
            })
        },
        // 客户来源
        changeCounty(){
            var self = this
            for (let i = 0 ;i < this.countyIdList.length; i++) {
                this.countyIdList[i].id = this.countyIdList[i].countyId
                this.countyIdList[i].label = this.countyIdList[i].countyName
            }
            console.log('this.countyIdList',this.countyIdList)
            NlDropdown({
                confirmBtn: false,
                datalist: this.countyIdList
            },function (retVal) {
                self.county = retVal
                self.orgId = retVal.id
            });
        },
        choosePer() {
            if(!this.orgId) {
                this.$toast('请选择归属区县')
                return
            }
            this.personFlg = true
        },
        // 指派
        closeExecute(){
            this.personFlg = false
        },
        getExecute(item) {
            console.log(item)
            this.chooseUserInfo = JSON.parse(JSON.stringify(item))
            console.log('this.chooseUserInfo',this.chooseUserInfo)

            this.personFlg = false

            if (this.overType == 1 && this.newMsisdn) {
                this.get3Add1Result(this.overType)
            }
            if (this.overType == 2 ) {
                this.get3Add1Result(this.overType)
            }
        },
        clearOCRimg(idx) {
            this.OCRimgs.splice(idx, 1)
            if (this.OCRimgs.length > 0) {
                this.OCRname = ''
                this.OCRtel = ''
                for (let i = 0; i < this.OCRimgs.length; i++) {
                    if (this.OCRimgs[i].userName) {
                        this.OCRname = this.OCRimgs[i].userName.charAt(0)
                    }
                    if (this.OCRimgs[i].telnum) {
                        this.OCRtel = this.OCRimgs[i].telnum.slice(-4)
                    }
                }
            } else {
                this.OCRname = ''
                this.OCRtel = ''
            }

        },
        clearOCRVideo(idx) {
            this.OCRVideo.splice(idx, 1)
        },
        uploadVideo(val) {
            let param = {
                videoStr: val,
                unEncrpt: true,
            }
            this.$http.post('/xsb/ability/videoUpload/h5QcVideoSub', param).then((res) => {
                if (res.data.retCode == '0') {
                    let obj = {
                        video: val,
                        videoName: res.data.data.videoFileName,
                    }
                    console.log('videoName',obj)
                    this.OCRVideo.push(obj)
                } else {
                    this.$toast(res.data.retMsg)
                }
            })
        },
        startCountdown() {
            // 验证码倒计时
            if (this.timer) return
            this.$messagebox({
                title: '温馨提示',
                showCancelButton: true,
                message: '确定给' + `<span style="color: #0b7ffe">${this.telNum}</span>` + '发送验证码短信吗？',
                confirmButtonText: '确定'
            }).then((ressend) => {
                if (ressend === 'confirm') {
                    this.sendSms()
                    // 这里可以添加点击按钮后需要执行的逻辑
                }
            })
        },
        sendSms() {
            // 发送验证码接口
            let url = '/xsb/gridCenter/sendCode/h5SendVerification'
            let param = {
                'crmId': Storage.session.get('userInfo').crmId,
                'region': Storage.session.get('userInfo').region,
                'telnum': this.telNum,
            }
            this.$http.post(url, param).then((res) => {
                console.log('发送验证码接口===>res:', res)
                if (res.data.retCode == 0) {
                    this.countdown = 60 // 设置倒计时时间，如60秒
                    this.timer = setInterval(() => {
                        if (this.countdown > 0) {
                            this.countdown -= 1
                        } else {
                            clearInterval(this.timer)
                            this.timer = null
                        }
                    }, 1000)
                    this.$toast('验证码发送成功')
                    return
                } else {
                    this.$alert(res.data.retMsg || '验证码发送失败')
                }
            }).catch((response) => {
                this.$alert(response || '验证码发送失败')
            })
        },
        OCRfun(val) {
            let param2 = {
                photoStr: val,
                unEncrpt: true,
                comeFrom: 'familyDiagnosis'
            }
            console.log('param2---',param2)
            this.ocrChoose = false
            this.$http.post('/xsb/personBusiness/queryCalculate/h5SubmitPhoto', param2).then((res) => {
                let param1 = {
                    'crmId': Storage.session.get('userInfo').crmId,
                    'region': Storage.session.get('userInfo').region,
                    "operatorTel": Storage.session.get('userInfo').servNumber,  //操作人手机号
                    "images":val,
                    "imageName" : res.data.data,
                    "streamSeq": new Date().getTime(),
                    "params": JSON.stringify([
                        {
                            "key": "busiTel",
                            "desc": "你需要分析提供的图片中的内容并提取其中的手机号码信息。图片形式可能为页面截图也可能为拍摄的图片，号码中间4位可能显示为*，存在多个手机号的时候只需要返回一个手机号，请优先给出最先识别出的那一个号码或者“主号码”、“用户号码”、“我的号码”、“联系电话”等字样后的那一个号码。请以 11 位手机号码的格式输出结果，若图片中不存在手机号码，则返回空字符串。注意请直接返回字符串，不要添加任何其他文本或说明。",
                            "exp": "13901582505"
                        },
                        {
                            "key": "busiUserName",
                            "desc": "你需要分析提供的图片中的内容并提取其中的姓名信息。图片形式可能为页面截图也可能为拍摄的图片，姓名中可能包含1个或多个*，存在多个姓名的时候只需要返回最先识别出来的那一个姓名。请以字符串格式输出结果，若图片中不存在姓名，则返回空字符串。注意请直接返回字符串，不要添加任何其他文本或说明。,'战'也可以是一个人的姓",
                            "exp": "王涛"
                        },
                        {
                            "key": "bandInstall",
                            "desc": "你需要分析提供的图片中的内容并提取其中的地址信息。图片形式可能为页面截图也可能为拍摄的图片，地址中可能包含1个或多个*，存在多个地址的时候只需要返回一个地址，请优先给出最先识别出的那一个地址。请以字符串格式输出结果，例如：中海大厦15A，若图片中不存在地址，则返回空字符串。注意请直接返回字符串，不要添加任何其他文本或说明。",
                            "exp": "中海大厦15A"
                        },
                        {
                            "key": "monthConsum",
                            "desc": "你需要分析提供的图片中的内容并提取其中的金额信息。图片形式可能为页面截图也可能为拍摄的图片，金额类型一般为包含两位小数的数字，存在多个金额的时候只需要最先识别出来的那一个金额信息。请以字符串格式输出结果，例如：157.00，若图片中不存在金额，则返回空字符串。注意请直接返回字符串，不要添加任何其他文本或说明。",
                            "exp": "157.00"
                        }
                    ]),
                    "prompt": "分析文本并提取以下信息如果没有则传空",
                    "modelName": "QWEN2.5-14B",
                    'unEncrpt' : true,
                    "identifySource": 1
                }
                console.log('ocr---', param1)
                let url1 = `/xsb/gridCenter/familyDiagnosis/h5AiIdentify`
                this.$http.post(url1, param1).then((res1) => {
                    if (res.data.data) {
                        if (res1.data.retCode != 0) {
                            let obj = {
                                img: val,
                                imageName: res.data.data,
                                userName: '',
                                telnum: ''
                            }
                            this.OCRimgs.push(obj)
                            this.$toast('图片识别异常，请稍后重试')
                            return
                        }
                        if (res1.data.data) {
                            if (res1.data.data.data.busiUserName || res1.data.data.data.busiTel) {
                                let obj = {
                                    img: val,
                                    imageName: res.data.data,
                                    userName: res1.data.data.data.busiUserName,
                                    telnum: res1.data.data.data.busiTel
                                }
                                this.OCRimgs.push(obj)
                                console.log('this.OCRimgs', this.OCRimgs)
                                this.OCRname = ''
                                this.OCRtel = ''
                                for (let i = 0; i < this.OCRimgs.length; i++) {
                                    if (this.OCRimgs[i].userName) {
                                        this.OCRname = this.OCRimgs[i].userName.charAt(0)
                                    }
                                    if (this.OCRimgs[i].telnum) {
                                        this.OCRtel = this.OCRimgs[i].telnum.slice(-4)
                                    }
                                }
                                if (process.env.NODE_ENV === 'development') {
                                    // 开发环境
                                    this.$toast('检测到当前为开发环境,修改ocr识别信息')
                                    this.OCRname = '王'
                                    this.OCRtel = ''
                                }
                            } else {
                                let obj = {
                                    img: val,
                                    imageName: res.data.data,
                                    userName: '',
                                    telnum: ''
                                }
                                this.OCRimgs.push(obj)
                                this.$toast('当前图片未识别到有效信息，请重新上传含姓名和号码后四位信息的商机号码掌厅截图')
                            }
                        } else {
                            let obj = {
                                img: val,
                                imageName: res.data.data,
                                userName: '',
                                telnum: ''
                            }
                            this.OCRimgs.push(obj)
                            this.$toast('图片识别异常，请稍后重试')
                        }


                    }
                })
            })

        },
        showOCRCameraChoose() {
            let num1 = this.OCRimgs.length
            let num2 = this.OCRVideo.length
            if (num1 + num2 == 3) {
                this.$toast('图片和视频最多允许三张')
                return
            }
            this.ocrChoose = true
        },
        showOCRCamera0() {
            ClientJs.openCameraAndShow('1', 'getOcrPicCb0')
        },
        showOCRCamera1() {
            if(this.OCRVideo.length == 5) {
                this.$toast('最多拍5个视频')
                return
            }
            this.ocrChoose = true
        },
        showOCRCamera(type) {
            if (type == 'photo') {
                ClientJs.openCameraOne('getOcrPicCb');
            } else if (type == 'video') {
                ClientJs.openCameraVideo("2", "getOcrVideoCb");
            } else {
                ClientJs.openCameraVideo("3", "getOcrAlbum");
            }
        },
        changeOverType(val) {
            // 结单类型
            this.overType = val
            if (val == 2) {
                this.get3Add1Result(2)
            }
            // 跟进信息清空
            this.followSituation = ''
            this.LCphotos = ''
            this.LCphotos = ''
            this.XHphotos = ''
            this.XHvideos = ''
            this.priceAfter = ''
            this.priceBefore = ''
            this.remark = ''
            this.faileReson = ''
            this.OCRimgs = []
            this.OCRname = ''
            this.OCRtel = ''
            this.newMsisdn = ''

            if (val == 1 || val == 3) {
                this.OCRimgs = this.createImg
                this.OCRname = ''
                this.OCRtel = ''
                for (let i = 0; i < this.OCRimgs.length; i++) {
                    if (this.OCRimgs[i].userName) {
                        this.OCRname = this.OCRimgs[i].userName.charAt(0)
                    }
                    if (this.OCRimgs[i].telnum) {
                        this.OCRtel = this.OCRimgs[i].telnum.slice(-4)
                    }
                }
            }

        },
        getWhiteFlg() {
            // 拍照/验证码白名单
            let url1 = `/xsb/personBusiness/gridSand/h5GridMarketSwitch`
            let param = {
                switchType: 'potentialuser_maintenance_flag'
            }
            console.log('白名单===>',param)
            this.$http.post(url1,param).then((res) => {
                if(res.data.retCode == 0) {
                    if (res.data.data == 'true') {
                        this.nameAndTelFlg = false
                    } else {
                        this.nameAndTelFlg = true
                    }
                } else {
                    this.nameAndTelFlg = true
                }
            })
        },
        safeDateParse(dateStr) {
            // 替换空格为'T'（转ISO 8601格式）
            const isoStr = dateStr.replace(' ', 'T') + 'Z';
            return new Date(isoStr).getTime(); // 兼容所有浏览器
        },
        getTodayDate() {
            let url1 = `/xsb/gridCenter/familyDiagnosis/h5GetCurrentTime`
            this.$http.post(url1).then((res) => {
                if(res.data.retCode == 0) {
                    this.todayDate = res.data.data
                    // this.todayDate = this.safeDateParse(this.todayDate)
                    console.log('this.todayDate',this.todayDate)
                } else {
                    this.todayDate = new Date().getTime()
                }
            })

        },
        changeDate(d) {
            const year = d.getFullYear();
            const month = String(d.getMonth() + 1).padStart(2, '0');
            const day = String(d.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        },
        getPast3Add1Month() {
            const today = new Date(this.todayDate);
            const currentDay = today.getDate();
            const currenyear = today.getFullYear();
            const currenmonth = today.getMonth() + 1; // 月份从0开始，所以要加1
            const currenday = today.getDate();
            let currentTime = currenyear + '-' + currenmonth + '-' + currenday + ' 23:59:59';
            // 获取三个月前的日期
            const threeMonthsAgo = new Date(today);
            threeMonthsAgo.setMonth(today.getMonth() - 3);

            // 处理跨年情况
            if (threeMonthsAgo.getMonth() > today.getMonth()) {
                threeMonthsAgo.setFullYear(today.getFullYear() - 1);
            }

            // 获取三个月前当月的1号
            const firstDayOfMonth = new Date(threeMonthsAgo);
            firstDayOfMonth.setDate(1);

            // 获取三个月前的同一天（如果该月没有这一天则取最后一天）
            const sameDayOfMonth = new Date(threeMonthsAgo);
            const daysInMonth = new Date(
                sameDayOfMonth.getFullYear(),
                sameDayOfMonth.getMonth() + 1,
                0
            ).getDate();

            sameDayOfMonth.setDate(Math.min(currentDay, daysInMonth));

            let firstTime = firstDayOfMonth.toISOString().split('T')[0] + ' 00:00:00';
            return [
                firstTime, // 三个月前1号
                currentTime   // 三个月前同一天
            ];
        },

        async get3Add1Result(val,banliren,times) {
            // val---1新版卡 2归位
            this.days15flg = false
            if (val == 1 && !this.newMsisdn) {
                this.$toast('请输入新卡号码')
                return
            }
            if (val == 2 && !this.telNum) {
                this.$toast('请输入商机号码')
                return
            }
            if (!this.chooseUserInfo.execMsisdn) {
                this.$toast('请选择商机跟进人')
                return
            }
            console.log('商机跟进人',this.chooseUserInfo)
            let dateArr = this.getPast3Add1Month()
            let param1 = {
                "crmId": Storage.session.get('userInfo').crmId,
                "region": Storage.session.get('userInfo').region,
                "serviceNumber": val == 1 ? this.newMsisdn : this.telNum, // 用户号码，11位手机号码，例如: "17823001821"
                "begin": dateArr[0], // 开始时间，格式：yyyy-mm-dd hh24:mi:ss
                "end": dateArr[1], // 结束时间，格式：yyyy-mm-dd hh24:mi:ss
                "busiCode": val == 1 ? '' : "CreateSubscriber", // 受理类型，见附录1，例如："CreateSubscriber"，如果不传则查询所有受理类型
                "operId": this.chooseUserInfo.execMsisdn, // 跟进人手机号
                "modifyTel": this.chooseUserInfo.execMsisdn, // 跟进人手机号
                "status": "OS90", // 受理状态，OS20：处理中；OS90：完成；不传该参数表示查询所有状态
                "beId": Storage.session.get('userInfo').region, // 地区编码，取自字典组OC.AREA.TYPE，见附录4
                "incBackProcess": "Y", // 是否包含后台业务，Y：包含；N：不包含
                "incRollback": "", // 是否显示回退业务，Y：是；N：否
                "relaRequest": "", // 是否查询相关受理，Y：是；N：否
                "noPrintInvoice": "", // 是否查询未打印发票业务，Y：是；N：否
                "queryLogDetail": "", // 是否包含下级单位的业务，Y：是；N：否
                "beginRowNum": "0", // 开始行数，首次查询填0，后续填(当前页数-1)*每页行数
                "fetchRowNum": "10", // 每页显示行数，固定值，翻页查询时不能变
                "curPage": "1", // 当前页，首次查询填1
                "totalRowNum": "0", // 总行数，首次查询填0，第二次以后填返回的总记录数
            }
            if(process.env.NODE_ENV === 'development'){
                param1.operId = '13901582505'
                param1.modifyTel = '13901582505'
            }
            // 查新版卡-办理人
            if (banliren == 'newMsisdn') {
                param1.serviceNumber = this.newMsisdn
            }
            // 查商机号-办理人
            if (banliren == 'busiTel') {
                param1.serviceNumber = this.telNum
            }
            if (times == 1) {
                param1.busiCode = 'CreateSubscriber'
            }
            if (times == 2) {
                param1.busiCode = 'SupplementInfo'
            }

            console.log('15---h5GetOrderLogList',param1)
            let url1 = `/xsb/gridCenter/familyDiagnosis/h5GetOrderLogList`
            return this.$http.post(url1, param1)
        },
        get180DaysResult(val) {
            this.flg180Days = false
            if (!this.telNum) {
                this.$toast('请输入商机号码')
                return
            }
            if (this.busiTelIsJSCMCC && this.telNum) {
                this.$toast('请输入正确的商机号码')
                return
            }
            this.changeOverType(val)
        },


        changeFollowType(val) {
            // 持续跟进情况
            if (val == this.followSituation) {
                this.followSituation = ''
            } else {
                this.followSituation = val
            }
            this.LCphotos = ''
            this.LCphotos = ''
            this.XHphotos = ''
            this.XHvideos = ''
            this.priceAfter = ''
            this.priceBefore = ''
            this.remark = ''

        },
        changeFaileReson(val) {
            // 失败原因
            this.faileReson = val
            this.remark = ''

        },
        //打开理财拍摄组件
        openLicaiCamera() {
            Storage.session.set('afterOrbeforePhotoName', '')
            Storage.session.set('afterOrbeforeVideoName', '')
            this.showLicaiFlg = true
        },
        // 关闭理财拍摄组件
        getLicaiClose() {
            this.showLicaiFlg = false
            this.LCphotos = Storage.session.get('afterOrbeforePhotoName') ? Storage.session.get('afterOrbeforePhotoName') : '' // 理财前照片
            this.LCvideos = Storage.session.get('afterOrbeforeVideoName') ? Storage.session.get('afterOrbeforeVideoName') : ''   // 理财后照片
            console.log('LCphotos---', this.LCphotos)
            console.log('LCvideos---', this.LCvideos)
        },
        //打开销户拍摄组件
        openXHcamera() {
            Storage.session.set('videoHandcliendYw', '')
            Storage.session.set('photosHandcliendYw', '')
            this.showXiaohuFlg = true//打开拍照组件
        },
        // 关闭销户拍摄组件
        getXHClose() {
            this.showXiaohuFlg = false
            this.XHphotos = Storage.session.get('ywUserInfoPhoto') ? Storage.session.get('ywUserInfoPhoto') : ''   // 销户-照片
            this.XHvideos = Storage.session.get('ywUserInfoVideo') ? Storage.session.get('ywUserInfoVideo') : ''   // 销户-视频
        },

        getRegTime() {
            // 新卡-------获取新卡开户信息
            this.userApplyDate = ''
            this.userApplyName = ''
            let url = `/xsb/gridCenter/familyDiagnosis/h5QryBusiCountyId` + `?regionId=${this.userInfo.region}&operId=${this.userInfo.staffId}&telnum=${this.newMsisdn}`;
            this.$http.post(url).then((res) => {
                console.log('res-getRegTime',res)
                this.userApplyDate = res.data.data.userApplyDate
                this.userId = res.data.data.userId
                this.userApplyName = res.data.data.userName.charAt(0)
                if (process.env.NODE_ENV === 'development') {
                    // 开发环境
                    this.$toast('检测到当前为开发环境,userApplyDate=>')
                    this.userApplyDate = '20250901101010'
                }
                // this.userApplyTel = res.data.data.userCounty.slice(-4)
                if (res.data.data.custIcType == 32 || res.data.data.custIcType == 99) {
                    this.isCust_ic_type = true
                } else {
                    this.isCust_ic_type = false
                }
            })
        },
        isMoreThan3add1Month(inputDateStr) {
            if (!inputDateStr) return false;
            // 获取当前日期
            const today = new Date(this.todayDate);
            today.setHours(0, 0, 0, 0);

            // 获取三个月前的1号
            const threeMonthsAgo = new Date(today);
            threeMonthsAgo.setMonth(today.getMonth() - 3);
            threeMonthsAgo.setDate(1);
            threeMonthsAgo.setHours(0, 0, 0, 0);

            // 处理跨年情况
            if (threeMonthsAgo.getMonth() > today.getMonth()) {
                threeMonthsAgo.setFullYear(today.getFullYear() - 1);
            }

            // 解析输入日期
            const year = parseInt(inputDateStr.substring(0, 4));
            const month = parseInt(inputDateStr.substring(4, 6)) - 1;
            const day = parseInt(inputDateStr.substring(6, 8));
            const inputDate = new Date(year, month, day);
            inputDate.setHours(0, 0, 0, 0);

            // 判断是否在范围内
            let flg = inputDate >= threeMonthsAgo && inputDate <= today;
            return !flg
        },
        inputBusiTel() {
            // 商机号输入事件
            this.busiTelIsJSCMCC = false
            this.flg180Days = false
            this.overType = ''
            if (this.telNum) {
                let str = this.telNum + ''
                if (str['0'] == '0') {
                    if (str.length >= 11) {
                        this.$toast('请输入正确的手机号码')
                        return
                    }
                } else {
                    if (str.length >= 11) {
                        let urlnew = `/xsb/gridCenter/familyDiagnosis/h5QryUserCityByBoss`
                        let paramenw ={
                            "crmId": this.userInfo.crmId,
                            "region":  this.userInfo.region,
                            "servicenumber":this.telNum,  // 手机号码
                            "is_show_csp": "1"              // 是否展示运营商   0：否   1：是
                        }
                        // 判断归属
                        this.$http.post(urlnew, paramenw).then((resTel) => {
                            if(process.env.NODE_ENV === 'development'){
                                // 开发环境
                                this.$toast('检测到当前为开发环境,已修改商机号为csp_type=4;is_np_number=1,不影响生产')
                                resTel.data.data.csp_type = 4
                                resTel.data.data.is_np_number = 1
                            }

                            if (resTel.data.retCode == '0') {
                                if (resTel.data.data.province_name.indexOf('江苏') >= 0) {
                                    if(resTel.data.data.csp_type == 1) {
                                        if(resTel.data.data.is_np_number == '0') {
                                            this.mItem.oregionId = '1' // 本网
                                            this.mItem.operaId = resTel.data.data.csp_type
                                            this.busiTelIsJSCMCC = true
                                            this.$alert('存量号码，请重新输入')
                                        } else {
                                            this.userApplyDate = ''
                                            this.userApplyName = ''
                                            let url = `/xsb/gridCenter/familyDiagnosis/h5QryBusiCountyId` + `?regionId=${this.userInfo.region}&operId=${this.userInfo.staffId}&telnum=${this.telNum}`;
                                            this.$http.post(url).then((res) => {
                                                let userApplyDate = res.data.data.userApplyDate

                                                // userApplyDate = '20250607120641'
                                                if(this.isMoreThan3add1Month(userApplyDate)) {
                                                    this.mItem.oregionId = '1' // 本网
                                                    this.mItem.operaId = resTel.data.data.csp_type
                                                    this.busiTelIsJSCMCC = true
                                                    this.$alert('存量号码，请重新输入')
                                                } else{
                                                    this.mItem.oregionId = '1' // 省内本网
                                                    this.mItem.operaId = resTel.data.data.csp_type
                                                    this.busiTelIsJSCMCC = false
                                                    this.getJudgment()
                                                    this.getWhiteFlg()
                                                    this.findNewTelSms()

                                                }
                                            })
                                        }

                                    } else {
                                        this.mItem.oregionId = '2' // 省内异网
                                        this.mItem.operaId = resTel.data.data.csp_type
                                        this.busiTelIsJSCMCC = false
                                        this.getJudgment()
                                        this.getWhiteFlg()
                                        this.findNewTelSms()

                                    }
                                } else{

                                    this.mItem.oregionId = '3' // 外省
                                    this.mItem.operaId = resTel.data.data.csp_type
                                    this.busiTelIsJSCMCC = false
                                    this.getJudgment()
                                    this.getWhiteFlg()
                                    this.findNewTelSms()

                                }
                            } else {
                                this.busiTelIsJSCMCC = false
                                this.$alert(resTel.data.retMsg || '未识别出号码归属，请重新输入号码')
                            }
                        })
                    }
                }
            }
        },
        findNewTelSms() {
            // 查找新卡号码
            let urlnew = `/xsb/gridCenter/familyDiagnosis/h5QryTelIsCmsVerify`
            let paramenw ={
                "busiTel":this.telNum,  // 手机号码
            }
            this.$http.post(urlnew, paramenw).then((res) => {
                if(res.data.data == true) {
                    this.isMessageVerify = 'true'
                } else {
                    this.isMessageVerify = 'false'
                }
            })
        },
        inputNewTel() {
            // 新卡号码输入事件
            this.isJSCMCC = false
            this.isCust_ic_type = false

            if (this.newMsisdn) {
                this.isAllowExist = false
                let str = this.newMsisdn + ''
                if (str['0'] == '0') {
                    if (str.length >= 11) {
                        this.$toast('请输入正确的手机号码')
                        return
                    }
                } else {
                    if (str.length >= 11) {
                        let urlnew = `/xsb/gridCenter/familyDiagnosis/h5QryUserCityByBoss`
                        let paramenw ={
                            "crmId": this.userInfo.crmId,
                            "region":  this.userInfo.region,
                            "servicenumber":this.newMsisdn,  // 手机号码
                            "is_show_csp": "1"              // 是否展示运营商   0：否   1：是
                        }
                        // 判断归属
                        this.$http.post(urlnew, paramenw).then((resTel) => {
                            if (resTel.data.retCode == '0') {
                                if(process.env.NODE_ENV === 'development'){
                                    // 开发环境
                                    this.$toast('检测到当前为开发环境,已修改新卡为csp_type=1;不影响生产')
                                    resTel.data.data.csp_type = 1
                                }
                                if (resTel.data.data.province_name.indexOf('江苏') >= 0) {
                                    if(resTel.data.data.csp_type != 1) {
                                        this.$toast('请输入正确的新卡号码')
                                        this.isJSCMCC = false

                                    } else {
                                        this.isJSCMCC = true
                                        this.getRegTime()
                                    }
                                } else{
                                    this.$toast('请输入正确的新卡号码')

                                    this.isJSCMCC = false

                                }
                            } else {
                                this.$alert(resTel.data.retMsg || '未识别出号码归属，请重新输入号码')
                            }
                        })
                    }
                }
            }
        },

        // 提交
        submitForm(subType) {
            if (!this.telNum) {
                this.$toast('请输入用户号码')
                return
            } else {
                let regnum = /^[\d|\.]*$/
                if (this.telNum['0'] == 0) {
                    if (!regnum.test(this.telNum)) {
                        this.$toast('请输入正确的用户号码')
                        return
                    }
                    const reg = /^0\d{2,3}\d{7,8}$/
                    if (!reg.test(this.telNum)) {
                        this.$toast('请输入正确的用户号码')
                        return
                    }
                } else if (this.telNum['0'] == 1) {
                    if (!regnum.test(this.telNum)) {
                        this.$toast('请输入数字')
                        return
                    }
                    if (this.telNum.length != 11) {
                        this.$toast('请输入正确的用户号码')
                        return
                    }
                    var regnum2 = /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/
                    if (!regnum2.test(this.telNum)) {
                        this.$toast('请输入正确的手机号')
                        return
                    }
                } else {
                    this.$toast('请输入正确的用户号码')
                    return
                }
            }



            if(this.busiTelIsJSCMCC) {
                this.$toast('商机号码为存量号码,请重新输入')
                return
            }
            if(this.flg180Days) {
                this.$toast('当前商机号码已有该结单类型的数据，不允许同类型重复结单')
                return
            }
            if (!this.busiUserName) {
                this.$toast('请输入用户姓名')
                return
            }
            if (!this.county.countyId) {
                this.$toast('请选择归属区县')
                return
            }
            if (!this.chooseUserInfo.execName) {
                this.$toast('请选择商机跟进人')
                return
            }

            if (!this.overType ) {
                this.$toast('请选择结单类型')
                return
            }

            // 新办卡
            if (this.overType == 1) {
                this.newCardSub(subType)
            }
            // 归位
            if (this.overType == 2) {
                this.homingSub(subType)
            }
        },

        // 新办卡-提交校验
        async newCardSub(subType) {

            let telReg = /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/
            if (!telReg.test(this.newMsisdn)) {
                this.$toast('请输入正确的新卡号码')
                return
            }
            if(!this.isJSCMCC) {
                this.$toast('请输入正确的江苏本网新卡号码')
                return
            }

            // 地市要求上传图片及短信验证的情况下-阻断性
            if(!this.nameAndTelFlg) {
                // 未进行短信验证--校验短信验证码是否填写-阻断性
                if (this.isMessageVerify != "true" && !this.yzmCode) {
                    this.$toast('短信验证码未填写')
                    return
                }
                // 校验图片是否上传-阻断性
                if(this.OCRimgs.length == 0) {
                    this.$toast('掌厅图片未上传')
                    return
                }


            }

            // 短信验证码填写的情况下，判定填写是否正确-阻断性
            let checkRes = null
            if (this.yzmCode) {
                checkRes = await this.getSmsCheckVerification()
                if (checkRes.data.retCode != 0) {
                    this.$toast('验证码已超期或输入错误')
                    return
                }
            }
            // 非阻断校验2-4
            this.newMsisdnVerify2To4(checkRes)
        },
        async get184DaysResult(val) {
            let param1 = {
                followResult: val,
                busiTel: this.telNum
            }
            console.log('h5QuerySuccessAccountTime',param1)
            let url1 = `/xsb/gridCenter/familyDiagnosis/h5QuerySuccessAccountTime`
            return this.$http.post(url1, param1)
            // this.$toast('当前商机号码已有该结单类型的数据，不允许同类型重复结单')
        },
        async get184DaysNewResult(val) {
            let param1 = {
                userId: this.userId,
                regionId: this.userInfo.region
            }
            console.log('h5QuerySuccessAccountTime',param1)
            let url1 = `/xsb/gridCenter/familyDiagnosis/h5QueryNewCardUserIdTime`
            return this.$http.post(url1, param1)
            // this.$toast('当前商机号码已有该结单类型的数据，不允许同类型重复结单')
        },
        async getSmsCheckVerification() {
            this.yzmCode = this.yzmCode + ''
            if (this.yzmCode.length != 4) {
                this.$toast('请输入4位的短信验证码')
                return
            }
            let url = '/xsb/gridCenter/sendCode/h5CheckVerification'
            let param = {
                'crmId': Storage.session.get('userInfo').crmId,
                'region': Storage.session.get('userInfo').region,
                'sendCode': this.yzmCode,
                'telnum': this.telNum
            }
            return this.$http.post(url, param)
        },
        async newMsisdnVerify2To4(checkRes){
            // 非阻断校验2-4
            let remarkText = ''
            // 结单校验二、新卡开户时间校验
            if(this.isMoreThan3add1Month(this.userApplyDate)) {
                this.$messagebox({
                    title: '温馨提示',
                    message: '结单失败，结单人3个月内未办理该业务!',
                    showCancelButton: false,
                    confirmButtonText: '返回完善信息',
                }).then((action2) => {
                });
                return
            }

            // 结单校验三：卡号办理人与结单人一致性校验：
            let handlerRes = await this.get3Add1Result(1,'newMsisdn','1')
            if (handlerRes.data.retCode == 0) {
                if (process.env.NODE_ENV === 'development') {
                    // 开发环境
                    // this.$toast('检测到当前为开发环境,totalRowNum = 0')
                    handlerRes.data.data.totalRowNum = 0
                }

                if (handlerRes.data.data.totalRowNum <= 0) {
                    let handlerRes2 = await this.get3Add1Result(1,'newMsisdn','2')
                    if (handlerRes2.data.retCode == 0) {
                        if (process.env.NODE_ENV === 'development') {
                            // 开发环境
                            // this.$toast('检测到当前为开发环境,totalRowNum2 = 0')
                            handlerRes2.data.data.totalRowNum = 10
                        }
                        if (handlerRes2.data.data.totalRowNum <= 0) {
                            this.$messagebox({
                                title: '温馨提示',
                                message: '结单人和新卡办理人不一致',
                                showCancelButton: false,
                                confirmButtonText: '返回完善信息',
                            }).then((action3) => {
                            });
                            return
                        }
                    }
                }
            } else {
                this.$messagebox({
                    title: '温馨提示',
                    message: '结单人和新卡办理人不一致',
                    showCancelButton: true,
                    confirmButtonText: '返回完善信息',
                    cancelButtonText: '转派给办理人',
                }).then((action3) => {
                });
                return
            }


            console.log('checkRes--4',checkRes)


            // 结单校验四 同商机号码184天内重复“新办卡”类型结单校验
            let get184Res = await this.get184DaysResult(1)
            if(get184Res.data.retCode != 0) {
                this.$messagebox({
                    title: '温馨提示',
                    message: '当前商机号码已有同类型结单记录，是否继续提交？' ,
                    showCancelButton: true,
                    confirmButtonText: '继续提交',
                    cancelButtonText: '返回完善信息',
                }).then((action4) => {
                    if (action4 == 'cancel' ) return
                    remarkText = remarkText + '当前商机号码已有同类型结单记录;'
                    this.newMsisdnVerify5(remarkText,checkRes)
                });
            } else {
                this.newMsisdnVerify5(remarkText,checkRes)
            }
        },
        async newMsisdnVerify5(remarkText,checkRes){
            console.log('checkRes--5',checkRes)

            // 结单校验五、新卡号码184天重复结单校验
            let get184NewRes = await this.get184DaysNewResult(1)
            if (process.env.NODE_ENV === 'development') {
                // 开发环境
                get184NewRes.data.retCode = 0
            }
            if (get184NewRes.data.retCode != 0) {
                this.$messagebox({
                    title: '温馨提示',
                    message: '新办卡已有同类型结单记录，是否继续提交？' ,
                    showCancelButton: true,
                    confirmButtonText: '继续提交',
                    cancelButtonText: '返回完善信息',
                }).then((action) => {
                    if (action == 'cancel') return
                    remarkText = remarkText + '新办卡已有同类型结单记录;'
                    this.newMsisdnVerify6(remarkText,checkRes)
                });
            } else{
                this.newMsisdnVerify6(remarkText,checkRes)
            }
        },
        newMsisdnVerify6(remarkText,checkRes){
            console.log('checkRes',checkRes)
            // 结单校验六：是否完成短信验证
            if (this.isMessageVerify != "true") {
                if (!checkRes || (checkRes && checkRes.data.retCode != 0)) {
                    this.$messagebox({
                        title: '温馨提示',
                        message: '该商机单未输入验证码，将增加事后稽核方式。' ,
                        showCancelButton: true,
                        confirmButtonText: '补充验证码',
                        cancelButtonText: '继续提交',
                    }).then((action6) => {
                        if (action6 != 'cancel' ) return
                        remarkText = remarkText + '该商机单未输入验证码;'
                        // 参数分别
                        // subType --1:跟进;2:结单;3:补充; 4:挂起;5:转市公司人工稽核
                        // jiheFlg  ---稽核成功失败true/false
                        // str提交整合原因
                        // smsFlg ---是否发送短信 true/false
                        // msgType--成功/失败结单提示
                        // telFlg--是否需要通话圈判定
                        this.subMethod(2,true,remarkText,false,true,1,'结单校验六')
                    })
                } else {
                    this.newMsisdnVerify7(remarkText)
                }
            } else {
                this.newMsisdnVerify7(remarkText)
            }
        },
        newMsisdnVerify7(remarkText){
            // 结单校验七：是否上传掌厅截图
            if (this.OCRimgs.length == 0) {
                this.$messagebox({
                    title: '温馨提示',
                    message: '该商机单未上传掌厅图片，将增加事后稽核方式。',
                    showCancelButton: true,
                    confirmButtonText: '补传掌厅图片',
                    cancelButtonText: '继续提交',
                }).then((action7) => {
                    if (action7 != 'cancel') return
                    remarkText = remarkText + '该商机单未上传掌厅图片;'
                    // 参数分别
                    // subType --1:跟进;2:结单;3:补充; 4:挂起;5:转市公司人工稽核
                    // jiheFlg  ---稽核成功失败true/false
                    // str提交整合原因
                    // smsFlg ---是否发送短信 true/false
                    // msgType--成功/失败结单提示
                    // telFlg--是否需要通话圈判定
                    this.subMethod(2,true,remarkText,true,true,1,'结单校验七')
                });
            } else{
                this.newMsisdnVerify8(remarkText)
            }

        },
        newMsisdnVerify8(remarkText){
            // 结单校验八、判断"姓"和“号码”是否均被识别出来
            console.log(this.OCRname, this.OCRtel)
            if (!this.OCRname || !this.OCRtel) {
                this.$messagebox({
                    title: '温馨提示',
                    message: '用户姓/商机号码后四位未识别到',
                    showCancelButton: true,
                    confirmButtonText: '提交市公司人工稽核',
                    cancelButtonText: '返回完善信息',
                }).then((action) => {
                    if (action == 'cancel') return
                    remarkText = remarkText + '用户姓/商机号码后四位未识别到;'
                    // 参数分别
                    // subType --1:跟进;2:结单;3:补充; 4:挂起;5:转市公司人工稽核
                    // jiheFlg  ---稽核成功失败true/false
                    // str提交整合原因
                    // smsFlg ---是否发送短信 true/false
                    // msgType--成功/失败结单提示
                    // telFlg--是否需要通话圈判定
                    this.subMethod(5,false,remarkText,true,'结单信息已提交人工审核',2)
                })
            } else {
                this.newMsisdnVerify9(remarkText)
            }
        },
        newMsisdnVerify9(remarkText){
            // 结单校验九：获取新卡号码的开户证件类型，判定是否集团证件
            console.log('集团证件开户',this.isCust_ic_type)
            if (process.env.NODE_ENV === 'development') {
                // 开发环境
                // this.$toast('检测到当前为开发环境,修改集团证件开户信息true')
                // this.isCust_ic_type = true
            }
            if (this.isCust_ic_type) {
                this.$messagebox({
                    title: '温馨提示',
                    message: '该新卡为集团证件开户',
                    showCancelButton: true,
                    confirmButtonText: '提交市公司人工稽核',
                    cancelButtonText: '返回完善信息',
                }).then((action) => {
                    if (action == 'cancel') return
                    remarkText = remarkText + '该新卡为集团证件开户;'
                    // 参数分别
                    // subType --1:跟进;2:结单;3:补充; 4:挂起;5:转市公司人工稽核
                    // jiheFlg  ---稽核成功失败true/false
                    // str提交整合原因
                    // smsFlg ---是否发送短信 true/false
                    // msgType--成功/失败结单提示
                    // telFlg--是否需要通话圈判定
                    this.subMethod(5,false,remarkText,true,'结单信息已提交人工审核',2)
                })
            } else {
                this.newMsisdnVerify10(remarkText)
            }
        },
        newMsisdnVerify10(remarkText){
            // 结单校验十：判断新办号码对应的“姓”与识别出的“姓”是否一致
            if (this.userApplyName != this.OCRname) {
                this.$messagebox({
                    title: '温馨提示',
                    message: '新办卡与商机号码身份证不一致',
                    showCancelButton: true,
                    confirmButtonText: '提交市公司人工稽核',
                    cancelButtonText: '返回完善信息',
                }).then((action) => {
                    if (action == 'cancel') return
                    remarkText = remarkText + '新办卡与商机号码身份证不一致;'
                    // 提交市公司人工稽核--结单信息已提交人工审核
                    // 参数分别
                    // subType --1:跟进;2:结单;3:补充; 4:挂起;5:转市公司人工稽核
                    // jiheFlg  ---稽核成功失败true/false
                    // str提交整合原因
                    // smsFlg ---是否发送短信 true/false
                    // msgType--成功/失败结单提示
                    // telFlg--是否需要通话圈判定
                    this.subMethod(5,false,remarkText,true,'结单信息已提交人工审核',2)
                });
                return
            } else {
                this.newMsisdnVerify11(remarkText)
            }
        },
        newMsisdnVerify11(remarkText){
            console.log('商机号码与截图号码==>',this.telNum.slice(-4), this.OCRtel)
            // 结单校验十一：判断录入的异网号码后4位要与上传的YW掌厅截图内的号码后4位一致是否一致
            if (this.telNum.slice(-4) != this.OCRtel) {
                this.$messagebox({
                    title: '温馨提示',
                    message: '商机号码与截图号码不一致',
                    showCancelButton: true,
                    confirmButtonText: '提交市公司人工稽核',
                    cancelButtonText: '返回完善信息',
                }).then((action) => {
                    if (action == 'cancel') return
                    remarkText = remarkText + '商机号码与截图号码不一致;'
                    // 提交市公司人工稽核--结单信息已提交人工审核
                    // 参数分别
                    // subType --1:跟进;2:结单;3:补充; 4:挂起;5:转市公司人工稽核
                    // jiheFlg  ---稽核成功失败true/false
                    // str提交整合原因
                    // smsFlg ---是否发送短信 true/false
                    // msgType--成功/失败结单提示
                    // telFlg--是否需要通话圈判定
                    this.subMethod(5,false,remarkText,true,'结单信息已提交人工审核',2)
                });
            } else {
                // “一致”的情况下--成功结单
                // 参数分别
                // subType --1:跟进;2:结单;3:补充; 4:挂起;5:转市公司人工稽核
                // jiheFlg  ---稽核成功失败true/false
                // str提交整合原因
                // smsFlg ---是否发送短信 true/false
                // msgType--成功/失败结单提示
                // telFlg--是否需要通话圈判定
                this.subMethod(2,true,remarkText,true,true,2)
            }
        },

        // 归为-提交校验
        async homingSub(subType) {
            let that = this
            let remarkText = ''
            // 结单校验一、商机号码校验
            let url = `/xsb/gridCenter/familyDiagnosis/h5QryUserCityByBoss`
            let param ={
                "crmId": this.userInfo.crmId,
                "region":  this.userInfo.region,
                "servicenumber":this.telNum,  // 手机号码
                "is_show_csp": "1"              // 是否展示运营商   0：否   1：是
            }
            // 判断归属
            this.$http.post(url, param).then(async (res) => {
                if (res.data.retCode == '0') {
                    if (res.data.data.province_name.indexOf('江苏') >= 0) {
                        if (process.env.NODE_ENV === 'development') {
                            // 开发环境
                            this.$toast('检测到当前为开发环境,csp_type=1')
                            res.data.data.csp_type = 1
                            // this.isCust_ic_type = true
                        }
                        if(res.data.data.csp_type == 1) {
                            // 结单校验二、开户时间校验
                            let url2 = `/xsb/gridCenter/familyDiagnosis/h5QryBusiCountyId` + `?regionId=${this.userInfo.region}&operId=${this.userInfo.staffId}&telnum=${this.telNum}`;
                            that.$http.post(url2).then(async(res2) => {
                                if (res2.data.retCode == '0') {
                                    let userApplyDate2 = res.data.data.userApplyDate
                                    if (process.env.NODE_ENV === 'development') {
                                        // 开发环境
                                        this.$toast('检测到当前为开发环境,userApplyDate2=8月')
                                        userApplyDate2= "20250802101010"
                                        // this.isCust_ic_type = true
                                    }
                                    console.log('结单校验er：')

                                    if(that.isMoreThan3add1Month(userApplyDate2)) {
                                       console.log('结单校验er：***********')

                                        this.$messagebox({
                                            title: '温馨提示',
                                            message: '结单失败，结单人3个月内未办理该业务',
                                            showCancelButton: false,
                                            confirmButtonText: '返回完善信息',
                                        })
                                        return
                                    } else {
                                        console.log('结单校验三：')
                                        // 结单校验三：卡号办理人与结单人一致性校验：
                                        let handlerRes = await that.get3Add1Result(2,'busiTel','1')
                                        console.log('结单校验三：handlerRes')

                                        if (handlerRes.data.retCode == 0) {
                                            if (process.env.NODE_ENV === 'development') {
                                                // 开发环境
                                                this.$toast('检测到当前为开发环境,totalRowNum = 8')
                                                handlerRes.data.data.totalRowNum = 8
                                            }

                                            if(handlerRes.data.data.totalRowNum <= 0) {
                                                this.$messagebox({
                                                    title: '温馨提示',
                                                    message: '结单人和归位办理人不一致。',
                                                    showCancelButton: false,
                                                    confirmButtonText: '返回完善信息',
                                                })
                                            } else {
                                                that.subMethod(2,true,remarkText,'',true,'')
                                            }
                                        } else {
                                            this.$messagebox({
                                                title: '温馨提示',
                                                message: '结单人和归位办理人不一致。',
                                                showCancelButton: false,
                                                confirmButtonText: '返回完善信息',
                                            })
                                        }
                                    }
                                } else {
                                    this.$messagebox({
                                        title: '温馨提示',
                                        message: '结单失败，结单人3个月内未办理该业务!',
                                        showCancelButton: false,
                                        confirmButtonText: '返回完善信息',
                                    })
                                    return
                                }
                            })
                        } else {
                            this.$toast('当前商机号码未携入')
                            return
                        }
                    } else {
                        this.$toast('当前商机号码未携入')
                        return
                    }
                } else {
                    this.$toast('当前商机号码未携入')
                    return
                }
            })
        },

        // 提交触发的接口
        subFollow(subType, jiheFlg,str) {
            // subType --挂起1/结单2
            // jiheFlg  ---true/false
            if (!this.followSituation) {
                this.$toast('请选择持续跟进情况')
                return false
            }
            // 理财
            if(this.followSituation == 1) {
                if(!this.priceBefore) {
                    this.$toast('请输入理财前套餐价值')
                    return false
                }
                if(!this.priceAfter) {
                    this.$toast('请输入理财后套餐价值')
                    return false
                }
                if ( this.LCphotos == '' && this.LCvideos == '' ) {
                    this.$toast('请上传理财佐证资料');
                    return false
                }
            }
            // 销户
            if(this.followSituation == 2 || this.followSituation == 3) {
                if ( this.XHphotos == '' && this.XHvideos == '' ) {
                    this.$toast('请上传销户证明');
                    return false
                }
            }
            return true


        },
        formatDate(input) {
            if (!input) return ''
            // 创建一个新的Date对象
            const date = new Date(
                parseInt(input.substring(0, 4)),  // 年
                parseInt(input.substring(4, 6)) - 1, // 月（JavaScript中月份是从0开始的）
                parseInt(input.substring(6, 8)), // 日
                parseInt(input.substring(8, 10)), // 时
                parseInt(input.substring(10, 12)), // 分
                parseInt(input.substring(12, 14)) // 秒
            );

            // 格式化日期时间
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份需要+1因为是从0开始的，并且需要补0
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');

            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        },
        subMethod(subType, jiheFlg,str,smsFlg,msgType,telFlg,jiedan) {
            // subType --挂起1/结单2
            // jiheFlg  ---稽核成功失败true/false
            // str提交整合原因
            // smsFlg ---是否发送短信 true/false
            // msgType--成功/失败结单提示
            // telFlg--是否需要通话圈判定
            // jiedan--结单校验6/7特殊标识
            let OCRname = ''
            for (let i =0 ; i < this.OCRimgs.length; i++) {
                if (OCRname) {
                    OCRname = OCRname + '|' +this.OCRimgs[i].imageName
                } else {
                    OCRname = this.OCRimgs[i].imageName
                }
            }
            let videoNames = ''
            for (let i =0 ; i < this.OCRVideo.length; i++) {
                if (videoNames) {
                    videoNames = videoNames + '|' +this.OCRVideo[i].videoName
                } else {
                    videoNames = this.OCRVideo[i].videoName
                }
            }
            let paramhome = {
                'msisdn': this.telNum
            }
            console.info('不存在:查询当前号码是否已有归属家庭?', paramhome)
            let urlhome = '/xsb/personBusiness/queryCalculate/h5QryJudgmentNum'
            this.$http.post(urlhome, paramhome).then((res0) => {
                if (res0.data.retCode == 0) {
                    this.homeId = res0.data.data.result.homeId
                    // this.homeId = ''
                    if (this.homeId) {
                        // subType 1挂起 /  2结单
                        let subParam = {
                            "crmId": this.userInfo.crmId,
                            "regionId": this.userInfo.region, // 区域ID
                            "oaId": this.userInfo.oa, // 办公自动化系统ID
                            "isStaright": 1, // 是否直办 1：是；2：否
                            "busiSource": "1", // 商机来源
                            "busiType": "1", // 商机类型
                            "busiTel": this.telNum, // 商机联系电话
                            "busiUserName": this.busiUserName, // 商机用户姓名
                            "countyName": this.county.countyName, // 县区名称
                            "countyId": this.county.countyId, // 县区ID
                            "followBy": this.chooseUserInfo.execName, // 跟进人
                            "followTel": this.chooseUserInfo.execMsisdn, // 跟进人电话
                            "buopStatus": jiheFlg ? 12 : 10, // 10/12
                            'modifyOper': this.userInfo.operatorName, // 操作人
                            'modifyTel': this.userInfo.servNumber, // 操作人电话
                            "releBillId": "", // 关联算账单号
                            "homeId": this.homeId, // 家庭号--要查
                            "userAffiliation": this.mItem.oregionId,  // 用户归属
                            "busiOperatorName": this.mItem.operaId,  //运营商
                            "operateType": "2", // 操作类型-结单
                            "followResult": this.overType, // 跟进结果-1/2
                            "newCardTel": this.newMsisdn, // 新办卡号码
                            "ztVideoUrl": OCRname, // 掌厅图片URL
                            "cardVedio": videoNames, // 掌厅视频
                            'userName': this.OCRname, // 用户姓名
                            'lastFourBusiTel': this.OCRtel, // 商机号后四位
                            "realtimeAuditStatus": jiheFlg ? 1 : 2, // 实时稽核状态
                            "auditFailReason": str ? str : '', //稽核失败原因
                            'userApplyDate': (this.overType == 1 || this.overType == 2) && jiheFlg ?this.formatDate(this.userApplyDate) : '', // 开户时间
                            "isMessageVerify": smsFlg ? 1 : 2,//是否短信验证
                            "isJudgeByCall": telFlg,//是否通话圈判定
                            "newCardUserId": this.userId, // 新办卡用户ID
                        }
                        // 新版卡校验-失败结单
                        if(!msgType && str) {
                            subParam.realtimeAuditStatus = 6
                            subParam.followResult = 4 // 失败结单
                            subParam.failReason = 2 // 失败结单-其他
                            subParam.failReasonDesc = str // 失败结单-其他-原因
                        }
                        if (this.isMessageVerify == "true") {
                            subParam.isMessageVerify = 1
                        }
                        if(jiedan) {
                            // 结单校验六七特殊修改-稽核一半
                            subParam.realtimeAuditStatus = 7
                        }
                        let url = `/xsb/gridCenter/familyDiagnosis/h5StraightBusiAdd`
                        console.log('h5StraightBusiAdd提交===>',subParam)
                        this.$http.post(url,subParam).then((res) => {
                            console.log('h5StraightBusiAdd返回===>',res)

                            if (res.data.retCode == '0') {
                                this.$alert("提交成功");
                                history.go(-1)
                                let param3 = {
                                    'operatorMsisdn': this.userInfo.servNumber,
                                    'operatorName': this.userInfo.operatorName,
                                    'operatorCrm': this.userInfo.crmId,
                                    'changeType': '3',
                                    'regMsisdn': this.telNum,//登记主号码即潜在成员登记哪个号码下
                                    'homeID': this.homeId,//归属家庭
                                    'msisdn': this.telNum,//号码
                                    'ascriptionType': this.mItem.oregionId,//号码归属1：本网2：异网
                                    'teleType': this.mItem.operaId,//运营商1：移动2：电信3：联通
                                    'userID': this.userInfo.servNumber,//变更人
                                    'changeTime': dateFormat(new Date(), 'yyyy/MM/dd hh:mm:ss'),//变更时间
                                    'otherInfo': ''
                                }
                                let url3 = '/xsb/personBusiness/queryCalculate/h5QryFamilyMemberChange'
                                console.info('有家庭----更新已有的成员信息', param3)
                                this.$http.post(url3, param3)
                            } else {
                                this.$alert(res.data.retMsg);
                            }
                        })
                    } else {
                        let param0 = {
                            'operatorMsisdn': this.userInfo.servNumber,
                            'operatorName': this.userInfo.operatorName,
                            'operatorCrm': this.userInfo.crmId,
                            'changeType': '1',
                            'regMsisdn': this.telNum,//登记主号码即潜在成员登记哪个号码下
                            'homeID': '',//归属家庭
                            'msisdn': this.telNum,//号码
                            'ascriptionType': this.mItem.oregionId,//号码归属1：本网2：异网
                            'teleType': this.mItem.operaId,//运营商1：移动2：电信3：联通
                            'userID': this.userInfo.servNumber,//变更人
                            'changeTime': dateFormat(new Date(), 'yyyy/MM/dd hh:mm:ss'),//变更时间
                            'otherInfo': ''
                        }
                        let url0 = '/xsb/personBusiness/queryCalculate/h5QryFamilyMemberChange'
                        console.info('没有家庭----保存成员信息', param0)

                        this.$http.post(url0, param0).then((res1) => {
                            if (res1.data.retCode == 0) {
                                let url3 = '/xsb/personBusiness/queryCalculate/h5QryJudgmentNum'
                                this.$http.post(url3, { 'msisdn': this.telNum }).then((res4) => {
                                    if (res4.data.retCode == 0) {
                                        this.homeId = res4.data.data.result.homeId
                                        let subParam = {
                                            "crmId": this.userInfo.crmId,
                                            "regionId": this.userInfo.region, // 区域ID
                                            "oaId": this.userInfo.oa, // 办公自动化系统ID
                                            "isStaright": 1, // 是否直办 1：是；2：否
                                            "busiSource": "1", // 商机来源
                                            "busiType": "1", // 商机类型
                                            "busiTel": this.telNum, // 商机联系电话
                                            "busiUserName": this.busiUserName, // 商机用户姓名
                                            "countyName": this.county.countyName, // 县区名称
                                            "countyId": this.county.countyId, // 县区ID
                                            "followBy": this.chooseUserInfo.execName, // 跟进人
                                            "followTel": this.chooseUserInfo.execMsisdn, // 跟进人电话
                                            "buopStatus": jiheFlg ? 12 : 10, // 10/12
                                            'modifyOper': this.userInfo.operatorName, // 操作人
                                            'modifyTel': this.userInfo.servNumber, // 操作人电话
                                            "releBillId": "", // 关联算账单号
                                            "homeId": this.homeId, // 家庭号--要查
                                            "userAffiliation": this.mItem.oregionId,  // 用户归属
                                            "busiOperatorName": this.mItem.operaId,  //运营商
                                            "operateType": "2", // 操作类型-结单
                                            "followResult": this.overType, // 跟进结果-1/2
                                            "newCardTel": this.newMsisdn, // 新办卡号码
                                            "ztVideoUrl": OCRname, // 掌厅图片URL
                                            "cardVedio": videoNames, // 掌厅视频
                                            'userName': this.OCRname, // 用户姓名
                                            'lastFourBusiTel': this.OCRtel, // 商机号后四位
                                            "realtimeAuditStatus": jiheFlg ? 1 : 2, // 实时稽核状态
                                            "auditFailReason": str ? str : '', //稽核失败原因
                                            'userApplyDate': (this.overType == 1 || this.overType == 2) && jiheFlg ?this.formatDate(this.userApplyDate) : '', // 开户时间
                                            "isMessageVerify": smsFlg ? 1 : 2,//是否短信验证
                                            "isJudgeByCall": telFlg,//是否通话圈判定
                                            "newCardUserId": this.userId, // 新办卡用户ID
                                        }
                                        // 新版卡校验-失败结单
                                        if(!msgType && str) {
                                            subParam.realtimeAuditStatus = 6
                                            subParam.followResult = 4 // 失败结单
                                            subParam.failReason = 2 // 失败结单-其他
                                            subParam.failReasonDesc = str // 失败结单-其他-原因
                                        }
                                        if (this.isMessageVerify == "true") {
                                            subParam.isMessageVerify = 1
                                        }
                                        if(jiedan) {
                                            // 结单校验六七特殊修改-稽核一半
                                            subParam.realtimeAuditStatus = 7
                                        }
                                        let url = `/xsb/gridCenter/familyDiagnosis/h5StraightBusiAdd`
                                        console.log('h5StraightBusiAdd提交===>',subParam)
                                        this.$http.post(url,subParam).then((res) => {
                                            console.log('h5StraightBusiAdd返回===>',res)
                                            if (res.data.retCode == '0') {
                                                this.$alert("提交成功");
                                                history.go(-1)
                                            } else {
                                                this.$alert(res.data.retMsg);
                                            }
                                        })
                                    }
                                })
                            }
                        })
                    }
                }
            })

        },
        //主副号判断服务
        getJudgment(){
            return
            let param = {
                "msisdn":this.telNum,
            };
            let url='/xsb/personBusiness/queryCalculate/h5QryJudgmentNum';
            this.$http.post(url,param).then(res => {
                let {retCode,data,retMsg} = res.data;
                if(retCode == '0') {
                    this.homeId = data.result.homeId;
                }
            })
        },
    }

}
</script>

<style scoped lang='less'>
.td-wrap1 {
    padding: 50px 10px 100px;
    box-sizing: border-box;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 588;
    background: #f2f2f2;
    box-sizing: border-box;
    width: 100vw;
    height: 100vh;
    overflow: auto;

    .content-item {
        margin-top: 10px;
        width: 100%;
        box-sizing: border-box;
        border-radius: 8px;
        margin-bottom: 20px;
        //box-shadow:4px 4px 12px 0px rgba(184,184,184,0.5);
        background: #fff;


        .first-title {
            border-radius: 8px 8px 0 0px;
            overflow: hidden;

            .left-info {
                float: left;
                font-size: 15px;
                font-weight: 700;
                line-height: 24px;
                padding: 3px 10px;
                box-sizing: border-box;
                color: #257CDB;

                .wenhao1 {
                    color: #bbb;
                }

                img {
                    vertical-align: sub;
                    display: inline-block;
                    height: 22px;
                }

            }

            .left-info2 {
                position: relative;
                font-size: 12px;
                color: red;
                font-weight: 400;

                .wenhao {
                    font-size: 14px;
                    margin-left: 5px;
                    vertical-align: bottom;
                }

                .dialog-info {
                    position: absolute;
                    font-size: 12px;
                    width: calc(100vw - 40px);
                    left: 10px;
                    top: 36px;
                    box-sizing: border-box;
                    background: #e2f0ff;
                    padding: 10px;
                    border-radius: 7px;
                    z-index: 99;
                    line-height: 20px;
                    color: #02337f;
                    font-weight: 400;
                    box-shadow: 2px 2px 6px 0px rgba(0, 0, 0, 0.2);

                }

                .dialog-info::after {
                    content: '';
                    position: absolute;
                    width: 0;
                    height: 0;
                    border-style: solid;
                    border-width: 0 6px 12px 6px;
                    border-color: transparent transparent #e2f0ff transparent;
                    top: -9px;
                    left: 223px;
                }

            }

            .right-info {
                float: right;
                padding: 3px 10px 3px 0;
                line-height: 30px;
                font-style: italic;
                color: #a1c7f1;
                font-weight: 700;
                font-size: 18px;

                .iconfont {
                    color: #257CDB !important;
                }
            }

            .red {
                color: red;
                font-weight: 400;
                font-size: 12px;
            }

            .grey {
                color: #929292;
                font-weight: 400;
                font-size: 12px;
            }
        }

        .more-line {
            box-sizing: border-box;
            border-radius: 0px 0px 8px 8px;
            padding-bottom: 15px;
            overflow: hidden;

            .fadd-info {
                box-sizing: border-box;
                padding: 0 15px;

            }
        }

        .fadd-info {
            box-sizing: border-box;
            padding: 0 15px;

            h2 {
                img {
                    width: 14px;
                    height: 14px;
                    display: inline-block;
                    vertical-align: -2px;
                    margin-right: 4px;
                }

                color: #3A3A3A;
                font-size: 14px;
            }

            .fa-tips {
                font-weight: normal;
                margin-top: 8px;
                font-size: 12px;
                color: red;
                line-height: 14px;
                display: block;
            }


            .add-item2 {
                line-height: 40px;
                text-align: center;
                position: relative;

                .button {
                    width: 80px;
                    height: 40px;
                    text-align: center;
                    color: #fff;
                    display: inline-block;
                    border-radius: 6px;
                    background: #0b7fff;
                    margin: 5px 10px;
                    overflow: hidden;

                    .iconfont {
                        font-size: 18px;
                    }

                    .recwave-box {
                        width: 80px;
                        height: 48px;
                    }
                }
            }

            .add-itemtel {
                padding-right: 25px;
                box-sizing: border-box;

                .dianhua {
                    font-size: 20px !important;
                    right: 0;
                    top: 0px;
                }

                .mini {
                }
            }

            .add-itemdizhi {
                border-top: 1px solid #e5e4e4;
            }

            .add-item {
                height: 40px;
                line-height: 40px;
                //border-bottom: 1px solid #F0F0F0;
                box-sizing: border-box;
                display: flex;
                justify-content: space-between;
                align-items: center;
                position: relative;
                font-size: 10px;
                .checkboxround1 {
                    color: #289a07;
                    font-size: 12px;
                }
                .red-font {
                    position: absolute;
                    font-size: 10px;
                    color: red;
                    left: 0;
                    bottom: 0;
                    line-height: 12px;
                    width: 100%;
                    overflow: auto;
                    white-space: nowrap;
                }
                .choose-box {
                    width: 50%;
                    height: 100px;
                    position: absolute;
                    right: 0px;
                    top: 40px;
                    background: #fff;
                    z-index: 33;
                    box-shadow: 0px 4px 7px 0px rgba(0, 0, 0, 0.5);
                    padding: 5px 15px;
                    border-radius: 7px;
                    overflow: auto;

                    .item {
                        line-height: 32px;
                        font-size: 12px;
                        width: 100%;
                        overflow: auto;
                        white-space: nowrap;
                        color: #4f9bff;
                    }
                }

                .zhangting {
                    color: #bbb;
                    position: absolute;
                    font-size: 12px;
                    right: 15px;
                    bottom: 0;
                }

                .mini {
                    color: #0b7ffe;
                    position: absolute;
                    font-size: 12px;
                    right: 0;
                    bottom: 0;
                }

                .dianhua {
                    color: #0b7ffe;
                    font-size: 20px !important;

                }

                .dianhuaa {
                    font-size: 20px !important;
                    outline: none;
                    right: 0;
                    top: 0px;
                    text-decoration: 0;
                    vertical-align: bottom;
                }

                .add-left {
                    color: #232323;
                    font-size: 12px;

                }

                .add-left2 {
                    color: #232323;
                    font-size: 14px;
                    font-weight: 700;

                }

                .add-input {
                    font-size: 12px;
                    color: #232323;
                    outline: none;
                    text-align: right;
                    line-height: 30px;
                    min-width: 50%;

                    &::placeholder {
                        color: #BBBBBB;

                    }
                }

                .add-input2 {
                    padding-right: 120px;
                }

                .add-right {
                    flex: 1;
                    text-align: right;
                    color: #232323;
                    font-size: 12px;

                    i {
                        color: #232323;
                        font-size: 14px;
                        vertical-align: bottom;
                        display: inline-block;
                        margin-left: 2px;
                    }

                    .btn-item {
                        padding: 4px 0;
                        border-radius: 3px;
                        line-height: 16px;
                        font-size: 10px;
                        background: #eee;
                        display: inline-block;
                        margin: 2px;
                        text-align: center;
                        width: 68px;
                    }
                }

                .add-right2 {
                    text-align: right;
                    display: inline-block;
                    color: #232323;
                    font-size: 12px;
                    .shibie {
                        background: #0b8aff;
                        padding: 3px 6px;
                        color: #fff;
                    }
                    .grey-btn {
                        background: #ccc;
                    }
                    i {
                        color: #CBCBCB;
                        font-size: 14px;
                        vertical-align: middle;
                        display: inline-block;
                    }

                    .luyin {
                        font-size: 12px;
                        color: #0b7ffe;
                    }
                }
            }

            .add-item-yiju {
                height: auto !important;
                align-items: unset;
                line-height: 14px;
                margin: 15px 0;
            }
        }

        .value2 {
            width: calc(100% - 140px);
            line-height: 14px;
            text-align: right;

            span {
                margin-left: 3px;
            }
        }

        .value {
            width: calc(100% - 100px);

        }

        .reserve3-box {
            position: relative;
            display: flex;
            width: 50%;
            margin-top: 10px;
            justify-content: end;
            .OCRtel {
                font-size: 12px;
                position: absolute;
                right: 5px;
                bottom: 8px;
                color: #bbb;
            }

            .btn-item {
                padding: 4px 0;
                border-radius: 3px;
                line-height: 16px;
                font-size: 10px;
                background: #eee;
                display: inline-block;
                margin: 2px;
                max-width: 68px;
                flex: 1;
                text-align: center;
            }

            .active {
                background: #0b7ffe;
                color: #fff;
            }

            .info {
                display: inline-block;
                padding: 5px;
                box-sizing: border-box;
                font-size: 12px;
                line-height: 20px;
                border: 1px solid #c6c5c5;
                width: 100%;
                border-radius: 6px;
                min-height: 60px;

            }
        }
        .reserve4-box {
            width: 100%;
        }
        .pop-textarea {
            width: 100%;
            resize: none;
            height: 100px;
            line-height: 22px;
            font-size: 12px;
            border: 1px solid #bbb;
            padding: 5px;
            box-sizing: border-box;
            border-radius: 4px;
            outline: none;

        }
    }

    .content-item2 {
        margin-bottom: 80px;
    }

    .op-button-box {
        background-color: #fff;
        text-align: center;
        padding: 10px 12px;
        display: flex;
        justify-content: space-between;
        position: fixed;
        bottom: 0;
        left: 0;
        background: #fff;
        width: 100vw;
        box-sizing: border-box;
        box-shadow: 0px 0px 7px 5px rgba(0, 0, 0, 0.2);
        z-index: 333;


        .op-button {
            border-radius: 22px;
            height: 44px;
            line-height: 44px;
            flex: 1;
            font-size: 14px;
            outline: none;
            border: none;
            background: rgba(204, 204, 204, 1);
            color: #fff;
            margin: 0 10px;

            &.active {
                background: #1681FB;
                color: #fff;
            }
        }
    }

    .add-right-grey {
        color: #bfbfbf !important;
    }

    .jiantou-copy-copy {
        font-size: 14px;
        vertical-align: sub;
    }

    .dialog-box {
        position: fixed;
        top: -300px;
        left: 50%;
        transform: translateX(-50%);
        width: 70vw;
        padding: 40px 20px;
        box-sizing: border-box;
        background: #f5f6f6;
        border-radius: 8px;
        text-align: center;
        z-index: 777;

        img {
            width: 85%;

            margin-bottom: 30px;
        }

        div {
            font-size: 12px;
            color: #ccc;
        }

    }

    .recwave-box {
        width: 1px;
        height: 1px;
    }
}

input[disabled], textarea[disabled] {
    background: #fff !important;
}

.tel-list {
    font-size: 12px;

    .tel-item {
        width: 32%;
        text-align: left;
        display: inline-block;
        line-height: 26px;

        .shanchu {
            color: red;
            font-size: 12px;
        }
    }
}

.add-OCRtel {
    font-size: 12px;
    overflow: hidden;
    color: #1c9b00;
    line-height: 30px;

    input {
        float: left;
        border: 1px solid #bbb;
        font-size: 14px;
        line-height: 30px;
        padding: 0 10px;
    }

    .add {
        float: right;
        font-size: 12px;
        line-height: 30px;

    }
}

.xiangji {
    color: #0b7ffe !important;
}

.input-box {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100vh;
    background: rgba(0, 0, 0, .6);
    z-index: 599;

    .input-dialog {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: #fff;
        border-radius: 9px;
        width: 70vw;
        padding: 10px 20px;

        .title {
            font-size: 16px;
            text-align: center;
            font-weight: 700;
            line-height: 36px;
        }

        .input {
            width: 100%;
            line-height: 36px;
            border-bottom: 1px solid #f2f2f2;

            input {
                width: 100%;
                text-align: center;
                line-height: 36px;

            }

        }

        .btn {
            display: flex;
            margin: 20px 10px 10px;

            div {
                flex: 1;
                text-align: center;
            }

            .btn0 {
                border-radius: 4px;
                height: 30px;
                line-height: 30px;
                flex: 1;
                font-size: 14px;
                outline: none;
                border: none;
                background: rgba(204, 204, 204, 1);
                color: #fff;
                margin: 0 10px;
            }

            .btn1 {
                border-radius: 4px;
                height: 30px;
                line-height: 30px;
                flex: 1;
                font-size: 14px;
                outline: none;
                border: none;
                background: #1681FB;
                color: #fff;
                margin: 0 10px;
            }
        }
    }
}



.img-boxs {
    width: 100%;
    height: 100px;
    .img-box {
        overflow: hidden;
        position: relative;
        width: calc(33% - 10px);
        display: inline-block;
        margin: 0 5px;
        float: right;
        box-shadow: 2px 2px 6px 0px rgba(0, 0, 0, 0.2);

        .guanbi2 {
            position: absolute;
            right: 5px;
            top: 5px;
            color: red;
            z-index: 999;
        }

        img {
            width: 100%;
            height: 100px;
            float: right;

        }
    }
}
.img-boxs2 {
    width: 100%;
    padding: 6px;
    box-sizing: border-box;
    overflow: hidden;
    .img-box {
        overflow: hidden;
        position: relative;
        display: inline-block;
        box-shadow: 2px 2px 6px 0px rgba(0, 0, 0, 0.2);
        box-sizing: border-box;
        width: 100%;
        .guanbi2 {
            position: absolute;
            right: 5px;
            top: 10px;
            color: red;
            z-index: 999;
        }

        video {
            width: 100%;
            margin-top: 5px;
            float: right;
            max-height: 180px;
        }
    }
}
.red {
    color: red;
}

.add-right3 {
    font-size: 12px !important;
    color: #0b7ffe !important;
}

.add-right4 {
    color: #bbb !important;
    font-size: 12px !important;
}

.camera-wrap {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    background: #ECF0FA;
    overflow: auto;
}

.active {
    background: #0b7ffe !important;
    color: #fff;
}

.chooseDialog {
    position: fixed;
    z-index: 5000;
    background: #fff;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    padding: 12px;
    border-radius: 8px;
    box-shadow: 2px 2px 6px 0px #747576;
    width: 260px;

    .title {
        font-size: 18px;
        line-height: 26px;
        color: #0b7fff;
        border-bottom: 2px solid #1681FB;
    }

    .info {
        font-size: 14px;
        line-height: 22px;
        margin-top: 8px;
    }

    .btnlist {
        margin-top: 8px;
        display: unset !important;
        width: 100%;

        div {
            display: block;
            background: #1681FB;
            text-align: center;
            padding: 12px 15px;
            box-sizing: border-box;
            margin-top: 10px;
            border-radius: 5px;
            color: #fff;
            font-size: 15px;
            width: 100%;
        }
    }
}
.chooseDialogBgc {
    background: rgba(0, 0, 0, .4);
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    z-index: 95;
    z-index: 3000;
    left: 0;
}
.tip {
    color: red;
    position: absolute;
    font-size: 10px;
    right: 0;
    bottom: -3px;
    line-height: 15px;
}
.jiantou-copy-copy {
    font-size: 14px;
    vertical-align: sub;
}
</style>
