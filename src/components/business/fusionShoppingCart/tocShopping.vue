<template>
    <div>
        <Header tsTitleTxt='购物车受理(个人)' @emGoPrev='goPrev' backType='custom' tsBtnTxt='集团购物车'
                @emBtnCk='goTobCart'
                style='z-index:100'></Header>

        <div v-show='!isShow' class='place-order wrapper-medias'>

            <!--tab切换-->
            <div class='tab-contail'>
                <div class='cs-custominfo' v-show='serviceNum || groupId'>
                    <div class='cs-custominfo-detail'>
                        <div class='cs-custominfo-telnum'>
                            <span v-show='serviceNum'>{{ serviceNum | getTelNum }}</span>
                            <span class='custom-tag' v-show='!serviceNum && groupId'>{{ groupName }}</span>
                            <span class=' cs-hn-tag-new' :class="{ 'cs-no-scene': !sceneType }">{{ sceneType || '楼宇园区'
                                }}</span>
                        </div>
                    </div>

                    <div class='cs-cstominfo-qry'>
                        <div class='new-custom-box' @click='changeNum()'>
                            <span class='iconfont qiehuan4'></span>
                            <span class='new-custom-txt'>切换</span>
                        </div>
                    </div>

                </div>
                <div class='tab-box'>
                    <!--                    <ul>-->
                    <!--                        <li v-for='(item, index) in tabList' :key='index' :class="tabIndex == item.index ? 'active' : ''"-->
                    <!--                            @click='chooseTabType(item.index)'>-->
                    <!--                            <p>{{ item.name }}</p>-->
                    <!--                        </li>-->
                    <!--                    </ul>-->
                    <div class='ser-box'>
                        <div class='small-type' v-show='tabIndex == 0'>
                            <p v-for='(item, index) in smallTypeList' :key='index'
                               :class="smallType.id == item.id ? 'color-content' : ''" @click='chooseMixeType(item)'>
                                {{ item.label }}
                            </p>
                        </div>
                        <!--搜索框-->
                        <div class='menu-search-container'>
                            <div class='menu-search-box'>
                                <em class='iconfont sousuo'></em>
                                <input class='menu-box' ref='queryKey' v-model='queryKey' placeholder='请输入' />
                                <em @click='clearQueryKey()' v-show='queryKey' class='iconfont shanchu'></em>
                                <span class='menu-search-txt' @click='searchByName'>搜索</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 列表-->
            <div class='content-list' v-show='tabIndex != 2'>
                <ul class='left-list'>
                    <li v-for='(item, index) in busiList' :key='index' :class="{ 'choose-li': index == chooseIndex }"
                        @click='goProductConfig(item, index)'>
                        <div class='text'>{{ item.busiDesc }}</div>
                    </li>
                </ul>
                <ul class='right-list' v-show='menuList && menuList.length > 0'>
                    <div v-if='busiType == 2' style='    font-size: 15px;
    color: rgb(35, 112, 243);
    margin-top: 15px;
    display: flex
;
    overflow: hidden;
    align-items: center;'>
                        <div v-show='step != 1' style='    font-size: 15px;
    color: #2370f3;'>
                            <div class='blue-line'></div>
                            主套餐
                        </div>
                        <div v-show='step == 1' style='    font-size: 15px;
    color: #2370f3;'>
                            <div class='blue-line'></div>
                            叠加包
                        </div>
                    </div>
                    <Li v-show='step == 0' v-for='(item, index) in menuList' :key='index' @click='chooseProduct(item)'>
                        <p @click.stop='showOfferDetail(item)'>!</p>
                        <div class='text'>{{ item.prodName || item.busiDesc }}
                            <img v-show="item.attrValType == '1'" src='../../../assets/img/huore.png' alt='' />
                        </div>
                        <div class='num'>
                            <em class='iconfont youjiantou jiant'></em>
                        </div>
                    </Li>
                    <Li v-show='step == 1' v-for='(item, index) in secondMenuList' :key='index'
                        @click='goProductConfig(item, 2, true)'>
                        <p @click.stop='showOfferDetail(item)'>!</p>
                        <div class='text'>{{ item.busiDesc }}<img v-show='index < 3' src='@/assets/img/huore.png'
                                                                  alt='' /></div>
                        <div class='num'>
                            <em class='iconfont youjiantou jiant'></em>
                        </div>
                    </Li>
                </ul>
                <NoDataPage v-show='!menuList || menuList.length <= 0' class='right-list no-data'
                            tipTxt='没有更多数据'></NoDataPage>

            </div>
            <div class='btn'>
                <div class='shop-car' @click='goFusionCar'>
                    <div>
                        <img src='../../../assets/img/fusion-car.png' />
                        <p>{{ cartListNumber || 0 }}</p>
                    </div>
                    <p>去购物车</p>
                </div>
            </div>
        </div>
        <div>
            <ShoppingCartZengZhi ref='shopzengzhi' v-if='isShow && busiType == 3'></ShoppingCartZengZhi>
            <kuanDaiKaiTongRefactor ref='shopkuandai' v-if='isShow && busiType == 1 && isMband == false'>
            </kuanDaiKaiTongRefactor>
            <shopBandchange ref='shopBandchange' v-if='isShow && busiType == 1 && isMband == true'></shopBandchange>
            <NetWorkEntryCartNew ref='shopzuwang' v-if='isShow && busiType == 5'></NetWorkEntryCartNew>
            <SecurityEntryShopCart ref='shopanfang' v-if='isShow && busiType == 4'></SecurityEntryShopCart>
            <ProdChoose ref='shopzhuti' v-if='isShow && busiType == 6' :selectOfferid='selectOfferid'></ProdChoose>
            <shopTvOpen ref='shopzhuti' v-if='isShow && busiType == 7' :tvSort='tvSort'
                        :alreadProd='alreadProd'></shopTvOpen>
            <BusiCustomConfig v-if='isShow && !isCreate && busiType == 2 && step == 2' @closeComponent='closeComponent'
                              :promotion='currentProduct' :offeringInfo='currentProduct' :serviceNum='serviceNum'
                              :groupId='groupId'
                              @createBusiGroup='createBusiGroup'>
            </BusiCustomConfig>
            <BusiGroupFiling v-if='isShow && isCreate' @closeComponent='closeComponent("",true)'
                             @createBusiGroup='createBusiGroup'></BusiGroupFiling>
        </div>
        <div>
            <DailogBox v-if='showOfferingDetail' @closeDaiogBox='showOfferingDetail = false'
                       :offeringInfo='showCurrentProduct'
                       offeringType='2'></DailogBox>
        </div>
    </div>
</template>
<script>
import Header from 'components/common/Header.vue'
import Storage from '@/base/storage.js'
import FusionConfig from './FusionConfig.vue'
import PromotionConfig from './PromotionConfig.vue'
import DailogBox from './DailogBox.vue'
import ShoppingCartZengZhi from 'components/business/ShoppingCart/ShoppingCartZengZhi'
import kuanDaiKaiTongRefactor from 'components/business/BandTvMarket/kuanDaiKaiTongRefactor'
import NetWorkEntryCartNew from 'components/business/securityService/NetWorkEntryCartNew'
import SecurityEntryShopCart from 'components/business/securityService/SecurityEntryShopCart'
import ProdChoose from './ProdChoose2'
import NoDataPage from 'components/common/NoDataPage'
import Authenct from 'components/common/Authenticate2/index.js'
import shopBandchange from 'components/business/BandTvMarket/BandChangeCart'
import shopTvOpen from 'components/business/fusionShoppingCart/NetTvCart2'
import { skipCommom } from 'components/desk/WorkStageNew/commonBusiCk.js'
import { menuChain } from '@/base/mixins/menuChainMixin'
import BusiCustomConfig from './BusiCustomConfig.vue'
import BusiGroupFiling from './BusiGroupFiling.vue'

export default {
    name: 'fusionPlaceOrder',
    mixins: [skipCommom, menuChain],
    components: {
        Header,
        FusionConfig,
        PromotionConfig,
        DailogBox,
        NoDataPage,
        ShoppingCartZengZhi,
        kuanDaiKaiTongRefactor,
        NetWorkEntryCartNew,
        SecurityEntryShopCart,
        ProdChoose,
        shopBandchange,
        shopTvOpen,
        BusiCustomConfig,
        BusiGroupFiling
    },
    data() {
        return {
            serviceNum: '', //服务号码
            uinfo: {}, //操作员信息
            step: 0,
            offeringList: [],
            tabIndex: 1, //tab类型
            currentProduct: {},
            tabList: [{ name: '商客产品', index: 1 }, { name: '场景产品', index: 0 }],
            cartListNumber: 0, //购物车数量
            pageNo: 1, //页面页数
            pageSize: 10, //每页数量
            totalRecord: '', //总数
            menuList: [],
            menuSearchList: [], //个人业务菜单搜索
            smallTypeList: [
                //融合套餐类型
                { id: '', label: '全部' },
                { id: 'rtShops', label: '沿街商铺' },
                { id: 'rtBuilding', label: '楼宇办公' },
                { id: 'rtHotel', label: '酒店住宿' }
            ], //, '物业'
            smallType: { id: '', label: '全部' },
            queryKey: '',
            showOfferingDetail: false, //是否展示商品详情弹框
            allLoaded: false,
            moreData: false,
            componentName: '', //组件名称
            attrList: [],
            busiList: [],
            isShow: false,
            chooseIndex: 0,
            busiType: '',
            selectOfferid: '',
            isMband: false,
            alreadProd: [],
            tvSort: '1',
            secondMenuList: [],
            groupId: '',
            isCreate: false,
            showCurrentProduct: {}

        }
    },
    created() {
        this.tabIndex = this.$route.query.tabIndex || 1
        this.serviceNum = Storage.session.get('jqData').telnum
        this.uinfo = Storage.session.get('userInfo')
        this.initData()
    },
    methods: {
        initData() {
            this.qryOfferingList(0) //查询融合套餐列表
            this.qryCartNumber() //查询购物车项数量
            this.qryAttrCfg() //查询个人业务列表（菜单路由）
        },
        goPrev() {
            if (this.isShow) {
                this.isShow = false
                this.initData()
                this.step = 0
            } else {
                history.go(-1)
            }
        },
        //上拉刷新
        loadTop() {
            this.$refs.loadmore.onTopLoaded()
            this.pageNo = 1
            this.qryOfferingList(1)
        },
        //下拉加载更多
        loadBottom() {
            this.$refs.loadmore.onBottomLoaded()
            this.pageNo++
            this.qryOfferingList()
        },
        //切换tab
        chooseTabType(index) {
            this.offeringList = []
            this.tabIndex = index
            if (this.tabIndex != 2) {
                this.qryOfferingList(0)
            }
        },
        changeNum() {
            Authenct(
                {
                    popFlag: true,
                    hasPwd: false,
                    idCardWay: false
                }, obj => {
                    this.serviceNum = obj.telnum
                }
            )
        },
        //根据关键字搜索商品
        searchByName() {
            if (this.tabIndex != 2) {
                this.qryOfferingList(1) //通过关键字查询
            } else {
                //遍历个人业务列表
                let listTemp = []
                this.menuList.forEach((item) => {
                    if (item.attrName.indexOf(this.queryKey) != -1) {
                        listTemp.push(item)
                    }
                })
                this.menuSearchList = listTemp
            }
        },
        //清空搜索词
        clearQueryKey() {
            this.queryKey = ''
            if (this.tabIndex == 2) {
                this.menuSearchList = JSON.parse(JSON.stringify(this.menuList)) //深拷贝不影响原数组
            }
        },
        showOfferDetail(item) {
            let prodIdList = [{ packageCode: item.busiName }]
            let url = '/xsb/personBusiness/businessOpenTv/h5QryProdDesc?prodIdList=' + JSON.stringify(prodIdList)
            this.$http.get(url).then(res => {
                let { retCode, retMsg, data } = res.data
                if (retCode == '0') {
                    item.prodDesc = data.prodDesc
                    this.showCurrentProduct = item
                    this.showCurrentProduct.busiDesc = item.prodName || item.busiDesc
                    this.showCurrentProduct.busiId = item.attrId || item.busiName
                    this.showOfferingDetail = true
                } else {
                    this.$toast('暂未套餐描述')
                }
            }).catch((response) => {
            })
        },
        createBusiGroup(param) {
            param.serviceNumber = this.serviceNum
            let url = '/xsb/personBusiness/yiwtongUpgrade/h5CorporateCustRegister'
            this.$http.post(url, param).then(res => {
                let { retCode, retMsg } = res.data
                if (retCode == '0' || retCode == '-20' || retMsg.indexOf('虚拟集团已存在') != -1) {
                    this.getVirtualGroup(true)
                } else {
                    this.$alert(retMsg || '虚拟集团创建失败')
                    this.showDetailflag = false
                }
            }).catch(res => {
                this.$alert('创建虚拟集团网络异常:' + res)
            })
        },
        //选择并搜索不同类型的融合套餐列表
        chooseMixeType(item) {
            this.smallType = item
            this.qryOfferingList(0)
        },
        //跳转全网购物车
        goFusionCar() {
            this.$router.push({
                path: '/fusionCar',
                srcFrom: 'tocShopping'
            })
        },
        closeComponent() {
            this.step = 0
            this.currentProduct = {}
            this.componentName = ''
            this.isShow = true
            this.showOfferingDetail = false
        },
        initSubProduct(step, item) {
            let offerInfo = {
                offeringId: item.busiId,
                offeringName: item.iconUrl,
                mainOfferId: '1000100498',
                mainOfferName: '商客套餐-楼园办公'
            }
            if (item.configList && item.configList.length > 0) {
                let subOfferList = []
                item.configList.forEach(e => {
                    subOfferList.push({
                        offeringId: e.prodId,
                        offeringName: item.busiDesc,
                        subsId: e.attrId,
                        subsName: e.attrDesc,
                        mainOfferId: e.attrName,
                        isNeed: e.isNeed
                    })
                })
                offerInfo.subOfferList = subOfferList
            }
            this.currentProduct.mainProduct = offerInfo
            this.currentProduct.name1 = item.busiDesc
        },
        //去商品属性页面填写参数
        goProductConfig(item, index, flag) {
            if (flag) {
                this.step = 2
                item.isAttr = 2
                this.busiType = 2
            } else {
                this.busiType = item.isAttr
                this.chooseIndex = index
                this.step = 0
            }
            if (item.isAttr != '3' && item.isAttr != '6' && item.isAttr != '2') {
                if (item.isAttr == '1') {
                    this.checkBandTv(item.iconUrl, 'band')
                } else if (item.isAttr == '7') {
                    this.checkBandTv(item.iconUrl, 'tv')
                } else {
                    this.isShow = true
                }
                if (item.isAttr == '1') {
                    this.isShow = true
                    this.$nextTick(() => {
                        this.$refs.shopkuandai.goStep1()
                        if (this.isMband) {
                            this.$refs.shopkuandai.goPrevright()
                        }
                    }, 500)
                } else if (item.isAttr == '6') {
                    this.$nextTick(() => {
                        this.$refs.shopzhuti.openComponent()
                    })
                }
            } else if (this.busiType == 2 && this.step == 2) {
                let offerInfoList = []
                let offerInfo = {
                    offeringId: item.busiId,
                    offeringName: item.busiDesc
                }
                if (item.configList && item.configList.length > 0) {
                    let subOfferList = []
                    item.configList.forEach(e => {
                        subOfferList.push({
                            offeringId: e.prodId,
                            offeringName: e.attrDesc,
                            isSub: e.attrValType,
                            pkgId: e.attrId,
                            prodType: e.attrField,//产品类型，1：增值化产品 4：政企增值化产品
                            packageId: e.attrName,
                            packageName: e.prodName//商品包编码

                        })
                    })
                    offerInfo.subOfferList = subOfferList
                }
                offerInfoList.push(this.currentProduct.mainProduct)
                offerInfoList.push(offerInfo)

                this.currentProduct.offeringInfoList = offerInfoList
                this.currentProduct.name2 = item.busiDesc
                // this.componentName = 'BusiCustomConfig'
                // 判断item.configList 是否有"attrId"为"2891001351"
                let isHas = false
                if (item.configList) {
                    isHas = item.configList.some((e) => {
                        return e.attrId === '2891001351'
                    })
                }
                if (isHas) {
                    this.getVirtualGroup()
                } else {
                    this.isShow = true
                }
            } else if (this.busiType == 2 && this.step == 0) {
                let url = `/xsb/gridCenter/businessOrder/h5getBusiProductList?sceneType=`
                this.$http.get(url)
                    .then((res) => {
                        let { retCode, retMsg, data } = res.data
                        if ('0' == retCode) {
                            this.menuList = data[1]
                            this.menuSearchList = data[1]
                        } else {
                            this.$alert(retMsg || '查询个人业务产品类型失败')
                        }
                    })
                    .catch((response) => {
                        this.$alert(response || '查询个人业务产品类型异常')
                    })
            } else {
                this.qryAttrCfg1(item)

            }
        },
        chooseProduct(item) {
            this.selectOfferid = item.attrId
            if (this.busiType != 2) {
                this.isShow = true
                this.$nextTick(() => {
                    if (this.chooseIndex == 1) {
                        // this.$refs.shopzhuti.openComponent()
                        return
                    }
                    this.$refs.shopzengzhi.changeGroup(this.busiList[this.chooseIndex].isDiscount)
                    this.$refs.shopzengzhi.searchProName(item.prodName)

                })
            } else {
                let param = {
                    productId: item.busiName
                }
                let url = `/xsb/personBusiness/cloudOrder/h5getBusiProductList?busiType=2400000499`
                this.$http.post(url, param)
                    .then((res) => {
                        let { retCode, retMsg, data } = res.data
                        if ('0' == retCode) {
                            this.secondMenuList = data
                            this.secondMenuSearchList = data
                            this.step = 1
                            if (this.busiType == '2') {
                                this.initSubProduct(1, item)
                            }
                        } else {
                            this.$alert(retMsg || '查询个人业务产品类型失败')
                        }
                    })
                    .catch((response) => {
                        this.$alert(response || '查询个人业务产品类型异常')
                    })
            }

        },
        qryAttrCfg1(item) {
            //查本地配置个人业务列表
            let param = {
                productId: item.busiName
            }
            let url = `/xsb/personBusiness/cloudOrder/h5GetProductCfg`
            return this.$http
                .post(url, param)
                .then((res) => {
                    let { retCode, retMsg, data } = res.data
                    if ('0' == retCode) {
                        this.menuList = data.attrCfgList
                        if (this.busiType == 2) {
                            this.menuList =
                                [
                                    {
                                        'prodId': '1001',
                                        'prodName': '楼园办公套餐139元/月（宽带300M、语音200分钟、流量30G、V网10人）',
                                        'attrId': '2000013716',
                                        'attrName': '融合套餐',
                                        'attrDesc': null,
                                        'attrValType': '1',
                                        'attrDefault': null,
                                        'attrValDesc': null,
                                        'isNeed': '1',
                                        'isEditable': '0',
                                        'isDisplay': '1',
                                        'state': '1',
                                        'prodArea': '套餐',
                                        'sort': '1',
                                        'attrField': '1'
                                    },
                                    {
                                        'prodId': '1001',
                                        'prodName': '楼园办公套餐199元/月（宽带500M、语音1000分钟、流量60G、V网20人）',
                                        'attrId': '2000013717',
                                        'attrName': '融合套餐',
                                        'attrDesc': null,
                                        'attrValType': '1',
                                        'attrDefault': null,
                                        'attrValDesc': null,
                                        'isNeed': '1',
                                        'isEditable': '0',
                                        'isDisplay': '1',
                                        'state': '1',
                                        'prodArea': '套餐',
                                        'sort': '2',
                                        'attrField': '1'
                                    },
                                    {
                                        'prodId': '1001',
                                        'prodName': '楼园办公套餐299元/月（宽带1000M、语音1500分钟、流量100G、V网50人）',
                                        'attrId': '2000013718',
                                        'attrName': '融合套餐',
                                        'attrDesc': null,
                                        'attrValType': '1',
                                        'attrDefault': null,
                                        'attrValDesc': null,
                                        'isNeed': '1',
                                        'isEditable': '0',
                                        'isDisplay': '1',
                                        'state': '1',
                                        'prodArea': '套餐',
                                        'sort': '3',
                                        'attrField': '1'
                                    }
                                ]
                        }
                    } else {
                        this.$alert(retMsg || '查询个人业务产品类型失败')
                    }
                })
                .catch((response) => {
                    this.$alert(response || '查询个人业务产品类型异常')
                })
        },
        showOfferingDesc(item) {
            this.currentProduct = item
            this.showOfferingDetail = 'true'
        },
        goTobCart() {
            let item = {
                privId: '4952',
                picUrl: 'static/skzq/menu/tob.png',
                privName: '商客购物车(B)',
                opId: 'tob_business_home',
                opParentid: 'tob_business_home'
            }
            this.updateMenuChain(item.privId)
            this.goBusinessPage(item)
        },
        qryAttrCfg() {
            //查本地配置个人业务列表
            let url = `/xsb/personBusiness/cloudOrder/h5getBusiProductList?busiType=5`
            return this.$http
                .get(url)
                .then((res) => {
                    let { retCode, retMsg, data } = res.data
                    if ('0' == retCode) {
                        this.busiList = data
                        this.busiList[0] = {
                            'busiId': '5',
                            'busiName': '1001',
                            'iconUrl': '100061',
                            'busiDesc': '融合套餐',
                            'isProduct': '5',
                            'isAttr': '2',
                            'isDiscount': '0',
                            'isGood': '1',
                            'isNeedContract': '0',
                            'configList': [
                                {
                                    'prodId': '1001',
                                    'prodName': '楼园办公套餐139元/月（宽带300M、语音200分钟、流量30G、V网10人）',
                                    'attrId': '2000013716',
                                    'attrName': '融合套餐',
                                    'attrDesc': null,
                                    'attrValType': '1',
                                    'attrDefault': null,
                                    'attrValDesc': null,
                                    'isNeed': '1',
                                    'isEditable': '0',
                                    'isDisplay': '1',
                                    'state': '1',
                                    'prodArea': '套餐',
                                    'sort': '1',
                                    'attrField': '1'
                                },
                                {
                                    'prodId': '1001',
                                    'prodName': '楼园办公套餐199元/月（宽带500M、语音1000分钟、流量60G、V网20人）',
                                    'attrId': '2000013717',
                                    'attrName': '融合套餐',
                                    'attrDesc': null,
                                    'attrValType': '1',
                                    'attrDefault': null,
                                    'attrValDesc': null,
                                    'isNeed': '1',
                                    'isEditable': '0',
                                    'isDisplay': '1',
                                    'state': '1',
                                    'prodArea': '套餐',
                                    'sort': '2',
                                    'attrField': '1'
                                },
                                {
                                    'prodId': '1001',
                                    'prodName': '楼园办公套餐299元/月（宽带1000M、语音1500分钟、流量100G、V网50人）',
                                    'attrId': '2000013718',
                                    'attrName': '融合套餐',
                                    'attrDesc': null,
                                    'attrValType': '1',
                                    'attrDefault': null,
                                    'attrValDesc': null,
                                    'isNeed': '1',
                                    'isEditable': '0',
                                    'isDisplay': '1',
                                    'state': '1',
                                    'prodArea': '套餐',
                                    'sort': '3',
                                    'attrField': '1'
                                }
                            ]
                        }
                    } else {
                        this.$alert(retMsg || '查询个人业务产品类型失败')
                    }
                })
                .catch((response) => {
                    this.$alert(response || '查询个人业务产品类型异常')
                })
        },
        //查询融合和政企套餐列表
        qryOfferingList(flag) {
            let queryKey = this.queryKey
            if (flag == 0) {
                this.pageNo = 1
                this.totalRecord = ''
                queryKey = ''
            } else if (flag == 1) {
                this.pageNo = 1
                this.totalRecord = ''
            }
            let param = {
                beId: this.uinfo.region,
                serverNumber: this.serviceNum, //服务号码
                serviceNum: this.serviceNum, //服务号码
                type: '175', //融合业务
                offerType: '1', //1：增值增值化融合商品
                totalRecord: this.totalRecord, //总条数
                keyWord: queryKey, //搜索关键字
                beginRowNumber: (this.pageNo - 1) * this.pageSize, //起始行数
                recordPerPage: this.pageSize, //页数
                curPage: this.pageNo, //页码
                promBusinessSubType: this.smallType.id //业务类型：rtBuildin-楼宇/园区 rtHotel-酒店 rtShops-商铺
            }
            let url = ''
            if (this.tabIndex == 0) {
                url = '/xsb/personBusiness/shopCart/h5qryPromotionListByServiceNum' //融合套餐
            } else {
                url = '/xsb/personBusiness/shopCart/h5qryOfferListByServiceNum' //政企套餐
            }
            this.$http
                .post(url, param)
                .then((res) => {
                    let { retMsg, retCode, data } = res.data
                    if (retCode == '0') {
                        let pageInfo = data.pageInfo
                        this.totalRecord = pageInfo.totalRecord //总数
                        let offeringListTemp
                        if (this.tabIndex == 0) {
                            offeringListTemp = data.promotionList || []
                        } else {
                            offeringListTemp = data.offeringInfoList ? data.offeringInfoList.offeringInfo : []
                        }
                        if (!offeringListTemp || offeringListTemp.length < 1) {
                            this.moreData = false
                            if (this.pageNo > 1) {
                                this.$toast('没有更多数据')
                            } else {
                                this.offeringList = []
                                // this.$toast('查询融合产品列表为空')
                            }
                        } else {
                            this.moreData = true
                            if (this.pageNo == 1) {
                                this.offeringList = []
                                this.offeringList = offeringListTemp
                            } else {
                                this.offeringList.push(...offeringListTemp)
                            }
                            if (this.totalRecord <= this.offeringList.length) {
                                this.moreData = false
                            }
                        }
                    } else {
                        this.moreData = false
                        this.offeringList = []
                        this.$alert(retMsg || '查询产品列表失败')
                    }
                })
                .catch((response) => {
                    this.$alert('查询产品列表异常:' + response)
                })
        },
        //查询购物车数量
        qryCartNumber() {
            let param = {
                serviceNum: this.serviceNum, //服务号码
                channelId: '', //查询渠道
                curPage: this.pageNo, //当前页
                pageSize: this.pageSize, //每页条数
                unLoadFlg: true //关闭加载圈
            }
            let url = `/xsb/personBusiness/shopCart/h5qryAllNetShopCartListCount`
            this.$http
                .post(url, param)
                .then((res) => {
                    let { retCode, retMsg, data } = res.data
                    if (retCode == '0') {
                        this.cartListNumber = data.totalCount
                    } else {
                        this.$alert(retMsg || '购物车商品数量查询失败')
                    }
                })
                .catch((response) => {
                    this.$toast('购物车商品数量查询异常:' + response)
                })
        },
        //判断电视宽带开通情况
        checkBandTv(privid, tvBandFlg) {
            let url = `/xsb/personBusiness/personInfo/h5QryAlreadyOpened?privid=${privid}&telnum=${this.serviceNum}`
            this.$http.get(url).then(res => {
                let { retCode, retMsg, data } = res.data
                if (retCode === '0') {
                    this.isMband = data.isMband == '1' ? true : false // 是否开通宽带 0：否，1：是
                    this.isNetTv = data.isNetTv == '1' ? true : false // 是否开通互联网电视 0：否，1：是
                    if (tvBandFlg === 'band') {//宽带
                        this.isShow = true
                    } else if (tvBandFlg === 'tv') {//电视
                        if (this.isNetTv === '1') {
                            //this.$alert('已开通过互联网电视！');
                            //跳第二台电视开通
                            this.alreadProd = JSON.stringify(data.prodNetList[0])
                            this.tvSort = '2'
                            this.isShow = true
                            // this.$router.push('/internetTvOpen?tvSort=2&alreadProd='+JSON.stringify(prodNetList[0]));
                        } else if (this.isNetTv === '2') {
                            //this.$alert('已开通过2台互联网电视！');
                            let prodNetList = data.prodNetList
                            let prodInfo = {}
                            for (let item of prodNetList) {
                                if (item.pakageid === '2013000101' || item.pakageid === '2013000102') {
                                    prodInfo = item
                                    break
                                }
                            }
                            //跳第三台电视开通
                            this.alreadProd = JSON.stringify(prodNetList[0])
                            this.tvSort = '3'
                            this.isShow = true
                            // this.$router.push('/internetTvOpen?tvSort=3&alreadProd='+JSON.stringify(prodInfo));
                            /*let param = {
                                busiType: "three_tv_open",
                            }
                            this.$http.post('/xsb/ability/businessLimit/h5QryBusiPermission', param).then(res => {
                                if (res.data.retCode === '0') {
                                    let prodNetList = data.prodNetList;
                                    let prodInfo = {};
                                    for (let item of prodNetList) {
                                        if (item.pakageid === "2013000101" || item.pakageid === "2013000102") {
                                            prodInfo = item;
                                            break;
                                        }
                                    }
                                    //跳第三台电视开通
                                    this.alreadProd = JSON.stringify(prodNetList[0])
                                    this.tvSort = '3'
                                    this.isShow = true
                                    // this.$router.push('/internetTvOpen?tvSort=3&alreadProd='+JSON.stringify(prodInfo));
                                } else {
                                    this.$alert('已开通过2台互联网电视！');
                                }
                            })*/
                        } else if (this.isNetTv == '3') {
                            this.$alert('已开通过3台互联网电视！')
                        } else {
                            if (this.isMband == '0') {//没开通宽带
                                this.$alert('请先开通宽带！')
                            } else {
                                this.alreadProd = []
                                this.tvSort = '1'
                                this.isShow = true
                                // this.$router.push('/internetTvOpen?tvSort=1');
                            }
                        }
                    }
                } else {
                    this.$alert(retMsg || '查询开通宽带电视信息异常')
                }
            }).catch(res => {
                this.$alert('查询开通宽带电视信息网络异常:' + res)
            })
        },
        getVirtualGroup() {
            let url = `/xsb/personBusiness/yiwtongUpgrade/h5QryVirtualGroupInfo?serviceNumber=${this.serviceNum}`
            this.$http.get(url).then(res => {
                let { retCode, retMsg, data } = res.data
                if (retCode == '0') {
                    //返回集团
                    this.groupId = data.groupId
                    this.groupName = data.groupName
                    this.isShow = true
                    this.isCreate = false
                } else {
                    if (retCode == '1') {
                        this.$messagebox({
                            title: '温馨提示',
                            message: '此包包含政企增值产品，需要创建集团',
                            showCancelButton: true,
                            confirmButtonText: '去创建',
                            cancelButtonText: '取消'
                        }).then((action) => {
                            if (action === 'confirm') {
                                this.isShow = true
                                this.isCreate = true
                            }
                        })
                    } else {
                        let msg = retMsg || '查询虚拟集团信息失败'
                        this.$alert(msg)
                    }
                }
            }).catch((response) => {
                this.$alert(`查询虚拟集团信息网络超时${response}`)
            })
        }
    },
    computed: {
        showList() {
            return this.componentName == ''
        },
        showNoData() {
            return (
                (this.tabIndex != 2 && (!this.offeringList || this.offeringList.length < 1)) ||
                (this.tabIndex == 2 && (!this.menuSearchList || this.menuSearchList.length < 1))
            )
        }
        // num() {
        //   let arr = this.offeringList.filter((item) => {
        //     return item.num > 0
        //   })
        //   return arr.length
        // },
    },
    filters: {
        getTelNum(telNum) {
            if (telNum) {
                return telNum.replace(/^(\d{3})(\d{4})(\d{4})$/, '$1****$3')
            }
        }
    }
}
</script>
<style scoped lang='less'>
.place-order {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    background-color: #fff;
    padding-top: 45px;
    display: flex;
    flex-direction: column;

    .tab-contail {
        width: 100%;
        background: #fff url(../../../assets/img/fusionPlaceOrder-bg.png) no-repeat center 0;
        padding: 11px 12px 0;
        box-sizing: border-box;
        //background-size: 100% auto;
        background: linear-gradient(315deg, #69ACEC 0%, #0D5DF6 100%);
        border-radius: 0 0 20% 20%;

        .tab-box {
            margin-top: 15px;
            width: 100%;
            border-radius: 20px;
            overflow: hidden;
            background: inherit;
            position: relative;
            border-top: 1px solid #adcaf7;
            box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.04);
            //margin-top: 40px;

            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(255, 255, 255, 0.2);
                backdrop-filter: blur(10px);
            }

            ul {
                display: flex;
                color: #fff;
                font-size: 15px;
                height: 34px;
                position: relative;
                justify-content: space-evenly;

                li {
                    width: 25%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-direction: column;
                }

                .active {
                    background-color: #fff;
                    border-radius: 18px 18px 2px 2px;
                    color: #007aff;
                    padding: 0 20px;
                }

                p {
                    position: relative;

                    span {
                        position: absolute;
                        bottom: -8px;
                        font-weight: 600;
                        left: 40%;
                        font-size: 20px;
                    }
                }
            }

            .ser-box {
                border-radius: 16px;
                overflow: hidden;
                background-color: #fff;
                padding: 20px 13px 23px 12px;
                position: relative;

                .small-type {
                    display: flex;
                    // margin-bottom: 14px;
                    margin: 0px 10px 14px;
                    justify-content: space-around;

                    p {
                        color: #575757;
                        font-size: 14px;
                        margin-right: 14px;

                        &.color-content {
                            color: #007aff;
                        }
                    }
                }

                .menu-search-container {
                    width: 100%;
                    background-color: #fff;
                    display: flex;
                    align-items: center;
                    box-sizing: border-box;
                    height: 31px;

                    .menu-search-box {
                        flex: auto;
                        position: relative;
                        margin-right: 0.5rem;

                        .menu-box {
                            width: 100%;
                            height: 30px;
                            background: rgba(241, 241, 241, 1);
                            border-radius: 1rem;
                            box-sizing: border-box;
                            outline: 0;
                            padding-left: 2rem;
                            padding-right: 5.5rem;
                            overflow: hidden;
                            font-size: 12px;

                            &::placeholder {
                                color: #b2b2b2;
                            }
                        }

                        .sousuo {
                            position: absolute;
                            left: 0.75rem;
                            top: 50%;
                            transform: translateY(-50%);
                            color: #b2b2b2;
                            font-size: 20px;
                        }

                        .shanchu {
                            position: absolute;
                            right: 3rem;
                            top: 50%;
                            color: #b2b2b2;
                            transform: translateY(-50%);
                            font-size: 12px;
                        }

                        .menu-search-txt {
                            position: absolute;
                            right: 0.75rem;
                            top: 50%;
                            color: #007aff;
                            transform: translateY(-50%);
                            font-size: 12px;
                        }
                    }
                }
            }
        }
    }

    .content-list {
        flex: 1;
        overflow-y: auto;
        background-color: #FBFBFB;
        display: flex;

        .left-list {
            //padding: 0 20px;
            width: 33%;
            overflow-y: auto;

            li {
                padding: 20px 5px 20px 0;;
                display: flex;
                align-items: center;

                &.choose-li {
                    background-color: #E1EFFF;
                    color: #1681FB;
                }

                &:last-child {
                    border-bottom: none;
                }

                > p {
                    width: 16px;
                    height: 16px;
                    background: #007aff;
                    border-radius: 50%;
                    color: #fff;
                    text-align: center;
                    line-height: 16px;
                    font-size: 12px;
                }

                .text {
                    font-size: 14px;
                    flex: 1;
                    margin-left: 18px;
                    font-weight: 400;

                    img {
                        color: #ffdf99;
                        width: 15px;
                        margin-left: 7px;
                        position: relative;
                        top: 2px;
                    }
                }

                .num {
                    display: flex;
                    width: 40px;
                    justify-content: flex-end;
                    align-items: center;

                    p {
                        color: #2b2b2b;
                        font-size: 16px;
                        margin: 0 7px;
                    }

                    > em {
                        color: #007aff;
                        font-size: 24px;
                    }

                    .jiant {
                        font-size: 14px;
                    }
                }
            }
        }

        .right-list {
            padding: 0 20px;
            width: 67%;
            background: #fff;

            li {
                padding: 25px 5px 25px 0;
                display: flex;
                align-items: center;
                color: #3d3d3d;
                border-bottom: 1px solid #ececec;

                &.choose-li {
                    background-color: #E1EFFF;
                    color: #1681FB;
                }

                &:last-child {
                    border-bottom: none;
                }

                > p {
                    width: 16px;
                    height: 16px;
                    background: #007aff;
                    border-radius: 50%;
                    color: #fff;
                    text-align: center;
                    line-height: 16px;
                    font-size: 12px;
                }

                .text {
                    font-size: 14px;
                    flex: 1;
                    margin-left: 18px;

                    img {
                        color: #ffdf99;
                        width: 15px;
                        margin-left: 7px;
                        position: relative;
                        top: 2px;
                    }
                }

                .num {
                    display: flex;
                    width: 40px;
                    justify-content: flex-end;
                    align-items: center;

                    p {
                        color: #2b2b2b;
                        font-size: 16px;
                        margin: 0 7px;
                    }

                    > em {
                        color: #007aff;
                        font-size: 24px;
                    }

                    .jiant {
                        font-size: 14px;
                    }
                }
            }
        }
    }

    .btn {
        width: 100%;
        height: 68px;
        display: flex;
        justify-content: flex-end;
        border-top: 1px solid #e1e1e1;
        align-items: center;

        .shop-car {
            width: 142px;
            height: 44px;
            background: #1681fb;
            border-radius: 22px;
            color: #fff;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 17px;

            > div {
                position: relative;

                p {
                    position: absolute;
                    width: 14px;
                    height: 14px;
                    background: #ef2f12;
                    text-align: center;
                    top: -2px;
                    right: 0;
                    border-radius: 50%;
                    font-size: 12px;
                    line-height: 14px;
                    padding: 2px;
                }
            }

            img {
                width: 31px;
                margin-right: 10px;
            }
        }
    }
}

.more-data {
    font-size: 12px;
    color: #8a8686;
    text-align: center;
    width: 100%;
    display: block;
    margin: 8px 0;
}

.cs-custominfo {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    height: auto;
    margin-top: 12px;
    border-radius: 9px;
    margin-bottom: 10px;
    color: #373737;
}

.cs-custominfo-telnum {
    padding: 0px 6px 0px 10px;
    font-size: 17px;
    font-weight: 600;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    align-items: center;
    color: #fff;


    .cs-custominfo-an {
        background: linear-gradient(to bottom, #FFD861, #FFBE33);
        font-size: 12px;
        color: white;
        border-radius: 3px;
        width: 20px;
        height: 20px;
        line-height: 20px;
        text-align: center;
        font-weight: normal;
        margin-left: 3px;
    }
}


.bottom-line {
    display: block;
    width: 100%;
}

.cs-userdetail {
    padding: 4px 5px 10px 20px;
}

.cs-username {
    font-size: 18px;
    font-weight: 400;
}


.cs-cstominfo-qry {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    background-image: url(../../../../static/img/new-custom-qrybg.png);

    .new-custom-box {
        display: flex;
        justify-content: center;
        flex-direction: row;
        align-items: center;
        /* padding: 6px 21px; */
        border-radius: 12px;
        border: 1.2px solid #fff;
        padding: 5px 10px;
        background: #fff;
        color: #3f88f0;

        .zhanghaoqiehuan {
            color: #2957C1;
            font-size: 21px;
            font-weight: 500;
        }

    }

    .new-custom-label {
        width: 26px;
        height: 23px;
    }

    .new-custom-txt {
        font-size: 11px;
        font-weight: 500;
    }
}

.custom-tag {
    width: auto;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 15px;
}

.cs-hn-tag-new {
    height: 19px;
    border-radius: 4px;
    text-align: center;
    line-height: 19px;
    color: #FFEB3B;
    font-size: 12px;
    border: 1px solid #FFEB3B;
    padding: 2px 7px;
    margin-left: 4px;
    white-space: nowrap;
}

.blue-line {
    flex: 0 0 6px;
    height: 19px;
    background: #007AFF;
    float: left;
    margin-right: 7px;
}
</style>
