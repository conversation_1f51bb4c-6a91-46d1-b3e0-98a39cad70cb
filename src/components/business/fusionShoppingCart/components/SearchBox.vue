<template>
  <div class="menu-search-container">
    <div class="menu-search-box">
      <em class="iconfont sousuo"></em>
      <input 
        ref="queryInput"
        v-model="localQueryKey" 
        class="menu-box" 
        placeholder="请输入" 
        @input="handleInput"
        @keyup.enter="handleSearch"
      />
      <em 
        v-show="localQueryKey" 
        class="iconfont shanchu" 
        @click="clearQuery"
      ></em>
      <span class="menu-search-txt" @click="handleSearch">搜索</span>
    </div>
  </div>
</template>

<script>
import { debounce } from '../utils/shoppingUtils'

export default {
  name: 'SearchBox',
  props: {
    queryKey: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请输入'
    },
    debounceTime: {
      type: Number,
      default: 300
    },
    autoSearch: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      localQueryKey: this.queryKey
    }
  },
  watch: {
    queryKey(newVal) {
      this.localQueryKey = newVal
    }
  },
  created() {
    // 创建防抖搜索函数
    this.debouncedSearch = debounce(this.emitSearch, this.debounceTime)
  },
  methods: {
    handleInput() {
      this.$emit('update:queryKey', this.localQueryKey)
      
      if (this.autoSearch) {
        this.debouncedSearch()
      }
    },
    
    handleSearch() {
      this.emitSearch()
    },
    
    emitSearch() {
      this.$emit('search', this.localQueryKey)
    },
    
    clearQuery() {
      this.localQueryKey = ''
      this.$emit('update:queryKey', '')
      this.$emit('clear')
      
      // 聚焦输入框
      this.$nextTick(() => {
        this.$refs.queryInput.focus()
      })
    },
    
    focus() {
      this.$refs.queryInput.focus()
    },
    
    blur() {
      this.$refs.queryInput.blur()
    }
  }
}
</script>

<style scoped lang="less">
.menu-search-container {
  width: 100%;
  background-color: #fff;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  height: 31px;

  .menu-search-box {
    flex: auto;
    position: relative;
    margin-right: 0.5rem;

    .menu-box {
      width: 100%;
      height: 30px;
      background: rgba(241, 241, 241, 1);
      border-radius: 1rem;
      box-sizing: border-box;
      outline: 0;
      border: none;
      padding-left: 2rem;
      padding-right: 5.5rem;
      overflow: hidden;
      font-size: 12px;
      transition: all 0.3s ease;

      &::placeholder {
        color: #b2b2b2;
      }

      &:focus {
        background: rgba(230, 230, 230, 1);
        box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.2);
      }
    }

    .sousuo {
      position: absolute;
      left: 0.75rem;
      top: 50%;
      transform: translateY(-50%);
      color: #b2b2b2;
      font-size: 20px;
      pointer-events: none;
    }

    .shanchu {
      position: absolute;
      right: 3rem;
      top: 50%;
      color: #b2b2b2;
      transform: translateY(-50%);
      font-size: 12px;
      cursor: pointer;
      padding: 4px;
      transition: color 0.3s ease;

      &:hover {
        color: #666;
      }
    }

    .menu-search-txt {
      position: absolute;
      right: 0.75rem;
      top: 50%;
      color: #007aff;
      transform: translateY(-50%);
      font-size: 12px;
      cursor: pointer;
      padding: 4px;
      transition: all 0.3s ease;

      &:hover {
        color: #0056cc;
        transform: translateY(-50%) scale(1.05);
      }

      &:active {
        transform: translateY(-50%) scale(0.95);
      }
    }
  }
}
</style>
