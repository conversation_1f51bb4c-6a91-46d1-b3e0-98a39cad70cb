<template>
  <div class="content-list" v-show="tabIndex != 2">
    <!-- 左侧业务列表 -->
    <ul class="left-list">
      <li 
        v-for="(item, index) in businessList" 
        :key="item.busiId || index"
        :class="{ 'choose-li': index === chooseIndex }"
        @click="handleBusinessClick(item, index)"
      >
        <div class="text">{{ item.busiDesc }}</div>
      </li>
    </ul>

    <!-- 右侧产品列表 -->
    <div class="right-list" v-show="hasMenuData">
      <!-- 步骤指示器 -->
      <div v-if="businessType == 2" class="step-indicator">
        <div v-show="step != 1" class="step-item">
          <div class="blue-line"></div>
          主套餐
        </div>
        <div v-show="step == 1" class="step-item">
          <div class="blue-line"></div>
          叠加包
        </div>
      </div>

      <!-- 主套餐列表 -->
      <ul v-show="step == 0">
        <li 
          v-for="(item, index) in menuList" 
          :key="item.attrId || index"
          @click="handleProductClick(item)"
          class="product-item"
        >
          <p class="info-btn" @click.stop="showProductDetail(item)">!</p>
          <div class="text">
            {{ item.prodName || item.busiDesc }}
            <img 
              v-show="item.attrValType == '1'" 
              src="@/assets/img/huore.png" 
              alt="热门" 
              class="hot-icon"
            />
          </div>
          <div class="num">
            <em class="iconfont youjiantou jiant"></em>
          </div>
        </li>
      </ul>

      <!-- 叠加包列表 -->
      <ul v-show="step == 1">
        <li 
          v-for="(item, index) in secondMenuList" 
          :key="item.busiId || index"
          @click="handleSecondMenuClick(item, index)"
          class="product-item"
        >
          <p class="info-btn" @click.stop="showProductDetail(item)">!</p>
          <div class="text">
            {{ item.busiDesc }}
            <img 
              v-show="index < 3" 
              src="@/assets/img/huore.png" 
              alt="热门"
              class="hot-icon"
            />
          </div>
          <div class="num">
            <em class="iconfont youjiantou jiant"></em>
          </div>
        </li>
      </ul>
    </div>

    <!-- 无数据提示 -->
    <NoDataPage 
      v-show="!hasMenuData" 
      class="right-list no-data"
      tipTxt="没有更多数据"
    />
  </div>
</template>

<script>
import NoDataPage from 'components/common/NoDataPage'
import { isEmptyArray } from '../utils/shoppingUtils'

export default {
  name: 'ProductList',
  components: {
    NoDataPage
  },
  props: {
    tabIndex: {
      type: Number,
      default: 1
    },
    businessList: {
      type: Array,
      default: () => []
    },
    menuList: {
      type: Array,
      default: () => []
    },
    secondMenuList: {
      type: Array,
      default: () => []
    },
    chooseIndex: {
      type: Number,
      default: 0
    },
    businessType: {
      type: [String, Number],
      default: ''
    },
    step: {
      type: Number,
      default: 0
    }
  },
  computed: {
    hasMenuData() {
      return !isEmptyArray(this.menuList)
    }
  },
  methods: {
    handleBusinessClick(item, index) {
      this.$emit('business-click', { item, index })
    },

    handleProductClick(item) {
      this.$emit('product-click', item)
    },

    handleSecondMenuClick(item, index) {
      this.$emit('second-menu-click', { item, index })
    },

    showProductDetail(item) {
      this.$emit('show-detail', item)
    }
  }
}
</script>

<style scoped lang="less">
.content-list {
  flex: 1;
  overflow-y: auto;
  background-color: #FBFBFB;
  display: flex;

  .left-list {
    width: 33%;
    overflow-y: auto;

    li {
      padding: 20px 5px 20px 0;
      display: flex;
      align-items: center;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background-color: #f0f8ff;
      }

      &.choose-li {
        background-color: #E1EFFF;
        color: #1681FB;
      }

      .text {
        font-size: 14px;
        flex: 1;
        margin-left: 18px;
        font-weight: 400;
        line-height: 1.4;
      }
    }
  }

  .right-list {
    padding: 0 20px;
    width: 67%;
    background: #fff;

    .step-indicator {
      font-size: 15px;
      color: rgb(35, 112, 243);
      margin-top: 15px;
      display: flex;
      overflow: hidden;
      align-items: center;

      .step-item {
        font-size: 15px;
        color: #2370f3;
        display: flex;
        align-items: center;

        .blue-line {
          width: 3px;
          height: 16px;
          background: #2370f3;
          margin-right: 8px;
          border-radius: 2px;
        }
      }
    }

    .product-item {
      padding: 25px 5px 25px 0;
      display: flex;
      align-items: center;
      color: #3d3d3d;
      border-bottom: 1px solid #ececec;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background-color: #f9f9f9;
        transform: translateX(2px);
      }

      &:last-child {
        border-bottom: none;
      }

      .info-btn {
        width: 16px;
        height: 16px;
        background: #007aff;
        border-radius: 50%;
        color: #fff;
        text-align: center;
        line-height: 16px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        flex-shrink: 0;

        &:hover {
          background: #0056cc;
          transform: scale(1.1);
        }
      }

      .text {
        font-size: 14px;
        flex: 1;
        margin-left: 18px;
        line-height: 1.4;

        .hot-icon {
          width: 15px;
          margin-left: 7px;
          position: relative;
          top: 2px;
        }
      }

      .num {
        display: flex;
        width: 40px;
        justify-content: flex-end;
        align-items: center;

        .jiant {
          color: #007aff;
          font-size: 14px;
          transition: transform 0.3s ease;
        }
      }

      &:hover .jiant {
        transform: translateX(3px);
      }
    }
  }

  .no-data {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 14px;
  }
}
</style>
