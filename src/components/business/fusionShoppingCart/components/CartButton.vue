<template>
  <div class="cart-button-container">
    <div class="shop-car" @click="handleCartClick">
      <div class="cart-icon-wrapper">
        <img src="@/assets/img/fusion-car.png" alt="购物车" />
        <p v-show="cartCount > 0" class="cart-badge">{{ displayCount }}</p>
      </div>
      <p class="cart-text">去购物车</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CartButton',
  props: {
    cartCount: {
      type: Number,
      default: 0
    },
    maxDisplayCount: {
      type: Number,
      default: 99
    }
  },
  computed: {
    displayCount() {
      return this.cartCount > this.maxDisplayCount 
        ? `${this.maxDisplayCount}+` 
        : this.cartCount
    }
  },
  methods: {
    handleCartClick() {
      this.$emit('cart-click')
    }
  }
}
</script>

<style scoped lang="less">
.cart-button-container {
  width: 100%;
  height: 68px;
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #e1e1e1;
  align-items: center;
  background: #fff;

  .shop-car {
    width: 142px;
    height: 44px;
    background: linear-gradient(135deg, #1681fb 0%, #0d5df6 100%);
    border-radius: 22px;
    color: #fff;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 17px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(22, 129, 251, 0.3);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(22, 129, 251, 0.4);
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 6px rgba(22, 129, 251, 0.3);
    }

    .cart-icon-wrapper {
      position: relative;
      margin-right: 10px;

      img {
        width: 31px;
        height: auto;
        filter: brightness(0) invert(1);
      }

      .cart-badge {
        position: absolute;
        width: auto;
        min-width: 14px;
        height: 14px;
        background: #ef2f12;
        text-align: center;
        top: -2px;
        right: -6px;
        border-radius: 7px;
        font-size: 10px;
        line-height: 14px;
        padding: 0 4px;
        box-sizing: border-box;
        font-weight: bold;
        border: 1px solid #fff;
        animation: pulse 2s infinite;
      }
    }

    .cart-text {
      font-weight: 500;
      letter-spacing: 0.5px;
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}
</style>
