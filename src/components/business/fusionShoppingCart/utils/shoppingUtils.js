/**
 * 购物车相关工具函数
 */

/**
 * 格式化电话号码
 * @param {string} telNum 电话号码
 * @returns {string} 格式化后的电话号码
 */
export function formatTelNum(telNum) {
  if (!telNum) return ''
  return telNum.replace(/^(\d{3})(\d{4})(\d{4})$/, '$1****$3')
}

/**
 * 深拷贝对象
 * @param {any} obj 要拷贝的对象
 * @returns {any} 拷贝后的对象
 */
export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime())
  if (obj instanceof Array) return obj.map(item => deepClone(item))
  if (typeof obj === 'object') {
    const clonedObj = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
}

/**
 * 防抖函数
 * @param {Function} func 要防抖的函数
 * @param {number} wait 等待时间
 * @param {boolean} immediate 是否立即执行
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, wait, immediate = false) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      timeout = null
      if (!immediate) func.apply(this, args)
    }
    const callNow = immediate && !timeout
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
    if (callNow) func.apply(this, args)
  }
}

/**
 * 节流函数
 * @param {Function} func 要节流的函数
 * @param {number} limit 时间限制
 * @returns {Function} 节流后的函数
 */
export function throttle(func, limit) {
  let inThrottle
  return function(...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * 检查数组是否为空
 * @param {Array} arr 要检查的数组
 * @returns {boolean} 是否为空
 */
export function isEmptyArray(arr) {
  return !arr || !Array.isArray(arr) || arr.length === 0
}

/**
 * 安全的数组访问
 * @param {Array} arr 数组
 * @param {number} index 索引
 * @param {any} defaultValue 默认值
 * @returns {any} 数组元素或默认值
 */
export function safeArrayAccess(arr, index, defaultValue = null) {
  return arr && Array.isArray(arr) && arr[index] !== undefined ? arr[index] : defaultValue
}

/**
 * 创建子产品信息
 * @param {Object} item 产品项
 * @returns {Object} 子产品信息
 */
export function createSubProductInfo(item) {
  const offerInfo = {
    offeringId: item.busiId,
    offeringName: item.iconUrl,
    mainOfferId: '1000100498',
    mainOfferName: '商客套餐-楼园办公'
  }

  if (item.configList && item.configList.length > 0) {
    const subOfferList = item.configList.map(config => ({
      offeringId: config.prodId,
      offeringName: item.busiDesc,
      subsId: config.attrId,
      subsName: config.attrDesc,
      mainOfferId: config.attrName,
      isNeed: config.isNeed
    }))
    offerInfo.subOfferList = subOfferList
  }

  return offerInfo
}

/**
 * 创建产品配置信息
 * @param {Object} item 产品项
 * @returns {Object} 产品配置信息
 */
export function createProductConfig(item) {
  const offerInfo = {
    offeringId: item.busiId,
    offeringName: item.busiDesc
  }

  if (item.configList && item.configList.length > 0) {
    const subOfferList = item.configList.map(config => ({
      offeringId: config.prodId,
      offeringName: config.attrDesc,
      isSub: config.attrValType,
      pkgId: config.attrId,
      prodType: config.attrField,
      packageId: config.attrName,
      packageName: config.prodName
    }))
    offerInfo.subOfferList = subOfferList
  }

  return offerInfo
}

/**
 * 检查是否需要虚拟集团
 * @param {Array} configList 配置列表
 * @param {string} targetAttrId 目标属性ID
 * @returns {boolean} 是否需要虚拟集团
 */
export function checkVirtualGroupRequired(configList, targetAttrId) {
  if (!configList || !Array.isArray(configList)) return false
  return configList.some(config => config.attrId === targetAttrId)
}

/**
 * 搜索列表项
 * @param {Array} list 列表
 * @param {string} keyword 关键词
 * @param {string} searchField 搜索字段
 * @returns {Array} 搜索结果
 */
export function searchListItems(list, keyword, searchField = 'attrName') {
  if (!keyword || !list || !Array.isArray(list)) return list
  
  return list.filter(item => {
    const fieldValue = item[searchField]
    return fieldValue && fieldValue.indexOf(keyword) !== -1
  })
}

/**
 * 处理API响应
 * @param {Object} response API响应
 * @returns {Object} 处理后的响应
 */
export function handleApiResponse(response) {
  const { retCode, retMsg, data } = response
  
  if (retCode === '0') {
    return { success: true, data, message: retMsg }
  } else {
    return { success: false, data: null, message: retMsg || '请求失败' }
  }
}

/**
 * 生成唯一ID
 * @returns {string} 唯一ID
 */
export function generateUniqueId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

/**
 * 延迟执行
 * @param {number} ms 延迟时间(毫秒)
 * @returns {Promise} Promise对象
 */
export function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms))
}
