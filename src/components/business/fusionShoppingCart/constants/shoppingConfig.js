/**
 * 购物车页面配置常量
 */

// 小类型列表配置
export const SMALL_TYPE_LIST = [
  { id: '', label: '全部' },
  { id: 'rtShops', label: '沿街商铺' },
  { id: 'rtBuilding', label: '楼宇办公' },
  { id: 'rtHotel', label: '酒店住宿' }
]

// Tab列表配置
export const TAB_LIST = [
  { name: '商客产品', index: 1 }, 
  { name: '场景产品', index: 0 }
]

// 业务类型映射
export const BUSINESS_TYPE_MAP = {
  BAND: '1',
  FUSION: '2', 
  VALUE_ADDED: '3',
  SECURITY: '4',
  NETWORK: '5',
  PRODUCT_CHOOSE: '6',
  TV: '7'
}

// 融合套餐硬编码配置
export const FUSION_PACKAGE_CONFIG = [
  {
    prodId: '1001',
    prodName: '楼园办公套餐139元/月（宽带300M、语音200分钟、流量30G、V网10人）',
    attrId: '2000013716',
    attrName: '融合套餐',
    attrDesc: null,
    attrValType: '1',
    attrDefault: null,
    attrValDesc: null,
    isNeed: '1',
    isEditable: '0',
    isDisplay: '1',
    state: '1',
    prodArea: '套餐',
    sort: '1',
    attrField: '1'
  },
  {
    prodId: '1001',
    prodName: '楼园办公套餐199元/月（宽带500M、语音1000分钟、流量60G、V网20人）',
    attrId: '2000013717',
    attrName: '融合套餐',
    attrDesc: null,
    attrValType: '1',
    attrDefault: null,
    attrValDesc: null,
    isNeed: '1',
    isEditable: '0',
    isDisplay: '1',
    state: '1',
    prodArea: '套餐',
    sort: '2',
    attrField: '1'
  },
  {
    prodId: '1001',
    prodName: '楼园办公套餐299元/月（宽带1000M、语音1500分钟、流量100G、V网50人）',
    attrId: '2000013718',
    attrName: '融合套餐',
    attrDesc: null,
    attrValType: '1',
    attrDefault: null,
    attrValDesc: null,
    isNeed: '1',
    isEditable: '0',
    isDisplay: '1',
    state: '1',
    prodArea: '套餐',
    sort: '3',
    attrField: '1'
  }
]

// 默认业务列表配置
export const DEFAULT_BUSINESS_CONFIG = {
  busiId: '5',
  busiName: '1001',
  iconUrl: '100061',
  busiDesc: '融合套餐',
  isProduct: '5',
  isAttr: '2',
  isDiscount: '0',
  isGood: '1',
  isNeedContract: '0',
  configList: FUSION_PACKAGE_CONFIG
}

// API 端点配置
export const API_ENDPOINTS = {
  PROMOTION_LIST: '/xsb/personBusiness/shopCart/h5qryPromotionListByServiceNum',
  OFFER_LIST: '/xsb/personBusiness/shopCart/h5qryOfferListByServiceNum',
  CART_COUNT: '/xsb/personBusiness/shopCart/h5qryAllNetShopCartListCount',
  BUSINESS_PRODUCT_LIST: '/xsb/personBusiness/cloudOrder/h5getBusiProductList',
  PRODUCT_CONFIG: '/xsb/personBusiness/cloudOrder/h5GetProductCfg',
  PRODUCT_DESC: '/xsb/personBusiness/businessOpenTv/h5QryProdDesc',
  VIRTUAL_GROUP_REGISTER: '/xsb/personBusiness/yiwtongUpgrade/h5CorporateCustRegister',
  VIRTUAL_GROUP_INFO: '/xsb/personBusiness/yiwtongUpgrade/h5QryVirtualGroupInfo',
  ALREADY_OPENED: '/xsb/personBusiness/personInfo/h5QryAlreadyOpened'
}

// 分页配置
export const PAGINATION_CONFIG = {
  DEFAULT_PAGE_SIZE: 10,
  DEFAULT_PAGE_NO: 1
}

// 特殊属性ID
export const SPECIAL_ATTR_IDS = {
  VIRTUAL_GROUP_REQUIRED: '2891001351'
}

// 电视开通状态
export const TV_STATUS = {
  NOT_OPENED: '0',
  ONE_TV: '1', 
  TWO_TV: '2',
  THREE_TV: '3'
}

// 集团购物车配置
export const GROUP_CART_CONFIG = {
  privId: '4952',
  picUrl: 'static/skzq/menu/tob.png',
  privName: '商客购物车(B)',
  opId: 'tob_business_home',
  opParentid: 'tob_business_home'
}
