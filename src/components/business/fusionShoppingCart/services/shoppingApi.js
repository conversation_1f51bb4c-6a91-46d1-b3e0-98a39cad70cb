/**
 * 购物车相关API服务
 */
import { API_ENDPOINTS, PAGINATION_CONFIG } from '../constants/shoppingConfig'

class ShoppingApiService {
  constructor(http) {
    this.http = http
    this.requestCache = new Map()
    this.requestTimers = new Map()
  }

  /**
   * 防抖请求
   * @param {string} key 请求唯一标识
   * @param {Function} requestFn 请求函数
   * @param {number} delay 防抖延迟时间
   */
  debounceRequest(key, requestFn, delay = 300) {
    // 清除之前的定时器
    if (this.requestTimers.has(key)) {
      clearTimeout(this.requestTimers.get(key))
    }

    return new Promise((resolve, reject) => {
      const timer = setTimeout(async () => {
        try {
          const result = await requestFn()
          this.requestTimers.delete(key)
          resolve(result)
        } catch (error) {
          this.requestTimers.delete(key)
          reject(error)
        }
      }, delay)
      
      this.requestTimers.set(key, timer)
    })
  }

  /**
   * 缓存请求结果
   * @param {string} key 缓存键
   * @param {Function} requestFn 请求函数
   * @param {number} ttl 缓存时间(毫秒)
   */
  async cachedRequest(key, requestFn, ttl = 5 * 60 * 1000) {
    const cached = this.requestCache.get(key)
    
    if (cached && Date.now() - cached.timestamp < ttl) {
      return cached.data
    }

    try {
      const data = await requestFn()
      this.requestCache.set(key, {
        data,
        timestamp: Date.now()
      })
      return data
    } catch (error) {
      throw error
    }
  }

  /**
   * 查询套餐列表
   */
  async queryOfferingList(params) {
    const { tabIndex, serviceNum, uinfo, queryKey, pageNo, pageSize, totalRecord, smallType } = params
    
    const requestParams = {
      beId: uinfo.region,
      serverNumber: serviceNum,
      serviceNum: serviceNum,
      type: '175',
      offerType: '1',
      totalRecord: totalRecord,
      keyWord: queryKey,
      beginRowNumber: (pageNo - 1) * pageSize,
      recordPerPage: pageSize,
      curPage: pageNo,
      promBusinessSubType: smallType.id
    }

    const url = tabIndex === 0 
      ? API_ENDPOINTS.PROMOTION_LIST 
      : API_ENDPOINTS.OFFER_LIST

    const response = await this.http.post(url, requestParams)
    return response.data
  }

  /**
   * 查询购物车数量
   */
  async queryCartNumber(serviceNum, pageNo = PAGINATION_CONFIG.DEFAULT_PAGE_NO, pageSize = PAGINATION_CONFIG.DEFAULT_PAGE_SIZE) {
    const params = {
      serviceNum: serviceNum,
      channelId: '',
      curPage: pageNo,
      pageSize: pageSize,
      unLoadFlg: true
    }

    const cacheKey = `cart_count_${serviceNum}`
    return this.cachedRequest(cacheKey, async () => {
      const response = await this.http.post(API_ENDPOINTS.CART_COUNT, params)
      return response.data
    }, 30000) // 30秒缓存
  }

  /**
   * 查询业务产品列表
   */
  async queryBusinessProductList(busiType = '5') {
    const cacheKey = `business_product_list_${busiType}`
    return this.cachedRequest(cacheKey, async () => {
      const response = await this.http.get(`${API_ENDPOINTS.BUSINESS_PRODUCT_LIST}?busiType=${busiType}`)
      return response.data
    })
  }

  /**
   * 查询产品配置
   */
  async queryProductConfig(productId) {
    const params = { productId }
    const response = await this.http.post(API_ENDPOINTS.PRODUCT_CONFIG, params)
    return response.data
  }

  /**
   * 查询产品描述
   */
  async queryProductDescription(packageCode) {
    const prodIdList = [{ packageCode }]
    const url = `${API_ENDPOINTS.PRODUCT_DESC}?prodIdList=${JSON.stringify(prodIdList)}`
    const response = await this.http.get(url)
    return response.data
  }

  /**
   * 创建虚拟集团
   */
  async createVirtualGroup(param) {
    const response = await this.http.post(API_ENDPOINTS.VIRTUAL_GROUP_REGISTER, param)
    return response.data
  }

  /**
   * 查询虚拟集团信息
   */
  async queryVirtualGroupInfo(serviceNumber) {
    const url = `${API_ENDPOINTS.VIRTUAL_GROUP_INFO}?serviceNumber=${serviceNumber}`
    const response = await this.http.get(url)
    return response.data
  }

  /**
   * 查询已开通信息
   */
  async queryAlreadyOpened(privid, telnum) {
    const url = `${API_ENDPOINTS.ALREADY_OPENED}?privid=${privid}&telnum=${telnum}`
    const response = await this.http.get(url)
    return response.data
  }

  /**
   * 清除缓存
   */
  clearCache(key) {
    if (key) {
      this.requestCache.delete(key)
    } else {
      this.requestCache.clear()
    }
  }

  /**
   * 清除所有定时器
   */
  clearAllTimers() {
    this.requestTimers.forEach(timer => clearTimeout(timer))
    this.requestTimers.clear()
  }

  /**
   * 销毁服务
   */
  destroy() {
    this.clearAllTimers()
    this.clearCache()
  }
}

export default ShoppingApiService
