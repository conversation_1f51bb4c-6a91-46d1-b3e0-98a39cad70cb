<template>
  <div>
    <div class='fusion-car wrapper-medias'>
      <!--暂时隐藏 :tsBtnTxt='isDelete?"退出管理":"管理"'> -->
      <Header tsTitleTxt='我的订购' backType='custom' @emGoPrev='goBack'></Header>
      <div class='first-page'>
        <ul class='gl-tab'>
          <li class='gl-tab-li' @click='qryOrderList(1)'>
            <span class='gl-tab-txt' :class='{"choose-type":busiType==1}'>TOB模式</span>
            <span class='txt-line' v-show='busiType==1'></span>
          </li>
          <li class='gl-tab-li' @click='qryOrderList(2)'>
            <span class='gl-tab-txt' :class='{"choose-type":busiType==2}'>TOC模式</span>
            <span class='txt-line' v-show='busiType==2'></span>
          </li>
        </ul>
      </div>
      <div class='flexs'>
        <div>
          <div class='list-wrapper'>
            <div class='choose-time'>
              <span class='iconfont sousuo'></span>
              <input class='group-name' type='text' placeholder='请输入集团/OneId/订单号/合同...' v-model='queryKey' />
              <span class='sousuo-button' @click='search()'>搜索</span>
            </div>
            <div class='sum-order-content'>
              <ul class='sum-order-data'>
                <li class='title-name' v-for='(item,index) in dataTypeList' :key='index'
                    :class="{'choose':orderState == item.id}" @click='changeState(item.id)'>
                  {{ item.label }}({{ item.sum || '0' }})
                </li>
              </ul>
            </div>
          </div>
          <div v-show='orderList && orderList.length > 0' class='ma-group'>
            <div>
              <mt-loadmore
                :top-method='loadTop'
                :bottom-method='loadBottom'
                :bottom-all-loaded='allLoaded'
                :auto-fill='false'
                topPullText='刷新'
                topDropText='释放更新'
                topLoadingText='刷新中···'
                bottomPullText='加载更多'
                bottomDropText='释放加载'
                bottomLoadingText='加载中···'
                ref='loadmore'
              >
                <ul class='order-list'>
                  <li v-for='(item,index) in orderList' :key='index' class='order-item' @click='openOrderDetail(item)'>
                    <!-- 订单头部信息 -->
                    <div class='order-header'>
                      <div class='order-info'>
                        <!--                        <img src='static/img/grid-ywa.png' class="order-icon" />-->
                        <span class='order-id'>{{ item.traceId }}</span>
                      </div>
                      <span class='order-status'
                            :class="{'status-completed': item.linkStateName=='已完成' || item.linkStateName=='已结单'}">
                        {{ item.linkStateName || '待实名' }}
                      </span>
                    </div>

                    <!-- 客户信息 -->
                    <div class='customer-info'>
                      <span class='customer-name'>{{ item.customerName }}</span>
                      <span class='customer-id'>({{ item.customerId }})</span>
                    </div>

                    <!-- 业务信息 -->
                    <div class='business-info'>
                      <div class='info-row'>
                        <span class='label'>产品类型：</span>
                        <span class='value'>{{ item.addCartItemName }}</span>
                        <!--                        <span class='action-type'>{{ item.actionType || '新增'}}</span>-->
                      </div>
                      <div class='info-row'>
                        <span class='label'>创建时间：</span>
                        <span class='value'>{{ item.createDate }}</span>
                      </div>
                    </div>

                    <!-- 流程导航 -->
                    <div class='process-navigation' v-if='orderLinkTypeList && orderLinkTypeList.length'>
                      <div class='navigation-container'>
                        <!-- 左侧：流程进度 -->
                        <div class='navigation-left'>
                          <div class='navigation-steps'>
                            <div v-for='(link, linkIndex) in orderLinkTypeList'
                                 :key='linkIndex'
                                 class='step-item'
                                 :class='getStepStatus(item, link, linkIndex)'
                                 @click.stop='onStepClick(item, link, linkIndex)'>
                              <div class='step-node'>
                                <div class='step-circle'>
                                  <span v-if="getStepStatus(item, link, linkIndex) === 'completed'"
                                        class='check-mark'>✓</span>
                                  <span v-else-if="getStepStatus(item, link, linkIndex) === 'current'"
                                        class='current-dot'></span>
                                </div>
                              </div>
                              <div class='step-label'>{{ link.label }}</div>
                            </div>
                          </div>
                        </div>

                        <!-- 右侧：下一步按钮 -->
                        <div class='navigation-right'>
                          <div class='next-step-btn' @click.stop='goToNextStep(item)'>
                            {{ getNextStepName(item) }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </li>
                </ul>
                <span class='more-data' v-show='moreData'>上拉加载数据</span>
              </mt-loadmore>
            </div>
          </div>
          <NoDataPage v-show='!orderList || orderList.length < 1' class='nodata' tipTxt='暂无记录'></NoDataPage>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import Header from 'components/common/Header.vue'
import { ShopCartrMixin } from '@/base/mixins/aizhiPaperMixin.js'
import Storage from '@/base/storage.js'
import jikeQueryFilter from '../../jikeBusinessOrder/jikeQueryFilter.vue'
import GroupList from '../../jikeBusinessOrder/jikeGroupList.vue'
import NoDataPage from 'components/common/NoDataPage'

export default {
  components: { GroupList, jikeQueryFilter, Header, NoDataPage },
  mixins: [ShopCartrMixin],
  data() {
    return {
      busiType: 1,//tob-1;toc-2
      pageNo: 1, //当前页
      pageSize: 10, //每页条数
      orderList: [], //算费项列表，算费接口返回
      orderLinkType: '',//查询工单环节状态 addCart-加购；realName-实名；confirmCart-结算；commitContract-合同生成；recharge-充值缴费;sign-签名；archived-归档
      orderLinkTypeList: [
        { id: 'addCart', label: '加购' },
        { id: 'realName', label: '实名' },
        { id: 'confirmCart', label: '结算' },
        { id: 'commitContract', label: '合同生成' },
        { id: 'recharge', label: '缴费' },
        { id: 'sign', label: '签名' },
        { id: 'archived', label: '归档' }],
      queryKey: '',
      showFilter: false,
      filterData: '',
      dataTypeList: [{ id: '', label: '全部', sum: 0 }, { id: '0', label: '进行中', sum: 0 }, {
        id: '1',
        label: '已归档',
        sum: 0
      }],
      orderState: '',
      allLoaded: false // 是否已加载完所有数据
    }
  },
  created() {
    this.jqData = Storage.session.get('jqData')
    this.uinfo = Storage.session.get('userInfo')
    this.serviceNum = this.jqData.telnum
    this.qryOrderList(1) //查询购物车列表
  },
  methods: {
    goBack() {
      //返回全网下单页面
      if (this.$route.query && this.$route.query.srcFrom == 'businessOrder') {
        this.$router.push('businessOrder')
      } else if (this.$route.query.srcFrom == 'tocShopping') {
        this.$router.push('tocShopping')
      } else {
        this.$router.push('fusionPlaceOrder')
      }

    },
    //上拉
    loadTop() {
      this.currentPage = 1
      this.allChecked = false
      this.qryTobOrderList()
      this.$refs.loadmore.onTopLoaded()
    },
    //下拉加载更多
    loadBottom() {
      if (!this.allLoaded) {
        this.pageNo += 1
        this.qryTobOrderList()
      }
      this.$refs.loadmore.onBottomLoaded()
    },
    search() {
      this.pageNo = 1
      this.qryOrderList(this.busiType)
    },
    qryOrderList(busiType) {
      this.busiType = busiType
      this.allLoaded = false // 重置加载状态
      if (this.busiType == 1) {
        this.qryTobOrderList()
      } else {

      }
    },
    changeState(orderState) {
      this.orderState = orderState
      // 重新查询订单列表
      this.pageNo = 1 // 重置页码
      this.allLoaded = false // 重置加载状态
      this.qryOrderList(this.busiType)
    },

    // 获取流程步骤状态
    getStepStatus(orderItem, step, stepIndex) {
      // 根据订单的当前环节状态判断步骤状态
      const currentLinkId = orderItem.latestLinkId || orderItem.linkId || ''
      const currentStepIndex = this.orderLinkTypeList.findIndex(link => link.id === currentLinkId)
      const latestLinkState = orderItem.latestLinkState || 0

      if (currentStepIndex === -1) {
        // 如果找不到当前环节，默认第一个为当前状态
        return stepIndex === 0 ? 'current' : 'pending'
      }

      if (stepIndex < currentStepIndex) {
        return 'completed' // 已完成 - 蓝色
      } else if (stepIndex === currentStepIndex) {
        // 如果当前环节的状态是已完成(latestLinkState=1)，则显示为已完成状态
        if (latestLinkState === 1) {
          return 'completed' // 已完成 - 蓝色
        } else {
          return 'current' // 当前环节 - 黄色
        }
      } else {
        return 'pending' // 未开始 - 灰色
      }
    },

    // 获取下一步环节名称
    getNextStepName(orderItem) {
      const currentLinkId = orderItem.latestLinkId || orderItem.linkId || ''
      const currentStepIndex = this.orderLinkTypeList.findIndex(link => link.id === currentLinkId)

      if (currentStepIndex === -1 || currentStepIndex >= this.orderLinkTypeList.length - 1) {
        return '已完成'
      }

      const nextStep = this.orderLinkTypeList[currentStepIndex + 1]
      return nextStep ? nextStep.label : '已完成'
    },

    // 处理步骤点击事件
    onStepClick(orderItem, step, stepIndex) {
      const stepStatus = this.getStepStatus(orderItem, step, stepIndex)

      // 只有已完成的步骤可以点击跳转到下一个环节
      if (stepStatus === 'completed') {
        const nextStepIndex = stepIndex + 1
        if (nextStepIndex < this.orderLinkTypeList.length) {
          const nextStep = this.orderLinkTypeList[nextStepIndex]
          this.$toast(`跳转到下一环节：${nextStep.label}`)
          // 这里可以根据具体业务需求进行跳转
          // 例如：this.$router.push(`/process/${nextStep.id}?orderId=${orderItem.orderId}`);
        } else {
          this.$toast('已是最后一个环节')
        }
      } else if (stepStatus === 'current') {
        this.$toast('当前环节正在进行中')
      } else {
        this.$toast('该环节尚未开始')
      }
    },

    // 处理下一步操作
    goToNextStep(orderItem) {
      const nextStepName = this.getNextStepName(orderItem)
      if (nextStepName === '已完成') {
        this.$toast('流程已完成')
        return
      }

      // 这里可以根据具体业务需求进行跳转或操作
      this.$toast(`即将进入：${nextStepName}`)
      // 例如：this.$router.push(`/process/${nextStepName}?orderId=${orderItem.orderId}`);
    },


    //查询TOB工单列表
    qryTobOrderList() {
      let param = {
        'busiType': 'oneClickOrderTob',
        'latestLinkId': this.orderLinkType,
        'pageNo': this.pageNo,
        'pageSize': this.pageSize,
        'orderState': this.orderState // 添加订单状态过滤
      }
      if (this.isGroupId(this.queryKey)) {
        param.customerId = this.queryKey
      } else {
        param.customerName = this.queryKey
      }
      let url = '/xsb/gridCenter/zqGroup/h5queryOneClickOrderList'
      this.$http.post(url, param).then((res) => {
        let { retCode, retMsg, data } = res.data
        if (retCode == '0') {
          if (data.orderStateNum) {
            this.dataTypeList[0].sum = data.orderStateNum.allNum || 0
            this.dataTypeList[1].sum = data.orderStateNum.doingNum || 0
            this.dataTypeList[2].sum = data.orderStateNum.doneNum || 0
          }

          if (this.pageNo == 1) {
            if (data && data.tobOrderInfoList && data.tobOrderInfoList.length > 0) {
              this.orderList = data.tobOrderInfoList
              this.allLoaded = data.tobOrderInfoList.length < this.pageSize
            } else {
              this.orderList = []
              this.allLoaded = true
              this.$toast('暂无数据')
            }
          } else {
            if (data && data.tobOrderInfoList && data.tobOrderInfoList.length > 0) {
              this.orderList.push(...data.tobOrderInfoList)
              this.allLoaded = data.tobOrderInfoList.length < this.pageSize
            } else {
              this.allLoaded = true
              this.$toast('暂无更多数据')
            }
          }
        } else {
          this.$toast(retMsg || '查询工单列表失败')
          if (this.pageNo == 1) {
            this.orderList = []
          }
        }
      })
    },
    isGroupId(str) {
      // 定义正则表达式，匹配 11 位纯数字字符串
      const regex = /^\d{11}$/
      // 使用正则表达式进行测试
      return regex.test(str)
    },

    checkCartOne(item) {
      //被锁不允许勾选
      if (item.status == '4') {
        return
      }
      if (item.checked) {
        item.checked = false
        this.checkedList = this.checkedList.filter((value) => item.itemId != value.itemId)
        // if (item.realFee) {
        //   this.allPrice = (parseFloat(this.allPrice) - parseFloat(item.realFee / 1000)).toFixed(2)
        // }
      } else {
        this.$set(item, 'checked', true)
        this.checkedList.push(item)
        // if (item.realFee) {
        //   this.allPrice = (parseFloat(this.allPrice) + parseFloat(item.realFee / 1000)).toFixed(2)
        // }
      }
    },
    // 全选
    allCheck() {
      this.checkedList = []
      this.allChecked = !this.allChecked
      this.shopCartList.forEach((product) => {
        product.itemList.forEach((item) => {
          if (item.status != '4') {
            this.$set(item, 'checked', this.allChecked)
            if (this.allChecked) {
              this.checkedList.push(item)
            }
          }
        })
      })
      // let allPrice = 0
      // if (this.allChecked) {
      //   for (let item of this.checkedList) {
      //     if (item.realFee) {
      //       allPrice = (parseFloat(allPrice) + parseFloat(item.realFee / 1000)).toFixed(2)
      //     }
      //   }
      // }
      // this.allPrice = allPrice
    },
    showCartItem(item) {
      this.qryItemInfo(item)
    },
    //关闭算费页面
    closeComponent() {
      this.showFeeFlag = false
      this.showItemInfoFlag = false
    },
    //查询是否有权限减免调测费
    qryOperAuth() {
      //减免令牌，目前写死
      var authId = '6013120191213104501'
      let url = '/xsb/personBusiness/businessOpen/h5qryOperAuth?authId=' + authId
      return this.$http.get(url).then((res) => {
        if (res.data.retCode == '0') {
          this.isReliefAuth = true
        }
      })
    },
    // 结算调用校验+算费接口
    async submit() {
      if (!this.checkedList || this.checkedList.length < 1) {
        this.$toast('请至少选择一项进行提交')
        return
      }
      let chooseList = []
      let shoppingItemIds = []
      chooseList = this.checkedList.map((item) => {
        let itemInfo = {
          itemId: item.itemId,
          accessType: item.accessType
        }
        shoppingItemIds.push(item.itemId)
        return itemInfo
      })
      this.items = shoppingItemIds.join('|')
      let param = {
        serviceNum: this.serviceNum, //服务号码
        shoppingCartItemList: JSON.stringify(chooseList)
      }
      await this.qryOperAuth()
      //购物车算费，返回费用项列表、总费用
      this.goCartSuanFei(param)
    },
    //购物车删除 index购物车父节点下标 item购物车项 idx购物车子节点下标
    deleteCarItems(index, item, idx) {
      this.$messagebox({
        title: '温馨提示',
        message: `是否要删除【${item.entityName}】商品套餐`,
        closeOnPressEscape: false,
        closeOnClickModal: false,
        showCancelButton: true,
        cancelButtonText: '取消',
        confirmButtonText: '确认'
      }).then((action) => {
        if (action == 'confirm') {
          this.delShoppingCartItem(index, item, idx)
        }
      })
    },
    //删除购物车项
    delShoppingCartItem(index, item, idx) {
      let shoppingCartItem = {
        itemId: item.itemId,
        accessType: item.accessType
      }
      let param = {
        serviceNum: this.serviceNum, //服务号码
        shoppingCartItemList: JSON.stringify([shoppingCartItem]) //购物车项列表
      }
      let url = `/xsb/personBusiness/shopCart/h5delShoppingCartItem`
      this.$http
        .post(url, param)
        .then((res) => {
          let { retCode, retMsg, data } = res.data
          if (retCode == '0') {
            this.shopCartList[index].itemList.splice(idx, 1)
            let checkIdx
            this.checkedList.forEach((cartItem, i) => {
              if (cartItem.itemId == item.itemId) {
                checkIdx = i
                // if (cartItem.realFee) {
                //   this.allPrice = (parseFloat(this.allPrice) - parseFloat(cartItem.realFee / 1000)).toFixed(2)
                // }
              }
            })
            if (checkIdx) {
              this.checkedList.splice(checkIdx, 1)
            }
          } else {
            this.$alert(retMsg || '购物车删除失败')
          }
        })
        .catch((response) => {
          this.$alert('购物车删除异常:' + response)
        })
    },
    //购物车解锁
    unlockCartItem(item) {
      this.$messagebox({
        title: '温馨提示',
        message: '请确认是否解锁该购物车项：' + item.entityName + '【' + item.itemId + '】',
        closeOnPressEscape: false,
        closeOnClickModal: false,
        showCancelButton: true,
        cancelButtonText: '取消',
        confirmButtonText: '确认'
      }).then((action) => {
        if (action == 'confirm') {
          let shoppingCartItemList = [{ itemId: item.itemId }]
          let param = {
            serviceNum: this.serviceNum, //服务号码
            shoppingCartItemList: JSON.stringify(shoppingCartItemList) //购物车项列表
          }
          let url = `/xsb/personBusiness/shopCart/h5unlockShopCartItem`
          this.$http
            .post(url, param)
            .then((res) => {
              if (res.data.retCode == '0') {
                this.$toast('解锁成功')
                this.qryShoppingCart() //刷新页面
              } else {
                this.$alert(res.data.retMsg || '购物车解锁失败')
              }
            })
            .catch((response) => {
              this.$alert('购物车解锁异常:' + response)
            })
        }
      })
    },
    goFusionOrder(tabIndex) {
      if (!this.isBusi) {
        this.$router.push(`/fusionPlaceOrder?tabIndex=${tabIndex}`)
      } else {
        this.$router.push(`/businessOrder?srcFrom=fusionCar`)
      }

    }
  },
  filters: {
    //状态转译
    getStatusName(status) {
      if (status == '1') {
        return '有效'
      } else if (status == '2') {
        return '失效'
      } else if (status == '3') {
        return '转订单'
      } else if (status == '4') {
        return '锁定'
      } else {
        return status
      }
    },
    getYuan(money) {
      if (money) {
        return Number(money) / 1000
      } else {
        return 0
      }
    }
  }
}
</script>

<style scoped lang='less'>
.fusion-car {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  background-color: #f4f5f6;
  padding: 45px 0 68px;

  .first-page {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    display: flex;
    flex-direction: column;

    .gl-tab {
      margin-top: 44px;
      height: 40px;
      background: #ffffff;
      border-top: 1px solid #EAEAEA;

      .gl-tab-li {
        width: 50%;
        display: flex;
        flex-direction: column;
        align-items: center;
        height: 39px;
        float: left;
        text-align: center;
        color: #333;
        line-height: 40px;
        border-bottom: 1px solid #EAEAEA;
      }

      .gl-tab-txt {
        position: relative;
        display: inline-block;
        height: 32px;
        color: #5a5a5a;

        &.choose-type {

          color: rgba(29, 109, 220, 1);
        }
      }

      .txt-line {
        background: linear-gradient(to left, rgba(2, 114, 254, 1), rgba(93, 174, 249, 1)); /* 渐变背景 */;
        width: 50px;
        height: 4px;
        margin-top: 5px;
      }
    }

    .searchs {
      background: linear-gradient(180deg, #F7FBFF 0%, #FFFFFF 100%);

      .gl-search {
        padding-bottom: 14px;
        background: #fff;
        flex-shrink: 0;
        position: relative;
        overflow: hidden;

        .gl-searchblock {
          margin-top: 10px;
          margin-left: 15px;
          margin-right: 50px;
          position: relative;
          height: 30px;
          border-radius: 4px;
          overflow: hidden;
          border: 1px solid #9DC2EB;
          width: 92%;

          .gl-ssicon {
            width: 20px;
            height: 20px;
            display: block;
            position: absolute;
            top: 5px;
            left: 5px;
            background-size: 80% 80%;
            background-repeat: no-repeat;
            background-position: center;
          }

          .gl-searchinpt {
            height: 20px;
            line-height: 36px;
            margin: 6px 0;
            font-size: 14px;
            color: #555;
            width: 100%;
            text-indent: 25px;
            outline: none;
            background: #FFFFFF;
          }

          .gl-ssclose {
            width: 20px;
            height: 20px;
            display: block;
            position: absolute;
            top: 5px;
            right: 40px;
            background-size: 80% 80%;
            background-repeat: no-repeat;
            background-position: center;
          }

          .gl-sst {
            display: block;
            width: 50px;
            height: 32px;
            line-height: 32px;
            position: absolute;
            right: 0;
            top: 0;
            color: #108EE9;
            font-size: 14px;
            text-align: center;
          }
        }

      }

      .search-content {
        height: 32px;
        line-height: 32px;
        display: flex;
        justify-content: space-between;
        padding: 0 12px;
        background: #fff;
        border-top: 1px solid #EAEAEA;

        .total {
          font-size: 14px;
          font-weight: 400;
          color: rgba(187, 187, 187, 1);
        }

        .pai-count {
          font-size: 14px;
          font-weight: 400;
          color: #187BEC;
        }

        .search-choose {
          font-size: 0;

          .search-title {
            font-size: 14px;
            font-weight: 400;
            color: rgba(187, 187, 187, 1);
            display: inline-block;
          }

          .choose {
            display: inline-block;

            .choose-name {
              font-size: 14px;
              font-weight: 400;
              margin-left: 10px;
              color: rgba(22, 129, 251, 1);
            }

            .choose-icon {
              color: rgb(189, 189, 189);
              font-size: 10px;
              margin-left: 3px;
            }
          }

          .go-report-jt {
            color: #1681FB;
            font-size: 16px;
            font-weight: 400;
            display: inline-block;
            transform: rotate(90deg);
          }
        }
      }
    }

    .gl-main {
      background: #ECF0FA;
      overflow: scroll;
      flex-grow: 1;
      -webkit-overflow-scrolling: touch;
      margin: 10px 0 0 0;

      .gl-mar {
        margin-bottom: 60px;

        .gl-ul {
          display: block;
          height: auto;
          margin: 0 15px 10px 15px;

          .gl-list {
            height: auto;
            display: block;
            background: #FFFFFF;
            box-shadow: 4px 4px 12px 0 rgba(184, 184, 184, 0.50);
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 10px;
            position: relative;
            padding: 20px 0 20px 0;

            .order-tag-l {
              position: absolute;
              top: 0;
              left: 0;
              padding: 3px 8px;
              border-top-left-radius: 8px;
              border-bottom-right-radius: 8px;
              background: #007AFF;
              font-size: 12px;
              color: white;
            }

            .order-tag-r {
              position: absolute;
              top: 20px;
              right: 20px;
              padding: 3px 8px;
              border-radius: 4px;
              font-size: 12px;
            }

            .orange {
              color: #FC7F04;
              background: #FFEBDB;
              border: 1px solid #FC7F04;
            }

            .blue {
              color: rgba(0, 122, 255, 1);
              background: rgba(62, 127, 227, 0.10);
              border: 1px solid rgba(0, 122, 255, 1);
            }

            .green {
              color: #00C853;
              background: #E8F5E9;
              border: 1px solid #00C853;
            }

            .red {
              color: rgba(255, 93, 93, 1);
              background: rgba(235, 71, 24, 0.17);
              border: 1px solid rgba(255, 93, 93, 1);
            }

            .order-main {
              display: flex;
              justify-content: center;

              .order-main-left {
                height: 100%;
                display: flex;
                flex-direction: column;
                width: 10%;

                img {
                  width: 20px;
                  height: 20px;
                }
              }

              .order-main-mid {
                height: 100%;
                width: 60%;
                display: flex;
                flex-direction: column;

                .mid-txt {
                  color: rgba(114, 114, 114, 1);
                  font-size: 14px;
                  margin: 5px 0;
                }

                .title {
                  font-size: 14px;
                  font-weight: bold;
                }
              }

              .order-main-right {
                width: 20%;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;

                .detail-icon {
                  font-size: 30px;
                  color: #0079FD;
                }
              }
            }
          }
        }
      }
    }
  }

  .content {
    padding: 12px;
    overflow-y: auto;
    white-space: nowrap;
    text-overflow: ellipsis;
    height: 80vh;

    > li {
      width: 100%;
      background-color: #fff;
      box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.05);
      border-radius: 4px;
      margin-bottom: 12px;

      .title {
        padding-left: 12px;
        border-bottom: 1px solid #e5e5e5;
        height: 42px;
        line-height: 42px;
        color: #222222;

        em {
          display: inline-block;
          margin-right: 10px;
          background: linear-gradient(135deg, #1681fb 0%, #56a5ff 100%);
          -webkit-background-clip: text;
          color: transparent;
        }
      }

      .bussiness {
        padding: 12px 11px;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #e5e5e5;

        &:last-child {
          border-bottom: none;
        }

        &.able {
          .ashbin {
            color: #007aff;
            padding: 6px;
            border-radius: 22px;
            border: 1px solid #007aff;
            font-size: 14px !important;
          }
        }

        &.disabled {
          background: #f3f3f3;

          .suo {
            border: 1px solid rgb(0, 122, 255);
            padding: 6px;
            border-radius: 22px;
            border: 1px solid rgb(0, 122, 255);
            font-size: 14px !important;
          }

          .title-name {
            color: #585858;
          }
        }

        .radio {
          font-size: 21px;

          &.juxing {
            color: #d8d8d8;
          }

          &.checkboxround1 {
            color: #1680f9;
          }
        }

        .detail-box {
          margin-left: 10px;
          flex: 1;

          > div {
            //margin-bottom: 8px;
            .title-name {
              font-size: 16px;
              white-space: break-spaces;
            }

            em {
              font-size: 15px;
              color: #d8d8d8;
              display: inline-block;
            }

            .shopCart-item-status {
              padding: 3px 4px;
              background: #d9eaff;
              border-radius: 2px 2px 2px 2px;
              font-size: 10px;
              font-weight: 500;
              color: #1681fb;
              vertical-align: bottom;
              display: inline-block;

              &.active {
                background: #e3e3e3;
                color: #3d3d3d;
              }
            }
          }

          .content-desc {
            color: #686868;
            font-size: 12px;
            margin-bottom: 5px;
          }

          > p {
            font-size: 14px;
            color: #f1260a;

            span {
              font-size: 10px;
              color: #d8d8d8;
              text-decoration: line-through;
              margin-left: 4px;
            }
          }
        }

        .num {
          display: flex;
          justify-content: flex-end;
          align-items: center;

          p {
            color: #2b2b2b;
            font-size: 15px;
            margin: 0 6px;
          }

          > em {
            color: #007aff;
            font-size: 22px;
          }

          .bianji1 {
            color: #d8d8d8;
            font-size: 19px;
            display: inline-block;
            margin-left: 12px;
          }
        }
      }
    }
  }

  .footer {
    width: 100%;
    height: 68px;
    background-color: #fff;
    border-top: 1px solid #e5e5e5;
    position: fixed;
    bottom: 0;
    left: 0;
    display: flex;
    align-items: center;

    .all-check {
      font-size: 21px;
      display: inline-block;
      margin-left: 12px;
      color: #d8d8d8;

      &.checkboxround1 {
        color: #1680f9;
      }
    }

    > p {
      font-size: 14px;
      color: #686868;
      margin-left: 8px;
    }

    .total {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      margin-right: 12px;

      p {
        font-size: 12px;
        margin-right: 11px;

        span {
          font-size: 14px;
          color: #f1260a;
        }
      }

      > div {
        width: 100px;
        height: 44px;
        background: #1681fb;
        border-radius: 22px;
        text-align: center;
        line-height: 44px;
        color: #fff;
      }
    }

    .delete {
      p {
        color: #9e9e9e;
        font-size: 14px;
        margin-right: 25px;
      }

      div {
        background: #f94144;
      }
    }
  }
}

.shopCart-item-suo {
  color: #007aff;
  padding: 8px 6px;
  border-radius: 15px;
  font-weight: 600;
}

.no-product {
  height: 50px;
  position: relative;

  > span {
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    text-decoration: underline;
    font-size: 13px;
    color: #d9d8d8;
  }
}

.flexs {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #FFFFFF;

  > div {
    height: 100%;
  }

  .list-wrapper {
    height: 64px;
    z-index: 100;
    margin-top: 23px;
    background-color: #F7FBFF;
    //background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0), #fbfbfb), url('../../../../../static/img/jikeOrder-1.png');
    padding: 30px 15px;

    .choose-time {
      background-color: #fff;
      padding: 10px 10px;
      font-size: 14px;
      border-radius: 8px;
      //color: rgba(0, 0, 0, 0.45);
      line-height: 20px;
      color: #26a2ff;
      border: 1px solid #9DC2EB;

      .sousuo {
        color: #828282;
      }

      .split-span {
        font-size: 16px;
        color: #cdcdcd;
      }

      .filter-button {
        color: #ed8a1d;
      }

      .filter-number {
        margin-left: -3px;
        position: absolute;
        color: white;
        font-size: 8px;
        background-color: #ff8117;
        top: 82px;
        text-align: center;
        border-radius: 24px;
        padding: 4px;
        white-space: nowrap;
        display: inline-block;
        height: 4px;
        line-height: 4px;
      }

      .group-name {
        color: rgba(36, 36, 36, 0.45);
        font-size: 14px;
        width: 64%;
        outline: none;
        border: none;
        background: transparent;
        pointer-events: auto;
        z-index: 1;
        position: relative;
        text-indent: 0.5rem;

        &::placeholder {
          color: #A1A1A1;
        }
      }

      .sousuo-button {
        position: relative;
        float: right;
        color: #1681FB;
      }

      img {
        width: 12px;
        vertical-align: text-top;
      }
    }

    .sum-order-content {
      margin-top: 15px;
    }

    .sum-order-filter {
      display: flex;
      justify-content: space-between;
      width: 100%;
      white-space: nowrap;


    }

    .split-line {
      border-bottom: 1px dashed #B0DEF0;
      margin: 10px 5px 15px 5px;
    }

    .sum-order-data {
      display: flex;
      justify-content: space-between;
      //margin: 0 10px;

      .title-name {
        font-size: 14px;
        color: #565656;
        line-height: 30px;
        width: 33%;
        text-align: center;

        &.choose {
          border-bottom: 2px solid #26a2ff;
          color: #26a2ff;
          background: #007AFF;
          border-radius: 8px 8px 0px 0px;
          color: #fff;
        }
      }

      .sum-number {
        font-size: 12px;
        color: #0E64C2;
        line-height: 31px;
        text-align: left;
        font-style: normal;
        text-transform: none;

        span {
          font-size: 24px;
          margin-right: 2px;

        }
      }

    }

  }


  .search-btn {
    background: #1681FB;
    color: #fff;
    text-align: center;
    width: 55px;
    border-radius: 16px;
    border: 1px solid #1681fb;
    box-sizing: border-box;
    position: absolute;
    float: right;
    top: 230px;
    right: 30px;
    font-size: 13px;
    line-height: 24px;
    font-style: normal;
    text-transform: none;
  }

  .ma-group {
    height: 100%;
    overflow-y: auto;
    display: block;
    flex-grow: 1;
    -webkit-overflow-scrolling: touch;
    padding: 10px 20px;
    font-size: 14px;
    line-height: 20px;
    background-color: #fbfbfb;
    box-shadow: 0px 0px 8px 0px rgba(167, 167, 167, 0.2);


    .order-list {
      display: flex;
      flex-direction: column;
      margin-bottom: 182px;
      gap: 12px;

      .order-item {
        background: #fff;
        border-radius: 12px;
        padding: 16px;
        position: relative;
        box-sizing: border-box;
        width: 100%;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        cursor: pointer;

        &:hover {
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
          transform: translateY(-2px);
        }

        &:active {
          transform: translateY(0);
        }

        // 订单头部样式
        .order-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .order-info {
            display: flex;
            align-items: center;

            .order-icon {
              width: 16px;
              margin-right: 8px;
            }

            .order-id {
              font-size: 16px;
              font-weight: 600;
              color: #333;
            }
          }

          .order-status {
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: 500;
            background: #ffeeba;
            color: #e0a700;

            &.status-completed {
              background: #e8f5e8;
              color: #2e7d32;
            }
          }
        }

        // 客户信息样式
        .customer-info {
          margin-bottom: 6px;

          .customer-name {
            //font-size: 15px;
            //font-weight: 500;
            color: #333;
          }

          .customer-id {
            //font-size: 14px;
            color: #333;
            //margin-left: 4px;
          }
        }

        // 业务信息样式
        .business-info {
          margin-bottom: 12px;

          .info-row {
            display: flex;
            align-items: center;
            margin-bottom: 6px;

            &:last-child {
              margin-bottom: 0;
            }

            .label {
              font-size: 13px;
              color: #888;
              flex-shrink: 0;
            }

            .value {
              font-size: 13px;
              color: #333;
              margin-right: 8px;
            }

            .action-type {
              font-size: 13px;
              color: #1976d2;
              background: #e3f2fd;
              padding: 2px 8px;
              border-radius: 12px;
            }
          }
        }

        // 流程导航样式
        .process-navigation {
          border-top: 1px solid #f0f0f0;
          padding-top: 16px;

          .navigation-container {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: 16px;
          }

          .navigation-left {
            flex: 1;
            min-width: 0; // 防止flex子项溢出

            .navigation-title {
              font-size: 13px;
              color: #333;
              font-weight: 500;
              margin-bottom: 12px;
            }
          }

          .navigation-right {
            flex-shrink: 0; // 防止按钮被压缩
            display: flex;
            align-items: center;

            .next-step-btn {
              background: linear-gradient(135deg, #1681fb 0%, #56a5ff 100%);
              color: #fff;
              padding: 6px 16px;
              border-radius: 12px;
              font-size: 12px;
              font-weight: 500;
              cursor: pointer;
              transition: all 0.3s ease;
              white-space: nowrap;

              &:hover {
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(22, 129, 251, 0.3);
              }

              &:active {
                transform: translateY(0);
              }
            }
          }

          .navigation-steps {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            position: relative;

            .step-item {
              display: flex;
              flex-direction: column;
              align-items: center;
              flex: 1;
              position: relative;

              .step-node {
                display: flex;
                align-items: center;
                justify-content: center;
                position: relative;

                .step-circle {
                  width: 10px;
                  height: 10px;
                  border-radius: 50%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  position: relative;
                  z-index: 2;
                  font-size: 6px;

                  .current-dot {
                    width: 3px;
                    height: 3px;
                    border-radius: 50%;
                    background: #fff;
                  }

                  .check-mark {
                    color: #fff;
                    font-weight: bold;
                    font-size: 7px;
                  }
                }
              }

              // 连接线单独处理，放在step-item层级
              &:not(:last-child)::after {
                content: '';
                position: absolute;
                top: 5px; // 圆圈中心位置 (10px / 2)
                left: calc(50% + 5px); // 从圆圈右边开始
                right: calc(-50% + 5px); // 到下一个圆圈左边
                height: 1px;
                z-index: 1;
              }

              &.completed:not(:last-child)::after {
                background: #1681fb;
              }

              &.current:not(:last-child)::after,
              &.pending:not(:last-child)::after {
                background: #e0e0e0;
              }

              .step-label {
                font-size: 10px;
                margin-top: 4px;
                text-align: center;
                white-space: nowrap;
                max-width: 45px;
                overflow: hidden;
                text-overflow: ellipsis;
              }

              // 已完成状态 - 蓝色（与下一步按钮一致）
              &.completed {
                cursor: pointer; // 添加手型光标，表示可点击
                transition: transform 0.2s ease;

                &:hover {
                  transform: scale(1.05); // 悬停时轻微放大
                }

                .step-circle {
                  background: linear-gradient(135deg, #1681fb 0%, #56a5ff 100%);
                  color: #fff;
                  border: 2px solid #1681fb;
                }

                .step-label {
                  color: #1681fb;
                  font-weight: 500;
                }
              }

              // 当前状态 - 黄色
              &.current {
                .step-circle {
                  background: #ffc107;
                  border: 2px solid #ffc107;
                  animation: pulse 2s infinite;
                }

                .step-label {
                  color: #ffc107;
                  font-weight: 600;
                }
              }

              // 未开始状态 - 灰色
              &.pending {
                .step-circle {
                  background: #f5f5f5;
                  border: 2px solid #e0e0e0;
                }

                .step-label {
                  color: #999;
                }
              }
            }
          }
        }

        // 脉冲动画
        @keyframes pulse {
          0% {
            box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7);
          }
          70% {
            box-shadow: 0 0 0 10px rgba(255, 193, 7, 0);
          }
          100% {
            box-shadow: 0 0 0 0 rgba(255, 193, 7, 0);
          }
        }

        .people-info {
          width: 100%;
          display: flex;

          > div {
            white-space: nowrap;
          }


          .split {
            width: 1px;
            height: 10px;
            background-color: #bcb2b2;
            display: inline-block;
            margin: 0 0.02rem;
          }

          .title-name {
            color: #979797;
            white-space: nowrap;
            //color: #2e2e2e;
          }

          span {
            font-size: 14px;
            //color: #2e2e2e;
            color: #747474;

          }

          a {
            text-decoration: none;
            display: inline-block;

            .gl-sublist-dasval-tel {
              color: #26a2ff;
              font-size: 14px;
              line-height: 20px;
              height: auto;
              display: block;
              text-align: justify;
              border: none;
            }
          }

          .xingzhuang7 {
            font-size: 13px;
            color: #26a2ff;
          }
        }

        .right-content {
          display: flex;
          flex-direction: row;
          width: 100%;
          justify-content: space-between;
          margin-bottom: 2px;

          > span {
            font-size: 12px;
            color: #8d8d8d;
          }

          .status {
            color: #69a5f7;
            padding: 4px;
            font-size: 12px;
            /* margin-left: 6px; */
            background-color: #edf4ff;
            border-radius: 4px;


            &.done {
              color: #79d668;
              background-color: #e1fff0;
            }
          }


          .product-name {
            font-size: 14px;
            color: #404040;
            line-height: 13px;
            display: flex;
            align-items: center;

            .order-num {
              font-size: 15px;
              margin-left: 3px;
              color: #535151;
              font-weight: 600;
            }


          }

          .sum-number {
            font-size: 12px;
            color: #4D4D4D;
            line-height: 25px;

            span :first-child {
              font-size: 23px;
              font-family: inherit;

            }
          }

          .total-number {
            font-size: 12px;
            color: #969696;
            line-height: 12px;
            text-align: left;
          }

          img {
            width: 14px;

          }

        }
      }
    }


    .ma-glis.active .icon-ma-jt2 {
      transform: rotate(-180deg);
      color: #007AFF;
    }

    .ma-glis.active {
      display: block;
    }

    .more-data {
      font-size: 12px;
      color: #8a8686;
      text-align: center;
      width: 100%;
      display: block;
      margin: 8px 0;
    }
  }

  .nodata {
    margin-top: 45px;
  }
}
</style>
