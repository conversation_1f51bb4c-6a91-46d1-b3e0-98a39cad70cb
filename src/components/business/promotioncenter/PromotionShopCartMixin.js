import AuthenctOld from 'components/common/Authenticate/Authenticate';
import Authenct from 'components/common/uniteauth/index.js';
import {dateFormat} from '@/base/utils';
import ClientJs from '@/base/clientjs.js'
import { Indicator } from 'mint-ui'

//爱知无纸化mixin公用方法
export const PromotionShopCartMixin = {
  data() {
    return {}
  },
  computed: {
    //是否展示附加属性选择
    showFuJiaFlag() {
      let flag = false;
      if (this.yuYinFlag || this.isBindImei || this.promCoinAmount) {
        flag = true;
      }
      if (this.hefenqiFlag || this.xinyonggouFlag || this.zhimaFlag) {
        flag = true;
      }
      if(this.cjzPhotoFlag){
        flag = true;
      }
      return flag;
    },

    //是否展示附购物车提交按钮
    showNlButtonShop() {
      let flag = true;
      if (this.yuYinFlag) {
        flag = false;
      }
    if ((this.hefenqiFlag && !this.savingsCardMaShopFlag) || this.xinyonggouFlag || (this.zhimaFlag && !this.zhiMaShopFlag)) {
        flag = false;
      }
      return flag;
    },

  },
  methods: {
    //检查是否需要选择云电脑属性
    checkYunDianComputerProm(sitem) {
      if(sitem.checkFlg){
        return this.yunDianComputerListProm.hasOwnProperty(sitem.offeringId);
      }else{
        return false;
      }
    },
    //获取云电脑列表
    getYunDianComputerProm() {
      // let url = `/xsb/personBusiness/promotionCenter/h5dictCommonAdditionalAttr?dictId=1129`;
      // this.$http.get(url,{unLoadFlg:true}).then(res => {
      //   if (res.data.retCode == '0') {
          this.yunDianComputerListProm = {"2000062282":{"dictName":"2000062282","value":"2000062282","value1":"云电脑调测服务（50元）","region":"99"}};
          // console.log( JSON.stringify(this.yunDianComputerListProm))
      //   }
      // }).catch((response) => {
      //   this.$toast("云电脑列表查询异常" + response);
      // });
    },

    // 获取垫资方列表
    getDzfList() {
      let url = `/xsb/personBusiness/creditLoan/h5Underwriters?orgId=${this.orgId}`;
      this.$http.get(url, {unLoadFlg: true}).then(res => {
        let result = res.data;
        if ('0' == result.retCode) {
          this.dzfList = result.data;
          if (!this.dzfList || this.dzfList.length < 1) {
            this.$alert(result.retMsg);
          }
        } else {
          this.$alert(result.retMsg || '获取垫资方列表失败');
        }
      }).catch((response) => {
        this.$alert('获取垫资方列表网络超时' + response);
      });
    },

    // 叠加包选择
    overpkgChoose(item) {
      if (this.overpackvalue == item) {
        this.overpackvalue = "";
      } else {
        this.overpackvalue = item;
      }
    },
    //信用购资方
    dzfChoose(item) {
      this.ckDzfId = item.agentmerno;
      this.ckDzfName = item.agentmername;
    },
    //提交 按钮点击 事件
    submitSth() {
      //副号
      if (this.enterSpeNumber == '1') {
        if (!this.subnumber) {
          this.$toast('请在列表最下方输入指定号码');
          return;
        } else {
          let phoneReg = /^1[345789]\d{9}$/;
          if (!phoneReg.test(this.subnumber)) {
            this.$toast('请输入正确手机号');
            return;
          }
        }
      }
      //机卡互锁号码
      if (this.isBindImei) {
        if (!this.isBindNumber) {
          this.$toast('请在列表最下方输入指定号码');
          return;
        } else {
          let phoneReg = /^1[345789]\d{9}$/;
          if (!phoneReg.test(this.isBindNumber)) {
            this.$toast('请输入正确手机号');
            return;
          }
        }
      }
      //和分期提交
      if (this.hefenqiFlag) {
        if (this.overlayPkg.length > 0 && this.overpackvalue == '') {
          this.$messagebox({
            title: '温馨提示',
            message: '该活动下有购机叠加包可供选择办理（请滑至页面下方选择），是否办理？',
            showCancelButton: true,
            showConfirmButton: true,
            confirmButtonText: "返回选择",
            cancelButtonText: "继续提交"
          }).then(action => {
            if (action == 'confirm') {
              return false;
            } else {
              //包贷款申请
              this.checkChuanhao();
            }
          })
        } else {
          //包贷款申请
          this.checkChuanhao();
        }
        //信用购提交
      } else if (this.xinyonggouFlag) {
        if (this.ckDzfId != '') {
          this.xinYongGouSubmit();
        } else {
          this.$toast('请选择资方');
          return false;
        }
        //芝麻信用提交
      } else if (this.zhimaFlag) {
        this.zhiMaSubmit();
      }

    },


    //串号校验,包贷款申请
    checkChuanhao() {
      let rewlist = [];
      let checkInfo = {};
      for (let j = 0; j < this.adaptationList.length; j++) {
        let count = 0;
        if (this.adaptationList[j].checkFlg) {
          for (let i = 0; i < this.adaptationList[j].promOfferList.length; i++) {
            if (this.adaptationList[j].promOfferList[i].checkFlg) {
              count = count + 1;
              if (this.adaptationList[j].adaptationType == 'O') {
                checkInfo = {
                  rewardPkgId: this.adaptationList[j].adaptationId,//条件编码
                  rewardId: this.adaptationList[j].promOfferList[i].offeringId,//条件对象编码 商品编码
                  rewardSn: '',//串号
                  rewardQuantity: '1',//数量
                  rewardApd: '',//属性
                  rewardDrawtype: '',//领取方式
                  rewardName: this.adaptationList[j].promOfferList[i].offeringName,//商品名称
                };
              } else {
                if (this.adaptationList[j].promOfferList[i].identityId == '' || this.adaptationList[j].promOfferList[i].identityId == undefined) {
                  this.$alert("请选择串号");
                  return
                }
                checkInfo = {
                  rewardPkgId: this.adaptationList[j].adaptationId,//条件编码
                  rewardId: this.adaptationList[j].promOfferList[i].offerCode,//条件对象编码 商品编码
                  rewardSn: this.adaptationList[j].promOfferList[i].identityId,//串号
                  rewardQuantity: '1',//数量
                  rewardApd: '',//属性
                  rewardDrawtype: '',//领取方式
                  rewardName: this.adaptationList[j].promOfferList[i].offerName,//商品名称
                };
              }
              rewlist.push(checkInfo);
            }
          }
        }
        if (this.adaptationList[j].offerMinNumber && this.adaptationList[j].offerMinNumber > count) {
          let str = "条件:[" + this.adaptationList[j].adaptationId + "]" + this.adaptationList[j].adaptationName + "的最小选择数量为"
            + this.adaptationList[j].offerMinNumber;
          if (this.adaptationList[j].offerMaxNumber && this.adaptationList[j].offerMaxNumber < count) {
            str += ",最大选择数量为" + this.adaptationList[j].offerMaxNumber;
          }
          this.$alert(str);
          return;
        } else {
          if (this.adaptationList[j].offerMaxNumber && this.adaptationList[j].offerMaxNumber < count) {
            let str = "条件:[" + this.adaptationList[j].adaptationId + "]" + this.adaptationList[j].adaptationName
              + "的最大选择数量为" + this.adaptationList[j].offerMaxNumber;
            this.$alert(str);
            return;
          }
        }
      }
      if (rewlist.length == 0 && this.adaptationList.length > 0) {
        this.$alert('请选择商品');
        return
      }
      let self = this;
      //鉴权没通过
      if(this.jqData.result != '1') {
        Authenct({
          popFlag: true,
          idCardWay: this.jqData.idCardWay,//是否需要身份证鉴权,
          readOnlyFlag: true,
          telnum: this.telnum
        }, function(obj) {
          self.telnum = obj.telnum;
          self.jqData = obj;
          self.h5ApplyLoans(rewlist);
        });
      }else{
        self.h5ApplyLoans(rewlist);
      }
    },
    //和分期提交
    h5ApplyLoans(rewlist) {
      let levelAttrList = [];
      if (this.subnumber != "") {
        let vicenum = {
          vicenum: this.subnumber
        };
        levelAttrList.push(vicenum);
      }
      let savingsCardNum="";
      let topTipFlag='2';
      if(this.savingsCardFlag){
        savingsCardNum="1";
        topTipFlag='4';
      }

      let url = '/xsb/personBusiness/installmenthe/h5ApplyLoans';
      let param = {
        "overpackvalue": this.overpackvalue,
        "telnum": this.jqData.telnum,
        "offerId": "Promotion",
        "levelId": this.promotionId,
        "rewardList": JSON.stringify(rewlist),
        "offerName": this.actPlanName,
        "levelName": this.promotionName,
        "levelAttrList": JSON.stringify(levelAttrList),
        "bindTelnum": this.isBindNumber,
        "savingsCardFlag":savingsCardNum,//储蓄卡冻结
      }
      this.$http.post(url, param).then(res => {
        if (res.data.retCode == '0') {
          this.$messagebox.alert('提交成功,稍后可在【我的订单】查询贷款进度', '温馨提示').then(action => {
            this.$router.push({
              path: '/promotionOrder',
              query: {
                srcFrom: this.srcFrom,
                topTipFlag: topTipFlag,
              }
            })
          });
        } else {
          this.$alert(res.data.retMsg || '提交奖品失败');
        }
      }).catch((response) => {
        this.$alert('提交奖品网络连接失败');
      })
    },
    //芝麻信用商品校验
    zhiMaSubmit() {
      let rewardListStr = '';
      let rewardList = [];
      let rewardIdList = [];
      let checkInfo = {};
      let imei = "";
      for (let j = 0; j < this.adaptationList.length; j++) {
        let count = 0;
        let imeicount = 0;//终端数量
        if (this.adaptationList[j].checkFlg) {
          for (let i = 0; i < this.adaptationList[j].promOfferList.length; i++) {
            if (this.adaptationList[j].promOfferList[i].checkFlg) {
              count = count + 1;
              rewardListStr = rewardListStr + this.adaptationList[j].adaptationId + '|';
              if (this.adaptationList[j].adaptationType == 'O') {
                rewardListStr = rewardListStr + this.adaptationList[j].promOfferList[i].offeringId + ';';
                checkInfo = {
                  rewardId: this.adaptationList[j].promOfferList[i].offeringId,// 商品编码
                  rewardname: this.adaptationList[j].promOfferList[i].offeringName,//商品名称
                };
                rewardIdList.push(this.adaptationList[j].promOfferList[i].offeringId);
              } else {
                rewardListStr = rewardListStr + this.adaptationList[j].promOfferList[i].offerId + ';';
                checkInfo = {
                  rewardId: this.adaptationList[j].promOfferList[i].offerId,// 商品编码
                  rewardname: this.adaptationList[j].promOfferList[i].offerName,//商品名称
                };
                rewardIdList.push(this.adaptationList[j].promOfferList[i].offerId);

                if (this.adaptationList[j].promOfferList[i].identityId != null &&
                  this.adaptationList[j].promOfferList[i].identityId != undefined && this.adaptationList[j].promOfferList[i].identityId != '') {
                  imei = this.adaptationList[j].promOfferList[i].identityId;//串号
                  imeicount = imeicount + 1;
                }
              }
              rewardList.push(checkInfo);
            }
          }
        }

        if (imeicount > 1) {
          this.$alert("芝麻信用类促销活动最多只支持一个终端");
          return;
        }


        if (this.adaptationList[j].offerMinNumber && this.adaptationList[j].offerMinNumber > count) {
          let str = "条件:[" + this.adaptationList[j].adaptationId + "]" + this.adaptationList[j].adaptationName + "的最小选择数量为"
            + this.adaptationList[j].offerMinNumber;
          if (this.adaptationList[j].offerMaxNumber && this.adaptationList[j].offerMaxNumber < count) {
            str += ",最大选择数量为" + this.adaptationList[j].offerMaxNumber;
          }
          this.$alert(str);
          return;
        } else {
          if (this.adaptationList[j].offerMaxNumber && this.adaptationList[j].offerMaxNumber < count) {
            let str = "条件:[" + this.adaptationList[j].adaptationId + "]" + this.adaptationList[j].adaptationName
              + "的最大选择数量为" + this.adaptationList[j].offerMaxNumber;
            this.$alert(str);
            return;
          }
        }
      }
      if (rewardList.length == 0 && this.adaptationList.length > 0) {
        this.$alert('请选择商品');
        return
      } else {
        rewardListStr = rewardListStr.substr(0, rewardListStr.length - 1);
      }

      //鉴权没通过
      if(this.jqData.result != '1') {
        Authenct({
          popFlag: true,
          idCardWay: this.jqData.idCardWay,//是否需要身份证鉴权,
          readOnlyFlag: true,
          telnum: this.telnum
        }, obj => {
          this.telnum = obj.telnum;
          this.jqData = obj;
          this.h5CalyxApplication(rewardList, rewardListStr, rewardIdList, imei);
        });
      }else{
        this.h5CalyxApplication(rewardList, rewardListStr, rewardIdList, imei);
      }
    },
    //芝麻积分申请
    h5CalyxApplication(rewardList, rewardListStr, rewardIdList, imei) {
      let authType = this.jqData.crmAuthType;
      /*if (authType === '06') {
        authType = 'AuthCheckB'; // 服务密码
      } else if (authType === '07') {
        authType = 'AuthCheckR'; // 短信验证码
      } else if (authType === '00') {
        authType = 'AuthCheckG' // 身份证
      } else if(authType === '14') {
        authType = 'AuthChkFaceIden'; // 刷脸
      }*/
      let keyJson = {
        'activeId': 'Promotion',//批次编码
        'activeName': this.actPlanName, // 批次名称
        'levelId': this.promotionId,//档次编码
        'levelName': this.promotionName,  // 档次名称
        'rewardList': JSON.stringify(rewardList), // 奖品列表
        'fundType': '1', // 资金冻结方式
        'imei': imei,//终端串号
        'cardAuthSrl':this.jqData.cardAuthSrl,
        'stationId': this.uinfo.stationId, // 岗位编码
        'deviceType': /android|harmony/gi.test(navigator.userAgent) ? 'ANDROID' : 'IOS', // 设备类型 android,ios
        'location': this.location, // 位置信息
        'latitude': this.latitude, // 纬度
        'longitude': this.longitude, // 经度
        "daiweiId": this.uinfo.dwUser, // 代维工号
        'studentPhone': this.uinfo.relTelnum, // 协销账号
        'staffId': this.uinfo.staffId, // 操作员唯一标识
        'operatorName': this.uinfo.operatorName, // 操作员姓名
      };
      let params = {
        'actId': 'Promotion', // 批次编号
        'telNum': this.telnum,
        'privId': this.promotionId, // 档次编号
        'rewardList': rewardListStr,
        'jsonParam': JSON.stringify(keyJson),
        'rewardId': JSON.stringify(rewardIdList),
        'scoreNum': this.zhiMaCount,//芝麻积分
        'freezeamt': '0',// 冻结金额
        'imei': imei,//终端串号
        "bindTelnum": this.isBindNumber,
        'authType': authType,
      };
      if(this.deliveryCeditModule){
        params.scoreNum = this.deliveryExtendionInfo.zhimaFen;
      }
      if (!this.isAz) {
        let url = '/xsb/personBusiness/Calyx/h5CalyxApplication';
        this.$http.post(url, params).then(res => {
          let result = res.data;
          if ('0' == result.retCode) {
            let pid = result.data.pId;
            let srlid = result.data.srlId;
            let freezeamt = result.data.freezeamt;
            this.balanceFreeze(pid, srlid, freezeamt);
          } else {
            this.$alert(result.retMsg || '预约申请失败');
          }
        }).catch((response) => {
          this.$alert('预约申请网络超时');
        });
      } else {
        let url = '/xsb/personBusiness/Calyx/h5calyxApplicationPreAz';
        this.azSubmitUrl = '/xsb/personBusiness/Calyx/h5calyxApplicationFinalAz';
        this.hasExtraMethod = true;
        this.ySubmit(url,params);
      }
    },
    // 余额冻结
    balanceFreeze(pid, srlid, freezeamt) {
      //let freezeamt = parseFloat(fee / 100).toFixed(2);
      let params = {
        'srlId': srlid,
        'telNum': this.telnum, // 手机号
        'marketno': this.promotionId, //营销案编号
        'fundtyp': '1', // 资金冻结方式 1-芝麻信用 2-花呗
        'ordno': pid,
        'freezeamt': freezeamt, // 冻结金额
        'notifyurl': '',
        'scorenum': this.zhiMaCount, // 芝麻信用分
        'expiretime': '60',
        'reserve1': '',
        'storeId': this.orgId,//门店编码
        'storeName': this.orgName,//门店名称
      };
      if(this.deliveryCeditModule){
        params.scorenum = this.deliveryExtendionInfo.zhimaFen;
      }

      let url = '/xsb/personBusiness/Calyx/h5BalanceFreeze';
      this.$http.post(url, params).then(res => {
        let result = res.data;
        if ('0' == result.retCode) {
          let payCode = result.data.qrcode;
          if (payCode) {
            this.$router.push({
              path: '/promotionBuyQrCode',
              query: {
                status: '3',
                parUrl: payCode,
                srcFrom: this.srcFrom,
              }
            });
          } else {
            this.$messagebox({
              title: '提示',
              message: '余额冻结成功',
              showCancelButton: false,
              showConfirmButton: true
            }).then(action => {
              if (action == 'confirm') {
                this.$router.push({
                  path: '/promotionOrder',
                  query: {
                    srcFrom: this.srcFrom,
                    topTipFlag: '3',
                  }
                })
              }
            });
          }
        } else {
          this.$alert(result.retMsg || '余额冻结失败');
        }
      }).catch((response) => {
        this.$alert('余额冻结网络超时');
      });
    },

    //信用购提交
    xinYongGouSubmit() {
      let allRew = [];//取出奖品按照格式
      let rewId=[];//奖品id
      let rewname=[];//奖品名称
      let checkInfo = {};
      for(let j = 0; j < this.adaptationList.length; j++){
        let count=0;
        if(this.adaptationList[j].checkFlg){
          for(let i = 0; i < this.adaptationList[j].promOfferList.length; i++){
            if(this.adaptationList[j].promOfferList[i].checkFlg){
              count=count+1;
              if(this.adaptationList[j].adaptationType=='O'){
                checkInfo = {
                  rewardPkgid:this.adaptationList[j].adaptationId,//条件编码
                  rewardid:this.adaptationList[j].promOfferList[i].offeringId,//条件对象编码 商品编码
                  rewardsn:'',//串号
                };
                rewId.push(this.adaptationList[j].promOfferList[i].offeringId);
                rewname.push(this.adaptationList[j].promOfferList[i].offeringName);
              }else{
                if(this.adaptationList[j].promOfferList[i].identityId==''||this.adaptationList[j].promOfferList[i].identityId==undefined){
                  this.$alert("请选择串号");
                  return
                }
                checkInfo = {
                  rewardPkgid:this.adaptationList[j].adaptationId,//条件编码
                  rewardid:this.adaptationList[j].promOfferList[i].offerCode,//条件对象编码 商品编码
                  rewardsn:this.adaptationList[j].promOfferList[i].identityId,//串号
                };
                rewId.push(this.adaptationList[j].promOfferList[i].offerCode);
                rewname.push(this.adaptationList[j].promOfferList[i].offerName);
              }
              allRew.push(checkInfo);
            }
          }
        }
        if(this.adaptationList[j].offerMinNumber && this.adaptationList[j].offerMinNumber > count){
          let str = "条件:["+this.adaptationList[j].adaptationId+"]"+this.adaptationList[j].adaptationName+"的最小选择数量为"
            +this.adaptationList[j].offerMinNumber;
          if(this.adaptationList[j].offerMaxNumber && this.adaptationList[j].offerMaxNumber<count){
            str += ",最大选择数量为"+this.adaptationList[j].offerMaxNumber;
          }
          this.$alert(str);
          return;
        }else{
          if(this.adaptationList[j].offerMaxNumber && this.adaptationList[j].offerMaxNumber<count){
            let str = "条件:["+this.adaptationList[j].adaptationId+"]"+this.adaptationList[j].adaptationName
              +"的最大选择数量为"+this.adaptationList[j].offerMaxNumber;
            this.$alert(str);
            return;
          }
        }
      }
      if(allRew.length==0 && this.adaptationList.length>0){
        this.$alert('请选择商品');
        return
      }

      if (this.gzhPhotoStr === '' && this.isAz) {
        this.$alert("请上传告知函");
        return;
      }

      let self = this;
      //鉴权没通过
      if(this.jqData.result != '1'){
      AuthenctOld({
        popFlag:true,
        idCardWay:this.jqData.idCardWay,//是否需要身份证鉴权,
        readOnlyFlag:true,
        telnum:this.telnum
      },function (obj){
        self.telnum = obj.telnum;
        self.jqData = obj;
        self.h5preCreditLoan(rewId,allRew,rewname);
      });
      } else {
          self.h5preCreditLoan(rewId,allRew,rewname);
      }
    },
    async h5preCreditLoan(rewId,allRew,rewname){
      let chktype='';
      if (this.jqData.authtype == '06') { //服务密码
        chktype = 'AuthCheckB';
      } else if (this.jqData.authtype == '07') { //验证码
        chktype  = 'AuthCheckR'
      } else if (this.jqData.authtype == '00') {
        chktype  = 'AuthCheckG'; //身份证
      } else {
        chktype = this.jqData.crmAuthType;
      }

      this.keyJson = {
        'marketId': '',
        'marketName': '',
        'activeId': 'Promotion',//批次编码
        'activeName': this.actPlanName, // 批次名称
        'levelId': this.promotionId,//档次编码
        'levelName': this.promotionName,  // 档次名称
        'agentmerno': this.ckDzfId, //垫资方编码
        'agentmername': this.ckDzfName,//垫资方名称
        'rewardid': rewId.join("|"),//奖品编码
        'rewardName':rewname.join("|"),
        'fee':this.productAmt*100,   //金额转换为分
        'stagenum':24,  //期数
        'depid': this.orgId,
        'depnm':  this.orgName,
        'enterspenumber':this.enterSpeNumber,
        'subnumber':this.subnumber,
        'cardAuthSrl':this.jqData.cardAuthSrl,
        'authtype':this.jqData.authtype,
        'rewardList':JSON.stringify(allRew),
        'stationId': this.uinfo.stationId, // 岗位编码
        'deviceType': /android|harmony/gi.test(navigator.userAgent) ? 'ANDROID' : 'IOS', // 设备类型 android,ios
        'location': this.location, // 位置信息
        'latitude': this.latitude, // 纬度
        'longitude': this.longitude, // 经度
        "daiweiId": this.uinfo.dwUser, // 代维工号
        'studentPhone': this.uinfo.relTelnum, // 协销账号
        'staffId': this.uinfo.staffId, // 操作员唯一标识
        'operatorName': this.uinfo.operatorName, // 操作员姓名
      };

      let params = {
        'source': '01',
        'telnum': this.jqData.telnum,
        'actid': 'Promotion',
        'levelid': this.promotionId,
        'levelattrvalue': this.ckDzfId,
        'rewardid': rewId.join("|"),
        'enterspenumber': this.enterSpeNumber,
        // 'rewardPkgid': _this.packageItem.rwdpackageid || '',
        'rewardList':JSON.stringify(allRew),
        'authtype': this.jqData.authtype,
        'chktype':chktype,
        'keyJson': JSON.stringify(this.keyJson),
        'subnumber': this.subnumber,
        "bindTelnum":this.isBindNumber,
      };
      if (!this.isAz) {
        let url = '/xsb/personBusiness/creditLoan/h5preCreditLoan';
        this.$http.post(url, params).then(res => {
          let result = res.data;
          if ('0' == result.retCode) {
            let pid = result.data.pid;
            let srlid = result.data.srlid;
            this.loanOrder(pid, srlid);
          } else {
            this.$alert(result.retMsg || '预约申请失败');
          }
        }).catch((response) => {
          this.$alert(response);
        });
      } else {
        let result = await this.uploadPhoto(this.jqData.telnum);
        let {retCode, retMsg, data} = result.data;
        if (retCode === '0') {
          params.gzhAttachId = data.attachId;
          params.gzhFileName = data.attachName;

          this.azPreCreditLoan(params);
        } else {
          this.$alert(retMsg || '上传照片失败');
        }
      }
    },
    // 预约申请预提交
    azPreCreditLoan(params) {
      let url = '/xsb/personBusiness/creditLoan/h5preCreditLoanPreAz';
      this.azSubmitUrl = '/xsb/personBusiness/creditLoan/h5preCreditLoanFinalAz'
      this.hasExtraMethod = true;
      this.ySubmit(url,params);
    },
    // 贷款下单
    loanOrder(pid, srlid) {
      let params = {
        'cusmblno': this.jqData.telnum,  // 手机号
        'prepayId': pid,  //pid
        'productnm': this.promotionName, //档次名称
        'productamt': parseFloat(this.productAmt).toFixed(2), //贷款本金 单位：元,精确到小数点后两位
        'pkgmonth': "24", //合约分期数 单位月
        'goodsnm': this.promotionName,//奖品名称(传奖品名称有问题，改成档次名称)
        'depid': this.orgId,//orgId
        'depnm': this.orgName,//orgName
        'agentmerno': this.ckDzfId,//垫资方编码
        'srlid': srlid,
        'authtype': this.jqData.authtype,
        'keyJson': JSON.stringify(this.keyJson)
      };
      let url = '/xsb/personBusiness/creditLoan/h5creditLoan';
      this.$http.post(url, params).then(res => {
        let result = res.data;
        if ('0' == result.retCode) {
          this.$router.push({
            path: '/promotionBuyQrCode',
            query: {
              status:'1',
              parUrl: result.data.paycode,
              srcFrom:this.srcFrom,
            }
          });
        } else {
          this.$alert(result.retMsg || '贷款申请失败');
        }
      }).catch((response) => {
        this.$alert('贷款申请网络超时');
      });
    },
    // 最终提交后调用
    submitExtraMethod(data) {
      // 芝麻信用
      if (this.zhimaFlag) {
        this.balanceFreeze(data.pId, data.srlId, data.freezeamt);
        // 信用购
      } else if (this.xinyonggouFlag) {
        this.loanOrder(data.pid, data.srlid);
      }
    },

      //打包
      dabao(){
          this.$messagebox.prompt("请输入自定义包名称").then(({ value, action }) => {
              if (value == null){
                  this.$alert("自定义名称不能为空")
              }else{
                  this.marketName = value;
                  this.pack();
              }
          });
      },
      //打包
      pack(){

          let packlist = [];
          packlist = [{
              pkgId: "",  //不填
              prodId:  this.promotionId,  //"rewardId": "1001112",
              prodName: this.promotionName,	  //"rewardName": "奖品12",
              isPackage: 0, 			//是否是包（最外面）0
              effectType: 0,			//生效方式 0立即 1次日 2次月
              prodLevel: 2,			//产品等级 1批次 2档次  3奖品
              type: 6,   	//产品类型 1.营销案 2.宽带 3.增值产品
              parentId: this.actPlanId,
              isMust: 0,      //是否必选  0否 1是
              isNeed: 0        //是否需要输入串号 1需要 0不需要
          },{
              pkgId: "",  //不填
              prodId:  this.actPlanId,  //"rewardId": "1001112",
              prodName: this.actPlanName,	  //"rewardName": "奖品12",
              isPackage: 0, 			//是否是包（最外面）0
              effectType: 0,			//生效方式 0立即 1次日 2次月
              prodLevel: 1,			//产品等级 1批次 2档次  3奖品
              type: 6,   	//产品类型 1.营销案 2.宽带 3.增值产品
              parentId: -1,
              isMust: 0,      //是否必选  0否 1是
              isNeed: 0        //是否需要输入串号 1需要 0不需要
          }];

          for(let j = 0; j < this.adaptationList.length; j++){
              if(this.adaptationList[j].checkFlg){
                  for(let i = 0; i < this.adaptationList[j].promOfferList.length; i++){
                      if(this.adaptationList[j].promOfferList[i].checkFlg){
                          let rewarditem = {
                              pkgId: "",  //不填
                              prodId:  this.adaptationList[j].adaptationType=='O'?this.adaptationList[j].promOfferList[i].offeringId:this.adaptationList[j].promOfferList[i].offerId,  //"rewardId": "1001112",
                              prodName: this.adaptationList[j].adaptationType=='O'?this.adaptationList[j].promOfferList[i].offeringName:this.adaptationList[j].promOfferList[i].offerName,	  //"rewardName": "奖品12",
                              isPackage: 0, 			//是否是包（最外面）0
                              effectType: 0,			//生效方式 0立即 1次日 2次月
                              prodLevel: 4,			//产品等级 1批次 2档次  3奖品包 4 奖品
                              type:6,   	//产品类型 1.营销案 2.宽带 3.增值产品
                              parentId: this.adaptationList[j].adaptationId,
                              isMust: 0,      //是否必选  0否 1是
                              isNeed: this.adaptationList[j].adaptationType=='O'?0:1,        //是否需要输入串号 1需要 0不需要
                              offerCode:this.adaptationList[j].adaptationType=='O'?'':this.adaptationList[j].promOfferList[i].offerCode
                          }
                          let rewardpackageitem = {
                              pkgId: "",  //不填
                              prodId:  this.adaptationList[j].adaptationId,  //"rewardId": "1001112",
                              prodName: this.adaptationList[j].adaptationName,	  //"rewardName": "奖品12",
                              isPackage: 0, 			//是否是包（最外面）0
                              effectType: 0,			//生效方式 0立即 1次日 2次月
                              prodLevel: 3,			//产品等级 1批次 2档次  3奖品包 4 奖品
                              type: 6,   	//产品类型 1.营销案 2.宽带 3.增值产品
                              parentId: this.promotionId,
                              isMust: 0,      //是否必选  0否 1是
                              isNeed: this.adaptationList[j].adaptationType=='O'?0:1,       //是否需要输入串号 1需要 0不需要
                          }
                          packlist.push(rewarditem);
                          packlist.push(rewardpackageitem);
                      }
                  }
              }
          }
          this.goPack(packlist);
      },
      //进行打包
      goPack(packlist){
          let rewardpacklist = packlist;
          if (!this.marketName) {
              let msg = '自定义名称不能为空';
              this.$messagebox({
                  title: '提示',
                  message: msg,
                  showConfirmButton: true
              }).then(action => {
                  //this.goPack(rewardpacklist);
              });
          }else if(this.marketName.length <15){

              let url = '/xsb/personBusiness/marketPackage/h5AddCustomMarketPkg';
              let param = {
                  marketName: this.marketName,
                  type: 6,	//产品类型 1.营销案 2.宽带 3.增值产品
                  marketDesc: "促销活动打包",
                  startDate:dateFormat(new Date(),("yyyy-MM-dd")),
                  endDate:"",
                  source: "",
                  releaseType: 2,  //发布方式 1地市 2操作员 3岗位
                  releaseValue: this.uinfo.crmId
              }
              param.prodList = JSON.stringify(packlist);
              this.$http.post(url,param).then(res => {
                  if(res.data.retCode == '0'){
                      this.$messagebox({
                          message: '促销活动打包成功',
                          showConfirmButton: true
                      }).then(action => {
                          this.$router.push({
                              path: '/promotionEntry',
                              query: {
                                  srcFrom: this.srcFrom
                              }
                          })
                      });
                  } else {
                      this.$alert(res.data.retMsg || '促销活动打包失败');
                  }
              })
          }else{
              let msg = '自定义包名称超过15个字符';
              this.$messagebox({
                  title: '提示',
                  message: msg,
                  showConfirmButton: true
              }).then(action => {
                  //this.goPack(rewardpacklist);
              });
          }
      },

    //电子学生证扫码
    scaleDianZicode(idx,sidx){
      this.dianZiIdx=idx;
      this.dianZiSidx=sidx;
      ClientJs.scanQRcode('scanDianZiBack');
    },

    //用户电商基地查询串号
    async queryImeiInfo(identityId,offerCode) {
      let imeiList=[];
      let obj = {
        invid: identityId,
      };
      imeiList.push(obj);
      imeiList = JSON.stringify(imeiList);
      let flag= false;
      let url = `/xsb/personBusiness/terminalNakedSale/h5queryImeiInfo?imeiList=${imeiList}`;
      let res = await this.queryImeiInfoGet(url);
      if (res.data.retCode == "0") {
        this.imeiInfoList = res.data.data;
        if (this.imeiInfoList != null && this.imeiInfoList.length > 0) {

          if (this.imeiInfoList[0].invExists != "1") {
            this.$alert(res.data.retMsg + "当前串号状态不存在");
          } else {
            if (this.imeiInfoList[0].invStatus != "DS") {
              this.$alert(res.data.retMsg + "当前串号不可销售，请核实输入的串号是否正确");
            }else if(this.imeiInfoList[0].goodsNum != offerCode){
                this.$alert(res.data.retMsg + "终端【"+this.imeiInfoList[0].goodsNum+"】串号的商品SKU【"+offerCode+"】不一致");
            }else if(this.imeiInfoList[0].invOrg != this.orgId){
                this.$alert(res.data.retMsg + "当前串号【"+identityId+"】归属机构【"+this.imeiInfoList[0].invOrg+"】和工号归属机构【"+this.orgId+"】不一致");
            }

            else {
              flag = true;
            }
          }
        } else {
          this.$alert(res.data.retMsg + "该串号查询信息为空");
        }
      } else {
        this.$alert(res.data.retMsg || "获取串号信息失败");
      }
      return flag;
      },

    //用户电商基地查询串号
    async queryImeiInfoGet(url) {
      return this.$http.get(url).catch(err => {
        this.$alert("串号合法性查询异常："+err);
      });
    },

    //判断是否开启上传场景照
    qryUploadCjz() {
      let param = {
        busiType: 'uplod_cjz_flag',
        unLoadFlg:true//屏蔽加载圈
      }
      this.$http.post('/xsb/ability/businessLimit/h5QryBusiPermission', param).then((res) => {
        if(res.data.retCode == '0'){
          this.cjzPhotoFlag = true;
          this.duration =this.promotionDetail.duration;
        }
      })
    },

    //判断信控开关
    async qryXinKongOpen() {
      let flag = false;
      if(this.promotionDetail.deliveryModuleFlag == 'Y' || this.promotionDetail.creditModuleFlag == 'Y') {
        let param = {
          busiType: 'xinkong_flag',
          unLoadFlg: true//屏蔽加载圈
        }
        let xinKongRes = await this.qryXinKongOpenPost(param);
        if (xinKongRes.data.retCode == '0') {
          flag= true;
        }
      }
      return flag;
    },
    //获取信控开关
    async qryXinKongOpenPost(param) {
      return this.$http.post('/xsb/ability/businessLimit/h5QryBusiPermission',param);
    },

    // 上传照片
    uploadCjzPhoto(durationDate) {
      Indicator.open('上传照片中');
      let param = {
        photostr: this.cjzPhotoStr,
        telnum: this.jqData.telnum,
        duration:durationDate,
        unEncrpt: true
      };
      // 上传照片
      return this.$http.post('/xsb/personBusiness/promotionCenter/h5upLoadCjz', param);
    },


    //判断芝麻信用是否支持购物车
    qryZhiMaShop() {
      // let param = {
      //   busiType: 'zhima_shop_flag',
      //   unLoadFlg:true//屏蔽加载圈
      // }
      // this.$http.post('/xsb/ability/businessLimit/h5QryBusiPermission', param).then((res) => {
      //   if(res.data.retCode == '0'){
          this.zhiMaShopFlag = true;
          this.busiType ="Calyx";
          this.isCloseJiaRu="1" //屏蔽加入购物车按钮
          this.getEffectTypeInfo();
      //   }
      // })
    },
    //获取生效方式
    getEffectTypeInfo(){
      if (this.promotionDetail.effectType != null && this.promotionDetail.effectType != '') {
        //生效方式判断
        let str = this.promotionDetail.effectType.split(',');
        for (let i = 0; i < str.length; i++) {
          if (str[i] == "1") {
            this.immediatelyFlg = true;
          } else if (str[i] == "2") {
            this.nextMonthFlg = true;
          } else if (str[i] == "3") {
            this.appointFlg = true;
          } else {
            this.$alert(this.promotionDetail.interfaceBaseMsg + '接口提示:CRM返回effect_type生效方式节点错误');
          }
        }
      } else {
        this.$alert(this.promotionDetail.interfaceBaseMsg + '接口提示:CRM返回effect_type生效方式节点错误');
      }
    },

    //芝麻信用购物车信息记表
    async insertZhiMaShop(shopCartItemIdReturn){
      let insertZhiMaShopFlag =false;
      let rewardListStr = '';
      let rewardList = [];
      let rewardIdList = [];
      let checkInfo = {};
      let imei = "";
      for (let j = 0; j < this.adaptationList.length; j++) {
        let count = 0;
        let imeicount = 0;//终端数量
        if (this.adaptationList[j].checkFlg) {
          for (let i = 0; i < this.adaptationList[j].promOfferList.length; i++) {
            if (this.adaptationList[j].promOfferList[i].checkFlg) {
              count = count + 1;
              rewardListStr = rewardListStr + this.adaptationList[j].adaptationId + '|';
              if (this.adaptationList[j].adaptationType == 'O') {
                rewardListStr = rewardListStr + this.adaptationList[j].promOfferList[i].offeringId + ';';
                checkInfo = {
                  rewardId: this.adaptationList[j].promOfferList[i].offeringId,// 商品编码
                  rewardname: this.adaptationList[j].promOfferList[i].offeringName,//商品名称
                };
                rewardIdList.push(this.adaptationList[j].promOfferList[i].offeringId);
              } else {
                rewardListStr = rewardListStr + this.adaptationList[j].promOfferList[i].offerId + ';';
                checkInfo = {
                  rewardId: this.adaptationList[j].promOfferList[i].offerId,// 商品编码
                  rewardname: this.adaptationList[j].promOfferList[i].offerName,//商品名称
                };
                rewardIdList.push(this.adaptationList[j].promOfferList[i].offerId);

                if (this.adaptationList[j].promOfferList[i].identityId != null &&
                  this.adaptationList[j].promOfferList[i].identityId != undefined && this.adaptationList[j].promOfferList[i].identityId != '') {
                  imei = this.adaptationList[j].promOfferList[i].identityId;//串号
                  imeicount = imeicount + 1;
                }
              }
              rewardList.push(checkInfo);
            }
          }
        }

        if (imeicount > 1) {
          this.$alert("芝麻信用类促销活动最多只支持一个终端");
          return;
        }

        if(!(this.deliveryType =='2' && this.adaptationList[j].adaptationType == 'G')) {
          if (this.adaptationList[j].offerMinNumber && this.adaptationList[j].offerMinNumber > count) {
            let str = "条件:[" + this.adaptationList[j].adaptationId + "]" + this.adaptationList[j].adaptationName + "的最小选择数量为"
              + this.adaptationList[j].offerMinNumber;
            if (this.adaptationList[j].offerMaxNumber && this.adaptationList[j].offerMaxNumber < count) {
              str += ",最大选择数量为" + this.adaptationList[j].offerMaxNumber;
            }
            this.$alert(str);
            return;
          } else {
            if (this.adaptationList[j].offerMaxNumber && this.adaptationList[j].offerMaxNumber < count) {
              let str = "条件:[" + this.adaptationList[j].adaptationId + "]" + this.adaptationList[j].adaptationName
                + "的最大选择数量为" + this.adaptationList[j].offerMaxNumber;
              this.$alert(str);
              return;
            }
          }
        }
      }
      if (rewardList.length == 0 && this.adaptationList.length > 0) {
        this.$alert('请选择商品');
        return
      } else {
        rewardListStr = rewardListStr.substr(0, rewardListStr.length - 1);
      }

      let keyJson = {
        'activeId': 'ShoppingCart',//批次编码
        'activeName': this.actPlanName, // 批次名称
        'levelId': this.promotionId,//档次编码
        'levelName': this.promotionName,  // 档次名称
        // 'rewardList': JSON.stringify(rewardList), // 奖品列表
        'fundType': '1', // 资金冻结方式
        'imei': imei,//终端串号
        'cardAuthSrl':this.jqData.cardAuthSrl,
        'stationId': this.uinfo.stationId, // 岗位编码
        'deviceType': /android|harmony/gi.test(navigator.userAgent) ? 'ANDROID' : 'IOS', // 设备类型 android,ios
        'location': this.location, // 位置信息
        'latitude': this.latitude, // 纬度
        'longitude': this.longitude, // 经度
        "daiweiId": this.uinfo.dwUser, // 代维工号
        'studentPhone': this.uinfo.relTelnum, // 协销账号
        'staffId': this.uinfo.staffId, // 操作员唯一标识
        'operatorName': this.uinfo.operatorName, // 操作员姓名
      };
      let params = {
        'srlId': shopCartItemIdReturn,//购物车项编码
        'actId': 'ShoppingCart', // 批次编号
        'telNum': this.telnum,
        'privId': this.promotionId, // 档次编号
        // 'rewardList': rewardListStr,
        'jsonParam': JSON.stringify(keyJson),
        'rewardId': JSON.stringify(rewardIdList),
        'scoreNum': this.zhiMaCount,//芝麻积分
        'imei': imei,//终端串号
        "bindTelnum": this.isBindNumber,
      };
      if(this.deliveryCeditModule){
        params.scoreNum = this.deliveryExtendionInfo.zhimaFen;
      }
        let url = '/xsb/personBusiness/Calyx/h5zhiMaShoppingCart';
      await this.$http.post(url, params).then(res => {
          let result = res.data;
          if ('0' == result.retCode) {
            insertZhiMaShopFlag = true;
          } else {
            this.$alert(result.retMsg || '芝麻信用购物车信息记录失败');
          }
        }).catch((response) => {
          this.$alert('芝麻信用购物车信息记录网络超时'+response);
        })

      return insertZhiMaShopFlag;
    },

    //查询储蓄卡冻结是否走购物车
    qrySavingsCardShop(){
      this.savingsCardMaShopFlag = true;
      this.busiType ="INSTALLMENT_SAVINGS";
      if(this.zhongYiDanBaoFlag){
        this.busiType ="promotion_zhongyi";
      }
      this.isFromOther ="1";
      this.getEffectTypeInfo();
    },
    //储蓄卡冻结贷款申请
    insertChuXuShop(shopCartItemIdReturn){
      let rewlist = [];
      let checkInfo = {};
      for (let j = 0; j < this.adaptationList.length; j++) {
        let count = 0;
        if (this.adaptationList[j].checkFlg) {
          for (let i = 0; i < this.adaptationList[j].promOfferList.length; i++) {
            if (this.adaptationList[j].promOfferList[i].checkFlg) {
              count = count + 1;
              if (this.adaptationList[j].adaptationType == 'O') {
                checkInfo = {
                  rewardPkgId: this.adaptationList[j].adaptationId,//条件编码
                  rewardId: this.adaptationList[j].promOfferList[i].offeringId,//条件对象编码 商品编码
                  rewardSn: '',//串号
                  rewardQuantity: '1',//数量
                  rewardApd: '',//属性
                  rewardDrawtype: '',//领取方式
                  rewardName: this.adaptationList[j].promOfferList[i].offeringName,//商品名称
                };
              } else {
                if(!(this.deliveryType == '2' ||  this.terminalDeliveryInfo.deliveryModule == "4" ) || this.adaptationList[j].adaptationType == 'T' ){
                  if (this.adaptationList[j].promOfferList[i].identityId == '' || this.adaptationList[j].promOfferList[i].identityId == undefined) {
                    this.$alert("请选择串号");
                    return
                  }
                }
                if(!(this.deliveryType =='2' && this.adaptationList[j].adaptationType == 'G')) {
                  checkInfo = {
                    rewardPkgId: this.adaptationList[j].adaptationId,//条件编码
                    rewardId: this.adaptationList[j].promOfferList[i].offerCode,//条件对象编码 商品编码
                    rewardSn: this.adaptationList[j].promOfferList[i].identityId,//串号
                    rewardQuantity: '1',//数量
                    rewardApd: '',//属性
                    rewardDrawtype: '',//领取方式
                    rewardName: this.adaptationList[j].promOfferList[i].offerName,//商品名称
                  };
                }
              }
              rewlist.push(checkInfo);
            }
          }
        }
      }

      let levelAttrList = [];
      if (this.subnumber != "") {
        let vicenum = {
          vicenum: this.subnumber
        };
        levelAttrList.push(vicenum);
      }
      let savingsCardNum="";
      let topTipFlag='2';
      let loanAmountTmp ="";
      //信控模组
      if(this.deliveryCeditModule){
        //信用卡分期
        if(this.deliveryCreditType =='123'){
          savingsCardNum="3";
          topTipFlag='6';
          if(this.fenQiFee){
            loanAmountTmp = parseFloat(this.fenQiFee) * 1000;
          }
        }
        //储蓄卡冻结
        if(this.deliveryCreditType =='124'){
          savingsCardNum="1";
          topTipFlag='4';
          loanAmountTmp = this.deliveryExtendionInfo.cardFreezeAmt;
        }
        //中移担保模式
        if(this.deliveryCreditType =='125'){
          savingsCardNum="2";
          topTipFlag='5';
          loanAmountTmp ="";
        }
      }else{
        if(this.savingsCardFlag){
          savingsCardNum="1";
          topTipFlag='4';
          loanAmountTmp =this.cardFreezeAmt;
        }
        if(this.zhongYiDanBaoFlag){
          savingsCardNum="2";
          topTipFlag='5';
          loanAmountTmp =this.guaranteeAmt;
        }
      }

      let url = '/xsb/personBusiness/installmenthe/h5promLoanApply';
      let param = {
        "overpackvalue": this.overpackvalue,
        "telnum": this.jqData.telnum,
        "offerId": "ShoppingCart",
        "levelId": this.promotionId,
        "rewardList": JSON.stringify(rewlist),
        "offerName": this.actPlanName,
        "levelName": this.promotionName,
        "levelAttrList": JSON.stringify(levelAttrList),
        "bindTelnum": this.isBindNumber,
        "savingsCardFlag":savingsCardNum,//储蓄卡冻结
        "srlId":shopCartItemIdReturn,//购物车项
        "loanAmount":loanAmountTmp,//储蓄卡冻结金额cardFreezeAmt，单位厘
      }
      this.$http.post(url, param).then(res => {
        if (res.data.retCode == '0') {
          this.$messagebox.alert('提交成功,稍后可在【我的订单】查询贷款进度', '温馨提示').then(action => {
            this.$router.push({
              path: '/promotionOrder',
              query: {
                srcFrom: this.srcFrom,
                topTipFlag: topTipFlag,
              }
            })
          });
        } else {
          this.$alert(res.data.retMsg || '提交奖品失败');
        }
      }).catch((response) => {
        this.$alert('提交奖品网络连接失败');
      })

    },

    //余额转移
    balanceTransferPromotion(){
      let url = '/xsb/personBusiness/promotionCenter/h5balanceTransferPromotion';
      let transferbeId = this.jqData.userCity;
      if(!transferbeId){
        transferbeId= this.beId;
      }
      let param = {
        "serviceNumber": this.telnum,
        "beId": transferbeId,
        unLoadFlg:true//屏蔽加载圈
      }
      this.$http.post(url, param).then(res => {
        if (res.data.retCode != '0') {
          if(res.data.retMsg && res.data.retMsg.includes("当前副号办理了话费返还类活动")){
            this.$alert(res.data.retMsg || '余额转移查询失败');
          }
        }
      }).catch((response) => {
        this.$toast('余额转移查询异常'+response);
      })
    },

    //促销活动提货信控参与方式查询
    queryPromotionDelivery(){
      if(this.deliveryCeditModule){

        let url = '/xsb/promotionCenter/promotionCenter/h5queryPromotionDeliveryList';
        let transferbeId = this.jqData.userCity;
        if(!transferbeId){
          transferbeId= this.beId;
        }
        let param = {
          "beId": transferbeId,
          "serverNumber": this.telnum,
          "promotionId": this.promotionId,
          "orgid": this.orgId,
        }
        this.$http.post(url, param).then(res => {
          if (res.data.retCode == '0') {
            this.deliveryModuleList=res.data.data.deliveryModuleList;
            this.creditModuleList=res.data.data.creditModuleList;
          }else{
            this.$alert(res.data.retMsg || '促销活动提货信控参与方式查询失败');
          }
        }).catch((response) => {
          this.$alert('促销活动提货信控参与方式查询异常'+response);
        })
      }
    },
    //点击取货方式
    deliveryModuleClick(item){
      for (let j = 0; j < this.deliveryModuleList.length; j++) {
        this.$set(this.deliveryModuleList[j], 'checkFlg', false);
      }
      if(item.chooseFlag =='1'){
        if(item.checkFlg){
          this.$set(item, 'checkFlg', false);
        }else{
          //清空
          // this.terminalDeliveryList=[];

          this.$set(item, 'checkFlg', true);
          this.deliveryType = item.deliveryType;
          console.log(item.deliveryType)
          //如果选了“积分商城兑换”，信用卡分期、中移担保则 置灰不能选择，因为这两种模式不支持非终端流程的受理。
          for (let i = 0; i < this.creditModuleList.length; i++) {
            if (this.creditModuleList[i].creditType == '123' || this.creditModuleList[i].creditType == '125') {
              if (item.deliveryType == '2') {
                this.$set(this.creditModuleList[i], 'checkFlg', false)
                this.$set(this.creditModuleList[i], 'shanChengFlg', true)
              }else {
                this.$set(this.creditModuleList[i], 'shanChengFlg', false)
              }
            }
          }
        }
      }else{
        this.showData = item.chooseDesc || '暂无描述';
      }
    },
    //授信方式
    creditModuleClick(item){
      for (let i = 0; i < this.creditModuleList.length; i++) {
        this.$set(this.creditModuleList[i], 'checkFlg', false)
      }
      this.deliveryExtendionInfo={};
      this.deliveryCreditType = "";
      this.isCloseJiaRu="";
      this.isFromOther="";
      if(item.shanChengFlg){
        this.showData = item.chooseDesc || '不支持非终端流程的受理';
      }else {
        if(item.chooseFlag =='1'){
          if(item.checkFlg){
            this.$set(item, 'checkFlg', false);
          }else{
            //清空
            // this.terminalDeliveryList=[];
            // this.terminalDeliveryInfo={};
            this.$set(item, 'checkFlg', true);
            this.deliveryCreditType = item.creditType;
            //场景照上传，Y表示必须上传
            if(item.scenePicUpload =="Y"){
              this.cjzPhotoFlag = true;
              this.duration =this.promotionDetail.duration;
            }else{
              this.cjzPhotoFlag = false;
            }
            if(item.isBindImei =="1"){
              this.isBindImei = true;
            }else{
              this.isBindImei = false;
            }
            //芝麻信用屏蔽加入购物车按钮
            if(this.deliveryCreditType =='1'){
              this.isCloseJiaRu="1" //屏蔽加入购物车按钮
            }
            //信用卡分期、储蓄卡冻结、中移担保模式 只有加入购物车
            if(this.deliveryCreditType =='123' || this.deliveryCreditType =='124' || this.deliveryCreditType =='125'){
              this.isFromOther="1" //只有加入购物车
            }

            if(item.extendionInfo && item.extendionInfo.length >0){
            for(let j = 0; j < item.extendionInfo.length; j++){
              if(item.extendionInfo[j].extendionId =='zhimaFen'){//芝麻积分
                this.$set(this.deliveryExtendionInfo, 'zhimaFen', item.extendionInfo[j].extendionValue)
              }else if(item.extendionInfo[j].extendionId =='creditMonthFee'){ //月功能费 单位厘
                this.$set(this.deliveryExtendionInfo, 'creditMonthFee', item.extendionInfo[j].extendionValue)
              }else if(item.extendionInfo[j].extendionId =='minInstallmentAmt'){//最小分期金额 单位厘
                this.$set(this.deliveryExtendionInfo, 'minInstallmentAmt', item.extendionInfo[j].extendionValue)
              }else if(item.extendionInfo[j].extendionId =='maxInstallmentAmt'){//最大分期金额 单位厘
                this.$set(this.deliveryExtendionInfo, 'maxInstallmentAmt', item.extendionInfo[j].extendionValue)
              }else if(item.extendionInfo[j].extendionId =='cardUnFreezePolicy'){//储蓄卡冻结策略  1分月解冻;2:一次性解冻
                this.$set(this.deliveryExtendionInfo, 'cardUnFreezePolicy', item.extendionInfo[j].extendionValue)
              }else if(item.extendionInfo[j].extendionId =='cardFreezeAmt'){//储蓄卡冻结金额 单位厘
                this.$set(this.deliveryExtendionInfo, 'cardFreezeAmt', item.extendionInfo[j].extendionValue)
              }else if(item.extendionInfo[j].extendionId =='chargeAmount'){//充值金额 单位厘
                this.$set(this.deliveryExtendionInfo, 'chargeAmount', item.extendionInfo[j].extendionValue)
              }
            }
            }
          }
        }else{
          this.showData = item.chooseDesc || '暂无描述';
        }
      }
    },

    //根据终端信息查询终端提货方式接口
    async queryPromotionDeliveryModule(item){
        let url = '/xsb/promotionCenter/promotionCenter/h5queryPromotionDeliveryModule';
        let transferbeId = this.jqData.userCity;
        if(!transferbeId){
          transferbeId= this.beId;
        }
        let param = {
          "beId": transferbeId,
          "serverNumber": this.telnum,
          "promotionId": this.promotionId,
          "orgid": this.orgId,
          "skuCode": item[0].offerCode,
          // "identityId": item[0].identityId,
        }
      console.log(param)
        this.$http.post(url, param).then(res => {
          if (res.data.retCode == '0') {
            this.terminalDeliveryList=res.data.data;
            if(this.terminalDeliveryList &&  this.terminalDeliveryList.length >0){
              for (let i = 0; i < this.terminalDeliveryList.length; i++) {
                if(this.terminalDeliveryList[i].deliveryModule == '4'){
                  this.terminalDeliveryInfo =this.terminalDeliveryList[i];
                }else{
                  if(item[0].identityId){
                    this.queryPromotionDeliveryModule2(item[0])
                  }
                }
              }
            }
          }else{
            this.$alert(res.data.retMsg || '促销活动提货信控参与方式查询失败');
          }
          return item;
        }).catch((response) => {
          this.$alert('促销活动提货信控参与方式查询异常'+response);
        })
      return item;
    },


    //根据终端信息查询终端提货方式接口2
    queryPromotionDeliveryModule2(item){
      let url = '/xsb/promotionCenter/promotionCenter/h5queryPromotionDeliveryModule';
      let transferbeId = this.jqData.userCity;
      if(!transferbeId){
        transferbeId= this.beId;
      }
      let param = {
        "beId": transferbeId,
        "serverNumber": this.telnum,
        "promotionId": this.promotionId,
        "orgid": this.orgId,
        "skuCode": item.offerCode,
        "identityId": item.identityId,
      }
      this.$http.post(url, param).then(res => {
        if (res.data.retCode == '0') {
          this.terminalDeliveryList=res.data.data;
          for (let i = 0; i < this.terminalDeliveryList.length; i++) {
            this.terminalDeliveryInfo =this.terminalDeliveryList[i];
          }
        }else{
          this.$alert(res.data.retMsg || '促销活动提货信控参与方式查询失败');
        }
      }).catch((response) => {
        this.$alert('促销活动提货信控参与方式查询异常'+response);
      })
    },

    //信控参数封装
    async deliveryParameterEncapsulation() {
      //1.促销活动列表参数封装
      this.promMemberList = [];
      this.atchPromotionSubmitList = [];
      this.promOffeZhongDuanList = [];
      let checkInfo = {};

      console.log(this.adaptationList)
      for (let j = 0; j < this.adaptationList.length; j++) {
        let count = 0;
        let adaptationListJ = this.adaptationList[j];
        if (adaptationListJ.checkFlg) {
          for (let i = 0; i < adaptationListJ.promOfferList.length; i++) {
            let promOfferListI = adaptationListJ.promOfferList[i];
            if (promOfferListI.checkFlg) {
              count = count + 1;
              if (this.adaptationList[j].adaptationType == 'O') {
                //是否选择了校区属性
                if (promOfferListI.schoolPropId && promOfferListI.schoolPropCode) {
                  if (!promOfferListI.memberEntityAttr) {
                    this.$alert('请选择校区属性');
                    return false;
                  }
                }
                //是否选择了全国亲情网群组实例编码
                if (promOfferListI.offeringId == '2000013306') {
                  if (!promOfferListI.memberEntityAttr) {
                    this.$alert('请选择全国亲情网群组实例编码');
                    return false;
                  } else {
                    let memberEntityAttr = promOfferListI.memberEntityAttr;
                    if (memberEntityAttr.indexOf('KSJTWGroupInstId') == -1) {
                      this.$set(promOfferListI, 'memberEntityAttr', "KSJTWGroupInstId=" + memberEntityAttr);
                    }
                  }
                }
                checkInfo = {
                  adaptationId: adaptationListJ.adaptationId,//条件编码
                  memberEntityType: adaptationListJ.adaptationType,//条件对象类型 O：商品 G.终端
                  memberEntityId: promOfferListI.offeringId,//条件对象编码 商品编码
                  memberEntityPkgId: adaptationListJ.offerPackId,//有线业务商品包
                  memberEntityAttr: promOfferListI.memberEntityAttr,//附加属性，校区属性，全国亲情网群组实例编码
                  memberEntityInstId: promOfferListI.memberEntityInstid,
                };
              } else {
                //积分商城兑换不需要选择终端串号，集采类不受影响
                console.log(this.deliveryType)
                console.log(this.terminalDeliveryInfo.deliveryModule)
                console.log(this.adaptationList[j].adaptationType)
                console.log(!(this.deliveryType == '2' ||  this.terminalDeliveryInfo.deliveryModule == "4" ))
                console.log(!(this.deliveryType == '2' ||  this.terminalDeliveryInfo.deliveryModule == "4" ) || this.adaptationList[j].adaptationType == 'T')
                if (!(this.deliveryType == '2' ||  this.terminalDeliveryInfo.deliveryModule == "4" ) || this.adaptationList[j].adaptationType == 'T' ) {
                  if (!promOfferListI.identityId) {
                    this.$alert('请选择串号');
                    return false;
                  }
                }
                  //集采家庭终端
                  if (this.adaptationList[j].adaptationType == 'T') {
                    let identityIdFlag = await this.queryImeiInfo(promOfferListI.identityId, promOfferListI.offerCode);
                    if (!identityIdFlag) {
                      return false;
                    }
                  }
                  if(!(this.deliveryType =='2' && this.adaptationList[j].adaptationType == 'G')) {
                    //终端商城代发不需要选择串号
                    if (this.terminalDeliveryInfo.deliveryModule == '4') {
                      checkInfo = {
                        adaptationId: adaptationListJ.adaptationId,//条件编码
                        memberEntityType: adaptationListJ.adaptationType,//条件对象类型 O：商品 G.终端
                        memberEntityId: adaptationListJ.promOfferList[i].offerId,//条件对象编码 商品编码
                        memberEntityPkgId: adaptationListJ.offerPackId,//有线业务商品包
                      };
                    } else {
                      checkInfo = {
                        adaptationId: adaptationListJ.adaptationId,//条件编码
                        memberEntityType: adaptationListJ.adaptationType,//条件对象类型 O：商品 G.终端
                        memberEntityId: adaptationListJ.promOfferList[i].offerId,//条件对象编码 商品编码
                        identityId: promOfferListI.identityId,
                        memberEntityPkgId: adaptationListJ.offerPackId,//有线业务商品包
                      };
                    }
                  }
                  //选择了终端叠加活动
                  if (promOfferListI.atchPromotionList) {
                    this.atchPromotionSubmitList.push(...promOfferListI.atchPromotionList);
                  }
              }
                let checkInfoZhongDuan = {
                  adaptation_id: checkInfo.adaptationId,
                  offer_id: checkInfo.memberEntityId,
                  offer_pkgid: checkInfo.memberEntityPkgId,
                }
                this.promOffeZhongDuanList.push(checkInfoZhongDuan);

              console.log(checkInfo)
                this.promMemberList.push(checkInfo);
            }
          }
        }
        console.log(this.deliveryType)
        console.log(adaptationListJ.adaptationType)
        console.log(!(this.deliveryType =='2' && adaptationListJ.adaptationType == 'G'))
        if(!(this.deliveryType =='2' && adaptationListJ.adaptationType == 'G')) {
          if (adaptationListJ.offerMinNumber && adaptationListJ.offerMinNumber > count) {
            let str = "条件:[" + adaptationListJ.adaptationId + "]" + adaptationListJ.adaptationName + "的最小选择数量为"
              + adaptationListJ.offerMinNumber;
            if (adaptationListJ.offerMaxNumber && adaptationListJ.offerMaxNumber < count) {
              str += ",最大选择数量为" + adaptationListJ.offerMaxNumber;
            }
            this.$alert(str);
            return false;
          } else {
            if (adaptationListJ.offerMaxNumber && adaptationListJ.offerMaxNumber < count) {
              let str = "条件:[" + adaptationListJ.adaptationId + "]" + adaptationListJ.adaptationName
                + "的最大选择数量为" + adaptationListJ.offerMaxNumber;
              this.$alert(str);
              return false;
            }
          }
        }
      }
        if (this.promMemberList.length == 0 && this.adaptationList.length > 0) {
          if(this.terminalDeliveryInfo.deliveryModule !='4'){
            this.$alert('请选择商品');
            return false;
          }
        }
      //芝麻信用
      if(this.deliveryCreditType =='1'){
        this.busiType ="Calyx";
      }else if(this.deliveryCreditType =='123'){
        this.busiType ="promotion_xinyongka";
      }else if(this.deliveryCreditType =='124'){
        this.busiType ="INSTALLMENT_SAVINGS";
      }else if(this.deliveryCreditType =='125'){
        this.busiType ="promotion_zhongyi";
      }else{
        this.busiType ="promotion_center";
      }

      return true;
    },
    //信控提交后
    async insertXinKong(shopCartItemIdReturn){
      //芝麻信用
      if(this.deliveryCreditType =='1'){
        this.busiType ="Calyx";
        this.zhiMaItemId = shopCartItemIdReturn;
        let insertZhiMaShopFlag = await this.insertZhiMaShop(shopCartItemIdReturn);
        if (insertZhiMaShopFlag) {
          //直接提交
          if (this.preShopSubmitFlag) {
            this.shoppingCartDirectSubmission();
          } else {
            //前往购物车
            this.gotoCartFlag = true;
          }
        }
      }else if(this.deliveryCreditType =='123'){
        this.busiType ="promotion_xinyongka";
        //信用卡分期 贷款
        this.insertChuXuShop(shopCartItemIdReturn);
      }else if(this.deliveryCreditType =='124'){
        this.busiType ="INSTALLMENT_SAVINGS";
        //储蓄卡冻结 贷款
        this.insertChuXuShop(shopCartItemIdReturn);
      }else if(this.deliveryCreditType =='125'){
        this.busiType ="promotion_zhongyi";
        //中移担保模式 贷款
        this.insertChuXuShop(shopCartItemIdReturn);
      }else if(this.deliveryCreditType =='200' || this.deliveryCreditType =='201'){
        //直接提交
        if (this.preShopSubmitFlag) {
          this.shoppingCartDirectSubmission();
        } else {
          //前往购物车
          this.gotoCartFlag = true;
        }
      }


    }

  },

  mounted() {
    window['scanDianZiBack'] = (result) => {
      this.$toast('扫码成功');
      this.$set(this.adaptationList[this.dianZiIdx].promOfferList[this.dianZiSidx], 'identityId',result);
    }
  },
};

//终端类购物车
export const ZhongDuanShopCartMixin = {
  data() {
    return {

    }
  },
  computed: {

  },
  methods: {
    //芝麻积分申请
    applyPromAndFreeze(callback) {
      let params = {
        'serverNumber': callback.serverNumber,
        'orderId':callback.orderId,
        'caseNo':callback.caseNo,
        'status':callback.status,
        'crmAzOrderId':callback.crmAzOrderId,
        'zhiMaShopItem':callback.zhiMaShopItem,
        'storeId':callback.storeId,
        'storeName':callback.storeName,
      };
        let url = '/xsb/personBusiness/Calyx/h5applyPromAndFreeze';
        this.$http.post(url, params).then(res => {
          let result = res.data;
          if ('0' == result.retCode) {
            let payCode = result.data.payurl;
            if (payCode) {
              this.$router.push({
                path: '/promotionBuyQrCode',
                query: {
                  status: '3',
                  parUrl: payCode,
                  srcFrom: this.srcFrom,
                }
              });
            } else {
              this.$messagebox({
                title: '提示',
                message: '余额冻结成功',
                showCancelButton: false,
                showConfirmButton: true
              }).then(action => {
                if (action == 'confirm') {
                  this.$router.push({
                    path: '/promotionOrder',
                    query: {
                      srcFrom: this.srcFrom,
                      topTipFlag: '3',
                    }
                  })
                }
              });
            }

          } else {
            this.$alert(result.retMsg || '预约申请失败');
          }
        }).catch((response) => {
          this.$alert('预约申请网络超时');
        });
    },
  },
  mounted() {

  },
};
