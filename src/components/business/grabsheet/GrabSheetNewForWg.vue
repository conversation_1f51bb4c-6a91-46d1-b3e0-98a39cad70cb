<!--电渠抢单 首页-->
<template>
    <div class="grab-list">
        <Header :tsTitleTxt="headTitle" backType="custom" @emGoPrev="goPrev"></Header>
        <ul class="gl-tab">
            <li class="gl-tab-li" :class="{active:qdTabFlag==0}" @click="qdTab(0)">
		        <span class="gl-tab-txt">可抢单
			        <span class="gl-tabli-num">{{contentData.order_info_dt.untreatedTotalNumber>=10?'':contentData.order_info_dt.untreatedTotalNumber}}</span>
                </span>
            </li>
            <li class="gl-tab-li" :class="{active:qdTabFlag==1}" @click="qdTab(1)">
		        <span class="gl-tab-txt">我的订单
			        <span class="gl-tabli-num">{{contentData.order_info_dt.myUntreatedTotalNumber>=10?'':contentData.order_info_dt.myUntreatedTotalNumber}}</span>
                </span>
            </li>
        </ul>

        <div class="searchs">
            <div class="gl-search">
                <div class="gl-searchblock">
                    <span class="gl-ssicon" :style="{backgroundImage:'url(static/img/search.png)'}"></span>
                    <input type="text" placeholder="请输入11位手机号/寄送地址" class="gl-searchinpt" v-model="searchValue"
                           @keyup.enter="showSearch"> <span class="gl-ssclose" @click="removeSearch"
                                                            :style="{backgroundImage:'url(static/img/close.png)'}"></span>
                </div>
                <span class="gl-sst" @click="showSearch">搜索</span>
            </div>
            <!-- <div class="search-content" v-show="(showDataFlag && chooseSheetId == '1') || !showDataFlag">
                <span class="pai-count">近一个月已派{{myOrderedNumber}}，完成{{myFinshTotalNumber}}，完成率：{{completeRate}}%</span>
                <div class="search-choose" v-show="positionCode == 'GLY' && showDataFlag" @click="goReport">
                    <span class="iconfont shangyi go-report-jt"></span>
                </div>
            </div> -->
            <div class="search-content" v-show="showDataFlag">
                <span class="total">共{{grabSheetList.length}}条工单</span>
                <div class="search-choose">

                    <div class="choose" @click="dateFilter">
                        <span class="choose-name">{{chooseDateFilter}}</span>
                        <span class="iconfont jiantou choose-icon"></span>
                    </div>

                      <span class="search-title" v-show="chooseSheetId == '0' && serviceCode=='42041'">&#12288;</span>
                      <div class="choose" @click="orderStatusFilter" v-show="chooseSheetId == '0' && serviceCode=='42041'">
                        <span class="choose-name">{{chooseOrderStatusFilter}}</span>
                        <span class="iconfont jiantou choose-icon"></span>
                      </div>


                    <span class="search-title">&#12288;</span>
                    <div class="choose" @click="sheetFilter">
                        <span class="choose-name">{{chooseSheetFilter}}</span>
                        <span class="iconfont jiantou choose-icon"></span>
                    </div>
                </div>
            </div>
            <div class="search-content" v-show="!showDataFlag">
                <span class="total">共{{myOrderList.length}}条工单</span>
                <div class="search-choose">

                    <div class="choose" @click="dateFilter">
                        <span class="choose-name">{{chooseDateFilter}}</span>
                        <span class="iconfont jiantou choose-icon"></span>
                    </div>
                    <span class="search-title">&#12288;</span>
                    <div class="choose" @click="chooseSearch">
                        <span class="choose-name">{{chooseName}}</span>
                        <span class="iconfont jiantou choose-icon"></span>
                    </div>
                </div>
            </div>
        </div>
        <div class="gl-main">
            <mt-loadmore :top-method="loadTop" :bottom-method="loadBottom" :bottom-all-loaded="false"
                         :auto-fill="false"
                         ref="loadmore">
                <div v-show="showDataFlag" class="gl-mar">
                    <ul class="gl-ul">
                        <!-- 可抢单 -->
                        <li class="gl-list" v-show="chooseSheetId == '0'" v-for="(item,index) in grabSheetList" v-cloak
                            :key="index"
                            @click="qdListCk(item,index)"
                            :class="{active:item.active}">
                            <span class="gl-abs isBack" v-show="item.isBack=='1'">回退单</span>
                            <span class="gl-abs orderStatus">{{item.status}}</span>
                            <span class="gl-abs qiangdan"
                                  v-show="item.grabType=='07'">可抢</span>
                            <span class="gl-abs pai" v-show="item.grabType=='08'">可派</span>
                            <span class="delay"
                                  v-show="item.orderStatus!='88'"
                                  @click.stop="delay(item)">延时</span>
                            <div class="gl-ltit">
                                <span class="gl-ltitle">{{item.accNbrr}}</span>
                                <span class="gl-ts-txt orders">{{item.order_from}}</span>
                            </div>
                            <div class="gl-sublist">
                                订单号<span class="gl-sublist-order">{{item.orderId}}</span>
                            </div>
                            <div class="gl-sublist">
                                创建时间<span class="gl-sublist-val">{{item.create_time}}</span>
                            </div>
                            <!-- <div class="gl-sublist">
                              订单来源<span class="gl-sublist-val">{{item.order_from}}</span>
                            </div> -->
                            <div class="gl-sublist">
                                订单描述<span class="gl-sublist-val">{{item.remark}}</span>
                            </div>
                            <!-- <div class="gl-sublist">
                              抢单时间<span class="gl-sublist-val">{{item.grab_time}}</span>
                            </div>-->
                            <div class="gl-sublist">
                                联系方式<span class="gl-sublist-val">{{item.contactPhone || '无'}}</span>
                            </div>
                            <div class="gl-sublist">
                                寄送地址<span class="gl-sublist-val">{{item.shipAddr || '无'}}</span>
                            </div>
                            <div class="gl-sublist" v-show="item.dealMessage!=null&&item.dealMessage!=''">
                                抢单备注<span class="gl-sublist-val">{{item.dealMessage}}</span>
                            </div>
                          <div class="gl-sublist">
                            抢单类型<span class="gl-sublist-val" v-show="item.grabType=='07'">07(自动抢单模式)</span>
                            <span class="gl-sublist-val" v-show="item.grabType=='08'">08(管理员派单模式)</span>
                          </div>
                            <span class="gl-select-icon"> <span
                                    class="iconfont duihao gl-selefont"></span>
                            </span>
                        </li>
                        <!-- 已派单 -->
                        <li class="gl-list" v-show="chooseSheetId == '1'" v-for="(item,index) in successGrabList"
                            v-cloak
                            :key="index"
                            @click="pullClick(item)"
                            :class="{active:item.pullCheck}">
                            <span class="gl-state qiangdan">可拉回</span>
                            <div class="gl-ltit">
                                <span class="gl-ltitle">{{item.accNbrr}}</span>
                                <span class="gl-ts-txt orders">{{item.order_from}}</span>
                            </div>
                            <div class="gl-sublist">
                                订单号<span class="gl-sublist-order">{{item.orderId}}</span>
                            </div>
                            <div class="gl-sublist">
                                创建时间<span class="gl-sublist-val">{{item.create_time}}</span>
                            </div>
                            <!-- <div class="gl-sublist">
                              订单来源<span class="gl-sublist-val">{{item.order_from}}</span>
                            </div> -->
                            <div class="gl-sublist">
                                订单描述<span class="gl-sublist-val">{{item.remark}}</span>
                            </div>
                            <!-- <div class="gl-sublist">
                              抢单时间<span class="gl-sublist-val">{{item.grab_time}}</span>
                            </div>-->
                            <div class="gl-sublist">
                                联系方式<span class="gl-sublist-val">{{item.contactPhone || '无'}}</span>
                            </div>
                            <div class="gl-sublist">
                                寄送地址<span class="gl-sublist-val">{{item.shipAddr || '无'}}</span>
                            </div>
                            <span class="gl-select-icon"> <span
                                    class="iconfont duihao gl-selefont"></span>
                            </span>
                            <div class="gl-txt-state">
                                <span class="gl-ts-b green">{{item.status}}</span>
                            </div>
                        </li>
                    </ul>

                </div>
                <div v-show="!showDataFlag">

                    <ul class="gl-ul">
                        <li class="gl-list" v-for="(item,index) in myOrderList" v-cloak :key="index"
                            @click="sheetdetail(item)">
                            <h4>{{item.accNbrr}}</h4>
                            <div class="gl-sublist">
                                订单号<span class="gl-sublist-order">{{item.orderId}}</span>
                            </div>
                            <div class="gl-sublist" @click.stop="doNothing">
                                联系方式<a :href="'tel:'+item.contactPhone"><span class="gl-sublist-val-tel">{{item.contactPhone || '无'}}</span></a>
                            </div>
                            <div class="gl-sublist">
                                订单来源<span class="gl-sublist-val">{{item.order_from}}</span>
                            </div>
                            <div class="gl-sublist">
                                创建时间<span class="gl-sublist-val">{{item.create_time}}</span>
                            </div>
                            <div class="gl-sublist">
                                抢单时间<span class="gl-sublist-val">{{item.grab_time}}</span>
                            </div>
                            <div class="gl-sublist">
                                预约备注<span class="gl-sublist-val">{{item.dealMessage || '无'}}</span>
                            </div>
                            <div class="gl-sublist">
                                寄送地址<span class="gl-sublist-val">{{item.shipAddr || '无'}}</span>
                            </div>
                            <div class="pay-action" @click.stop="payAction(item)" v-if="item.hasFirstCharge=='1'">
                                <span class="pay-text">首充</span>
                            </div>
                            <div class="gl-txt-state">
                                <span class="gl-ts-a red"></span><span class="gl-ts-b green">{{item.status}}</span>
                            </div>
                        </li>
                    </ul>

                </div>
            </mt-loadmore>
            <NoDataPage class="nodata"
                        v-show="chooseSheetId == '0' && showDataFlag && (!grabSheetList || grabSheetList.length < 1)"
                        tipTxt="暂无订单记录"></NoDataPage>
            <NoDataPage class="nodata" v-show="chooseSheetId == '1' && (!successGrabList || successGrabList.length < 1)"
                        tipTxt="暂无订单记录"></NoDataPage>
            <NoDataPage class="nodata" v-show="!showDataFlag && (!myOrderList || myOrderList.length < 1)"
                        tipTxt="暂无订单记录"></NoDataPage>
        </div>
        <div class="ml-bottom wrapper-medias" @click="paidan()" v-show="chooseSheetId == '0' && bottomButton">
            <div class="btn-style-blue">确定</div>
        </div>

        <div class="ml-bottom wrapper-medias" @click="pullSheet()" v-show="chooseSheetId == '1' && bottomButton">
            <div class="btn-style-blue">拉回</div>
        </div>
    </div>

</template>
<script>
    import Header from "components/common/Header.vue";
    import Storage from '@/base/storage.js';
    import NlDropdown from 'components/common/NlDropdown/dropdown.js'
    import NoDataPage from 'components/common/NoDataPage.vue'
    import { dateFormat } from "@/base/utils";
    import NlDatePicker from "components/common/NlDatePick/datePicker.js";
    export default {
        components: {Header, NoDataPage},
        data() {
            return {
                headTitle: '网格通抢单',
                searchValue: '',//搜索内容
                showDataFlag: true, // true-切换可派单，false-切换我的订单
                shownav: true,
                qdFlag: 0,	//切换的值
                qdTabFlag: 0, //0可抢单，1我的订单切换
                serviceCode: '42041',
                sheetType: '1',
                qdTabList: [],
                qdTabMyList: [],
                serviceCodeList: [42041, 42041, 42041, 42041, 42041,42041],
                contentData: {
                    order_info_dt: {
                        myUntreatedTotalNumber: '', //我的订单-待处理总数
                        myUntreatedEchannelNumber: '', //我的订单-电渠抢单
                        untreatedTotalNumber: '', //可抢单-总数
                        untreatedEchannelNumber: '' //可抢单-电渠数量
                    }
                },
                ScheduleTypeEnum: [
                    {value: '100', description: '订单中心'},
                    {value: '101', description: '营业厅'},
                    {value: '102', description: '网厅'},
                    {value: '103', description: '掌厅'},
                    {value: '104', description: '终端商城'},
                    {value: '105', description: '行商'},
                    {value: '106', description: 'CSP'},
                    {value: '107', description: '支付中心'},
                    {value: '108', description: '集团网厅'},
                    {value: '109', description: '天猫'},
                    {value: '110', description: '掌中店'},
                    {value: '111', description: 'AMS'},
                    {value: '112', description: '短厅'},
                    {value: '113', description: '上行下达'},
                    {value: '114', description: 'GIMS'},
                    {value: '115', description: '京东'},
                    {value: '116', description: '中移互联网'},
                    {value: '117', description: '在线公司'},
                    {value: '1171', description: '在线公司(号卡)'},
                    {value: '118', description: '便利店'},
                    {value: '119', description: '动力100'},
                    {value: '120', description: '和力云'},
                    {value: '121', description: '苏宁'},
                    {value: '122', description: '热线'},
                    {value: '123', description: '京东2'},
                    {value: '124', description: '一级自有电渠'},
                    {value: '125', description: 'ICT云平台'},
                    {value: '126', description: '在线一体化'},
                    {value: '127', description: '10086热线'},
                    {value: '128', description: '中高端存量运营'},
                    {value: '129', description: '一级外部电渠'},
                    {value: '130', description: '一级能力开放平台'},
                    {value: '131', description: '一级外部其他电渠'},
                ],
                ScheduleStatusEnum: [
                    {value: '-1', description: '已作废'},
                    {value: '0', description: '订单创建'},
                    {value: '10', description: '待同步'},
                    {value: '11', description: '待确认'},
                    {value: '20', description: '待支付'},
                    {value: '21', description: '已付款'},
                    {value: '22', description: '订单抢单'},
                    {value: '30', description: '待审核'},
                    {value: '31', description: '审核通过'},
                    {value: '32', description: '审核不通过'},
                    {value: '4', description: '待实名认证'},
                    {value: '40', description: '业务办理中'},
                    {value: '5', description: '订单缓存'},
                    {value: '50', description: '施工中'},
                    {value: '58', description: '服务开通'},
                    {value: '59', description: '办理成功'},
                    {value: '6', description: '订单异常缓存'},
                    {value: '60', description: '待发货'},
                    {value: '61', description: '办理失败'},
                    {value: '62', description: '等待中断'},
                    {value: '63', description: '临时号超时释放'},
                    {value: '64', description: '开户成功'},
                    {value: '65', description: '写卡失败'},
                    {value: '66', description: '待申请写卡数据'},
                    {value: '67', description: '待接收卡上发的写卡数据申请'},
                    {value: '68', description: '待接收验证码'},
                    {value: '69', description: '已下发写卡短信'},
                    {value: '70', description: '(已发货)待签收'},
                    {value: '71', description: '退款审核中'},
                    {value: '72', description: '已写卡'},
                    {value: '74', description: '待激活'},
                    {value: '80', description: '安装失败'},
                    {value: '87', description: '订单终止'},
                    {value: '88', description: '交易成功'},
                    {value: '90', description: '撤单中'},
                    {value: '91', description: '已撤单，退款中'},
                    {value: '92', description: '退款失败'},
                    {value: '93', description: '撤单失败'},
                    {value: '99', description: '撤单成功'},
                ],
                grabSheetList: [], // 可抢单列表
                myOrderList: [], // 我的订单列表
                listItemshow: [],
                selectdata: [],//已选择订单
                bottomButton: true,
                pageNo: 1,
                siteId: '',//携号转网的组织机构id
                positionCode: '', // 人员职位  GLY-管理员 YG-员工
                effectList: [
                    {id: '0', label: '全 部'},
                    {id: '2',label: '业务办理中'},
                    {id: '3',label: '待发货'},
                    {id: '4',label: '已完成'}],
                chooseName: '全 部',//选择的名字
                sheetFilterList: [
                    {id: '0', label: '可抢单'},
                    {id: '1', label: '已派单'}],
                chooseSheetFilter: '可抢单',
                chooseSheetId: '0',
                orderStatusFilterList:[
                    {id: '0',label: '全 部'},
                    {id: '40',label: '业务办理中'},
                    {id: '90',label: '撤单中'},
                    {id: '93',label: '撤单失败'},
                    {id: '99',label: '撤单成功'}
                ],
                chooseOrderStatusFilter: '业务办理中',
                chooseOrderStatusId: '40',
                dateFilterList:[
                    {id:'0',label: '自定义'},
                    {id: '1',label: '近1天'},
                    {id: '3',label: '近3天'},
                    {id: '7',label: '近7天'},
                    {id: '10',label: '近10天'},
                    {id: '15',label: '近15天'},
                    {id: '30',label: '近30天'},
                    {id: '',label: '全 部'}
                ],
                chooseDateFilter: '近7天',
                chooseDateId: '7',
                successGrabList: [],
                crmId: '',
                region: '',
                myOrderedNumber: '-', // 已派单数量
                myFinshTotalNumber: '-', // 派单中交易已完成数量
                completeRate: '-', // 完成率
                perClick:10000,
                showOrderStatus: '',
            };
        },
        created() {
              this.initDate();
        },
        activated() {
            let userInfo = Storage.session.get('userInfo');
            this.crmId = userInfo.crmId;
            this.region = userInfo.region;
            let doRefresh = this.$route.query.doGradSheetRefresh;
            if (doRefresh == '1') {
                // 做了提交操作或首次进入，重新加载
                this.grabSheetList = [];
                this.myOrderList = [];

                this.contentData = {
                    order_info_dt: {
                        myUntreatedTotalNumber: '', //我的订单-待处理总数
                        myUntreatedEchannelNumber: '', //我的订单-电渠抢单
                        untreatedTotalNumber: '', //可抢单-总数
                        untreatedEchannelNumber: '' //可抢单-电渠数量
                    }
                };
                this.getDispatch(this.serviceCode);
            }
        },
        methods: {
            goPrev() {
                this.$router.push({
                    path: '/business',
                });
            },
            //首充的跳转
            payAction(item) {
                //获取我的订单获取维码
                let param = {
                    "orderId": item.orderId, // 订单编号
                    "telnum": item.accNbrr, // 用户手机号
                    "stationId": Storage.session.get('userInfo').stationId, // 岗位编号
                    "orderFrom":item.orderFrom,//订单来源
                    "operTelnumber":Storage.session.get('userInfo').servNumber,//操作员手机号
                    'deviceType': /android|harmony/gi.test(navigator.userAgent) ? 'ANDROID' : 'IOS', // 设备类型 android,ios
                    'location': Storage.get('location'),//位置信息
                    'latitude': Storage.get('latitude'),//纬度
                    'longitude': Storage.get('longitude'),//经度
                };
                let url = '/xsb/personBusiness/telRecharge/h5GoRechargeUrl';
                this.$http.post(url, param).then((res) => {
                    let {retCode, data, retMsg} = res.data;
                    if (retCode == '0') {  //如果成功则跳转页面
                        let datas = {
                            'orderId': item.orderId,
                            'telnum': item.accNbrr,
                            'flag': '1',
                            'payUrl': data,
                            'orderFrom':item.orderFrom
                        };
                        this.$router.push({
                            path: '/echanelPay',
                            query: datas
                        })
                    } else {
                        this.$alert(retMsg || '首充失败');
                    }
                }).catch((response) => {
                    this.$alert(`首充网络超时${response}`);
                })

            },
            //我的订单筛选
            chooseSearch() {
                let self = this;
                NlDropdown({
                    confirmBtn: false,
                    datalist: self.effectList,
                }, function (res) {
                    self.chooseName = res.label;
                    self.searchValue = '';
                    self.h5grabsheetMessage(1);
                });
            },
            //订单状态筛选
            orderStatusFilter(){
                let self = this;
                NlDropdown({
                    confirmBtn: false,
                    datalist: self.orderStatusFilterList,
                }, function (res) {
                    self.chooseOrderStatusFilter = res.label;
                    self.chooseOrderStatusId = res.id;
                    self.h5grabsheetMessage(1);
                });
            },

            //时间筛选
            dateFilter(){
                let self = this;
                NlDropdown({
                    confirmBtn: false,
                    datalist: self.dateFilterList,
                }, function (res) {
                    self.chooseDateFilter = res.label;
                    self.chooseDateId = res.id;
                    if(self.chooseDateId=='0'){
                        //选择了自定义，则打开时间控件
                        self.openDatePicker();
                    }else{
                        //选择具体的天数，直接调用接口
                        if(self.chooseSheetId=='0'){
                           self.h5grabsheetMessage(1);
                        }
                        if(self.chooseSheetId=='1'){
                            self.getSuccessGrabList(1);
                        }
                    }
                });
            },

            // 可抢单筛选
            sheetFilter() {
                let self = this;
                NlDropdown({
                    confirmBtn: false,
                    datalist: self.sheetFilterList,
                }, function (res) {
                    self.chooseSheetFilter = res.label;
                    self.chooseSheetId = res.id;
                    if (res.id == '0') {
                        self.h5grabsheetMessage(1);
                    }
                    if (res.id == '1') {
                        self.getSuccessGrabList(1);
                      //  self.countGrabNumber(); 订单中心获取接口数据存在问题，先注销此方法
                    }
                });
            },
            loadTop() {
                // 刷新数据
                this.$refs.loadmore.onTopLoaded();
                if (this.chooseSheetId == '0') {
                    this.h5grabsheetMessage(1);
                }
                if (this.chooseSheetId == '1') {
                    this.getSuccessGrabList(1);
                }
            },
            loadBottom() {
                this.$refs.loadmore.onBottomLoaded();
                this.pageNo++;
                if (this.chooseSheetId == '0') {
                    this.h5grabsheetMessage(2);
                }
                if (this.chooseSheetId == '1') {
                    this.getSuccessGrabList(2);
                }
            },
            qdTabCk(n) {
                this.qdFlag = n;
                if (this.qdTabFlag == 0) {
                    //待抢单
                    this.shownav = true;
                    this.sheetType = "1";
                } else if (this.qdTabFlag == 1) {
                    //我的订单
                    this.shownav = false;
                    this.sheetType = "2";
                }
                //判断业务类型30018就添加serviceCode
                this.serviceCode = this.serviceCodeList[n];
                if(this.perClick!=this.serviceCode){
                  if(this.serviceCode=='30018'){
                    this.getDispatchServiceCodeOnly('30018');
                  }else if(this.perClick=='30018'){
                    this.getDispatchServiceCodeOnly('');
                  }else{
                    this.h5grabsheetMessage(1);
                  }
                }else{
                  this.h5grabsheetMessage(1);
                }

            },
            qdTab(n) {
                this.chooseSheetFilter = '可抢单';
                this.chooseSheetId = '0';
                this.qdTabFlag = n;
                this.chooseName = '全 部';//初始化
                this.searchValue = '';//清空文本框
                if (n == 1) {
                    this.qdTabCk(0);
                    this.showDataFlag = false;
                    this.bottomButton = false;
                   // this.countGrabNumber(); 订单中心获取数据接口有问题，先进行注销
                } else if (n == 0) {
                    this.qdTabCk(0);
                    this.showDataFlag = true;
                    this.bottomButton = true;
                }
            },
            // 查询人员信息
            getDispatch(serviceCode) {
                let reqCode = serviceCode=='30018'?'30018':'';
                this.perClick = serviceCode;
                let param = {
                    testFlag: "0",
                    version: "0.1",
                    userName: this.crmId,
                    templateCode: "TMPL_ORDER_NEWQUERY_DISTRIBUTOR",
                    serviceCode:reqCode
                };
                this.$http.post('/xsb/personBusiness/grabsheet/h5dispatchForWgt', param).then(res => {
                    let result = res.data;
                    if ('0' == result.retCode) {
                        let list = result.data;
                        if (list.length > 0) {
                            this.siteId = list[0].agencyCode;
                            this.positionCode = list[0].positionCode;
                            this.initSheet();
                        //    this.grabsheetnum(); 订单中心获取接口数据存在问题，先注销此方法
                        }
                    } else {
                        this.$alert(result.retMsg || '查询人员信息失败');
                    }
                }).catch((response) => {
                    this.$alert('查询人员信息异常: ' + response);
                });
            },
            //初始化订单列表
            initSheet() {
                if (this.positionCode == 'GLY') {
                    this.qdTab(0);
                } else {
                    this.qdTab(1);
                }
            },
            //获取订单列表信息
            h5grabsheetMessage(num) {
                if (!this.siteId) {
                    this.$alert('请确认你要办理的业务（在页面上部tab切换），是否在订单中心配置了组织关系，请联系订单中心确认！');
                    return;
                }
                if (num == 1) {
                    this.pageNo = 1;//第一页
                    this.grabSheetList = [];
                    this.myOrderList = [];
                    this.selectdata = [];//置空已选择订单
                }

                let paramObj = {};
                paramObj.testFlag = "0";//标记
                paramObj.version = "0.1";//版本号
                paramObj.templateCode = "TMPL_ORDER_NEWGRAB_LIST";//模板编码
                //所属机构编码
                paramObj.siteId = this.siteId;
                //判断列表页面
                paramObj.queryType = this.sheetType;
                //我的订单状态参数设置
                if(this.sheetType == '2'){
                    if(this.chooseName == '未完成'){
                        paramObj.orderStatus = '40|72|101|102|89|70|60|74';
                    }else if(this.chooseName == '业务办理中'){
                        paramObj.orderStatus='40';
                    }else if(this.chooseName == '待发货'){
                        paramObj.orderStatus='60';
                    }else if(this.chooseName == '已完成'){
                        paramObj.orderStatus='88';
                    }
                }
                //时间的筛选(非自定义)
                paramObj.startTime=this.chooseDateId;
                //自定义的时间筛选控件(新增两个字段)
                paramObj.startDate=this.startDate;
                paramObj.endDate=this.endDate;
                //判断选择的订单状态
                if(this.sheetType == '1' && this.chooseOrderStatusFilter!='全 部' && this.chooseSheetId == '0' && this.serviceCode =='42041'){
                    paramObj.orderStatus = this.chooseOrderStatusId;
                }
                //分页
                paramObj.pageIndex = this.pageNo;//第几页
                paramObj.pageSize = 30; //每页多少条
                if(this.serviceCode=='10000'){
                    paramObj.pageSize = 100;
                }

                //根据服务编码区分业务类型
                paramObj.serviceCode = this.serviceCode;
                paramObj.grabStaff = this.crmId;

                let url = '/xsb/personBusiness/grabsheet/h5grabsheetMessageForWgt';
                this.$http.post(url, paramObj).then(res => {
                    let result = res.data;
                    if ('0' == result.retCode) {
                        if (this.sheetType == "1") {
                            this.setGrabSheetList(result, this.pageNo);
                        }
                        if (this.sheetType == "2") {
                            this.setMyOrderList(result, this.pageNo);
                        }
                    } else {
                        this.$alert(result.retMsg || '查询抢单信息失败');
                    }
                }).catch((response) => {
                    this.$alert('查询抢单信息异常: ' + response);
                });
            },
            // 设置可抢单列表
            setGrabSheetList(result, pageIndex) {
                if (1 == pageIndex) {
                    //页面为第一页
                    this.grabSheetList = result.data;
                } else {
                    //继续加载更多数据
                    let tempList = result.data;
                    tempList.forEach((item, index) => {
                        this.grabSheetList.push(item);
                        Vue.set(item, 'active', false);
                    });
                }
                this.grabSheetList.forEach((item, index) => {
                    item.create_time = this.dateFormat(item.createTime);
                    item.grab_time = this.dateFormat(item.grabTime);
                    item.order_from = this.getDescriptionById(this.ScheduleTypeEnum, item.orderFrom);
                    item.status = this.getDescriptionById(this.ScheduleStatusEnum, item.orderStatus);
                    // 第一页重置选择
                    if (1 == pageIndex) {
                        Vue.set(item, 'active', false);
                    }
                });
            },
            // 设置我的订单列表
            setMyOrderList(result, pageIndex) {
                if (1 == pageIndex) {
                    //页面为第一页
                    this.myOrderList = result.data;
                } else {
                    //继续加载更多数据
                    let tempList = result.data;
                    tempList.forEach((item, index) => {
                        Vue.set(item, 'active', false);
                        this.myOrderList.push(item);
                    });
                }
                this.myOrderList.forEach((item, index) => {
                    item.create_time = this.dateFormat(item.createTime);
                    item.grab_time = this.dateFormat(item.grabTime);
                    item.order_from = this.getDescriptionById(this.ScheduleTypeEnum, item.orderFrom);
                    item.status = this.getDescriptionById(this.ScheduleStatusEnum, item.orderStatus);
                    // 第一页重置选择
                    if (1 == pageIndex) {
                        Vue.set(item, 'active', false);
                    }
                });
            },
            //转换订单来源和状态
            getDescriptionById(enums, id) {
                let description = '';
                for (let i = 0; i < enums.length; i++) {
                    if (enums[i].value != undefined && enums[i].value == id) {
                        description = enums[i].description;
                    }
                }
                return description;
            },
            //选择订单
            qdListCk(item, index) {
                if (item.active) {
                    item.active = false;
                    for (let i = 0; i < this.selectdata.length; i++) {
                        if (item.orderId == this.selectdata[i].orderId) {
                            this.selectdata.splice(i, 1);
                        }
                    }
                } else {
                    if (this.selectdata[0]) {
                        let type = this.selectdata[0].grabType;
                        let dtype = item.grabType;
                        if (type == '07') {
                            if (dtype == '07') {
                                item.active = true;
                                Vue.set(item, 'keyNum', index);
                                this.selectdata.push(item);
                            } else {
                                this.$toast('抢单和派单不能同时进行');
                            }
                        } else if (type == '08') {
                            if (dtype == '08') {
                                item.active = true;
                                Vue.set(item, 'keyNum', index);
                                this.selectdata.push(item);
                            } else {
                                this.$toast('抢单和派单不能同时进行');
                            }
                        }
                    } else {
                        item.active = true;
                        Vue.set(item, 'keyNum', index);
                        this.selectdata.push(item);
                    }
                }
            },
            showSearch() {
                this.searchSheet();
                this.pageNo = 0;//第一页
            },
            //搜索订单
            searchSheet() {
                if (!this.siteId) {
                    this.$alert('请确认你要办理的业务（在页面上部tab切换），是否在订单中心配置了组织关系，请联系订单中心确认！');
                    return;
                }
                this.selectdata = []; //置空已选择订单
                //清除打钩
                let url = '/xsb/personBusiness/grabsheet/h5grabsheetMessageForWgt';
                let paramObj = {};
                if (!this.searchValue) {
                    this.$toast('搜索内容不能为空');
                } else {
                    if (this.checkNumber(this.searchValue) == true) {
                        paramObj.accNbr = this.searchValue; //手机号或者业务号码
                    } else {
                        paramObj.shipAddr = this.searchValue; //寄送地址
                    }
                    paramObj.testFlag = "0"; //测试标记
                    paramObj.version = "0.1"; //版本号
                    paramObj.templateCode = "TMPL_ORDER_NEWGRAB_LIST"; //模板编码

                    //所属机构编码
                    paramObj.siteId = this.siteId;
                    //判断列表页面
                    paramObj.queryType = this.sheetType;
                    //判断未完成和全部
                    if (this.sheetType == "2" && this.chooseName == '未完成') {
                        paramObj.orderStatus = '40|72|101|102|89|70|60|74';
                    }
                    //根据服务编码区分业务类型
                    paramObj.serviceCode = this.serviceCode;
                    paramObj.grabStaff = this.crmId;
                    //获取订单列表信息
                    this.$http.post(url, paramObj).then(res => {
                        let result = res.data;
                        if ('0' == result.retCode) {
                            if (this.sheetType == "1") {
                                this.setGrabSheetList(result, 1);
                            }
                            if (this.sheetType == "2") {
                                this.setMyOrderList(result, 1);
                            }
                        } else {
                            this.grabSheetList = [];
                            this.myOrderList = [];
                            this.$alert(result.retMsg || '查询抢单信息失败');
                        }
                    }).catch((response) => {
                        this.$alert('查询抢单信息异常: ' + response);
                    });
                }
            },
            //清除输入搜索内容
            removeSearch() {
                this.searchValue = "";
            },
            //点击查看我的订单详情
            sheetdetail(item) {
                if (item.serviceCode == '10000' || item.serviceCode == '42000') {
                    let sheetdata = [];
                    sheetdata.push(item);
                    this.$router.push({
                        path: '/sheetDetailNewForWg',
                        query: {
                            selectdata: JSON.stringify(sheetdata),
                            siteId: this.siteId
                        }
                    });
                } else if (item.serviceCode == '42041' || item.serviceCode == '30000' || item.serviceCode == '30054' || item.serviceCode == '30018') {
                    this.$router.push({
                        path: '/reserveSheetDetailForWg',
                        query: {
                            orderItem: item,
                            siteId: this.siteId
                        }
                    });
                } else {
                    this.$alert("service_code未知类型");
                }
            },
            //点击确定按钮
            paidan() {
                if (null == this.selectdata || '' == this.selectdata) {
                    this.$toast('请选择订单');
                    return;
                }
                if (this.selectdata[0].grabType=='08'){
                  //跳转派单页面 sheetList.vue
                  this.$router.push({
                    path: '/sheetListNewForWg',
                    query: {
                      siteId: this.siteId,
                      selectdata: JSON.stringify(this.selectdata),
                      servicecode: this.serviceCode,
                    }
                  });
                }else{
                  //如果是07模式，不跳转页面直接提示，点击确认自动执行自动派单给自己
                  this.$messagebox({
                    title: '提示',
                    message: '当前操作为自动抢单，抢单后可在我的订单中查询，是否继续？',
                    showCancelButton: true,
                    showConfirmButton: true
                  }).then(action => {
                    if (action == 'confirm') {
                      //遍历orderId
                      let orderIdArr = [];
                      for (let i = 0; i < this.selectdata.length; i++) {
                        orderIdArr.push(this.selectdata[i].orderId);
                      }
                      let paramOrderId = orderIdArr.join(','); // 多订单 用英文',' 拼接
                      let param = {
                        testFlag: "0",
                        version: "0.1",
                        actionCode: "YWZX", //动作编码
                        templateCode: "TMPL_ORDER_PROCESS_GRAB_YWZX",
                        orderId: paramOrderId, //订单编号
                        dealType: "2", //处理状态 0：修改预约时间、抢单备注，1：锁单，2：抢单/派单，3：转派，4：抢单人员主动退单
                        dealStaff: this.crmId, //抢单人工号
                        appointmentTime: "", //预约时间
                        dealMessage: "",   //处理描述
                      };
                      this.$http.post('/xsb/personBusiness/grabsheet/h5accountExe', param).then(res => {
                        let result = res.data;
                        if ('0' == result.retCode) {
                          this.$messagebox.alert('操作成功', '温馨提示').then(action => {
                              //跳转到我的订单页面
                              this.qdTab(1);
                            }
                          );
                        } else {
                          this.$alert(result.retMsg);
                        }
                      });
                    }
                  });

                }
            },
            //验证字符串是否是数字
            checkNumber(theObj) {
                let reg = /^[0-9]+.?[0-9]*$/;
                return reg.test(theObj);
            },
            //时间格式化
            dateFormat(val) {
                let formatTime = '';
                if (val != '') {
                    let year = val.substring(0, 4);
                    let mon = val.substring(4, 6);
                    let day = val.substring(6, 8);
                    let hour = val.substring(8, 10);
                    let min = val.substring(10, 12);
                    let sec = val.substring(12, 14);
                    formatTime = year + "-" + mon + "-" + day + " " + hour + ":" + min + ":" + sec;
                }
                return formatTime;
            },
            // 获取已派单信息列表
            getSuccessGrabList(num) {
                if (num == 1) {
                    this.pageNo = 1;//第一页
                    this.successGrabList = [];
                    this.selectdata = [];//置空已选择订单
                }
                let params = {
                    testFlag: '0',
                    version: '0.1',
                    templateCode: 'TMPL_ORDER_NEWGRAB_LIST',
                    grabStatus: '2',
                    siteId: this.siteId,
                    queryType: '3',
                    pageIndex: this.pageNo,
                    pageSize: 50,
                    serviceCode: this.serviceCode,
                    grabStaff: this.crmId,
                    startTime: this.chooseDateId,
                    startDate: this.startDate,
                    endDate: this.endDate
                };
                let url = '/xsb/personBusiness/grabsheet/h5GetSuccessGrabListForWgt';
                this.$http.post(url, params).then(res => {
                    let result = res.data;
                    if ('0' == result.retCode) {
                        if (1 == this.pageNo) {
                            //页面为第一页
                            this.successGrabList = result.data;
                        } else {
                            //继续加载更多数据
                            let tempList = result.data;
                            tempList.forEach((item, index) => {
                                Vue.set(item, 'pullCheck', false);
                                this.successGrabList.push(item);
                            });
                        }

                        this.successGrabList.forEach((item, index) => {
                            item.create_time = this.dateFormat(item.createTime);
                            item.grab_time = this.dateFormat(item.grabTime);
                            item.order_from = this.getDescriptionById(this.ScheduleTypeEnum, item.orderFrom);
                            item.status = this.getDescriptionById(this.ScheduleStatusEnum, item.orderStatus);
                            if (this.pageNo == 1) {
                                Vue.set(item, 'pullCheck', false);
                            }
                        });
                    }
                    /*else {
                        this.$alert(result.retMsg);
                    }*/
                }).catch((response) => {
                    this.$alert('获取已抢单信息列表异常: ' + response);
                });
            },
            pullClick(item) {
                if (item.pullCheck) {
                    item.pullCheck = false;
                } else {
                    item.pullCheck = true;
                }
            },
            pullSheet() {
                let pullList = this.successGrabList.filter((item => item.pullCheck));
                if (!pullList || pullList.length < 1) {
                    this.$toast('请选择订单');
                    return;
                }
                this.$router.push({
                    path: '/pullSheetListForWg',
                    query: {
                        siteId: this.siteId,
                        pullList: JSON.stringify(pullList),
                        serviceCode: this.serviceCode,
                    }
                });
            },
            // 统计网格订单数量
            countGrabNumber() {
                this.myOrderedNumber = '-';
                this.myFinshTotalNumber = '-';
                this.completeRate = '-';
                let params = {
                    testFlag: '0',
                    version: '0.1',
                    templateCode: 'TMPL_ORDER_COUNTGRAB_NUMBER',
                    siteId: this.siteId,
                    positionCode: this.positionCode,
                    unLoadFlg: true
                };
                let url = `/xsb/personBusiness/grabsheet/h5CountGrabNumber`;
                this.$http.post(url, params).then(res => {
                    let result = res.data;
                    if ('0' == result.retCode) {
                        let countGrabNumberData = result.data;
                        this.myOrderedNumber = countGrabNumberData.myOrderedNumber;
                        this.myFinshTotalNumber = countGrabNumberData.myFinshTotalNumber;
                        this.completeRate = countGrabNumberData.completeRate * 100;
                    } else {
                        this.$alert(result.retMsg || '统计网格订单数量失败');
                    }
                }).catch((response) => {
                    this.$alert('统计网格订单数量异常' + response);
                });
            },
            goReport() {
                this.$router.push({
                    path: '/grabSheetReport',
                    query: {
                        siteId: this.siteId,
                        positionCode: this.positionCode
                    }
                });
            },
            // 订单延时
            delay(item) {
                this.$messagebox({
                    title: '提示',
                    message: '订单号: ' + item.orderId + ', 确认延时?',
                    showCancelButton: true,
                    showConfirmButton: true
                }).then(action => {
                    if (action == 'confirm') {
                        this.doDelay(item)
                    }
                });
            },
            //打开时间控件
      openDatePicker() {
      NlDatePicker(
        {
          startDate: this.startDate,
          endDate: this.endDate,
        },
        (retVal) => {
          //获取返回回调
          this.startDate = retVal.startDate;
          this.endDate = retVal.endDate;
          if (new Date(this.startDate) > new Date(this.endDate)) {
            this.$alert("开始时间不能大于结束时间");
            this.startDate = this.initSTime;
            this.endDate = this.initETime;
            return;
          }
          if (this.compareNow(this.endDate)) {
            this.endDate = this.initETime;
            this.$alert("结束时间不能大于当天");
            return;
          }
          //时间控制点确定的操作
          if(this.chooseSheetId=='0'){
            this.h5grabsheetMessage(1);
          }
          if(this.chooseSheetId=='1'){
              this.getSuccessGrabList(1);
          }
        }
      );
    },

    compareNow(time) {
      var time = time.replace(/-/g, "/");
      var tody = new Date();
      var nian = tody.getFullYear();
      var yue = tody.getMonth() + 1;
      var day = tody.getDate();
      var nowDate = new Date(nian + "/" + yue + "/" + day);
      if (Date.parse(time) - Date.parse(nowDate) > 0) {
        return true;
      } else {
        return false;
      }
    },
    //初始化时间控制日期
    initDate() {
      var today = new Date();
      var beforetoday = new Date();
      today.setDate(today.getDate());
      beforetoday.setMonth(beforetoday.getMonth() - 1);
      beforetoday.setDate(beforetoday.getDate());
      this.startDate = dateFormat(beforetoday, "yyyy/MM/dd");
      this.endDate = dateFormat(today, "yyyy/MM/dd");
      this.initSTime = this.startDate;
      this.initETime = this.endDate;
    },
            doDelay(item) {
                let params = {
                    testFlag: '0',
                    version: '0.1',
                    actionCode: 'YWZX',
                    templateCode: 'TMPL_ORDER_PROCESS_GRAB_YWZX',
                    orderId: item.orderId,
                    dealType: '9',
                    dealMessage: '',
                    dealStaff: this.crmId,
                    appointment_time: item.appointmentTime,
                    delayTime: '120'
                };

                let url = '/xsb/personBusiness/grabsheet/h5PullGrabList';
                this.$http.post(url, params).then(res => {
                    let result = res.data;
                    if ('0' == result.retCode) {
                        this.$alert('订单延时成功');
                    } else {
                        this.$alert(result.retMsg || '订单延时失败');
                    }
                }).catch((response) => {
                    this.$alert('订单延时异常' + response);
                });
            },
            doNothing(){},
        }
    }
</script>

<style scoped lang="less">
    .grab-list {
        background: #ECF0FA;
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        display: flex;
        flex-direction: column;
    }

    .gl-tab {
        margin-top: 44px;
        height: 40px;
        background: #F1F1F1;
        border-top: 1px solid #EAEAEA;
    }

    .gl-tab.tablist1 .gl-tab-li {
        width: 100%;
    }

    .gl-tab-txt {
        position: relative;
    }

    .gl-tabli-num {
        width: 10px;
        height: 10px !important;
        border-radius: 50%;
        background: #F43A45;
        position: absolute;
        top: -8px;
        right: 6px;
        color: #fff;
        font-size: 10px;
        text-align: center;
        line-height: 10px;
        padding: 1px;
        border-bottom: none !important;
    }

    .gl-sub-tab {
        padding-top: 4px;
        height: 36px;
        display: flex;
        flex-wrap: nowrap;
        overflow: scroll;
        flex-shrink: 0;
        background: #fff;
        -webkit-overflow-scrolling: touch;
    }

    .gl-sub-tli {
        display: block;
        text-align: center;
        flex-shrink: 0;
        width: auto;
        margin-left: 20px;
        color: #585858;

        line-height: 36px;
        font-size: 14px;
    }

    .gl-sub-tli.active {
        color: #187BEC;
    }

    .gl-main {
        background: #ECF0FA;
        overflow: scroll;
        flex-grow: 1;
        -webkit-overflow-scrolling: touch;
        margin: 10px 0 0 0;
    }

    .gl-search {
        padding-bottom: 14px;
        background: #fff;
        flex-shrink: 0;
        position: relative;
        overflow: hidden;
    }

    .gl-mar {
        margin-bottom: 60px;
    }

    .gl-ul {
        display: block;
        height: auto;
        margin: 0 10px 10px 10px;
    }

    .gl-list {
        height: auto;
        display: block;
        background: #FFFFFF;
        box-shadow: 4px 4px 12px 0 rgba(184, 184, 184, 0.50);
        border-radius: 8px;
        overflow: hidden;
        margin-bottom: 10px;
        position: relative;
        padding-bottom: 5px;
    }

    .pay-action {
        border-top: 1px solid #dedede;
        padding: 8px 12px 0 0;
        text-align: right;
        .pay-text {
            width: 54px;
            height: 26px;
            font-size: 12px;
            border-radius: 15px;
            border: 1px solid rgba(0, 129, 255, 1);
            color: rgba(0, 129, 255, 1);
            line-height: 26px;
            text-align: center;
            display: inline-block;
        }

    }

    .gl-txt-state {
        position: absolute;
        right: 10px;
        top: 10px;
        height: 20px;
        line-height: 20px;
        font-size: 14px;
    }

    .gl-ts-a.red {
        color: red;
    }

    .gl-ts-a.yellow {
        color: yellow;
    }

    .gl-ts-b {
        margin-left: 2px;
    }

    .gl-ts-a {
        margin-right: 2px;
    }

    .gl-ts-b.grey {
        color: #333;
    }

    .gl-ts-b.green {
        color: #63d163;
    }

    .gl-list h4 {
        text-indent: 12px;
        margin-top: 10px;
        margin-bottom: 8px;
    }

    .gl-ltit {
        height: 22px;
        font-size: 16px;
        font-weight: 600;
        color: rgba(60, 60, 60, 1);
        line-height: 22px;
        text-indent: 12px;
        margin-top: 10px;
        margin-bottom: 8px;

    }

    .gl-ltitle {
        display: block;
        float: left;
    }

    .gl-ts-txt {
        float: left;
        display: block;
        width: 20px;
        height: 20px;
    }

    .orders {
        width: auto;
        padding: 0 6px;
        height: 20px;
        font-size: 12px;
        color: #fff;
        text-indent: 0;
        font-weight: normal;
        background: rgba(24, 123, 236, 1);
        line-height: 20px;
        text-align: center;
        margin-left: 8px;
        border-radius: 4px;
    }

    .gl-abs {
        position: absolute;
        top: 10px;
        right: 12px;
        width: auto;
        padding: 3px 4px;
        font-size: 12px;
        text-indent: 0;
        font-weight: normal;
        text-align: center;
        box-sizing: border-box;
    }

    .gl-state {
        position: absolute;
        top: 40px;
        right: 12px;
        width: auto;
        padding: 3px 4px;
        font-size: 12px;
        text-indent: 0;
        font-weight: normal;
        text-align: center;
        box-sizing: border-box;
    }

    .qiangdan {
        color: rgba(24, 123, 236, 1);
        border: 1px solid rgba(24, 123, 236, 1);
    }

    .pai {
        color: rgba(254, 165, 20, 1);
        border: 1px solid rgba(254, 165, 20, 1);
    }
    .orderStatus {
        color: rgba(254, 165, 20, 1);
        border: 1px solid rgba(254, 165, 20, 1);
        margin-right: 39px;
    }
    .isBack{
        color: rgba(254, 165, 20, 1);
        border: 1px solid rgba(254, 165, 20, 1);
        margin-right: 143px;
    }

    .delay {
        position: absolute;
        top: 40px;
        right: 12px;
        width: auto;
        padding: 8px 10px;
        font-size: 12px;
        color: #ffffff;
        background: #1681FB;
        border-radius: 4px;
    }

    .gl-sublist {
        height: 18px;
        line-height: 18px;
        font-size: 14px;
        color: #AEAEAF;
        margin: 5px 12px;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
    }

    .gl-sublist-val {
        margin-left: 10px;
        color: #484848;
        font-size: 14px;
    }

    .gl-sublist-val-tel {
        margin-left: 10px;
        font-size: 14px;
        border: none;
        color: #007aff;
    }

    .gl-sublist a{text-decoration:none;}

    .gl-sublist-order {
        margin-left: 22px;
        color: #484848;
        font-size: 14px;
    }

    .gl-select-icon {
        display: block;
        width: 30px;
        height: 30px;
        position: absolute;
        bottom: -8px;
        right: -6px;
        background: #1680F9;
        border-radius: 50%;
    }

    .gl-list .gl-select-icon {
        display: none;
    }

    .gl-list.active .gl-select-icon {
        display: block;
    }

    .gl-list.active .gl-select-icon .gl-selefont {
        display: block;
        margin: 5px 0 0 6px;
        font-size: 14px;
        color: #fff;
    }

    .gl-tab-li {
        width: 50%;
        display: block;
        height: 39px;
        float: left;
        text-align: center;
        color: #333;
        line-height: 40px;
        border-bottom: 1px solid #EAEAEA;
    }

    .gl-tab-li.active {
        background: #fff;

        border-bottom: 2px solid #187BEC;
        box-sizing: border-box;
    }

    .gl-tab-li span {
        display: inline-block;
        height: 32px;
        border-bottom: 2px solid rgba(0, 0, 0, 0);
        position: relative;
    }

    .ml-bottom {
        height: 60px;
        background: #FFFFFF;
        box-shadow: 0 -4px 12px 0 rgba(184, 184, 184, 0.50);
        text-align: center;
        flex-shrink: 0;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
    }

    .btn-style-blue {
        height: 44px;
        background: rgba(22, 129, 251, 1);
        border-radius: 22px;
        text-align: center;
        line-height: 44px;
        font-size: 14px;
        letter-spacing: 4px;
        color: #fff;
        margin-left: 15px;
        margin-right: 15px;
        margin-top: 8px;
    }

    .gl-searchblock {
        margin-left: 15px;
        margin-right: 50px;
        position: relative;
        height: 30px;
        background: rgba(241, 241, 241, 1);
        border-radius: 15px;
        overflow: hidden;
    }

    .gl-searchinpt {
        height: 20px;
        line-height: 20px;
        margin: 6px 0;
        background: none;
        font-size: 12px;
        color: #555;
        border: none;
        width: 100%;
        text-indent: 25px;
        outline: none;
    }

    .gl-sst {
        display: block;
        width: 50px;
        height: 32px;
        line-height: 32px;
        position: absolute;
        right: 0;
        top: 0;
        color: #108EE9;
        font-size: 14px;
        text-align: center;
    }

    .gl-ssicon {
        width: 20px;
        height: 20px;
        display: block;
        position: absolute;
        top: 5px;
        left: 5px;
        background-size: 80% 80%;
        background-repeat: no-repeat;
        background-position: center;
    }

    .gl-ssclose {
        width: 20px;
        height: 20px;
        display: block;
        position: absolute;
        top: 5px;
        right: 5px;
        /*background: url(static/img/close.png) no-repeat center;*/
        background-size: 80% 80%;
        background-repeat: no-repeat;
        background-position: center;
    }

    .search-content {
        height: 32px;
        line-height: 32px;
        display: flex;
        justify-content: space-between;
        padding: 0 12px;
        background: #fff;
        border-top: 1px solid #EAEAEA;
        .total {
            font-size: 12px;
            font-weight: 400;
            color: rgba(187, 187, 187, 1);
        }
        .pai-count {
            font-size: 12px;
            font-weight: 400;
            color: #187BEC;
        }
        .search-choose {
            font-size: 0;
            .search-title {
                font-size: 12px;
                font-weight: 400;
                color: rgba(187, 187, 187, 1);
                display: inline-block;
            }
            .choose {
                display: inline-block;
                .choose-name {
                    font-size: 14px;
                    font-weight: 400;
                    color: rgba(22, 129, 251, 1);
                }
                .choose-icon {
                    color: #1681FB;
                    font-size: 12px;
                    margin-left: 6px;
                }
            }

            .go-report-jt {
                color: #1681FB;
                font-size: 16px;
                font-weight: 400;
                display: inline-block;
                transform: rotate(90deg);
            }
        }
    }
    .wgt{
        color: #1681FB;
        font-size: 14px;
        font-weight: 400;
    }
</style>
