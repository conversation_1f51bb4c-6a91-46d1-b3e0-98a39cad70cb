<!-- 集团建档 -->
<template>
    <div :class="{wrapper : tab == '1'}">
        <Header class="header" :tsTitleTxt="title"  backType="custom" @emGoPrev="goPrev"></Header>
        <!--预览集团证件图片，页面置灰-->
        <div class="picture-bottom" v-if="isShowPicture" @click="closePicture"></div>
        <div class="hide-tip" v-show="showDetailflag"><i class="iconfont a-bianzu143x"></i><span>隐藏区域是非必填，其他字段必填</span></div>

        <div class="flex-wrapper">
            <div class="order-tab">
				<span :class="{'active':tab=='1'}" @click="cTab('1')">资料补录</span>
				<span :class="{'active':tab=='2'}" @click="cTab('2')">历史补录</span>
			</div>
            <div class="ma-search wrapper-medias">
				<div class="gl-searchblock">
                    <div v-show="tab == '1'">
					    <!-- <span class="iconfont sousuo gl-ssicon" @click="search" ></span>
					    <input  type="text" placeholder="请输入服务号码或一网通账号" class="gl-searchinpt" v-model="telNum"/>
                                        <div class="ma-search">
                                            </div> -->
                    <div class="number-name" @click="showsChoose">{{zkType == '1' ? '服务号码' :'集团编号'}}<i class="iconfont shangzhankai-copy"></i></div>
                    <input  type="text" :placeholder="zkType == '1' ? '请输入服务号码' :  '请输入集团编号'" class="gm-searchinpt" v-model="telNum"/>
                    <span class="iconfont shanchu icon-delete" v-show="telNum" @click.stop="deles"></span>
					<span class="gl-search-btn" @click="search" >搜索</span>
				</div>

                    <div  v-show="tab == '2'">
                        <div class="number-name" @click="chooseQueryType">{{qrType == '1' ? '服务号码' : (qrType == '2' ? '集团编号' : '集团名称')}}<i class="iconfont shangzhankai-copy"></i></div>
                        <input  type="text" :placeholder="qrType == '1' ? '请输入服务号码或一网通账号' : (qrType == '2' ? '请输入集团编号' : '请输入集团名称')" class="gm-searchinpt" v-model="searchContent"/>
                    </div>
					<span class="gl-search-btn" @click="search">搜索</span>
				</div>

			</div>
        </div>
        <div>
            <div :class="['mainWrapper',{'no-commit' : dataSuppleStatus == '1'}]" v-show="showDetailflag">
        <!-- 预览集团证件图片 -->
        <div class="show-picture" v-if="isShowPicture" @click="closePicture">
            <img :src="imgRef"/>
            <span class="iconfont guanbi2"></span>
        </div>
            <!-- 集团证件信息 -->
            <div class="container">
                <div :class="{'no-edit': dataSuppleStatus == '1'}">
                <div class="section-title">
                    <div class="blue-line"></div>
                    <span class="son-title">集团证件信息</span>
                </div>
                <div class="box top-box">
                    <div class="txt">证件类型：</div>
                    <div class="item-choose" @click="chooseCertificateType">{{certificateType.label}}</div>
                    <span class="iconfont jiantou2 icon-sure" @click="chooseCertificateType"></span>
                    <div class="distinguish-btn" @click="scanCertInfo('1')">证件上传</div>
                    <span class="iconfont chakan icon-chakan" :class="{'icon-blue':isExistPicture}" @click="downPicture"></span>
                </div>
                <div class="box with-line">
                    <div class="txt">集团名称：</div>
                    <input class="group-input-content placeholder" type="text" v-model="certificateName" :placeholder="defeaultTip" @keyup="btKeyUp"
                    @keyup.enter.native="$event.target.blur" @change="judgeTrue('1')">
                </div>
                <div class='box top-box with-line' v-show='certificateType.label == "身份证" || shopContactPerson'>
                    <div class='txt'>商客联系人号码：</div>
                    <input class='group-input-content placeholder' type='text' v-model='shopContactPerson'
                           placeholder='请输入移动服务号码'>
                </div>
                <div class="box with-line">
                    <div class="txt">集团机构类型：</div>
                    <div class="item-choose" @click="chooseGroupOrgType">{{groupOrgType.label}}</div>
                    <span class="iconfont jiantou2 icon-sure" @click="chooseGroupOrgType"></span>
                </div>
                <div class="box with-line">
                    <div class="txt">行业类别大类：</div>
                    <div class="item-choose" @click="chooseCategoryBigType">{{categoryBigType.label}}</div>
                    <span class="iconfont jiantou2 icon-sure" @click="chooseCategoryBigType"></span>
                </div>
                <div class="box with-line">
                    <div class="txt">行业类别小类：</div>
                    <div class="item-choose" @click="chooseCategorySmallType">{{categorySmallType.label}}</div>
                    <span class="iconfont jiantou2 icon-sure" @click="chooseCategorySmallType"></span>
                </div>
                  <div class="box with-line">
                    <div class="txt">地市：</div>
                    <div class="item-choose">{{cityType.label}}</div>
                </div>
                <div class="box with-line">
                    <div class="txt">区县：</div>
                    <div class="item-choose" @click="chooseDistrictType">{{districtType.label}}</div>
                    <span class="iconfont jiantou2 icon-sure" @click="chooseDistrictType"></span>
                </div>
                <div class="box with-line">
                    <div class="txt">网格区县：</div>
                    <div class="item-choose" @click="chooseGridType">{{gridType.label}}</div>
                    <span class="iconfont jiantou2 icon-sure" @click="chooseGridType"></span>
                </div>
                <div class="box with-line">
                    <div class="txt">网格区域中心：</div>
                    <div class="item-choose" @click="chooseAreaType">{{areaType.label}}</div>
                    <span class="iconfont jiantou2 icon-sure" @click="chooseAreaType"></span>
                </div>
                <div class="box with-line">
                    <div class="txt">员工数：</div>
                    <input class="group-input-content" type="tel" v-model="peopleNum" placeholder="请输入">
                </div>
                <div class="box with-line">
                    <div class="txt">年营业额（万元）：</div>
                    <input class="group-input-content" type="tel" v-model="yearAmount" placeholder="请输入">
                </div>
                <div class="box with-line">
                    <div class="txt">集团联系电话：</div>
                    <input class="group-input-content" type="text" v-model="groupTel"  placeholder="请输入">
                </div>
                  <div class="box with-line">
                    <div class="txt">宽带地址：</div>
                    <div class="item-choose" @click="chooseBroadbandAddress">{{broadbandAddress.label}}</div>
                    <span class="iconfont jiantou2 icon-sure" @click="chooseBroadbandAddress"></span>
                </div>
                <div class="box with-line">
                    <div class="txt">登记管理部门：</div>
                    <div class="item-choose" @click="chooseRegManage">{{regManaDepartment.label}}</div>
                    <span class="iconfont jiantou2 icon-sure" @click="chooseRegManage"></span>
                </div>
                <div class="box with-line">
                    <div class="txt">企业类型：</div>
                    <div class="item-choose" @click="chooseEntType">{{entType.label}}</div>
                    <span class="iconfont jiantou2 icon-sure" @click="chooseEntType"></span>
                </div>
                <div class="box with-line">
                    <div class="txt">生产经营所在省：</div>
                    <div class="item-choose" @click="chooseRegisteredProvince">{{registeredProvince.label}}</div>
                    <span class="iconfont jiantou2 icon-sure" @click="chooseRegisteredProvince"></span>
                </div>
                <div class="box with-line">
                    <div class="txt">生产经营所在地市：</div>
                    <div class="item-choose" @click="chooseRegisteredCity">{{registeredCity.label}}</div>
                    <span class="iconfont jiantou2 icon-sure" @click="chooseRegisteredCity"></span>
                </div>
                <div class="box with-line">
                    <div class="txt">生产经营所在区县：</div>
                    <div class="item-choose" @click="chooseRegisteredCountry">{{registeredCountry.label}}</div>
                    <span class="iconfont jiantou2 icon-sure" @click="chooseRegisteredCountry"></span>
                </div>
                <div class="box with-line">
                  <div class="txt">场所类别：</div>
                  <div class="item-choose" @click="chooseScenarioType">{{clusteringScenarioType.label}}</div>
                  <span class="iconfont jiantou2 icon-sure" @click="chooseScenarioType"></span>
                </div>
                <div class="box with-line" v-show="this.isScenarioDetail()">
                  <div class="txt">聚类细分场景：</div>
                  <div class="item-choose" @click="chooseScenarioDetailType">{{clusteringScenarioDetailType.label}}</div>
                  <span class="iconfont jiantou2 icon-sure" @click="chooseScenarioDetailType"></span>
                </div>
                <div class='box with-line'>
                  <div class='txt'>细分客群类别：</div>
                  <div class='item-choose' @click='chooseBusCustSubType'>{{ busCustSub.label }}</div>
                  <span class='iconfont jiantou2 icon-sure' @click='chooseBusCustSubType'></span>
                </div>
                <!-- v-show='busCustSub.id === "05"' 2025/07/15开放自定义字段全局显示 -->
                <div class="box with-line">
                  <div class='txt'>特色客群（自定义）：</div>
                  <input class="group-input-content" type="text" v-model="busCustSubDefine" placeholder="请输入" v-show='allowModifyDefine'>
                  <input class="group-input-content" type="text" v-model="busCustSubDefine" placeholder="请输入" v-show='!allowModifyDefine' readonly>
                </div>
                <DynamicAttrTemp :attrList="dynamicFieldModule" :hideFlag="true" :isHide='true' type='1'></DynamicAttrTemp>

                <!-- 默认隐藏区域 start-->
                <!-- <div class="box with-line" v-show="certificateFlag">
                    <div class="txt">生产经营具体地址：</div>
                    <textarea class="installUl-textarea" rows="2" v-model="actAddr" :placeholder="defeaultTip"></textarea>
                    <div class="location-btn" @click="locationConfirm()">自动定位</div>
                </div> -->
                <div class="box with-line" v-show="certificateFlag">
                    <div class="txt">办公地址：</div>
                    <textarea class="installUl-textarea" rows="2" v-model="location" placeholder="自动获取"></textarea>
                    <div class="location-btn" @click="locationConfirm()">自动定位</div>
                </div>
                <div class="box with-line" v-show="certificateFlag">
                    <div class="txt">办公地址经度：</div>
                    <input class="group-input-content" type="text" v-model="longitude" placeholder="自动获取"  readonly="true">
                </div>
                <div class="box with-line" v-show="certificateFlag">
                    <div class="txt">办公地址纬度：</div>
                    <input class="group-input-content" type="text" v-model="latitude" placeholder="自动获取"  readonly="true">
                </div>
                <div class="box with-line" v-show="certificateFlag">
                    <div class="txt">证件号码：</div>
                    <input class="group-input-content placeholder" type="text"  v-model="certificateNo" :placeholder="getGroupCertId(showGroupCertId) || '输入或OCR自动回显'"
                    @keyup.enter.native="$event.target.blur" @change="judgeTrue()">
                </div>
                <div class="box with-line"  v-show="certificateFlag">
                    <div class="txt">证件地址：</div>
                    <input class="group-input-content placeholder" type="text" v-model="certificateAddress" :placeholder="defeaultTip">
                </div>
                <div class="box with-line" v-show="certificateFlag">
                    <div class="txt">法人：</div>
                    <input class="group-input-content placeholder" type="text" v-model="legalPerson" :placeholder="defeaultTip"
                    @keyup.enter.native="$event.target.blur" @change="judgeTrue()">
                </div>
                <div class="box with-line" v-show="certificateFlag">
                    <div class="txt">集团类型：</div>
                    <div class="item-choose">{{groupType.label}}</div>
                    <span class="iconfont jiantou2 icon-sure" @click="chooseGroupType"></span>
                </div>
                <div class="box with-line" v-show="certificateFlag">
                     <div class="txt">行业类别3：</div>
                    <div class="item-choose" @click="chooseThirdIndustry">{{industryThird.label}}</div>
                    <span class="iconfont jiantou2 icon-sure" @click="chooseThirdIndustry"></span>
                </div>
                <div class="box with-line" v-show="certificateFlag">
                    <div class="txt">行业类别4：</div>
                    <div class="item-choose" @click="chooseFourthIndustry">{{industryFourth.label}}</div>
                    <span class="iconfont jiantou2 icon-sure" @click="chooseFourthIndustry"></span>
                </div>
                <div class="box with-line" v-show="certificateFlag">
                    <div class="txt">是否战略客户：</div>
                    <div class="item-choose" @click="chooseSVipType">{{sVipType.label}}</div>
                    <span class="iconfont jiantou2 icon-sure" @click="chooseSVipType"></span>
                </div>
                <div class="box with-line" v-show="certificateFlag">
                    <div class="txt">集团机构级别：</div>
                    <div class="item-choose" @click="chooseGroupLevelType">{{groupLevelType.label}}</div>
                    <span class="iconfont jiantou2 icon-sure" @click="chooseGroupLevelType"></span>
                </div>
                <div class="box with-line" v-show="certificateFlag">
                    <div class="txt">客户发展渠道：</div>
                    <div class="item-choose" @click="chooseDepWayType">{{depWayType.label}}</div>
                    <span class="iconfont jiantou2 icon-sure" @click="chooseDepWayType"></span>
                </div>
                <div class="box with-line" v-show="certificateFlag">
                    <div class="txt">客户发展工号：</div>
                    <input class="group-input-content" type="text" v-model="depNumber" placeholder="请输入">
                </div>
                <div class="box with-line" v-show="certificateFlag">
                    <div class="txt">主动服务渠道：</div>
                    <div class="item-choose" @click="chooseServiceWayType">{{serviceWayType.label}}</div>
                    <span class="iconfont jiantou2 icon-sure" @click="chooseServiceWayType"></span>
                </div>
                <div class="box with-line" v-show="certificateFlag">
                    <div class="txt">主动服务工号：</div>
                    <input class="group-input-content" type="text" v-model="serviceNumber" placeholder="请输入">
                </div>

                <DynamicAttrTemp :attrList="dynamicFieldModule" :hideFlag="certificateFlag" :isHide='true' type='1'></DynamicAttrTemp>

                <div class="box" v-show="certificateFlag">
                    <span class="txt">外围渠道受理：</span>
                </div>
                <ul class="use-per-check-list" v-show="certificateFlag">
                    <li :key="idx"  v-for="(item,idx) in channelList" v-show="channelList && channelList.length > 0">
                        <label>
                            <input type="checkbox" name="item" :value="item.id" v-model="channelVal" />
                            {{item.label}}
                        </label>
                    </li>
                </ul>
                <div class="box with-line" v-show="certificateFlag">
                    <div class="txt">备注：</div>
                    <textarea class="group-input-content li-textarea" v-model="remark" type="text" wrap="virtual" placeholder="请输入"></textarea>
                </div>
                <!-- end -->
                </div>
                <div class="box last-box">
                    <span class="more-txt" v-show="!certificateFlag" @click="certificateFlag = !certificateFlag">查看更多</span>
                    <span class="iconfont icon-more" :class="{ 'jiantou2': !certificateFlag, 'jiantouxiangshang': certificateFlag }"  @click="certificateFlag = !certificateFlag"></span>
                </div>
            </div>

            <!-- 集团关联人信息 -->
            <div class="container">
                <div :class="{'no-edit': dataSuppleStatus == '1'}">
                <div class="section-title">
                    <div class="blue-line"></div>
                    <span class="son-title">集团关联人信息</span>
                </div>
                <div class="box with-line">
                    <div class="txt">姓名：</div>
                    <input class="group-input-content placeholder" type="text" v-model="idCardName" :placeholder="formatName(showIdCardName) || '输入或OCR自动回显'" />
                </div>
                <div class="box with-line">
                    <div class="txt">服务号码：</div>
                    <input class="group-input-content" type="text" v-model="linkerNumber" placeholder="必填">
                </div>
                <div class="box with-line">
                    <div class="txt">服务角色：</div>
                    <div class="item-choose" @click="chooseServiceRole">{{serviceRoleType.label}}</div>
                    <span class="iconfont jiantou2 icon-sure" @click="chooseServiceRole"></span>
                </div>
                <div class="box top-box" >
                    <div class="txt">证件类型：</div>
                    <div class="item-choose" @click="chooseIdCardType">{{idCardType.label}}</div>
                    <span class="iconfont jiantou2 icon-sure" @click="chooseIdCardType"></span>
                    <div class="distinguish-btn" @click="scanCertInfo('2')">证件扫描识别</div>
                </div>
                 <div class="box with-line" v-show="idCardFlag">
                    <div class="txt">证件号码：</div>
                    <input class="group-input-content placeholder" type="text" v-model="showIdCardNo" placeholder="输入或OCR自动回显">
                </div>
                <!-- <div class="box with-line" v-show="idCardFlag">
                    <div class="txt">证件地址：</div>
                    <input class="group-input-content" type="text" v-model="idCardAddress" placeholder="自动获取">
                </div> -->
                <div class="trust-camera" @click="openCamera()" v-show="idCardFlag && ((idCardName && idCardName != legalPerson) || (!idCardName && showIdCardName && showIdCardName != legalPerson)) ">
                    <img class="camera-img" :src="groupPicObj.src" v-show="groupPicObj.has"/>
                    <img src="static/panImg/pai-bg.png" class="camera-img" v-show="!groupPicObj.has">
                    <div class="camera-btn" v-show="!groupPicObj.has">
                        <i class="iconfont xiangji"></i>
                        <span class="camera-text" >拍摄委托书</span>
                    </div>
				</div>
                </div>
                <div class="box last-box">
                    <span class="more-txt" v-show="!idCardFlag" @click="idCardFlag = !idCardFlag">查看更多</span>
                    <span class="iconfont icon-more" :class="{ 'jiantou2': !idCardFlag, 'jiantouxiangshang': idCardFlag }"  @click="idCardFlag = !idCardFlag"></span>
                </div>
            </div>

            <!-- 其他可选信息 -->
            <div class="container">
                <div class="section-title">
                    <div class="blue-line"></div>
                    <div class="son-title">其他可选信息</div>
                    <div class="iconfont icon-detail" :class="{ 'jiantou2': !otherInfoFlag, 'jiantouxiangshang': otherInfoFlag }"  @click="otherInfoFlag = !otherInfoFlag"></div>
                </div>
                <div :class="{'no-edit': dataSuppleStatus == '1'}">
                <div class="box with-line" v-show="otherInfoFlag">
                    <div class="txt">集团规模：</div>
                    <div class="item-choose" @click="chooseGroupSizeType">{{groupSize.label}}</div>
                    <span class="iconfont jiantou2 icon-sure" @click="chooseGroupSizeType"></span>
                </div>
                <div class="box with-line" v-show="otherInfoFlag">
                    <div class="txt">客户等级：</div>
                    <div class="item-choose" @click="chooseCutsomLevelType">{{cutsomLevelType.label}}</div>
                    <span class="iconfont jiantou2 icon-sure" @click="chooseCutsomLevelType"></span>
                </div>
                <div class="box with-line" v-show="otherInfoFlag">
                    <div class="txt">客户信用等级：</div>
                    <div class="item-choose" @click="chooseCreditLevelType">{{creditLevel.label}}</div>
                    <span class="iconfont jiantou2 icon-sure" @click="chooseCreditLevelType"></span>
                </div>
                <div class="box with-line" v-show="otherInfoFlag">
                    <div class="txt">服务等级：</div>
                    <div class="item-choose" @click="chooseServiceLevelType">{{serviceLevelType.label}}</div>
                    <span class="iconfont jiantou2 icon-sure" @click="chooseServiceLevelType"></span>
                </div>
                <div class="box with-line" v-show="otherInfoFlag">
                    <div class="txt">电子邮箱：</div>
                    <input class="group-input-content" type="text" v-model="email" placeholder="选填">
                </div>
                <div class="box with-line" v-show="otherInfoFlag">
                    <div class="txt">办公邮编：</div>
                    <input class="group-input-content" type="tel" v-model="postCode"  placeholder="必填">
                </div>
                <div class="box with-line" v-show="otherInfoFlag">
                    <div class="txt">集团服务级别：</div>
                    <div class="item-choose" @click="chooseGroupServiceLevelType">{{groupServiceLevelType.label}}</div>
                    <span class="iconfont jiantou2 icon-sure" @click="chooseGroupServiceLevelType"></span>
                </div>
                <div class="box with-line" v-show="otherInfoFlag">
                    <div class="txt">是否直管客户：</div>
                    <div class="item-choose" @click="chooseIsUpCustomType">{{isUpCustomType.label}}</div>
                    <span class="iconfont jiantou2 icon-sure" @click="chooseIsUpCustomType"></span>
                </div>
                <div class="box with-line" v-show="otherInfoFlag">
                    <div class="txt">是否省管客户：</div>
                    <div class="item-choose" @click="chooseIsProCustomType">{{isProCustomType.label}}</div>
                    <span class="iconfont jiantou2 icon-sure" @click="chooseIsProCustomType"></span>
                </div>
                <div class="box with-line" v-show="otherInfoFlag">
                    <div class="txt">SLA信息：</div>
                    <div class="item-choose" @click="chooseSlaInfoType">{{slaInfoType.label}}</div>
                    <span class="iconfont jiantou2 icon-sure" @click="chooseSlaInfoType"></span>
                </div>
                <div class="box with-line" v-show="otherInfoFlag">
                    <div class="txt">集团客户级别（2016年）：</div>
                    <div class="item-choose" @click="chooseGroupCutsomLevel">{{groupCutsomLevel.label}}</div>
                    <span class="iconfont jiantou2 icon-sure" @click="chooseGroupCutsomLevel"></span>
                </div>
                <div class="box with-line" v-show="otherInfoFlag">
                    <div class="txt">集团客户专线是否可以停机：</div>
                    <div class="item-choose" @click="chooseIsStopPhoneType">{{isStopPhoneType.label}}</div>
                    <span class="iconfont jiantou2 icon-sure" @click="chooseIsStopPhoneType"></span>
                </div>
                <div class="box with-line" v-show="otherInfoFlag">
                    <div class="txt">是否发送充值提醒短信：</div>
                    <div class="item-choose" @click="chooseIsShortMessType">{{isShortMessType.label}}</div>
                    <span class="iconfont jiantou2 icon-sure" @click="chooseIsShortMessType"></span>
                </div>
                <DynamicAttrTemp :attrList="dynamicFieldModule" :hideFlag="otherInfoFlag" :isHide='true' type='3' ></DynamicAttrTemp>
                </div>
            </div>
            <NlButton enableTip="提 交" @click="doSubmit" :defaultState="isClick" v-show="dataSuppleStatus != '1'"></NlButton>
        </div>
            <NoDataPage class="no-data" v-show="!showDetailflag && !showListFlag" tipTxt="请输入服务号码或者集团编号"></NoDataPage>
        </div>

        <!-- 创建虚拟集团的加载弹框 -->
        <LoadingMessageBox
            ref="createGroupDialog"
            title="温馨提示"
            message="是否要创建虚拟集团?"
            :showCancelButton="true"
            :showConfirmButton="true"
            confirmButtonText="创建"
            cancelButtonText="取消"
            loadingText="创建中..."
            @action="handleCreateGroupAction"
        />

        <div v-show="showListFlag">
            <ul class="order-ul" v-show="historyDataList.length > 0">
				<li class="order-li" v-for="(item,index) in historyDataList" :key="index" @click="chooseOne(item)">
					<div :class="height-auto">
						<div class="top-content">
							<span class="tel">{{item.telNumber}}</span>
							<span class="status" :class="[item.status == '0' ? 'unpend' : (item.status == '1' ? 'success' : 'fail')]">{{item.status == '0' ? '未审核' : (item.status == '1' ? '已通过' : '不通过')}}</span>
						</div>
						<div class="top-content">
							<span class="c-item">集团编号：<i>{{item.groupNumber}}</i></span>
							<span class="c-right">{{item.createTime}}</span>
						</div>
						<div class="top-content">
							<span class="c-item">集团名称：<i>{{item.groupName}}</i></span>
						</div>
                        <div class="top-content" v-show="item.status == '2' && item.failReason != null && item.failReason != ''">
							<span class="c-item">不通过原因：</span><span class="reason-item">{{item.failReason}}</span>
						</div>
					</div>
                    <i class="iconfont jiantou" :class="{'lower-jiantou' : item.status == '2' && item.failReason != null && item.failReason != ''}"></i>
				</li>
			</ul>
			<NoDataPage class="no-data" v-show="historyDataList.length < 1" tipTxt="暂无订单记录"></NoDataPage>
        </div>
    </div>
</template>

<script>
import Header from "components/common/Header.vue";
import NlButton from 'components/common/NlButton';
import NlDropdown from "components/common/NlDropdown/dropdown.js";
import Storage from '@/base/storage';
import ClientJs from '@/base/clientjs';
import {Indicator} from 'mint-ui';
import NoDataPage from 'components/common/NoDataPage';
import DynamicAttrTemp from "components/business/groupUserChange/DynamicAttrTemp.vue"
import LoadingMessageBox from 'components/common/LoadingMessageBox.vue';
import {attrChange} from "./comm/optionValueAdapter";
import { commonBuiValid } from './groupCommCfg.js'


export default {
    components: { Header, NlButton, NlDropdown, NoDataPage, DynamicAttrTemp, LoadingMessageBox },
    mixins: [commonBuiValid],
    data() {
        return {
            title: '集团资料补录', //标题
            uinfo: {}, //用户信息
            defeaultTip: '输入或OCR自动回显',
            judgeTypeList: [], //判断list
            certificateType:{  //集团证件类型选中的对象
                id:'-99',
                label:'请选择'
            },
            certificateTypeList:[], //集团证件类型可选列表
            certificateName: '', //集团证件名称
            certificateNo: '', //集团证件号码
            certificateAddress: '', //集团证件地址
            legalPerson: '', //法人
            groupOrgType:{  //集团机构类型选中的对象
                id:'-99',
                label:'请选择'
            },
            groupOrgTypeList: [], //集团机构类型可选列表
            categoryBigType:{  //行业类别大类选中的对象
                id:'-99',
                label:'请选择',
                son:[]
            },
            categoryBigTypeList: [], //行业类别大类可选列表
            categorySmallType:{  //行业类别小类选中的对象
                id:'-99',
                label:'请选择'
            },
            categorySmallTypeList: [], //行业类别小类可选列表
            certificateFlag: false,  //默认关闭集团证件下拉框--------- 第一部分
            longitude: '', //经度
            latitude: '', //纬度
            cityType:{  //地市选中的对象
                id:'-99',
                label:'请选择'
            },
            cityList: [], //地市可选列表
            districtType:{  //县区选中的对象
                id:'-99',
                label:'请选择'
            },
            districtTypeList: [], //县区可选列表
            gridType:{  //网格区县选中的对象
                id:'-99',
                label:'请选择',
                son:[]
            },
            gridTypeList: [], //网格区县可选列表
            areaType:{  //网格区域中心选中的对象
                id:'-99',
                label:'请选择'
            },
            areaTypeList: [], //网格区域中心可选列表
            groupType:{  //集团类型选中的对象
                id:'-99',
                label:'请选择'
            },
            groupTypeList: [], //集团类型可选列表
            sVipType:{  //战略客户选中的对象
                id:'-99',
                label:'请选择'
            },
            groupLevelType:{  //集团机构级别选中的对象
                id:'-99',
                label:'请选择'
            },
            groupLevelTypeList: [], //集团机构级别可选列表
            depWayType:{  //客户发展渠道选中的对象
                id:'-99',
                label:'请选择'
            },
            depWayTypeList: [], //客户发展渠道可选列表
            depNumber: '', //客户发展工号
            serviceWayType:{  //主动服务渠道选中的对象
                id:'-99',
                label:'请选择'
            },
            servicepWayTypeList: [], //主动服务渠道可选列表
            serviceNumber: '', //主动服务工号
            channelVal:[], //外围渠道受理
            channelList:[], //查询出来的渠道的值
            idCardFlag: false,  //默认关闭集团证件下拉框--------- 第二部分
            idCardType:{  //关联人类型选中的对象
                id:'-99',
                label:'请选择'
            },
            idCardTypeList:[
                {
                    id:'idCard',
                    label:'居民身份证'
                }
            ], //关联人类型可选列表
            idCardName: '', //关联人证件姓名
            idCardNo: '', //关联人证件号码
            idCardAddress: '', //关联人证件地址
            serviceRoleType:{  //服务角色选中的对象
                id:'-99',
                label:'请选择'
            },
            servicepRoleTypeList: [], //服务角色可选列表
            linkerNumber: '',//服务号码
            groupPicObj: {//委托书
                src: '',
                has: false,
                name: ''
            },
            otherInfoFlag: false, //默认关闭其他可选信息下拉框--------- 第三部分
            groupSize:{   //集团规模选中的对象
                id:'3',
                label:'小微型'
            },
            groupSizeList:[], //集团规模可选列表
            peopleNum:'',  //员工数
            cutsomLevelType:{ //客户等级
              id:'-99',
              label:'请选择'
            },
            cutsomLevelTypeList: [], //客户等级可选列表
            serviceLevelType:{  //服务等级选中的对象
                id:'4',
                label:'铜牌级'
            },
            serviceLevelTypeList: [], //服务等级可选列表
            email: '',//电子邮箱
            yearAmount:'1', //年营业额（万元）
            postCode: '000000', //办公邮编
            groupTel: '', //集团联系电话
            groupServiceLevelType:{  //集团服务级别选中的对象
                id:'1',
                label:'普通'
            },
            groupServiceLevelTypeList: [], //集团服务级别可选列表
            isUpCustomType:{  //是否直管客户选中的对象
                id:'-99',
                label:'请选择'
            },
            isProCustomType:{  //是否省管客户选中的对象
                id:'-99',
                label:'请选择'
            },
            slaInfoType:{  //SLA信息选中的对象
                id:'0',
                label:'普通级'
            },
            slaInfoTypeList: [], //SLA信息可选列表
            groupCutsomLevel: { //集团客户级别（2016年）选中的对象
                id:'D',
                label:'D'
            },
            groupCutsomLevelList: [], ////集团客户级别（2016年）可选列表
            isStopPhoneType:{  //集团客户专线是否可以停机选中的对象
                id:'-99',
                label:'请选择'
            },
            isStopPhoneList: [], //集团客户专线是否可以停机可选列表
            isShortMessType:{  //集团客户专线是否发送充值提醒选中的对象
                id:'-99',
                label:'请选择'
            },
            certImg: '', //集团证件照片文件名称
            idcardImg: '', //身份证照片文件名称
            authImg: '', //授权委托书照片文件名称,
            verificIdentific: '', //判断企业证照验真假
            chkGrpSocial: '', //统一社会信用代码校验
            creditLevelList: [],
            creditLevel: '',
            isDoubleAdress: '',//是否双地址集团，一网通带出
            telNum: '',//需要查询虚拟集团的手机号码
            groupId: '',
            remark: '', //备注
            tab: '',
            showDetailflag: false,
            showListFlag: false,
            type:'',
            historyDataList: '',
            grpBaseInfo: '',
            grpDevChannel: '',
            grpExpandInfo: '',
            grpOrgLevel: '',
            grpRelPerson: '',
            grpRelation: '',
            dataSuppleStatus: '',
            isClick: 'enable',
            procInstId: '',//未审核状态补资料的工单编号
            isAddTable: 'true',//是否允许补资料入库
            qrType: '2',//查询历史补录筛选条件（1：服务号码；2：集团编码，3：集团编号）
            qrTypeList: [{id:'2',label:'集团编号'},{id:'1',label:"服务号码"},{id:'3',label:'集团名称'}],
            searchContent: '',//历史补录查询条件
            isShowPicture: false,//是否显示图片
            imgRef: '',//集团证件图片
            isExistPicture: false,//是否有集团证件图片
            broadbandAddressList: [],//个人和企业宽带地址列表
            broadbandAddress: {id:'-99',label:'请选择'},//宽带地址，默认为企业宽带地址
            regManaDepartment: {id:'-99',label:'请选择'}, //登记管理部门
            regManaDepartmentList: [], //登记管理部门列表
            registeredProvince: {id:'320000',label:'江苏省'}, //注册省份
            registeredProvinceList: [{id:'320000',label:'江苏省'}], //省份列表，目前只支持江苏
            registeredCity: {id:'-99',label:'请选择'}, //注册地市
            registeredCityList: [], //地市列表
            registeredCountry: {id:'-99',label:'请选择'}, //注册区县
            registeredCountryList: [],  //区县列表
            actAddr: '', //实际地址
            entType: {id:'-99',label:'请选择'}, //企业类型
            entTypeList: [], //企业类型列表
            dynamicFieldModule: [],//各模块动态配置字段,集团证件信息
            zkType:'1',//
            zkTypeList:[{id:'1',label:'服务号码'},{id:'2',label:'集团编号'}],
            industryThird:{id:'',label:'请选择'},//行业类别3
            industryFourth:{id:'',label:'请选择'},//行业类别4
            industryThirdList:[],//行业类别3列表
            industryFourthList:[],//行业类别4列表
            industryTemp:{'isParse': false,'vocaionkind3':'','vocaionkind4':''},
            groupFilingType:"MOD",//集团建档类型  MOD-一网通建档 ADD-阿拉盯建档
            groupModifyDateStr:'', //可以修改的配置时间
            groupCrateTime:'', //集团建档时间
            busCustSubList: [], //细分客群类别列表
            busCustSub: { id: '', label: '请选择' }, //细分客群类别
            busCustSubDefine: '', //特色客群（自定义）
            clusteringScenarioList: [],//场景大类可选列表
            clusteringScenarioType: {
              id: '-99',
              label: '请选择',
              son: []
            },
            clusteringScenarioDetailList: [],//场景子类可选列表
            clusteringScenarioDetailType: {
              id: '-99',
              label: '请选择'
            },
            shopContactPerson: '',
            showIdCardNo: '',//作为本次展现的，原来查询出来的不展示
            showIdCardName: '',//作为本次展现的，原本查询出来的带星展示
            showGroupCertId:'',////作为本次展现的，原本查询出来的带星展示
            isCreatingVirtualGroup: false, // 创建虚拟集团的加载状态
        };
    },
    methods: {
        //上一步
        goPrev(){
            history.go(-1);
        },
        //证件扫描函数
        scanCertInfo(val){
            if(val == '1' && this.certificateType.id == '-99'){
                this.$alert('请选择集团证件信息的证件类型');
                return;
            }

            if(val == '2' && this.idCardType.id == '-99'){
                this.$alert('请选择集团关联人信息的证件类型');
                return;
            }
            this.pzType = val;
            ClientJs.openCameraAndShow("1", "certPaizhaoCb");
        },
        //选择集团证件类型
        chooseCertificateType(){
            var self = this;
            NlDropdown({
                confirmBtn: false,
                datalist: self.certificateTypeList
            }, function (res) {
                self.certificateType = res;
            });
        },
        //选择集团机构类型
        chooseGroupOrgType(){
            var self = this;
            NlDropdown({
                confirmBtn: false,
                datalist: self.groupOrgTypeList
            }, function (res) {
                self.groupOrgType = res;
            });
        },
        //选择行业类别大类
        chooseCategoryBigType(){
            var self = this;
            NlDropdown({
                confirmBtn: false,
                datalist: self.categoryBigTypeList
            }, function (res) {
                self.categoryBigType = res;
            });
        },
        //选择行业类别小类
        chooseCategorySmallType(){
            var self = this;
            NlDropdown({
                confirmBtn: false,
                datalist: self.categorySmallTypeList
            }, function (res) {
                self.categorySmallType = res;
            });
        },
        chooseFourthIndustry(){
        var self = this;
        NlDropdown({
          confirmBtn: false,
          datalist: self.industryFourthList
        }, function (res) {
          self.industryFourth = res;
        });
      },
      chooseThirdIndustry(){
        let self = this;
        NlDropdown({
          confirmBtn: false,
          datalist: self.industryThirdList
        }, function (res) {
          self.industryThird = res;
        });
      },
        chooseRegisteredProvince(){
            var self = this;
            NlDropdown({
                confirmBtn: false,
                datalist: self.registeredProvinceList
            }, function (res) {
                self.registeredProvince = res;
            });
        },
        chooseRegisteredCity(){
            var self = this;
            NlDropdown({
                confirmBtn: false,
                datalist: self.registeredCityList
            }, function (res) {
                self.registeredCity = res;
            });
        },
        chooseRegisteredCountry(){
            var self = this;
            NlDropdown({
                confirmBtn: false,
                datalist: self.registeredCountryList
            }, function (res) {
                self.registeredCountry = res;
            });
        },
        chooseBroadbandAddress(){
            var self = this;
            NlDropdown({
                confirmBtn: false,
                datalist: self.broadbandAddressList
            }, function (res) {
                self.broadbandAddress = res;
            });
        },
        //选择地市类型
        chooseCityType(){
            var self = this;
            let oldValue = self.cityType;
            NlDropdown({
                confirmBtn: false,
                datalist: self.cityList
            }, function (res) {
                self.cityType = res;
                if(oldValue != res){
                    self.districtType = { id:'-99',label:'请选择'};
                    self.gridType = { id:'-99',label:'请选择'};
                    self.areaType = { id:'-99',label:'请选择'};
                    self.qrychildGridList("", "1");
                }
            });
        },
        //选择县区类型
        chooseDistrictType(){
            var self = this;
            NlDropdown({
                confirmBtn: false,
                datalist: self.districtTypeList
            }, function (res) {
                self.districtType = res;
            });
        },
        //选择网格区县类型
        chooseGridType(){
            var self = this;
            let oldValue = self.gridType;
            NlDropdown({
                confirmBtn: false,
                datalist: self.gridTypeList
            }, function (res) {
                self.gridType = res;
                if(oldValue != res){
                    self.areaType = { id:'-99',label:'请选择'};
                    self.qrychildGridList(self.gridType.id, "3");
                }
            });
        },
        //选择网格区域中心类型
        chooseAreaType(){
            var self = this;
            NlDropdown({
                confirmBtn: false,
                datalist: self.areaTypeList
            }, function (res) {
                self.areaType = res;
            });
        },
        //选择登记管理部门
        chooseRegManage(){
            var self = this;
            NlDropdown({
                confirmBtn: false,
                datalist: self.regManaDepartmentList
            }, function (res) {
                self.regManaDepartment = res;
            });
        },
        chooseEntType(){
            var self = this;
            NlDropdown({
                confirmBtn: false,
                datalist: self.entTypeList
            }, function (res) {
                self.entType = res;
            });
        },
        //选择集团类型
        chooseGroupType(){
            var self = this;
          //根据条件判断，当前类型是否能够修改（1.满足后台配置天数内的条件，网格普通集团(LocationType04)、区县战略集团(LocationType08)、市战略集团(LocationType10)、省战略集团(LocationType11)这4类集团可以互相变更
          // 2.区县统一运营集团(LocationType07)、市统一运营集团(LocationType09)这两个集团可以相互修改 不受限制
          let groupTypeListNew=[];
          if("LocationType07" == self.groupType.id||"LocationType09" == self.groupType.id){
            groupTypeListNew = self.groupTypeList.filter(item => item.id == "LocationType07" || item.id == "LocationType09");
          }else{
            //1.判断新建集团是否在60天以内
            if(self.groupCrateTime-self.groupModifyDateStr>0){
              //在60天内
              groupTypeListNew = self.groupTypeList.filter(item => item.id == "LocationType04" || item.id == "LocationType10" || item.id == "LocationType11" || item.id == "LocationType08");
            }else{
              this.$alert('当前集团创建超过后台配置天数集团类型不支持修改');
              return;
            }
          }
          NlDropdown({
                confirmBtn: false,
                datalist: groupTypeListNew
            }, function (res) {
                self.groupType = res;
            });
        },
        //选择战略客户类型
        chooseSVipType(){
            var self = this;
            NlDropdown({
                confirmBtn: false,
                datalist: self.judgeTypeList
            }, function (res) {
                self.sVipType = res;
            });
        },
        //选择集团机构级别
        chooseGroupLevelType(){
            var self = this;
            NlDropdown({
                confirmBtn: false,
                datalist: self.groupLevelTypeList
            }, function (res) {
                self.groupLevelType = res;
            });
        },
        //选择场景类型
        chooseScenarioType() {
          var self = this
          this.clusteringScenarioDetailType = { id: '-99', label: '请选择' }
          NlDropdown({
            confirmBtn: false,
            datalist: self.clusteringScenarioList
          }, function(res) {
            self.clusteringScenarioType = res
          })
        },
        //选择行业类别小类
        chooseScenarioDetailType() {
          var self = this
          NlDropdown({
            confirmBtn: false,
            datalist: self.clusteringScenarioDetailList
          }, function(res) {
            self.clusteringScenarioDetailType = res
          })
        },
        //选择客户发展渠道
        chooseDepWayType(){
            var self = this;
            NlDropdown({
                confirmBtn: false,
                datalist: self.depWayTypeList
            }, function (res) {
                self.depWayType = res;
            });
        },
        //选择主动服务渠道
        chooseServiceWayType(){
            var self = this;
            NlDropdown({
                confirmBtn: false,
                datalist: self.servicepWayTypeList
            }, function (res) {
                self.serviceWayType = res;
            });
        },
        //选择身份证类型
        chooseIdCardType(){
            var self = this;
            NlDropdown({
                confirmBtn: false,
                datalist: self.idCardTypeList
            }, function (res) {
                self.idCardType = res;
            });
        },
        //选择服务角色
        chooseServiceRole(){
            var self = this;
            NlDropdown({
                confirmBtn: false,
                datalist: self.servicepRoleTypeList
            }, function (res) {
                self.serviceRoleType = res;
            });
        },
        //二次确认打卡
        locationConfirm() {
            Indicator.open("正在获取定位");
            ClientJs.getLocation('', "getAutoLocation");
        },
        //拍照委托书
        openCamera() {
        	ClientJs.openCameraAndShow("1", "authPaizhaoCb");
        },
        //上传委托书、身份证
        uploadImg(param) {
            let url = '/xsb/personBusiness/groupArchives/h5upLoadImg';
            this.$http.post(url,param).then(res => {
                let {retCode,retMsg,data} = res.data;
                if(retCode == '0'){
                    //拍摄的是委托书的时候 需要回显
                    if(param.type == "03"){
                        this.groupPicObj.name = data;
                        this.groupPicObj.has = true;
                        this.authImg = data;
                    }else if(param.type == "02"){
                        this.idcardImg = data;
                    }else if(param.type == "01"){
                        this.certImg = data;
                    }
                } else {
                    this.$alert(retMsg||'上传照片失败请重新拍照');
                }
            })
        },
        //选择集团规模
        chooseGroupSizeType(){
            var self = this;
            NlDropdown({
                confirmBtn: false,
                datalist: self.groupSizeList
            }, function (res) {
                self.groupSize = res;
            });
        },
        //选择客户等级
        chooseCutsomLevelType(){
            var self = this;
            NlDropdown({
                confirmBtn: false,
                datalist: self.cutsomLevelTypeList
            }, function (res) {
                self.cutsomLevelType = res;
            });
        },
        //选择客户信用等级
        chooseCreditLevelType(){
            var self = this;
            NlDropdown({
                confirmBtn: false,
                datalist: self.creditLevelList
            }, function (res) {
                self.creditLevel = res;
            });
        },
        //选择服务等级
        chooseServiceLevelType(){
            var self = this;
            NlDropdown({
                confirmBtn: false,
                datalist: self.serviceLevelTypeList
            }, function (res) {
                self.serviceLevelType = res;
            });
        },
        //选择集团服务级别
        chooseGroupServiceLevelType(){
            var self = this;
            NlDropdown({
                confirmBtn: false,
                datalist: self.groupServiceLevelTypeList
            }, function (res) {
                self.groupServiceLevelType = res;
            });
        },
        //选择是否直管客户
        chooseIsUpCustomType(){
            var self = this;
            NlDropdown({
                confirmBtn: false,
                datalist: self.judgeTypeList
            }, function (res) {
                self.isUpCustomType = res;
            });
        },
        //选择是否省管客户
        chooseIsProCustomType(){
            var self = this;
            NlDropdown({
                confirmBtn: false,
                datalist: self.judgeTypeList
            }, function (res) {
                self.isProCustomType = res;
            });
        },
        //选择SLA信息
        chooseSlaInfoType(){
            var self = this;
            NlDropdown({
                confirmBtn: false,
                datalist: self.slaInfoTypeList
            }, function (res) {
                self.slaInfoType = res;
            });
        },
        //集团客户级别（2016）
        chooseGroupCutsomLevel(){
            var self = this;
            NlDropdown({
                confirmBtn: false,
                datalist: self.groupCutsomLevelList
            }, function (res) {
                self.groupCutsomLevel = res;
            });
        },
        //选择集团客户专线是否可以停机
        chooseIsStopPhoneType(){
            var self = this;
            NlDropdown({
                confirmBtn: false,
                datalist: self.isStopPhoneList
            }, function (res) {
                self.isStopPhoneType = res;
            });
        },
        //选择是否发送充值提醒短信
        chooseIsShortMessType(){
            var self = this;
            NlDropdown({
                confirmBtn: false,
                datalist: self.judgeTypeList
            }, function (res) {
                self.isShortMessType = res;
            });
        },
        //黑户弹框提醒
        blackCountTip(){
            let tMsg= "集团证件验真失败，请确认证件是否有效。";
            let msg=`<span style="color:#007AFF">${tMsg}</span>`;
            this.$messagebox({
                title: '温馨提示',
                message: msg,
                closeOnClickModal: false,
                showCancelButton: false
            }).then(action =>{
                return;
            });
        },
        //选择行业类别小类
        chooseBusCustSubType(){
          var self = this;
          NlDropdown({
            confirmBtn: false,
            datalist: self.busCustSubList
          }, function (res) {
            self.busCustSub = res;
            if(res.id != '05'){
              this.busCustSubDefine = ''
            }
          });
        },
        //提交前校验
        async doSubmit(){
            // await this.judgeGroupName();
            // if(!this.isValidName){
            //     this.$alert(`该集团名称【${this.certificateName}】已存在，请确认`)
            //     return;
            // }
          //集团名称和集团地址必须大于三个字符且不能全为数字
          if(this.certificateName.length <= 3 || this.certificateAddress.length <= 3){
            this.$alert('集团名称和集团证件地址必须大于三个字符');
            return;
          }
          if(!isNaN(this.certificateName) || !isNaN(this.certificateAddress)){
            this.$alert('集团名称和集团证件地址不能全为数字');
            return;
          }
            if(this.certificateType.id == "-99"){
                this.$alert('请选择集团证件信息的证件类型');
                return;
            }
            let flag = this.certificateType.label == "营业执照" ||
                this.certificateType.label == "组织机构代码证" ||
                this.certificateType.label == "事业单位登记证" ||
                this.certificateType.label == "统一社会信用代码证书";
            if(!this.certImg && flag){
                this.$alert('请上传'+this.certificateType.label);
                return;
            }
            if( this.certificateType.label == "身份证" && !this.shopContactPerson){
                this.$alert('请输入商客联系人号码');
                return;
            }
            if(this.certificateName == ""){
                this.$alert('请输入集团证件名称');
                return;
            }
            // if(this.legalPerson == ""){
            //     this.$alert('请输入集团证件法人');
            //     return;
            // }
            if(this.groupOrgType.id == "-99"){
                this.$alert('请选择集团机构类型');
                return;
            }
            if(this.categoryBigType.id == "-99"){
                this.$alert('请选择行业类别大类');
                return;
            }
            if(this.categorySmallType.id == "-99"){
                this.$alert('请选择行业类别小类');
                return;
            }
            if(this.idCardName == "" && !this.showIdCardName){
                this.$alert('请输入关联人证件姓名');
                return;
            }
            if(this.serviceRoleType.id == "-99"){
                this.$alert('请选择关联人服务角色');
                return;
            }
            if(this.cityType.id == "-99"){
                this.certificateFlag = true;
                this.$alert('请选择地市');
                return;
            }
            if(this.districtType.id == "-99"){
                this.certificateFlag = true;
                this.$alert('请选择区县');
                return;
            }
            if(this.gridType.id == "-99"){
                this.certificateFlag = true;
                this.$alert('请选择网格区县');
                return;
            }
            if(this.areaType.id == "-99"){
                this.certificateFlag = true;
                this.$alert('请选择网格区域中心');
                return;
            }
            if(this.peopleNum == ''){
              this.$alert('请填写员工数');
              return;
            }
            if(this.yearAmount == ''){
              this.$alert('请填写年营业额');
              return;
            }
            if(this.broadbandAddress.id == '-99'){
              this.$alert('请选择宽带地址');
                return;
            }
            if(this.regManaDepartment.id == '-99'){
                this.$alert("请选择登记管理部门");
                return;
            }
            if(this.entType.id == '-99'){
                this.$alert("请选择企业类型");
                return;
            }
            //场景类型不能为空
            if(this.clusteringScenarioType.id==='-99'){
              this.$alert('请选择场所类别');
              return;
            }
            if(this.isScenarioDetail()){
              if(this.clusteringScenarioDetailType.id==='-99'){
                this.$alert('请选择聚类细分场景类型');
                return;
              }
            }
            if (this.busCustSub.id === '') {
              this.$alert('请选择细分客群类别')
              return
            } else if (this.busCustSub.id === '05' && !this.busCustSubDefine) {
              this.$alert('请填写特色客群（自定义）')
              return
            }
            if(this.linkerNumber==''){
              this.$alert('请填写服务号码');
              return;
            }
          if(this.postCode==''){ //邮编默认值
            this.$alert('请填写办公邮编');
            return;
          }
            let check = {};
            check = this.checkNull(this.dynamicFieldModule);
            this.dynamicFieldModule.forEach(attr => {
                if(attr.isNeed === '1' && (attr.defaultValue.id === '98' ||attr.defaultValue === '' || attr.defaultValue === null)){
                    check.flag = true;
                    check.attrName = attr.attrName;
                }
            });
            if(check.flag){
                this.$alert("请填写"+check.attrName);
                return;
            }
            let idCardNameTemp = this.idCardName || this.showIdCardName
            let idCardNoTemp = this.showIdCardNo || this.idCardNo
            let groupCertIdTemp = this.certificateNo || this.showGroupCertId || this.linkerNumber
            let groupType = this.groupType.label; //集团类型
          let groupTypeNew =this.groupType.id; //集团类型(新增传英文)
            if(groupType == '请选择'){
                groupType = "";
            }
            let cutsomLevelType = this.cutsomLevelType.id; //客户等级
            if(cutsomLevelType == "-99"){
                cutsomLevelType = "";
            }
            let groupSize = this.groupSize.label; //集团规模
            if(groupSize == '请选择'){
                groupSize = "";
            }
            let serviceLevelType = this.serviceLevelType.id; //集团服务等级
            if(serviceLevelType == "-99"){
                serviceLevelType = "";
            }
            let isStopPhoneType = this.isStopPhoneType.label;//集团客户专线是否可以停机
            if(isStopPhoneType == "请选择"){
                isStopPhoneType = "";
            }
            let groupServiceLevelType = this.groupServiceLevelType.id;//集团服务级别
            if(groupServiceLevelType == "-99"){
                groupServiceLevelType = "";
            }
            let groupCutsomLevel = this.groupCutsomLevel.id; //集团客户级别
            if(groupCutsomLevel == "-99"){
                groupCutsomLevel = "";
            }
            let slaInfoType =  this.slaInfoType.label; //SLA信息
            if(slaInfoType == "请选择"){
                slaInfoType = "";
            }
            let isShortMessType = this.isShortMessType.label;//集团客户专线是否发送充值提醒
            if(isShortMessType == "请选择"){
                isShortMessType = "";
            }

            let industryThird =  this.industryThird.id == '-99' ? '' : this.industryThird.id;
            let industryFourth = this.industryFourth.id == '-99' ? '' : this.industryFourth.id;

            this.groupTel = this.groupTel || '0000000000000';
            //集团证件地址用户如果不输入就默认：无
            this.certificateAddress = this.certificateAddress || '无'
            //法人默认读取联系人
            this.legalPerson = this.legalPerson || idCardNameTemp
            //备注若不填，默认无
            this.remark = this.remark || '无'
            //集团证件号码读取集团联系人服务号码
            // this.certificateNo = this.certificateNo || this.linkerNumber;
            //校验外围渠道受理不能有重复值
            let newChannvelValList = [];
		    this.channelVal.forEach( label=>{
                if(newChannvelValList.indexOf(label) == '-1'){
                    newChannvelValList.push(label);
                }
		    });


            let grpBaseInfo = {  //集团基本信息
                custName: this.certificateName, //集团名，
                cityId: this.cityType.id, //地市编码，
                countyId: this.districtType.id, //区县编码
                channelPlatCounty: this.gridType.id, //网格化区县
                channelPlatArea: this.areaType.id, //网格化区域中心
                certType: this.certificateType.label, //单位证件类别
                certId: groupCertIdTemp, //证件号码
                certAddress: this.certificateAddress, //证件地址
                legal: this.legalPerson, //法人代表 可为空
                address: this.location, //办公地址，和crm的集团地址是同一个值
                longitudeLatude: this.longitude+","+this.latitude, //经纬度，经纬度用逗号分隔 可为空
                postCode: this.postCode, //办公地址邮编 可为空
                contactPhone: this.groupTel, //集团联系电话 可为空
                jkdtindustryLevel1: this.categoryBigType.label, //行业类别1
                jkdtindustryLevel2: this.categorySmallType.label, //行业类别2
                industryLevel3: industryThird, //行业类型3
                industryLevel4: industryFourth, //行业类型4
                locationType: groupType, //集团类型
                locationTypeNew: groupTypeNew, //集团类型（新增传英文）
                annualTurnover: this.yearAmount, //年营业额（万元） 新增或变更客户操作必填，传默认值
                isStrategicCustomer: this.sVipType.id, //是否战略客户，默认传否的字典id
                custLevel: newChannvelValList.toString(), //外围受理渠道，渠道编码支持多个，使用逗号分隔
                grpCertificate: this.certImg, //集团证件照片流
                authorization: this.authImg, //单位授权书图片 可为空  授权书地址
                groupType: this.groupOrgType.label, //集团机构类型
                corpType: this.entType.id,//企业类型
            };
            let grpOrgLevel = {//集团机构级别信息
                grpOrgLevel: this.groupLevelType.id, //集团机构级别
            };
            let grpRelation = {//集团关系信息
                creditLevel: this.creditLevel.label,//客户信用等级 可为空
                corpScale: groupSize,//集团规模
                employeeNum: this.peopleNum, //集团员工数
                customerLevel: cutsomLevelType, //客户等级 可为空
                serviceLevel: serviceLevelType, //集团服务等级
            }
            let grpExpandInfo = { //集团扩展信息，传默认值
                ifCanStopUser: isStopPhoneType, //集团专线是否可以停机，传默认值 可为空
                custServiceClass: groupServiceLevelType, //集团服务级别
                isOrg: this.isUpCustomType.label, //是否直管客户，默认否
                isProvinceOrg: this.isProCustomType.label, //是否省管客户，默认否
                GroupLevel2016: groupCutsomLevel, //集团客户级别（2016）
                sla: slaInfoType, //sla信息
                isReChargeNotify: isShortMessType, //是否发送充值提醒短信 可为空
                acceptChannel: newChannvelValList.toString(), //外围渠道受理
                verificEcCertResult: this.verificIdentific,
                identificLicenseInfo: 'uniscId='+groupCertIdTemp+"|name="+this.certificateName+"|address="+
                                        this.certificateAddress+"|legalPersonName="+this.legalPerson+
                                        "|type="+this.certificateType.label,
                grpAuthFlag: idCardNameTemp == this.legalPerson ? '0' : '1',
                isDoubleAdress: '1',//是否双地址集团
                regManaDepartment: this.regManaDepartment.label, //登记管理部门
                busiCompanyID: this.registeredProvince.id, //生产经营所在省编码
                busiLocation: this.registeredCity.id, //生产经营所在地市编码
                busiCountyID: this.registeredCountry.id, //生产经营所在区县编码
                busiCountyName: this.registeredCountry.label, //生产经营所在区县名称
            }
            let grpDevChannel = { //集团发展渠道
                developOrgId: this.depWayType.label , //客户发展渠道
                developMan: this.depNumber, //客户发展工号
                masterChannelType: this.serviceWayType.label, //主动服务渠道
                masterCustMgr: this.serviceNumber, //主动服务工号
            }
            let grpRelPerson = { //集团联系人信息
                concPrsnName: idCardNameTemp,//姓名，
                concPrsnTelnum: this.linkerNumber, //服务号码，移动号码
                memberRoleType: this.serviceRoleType.id, //服务角色，默认联系人
                concCredTypeCd: this.idCardType.label, //证件类别，只能是身份证
                concIdno: idCardNoTemp, //证件号码
                cIdImage: this.idcardImg, //身份证图片可为空
                postcode: this.email, //电子邮箱
            }
            //获取动态节点参数
            let dynamicValueList = [];
            grpExpandInfo.attrInfos = await this.initDynamicList(dynamicValueList);

            let param = {
                grpBaseInfo: JSON.stringify(grpBaseInfo),
                grpOrgLevel: JSON.stringify(grpOrgLevel),
                grpRelation: JSON.stringify(grpRelation),
                grpDevChannel: JSON.stringify(grpDevChannel),
                grpRelPerson: JSON.stringify(grpRelPerson),
                grpExpandInfo: JSON.stringify(grpExpandInfo),
                rmk: this.remark,  //备注 不可为空
                operType: 'MOD', //以此判断时集团补录操作,
                groupId: this.groupId,
                isAgain: this.dataSuppleStatus,
                broadbandAddress: this.broadbandAddress.label,//宽带安装地址
                shopContactPerson: this.shopContactPerson, //商客联系人
               "stationId":this.uinfo.stationId//岗位id
            }
            this.$messagebox({
                title: '提示',
                message: '确认提交？',
                showCancelButton: true,
                showConfirmButton: true
            }).then(action => {
                if (action == 'confirm') {
                    this.isClick = 'disable'
                    this.createGroupCust(param);
                }
            });
        },
        //集团建档初始化接口
        getInitData(){
            let url = `/xsb/personBusiness/groupArchives/h5getInitData`;
            return this.$http.get(url).then(res =>{
                if(res.data.retCode == '0'){
                    let data = res.data.data;
                    //集客专线是否可停机
                    this.isStopPhoneList = data[1084];
                    //默认集团客户专线可停机
                    this.isStopPhoneType = this.isStopPhoneList[1];
                    //1085 是否，包括是否发送短信
                    this.judgeTypeList = data[1085];
                    //1086 地市
                    this.cityList = data[1086];
                    this.cityType = this.cityList[0];
                    if(this.cityType.son){
                        this.districtTypeList = this.cityType.son;
                        this.districtType = this.districtTypeList[0];
                    }else{
                        this.districtTypeList = [];
                    }
                    //是否战略客户
                    this.sVipType = this.judgeTypeList[0];
                    //是否直管客户
                    this.isUpCustomType = this.judgeTypeList[0];
                    //是否省管客户
                    this.isProCustomType = this.judgeTypeList[0];
                    //是否发送
                    this.isShortMessType = this.judgeTypeList[1];
                    //集团证件类型
                    this.certificateTypeList = data[1089];
                    //集团类型
                    this.groupTypeList = data[1116];
                    this.groupType = this.groupTypeList.find(function (item) {
                        return  item.label == "网格普通集团"
                    })
                    //行业类别大类：
                    this.categoryBigTypeList = data[1111];
                    //集团机构类型
                    this.groupOrgTypeList = data[1103];
                    //集团机构级别  -- 默认独立集团
                    this.groupLevelTypeList = data[1092];
                    this.groupLevelType = this.groupLevelTypeList.find(function (item) {
                        return  item.label == "独立集团"
                    })
                    //客户发展渠道 && 主动服务渠道
                    this.depWayTypeList = data[1099];
                    this.servicepWayTypeList = data[1099];
                    this.depWayType = this.depWayTypeList.find(function (item) {
                        return  item.label == "客户经理"
                    })
                     this.serviceWayType = this.servicepWayTypeList.find(function (item) {
                        return  item.label == "客户经理"
                    })
                    //外围渠道受理	默认ESOP
                    this.channelList = data[1091];
                    let channelObj = {
                        "id":"",
                        "label":""
                    }
                    channelObj = this.channelList.find(function (item) {
                        return  item.label == "G3版ESOP"
                    })
                    this.channelVal.push(channelObj.id);
                    //服务角色 默认为联系人
                    this.servicepRoleTypeList = data[1100];
                    this.serviceRoleType = this.servicepRoleTypeList.find(function (item) {
                        return  item.label == "集团主联系人"
                    })
                    //客户信用类型
                    this.creditLevelList = data[1102];
                    this.creditLevel = this.creditLevelList[3];

                    //集团规模
                    this.groupSizeList = data[1093];
                    //客户等级
                    this.cutsomLevelTypeList = data[1094];
                    //服务等级
                    this.serviceLevelTypeList = data[1095];
                    //集团服务级别
                    this.groupServiceLevelTypeList = data[1096];
                    //SLA信息
                    this.slaInfoTypeList = data[1098];
                    //集团客户级别（2016）
                    this.groupCutsomLevelList = data[1097];
                    //网格区县
                    this.gridTypeList = data[1];
                    this.gridType = this.gridTypeList[0];
                    //网格区域中心
                    if(this.gridType.son && this.gridType.son.length > 0){
                        this.areaTypeList = this.gridType.son;
                    }else{
                        this.areaTypeList = [{id:"-1",label:"暂无"}];
                    }
                    this.areaType = this.areaTypeList[0];
                    //生产经营所在地市
                    this.registeredCityList = data[1106];
                    this.registeredCity = this.registeredCityList[0];
                    //生产经营所在区县
                    this.registeredCountryList = this.registeredCity.son;
                    this.registeredCountry = this.registeredCountryList[0];
                    //登记管理部门
                    this.regManaDepartmentList = data[1109];
                    //企业类型
                    this.entTypeList = data[1108];
                    //场景类型
                    this.clusteringScenarioList=data[1113]
                    this.clusteringScenarioList.push({ id: '7', label: '其他', son: [] })
                  //细分客群类别
                    this.busCustSubList = data[1117]

                } else {
                    this.$alert(res.data.retMsg || '查询网格区域列表失败');
                }
            }).catch(res =>{
                this.$alert('查询网格区域列表网络异常:'+ res);
            });
        },
        //网格区域列表查询接口
        qrychildGridList(parentId,type){
            let url = `/xsb/personBusiness/groupArchives/h5qrychildGridList?parentId=${parentId}&selectRegion=${this.cityType.id}`;
            return this.$http.get(url).then(res =>{
                if(res.data.retCode == '0'){
                    let data = res.data.data;
                    if(type == '1'){
                        this.getDistrictList(this.cityType.id);
                        this.gridTypeList = data;
                        if(this.gridTypeList && this.gridTypeList.length > 0){
                            this.gridType =  this.gridTypeList[0];
                            this.qrychildGridList(this.gridType.id,'3');
                        }
                    }else if(type == '3'){
                        this.areaTypeList = data;
                        if(this.areaTypeList && this.areaTypeList.length > 0){
                            this.areaType = this.areaTypeList[0];
                        }
                    }
                } else {
                    if(type=='1'){
                      this.districtTypeList=[];
                      this.districtType = {
                        id:'-99',
                          label:'请选择'
                      };
                    }else if(type == '2'){
                      this.gridTypeList=[];
                      this.gridType = {
                        id:'-99',
                        label:'请选择'
                      };
                    }else if(type == '3'){
                      this.areaTypeList=[];
                      this.areaType = {
                        id:'-99',
                        label:'请选择'
                      };
                    }
                    this.$alert(res.data.retMsg || '查询网格区域列表失败');
                }
            }).catch(res =>{
                if(type=='1'){
                  this.districtTypeList=[];
                  this.districtType = {
                    id:'-99',
                    label:'请选择'
                  };
                }else if(type == '2'){
                  this.gridTypeList=[];
                  this.gridType = {
                    id:'-99',
                    label:'请选择'
                  };
                }else if(type == '3'){
                  this.areaTypeList=[];
                  this.areaType = {
                    id:'-99',
                    label:'请选择'
                  };
                }
                this.$alert('查询网格区域列表网络异常:'+ res);
            });
        },
        //查询地市列表接口
        getDistrictList(selectRegion){
            this.districtTypeList=[];
            this.districtType = {
                id:'-99',
                label:'请选择'
            };
            let url = `/xsb/personBusiness/groupArchives/h5GetDistrictList?selectRegion=`+selectRegion;
            this.$http.get(url).then(res =>{
                if(res.data.retCode == '0'){
                     this.districtTypeList = res.data.data;
                      if(this.districtTypeList && this.districtTypeList.length > 0){
                            this.districtType =  this.districtTypeList[0];
                        }
                }else{
                    this.$alert('查询区县列表失败,查询异常');
                }
            }).catch(res =>{
                this.$alert('查询区县列表失败:'+ res);
            });
        },
        //企业/个人证照识别并验真
        identificOrc(param){
            let failMsg='无法识别，请确认图片拍摄质量再重试，或者手动输入！';
            let url = `/xsb/personBusiness/groupArchives/h5IdentificOrc`;
            this.$http.post(url,param).then(res =>{
                if(res.data.retCode == '0'){
                    let data = res.data.data;
                    if(this.pzType == '1' && data.ecInfo){
                        if(data.ecInfo.name != null && data.ecInfo.uniscId != null && data.ecInfo.name != '' && data.ecInfo.uniscId != ''){
                            this.certificateName = data.ecInfo.name;
                            this.certificateNo = data.ecInfo.uniscId;
                            if(data.ecInfo.address != null && data.ecInfo.address != ''){
                                this.certificateAddress = data.ecInfo.address;
                            }
                            if(data.ecInfo.legalPersonName != null && data.ecInfo.legalPersonName != ''){
                                this.legalPerson = data.ecInfo.legalPersonName;
                            }
                            this.judgeTrue('1');
                        }else if(this.certificateType.label != "个人有效证件+店铺门头照"){
                          this.$alert(failMsg);
                        }
                    }else if(this.pzType == '2' && data.peInfo){
                        if(data.peInfo.name != null && data.peInfo.idCardNumber != null){
                          this.idCardName = data.peInfo.name;
                          this.idCardNo = data.peInfo.idCardNumber;
                          this.showIdCardNo = data.peInfo.idCardNumber;
                          this.idCardAddress = data.peInfo.address;
                          this.idCardFlag = false;
                          //法人与身份证名字一致  显示授权书
                          if(this.idCardName != this.legalPerson){
                            this.idCardFlag = true;
                          }
                        }else{
                          this.$alert(failMsg);
                        }
                    }else if(this.certificateType.label != "个人有效证件+店铺门头照"){
                          this.$alert(failMsg);
                    }
                } else if(this.certificateType.label != "个人有效证件+店铺门头照"){
                    this.$alert(failMsg);
                    this.defeaultTip = '输入或OCR自动回显';
                }

                if(this.pzType == '1'){
                    //集团证件照片上传
                    let uploadParam = {
                        "photoStr": param.image, //照片流
                        "type": "01", //集团照01,身份证02,委托书03
                        "certId": this.uinfo.crmId + parseInt(new Date().getTime()/1000,10), //证件号 委托书证件号填身份证号
                        "unEncrpt": "true",
                        "longitudeLatude": `${this.longitude},${this.latitude}`,
                        "location": this.location
                    }
                    this.uploadImg(uploadParam);
                }else if(this.pzType == '2'){
                    //身份证照片上传  获取身份证文件名
                    let uploadParam = {
                    "photoStr": param.image, //照片流
                    "type": "02", //集团照01,身份证02,委托书03
                    "certId": this.uinfo.crmId + parseInt(new Date().getTime()/1000,10), //证件号 委托书证件号填身份证号
                    "unEncrpt": "true"
                    }
                    this.uploadImg(uploadParam);
                }
            }).catch(res =>{
                this.$alert('证照识别并验真网络异常:'+ res);
            });
        },
        //集团客户创建
        createGroupCust(param){
          let url = `/xsb/personBusiness/groupArchives/h5createGroupCust`;
          this.$http.post(url,param).then(async res =>{
            if(res.data.retCode == '0' || res.data.retMsg.indexOf('上传OBS失败') != -1){
                // 同步给瑞月
                await this.syncGroupCust();
                if(!this.isAddTable){
                    this.isClick = 'enable';
                }else{
                    param.procInstId = this.procInstId;
                    let params = {
                        telNumber : this.linkerNumber,
                        groupNumber : this.groupId,
                        groupName : this.certificateName,
                        cityType : this.cityType.id,
                        districtType : this.districtType.id,
                        gridType : this.gridType.id,
                        areaType : this.areaType.id,
                        address : this.location,
                        reqContent: JSON.stringify(param),
                    }
                    let url = '/xsb/personBusiness/groupDataSupple/h5AddGroupDataSuppleInfo';
                    this.$http.post(url,params).then(res => {
			            let {retCode,retMsg} = res.data;
                        if(retCode == '0'){
                            this.$messagebox({
                                title: '提示',
                                message: '提交集团资料补录信息成功',
                                showCancelButton: false,
                                showConfirmButton: true
                            }).then(action => {
                                this.cTab('1');
                            });
                        }else{
                            this.$alert(retMsg || '虚拟集团补录入库异常');
                        }
                        this.isClick = 'enable';
                    }).catch(res =>{
                        this.$alert('补资料信息录入网络异常:'+ res);
                    });
                }

            } else {
                this.isClick = 'enable';
                this.$alert(res.data.retMsg || '集团客户创建失败');
            }
          }).catch(res =>{
                this.isClick = 'enable';
                this.$alert('集团客户创建网络异常:'+ res);
          });
        },
        //获取虚拟集团信息
        getVirtualGroupInfo(isShowTip){
            //是否需要提示信息
            if(isShowTip){
                this.showVirtualData();
            }else{
                this.$messagebox({
                    title: '温馨提示',
                    message: '已存在虚拟集团，是否要补资料？',
                    showCancelButton: true,
                    showConfirmButton: true,
                    confirmButtonText: "补录",
                    cancelButtonText: "取消"
                }).then(action => {
                    if (action == 'confirm') {
                        this.showVirtualData();
                    }
                });
            }
        },
        //先查询该服务号码是否有虚拟集团，若有再查询补录表是否有补录记录
        showVirtualData(){
            let url = `/xsb/personBusiness/yiwtongUpgrade/h5QryVirtualGroupInfo?serviceNumber=${this.telNum}`;
            this.$http.get(url).then(async res =>{
                let {retCode,retMsg,data} = res.data;
                if(retCode == '0'){
                let virtualInfo = data;
                this.groupId = data.groupId;
                //回显页面
                this.location = data.groupInstallAdress;
                this.actAddr = data.groupInstallAdress;
                this.certificateAddress = data.groupInstallAdress;
                //查询虚拟集团信息后需要再判断是否有补录记录，若存在补录记录并且状态为审核通过(status=1)不允许补录
                let url = `/xsb/personBusiness/groupDataSupple/h5GetGroupDataSuppleInfo?groupNumber=${this.groupId}`;
                this.$http.get(url).then(async res => {
			        let {retCode,data} = res.data;
                    let message = '';
                    //判断数据库是否已经有补录记录
                    if(retCode == '0' && data != null){
                        if(data.status == '0'){
                            message = '补录信息未审核，是否进行重新补录';
                        }else if(data.status == '2'){
                            message = '补录信息审核未通过，是否进行重新补录';
                        }else{
                            message = '已补录，是否查看补录信息';
                        }
                        this.$messagebox({
                            title: '温馨提示',
                            message: message,
                            showCancelButton: true,
                            showConfirmButton: true,
                            confirmButtonText: "确定",
                            cancelButtonText: "取消"
                        }).then(action => {
                            if (action == 'confirm') {
                                //显示补录历史信息
                                this.showHistoryData(data);
                            }
                        });
                    }else{
                        //补录表没有补录信息，视为未补录过
                        this.locationConfirm();
                        this.dataSuppleStatus = '';
                        this.groupNumber = virtualInfo.groupId;
                        this.certificateName = virtualInfo.groupName;
                        this.showDetailflag = true;
                        this.getUserInfo();
                        await this.getBroadbandAddress();
                        this.getGroupDetailInfo(this.groupId);
                    }
                }).catch(res =>{
                    this.$alert('查询历史补录信息网络异常:'+ res);
                });
            }else if(retCode == '1'){
                if(this.type == '1'){
                    //一网通集团资料补录
                    this.$alert('查无虚拟集团，无法补录');
                    this.showDetailflag = false;
                }else{
                    this.$alert('创建虚拟集团异常,请重新输入号码再试');
                }
            }else{
                this.$alert(retMsg || '查询虚拟集团信息失败');
			}
        }).catch(res =>{
            this.$alert('查询虚拟集团异常:'+ res);
        });
    },
      //集团号码验真
      judgeTrue(val){
        // if(val == '1'){
        //     this.judgeGroupName();
        // }
        if(this.certificateType.label == '' || this.certificateNo == ''){
            return;
        }
        let param = {
            name : this.certificateName,
            uniscId : this.certificateNo,
            legalPersonName : this.legalPerson,
            unLoadFlg : true,
            licenseAttach : this.certImg,
        };
        let tMsg= '';
        let url = `/xsb/personBusiness/groupArchives/h5VerificIdentific`;
        this.$http.post(url,param).then(res =>{
            this.verificIdentific = res.data.data.verificIdentific;
            this.chkGrpSocial = res.data.data.chkGrpSocialUnitCreditCode;
            let groupInfo = res.data.data.groupInfo;
            this.showVerData(groupInfo);
            if(this.verificIdentific == '2' && this.chkGrpSocial == '1'){
                tMsg = "您录入的统一社会信用代码可能有误且该统一社会信用代码已经存在，请确认是否继续！";
            } else if(this.verificIdentific == '2'){
                tMsg = "您录入的统一社会信用代码可能有误，请确认是否继续！";
            } else if(this.chkGrpSocial == '1'){
                tMsg = "您录入的统一社会信用代码已经存在，请确认是否继续！";
            }
            if(tMsg != ''){
                this.$messagebox({
                    title: '温馨提示',
                    message: tMsg,
                    showCancelButton: true,
                    showConfirmButton: true,
                    confirmButtonText: "继续",
                    cancelButtonText: "确定"
                }).then(action => {
                    if (action == 'confirm') {
                        this.$refs.certificateNo.focus();
                    }
                });
                }

            }).catch(res =>{
                this.$alert('集团验证失败:'+ res);
            });
        },
        //校验集团客户名称是否重名
    //     async judgeGroupName(){
    //     if(!this.certificateName){
    //       return;
    //     }
    //     let url = `/xsb/personBusiness/groupArchives/h5JudgeGroupName?groupName=${this.certificateName}`;
    //     return this.$http.get(url).then(res =>{
    //       let {retCode,retMsg} = res.data;
    //       if(retCode == '99'){
    //         this.isValidName = false;
    //         this.$alert(`该集团名称【${this.certificateName}】已存在，请确认`)
    //       } else if(retCode == '00'){
    //         this.isValidName = true;
    //       } else{
    //         this.$alert(retMsg || '校验集团客户名称是否重名失败');
    //       }
    //     }).catch(res =>{
    //       this.$alert('校验集团客户名称是否重名异常:'+ res);
    //     });
    //   },
        //对集团名称进行特殊字符过滤
        btKeyUp(e){
            e.target.value = e.target.value.replace(/[`~!@#$%^&*_\-+=<>?:"{}|,.\/;'\\[\]·~！@#￥%……&*——\-+={}|《》？：“”【】、；‘’，。、 ]/g,"");
            this.certificateName = e.target.value;
        },
        //一网通创建集团菜单进入——创建虚拟集团
        createVirtualGroup(){
            // 如果正在创建中，直接返回，防止重复提交
            if (this.isCreatingVirtualGroup) {
                return;
            }

            this.isCreatingVirtualGroup = true;
            let marketType={id:"2",label:"政企"}
            marketType=attrChange("VirtualMarketType",{id:"2",label:"政企"})
            let param = {
              serviceNumber : this.telNum,
              marketType:marketType.id
            };
            let url = '/xsb/personBusiness/yiwtongUpgrade/h5CreateVirtualGroup';
            this.$http.post(url,param).then(res => {
                let {retCode,retMsg} = res.data;
                if(retCode == '0' || retCode == '-20' || retMsg.indexOf('虚拟集团已存在') != -1){
                    this.getVirtualGroupInfo(true);
                }else{
                    this.$alert(retMsg||'虚拟集团创建失败');
                    this.showDetailflag = false;
                }
            }).catch(res =>{
                this.$alert('创建虚拟集团网络异常:'+ res);
            }).finally(() => {
                this.isCreatingVirtualGroup = false;
            });
        },
        // 显示创建虚拟集团弹框
        showCreateVirtualGroupDialog() {
            this.$refs.createGroupDialog.show();
        },
        // 处理创建虚拟集团弹框的操作
        handleCreateGroupAction(action) {
            if (action === 'confirm') {
                // 防抖处理：如果正在创建中，不允许再次点击
                if (this.isCreatingVirtualGroup) {
                    return;
                }

                // 设置加载状态
                this.$refs.createGroupDialog.setLoading(true);

                // 创建虚拟集团
                this.createVirtualGroupWithDialog();
            } else {
                // 取消操作，直接关闭弹框
                this.$refs.createGroupDialog.hide();
            }
        },
        // 带弹框状态管理的创建虚拟集团方法
        createVirtualGroupWithDialog() {
            this.isCreatingVirtualGroup = true;
            let marketType={id:"2",label:"政企"}
            marketType=attrChange("VirtualMarketType",{id:"2",label:"政企"})
            let param = {
              serviceNumber : this.telNum,
              marketType:marketType.id
            };
            let url = '/xsb/personBusiness/yiwtongUpgrade/h5CreateVirtualGroup';
            this.$http.post(url,param).then(res => {
                let {retCode,retMsg} = res.data;
                if(retCode == '0' || retCode == '-20' || retMsg.indexOf('虚拟集团已存在') != -1){
                    this.getVirtualGroupInfo(true);
                }else{
                    this.$alert(retMsg||'虚拟集团创建失败');
                    this.showDetailflag = false;
                }
            }).catch(res =>{
                this.$alert('创建虚拟集团网络异常:'+ res);
            }).finally(() => {
                this.isCreatingVirtualGroup = false;
                // 关闭弹框
                this.$refs.createGroupDialog.hide();
            });
        },
        //切换tab——资料补录tab：1 / 历史补录tab：2
        cTab(tabNo){
            this.tab = tabNo;
            if(this.tab == '1'){
                this.showListFlag = false;
                this.showDetailflag = false;
            }else{
                //点击历史补录tab时清掉搜索内容
                this.searchContent = '';
                this.searchHistoryList();
                this.showDetailflag = false;

            }
	    },
        //根据服务号码进行集团补录或查询历史集团列表
        async search(){
            if(this.tab == '1'){
                this.showDetailflag = false;
                if(this.telNum == null || this.telNum == ''){
                    this.$alert("服务号码不能为空");
                    return;
                }
                await this.clearDetailData();
                if(this.type == '1'){
                     if(this.zkType == '1'){
                        this.getVirtualGroupInfo(true);
                    }else{
                        //根据集团编号补录，telNum值为集团编号
                       this.groupId = this.telNum;
                       this.getGroupDetailInfo(this.groupId);
                    }
                }else{
                    //根据服务号码补录
                    if(this.zkType == '1'){
                       this.judgeIsExistGroup();
                    }else{
                        //根据集团编号补录，telNum值为集团编号
                       this.groupId = this.telNum;
                       this.getGroupDetailInfo(this.groupId);
                    }
                }
            }else{
                this.searchHistoryList();
            }

        },
      //根据集团编号查询集团扩展信息
      getGroupExtendInfo(groupNumber) {
        let url = `/xsb/personBusiness/groupArchives/h5GetGrpCustExtendInfo?groupId=${groupNumber}`
        let _this=this;
        this.$http
          .get(url, {
            unLoadFlg: true, //不展示加载圈
          })
          .then((res) => {
            let { retCode, retMsg, data } = res.data
            if (retCode == '0') {
              //获取集团创建时间
              if(data.grpRelation){
                _this.groupCrateTime = data.grpRelation.createTime
              }
            }
          })
          .catch((e) => {
          })
      },

     //根据集团编号查询集团信息
    getGroupDetailInfo(groupNumber){
        let url = `/xsb/personBusiness/groupArchives/h5GetGroupDetailInfo?groupNumber=${groupNumber}`;
        this.$http.get(url).then(res =>{
          let {retCode,retMsg,data} = res.data;
          if(retCode == '0'){
            let groupInfo = data;
            //数据回显页面
            this.showDetailflag = true;
            this.parseData(groupInfo);
            this.getGroupExtendInfo(groupNumber); //调用接口获取集团创建时间
          }else{
            this.$alert(retMsg || "查询集团客户详细信息失败");
          }
        }).catch(res =>{
          this.$alert('查询集团客户详细信息异常:'+ res);
        });
      },
      //将集团编号查询出来的集团信息回显页面
        async parseData(groupInfo){
            let relationInfo = groupInfo ? groupInfo.relationInfo : {};
            if(groupInfo.groupname){
                this.certificateName = groupInfo.groupname;
            }
            //地市
            if(groupInfo.region){
                this.cityType =  this.cityList.find(function (item) {
                                return  item.id == groupInfo.region;
                            });
                //区县列表
                this.districtTypeList = this.cityType.son;
            }
            //区县
            let districtTypeTemp;
            if(groupInfo.countryid){
                districtTypeTemp = this.districtTypeList.find(function (item) {
                                return  item.id == groupInfo.countryid;
                            });
            }
            this.districtType = typeof districtTypeTemp == "undefined" ? this.districtType : districtTypeTemp;
            //网格区县
            let gridTypeTemp;
            if(groupInfo.channelplatcounty){
                gridTypeTemp = this.gridTypeList.find(function (item) {
                                return  item.id == groupInfo.channelplatcounty;
                            });
            }
            this.gridType = typeof gridTypeTemp == "undefined" ? this.gridType : gridTypeTemp;
            //等待根据网格区县查网格区域中心
            await this.qrychildGridList(this.gridType.id, "3");
            //网格区域中心
            let areaTypeTemp;
            if(groupInfo.channelplatarea){
                areaTypeTemp = this.areaTypeList.find(function (item) {
                                return  item.id == groupInfo.channelplatarea;
                            });
            }
            this.areaType = typeof areaTypeTemp == "undefined" ? this.areaType : areaTypeTemp;
            //证件类型
            let certificateTypeTemp;
            if(groupInfo.certtype){
                certificateTypeTemp = this.certificateTypeList.find(function (item) {
                                return  item.id == groupInfo.certtype;
                            });
              if (groupInfo.certid) {
                if (certificateTypeTemp && certificateTypeTemp.id == 'PePhoLicence') {
                  this.certificateNo = ''
                  this.showGroupCertId = groupInfo.certid
                }else{
                  this.certificateNo = groupInfo.certid
                }
              }
            } else {
              //证件号码
              if (groupInfo.certid) {
                this.certificateNo = groupInfo.certid
              }
            }
            this.certificateType = typeof certificateTypeTemp == "undefined" ? this.certificateType : certificateTypeTemp;
            //证件地址
            if(groupInfo.certaddr){
                this.certificateAddress = groupInfo.certaddr;
            }
            //办公地址
            if(groupInfo.address){
                this.location = groupInfo.address;
            }
            //生产经营具体地址用集团地址address参数回显
            if(groupInfo.address){
                this.actAddr = groupInfo.address;
            }
            //邮编
            if(groupInfo.postcode){
                this.postCode = groupInfo.postcode;
            }
            //联系电话
            if(groupInfo.linkphone){
                this.groupTel = groupInfo.linkphone;
            }
            if(groupInfo.employeetotal){
                this.peopleNum = groupInfo.employeetotal;
            }
            //行业大类
            let categoryBigTypeTemp;
            if(groupInfo.vocaionkind1){
                categoryBigTypeTemp = this.categoryBigTypeList.find(function (item) {
                                return  item.id == groupInfo.vocaionkind1;
                            });
            }
            this.categoryBigType = typeof categoryBigTypeTemp == "undefined" ? this.categoryBigType : categoryBigTypeTemp;
            //行业小类列表
            this.categorySmallTypeList = this.categoryBigType.son;
            //年营业额
            if(groupInfo.annualsales){
                this.yearAmount = groupInfo.annualsales;
            }
            //集团机构类型
            let groupOrgTypeTemp;
            if(groupInfo.departtype){
                groupOrgTypeTemp = this.groupOrgTypeList.find(function (item) {
                                return  item.id == groupInfo.departtype;
                            });
            }
            this.groupOrgType = typeof groupOrgTypeTemp == "undefined" ? this.groupOrgType : groupOrgTypeTemp;
            //集团类型
            let groupTypeTemp;
            if(groupInfo.areatype){
                groupTypeTemp = this.groupTypeList.find(function (item) {
                                return  item.id == groupInfo.areatype;
                            });
            }
            this.groupType = typeof groupTypeTemp == "undefined" ? this.groupType : groupTypeTemp;
            //客户发展工号
            // if(groupInfo.developman){
            //     this.depNumber = groupInfo.developman;
            // }
            //主动发展渠道
            let serviceWayTypeTemp;
            if(groupInfo.channeltype){
                serviceWayTypeTemp = this.servicepWayTypeList.find(function (item) {
                                return  item.id == groupInfo.channeltype;
                            });
            }
            this.serviceWayType = typeof serviceWayTypeTemp == "undefined" ? this.serviceWayType : serviceWayTypeTemp;

            //客户发展渠道
            let depWayTypeTemp;
            if(groupInfo.developorgid){
                depWayTypeTemp = this.depWayTypeList.find(function (item) {
                                return  item.id == groupInfo.developorgid;
                            });
            }
            this.depWayType = typeof depWayTypeTemp == "undefined" ? this.depWayType : depWayTypeTemp;
            //主动发展工号
            if(groupInfo.custmgr){
                this.serviceNumber = groupInfo.custmgr;
                this.depNumber = groupInfo.custmgr;
            }
            if(relationInfo){
                //联系人姓名
                if(relationInfo.name){
                    this.idCardName = '';
                    this.showIdCardName = relationInfo.name;
                }
                //联系人服务号码
                if(relationInfo.servnumber){
                    this.linkerNumber = relationInfo.servnumber;
                }
                //联系人服务角色
                let serviceRoleTypeTemp;
                if(relationInfo.roletype){
                    serviceRoleTypeTemp = this.servicepRoleTypeList.find(function (item) {
                                return  item.id == relationInfo.roletype;
                            });
                }
                this.serviceRoleType = typeof serviceRoleTypeTemp == "undefined" ? this.serviceRoleType : serviceRoleTypeTemp;
                //联系人证件类型
                let idCardTypeTemp;
                if(relationInfo.certtype){
                    //crm接口查询出来的类型字段是开头大写的IdCard
                    let certtype = relationInfo.certtype.charAt(0).toLowerCase() + relationInfo.certtype.slice(1);
                    idCardTypeTemp = this.idCardTypeList.find(function (item) {
                                return  item.id == certtype;
                            });
                }
                this.idCardType = typeof idCardTypeTemp == "undefined" ? this.idCardType : idCardTypeTemp;
                //联系人证件编码
                if(relationInfo.certid){
                    this.idCardNo = relationInfo.certid;
                }
                //联系人邮箱
                if(relationInfo.email){
                    this.email = relationInfo.email;
                }
            }
            //集团详情扩展字段
            let attrList = groupInfo.grpExpandInfo.attrInfoList.attrInfo
            if (attrList && attrList.length > 0) {
              for (let i in attrList) {
                if (attrList[i].attrCode == 'longitude' && attrList[i].attrValue) {
                  this.longitude = attrList[i].attrValue.split(',')[0]
                  this.latitude = attrList[i].attrValue.split(',')[1]
                }
                //登记管理部门
                if (attrList[i].attrCode == 'regManaDepartment' && attrList[i].attrValue) {
                  let regManaDepartmentTemp = this.regManaDepartmentList.find((item) => {
                    return item.label == attrList[i].attrValue
                  })
                  this.regManaDepartment = regManaDepartmentTemp ? regManaDepartmentTemp : this.regManaDepartment
                }
                //生产经营所在地市
                if (attrList[i].attrCode == 'busiLocation' && attrList[i].attrValue) {
                  let registeredCityTemp = this.registeredCityList.find((item) => {
                    return item.id == attrList[i].attrValue
                  })
                  this.registeredCity = registeredCityTemp ? registeredCityTemp : this.registeredCity
                }
                //生产经营所在区县
                if (attrList[i].attrCode == 'busiCountyID' && attrList[i].attrValue) {
                  let registeredCountryTemp = this.registeredCountryList.find((item) => {
                    return item.id == attrList[i].attrValue
                  })
                  this.registeredCountry = registeredCountryTemp ? registeredCountryTemp : this.registeredCountry
                }
                //集团客户专线是否可以停机：
                if (attrList[i].attrCode == 'ifCanStopUser' && attrList[i].attrValue) {
                  let isStopPhoneTypeTemp = this.judgeTypeList.find((item) => {
                    return item.id == attrList[i].attrValue
                  })
                  this.isStopPhoneType = isStopPhoneTypeTemp ? isStopPhoneTypeTemp : this.isStopPhoneType
                }
                //是否战略客户：
                if (attrList[i].attrCode == 'isStrategicCustomer' && attrList[i].attrValue) {
                  let sVipTypeTemp = this.judgeTypeList.find((item) => {
                    return item.id == attrList[i].attrValue
                  })
                  this.sVipType = sVipTypeTemp ? sVipTypeTemp :this.sVipType
                }
                //是否直管客户
                if (attrList[i].attrCode == 'ISVERTICALCUST' && attrList[i].attrValue) {
                  let isUpCustomTypeTemp = this.judgeTypeList.find((item) => {
                    return item.label == attrList[i].attrValue
                  })
                  this.isUpCustomType = isUpCustomTypeTemp ? isUpCustomTypeTemp : this.isUpCustomType
                }
                //是否省管客户
                if (attrList[i].attrCode == 'ISPROVINCECUST' && attrList[i].attrValue) {
                  let isProCustomTypeTemp = this.judgeTypeList.find((item) => {
                    return item.label == attrList[i].attrValue
                  })
                  this.isProCustomType = isProCustomTypeTemp ? isProCustomTypeTemp : this.isProCustomType
                }
                //场所类别
                if (attrList[i].attrCode == 'clusteringScenario' && attrList[i].attrValue) {
                  let clusteringScenarioTemp = this.clusteringScenarioList.find((item) => {
                    return item.id == attrList[i].attrValue
                  })
                  this.clusteringScenarioType = clusteringScenarioTemp ? clusteringScenarioTemp : this.clusteringScenarioType
                }
                //细分场景
                if (attrList[i].attrCode == 'clusteringScenarioDetail' && attrList[i].attrValue) {
                  let clusteringTypeDetailTemp = this.busCustSubList.find((item) => {
                    return item.id == attrList[i].attrValue
                  })
                  groupInfo.clusteringScenarioDetailType = attrList[i].attrValue
                }

                //细分客群类别
                if (attrList[i].attrCode == 'BusinessCustScenarioSub' && attrList[i].attrValue) {
                  let busCustSubTemp = this.busCustSubList.find((item) => {
                    return item.id == attrList[i].attrValue
                  })
                  this.busCustSub = busCustSubTemp ? busCustSubTemp : this.busCustSub
                }

                //特色客群（自定义）
                if (attrList[i].attrCode == 'BusinessCustScenarioUserDef' && attrList[i].attrValue) {
                  this.busCustSubDefine = attrList[i].attrValue
                }
              }
            }
            this.industryTemp.isParse = true;
            this.industryTemp.vocaionkind2 = groupInfo.vocaionkind2 ? groupInfo.vocaionkind2 : '';
            this.industryTemp.vocaionkind3 = groupInfo.vocaionkind3 ? groupInfo.vocaionkind3 : '';
            this.industryTemp.vocaionkind4 = groupInfo.vocaionkind4 ? groupInfo.vocaionkind4 : '';
            this.industryTemp.clusteringScenarioDetailType = groupInfo.clusteringScenarioDetailType ? groupInfo.clusteringScenarioDetailType : '';
            this.getBroadbandAddress();

        },
        //根据服务号码判断是否存在虚拟集团，若不存在提示创建
        judgeIsExistGroup(){
            let url = `/xsb/personBusiness/yiwtongUpgrade/h5QryVirtualGroupInfo?serviceNumber=${this.telNum}`;
               this.$http.get(url).then(res =>{
                let retCode = res.data.retCode;
                if(retCode == '0'){
                    //进入补录流程
                    this.getVirtualGroupInfo(false);
                }else{
                    // 使用新的带防抖和加载状态的弹框
                    this.showCreateVirtualGroupDialog();
                }
            }).catch(res =>{
                this.$alert('查询虚拟集团信息网络异常:'+ res);
            });
        },
        //查询历史集团补录列表
        searchHistoryList(){
            this.historyDataList = [];
            //根据集团编号或者服务号码查询历史补录
            let url =  `/xsb/personBusiness/groupDataSupple/h5GetHistoryGroupDataSuppleList?searchContent=${this.searchContent}&&searchType=${this.qrType}`;
            this.$http.get(url).then(res => {
			    let {retCode,retMsg,data} = res.data;
                if(retCode == '0'){
                    this.historyDataList = data;
                    this.showListFlag = true;
                }else{
                    this.$alert(retMsg||'虚拟集团创建失败');
                }
            }).catch(res =>{
                this.$alert('查询历史补录列表网络异常:'+ res);
            });
        },
        //每点击一次tab或搜索需清数据/初始化数据
        async clearDetailData(){
            this.uinfo = Storage.session.get('userInfo');
            this.longitude = Storage.get('longitude'); //经度
            this.latitude = Storage.get('latitude'); //纬度
            this.location = Storage.get('location');//办公地址
            this.certificateType = {id:'-99',label:'请选择'};
            this.certImg = '';
            this.certificateName = '';
            this.legalPerson = '';
            this.groupOrgType = {id:'-99',label:'请选择'};
            this.categoryBigType = {id:'-99',label:'请选择',son:[]};
            this.categorySmallType = {id:'-99',label:'请选择'};
            this.peopleNum = '';
            this.yearAmount = '1';
            this.remark = '';
            this.certificateNo = '';
            this.showGroupCertId = ''
            this.certificateAddress = '';
            this.certificateFlag = false;
            this.idCardName = '';
            this.showIdCardName = '';
            this.linkerNumber = '';
            this.idCardNo = '';
            this.showIdCardNo = '';
            this.idCardAddress = '';
            this.idCardFlag = false;
            this.otherInfoFlag = false;
            this.groupSize = {id:'3',label:'小微型'};
            this.cutsomLevelType = {id:'-99',label:'请选择'};
            this.serviceLevelType = {id:'4',label:'铜牌级'};
            this.email = '';
            this.groupTel = '';
            this.postCode = '000000';
            this.groupServiceLevelType = {id:'1',label:'普通'};
            this.slaInfoType = {id:'0',label:'普通级'};
            this.groupCutsomLevel = {id:'D',label:'D'};
            this.isStopPhoneType = {id:'-99',label:'请选择'};
            this.groupPicObj = {src: '',has: false,name: ''};
            this.channelVal = [];
            //客户发展工号默认操作员
            this.depNumber = this.uinfo.crmId;
            //主动服务工号默认操作员
            this.serviceNumber = this.uinfo.crmId;
            //默认选中身份证类型
            this.idCardType = this.idCardTypeList[0];
            this.procInstId = '';
            this.isAddTable = true;
            this.isShowPicture = false;
            this.imgRef = '';//集团证件流
            this.isExistPicture = false;
            this.broadbandAddress = {id:'-99',label:'请选择'};
            this.broadbandAddressList = [];
            this.entType = {id:'-99',label:'请选择'};
            this.regManaDepartment = {id:'-99',label:'请选择'};
            this.registeredProvince = {id:'320000',label:'江苏省'};
            this.registeredCity = {id:'-99',label:'请选择'};
            this.registeredCountry = {id:'-99',label:'请选择'};
            this.actAddr = '';
            this.dataSuppleStatus='';
            this.busCustSub = { id: '', label: '请选择' }//细分客群类别
            this.busCustSubDefine = '' //特色客群（自定义）
            this.shopContactPerson = ''
            this.getGroupModifyDateStr();
            await this.getDynamicFieldCfg();
            return await this.getInitData();
        },

      //查询后台配置的可修改集团类型的天数
      getGroupModifyDateStr(){
        let url = `/xsb/personBusiness/groupArchives/h5QueryConfigDay`
        this.$http
          .get(url)
          .then((res) => {
            if (res.data.retCode == '0') {
              this.groupModifyDateStr = res.data.data
            }
          })
          .catch((err) => {
            console.log(err)
          })
      },
        //将补录表的大字段解析到页面上
        async showGroupData(reqContent){
            let reqJson = JSON.parse(reqContent);
            this.grpBaseInfo = JSON.parse(reqJson.grpBaseInfo);
            this.grpDevChannel = JSON.parse(reqJson.grpDevChannel);
            this.grpExpandInfo = JSON.parse(reqJson.grpExpandInfo);
            this.grpOrgLevel = JSON.parse(reqJson.grpOrgLevel);
            this.grpRelPerson = JSON.parse(reqJson.grpRelPerson);
            this.grpRelation = JSON.parse(reqJson.grpRelation);
            this.remark = reqJson.rmk;
            this.isDoubleAdress = this.grpExpandInfo.isDoubleAdress;
            this.procInstId = reqJson.procInstId;
            let that = this;
            if(this.grpBaseInfo.custName){
                this.certificateName = this.grpBaseInfo.custName;
            }
            //将上传obs照片名称解析给certImg，重新补录无需再上传照片
            if(this.grpBaseInfo.grpCertificate){
                this.certImg = this.grpBaseInfo.grpCertificate;
            }
            if(this.grpBaseInfo.cityId){
                this.cityType =  this.cityList.find(function (item) {
                                return  item.id == that.grpBaseInfo.cityId;
                            })
                this.districtTypeList = this.cityType.son;
            }
            if(this.grpBaseInfo.countyId){
                this.districtType = this.districtTypeList.find(function (item) {
                                return  item.id == that.grpBaseInfo.countyId;
                            })
            }
            let gridTypeTemp;
            if(this.grpBaseInfo.channelPlatCounty){
                gridTypeTemp = this.gridTypeList.find(function (item) {
                                return  item.id == that.grpBaseInfo.channelPlatCounty;
                            })
            }
            this.gridType = typeof gridTypeTemp == "undefined" ? this.gridType : gridTypeTemp;
            await this.qrychildGridList(this.gridType.id, "3");
            let areaTypeTemp;
            if(this.grpBaseInfo.channelPlatArea){
                areaTypeTemp = this.areaTypeList.find(function (item) {
                                return  item.id == that.grpBaseInfo.channelPlatArea;
                            })
            }
            this.areaType = typeof areaTypeTemp == "undefined" ? this.areaType : areaTypeTemp;

            if(this.grpBaseInfo.certType){
                this.certificateType = this.certificateTypeList.find(function (item) {
                                return  item.label == that.grpBaseInfo.certType;
                            })
            }
            if(this.grpBaseInfo.certId){
                this.certificateNo = this.grpBaseInfo.certId;
            }
            if(this.grpBaseInfo.certAddress){
                this.certificateAddress = this.grpBaseInfo.certAddress;
            }
            if(this.grpBaseInfo.legal){
                this.legalPerson = this.grpBaseInfo.legal;
            }
            if( this.grpBaseInfo.longitudeLatude){
                this.longitude = this.grpBaseInfo.longitudeLatude.split(',')[0];
                this.latitude = this.grpBaseInfo.longitudeLatude.split(',')[1];
            }
            if(this.grpBaseInfo.address){
                this.location = this.grpBaseInfo.address;
            }
            if(this.grpBaseInfo.postCode){
                this.postCode = this.grpBaseInfo.postCode;
            }
            if(this.grpBaseInfo.contactPhone){
                this.groupTel = this.grpBaseInfo.contactPhone;
            }
            if(this.grpBaseInfo.industryLevel1){
                this.categoryBigType = this.categoryBigTypeList.find(function (item) {
                                return  item.label == that.grpBaseInfo.industryLevel1;
                            });
            }else if(this.grpBaseInfo.jkdtindustryLevel1){
                this.categoryBigType = this.categoryBigTypeList.find(function (item) {
                                return  item.label == that.grpBaseInfo.jkdtindustryLevel1;
                            });
            }
            if(this.grpBaseInfo.isStrategicCustome){
                this.sVipType = this.judgeTypeList.find(function (item) {
                                return  item.id == that.grpBaseInfo.isStrategicCustomer;
                            });
            }
            if(this.grpBaseInfo.annualTurnover){
                this.yearAmount = this.grpBaseInfo.annualTurnover;
            }

            if(this.grpBaseInfo.custLevel){
                this.channelVal = [];
                let channelIdList = this.grpBaseInfo.custLevel.split(',');
                channelIdList.filter((item,index) => {
                    let obj = this.channelList.find(function (item1) {
                                return  item1.id == item
                    });
                    this.channelVal.push(obj.id);
                });
            }
            if(this.grpBaseInfo.groupType){
                this.groupOrgType = this.groupOrgTypeList.find(function (item) {
                                return  item.label == that.grpBaseInfo.groupType;
                            });
            }
            if(this.grpBaseInfo.locationType){
                this.groupType = this.groupTypeList.find(function (item) {
                                return  item.label == that.grpBaseInfo.locationType;
                            });
            }
            if(this.grpOrgLevel.grpOrgLevel){
                this.groupLevelType = this.groupLevelTypeList.find(function (item) {
                                return  item.id == that.grpOrgLevel.grpOrgLevel;
                            });
            }
            if(this.grpRelation.creditLevel){
                this.creditLevel = this.creditLevelList.find(function (item) {
                                return  item.label == that.grpRelation.creditLevel;
                            });
            }
            if(this.grpRelation.corpScale){
                this.groupSize = this.groupSizeList.find(function (item) {
                                return  item.label == that.grpRelation.corpScale;
                            });
            }
            if(this.grpRelation.employeeNum){
                this.peopleNum = this.grpRelation.employeeNum;
            }
            if(this.grpRelation.customerLevel){
                this.cutsomLevelType = this.cutsomLevelTypeList.find(function (item) {
                                return  item.id == that.grpRelation.customerLevel;
                            });
            }
            if(this.grpRelation.serviceLevel){
                this.serviceLevelType = this.serviceLevelTypeList.find(function (item) {
                        return  item.id == that.grpRelation.serviceLevel;
                });
            }

            if(this.grpDevChannel.developOrgId){
                this.depWayType = this.depWayTypeList.find(function (item) {
                                return  item.label == that.grpDevChannel.developOrgId;
                            });
            }
            if(this.developMan){
                this.depNumber = this.developMan;
            }
            if(this.grpDevChannel.masterChannelType){
                this.serviceWayType = this.servicepWayTypeList.find(function (item) {
                                return  item.label == that.grpDevChannel.masterChannelType;
                            });
            }
            if(this.grpDevChannel.masterCustMgr){
                this.serviceNumber = this.grpDevChannel.masterCustMgr;
            }
            if(this.grpRelPerson.concPrsnName){
                this.idCardName = '';
                this.showIdCardName = this.grpRelPerson.concPrsnName;
            }
            if(this.grpRelPerson.concPrsnTelnum){
                this.linkerNumber = this.grpRelPerson.concPrsnTelnum;
            }
            if(this.grpRelPerson.memberRoleType){
                this.serviceRoleType = this.servicepRoleTypeList.find(function (item) {
                                return  item.id == that.grpRelPerson.memberRoleType;
                            });
            }
            if(this.grpRelPerson.concCredTypeCd){
                this.idCardType = this.idCardTypeList.find(function (item) {
                                return  item.label == that.grpRelPerson.concCredTypeCd;
                            });
            }
            if(this.grpRelPerson.concIdno){
                this.idCardNo = this.grpRelPerson.concIdno;
            }
            if(this.grpRelPerson.postcode){
                this.email = this.grpRelPerson.postcode;
            }
            if(this.grpExpandInfo.ifCanStopUser){
                this.isStopPhoneType = this.isStopPhoneList.find(function (item) {
                                return  item.label == that.grpExpandInfo.ifCanStopUser;
                            });
            }
            if(this.grpExpandInfo.custServiceClass){
                this.groupServiceLevelType = this.groupServiceLevelTypeList.find(function (item) {
                                return  item.id == that.grpExpandInfo.custServiceClass;
                            });
            }
            if(this.grpExpandInfo.isOrg){
                this.isUpCustomType = this.judgeTypeList.find(function (item) {
                                return  item.label == that.grpExpandInfo.isOrg;
                            });
            }
            if(this.grpExpandInfo.isProvinceOrg){
                this.isProCustomType = this.judgeTypeList.find(function (item) {
                                return  item.label == that.grpExpandInfo.isProvinceOrg;
                            });
            }
            if(this.grpExpandInfo.GroupLevel2016){
                this.groupCutsomLevel = this.groupCutsomLevelList.find(function (item) {
                                return  item.id == that.grpExpandInfo.GroupLevel2016;
                            });
            }
            if(this.grpExpandInfo.sla){
                this.slaInfoType = this.slaInfoTypeList.find(function (item) {
                                return  item.label == that.grpExpandInfo.sla;
                            });
            }
            if(this.grpExpandInfo.isReChargeNotify){
                this.isShortMessType = this.judgeTypeList.find(function (item) {
                                return  item.label == that.grpExpandInfo.isReChargeNotify;
                            });
            }
            if(reqJson.broadbandAddress){
                this.broadbandAddress = this.broadbandAddressList.find(function (item) {
                                return  item.label == reqJson.broadbandAddress;
                            });
            }
            if(typeof this.broadbandAddress  == 'undefined'){
                this.broadbandAddress = {id:'-99',label:'请选择'};
            }
            //登记管理部门
            if(this.grpExpandInfo.regManaDepartment){
                this.regManaDepartment = this.regManaDepartmentList.find(function (item) {
                                return  item.label == that.grpExpandInfo.regManaDepartment;
                            });
            }
            //企业类型
            if(this.grpBaseInfo.corpType){
                this.entType = this.entTypeList.find(function (item) {
                                return  item.id == that.grpBaseInfo.corpType;
                            });
            }
            //注册省份
            if(this.grpExpandInfo.busiCompanyID){
                this.registeredProvince = this.registeredProvinceList.find(function (item) {
                                return  item.id == that.grpExpandInfo.busiCompanyID;
                });
            }
            //注册地市
            if(this.grpExpandInfo.busiLocation){
                this.registeredCity = this.registeredCityList.find(function (item) {
                                return  item.id == that.grpExpandInfo.busiLocation;
                            });
            }
            if(reqJson.actAddr){
                this.actAddr = reqJson.actAddr;
            }
            this.industryTemp.isParse = true;
            if(this.grpBaseInfo.industryLevel2){
                this.industryTemp.vocaionkind2 = this.grpBaseInfo.industryLevel2
            }else if(this.grpBaseInfo.jkdtindustryLevel2){
                this.industryTemp.vocaionkind2 = this.grpBaseInfo.jkdtindustryLevel2
            }
            this.industryTemp.vocaionkind3 = this.grpBaseInfo.industryLevel3 ? this.grpBaseInfo.industryLevel3 : '';
            this.industryTemp.vocaionkind4 = this.grpBaseInfo.industryLevel4 ? this.grpBaseInfo.industryLevel4 : '';
            setTimeout(function () {
                //注册区县
                if(that.grpExpandInfo.busiCountyID){
                    that.registeredCountry = that.registeredCountryList.find(function (item) {
                                return  item.id == that.grpExpandInfo.busiCountyID;
                            });
                }
            },500);
            if(this.grpExpandInfo.attrInfos){
                this.grpExpandInfo.attrInfos.forEach(attrInfo => {
                    this.dynamicFieldModule.forEach((attrConf,index) => {
                        if(attrInfo.attrCode == attrConf.attrId){
                            if(attrConf.commitType == '1'){
                                attrConf.defaultValue = attrConf.optionList.find(function (item) {
                                return  item.id == attrInfo.attrValue
                                });
                            }else if(attrConf.commitType == '2'){
                                attrConf.defaultValue = attrConf.optionList.find(function (item) {
                                return  item.label == attrInfo.attrValue
                                });
                            }else{
                                attrConf.defaultValue = attrInfo.attrValue
                            }
                            this.$set(this.dynamicFieldModule,index,attrConf)
                        }
                    })
                });
            }

        },

        //历史列表选择一条查看
        async chooseOne(item){
            //先将补录页面数据清掉并初始化
            await this.clearDetailData();
            let url = `/xsb/personBusiness/groupDataSupple/h5GetGroupDataSuppleInfo?groupNumber=${item.groupNumber}`;
            this.$http.get(url).then(res => {
			    let {retCode,retMsg,data} = res.data;
                if(retCode == '0' && data != null){
                    let message = '';
                    let confirmText = '确定';
                    let title = '温馨提示';
                    if(data.status == '0'){
                        message = '补录信息未审核，是否进行重新补录';
                    }else if(data.status == '2' && data.failReason != null && data.failReason != ''){
                        message = data.failReason;
                        title = '审核不通过原因';
                        confirmText = '重新补录';
                    }else if(data.status == '2'){
                        message = '补录信息审核不通过，是否进行重新补录';
                    }else{
                        message = '已补录，是否查看补录信息';
                    }
                    this.$messagebox({
                        title: title,
                        message: message,
                        showCancelButton: true,
                        showConfirmButton: true,
                        confirmButtonText: confirmText,
                        cancelButtonText: "取消"
                    }).then(action => {
                        if (action == 'confirm') {
                            this.showHistoryData(data);
                        }else{
                            //不进行其他操作
                            return;
                        }
                    });
                }else{
                    this.$alert(retMsg || "查询补录资料失败")
                }
            }).catch(res =>{
                this.$alert('查询补录信息网络异常:'+ res);
            });
        },
        async showHistoryData(data){
            this.tab = '1';
            this.showListFlag = false;
            this.showDetailflag = true;
            this.dataSuppleStatus = data.status;
            this.groupId = data.groupNumber;
            this.telNum = data.telNumber;
            this.zkType = '1';
            await this.getBroadbandAddress();
            this.getGroupDetailInfo(this.groupId);
            let reqJson = JSON.parse(data.reqContent);
            let grpBaseInfo = JSON.parse(reqJson.grpBaseInfo);
            this.certImg = grpBaseInfo.grpCertificate ? grpBaseInfo.grpCertificate : this.certImg;
            this.procInstId = reqJson.procInstId;
            // this.showGroupData(data.reqContent);
        },
        //根据操作员输入的服务号码搜索关联人姓名，回显到页面的关联人姓名和法人字段
        getUserInfo(){
            this.linkerNumber = this.telNum;
            let url = `/xsb/personBusiness/groupDataSupple/h5GetPersonalInfoByTel?telNumber=${this.telNum}`;
            this.$http.get(url).then(res => {
                let data = res.data.data;
                if(data != null && data != ''){
                    this.idCardName = '';
                    this.showIdCardName = data;
                    this.legalPerson = data;
                }
            }).catch(res =>{
                this.$alert('查询关联人姓名网络异常:'+ res);
            });
        },
        //补资料信息同步给瑞月
        syncGroupCust(){
            let uinfo = Storage.session.get('userInfo');
            let judgeNew = true;
            //审核状态为0——未审核，为重新补录
            if(this.dataSuppleStatus == '0'){
                judgeNew = false;
            }
            //补录信息数据没有procInstId，当作新补录，可能出现(审核状态为未审核，但没有工单编号)
            if(typeof this.procInstId == 'undefined'){
                judgeNew = true;
            }
            let syncParam = {
                countyId : this.gridType.id,
                wgId : this.areaType.id,
                custId : this.groupId,
                custName : this.certificateName,
                officeAddress : this.location,
                mtFileName : this.certImg,//集团证件obs名称
                createMobile : uinfo.servNumber,//操作员手机号
                createName : uinfo.operatorName,//操作员姓名
                operType : judgeNew ? '1': '2' ,//若审核状态为未审核，操作类型传2——修改
                procInstId : judgeNew ? '' : this.procInstId,//工单编号
                kdAddress : this.broadbandAddress.label || '无',//宽带安装地址
                longitude : this.longitude,//经度
                latitude : this.latitude,//纬度
            }
            let url = `/xsb/personBusiness/groupDataSupple/h5SyncGroupCust`;
            return this.$http.post(url,syncParam).then(res => {
                let {retCode,retMsg,data} = res.data;
                if(retCode == '0'){
                    this.isAddTable = true;
                    if(data != '' && data != null){
                        this.procInstId = data;
                    }
                }else{
                    this.isAddTable = false;
                    this.$alert(retMsg || "集团补资料同步推送失败,请重试")
                }
            }).catch(res =>{
                this.$alert('调用集团补资料同步接口网络异常:'+ res);
            });
        },
        chooseQueryType(){
              NlDropdown({
                confirmBtn: false,
                datalist: this.qrTypeList,
              }, (retVal) => {
                this.searchContent = '';
                this.qrType = retVal.id;
              });
        },
        closePicture(){
            this.isShowPicture = false;
        },

        downPicture(){
            if(this.imgRef == '' && this.dataSuppleStatus == ''){
                this.$alert("未上传照片");
                return;
            }else if(this.imgRef == '' && this.dataSuppleStatus != ''){
                let url = `/xsb/personBusiness/groupArchives/h5DownloadObsCard?key=${this.certImg}`;
                this.$http.get(url).then(res => {
                    let {retCode,data} = res.data;
                    if(retCode == '0'){
                        this.imgRef = "data:image/jpeg;base64," + data;
                    }
                });
            }
            this.isShowPicture = true;
        },
        getBroadbandAddress(){
            let url = `/xsb/personBusiness/groupDataSupple/h5GetBroadbandAddress?telNum=${this.telNum}`;
            return this.$http.get(url).then(res => {
                let addressMap = res.data.data;
                let groupAddress = {id:'1',label:addressMap.groupAddress};
                let selfAddress = {id:'2',label:addressMap.selfAddress};
                if(groupAddress.label){
                    this.broadbandAddressList.push(groupAddress);
                }
                if(selfAddress.label){
                    this.broadbandAddressList.push(selfAddress);
                }
                if(this.broadbandAddressList.length == 0){
                    this.broadbandAddressList.push({id:'0',label:'无'})
                }
                if(this.isAgain){
                    this.broadbandAddress = this.broadbandAddressList[0];
                }
            }).catch(res =>{
                this.$alert('查询宽带地址异常:'+ res);
            });
        },
        isScenarioDetail(){
          if(['7'].includes(this.clusteringScenarioType.id)){
            return false;
          }else{
            return true;
          }
        },
        //验证通过返回的集团客户信息回显页面,对需要匹配字段的字段都特殊处理
        showVerData(data){
            if(data == null){
                return;
            }
            let categoryBigTypeTemp;
            if(data.industry){
                categoryBigTypeTemp = this.categoryBigTypeList.find(function (item) {
                                return  item.id == data.industry;
                            });
            }
            this.categoryBigType = typeof categoryBigTypeTemp == "undefined" ? this.categoryBigType : categoryBigTypeTemp;
            //企业类型对应不上自己下拉框选择
            if(data.entType){
                this.entType = this.entTypeList.find(function (item) {
                                return  item.label == data.entType;
                            });
            }
            this.entType = typeof this.entType == "undefined" ? {id:"-99",label:"请选择"} : this.entType;
            //登记管理部门，查不到就下拉框选取
            if(data.regManaDepartment){
                 this.regManaDepartment = this.regManaDepartmentList.find(function (item) {
                                return  item.label == data.regManaDepartment;
                            });
            }
            this.regManaDepartment = typeof this.regManaDepartment == "undefined" ? {id:"-99",label:"请选择"} :  this.regManaDepartment;
            if(data.entInCome){
                this.yearAmount = data.entInCome;
            }
            if(data.actAddr){
                this.actAddr = data.actAddr;
            }
            if(data.tel){
                this.groupTel = data.tel;
            }
            if(data.email){
                this.email = data.email;
            }
            if(data.empNum){
                this.peopleNum = data.empNum;
            }
            let registeredProvinceTemp;
            //注册省份
            if(data.registeredProvince){
                registeredProvinceTemp = this.registeredProvinceList.find(function (item) {
                                return  item.label == data.registeredProvince;
                            });
            }
            this.registeredProvince = typeof registeredProvinceTemp == "undefined" ? {id:"-99",label:"请选择"} :  registeredProvinceTemp;

            let registeredCityTemp;
            //注册地市
            if(data.registeredCity){
                registeredCityTemp = this.registeredCityList.find(function (item) {
                                return  item.label == data.registeredCity;
                            });
            }
            this.registeredCity = typeof registeredCityTemp == "undefined" ? {id:"-99",label:"请选择"} :  registeredCityTemp;

            let that = this;
            let categorySmallTypeTemp;
            let registeredCountryTemp;
            setTimeout(function () {
               if(data.industrySub){
                    categorySmallTypeTemp = that.categorySmallTypeList.find(function (item) {
                                return  item.remark == data.industrySub;
                            });
                }
                if(data.registeredCountry){
                    registeredCountryTemp = that.registeredCountryList.find(function (item) {
                                return  item.label == data.registeredCountry;
                            });
                }
                that.categorySmallType = typeof categorySmallTypeTemp == "undefined" ? that.categorySmallType : categorySmallTypeTemp;
                that.registeredCountry = typeof registeredCountryTemp == "undefined" ? that.registeredCountry : registeredCountryTemp;
            },500)
        },
        getDynamicFieldCfg(){
          let param = {
            unLoadFlg : true
          }
          let url = `/xsb/personBusiness/groupArchives/h5GetDynamicFieldCfg?businessType=3`;
          return this.$http.get(url,param).then(res =>{
            let {retCode,retMsg,data} = res.data;
            if(retCode == '0'){
              data=data.filter(attr => attr.attrId !== 'clusteringScenario');
              data=data.filter(attr => attr.attrId !== 'BusinessCustScenarioSub');
              this.parseOptionValue(data);
                this.dynamicFieldModule = data;
            } else {
              this.$alert(retMsg || '获取动态字段配置信息失败');
            }
          }).catch(res =>{
            this.$alert('获取动态字段配置信息异常:'+ res);
          });
        },
        parseOptionValue(dataList){
            dataList.forEach(attr => {
                if(attr.attrType === '2'){
                    let defaultObj = attr.defaultValue.split('-') || ["-99","请选择"];
                    attr.defaultValue = {id : defaultObj[0],label : defaultObj[1]};
                  attrChange(attr.attrId,attr,this.groupFilingType);
                    let optionList = [];
                    attr.optionValue.split('|').forEach((option,index) =>{
                        let optionObj = option.split('-');
                        optionList[index] = {id: optionObj[0], label: optionObj[1]};
                    });
                    attr.optionList = optionList;

                }
            });
        },
        checkNull(attrList){
            let check = {};
            attrList.forEach(attr => {
                if(attr.isNeed === '1' && (attr.defaultValue === '' || attr.defaultValue === null)){
                    check.flag = true;
                    check.attrName = attr.attrName;
                }
            });
            return check;
        },
        //根据行业类别2编码查询行业3、4
      getGroupIndustryList(){
        let url = `/xsb/personBusiness/groupArchives/h5GetGroupIndustryList?industryCode=${this.categorySmallType.id}`;
        return this.$http.get(url).then(res =>{
          if(res.data.retCode == '0'){
            this.industryThirdList = res.data.data
          } else {
            this.$alert(res.data.retMsg || '查询行业3失败');
          }
        }).catch(res =>{
          this.$alert('查询行业3异常:'+ res);
        });
      },
      //点击选择
      showsChoose(){
        NlDropdown({
          confirmBtn: false,
          datalist: this.zkTypeList,
        }, (retVal) => {
          this.telNum = '';
          this.zkType = retVal.id;
        });
      },
      deles(){
        this.telNum = '';
      }
    },
    mounted(){
        //客户端定位回调
        window["getAutoLocation"] = result => {
            Indicator.close();
            let locationParam = result;
            this.longitude = locationParam.longitude; //经度
            this.latitude = locationParam.latitude; //纬度
            this.location = locationParam.address;//办公地址
            this.actAddr = locationParam.address;
            // this.certificateAddress = locationParam.address; //位置信息
        };
        //委托书回调
        window["authPaizhaoCb"] = info => {
            let imgs = "data:image/jpeg;base64," + info.fileImage;
            let param = {
                "photoStr": info.fileImage, //照片流
                "type": "03", //集团照01,身份证02,委托书03
                "certId": this.showIdCardNo || this.idCardNo, //证件号 委托书证件号填身份证号
                "unEncrpt": "true"
            };
            this.groupPicObj.src = imgs;
            //上传照片
            this.uploadImg(param);
        };
        //证件照、身份证回调
        window["certPaizhaoCb"] = info => {
            let param = {};
            param.unEncrpt = true;
            param.image = info.fileImage;
            let index = info.fileName .lastIndexOf("\.");
            let fileType  = info.fileName.substring(index, info.fileName.length);
            param.fileType = fileType;
            if('1'==this.pzType) { //集团证件照片
                param.license = this.certificateType.id;
                this.imgRef = "data:image/jpeg;base64,"+info.fileImage;
            } else if('2'== this.pzType) { //身份证照片
                param.license = 'IdCard';
            }
            //先识别ORC
            this.identificOrc(param);
        };
    },
    watch:{
        //场景类型修改的时候获取细分场景类型
        clusteringScenarioType(){
          this.clusteringScenarioDetailType = { id:'-99',label:'请选择'};
          this.clusteringScenarioDetailList=this.clusteringScenarioType.son;
          //判断是否需要从接口返回数据中解析行业小类
          if(this.industryTemp.isParse && this.industryTemp.clusteringScenarioDetailType){
            let clusteringScenarioDetailTemp = this.clusteringScenarioDetailList.find(item=> {
              return item.id == this.industryTemp.clusteringScenarioDetailType;
            });
            this.clusteringScenarioDetailType = typeof clusteringScenarioDetailTemp == 'undefined' ? this.clusteringScenarioDetailType : clusteringScenarioDetailTemp;
          }
        },
        //行业类型大类修改的时候  获取行业类型小类列表
        categoryBigType(){
            this.categorySmallType = { id:'-99',label:'请选择'};
            this.categorySmallTypeList = this.categoryBigType.son;
            let that = this;
            //判断是否需要从接口返回数据中解析行业小类
            if(this.industryTemp.isParse && this.industryTemp.vocaionkind2){
                let categorySmallTypeTemp = this.categorySmallTypeList.find(function (item) {
                            return item.id == that.industryTemp.vocaionkind2;
                        });
                if(typeof categorySmallTypeTemp == 'undefined'){
                    categorySmallTypeTemp = this.categorySmallTypeList.find(function (item) {
                            return item.label == that.industryTemp.vocaionkind2;
                        });
                }
                this.categorySmallType = typeof categorySmallTypeTemp == 'undefined' ? this.categorySmallType : categorySmallTypeTemp;

            }
        },
        async categorySmallType(){
            this.industryThird = { id:'-99',label:'请选择'};
            this.industryThirdList = [];
            let that = this;
            if(this.categorySmallType.id != '-99'){
                await this.getGroupIndustryList();
                 if(this.industryTemp.isParse && this.industryThirdList.length > 0 && this.industryTemp.vocaionkind3){
                     let industryThirdTemp;
                     industryThirdTemp =  that.industryThirdList.find(function (item) {
                                return  item.id == that.industryTemp.vocaionkind3;
                     });
                    this.industryThird = typeof industryThirdTemp == 'undefined' ? this.industryThird : industryThirdTemp;
                }else{
                    this.industryTemp.isParse = false;
                }
            }
        },
        industryThird(){
            let that = this;
            this.industryFourth = { id:'-99',label:'请选择'};
            this.industryFourthList = this.industryThird.son;
            if(typeof this.industryFourthList == 'undefined' || this.industryFourthList.length == 0){
                return;
            }
            if(this.industryTemp.isParse && this.industryTemp.vocaionkind4){
                let industryFourthTemp;
                industryFourthTemp = this.industryFourthList.find(function (item) {
                            return item.id == that.industryTemp.vocaionkind4;
                        });
                this.industryFourth = typeof industryFourthTemp == 'undefined' ? this.industryFourth : industryFourthTemp;
                this.industryTemp.isParse = false;
            }
        },
        registeredCity(){
            this.registeredCountry = { id:'-99',label:'请选择'};
            if (this.registeredCity.son && this.registeredCity.son.length > 0){
                //对son判空
                this.registeredCountryList = this.registeredCity.son;
                this.registeredCountry = this.registeredCountryList[0];
            }
        },
        certImg(){
            if(this.certImg != null && this.certImg != ''){
                this.isExistPicture = true;
            }else{
                this.isExistPicture = false;
            }
        }
    },
    filters: {},
    created() {
        this.type = this.$route.query.type;
        this.tab = '1';
        this.uinfo = Storage.session.get('userInfo');
        this.longitude = Storage.get('longitude'); //经度
        this.latitude = Storage.get('latitude'); //纬度
        this.location = Storage.get('location'); //位置信息
    },
    beforeDestroy() {
        // 组件销毁时的清理工作
    }
};
</script>
<style lang='less' scoped>

  .placeholder::-webkit-input-placeholder{
    font-size:12px
  }

.wrapper {
    position: absolute;
    top: 0px;
    left: 0px;
    right: 0px;
    bottom: 0px;
    z-index: 10;
    background: #fff;
    overflow:auto;
}
.mainWrapper{
    height: auto;
    margin-top: 157px;
    overflow: hidden;
    border-top: 1px solid #EAEAEA;
    margin-bottom: 80px;
    .container{
        padding: 12px 12px 12px 12px;
        margin: 12px;
        box-shadow: 0px 0px 8px 0px rgba(167, 167, 167, 0.23);
        border-radius: 8px;
        .section-title{
            display: flex;
            overflow: hidden;
            .blue-line{
                flex: 0 0 6px;
                height: 19px;
                background: #007AFF;
                float: left;
                margin-right: 7px;
            }
            .son-title{
                flex: 1;
                font-size: 16px;
                font-weight: 600;
                color: #434343;
                line-height: 19px;
            }
            .icon-detail{
                flex: 0 0 8px;
                color: #8B8B8B;
            }
        }
        .with-line{
            border-bottom: 1px solid #F0F0F0;
                textarea::placeholder{
                color:#a8a6ab;
                font-size: 13px;
            }
        }
        .box{
            padding: 11px 0px;
            overflow: hidden;
            display: flex;
            .txt{
                flex: 0 0 auto;
                font-size: 14px;
                color: #949494;
                align-self: center;
                text-align: end;
                letter-spacing: 1px;
            }
            .item-choose{
                flex: 1;
                font-size: 14px;
                color: #4E4E4E;
                text-align: end;
                margin-right: 2px;
                align-self: center;
                letter-spacing: 1px;
            }
            .more-txt{
                flex: 0 0 68px;
                color: #4E4E4E;
                align-self: center;
                text-align: center;
                font-size: 14px;
                letter-spacing: 1px;
            }
            .icon-more{
                flex: 0 0 auto;
                color: #8B8B8B;
                align-self: center;
                text-align: center;
                font-size: 17px;
            }
            .icon-sure{
                flex: 0 0 12px;
                color: #8B8B8B;
                align-self: center;
                font-size: 10px;
                display: inline-block;
                transform: scale(0.8);
            }
            .icon-chakan{
                color: #8B8B8B;
                line-height: 25px;
                display: inline-block;
                transform: scale(0.8);
                padding-left: 6px;
                padding-right: 4px;
            }
            .icon-blue{
                color: #007AFF;
            }
            .distinguish-btn{
                flex: 0 0 94px;
                height: 25px;
                background: #007AFF;
                border-radius: 12px;
                font-size: 12px;
                color: #FFFFFF;
                text-align: center;
                line-height: 25px;
                align-self: center;
                margin-left: 5px;
            }
            .location-btn{
                flex: 0 0 68px;
                height: 25px;
                background: #007AFF;
                border-radius: 12px;
                font-size: 12px;
                color: #FFFFFF;
                text-align: center;
                line-height: 25px;
                align-self: center;
                margin-left: 5px;
            }
            .group-input-content{
                flex: 1;
                white-space: nowrap;
                outline: none;
                text-align: right;
                color: #4E4E4E;
                font-size: 14px;
                box-sizing: border-box;
                letter-spacing: 1px;
                width: 100%;
            }
            .gantanhao-yuankuang{
                color: #ED8325;
                font-size: 16px;
            }
            .installUl-textarea {
                flex-grow: 1;
                flex-shrink: 1;
                text-align: right;
                color: #323232;
                font-size: 14px;
                outline: none;
                border: none;
                resize: none;
                padding: 0;
                line-height: 16px;
            }
            .installUl-textarea::-webkit-input-placeholder {
                color: #BBBBBB;
                font-size: 12px;
            }
        }
        .top-box{
            padding-bottom: 5px;
        }
        .last-box{
            padding-bottom: 0px;
            justify-content: center;
        }
        .use-per-check-list {
            display: flex;
            flex-wrap: wrap;
                li {
                    width: 32%;
                    padding-top: 4px;
                    font-size: 12px;
                    font-weight: 500;
                    color: #858585;
                    line-height: 20px;
                    label {
                        margin-left: 4px;
                    }
                }
        }

    }
}


.trust-camera{
    position:relative;
    margin-top:14px;
    img{
        width:100%;
        height:100%;
    }
    .camera-btn{
        background:#1681FB;
        color:#fff;
        font-size:14px;
        position:absolute;
        left:50%;
        top:50%;
        width:120px;
        padding:8px 0;
        text-align: center;
        border-radius:16px;
        transform: translate(-50%,-50%);
    }
}
.li-textarea{
    margin-top:8px;
    width: 100%;
    display: block;
    outline: none;
    border: 1px solid #E5E5E5;
    box-sizing: border-box;
    font-size: 14px;
    padding: 10px 12px;
    background:#f5f5f5;
    height: 70px;
    resize: none;
    color: #4E4E4E;
    text-align: left !important;
}
.order-tab{
		margin-top:44px;
		display: flex;
		span{
			color:#3D3D3D;
			font-size:14px;
			text-align: center;
			flex:1;
			height:40px;
			line-height: 40px;
			background:#F8F8F8;
			&.active{
				background:#fff;
				color:#1681FB;
				border-bottom:2px solid #187BEC;
				box-sizing: border-box;
			}
		}
	}
	.ma-search {
        height: 55px;
        border-bottom: 1px solid #E2E2E2;
        overflow: hidden;
        background: #fff;
        border-top: 1px solid #E2E2E2;
        flex-shrink: 0;
        position: relative;
    }
    .gl-searchinpt {
        height: 20px;
        line-height: 20px;
        background: none;
        font-size: 12px;
        color: #555;
        border: none;
        width: 100%;
        text-indent: 25px;
        outline: none;
        margin-top: 5px;
    }
    .gm-searchinpt {
        height: 20px;
        line-height: 20px;
        background: none;
        font-size: 12px;
        color: #555;
        border: none;
        text-indent: 15px;
        outline: none;
        margin-top: 5px;
        position: absolute;
        width: 65%;
    }

	.shanchu{
        position:absolute;
        right:3.5rem;
        top: 50%;
        color: #828282;
        transform: translateY(-50%);
        font-size:12px;
    }
    .gl-search-btn {
        position: absolute;
        right: 8px;
        top: 4px;
        font-size: 12px;
        color: #007AFF;
        width: 30px;
        height: 22px;
        line-height: 22px;
    }
    .flex-wrapper{
		display: flex;
		flex-direction: column;
    font-size: 12px;
    position: fixed;
    background-color: #fff;
    width: 100%;
    z-index: 10;
    top: 0px;
	}
    .gl-searchblock {
        margin: 12px 16px 0 16px;
        position: relative;
        height: 30px;
        background: rgba(241, 241, 241, 1);
        border-radius: 16px;
        overflow: hidden;
    }
    .gl-ssicon {
        width: 30px;
        height: 30px;
        display: block;
        position: absolute;
        top: 0;
        left: 0;
        font-size: 16px;
        line-height: 30px;
        color: #828282;
        text-align: center;
        z-index: 5;
    }
    .order-ul{
		flex:1;
		height:100%;
		overflow: auto;
		margin:12px;
        margin-top: 150px;
	}
	.order-li{
		margin-bottom:12px;
		border-radius: 8px;
		padding-top:12px;
		background:#FFFFFF;
		position:relative;
        padding-bottom: 12px;
	}
	.height-auto{
		height: 64px;
    	overflow: hidden;
	}
	.top-content{
		padding:0 12px;
		margin-bottom:6px;
		align-items: center;
		display:flex;
		justify-content:space-between;
		.tel{
			color:#323232;
			font-size:16px;
			font-weight: bold;
		}
		.status{
			width: 50px;
			height: 24px;
			font-size:12px;
			line-height: 24px;
			border-radius: 4px;
			text-align: center;
			box-sizing: border-box;
			&.unpend{
				color:#1680F9;
				border: 1px solid #1680F9;
			}
            &.success{
				color:#2BAA30;
				border: 1px solid #2BAA30;
			}
			&.fail{
				color:#CE8610;
				border: 1px solid #CE8610;
			}
		}
		.c-item{
			display: flex;
			font-size:14px;
			color:#848484;
			i{
				font-style: normal;
				color:#323232;
				display: inline-block;
    			flex: 1;
				line-height: 14px;
			}
		}
        .reason-item{
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            font-style: normal;
            display: inline-block;
            flex: 1;
            line-height: 14px;
            font-size: 14px;
            margin-right: 5px;
            color:red;
		}
		.c-right{
			font-size:12px;
		}
		&:last-child{
			margin-bottom: 0 !important;
		}
	}
	.jiantou{
		font-size:12px;
		color:#979797;
		position:absolute;
		right:10px;
		top:65%;
		transform:translateY(54%) rotate(-90deg)
	}
	.bottom-more{
		border-top:1px solid #F0F0F0;
		box-sizing: border-box;
		text-align: center;
		font-size:12px;
		color:#9B9B9B;
		padding:5px 0;
		margin-top: 12px;
		&.up{
			color:#276CE5;
		}
		.xiangqing{
			display: inline-block;
			color:#9B9B9B;
			transform:rotate(90deg);
			font-size:12px;
			margin-right: 4px;
			&.icon-up{
				transform:rotate(-90deg);
			}
		}
	}
	.no-data{
		margin-top: 150px;
	}
    .no-commit{
        margin-bottom: 0px;
    }
    .no-edit{
        pointer-events: none;
    }
    .number-name{
        font-size: 12px;
        height: 30px;
        line-height: 30px;
        color: #fff;
        padding: 0 5px 0 12px;
        -ms-flex-negative: 0;
        flex-shrink: 0;
        background-color: #1680F9;
        width: 64px;
        display: inline-block;
        i{
            vertical-align:middle;
        }
    }
    .show-picture{
        position: absolute;
        margin-top: 10px;
        text-align:center;
        width: 100%;
        z-index: 200;
        img{
            max-height: 400px;
            max-width:  300px;
            min-height: 200px;
        }
        span{
            color: #fff;
            position: absolute;
            margin-top: 0px;
        }
    }
    .picture-bottom{
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(55,55,55,.6);
        z-index: 100;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .lower-jiantou{
        top: 72%;
    }

.hide-tip{
    text-align: center;
    font-size: 12px;
    margin-top: 141px;
    color: #bcbcbc;
    position: fixed;
    background-color: #fff;
    width: 100%;
    height: 20px;
    padding-top: 8px;
    i{
      color: #ecda75;
      margin: 0px 2px;
      font-size: 12px;
      vertical-align: baseline;
    }
  }
  .header{
     border-bottom: 1px solid #EAEAEA;
  }
</style>
