<template>
  <div :class="{'wrapper':!showRealNameCertify  && !showNoIdCardRealNameCertify}">
    <div class="wrapper" v-show="!showRealNameCertify  && !showNoIdCardRealNameCertify">
      <Header tsTitleTxt="集团证件入网" tsBtnTxt="入网记录" @emBtnCk="goList"></Header>
      <div class="cardInfo">
            <span class="card-type">证件类型：
              <span v-show='!throwOrder.orderId' class="type-text" @click="chooseType">{{cardType && cardType.label}}<i class="iconfont youjiantou ims-input-icon"></i></span>
              <span v-show='throwOrder.orderId' class="type-text">{{cardType && cardType.label}}</span>
            </span>
            <span class="shop-btn" style="float: right;" @click="scanCertInfo">OCR</span>
        <div class="card-shop">
          <span class="card-number">证件号码：</span>
          <input v-show='!throwOrder.orderId' type="text" v-model="number" class="shop-input" placeholder="请输入证件号码" @input="check"/>
          <span v-show='throwOrder.orderId'  class="shop-input">{{number}}</span>
          <span class="shop-btn" @click="checkNum">校验</span>
        </div>
      </div>
      <div class="card-main">
        <div class="card-info" v-show="isShowGroup || showContent">
          <h3><i class="iconfont yingxiao2 icon-yingxiao2"></i>证件信息</h3>
          <ul class="info-ul">
            <li><span>证件类型：</span>{{cardType.label}}</li>
            <li><span>证件名称：</span><textarea type="text" v-model="showInfo.custName"></textarea><i class="iconfont bianji"></i></li>
            <li><span>证件所属省市：</span>
              <span class="time" style="color: #1681FB;" @click="openProvinceCityPicker">
                {{ selectedProvinceName || '请选择' }} {{ selectedCityName ? '/ ' + selectedCityName : '' }}
              </span>
            </li>
            <li><span>证件详细地址：</span>
	            <input v-model='showInfo.certAddr' type='text'>
              <i class="iconfont bianji"></i></li>
            <li @click="openDatePicker('1')"><span>证件生效日期：</span><span class="time">{{showInfo.certEffdate | changYear}}</span><i class="iconfont bianji"></i></li>
            <li @click="openDatePicker('2')"><span>证件失效日期：</span><span  class="time">{{showInfo.certExpdate | changYear}}</span><i class="iconfont bianji"></i></li>
          </ul>
        </div>
        <div v-show="showContent">
           <div class="card-real">
          <h3><i class="iconfont yingxiao2 icon-yingxiao2"></i>证件信息</h3>
          <div class="choose-name top">
            <div class="user-name">
              <span class="name">使用人姓名：</span>{{hideCustName(custName) || '待认证'}}
              <span class="real-true" v-show="idCard">已实名</span>
            </div>
            <div class="user-name">
              <span class="name">使用人身份证号码：</span>{{hideIdCard(custNo) || '待认证'}}
            </div>
            <div class="change-type" @click="changeSYCardType()"><span>{{syIdCardType.label}}</span><i class="iconfont youjiantou ims-input-icon"></i></div>
            <span class="real" @click="openRealNameCertify('1')">实名认证</span>
          </div>
          <div class="select-real" @click="showPerson"><i class="iconfont"
                                                          :class="[showName ? 'checkboxround1':'checkboxround0']"></i>使用人，经办人是同一个人
          </div>
          <div class="choose-name">
            <div class="user-name">
              <span class="name">经办人姓名：</span> {{hideCustName(handledName) || '待认证'}}
              <span class="real-true" v-show="idCard">已实名</span>
            </div>
            <div class="user-name">
              <span class="name">经办人身份证号码：</span>{{hideIdCard(handleNo) || '待认证'}}
            </div>
            <div v-show="!showName" class="change-type" @click="changeJBCardType()"><span>{{jbIdCardType.label}}</span><i class="iconfont youjiantou ims-input-icon"></i></div>
            <span class="real" @click="openRealNameCertify('2')" v-show="!showName">实名认证</span>
          </div>
        </div>
        <div class="card-photo">
          <div class="pub-card">
            <h3><i class="iconfont yingxiao2 icon-yingxiao2"></i>集团证件照上传</h3>
            <div class="photo" @click="openCamera(1)">
              <i class="iconfont paizhao ims-id-pt-icon" v-show="!groupPicObj.has"></i>
              <span v-show="!groupPicObj.has">点击拍照</span>
              <img class="img-src" :src="groupPicObj.src" v-show="groupPicObj.has"/>
            </div>
          </div>
          <div class="pub-card">
              <h3>
                <i class="iconfont yingxiao2 icon-yingxiao2"></i>授权委托书上传
                <div class="counter-controls" v-if='uploadFlag'>
                  <span class="counter-btn minus" @click="decreaseAuthDocs" v-show="agreePicObjList.length > 1">-</span>
                  <span class="counter-value">{{ agreePicObjList.length }}</span>
                  <span class="counter-btn plus" @click="increaseAuthDocs">+</span>
                </div>
              </h3>
              <div v-for="(agreePic, index) in agreePicObjList" :key="index" class="photo-container">
                <div class="photo" @click="openCamera(2, index)">
                  <i class="iconfont paizhao ims-id-pt-icon" v-show="!agreePic.has"></i>
                  <span v-show="!agreePic.has">点击拍照</span>
                  <img class="img-src" :src="agreePic.src" v-show="agreePic.has" />
                </div>
              </div>
          </div>
<!--          <div class="pub-card">-->
<!--            <h3><i class="iconfont yingxiao2 icon-yingxiao2"></i>门头照上传(<span class="optional-text">可选</span>)</h3>-->
<!--            <div class="photo" @click="openCamera(3)">-->
<!--              <i class="iconfont paizhao ims-id-pt-icon" v-show="!doorstepPicObj.has"></i>-->
<!--              <span v-show="!doorstepPicObj.has">点击拍照</span>-->
<!--              <img class="img-src" :src="doorstepPicObj.src" v-show="doorstepPicObj.has"/>-->
<!--            </div>-->
<!--          </div>-->

          <div class="pub-card" v-if="needApprovalWorkOrder">
            <h3><i class="iconfont yingxiao2 icon-yingxiao2"></i>审批工单上传(<span class="optional-text">可选</span>)</h3>
            <div class="photo" @click="openCamera(4)">
              <i class="iconfont paizhao ims-id-pt-icon" v-show="!approveOrderPicObj.has"></i>
              <span v-show="!approveOrderPicObj.has">点击拍照</span>
              <img class="img-src" :src="approveOrderPicObj.src" v-show="approveOrderPicObj.has"/>
            </div>
          </div>
        </div>
      </div>
        </div>


      <div class="nodata" v-show="!showContent">
        <div class="empty">
          <img src="static/img/wtf.png"/>
          <p class="tip-txt">请输入营业执照编号校验后继续</p>
        </div>
      </div>
      <NlButton enableTip="下一步" @click="chooessCertAddr" v-show="showContent"></NlButton>
    </div>
    <RealNameCertify
      ref="realNameCertify"
      v-if="showRealNameCertify"
      @realNameCallBack="realNameCallBack"
      @goPrev="realNamePrev"
      :readCardBusinessType="readCardBusinessType"
    >
    </RealNameCertify>
    <OtherCardRealNameCertify
      ref="otherCardRealNameCertify"
      v-if="showNoIdCardRealNameCertify"
      @comfirmRealNameData="comfirmRealNameData"
      @goPrev="realNamePrev"
      :cardType="noIdcardType"
      :currentPersonType="flag"
    >
    </OtherCardRealNameCertify>
    <mt-datetime-picker
      ref="picker"
      v-model="pickerVisible"
      type="date"
      :startDate="startTime"
      :endDate="endTime"
      @confirm="handleConfirm" >
    </mt-datetime-picker>

    <ProvinceCityPicker
      ref="showPicker"
      v-if="showPicker"
      @cancel="handleCancel"
      @confirm="handleProvinceCitySelected"
    >
    </ProvinceCityPicker>

	  <!-- 使用mt-popup组件包装审批工单验证组件 -->
	  <mt-popup v-model='showApprovalDialog' :closeOnClickModal='false' class='approval-dialog-popup' position='center'>
		  <div class='popup-header'>
			  <h3>
				  <i class='iconfont yingxiao2'></i>
				  审批工单审批工单校验不通过{{ approvalCodeMsg }}
			  </h3>
			  <span class='close-icon' @click='showApprovalDialog = false'>×</span>
		  </div>
		  <div class='popup-content'>
			  <ApprovalVerifyDialog :handlerInfo='{ name: handledName, idCard: handleNo }' :title='approvalCodeMsg'
			                        :userInfo='{ name: custName, idCard: custNo }' :verifyResults='approvalVerifyResult' />
		  </div>
		  <div class='popup-footer'>
			  <mt-button size='small' type='primary' @click='showApprovalDialog = false'>关闭</mt-button>
		  </div>
	  </mt-popup>
  </div>
</template>

<script>
  import Header from 'components/common/Header.vue'
  import NlButton from 'components/common/NlButton'
  import RealNameCertify from 'components/business/GroupCard/RealNameCertify.vue'
  import ClientJs from "@/base/clientjs";
  import Storage from '@/base/storage'
  import NlDropdown from 'components/common/NlDropdown/dropdown.js'
  import {dateFormat} from '@/base/utils';
  import OtherCardRealNameCertify from './OtherCardRealNameCertify.vue'
  import {qryAutoConfigByFeature} from  '@/base/request/commonReq.js';
  import ProvinceCitySelector from 'components/common/ProvinceCitySelector.vue'
  import ProvinceCityPicker from 'components/common/ProvinceCityPicker.vue';
  import ApprovalVerifyDialog from './ApprovalVerifyDialog.vue' // 添加引入
  import Vue from 'vue' // 添加Vue引入

  export default {
    components: {
      Header,
      NlButton,
      RealNameCertify,
      OtherCardRealNameCertify,
      ProvinceCitySelector,
	    ProvinceCityPicker,
	    ApprovalVerifyDialog // 添加新组件
    },
    data() {
      return {
	      // 多审批单
	      grpApproveList: [],
				// 多张委托书上传开关
	      uploadFlag:false,
        faceCheckFlag:'0',
        startTime: new Date(new Date().getFullYear() - 30, 0, 1),
        endTime:new Date(new Date().getFullYear() + 100, 11, 31),
        pickerVisible:'', //插件时间值
        showInfo: {},//展示信息
        showRealNameCertify: false,
        groupPicObj: {//证件照上传
          src: '',
          has: false,
          name: '',
          attachId: ''
        },
        doorstepPicObj: {//门头照上传
          src: '',
          has: false,
          name: '',
          attachId: ''
        },
        approveOrderPicObj: {//审批工单上传
          src: '',
          has: false,
          name: '',
          attachId: ''
        },
        number: '',//营业执照编号
        showName: false,//按钮是否经办人
        showContent: false,//展示子内容
        custName: '',//使用人姓名
        custNo: '',//使用人身份证号
        handledName: '', //经办人姓名
        handleNo: '',//经办人身份证号
        userEnterNetSfz: {},//使用人
        jbEnterNetSfz: {},//经办人
        enterNetSfz: {},
        flag: '',//是否经办人
        typeList: [{id: "UnitsTestify", label: "单位证明（部队专用）"},
          {id: "CorpAggrCredential", label: "社团法人登记证"},
          {id: "OrgaCredential", label: "组织机构代码证"},
          {id: "BusinessLicence", label: "营业执照"},
          {id: "EnteAggrCredential", label: "事业单位法人登记证"},
          {id: "UnionSocietyCredit", label: "统一社会信用代码证书"}],//集团证件类型
        cardType: {id: "BusinessLicence", label: "营业执照"},//证件类型
        IdcardTypeList: [
          { id: 'IdCard', label: '居民身份证' },
          { id: 'IdCardGA', label: '港澳居民居住证' },
          { id: 'HKMCPassport', label: '港澳居民来往内地通行证（仅限港澳居民使用）' },
          { id: 'Passport', label: '护照（仅限外国公民使用）' },
          { id: 'HuKouBu ', label: '户口簿' },
          { id: 'HuaQiaoPassport', label: '华侨护照' },
          { id: 'PLA', label: '军人身份证' },
          { id: 'TempId', label: '临时身份证' },
          { id: 'IdCardTW', label: '台湾居民居住证' },
          { id: 'TaiBaoZheng', label: '台湾居民来往大陆通行证（仅限台湾居民使用）' },
          { id: 'IdCardEx', label: '外国人永久居留证' },
          { id: 'PolicePaper', label: '武装警察身份证' },
        ],
        jbIdCardType: { id: 'IdCard', label: '居民身份证' }, //经办人
        syIdCardType: { id: 'IdCard', label: '居民身份证' },
        noIdcardType: { id: 'IdCard', label: '居民身份证' },
        showNoIdCardRealNameCertify: false, //非身份证类型实名
        datePicker:'',//1 startTime~ 2endTime
        readCardBusinessType: this.CONSTVAL.BUSI_TYPE_GROUP_ENTER_NET,
        isShowGroup:false,//是否展示集团信息（ocr成功后展现)
        judgeSalesAuthFlag:false,//是否有自办厅营业员权限
        uinfo:'',//操作员信息
        throwOrder: {},
        selectedProvince: null,
        selectedCity: null,
        srcFrom:'',
        showId: '', //入网审批工单号
        approvalCodeUserCertId: '',//使用人证件ID(校验接口返回)
        approvalCodeJbCertId: '',  //经办人证件ID(校验接口返回)
      approvalCodeList: [], //审批工单接口返回的使用人和经办人的集合
      agreePicObjList: [{
        src: '',
        has: false,
        name: '',
        attachId: ''
      }], //授权委托书列表
      currentAuthDocIndex: null,
        approvalCodeMsg: '', //审批工单接口返回的信息
        selectedProvinceCode: '',
        selectedProvinceName: '',
        selectedCityCode: '',
        selectedCityName: '',
        showPicker: false,
	      // 添加新的数据项
	      showApprovalDialog: false, // 显示审批工单选择对话框
	      selectedApproval: null, // 当前选中的审批工单
	      approvalVerifyResult: [], // 审批工单验证结果
        paizhaoPermission:false,
        needApprovalWorkOrder: false, // 是否需要审批工单校验
      };
    },
    async created() {
	    let param = {
		    busiType: 'group_access_mult_upload'
	    }
	    await this.$http.post('/xsb/ability/businessLimit/h5QryBusiPermission', param).then((res) => {
		    let { retCode } = res.data
		    if (retCode === '0') {
			    this.uploadFlag = true
		    }
	    })
      this.uinfo = Storage.session.get('userInfo');
      this.srcFrom = this.$route.query.srcFrom;
      // 若从政企视图或四个岗位则需要校验是否有无自办厅营业员权限
      // if(this.uinfo && (this.uinfo.stationId==this.CONSTVAL.STATION_ZWSXN
      //   ||this.uinfo.stationId==this.CONSTVAL.STATION_ZYZXRYN
      //   ||this.uinfo.stationId==this.CONSTVAL.STATION_JTYXN
      //   ||this.uinfo.stationId==this.CONSTVAL.STATION_YYYHWYXN)){
      //     this.judgeSales();
      // }
      // 非客户经理岗位的都需要先校验是否自办厅人员
      if(this.uinfo && (this.uinfo.stationId != this.CONSTVAL.STATION_KHJL)){
          this.judgeSales();
          this.judgeSalesAuthFlag = true
      }
      if (this.$route.query && this.$route.query.source == 'groupThrowOrder' && this.$route.query.orderInfo) {
        this.throwOrder = this.$route.query.orderInfo
        this.number = this.throwOrder.groupCertId
        if (this.throwOrder.groupCertType) {
          this.cardType = this.typeList.find(item => {
            return item.id == this.throwOrder.groupCertType
          })
        }
      }

      if(this.srcFrom === 'ocrFrom'){
        let data = Storage.session.get('ocrInfo')
        Storage.session.remove('ocrInfo')
        this.showInfo.custName = data.certificateName;
        this.number = data.certificateNo;
        this.showInfo.certAddr = data.certificateAddress;
        this.checkNum();
      }
      this.getBusiPermission();
    },
    computed: {
    },
    mounted: function () {

      window["paizhaoCallBack"] = res => {
        var info = res.replace(/\+/g,"%2B");
        info = JSON.parse(info.replace(new RegExp('\n', 'g'),'\\n'));
      if(info.imageB){
        let imgs = "data:image/jpeg;base64," + info.imageB;
        let param = {};
        param.unEncrpt = true;
        param.photoStr = info.imageB;
        param.certId = this.number;
        if ('1' == this.pzType) { //集团证件照片
          this.groupPicObj.src = imgs;
          param.type = '10';
        } else if ('2' == this.pzType) { //授权书照片
          // 确保当前索引有效
          const index = this.currentAuthDocIndex !== null ? this.currentAuthDocIndex : 0;

          // 更新对应索引的授权委托书对象
          this.$set(this.agreePicObjList[index], 'src', imgs);

          param.type = '11';
        } else if('3'==this.pzType){//门头照片
          this.doorstepPicObj.src = imgs;
          param.type = '19';
        }else if('4'==this.pzType){//审批工单照片
          this.approveOrderPicObj.src = imgs;
          param.type = '20';
        }
        this.uploadImg(param);
      }

      };



      window["paizhaoCb"] = info => {
        let imgs = "data:image/jpeg;base64," + info.fileImage;
        let param = {};
        param.unEncrpt = true;
        param.photoStr = info.fileImage;
        param.certId = this.number;
        if ('1' == this.pzType) { //集团证件照片
          this.groupPicObj.src = imgs;
          param.type = '10';
        } else if ('2' == this.pzType) { //授权书照片
          // 确保当前索引有效
          const index = this.currentAuthDocIndex !== null ? this.currentAuthDocIndex : 0;
					if (index > 0)
	          param.certId += `_${index}` // 添加索引到certId
          // 更新对应索引的授权委托书对象
          this.$set(this.agreePicObjList[index], 'src', imgs);

          param.type = '11';
        } else if('3'==this.pzType){//门头照片
          this.doorstepPicObj.src = imgs;
          param.type = '19';
        }else if('4'==this.pzType){//审批工单照片
          this.approveOrderPicObj.src = imgs;
          param.type = '20';
        }
        this.uploadImg(param);
      };

      window["certPaizhaoCb"] = info => {
        let param = {};
        param.unEncrpt = true;
        param.image = info.fileImage;
        let index = info.fileName .lastIndexOf("\.");
        let fileType  = info.fileName.substring(index, info.fileName.length);
        param.fileType = fileType;
        param.license = this.cardType.id;
        //先识别ORC
        this.identificOrc(param);
        };
    },
    filters: {
      changYear(val) {
        if (!val) {
          return '无';
        }
        if (val.length == 14) {
          let year = val.substr(0, 4);
          let month = val.substr(4, 2);
          let day = val.substr(6, 2);
          return `${year}/${month}/${day}`;
        }
      }
    },
    methods: {
      //打开日期选择控件
      openDatePicker(idx){
        this.$refs.picker.open();
        this.datePicker = idx;
      },
      //时间选择回调
      handleConfirm(val){
        if(!val){
          return;
        }
        let time=dateFormat(val, "yyyyMMddhhmmss");
        this.datePicker=='1'?this.showInfo.certEffdate=time: this.showInfo.certExpdate=time;
      },
      //选择使用人证件类型
      changeSYCardType() {
        NlDropdown(
          {
            confirmBtn: false,
            datalist: this.IdcardTypeList,
          },
          (retVal) => {
            if(retVal != this.syIdCardType){
              this.userEnterNetSfz = {}
              this.custName = ''
              this.custNo = ''
              if(this.showName){
                 this.jbEnterNetSfz = {}
                 this.handledName = ''
                 this.handleNo = ''
              }
            }
            this.syIdCardType = retVal
          }
        )
      },
      //选择经办人证件类型
      changeJBCardType() {
        NlDropdown(
          {
            confirmBtn: false,
            datalist: this.IdcardTypeList,
          },
          (retVal) => {
             if(retVal != this.jbIdCardType){
                this.jbEnterNetSfz = {}
                this.handledName = ''
                this.handleNo = ''
            }
            this.jbIdCardType = retVal
          }
        )
      },
      chooseType() {
        let self = this;
        NlDropdown(
          {
            confirmBtn: false,
            datalist: self.typeList
          },
          function(retVal) {
            self.cardType = retVal;
          }
        );
      },
      showPerson() {
        this.showName = !this.showName;
        if (this.showName) { //如果是同一人
          this.jbIdCardType = this.syIdCardType; //同类型证件
          if (this.custNo) {
            this.handledName = this.custName;
            this.handleNo = this.custNo;
            this.jbEnterNetSfz = this.userEnterNetSfz;
          } else if (!this.custNo && this.handleNo) {
            this.custNo = this.handleNo;
            this.custName = this.handledName;
            this.userEnterNetSfz = this.jbEnterNetSfz;
          }
        } else if (!this.showName) {
          this.handledName = '';
          this.handleNo = '';
          this.jbEnterNetSfz = '';
        }
      },
      //入网记录
      goList() {
        this.$router.push('/groupAccessList');
      },
      //输入框验证
      check() {
        if (!this.number) {
          this.showContent = false;
        }
        if (this.number) {//营业执照
          let certIdLength=this.number.length;
          if (this.cardType && this.cardType.id == 'UnionSocietyCredit') {//只能输入大写字母及数字且长度不超过18位（可以为18位）
            if (certIdLength != 18) {
              this.showContent = false;
            }
          } else if (this.cardType && this.cardType.id == 'BusinessLicence') {//营业执照
            //营业执照：证件号码长度需满足15位、13位、18位、20位、22位、24位；
            let range = [13, 15, 18, 20, 22, 24];
            if (range.indexOf(certIdLength) < 0) {
              this.showContent = false;
            }
          } else if (this.cardType && this.cardType.id == 'OrgaCredential') {
            //（2）组织机构代码证：
	          //a）证件号码长度满足10位，其规则为"XXXXXXXX-X"，倒数第2位是"-"。
            // b）证件号码长度为18位时，不校验证件号码规则。
            if (certIdLength != 10 && certIdLength != 18) {
              this.showContent = false;
            } else {
              if (certIdLength == 10) {
                let temp = this.number.substring(certIdLength - 2, certIdLength - 1);
                if (temp != '-') {
                  this.showContent = false;
                }
              }
            }
          } else if (this.cardType && this.cardType.id == 'EnteAggrCredential') {
            if (certIdLength != 12 && certIdLength != 18) {//事业单位法人登记证书：证件号码长度需满足12、18位。
              this.showContent = false;
            }
          }
        }
      },
      //检验
	    async checkNum() {
        if (!this.number) {
          this.$alert("请输入证件编号");
          return;
        }
        //增加校验，证件编号不能出现中文
        const chineseReg = /[\u4e00-\u9fa5]/;
        if(chineseReg.test(this.number)){
          this.$alert("证件编号中不能出现中文字符");
          return false;
        }
        if (this.number) {
          let certIdLength=this.number.length;
          if (this.cardType && this.cardType.id == 'UnionSocietyCredit') {//只能输入大写字母及数字且长度不超过18位（可以为18位）
            if (certIdLength != 18) {
              this.$alert("统一社会信用代码证书证件号码必须为18位");
              return false;
            }
          } else if (this.cardType && this.cardType.id == 'BusinessLicence') {//营业执照
            //营业执照：证件号码长度需满足15位、13位、18位、20位、22位、24位；
            let range = [13, 15, 18, 20, 22, 24];
            if (range.indexOf(certIdLength) < 0) {
              this.$alert("营业执照证件号码长度需满足13位、15位、18位、20位、22位、24位,当前长度为：" + certIdLength);
              return false;
            }
          } else if (this.cardType && this.cardType.id == 'OrgaCredential') {
            //（2）组织机构代码证：
	          //a）证件号码长度满足10位，其规则为"XXXXXXXX-X"，倒数第2位是"-"。
            // b）证件号码长度为18位时，不校验证件号码规则。
            if (certIdLength != 10 && certIdLength != 18) {
              this.$alert("组织机构代码证长度只能为10位或18位,当前长度为：" + certIdLength);
              return false;
            } else {
              if (certIdLength == 10) {
                let temp = this.number.substring(certIdLength - 2, certIdLength - 1);
                if (temp != '-') {
                  this.$alert("组织机构代码证长度为10位时，倒数第2位应是'-'");
                  return false;
                }
              }
            }
          } else if (this.cardType && this.cardType.id == 'EnteAggrCredential') {
            if (certIdLength != 12 && certIdLength != 18) {//事业单位法人登记证书：证件号码长度需满足12、18位。
              this.$alert("事业单位法人登记证书证件号码只能为12位或18位,当前长度为：" + certIdLength);
              return false;
            }
          }
        }
        const param = {
          busiType: 'approval_work_order_flag'
        };
        this.$http.post('/xsb/ability/businessLimit/h5QryBusiPermission', param).then(res => {
          const { retCode } = res.data;
          // if (data === '1') {
          //   //开关开启了，调用单审批工单校验接口
          //   //调用审批工单校验接口，通过后再调用checkGroupEnterNet()接口
          //   this.getGroupApprove()
          // }
          if (retCode === '0') {
						// 以前的逻辑：开关等于2的时候，调用多审批工单查询接口
            // 现在的逻辑：开关开则调用多审批工单的接口
            // 此接口会返回多个审批单号，需要调用checkGroupEnterNet()接口校验
            this.needApprovalWorkOrder = true; // 设置需要审批工单校验
            this.getGroupApproveList();
          } else { // 开关关闭了，不调用审批工单校验接口
            this.needApprovalWorkOrder = false; // 设置不需要审批工单校验
            this.checkGroupEnterNet();
          }
        }).catch(error => {
          this.needApprovalWorkOrder = false; // 发生错误时设置不需要审批工单校验
          this.checkGroupEnterNet();
        });
      },
      //集团证件入网审批工单查询
      getGroupApprove(){
        let url = '/xsb/personBusiness/groupEnterNet/h5CheckGroupApprovalCode';
        let param = {
          "certType": this.cardType.id,
          "certId": this.number
        }
        this.$http.post(url, param).then(res => {
          let { retCode, retMsg, data } = res.data
          this.approvalCodeMsg = retMsg;
          if (retCode == '0') {
            //获取审批单号
            this.showId = data.showId
            if (this.showId) {
              //有审批单号，获取使用人和经办人的证件号，后期作校验处理
              if(data.custInfoList && data.custInfoList.custInfo){
                //这是返回的使用人集合和经办人集合
                this.approvalCodeList = data.custInfoList.custInfo;
              }
              this.checkGroupEnterNet();
            } else {
              if (retMsg) {
                // 提取 FSOP2BDS2052 和 2025032599895853
                const match = retMsg.match(/【.*?】/);
                if (match) {
                  const code = match[0];
                  this.$alert(`${code}-该集团证件下没有有效的入网审批工单，不允许办理，请先提交集团证件线下入网审批工单`);
                } else {
                  this.$alert('该集团证件下没有有效的入网审批工单，不允许办理，请先提交集团证件线下入网审批工单');
                }
              } else {
                this.$alert('该集团证件下没有有效的入网审批工单，不允许办理，请先提交集团证件线下入网审批工单');
              }
            }
          } else {
            this.$alert(retMsg || '集团证件入网查询审批单号失败')
          }
        }).catch((response) => {
          this.$alert('集团证件入网查询审批单号异常')
        })
      },
	    //集团证件入网审批工单查询
	    getGroupApproveList() {
		    let url = '/xsb/personBusiness/groupEnterNet/h5queryGrpApproveList'
		    let param = {
			    'certType': this.cardType.id,
			    'certId': this.number.trim()
		    }
		    this.$http.post(url, param).then(res => {
			    let { retCode, retMsg, data } = res.data
			    this.approvalCodeMsg = retMsg
			    if (retCode === '0') {
				    this.grpApproveList = data
				    // 判断是否存在审批工单
				    if (data && data.length > 0) {
					    this.checkGroupEnterNet()
				    } else {
					    this.showContent = false
					    // 提取错误码用于显示
					    let errorCode = ''
					    if (retMsg) {
						    const match = retMsg.match(/【.*?】/)
						    if (match) {
							    errorCode = match[0]
						    }
					    }
					    this.$alert(`${errorCode ? errorCode : ''}该集团证件下没有有效的入网审批工单，不允许办理，请先提交集团证件线下入网审批工单`)
				    }
			    } else {
				    // 提取错误码用于显示
				    let errorCode = ''
				    if (retMsg) {
					    const match = retMsg.match(/【.*?】/)
					    if (match) {
						    errorCode = match[0]
					    }
				    }
				    this.$alert(`${errorCode ? errorCode : ''}该集团证件下没有有效的入网审批工单，不允许办理，请先提交集团证件线下入网审批工单`)
			    }
		    }).catch((response) => {
			    this.$alert('集团证件入网查询审批单号异常')
		    })
	    },
      //校验集团证件号是否正确
      checkGroupEnterNet(){
        let url = `/xsb/personBusiness/groupEnterNet/h5CheckGroupEnterNet?certId=${this.number}&certType=${this.cardType.id}`;
        this.$http.get(url).then((res) => {
          let {retCode, data, retMsg} = res.data;
          if (retCode == "0") {
            this.showContent = true;
            this.showInfo = data;
          } else {
            this.showContent = false;
            this.$messagebox({
              title: '温馨提示',
              message: '确认证件号码是否正确?',
              showCancelButton: true,
              showConfirmButton: true,
              confirmButtonText: "是",
              cancelButtonText: "否"
            }).then(action => {
              if (action == 'confirm') {
                //所有岗位放开首开校验 2024/6/21 edit by wjx
                // if(!this.judgeSalesAuthFlag){
                // this.judgeAuth();
                // }else{
                this.showContent = true;
                // }
              }
            });
            // this.$alert(retMsg || '查询集团证件信息获取失败请重试');
          }
        })
      },


      //隐藏姓名
      hideCustName(name) {
        if (name && name.length > 1) {
          let prefix = name.substr(0, 1);
          let bustr = '';
          for (let i = 1; i < name.length; i++) {
            bustr += '*';
          }
          return prefix + bustr;
        }
        return '';
      },
      //隐藏身份证号码
      hideIdCard(card) {
        if (card) {
          let prefix = card.substr(0, 6);
          let suffix = card.substr(card.length - 4, 4)
          let bustr = '';
          for (let i = 6; i < card.length - 4; i++) {
            bustr += '*';
          }
          return prefix + bustr + suffix;
        }
        return '';
      },
      //打开实名认证
      openRealNameCertify(data) {
        if(data == '2' && (!this.userEnterNetSfz || !this.userEnterNetSfz.pseq)) {
            this.$alert('请先对使用人进行实名认证');
            return
        }
        this.flag = data
        if (data == '1' && this.syIdCardType.id != 'IdCard') {
          this.noIdcardType = this.syIdCardType
          this.showNoIdCardRealNameCertify = true
          return
        } else if (data == '2' && this.jbIdCardType.id != 'IdCard') {
          this.noIdcardType = this.jbIdCardType
          this.showNoIdCardRealNameCertify = true
          return
        }
        if (data && data == '2') {
          this.faceCheckFlag = '1'
          this.readCardBusinessType = 'group_enter_net_jbr'
        } else {
          this.faceCheckFlag = '0'
          this.readCardBusinessType = this.CONSTVAL.BUSI_TYPE_GROUP_ENTER_NET
        }
        this.showRealNameCertify = true
      },
      async comfirmRealNameData(userData)
      {
        this.showNoIdCardRealNameCertify = false
        if (this.flag == 1) {
          this.userEnterNetSfz = userData
          console.log(userData)
          //当使用人为非二代证认证时，需要增加证件预占，身份证时组件已获取过预占码
          if (this.syIdCardType.id != 'IdCard') {
            let res = await this.getPreOccupancyCode()
            let { retCode, retMsg, data } = res.data
            if (retCode === '0') {
              this.$set(this.userEnterNetSfz, 'pseq', data.pseq)
            }
          }
        } else {
          this.jbEnterNetSfz = userData
        }
        this.showRealNameCertify = false
        //如果是同一个人的情况
        if (this.flag == '1') {
          if (this.showName) {
            //是否是同一人
            this.handledName = userData.sfzObj.sfzName
            this.handleNo = userData.sfzObj.sfzId
            this.jbEnterNetSfz = userData
          }
          this.userEnterNetSfz = userData
          this.custName = userData.sfzObj.sfzName
          this.custNo = userData.sfzObj.sfzId
        }
        if (this.flag == '2' && !this.showName) {
          //如果有经办人非同一个人
          this.jbEnterNetSfz = userData
          this.handledName = userData.sfzObj.sfzName
          this.handleNo = userData.sfzObj.sfzId
          if (this.custNo == this.handleNo) {
            //如果使用人的和经办人一致
            this.showName = true
          }
        }
      },




      //实名认证回调
      realNameCallBack(value) {
        this.enterNetSfz = JSON.parse(JSON.stringify(value));
        this.showRealNameCertify = false;
        //如果是同一个人的情况
        if (this.flag == "1") {
          if (this.showName) { //是否是同一人
            this.handledName = this.enterNetSfz.sfzObj.sfzName;
            this.handleNo = this.enterNetSfz.sfzObj.sfzId;
            this.jbEnterNetSfz = this.enterNetSfz;
          }
          this.userEnterNetSfz = this.enterNetSfz;
          this.custName = this.enterNetSfz.sfzObj.sfzName;
          this.custNo = this.enterNetSfz.sfzObj.sfzId;
        }
        if (this.flag == '2' && !this.showName) { //如果有经办人非同一个人
          this.jbEnterNetSfz = this.enterNetSfz;
          this.handledName = this.enterNetSfz.sfzObj.sfzName;
          this.handleNo = this.enterNetSfz.sfzObj.sfzId;
          if (this.custNo == this.handleNo) {//如果使用人的和经办人一致
            this.showName = true;
          }
        }
      },
      //实名认证返回
      realNamePrev() {
        this.showRealNameCertify = false;
        this.showNoIdCardRealNameCertify = false
      },

      chooessCertAddr(){

        if(this.selectedProvinceName.trim() && this.selectedProvinceName.trim().length > 0){
            let certAddr = this.showInfo.certAddr;

            const provinceCityCombined = this.selectedProvinceName + this.selectedCityName;

            if (certAddr.startsWith(provinceCityCombined)) {
                certAddr = certAddr.slice(provinceCityCombined.length).trim();
            } else if (certAddr.startsWith(this.selectedProvinceName)) {
                certAddr = certAddr.slice(this.selectedProvinceName.length).trim();
            } else if (certAddr.startsWith(this.selectedCityName)) {
                certAddr = certAddr.slice(this.selectedCityName.length).trim();
            }
            this.showInfo.certAddr= certAddr;
              let finalAddr = `${this.selectedProvinceName}${this.selectedCityName}${this.showInfo.certAddr}`;
              this.$messagebox({
                  title: '温馨提示',
                  message: '集团证件地址系统自动匹配后为：' + finalAddr +'，（格式：选择省+市+文本框输入），请确认是否正确，如有问题请修改',
                  showCancelButton: true,
                  closeOnClickModal: false,
                  closeOnPressEscape: false,
                  confirmButtonText: '确认提交'
                }).then((action) => {
                  if (action === 'confirm') {
                    this.submit();
                  }
                });
        }else{
          this.$alert('请选择省市');
          return
        }

      },
      //提交
      async submit() {
        if (!this.number) {
          this.$alert("请输入证件编号");
          return;
        }else {
	        // 先对证件号码进行trim处理
	        this.number = this.number.trim()

	        //增加校验，证件编号不能出现中文
          const chineseReg = /[\u4e00-\u9fa5]/;
          if(chineseReg.test(this.number)){
            this.$alert("证件编号中不能出现中文字符");
            return false;
          }
            let certIdLength=this.number.length;
            if (this.cardType && this.cardType.id == 'UnionSocietyCredit') {//只能输入大写字母及数字且长度不超过18位（可以为18位）
              if (certIdLength != 18) {
                this.$alert("统一社会信用代码证书证件号码必须为18位");
                return false;
              }
            } else if (this.cardType && this.cardType.id == 'BusinessLicence') {//营业执照
              //营业执照：证件号码长度需满足15位、13位、18位、20位、22位、24位；
              let range = [13, 15, 18, 20, 22, 24];
              if (range.indexOf(certIdLength) < 0) {
                this.$alert("营业执照证件号码长度需满足13位、15位、18位、20位、22位、24位,当前长度为：" + certIdLength);
                return false;
              }
            } else if (this.cardType && this.cardType.id == 'OrgaCredential') {
              //（2）组织机构代码证：
	            //a）证件号码长度满足10位，其规则为"XXXXXXXX-X"，倒数第2位是"-"。
              // b）证件号码长度为18位时，不校验证件号码规则。
              if (certIdLength != 10 && certIdLength != 18) {
                this.$alert("组织机构代码证长度只能为10位或18位,当前长度为：" + certIdLength);
                return false;
              } else {
                if (certIdLength == 10) {
                  let temp = this.number.substring(certIdLength - 2, certIdLength - 1);
                  if (temp != '-') {
                    this.$alert("组织机构代码证长度为10位时，倒数第2位应是'-'");
                    return false;
                  }
                }
              }
            } else if (this.cardType && this.cardType.id == 'EnteAggrCredential') {
              if (certIdLength != 12 && certIdLength != 18) {//事业单位法人登记证书：证件号码长度需满足12、18位。
                this.$alert("事业单位法人登记证书证件号码只能为12位或18位,当前长度为：" + certIdLength);
                return false;
              }
            }
          }
	      let finalAddr = `${this.selectedProvinceName}${this.selectedCityName}${this.showInfo.certAddr}`
        let addressValidFlag = await this.judgeAddress(finalAddr);
        if(!addressValidFlag){
          this.$alert('集团证件地址超长，请简短地址信息')
          return;
        }
        if (!this.userEnterNetSfz || !this.userEnterNetSfz.sfzObj) {
          this.$alert("请认证使用人信息");
          return;
        }
        if (!this.jbEnterNetSfz || !this.jbEnterNetSfz.sfzObj) {
          this.$alert("请认证经办人信息");
          return;
        }
        if (!this.groupPicObj.name) {
          this.$alert('集团证件照不能为空');
          return;
        }

      // 检查是否至少有一个授权委托书
      const hasAuthDoc = this.agreePicObjList.some(doc => doc.has && doc.name);
      if (!hasAuthDoc) {
          this.$alert('授权书委托书不能为空');
          return;
      }

	      // 如果有多个审批工单列表，需要遍历验证经办人和使用人是否在同一个审批工单中
	      if (this.grpApproveList && this.grpApproveList.length > 0) {
		      // 确保证件号码trim处理
		      const custNoTrimmed = this.custNo.trim()
		      const handleNoTrimmed = this.handleNo.trim()

		      // 存储每个审批工单验证结果
		      this.approvalVerifyResult = []
		      let matchedApproval = null

		      // 遍历所有审批工单，检查每个工单中的使用人和经办人
		      for (let i = 0; i < this.grpApproveList.length; i++) {
			      const approval = this.grpApproveList[i]
			      if (!approval.custInfoList || !approval.custInfoList.custInfo) {
				      this.approvalVerifyResult.push({
					      approval: approval,
					      hasUser: false,
					      hasHandler: false
				      })
				      continue
			      }

			      let hasUser = false
			      let hasHandler = false
			      let hasUserInApproval = false // 标记审批工单中是否有使用人数据

			      // 检查该审批工单中是否包含使用人和经办人
			      for (let j = 0; j < approval.custInfoList.custInfo.length; j++) {
				      const custInfo = approval.custInfoList.custInfo[j]
				      if (custInfo.custType === '1') {
					      hasUserInApproval = true // 审批工单中有使用人数据
					      if (custInfo.custCertId.trim() === custNoTrimmed) {
						      hasUser = true
					      }
				      } else if (custInfo.custType === '0' && custInfo.custCertId.trim() === handleNoTrimmed) {
					      hasHandler = true
				      }
			      }

			      // 保存验证结果
			      this.approvalVerifyResult.push({
				      approval: approval,
				      hasUser: hasUser,
				      hasHandler: hasHandler
			      })

			      // 修改匹配逻辑：如果审批工单中没有使用人数据，则只校验经办人；如果有使用人数据，则需要同时匹配使用人和经办人
			      if ((!hasUserInApproval && hasHandler) || (hasUserInApproval && hasUser && hasHandler)) {
				      matchedApproval = approval
		        }
		      }

		      // 判断是否找到匹配的审批工单
		      if (matchedApproval) {
		        // 设置匹配到的审批单号
	          this.showId = matchedApproval.showId
		      } else {
			      // 获取错误码
			      let errorCode = ''
			      if (this.approvalCodeMsg) {
				      const match = this.approvalCodeMsg.match(/【.*?】/)
				      if (match) {
					      errorCode = match[0]
				      }
			      }

			      // 直接显示审批工单验证对话框
			      this.showApprovalDialog = true
			      return
		      }
	      }
        //当approvalCodeList不为空时，表示查询了审批工单校验接口
	      else if (this.approvalCodeList.length > 0) {
		      // 确保证件号码trim处理
		      const custNoTrimmed = this.custNo.trim()
		      const handleNoTrimmed = this.handleNo.trim()

		      // 检查审批工单中是否有使用人数据
		      let hasUserInApproval = this.approvalCodeList.some(item => item.custType == '1')

		      // 使用人校验：只有当审批工单中有使用人数据时才进行校验
		      if (hasUserInApproval) {
			      let isUserFlag = this.approvalCodeList.find((item) => {
				      return item.custType == '1' && item.custCertId.trim() === custNoTrimmed
			      });

			      if(!isUserFlag){
				      //当不存在时，增加接口的唯一编号，方便查询日志
				      let errorCode = ''
				      if (this.approvalCodeMsg) {
					      const match = this.approvalCodeMsg.match(/【.*?】/);
					      if (match) {
						      errorCode = match[0]
					      }
				      }
				      this.$alert(`${errorCode ? errorCode : ''}当前认证的使用人证件号和接口返回的使用人证件号不一致，请确认`)
				      return;
			      }
		      }

		      // 经办人校验：始终进行校验
          let isJbFlag = this.approvalCodeList.find((item) => {
	          return item.custType == '0' && item.custCertId.trim() === handleNoTrimmed
          });
          if(!isJbFlag){
	          let errorCode = ''
            if (this.approvalCodeMsg) {
              const match = this.approvalCodeMsg.match(/【.*?】/);
              if (match) {
	              errorCode = match[0]
              }
            }
	          this.$alert(`${errorCode ? errorCode : ''}当前认证的经办人证件号和接口返回的经办人证件号不一致，请确认`)
            return;
          }
        }

      // 处理多个授权委托书名称，用分号连接
      const authDocNames = this.agreePicObjList
        .filter(doc => doc.has && doc.name)
        .map(doc => doc.name)
        .join(';');

      // 处理多个授权委托书ID，用分号连接
      const authDocIds = this.agreePicObjList
        .filter(doc => doc.has && doc.attachId)
        .map(doc => doc.attachId)
        .join(';');

        let url = '/xsb/personBusiness/groupEnterNet/h5StoreGroupEnterNetInfo';
        let param = {
          "groupCertType": this.cardType.id,
	        'groupCertId': this.number.trim(), // 确保trim处理
          "groupCertName": this.showInfo.custName,
          "groupCertAddr": finalAddr,
          "grpcustcertpic": this.groupPicObj.name,
          "grpcustproxypic": authDocNames,
          "grpcustcertAttachId": this.groupPicObj.attachId,
          "grpCertEffdate": this.showInfo.certEffdate,
          "grpCertExpdate": this.showInfo.certExpdate,
          "jbEnterNetSfz": JSON.stringify(this.jbEnterNetSfz),
          "userEnterNetSfz": JSON.stringify(this.userEnterNetSfz),
          "grpcustproxypicId": authDocIds,
          "doorStepPic":this.doorstepPicObj.name, //门头照
          "approveOrderPic":this.approveOrderPicObj.name, //审批单
          'throwOrderId': this.throwOrder.orderId,
          'showId': this.showId  //入网审批单号
        }
        console.log('address'+param.groupCertAddr);
				console.log(param, 'param')
        this.$http.post(url, param).then(res => {
          let {retCode, retMsg, data} = res.data;
          if (retCode == '0') {
            let Info = {
              groupOrdered: data.recId, //流水号
              groupSamePerson: this.showName ? '1' : '0', //是否同一人
            }
            Storage.session.set('select_sfz', Object.assign(Info, this.userEnterNetSfz))
            this.$router.push('/groupSelectNumber')
          } else {
            this.$alert(retMsg || '集团证件入网失败')
          }
        })
        .catch((response) => {
          this.$alert('集团证件入网网络连接失败')
        })
    },

      //拍照
    openCamera(type, index) {
        this.pzType = type;
      if (type === 2) {
        this.currentAuthDocIndex = index;
      }
      if(this.paizhaoPermission){
        ClientJs.openCameraOne('paizhaoCallBack');
      }else{
         ClientJs.openCameraAndShow("1", "paizhaoCb");
      }


      },

      //上传图片
      uploadImg(param) {
        let url = '/xsb/personBusiness/groupImsTrueName/h5upLoadImg';
        this.$http.post(url, param).then(res => {
          let {retCode, retMsg, data} = res.data;
          if (retCode == '0') {
            let attachName = data.attachName;
            if (this.pzType == '1') { //集团证件照片
              this.groupPicObj.name = attachName;
              this.groupPicObj.has = true;
              this.groupPicObj.attachId = data.attachId;
            } else if (this.pzType == '2') { //授权书照片
              // 确保当前索引有效
              const index = this.currentAuthDocIndex !== null ? this.currentAuthDocIndex : 0;

              // 使用$set确保视图更新
              this.$set(this.agreePicObjList[index], 'name', attachName);
              this.$set(this.agreePicObjList[index], 'has', true);
              this.$set(this.agreePicObjList[index], 'attachId', data.attachId);
            } else if(this.pzType=='3'){ //门头照
              this.doorstepPicObj.name = attachName;
              this.doorstepPicObj.has = true;
              this.doorstepPicObj.attachId = data.attachId;
            } else if(this.pzType=='4'){ //审核工单
              this.approveOrderPicObj.name = attachName;
              this.approveOrderPicObj.has = true;
              this.approveOrderPicObj.attachId = data.attachId;
            }
          } else {
            this.$alert(retMsg || '上传图片失败请重新拍照');
          }
        })
      },
      //证件扫描函数
      scanCertInfo(val){
            this.pzType = val;
            ClientJs.openCameraAndShow("1", "certPaizhaoCb");
        },
        //企业/个人证照识别并验真（验真仅企业证件）
        identificOrc(param){
            let failMsg='无法识别，请确认图片拍摄质量再重试，或者手动输入！';
            let url = `/xsb/personBusiness/groupArchives/h5IdentificOrc`;
            this.$http.post(url,param).then(res =>{
                if(res.data.retCode == '0'){
                    let data = res.data.data;
                    if( data.ecInfo && data.ecInfo.name!=null && data.ecInfo.uniscId!=null){
                        this.isShowGroup = true;
                        this.showInfo.custName = data.ecInfo.name;
                        this.number = data.ecInfo.uniscId;
                        this.showInfo.certAddr = data.ecInfo.address;
                        this.checkNum();
                    }else{
                      this.$alert(failMsg);
                    }
                } else {
                    this.$alert(failMsg);
                    this.defeaultTip = '请输入';
                }
            }).catch(res =>{
                this.$alert('证照识别并验真网络异常:'+ res);
            });
        },
        //若是政企视图或四个岗位进入则先判断是否有无自办厅营业员权限，若没有则不准办理；
        //若原客户经理进入，校验集团信息不通过才校验自有厅店长权限
        async judgeAuth(){
        //   let authId = '116228';//自有厅店长
        //   let url = `/xsb/personBusiness/businessOpen/h5qryOperAuth?authId=${authId}`;
        //   this.$http.get(url).then((res) => {
        //   let {retCode, data, retMsg} = res.data;
        //   if (retCode == "0") {
        //       this.showContent = true;
        //   } else {
        //     this.$alert('非自有厅店长角色，不允许首开，请到营业厅前台受理');
        //   }
        // }).catch((response) => {
        //   this.$alert("操作员令牌查询异常");
        // })
          //上面逻辑搞不了，传参错误无法判断是否为自有厅店长，改为下面逻辑
          let resData = await this.getOperatorInfo()
          if (resData) {
            let { retCode, retMsg, data } = resData.data
            if (retCode == '0') {
              if (data == '1') {
                this.showContent = true
              } else {
                this.$alert(retMsg || '非自有厅店长角色，不允许首开，请到营业厅前台受理')
              }
            } else {
              this.$alert(retMsg || '查询操作员信息服务失败')
            }
          } else {
            this.$alert('查询操作员是否有入网权限网络超时，请重试')
          }
        },
      //非二代身份证获取预占码
       getPreOccupancyCode(){
        let urlYuZhan = '/xsb/personBusiness/notcradyupeihao/h5idcampon'
        let paramYuZhan = {
          userName: this.userEnterNetSfz.sfzObj.sfzName, //使用人姓名
          certId: this.userEnterNetSfz.sfzObj.sfzId, //使用人证件编号
          certType: this.syIdCardType.id, //使用人证件类型
          netSrl: this.userEnterNetSfz.netSrl, //人脸照片唯一编码
          unLoadFlg: true
        }
        return  this.$http.post(urlYuZhan, paramYuZhan)
      },
      judgeAddress(certAddr) {
        if(!certAddr){
          return true
        }
        let str = certAddr;
        var W = new Object();
        var iNumwords = 0;
        var sNumwords = 0;
        var sTotal = 0;
        var iTotal = 0;
        var eTotal = 0;
        var inum = 0;
        for (let i = 0; i < str.length; i++) {
          var c = str.charAt(i);
          if (c.match(/[\u4e00-\u9fa5]/)) {
            if (isNaN(W[c])) {
              iNumwords++;
              W[c] = 1;
            }
            iTotal++;
          }
        }
        for (let i = 0; i < str.length; i++) {
          var c = str.charAt(i);
          if (c.match(/[^\x00-\xff]/)) {
            if (isNaN(W[c])) {
              sNumwords++;
            }
            sTotal++;
          } else {
            eTotal++;
          }
          if (c.match(/[0-9]/)) {
            inum++;
          }
        }
        let len = iTotal * 2 + (sTotal - iTotal) * 2 + eTotal;
        if (len > 128) {
          return false
        } else {
          return true
        }
    },
    // 增加授权委托书数量
    increaseAuthDocs() {
			if (this.agreePicObjList.length === 2)
				return
      this.agreePicObjList.push({
        src: '',
        has: false,
        name: '',
        attachId: ''
      });
    },

    // 减少授权委托书数量
    decreaseAuthDocs() {
      if (this.agreePicObjList.length > 1) {
        this.agreePicObjList.pop();
      }
      },
        openProvinceCityPicker() {
          this.showPicker = true;
        },

        handleProvinceCitySelected(provinceCode, provinceName, cityCode, cityName) {
            this.showPicker = false;
            this.selectedProvinceCode = provinceCode;
            this.selectedProvinceName = provinceName;
            this.selectedCityCode = cityCode;
            this.selectedCityName = cityName;
            let certAddr = this.showInfo.certAddr;
            const provinceCityCombined = provinceName + cityName;

            if (!certAddr || certAddr.trim() === "") {
                return;
            } else if (certAddr.startsWith(provinceCityCombined)) {
                certAddr = certAddr.slice(provinceCityCombined.length).trim();
            } else if (certAddr.startsWith(provinceName)) {
                certAddr = certAddr.slice(provinceName.length).trim();
            } else if (certAddr.startsWith(cityName)) {
                certAddr = certAddr.slice(cityName.length).trim();
            }
            this.showInfo.certAddr = certAddr;
        },

        handleCancel() {
          this.showPicker = false;
        },

        getBusiPermission(){
          const param = {
               busiType: 'fea_gtzj_pz', //集团证件照片上传模式
          }
          this.$http.post('/xsb/ability/businessLimit/h5QryBusiPermission', param).then((res) => {
              if (res.data.retCode == '0') {
                  this.paizhaoPermission =true;
                }else{

                }
          })
        },
    },
  }
</script>

<style scoped lang="less">
    .card-type {
      .type-text{
        color: black;
        i{
          color: #1681fb;
        }
      }
  }
  .wrapper {
    height: 100%;
  }

  .cardInfo {
    font-size: 16px;
    margin-bottom: 8px;
    padding: 54px 12px 10px 12px;
    background: #fff;
    box-shadow: 0px 2px 4px 0px rgba(197, 197, 197, 0.5);

    p {
      font-size: 14px;
      color: #505050;
      margin-bottom: 4px;
    }
    .shop-btn {
        text-align: center;
        width: 53px;
        color: #fff;
        font-size: 12px;
        height: 20px;
        line-height: 20px;
        background: rgba(22, 129, 251, 1);
        border-radius: 12px;
      }

    .card-shop {
      margin-top: 10px;
      display: flex;
      justify-content: space-between;

      .shop-input {
        flex: auto;
        font-size: 14px;
        outline: none;

        &::-webkit-input-placeholder {
          color: #C5C5C5;
        }
      }
    }
  }

  .card-main {
  }

  .card-info {
    background: #fff;
    padding: 12px;
    margin-bottom: 8px;

    .info-ul {
      i{
        font-size: 14px;
        color: #BBBBBB;
      }
      margin-top: 10px;
      .time{
        color: black;
        margin-right: 5px;
      }
      li {
        color: #6C6C6C;
        font-size: 14px;
        margin-bottom: 8px;
        span {
          color: #BBBBBB
        }
        input{
          font-size: 14px;
          width: 65%;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          margin-right: 5px;
        }
        textarea {
          font-size: 12px;
          width: 60%;
          margin-right: 5px;
          padding: 8px;
          border: 1px solid #ccc;
          border-radius: 4px;
          background-color: #fff;
          resize: none; /* 禁止手动调整大小 */
          outline: none;
          box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
          transition: border-color 0.3s ease;
          min-height: 25px; /* 设置最小高度 */
          height: auto; /* 高度自适应 */
          overflow-y: hidden; /* 初始时隐藏垂直滚动条 */

          &:focus {
            border-color: #1681fb;
          }
        }
      }
    }
  }

  h3 {
    color: #505050;
    font-size: 14px;

    i {
      font-size: 20px;
      color: #007AFF;
      vertical-align: -2px;
      margin-right: 2px;
    }
  }

  .card-real {
    background: #fff;
    margin-bottom: 12px;
    padding: 12px;

    .choose-name {
      position: relative;

      .real {
        line-height: 20px;
        width: 64px;
        height: 20px;
        color: #fff;
        font-size: 12px;
        background: rgba(22, 129, 251, 1);
        border-radius: 17px;
        position: absolute;
        right: 0;
        text-align: center;
      bottom: 15%;
        transform: translateY(50%);
      }

    .change-type {
      line-height: 20px;
      height: 20px;
      font-size: 12px;
      border-radius: 17px;
      position: absolute;
      right: 0;
      text-align: center;
      bottom: 55%;
      color: #a3a3a3;
      max-width: 43%;
      display: flex;
      overflow: hidden;
      text-overflow: ellipsis;
      color: #1681fb;
      i {
        font-size: 10px;
      }
    }
      &.top {
        margin: 10px 0 8px 0;
      }
    }
  }

  .user-name {
    font-size: 14px;
    color: #6C6C6C;
    margin-bottom: 8px;

    .name {
      color: #BBBBBB;
    }

    .real-true {
      margin-left: 8px;
      display: inline-block;
      line-height: 16px;
      box-sizing: border-box;
      width: 55px;
      height: 16px;
      text-align: center;
      font-size: 12px;
      color: #71C43A;
      border: 1px solid rgba(113, 196, 58, 1);
    }
  }

  .select-real {
    i {
      color: #1680F9;
      margin-right: 4px;
    }

    margin-bottom: 8px;
    color: #6C6C6C;
    font-size: 14px;
  }

  .card-photo {
    padding: 12px;
    background: #fff;
  }

  .photo {
    border: 2px dashed #1681FB;
    border-radius: 8px;
    margin: 30px auto;
    width: 52%;
    height: 104px;
    text-align: center;
    position: relative;

    i {
      color: #1681FB;
      font-size: 38px;
      margin-top: 22px;
      display: block;
    }

    span {
      display: block;
      font-size: 14px;
      color: #1681FB;
      margin-top: 12px;
    }

    .img-src {
      width: 100%;
      height: 100%;
    }
  }

  .photo:last-child {
    margin-bottom: 16px;
  }

  .nodata {
    height: calc(100% - 110px);
    background: #fff;
    position: relative;
  }

  .empty {
    position: absolute;
    top: 30%;
    transform: translateY(-30%);
    width: 100%;
    margin: auto;
    text-align: center;
    color: #737373;
    font-size: 12px;
    background: #fff;

    img {
      width: 150px;
    }

    .tip-txt {
      text-align: center;
      margin-top: 8px;
      color: #bbb;
    }
  }

  .photo-container {
  position: relative;
  margin-bottom: 15px;
.province-city-selector {
  color: #333;
  cursor: pointer;
  display: inline-block;
  min-width: 150px;
  text-align: left;
}

.province-city-selector:hover {
  color: #1890ff;
}
}

.counter-controls {
  display: inline-block;
  float: right;
  margin-right: 10px;

  .counter-btn {
    display: inline-block;
    width: 20px;
    height: 20px;
    line-height: 18px;
    text-align: center;
    border-radius: 50%;
    background-color: #1681FB;
    color: white;
    font-size: 16px;
    cursor: pointer;
    user-select: none;

    &.minus {
      background-color: #ff6b6b;
    }
  }

  .counter-value {
    display: inline-block;
    margin: 0 5px;
    font-size: 14px;
    color: #505050;
  }

}

    /* 添加弹窗样式 */
    .approval-dialog-popup {
	    width: 90%;
	    max-width: 800px;
	    height: 80%;
	    max-height: 650px;
	    border-radius: 8px;
	    display: flex;
	    flex-direction: column;
	    overflow: hidden;
	    background-color: #fff;
    }

    .popup-header {
	    display: flex;
	    justify-content: space-between;
	    align-items: center;
	    padding: 15px;
	    background-color: #f5f7fa;
	    border-bottom: 1px solid #ebeef5;
    }

    .popup-header h3 {
	    margin: 0;
	    font-size: 16px;
	    color: #303133;
	    display: flex;
	    align-items: center;
    }

    .popup-header h3 i {
	    margin-right: 8px;
	    color: #1681FB;
	    font-size: 20px;
    }

    .popup-content {
	    flex: 1;
	    overflow: hidden;
    }

    .close-icon {
	    font-size: 22px;
	    color: #909399;
	    cursor: pointer;
	    width: 24px;
	    height: 24px;
	    line-height: 24px;
	    text-align: center;
    }

    .close-icon:hover {
	    color: #606266;
    }

    .popup-footer {
	    padding: 12px 15px;
	    text-align: right;
	    border-top: 1px solid #ebeef5;
	    background-color: #f5f7fa;
    }

    .popup-footer .mint-button {
	    padding: 8px 20px;
	    font-size: 14px;
    }

    .optional-text {
      color: red;
      font-size: 12px;
    }
</style>
