<template>
    <div>
        <div class='wrapper' v-show='!showRealNameCertify  && !showNoIdCzardRealNameCertify'>
            <Header :tsTitleTxt='titleTxt'></Header>
            <div class='cardInfo'>
            </div>
            <div class='card-main' :class="[{'transfer-main':groupBusiType=='2'}]">
                <!--集团证件开始-->
                <div class='card-info no-edit'>
                    <h3><i class='iconfont yingxiao2 icon-yingxiao2'></i>集团证件</h3>
                    <ul class='info-ul'>
                        <li style='display: flex; align-items: center;'>
                            <span>过户类型：</span>
                            <span class='readOnly important'>{{ groupBusiType == 2 ? '集团转个人' : '个人转集团'
                                }}</span>
                        </li>
                        <li>
                            <span class='card-type'>证件类型：</span>
                            <span class='type-text' :class="[{'readOnly':groupBusiType=='2'}]"
                                  @click='chooseType'>{{ cardType && cardType.label }}<i
                                class='iconfont youjiantou ims-input-icon'></i></span>
                            <span class='shop-btn ocr-button' @click='scanCertInfo'>OCR</span>
                        </li>
                        <li>
                            <span>证件号码：</span><input type='text' v-model='showInfo.certId'
                                                         placeholder='请输入证件号码'><i
                            class='iconfont bianji'></i>
                            <span class='shop-btn' style='float: right;color: #1681fb' @click='checkNum'>校验</span>
                        </li>
                        <li>
                            <span>证件名称：</span>
                            <textarea v-show="groupBusiType!='2'" type='text' v-model='showInfo.custName'
                                      placeholder='请输入证件名称'></textarea><i
                            class='iconfont bianji'></i>
                            <div v-show="groupBusiType=='2'" class='readOnly'>{{ showInfo.custName }}</div>
                        </li>
                        <li>
                            <span>证件地址：</span>
                            <input v-show="groupBusiType!='2'" type='text' v-model='showInfo.custAddress'
                                   placeholder='请输入证件地址' /><i
                            class='iconfont bianji'></i>
                            <div v-show="groupBusiType=='2'" class='readOnly'>{{ showInfo.custAddress }}</div>
                        </li>
                        <li @click="openDatePicker('1')">
                            <span>证件生效日期：</span>
                            <span class='time readOnly'>{{ showInfo.certEffDate | changYearNew }}</span><i
                            class='iconfont bianji'></i>
                        </li>
                        <li @click="openDatePicker('2')"><span>证件失效日期：</span><span
                            class='time readOnly'>{{ showInfo.certExpDate | changYearNew }}</span><i
                            class='iconfont bianji'></i></li>
                    </ul>

                    <div class='card-real'>
                        <!-- 使用人实名 -->
                        <h3><i class='iconfont yingxiao2 icon-yingxiao2'></i>使用人证件<span class='need-edit'>*</span>
                        </h3>
                        <div class='choose-name top'>
                            <div class='user-name'>
                                <span class='name'>使用人姓名：</span>
                                {{ hideCustName(userEnterNetSfzNew && userEnterNetSfzNew.custName) || '待认证' }}
                            </div>
                            <div class='user-name'>
                <span
                    class='name'>使用人身份证号码：</span>{{ hideIdCard(userEnterNetSfzNew && userEnterNetSfzNew.certId) || '待认证'
                                }}
                            </div>
                            <div class='change-type' @click='changeSYCardType()'>
                                <span>{{ syIdCardType.label }}</span>
                                <i class='iconfont youjiantou ims-input-icon'></i>
                            </div>
                            <span class='real' @click="openRealNameCertify('1')">实名认证</span>
                        </div>
                    </div>
                    <div class='select-real' @click='showPerson'><i class='iconfont'
                                                                    :class="[showName ? 'checkboxround1':'checkboxround0']"></i>使用人，经办人是同一个人
                    </div>
                    <div class='card-real'>
                        <!-- 经办人实名 -->
                        <h3><i class='iconfont yingxiao2 icon-yingxiao2'></i>经办人证件<span class='need-edit'>*</span>
                        </h3>
                        <div class='choose-name top'>
                            <div class='user-name'>
                                <span class='name'>经办人姓名：</span> {{ hideCustName(handledName) || '待认证' }}
                            </div>
                            <div class='user-name'>
                                <span class='name'>经办人身份证号码：</span>{{ hideIdCard(handleNo) || '待认证' }}
                            </div>
                            <div v-show='!showName' class='change-type' @click='changeJBCardType()'>
                                <span>{{ jbIdCardType.label }}</span><i class='iconfont youjiantou ims-input-icon'></i>
                            </div>
                            <span class='real' @click="openRealNameCertify('2')" v-show='!showName'>实名认证</span>
                        </div>

                    </div>


                    <div class='pub-card'>
                        <h3><i class='iconfont yingxiao2 icon-yingxiao2'></i>集团证件上传<span
                            class='need-edit'>*</span></h3>
                        <div class='photo' @click='openCamera(1)'>
                            <i class='iconfont paizhao ims-id-pt-icon' v-show='!groupPicObj.has'></i>
                            <span v-show='!groupPicObj.has'>集团证件拍照</span>
                            <img class='img-src' :src='groupPicObj.src' v-show='groupPicObj.has' />
                        </div>
                    </div>

                    <div class='pub-card'>
                        <h3><i class='iconfont yingxiao2 icon-yingxiao2'></i>授权委托书上传<span
                            class='need-edit'>*</span></h3>
                        <div class='photo' @click='openCamera(2)'>
                            <i class='iconfont paizhao ims-id-pt-icon' v-show='!agreePicObj.has'></i>
                            <span v-show='!agreePicObj.has'>委托书拍照</span>
                            <img class='img-src' :src='agreePicObj.src' v-show='agreePicObj.has' />
                        </div>
                    </div>

                </div>
                <!--集团证件结束-->

                <!--个人证件开始-->
                <div class='card-real'>


                </div>
                <!--个人证件结束-->

                <!--其他信息开始-->
                <div class='card-info other-info display'>
                    <h3><i class='iconfont yingxiao2 icon-yingxiao2'></i>其他信息<span class='need-edit'>*</span></h3>
                    <ul class='info-ul right-content'>
                        <!--                        <li>-->
                        <!--                            <span>联系人：</span><input type='text' v-model='showInfo.linkman' placeholder='请输入姓名'>-->
                        <!--                        </li>-->
                        <!--                        <li>-->
                        <!--                            <span>联系电话：</span><input type='number' v-model='showInfo.linkphone'-->
                        <!--                                                         placeholder='请输入电话'>-->
                        <!--                        </li>-->
                        <li>
                            <span>设置服务密码：</span>
                            <span class='txt-td select-span'>
                                <input v-show='!pwdShowFlg' v-model='sixPassword' type='password' maxlength='6'
                                       oninput="value=value.replace(/[^0-9.]/g,'')"
                                       placeholder='请输入6位初始服务密码'>
                                 <input v-show='pwdShowFlg' type='tel' v-model='sixPassword'
                                        placeholder='请输入6位初始服务密码'
                                        oninput="value=value.replace(/[^0-9.]/g,'')"
                                        maxlength='6'>
                                 <i class='iconfont' :class="[pwdShowFlg?'kejian':'bukejian']"
                                    @click='pwdShowFlg=!pwdShowFlg'></i>
                            </span>
                        </li>
                        <li>
                            <span>确定服务密码：</span>
                            <span class='txt-td select-span'>
                                <input v-show='!pwdShowFlg2' v-model='sixPassword2' type='password' maxlength='6'
                                       oninput="value=value.replace(/[^0-9.]/g,'')" placeholder='请确认6位初始服务密码'>
                                <input v-show='pwdShowFlg2' type='tel' v-model='sixPassword2'
                                       placeholder='请确认6位初始服务密码' oninput="value=value.replace(/[^0-9.]/g,'')"
                                       maxlength='6'>
                                <i class='iconfont' :class="[pwdShowFlg2?'kejian':'bukejian']"
                                   @click='pwdShowFlg2=!pwdShowFlg2'></i>
                            </span>
                        </li>
                    </ul>
                </div>
                <!--其他信息结束-->
            </div>

            <NlButton enableTip='提交' @click='groupTransferSubmit'></NlButton>
        </div>
        <RealNameCertify
            ref='realNameCertify'
            v-if='showRealNameCertify'
            @realNameCallBack='realNameCallBack'
            @goPrev='realNamePrev'
            :readCardBusinessType='readCardBusinessType'
        >
        </RealNameCertify>
        <OtherCardRealNameCertify
            ref='otherCardRealNameCertify'
            v-if='showNoIdCardRealNameCertify'
            @comfirmRealNameData='comfirmRealNameData'
            @goPrev='realNamePrev'
            :cardType='noIdcardType'
            :currentPersonType='flag'
        >
        </OtherCardRealNameCertify>
        <mt-datetime-picker
            ref='picker'
            v-model='pickerVisible'
            type='date'
            :startDate='startTime'
            :endDate='endTime'
            @confirm='handleConfirm'>
        </mt-datetime-picker>
        <!-- 采集视频 -->
        <!--    <div class='cert-content'>-->
        <!--            <div class='yph-box' v-show='videoCollectFlag && !videoNoCollectFlag'>-->
        <!--                <div class='part-title' style='display: flex;justify-content: space-between;'>采集视频-->
        <!--                    <div @click='openVideoChoiceButton()' style='color: #1681FB;' v-show='reVideoChoiceFlag'>-->
        <!--                        {{ dictName }}-->
        <!--                        <i class='iconfont qiehuan1'></i>-->
        <!--                    </div>-->
        <!--                </div>-->
        <!--                <div class='video-box'>-->
        <!--                    <div class='replay-container' v-if='filePath'>-->
        <!--                        <span><i class='iconfont duihao1'></i>视频已拍摄</span>-->
        <!--                        <span class='play' @click='playVideoButton'><i class='iconfont sanjiao'></i>播放</span>-->
        <!--                    </div>-->
        <!--                    <div class='replay-container' v-else>-->
        <!--                        <span><i class='iconfont duihao1'></i>视频尚未采集</span>-->
        <!--                    </div>-->


        <!--                    <div class='rebox-choice' @click='reVideoChoiceButton(readTypeNumber,dictName)'>-->
        <!--                        <img src='static/img/videoReload.png'>-->
        <!--                        <span class='rebox-txt'>重拍</span>-->
        <!--                    </div>-->

        <!--                </div>-->
        <!--                <p class='video-tip-msg' style='text-align: inherit;' v-if='filePath'>-->
        <!--                    检查视频中的图像和声音，如正确请签名</p>-->
        <!--                <p class='video-tip-msg' style='text-align: inherit;' v-else>视频采集失败，请点击重拍按钮进行拍摄</p>-->
        <!--                <div class='check-rebox' v-show='artificialFlag'>-->
        <!--                    <p class='video-tip-msg' style='text-align: inherit;float: left;'>AI审核失败，可人工审核或重拍</p>-->
        <!--                    <button @click='artificialCheckButton' class='sign-btn'>人工审核</button>-->
        <!--                </div>-->
        <!--            </div>-->

        <!--            <div class='yph-box' v-show='videoCollectFlag && videoNoCollectFlag'>-->
        <!--                <div class='part-title' style='display: flex;justify-content: space-between;'>采集视频-->
        <!--                </div>-->
        <!--                <div class='video-box'>-->
        <!--                    <div class='replay-container'>-->
        <!--                        <span><i class='iconfont duihao1'></i>视频不采集</span>-->
        <!--                    </div>-->
        <!--                </div>-->
        <!--            </div>-->

        <!--            <div class='cert-content-box'>-->
        <!--                <div class='content-box-top'>身份图像</div>-->
        <!--                <div class='content-box-bottom'>-->
        <!--                    <img src='static/img/default-sfz.png' ref='sfzImg' @error='defaultImg()' class='cert-img' />-->
        <!--                </div>-->
        <!--            </div>-->

        <!--收入证明-->
        <!--      <zmUpload ref='zmUpload'-->
        <!--                :count='1'-->
        <!--                :busiType='busiType'-->
        <!--                :telNum='telNumber'-->
        <!--                :workseq='netSrl'-->
        <!--                :source='1'-->
        <!--                v-show='uploadZmFlag && zmSwitchFlag'-->
        <!--      ></zmUpload>-->
        <!--    </div>-->
    </div>
</template>

<script>
import Header from 'components/common/Header.vue'
import NlButton from 'components/common/NlButton'
import RealNameCertify from 'components/business/GroupCard/RealNameCertify.vue'
import ClientJs from '@/base/clientjs'
import Storage from '@/base/storage'
import NlDropdown from 'components/common/NlDropdown/dropdown.js'
import { dateFormat } from '@/base/utils'
import OtherCardRealNameCertify from 'components/business/GroupCard/OtherCardRealNameCertify.vue'
import AuthenctCorporate from 'components/common/uniteauth/index.js'
import { aizhiPaperMixin } from '@/base/mixins/aizhiPaperMixin.js'
import { groupEnterCommon } from 'components/business/GroupCard/common/commonData.js'
import ImageObj from '@/base/request/ImageServReq'
import { VideoCollectMixin } from '@/base/mixins/VideoCollectMixin.js'
import zmUpload from 'components/common/zmUpload/zmUpload.vue'

export default {
    components: {
        Header,
        NlButton,
        RealNameCertify,
        OtherCardRealNameCertify,
        // ProvinceCitySelector
        zmUpload
    },
    mixins: [aizhiPaperMixin, groupEnterCommon, VideoCollectMixin],
    data() {
        return {
            titleTxt: '集团入网资料修改',
            telNum: '',
            readCardBusinessType: this.CONSTVAL.BUSI_TYPE_MOD_GROUP_ENTER_NET,
            azSubmitUrl: '/xsb/personBusiness/groupEnterNet/h5groupPersonTransFinalSubmit', // 爱知提交url
            message: '集团资料过户成功，点击确认退出', //业务办理成功的提示信息
            groupBusiType: 1,//1：个人号码转集团  2：集团号码转个人
            sixPassword: '',//6位服务密码
            passwordFlag: false,
            pwdShowFlg: false,
            sixPassword2: '',//确认服务密码
            pwdShowFlg2: false,
            passwordFlag2: false,
            uploadZmFlag: false,  //是否需要16-18周岁收入证明
            zmSwitchFlag: false,  //是否收入证明提示
            zmAttachIds: ''     //收入证明附件ID
        }
    },
    created() {
        this.titleTxt = '集团过户'
        this.uinfo = Storage.session.get('userInfo')
        //获取手机号
        let jqData = Storage.session.get('jqData')
        this.telNum = jqData.telnum
        console.log(this.telNum)
        this.transUserCheck()
        // this.getBusiPermission();
    },
    computed: {},
    mounted: function() {

        window['paizhaoCallBack'] = res => {
            var info = res.replace(/\+/g, '%2B')
            info = JSON.parse(info.replace(new RegExp('\n', 'g'), '\\n'))
            if (info.imageB) {
                let imgs = 'data:image/jpeg;base64,' + info.imageB
                let param = {}
                param.unEncrpt = true
                param.photoStr = info.imageB
                param.certId = this.showInfo.certId
                if ('1' == this.pzType) { //集团证件照片
                    this.groupPicObj.src = imgs
                    param.type = '10'
                } else if ('2' == this.pzType) { //授权书照片
                    this.agreePicObj.src = imgs
                    param.type = '11'
                } else if ('3' == this.pzType) {//门头照片
                    this.doorstepPicObj.src = imgs
                    param.type = '19'
                } else if ('4' == this.pzType) {//审批工单照片
                    this.approveOrderPicObj.src = imgs
                    param.type = '20'
                }
                this.uploadImg(param)
            }
        }


        window['paizhaoCb'] = info => {
            let imgs = 'data:image/jpeg;base64,' + info.fileImage
            let param = {}
            param.unEncrpt = true
            param.photoStr = info.fileImage
            param.certId = this.showInfo.certId
            if ('1' == this.pzType) { //集团证件照片
                this.groupPicObj.src = imgs
                param.type = '10'
            } else if ('2' == this.pzType) { //授权书照片
                this.agreePicObj.src = imgs
                param.type = '11'
            } else if ('3' == this.pzType) {//门头照片
                this.doorstepPicObj.src = imgs
                param.type = '19'
            } else if ('4' == this.pzType) {//审批工单照片
                this.approveOrderPicObj.src = imgs
                param.type = '20'
            }
            this.uploadImg(param)
        }

        window['certPaizhaoCb'] = info => {
            let param = {}
            param.unEncrpt = true
            param.image = info.fileImage
            let index = info.fileName.lastIndexOf('\.')
            let fileType = info.fileName.substring(index, info.fileName.length)
            param.fileType = fileType
            param.license = this.cardType.id
            //先识别ORC
            this.identificOrc(param)
        }
    },
    filters: {
        changYear(val) {
            if (!val) {
                return '无'
            }
            if (val.length == 14) {
                let year = val.substr(0, 4)
                let month = val.substr(4, 2)
                let day = val.substr(6, 2)
                return `${year}/${month}/${day}`
            }
        },
        changYearNew(val) {
            if (!val) {
                return ''
            }
            if (val.length == 8 || val.length == 14) {
                let year = val.substr(0, 4)
                let month = val.substr(4, 2)
                let day = val.substr(6, 2)
                return `${year}/${month}/${day}`
            } else {
                return val
            }
        }
    },
    methods: {
        //打开日期选择控件
        openDatePicker(idx) {
            if (this.groupBusiType != 1) {
                return
            }
            this.$refs.picker.open()
            this.datePicker = idx
        },
        //时间选择回调
        handleConfirm(val) {
            if (!val) {
                return
            }
            let time = dateFormat(val, 'yyyyMMdd')
            //集团证件的生效日期和失效日期
            if (this.datePicker == '1') {
                this.showInfo.certEffDate = time
            } else if (this.datePicker == '2') {
                this.showInfo.certExpDate = time
            } else if (this.datePicker == '3') {
                this.userEnterNetSfz.effDate = time
            } else if (this.datePicker == '4') {
                this.userEnterNetSfz.expDate = time
            } else if (this.datePicker == '5') {
                this.handleEffDate = time
            } else if (this.datePicker == '6') {
                this.handleExpDate = time
            }
        },
        //过户资格校验
        transUserCheck() {
            let url = `/xsb/personBusiness/transUser/h5transUserCheck?telNum=${this.telNum}`
            this.$http.get(url).then(res => {
                if (res.data.retCode === '0') {
                    this.initCustomerInfo()
                } else {
                    this.$alert(res.data.retMsg || '该号码不可进行过户')
                    history.go(-1)
                }
            })
        },
        //可以修改使用人的信息
        changeSYCardType() {
            // this.$alert('使用人信息无法更改，如需过户，请到前台受理')
            if (this.groupBusiType != 1) {
                return
            }
            NlDropdown(
                {
                    confirmBtn: false,
                    datalist: this.IdcardTypeList
                },
                (retVal) => {
                    if (retVal != this.syIdCardType) {
                        this.userEnterNetSfz = {}
                        this.userEnterNetSfzNew.custName = ''
                        this.userEnterNetSfzNew.certId = ''
                        //未实名
                        this.userIsRealName = false
                        if (this.showName) {
                            this.jbEnterNetSfz = {}
                            this.handledName = ''
                            this.handleNo = ''
                            this.jbIsRealName = false
                        }
                    }
                    this.syIdCardType = retVal
                }
            )
        },

        showPerson() {
            this.showName = !this.showName
            if (this.showName) { //如果是同一人
                this.jbIdCardType = this.syIdCardType //同类型证件
                if (this.userEnterNetSfzNew.certId) {
                    // this.handledName = this.userEnterNetSfzNew.custName
                    // this.handleNo = this.userEnterNetSfzNew.certId
                    //判断使用人有没有进行过实名认证
                    if (this.userEnterNetSfz.sfzObj && this.userEnterNetSfz.sfzObj.sfzName) {
                        this.handledName = this.userEnterNetSfz.sfzObj.sfzName
                        this.handleNo = this.userEnterNetSfz.sfzObj.sfzId
                    } else {
                        this.handledName = this.userEnterNetSfzNew.custName
                        this.handleNo = this.userEnterNetSfzNew.certId
                    }
                    this.jbEnterNetSfz = this.userEnterNetSfz
                } else if (!this.custNo && this.handleNo) {
                    this.userEnterNetSfzNew.certId = this.handleNo
                    this.userEnterNetSfzNew.custName = this.handledName
                    this.userEnterNetSfz = this.jbEnterNetSfz
                }
            } else if (!this.showName) {
                this.handledName = ''
                this.handleNo = ''
                this.jbEnterNetSfz = ''
                this.jbIsRealName = false
            }
        },

        //选择经办人证件类型
        changeJBCardType() {
            NlDropdown(
                {
                    confirmBtn: false,
                    datalist: this.IdcardTypeList
                },
                (retVal) => {
                    if (retVal != this.jbIdCardType) {
                        this.jbEnterNetSfz = {}
                        this.handledName = ''
                        this.handleNo = ''
                        this.jbIsRealName = false
                    }
                    this.jbIdCardType = retVal
                }
            )
        },

        //集团证件类型选择
        chooseType() {
            if (this.groupBusiType == '2') {
                return
            }
            let self = this
            NlDropdown(
                {
                    confirmBtn: false,
                    datalist: self.typeList
                },
                function(retVal) {
                    if (retVal != self.cardType) {
                        self.showInfo.certId = ''
                        self.showInfo.custName = ''
                        self.showInfo.custAddress = ''
                        self.showInfo.certEffDate = ''
                        self.showInfo.certExpDate = ''
                    }
                    self.cardType = retVal
                }
            )
        },
        async judgeSales() {
            let resData = await this.getOperatorInfo()
            let errorMsg = ''
            if (resData) {
                let { retCode, retMsg, data } = resData.data
                if (retCode == '0') {
                    if (data == '1') {
                        //调用通过入网手机号码查询客户入网证件信息
                        this.queryCustomerInfo()
                        return
                    } else {
                        errorMsg = '非自办厅营业员角色，不允许修改集团证件入网资料，请到营业厅前台受理'
                    }
                } else {
                    errorMsg = retMsg || '查询操作员信息服务失败'
                }
            } else {
                errorMsg = '查询操作员是否有入网权限网络超时，请重试'
            }
            this.$messagebox({
                title: '温馨提示',
                message: errorMsg,
                showCancelButton: false,
                showConfirmButton: true,
                closeOnClickModal: false,
                confirmButtonText: '确认'
            }).then(action => {
                history.go(-1)
            })
        },
        //通过入网手机号码查询客户入网证件信息
        async initCustomerInfo() {
            let enterInfo = await this.queryCustomerInfo(this.telNum)
            if (enterInfo) {
                let { retCode, retMsg, data } = enterInfo.data
                if (retCode == '0') {
                    //获取数据
                    this.groupBusiType = 1 //初始化为：个人号码转集团
                    this.cardType = { id: 'BusinessLicence', label: '营业执照' }
                    for (let i = 0; i < this.typeList.length; i++) {
                        if (this.typeList[i].id == data.custinfo.certType) {
                            this.groupBusiType = 2 //集团号码转个人
                            this.cardType = this.typeList[i]
                            this.showInfo = data.custinfo
                            console.log(this.showInfo)
                            //参与人节点 0 经办人 1 使用人  2 监护人
                            let peList = data.partnerList && data.partnerList.partnerInfo
                            console.log(peList)
                            //使用人信息
                            if (peList) {
                                this.userEnterNetSfzNew = peList.find(item => {
                                    return item.partnerType == '1'
                                })
                                //使用人证件类型
                                let certificateTypeTempUser
                                if (this.userEnterNetSfzNew.certType) {
                                    certificateTypeTempUser = this.IdcardTypeList.find(item => {
                                        return item.id == this.userEnterNetSfzNew.certType
                                    })
                                    this.syIdCardType = certificateTypeTempUser ? certificateTypeTempUser : this.syIdCardType
                                }
                                //使用人的信息，证件编号不能变，如果不一致，则提示不支持过户，请去前台受理
                                this.queryUserCertId = this.userEnterNetSfzNew.certId
                            }
                        }
                    }
                    if (this.groupBusiType != 2 && data.custinfo) {
                        this.userEnterNetSfzNew = data.custinfo
                        //使用人证件类型
                        let certificateTypeTempUser
                        if (this.userEnterNetSfzNew.certType) {
                            certificateTypeTempUser = this.IdcardTypeList.find(item => {
                                return item.id == this.userEnterNetSfzNew.certType
                            })
                            this.syIdCardType = certificateTypeTempUser ? certificateTypeTempUser : this.syIdCardType
                        }
                        this.queryUserCertId = this.userEnterNetSfzNew.certId
                    }
                    console.log(this.showInfo)

                } else {
                    this.$alert(retMsg || '通过入网手机号码查询客户入网证件信息失败')
                }
            } else {
                this.$alert('通过入网手机号码查询客户入网证件信息网络异常，请重试')
            }
        },
        //查询操作员是否是移动自由人员
        getOperatorInfo() {
            let url = '/xsb/personBusiness/groupEnterNet/h5qryOperatorAuthority'
            let params = {
                operMobile: this.uinfo.servNumber,
                operName: this.uinfo.operatorName
            }
            return this.$http.post(url, params)
        },

        //入网记录
        goList() {
            this.$router.push('/groupAccessList')
        },
        //输入框验证
        check() {
            if (!this.number) {
                this.showContent = false
            }
            if (this.number) {//营业执照
                let certIdLength = this.number.length
                if (this.cardType && this.cardType.id == 'UnionSocietyCredit') {//只能输入大写字母及数字且长度不超过18位（可以为18位）
                    if (certIdLength != 18) {
                        this.showContent = false
                    }
                } else if (this.cardType && this.cardType.id == 'BusinessLicence') {//营业执照
                    //营业执照：证件号码长度需满足15位、13位、18位、20位、22位、24位；
                    let range = [13, 15, 18, 20, 22, 24]
                    if (range.indexOf(certIdLength) < 0) {
                        this.showContent = false
                    }
                } else if (this.cardType && this.cardType.id == 'OrgaCredential') {
                    //（2）组织机构代码证：
                    //a）证件号码长度满足10位，其规则为“XXXXXXXX-X”，倒数第2位是“-”。
                    // b）证件号码长度为18位时，不校验证件号码规则。
                    if (certIdLength != 10 && certIdLength != 18) {
                        this.showContent = false
                    } else {
                        if (certIdLength == 10) {
                            let temp = this.number.substring(certIdLength - 2, certIdLength - 1)
                            if (temp != '-') {
                                this.showContent = false
                            }
                        }
                    }
                } else if (this.cardType && this.cardType.id == 'EnteAggrCredential') {
                    if (certIdLength != 12 && certIdLength != 18) {//事业单位法人登记证书：证件号码长度需满足12、18位。
                        this.showContent = false
                    }
                }
            }
        },
        //检验
        checkNum() {
            this.number = this.showInfo.certId
            if (!this.number) {
                this.$alert('请输入证件编号')
                return
            }
            if (this.number) {
                let certIdLength = this.number.length
                if (this.cardType && this.cardType.id == 'UnionSocietyCredit') {//只能输入大写字母及数字且长度不超过18位（可以为18位）
                    if (certIdLength != 18) {
                        this.$alert('统一社会信用代码证书证件号码必须为18位')
                        return false
                    }
                } else if (this.cardType && this.cardType.id == 'BusinessLicence') {//营业执照
                    //营业执照：证件号码长度需满足15位、13位、18位、20位、22位、24位；
                    let range = [13, 15, 18, 20, 22, 24]
                    if (range.indexOf(certIdLength) < 0) {
                        this.$alert('营业执照证件号码长度需满足13位、15位、18位、20位、22位、24位,当前长度为：' + certIdLength)
                        return false
                    }
                } else if (this.cardType && this.cardType.id == 'OrgaCredential') {
                    //（2）组织机构代码证：
                    //a）证件号码长度满足10位，其规则为“XXXXXXXX-X”，倒数第2位是“-”。
                    // b）证件号码长度为18位时，不校验证件号码规则。
                    if (certIdLength != 10 && certIdLength != 18) {
                        this.$alert('组织机构代码证长度只能为10位或18位,当前长度为：' + certIdLength)
                        return false
                    } else {
                        if (certIdLength == 10) {
                            let temp = this.number.substring(certIdLength - 2, certIdLength - 1)
                            if (temp != '-') {
                                this.$alert('组织机构代码证长度为10位时，倒数第2位应是\'-\'')
                                return false
                            }
                        }
                    }
                } else if (this.cardType && this.cardType.id == 'EnteAggrCredential') {
                    if (certIdLength != 12 && certIdLength != 18) {//事业单位法人登记证书：证件号码长度需满足12、18位。
                        this.$alert('事业单位法人登记证书证件号码只能为12位或18位,当前长度为：' + certIdLength)
                        return false
                    }
                }
            }
            let url = `/xsb/personBusiness/groupEnterNet/h5CheckGroupEnterNet?certId=${this.number}&certType=${this.cardType.id}`
            this.$http.get(url).then((res) => {
                let { retCode, data, retMsg } = res.data
                if (retCode == '0') {
                    if (!this.showInfo) {
                        this.showInfo = {} // 确保 showInfo 是一个对象
                    }

                    this.$set(this.showInfo, 'custName', data.custName)
                    this.$set(this.showInfo, 'custAddress', data.certAddr)
                    this.$set(this.showInfo, 'certEffDate', data.certEffdate)
                    this.$set(this.showInfo, 'certExpDate', data.certExpdate)
                } else {
                    this.$messagebox({
                        title: '温馨提示',
                        message: '确认证件号码是否正确?',
                        showCancelButton: true,
                        showConfirmButton: true,
                        confirmButtonText: '是',
                        cancelButtonText: '否'
                    }).then(action => {
                        if (action == 'confirm') {
                            this.showContent = true
                        }
                    })
                }
            })
        },
        //隐藏姓名
        hideCustName(name) {
            if (name && name.length > 1) {
                let prefix = name.substr(0, 1)
                let bustr = ''
                for (let i = 1; i < name.length; i++) {
                    bustr += '*'
                }
                return prefix + bustr
            }
            return ''
        },
        //隐藏身份证号码
        hideIdCard(card) {
            if (card) {
                let prefix = card.substr(0, 6)
                let suffix = card.substr(card.length - 4, 4)
                let bustr = ''
                for (let i = 6; i < card.length - 4; i++) {
                    bustr += '*'
                }
                return prefix + bustr + suffix
            }
            return ''
        },
        //打开实名认证
        openRealNameCertify(data) {
            this.flag = data
            if (data == '1' && this.syIdCardType.id != 'IdCard') {
                this.noIdcardType = this.syIdCardType
                //把其它信息带过去
                Storage.set('modifyCustName', this.userEnterNetSfzNew.custName)
                Storage.set('modifyCustAdd', this.userEnterNetSfzNew.custAddress)
                Storage.set('modifyCertId', this.userEnterNetSfzNew.certId)
                this.showNoIdCardRealNameCertify = true
                return
            } else if (data == '2' && this.jbIdCardType.id != 'IdCard') {
                this.noIdcardType = this.jbIdCardType
                this.showNoIdCardRealNameCertify = true
                return
            }
            if (data && data == '2') {
                this.faceCheckFlag = '1'
                this.readCardBusinessType = 'group_enter_net_jbr'
            } else {
                this.faceCheckFlag = '0'
                this.readCardBusinessType = this.CONSTVAL.BUSI_TYPE_MOD_GROUP_ENTER_NET
            }
            this.showRealNameCertify = true
        },
        //选择其它证件类时的回调方法
        comfirmRealNameData(userData) {
            this.showNoIdCardRealNameCertify = false
            this.showRealNameCertify = false
            if (this.flag == 1) { //使用人非身份证认证方式结果回调
                //使用人回调时需要校验和查询出来的是否一致
                this.userEnterNetSfz = userData
                //数据在页面回显
                this.userEnterNetSfzNew.custName = userData.sfzObj.sfzName
                this.userEnterNetSfzNew.certId = userData.sfzObj.sfzId
                //使用人已认证过
                this.userIsRealName = true
                this.netSrl = userData.netSrl
                if (this.showName) {
                    //使用人和经办人是同一人
                    this.handledName = userData.sfzObj.sfzName
                    this.handleNo = userData.sfzObj.sfzId
                    this.jbEnterNetSfz = userData
                }
            } else {
                //经办人
                this.jbEnterNetSfz = userData
            }


            if (this.flag == '2' && !this.showName) { //经办人的非身份证方式回调，并且和使用人不是同一人
                this.jbEnterNetSfz = userData
                this.handledName = userData.sfzObj.sfzName
                this.handleNo = userData.sfzObj.sfzId
                //经办人已认证过
                this.jbIsRealName = true
                if (this.custNo == this.handleNo) {//如果使用人的和经办人一致
                    this.showName = true
                }
            }
        },


        //实名认证回调(身份证的回调)
        realNameCallBack(value) {
            this.enterNetSfz = JSON.parse(JSON.stringify(value))
            console.log(this.enterNetSfz,this.queryUserCertId)
            this.showRealNameCertify = false
            if (this.flag == '1') { //使用人的身份证方式回调
                // if (this.enterNetSfz.sfzObj.sfzId != this.queryUserCertId) {
                //     this.$messagebox({
                //         title: '温馨提示',
                //         message: '集团过户不允许变更使用人信息，请重新进行使用人实名认证',
                //         showCancelButton: false,
                //         confirmButtonText: '确认'
                //     }).then((action) => {
                //         return
                //     })
                // } else {
                    //一致的情况下
                    this.userEnterNetSfz = this.enterNetSfz
                    //使用人已认证过
                    this.userIsRealName = true
                    this.netSrl = this.enterNetSfz.netSrl
                    //将新的信息展示在页面上
                    this.userEnterNetSfzNew.custName = this.enterNetSfz.sfzObj.sfzName
                    this.userEnterNetSfzNew.certId = this.enterNetSfz.sfzObj.sfzId
                    if (this.showName) {  //使用人和经办人是同一人
                        this.handledName = this.enterNetSfz.sfzObj.sfzName
                        this.handleNo = this.enterNetSfz.sfzObj.sfzId
                        this.jbEnterNetSfz = this.enterNetSfz
                    }
                // }
            }
            if (this.flag == '2' && !this.showName) { //经办人的身份证方式回调，并且和使用人不是同一人
                this.jbEnterNetSfz = this.enterNetSfz
                this.handledName = this.enterNetSfz.sfzObj.sfzName
                this.handleNo = this.enterNetSfz.sfzObj.sfzId
                //经办人已认证过
                this.jbIsRealName = true
                if (this.custNo == this.handleNo) {//如果使用人的和经办人一致
                    this.showName = true
                }
            }
        },
        //实名认证返回
        realNamePrev() {
            this.showRealNameCertify = false
            this.showNoIdCardRealNameCertify = false
        },
        //提交
        async groupTransferSubmit() {

            this.number = this.showInfo.certId

            if (!this.number) {
                this.$alert('请输入证件编号')
                return
            }
            if (!this.showInfo.custName) {
                this.$alert('集团证件名称不能为空')
                return
            }

            if (!this.showInfo.custAddress) {
                this.$alert('集团证件地址不能为空')
                return
            }

            let addressValidFlag = await this.judgeAddress(this.showInfo.custAddress)
            if (!addressValidFlag) {
                this.$alert('集团证件地址超长，请简短地址信息')
                return
            }


            if (!this.showInfo.certEffDate) {
                this.$alert('集团证件生效日期不能为空')
                return
            } else {
                this.showInfo.certEffDate = (this.showInfo.certEffDate.length > 8) ? this.showInfo.certEffDate.substring(0, 8) : this.showInfo.certEffDate
            }
            if (!this.showInfo.certExpDate) {
                this.$alert('集团证件失效日期不能为空')
                return
            } else {
                this.showInfo.certExpDate = (this.showInfo.certExpDate.length > 8) ? this.showInfo.certExpDate.substring(0, 8) : this.showInfo.certExpDate
            }

            //使用人信息实名认证填写判断
            if (this.groupBusiType == '1' && !this.userIsRealName) {
                this.$alert('使用人信息未实名认证，请认证')
                return
            }


            if (!this.groupPicObj.name) {
                this.$alert('集团证件照不能为空')
                return
            }
            //授权委托书不能为空
            if (!this.agreePicObj.name) {
                this.$alert('授权委托书不能为空')
                return
            }

            //使用人信息实名认证填写判断
            if (!this.userIsRealName) {
                this.$alert('使用人信息未实名认证，请认证')
                return
            }
            //经办人信息填写判断,如果是同一人，则不需要判断了，如果不是同一个人则需要判断经办人是否实名
            if (!this.showName) {
                if (!this.jbIsRealName) {
                    this.$alert('经办人信息未实名认证，请认证')
                    return
                }
            }
            if (this.sixPassword !== this.sixPassword2) {
                this.$alert('两次输入的密码不一致')
                return
            }
            if (this.sixPassword.length !== 6) {
                this.$alert('请输入6位服务密码')
                return
            }
            if (this.sixPassword2.length !== 6) {
                this.$alert('请确认6位初始服务密码')
            }
            //增加判断，如果修改了使用人给出提示，确认后再进行下一步一证五号的校验操作,否则将使用人认证置为未认证
            // if (this.queryUserCertId != this.userEnterNetSfzNew.certId) {
            //     this.$messagebox({
            //         title: '温馨提示',
            //         message: '集团过户不允许变更使用人信息，请重新进行使用人实名认证',
            //         showCancelButton: false,
            //         confirmButtonText: '确认'
            //     }).then((action) => {
            //         return
            //     })
            // } else {
                this.$messagebox({
                    title: '提示',
                    message: '确认提交？',
                    showCancelButton: true,
                    showConfirmButton: true
                }).then(action => {
                    if (action == 'confirm') {
                        this.confirmUser()
                    }
                });
            // }
        },
        //更换使用人，点击确认后调用的方法
        confirmUser() {
            console.log(this.userEnterNetSfz)
            console.log(this.jbEnterNetSfz)
            //校验通过，用使用人的信息获取预占码
            let urlYuZhan = '/xsb/personBusiness/notcradyupeihao/h5idcampon'
            let paramYuZhan = {
                userName: this.userEnterNetSfz.sfzObj.sfzName, //使用人姓名
                certId: this.userEnterNetSfz.sfzObj.sfzId, //使用人证件编号
                certType: this.syIdCardType.id, //使用人证件类型
                netSrl: this.netSrl, //人脸照片唯一编码
                unLoadFlg: true
            }
            this.$http.post(urlYuZhan, paramYuZhan).then((res) => {
                let { retCode, retMsg, data } = res.data
                if (retCode === '0') {
                    this.pseq = data.pseq //预占码
                    this.submitAz()
                } else {
                    this.$alert(retMsg || '集团过户获取预占码失败')
                }
            }).catch((err) => {
                this.$alert('集团过户获取预占码网络异常')

            })
        },
        submitAz() {
            //预占码获取成功，跳转爱知无纸化
            let url = '/xsb/personBusiness/groupEnterNet/h5groupPersonTransPreSubmit'
            let deviceType = this.uinfo.device

            try {
                let param = {
                    'stationId': this.uinfo.stationId,// 岗位id
                    'longitude': Storage.get('longitude'),// 经度
                    'latitude': Storage.get('latitude'), // 纬度
                    'location': Storage.get('location'), // 定位信息
                    'deviceType': deviceType, // 设备类型 android,ios
                    'telNum': this.telNum,
                    'password': this.sixPassword,
                    'cusName': this.userEnterNetSfz.sfzObj.sfzName,
                    'cusGender': ~this.userEnterNetSfz.sfzObj.sex.indexOf('男') ? '1' : (~this.userEnterNetSfz.sfzObj.sex.indexOf('女') ? '0' : '9'),
                    'certType': this.syIdCardType.id,
                    'certId': this.userEnterNetSfz.sfzObj.sfzId,
                    'certAdd': this.userEnterNetSfz.sfzObj.sfzAddr,
                    'cusAdd': this.userEnterNetSfz.sfzObj.sfzAddr,
                    'attachmentsId': 'ALD_' + this.userEnterNetSfz.sfzObj.sfzId + '_ZF.jpg,' + '' + this.userEnterNetSfz.netSrl + '.jpg,'
                        + 'ALD_' + this.userEnterNetSfz.sfzObj.sfzId  + '_ZF.jpg,' + this.userEnterNetSfz.netSrl + '.jpg',
                    'transType': this.groupBusiType,
                    'pseq': this.pseq,
                    'authChkType': "AuthCheckG",//固定二代证鉴权
                    'msisdn': this.telNum,
                    'grpcustcertAttachId': this.groupPicObj.attachId,
                    'grpcustproxypicId': this.agreePicObj.attachId,
                    'grpcustproxypic': this.agreePicObj.name,
                    'grpcustcertpic': this.groupPicObj.name,
                    'grpCertEffdate': this.showInfo.certEffDate,
                    'grpCertExpdate': this.showInfo.certExpDate,
                    'jbrEnterNetsfz': this.jbEnterNetSfz,
                    'userEnterNetsfz': this.userEnterNetSfz,
                    'videoStartAiFlag': this.videoStartAiFlag,//需要人工审核
                    'videoIsdaidu': this.isdaidu,//是否代读
                    'videoCollectAttachId': this.videoCollectAttachId,
                    'videoCheckHasVideoFlag': this.checkHasVideo(),//校验是否有视频
                    'zmAttachIds': this.zmAttachIds, // 收入证明附件编码
                    'uploadZmFlag': this.uploadZmFlag && this.zmSwitchFlag ? 'Y' : 'N', // 是否需要上传收入证明
                    'groupCertType': this.cardType.id
                }
                if (this.groupBusiType == 1) {//个人过户集团
                    param.newGrpCustInfo = {
                        'grpCertType': this.cardType.id,
                        'grpCertId': this.number,
                        'grpCustName': this.showInfo.custName,
                        'grpCertAddr': this.showInfo.custAddress,
                        'grpCustAddr': this.showInfo.custAddress,
                        'grpCertPic': this.groupPicObj.name,
                        'commission': this.agreePicObj.name,
                        'doorpic': '',
                        'approveId': ''
                    }
                }
                console.info(param)
                console.log(this.ySubmit)
                this.ySubmit(url, param)
            } catch (err) {
                this.$alert(err)
            }

        },
        //拍照
        openCamera(type) {
            this.pzType = type
            if (this.paizhaoPermission) {
                ClientJs.openCameraOne('paizhaoCallBack')
            } else {
                ClientJs.openCameraAndShow('1', 'paizhaoCb')
            }
        },

        //上传图片
        uploadImg(param) {
            let url = '/xsb/personBusiness/groupImsTrueName/h5upLoadImg'
            this.$http.post(url, param).then(res => {
                let { retCode, retMsg, data } = res.data
                if (retCode == '0') {
                    let attachName = data.attachName
                    if (this.pzType == '1') { //集团证件照片
                        this.groupPicObj.name = attachName
                        this.groupPicObj.has = true
                        this.groupPicObj.attachId = data.attachId
                    } else if (this.pzType == '2') { //授权书照片
                        this.agreePicObj.name = attachName
                        this.agreePicObj.has = true
                        this.agreePicObj.attachId = data.attachId
                    } else if (this.pzType == '3') { //门头照
                        this.doorstepPicObj.name = attachName
                        this.doorstepPicObj.has = true
                        this.doorstepPicObj.attachId = data.attachId
                    } else if (this.pzType == '4') { //审核工单
                        this.approveOrderPicObj.name = attachName
                        this.approveOrderPicObj.has = true
                        this.approveOrderPicObj.attachId = data.attachId
                    }
                } else {
                    this.$alert(retMsg || '上传图片失败请重新拍照')
                }
            })
        },
        //证件扫描函数
        scanCertInfo(val) {
            this.pzType = val
            ClientJs.openCameraAndShow('1', 'certPaizhaoCb')
        },
        //企业/个人证照识别并验真（验真仅企业证件）
        identificOrc(param) {
            let failMsg = '无法识别，请确认图片拍摄质量再重试，或者手动输入！'
            let url = `/xsb/personBusiness/groupArchives/h5IdentificOrc`
            this.$http.post(url, param).then(res => {
                if (res.data.retCode == '0') {
                    let data = res.data.data
                    if (data.ecInfo && data.ecInfo.name != null && data.ecInfo.uniscId != null) {
                        //ocr识别出的1.证件名称  2.证件地址   3.证件号码
                        this.showInfo.custName = data.ecInfo.name
                        this.number = data.ecInfo.uniscId
                        this.showInfo.certId = data.ecInfo.uniscId
                        this.showInfo.certId = data.ecInfo.uniscId
                        this.showInfo.custAddress = data.ecInfo.address
                        this.checkNum()
                    } else {
                        this.$alert(failMsg)
                    }
                } else {
                    this.$alert(failMsg)
                    this.defeaultTip = '请输入'
                }
            }).catch(res => {
                this.$alert('证照识别并验真网络异常:' + res)
            })
        },
        //若是政企视图或四个岗位进入则先判断是否有无自办厅营业员权限，若没有则不准办理；
        //若原客户经理进入，校验集团信息不通过才校验自有厅店长权限
        async judgeAuth() {
            //   let authId = '116228';//自有厅店长
            //   let url = `/xsb/personBusiness/businessOpen/h5qryOperAuth?authId=${authId}`;
            //   this.$http.get(url).then((res) => {
            //   let {retCode, data, retMsg} = res.data;
            //   if (retCode == "0") {
            //       this.showContent = true;
            //   } else {
            //     this.$alert('非自有厅店长角色，不允许首开，请到营业厅前台受理');
            //   }
            // }).catch((response) => {
            //   this.$alert("操作员令牌查询异常");
            // })
            //上面逻辑搞不了，传参错误无法判断是否为自有厅店长，改为下面逻辑
            let resData = await this.getOperatorInfo()
            if (resData) {
                let { retCode, retMsg, data } = resData.data
                if (retCode == '0') {
                    if (data == '1') {
                        this.showContent = true
                    } else {
                        this.$alert(retMsg || '非自有厅店长角色，不允许首开，请到营业厅前台受理')
                    }
                } else {
                    this.$alert(retMsg || '查询操作员信息服务失败')
                }
            } else {
                this.$alert('查询操作员是否有入网权限网络超时，请重试')
            }
        },
        judgeAddress(certAddr) {
            if (!certAddr) {
                return true
            }
            let str = certAddr
            var W = new Object()
            var iNumwords = 0
            var sNumwords = 0
            var sTotal = 0
            var iTotal = 0
            var eTotal = 0
            var inum = 0
            for (let i = 0; i < str.length; i++) {
                var c = str.charAt(i)
                if (c.match(/[\u4e00-\u9fa5]/)) {
                    if (isNaN(W[c])) {
                        iNumwords++
                        W[c] = 1
                    }
                    iTotal++
                }
            }
            for (let i = 0; i < str.length; i++) {
                var c = str.charAt(i)
                if (c.match(/[^\x00-\xff]/)) {
                    if (isNaN(W[c])) {
                        sNumwords++
                    }
                    sTotal++
                } else {
                    eTotal++
                }
                if (c.match(/[0-9]/)) {
                    inum++
                }
            }
            let len = iTotal * 2 + (sTotal - iTotal) * 2 + eTotal
            if (len > 128) {
                return false
            } else {
                return true
            }
        },

        getBusiPermission() {
            const param = {
                busiType: 'fea_gtzj_pz' //集团证件照片上传模式
            }
            this.$http.post('/xsb/ability/businessLimit/h5QryBusiPermission', param).then((res) => {
                if (res.data.retCode == '0') {
                    this.paizhaoPermission = true
                } else {

                }
            })
        },
        //点击拍照按钮
        doPaizhao() {
            this.paizhao(this.uinfo) //请求客户端拍照
        },

        //展示身份证照片
        checkSucess() {
            let targetSfz = {
                sfzObj: this.sfzObj,
                cardPicId: this.sfzAttachId,
                headId: this.headId,
                netSrl: this.netSrl,
                certType: this.sfzObj.idCardType,
                pseq: this.pseq,//拍照返回 证件欲占返回的
                isRare: this.isRare, // 是否包含生僻字
                srcFrom: this.srcFrom
            }
            this.targetSfz = targetSfz
            this.step = 3
            this.currentStep = 'step2'
            this.defaultImg()
            //视频采集权限查询
            this.permissionCheck('transfer_user_videoWhite')
        },

        //获取服务端的图片流失败的默认图片
        defaultImg() {
            let self = this
            let attachId = self.sfzAttachId
            self.getSfzImgFlg = false
            this.count++
            //attachId = "2022012610033103";
            if (self.count == 3) {
                self.$refs.sfzImg.src = 'static/img/default-sfz.png'
                self.count = 0
            } else {
                setTimeout(function() {
                    ImageObj.getImgUrl(attachId).then(res => {
                        self.$refs.sfzImg.src = res
                        self.getSfzImgFlg = true
                    })
                }, 400)
            }
        },

        //视频采集返回
        submitChoose(obj) {
            //白名单不采集视频
            if (this.videoCollectFlag && !obj.videoWhiteFlag) {
                this.reVideoChoice(obj.keyNumber, obj.dictName, this.telNumber)
            }
        },
        //开启视频采集
        openVideoChoiceButton() {
            this.openVideoChoice()
        },
        //视频播放
        playVideoButton() {
            this.playVideo()
        },
        //重拍
        reVideoChoiceButton(readTypeNumber, dictName) {
            this.reVideoChoice(readTypeNumber, dictName, this.telNumber)
        },
        //人工审核
        artificialCheckButton() {
            this.artificialCheck()
        }
    }

}
</script>

<style scoped lang='less'>
.wrapper {
    height: auto;
    border-top: 1px solid #EAEAEA;
    margin-bottom: 80px;
}

.cardInfo {
    font-size: 16px;
    margin-bottom: 8px;
    padding: 40px 12px 10px 12px;
    background: #fff;
    box-shadow: 0px 2px 4px 0px rgba(197, 197, 197, 0.5);

    p {
        font-size: 14px;
        color: #505050;
        margin-bottom: 4px;
    }

    .card-shop {
        margin-top: 10px;
        display: flex;
        justify-content: space-between;

        .shop-input {
            flex: auto;
            font-size: 14px;
            outline: none;

            &::-webkit-input-placeholder {
                color: #C5C5C5;
            }
        }
    }
}

.card-main {
    height: 105%;
    border: 1px solid #ccc;
    overflow-y: auto;

    .need-edit {
        color: red;
        font-size: 17px;
        margin-left: 3px;
        font-weight: normal;
    }

    .card-info {
        background: #fff;
        padding: 12px;
        margin-bottom: 8px;


        &.other-info {

            input {
                right: 0px;
                position: absolute;
                text-align: right;
                outline: none;
            }

            li {
                line-height: 18px;
            }
        }

        .info-ul {
            i {
                font-size: 14px;
                color: #BBBBBB;
            }

            .checkbox {
                color: #1c7ff1;
            }

            .shop-btn {
                text-align: center;
                width: 53px;
                color: #fff;
                height: 20px;
                line-height: 20px;
                border-radius: 12px;

                &.ocr-button {
                    float: right;
                    color: rgb(22, 129, 251);
                    right: 0;
                    position: absolute;
                }
            }


            margin-top: 10px;

            .time {
                color: black;
                margin-right: 5px;
            }

            li {
                color: #6C6C6C;
                font-size: 14px;
                margin-bottom: 8px;
                display: flex;
                position: relative;

                span {
                    color: #BBBBBB;
                    white-space: nowrap;
                }

                input {
                    font-size: 14px;
                    width: 63%;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    margin-right: 5px;
                }

                textarea {
                    font-size: 12px;
                    width: 58%;
                    margin-right: 5px;
                    padding: 8px;
                    border: 1px solid #ccc;
                    border-radius: 4px;
                    background-color: #fff;
                    resize: none; /* 禁止手动调整大小 */
                    outline: none;
                    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
                    transition: border-color 0.3s ease;
                    min-height: 25px; /* 设置最小高度 */
                    height: auto; /* 高度自适应 */
                    overflow-y: hidden; /* 初始时隐藏垂直滚动条 */

                    &:focus {
                        border-color: #1681fb;
                    }
                }

                .card-type {
                    i {
                        color: #1681fb !important;
                    }
                }

                .type-text {
                    color: #1681fb;
                }
            }
        }
    }

    h3 {
        color: #505050;
        font-size: 14px;

        i {
            font-size: 20px;
            color: #007AFF;
            vertical-align: -2px;
            margin-right: 2px;
        }
    }

    .pub-card {
        h3 {
            margin-top: 10px;
        }
    }

    .card-real {
        background: #fff;
        //margin-bottom: 120px;
        //padding: 12px;
        h3 {
            margin-top: 10px;
        }

        .choose-name {
            position: relative;

            .real {
                line-height: 20px;
                width: 64px;
                height: 20px;
                color: #fff;
                font-size: 12px;
                background: rgba(22, 129, 251, 1);
                border-radius: 17px;
                position: absolute;
                right: 0;
                text-align: center;
                bottom: 15%;
                transform: translateY(50%);
            }

            .change-type {
                line-height: 20px;
                height: 20px;
                font-size: 12px;
                border-radius: 17px;
                position: absolute;
                right: 0;
                text-align: center;
                bottom: 55%;
                color: #a3a3a3;
                max-width: 43%;
                display: flex;
                overflow: hidden;
                text-overflow: ellipsis;
                color: #1681fb;

                i {
                    font-size: 10px;
                }
            }

            &.top {
                margin: 10px 0 8px 0;
            }
        }
    }

    .card-other {
        background: #fff;
        margin-bottom: 12px;
        padding: 12px;

        .choose-name {
            position: relative;

            .real {
                line-height: 20px;
                width: 64px;
                height: 20px;
                color: #fff;
                font-size: 12px;
                background: rgba(22, 129, 251, 1);
                border-radius: 17px;
                position: absolute;
                right: 0;
                text-align: center;
                bottom: 15%;
                transform: translateY(50%);
            }

            .change-type {
                line-height: 20px;
                height: 20px;
                font-size: 12px;
                border-radius: 17px;
                position: absolute;
                right: 0;
                text-align: center;
                bottom: 55%;
                color: #a3a3a3;
                max-width: 43%;
                display: flex;
                overflow: hidden;
                text-overflow: ellipsis;
                color: #1681fb;

                i {
                    font-size: 10px;
                }
            }

            &.top {
                margin: 10px 0 8px 0;
            }
        }
    }

    .user-name {
        font-size: 14px;
        color: #6C6C6C;
        margin-bottom: 8px;

        .name {
            color: #BBBBBB;
        }

        .real-true {
            margin-left: 8px;
            display: inline-block;
            line-height: 16px;
            box-sizing: border-box;
            width: 55px;
            height: 16px;
            text-align: center;
            font-size: 12px;
            color: #71C43A;
            border: 1px solid rgba(113, 196, 58, 1);
        }
    }

    .select-real {
        i {
            color: #1680F9;
            margin-right: 4px;
        }

        margin-bottom: 8px;
        color: #6C6C6C;
        font-size: 14px;
    }

    .card-photo {
        padding: 12px;
        background: #fff;
    }

    .photo {
        border: 2px dashed #1681FB;
        border-radius: 8px;
        margin: 30px auto;
        width: 52%;
        height: 104px;
        text-align: center;
        position: relative;

        i {
            color: #1681FB;
            font-size: 38px;
            margin-top: 22px;
            display: block;
        }

        span {
            display: block;
            font-size: 14px;
            color: #1681FB;
            margin-top: 12px;
        }

        .img-src {
            width: 100%;
            height: 100%;
        }
    }

    .photo:last-child {
        margin-bottom: 16px;
    }

    .nodata {
        height: calc(100% - 110px);
        background: #fff;
        position: relative;
    }

    .empty {
        position: absolute;
        top: 30%;
        transform: translateY(-30%);
        width: 100%;
        margin: auto;
        text-align: center;
        color: #737373;
        font-size: 12px;
        background: #fff;

        img {
            width: 150px;
        }

        .tip-txt {
            text-align: center;
            margin-top: 8px;
            color: #bbb;
        }
    }

    //集团过户功能样式
    &.transfer-main {
        .card-info {
            //集团过户到个人不允许编辑集团信息
            &.no-edit {
                .change-type {
                    &.no-change {
                        i {
                            display: none;
                        }

                        span {
                            color: #4a4a4a;
                            margin-right: 4px;
                            font-size: 13px;
                        }
                    }
                }

                > ul {
                    pointer-events: none;
                    i {
                        display: none;
                    }

                    .shop-btn {
                        display: none;
                    }


                    input {
                        color: #4a4a4a;
                    }

                    textarea {
                        color: #4a4a4a;
                    }
                }
            }

            &.other-info {
                &.display {
                    display: block;
                    margin-bottom: 200px;
                }
            }
        }

        .card-real {
            margin-bottom: 12px;
        }


    }

    .select-span {
        input {
            margin-right: 20px !important;
        }

        i {
            right: 0px;
            position: absolute;
        }
    }

}

.cert-content {
    padding: 164px 16px 92px 16px;

    .cert-content-box {
        background-color: white;
        box-shadow: 0 3px 8px #dadada;

        .content-box-top {
            font-size: 16px;
            color: #737373;
            text-align: center;
            padding: 16px 0;
            border-bottom: 1px solid #f5f5f5;
        }

        .content-box-bottom {
            padding: 20px;

            .cert-img {
                width: 100%;
            }
        }
    }
}

.yph-box {
    border-radius: 10px 10px 0px 0px;
    background: #ffffff;
    padding: 0.3rem 1.5rem 0.6rem 1.5rem;
    border-bottom: 1px solid #E4E4E4;

    .video-box {
        display: flex;
        flex-flow: wrap;

        .replay-container {
            flex: 1;
            height: 30px;
            background: #FFF9EC;
            border-radius: 15px;
            border: 1px solid #F7EDD8;
            line-height: 30px;
            font-size: 12px;
            color: #494949;
            margin-bottom: 4px;
            min-width: 150px;
            margin-right: 12px;

            .duihao1 {
                font-size: 10px;
                margin-left: 12px;
                margin-right: 2px;
            }

            .sanjiao {
                font-size: 10px;
                margin-right: 2px;
            }

            .play {
                float: right;
                margin-right: 18px;
                font-weight: 600;
                font-size: 13px;
            }
        }

        .shipin-duordaidu {
            display: flex;

            .rebox {
                line-height: 30px;
                border-radius: 22px 0 0 22px;
                font-weight: bold;
                height: 30px;
                background: #1681FB;

                img {
                    margin-left: 8px;
                    width: 13px;
                }

                .txt {
                    padding-right: 4px;
                    font-size: 13px;
                    color: #FFFFFF;
                    letter-spacing: 2px;
                }
            }

            .rebox-budaidu {
                line-height: 30px;
                height: 30px;
                background: #0fa0f5;
                border-radius: 0 22px 22px 0;
                font-weight: bold;

                .txt {
                    padding-right: 6px;
                    padding-left: 4px;
                    font-size: 13px;
                    color: #FFFFFF;
                    letter-spacing: 2px;
                }
            }
        }

        .rebox-choice {
            line-height: 30px;
            flex: 0 0 78px;
            height: 30px;
            background: #1681FB;
            border-radius: 15px;

            img {
                margin-left: 15px;
                width: 13px;
            }

            .rebox-txt {
                font-size: 13px;
                color: #FFFFFF;
                letter-spacing: 2px;
            }
        }
    }

    .video-tip-msg {
        font-size: 14px;
        color: #F87227;
        line-height: 20px;
        margin-top: 4px;
        margin-bottom: 0.5rem;
    }
}

.part-title {
    letter-spacing: 0.5px;
    height: 42px;
    font-size: 14px;
    font-weight: 600;
    color: #8F8F8F;
    line-height: 43px;
}

.check-rebox {
    text-align: center;
    width: 100%;

    button {
        background: rgba(22, 129, 251, 1);
        border-radius: 22px;
        font-size: 14px;
        font-weight: 400;
        color: #FFF;
        padding: 0.25rem 1.0rem;
        border: none;
    }
}

.readOnly {
    color: #4a4a4a !important;

    &.important {
        color: #1681FB;
    }
}
</style>
