<template>
  <div>
    <Header :tsTitleTxt='headTitle' backType='custom' @emGoPrev='goPrev'></Header>
    <ul class='group-self'>
      <li class='self-item' @click='netClick(item.netUrl)' v-for='(item,index) in netTypes' :key='index'>
        <img :src='item.background' />
        <span>{{ item.text }}</span>
      </li>
    </ul>
  </div>
</template>

<script>
import Header from 'components/common/Header.vue'
import Storage from '@/base/storage'

export default {
  data() {
    return {
      netTypes: [{
        'text': '入网甩单',
        'background': 'static/businessNew/10010.png',
        'netUrl': '/groupEnterNetThrowOrder'
      },
        { 'text': '甩单办理', 'background': 'static/businessNew/10024.png', 'netUrl': '/groupThrowListOrder' }],
      headTitle: '集团入网甩单',
      srcFrom: '',
      uinfo:{},
      busiVersion: 0
    }
  },
  created() {
    this.srcFrom = this.$route.query.srcFrom || ''
    this.uinfo = Storage.session.get('userInfo');
    this.getBusiVersion()
  },
  mounted() {

  },
  methods: {
    goPrev() {
      if (this.srcFrom) {
        this.$router.push(this.srcFrom)
      } else {
        this.$router.push('/business')
      }
    },
    qryBusiPermission() {
        let url = `/xsb/ability/businessLimit/h5QryBusiPermission`
        let param = {
            busiType: 'fea_group_enter_net',
            unLoadFlg: true
        }
        return this.$http.post(url, param).then(res => {
            let { retCode } = res.data
            return retCode === '0'
        }).catch(() => {
            return false
        })
    },
    //点击事件
    async netClick(link) {
      if (link == '/groupThrowListOrder') {
        if (this.uinfo && (this.uinfo.stationId != this.CONSTVAL.STATION_KHJL)) {
          let resData = await this.getOperatorInfo()
          if (resData) {
            let { retCode, retMsg, data } = resData.data
            if (retCode == '0') {
              if (data == '1') {
                this.$router.push({ path: link })
              } else {
                this.$alert(retMsg || '非自办厅人员，目前仅针对自办厅人员开放，请使用自办厅工号办理')
              }
            } else {
              this.$alert(retMsg || '查询操作员信息服务失败')
            }
          } else {
            this.$alert('查询操作员是否有入网权限网络超时，请重试')
          }
        } else {
          let groupEnterAccess = await this.qryBusiPermission()
          if (groupEnterAccess) {
            this.$router.push({ path: link })
          } else {
            this.$alert('当前客户经理无集团证件入网办理权限，请向地市管理员申请权限')
          }
        }
      } else if (link) {
        this.$router.push({ path: link + `?busiVersion=${this.busiVersion}` })
      } else {
        this.$toast('暂未开放')
      }

    },
    //查询操作员是否是移动自由人员
    getOperatorInfo() {
      let url = '/xsb/personBusiness/groupEnterNet/h5qryOperatorAuthority'
      let params = {
        operMobile: this.uinfo.servNumber,
        operName: this.uinfo.operatorName,
      }
      return this.$http.post(url, params)
    },
    getBusiVersion() {
      let param = {
        busiName: 'group_throw_flag',
        busiKey: 'accessKey',
        defaultRegionValue: ''
      }
      let url = `/xsb/gridCenter/groupBroadBand/h5getBusiTypeZkFlag`
      return this.$http.post(url, param).then(async res => {
        let { retCode } = res.data
        if (retCode == '0') {
          this.busiVersion = '1'
        } else {
          this.busiVersion = '0'
        }
      })
    }
  },
  components: { Header }
}
</script>

<style scoped lang='less'>
.group-self {
  margin-top: 44px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 30px 35px;

  .self-item {
    text-align: center;
    width: 46%;
    margin-bottom: 20px;
    background: linear-gradient(137deg, rgba(255, 255, 255, 1) 0%, rgba(248, 248, 248, 1) 100%);
    box-shadow: 4px 4px 8px 0px rgba(0, 0, 0, 0.04);
    border-radius: 8px;
    background: #fff;
    padding: 20px 0;

    img {
      display: block;
      width: 3.75rem;
      height: auto;
      margin: 0 auto 10px auto;
    }

    span {
      font-size: 16px;
      color: #232323
    }
  }
}
</style>
