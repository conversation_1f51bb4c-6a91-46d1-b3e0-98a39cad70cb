<template>
    <div>
        <Header :tsTitleTxt='headTitle' backType='custom' @emGoPrev='goPrev'></Header>
        <ul class='group-self'>
            <li class='self-item' @click='netClick(item.netUrl,item)' v-for='(item,index) in netTypes' :key='index'>
                <img :src='item.background' />
                <span>{{ item.text }}</span>
                <div v-if='item.tipText'>{{ item.tipText }}</div>
            </li>
        </ul>
    </div>
</template>

<script>
import Header from 'components/common/Header.vue'
import Storage from '@/base/storage'
import AuthenctCheck from 'components/common/Authenticate2/index.js'
import Authenct from 'components/common/Authenticatejituanruwang/Authenticate'
import { groupEnterCommon } from 'components/business/GroupCard/common/commonData.js'

export default {
    mixins: [groupEnterCommon],
    data() {
        return {
            netTypes: [
                {
                    'text': '集团选号入网',
                    'background': 'static/business/10028.png',
                    'netUrl': '/groupInfoAccess',
                    'tipText': '',
                    'isJudgeAuth': true,
                },
                {
                    'text': '集团预配号入网',
                    'background': 'static/business/100383.png',
                    'netUrl': '/yuPeiHaoGroupEnter',
                    'tipText': '',
                    'isJudgeAuth': true,
                },
                {
                    'text': '集团资料完善',
                    'background': 'static/business/100028.png',
                    'netUrl': '/groupInfoModify',
                    'tipText': '（修改集团证件、使用人）',
                    'isAuth': true,
                    'isCheck': false,
                },
                {
                    'text': '集团过户',
                    'background': 'static/business/10003.png',
                    'netUrl': '/groupPersonTransfer',
                    'tipText': '（集团、个人互转）',
                    'isAuth': true,
                    'isCheck': true,
                    'isJudgeAuth': true,
                }
            ],
            headTitle: '集团入网管理',
            srcFrom: '',
            uinfo: {},
            enterAuthFlag: false, //入网权限
        }
    },
    created() {
        this.srcFrom = this.$route.query.srcFrom || ''
        this.uinfo = Storage.session.get('userInfo')
    },
    mounted() {

    },
    methods: {
        goPrev() {
            if (this.srcFrom) {
                this.$router.push(this.srcFrom)
            } else {
                this.$router.push('/business')
            }
        },
        //点击事件
        async netClick(link,item) {
            if (link) {
                if(item.isJudgeAuth) {
                    let enterNetAuthFlag = await this.judgeSales()
                    if (!enterNetAuthFlag) {
                        return
                    }
                }

                if (item.isAuth) {
                    if (item.isCheck) {
                        AuthenctCheck(
                            {
                                popFlag: true,
                                hasPwd: false,
                                idCardWay: false
                            }, obj => {
                                this.$router.push({ path: link })
                            }
                        )
                    } else {
                        Authenct(
                            {
                                popFlag: true,
                                hasPwd: false,
                                idCardWay: false
                            }, obj => {
                                this.$router.push({ path: link })
                            }
                        )
                    }
                } else {
                    this.$router.push({ path: link })
                }
            } else {
                this.$toast('暂未开放')
            }
        },
    },
    components: { Header }
}
</script>

<style scoped lang='less'>
.group-self {
    margin-top: 44px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding: 30px 35px;

    .self-item {
        text-align: center;
        width: 46%;
        margin-bottom: 20px;
        background: linear-gradient(137deg, rgba(255, 255, 255, 1) 0%, rgba(248, 248, 248, 1) 100%);
        box-shadow: 4px 4px 8px 0px rgba(0, 0, 0, 0.04);
        border-radius: 8px;
        background: #fff;
        padding: 20px 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-around;

        img {
            display: block;
            width: 3.75rem;
            height: auto;
            margin: 0 auto 14px auto;
        }

        span {
            font-size: 16px;
            color: #232323
        }
        div{
            font-size: 11px;
            margin-top: 5px;
            color: #3a3a3a;
        }
    }
}
</style>
