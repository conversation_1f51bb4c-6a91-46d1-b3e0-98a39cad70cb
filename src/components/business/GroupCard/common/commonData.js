import Storage from '@/base/storage';
import ClientJs from '@/base/clientjs'

export const groupEnterCommon = {
  mixins: [],
  data() {
    return {
        faceCheckFlag: '0',
        startTime: new Date(new Date().getFullYear() - 50, 0, 1),
        endTime: new Date(new Date().getFullYear() + 100, 11, 31),
        pickerVisible: '', //插件时间值
        showInfo: {},//集团证件展示信息
        showRealNameCertify: false,
        showNoIdCardRealNameCertify: false, //非身份证类型实名
        groupPicObj: {//证件照上传
            src: '',
            has: false,
            name: '',
            attachId: ''
        },
        agreePicObj: {//授权委托书上传
            src: '',
            has: false,
            name: '',
            attachId: ''
        },
        doorstepPicObj: {//门头照上传
            src: '',
            has: false,
            name: '',
            attachId: ''
        },
        approveOrderPicObj: {//审批工单上传
            src: '',
            has: false,
            name: '',
            attachId: ''
        },
        number: '',//营业执照编号
        showName: false,//按钮是否经办人
        showContent: false,//展示子内容
        custName: '',//使用人姓名
        custNo: '',//使用人身份证号
        handledName: '', //经办人姓名
        handleNo: '',//经办人身份证号
        handleAddr: '',//经办人身份证地址
        handleEffDate: '',//经办人身份证生效日期
        handleExpDate: '',//经办人身份证失效日期
        enterNetSfz: {},
        flag: '',//是否经办人
        typeList: [{ id: 'UnitsTestify', label: '单位证明（部队专用）' },
            { id: 'CorpAggrCredential', label: '社团法人登记证' },
            { id: 'OrgaCredential', label: '组织机构代码证' },
            { id: 'BusinessLicence', label: '营业执照' },
            { id: 'EnteAggrCredential', label: '事业单位法人登记证' },
            { id: 'UnionSocietyCredit', label: '统一社会信用代码证书' }],//集团证件类型
        cardType: { id: 'BusinessLicence', label: '营业执照' },//证件类型
        IdcardTypeList: [
            { id: 'IdCard', label: '居民身份证' },
            { id: 'IdCardGA', label: '港澳居民居住证' },
            { id: 'HKMCPassport', label: '港澳居民来往内地通行证（仅限港澳居民使用）' },
            { id: 'Passport', label: '护照（仅限外国公民使用）' },
            { id: 'HuKouBu ', label: '户口簿' },
            { id: 'HuaQiaoPassport', label: '华侨护照' },
            { id: 'PLA', label: '军人身份证' },
            { id: 'TempId', label: '临时身份证' },
            { id: 'IdCardTW', label: '台湾居民居住证' },
            { id: 'TaiBaoZheng', label: '台湾居民来往大陆通行证（仅限台湾居民使用）' },
            { id: 'IdCardEx', label: '外国人永久居留证' },
            { id: 'PolicePaper', label: '武装警察身份证' }
        ],
        jbIdCardType: { id: 'IdCard', label: '居民身份证' }, //经办人
        syIdCardType: { id: 'IdCard', label: '居民身份证' },
        noIdcardType: { id: 'IdCard', label: '居民身份证' },
        datePicker: '',//1 startTime~ 2endTime
        isShowGroup: false,//是否展示集团信息（ocr成功后展现)
        uinfo: '',//操作员信息
        throwOrder: {},
        selectedProvince: null,
        selectedCity: null,
        userEnterNetSfz: {},//使用人(提交入参)
        userEnterNetSfzNew: {},//接口返回
        jbEnterNetSfz: {},//经办人
        cardPicId: '',//经办人实名认证返回的
        queryUserCertId: '',//后台查询出的使用人，资料完善时这个字段无法更改
        userIsRealName: false, //使用人是否实名
        jbIsRealName: false, //经办人是否实名
        netSrl: '',//人脸照片唯一编码
        pseq: '',//预占码
        authChkType: 'AuthCheckB',//鉴权方式(默认服务密码) 06：服务密码(AuthCheckB)  07：验证码(AuthCheckR)  08：二代证(AuthCheckG)
        customer: {},
        paizhaoPermission: false
    }
  },
  methods: {
      //通过入网手机号码查询客户入网证件信息,包括使用人和集团信息（可无）
      queryCustomerInfo(telNum) {
          console.log(this.telNum)
          let url = `/xsb/gridCenter/grabSheet/h5QueryCustomerInfo`
          let params = {
              msisdn: telNum
          }
          return this.$http.post(url, params)
      },
      //校验地址
      isValidAddress(address) {
          // 定义省、市、区的正则表达式
          const provinces = ['北京', '天津', '河北', '山西', '内蒙古', '辽宁', '吉林', '黑龙江', '上海', '江苏', '浙江', '安徽', '福建', '江西', '山东', '河南', '湖北', '湖南', '广东', '广西', '海南', '重庆', '四川', '贵州', '云南', '西藏', '陕西', '甘肃', '青海', '宁夏', '新疆']
          const provincePattern = new RegExp(provinces.join('|'), 'g')
          const cityPattern = /市/g
          const districtPattern = /区|县/g

          // 检查省、市、区的匹配次数
          const provinceMatches = address.match(provincePattern)
          const cityMatches = address.match(cityPattern)
          const districtMatches = address.match(districtPattern)

          // 确保只有一个省（直辖市、自治区），一个市，一个区
          if ((provinceMatches ? provinceMatches.length : 0) !== 1) {
              this.$alert('请确保证件地址有且只包含一个省份、直辖市或者自治区')
              return false
          }
          if ((cityMatches ? cityMatches.length : 0) !== 1) {
              this.$alert('请确保证件地址有且只包含一个地市')
              return false
          }
          if ((districtMatches ? districtMatches.length : 0) !== 1) {
              this.$alert('请确保证件地址有且包含一个区县')
              return false
          }
          return true
      },
      async judgeSales(isNeedGoBack) {
          let resData = await this.getOperatorInfo()
          let errorMsg = '';
          if (resData) {
              let { retCode, retMsg, data } = resData.data
              if (retCode == '0') {
                  if (data == '1') {
                      return true
                  } else {
                      errorMsg = '合作厅不能办理公户卡入网功能，请联系厅管理员向地市管理员申请权限'
                  }
              } else {
                  errorMsg = retMsg || '查询操作员信息服务失败'
              }
              this.$messagebox({
                  title: '温馨提示',
                  message: errorMsg,
                  showCancelButton: false,
                  showConfirmButton: true,
                  closeOnClickModal: false,
                  confirmButtonText: '确认'
              }).then(action => {
                  if(isNeedGoBack){
                      history.go(-1)
                  }
              })
              return false
          } else {
              errorMsg = '查询操作员是否有入网权限网络超时，请重试'
              this.$messagebox({
                  title: '温馨提示',
                  message: errorMsg,
                  showCancelButton: false,
                  showConfirmButton: true,
                  closeOnClickModal: false,
                  confirmButtonText: '确认'
              }).then(action => {
                  history.go(-1)
              })
              return false
          }

      },
      //查询操作员是否是移动自由人员
      getOperatorInfo() {
          let url = '/xsb/personBusiness/groupEnterNet/h5qryOperatorAuthority'
          let params = {
              operMobile: this.uinfo.servNumber,
              operName: this.uinfo.operatorName,
          }
          return this.$http.post(url, params)
      },
  },
}
