<template>
  <div class='wrap'>
    <Header tsTitleTxt='支付清单' backType='custom' @emGoPrev='goPrev'></Header>
    <div class='deposit-content'>
      <div class='deposit-head'>
        <div class='head-number'>
          <div class='number-box' @click='chooseType'>
            <span class='number-name'>{{ payVal }}<i class='iconfont shangzhankai-copy'></i></span>
          </div>
          <NlInput class='number-input number-name' :max='11' v-model='tel' type='tel'
                   placeholder='请输入号码'></NlInput>
          <i v-show='tel' class='iconfont shanchu' @click="tel=''"></i>
        </div>
        <span class='search-btn' @click='getSearch'>搜索</span>
      </div>
      <div v-show="isLoading" class="loading-overlay">
        <div class="loading-spinner">正在加载中...</div>
      </div>
      <div class='pul' v-show='getList && getList.length > 0'>
        <mt-loadmore
          :top-method='loadTop'
          :bottom-method='loadBottom'
          :bottom-all-loaded='allLoaded'
          :auto-fill='false'
          topPullText='刷新'
          topDropText='释放更新'
          topLoadingText='刷新中···'
          bottomPullText='加载更多'
          bottomDropText='释放加载'
          bottomLoadingText='加载中···'
          ref='loadmore'
          bottomDistance='20'>
          <ul class='plist'>
            <li v-for='(items,index) in getList' :key='index'>
              <!-- <div class="plist-left"> -->
              <div class='left-top'>
                {{ items.payBusiInfo.busiNumber }}
                <span class='status' :class='status[items.state] && status[items.state].bg'>{{ getState(items) }}</span>
              </div>
              <div class='left-text mar'>订单号：{{ items.orderid }}</div>
              <!-- <div class="left-text">充值方式：{{items.payType | TypeSfiltertatus}}</div> -->
              <div class='left-date'>{{ items.createTime }}</div>
              <!-- </div> -->

              <!-- <div class="plist-right">
                <span class="money">{{items.realFee | chgYuan}}元</span>
                <span class="right-btn" v-show="items.state=='31' && (items.isInvoice=='1' || items.isInvoice == '0')"
                @click="goEmail(items)">{{items.isInvoice=='1' ? '推送邮箱' : '开票'}}</span>
              </div> -->
              <div class='pay_busi'>
                <div class='busi_item'>
                  <span class='item-bus item-icon'>{{ items.payBusiInfo.payBusiType | payBusiType }}</span>
                  <span class='item-type item-icon'>{{ items.payType | TypeSfiltertatus }}</span>
                </div>
                <span class='money'>{{ items.realFee | chgYuan }}<i>￥</i></span>
              </div>
              <!--20 待支付  23 支付中-->
<!--              <div class='button-pub' v-show="items.state=='20'">-->
<!--                <span class='pub-btn' @click.prevent='goDepositNew()'>去充值</span>-->
<!--              </div>-->
              <div class='button-pub' v-show="getState(items)==='支付中'">
                <span class='pub-btn' @click.prevent='orderResult(items)'>查结果</span>
              </div>
            </li>
          </ul>
        </mt-loadmore>
      </div>
      <div class='search-nodata' v-show='getList.length <= 0'>
        <NoDataPage tipTxt='暂无记录'></NoDataPage>
      </div>
    </div>
  </div>
</template>

<script>
import Header from 'components/common/Header.vue'
import NlInput from '@/panComponents/common/NlInput.vue'
import NlDropdown from 'components/common/NlDropdown/dropdown.js'
import NoDataPage from 'components/common/NoDataPage.vue'
import Storage from '@/base/storage'
import { Indicator } from 'mint-ui'

const PageSize = 20//每页条数
export default {
  components: { Header, NlInput, NoDataPage },
  data() {
    return {
      currPage: 1,
      tel: '',//手机号码
      getList: [],//列表数据
      list: [],//业务类型列表
      allLoaded: false,
      payType: '',//选择类型
      payVal: '全部',//选择的名字
      isLoading: false,//加载状态
      status: {
        '-1': { 'text': '已作废', bg: 'fail' },//支付状态
        '0': { 'text': '订单创建', bg: 'running' },
        '10': { 'text': '待审核', bg: 'running' },
        '11': { 'text': '审核通过', bg: 'success' },
        '12': { 'text': '审核不通过', bg: 'fail' },
        '20': { 'text': '待支付', bg: 'success' },
        '21': { 'text': '支付成功', bg: 'success' },
        '22': { 'text': '支付失败', bg: 'fail' },
        '23': { 'text': '支付中', bg: 'running' },
        '31': { 'text': '充值成功', bg: 'success' },
        '32': { 'text': '充值失败', bg: 'fail' },
        '33': { 'text': '支付超时', bg: 'fail' },
        '40': { 'text': '退款中', bg: 'running' },
        '41': { 'text': '退款成功', bg: 'success' },
        '42': { 'text': '退款失败', bg: 'fail' },
        '51': { 'text': '业务撤销成功', bg: 'success' },
        '52': { 'text': '业务撤销失败', bg: 'fail' },
        '60': { 'text': '待发货', bg: 'running' },
        '61': { 'text': '已发货', bg: 'running' },
        '62': { 'text': '已签收', bg: 'success' }
      },
      srcFrom: '',//来源
      totalNum: 0,//总数
      uinfo: {}//用户信息
    }
  },
  created() {
    let query = this.$route.query
    this.srcFrom = query.srcFrom || ''
    this.payType = query.payType || ''//进入页面查询的类型ID
    this.getType()//获取业务类型
    this.getDataList()//获取列表
    this.uinfo = Storage.session.get('userInfo')
  },
  methods: {
    goPrev() {
      this.$router.push('/deposit?srcFrom='+this.srcFrom)
      return
      if (this.srcFrom) {
        if (this.srcFrom == 'tool') {
          this.$router.push('/tools')
        } else if (~this.srcFrom.indexOf('/')) {
            this.$router.push(this.srcFrom)
        } else {
            this.$router.push('/' + this.srcFrom)
        }
      } else {
        this.$router.push('/tools')
      }
    },
    //搜索
    getSearch() {
      this.currPage = 1
      this.getDataList()
    },
    goDepositNew() {
      this.$router.push(this.srcFrom)
    },
    orderResult(item) {
      let url = `/xsb/personBusiness/pay/h5PayOrderResultNew?orderno=` + item.orderid
      this.$http.get(url).then((res) => {
        let { retCode, data, retMsg } = res.data
        if (retCode == '0') {
          this.$alert('更新支付状态成功')
          this.getSearch()
        } else {
          this.$alert('未查询到支付结果')
        }
      }).catch((response) => {
        this.$alert(`更新支付状态失败网络超时${response}`)
      })
    },
    //订单状态修改
    getState(item) {
      //如果item中的时间小于当前时间20分钟，则是支付超时
      let nowTime = new Date().getTime()
      let payTime = new Date(item.createTime).getTime()
      if (item.state=='23' && nowTime - payTime > 20 * 60 * 1000) {
        return this.status['33'] && this.status['33'].text
      }else{
        return this.status[item.state] && this.status[item.state].text
      }
    },
  //获取业务类型
  getType() {
    this.list = []
    let url = `/xsb/personBusiness/pay/h5GetPayBusiTypeList?unLoadFlg=true`
    this.$http.get(url).then((res) => {
      let { retCode, data, retMsg } = res.data
      if (retCode == '0') {
        if (data && data.length > 0) {
          data.unshift({ id: '', label: '全部' })
          this.list = data
          if (this.payType) {
            for (let i = 0; i < this.list.length; i++) {
              if (this.list[i].id == this.payType) {
                this.payVal = this.list[i].label
                break
              }
            }
          }
        }
      } else {
        this.$alert(retMsg || '获取业务类型失败')
      }
    }).catch((response) => {
      this.$alert('获取业务类型网络超时')
    })
  },
  //选择类型
  chooseType() {
    NlDropdown({
      confirmBtn: false,
      datalist: this.list
    }, retVal => {
      this.payType = retVal.id
      this.payVal = retVal.label
    })
  },
  //获取列表
  getDataList() {
    this.isLoading = true
    let url = `/xsb/personBusiness/pay/h5GetPayInfoList?pageNum=${this.currPage}&pageSize=${PageSize}&
				payBusiType=${this.payType}&busiNumber=${this.tel}`
    this.$http.get(url).then((res) => {
      this.isLoading = false
      let { retCode, data, retMsg } = res.data
      if (retCode == '0') {
        this.totalNum = data.total
        if (data.pageData.length > 0) {
          let dataPage = []//去除paybusiInfo为空
          for (let i = 0; i < data.pageData.length; i++) {
            if (data.pageData[i].payBusiInfo) {
              dataPage.push(data.pageData[i])
            }
          }
          if (dataPage.length > 0 && dataPage) {
            if (this.currPage == 1) {
              this.getList = []
              this.getList = dataPage
            } else {
              this.getList.push(...dataPage)
            }
          }
        } else {
          this.getList = []
        }
      } else {
        this.getList = []
        this.$alert(retMsg || '获取订单接口失败')
      }
    }).catch((response) => {
      this.isLoading = false
      this.getList = []
      this.$alert('获取订单接口网络超时')
    })
  },
  loadTop() {
    // 加载更多数据
    this.$refs.loadmore.onTopLoaded()
    this.currPage = 1
    this.getDataList()
  },
  loadBottom() {
    this.$refs.loadmore.onBottomLoaded()
    if (this.currPage * PageSize < this.totalNum) {
      this.currPage++
      this.getDataList()
    } else {
      this.$toast('全部加载完毕')
      return
    }
  }
}
,
filters:{
  //支付状态
  TypeSfiltertatus(val)
  {
    if (val == 'WECHAT') {
      return '微信支付'
    } else if (val == 'ALIPAY') {
      return '支付宝支付'
    } else if (val == 'QUICKPAY') {
      return '银联云闪付'
    } else {
      return '和包支付'
    }
  }
,
  //根据业务类型判断类型
  payBusiType(data)
  {
    if (data == 'mband_prod_kaitong') {//宽带开通
      return '宽度开通'
    } else if (data == 'tel_recharge') {//充值
      return '充值'
    } else if (data == 'arrears_pay') {//欠费缴费
      return '欠费缴费'
    }
    return ''
  }
,
  //判断钱是否是整数
  chgYuan(val)
  {
    if (!val) {
      return 0
    }
    try {
      let money = parseInt(val)
      return money / 100
    } catch (error) {
      return 0
    }
  }
}
,
}
</script>

<style scoped lang='less'>
@import '../../../base/less/variable.less';

.wrap {
  height: 100%;
  overflow: hidden;
}

.deposit-content {
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: hidden;
  height: 100%;
}

// .search-box{
// 	background: #fff;
// 	padding:12px 16px;
// 	margin-top:44px;
// 	border-top:1px solid #EAEAEA;
// 	box-sizing: border-box;
// }
// .search{height: 30px;background: #F1F1F1;overflow: hidden;
//     border-radius: 15px;position: relative;}
// .s-ipt{background: none;
//     outline: none;
//     height: 20px;
//     font-size: 12px;
//     color: #8F8F8F;
//     border: none;
//     text-indent: 25px;
//     width: 100%;
//     margin: 5px;
//     line-height: 20px;
// }
.deposit-head {
  margin-top: 44px;
  background: #fff;
  display: flex;
  padding: 10px 12px;
  align-items: center;
  border-top: 1px solid #F0F0F0;
  box-sizing: border-box;

  .head-number {
    position: relative;
    height: 30px;
    line-height: 30px;
    background: #F2F2F2;
    border-radius: 8px;
    display: flex;
    flex: 1;
    align-items: center;

    .number-box {
      position: relative;
    }

    .number-position {
      position: absolute;
      background: #fff;
      width: 100%;
      transition: all 0.2s linear;
      opacity: 0;
      text-align: center;

      &.show {
        opacity: 1;
        z-index: 99;
      }

      span {
        font-size: 12px;
        padding: 4px 0;
        display: block;
      }
    }

    .shanchu {
      font-size: 12px;
      color: #acacac;
      position: absolute;
      right: 14px;
    }

    .number-name {
      font-size: 12px;
      color: #232323;
      padding-left: 10px;

      i {
        font-size: 14px;
        color: #000000;
        vertical-align: -1px;
      }
    }

    .number-input {
      width: 57%;
      padding-left: 10px;
      font-size: 12px;
      background: transparent;
      outline: 0;
    }
  }

  .number-more {
    margin: 0 10px;
    font-size: 14px;
    color: #232323;

    i {
      color: #CBCBCB;
      font-size: 12px;
    }
  }

  .search-btn {
    background: #1681FB;
    font-size: 14px;
    color: #fff;
    text-align: center;
    width: 55px;
    height: 28px;
    line-height: 28px;
    margin-left: 8px;
    // padding:4px 14px;
    border-radius: 16px;
    border: 1px solid rgba(22, 129, 251, 1);
    box-sizing: border-box;
  }
}

.s-btn-icon {
  font-size: 18px;
  color: #828282;
  position: absolute;
  top: 7px;
  left: 5px;
}

.icon-delete {
  width: 16px;
  height: 16px;
  position: absolute;
  right: 54px;
  top: 7px;
  color: #D8D8D8;
}

.search-text {
  position: absolute;
  right: 16px;
  top: 9px;
  color: #1681FB;
  font-size: 12px;
}

.pul {
  flex: 1;
  overflow: auto;
  // touch-action: none;
  padding: 8px;
}

.plist {
  li {
    background: #fff;
    padding: 12px 20px 12px 16px;
    margin-bottom: 8px;
    position: relative;
    border-radius: 8px;
  }
}

// .plist-left{
// 	flex:1;
.left-top {
  color: #030303;
  font-size: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .status {
    font-size: 12px;
    box-sizing: border-box;
    padding: 0 4px;
    height: 22px;
    line-height: 22px;
    display: inline-block;

    &.success {
      color: #71C43A;
      border: 1px solid rgba(113, 196, 58, 1);
    }

    &.fail {
      border: 1px solid rgba(250, 35, 107, 1);
      color: #FA236B;
    }

    &.running {
      border: 1px solid rgba(243, 160, 22, 1);
      color: #F3A016;
    }

    &.styles {
      border: 1px solid #1681FB;
      color: #1681FB;
    }
  }
}

.left-text {
  font-size: 14px;
  color: #A0A5AB;

  &.mar {
    margin: 8px 0;
  }
}

.left-date {
  color: #A1A1A1;
  font-size: 14px;
  // margin-top:6px;
}

//}
.pay_busi {
  align-items: center;
  margin-top: 6px;
  display: flex;
  justify-content: space-between;
}

.busi_item {
  .item-bus {
    background: rgba(239, 244, 255, 1);
    color: #3D64D5;
    margin-right: 4px;
  }

  .item-type {
    color: #F87227;
    background: #FFF3DF;
  }

  .item-icon {
    border-radius: 4px;
    font-size: 10px;
    padding: 0 8px;
    height: 18px;
    line-height: 18px;
    display: inline-block;
  }
}

.money {
  font-size: 24px;
  color: #CC6801;
  font-weight: 600;

  i {
    font-size: 12px;
    font-style: normal;
  }
}

// .plist-right{
// 	position:absolute;
// 	right:20px;
// 	top: 50%;
// 	transform: translateY(-25%);
// 	.money{
// 		color:#CC6700;
// 		font-size:20px;
// 		display:block;
// 		text-align: center;
// 		margin-bottom:4px;
// 	}
// 	.right-btn{
// 		display: block;
// 		text-align: center;
// 		width:76px;
// 		line-height:30px;
// 		height:30px;
// 		box-sizing: border-box;
// 		font-size:12px;
// 		color:#1680F9;
// 		border-radius:17px;
// 		border:1px solid rgba(22,128,249,1);
// 	}
// }
.msgbox-wrapper {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0.5;
  background: #000;
}

.msgbox {
  position: fixed;
  top: 50%;
  left: 50%;
  -webkit-transform: translate3d(-50%, -50%, 0);
  transform: translate3d(-50%, -50%, 0);
  background-color: #fff;
  width: 85%;
  border-radius: 7px;
  font-size: 16px;
  -webkit-user-select: none;
  overflow: hidden;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-transition: .2s;
  transition: .2s;

  .msgbox-content {
    padding: 20px 8px 0 8px;

    .msgbox-message {
      text-align: center;
      font-size: 20px;
      margin-bottom: 15px;
      color: #000000;
    }

    .msgbox-input {
      width: 100%;

      .inputs {
        outline: none;
        background: #F1F1F1;
        text-indent: 21px;
        width: 100%;
        height: 30px;
        line-height: 30px;
        color: #6B6B6B;
        font-size: 12px;
        border-radius: 16px;
      }

      .msgbox-errormsg {
        color: red;
        font-size: 12px;
        padding: 10px 0 0 10px;
      }
    }
  }

  .msgbox-btns {
    display: flex;
    margin-top: 22px;
    height: 48px;
    line-height: 48px;
    border-top: 1px solid #DDDDDD;
    box-sizing: border-box;

    .msgbox-btn {
      line-height: 48px;
      display: block;
      background-color: #fff;
      -webkit-box-flex: 1;
      -ms-flex: 1;
      flex: 1;
      margin: 0;
      border: 0;

      &.msgbox-cancel {
        box-sizing: border-box;
        border-right: 1px solid #ddd;
      }

      &.msgbox-confirm {
        color: #26a2ff;
      }
    }
  }
}

.button-pub {
  text-align: right;
  margin-top: 8px;
  border-top: 1px solid #EAEAEA;
  padding-top: 10px;

  .pub-btn {
    border: 1px solid #0081FF;
    border-radius: 15px;
    color: #0081FF;
    font-size: 12px;
    padding: 5px 10px;
  }
}

.search-nodata {
  margin-top: 40px;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-spinner {
  font-size: 16px;
  color: #fff;
  background: rgba(0, 0, 0, 0.6);
  padding: 20px 30px;
  border-radius: 8px;
}
</style>
