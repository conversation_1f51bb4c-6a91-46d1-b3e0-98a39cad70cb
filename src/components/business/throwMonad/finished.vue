<template>
  <div @click='closeAll'>
    <div class="head wrapper-medias">
      <div class="gl-title">
        <!--后退按钮-->
        <span class="iconfont zuojiantou gl-back" @click="goPrev"></span>
        <!--标题-->
        <span class="gl-title-txt">{{tsTitleTxt}}</span>
        <!--右侧文字按钮-->
        <span v-if="!this.isQw" class="iconfont jiahao2 txt-btn" @click="doSignAuth"></span>
      </div>
    </div>
    <div class="line"></div>
    <!-- 搜索框 -->
    <div class="search-wrapper">
      <div class="ipt-wrapper">
        <span class="iconfont sousuo1"></span>
        <input type="tel" class="seach-ipt" v-model="companyn" maxlength="11" placeholder="请输入手机号码" />
        <span class="query-btn" @click="search">搜索</span>
      </div>
    </div>
    <div class="status">
      <div @click.stop="all=!all;inProcess=true" >
        <span :class="{colorb:!all}">{{type}}</span>
        <span class="iconfont jiantou11 size change-direct" v-show="all"></span>
        <span class="iconfont jiantou11 size colorb" v-show="!all"></span>
      </div>
      <div @click.stop="inProcess=!inProcess;all=true">
        <span :class="{colorb:!inProcess}">{{status}}</span>
        <span class="iconfont jiantou11 size change-direct" v-show="inProcess"></span>
        <span class="iconfont jiantou11 size colorb" v-show="!inProcess"></span>
      </div>
      <div class="alls" v-show="!all">
        <div class="content">
          <div v-for="items in list" :key="items.id" :class="{choosed:choseda==items.id}" @click="chooseAll(items)">{{items.titles}}</div>
        </div>
      </div>
      <div class="inProcess" v-show="!inProcess">
        <div class="content">
          <div v-for="items in lists" :key="items.id" :class="{choosed:chosedb==items.id}" @click="chooseStatus(items)">{{items.titles}}</div>
        </div>
      </div>
        <div @click.stop="changeTime()" >
            <span>选择时间段</span>
            <span class="iconfont jiantou11 size change-direct"></span>
        </div>
    </div>
      <div class="status time">
              <span>当前时间段：{{startTime}}&nbsp;~</span>
              <span>&nbsp;{{endTime}}</span>
      </div>
    <!-- 暂无数据 -->
    <NoDataPage class="nodata" tipTxt="暂无数据" v-if="finished.length==0" style="margin-top:10px;"></NoDataPage>
    <mt-loadmore
      class="loader-padding"
      :top-method="loadTop"
      :bottom-method="loadBottom"
      :auto-fill="false"
      topPullText="刷新"
      topDropText="释放更新"
      topLoadingText="刷新中···"
      ref="loadmore"
      v-else
    >
      <div class="lista" v-for="(items,index) in finished" :key="index">
      <div class="lists">
        <div class="phoneNum">{{items.order_info_dt.acc_nbr}}</div>
        <div>
          <span class="titles">预约：</span>
          <span class="answers">{{items.order_info_dt.offerremark}}</span>
        </div>
        <div>
          <span class="titles">甩单时间：</span>
          <span class="answers">{{items.order_info_dt.create_time | toDate}}</span>
        </div>
        <div>
          <span class="titles">甩单对象：</span>
          <span class="answers">{{items.order_info_dt.oper_id}}</span>
        </div>
        <div class="processed">
          <div class="statu pedding">{{items.order_info_dt.status}}</div>
        </div>
      </div>
      <div class="hu" v-if="items.order_info_dt.service_code==30052">呼</div>
      <div class="timer" v-if="items.order_info_dt.offer_name">
        <div>
          <span class="titles">办理业务：</span>
          <span class="answers">{{items.order_info_dt.offer_name}}</span>
        </div>
        <div>
          <span class="titles">办理时间：</span>
          <span class="answers">{{items.order_info_wh.process_time | toDate}}</span>
        </div>
      </div>
    </div>
    </mt-loadmore>
    <NlButton enableTip='创建远程甩单' @click="createOrder" ></NlButton>

  </div>
</template>

<script>

import Authenct from 'components/common/Authenticate/Authenticate'
import AuthenctSwing from 'components/common/AuthenticateSwing/Authenticate'
import Authenctindex from 'components/common/uniteauth/index.js'
import Storage from '@/base/storage'
import { dateFormat,chgStrToDate } from "@/base/utils";
import NoDataPage from 'components/common/NoDataPage'
import NlDatePicker from "components/common/NlDatePick/datePicker.js";
import NlButton from 'components/common/NlButton';

export default {
  data() {
    return {
      tsTitleTxt: "已甩工单",
      all:true,
      inProcess:true,
      list:[
        {titles:"全部工单",id:"30052|30053"},
        {titles:"外呼甩单",id:"30052"},
        {titles:"预约甩单",id:"30053"}
      ],
      lists:[
        {titles:"全部类型",id:"00"},
        {titles:"待确认",id:"11"},
        {titles:"交易成功",id:"88"},
        {titles:"已作废",id:"-1"},
        {titles:"办理失败",id:"61"}
      ],
      type:'全部工单',
      status:'全部类型',
      choseda:'30052|30053',
      chosedb:'00',
      jqData:'',
      finished:[],
      companyn:'',
      page: 1, // 当前页码
      size: 10, // 页面长度
        startTime:"",
      endTime:"",
      isQw:false,
    }
  },
  computed: {},
  components: { NoDataPage,NlDatePicker,NlButton },
  methods: {
    goPrev() {
      if (this.backType === "custom") {
        this.$emit("emGoPrev");
      } else {
        history.go(-1);
      }
    },
    chooseAll(item){
      this.choseda=item.id;
      this.type=item.titles;
      this.getList(false);
    },
    chooseStatus(item){
      this.chosedb=item.id;
      this.status=item.titles;
      this.getList(false);
    },
    doSignAuth(){
      let _this=this;
      // 判断是否从客户视图来的并且已有鉴权信息，如果有则直接跳转不需要再次鉴权
      let srcFrom = this.$route.query.srcFrom;
      let jqData = Storage.session.get('jqData');
      
      if (srcFrom === 'csView' && jqData && jqData.telnum) {
        // 从客户视图来的且有鉴权信息，直接跳转
        _this.$router.push({path:'/profession'});
      } else {
        // 其他情况需要鉴权
        AuthenctSwing({
          popFlag: true,
          // rongHeFlag: this.rongHeFlag,//是否二次鉴权
          telnum: this.telnum,
          faceIdentifyFlag: true,//是否刷脸
          dimTelnumFlg: true,
          // faceIdentifyAttachId:this.authAt=====tmptachId,//图片标识
          // digitalFlag:this.digitalFlag,//数字鉴权
        }, function (obj) {
          _this.$router.push({path:'/profession'});
        });
      }
    },
    // 下拉刷新
    loadTop() {
      this.$refs.loadmore.onTopLoaded();
      this.page = 1;
      this.size = 10;
      this.getList(false);
    },
    // 上拉加载
    loadBottom() {
      this.$refs.loadmore.onBottomLoaded();
      this.page++;
      this.getList(true);
    },
    getList(append){
      // if (this.page * this.size<)
      let param = {
        service_code:this.choseda,
        acc_nbr:this.companyn,
        status:this.chosedb,
        page_index:this.page,
        page_size:this.size,
        oper_id:Storage.session.get("userInfo").crmId,
        authentication:1,
          s_time:dateFormat(chgStrToDate(this.startTime+"","yyyy/MM/dd hh:mm:ss"),"yyyyMMddhhmmss"),
          e_time:dateFormat(chgStrToDate(this.endTime+"","yyyy/MM/dd hh:mm:ss"),"yyyyMMddhhmmss")
      };
    this.$http
      .post("/xsb/personBusiness/throwOrder/h5QryOrdersInfo",param)
      .then(res => {
        if(res.data.retCode==0){
          var tempDate = [];
          res.data.data.forEach((item, index) => {
            tempDate.push(item);
          });
          console.log(tempDate.length);
          if (append) {
               if (tempDate.length <= 0) {
                this.$toast("暂无更多数据");
              } else {
                res.data.data.forEach((item, index) => {
                  this.finished.push(item);
                });
              }
            } else {
              this.finished = res.data.data;
            }
        }else{
          //this.$alert(res.data.retMsg)
        }
      });
    },
    closeAll(){
      this.all=true;
      this.inProcess=true;
    },
    search(){
      this.getList();
    },
      //改变查询时间
      changeTime(){
          let self  = this;
          NlDatePicker({
              startDate: this.startTime,
              endDate: this.endTime,
              dateType:'datetime',
              format:'yyyy/MM/dd hh:mm:ss',
          },(retVal) =>{
              if (chgStrToDate(retVal.endDate+"","yyyy/MM/dd hh:mm:ss").getTime()-chgStrToDate(retVal.startDate+"","yyyy/MM/dd hh:mm:ss").getTime() > 90*24*60*60*1000){
                  self.$toast("结束时间与开始时间相差不得超过九十天")
              }else{
                  self.startTime = retVal.startDate;
                  self.endTime = retVal.endDate;
                  self.getList();
              }
          });
      },
      toDate(val){
          return dateFormat(chgStrToDate(val+"","yyyy/MM/dd hh:mm:ss"),"yyyy/MM/dd hh:mm:ss");
      },
      getBeforeDate(n) {
        let date = new Date();
        let year, month, day,h,m,s;
        date.setDate(date.getDate() - n);
        year = date.getFullYear();
        month = date.getMonth() + 1;
        day = date.getDate();
        h = date.getHours();
        m = date.getMinutes();
        s = date.getSeconds();
        return year + '/' + (month < 10 ? ('0' + month) : month) + '/' + (day < 10 ? ('0' + day) : day)+" "+h+":"+m+":"+s;
    },
      // 远程甩单
    async createOrder(){
        // 企业微信来的已经鉴权过了，不需要再次鉴权
        let telnum='';
        if(window.triggerFrom && window.triggerFrom == 'qw') {
          // 前置校验甩单
           const res = await this.checkLimit();
          if (res.data.retCode != '0') {
            this.$alert(`当前工号【${Storage.session.get("userInfo").crmId}】无权限,信息已记录`);
            return;
          }
           telnum=Storage.session.get("qw_custNumber")
        }
        Authenct({
          popFlag:true,
          hasPwd:'',
          readOnlyFlag:false,
          onlyTelnum:"",
          telnum:telnum
        },(obj)=>{
          this.$router.push({path:'/profession',query:{remoteFlag:'1'}});
        });
    },

    // 校验甩单权限
    checkLimit() {
      let param = {
        busiType: 'fea_throw_order'
      }
      return this.$http.post('/xsb/ability/businessLimit/h5QryQwBusinessAuthority', param);
    }
  },
  created() {
    this.jqData=Storage.session.get('jqData');
    this.startTime = this.getBeforeDate(30);
    this.endTime = dateFormat(new Date(), ("yyyy/MM/dd hh:mm:ss"));
    this.getList();

    if(window.triggerFrom && window.triggerFrom == 'qw') {
      this.isQw = true;
    }
  },
  filters:{
    toDate(val){
      return dateFormat(chgStrToDate(val+"","yyyy/MM/dd hh:mm:ss"),"yyyy/MM/dd hh:mm:ss");
    }
  }
};
</script>

<style lang='less' scoped>
.head {
  height: auto;
  overflow: hidden;
  position: fixed;
  top: 0px;
  left: 0px;
  right: 0px;
  z-index: 99;
}

.gl-title {
  height: 44px;
  text-align: center;
  position: relative;
  background: #fff;
}

.gl-back {
  position: absolute;
  left: 0px;
  top: 50%;
  font-size: 20px;
  padding: 8px;
  transform: translateY(-50%);
}

.gl-back:before {
  color: #007aff;
}

.gl-title-txt {
  color: @color-TextTitle;
  display: inline-block;
  margin-top: 14px;
}

.gl-close {
  width: 20px;
  height: 20px;
  display: block;
  position: absolute;
  top: 12px;
  right: 10px;
  font-size: 18px;
  line-height: 20px;
}

.txt-btn {
  width: auto;
  position: absolute;
  top: 0px;
  right: 10px;
  line-height: 44px;
  font-size: 20px;
  color: #6f6d6d;
}
.line {
  width: 100%;
  height: 1px;
  background-color: #eaeaea;
  margin-top: 44px;
}
// 搜索框
.search-wrapper {
  height: 56px;
  padding: 12px 12px;
  box-sizing: border-box;
  background-color: #fff;
  .ipt-wrapper {
    width: 100%;
    height: 32px;
    line-height: 32px;
    border-radius: 16px;
    background: rgba(241, 241, 241, 1);
    padding: 0 12px;
    box-sizing: border-box;
    position: relative;
    .seach-ipt {
      font-size: 12px;
      width: 100%;
      height: 28px;
      padding-left: 20px;
      padding-right: 40px;
      background: rgba(241, 241, 241, 1);
      box-sizing: border-box;
      &:focus {
        outline: none;
      }
    }
    .sousuo1 {
      position: absolute;
      left: 12px;
      top: 50%;
      color: #828282;
      transform: translateY(-50%);
    }
    .query-btn {
      position: absolute;
      right: 0;
      top: 50%;
      color: #007aff;
      right: 12px;
      transform: translateY(-50%);
      font-size: 12px;
    }
  }
}
.status {
  width: 100%;
  height: 30px;
  background-color: #fff;
  font-size: 12px;
  display: flex;
  line-height: 30px;
  color: #525252;
  padding-left: 20px;
  box-sizing:border-box;
  position:relative;
  font-family:PingFangTC-Medium,PingFangTC;
  .size {
    font-size: 8px;
    color: #c4c4c4;
    display:inline-block;
  }
  .change-direct{
    transform: rotate(180deg);
  }
  .colorb{
    color:#0166FF;
  }
  .alls,
  .inProcess{
    position:absolute;
    top:30px;
    left:0;
    width:100%;
    z-index:1000;
    background-color: #fff;
    box-shadow:0px 4px 9px 0px rgba(0,0,0,0.15);
    border-top:2px solid rgb(240,241,244);
    .content{
      margin:5px 16px;
      display:flex;
      flex-wrap: wrap;
      .choosed{
        color:#0166FF;
        background-color: #D5E8FF;
      }
    }
    .content>div{
      height:30px;
      background-color: rgb(240,241,244);
      text-align: center;
      width:30%;
      margin:5px;
    }
  }
}
.status > div {
  margin-right: 12px;
}
.lista {
  margin: 10px 8px 0 8px;
  // padding: 10px 0 20px 20px;
  background-color: #fff;
  border-radius: 8px;
  font-size: 14px;
  overflow: hidden;
  position: relative;
  box-shadow:0px 3px 14px 0px rgba(238,238,238,0.7);

  .lists{
    margin:10px 0 20px 20px;
  }
  .phoneNum {
    font-size: 16px;
    color: #030303;
    font-family:PingFangSC-Semibold,PingFang SC;
  }
  .lists > div {
    margin-top: 10px;
  }
  .titles {
    color: #a0a5ab;
  }
  .answers {
    color: #414141;
    line-height:16px;
  }
  .statu {
    font-size: 12px;
    padding: 4px;
  }
  .processed {
    position: absolute;
    top: -2px;
    right: 15px;
    display:flex;
  }
  .hook {
    color: #2ed156;
    font-size:12px;
    margin-left:3px;
  }
  .colora{
    color:#767775;
    font-size:12px;
  }
  .success {
    color: #8fd065;
    border: 1px solid #8fd065;
  }
  .pedding{
    color:#4B8FFF;
    border:1px solid #4B8FFF;
  }
  .hu{
    position:absolute;
    background-color: #FF9932;
    font-size:12px;
    top:-12px;
    left:-12px;
    width:30px;
    height:35px;
    color:#fff;
    border-radius:50%;
    padding:16px 0 0 15px;
    box-sizing:border-box;
  }
  .timer{
    border-top:2px dashed #DFDFDF;
    margin-top:10px;
    background:rgba(251,251,251,1);
    padding: 5px 0 10px 20px;
  }
  .timer>div{
    margin-top:10px;
  }
}
.loader-padding{
  padding-bottom: 80px;
}
// .listb {
//   background-color: #EEEEEE;
//   margin: 10px 8px 0 8px;
//   background-color: #fff;
//   border-radius: 8px;
//   font-size: 14px;
//   overflow: hidden;
//   position: relative;
//   box-shadow:0px 3px 14px 0px rgba(238,238,238,0.7);
//   .processed {
//     position: absolute;
//     top: 8px;
//     right: 15px;
//   }
//   .lists{
//     padding: 10px 0 5px 20px;
//   }
//   .titles {
//     color: #a0a5ab;
//   }
//   .answers {
//     color: #414141;
//   }
//   .hook {
//     color: #2ed156;
//     font-size:12px;
//   }
//   .colora{
//     color:#767775;
//     font-size:12px;
//   }
//   .timer{
//     border-top:2px dashed #DFDFDF;
//     margin-top:10px;
//     background:rgba(251,251,251,1);
//     padding: 5px 0 10px 20px;
//   }
//   .lists > div {
//     margin-top: 10px;
//   }
//   .timer>div{
//     margin-top:10px;
//   }
// }
.status.time{
    background: #fbf8f8;
    color: #777;
    font-size: 11px;
    font-weight: lighter;
}
</style>
