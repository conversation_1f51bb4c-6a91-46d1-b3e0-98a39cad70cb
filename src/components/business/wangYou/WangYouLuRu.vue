<template>
  <div class="wrapper">
    <Header tsTitleTxt="问题投诉" backType="custom" @emGoPrev="goPrev" ></Header>

        <section class="contentBox" v-show="!showMapFlag">
          <div class="installInfo">
            <ul class="installUl">
              <li>
                <span class="installUl-title">工单标题<em class='installUl-title-red'>*</em></span>
                <input class="installUl-input" v-model="orderTitle" placeholder="请输入" type="text">
              </li>
              <li >
                <span class="installUl-title">区县<em class='installUl-title-red'>*</em></span>
                <a class="installUl-choose" @click="openQuXianNameMode">
                  <span class="locationText" :class="{'villageColor':distName=='点击选择'}">{{distName}}</span>
                  <span class="iconfont jiantou-copy-copy moreIcon"></span></a>
              </li>
              <li>
                <span class="installUl-title">无线归属网格<em class='installUl-title-red'>*</em></span>
                <a class="installUl-choose" @click="openEngineer">
                  <span class="locationText" :class="{'villageColor':selectEngineer.label=='点击选择'}">{{selectEngineer.label}}</span>
                  <span class="iconfont jiantou-copy-copy moreIcon"></span>
                </a>
              </li>
              <li>
                <span class="installUl-title">支撑无线工程师<em class='installUl-title-red'>*</em></span>
                <a class="installUl-choose">
                  <span class="locationText padding-style" :class="{'villageColor':selectEngineer.engName==''}">{{selectEngineer.engName}}</span>
                </a>
              </li>
              <li>
                  <span class="installUl-title">详细地址<em class='installUl-title-red'>*</em></span>
                  <div class='installUl-choose address' @click="openLocal">
<!--                    <input class="installUl-input" v-model="address" placeholder="请输入" type="text">-->
                    <span class="locationText padding-style" :class="{'no-address': !address}">{{ address ? address : '请选择详细地址' }}<span class="iconfont zb fresh"></span></span>
                  </div>
             </li>
              <li >
                <span class="installUl-title">场景<em class='installUl-title-red'>*</em></span>
                <a class="installUl-choose" @click="openSceneNameMode">
                  <span class="locationText" :class="{'villageColor':sceneName=='点击选择'}">{{sceneName}}</span>
                  <span class="iconfont jiantou-copy-copy moreIcon"></span></a>
              </li>
              <li >
                <span class="installUl-title">问题现象<em class='installUl-title-red'>*</em></span>
                <a class="installUl-choose" @click="openProblemDescMode">
                  <span class="locationText" :class="{'villageColor':problemDesc=='点击选择'}">{{problemDesc}}</span>
                  <span class="iconfont jiantou-copy-copy moreIcon"></span></a>
              </li>
              <li>
                <span class="installUl-title">现场处理手机号码</span>
                <input class="installUl-input" v-model="dealPhone" placeholder="请输入" type="text">
              </li>
              <li>
                <span class="installUl-title">现场处理联系人</span>
                <input class="installUl-input" v-model="dealPerson" placeholder="请输入" type="text">
              </li>
              <li>
                <span class="installUl-title">投诉人员号码<em class='installUl-title-red'>*</em></span>
                <input class="installUl-input" v-model="complainPhone" placeholder="请输入" type="text">
              </li>
              <li>
                <span class="installUl-title">手机型号</span>
                <input class="installUl-input" v-model="trmnBrandCode" placeholder="请输入" type="text">
              </li>
              <li>
                <span class="installUl-title">问题描述<em class='installUl-title-red'>*</em></span>
                <textarea class="installUl-textarea" v-model="complainDesc" rows="4" placeholder="请输入"></textarea>
              </li>
            </ul>
          </div>
        </section>

    <!--选取位置-->
    <MapLocal v-if="showMapFlag"
              :isComponent="isComponent"
              @emCloseMap="closeMap"
              @emComfirmMap="confirmMap"></MapLocal>
    <NlButton enableTip="工 单 录 入"  @click="wangYouInsert()" count="60" defaultState="counting"></NlButton>

  </div>

</template>

<script>
import Header from 'components/common/Header.vue'
import NlButton from 'components/common/NlButton';
import NlDropdown from 'components/common/NlDropdown/dropdown.js'
import Storage from '@/base/storage'
import { dateFormat } from '@/base/utils'
import MapLocal from '../../map/index.vue'

export default {
  components: { MapLocal, Header, NlButton,NlDropdown},
  mixins: [],
  props: {},
  data() {
    return {
      uinfo:{},
      srcFrom:"",
      orderTitle:"",//工单标题
      address:"",//详细地址
      sceneName:"点击选择",//场景
      problemDesc:"点击选择",//问题现象
      dealPerson:"",//现场处理联系人
      trmnBrandCode:"",//手机型号
      dealPhone:"",//现场处理手机号码
      complainPhone:"",//投诉人员号码
      complainDesc:"",//问题描述
      cityName:"",//地市
      distName:"点击选择",//区县
      longitude:"",//经度
      latitude:"",//纬度
      gridName:"",//网格名称
      dispatchAccount:"",//网格通账户
      dispatchId:"",//网格通单号
      curponType:{id:'',label:''},
      //场景 枚举值
      sceneNameList:[{id:'1',label:'城镇居民区'},{id:'2',label:'集团单位'},{id:'3',label:'乡镇农村居民区'},{id:'4',label:'党政军机关'}, {id:'5',label:'商场'},
                     {id:'6',label:'写字楼'},{id:'7',label:'酒店'},{id:'8',label:'学校'},{id:'9',label:'景区'},{id:'10',label:'交通道路/枢纽'}],
      //问题现象 枚举值
      problemDescList:[{id:'1',label:'通话不清晰/掉线'},{id:'2',label:'视频卡顿'},{id:'3',label:'游戏卡顿'},{id:'4',label:'网页/微信刷新慢'},
                       {id:'5',label:'信号弱/无信号'},{id:'6',label:'支撑需求'}],
      suRegionDistList:[{id:'1',label:'常熟市'},{id:'2',label:'张家港市'},{id:'3',label:'吴江区'},{id:'4',label:'昆山市'},{id:'5',label:'太仓市'},
                        {id:'6',label:'姑苏区'},{id:'7',label:'吴中区'},{id:'8',label:'相城区'},{id:'9',label:'虎丘区'},],
      huaiRegionDistList:[{id:'1',label:'淮安区'},{id:'2',label:'盱眙县'},{id:'3',label:'金湖县'},{id:'4',label:'洪泽区'}
                        ,{id:'5',label:'涟水县'},{id:'6',label:'淮阴区'},{id:'7',label:'清江浦区'}],
      qianRegionDistList:[{id:'1',label:'沭阳县'},{id:'2',label:'泗洪县'},{id:'3',label:'泗阳县'},{id:'4',label:'宿城区'}
                         ,{id:'5',label:'宿豫区'}],
      nanRegionDistList:[{id:'1',label:'江宁区'},{id:'2',label:'六合区'},{id:'3',label:'溧水区'},{id:'4',label:'高淳区'}
                         ,{id:'5',label:'浦口区'},{id:'6',label:'秦淮区'},{id:'7',label:'鼓楼区'},{id:'8',label:'玄武区'}
                         ,{id:'9',label:'栖霞区'},{id:'10',label:'建邺区'},{id:'11',label:'雨花台区'}],
      lianRegionDistList:[{id:'1',label:'东海县'},{id:'2',label:'赣榆区'},{id:'3',label:'灌云县'},{id:'4',label:'灌南县'}
                         ,{id:'5',label:'连云区'},{id:'6',label:'海州区'}],
      xuRegionDistList:[{id:'1',label:'丰县'},{id:'2',label:'沛县'},{id:'3',label:'新沂市'},{id:'4',label:'邳州市'}
                         ,{id:'5',label:'睢宁县'},{id:'6',label:'贾汪区'},{id:'7',label:'铜山区'},{id:'8',label:'鼓楼区'}
                        ,{id:'9',label:'泉山区'},{id:'10',label:'云龙区'}],
      changRegionDistList:[{id:'1',label:'溧阳市'},{id:'2',label:'金坛区'},{id:'3',label:'武进区'},{id:'4',label:'天宁区'}
                         ,{id:'5',label:'新北区'},{id:'6',label:'钟楼区'}],
      zhenRegionDistList:[{id:'1',label:'丹阳市'},{id:'2',label:'扬中市'},{id:'3',label:'句容市'},{id:'4',label:'丹徒区'}
                           ,{id:'5',label:'京口区'},{id:'6',label:'润州区'}],
      wuRegionDistList:[{id:'1',label:'江阴市'},{id:'2',label:'宜兴市'},{id:'3',label:'滨湖区'},{id:'4',label:'锡山区'}
                          ,{id:'5',label:'新吴区'},{id:'6',label:'梁溪区'},{id:'7',label:'惠山区'}],
      tongRegionDistList:[{id:'1',label:'通州区'},{id:'2',label:'海门区'},{id:'3',label:'启东市'},{id:'4',label:'海安市'}
                        ,{id:'5',label:'如东县'},{id:'6',label:'如皋市'},{id:'7',label:'港闸区'},{id:'8',label:'崇川区'},{id:'9',label:'开发区'}],
      taiRegionDistList:[{id:'1',label:'海陵区'},{id:'2',label:'姜堰区'},{id:'3',label:'靖江市'},{id:'4',label:'泰兴市'}
                         ,{id:'5',label:'兴化市'},{id:'6',label:'高港区'}],
      yanRegionDistList:[{id:'1',label:'响水县'},{id:'2',label:'滨海县'},{id:'3',label:'阜宁县'},{id:'4',label:'射阳县'}
                         ,{id:'5',label:'建湖县'},{id:'6',label:'东台市'},{id:'7',label:'大丰区'},{id:'8',label:'盐都区'}
                         ,{id:'9',label:'盐开'},{id:'10',label:'亭湖区'}],
      yangRegionDistList:[{id:'1',label:'江都区'},{id:'2',label:'仪征市'},{id:'3',label:'宝应县'},{id:'4',label:'高邮市'}
                         ,{id:'5',label:'邗江区'},{id:'6',label:'广陵区'}],
      wireGrid: "",
      wireEngineer: "",
      engineerList: [],
      selectEngineer: {id: "", label: "请选择", engName: ""},
      showMapFlag: false,
      isComponent: true,
    }
  },
  methods: {
    goPrev() {
      history.go(-1);
    },
    //区县枚举
    openQuXianNameMode(){
      let self = this;
      let ListCountry =[];
      if(self.uinfo.region =='11'){
        ListCountry =self.suRegionDistList;
      }else if(self.uinfo.region =='12'){
        ListCountry =self.huaiRegionDistList;
      }else if(self.uinfo.region =='13'){
        ListCountry =self.qianRegionDistList;
      }else if(self.uinfo.region =='14'){
        ListCountry =self.nanRegionDistList;
      }else if(self.uinfo.region =='15'){
        ListCountry =self.lianRegionDistList;
      }else if(self.uinfo.region =='16'){
        ListCountry =self.xuRegionDistList;
      }else if(self.uinfo.region =='17'){
        ListCountry =self.changRegionDistList;
      }else if(self.uinfo.region =='18'){
        ListCountry =self.zhenRegionDistList;
      }else if(self.uinfo.region =='19'){
        ListCountry =self.wuRegionDistList;
      }else if(self.uinfo.region =='20'){
        ListCountry =self.tongRegionDistList;
      }else if(self.uinfo.region =='21'){
        ListCountry =self.taiRegionDistList;
      }else if(self.uinfo.region =='22'){
        ListCountry =self.yanRegionDistList;
      }else if(self.uinfo.region =='23'){
        ListCountry =self.yangRegionDistList;
      }

      NlDropdown({
        confirmBtn: false,
        datalist: ListCountry,
      }, (retVal) => {
        self.distName = retVal.label;
        self.wangYouQryEngineerInfo();
      });
    },
    //场景 枚举值
    openSceneNameMode(){
      let self = this;
      NlDropdown({
        confirmBtn: false,
        datalist: self.sceneNameList,
      }, (retVal) => {
        self.sceneName = retVal.label;
      });
    },
    //问题现象 枚举值
    openProblemDescMode(){
      let self = this;
      NlDropdown({
        confirmBtn: false,
        datalist: self.problemDescList,
      }, (retVal) => {
        self.problemDesc = retVal.label;
      });
    },
    openEngineer(){
      NlDropdown({
        confirmBtn: false,
        datalist: this.engineerList,
      }, (retVal) => {
        this.gridName = retVal.label;
        this.selectEngineer = retVal;
      });
    },
    //工单录入
    wangYouInsert(){
      if(!this.orderTitle){
        this.$alert('请输入工单标题')
        return
      }else if(this.distName=='点击选择'){
        this.$alert('请选择区县')
        return
      } else if (!this.selectEngineer.label) {
        this.$alert('请选择无线归属网格')
        return
      } else if (!this.selectEngineer.id) {
        this.$alert('请选择支撑无线工程师')
        return
      } else if(!this.address){
        this.$alert('请输入详细地址')
        return
      }else if(this.sceneName=='点击选择'){
        this.$alert('请选择场景')
        return
      }else if(this.problemDesc=='点击选择'){
        this.$alert('请选择问题现象')
        return
      }else if(!this.complainPhone){
        this.$alert('请输入投诉人号码')
        return
      }else if(!this.complainDesc){
        this.$alert('请输入问题描述')
        return
      }
      if (!/^((1)+\d{10})$/.test(this.complainPhone) || this.complainPhone.length!=11) {
        this.$alert("请输入正确手机号");
        return;
      }

      let param = {
        "orderTitle": this.orderTitle,
        "cityName": this.cityName,
        "distName": this.distName,
        "address": this.address,
        "sceneName": this.sceneName,
        "problemDesc": this.problemDesc,
        "dealPerson": this.dealPerson,
        "trmnBrandCode": this.trmnBrandCode,
        "dealPhone": this.dealPhone,
        "complainPhone": this.complainPhone,
        "longitude": this.clockLongitude,
        "latitude": this.clockLatitude,
        "complainDesc": this.complainDesc,
        "gridName": this.gridName,
        "dispatchAccount": this.uinfo.servNumber,
        "dispatchId":dateFormat(new Date(), ('yyyyMMddhhmmss'))+this.uinfo.servNumber,
        "engName": this.selectEngineer.engName,
        "engPhone": this.selectEngineer.id,
      };
      this.$http.post('/xsb/paperless/wangYou/h5wangYouDispatch', param).then((res) => {
        let body = res.data;
        if (body.retCode == '0') {
            history.go(-1);
          this.$alert("工单录入成功");
        } else {
          this.$alert(res.data.retMsg || '工单录入失败');
        }
      }).catch((res) => {
        this.$alert('工单录入失败'+res);
      });
    },
    wangYouQryEngineerInfo() {
      this.engineerList = [];
      let url = `/xsb/paperless/wangYou/h5wangYouQryEngineerInfo`;
      let param = {
        cityName: this.cityName,
        distName: this.distName,
      }
      this.$http.post(url, param).then(res => {
        let { retCode, retMsg, data } = res.data;
        if (retCode === '0') {
          if (data && data.length > 0) {
            data.forEach(item => {
              this.engineerList.push({
                id: item.engPhone,
                label: item.gridName,
                engName: item.engName
              })
            })
          }
        } else {
          this.$alert(retMsg || '查询工单处理人员信息失败')
        }
      }).catch((res) => {
        this.$alert('查询工单处理人员信息异常，error：' + res);
      });
    },
    openLocal(){
      this.showMapFlag = true;
    },
    closeMap(){
      this.showMapFlag = false;
    },
    confirmMap(data){
      this.clockLongitude = data.markLnglatVal[0];
      this.clockLatitude = data.markLnglatVal[1];
      this.address = data.markAddress;
    },
    //获取网格信息
    getWangGeInfo(){
      let url = `/xsb/personBusiness/listGroupTarget/h5getGridData`;
       this.$http.get(url).then(res=>{
        let { retCode, retMsg, data } = res.data;
        if (retCode == '0') {
          // this.distName = data.countyName;
          this.gridName = data.gridName;
        } else {
          this.$alert(retMsg || '查询网格失败')
        }
      }).catch(err => {
        this.$alert(err || '网络异常')
      })
    },
  },
  //判断钱是否是整数
  filters: {
  },
  created() {
    //当前登录用户信息
    this.uinfo = Storage.session.get('userInfo');
    this.srcFrom = this.$route.query.srcFrom;
    //地市名称
    this.cityName = this.uinfo.regionName;
    //获取网格信息
    //this.getWangGeInfo();
  },
  computed:{
  },

  watch: {
  }
}
</script>
<style lang="less" scoped>
.wrapper {
  background-color: #ECF0FA;
  height: 100%;
}

.contentBox {
  height: 100%;
  box-sizing: border-box;
  padding: 48px 0 76px 0;
  overflow: auto;
}

.installInfo {
  margin: 16px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 2px 2px 6px 0px rgba(220, 220, 220, 0.5);
  overflow: hidden;
}
.installUl > li {
  display: flex;
  flex-direction: row;
  padding: 12px;
  box-sizing: border-box;
  border-bottom: 1px solid #EAEAEA;
  position: relative;
}

.installUl > li:last-child {
  border-bottom: none;
}

.installUl-title {
  color: #232323;
  font-size: 14px;
  line-height: 18px;
  flex-grow: 0;
  flex-shrink: 0;
  text-align: right;
  margin-right: 6px;
}
.installUl-title-red{
  color: red;
  margin-right: 5px;
}

.installUl-choose {
  flex-grow: 1;
  flex-shrink: 1;
  text-align: right;
  font-size: 14px;
  position: relative;
  &.address {
    display: flex;
    align-content: center;
    justify-content: end;
  }
}

.installUl-input {
  flex-grow: 1;
  flex-shrink: 1;
  text-align: right;
  color: #323232;
  font-size: 14px;
  outline: none;
  border: none;
}

.installUl-input::-webkit-input-placeholder {
  color: #BBBBBB;
  font-size: 14px;
}

.installUl-choose-input {
  text-align: right;
  outline: none;
  border: none;
  font-size: 14px;
}

.installUl-choose-input::-webkit-input-placeholder {
  color: #BBBBBB;
}

.installUl-choose * {
  vertical-align: middle;
}

.locationText {
  width: 100%;
  padding-right: 20px;
  box-sizing: border-box;
  display: block;
  line-height: 20px;
  &.padding-style {
    padding-right: 0;
  }
  &.no-address {
    color: #BBBBBB;
  }
}

.moreIcon {
  position: absolute;
  top: 50%;
  right: 0px;
  color: #BBBBBB;
  transform: translateY(-50%);
}
.fresh {
  color: #1681FB;
}

.installUl-textarea {
  flex-grow: 1;
  flex-shrink: 1;
  text-align: right;
  color: #323232;
  font-size: 14px;
  outline: none;
  border: none;
  resize: none;
  padding: 0;
}

.installUl-textarea::-webkit-input-placeholder {
  color: #BBBBBB;
  font-size: 14px;
}
.villageColor {
  color: #BBBBBB
}
</style>

