<template>
  <div class='wrapper'>
    <Header tsTitleTxt='问题投诉' backType='custom' @emGoPrev='goPrev' tsBtnTxt='工单录入'
            @emBtnCk='goWangYouLuRu'></Header>
    <section class='supplementMain'>
      <div class='itemSearch'>
        <div class='searchShow' v-if='suppleSearch'>
          <div class='allTime'>
            <div class='times'>
              <div class='tTitle'>派单时间</div>
              <span class='timer' @click="openDate('startTime')"><i class='iconfont rili'></i>{{ chooseTime }}</span>
            </div>
          </div>

          <button class='findSearch' @click='searchClick()'>查 询</button>
        </div>
        <div class='iconfont jiantouxiangshang upicons' :class="{'rotate':!suppleSearch}"
             @click='suppleSearch = !suppleSearch'></div>
      </div>
      <div class='searchContent' v-show='wangYouActivityList && wangYouActivityList.length > 0'>
        <p>投诉列表</p>
        <ul class='itemList'>
          <li class='itemLi' v-for='(item,ids) in wangYouActivityList' :key='ids'>
            <div class='itemInfo' style='flex: 1;'>
              <div class='itemLeft'>
                <span class='itemLeftInfo'>网优平台工单号: {{ item.orderNo }}</span>
                <span class='itemLeftInfo'>当前环节名称: {{ item.nowActivity | getActivityName }}</span>
                <span class='itemLeftInfo'>上一环节名称: {{ item.lastActivity | getActivityName }}</span>
                <span class='itemLeftInfo' v-show="item.address">详细地址: {{ item.address }}</span>
                <div v-for='(items,idx) in getParse(item.lastActInfo)' :key='idx' v-show="item.nowActivity !== '20' || !item.resultInfo">
                  <span class='itemLeftInfo detail'>{{ idx }}: {{ items }}</span>
                </div>
                <div v-for='(items,idx) in getParse(item.resultInfo)' :key='idx' v-show="item.nowActivity === '20'">
                  <span class='itemLeftInfo detail'>{{ idx }}: {{ items }}</span>
                </div>
              </div>
              <div class='itemButton-display' v-show="item.nowActivity == '20' && !item.daFenFlag">
                <button class='itemButton itemButton-bottom' @click='wangYouDaFenClick(item)'>打分</button>
              </div>
              <div class='itemButton-display' v-show="item.nowActivity == '20' && item.daFenFlag">
                <button class='itemButton itemButton-bottom' @click='wangYouDaFenCheck(item)'>确认打分</button>
              </div>
            </div>
            <div class='business-start' v-show='item.daFenFlag'>
              <div style="font-size: 14px;margin-bottom: 10px; padding-top: 14px; border-top: 1px solid #cacaca;">请为本次服务打分，5星为十分满意</div>
              <div class='start-icon'>
                <div class='entire-start'>
                  <div class='left-half-start' @click='daFenClick(1,item)'>
                    <em class='iconfont shoucang11' :class='{starLight: item.score > 0}' />
                  </div>
                  <div class='right-half-start' @click='daFenClick(2,item)'>
                    <em class='iconfont shoucang11' :class='{starLight: item.score > 1}' />
                  </div>
                </div>
                <div class='entire-start'>
                  <div class='left-half-start' @click='daFenClick(3,item)'>
                    <em class='iconfont shoucang11' :class='{starLight: item.score > 2}' />
                  </div>
                  <div class='right-half-start' @click='daFenClick(4,item)'>
                    <em class='iconfont shoucang11' :class='{starLight: item.score > 3}' />
                  </div>
                </div>
                <div class='entire-start'>
                  <div class='left-half-start'  @click='daFenClick(5,item)'>
                    <em class='iconfont shoucang11' :class='{starLight: item.score > 4}' />
                  </div>
                  <div class='right-half-start' @click='daFenClick(6,item)'>
                    <em class='iconfont shoucang11' :class='{starLight: item.score > 5}' />
                  </div>
                </div>
                <div class='entire-start'>
                  <div class='left-half-start' @click='daFenClick(7,item)'>
                    <em class='iconfont shoucang11' :class='{starLight: item.score > 6}' />
                  </div>
                  <div class='right-half-start' @click='daFenClick(8,item)'>
                    <em class='iconfont shoucang11' :class='{starLight: item.score > 7}' />
                  </div>
                </div>
                <div class='entire-start'>
                  <div class='left-half-start' @click='daFenClick(9,item)'>
                    <em class='iconfont shoucang11' :class='{starLight: item.score > 8}' />
                  </div>
                  <div class='right-half-start' @click='daFenClick(10,item)'>
                    <em class='iconfont shoucang11' :class='{starLight: item.score > 9}' />
                  </div>
                </div>
              </div>
              <div class='start-icon option-space' v-show="item.nowActivity == '20'">
                <span class='left-label'>问题解决</span>
                <div class="radio-info">
                  <a @click="item.solution = true" :class="{'true': item.solution}">
                    <span :class="[item.solution ? 'checkboxround1' : 'checkboxround0']" class="iconfont"></span>
                    <span>是</span>
                  </a>
                  <a @click="item.solution = false" :class="{'true': !item.solution}">
                    <span :class="[!item.solution ? 'checkboxround1' : 'checkboxround0']" class="iconfont"></span>
                    <span>否</span>
                  </a>
                </div>
              </div>
              <div class='start-icon option-space' v-show="item.nowActivity == '20'">
                <span class='left-label'>服务满意</span>
                <div class="radio-info">
                  <a @click="item.satisfaction = true" :class="{'true': item.satisfaction}">
                    <span :class="[item.satisfaction ? 'checkboxround1' : 'checkboxround0']" class="iconfont"></span>
                    <span>是</span>
                  </a>
                  <a @click="item.satisfaction = false" :class="{'true': !item.satisfaction}">
                    <span :class="[!item.satisfaction ? 'checkboxround1' : 'checkboxround0']" class="iconfont"></span>
                    <span>否</span>
                  </a>
                </div>
              </div>
            </div>
          </li>
        </ul>
      </div>
      <div class='noData' v-show='wangYouActivityList <= 0'>
        <NoDataPage tipTxt='暂无数据'></NoDataPage>
      </div>
    </section>
  </div>
</template>

<script>
import Header from 'components/common/Header.vue'
import Storage from '@/base/storage'
import NoDataPage from 'components/common/NoDataPage'
import NlButton from 'components/common/NlButton'
import { dateFormat } from '../../../base/utils'
import NlDatePicker from '../../common/NlDatePick/datePicker'

export default {
  mixins: [],
  components: { Header, NlButton, NoDataPage },
  data() {
    return {
      suppleSearch: true,//判断显示隐藏
      wangYouActivityList: [],//列表
      uinfo: {},//当前登录用户信息
      jqData: {},//鉴权对象
      telnum: '',//手机号
      srcFrom: '',
      beginDate: '',//开始时间
      //orderNo: '',//订单号
      startTime: "",
      endTime: "",
      chooseTime: "",
    }
  },
  created() {
    this.uinfo = Storage.session.get('userInfo')
    this.jqData = Storage.session.get('jqData')
    this.srcFrom = this.$route.query.srcFrom || '' //无纸化类型
    let nowDateTime = new Date();
    this.startTime = dateFormat(new Date(nowDateTime.getTime() - 1000 * 24 * 60 * 60 * 30), "yyyy-MM-dd hh:mm")
    let today = new Date()
    today.setDate(today.getDate())
    this.endTime = dateFormat(today, ('yyyy-MM-dd hh:mm'))
    this.chooseTime = this.startTime + '~' + this.endTime;
    this.searchClick();
  },
  computed: {},
  methods: {
    getParse(item){
      if(item){
        return JSON.parse(item);
      }else{
        return {};
      }
    },

    //打分提交
    wangYouDaFenCheck(item){
      let param = {
        'orderNo': item.orderNo,
        'dispatchId': '',
        'score': item.score,
        'solution': item.solution ? "是" : "否",
        'satisfaction': item.satisfaction ? "是" : "否"
      }
      let url = `/xsb/paperless/wangYou/h5wangYouActivityScore`
      this.$http.post(url, param).then(res => {
        let body = res.data
        if (body.retCode == '0') {
          this.$alert('打分成功')
          this.$set(item,'daFenFlag',false);
        } else {
          this.$alert(res.data.retMsg || '网格通投诉处理评估打分失败')
        }
      }).catch((response) => {
        this.$alert('网格通投诉处理评估打分网络请求失败' + response)
      })
    },
    //选择分数
    daFenClick(fenNum,item){
      this.$set(item,'score',fenNum);
    },
    //打分
    wangYouDaFenClick(item) {
      this.$set(item,'daFenFlag',true);
    },
    openDate() { //展开日期控件
      //this.$refs[picker].open()
      NlDatePicker({
        startDate: this.startTime,
        endDate: this.endTime,
        //tsMinDate: new Date('2025-01-01 00:00:00'),
        dateType: 'datetime',
        format: 'yyyy-MM-dd hh:mm',
      }, (retVal) => {
        if (!retVal.startDate) {
          this.$toast("未选择开始时间请重新选择");
          return;
        }
        if (!retVal.endDate) {
          this.$toast("未选择结束时间请重新选择");
          return;
        }
        this.startTime = retVal.startDate;
        this.endTime = retVal.endDate;
        this.chooseTime = this.startTime + '~' + this.endTime;
      })
    },

    goPrev() {
      if (this.srcFrom == 'tool') {//跳回工具箱
        this.$router.push('/tools')
      } else if (this.$route.query.srcFrom) {
        if (~this.$route.query.srcFrom.indexOf('/')) {
          this.$router.push(this.$route.query.srcFrom)
        } else {
          this.$router.push('/' + this.$route.query.srcFrom)
        }
      } else {
        this.$router.push('/business') //跳回业务
      }
    },
    //工单录入
    goWangYouLuRu() {
      this.$router.push('/wangYouLuRu')
    },

    //点击搜索
    searchClick() {
      let param = {
        //'orderNo': this.orderNo,
        'startTime': dateFormat(new Date(this.startTime), "yyyy-MM-dd hh:mm:ss"),
        'endTime': dateFormat(new Date(this.endTime), "yyyy-MM-dd hh:mm:ss"),
      }
      let url = `/xsb/paperless/wangYou/h5wangYouActivityInfo`
      this.$http.post(url, param).then(res => {
        let body = res.data
        if (body.retCode == '0') {
          let wangYouActivityList = body.data;
          for (let i = 0; i < wangYouActivityList.length; i++) {
            this.$set(wangYouActivityList[i], 'daFenFlag', false);
            this.$set(wangYouActivityList[i], 'solution', true);
            this.$set(wangYouActivityList[i], 'satisfaction', true);
          }
          this.wangYouActivityList = wangYouActivityList;
        } else {
          this.$alert(res.data.retMsg || '网优工单信息查询失败')
        }
      }).catch((response) => {
        this.$alert('网优工单信息查询网络请求失败' + response)
      })
    }

  },
  filters: {
    getActivityName(val) {
      if (val == '1') {
        return '发单'
      } else if (val == '2') {
        return '综合分析'
      } else if (val == '3') {
        return '现场测试'
      } else if (val == '4') {
        return '方案制定'
      } else if (val == '5') {
        return '方案审核'
      } else if (val == '6') {
        return '故障排除'
      } else if (val == '7') {
        return '参数调整'
      } else if (val == '8') {
        return '射频调整'
      } else if (val == '9') {
        return '规划新建'
      } else if (val == '10') {
        return '容量调整'
      } else if (val == '11') {
        return '工程整改'
      } else if (val == '12') {
        return '干扰排查'
      } else if (val == '13') {
        return '单点产品'
      } else if (val == '14') {
        return '方案实施确认'
      } else if (val == '15') {
        return '地市审核'
      } else if (val == '16') {
        return '复测分析'
      } else if (val == '17') {
        return '省专家评估'
      } else if (val == '18') {
        return '质检'
      } else if (val == '19') {
        return '地市回访'
      } else if (val == '20') {
        return '归档'
      } else {
        return val
      }
    }
  }
}
</script>

<style scoped lang='less'>
.supplementMain {
  height: auto;
  box-sizing: border-box;
  margin-top: 44px;
  display: flex;
  flex-direction: column;
  border-top: 1px solid #eaeaea;
}

.itemSearch {
  padding: 0 12px;
  background: #fff;
  margin-bottom: 8px;
}

.searchShow {
  margin: 30px 0 8px 0;

  dl {
    margin-bottom: 4px;
    font-size: 0;
    letter-spaceing: -4px;

    dt {
      color: #484848;
      font-size: 14px;
      margin-bottom: 12px;
      font-weight: bold;
    }

    dd {
      display: inline-flex;
      border: 1px solid #E9E9E9;
      color: #8F8F8F;
      font-size: 12px;
      padding: 6px 15px;
      box-sizing: border-box;
      margin: 0 12px 12px 0
    }

    dd.active {
      color: #1681FB;
      border-color: #1681FB
    }

    dd.styleup {
      justify-content: space-between;
      padding-bottom: 0;

      .styleRotate {
        color: #1681FB;
        margin-top: 5px;
      }
    }

    dd.styleup, dd.phones {
      border: none;
      padding: 0;
      border-bottom: 1px solid #E0E0E0;
      box-sizing: border-box;
      padding-bottom: 10px;
      width: 100%;
    }

    dd.phones i, .styleLeft i {
      color: #1681FB;
      font-size: 24px;
      vertical-align: -2px;
    }

    .stypeChoose,
    dd.phones input[type='tel'] {
      color: #323232;
      font-size: 14px;
      padding-left: 12px;
      outline: none;
      border: none;
      width: 100%;
      -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
    }

    .stypeChoose {
      color: #BBBBBB;
    }

    .stypeColor {
      color: #323232;
    }

    dd.phones input[type='tel']::-webkit-input-placeholder {
      color: #BBBBBB;
      font-size: 14px;
    }
  }
}

.findSearch {
  width: 100%;
  height: 44px;
  line-height: 44px;
  color: #fff;
  background: rgba(22, 129, 251, 1);
  border-radius: 22px;
  border: none;
  margin-top: 22px;
  font-size: 14px;
}

.upicons {
  text-align: center;
  padding: 12px 0;
  color: #1681FB;
}

.rotate {
  transform: rotate(-180deg);
  transition: transform 0.2s;
}

.searchContent {
  padding: 20px 12px;
  background: #fff;
}

.searchContent p {
  padding: 0 0 8px 4px;
  color: #3980CF;
  border-bottom: 1px dotted #CACACA;
  width: 100%;
  font-size: 17px;
  font-weight: bold;
}

.itemList {
  padding: 0 4px;
  font-size: 14px;

  .itemLi {

    width: 100%;
    border: 1px solid #95C6FE;
    border-radius: 4px;
    margin-top: 10px;

    .itemInfo {
      display: flex;
      justify-content: space-between;
      align-items: end;
    }

    .itemLeft {
      padding: 12px 0 12px 12px;
      flex: 1;

      .itemLeftInfo {
        color: #5a5a5a;
        display: block;
        line-height: 16px;
        margin-bottom: 8px;
        &.detail {
          color: #8F8F8F;
          padding: 4px 0;
        }
      }

      .itemLeftInfo1 {
        color: #8F8F8F;
        display: block;
        line-height: 16px;
        margin-bottom: 8px;
      }

      .itemLeftInfo:last-child {
        margin-bottom: 0 !important;
      }


      .button-bottom {
        margin-bottom: 10px;
      }
    }

    .itemButton {
      width: auto;
      padding: 0 12px;
      height: 28px;
      line-height: 28px;
      background: #1681fb;
      border-radius: 4px;
      border: none;
      color: #fff;
      font-size: 12px;
    }

    .itemButton-bottom {
      margin: 0 12px 16px 0;
    }

    .itemButton-display {
      display: flex;
      flex-direction: column;
    }
  }
}

.noData {
  padding: 20px;
}

.allTime {
}

.allTime .times {

  .tTitle {
    color: #484848;
    font-size: 14px;
    font-weight: bold;
    display: block;
    margin-bottom: 12px;
  }

  .timer {
    //width: 90%;
    height: 36px;
    line-height: 36px;
    border-radius: 4px;
    border: 1px solid #E9E9E9;
    box-sizing: border-box;
    padding: 10px;
    color: #7D7D7D;
    font-size: 14px;

    i {
      vertical-align: -2px;
      padding-right: 8px;
      color: #F2990A;
      font-size: 20px;
    }
  }
}

.business-start {
  color: #a1a1a1;
  font-size: 28px;
  display: flex;
  flex-direction: column;
  padding: 12px;
  .start-icon {
    display: flex;
    margin-bottom: 10px;
    .entire-start {
      display: flex;
      margin-right: 10px;
      .left-half-start {
        width: 14px;
      }
      .right-half-start {
        width: 14px;
        overflow: hidden;
        transform: rotateY(180deg);
      }
    }
    .shoucang11 {
      font-size: 28px;
      color: #c4c4c4;
    }

    .starLight {
      color: #efca00;
    }
    &.option-space {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 4px;
      margin-bottom: 0;
    }
    .left-label {
      font-size: 14px;
      font-weight: bold;
      white-space: nowrap;
      color: #575757;
    }
    .radio-info {
      height: 22px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 12px;
      .true {
        color: #4780f6;;
      }

      input {
        margin-left: 10px;
      }

      span {
        margin-left: 5px;
      }

      a {
        padding-left: 12px;
      }
    }
  }
}
</style>
