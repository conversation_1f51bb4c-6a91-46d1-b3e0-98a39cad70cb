<template>
	<div class='wrapper'>
		<Header :tsTitleTxt='title' backType='custom' @emGoPrev='goPrev'></Header>
		<div ref='content' class='main-content'>
			<div class='accordion'>
				<div class='basic-info-collect'>
					<img alt='' src='static/img/basic-info-collection.png'>
					<div class='info'>
						<div class='info-big-title'>基本信息采集</div>
						<div class='info-input'>
							<span class='info-title'>公司名称：</span>
							<input v-model='form.companyName' placeholder='请输入' type='text' />
							<i class='iconfont bianji' style='color: #929292;font-size: 14px;line-height: 20px;'></i>
						</div>
					</div>
				</div>
				<div class='other-info-collect'>
					<div class='accordion-item'>
						<div class='accordion-header' @click='toggleAccordion(1)'>
							1、现场考察报告
							<i
								:class="['iconfont', 'youjiantou2', 'accordion-icon', selected === 1 ? 'icon-down' : 'icon-up']"></i>
						</div>
						<div v-show='selected === 1' class='accordion-content'>
							<div class='information-list'>
								<div class='information-title'>
									<i class='information-icon'></i>
									线上资质审核查验截图
								</div>
								<div class='information-content'>
									<custom-pic :imgIds='form.onlineCheckPic' dataPath='onlineCheckPic'
										@emclick='takePhotoCallBack'></custom-pic>
								</div>
							</div>
							<div class='information-list'>
								<div class='information-title padding-bottom-10'>
									<i class='information-icon'></i>
									客户单位基本情况及使用场景描述
								</div>
								<div class='information-content padding-bottom-10'>
									<div class='description'>
										<textarea v-model='form.customerUseScene' maxLength='700'
											placeholder='请输入'></textarea>
									</div>
								</div>
							</div>
							<div class='information-list'>
								<div class='information-title'>
									<i class='information-icon'></i>
									客户门头照及考察人合影(<b style='color: red'>必传，且需要合影</b>)
								</div>
								<div class='information-content'>
									<custom-pic :imgIds='form.customerPic' dataPath='customerPic'
										@emclick='takePhotoCallBack'></custom-pic>
								</div>
							</div>
							<div class='information-list'>
								<div class='information-title'>
									<i class='information-icon'></i>
									客户生产工作或办公场所照片
								</div>
								<div class='information-content'>
									<custom-pic :imgIds='form.customerWorkPic' dataPath='customerWorkPic'
										@emclick='takePhotoCallBack'></custom-pic>
								</div>
							</div>
							<div class='information-list'>
								<div class='information-title'>
									<i class='information-icon'></i>
									终端设备照片及技术文档
								</div>
								<div class='information-content padding-bottom-10'>
									<custom-pic :imgIds='form.terminalPic' dataPath='terminalPic'
										@emclick='takePhotoCallBack'></custom-pic>
									<div class='description'>
										<textarea v-model='form.terminalDoc' maxLength='700'
											placeholder='请输入'></textarea>
									</div>
								</div>
							</div>
							<div class='information-list'>
								<div class='information-title padding-bottom-10'>
									<i class='information-icon'></i>
									考察结论
								</div>
								<div class='information-content padding-bottom-10'>
									<div class='description'>
										<textarea v-model='form.inspectionConclusion' maxLength='700'
											placeholder='请输入'></textarea>
									</div>
								</div>
							</div>
							<div class='information-list'>
								<div class='information-title padding-bottom-10'>
									<i class='information-icon'></i>
									现场考察人签字
								</div>
								<div class='information-content padding-bottom-10'>
									<sign :signId='form.inspector' dataPath='inspector' @emclick='signCallBack'></sign>
								</div>
							</div>
						</div>
					</div>
					<div class='accordion-item'>
						<div class='accordion-header' @click='toggleAccordion(2)'>
							2、现场考察资质
							<i
								:class="['iconfont', 'youjiantou2', 'accordion-icon', selected === 2 ? 'icon-down' : 'icon-up']"></i>
						</div>
						<div v-show='selected === 2' class='accordion-content'>
							<div class='information-list'>
								<div class='information-title'>
									<i class='information-icon'></i>
									单位资质
								</div>
								<div class='information-content'>
									<div class='confirmation-letter'>
										经现场考察确认，我司与客户<u>{{ form.companyName || '请在基本信息采集填写公司名称'
										}}</u>达成物联网业务合作意向，客户注册时间为<u @click='$refs.picker.open()'>{{
												form.registerTime || '点我选择'
											}}</u>，单位类型为<u @click='chooseCompanyType'>{{ form.companyType || '点我选择'
											}}</u>类型。
									</div>
									<div class='confirmation-letter'>
										经评估该客户<u>{{ form.highRisk | highRiskFilter
										}}</u>开通高风险应用场景（非定向大流量功能或用于无线上网类服务），根据《物联网客户准入风险评估表》及相关资质审核，
										<u @click='changeOpenStatus'>{{ form.opening | openingFilter }}</u>开卡要求。
									</div>
									<div class='confirmation-letter'>
										通过国家企业信用信息公示系统等途径认真查验该客户的单位证件真实有效，并对客户的使用场景进行现场考察，并留存现场考察材料，包括审核人员与客户单位的照片、设备照片及技术文档、生产工作或办公场所照片等。
									</div>
								</div>
							</div>
							<div class='information-list'>
								<div class='information-title padding-bottom-10'>
									<i class='information-icon'></i>
									集团客户签字
								</div>
								<div class='information-content padding-bottom-10'>
									<sign :signId='form.operator' dataPath='operator' @emclick='signCallBack'></sign>
								</div>
							</div>
							<div class='information-list'>
								<div class='information-title padding-bottom-10'>
									<i class='information-icon'></i>
									资质审核人签字
								</div>
								<div class='information-content padding-bottom-10'>
									<sign :signId='form.auditing' dataPath='auditing' @emclick='signCallBack'></sign>
								</div>
							</div>
						</div>
					</div>

					<div v-for='(risk, i) in form.risks' :key='i' class='accordion-item'>
						<div class='accordion-header' @click='toggleAccordion(i + 3)'>
							{{ i + 3 }}、客户风险评估
							<i
								:class="['iconfont', 'youjiantou2', 'accordion-icon', selected === i + 3 ? 'icon-down' : 'icon-up']"></i>
						</div>
						<div v-show='selected === i + 3' class='accordion-content'>
							<div v-for='(item, index) in options.selection' :key='index' class='information-list'>
								<div class='information-title'>
									<i class='information-icon'></i>
									{{ item.type }}
								</div>
								<div class='information-content'>
									<template v-for='(iter) in item.questions'>
										<div v-if='iter.type === "single"' class='information-item'
											@click='selectOption(iter, i)'>
											<div class='question'><b>{{ iter.id + 1 }}.</b>{{ iter.question }}:</div>
											<span class='selection'>
												{{ answerCompute(risk.selectionAnswer[iter.id], iter.answer) }}<i
													class='iconfont youjiantou2' style=''></i></span>
										</div>
										<div v-if='iter.type === "multi"' class='information-item'
											@click='selectMultiOption(iter, i)'>
											<div class='question'><b>{{ iter.id + 1 }}.</b>{{ iter.question }}:</div>
											<span class='selection'>
												{{ answerCompute(risk.selectionAnswer[iter.id], iter.answer) }}<i
													class='iconfont youjiantou2' style=''></i></span>
										</div>
										<div v-if='iter.type === "switch"' class='information-item'>
											<div class='question'><b>{{ iter.id + 1 }}.</b>{{ iter.question }}:</div>
											<span class='selection'>
												<div class='slider-container' @click='handleSwitchClick(iter, i)'>
													<span
														:class='{ active: !getSwitchValue(risk.selectionAnswer[iter.id]) }'
														class='option-text left'>否</span>
													<span
														:class='{ active: getSwitchValue(risk.selectionAnswer[iter.id]) }'
														class='option-text right'>是</span>
													<div class='slider-track'>
														<div :class="{ 'is-right': getSwitchValue(risk.selectionAnswer[iter.id]) }"
															class='slider-thumb'></div>
													</div>
												</div>
											</span>
										</div>
									</template>

								</div>
							</div>
							<div class='score-area'>
								<div>
									<img alt='得分icon' class='score-icon' src='static/img/score-icon.png' />
									<span class='score-txt'>得分</span>
								</div>
								<div>
									<span class='score-num'>{{ risk.score }}</span>
									<span class='score-total'>/100</span>
								</div>
							</div>
							<div class='information-list'>
								<div class='information-title padding-bottom-10'>
									<i class='information-icon'></i>
									客户经办人
								</div>
								<div class='information-content padding-bottom-10'>
									<sign :dataPath='i + ".handler"' :signId='risk.handler' @emclick='signCallBack'>
									</sign>
								</div>
							</div>
							<div class='information-list'>
								<div class='information-title padding-bottom-10'>
									<i class='information-icon'></i>
									责任人（移动）
								</div>
								<div class='information-content padding-bottom-10'>
									<sign :dataPath='i + ".responsiblePer"' :signId='risk.responsiblePer'
										@emclick='signCallBack'></sign>
								</div>
							</div>
							<div class='information-list'>
								<div class='information-title'>
									<i class='information-icon'></i>
									责任人分管领导（移动）
								</div>
								<div class='information-content padding-bottom-10'>
									<div :class='{ "border-none": risk.responsiblePerLeader.signType === 0 }'
										class='information-item'>
										签字方式:
										<span class='sign-selection'>
											<span
												:class='{ "sign-achive": risk.responsiblePerLeader.signType === 0, "sign-inachive": risk.responsiblePerLeader.signType === 1 }'
												@click='risk.responsiblePerLeader.signType = 0'>现场签字</span>
											<span
												:class='{ "sign-achive": risk.responsiblePerLeader.signType === 1, "sign-inachive": risk.responsiblePerLeader.signType === 0 }'
												@click='risk.responsiblePerLeader.signType = 1'>5G签字</span>
										</span>
									</div>
									<div v-show='risk.responsiblePerLeader.signType === 1'
										class='information-item border-none'>
										处理人选择:
										<span class='select-leader'>
											<span @click='chooseLeader("responsiblePerLeader", i, 1)'>
												{{ risk.responsiblePerLeader.name || '请选择'
												}}
												<i class='iconfont youjiantou2'
													style='color: #929292;font-size: 14px'></i>
											</span>
											<span class='send-msg'
												@click='send5GMessage("responsiblePerLeader", i, risk)'>
												{{ '推送' }}
											</span>
										</span>
									</div>
									<sign
										v-show='!(risk.responsiblePerLeader.signType === 1 && risk.responsiblePerLeader.remoteSignId === "")'
										:canEdit='risk.responsiblePerLeader.signType === 0'
										:dataPath='i + ".responsiblePerLeader.signId"'
										:signId='risk.responsiblePerLeader.signId || risk.responsiblePerLeader.remoteSignId'
										@emclick='signCallBack'></sign>
								</div>
							</div>
							<div class='information-list'>
								<div class='information-title'>
									<i class='information-icon'></i>
									评估人（移动）
								</div>
								<div class='information-content padding-bottom-10'>
									<div :class='{ "border-none": risk.assessor.signType === 0 }'
										class='information-item'>
										签字方式:
										<span class='sign-selection'>
											<span
												:class='{ "sign-achive": risk.assessor.signType === 0, "sign-inachive": risk.assessor.signType === 1 }'
												@click='risk.assessor.signType = 0'>现场签字</span>
											<span
												:class='{ "sign-achive": risk.assessor.signType === 1, "sign-inachive": risk.assessor.signType === 0 }'
												@click='risk.assessor.signType = 1'>5G签字</span>
										</span>
									</div>

									<div v-show='risk.assessor.signType === 1' class='information-item border-none'>
										处理人选择:
										<span class='select-leader'>
											<span @click='chooseLeader("assessor", i, 2)'>
												{{ risk.assessor.name || '请选择' }}
												<i class='iconfont youjiantou2'
													style='color: #929292;font-size: 14px'></i>
											</span>
											<span class='send-msg' @click='send5GMessage("assessor", i, risk)'>
												{{ '推送' }}
											</span>
										</span>
									</div>
									<sign v-show='!(risk.assessor.signType === 1 && risk.assessor.remoteSignId === "")'
										:canEdit='risk.assessor.signType === 0' :dataPath='i + ".assessor.signId"'
										:signId='risk.assessor.signId || risk.assessor.remoteSignId'
										@emclick='signCallBack'></sign>
								</div>
							</div>
							<div class='information-list'>
								<div class='information-title'>
									<i class='information-icon'></i>
									评估人分管领导（移动）
								</div>
								<div class='information-content padding-bottom-10'>
									<div :class='{ "border-none": risk.assessorLeader.signType === 0 }'
										class='information-item'>
										签字方式:
										<span class='sign-selection'>
											<span
												:class='{ "sign-achive": risk.assessorLeader.signType === 0, "sign-inachive": risk.assessorLeader.signType === 1 }'
												@click='risk.assessorLeader.signType = 0'>现场签字</span>
											<span
												:class='{ "sign-achive": risk.assessorLeader.signType === 1, "sign-inachive": risk.assessorLeader.signType === 0 }'
												@click='risk.assessorLeader.signType = 1'>5G签字</span>
										</span>
									</div>

									<div v-show='risk.assessorLeader.signType === 1'
										class='information-item border-none'>
										处理人选择:
										<span class='select-leader'>
											<span @click='chooseLeader("assessorLeader", i, 3)'>
												{{ risk.assessorLeader.name || '请选择' }}
												<i class='iconfont youjiantou2'
													style='color: #929292;font-size: 14px'></i>
											</span>
											<span class='send-msg' @click='send5GMessage("assessorLeader", i, risk)'>
												{{ '推送' }}
											</span>
										</span>
									</div>
									<sign
										v-show='!(risk.assessorLeader.signType === 1 && risk.assessorLeader.remoteSignId === "")'
										:canEdit='risk.assessorLeader.signType === 0'
										:dataPath='i + ".assessorLeader.signId"'
										:signId='risk.assessorLeader.signId || risk.assessorLeader.remoteSignId'
										@emclick='signCallBack'></sign>
								</div>
							</div>
						</div>
					</div>
					<div class='accordion-item'>
						<div class='accordion-header border-none' style='padding: 10px 0'>
							客户风险评估采集份数
							<div class='num'>
								<div class='accordion-icon' @click='riskNumChange(0)'>-</div>
								<span class='num-txt'>{{ form.risks.length }}</span>
								<div class='accordion-icon' @click='riskNumChange(1)'>+</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class='save-submit wrapper-medias'>
			<div v-show='selected' class='close-all' @click='$refs.content.scrollTop = 0; selected = null'>
				关
			</div>
			<button class='save-btn' @click='submit(0, true)'>临时保存</button>
			<button class='submit-btn' @click='submit(1, true)'>一键提交</button>
		</div>
		<mt-datetime-picker ref='picker' :endDate='new Date()'
			:startDate='new Date(new Date().getFullYear() - 50, 0, 1)' type='date' @confirm='handleConfirm'>
		</mt-datetime-picker>
		<NLMultiDropDown :data='treeData' :maxLevels='5' :selectionMode='selectionMode' :visible.sync='chooseFlag'
			@confirm='handleMultiConfirm' />
	</div>
</template>
<script>
import NLMultiDropDown from './NLMultiDropDown.vue'
import Header from '../../common/Header.vue'
import sign from './Sign.vue'
import NlDropdown from 'components/common/NlDropdown/dropdown.js'
import { dateFormat } from '@/base/utils'
import CustomPic from './CustomPic.vue'
import { handleOrderReqmixin } from '@/base/handleOrderReqmixin.js'
import Storage from '@/base/storage.js'

export default {
	mixins: [handleOrderReqmixin],
	components: {
		CustomPic,
		Header, sign, NLMultiDropDown
	},
	created() {
		let param = {
			busiType: 'networkonline_leader',
			unLoadFlg: true
		}
		this.$http.post('/xsb/ability/businessLimit/h5QryBusiPermission', param).then(res => {
			let { retCode } = res.data
			if (retCode === '0') {
				this.showLeader = true
			}
		})
		// 初始化领导数据
		this.getLeadersData()
		let orderItem = this.$route.query.orderItem
		if (orderItem) {
			this.getInitData(orderItem)
			this.title = '资料补录'
		}

		// 初始化风险评估表中的14、15题为"否"
		if (this.form.risks && this.form.risks.length > 0) {
			this.form.risks.forEach(risk => {
				// 确保selectionAnswer初始化为16个元素的数组
				if (!risk.selectionAnswer || risk.selectionAnswer.length < 16) {
					risk.selectionAnswer = Array(16).fill(null)
				}

				// 初始化14、15题为"否"
				if (!risk.selectionAnswer[13]) {
					risk.selectionAnswer[13] = {
						id: 'v14_2',
						score: 0,
						label: '否'
					}
				}
				if (!risk.selectionAnswer[14]) {
					risk.selectionAnswer[14] = {
						id: 'v15_2',
						score: 0,
						label: '否'
					}
				}
				if (!risk.selectionAnswer[15]) {
					risk.selectionAnswer[15] = {
						id: 'v16_2',
						score: 0,
						label: '否'
					}
				}
			})
		}
		// new Vconsole()
	},
	data() {
		return {
			// title
			title: '资料录入',
			// 选项列表
			selected: null,
			isShowSign: true,
			showLeader: false,
			selectionMode: 'single',
			// 选项值
			options: {
				// 领导（暂时）
				leaders: [],
				// 选择题
				selection: [
					{
						type: '单位资质（22分）',
						questions: [
							{
								id: 0,
								question: '单位类型',
								type: 'single',
								answer: [
									{
										score: 8,
										label: '物联网终端生产企业'
									},
									{
										score: 8,
										label: '提供公共服务的政府、企事业单位'
									},
									{
										score: 5,
										label: '物联网产品销售企业，物联网产品集成商，物联网平台运营企业'
									},
									{
										score: 0,
										label: '其他'
									}
								]
							},
							{
								id: 1,
								question: '是否有合作历史',
								type: 'single',
								answer: [
									{
										score: 3,
										label: '有过合作且未出现过高风险或违规、信息安全事件'
									},
									{
										score: 0,
										label: '没有合作过或有过合作且发生过高风险'
									},
									{
										score: -20,
										label: '有过合作且发生过违规、信息安全事件'
									}
								]
							},
							{
								id: 2,
								question: '营业执照注册时间',
								type: 'single',
								answer: [
									{
										score: 5,
										label: '注册时间大于一年或政府部门、事业单位'
									},
									{
										score: 3,
										label: '注册时间大于三个月小于一年'
									},
									{
										score: 0,
										label: '注册时间少于三个月'
									}
								]
							},
							{
								id: 3,
								question: '注册资金',
								type: 'single',
								answer: [
									{
										score: 3,
										label: '500万元及以上或政府部门、事业单位'
									},
									{
										score: 0,
										label: '500万元以下'
									}
								]
							},
							{
								id: 4,
								question: '是否进行现场考察',
								type: 'single',
								answer: [
									{
										score: 3,
										label: '进行单位现场考察及项目相关单位考察，且留存有考察人与门头一体照片、设备照片、办公场所照片'
									},
									{
										score: 1,
										label: '仅有单位现场考察但未进行项目相关单位考察'
									},
									{
										score: 0,
										label: '未进行现场考察'
									}
								]
							}
						]
					},
					{
						type: '使用情况（25分）',
						questions: [
							{
								id: 5,
								question: '使用范围',
								type: 'single',
								answer: [
									{
										score: 5,
										label: '本省或锁定基站'
									},
									{
										score: 2,
										label: '限定省使用'
									},
									{
										score: 0,
										label: '不限定范围'
									}
								]
							},
							{
								id: 6,
								question: '应用场景',
								type: 'multi',
								answer: [
									{
										id: '12-1',
										label: '智慧交通',
										children: [
											{
												id: 'v7_1',
												score: 2,
												label: '(2分)车辆信息采集'
											},
											{
												id: 'v7_2',
												score: 2,
												label: '(2分)路侧设施信息采集'
											},
											{
												id: 'v7_3',
												score: 2,
												label: '(2分)车载智能服务'
											},
											{
												id: 'v7_4',
												score: 0,
												label: '(0分)车/船载WiFi设备'
											},
											{
												id: 'v7_5',
												score: 2,
												label: '(2分)交通监控'
											},
											{
												id: 'v7_6',
												score: 2,
												label: '(2分)其他场景'
											}
										]
									},
									{
										id: '12-2',
										label: '公共服务',
										children: [
											{
												id: 'v7_7',
												score: 2,
												label: '(2分)公共安全'
											},
											{
												id: 'v7_8',
												score: 0,
												label: '(0分)移动办公'
											},
											{
												id: 'v7_9',
												score: 2,
												label: '(2分)消防设备'
											},
											{
												id: 'v7_10',
												score: 2,
												label: '(2分)安防监控'
											},
											{
												id: 'v7_11',
												score: 5,
												label: '(5分)市政设施'
											},
											{
												id: 'v7_12',
												score: 5,
												label: '(5分)智能抄表'
											},
											{
												id: 'v7_13',
												score: 2,
												label: '(2分)环境监测'
											},
											{
												id: 'v7_14',
												score: 0,
												label: '(0分)无线上网'
											},
											{
												id: 'v7_15',
												score: 2,
												label: '(2分)智慧停车'
											},
											{
												id: 'v7_16',
												score: 2,
												label: '(2分)共享服务'
											},
											{
												id: 'v7_17',
												score: 2,
												label: '(2分)其他场景'
											}
										]
									},
									{
										id: '12-3',
										label: '零售服务',
										children: [
											{
												id: 'v7_18',
												score: 2,
												label: '(2分)金融支付'
											},
											{
												id: 'v7_19',
												score: 2,
												label: '(2分)智能广告'
											},
											{
												id: 'v7_20',
												score: 2,
												label: '(2分)公网对讲'
											},
											{
												id: 'v7_21',
												score: 2,
												label: '(2分)其他场景'
											}
										]
									},
									{
										id: '12-4',
										label: '智能家居',
										children: [
											{
												id: 'v7_22',
												score: 0,
												label: '(0分)个人可穿戴设备'
											},
											{
												id: 'v7_23',
												score: 0,
												label: '(0分)智能家电'
											},
											{
												id: 'v7_24',
												score: 0,
												label: '(0分)家庭安防'
											},
											{
												id: 'v7_25',
												score: 0,
												label: '(0分)家庭网关'
											},
											{
												id: 'v7_26',
												score: 2,
												label: '(2分)其他场景'
											}
										]
									},
									{
										id: '12-5',
										label: '智慧农业',
										children: [
											{
												id: 'v7_27',
												score: 2,
												label: '(2分)环境监测'
											},
											{
												id: 'v7_28',
												score: 2,
												label: '(2分)定位场景'
											},
											{
												id: 'v7_29',
												score: 2,
												label: '(2分)农业自动化'
											},
											{
												id: 'v7_30',
												score: 2,
												label: '(2分)其他场景'
											}
										]
									},
									{
										id: '12-6',
										label: '工业制造',
										children: [
											{
												id: 'v7_31',
												score: 2,
												label: '(2分)采集类设备'
											},
											{
												id: 'v7_32',
												score: 2,
												label: '(2分)视频监控类设备'
											},
											{
												id: 'v7_33',
												score: 2,
												label: '(2分)高端装备'
											},
											{
												id: 'v7_34',
												score: 0,
												label: '(0分)工业网关类设备'
											},
											{
												id: 'v7_35',
												score: 2,
												label: '(2分)其他场景'
											}
										]
									},
									{
										id: '12-7',
										label: '智慧医疗',
										children: [
											{
												id: 'v7_36',
												score: 2,
												label: '(2分)医院医疗设备'
											},
											{
												id: 'v7_37',
												score: 2,
												label: '(2分)个人医疗设备'
											},
											{
												id: 'v7_38',
												score: 2,
												label: '(2分)其他场景'
											}
										]
									},
									{
										id: '12-8',
										label: '运输服务',
										children: [
											{
												id: 'v7_39',
												score: 0,
												label: '(0分)物流手持终端设备'
											},
											{
												id: 'v7_40',
												score: 2,
												label: '(2分)货物跟踪设备'
											},
											{
												id: 'v7_41',
												score: 2,
												label: '(2分)智能快递柜'
											},
											{
												id: 'v7_42',
												score: 2,
												label: '(2分)仓储视频监控'
											},
											{
												id: 'v7_43',
												score: 2,
												label: '(2分)其他场景'
											}
										]
									},
									{
										id: '12-9',
										label: '智能远控',
										children: [
											{
												id: 'v7_44',
												score: 2,
												label: '(2分)无人机巡查'
											},
											{
												id: 'v7_45',
												score: 2,
												label: '(2分)无人机救援'
											},
											{
												id: 'v7_46',
												score: 2,
												label: '(2分)无人机监控'
											},
											{
												id: 'v7_47',
												score: 2,
												label: '(2分)农业无人机'
											},
											{
												id: 'v7_48',
												score: 2,
												label: '(2分)无人机配送'
											},
											{
												id: 'v7_49',
												score: 2,
												label: '(2分)其他场景'
											}
										]
									},
									{
										id: '12-10',
										label: '其他场景',
										children: [
											{
												id: 'v7_50',
												score: 2,
												label: '(2分)其他场景'
											}
										]
									}
								]
							},
							{
								id: 7,
								question: '使用功能（得分取最低值）',
								type: 'multi',
								answer: [
									{
										id: 'v8_1',
										score: 5,
										label: '(5分)定向流量'
									},
									{
										id: 'v8_2',
										score: 2,
										label: '(2分)非定向流量'
									},
									{
										id: 'v8_3',
										score: 2,
										label: '(2分)定向短信'
									},
									{
										id: 'v8_4',
										score: 0,
										label: '(0分)定向语音'
									}
								]
							},
							{
								id: 8,
								question: '协议期内开卡数量',
								type: 'single',
								answer: [
									{
										score: 5,
										label: '小于1万'
									},
									{
										score: 2,
										label: '大于等于1万小于等于5万'
									},
									{
										score: 0,
										label: '大于5万'
									}
								]
							},
							{
								id: 9,
								question: '套餐流量（流量取最高值）',
								type: 'multi',
								answer: [
									{
										id: 'v10_1',
										score: 5,
										label: '(5分)月均流量<=300M'
									},
									{
										id: 'v10_2',
										score: 3,
										label: '(3分)300M<月均流量套餐<=1G'
									},
									{
										id: 'v10_3',
										score: 2,
										label: '(2分)1G<月均流量套餐<=5G'
									},
									{
										id: 'v10_4',
										score: 0,
										label: '(0分)5G<月均流量套餐'
									}
								]
							}
						]
					},
					{
						type: '资费折扣（20分）',
						questions: [
							{
								id: 10,
								question: '资费折扣',
								type: 'single',
								answer: [
									{
										score: 20,
										label: '标准资费7折及以上'
									},
									{
										score: 10,
										label: '高于管控底价但低于7折'
									},
									{
										score: 0,
										label: '低于管控底价'
									}
								]
							}
						]
					},
					{
						type: '安全管理情况（33分）',
						questions: [
							{
								id: 11,
								question: '采用的管控措施',
								type: 'multi',
								answer: [
									{
										id: '11-1',
										label: '完全符合分类限制标准',
										children: [
											{
												id: 'v12_1',
												score: 30,
												label: '(30分)完全符合分类限制标准'
											}]
									},
									{
										id: '11-2',
										label: '其他',
										children: [
											{
												id: 'v12_2',
												score: 10,
												label: '(10分)使用人实名'
											},
											{
												id: 'v12_3',
												score: 10,
												label: '(10分)贴片卡\\eSIM'
											},
											{
												id: 'v12_4',
												score: 5,
												label: '(5分)小流量限额管控'
											},
											{
												id: 'v12_5',
												score: 5,
												label: '(5分)区域限制'
											},
											{
												id: 'v12_6',
												score: 2,
												label: '(2分)机卡绑定'
											}, {
												id: 'v12_7',
												score: 2,
												label: '(2分)黑名单限制'
											}
										]
									}, {
										id: '11-3',
										label: '无',
										children: [
											{
												id: 'v12_8',
												score: 0,
												label: '(0分)无'
											}]
									}
								]
							},
							{
								id: 12,
								question: '是否具有独立的安全管控能力',
								type: 'single',
								answer: [
									{
										score: 3,
										label: '有技术手段'
									},
									{
										score: 0,
										label: '无技术手段'
									}
								]
							}
						]
					},
					{
						type: '附加项',
						questions: [
							{
								id: 13,
								question: '开通非定向大流量，非无线上网类服务场景',
								type: 'switch',
								answer: [
									{
										id: 'v14_1',
										score: 0,
										label: '是'
									}, {
										id: 'v14_2',
										score: 0,
										label: '否'
									}
								]
							},
							{
								id: 14,
								question: '开通非定向大流量，且用于无线上网类服务场景',
								type: 'switch',
								answer: [
									{
										id: 'v15_1',
										score: 0,
										label: '是'
									}, {
										id: 'v15_2',
										score: 0,
										label: '否'
									}
								]
							},
							{
								id: 15,
								question: '是否需要搭载销售管理',
								type: 'switch',
								answer: [
									{
										id: 'v16_1',
										score: 0,
										label: '是'
									},
									{
										id: 'v16_2',
										score: 0,
										label: '否'
									}
								]
							}
						]
					}
				]
			},
			// 表单参数
			form: {
				servNumber: Storage.session.get('userInfo').servNumber,
				// 审核人签字
				auditing: '',
				// 经办人签字
				operator: '',
				// 现场考察人签字
				inspector: '',
				// 客户风险评估
				risks: [{
					// token 区分每一份风险评估
					token: this.createUUID(),
					// 初始化选择题答案,为13、14、15题(对应数组索引)设置默认值"否"
					selectionAnswer: Array(16).fill(null).map((item, index) => {
						if ([13, 14, 15].includes(index)) {
							return {
								id: `v${index + 1}_2`, // _2表示"否"
								score: 0,
								label: '否'
							}
						}
						return null
					}),
					// 客户经办人
					handler: '',
					// 责任人签字
					responsiblePer: '',
					// 责任人分管领导
					responsiblePerLeader: {
						signId: '',//签名流水号
						signType: 0,// 签字方式
						mobile: '',// 远程手机号
						remoteSignId: '',// 远程签字流水号
						name: ''// 远程签字人姓名
					},
					// 评估人
					assessor: {
						signId: '',//签名流水号
						signType: 0,// 签字方式
						mobile: '',// 远程手机号
						remoteSignId: '',// 远程签字流水号
						name: ''// 远程签字人姓名
					},
					// 评估人分管领导
					assessorLeader: {
						signId: '',//签名流水号
						signType: 0,// 签字方式
						mobile: '',// 远程手机号
						remoteSignId: '',// 远程签字流水号
						name: ''// 远程签字人姓名
					},
					// 得分
					score: 0
				}],

				// 线上资质审查截图
				onlineCheckPic: '',
				// 客户单位使用场景描述
				customerUseScene: '',
				// 客户门头照及考察人合影
				customerPic: '',
				// 客户生产工作或办公场所照片
				customerWorkPic: '',
				// 终端设备照片
				terminalPic: '',
				// 终端设备技术文档
				terminalDoc: '',
				// 考察结论
				inspectionConclusion: '',
				companyName: '',// 公司名称
				srl: '', // 流水号
				state: 0, // 生效状态(0临时保存 1正式提交(生成中) -1删除 2正式提交(成功生成) 3正式提交(生成失败))
				registerTime: '', // 注册时间
				companyType: '', // 公司类型
				highRisk: null, // 开通非定向大流量功能
				opening: null // 开卡要求
			},
			// 展示多层选项框
			chooseFlag: false,
			// 树形数据
			treeData: [],
			// 多选题目下标
			multiIndex: 0
		}
	},
	methods: {
		// 生成token,token为物联网风险评估唯一标识
		createUUID() {
			// 根据时间23-10-25 + 8位随机数
			let date = new Date()
			let year = date.getFullYear()
			let month = date.getMonth() + 1
			let day = date.getDate()
			// 8位随机数字
			let random = Math.floor(Math.random() * *********)
			return `${year}${month}${day}${random}`
		},
		// 初始化领导数据
		getLeadersData() {
			const url = '/xsb/personBusiness/netWorkLine/h5getManagerInfo'
			const promises = []
			for (let i = 1; i <= 3; i++) {
				promises.push(this.$http.post(url, {
					type: i
				}))
			}

			Promise.all(promises)
				.then(results => {
					// 处理所有请求的结果并赋值
					results.forEach(({ data: temp }, index) => {
						let { data, retCode } = temp
						if (retCode === '0') {
							let leaders = data.map(item => {
								return {
									mobile: item.mobile,
									label: item.name,
									result: ''
								}
							})
							if (this.showLeader) {
								leaders.push({ mobile: '***********', label: '崔春贵', result: '' })
								leaders.push({ mobile: '***********', label: '钱庆丰', result: '' })
								leaders.push({ mobile: '***********', label: '张Q币', result: '' })
								leaders.push({ mobile: '***********', label: '黄琴', result: '' })
							}
							this.$set(this.options.leaders, index + 1, leaders)
						} else {
							this.$messagebox({
								title: '提示',
								message: data.retMsg
							}).then(() => {
								this.goPrev()
							})
						}
					})
				})
		},
		// 5G消息推送
		send5GMessage(type, i, risk) {
			// 注意：这里先临时保存，待拿到后台生成的 srl 后再进行 5G 推送，避免 "丢失流水号" 问题
			// 前置参数检查
			let selectionCheck = this.selectionCheck(i)
			if (selectionCheck !== -1) {
				this.$toast(`请填写第${i + 1}份风险评估第${selectionCheck + 1}题`)
				return
			}
			if (!this.form.companyName) {
				this.$toast('请填写公司名称')
				return
			}
			let selectionAnswer = JSON.parse(JSON.stringify(this.form.risks[i].selectionAnswer))
			selectionAnswer.forEach(item => {
				delete item.label
			})
			this.telnum = risk[type].mobile
			// 先不调用 getObjdata，等拿到 srl 后再调用
			const buildOrderData = () => [{
				selectionAnswer: selectionAnswer,
				srl: this.form.srl,
				signType: type,
				offername: this.form.companyName,
				riskToken: risk.token
			}]
			this.orderParams = {}
			// 先临时保存，等待拿到 srl
			this.$messagebox({
				title: '温馨提示',
				showCancelButton: true,
				message: '推送5G消息会临时保存信息，继续推送吗？',
				confirmButtonText: '推送'
			}).then((action) => {
				if (action === 'confirm') {
					// 先临时保存，等待拿到 srl
					const savePromise = this.submit(0, false)
					// 防止 submit 内部校验失败时返回 undefined 导致报错
					Promise.resolve(savePromise).then(() => {
						// 保存成功并拿到 srl 后再组织数据
						this.getObjdata(buildOrderData())
						// 拿到 srl 后再推送 5G 消息
						this.$http.post('/xsb/personBusiness/fifthGHandle/h5AddHandleOrder',
							this.handleOrderReq('物联网信息采集远程签字', 'iot_remote_sign', '/xsb/personBusiness/netWorkLine/h5remoteSign')
						).then((res) => {
							let { retCode, retMsg } = res.data
							if (retCode === '0') {
								this.$alert(retMsg || '推送成功')
							} else {
								this.canClickFlg = true
								this.$alert(retMsg || '提交远程签字失败')
							}
						}).catch((response) => {
							this.$alert('提交远程签字连接失败' + response)
						})
					})
				}
			})
		},
		// 客户风险评估采集份数调整
		riskNumChange(type) {
			if (type === 0) {
				if (this.form.risks.length <= 1) {
					this.$toast('至少保留一份客户风险评估')
					return
				}
				// 如果填写过值的话删除需要二次确认
				let risk = this.form.risks[this.form.risks.length - 1]
				let flag = false
				if (risk.selectionAnswer.length > 1 || risk.responsiblePer || risk.handler) flag = true
				let checkList = ['responsiblePerLeader', 'assessor', 'assessorLeader']
				for (let i = 0; i < checkList.length; i++) {
					const key = checkList[i]
					if (risk[key].signType !== 0 || risk[key].signId || risk[key].mobile) {
						flag = true
						break
					}
				}
				if (flag) {
					this.$messagebox({
						title: '提示',
						message: '您已填写部分信息，是否删除该份客户风险评估？(推送的5G签字也会删除)',
						showCancelButton: true,
						confirmButtonText: '确定',
						cancelButtonText: '取消',
						type: 'warning'
					}).then((action) => {
						if (action === 'confirm') {
							this.form.risks.pop()
							// 更新highRisk状态
							this.form.highRisk = this.checkIfHighRisk()
						}
					})
				} else {
					this.form.risks.pop()
					// 更新highRisk状态
					this.form.highRisk = this.checkIfHighRisk()
				}
			} else {
				if (this.form.risks.length === 3) {
					this.$toast('至多添加三份客户风险评估')
					return
				}
				// 初始化新的风险评估表selectionAnswer数组
				let selectionAnswer = Array(16).fill(null)

				// 如果有已存在的风险评估表，复制第一个的公司类型选项（问题0）
				if (this.form.risks[0].selectionAnswer && this.form.risks[0].selectionAnswer[0]) {
					selectionAnswer[0] = this.form.risks[0].selectionAnswer[0]
				}

				// 默认设置13、14、15题（实际对应array索引为13、14、15）为"否"
				selectionAnswer[13] = {
					id: 'v14_2',
					score: 0,
					label: '否'
				}
				selectionAnswer[14] = {
					id: 'v15_2',
					score: 0,
					label: '否'
				}
				selectionAnswer[15] = {
					id: 'v16_2',
					score: 0,
					label: '否'
				}

				this.form.risks.push({
					token: this.createUUID(),
					selectionAnswer: selectionAnswer,
					responsiblePer: '',
					handler: '',
					responsiblePerLeader: { signId: '', signType: 0, remoteSignId: '', name: '', mobile: '' },
					assessor: { signId: '', signType: 0, remoteSignId: '', name: '', mobile: '' },
					assessorLeader: { signId: '', signType: 0, remoteSignId: '', name: '', mobile: '' },
					score: 0
				})
				// 添加完新的风险评估后不需要立即更新highRisk，因为新添加的评估表没有选择任何选项
			}
		},
		//时间选择回调
		handleConfirm(val) {
			if (!val) {
				return
			}
			this.form.registerTime = dateFormat(val, 'yyyy年MM月dd日')
		},
		// 多选题选择回调处理函数
		handleMultiConfirm(val) {
			if (!val) return

			const i = this.multiIndex // 获取当前处理的是第几份风险评估
			const riskSelAns = this.form.risks[i].selectionAnswer

			// 根据多选结果构建答案对象
			// items数组包含所有选中的选项
			const selectedItems = val.items

			// 对于第7题(使用功能)和第9题(套餐流量),取最低分数
			if (selectedItems[0].id.startsWith('v8_') || selectedItems[0].id.startsWith('v10_')) {
				// 合并选项值
				const lowestScoreItem = {
					id: selectedItems.map(item => item.id).join(','),
					score: selectedItems.reduce((lowest, item) => item.score < lowest.score ? item : lowest).score,
					label: selectedItems.map(item =>
						item.label.split(')')[1]).join('、')
				}
				// 更新答案数组，题号 - 1等于下标
				const questionId = parseInt(selectedItems[0].id.split('_')[0].substring(1)) - 1
				this.$set(riskSelAns, questionId, lowestScoreItem)
			}
			// 对于第12题(采用的管理措施),累加得分,最高30分
			else if (selectedItems[0].id.startsWith('v12_')) {
				let totalScore = Math.min(
					selectedItems.reduce((sum, item) => sum + item.score, 0),
					30
				)

				// 构建答案对象
				const answerObj = {
					id: selectedItems.map(item => item.id).join(','),
					score: totalScore,
					label: selectedItems.map(item =>
						item.label.split(')')[1]).join('、')
				}

				this.$set(riskSelAns, 11, answerObj)
			} else {
				// 其他情况，直接取第一个选项
				this.$set(riskSelAns, parseInt(selectedItems[0].id.split('_')[0].substring(1)) - 1, selectedItems[0])
			}
			// 计算15题的得分
			this.computerAnsScore(this.multiIndex)

			// 处理大流量相关逻辑
			this.handleLargeFlowAndHighRisk(this.multiIndex)

			// 自动更新highRisk字段
			this.form.highRisk = this.checkIfHighRisk()
		},
		// 计算15题的得分
		computerAnsScore(i) {
			const riskSelAns = this.form.risks[i].selectionAnswer

			// 重新计算总分
			this.form.risks[i].score = riskSelAns.reduce((totalScore, item) => totalScore + (item ? item.score : 0), 0)
		},
		// 修改答案显示计算函数以支持多选
		answerCompute(value, answer) {
			if (!value) return '请选择'

			// 提取公共逻辑：根据索引获取 label
			const getLabels = (ids, source) => {
				return ids
					.map((id) => {
						const index = parseInt(id.split('_')[1]) - 1
						if (source[index] && source[index].label) {
							return source[index].label
						}
						return ''
					})
					.filter((label) => label) // 过滤掉空字符串
					.join('、')
			}

			const ids = value.id.split(',')
			if ([8, 10].includes(parseInt(value.id.split('_')[0].substring(1)))) {
				// 多选情况
				return getLabels(ids, answer)
			}

			if (value.id.startsWith('v12_') || value.id.startsWith('v7_')) {
				// 处理带有 children 的情况
				let children = []
				answer.forEach((item) => {
					if (item.children) {
						children = children.concat(item.children)
					}
				})
				return getLabels(ids, children)
			}
			// 单选情况
			const index = parseInt(value.id.split('_')[1]) - 1
			if (answer[index] && answer[index].label) {
				return answer[index].label
			}
			return '未知选项'
		},
		/**
		 * 选择题选择handler
		 * @param iter 问题对象
		 * @param i 第几份风险评估
		 */
		selectOption(iter, i) {
			const riskSelAns = this.form.risks[i].selectionAnswer
			// 已发送5G消息签字，不允许修改，防止操作员用假信息骗取签名(第一版本不考虑)
			// if (this.state === 2) {
			// 	this.$toast('已发送5G消息签字，不允许修改，如果信息填写错误请重新上传信息！')
			// 	return
			// }
			// 公司类型动统一变动
			if (iter.id === 0) {
				this.chooseCompanyType()
				return
			}
			let datalist = iter.answer.map((item, idx) => ({
				id: `v${iter.id + 1}_${idx + 1}`,
				score: item.score,
				label: `(${item.score}分)${item.label}`
			}))
			NlDropdown({
				confirmBtn: false,
				datalist: datalist
			}, (retVal) => {
				this.$set(riskSelAns, iter.id, retVal)
				// 计算总得分
				this.computerAnsScore(i)
				// 自动更新highRisk字段
				this.form.highRisk = this.checkIfHighRisk()
			})
		},
		/**
		 * 多选选择handler
		 * @param iter 问题对象
		 * @param i 第几份风险评估
		 */
		selectMultiOption(iter, i) {
			// 更新多选题下标
			this.multiIndex = i
			// 找到数据并提前赋值到treeData
			this.treeData = iter.answer
			// 展示多选项框
			this.chooseFlag = true
			// 7 9 11 二级多选 6二级单选
			this.selectionMode = iter.id === 6 ? 'single' : 'multiple'
		},
		/**
		 * 选择公司领导
		 * @param dataPath 数据路径
		 * @param i 第几份风险评估
		 * @param index 领导类型
		 */
		chooseLeader(dataPath, i, index) {
			let dataPathPoi = this.form.risks[i][dataPath]
			NlDropdown({
				confirmBtn: false,
				datalist: this.options.leaders[index]
			}, (retVal) => {
				if (retVal.mobile !== dataPathPoi.mobile) {
					dataPathPoi.mobile = retVal.mobile
					dataPathPoi.name = retVal.label
					dataPathPoi.signId = ''
				}
			})
		},
		// 返回上一页面
		goPrev() {
			//如果srcFrom不为空，则跳转到srcFrom页面，如果为空，则跳转到历史上一页面
			if (this.srcFrom) {
				this.$router.push({ path: this.srcFrom })
			} else {
				this.$router.go(-1)
			}
		},
		// 切换选项
		toggleAccordion(index) {
			this.selected = this.selected === index ? null : index
		},
		/**
		 * 签名回调
		 * @param obj dataPath 数据路径
		 * @param obj signId 签名流水号
		 */
		signCallBack(obj) {
			// 判断是否是多级对象
			if (obj.dataPath.indexOf('.') === -1) {
				this.form[obj.dataPath] = obj.signId
			} else {
				let dataPathArr = obj.dataPath.split('.')
				let currentObj = this.form.risks[dataPathArr[0]]

				for (let i = 1; i < dataPathArr.length - 1; i++) {
					const key = dataPathArr[i]
					if (!currentObj[key]) {
						currentObj[key] = {}
					}
					currentObj = currentObj[key]
				}

				const lastKey = dataPathArr[dataPathArr.length - 1]
				currentObj[lastKey] = obj.signId
			}
		},
		// 图片回调
		takePhotoCallBack(obj) {
			this.form[obj.dataPath] = obj.imgIds
		},
		// 选择题检查
		selectionCheck(i) {
			// 选择题检查，由于选择题使用的是数组，所以需要检查是否有空值
			let answer = this.form.risks[i].selectionAnswer
			// 查看长度是否为14，如果不为14，说明有空值
			if (!answer || answer.length !== 16) {
				return answer ? answer.length : 0
			}
			// 长度为14但是有空值，返回空值的下标
			const emptyIndex = answer.findIndex(item => !item)
			return emptyIndex !== -1 ? emptyIndex : -1
		},
		// 一键提交
		submit(type, showTips) {
			// 一键提交
			if (type === 1) {
				// 检查14、15题是否有冲突
				const conflict = this.checkQuestionsMutualExclusion()
				if (conflict) {
					this.$toast('第14题和第15题不能同时为"是"，请修改后再提交')
					return
				}

				for (let i = 0; i < this.form.risks.length; i++) {
					let selectionCheck = this.selectionCheck(i)
					if (selectionCheck !== -1) {
						this.$toast(`请填写第${i + 1}份风险评估第${selectionCheck + 1}题`)
						return
					}
				}
				// 静态属性检查
				const staticKeys = [
					{ id: 'companyName', label: '填写公司名称' },
					{ id: 'auditing', label: '在现场考察资质中审核人签字签字' },
					{ id: 'operator', label: '在现场考察资质中集团客户签字签字' },
					{ id: 'inspector', label: '在现场考察报告中现场考察人签字' },
					{ id: 'onlineCheckPic', label: '在现场考察报告上传线上资质审查截图' },
					{ id: 'customerUseScene', label: '在现场考察报告中填写客户单位使用场景描述' },
					{ id: 'customerPic', label: '在现场考察报告中上传客户门头照及考察人合影' },
					{ id: 'customerWorkPic', label: '在现场考察报告中上传客户生产工作或办公场所照片' },
					{ id: 'terminalPic', label: '在现场考察报告中上传终端设备照片' },
					{ id: 'terminalDoc', label: '在现场考察报告中填写终端设备技术文档' },
					{ id: 'inspectionConclusion', label: '在现场考察报告中填写考察结论' },
					{ id: 'registerTime', label: '在现场考察资质中选择注册时间' },
					{ id: 'companyType', label: '在现场考察资质中选择公司类型' },
					{ id: 'highRisk', label: '在现场考察资质中选择是否开通非定向大流量功能' },
					{ id: 'opening', label: '在现场考察资质中选择是否符合开卡要求' }
				]

				for (const item of staticKeys) {
					if (this.form[item.id] === null || this.form[item.id] === '') {
						this.$toast(`请${item.label}`)
						return
					}
				}

				// 动态属性检查
				const actionKeys = [
					{ id: 'handler', label: '客户经办人' },
					{ id: 'responsiblePer', label: '责任人（移动）' },
					{ id: 'responsiblePerLeader', label: '责任人分管领导（移动）' },
					{ id: 'assessor', label: '评估人（移动）' },
					{ id: 'assessorLeader', label: '评估人分管领导（移动）' }
				]

				this.form.risks.forEach((risk, index) => {
					for (const { id, label } of actionKeys) {
						if (['responsiblePer', 'handler'].indexOf(id) !== -1) {
							if (!risk[id]) {
								this.$toast(`请填写客户风险评估第${index + 1}份${label}签字`)
								return
							}
						} else {
							const { signId, signType, remoteSignId } = risk[id]
							if ((signType === 0 && !signId) || (signType === 1 && !remoteSignId)) {
								this.$toast(`请完成客户风险评估第${index + 1}份${label}签字`)
								return
							}
						}
					}
				})
			}
			// 深拷贝this.form
			let form = JSON.parse(JSON.stringify(this.form))
			form.state = type
			// 减少传输量
			for (let i = 0; i < form.risks.length; i++) {
				form.risks[i].selectionAnswer.forEach(item => {
					if (item)
						delete item.label
				})
			}
			form.risks = JSON.stringify(form.risks)
			// 发送请求
			if (showTips) {
				this.$messagebox({
					title: '温馨提示',
					message: type === 0 ? '是否保存信息？' : '是否提交信息？',
					confirmButtonText: '确定',
					showCancelButton: true
				}).then((action) => {
					if (action === 'confirm') {
						const url = '/xsb/personBusiness/netWorkLine/h5netWorkSubmit'
						this.$http.post(url, form).then((res) => {
							if (res.data.retCode === '0') {
								this.$messagebox({
									title: '温馨提示',
									message: '订单已成功提交，请到我的资料里面查看结果！',
									confirmButtonText: '确定'
								}).then((action) => {
									this.$router.push({ path: '/ioTDataCollectionMenu' })
								})
							} else {
								this.$toast(res.data.retMsg)
							}
						})
					}
				})
			} else {
				const url = '/xsb/personBusiness/netWorkLine/h5netWorkSubmit'
				// 直接返回 Promise，方便外部链式调用
				return this.$http.post(url, form).then((res) => {
					if (res.data.retCode === '0') {
						// 保存后台返回的流水号
						if (res.data.data) {
							this.form.srl = res.data.data
						}
					} else {
						this.$toast(res.data.retMsg)
					}
				})
			}
		},
		// 选择公司类型
		chooseCompanyType() {
			this.$messagebox({
				title: '温馨提示',
				message: '更改此选项将同步更改现场考察资质和客户风险评估的公司类型',
				showCancelButton: false,
				confirmButtonText: '确定'
			}).then((action) => {
				if (action === 'confirm') {
					NlDropdown({
						confirmBtn: false,
						datalist: this.options.selection[0].questions[0].answer.map((item, index) => {
							return {
								id: `v${1}_${index + 1}`,
								score: item.score,
								label: `(${item.score}分)${item.label}`
							}
						})
					}, (retVal) => {
						// 将risks里面所有的selectionAnswer的第一项改成retVal
						this.form.risks.forEach(risk => {
							// 将selectedAnswers的第一项设置为retVal
							risk.selectionAnswer[0] = retVal
							// 重新计算计算总得分
							risk.score = risk.selectionAnswer.reduce((totalScore, item) => totalScore + (item ? item.score : 0), 0)
						})
						this.form.companyType = retVal.label.split(')')[1]
					})
				}
			})
		},
		// 选择开卡要求
		changeOpenStatus() {
			NlDropdown({
				confirmBtn: false,
				datalist: [{ id: true, label: '符合' }, { id: false, label: '不符合' }]
			}, (retVal) => {
				this.form.opening = retVal.id
			})
		},
		changeHighRiskStatus() {
			// 检查是否有任何一份风险评估满足条件
			const isHighRisk = this.checkIfHighRisk()
			this.$messagebox({
				title: '提示',
				message: isHighRisk ?
					'检测到至少有一份风险评估中存在开通非定向大流量或无线上网场景，系统已自动将此选项设置为"是"' :
					'未检测到任何风险评估中存在高风险场景，系统已自动将此选项设置为"否"',
				showCancelButton: false
			})
		},
		// 添加新方法：检查是否有任何一份风险评估满足高风险条件
		checkIfHighRisk() {
			// 遍历所有风险评估
			for (let i = 0; i < this.form.risks.length; i++) {
				const riskAnswers = this.form.risks[i].selectionAnswer

				// 检查是否为非定向大流量
				const isNonDirectional = this.isNonDirectionalLargeFlow(riskAnswers)

				// 检查是否为无线上网场景
				const isWireless = this.isWirelessInternet(riskAnswers)
				
				// 高风险应用场景判断：非定向大流量功能 OR 无线上网类服务
				// 满足任意一个条件就是高风险：
				// 1. 非定向大流量功能
				// 2. 无线上网类服务
				if (isNonDirectional || isWireless) {
					return true
				}
			}

			return false
		},
		// 判断是否选择了非定向大流量
		isNonDirectionalLargeFlow(riskAnswers) {
			// 检查第8题(使用功能)是否选择了非定向流量
			const funcAnswer = riskAnswers[7]
			if (!funcAnswer || !funcAnswer.id.split(',').includes('v8_2')) {
				return false
			}
			
			// 检查第10题(套餐流量)是否选择了大于300MB的选项
			const flowAnswer = riskAnswers[9]
			if (!flowAnswer) return false
			
			const largeFlowIds = ['v10_2', 'v10_3', 'v10_4'] // 300MB以上的选项
			const selectedIds = flowAnswer.id.split(',')
			return selectedIds.some(id => largeFlowIds.includes(id))
		},

		// 添加别名方法，解决可能存在的调用问题
		isNonDirectionalLargeFlowOrVoice(riskAnswers) {
			// 直接调用isNonDirectionalLargeFlow方法
			return this.isNonDirectionalLargeFlow(riskAnswers)
		},

		// 判断是否选择了无线上网场景
		isWirelessInternet(riskAnswers) {
			// 第7题是应用场景
			const sceneAnswer = riskAnswers[6]
			if (!sceneAnswer) return false

			// 检查是否选择了无线上网(v7_14)或车/船载WiFi设备(v7_4)
			const wirelessIds = ['v7_14', 'v7_4']
			return sceneAnswer.id.split(',').some(id => wirelessIds.includes(id))
		},

		// 处理大流量和高风险相关的逻辑
		handleLargeFlowAndHighRisk(i) {
			const risk = this.form.risks[i]
			const riskAnswers = risk.selectionAnswer

			// 判断是否为非定向大流量
			const isNonDirectional = this.isNonDirectionalLargeFlow(riskAnswers)

			// 判断是否为无线上网场景
			const isWireless = this.isWirelessInternet(riskAnswers)

			// 根据条件自动设置14题和15题
			if (isNonDirectional) {
				if (isWireless) {
					// 如果是非定向大流量且是无线上网场景，则15题为"是"，14题必须为"否"
					this.$set(riskAnswers, 13, {
						id: 'v14_2',
						score: 0,
						label: '否'
					})
					this.$set(riskAnswers, 14, {
						id: 'v15_1',
						score: 0,
						label: '是'
					})
				} else {
					// 如果是非定向大流量但不是无线上网场景，则14题为"是"，15题必须为"否"
					this.$set(riskAnswers, 13, {
						id: 'v14_1',
						score: 0,
						label: '是'
					})
					this.$set(riskAnswers, 14, {
						id: 'v15_2',
						score: 0,
						label: '否'
					})
				}
			} else {
				// 如果不是非定向大流量，则14题和15题都必须为"否"
				// 因为这两个问题都是针对非定向大流量的情况
				this.$set(riskAnswers, 13, {
					id: 'v14_2',
					score: 0,
					label: '否'
				})
				this.$set(riskAnswers, 14, {
					id: 'v15_2',
					score: 0,
					label: '否'
				})
			}

			// 计算总得分
			this.computerAnsScore(i)

			// 自动更新highRisk状态
			this.form.highRisk = this.checkIfHighRisk()
		},

		// 初始化数据
		getInitData(srl) {
			const url = `/xsb/personBusiness/netWorkLine/h5getNetWorkInfoBySrlNo?srl=${srl}`
			this.$http.get(url).then((res) => {
				if (res.data.retCode === '0') {
					this.form = res.data.data
					// 初始化risks
					this.$set(this.form, 'risks', JSON.parse(this.form.risks))
					// 初始化后自动更新highRisk字段
					this.$nextTick(() => {
						this.form.highRisk = this.checkIfHighRisk()
					})
				} else {
					this.$toast(res.data.retMsg)
				}
			})
		},
		// 检查14题和15题是否存在互斥冲突
		checkQuestionsMutualExclusion() {
			let hasConflict = false;
			this.form.risks.forEach(risk => {
				const answer13 = risk.selectionAnswer[13] // 14题
				const answer14 = risk.selectionAnswer[14] // 15题

				// 如果两题都存在且都是"是"，则报告冲突
				if (answer13 && answer14 &&
					answer13.id.endsWith('_1') && answer14.id.endsWith('_1')) {
					hasConflict = true;
				}
			})
			return hasConflict;
		},
		// 获取 switch 当前选中状态
		getSwitchValue(answer) {
			if (!answer) return false
			return answer.id.endsWith('_1') // _1 表示"是"，_2 表示"否"
		},
		// 处理 switch 点击事件
		handleSwitchClick(iter, riskIndex) {
			const risk = this.form.risks[riskIndex]
			const currentValue = this.getSwitchValue(risk.selectionAnswer[iter.id])
			const isChecked = !currentValue // 切换状态

			// 处理14和15题互斥逻辑（索引为13和14）
			if ((iter.id === 13 || iter.id === 14) && isChecked) {
				// 如果要设置为"是"，检查另一个是否已经是"是"
				const otherQuestionId = iter.id === 13 ? 14 : 13
				const otherAnswer = risk.selectionAnswer[otherQuestionId]

				if (otherAnswer && this.getSwitchValue(otherAnswer)) {
					// 另一个问题已经是"是"，需要将其设置为"否"
					this.$messagebox({
						title: '提示',
						message: `第${otherQuestionId + 1}题和第${iter.id + 1}题不能同时为"是"，系统将自动把第${otherQuestionId + 1}题设置为"否"`,
						showCancelButton: false
					}).then(() => {
						// 设置当前问题为"是"
						this.$set(risk.selectionAnswer, iter.id, {
							id: `v${iter.id + 1}_1`,
							score: 0,
							label: '是'
						})
						// 设置另一个问题为"否"
						this.$set(risk.selectionAnswer, otherQuestionId, {
							id: `v${otherQuestionId + 1}_2`,
							score: 0,
							label: '否'
						})
					})
					return
				}
			}

			// 设置选中值
			this.$set(risk.selectionAnswer, iter.id, {
				id: `v${iter.id + 1}_${isChecked ? 1 : 2}`,
				score: 0,
				label: isChecked ? '是' : '否'
			})

			// 只有当处理的不是第16题（索引为15）时，才调用大流量和高风险相关逻辑
			if (iter.id !== 15) {
				// 计算总得分
				this.computerAnsScore(riskIndex)
				// 自动更新highRisk字段
				this.form.highRisk = this.checkIfHighRisk()
			}
		}
	},
	filters: {
		openingFilter(val) {
			if (val === null) return '点击此处选择'
			return val ? '符合' : '不符合'
		},
		highRiskFilter(val) {
			if (val === null) return '（请完成客户风险评估）'
			return val ? '是' : '不是'
		}
	}
}
</script>
<style lang='less' scoped>
.wrapper {
	background-color: #FAFAFA;

	.main-content {
		margin-top: 44px;
		height: calc(100vh - 44px - 80px);
		overflow: auto;

		.accordion {
			padding: 30px 20px 70px 20px;

			// 基础信息录入区域
			.basic-info-collect {
				background: linear-gradient(221deg, #FFFFFF 0%, #F8FCFF 100%);
				height: 86px;
				border: 2px solid #F2F6FF;
				position: relative;

				img {
					width: 75px;
					height: 95px;
					left: 12px;
					bottom: 4px;
					position: absolute;
				}

				.info {
					margin-left: 87px;
					padding: 15px 10px;

					.info-big-title {
						font-size: 16px;
						font-weight: 400;
						color: #000000;
						line-height: 20px;
					}

					.info-input {
						margin-top: 10px;
						display: flex;
						justify-content: space-between;
						font-size: 14px;

						.info-title {
							width: 30%;
							color: #000000;
							line-height: 20px;
						}

						input {
							width: calc(70% - 15px);
							border: none;
							outline: none;
							background-color: transparent;

							&::-webkit-input-placeholder {
								font-size: 14px;
								color: #929292;
							}
						}
					}
				}
			}

			.other-info-collect {
				margin-top: 20px;
				background: #FFFFFF;
				box-shadow: 0 3px 10px 0 rgba(86, 125, 244, 0.05);
				border-radius: 4px;
				padding: 0 14px;

				// 得分区域
				.score-area {
					position: relative;
					width: 100%;
					height: 50px;
					margin-top: 20px;
					align-items: center;
					background-image: url('../../../../static/img/score-bgi.png');
					background-size: cover;

					.score-icon {
						position: absolute;
						width: 30px;
						height: 30px;
						left: 20px;
						top: 10px;
						vertical-align: middle;
					}

					.score-txt {
						left: 55px;
						font-size: 14px;
						color: #000000;
						line-height: 20px;
					}

					.score-num {
						right: 50px;
						font-size: 24px;
						font-weight: 400;
						color: #0079FE;
						line-height: 20px;
					}

					.score-total {
						right: 20px;
						font-size: 14px;
						color: #000000;
						line-height: 20px;
					}

					.score-txt,
					.score-num,
					.score-total {
						position: absolute;
						top: 15px;
					}
				}

				.accordion-item {

					// 现场考察资质的header以及签字版不需要border-bottom
					.border-none {
						border-bottom: none !important;
					}

					.accordion-header {
						font-weight: 400;
						color: #3D3D3D;
						line-height: 14px;
						font-size: 14px;
						display: flex;
						justify-content: space-between;
						align-items: center;
						padding: 14px 0;
						border-bottom: 1px solid #dddddd;

						.accordion-icon {
							display: inline-block;
							width: 20px;
							height: 20px;
							border-radius: 10px;
							line-height: 20px;
							text-align: center;
							background-color: #E5F1FE;
							color: #0079FE;
							font-weight: 900;
							font-size: 13px;
							transition: transform 0.2s;

						}

						// 右侧箭头动画
						.icon-up {
							transform: rotate(0deg);
						}

						.icon-down {
							transform: rotate(90deg);
						}

						// 客户风险评估采集份数
						.num {
							float: right;

							.num-txt {
								text-align: center;
								display: inline-block;
								width: 20px;
							}
						}
					}


					.accordion-content {
						padding-bottom: 10px;
					}

					.information-list {
						padding: 16px 16px 0;
						margin-top: 16px;
						border-radius: 4px;
						background-color: #F6F9FC;

						// 底部无padding修复
						.padding-bottom-10 {
							padding-bottom: 10px;
						}

						.information-title {
							font-size: 14px;

							.information-icon {
								display: inline-block;
								width: 3px;
								height: 14px;
								background: #007AFF;
								border-radius: 9999px;
								vertical-align: bottom;
							}
						}

						.information-content {
							.information-item {
								position: relative;
								border-bottom: 1px solid #dddddd;
								min-height: 50px; // 改为最小高度
								padding: 10px 0; // 添加上下padding
								display: flex; // 使用flex布局
								align-items: center; // 垂直居中
								font-size: 14px;

								// 问题标题样式
								>.question {
									margin-right: 5px;
								}

								// 问题内容区域样式(包含b标签和问题文本)
								>.question,
								>span:first-of-type {
									flex: 0 0 50%; // 固定宽度50%
									display: inline-flex;
									align-items: center;
								}

								// 选项样式
								.selection {
									flex: 0 0 50%; // 固定宽度50%
									text-align: right;
									padding-right: 20px; // 为右侧箭头留出空间
									word-break: break-all; // 允许在任意字符间换行
									line-height: 1.4; // 调整行高
									display: flex; // 添加flex布局
									justify-content: flex-end; // 内容靠右对齐
									align-items: center; // 垂直居中

									i {
										color: #929292;
										margin-left: 5px; // 添加左边距
										flex-shrink: 0; // 防止箭头被压缩
									}
								}

								// 5G消息签字
								.select-leader {
									float: right;

									.send-msg {
										display: inline-block;
										width: 78px;
										height: 24px;
										background: #0079FE;
										border-radius: 12px;
										line-height: 24px;
										text-align: center;
										color: #fff;
									}
								}

								// 签名方式选择
								.sign-selection {
									float: right;

									.sign-achive,
									.sign-inachive {
										display: inline-block;
										width: 78px;
										height: 24px;
										line-height: 24px;
										text-align: center;
										border-radius: 12px;
										font-size: 12px;
										color: #000000;
									}

									.sign-achive {
										color: #0079FE;
										background: #CEE7FF;
										border: 1px solid #4F8CEE;
									}

									.sign-inachive {
										color: #3D3D3D;
										border: 1px solid #D9D9D9;
									}
								}
							}

							> :last-child {
								border: none;
								/* 移除最后一个子元素的边框 */
							}

							// 文本输入框
							.description {
								textarea {
									border: none;
									width: 100%;
									padding: 0;
									height: 80px;
									resize: vertical;
									/* 垂直方向可拖动 */
									min-height: 80px;
									/* 最小高度 */
								}
							}

							// 资质确认书
							.confirmation-letter {
								// 正式文件格式
								font-size: 16px;
								/* 字体大小 */
								line-height: 1.6;
								/* 行高 */
								padding: 10px 0;
								/* 段落下边距 */
								text-indent: 2em;
								/* 首行缩进 */
								text-align: left;
								/* 文本两端对齐 */
								word-wrap: break-word;
								/* 自动换行 */
								font-family: "宋体", "Microsoft YaHei", "SimHei", Arial, sans-serif;
							}
						}
					}
				}
			}
		}
	}

	.close-all {
		position: fixed;
		bottom: 90px;
		right: 10px;
		background-color: #0079FE;
		color: #fff;
		font-weight: 900;
		text-align: center;
		width: 20px;
		height: 20px;
		line-height: 20px;
		border-radius: 3px;
		font-size: 14px;
	}

	.save-submit {
		position: fixed;
		bottom: 0;
		left: 0;

		display: flex;
		justify-content: space-evenly;
		align-items: center;

		width: 100%;
		height: 80px;
		background-color: #fff;
		box-shadow: 0px -2px 10px 0px rgba(0, 0, 0, 0.1);

		.save-btn,
		.submit-btn {
			width: 40%;
			height: 48px;
			border-radius: 5px;
			border: 1px solid #0079fd;
			font-size: 16px;
			font-weight: 400;
		}

		.save-btn {
			background-color: #fff;
			color: #0079fd;
		}

		.submit-btn {
			background-color: #0079fd;
			color: #fff;
		}
	}

	.switch-selection {
		display: flex !important;
		justify-content: flex-end;
		gap: 10px;

		span {
			padding: 5px 15px;
			border: 1px solid #ddd;
			border-radius: 15px;
			transition: all 0.3s;
		}

		.switch-active {
			background: #0079FE;
			color: white;
			border-color: #0079FE;
		}
	}

	.slider-container {
		display: flex;
		align-items: center;
		background: #F5F5F5;
		padding: 2px;
		border-radius: 5px;
		width: 120px;
		position: relative;
		height: 36px;

		.option-text {
			position: absolute;
			font-size: 12px;
			color: #666;
			transition: color 0.3s;
			z-index: 1;
			user-select: none;

			&.left {
				left: 22%;
			}

			&.right {
				right: 22%;
			}

			&.active {
				color: #0079FE;
				font-weight: 500;
			}
		}

		.slider-track {
			position: absolute;
			left: 0;
			right: 0;
			top: 0;
			bottom: 0;
			border-radius: 3px;
			overflow: hidden;

			.slider-thumb {
				position: absolute;
				width: 60px;
				height: 30px;
				background: white;
				border-radius: 10px;
				top: 2px;
				left: 2px;
				box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
				border: 1px solid #E8E8E8;
				transition: transform 0.3s ease;

				&.is-right {
					transform: translateX(56px);
				}
			}
		}
	}
}
</style>
