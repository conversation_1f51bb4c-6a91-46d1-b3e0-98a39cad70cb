<template>
  <div class='inlet-icon'>
    <div class='menu-box' ref='menuBoxRef'>
      <img src='static/img/ZeroMenu/inlet_icon.png'
           class='inlet-img'
           @touchstart.stop='touchStart'
           @touchend.stop='touchEnd'
           @touchmove.stop='touchMove'
           v-show='!inletFlg && !showPicture'
           alt='' />
      <img src='static/img/ZeroMenu/inlet_close.png' v-show='inletFlg' alt='' class='inlet_close' @click='closeInlet'>
      <img src='static/img/ZeroMenu/inlet_voice.png' v-show='inletFlg' alt='' class='inlet_voice'>
      <img src='static/img/ZeroMenu/inlet_photo.png' v-show='inletFlg' alt='' class='inlet_photo'
           @click='openOrcIdentify'>
    </div>
    <div class='inlet-menu' v-show='inletFlg'></div>
    <div v-if='ocrIdentifyFlg'
         @identificOrc='identificOrc'>
      <div class='ocr-identify'>
        <div class='id-box' v-show='step === "1"'>
          <div class='id-top-box'>
            <img :src='getInfo.logo' alt='' :class='getInfo.className'>
<!--            <img :src='getInfo.title' alt='' class='id-img'>-->
            <span class='id-img' :style="dynamicStyle"><span>「</span>{{ getInfo.text }}<span>」</span></span>
            <div class='id-message'>
              <img src='static/img/ZeroMenu/id-speak.png' alt='' class='id-speak'>
              <p v-if='showInfo'>您刚才拍照的是 『{{ getInfo.text }}』读取到以下信息</p>
              <p v-else>{{ failMsg }}</p>
            </div>
          </div>
          <div class='id-bottom-box' v-if='certType==="1"'>
            <div class='info'>
              <span>{{ certInfo.idCardName }}</span><span class='info-num'>{{ certInfo.idCardNo }}</span>
            </div>
            <p>是否需要办理：</p>
            <div class='btns'>
              <div v-for='(item,index) in idCardMenuList.slice(0,2)' :key='index' @click='businessCkPre(item)'>{{ item.privName }}
              </div>
            </div>
            <div class='more-business' v-show='!showMore' @click='showMore=true'><span>更多业务</span></div>
            <div class='btns' v-show='showMore'>
              <div v-for='(item,index) in idCardMenuList.slice(2,4)' :key='index' @click='businessCkPre(item)'>{{ item.privName }}
              </div>
            </div>
          </div>
          <div class='id-bottom-box' v-if='certType==="2"'>
            <div class='info license-info'>
              <div>{{ certInfo.certificateName }}<span class='iconfont sousuo' @click='searchGroup'></span></div>
              <p>执照编码：{{ certInfo.certificateNo }}</p>
            </div>
            <p>是否需要办理：</p>
            <div class='btns'>
              <div v-for='(item,index) in groupCardMenuList.slice(0,2)' :key='index' @click='businessCkPre(item)'>{{ item.privName }}
              </div>
            </div>
            <div class='more-business' v-show='!showMore' @click='showMore=true'><span>更多业务</span></div>
            <div class='btns' v-show='showMore'>
              <div v-for='(item,index) in groupCardMenuList.slice(2,4)' :key='index' @click='businessCkPre(item)'>{{ item.privName }}
              </div>
            </div>
<!--            <span class='iconfont shangzhankai' v-show='showMore' @click='showMore=false'></span>-->
          </div>
          <div class='id-bottom-box' v-if='certType==="3" && showInfo'>
            <div class='info license-info'>
              <div>{{ certInfo.busyName }}</div>
              <p>{{ certInfo.contractNbr }}</p>
            </div>
            <p class='tip'>您已上传1页，是否继续合同归档？</p>
            <div class='btns'>
              <div @click='continueAction'>继续归档</div>
            </div>
          </div>
          <img src='static/img/ZeroMenu/close.png' alt='' class='close' @click='closeOcrIdentify'>
        </div>
        <div class='license-img-box' v-if='step === "2"'>
          <div class='title-box'>
            <div class='title'>
              <div><img src='static/img/ZeroMenu/build.png' alt=''><span>{{ certInfo.busyName }}</span></div>
              <p>{{ certInfo.contractNbr }}</p>
            </div>
          </div>
          <ul class='content'>
            <li v-for='(item,index) in contractPage' :key='index'>
              <span v-show='item.status != 0' class='iconfont shanchu'
                    @click='clearPicInfo("contractPage",index)'></span>
              <div>
                <img v-if='item.status != 0 ' :src='item.src' alt=''>
                <img v-if='item.status == 0 ' src='static/img/ZeroMenu/unload.png' alt=''>
                <div class='blurring' v-if='item.status===2'><p>不清晰</p></div>
              </div>
              <p>第{{ item.index }}页</p>
            </li>
            <li>
              <span v-show='legalInfo.zPic.status != 0' class='iconfont shanchu'
                    @click='clearPicInfo("legal_z")'></span>
              <div @click='continueUploadNew("legal_z")'>
                <img v-if='legalInfo.zPic.status != 0 ' :src='legalInfo.zPic.src' alt=''>
                <img v-if='legalInfo.zPic.status == 0 ' src='static/img/ZeroMenu/idcard_z.png' alt=''>
                <div class='blurring' v-if='legalInfo.zPic.status===2'><p>不清晰</p></div>
              </div>
              <p>法人证件国徽面</p>
            </li>
            <li>
              <span v-show='legalInfo.fPic.status != 0' class='iconfont shanchu'
                    @click='clearPicInfo("legal_f")'></span>
              <div @click='continueUploadNew("legal_f")'>
                <img v-if='legalInfo.fPic.status != 0' :src='legalInfo.fPic.src' alt=''>
                <img v-if='legalInfo.fPic.status == 0' src='static/img/ZeroMenu/idcard_f.png' alt=''>
                <div class='blurring' v-if='legalInfo.fPic.status===2'><p>不清晰</p></div>
              </div>
              <p>法人证件人像面</p>
            </li>
            <li>
              <span v-show='jbrInfo.zPic.status != 0' class='iconfont shanchu' @click='clearPicInfo("jbr_z")'></span>
              <div @click='continueUploadNew("jbr_z")'>
                <img v-if='jbrInfo.zPic.status != 0 ' :src='jbrInfo.zPic.src' alt=''>
                <img v-if='jbrInfo.zPic.status == 0 ' src='static/img/ZeroMenu/idcard_z.png' alt=''>
                <div class='blurring' v-if='jbrInfo.zPic.status===2'><p>不清晰</p></div>
              </div>
              <p>经办人证件国徽面</p>
            </li>
            <li>
              <span v-show='jbrInfo.fPic.status != 0' class='iconfont shanchu' @click='clearPicInfo("jbr_f")'></span>
              <div @click='continueUploadNew("jbr_f")'>
                <img v-if='jbrInfo.fPic.status != 0' :src='jbrInfo.fPic.src' alt=''>
                <img v-if='jbrInfo.fPic.status == 0' src='static/img/ZeroMenu/idcard_f.png' alt=''>
                <div class='blurring' v-if='jbrInfo.fPic.status===2'><p>不清晰</p></div>
              </div>
              <p>经办人证件人像面</p>
            </li>
            <li>
              <span v-show='authInfo.status != 0' class='iconfont shanchu' @click='clearPicInfo("auth")'></span>
              <div @click='continueUpload("auth")'>
                <img v-if='authInfo.status != 0' :src='authInfo.src' alt=''>
                <img v-if='authInfo.status == 0' src='static/img/ZeroMenu/idcard_f.png' alt=''>
                <div class='blurring' v-if='authInfo.status===2'><p>不清晰</p></div>
              </div>
              <p>授权委托书</p>
            </li>
          </ul>
          <div class='btns'>
            <div @click='putInFile'>
              <img src='static/img/ZeroMenu/bag.png' alt=''>
              <span>合同归档</span>
            </div>
            <div class='upload' @click='continueMuchUpload("")'>
              <img src='static/img/ZeroMenu/upload.png' alt=''><span>继续上传</span>
            </div>
          </div>
          <img src='static/img/ZeroMenu/close.png' alt='' class='closeProtol' @click='closeOcrIdentify'>
        </div>

        <div class='success-box' v-show='step === "3"'>
          <img class='success' src='static/img/ZeroMenu/success.png' alt=''>
          <em id='qrcode' ref='qrcode'></em>
          <p>{{ contractTip || "合同归档成功" }}</p>
          <span>协议编码：{{ certInfo.contractNbr }}</span>
          <div>
            <div v-show='showShareOrder' @click='downLoadPic'><span class='iconfont a-Group427319090'></span>保存订单</div>
            <div v-show='!showShareOrder' @click='closeOcrIdentify'>确定</div>
            <div v-show='showShareOrder' @click='openWeiXin'><span class='iconfont weixin'></span>分享订单</div>
<!--            <div @click='goOrderList'><span class='iconfont xiangqing1'></span>查看订单</div>-->
          </div>
          <img src='static/img/ZeroMenu/close.png' alt='' class='closeSuccess' @click='closeOcrIdentify'>
        </div>
      </div>
    </div>
    <jikeGroupList v-if='showGroup' @chooseGroup='chooseGroup' :groupName='certInfo.certificateName' :isBottom='true'></jikeGroupList>

  </div>
</template>
<script>
import ClientJs from '@/base/clientjs'
import Storage from '@/base/storage.js'
import { dateFormat } from '@/base/utils'
import {cmpClientVersion} from '@/base/utils';
import { Indicator } from 'mint-ui'
import jikeGroupList from './jikeGroupList.vue'
import { skipCommom } from 'components/desk/WorkStageNew/commonBusiCk.js'
import QRCode from '@/base/qrcode.js'
import {h5getMenuByLevel} from '@/base/request/commonReq.js'

export default {
  components: { ClientJs, Storage, jikeGroupList },
  mixins: [skipCommom],
  props:{
    'showPicture':{
      type: Boolean,
      default: false
    },
    'srcFrom':{
      type: String,
      default: 'workStageNew'
    },
  },
  data() {
    return {
      inletFlg: false,
      time: '',
      windowHeigth: 0,
      ocrIdentifyFlg: false,
      certType: 0,
      certInfo: {},
      certImg: '',
      electronicToken: '',
      pageIndex: 1,
      contractPage: [],
      step: '1',
      idCardMenuList: [],
      groupCardMenuList: [],
      legalInfo: {
        zPic: {
          fileName: '',
          status: 0,
          src: ''
        },
        fPic: {
          fileName: '',
          status: 0,
          src: ''
        }
      },
      jbrInfo: {
        zPic: {
          fileName: '',
          status: 0,
          src: ''
        },
        fPic: {
          fileName: '',
          status: 0,
          src: ''
        }
      },
      authInfo: {
        fileName: '',
        status: 0,
        src: ''
      },
      //9300-合同盖章图片;6020-营业执照;6030-组织机构代码证; 6040-税务登记证;6120-法人有效证件-反面;6110-法人有效证件-正面;
      //6910-授权经办人有效证件-正面;6920-授权经办人有效证件-反面;6400-委托函附件
      actionTypeList: [
        { id: '9300', label: 'contractPage' },
        { id: '6120', label: 'legal_f' },
        { id: '6110', label: 'legal_z' },
        { id: '6910', label: 'jbr_z' },
        { id: '6920', label: 'jbr_f' },
        { id: '6400', label: 'auth' }],
      currentPicType:  { id: '9300', label: 'contractPage' },
      uinfo:{},
      showInfo:true,
      failMsg: '无法识别图片类型（支持身份证、营业执照、合同附件），请检查清晰度重新上传',
      getTokenCount:0,
      index: 0,
      cardData: {},
      showShareOrder: false,
      showGroup: false,
      groupName: '',
      showMore: false,
      menuList: [],
      codePic: '',
      sharaCrmId: '',
      isUseNewOcr: false,
      contractTip: '合同归档成功',
      isCurrentCert:false,
    }
  },
  mounted() {
    this.windowHeigth = window.innerHeight || (document.body) && (document.body.clientHeight)
    window['certPaizhaoCb'] = info => {
      let param = {}
      param.unEncrpt = true
      param.image = info.fileImage
      this.certImg = info.fileImage
      let index = info.fileName.lastIndexOf('\.')
      param.fileType = info.fileName.substring(index, info.fileName.length)
      param.certTypeList = '业务受理单,身份证(国徽),身份证(人脸),营业执照,事业单位法人证书,统一社会信用代码证书,社会团体法人登记证,组织机构代码证'
      this.identificOrc(param)
    }
    for (let i = 0; i <= 8; i++) {
      window[`muchPaiZhaoCb${i}`] = info => {
        if (info) {
          let picInfo = JSON.parse(info.replace(/\n/g, ''))
          let param = {}
          param.unEncrpt = true
          param.image = picInfo.fileImage
          // this.certImg = info.fileImage
          let index = picInfo.fileName.lastIndexOf('\.')
          param.fileType = picInfo.fileName.substring(index, picInfo.fileName.length)
          this.identificOrc(param)
        }
      }
    }
    for (let i = 0; i <= 8; i++) {
      window[`muchPaiZhaoCertCb${i}`] = info => {
        if (info) {
          let picInfo = JSON.parse(info.replace(/\n/g, ''))
          let param = {}
          param.unEncrpt = true
          param.image = picInfo.fileImage
          let index = picInfo.fileName.lastIndexOf('\.')
          param.fileType = picInfo.fileName.substring(index, picInfo.fileName.length)
          this.identificMuchOrc(param)
        }
      }
    }
    for (let i = 0; i <= 1; i++) {
      window[`muchPaiZhaoCbNew${i}`] = info => {
        if (info) {
          let picInfo = JSON.parse(info.replace(/\n/g, ''))
          let param = {}
          param.unEncrpt = true
          param.image = picInfo.fileImage
          let index = picInfo.fileName.lastIndexOf('\.')
          param.fileType = picInfo.fileName.substring(index, picInfo.fileName.length)
          this.identificOrcForCard(param)
        }
      }
    }
  },
  computed: {
    dynamicStyle() {
      const baseSize = 2; // 基础 font-size，单位 rem
      const maxLength = 12; // 最大字符长度
      const length = this.getInfo.text.length;
      // 动态计算字体大小，字数越多字体越小，最小字体大小为 1rem
      const fontSize = Math.max(baseSize - length / maxLength, 1);
      if(length <= 4){
        return {
          'width': 'calc(100% - 107px)','text-align': 'center', fontSize: `${fontSize}rem`,
        };
      }else{
        return {
          fontSize: `${fontSize}rem`,
        };
      }

    },
    getInfo() {
      let obj = {
        logo: '',
        title: '',
        text: '',
        className: ''
      }
      switch (this.certType) {
        case '1':
          obj.logo = 'static/img/ZeroMenu/id-logo.png'
          obj.title = 'static/img/ZeroMenu/id-img.png'
          obj.text = '身份证'
          obj.className = 'id-logo'
          break
        case '2':
          obj.logo = 'static/img/ZeroMenu/license-logo.png'
          obj.title = 'static/img/ZeroMenu/license-img.png'
          obj.text = '营业执照'
          obj.className = 'license-logo'
          break
        case '3':
          obj.logo = 'static/img/ZeroMenu/contract-logo.png'
          obj.title = 'static/img/ZeroMenu/contract-img.png'
          obj.text = '合同文件'
          obj.className = 'contract-logo'
          break
      }
      return obj
    }
  },
  methods: {
    closeInlet() {
      this.inletFlg = false
    },
    touchStart(e) {
      e.preventDefault()
      this.time = Date.now()
    },
    touchMove(e) {
      e.preventDefault()

      let touch = event.targetTouches[0] || event.changedTouches[0]
      let bottom = document.documentElement.clientHeight - touch.pageY - 40


      if (bottom < 50) {
        bottom = 50
      } else if (bottom + 150 > this.windowHeigth) {
        bottom = this.windowHeigth - 150
      }

      this.$refs.menuBoxRef.style.bottom = bottom + 20 + 'px'
    },
    touchEnd(e) {
      let dt = Date.now() - this.time
      //点击
      if (dt < 200) {
        this.inletFlg = true
      }
    },
    searchGroup(){
      let param = {
        'area': this.srcFrom,
        'privId': 'z-' + '100212',
        'stationId': this.uinfo.stationId,
        unLoadFlg: true //屏蔽加载圈
      }
      this.$http.post('/xsb/api-user/commonFunc/h5commCollect', param)
      this.showGroup = true
    },
    openOrcIdentify() {
      if(!this.showPicture){
        this.inletFlg = false
      }
      // 支持批量上传组合推荐

      let curV = Storage.get('appVersion');//从缓存中获取客户端版本
      let flag = cmpClientVersion(curV, '2.24.72')
      if (flag) {
        ClientJs.openMultiplePhoto('muchPaiZhaoCertCb')
      } else {
        ClientJs.openCameraAndShow('1', 'certPaizhaoCb')
      }
    },
    closeOcrIdentify() {
      this.ocrIdentifyFlg = false
      if (this.showPicture) {
        this.$emit('closeOcr')
      }
      this.certType = 0
      this.certInfo = {}
      this.certImg = ''
      this.electronicToken = ''
      this.pageIndex = 1
      this.contractPage = []
      this.step = '1'
      this.legalInfo = {
        zPic: {
          fileName: '',
          status: 0,
          src: ''
        },
        fPic: {
          fileName: '',
          status: 0,
          src: ''
        }
      }
      this.jbrInfo = {
        zPic: {
          fileName: '',
          status: 0,
          src: ''
        },
        fPic: {
          fileName: '',
          status: 0,
          src: ''
        }
      }
      this.authInfo = {
        fileName: '',
        status: 0,
        src: ''
      }
      this.currentPicType = { id: '9300', label: 'contractPage' }
      this.showInfo = true
      this.getTokenCount = 0
    },
    initProtocal(data,total) {
      this.$set(this.certInfo, 'busyName', data.busyName)
      this.$set(this.certInfo, 'contractCode', data.contractCode)
      this.$set(this.certInfo, 'prodNames', data.prodName)
      this.$set(this.certInfo, 'orderId', data.orderId)
      this.$set(this.certInfo, 'execId', data.execId)

      let contractPage = []
      for (let i = 0; i < this.certInfo.pageTotal; i++) {
        contractPage.push({
          index: i + 1,
          attachName: '',
          status: 0
        })
      }
      this.$set(contractPage, this.certInfo.pageIndex - 1, {
        index: this.certInfo.pageIndex,
        fileName: this.certInfo.fileName,
        status: 1,
        src: 'data:image/jpeg;base64,' + this.certImg,
        attachId:this.certInfo.attachId
      })
      this.contractPage = contractPage
      this.certType = '3'

    },
    async initCardInfo(data,param) {
      let image =  param && param.image ? param.image : this.certImg
      this.ocrIdentifyFlg = true
      if (data.type == '1' && data.ecInfo) {
        this.certType = '2'
        if (data.ecInfo.name != null && data.ecInfo.uniscId != null) {
          this.certInfo = {
            certificateName: data.ecInfo.name,
            certificateNo: data.ecInfo.uniscId,
            certificateAddress: data.ecInfo.address,
            cmIotGroupAddress: data.ecInfo.address,
            legalPerson: data.ecInfo.legalPersonName,
            certTypeName: data.ecInfo.certTypeName,
            fileName: data.fileName
          }
          this.getInfo.text = data.ecInfo.certTypeName
          Storage.session.remove('ocrInfo')
          Storage.session.set('ocrInfo', this.certInfo)
        }
      } else if (data.type == '2' && data.peInfo) {
        this.certType = '1'
        if (data.peInfo.name != null && data.peInfo.idCardNumber != null) {
          this.certInfo = {
            idCardName: data.peInfo.name,
            idCardNo: data.peInfo.idCardNumber,
            idCardAddress: data.peInfo.address,
            fileName: data.fileName
          }
          Storage.session.remove('ocrInfo')
          Storage.session.set('ocrInfo', this.certInfo)
        } else {
          this.$alert(this.failMsg)
        }
      } else if (data.type == '3') {
        // this.auditPic(image)
        if (this.contractPage && this.contractPage.length > 0) {
          this.pageIndex++
          if (this.certInfo) {
            this.$set(this.contractPage, this.pageIndex - 1, {
              index: this.pageIndex,
              fileName: this.certInfo.fileName,
              status: 1,
              src: 'data:image/jpeg;base64,' + image,
            })
          }
          let attachId = await this.fileUpload(data.fileName);
          this.certInfo.attachId = attachId
        } else {
          this.certInfo = {
            contractNbr: data.contractInfo.split('|')[2],
            pageIndex: parseInt(data.contractInfo.split('|')[1]),
          }
          if (this.electronicToken) {
             this.queryProtocol()
            let attachId = await this.fileUpload(data.fileName);
            this.certInfo.attachId = attachId
          } else {
             this.getProtocolToken(data.fileName)
          }

        }
      } else {
        this.showInfo = false
      }
    },
    shareOrder(){
      this.$router.push({
        path: '/zeroMenuJikeShare',
        query:{
          oneId: `${this.cardData.oneId}|${Storage.session.get('userInfo').crmId}`
        }
      })
    },
    continueAction() {
      let param = {
        'area': this.srcFrom,
        'privId': 'z-' + '999',
        'stationId': this.uinfo.stationId,
        unLoadFlg: true //屏蔽加载圈
      }
      this.$http.post('/xsb/api-user/commonFunc/h5commCollect', param)
      this.step = '2'
    },
    businessCkPre(item) {
      //设置调用链需要采集的菜单编号 add by qhuang
      this.$pointLesslog && this.$pointLesslog.setChainBusiType(item.privId)
      let isNewFeature = item.isNewFeature// 0：不用控制，1：控制
      if (isNewFeature == '1') {
        let featureType = item.featureType//业务类型
        let param = {
          busiType: featureType
        }
        this.$http.post('/xsb/ability/businessLimit/h5QryBusiPermission', param).then(res => {
          //注意 retCode=0 有权限点击该菜单
          let {retCode,retMsg,data} = res.data
          if (retCode == '0') {
            this.enterAuthCheck(item)
          } else {
            this.$toast(retMsg || '暂无权限')
          }
        })
      } else {
        this.enterAuthCheck(item);
      }
    },
    enterAuthCheck(item) {
      //入网鉴权校验
      let enterNetAuthLilt = ['10019', '100026', '100042', '100044']
      //判断鉴权
      if (enterNetAuthLilt.indexOf(item.privId) > -1) {
        let url = `/xsb/ability/businessLimit/h5qryEnterAuthority?privId=${item.privId}`
        this.$http.get(url).then((response) => {
          let { retCode, retMsg, data } = response.data
          if (retCode == '0') {
            if (data.isLimit == '1') {
              this.$messagebox({
                title: '提示',
                message: '操作员未做身份认证或认证信息已失效，请点击【确定】进行读证认证操作',
                showCancelButton: true,
                showConfirmButton: true
              }).then(action => {
                if (action == 'confirm') {
                  this.$router.push(
                    {
                      path: '/enternetAuth',
                      query: {
                        item: item,
                        srcFrom: 'WorkStageNew'
                      }
                    }
                  )
                }
              })
            } else {
              this.skipBusiness(item)
            }
          } else {
            this.$alert(retMsg || '入网鉴权异常')
            return false
          }
        }).catch((response) => {
          this.skipBusiness(item)
        })
      } else {
        this.skipBusiness(item)
      }
    },
    skipBusiness(item) {
      let param = {
        'area': this.srcFrom,
        'privId': 'z-' + item.privId,
        'stationId': this.uinfo.stationId,
        unLoadFlg: true //屏蔽加载圈
      }
      this.$http.post('/xsb/api-user/commonFunc/h5commCollect', param)
      this.goBusinessPage(item,'ocrFrom')
    },
    continueUpload(picType) {
      if (picType) {
        this.currentPicType = this.actionTypeList.find(item => item.label == picType)
      } else {
        this.currentPicType =  { id: '9300', label: 'contractPage' }
      }
      ClientJs.openCameraAndShow('1', 'certPaizhaoCb')
    },
    continueUploadNew(picType){
      if (picType) {
        this.currentPicType = this.actionTypeList.find(item => item.label == picType)
      } else {
        this.currentPicType =  { id: '9300', label: 'contractPage' }
      }
      let curV = Storage.get('appVersion');//从缓存中获取客户端版本
      let flag = cmpClientVersion(curV, '2.24.72')
      if(flag){
        ClientJs.openMultiplePhoto( 'muchPaiZhaoCbNew')
      }else{
        ClientJs.openCameraAndShow('1', 'certPaizhaoCb')
      }
    },
    continueMuchUpload(picType) {
      let curV = Storage.get('appVersion');//从缓存中获取客户端版本
      let flag = cmpClientVersion(curV, '2.24.72')
      if(flag){
        this.currentPicType =  { id: '9300', label: 'contractPage' }
        ClientJs.openMultiplePhoto( 'muchPaiZhaoCb')
      }else{
        this.continueUpload(picType)
      }
    },
    clearPicInfo(typeName, index) {
      let initPic = {
        fileName: '',
        status: 0,
        src: '',
        index:index+1
      }
      if (typeName == 'contractPage') {
        this.$set(this.contractPage, index, initPic)
      } else if (typeName == 'legal_z') {
        this.legalInfo.zPic = initPic
      } else if (typeName == 'legal_f') {
        this.legalInfo.fPic = initPic
      } else if (typeName == 'jbr_z') {
        this.jbrInfo.zPic = initPic
      } else if (typeName == 'jbr_f') {
        this.jbrInfo.fPic = initPic
      } else if (typeName == 'auth') {
        this.authInfo = initPic
      }

    },
    async ocrProtocol(data,param) {
      this.ocrIdentifyFlg = true
      let attachId = await this.fileUpload(data.fileName,param);
      let image =  param && param.image ? param.image : this.certImg
      if (this.currentPicType.label == 'legal_z') {
        this.legalInfo.zPic = {
          fileName: data.fileName,
          status: 1,
          src: 'data:image/jpeg;base64,' + image,
          attachId:attachId
        }
      } else if (this.currentPicType.label == 'legal_f') {
        if (data.peInfo && data.peInfo.name && data.peInfo.idCardNumber) {
          this.legalInfo.fPic = {
            fileName: data.fileName,
            status: 1,
            src: 'data:image/jpeg;base64,' + image,
            idCardName: data.peInfo.name,
            idCardNo: data.peInfo.idCardNumber,
            idCardAddress: data.peInfo.address,
            attachId:attachId
          }
        } else {
          this.legalInfo.fPic = {
            fileName: data.fileName,
            status: 2,
            src: 'data:image/jpeg;base64,' + image,
            attachId:attachId
          }
          this.$alert('法人身份证信息未识别成功，请确认图片质量')
        }
      } else if (this.currentPicType.label == 'jbr_z') {
        this.jbrInfo.zPic = {
          fileName: data.fileName,
          status: 1,
          src: 'data:image/jpeg;base64,' + image,
          attachId:attachId
        }
      } else if (this.currentPicType.label == 'jbr_f') {
        if (data.peInfo && data.peInfo.name && data.peInfo.idCardNumber) {
          this.jbrInfo.fPic = {
            fileName: data.fileName,
            status: 1,
            src: 'data:image/jpeg;base64,' + image,
            idCardName: data.peInfo.name,
            idCardNo: data.peInfo.idCardNumber,
            idCardAddress: data.peInfo.address,
            attachId:attachId
          }
        } else {
          this.jbrInfo.fPic = {
            fileName: data.fileName,
            status: 2,
            src: 'data:image/jpeg;base64,' + image,
            attachId:attachId
          }
          this.$alert('经办人身份证信息未识别成功，请确认图片质量')
        }
      } else if (this.currentPicType.label == 'auth') {
        this.authInfo = {
          fileName: data.fileName,
          status: 1,
          src: 'data:image/jpeg;base64,' + image
        }
      } else {
        if (data.type == '3') {
          if (this.contractPage && this.contractPage.length > 0) {
            if (this.certInfo) {
              if(data.contractInfo.split('|')[2] != this.certInfo.contractNbr){
                 this.$alert(`您上传的第${data.contractInfo.split('|')[1]}页合同附件检查非当前合同，请检查后重新上传`)
                 return
              }
              this.$set(this.contractPage, parseInt(data.contractInfo.split('|')[1])-1, {
                // index: this.certInfo.pageIndex,
                index: parseInt(data.contractInfo.split('|')[1]),
                fileName: this.certInfo.fileName,
                status: 1,
                src: 'data:image/jpeg;base64,' + image,
                attachId:attachId
              })
            }
          }
        }
      }
    },
    async ocrProtocolNew(data,param) {
      this.ocrIdentifyFlg = true
      let attachId = await this.fileUpload(data.fileName,param);
      let image =  param && param.image ? param.image : this.certImg
      if (this.currentPicType.label == 'legal_z' || this.currentPicType.label == 'legal_f') {
        if(data.type == '4'){
          this.legalInfo.zPic = {
            fileName: data.fileName,
            status: 1,
            src: 'data:image/jpeg;base64,' + image,
            attachId:attachId
          }
        }
        if(data.type=='2'){
          if (data.peInfo && data.peInfo.name && data.peInfo.idCardNumber) {
            this.legalInfo.fPic = {
              fileName: data.fileName,
              status: 1,
              src: 'data:image/jpeg;base64,' + image,
              idCardName: data.peInfo.name,
              idCardNo: data.peInfo.idCardNumber,
              idCardAddress: data.peInfo.address,
              attachId:attachId
            }
          } else {
            this.legalInfo.fPic = {
              fileName: data.fileName,
              status: 2,
              src: 'data:image/jpeg;base64,' + image,
              attachId:attachId
            }
            this.$alert('法人身份证信息未识别成功，请确认图片质量')
          }
        }
      } else if (this.currentPicType.label == 'jbr_z' || this.currentPicType.label == 'jbr_f') {
        if(data.type == '4') {
          this.jbrInfo.zPic = {
            fileName: data.fileName,
            status: 1,
            src: 'data:image/jpeg;base64,' + image,
            attachId:attachId
          }
        }
        if(data.type == '2'){
          if (data.peInfo && data.peInfo.name && data.peInfo.idCardNumber) {
            this.jbrInfo.fPic = {
              fileName: data.fileName,
              status: 1,
              src: 'data:image/jpeg;base64,' + image,
              idCardName: data.peInfo.name,
              idCardNo: data.peInfo.idCardNumber,
              idCardAddress: data.peInfo.address,
              attachId:attachId
            }
          } else {
            this.jbrInfo.fPic = {
              fileName: data.fileName,
              status: 2,
              src: 'data:image/jpeg;base64,' + image,
              attachId:attachId
            }
            this.$alert('经办人身份证信息未识别成功，请确认图片质量')
          }
        }
      }
    },
    //企业/个人证照识别并验真（验真仅企业证件）
    identificOrcForCard(param) {
      let failMsg = '无法识别，请确认图片拍摄质量再重试，或者手动输入！'
      let url = `/xsb/personBusiness/groupArchives/h5UniversalOcrNew`
      this.$http.post(url, param).then(res => {
        let { retCode, retMsg, data } = res.data
        if (retCode == '0') {
            this.ocrProtocolNew(data,param)
        } else {
          this.$alert(failMsg)
        }
      }).catch(res => {
        this.$alert('证照识别并验真网络异常:' + res)
      })
    },
    //企业/个人证照识别并验真（验真仅企业证件）
    identificOrc(param) {
      param.isUseNewOcr = this.isUseNewOcr
      let failMsg = '无法识别，请确认图片拍摄质量再重试，或者手动输入！'
      let url = `/xsb/personBusiness/groupArchives/h5UniversalOcrNew`
      this.$http.post(url, param).then(res => {
        let { retCode, retMsg, data } = res.data
        if (retCode == '0') {
          if (this.ocrIdentifyFlg) {
            this.ocrProtocol(data,param)
          } else {
            this.initCardInfo(data,param)
          }
          if (this.flag) {

          } else {
            this.flag = true
          }
        } else {
          this.$alert(failMsg)
        }
      }).catch(res => {
        this.$alert('证照识别并验真网络异常:' + res)
      })
    },
    //企业/个人证照识别并验真（验真仅企业证件）
    identificMuchOrc(param) {
      param.isUseNewOcr = this.isUseNewOcr
      let failMsg = '无法识别，请确认图片拍摄质量再重试，或者手动输入！'
      let url = `/xsb/personBusiness/groupArchives/h5UniversalOcrNew`
      this.$http.post(url, param).then(res => {
        let { retCode, retMsg, data } = res.data
        if (retCode == '0') {
          if (this.ocrIdentifyFlg) {
            this.ocrProtocol(data,param)
          } else {
            this.initCardInfo(data,param)
          }
        } else {
          this.$alert(failMsg)
        }
      }).catch(res => {
        this.$alert('证照识别并验真网络异常:' + res)
      })
    },
    getProtocolToken(fileName) {
      let clientType = /android|harmony/gi.test(navigator.userAgent) ? 'ANDROID' : 'IOS'
      let param1 = {
        'opId': 'jkddmNew', //写死
        'regionId': this.uinfo.region,
        'prviId': '999',
        'operId': this.uinfo.staffId,
        'phoneNumber': this.uinfo.servNumber,
        'stationId': this.uinfo.stationId,
        'clientType': clientType
      }
      let url = `/xsb/api-user/tokenAuthen/h5genToken.do?opId=${param1.opId}&prviId=${param1.prviId}&clientType=${param1.clientType}`
      url += `&regionId=${this.uinfo.region}&stationId=${this.uinfo.stationId}&phoneNumber=${param1.phoneNumber}&operId=${param1.operId}`
       this.$http
        .get(url)
        .then((response) => {
          let data = response.data
          let token = data.data.parameter
          // let token = data.parameter
          console.log('token',token)
          if(token){
           this.geneneToken(token,fileName)
          }
        })
        .catch((response) => {
          this.$alert(response)
        })


    },
    geneneToken(token,fileName){
      let param = {
        userName: '1',
        servNumber: this.uinfo.servNumber,
        transactionId: '12019092513420010000001',
        outerToken:token.replaceAll("%2B", "+").replaceAll("%26", "&").replaceAll("%3D", "=")
          .replaceAll("%2F", "/").replaceAll("%20", " ")
      }
      let url = `/xsb/gridCenter/electronicProtocol/h5getToken`
      return this.$http.post(url, param).then(async res => {
        let { retCode, retMsg, data } = res.data
        if (retCode == '0' && data) {
          this.electronicToken = data
          this.getTokenCount++;
          this.certType = '3'
          this.queryProtocol()
          if(fileName){
            let attachId = await this.fileUpload(fileName);
            this.certInfo.attachId = attachId
            this.certInfo.fileName = fileName
          }
          return true
        } else if (~retMsg.indexOf('登录已失效') && this.getTokenCount < 3) {
          this.electronicToken = ''
          this.getProtocolToken(fileName)
        }else {
          this.$alert(retMsg || '获取电子协议Token失败')
          return false
        }
      }).catch(res => {
        this.$alert('获取电子协议Token网络异常:' + res)
        return false
      })
    },
    queryProtocol() {
      let param = {
        userName: '1',
        servNumber: '' ,//| this.uinfo.servNumber
        transactionId: '12019092513420010000001',
        electronicToken: this.electronicToken,
        queryType: 'handle',
        dealPhone: this.uinfo.servNumber,
        queryContent:this.certInfo.contractNbr
      }
      let url = `/xsb/gridCenter/electronicProtocol/h5queryWorkOrder`
      this.$http.post(url, param).then(res => {
        let { retCode, retMsg, data } = res.data
        if (retCode == '0') {
          if (data && data.rows && data.rows[0]) {
            this.certInfo.contractId = data.rows[0].objInstId
            //合同预览接口耗性能，换接口查询
            // this.getPreviewFile(data.rows[0])
            this.getContractBaseInfo(data.rows[0])
          }else{
            // this.failMsg = `根据您上传的合同附件,查无相关合同可以归档`
            // this.showInfo = ''
            this.queryProcessedProtocol(param)
          }
        } else {
          this.failMsg = `您刚才拍照的是合同附件,合同查询信息为空，请检查合同附件`
          this.$toast(retMsg)
          this.showInfo = ''
        }
      }).catch(res => {
        this.failMsg = `您刚才拍照的是合同附件,合同查询信息为空，请检查合同附件`
        this.$toast('证照识别并验真网络异常:' + res)
        this.showInfo = ''
      })
    },
    queryProcessedProtocol(param){
      param.queryType = 'processed'
      let url = `/xsb/gridCenter/electronicProtocol/h5queryWorkOrder`
      this.$http.post(url, param).then(res => {
        let { retCode, retMsg, data } = res.data
        if (retCode == '0') {
          if (data && data.rows && data.rows[0]) {
            this.searchOrder(data.rows[0].contractNbr, false, 30)
          }else{
            this.failMsg = `根据您上传的合同附件,查无相关合同可以归档`
            this.showInfo = ''
          }
        } else {
          this.failMsg = `您刚才拍照的是合同附件,合同查询信息为空，请检查合同附件`
          this.$toast(retMsg)
          this.showInfo = ''
        }
      }).catch(res => {
        this.failMsg = `您刚才拍照的是合同附件,合同查询信息为空，请检查合同附件`
        this.$toast('证照识别并验真网络异常:' + res)
        this.showInfo = ''
      })
    },
    //暂时不用，耗性能
    getPreviewFile(workData) {
      let param = {
        userName: '1',
        servNumber: this.uinfo.servNumber,
        transactionId: '12019092513420010000001',
        electronicToken: this.electronicToken,
        agreeNbr: this.certInfo.contractNbr
      }
      Indicator.open('获取合同信息...');
      let url = `/xsb/gridCenter/electronicProtocol/h5getPreviewFile`
      this.$http.post(url, param).then(res => {
        let { retCode, retMsg, data } = res.data
        if (retCode == '0') {
          this.certInfo.pageTotal = data.pageCount
          this.initProtocal(workData)
          Indicator.close();// 响应成功关闭loading
        } else {
          this.$alert(retMsg)
          Indicator.close();// 响应成功关闭loading
        }
      }).catch(res => {
        this.$alert('证照识别并验真网络异常:' + res)
        Indicator.close();// 响应成功关闭loading
      })
    },
    getContractBaseInfo(workData) {
      let param = {
        userName: '1',
        servNumber: this.uinfo.servNumber,
        transactionId: '12019092513420010000001',
        electronicToken: this.electronicToken,
        contractId: this.certInfo.contractNbr
      }
      Indicator.open('获取合同信息...');
      let url = `/xsb/gridCenter/electronicProtocol/h5getContractBaseInfo`
      this.$http.post(url, param).then(res => {
        let { retCode, retMsg, data } = res.data
        if (retCode == '0') {
          this.certInfo.pageTotal = data.pageCount
          this.initProtocal(workData)
          Indicator.close();// 响应成功关闭loading
        } else {
          this.$alert(retMsg)
          Indicator.close();// 响应成功关闭loading
        }
      }).catch(res => {
        this.$alert('证照识别并验真网络异常:' + res)
        Indicator.close();// 响应成功关闭loading
      })
    },
    goOrderList(){
      this.$router.push({
        path: '/jikeOrderList',
        query: {
          orderProceed: '2',
          beginDateVal: dateFormat(new Date(new Date().getTime() - 2 * 24 * 60 * 60 * 1000), 'yyyyMMdd'),
          endDateVal: dateFormat(new Date(new Date().getTime() + 24 * 60 * 60 * 1000), 'yyyyMMdd'),
          contractCode: this.certInfo.contractNbr,
          groupInfo:{}
        }
      })
    },
    auditPic(img) {
      let param = {
        imgBase64: img,
        unEncrpt: true,
        unLoadFlg: true, //关闭加载圈
      }
      let url = `/xsb/gridCenter/electronicProtocol/h5auditPic`
      this.$http.post(url, param).then(res => {
        let { retCode, retMsg, data } = res.data
        console.log('稽核数据：',data)
      }).catch(res => {
        this.$alert('证照识别并验真网络异常:' + res)
        console.log(res)
      })
    },
    //9300-合同盖章图片;6020-营业执照;6030-组织机构代码证; 6040-税务登记证;6120-法人有效证件-反面;6110-法人有效证件-正面;
    //6910-授权经办人有效证件-正面;6920-授权经办人有效证件-反面;6400-委托函附件
    fileUpload(fileName,fileData) {
      let param = {
        userName: '1',
        servNumber: this.uinfo.servNumber,
        transactionId: '12019092513420010000001',
        electronicToken: this.electronicToken,
        fileName: fileName,
        fileContent: fileData && fileData.image ? fileData.image : this.certImg,
        actionType: this.currentPicType.id,
        unEncrpt: true,
        unLoadFlg: true, //关闭加载圈
      }
      let url = `/xsb/gridCenter/electronicProtocol/h5fileUpload`
      return this.$http.post(url, param).then(res => {
        let { retCode, retMsg, data } = res.data
        if (retCode == '0') {
          return data.attachId
        } else {
          this.$alert(retMsg)
          this.fault = ''
        }
        return ''
      }).catch(res => {
        this.$alert('证照识别并验真网络异常:' + res)
        return ''
      })
    },
    //9300-合同盖章图片;6020-营业执照;6030-组织机构代码证; 6040-税务登记证;6120-法人有效证件-反面;6110-法人有效证件-正面;
    //6910-授权经办人有效证件-正面;6920-授权经办人有效证件-反面;6400-委托函附件
    putInFile() {
      let addAttchIds = []
      let continueFlag = true
      for (let i = 0; i < this.contractPage.length; i++) {
        if (!this.contractPage[i].attachId) {
          this.$alert(`合同附件第${i + 1}页未上传照片，请确认并上传`)
          continueFlag = false
          break
        } else {
          addAttchIds.push(this.contractPage[i].attachId)
        }
      }
      if (!continueFlag) {
        return
      }
      if (!this.legalInfo.zPic.attachId && !this.legalInfo.fPic.attachId) {
        if (!this.jbrInfo.zPic.attachId && !this.jbrInfo.fPic.attachId) {
          this.$alert(`请上传法人正反照或经办人正反照片`)
          return
        } else if (!this.jbrInfo.zPic.attachId && this.jbrInfo.fPic.attachId) {
          this.$alert(`请上传经办人正照片`)
          return
        } else if (this.jbrInfo.zPic.attachId && !this.jbrInfo.fPic.attachId) {
          this.$alert(`请上传经办人反面照片`)
          return
        } else if (!this.authInfo.attachId) {
          this.$alert(`请上传授权委托书照片`)
          return
        }
      } else if (!this.legalInfo.zPic.attachId && this.legalInfo.fPic.attachId) {
        this.$alert(`请上传法人正面照片`)
        return
      } else if (this.legalInfo.zPic.attachId && !this.legalInfo.fPic.attachId) {
        this.$alert(`请上传法人反面照片`)
        return
      }
      if(this.legalInfo.zPic.attachId){
        addAttchIds.push(this.legalInfo.zPic.attachId)
      }
      if(this.legalInfo.fPic.attachId){
        addAttchIds.push(this.legalInfo.fPic.attachId)
      }
      if(this.jbrInfo.zPic.attachId){
        addAttchIds.push(this.jbrInfo.zPic.attachId)
      }
      if(this.jbrInfo.fPic.attachId){
        addAttchIds.push(this.jbrInfo.fPic.attachId)
      }
      if(this.authInfo.attachId){
        addAttchIds.push(this.authInfo.attachId)
      }
      let param = {
        userName: '1',
        servNumber: this.uinfo.servNumber,
        transactionId: '12019092513420010000001',
        electronicToken: this.electronicToken,
        addAttchIds: addAttchIds.join(','),
        agreeId: this.certInfo.contractId,
        agreeName: this.certInfo.busyName,
        agreeNbr: this.certInfo.contractNbr,
        agreeType: '1000',
        execId: this.certInfo.execId,
        expProcMethod: '1000',
        isSendOrder: 2,
        orderId: this.certInfo.orderId,
        prodNames: this.certInfo.prodNames,
        signDate: dateFormat(new Date(),"yyyy-MM-dd hh:mm:ss")
      }
      let url = `/xsb/gridCenter/electronicProtocol/h5putInFile`
      this.$http.post(url, param).then(res => {
        let { retCode, retMsg, data } = res.data
        if (retCode == '0') {
          setTimeout(() => {
            this.searchOrder(this.certInfo.contractNbr, true, 2)
          }, 500);
        } else {
          this.$alert(retMsg)
        }
        return ''
      }).catch(res => {
        this.$alert('证照识别并验真网络异常:' + res)
        return ''
      })
    },
    searchOrder(contractNbr, flag, beginDate) {
      let beginDateVal = dateFormat( new Date(new Date().getTime() - beginDate * 24 * 60 * 60 * 1000), 'yyyyMMdd')
      let endDateVal = dateFormat(new Date(new Date().getTime()  + 24 * 60 * 60 * 1000), 'yyyyMMdd')
      let param = {
        'beginTime': beginDateVal,
        'endTime': endDateVal,
        'pageIndex': '1',
        'pageSize': '2',
        'searchType': '3',
        'contractCode': contractNbr,
        'sharaCrmId': Storage.session.get('userInfo').crmId
      }
      let url = `/xsb/personBusiness/jikeOrder/h5qryJikeOrderDetail`
      this.$http.post(url, param).then((res) => {
        let { retCode, retMsg, data } = res.data
        if (retCode == '0') {
          //没有返回数据
          if (!data.orderDetailInfoList || data.orderDetailInfoList.length < 1) {
            this.step = '3'
            if(flag){
              Indicator.open('订单查询中...');
              setTimeout(() => {
                this.searchOrder(contractNbr, false, 2)
              }, 2000);
            }
          } else {
            this.cardData = data.orderDetailInfoList[0]
            if (data.orderDetailInfoList.length > 1) {
              this.cardData.busiTypeName = data.orderDetailInfoList[1].busiTypeName
            }
            this.getZeroMenuSharePic(this.cardData)
            this.generateQrcode(this.cardData.oneId)
            this.step = '3'
            if(beginDate == 30){
              this.contractTip = '合同已完成归档'
            }
            this.showShareOrder = true
          }
        } else {
          this.$toast(retMsg || '二维码订单信息查询失败')
        }
      }).catch((response) => {
        this.$toast('二维码订单信息网络异常，请重试')
      })
    },
    chooseGroup(item) {
      this.showGroup = !this.showGroup
      if(!item){
        return
      }
      let param = {
        'area': this.srcFrom,
        'privId': 'z-' + '100374',
        'stationId': this.uinfo.stationId,
        unLoadFlg: true //屏蔽加载圈
      }
      this.$http.post('/xsb/api-user/commonFunc/h5commCollect', param)
      if (item.groupId) {
        // 记录菜单链
        let workId = '';
        let url = `/xsb/personBusiness/villageMarket/h5qryWorkIdForSearchJt?villageId=${item.groupId}`;
        this.$http.get(url).then((res) => {
          if(res.data.retCode == '0'){
            workId = res.data.data;
          }
          this.$router.push({
            path: `/groupDetailNew`,
            query: {
              workIdFlag:true,
              taskId:workId,
              flag:'0',
              groupId:item.custId,
              whereFrom:'workStageNew',
              // 菜单链默认父节点级别
              dataRootLevel: 2
            }
          });
        }).catch((response) => {
        });
      }
    },
    async getZeroMenuList() {
      let data = await h5getMenuByLevel(this.uinfo.stationId, `12:0`, false, `unLoadFlg=true`);
      if (data.retCode == '0') {
        this.menuList = data.data;
        let idCardMenu = this.menuList.filter(items => {
          return items.privId == '20000152'
        })
        let groupCardMenu = this.menuList.filter(items => {
          return items.privId == '20000153'
        })

        this.idCardMenuList = idCardMenu[0].itemList
        this.groupCardMenuList = groupCardMenu[0].itemList
      } else {
        this.menuList = []
        this.$alert(data.retMsg || '获取菜单错误，请确认岗位')
      }
      // let url = `/xsb/api-user/menu/h5getMenuByLevel?stationId=${this.uinfo.stationId}&level=12:0&unLoadFlg=true`
      // this.$http
      //   .get(url)
      //   .then((res) => {
      //     let { retCode, retMsg, data } = res.data
      //     if (retCode == '0') {
      //       this.menuList = data
      //       let idCardMenu = this.menuList.filter(items => {
      //         return items.privId == '20000152'
      //       })
      //       let groupCardMenu = this.menuList.filter(items => {
      //         return items.privId == '20000153'
      //       })
      //
      //       this.idCardMenuList = idCardMenu[0].itemList
      //       this.groupCardMenuList = groupCardMenu[0].itemList
      //     } else {
      //       this.menuList = []
      //       this.$alert(retMsg || '获取菜单错误，请确认岗位')
      //     }
      //   })
      //   .catch((response) => {
      //   })
    },
    generateQrcode(oneId) {
      const qrcode = new QRCode(document.getElementById('qrcode'), {
        width: 40,
        height: 40,
        correctLevel: QRCode.CorrectLevel.L
      })
      qrcode.makeCode(oneId)
    },
    getZeroMenuSharePic(orderInfo){
      let param = {
        'oneId': orderInfo.oneId,
        'custName': orderInfo.custName,
        'custId': orderInfo.custId,
        'busiTypeName': orderInfo.busiTypeName,
        'orderId': orderInfo.orderId,
        'createTime': orderInfo.createTime,
        'unEncrpt': true
      }
      let url = `/xsb/ability/zeroMenuShare/getZeroMenuSharePic`
      this.$http.post(url, param).then((res) => {
        let { retCode, retMsg, data } = res.data
        if (retCode == '0') {
          //没有返回数据
          this.codePic = data
        } else {
          this.$toast(retMsg || '二维码订单信息查询失败')
        }
      }).catch((response) => {
        this.$toast('二维码订单信息网络异常，请重试')
      })
    },
    downLoadPic() {
      if(!this.codePic){
        this.$toast('订单二维码分享图正在生成中，请稍等')
        setTimeout(()=>{
          this.downLoadPic()
        },1000);
        return
      }
      let paramStr = { 'type': '1', 'param': this.codePic }
      ClientJs.savePhotoWithBase64Image(JSON.stringify(paramStr))
    },
    openWeiXin() {
      if(!this.codePic){
        this.$toast('订单二维码分享图正在生成中，请稍等')
        setTimeout(()=>{
          this.openWeiXin()
        },1000);
        return
      }
      ClientJs.copyImageShareWeChat(this.codePic)
    },
    getNewOcrFlag(){
      let param = {
        busiName: 'zero-ocr_flag',
        busiKey: 'accessKey',
        defaultRegionValue: '',
        unLoadFlg: true//不展示加载圈
      }
      let url = `/xsb/gridCenter/groupBroadBand/h5getBusiTypeZkFlag`
      return this.$http.post(url, param).then(async res => {
        let { retCode } = res.data
        if (retCode == '0') {
          this.isUseNewOcr = '1'
        }else{
          this.isUseNewOcr = '2'
        }
      })
    },
  },
  created(){
    this.uinfo = Storage.session.get('userInfo')
    this.getNewOcrFlag()
    if(this.showPicture){
      this.openOrcIdentify()
    }
    this.getZeroMenuList()
  },
}

</script>


<style scoped lang='less'>

.menu-box {
  position: fixed;
  left: 20px;
  bottom: 180px;
  width: 40px;
  height: 40px;
  z-index: 1001;

  > img {
    position: absolute;
  }
}

.inlet-img {
  left: 0;
  bottom: 0;
  width: 40px;
  height: 40px;
  z-index: 999;

}

.inlet_close {
  left: 0;
  bottom: 0;
  width: 40px;
  height: 40px;
}

.inlet_voice {
  left: -14px;
  bottom: -18px;
  width: 68px;
  height: 68px;
  animation: moveIcon2 .3s ease-in-out forwards;
}

.inlet_photo {
  left: -14px;
  bottom: -18px;
  width: 68px;
  height: 68px;
  animation: moveIcon .3s ease-in-out forwards;
}

.inlet-menu {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, .2);
  z-index: 1000;
}

@keyframes moveIcon {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(70px, 0);
  }
}

@keyframes moveIcon2 {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(28px, -74px);
  }
}

.ocr-identify {
  position: fixed;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  z-index: 1002;
  background-color: rgba(0, 0, 0, .2);
  display: flex;
  align-items: center;
  justify-content: center
}

.id-box {
  width: calc(100% - 58px);
  background: #E8E9F2 url("../../../../static/img/ZeroMenu/id-bg.png") no-repeat;
  background-size: 100% 100%;
  border-radius: 12px 12px 12px 12px;
  position: relative;

  .id-top-box {
    position: relative;
    height: 172px;
    width: 100%;

    .id-logo {
      width: 173.5px;
      height: 162px;
      position: absolute;
      left: 0;
      top: -20px;
    }

    .license-logo {
      width: 162px;
      height: 151px;
      position: absolute;
      left: -20px;
      top: -30px;
    }

    .contract-logo {
      width: 154.82px;
      height: 144.56px;
      position: absolute;
      left: 0px;
      top: -22px;
    }

    .id-img {
      span{
        font-size: 83%;
        position: relative;
        font-weight: bolder;
        &:nth-child(1) {
          top: -11%;
          right: 2px;
        }
        &:nth-child(2) {
          top: 2%;
          left: 2px;
        }
      }

      height: 36.23px;
      position: absolute;
      top: 22px;
      color: #0c63c2;
      font-weight: 600;
      font-size: 2rem;
      left: 107px;
      line-height: 24px;
    }

    .id-message {
      position: absolute;
      left: 27px;
      right: 33px;
      bottom: 28px;
      height: 70px;
      background: #FFFFFF;
      border-radius: 8px 8px 8px 8px;
      display: flex;
      align-items: center;

      img {
        width: 40px;
        height: 36px;
        margin-right: 12px;
        margin-left: 14px;
      }

      p {
        font-size: 14px;
        color: #4F4F4F;
        line-height: 19px;
        margin-right: 9px;
      }
    }
  }

  .id-bottom-box {
    background-color: #fff;
    overflow: hidden;
    border-radius: 8px 8px 12px 12px;
    padding-bottom: 12px;

    .info {
      display: flex;
      align-items: center;
      background: #E4F1FF;
      padding: 15px 0 13px 26px;

      span {
        font-size: 18px;
        color: #2E5586;
        font-weight: bold;
        &.sousuo{
          font-size: 18px;
          color: #26a2ff;
          font-weight: bold;
          position: relative;
          margin-left: 6px;
        }
      }

      .info-num {
        flex: 1;
        margin-left: 17px;
        word-break: break-all;
        margin-right: 6px;
      }
    }

    .license-info {
      flex-direction: column;
      align-items: flex-start;

      > div {
        font-size: 18px;
        color: #2E5586;
        line-height: 19px;
        margin-bottom: 4px;
        font-weight: bold;
      }

      > p {
        font-size: 16px;
        color: #456EA2;
        line-height: 19px;
      }
    }

    > p {
      font-size: 16px;
      color: #444444;
      line-height: 19px;
      margin: 12px 0 12px 26px;
    }

    .tip {
      margin: 18px 0 14px 26px;
    }

    .btns {
      display: flex;
      padding: 5px 27px;

      > div {
        flex: 1;
        height: 36px;
        background: #007AFF;
        border-radius: 8px 8px 8px 8px;
        font-size: 14px;
        color: #FFFFFF;
        line-height: 36px;
        text-align: center;

        &:nth-child(2) {
          margin-left: 8px;
        }
        &:nth-child(3) {
          margin-left: 8px;
        }
      }
    }
    .shangzhankai{
      left: 50%;
      transform: translateX(-50%);
      font-size: 30px;
      position: absolute;
    }
    .more-business {
      text-align: center;
      font-size: 14px;
      color: #007AFF;
      line-height: 24px;
      height: 20px;
      padding-top: 6px;

      span {
        position: relative;

        &::before {
          position: absolute;
          width: 5px;
          height: 9px;
          background: url("../../../../static/img/ZeroMenu/right-icon.png") no-repeat;
          background-size: 100% 100%;
          content: '';
          right: -7px;
          top: 50%;
          transform: translateY(-50%);
        }
      }


    }
  }


}

.license-img-box {
  width: calc(100% - 58px);

  .title-box {
    width: 100%;
    height: 84px;
    background: linear-gradient(to bottom, transparent 0%, transparent 95%, #fff 96%, #FFFFFF 100%);
    position: relative;
  }

  .title {
    position: absolute;
    width: 100%;
    background: transparent url(../../../../static/img/ZeroMenu/title-bg.png) 0px -11px no-repeat;
    height: 100%;
    top: 0;
    left: 0;
    background-size: 105% 94px;
    padding: 10px 3% 9px 17px;
    box-sizing: border-box;

    > div {
      display: flex;
      font-size: 17px;
      color: #2E5586;
      line-height: 19px;
      font-weight: bold;

      img {
        width: 18px;
        height: 16px;
        margin-right: 3px;
      }
    }

    > p {
      font-size: 16px;
      color: #456EA2;
      line-height: 19px;
    }

    .name {
      margin: 5px 0 3px;
    }
  }

  .content {
    width: 100%;
    height: 290px;
    overflow-y: auto;
    background-color: #fff;
    display: flex;
    flex-wrap: wrap;
    padding: 3px 0 10px;


    li {
      width: 22%;
      margin-left: 8.5%;
      margin-top: 15px;
      position: relative;

      .shanchu {
        position: absolute;
        color: #6c6c6c;
        font-size: 14px;
        margin-bottom: -6px;
        z-index: 1;
        position: relative;
        float: right;
      }

      > div {
        width: 100%;
        height: 109px;
        background: #E8E9F2;
        border-radius: 8px 8px 8px 8px;
        padding: 5px 6px 11px 6px;
        box-sizing: border-box;
        position: relative;

        img {
          width: 100%;
          height: 100%;
          border-radius: 4px;
        }

        .blurring {
          position: absolute;
          background: rgba(255, 222, 222, 0.6);
          width: 100%;
          height: 100%;
          top: 0;
          left: 0;
          display: flex;
          align-items: center;
          justify-content: center;

          p {
            width: 46px;
            height: 19px;
            background: #FFFFFF;
            border-radius: 4px 4px 4px 4px;
            font-size: 12px;
            color: #D62929;
            line-height: 19px;
            text-align: center;
          }
        }

        .unLoad {
          position: absolute;
          background: #E8E9F2;
          border-radius: 8px 8px 8px 8px;
          width: 100%;
          height: 100%;
          top: 0;
          left: 0;
          display: flex;
          align-items: center;
          justify-content: center;

          p {
            font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
            font-style: italic;
            font-weight: 400;
            font-size: 18px;
            color: #C4C7DE;
            line-height: 16px;
            text-align: left;
            font-style: normal;
            text-transform: none;
          }
        }
      }

      > p {
        font-size: 12px;
        color: #3D3D3D;
        line-height: 16px;
        text-align: center;
        margin-top: 5px;
      }
    }
  }

  .btns {
    display: flex;
    background: #E8E9F2;
    height: 59px;
    border-radius: 0 0 8px 8px;
    padding: 11px 22px;
    box-sizing: border-box;

    > div {
      display: flex;
      height: 34px;
      background: #F6FAFF;
      border-radius: 8px 8px 8px 8px;
      border: 1px solid #007AFF;
      font-size: 14px;
      color: #007AFF;
      align-items: center;
      justify-content: center;
      flex: 1;


      img {
        width: 13px;
        margin-right: 5px;
      }

      &:nth-child(2) {
        margin-left: 14px;
      }
    }

    .upload {
      background: #007AFF;
      color: #FFFFFF;
    }
  }
}

.success-box {
  width: calc(100% - 36px);
  height: auto;
  background: #FFFFFF;
  border-radius: 6px 6px 6px 6px;
  display: flex;
  flex-direction: column;
  align-items: center;

  .success {
    width: 76.45px;
    height: 73.47px;
    margin-top: 9px;
  }

  > p {
    font-size: 19px;
    color: #315386;
    line-height: 32px;
    font-weight: bold;
  }
  >span{
    margin-top: 2px;
    font-size: 13px;
    color: #a7a7a7;
  }

  >div{
    display: flex;
    width: 100%;
    justify-content: center;

    > div {
      width: 34%;
      height: 32px;
      background: #007AFF;
      border-radius: 6px 6px 6px 6px;
      font-size: 13px;
      color: #FFFFFF;
      line-height: 32px;
      text-align: center;
      margin-top: 14px;
      margin: 14px 15px 10px;
      border-radius: 20px;
      font-weight: 500;
      span{
        vertical-align: bottom;
        margin-right: 6px;
      }
    }
  }
  >em{
    position: absolute;
    margin-top: 12px;
    right: 27px;
    width: 42px;
  }

}

.close {
  width: 20px;
  position: absolute;
  bottom: -40px;
  left: 50%;
  transform: translateX(-50%);
}
.closeProtol{
  width: 20px;
  position: relative;
  margin-top: 10px;
  left: 50%;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
}
.closeSuccess{
  width: 20px;
  position: absolute;
  margin-top: 193px;
}
.dynamic-text {
  line-height: 1.5;
  text-align: center;
  padding: 10px;
}
</style>
