<template>
    <div>
        <div class="csViewIn" v-show="currentStep=='step1'">
            <div class="cs-header">
                <div class="cs-htel">客户视图</div>
                <!-- <div class="cs-hname">
                    <span class="cs-hn-txt">{{cusViewInfo.name|starName}}</span>
                    <span class="cs-hn-tag">{{userState}}</span>
                     <span class="cs-hn-tag-newcustom" @click="newCustomQry()"><i class="iconfont sousuo1 sou-suo"></i>新客户查询</span>
                    <span class="cs-label-tag" @click="openPop" v-show="!(labelList==null||labelList.length==0)">客户标签</span>
                </div> -->

                <!-- <div class="cs-hlocal" v-show="cusViewInfo.hasBroadBand == '1'">{{cusViewInfo.bandAddr}}</div> -->

                <div class="refresh-btn-new" :class="{'grey-btn':btnTxt!='刷新'}" @click="refreshPage"><i class="iconfont exchange freshbtn"></i>&nbsp;{{btnTxt}}</div>
                <div class="cs-btn-close" @click="goWorkstage">
                    <span class="iconfont shanchu iconclose"></span>
                </div>
            </div>

            <div class="cs-custominfo-date">
              <div class="cs-custominfo">
                <div class="cs-custominfo-detail">
                    <div class="cs-custominfo-telnum">{{telnum|starTel}}
                    <div class="cs-custominfo-an" v-show="restAssured">安</div>
                    </div>
                       <img src="static/img/csview-line.png"  class="bottom-line">
                        <div class="cs-userdetail">
                        <span class="cs-username">{{cusViewInfo.name|starName}}</span>
                        <span class="cs-hn-tag-new">{{userState}}</span>
                        <span class="cs-hn-tag-new" v-show="!(labelList==null||labelList.length==0)">客户标签</span>
                        </div>
                    </div>

                <div class="cs-cstominfo-qry">
                    <div class="new-custom-box" @click="newCustomQry()">
                    <img src="static/img/new-custom-qry.png" class="new-custom-label">
                    <span class="new-custom-txt">新客户查询</span>
                    </div>
                </div>
              </div>
              <div v-show='applyDate || payMethod' class="dotted-line"></div>
              <div v-show='applyDate' class="applyDate-line">入网时间: {{applyDate |checkApplyDate}} </div>
              <div v-show='payMethod' class="applyDate-line">付费方式: {{payMethod}} </div>
            </div>

            <div class="cs-meal">
                <div class="cs-mcard" :style="{backgroundImage:'url(static/img/mealbg.jpg)'}">
                    <div class="cs-mc-name">{{mainOffer&&mainOffer.offerName}}</div>
                    <div class="cs-mc-subprod" v-for="(item,index) in orderMProdList" :key="index" v-show="orderMProdList && item.status==1">资费：{{item.offername}}</div>
                    <div class="cs-mc-info">
                        <div class="cs-mi-lis">
                            <span class="cs-ml-val">{{cusViewInfo.balance ? cusViewInfo.balance : '--'}}<span
                                v-show="cusViewInfo.balance">元</span></span>
                            <span class="cs-ml-key">账户余额</span>
                        </div>
                        <div class="cs-mi-lis">
                            <span class="cs-ml-val">{{cusViewInfo.charge ? cusViewInfo.charge : '--'}}<span
                                v-show="cusViewInfo.charge">元</span></span>
                            <span class="cs-ml-key">账单</span>
                        </div>
                        <div class="cs-mi-lis">
                            <span class="cs-ml-val">{{cusViewInfo.remainingVal ? cusViewInfo.remainingVal : '--'}}</span>
                            <span class="cs-ml-key">剩余流量</span>
                        </div>
                        <div class="cs-mi-lis">
                            <span class="cs-ml-val">{{cusViewInfo.integration ? cusViewInfo.integration : '--'}}</span>
                            <span class="cs-ml-key">积分</span>
                        </div>
                    </div>
                    <span class="cs-mc-consume" @click="goHistory">历史账单</span>
                </div>
            </div>

            <!-- 客户三看 -->
            <div class='cs-all-views'>
                <div class="cs-all-views-title">
                  <img class='title-img' src='static/img/custom-3side.png'>
                  <span>客户三看</span>
                </div>
                <!-- 看饱和 -->
                <div class="cs-all-views-content">
                  <div class='content-title'>
                      <img class='content-title-img' src="static/img/full.png">
                      <span class='content-title-txt'>看饱和</span>
                    </div>
                  <div class='content-chart-data'>
                    <div class='data-item'>
                      <div class='charts' id='flux-charts'></div>
                      <div class='item-title'>流量<br/>饱和度</div>
                    </div>
                    <div class='data-item'>
                      <div class='charts' id='voice-charts'></div>
                      <div class='item-title'>语音<br/>饱和度</div>
                    </div>
                  </div>
                </div>
                <div class='div-line'></div>
                <!-- 看融合 -->
                <div class="cs-all-views-content">
                  <div class='content-title'>
                    <img class='content-title-img' src="static/img/combine.png">
                    <span class='content-title-txt'>看融合</span>
                  </div>
                  <div class='content-data'>
                    <div class='data-item'>
                      <span class='data-item-val' :class="[{'red':customerThreeSide.s000000095331=='否'}]">{{customerThreeSide.s000000095331}}</span>
                      <span class='data-item-key'>融合</span>
                    </div>
                    <div class='data-item'>
                      <span class='data-item-val' :class="[{'red':customerThreeSide.s000000038091=='否'}]">{{customerThreeSide.s000000038091}}</span>
                      <span class='data-item-key'>固移融合</span>
                    </div>
                    <div class='data-item'>
                      <span class='data-item-val' :class="[{'red':customerThreeSide.s000000094131=='否'}]">{{customerThreeSide.s000000094131}}</span>
                      <span class='data-item-key'>合约类</span>
                    </div>
                    <div class='data-item'>
                      <span class='data-item-val' :class="[{'red':customerThreeSide.s000000095321=='否'}]">{{customerThreeSide.s000000095321}}</span>
                      <span class='data-item-key'>成员融合</span>
                    </div>
                  </div>
                </div>
                <div class='div-line'></div>
                <!-- 看卡槽 -->
                <div class="cs-all-views-content">
                  <div class='content-title'>
                    <img class='content-title-img' src="static/img/card-slot.png">
                    <span class='content-title-txt'>看卡槽</span>
                  </div>
                  <div class='content-data'>
                    <div class='data-item'>
                      <span class='data-item-val' :class="[{'red':customerThreeSide.s000000096121=='否'}]">{{customerThreeSide.s000000096121}}</span>
                    </div>
                  </div>
                </div>

            </div>


            <div class="cs-vinfo">

                <!--待生效主体套餐-->
                <div class="cs-vinfo-lis" v-show="notActiveMainTariffFlag">
                    <CsViewTitle title="待生效主体套餐"></CsViewTitle>
                    <ul class="cs-main-tariff">
                        <li class="cs-mtariff-li" v-for="(item,index) in orderMProdList" :key="index" v-show="orderMProdList && item.status==0">
                            <div class="cs-mtariff-item">
                                <span class="cs-mtariff-label">主体:</span>
                                <span class="cs-mtariff-text">{{item.zhutiname}}</span>
                            </div>
                            <div class="cs-mtariff-item">
                                <span class="cs-mtariff-label">资费:</span>
                                <span class="cs-mtariff-text">{{item.offername}}</span>
                            </div>
                            <div class="cs-mtariff-item">
                                <span class="cs-mtariff-label">有效期:</span>
                                <span class="cs-mtariff-text">{{item.effectdate}}至{{item.expiredate}}</span>
                            </div>
                        </li>
                    </ul>
                </div>

                <!--账本-->
                <div class="cs-vinfo-lis">
                    <CsViewTitle title="账本明细"></CsViewTitle>
                    <div class="cs-vl-info">
                        <div class="cs-vli-lis">
                            <span
                                class="cs-vll-val">{{accountBookDetail.cashBook ? accountBookDetail.cashBook : '--'}}元</span>
                            <span class="cs-vll-key">现金账本</span>
                        </div>
                        <div class="cs-vli-lis">
                            <span
                                class="cs-vll-val">{{accountBookDetail.prepaidCardBook ? accountBookDetail.prepaidCardBook : '--'}}元</span>
                            <span class="cs-vll-key">充值卡账本</span>
                        </div>
                        <div class="cs-vli-lis">
                            <span
                                class="cs-vll-val">{{accountBookDetail.marketCashBook ? accountBookDetail.marketCashBook : '--'}}元</span>
                            <span class="cs-vll-key">营销案账本</span>
                        </div>
                        <div class="cs-vli-lis">
                            <span
                                class="cs-vll-val">{{accountBookDetail.discountBook ? accountBookDetail.discountBook : '--'}}元</span>
                            <span class="cs-vll-key">优惠账本</span>
                        </div>
                    </div>

                </div>

                <!--宽带有线专区-->
                <div class="cs-vinfo-lis" v-show="cusViewInfo.hasBroadBand == '1'">
                    <CsViewTitle title="宽带有线专区" v-show="cusViewInfo.prodBrandList && cusViewInfo.prodBrandList.length >0"></CsViewTitle>

                    <ul class="cs-kd-info">
                        <li class="cs-kdi-li" v-for="(item,index) in cusViewInfo.prodBrandList" :key="index">
                            <div class="cs-kdil-left">
                                <div class="cs-kdl-txt">
                                    <span class="iconfont Group- iconkd"></span>
                                    <span class="cs-kdl-title">{{item.prodname}}</span>
                                </div>
                                <div class="cs-kdl-name">宽带产品</div>
                            </div>
                            <!--0表示没到期，1表示即将到期,-1表示过期-->
                            <div class="cs-kdil-rig">
                                <span class="cs-kr-val">{{item.enddate}}</span>
                                <span class="cs-kr-key">
                                    <span class="cs-kk-timeout" v-show="item.isDue == '1'">即将到期</span>
                                    <span v-show="item.isDue != '1'">到期时间</span>
                                </span>
                            </div>
                        </li>

                        <li class="cs-kdi-li" v-for="(item,index) in cusViewInfo.prodNetList" :key="index">
                            <div class="cs-kdil-left">
                                <div class="cs-kdl-txt">
                                    <span class="iconfont dianshiji iconkd"></span>
                                    <span class="cs-kdl-title">{{item.prodname}}</span>
                                </div>
                                <div class="cs-kdl-name">电视产品</div>
                            </div>
                            <div class="cs-kdil-rig">
                                <span class="cs-kr-val">{{item.enddate}}</span>
                                <span class="cs-kr-key" >
                                    <span class="cs-kk-timeout" v-show="item.isDue == '1'">即将到期</span>
                                    <span v-show="item.isDue != '1'">到期时间</span>
                                </span>
                            </div>
                        </li>

                    </ul>
                </div>

                <!--流量专区-->
                <div class="cs-vinfo-lis">
                    <CsViewTitle title="流量专区"></CsViewTitle>
                    <div class="cs-vl-info">
                        <div class="cs-vli-lis">
                            <span class="cs-vll-val">{{commonRemainingVal}}</span>
                            <span class="cs-vll-key">通用流量剩余</span>
                        </div>
                        <div class="cs-vli-lis">
                            <span class="cs-vll-val">{{usedVal}}</span>
                            <span class="cs-vll-key">当前消费</span>
                        </div>
                      <div class="cs-vli-lis" @click="clickLiuLiang">
                        <div class="cs-vli-btn">流量明细</div>
                      </div>
                    </div>
                </div>

                <!--家庭专区-->
                <div class="cs-vinfo-lis" v-show="cusViewInfo.hasfmyprod == '1'">
                    <CsViewTitle title="家庭专区" v-show="cusViewInfo.familyMemberInfoList && cusViewInfo.familyMemberInfoList.length>0"></CsViewTitle>
                    <div class="cs-kd-title">{{familyProdName}}</div>
                    <ul class="cs-jt-info">
                        <li class="cs-fam-li" v-for="fitem in cusViewInfo.familyMemberInfoList" :key="fitem.shortnum">
                            <div class="cs-fl-left">{{fitem.servnumber | starTel}}</div>
                            <div class="cs-fl-hx" v-show="fitem.isKey == '1'">核心</div>
                            <div class="cs-fl-state">
                                <span>{{fitem.paytype == '1'?'主号代付':'不代付'}}</span>
                            </div>
                        </li>

                    </ul>
                </div>

                <!--副卡专区-->
                <div class="cs-vinfo-lis" v-show="secondaryCardNum > 0">
                    <CsViewTitle title="副卡专区" v-show="secondData && secondData.length > 0"></CsViewTitle>
                    <ul class="cs-jt-info">
                        <li class="cs-fam-li" v-for="(item,index) in secondData" :key="index">
                            <div class="cs-fl-left">{{item.propvalue | starTel}}</div>
                        </li>
                    </ul>
                </div>

                <!--终端精准营销-->
                <TerminalPrecisionMarketing v-if="telnum" @overnewCustomQry="overnewCustomQry" :telnum="telnum"/>


                <!--推荐话术-->
                <RecommendedScript v-if="axiosNum == 4" :dataStr="dataStr" />
            </div>

          <div class="cs-vinfo-lis">
            <CsViewTitle title="集团专区"></CsViewTitle>
            <div class='cs-group'>
              <span class='cs-group-text' v-show="groupBelongInf.groupName">V网归属：{{groupBelongInf.groupName}}({{groupBelongInf.groupId}})</span>
            </div>
            <div class='cs-group'>
              <span class='cs-group-text' v-show="paymode">V网统付：{{paymode}}，{{paymodeTime}}</span>
<!--              <span class='cs-group-text'>V网统付：-</span>-->
            </div>
            <div class='cs-group'>
              <span class='cs-group-text'>话费统付：{{paymentrela}}</span>
<!--              <span class='cs-group-text'>话费统付：-</span>-->
            </div>
          </div>
            <!-- banner-->
            <div class="cs-banner">
                <div class="cs-bannernei">
                    <slide ref="slide"
                        :autoPlay="true"
                        :loop="true"
                        :showDot="true"
                        :dataList="bannerData"
                        :interval="4000">
                        <div v-for="(item,index) in bannerData"
                            @click="bannerCk(item)"
                            :key="index" style="position: relative;">
                            <a href="javascript:;" style="border-radius: 8px;">
                                <img :src="item.src"/>
                            </a>
                            <span class="banner-cs-title">{{item.activeName}}</span>
                        </div>
                    </slide>
                </div>
            </div>

            <div class="cs-businessarea">
                <BusinessAreaInner
                    :tsBSList="bAList"
                    @emBusnCk="businessCkPre"
                ></BusinessAreaInner>
            </div>

            <!--滚动显示的块-->
            <div class="cs-scroll-show wrapper-medias">
                <div class="cs-header">
                    <div class="cs-htel">{{telnum|starTel}}
                        <span class="refresh-btn" :class="{'grey-btn':btnTxt!='刷新'}" @click="refreshPage"><i class="iconfont exchange"></i>&nbsp;{{btnTxt}}</span>
                    </div>
                    <div class="cs-hname">
                        <span class="cs-hn-txt">{{cusViewInfo.name|starName}}</span>
                        <span class="cs-hn-tag">{{userState}}</span>
                    </div>
                    <div class="cs-btn-close" @click="goWorkstage">
                        <span class="iconfont shanchu iconclose"></span>
                    </div>
                </div>
            </div>

            <!--banner 弹框提示-->
            <div class="banner-filter" v-show="bannerPopFlag">
                <div class="cs-vfpop">
                    <span class="iconfont guanbi icon-guanbi" @click="closeBannerPop"></span>
                    <div class="cs-vfpop-title">{{bannerPopTitle}}</div>
                    <div class="cs-vfpop-main">{{bannerPopDesc}}</div>
                    <div class="cs-vfpop-bottom" v-show="bannerPopCanDo == '0'">
                        <span class="cs-vb-ok" @click="bannerDo">办理</span>
                    </div>
                </div>
            </div>
        </div>
      <LabelPop
        v-show="popShow"
        :telnum="telnum"
        :dataList="labelList"
        @emClosePop="closePop"
      >

      </LabelPop>
      <!--流量明细-->
      <LiuLiangDetail  v-if="liuLiangFlag"
                       :fluxDetail="cusViewInfo.fluxDetail"
                       :todayFlux="todayFlux"
                       :dailyFlux="dailyFlux"
                       @closeMe="closeLiuLiang"></LiuLiangDetail>

        <div v-if="currentStep =='step2'">
            <HistoryList @orClick="goHistoryBack" :acctId="accountBookDetail.acctid" :subsId="cusViewInfo.userid"></HistoryList>
        </div>

          <!--新客户校验详情弹窗-->
        <div v-show="detailDetailShow == '1'" class="eject-box"></div>
        <div class="eject-window" v-show="detailDetailShow == '1'">
            <div class="eject-detail-tittle">温馨提示</div>
            <div class="eject-detail-content">
                 <div v-show="customDesc">{{customDesc}}</div>
                <div v-show="versionList">同证件同地市下用户是否办理宽带：{{versionList.s000000062691 | filterJudge}}</div>
                <div v-show="versionList">同证件同地市下用户是否办理互联网电视：{{versionList.s000000062701 | filterJudge}}</div>
                <div v-show="versionList">同证件同地市下用户是否办理信用购：{{versionList.s000000062711 | filterJudge}}</div>
                <div v-show="versionList">我网宽带客户(迎回客户）：{{versionList.s000000083871 | filterJudge}}</div>
                <div v-show="versionList">客户第二卡槽可迎回：{{versionList.s000000087461 | filterJudge}}</div>
            </div>
            <div class="eject-detail-bottom" @click="detailDetailShow = '0'">关闭</div>
        </div>

      <Market :showMarketingRecommend="showMarketingRecommend" ></Market>

      <div ref="fixedBtn"  v-show="shoppingCartFlag"  class="fixed-button pad-guide"
           @touchmove="moveBtn($event,'fixedBtn')">
        <img src="static/img/ShoppingCart.gif" @click="goShoppingCart()" class="gifCart">
      </div>
      <div ref="fixedSmartXin" class="fixed-button fixed-smart-xin" @touchmove="moveBtn($event,'fixedSmartXin')">
        <img src="static/business/areaIcon.png" @click="goAreaPage()" class="gificon">
      </div>
    </div>
</template>

<script>

    import BusinessAreaInner from 'components/business/BusinessAreaInner.vue'
    import CsViewTitle from 'components/business/CsViewTitle.vue'
    import Slide from 'components/common/Slide.vue'
    import Storage from '@/base/storage'
    import {qryBusiPermission} from '@/base/request/commonReq.js'
    import {needNumMixin,tokenFromAldMixin} from '@/base/mixin'
    import * as echarts from 'echarts';
    import { menuChain } from '@/base/mixins/menuChainMixin'
    import {menuLogRecord, checkScreenShot} from '@/base/request/commonReq.js'

    // import {iEncrpt} from '@/base/encrptH5.js'
    import ClientJs from '@/base/clientjs'
    import {getSessionId} from '@/base/utils.js'
    import LabelPop from 'components/common/LabelPop.vue'
    import HistoryList from 'components/business/HistoryLog/HistoryList.vue'
    import LiuLiangDetail from './LiuLiangDetail.vue'
    import Market from 'components/business/marketingView/MarketingWorkbench.vue'
    import TerminalPrecisionMarketing from 'components/business/CsViewInNew/terminalPrecisionMarketing.vue'
    import RecommendedScript from 'components/business/CsViewInNew/RecommendedScript.vue'
    import {chgStrToDate,dateFormat} from '@/base/utils'

    export default {
      mixins: [needNumMixin,tokenFromAldMixin,menuChain],
      data() {
            return {
                payMethod:'',
                dataStr: '',
                axiosNum: 0,
                currentStep:'step1',//步骤
                bListOrUserDFlag: true,             //业务列表和客户明细切换
                bannerData: [],
                scrollHeadFlag: false,              //滚动块显示
                bAList: [],                         //业务模块列表
                uinfo: {},
                cusViewInfo: {},
                userStateCode: '',                  //客户实名制状态编码(0,1,4,-1) 0-未登记 1-已登记 4-已审核 -1 -无登记
                accountBookDetail: {},              //账本信息
                familyProdName: '',                 //家庭产品名字
                telnum:'',                          //输入的手机号
                mainOffer:{},                       //主套餐
                secondaryCardNum:0,                 //副卡个数
                balance:'',                         //账单余额
                charge:'',                          //账单（当月消费）
                remainingVal:'',                    //剩余流量
                integration:'',                     //积分
                commonRemainingVal:'',              //通用流量剩余
                usedVal:'',                         //当前消费（已使用流量）
                prodBrandList:[],                   //宽带产品
                jqData:{},                          //鉴权信息
                secondData:[],                      //副卡数据
                bannerPopFlag:false,                //banner弹框是否显示
                bannerPopTitle:'',                  //banner弹框标题
                bannerPopDesc:'',                   //banner弹框简介
                bannerPopCanDo:'',                  //banner弹框是否要办理
                bannerItem:{},                      //banner Obj
                nationalFamilyNet: {},              //全国亲情网
                sCountDown:15,                      //倒计时
                orderMProdList: [],                 //主体资费套餐
                notActiveMainTariffFlag: true,     //待生效主体资费套餐展现
                popShow:false,                      //一级标签 显示
                labelList:[],                       //一级标签列表
                detailDetailShow:0,
                versionList:[],
                customDesc:'',                      //新用户校验明细
                filterJudge:'--',
                liuLiangFlag:false,                //流量明细弹窗
                activeMonth:'',                    //当前月
                activeDay:'',                       //当日
                dailyFlux:[],                       //当月流量明细
                todayFlux:'0M',                     //当日已用
                showMarketingRecommend:false,
                windowWidth:300,
                windowHeigth:500,
                noFaceIden:"",                      //不支持刷脸鉴权的菜单
                shoppingCartFlag:false,             //是否展示购物车
                rootLevel: -1, // 菜单链
                restAssured:false,                  //是否安心小号
                customerThreeSide: {
                  s000000096142 : '0',
                  s000000096152 : '0',
                  s000000095331 : '-',
                  s000000038091 : '-',
                  s000000094131 : '-',
                  s000000095321 : '-',
                  s000000096121 : '-',
                },              //客户三看
                groupBelongInf: {},//v网归属
                paymode:'',//统付方式
                paymentrela:'',//话费统付
                paymodeTime:'',
                applyDate:'',//入网时间
            }
        },
        computed: {
            userState: function () {
                let txt = '';
                if (this.userStateCode == '0') {
                    txt = '未登记';
                }
                if (this.userStateCode == '1') {
                    txt = '已登记';
                }
                if (this.userStateCode == '4') {
                    txt = '已审核';
                }
                if (this.userStateCode == '-1') {
                    txt = '无登记';
                }
                return txt;
            },
            btnTxt(){
                if(this.sCountDown < 15){
                    return `${this.sCountDown}s后刷新`;
                } else {
                    return '刷新';
                }
            }
        },
        created() {
            // 关闭截屏权限
            ClientJs.screenShot(0);
            this.getUserInfo();
            this.getTelNum();

            //设置调用链需要采集的菜单编号 add by qhuang at 20221103  客户视图默认就是一个特殊的菜单，初始化所有的调用链
            this.$pointLesslog && this.$pointLesslog.setChainBusiType('csview_menuid');

            let srcFrom = this.$route.query.srcFrom;
            this.dataRootLevel = Number(this.$route.query.dataRootLevel);
            // 如果不是数字，那取全局变量的值
            if(isNaN(this.dataRootLevel)) {
              this.dataRootLevel = window.dataRootLevel;
            } else {
              // 由于菜单返回不会携带dataRootLevel，所以要存到全局变量里
              window.dataRootLevel = this.dataRootLevel;
            }
            if(srcFrom == 'wh'||srcFrom=='whDesk'){
                Storage.session.set('csview_srcFrom',srcFrom);
            }
            if(srcFrom=='NetOrder'){
              Storage.session.set('csview_srcFrom',srcFrom);
            }
          if(srcFrom=='zxReservation'){
            Storage.session.set('csview_srcFrom',srcFrom);
          }
            this.getLabelInfo();
            this.showMarketingWorkbench();
            this.windowWidth = window.innerWidth || (document.body) && (document.body.clientWidth);
            //窗口高度
            this.windowHeigth = window.innerHeight||(document.body) && (document.body.clientHeight);
            //暂不支持刷脸鉴权的菜单查询
            this.getNoFaceIden();
            //是否展示购物车
            this.shoppingCartOpen();
            this.getCustomerThreeSide();

            if (Storage.session.get('csview_srcFrom') === "NetOrder") {
              if (this.$route.query.channel) {
                Storage.session.set("channel", this.$route.query.channel);
              }
            } else {
              Storage.session.set("channel", "");
            }
        },
        mounted() {
            document.querySelector(".csViewIn").addEventListener('scroll', this.handleScroll);
          // 截屏权限回调方法
          window['screenShotCallbackfunc'] = (result) => {
            console.log('截屏方法结果', result);
          };
        },
        methods: {
          payMethodParse(patStr) {
            //按|或、分割
            let arr = patStr.split('|');
            let res = '';
            for (let i = 0; i < arr.length; i++) {
              if (i != 0) {
                res += '、';
              }
              if (arr[i] === '0') {
                res += '统付';
              } else if (arr[i] === '1') {
                res += '被代付';
              } else if (arr[i] === '2') {
                res += '易充值';
              } else if (arr[i] === '3') {
                res += '全网自动充';
              } else if (arr[i] === '4') {
                res += '托收';
              } else if (arr[i] === '5') {
                res += '代扣';
              }
            }
            return res;
          },
            overnewCustomQry(val){
                this.axiosNum++
                this.dataStr = this.dataStr + '价位偏好' +val.s000000025351 + '主套餐档位' + val.s000000021072

            },
          getCustomerThreeSide() {
            let url = '/xsb/personBusiness/recordlogbusiaction/h5queryLabelByTel?telnum=' + this.telnum;
            this.$http.get(url)
              .then((res) => {
                let { retCode, retMsg, data } = res.data
                if (retCode == '0') {
                  console.info('3data',data)
                  if (data != '' && data != null && data.versionList) {
                    this.customerThreeSide = data.versionList;
                    //对空数据进行处理
                    if(!this.customerThreeSide.s000000096152||this.customerThreeSide.s000000096152==''){
                      this.customerThreeSide.s000000096152 = '0';
                    }
                    if(!this.customerThreeSide.s000000096142||this.customerThreeSide.s000000096142==''){
                      this.customerThreeSide.s000000096142 = '0';
                    }
                    if(!this.customerThreeSide.s000000095331||this.customerThreeSide.s000000095331==''){
                      this.customerThreeSide.s000000095331 = '-';
                    }
                    if(!this.customerThreeSide.s000000038091||this.customerThreeSide.s000000038091==''){
                      this.customerThreeSide.s000000038091 = '-';
                    }
                    if(!this.customerThreeSide.s000000094131||this.customerThreeSide.s000000094131==''){
                      this.customerThreeSide.s000000094131 = '-';
                    }
                    if(!this.customerThreeSide.s000000095321||this.customerThreeSide.s000000095321==''){
                      this.customerThreeSide.s000000095321 = '-';
                    }
                    if(!this.customerThreeSide.s000000096121||this.customerThreeSide.s000000096121==''){
                      this.customerThreeSide.s000000096121 = '-';
                    }
                  }
                }
                this.initCharts('flux-charts',this.customerThreeSide.s000000096142)
                this.initCharts('voice-charts',this.customerThreeSide.s000000096152)
                this.axiosNum++
                this.dataStr = this.dataStr + '流量饱和度' +this.customerThreeSide.s000000096142 + '语音饱和度' + this.customerThreeSide.s000000096152 + '看卡槽' + this.customerThreeSide.s000000096121
              }).catch((response) => {
                this.initCharts('flux-charts',this.customerThreeSide.s000000096142)
                this.initCharts('voice-charts',this.customerThreeSide.s000000096152)
              })
          },
          //初始化客户三看饱和度饼图
          initCharts(elId,data) {
            this.$nextTick(() => {
              let myChart = echarts.init(document.getElementById(elId))
              let datanum = parseFloat(data);
              if (datanum <= 1) {
                datanum = datanum * 100;
              }
              let sldata = [
                { name: "", value: 100-datanum, icon: "roundRect" },
                {
                  name: "", value: datanum, icon: "roundRect", label: {
                    normal: {
                      show: true,
                      fontSize:14,
                      color:'#505050',
                      fontWeight: 'bold',
                    }
                  }
                }
              ];
              let option = {
                color: ["#E4E4E4", "#1784F8"],
                animation: false,
                tooltip: {
                  show: false,
                },
                // 图列组件
                legend: {
                  show: false,
                  selectedMode: false
                },
                series: {
                  type: "pie",
                  // 饼图的大小
                  radius: ["75%", "90%"],
                  data: sldata,
                  label: {
                    normal: {
                      show: false,
                      formatter: "%\n{d}",
                      position: "center",
                      /*lineHight: 60,*/
                    },
                  },
                }
              };
              myChart.setOption(option)
              window.addEventListener("resize", () => {
                myChart.resize();
              })
            })
          },
            //是否展示购物车
            shoppingCartOpen() {
                qryBusiPermission('shoppingCart_open',this.$http).then((res)=> {
                    if(res.data.retCode == '0'){
                        this.shoppingCartFlag=true;//展示购物车
                    } else {
                        this.shoppingCartFlag=false;//不展示购物车
                    }
                })
            },

            //暂不支持刷脸鉴权的菜单查询
            getNoFaceIden(){
                let url = '/xsb/ability/businessLimit/h5getNoFaceIden?dictId=1108';
                this.$http.get(url,{unLoadFlg:true})
                    .then((res) =>{
                        if (res.data.retCode == '0') {
                            this.noFaceIden = res.data.data.toString();
                        }
                    })
            },

            getLabelInfo(){
              let _this = this;
              if(this.telnum === '' || this.telnum === null || this.telnum === undefined){
                this.$alert('未获取到用户手机号，无法查询一级标签列表，请重试');
                return;
              }
              let url = '/xsb/personBusiness/customerLabel/h5getCustomerLabelInfoList?telnum='+this.telnum;
              this.$http.get(url)
                .then((res) =>{
                  if (res.data.retCode == '0') {
                    //处理返回
                    _this.labelList = res.data.data;
                  }
                })
            },
            closePop(){

              this.popShow = false;

            },
            openPop(){
              this.popShow = true;
            },
            //返回第一页
            goHistoryBack(item){
              if(item=='back'){
                this.currentStep='step1';
              }
            },
            //消费记录
            goHistory(){
                this.currentStep='step2';
            },
            //获取用户信息
            getUserInfo(){
                this.uinfo = Storage.session.get('userInfo');
                this.jqData = Storage.session.get('jqData');
            },
            //获取用户输入的手机号
            getTelNum(){
                this.telnum = this.$route.query.telnum;
                this.getCustInfo();
                this.getSecondaryCard();
                this.getAccountBookDetail();
                this.getAlreadyOpened();//获取宽带产品、互联网电视
                this.getActives();//获取活动推荐数据
                this.getRestAssured();//是否是安心小号
                this.getVGroup();//查询v网归属
                this.getTelGroup();//查询统付
            },
            //刷新页面
            refreshPage(){
                if(this.sCountDown == 15){
                    let _this = this;
                    let timer = setInterval(() => {
                        _this.sCountDown--;
                        if (_this.sCountDown <= 0) {
                            _this.sCountDown = 15;          //恢复初始时间
                            clearInterval(timer);
                        }
                    }, 1000);
                    this.getCustInfo();
                    this.getSecondaryCard();
                    this.getAccountBookDetail();
                    this.getAlreadyOpened();//获取宽带产品、互联网电视
                    this.getActives();//获取活动推荐数据
                    this.getRestAssured();//是否是安心小号
                    this.getVGroup();//查询v网归属
                    this.getTelGroup();//查询统付
                }
            },
            //设置鉴权session里的username(外呼过来的没有用户姓名)
            setJqUserName(){
                let jqData = this.jqData;
                if(!jqData.userName){
                    jqData.userName = this.cusViewInfo.name;
                    Storage.session.set('jqData',jqData);
                }
            },
            //获取客户资料+查询用户是否审核过
            getCustInfo(){
                let _this = this;
                let url = '/xsb/personBusiness/customerView/h5CustomerInfo?isCsView=2&telnum='+this.telnum;
                this.$http.get(url).then((res)=>{
                    let resData = res.data.data;
                    if(res.data.retCode == '0'){
                      console.info('resData',resData)
                        Object.assign(_this.cusViewInfo,resData);
                        //入网时间
                        _this.applyDate = _this.cusViewInfo.applyDate;
                        //
                        _this.userStateCode = _this.cusViewInfo.realstatecode;
                        _this.payMethod = _this.parsePayMethod(_this.cusViewInfo.paytype);
                        _this.setJqUserName();
                        //得到userid
                        _this.getCustomerMoreInfo();
                    }else{
                        this.$toast( res.data.retMsg || '获取客户资料失败');
                    }
                }).catch((response)=>{
                })
            },
            parsePayMethod(str) {
              //按照|分割
              let arr = str.split('|');
              let res = '';
              //遍历
              for (let i = 0; i < arr.length; i++) {
                if (i != 0) {
                  res += '、';
                }
                if (arr[i] === '0') {
                  res += '统付';
                } else if (arr[i] === '1') {
                  res += '被代付';
                } else if (arr[i] === '2') {
                  res += '易充值';
                } else if (arr[i] === '3') {
                  res += '全网自动充';
                } else if (arr[i] === '4') {
                  res += '托收';
                } else if (arr[i] === '5') {
                  res += '代扣';
                }
              }
              return res;
            },
            //查询万能副卡(包含主体套餐)
            getSecondaryCard(){
                let _this = this;
                //return new Promise(function (resolve,reject){
                let url = '/xsb/personBusiness/customerView/h5SecondaryCard?telnum='+_this.telnum;
                _this.$http.get(url).then((res)=>{
                    let resData = res.data.data;
                    if(res.data.retCode == '0'){
                        _this.mainOffer = resData.mainOffer;
                        //判断是否有副卡，0 未办理，1已办理

                        if(resData.flag == '1'){
                            _this.secondaryCardNum = resData.attrList.length;
                            _this.secondData = resData.attrList;
                            _this.secondShowFlag = true;

                        }else{
                            _this.$toast('您未办理副卡');
                            _this.secondaryCardNum = 0;
                        }

                        //secondShowFlag
                        //resolve('');
                    }else{
                        // _this.$toast( res.data.retMsg || '查询万能副卡失败');
                    }
                }).catch((response)=>{
                })
                //});

                //查询主体资费套餐
                this.getMainTariff();
            },
            //获取 余额+当月消费+剩余流量+积分+宽带安装地址
            getCustomerMoreInfo(){
                let _this = this;
                //return new Promise(function (resolve,reject){
                let url = '/xsb/personBusiness/customerView/h5CustomerMoreInfo?telnum='+_this.telnum+'&userid='+_this.cusViewInfo.userid;
                _this.$http.get(url).then((res)=>{
                    let resData = res.data.data;
                    if(res.data.retCode == '0'){
                        _this.balance = resData.balance;                        //账单余额
                        _this.charge = resData.charge;                          //账单（当月消费）
                        _this.remainingVal = resData.fluxDetail && resData.fluxDetail.remainingVal;   //剩余流量
                        _this.integration = resData.integration;                //积分


                        _this.cusViewInfo.balance = resData.balance;
                        _this.cusViewInfo.charge = resData.charge;
                        _this.cusViewInfo.remainingVal = resData.fluxDetail && resData.fluxDetail.remainingVal;
                        _this.cusViewInfo.integration = resData.integration;
                        _this.cusViewInfo.fluxDetail = resData.fluxDetail;
                        _this.cusViewInfo.bandAddr = resData.bandAddr;

                        _this.commonRemainingVal = resData.fluxDetail && resData.fluxDetail.commonRemainingVal;
                        _this.usedVal = resData.fluxDetail && resData.fluxDetail.usedVal;


                        this.axiosNum++
                        this.dataStr = this.dataStr + '通用流量剩余' +_this.commonRemainingVal + '当前消费' +  _this.usedVal


                    }else{
                        _this.$toast( res.data.retMsg || '获取余额消费失败');
                    }
                }).catch((response)=>{
                })
                //});

            },
            //获取账本明细
            getAccountBookDetail() {
                let _this = this;
                let url = '/xsb/personBusiness/customerView/h5AccountBookDetail?telnum=' + _this.telnum;
                _this.$http.get(url).then((res) => {
                    let resData = res.data.data;
                    if (res.data.retCode == '0') {
                        _this.accountBookDetail = resData;
                        _this.axiosNum++
                        _this.dataStr = _this.dataStr + '现金账本' +_this.accountBookDetail.cashBook
                    }else{
                        _this.$toast( res.data.retMsg || '获取账本明细失败');
                    }
                }).catch((response) => {
                })
            },
            //获取宽带产品、互联网电视
            getAlreadyOpened(){
                let _this = this;
                //return new Promise(function (resolve,reject){
                let url = '/xsb/personBusiness/customerView/h5alreadyOpened?telnum='+_this.telnum;
                _this.$http.get(url).then((res)=>{
                    let resData = res.data.data;
                    if(res.data.retCode == '0'){
                        //判断宽带是否有
                        if(resData.isMband == '1'){
                            //_this.prodBrandName = resData.prodBrandList[0].prodname;
                            let prodBrandData = resData.prodBrandList;
                            for(var i=0;i<prodBrandData.length;i++){
                                prodBrandData[i].isDue = _this.isDueDate(prodBrandData[i].enddate);
                            }

                            _this.cusViewInfo.hasBroadBand = '1';       //判断宽带是否开通，存值
                            _this.cusViewInfo.prodBrandList = prodBrandData;

                        }else{
                            _this.prodBrandName = '';
                            _this.cusViewInfo.hasBroadBand = '0';       //判断宽带是否开通，存值
                        }
                        //判断电视产品是否有 0 没开 1开过第一台  2开过第二台电视 3开过第三台
                        if(resData.isNetTv == '1' || resData.isNetTv == '2' || resData.isNetTv == '3'){
                            //_this.prodNetName = resData.prodNetList[0].prodname;

                            let prodNetListData = resData.prodNetList;
                            for(var i=0;i<prodNetListData.length;i++){
                                prodNetListData[i].isDue = _this.isDueDate(prodNetListData[i].enddate);
                            }
                            _this.cusViewInfo.prodNetList = prodNetListData;
                            let isNetTv = resData.isNetTv;
                            _this.cusViewInfo.hasNetProd = resData.isNetTv;

                        }else{
                            _this.prodNetName = '';
                            _this.cusViewInfo.hasNetProd = '0';
                        }
                        //resolve('');
                    }else{
                        _this.$toast( res.data.retMsg || '获取宽带产品、互联网电视失败');
                        _this.cusViewInfo.hasNetProd = '-1';
                        _this.prodNetName = '';
                        _this.prodBrandName = '';
                        _this.cusViewInfo.hasBroadBand = '-1';       //判断宽带是否开通，存值
                    }
                    _this.getFamilyVNet();//获取家庭v网信息
                }).catch((response)=>{
                })
                //});
            },
            //获取家庭v网信息
            getFamilyVNet(){
                let _this = this;
                //return new Promise(function (resolve,reject){
                let url = '/xsb/personBusiness/customerView/h5familyVNet?telnum='+_this.telnum;

                _this.$http.get(url).then((res)=>{
                    let resData = res.data.data;
                    if(res.data.retCode == '0'){
                        //hasfmyprod 0代表没开通，1代表开通
                        if(resData.hasfmyprod == '1'){
                            if(resData.prodInfoList && resData.prodInfoList.length>0){
                                _this.familyProdName = resData.prodInfoList[0].prodName;
                            }else {
                                _this.familyProdName = '';
                            }
                            _this.cusViewInfo.familyMemberInfoList = resData.memberInfoList;
                            _this.cusViewInfo.familyProdInfoList = resData.prodInfoList;
                            _this.cusViewInfo.hasfmyprod = '1';
                        }else{
                            _this.familyProdName = '';
                            _this.cusViewInfo.hasfmyprod = '0';
                        }

                        //是否为家庭户主
                        _this.cusViewInfo.hashousehold = resData.ishousehold;

                        //resolve('');
                    }else{
                        _this.$toast( res.data.retMsg || '获取家庭v网信息失败');
                        _this.familyProdName = '';
                        _this.cusViewInfo.hasfmyprod = '-1';
                        _this.cusViewInfo.hashousehold = '0';
                    }

                    _this.getNationalFamilyNet();//获取亲情网

                }).catch((response)=>{
                })
                //});

            },
            //获取亲情网信息
            getNationalFamilyNet() {
                let _this = this;
                let url = '/xsb/personBusiness/familyNetwork/h5QryMemberInfo';
                let params = {
                    telnum: _this.telnum,
                    pkgprodid: '2000009794'
                };
                this.$http.post(url, params).then(res => {
                    let result = res.data;
                    if('0'== result.retCode) {
                        _this.nationalFamilyNet = result.data;
                    } else {
                        //_this.$toast(result.retMsg);
                    }
                    _this.getBusiness();
                });
            },

            //判断时间是否快过期（一月）
            isDueDate(dueDate){
                let isDue = '0';   //0表示没到期，1表示即将到期,-1表示过期
                let dueNum = 30;   //30天提醒
                let dueMS = 1000*60*60*24*dueNum;
                let dueTime = new Date(dueDate).getTime();
                let nowTime = new Date().getTime();
                if((dueTime-nowTime) <= dueMS && (dueTime-nowTime) > 0){
                    //在到期时间内
                    isDue = '1';
                }else if((dueTime-nowTime) > dueMS){
                    isDue = '0';
                }else{
                    isDue = '-1';
                }
                return isDue;
            },
            //滚动事件
            handleScroll() {
                let ele = document.querySelector(".cs-scroll-show");
                var scrollTop = document.querySelector(".csViewIn").scrollTop;
                if (scrollTop > 60) {
                    ele.style.transform = 'translate(0,0)';
                } else {
                    ele.style.transform = 'translate(0,-130%)';
                }
            },
            //回到业务主页
            goWorkstage() {
              this.$messagebox({
                title: "提示",
                message: "请确认退出客户视图",
                showConfirmButton: true,
                showCancelButton: true,
                confirmButtonText: '确认',
                cancelButtonText: '取消',
              }).then((action) => {
                if (action == "confirm") {
                  if (this.$route.query.fromFlag) {
                    ClientJs.closeWebKitWithCallBack('closeWgtWebkitCb', '')

                    return
                  }
                  let srcFrom = Storage.session.get('csview_srcFrom');
                  if(srcFrom == 'wh' ){
                    this.$router.push('/outCall');
                    Storage.session.remove('csview_srcFrom');
                  }else if(srcFrom==='zxReservation'){
                    this.$router.push({
                      path: "/zxReservationDetail",
                      query: {
                        srcFrom: 'csViewln'
                      }
                    });
                    Storage.session.remove('csview_srcFrom');
                  }else if(srcFrom=='NetOrder'){
                    this.$router.push({
                      path: "netOrderSheetDetail",
                      query: {
                        srcFrom: 'csViewln'
                      }
                    });
                    Storage.session.remove('csview_srcFrom');
                  }else if(srcFrom=='whDesk'){
                    this.$router.push('/WhUserDetail');
                    Storage.session.remove('csview_srcFrom');
                  }else if(this.$route.query.billId){
                    this.$router.push({
                      name:'FamilyDiagnosis',
                      query:{
                        isYiDong:this.$route.query.isYiDong,
                        telnum:this.$route.query.regMsisdn,
                        billId:this.$route.query.billId,
                        numList:this.$route.query.numList,
                      }
                    });
                  }else if(this.$route.query.comeFrom && this.$route.query.comeFrom == 'suanzhangdan'){
                    this.$router.push({
                      path: "/familyDiagnosisDeskTop",
                      query: {
                        respResult: this.$route.query.respResult,
                        strList: this.$route.query.strList,
                        items: this.$route.query.items,
                        comeFrom : this.$route.query.respResult == '' ? 'workStage' :''
                      }
                    })
                  }else if(this.$route.query.comeFrom && this.$route.query.comeFrom == 'business'){
                    this.$router.push("/business")
                  }else if(Storage.session.get('workStageNew')){
                    this.$router.push('/workStageNew');
                    Storage.session.remove('workStageNew')
                  } else {
                    this.$router.push({name: 'WorkStage'});
                  }
                }
              });
            },

            //获取业务模块数据
            getBusiness() {
                let _this = this;
                let isMband = _this.cusViewInfo.hasBroadBand;       //是否开通宽带        0否，1是
                let isNetTv = _this.cusViewInfo.hasNetProd;         //是否开通互联网电视     0否，1是
                let isFamilyNet = _this.cusViewInfo.hasfmyprod;     //是否开通家庭网     0否，1是
                let ishousehold = _this.cusViewInfo.hashousehold;   //是否是户主     0否，1是

                let relativeWeiHu = 1;//亲情网维护不显示
                let relativeFuhaoExit = 1;//亲情网退出不显示
                if(null!=_this.nationalFamilyNet) {
                    if(null!=_this.nationalFamilyNet.mainGroup&&_this.nationalFamilyNet.mainGroup.length>0) {
                        relativeWeiHu = 0;//亲情网维护显示
                    }
                    if(null!=_this.nationalFamilyNet.subGroup&&_this.nationalFamilyNet.subGroup.length>0) {
                        relativeFuhaoExit = 0;//亲情网退出显示
                    }
                }
                /*isMband --是否开通宽带 0/1
                isNetTv--是否开通互联网电视 0/1
                isFamilyNet--是否开通家庭网0/1
                ishousehold--如果开通家庭网，是否是户主0/1*/
                let param = `?stationId=${_this.uinfo.stationId}&level=3:0&isMband=${isMband}&isNetTv=${isNetTv}&isFamilyNet=${isFamilyNet}&ishousehold=${ishousehold}&relativeWeiHu=${relativeWeiHu}&relativeFuhaoExit=${relativeFuhaoExit}`;
                this.$http.get('/xsb/api-user/menu/h5getCustomViewMenuByLevel' + param).then((res) => {
                    if (res.data.retCode == '0') {
                        _this.bAList = res.data.data;
                        _this.filterMenu();
                    }else{
                        _this.$toast( res.data.retMsg || '查询失败');
                    }
                }).catch((response) => {
                })
            },

            //过滤菜单
            filterMenu() {
                // 工号不为 14095583 的操作员，不允许访问CH融合受理
                if (this.uinfo.crmId != "14095583") {
                    for (let i = 0; i < this.bAList.length; i++) {
                        // 宽带专区
                        if (this.bAList[i].privId == "20000035") {
                            for (let j = 0; j < this.bAList[i].itemList.length; j++) {
                                if (this.bAList[i].itemList[j].privId == "100325") {
                                    this.bAList[i].itemList.splice(j,1);
                                }
                            }
                        }
                    }
                }
            },

            //业务图标点击前判断权限
            async businessCkPre(item){
                // 记录菜单链
                this.updateMenuChain(item.privId,3)
              //   // 菜单点击日志采集
              // let menuParam = {
              //   stationId: this.uinfo.stationId,
              //   serverNumber: this.uinfo.servNumber,
              //   clickTime: dateFormat(new Date(), ('yyyy-MM-dd hh:mm:ss'))
              // };
              // let menuData = await menuLogRecord(menuParam);
              menuLogRecord({});
              // 菜单截屏权限控制
              checkScreenShot(item.privId);
                //设置调用链需要采集的菜单编号 add by qhuang at 20221103
                this.$pointLesslog && this.$pointLesslog.setChainBusiType(item.privId);

                let isNewFeature = item.isNewFeature;// 0：不用控制，1：控制
                if(isNewFeature == '1'){
                    let featureType = item.featureType;//业务类型
                    let param = {
                        busiType:featureType
                    }
                    let self = this;
                    this.$http.post('/xsb/ability/businessLimit/h5QryBusiPermission',param).then(res => {
                        //注意 retCode=0 有权限点击该菜单
                        if(res.data.retCode == '0'){
                            self.businsCk(item);
                        } else {
                            self.$alert(res.data.retMsg || '暂无权限')
                        }
                    })
                }else if(isNewFeature == '2'){
                    //crm操作状态判断 ,若type有值则同时判断权限
                    this.checkCrmStatus(item);
                }  else {
                    this.businsCk(item);
                }
            },

            //操作员工号生效状态校验
            async checkCrmStatus(item){
                let self = this;
                let uinfo = Storage.session.get("userInfo");
                if(!uinfo.crmId){
                    this.$alert('当前岗位CRM工号为空');
                    return false;
                }
                if(uinfo.crmStatus){
                    if(uinfo.crmStatus != '1'){
                        this.$alert('当前操作员CRM工号'+uinfo.crmId+'状态无效，禁止办理业务');
                        return false;
                    }
                }else{
                    let orgRes= await self.qryOrgId();
                    if(orgRes.data.retCode == '0'){
                        let orgData = orgRes.data.data;
                        if(orgData.orgId==""||orgData.orgId==undefined||orgData.orgId==null){
                            this.$alert('获取组织机构编号失败,暂无法办理业务');
                            return false;
                        }else{
                            uinfo.crmOrgId =orgData.orgId;
                            uinfo.crmStatus=orgData.status;//crm生效状态
                            uinfo.crmOrgName =orgData.orgName;
                          Storage.session.set('userInfo',uinfo);
                            if(uinfo.crmStatus != '1'){
                                this.$alert('当前操作员CRM工号【'+uinfo.crmId+'】状态无效，禁止办理业务');
                                return false;
                            }
                        }
                    }else{
                        this.$alert(orgRes.data.retMsg || '获取组织机构编号失败,暂无法办理业务');
                        return false;
                    }
                }

                let featureType = item.featureType;//业务类型
                if(featureType){
                    let param = {
                        busiType:featureType
                    }
                    this.$http.post('/xsb/ability/businessLimit/h5QryBusiPermission',param).then(res => {
                        //注意 retCode=0 有权限点击该菜单
                        if(res.data.retCode == '0'){
                            self.businsCk(item);
                        } else {
                            self.$alert(res.data.retMsg || '暂无权限')
                        }
                    })
                }else{
                    self.businsCk(item);
                }

            },

            //获取组织机构编码
            async qryOrgId(){
                return this.$http.get('/xsb/personBusiness/chooseTelEnterNet/h5QryOperatorInfo');
            },

            //业务图标点击
            businsCk(item){
                //副号鉴权
                if(this.jqData.crmAuthType=='AuthChkFaceIden') {
                    if (this.noFaceIden.indexOf(item.privId) !='-1') {
                        this.$alert("当前业务暂不支持刷脸鉴权，请退出重新鉴权");
                        return false;
                    }
                }

                // let _this = this;
                // let webUrl = Storage.get('webUrl');

                if(item.privId === '100045'){// 移机(新)
                    this.$router.push('/yiji');

                }else if(item.privId === '100035'){  //已开服务(新)
                    this.$router.push('/yiKai?srcFrom=csView');
                }else if(item.privId === '100036'){  //100036 家庭V网开通(新)
                    this.$router.push('/familyKaiTong?srcFrom=csView');
                }else if(item.privId === '100037'){  //100037 家庭V网维护(新)
                    this.$router.push('/familyWeiHu?srcFrom=csView');
                }else if(item.privId === '100038'){  //100038 家庭V网退订(新)
                    this.$router.push('/familyTuiDing?srcFrom=csView');
                }else if(item.privId === '100039'){  //100039 家庭V网退出(新)
                    this.$router.push('/familyTuiChu?srcFrom=csView');
                }else if(item.privId === '100012'){//100012 宽带开通新
                    this.$router.push('/kuandaiKaiTong?srcFrom=csView');
                }else if(item.privId === '100013' || item.privId === '100032' || item.privId === "100314"){//100013 电视开通
                    let tvSort = '1';
                    let rpath = '/internetTvOpen?srcFrom=csView';
                    if (this.cusViewInfo.hasNetProd == '0') {
                        tvSort = '1';
                        rpath += '&tvSort=' + tvSort;
                        this.$router.push(rpath);
                    }else if(this.cusViewInfo.hasNetProd == '1'){//开过一台电视，就传tvSort为2到电视开通页面
                        tvSort = '2';
                        //第二台电视只能开和第一台电视同样的产品，所以把第一台电视的产品传过去
                        rpath += '&alreadProd=' + JSON.stringify(this.cusViewInfo.prodNetList[0]);
                        rpath += '&tvSort=' + tvSort;
                        this.$router.push(rpath);
                    } else if (this.cusViewInfo.hasNetProd == '2') {
                      tvSort = '3';
                      let prodNetList = this.cusViewInfo.prodNetList;
                      let prodInfo = {};
                      for (let item of prodNetList) {
                        if (item.pakageid == "2013000101" || item.pakageid == "2013000102") {
                          prodInfo = item;
                          break;
                        }
                      }
                      //跳第三台电视开通
                      rpath += '&alreadProd=' + JSON.stringify(prodInfo);
                      rpath += '&tvSort=' + tvSort;
                      this.$router.push(rpath);
                    } else if (this.cusViewInfo.hasNetProd == '3') {
                        this.$alert('已开通过3台互联网电视！');
                    }

                }else if(item.privId === '100014'){//100014 个人营销案
                    // this.$router.push('/personPici?srcFrom=csView');
                    this.$router.push('/marketEntry?type=person&srcFrom=csView');
                }else if(item.privId === '100016') { //亲情网开通
                    this.$router.push('/relativeKaiTong?srcFrom=csView');
                }else if(item.privId === '100017') { //亲情网维护
                    this.$router.push('/relativeWeiHu?srcFrom=csView');
                }else if(item.privId === '100018') { //亲情网退出
                    this.$router.push('/relativeFuhaoExit?srcFrom=csView');
                }else if(item.privId === '100020') { //增值产品订购
                    this.$router.push('/valueAddOrderMarket?srcFrom=csView');
                }else if(item.privId === '100021') { //宽带产品变更
                    this.$router.push('/bandChange?srcFrom=csView');
                }else if(item.privId === '100047'){// 万能副卡 100047 /wanenngfuka
                    this.$router.push('/wanenngfuka?srcFrom=csView');
                }else if(item.privId === '100048'){// 核心成员(新)  /keyMemberKaiTong
                    this.$router.push('/keyMemberKaiTong?srcFrom=csView');
                } else if(item.privId === '100057'){ // 智能组网
                    this.$router.push('/smartNetWork?srcFrom=csView');
                }else if(item.privId == '100061'){ //主体变更(新)
                    this.$router.push('/mainProdMarket?srcFrom=csView');
                }else if(item.privId == '100074'){ //中断接续营销案
                    this.$router.push('/discontinueMain?srcFrom=csView');
                }else if(item.privId == '100085'){ //设备领取(新)
                    this.$router.push('/devicereceive?srcFrom=csView');
                }else if(item.privId === '100089'){ // 家庭组网
                    this.$router.push('/familyNetWork?srcFrom=csView');
                }else if(item.privId === '100102'){ //话费充值
                   this.$router.push('/finished?srcFrom=csView');
                }else if(item.privId === '100117'){ //甩单管理
                    this.$router.push('/finished?srcFrom=csView');
                }else {
                    //阿拉盯token拉起菜单，不考虑第三方的拉起
                    if(item.opParentid == 'fsop'){//阿拉盯渠道
                        this.goTokenAld(item);
                    }
                }
            },
            //阿拉盯拉起菜单
            goTokenAld(item){
                let client = /android|harmony/gi.test(navigator.userAgent)?'ANDROID':'IOS';
                let uinfo = this.uinfo;
                let  url = `/xsb/api-user/tokenAuthen/h5genToken.do?opId=${item.opId}&prviId=${item.privId}&clientType=${client}`;
                url += `&regionId=${uinfo.region}&operId=${uinfo.staffId}&stationId=${uinfo.stationId}&phoneNumber=${uinfo.servNumber}`;
                this.$http.get(url).then((response)=>{
                    let {retCode,retMsg,data} = response.data;
                    if(retCode == '0'){
                        let opUrl = data.opUrl;
                        //校验token码
                        this.$http.get(opUrl).then((res)=>{
                            let {retCode,retMsg,data} = res.data;
                            if(retCode == '0'){//令牌鉴权成功
                                console.info(data.url);
                                let pageUrl = data.url;
                                if(pageUrl && ~pageUrl.indexOf('?')){
                                    pageUrl += '&srcFrom=csView';
                                } else {
                                    pageUrl += '?srcFrom=csView';
                                }
                                this.$router.push(pageUrl);
                            } else {
                                this.$alert(retMsg || '令牌token鉴权失败')
                            }
                        });
                    } else {
                        this.$alert(retMsg || '拉起token失败');
                    }

                }).catch((response)=>{
                })
            },
            //活动推荐接口
            getActives(){
                let _this = this;
                let telNum = this.$route.query.telnum;
                let jqType = Storage.session.get('jqData').authtype;

                let url = '/xsb/personBusiness/activeRecommend/h5getActivity?telnum='+telNum+'&authtype='+jqType;
                this.$http.get(url).then((res)=>{

                    if(res.data.retCode == '0'){
                        let dataActiveList = res.data.data;
                        //置前
                        _this.bannerData.push({bid:'99',activeName:' ',src:'./static/img/banner-nomore.png'})//添加一个更多选项
                        if(dataActiveList.length > 0){
                            for(var i=0;i<dataActiveList.length;i++){
                                if(dataActiveList[i].productType == '2'){ //预警信息
                                    dataActiveList[i].src = './static/img/banner_cs2.jpg'
                                }else if(dataActiveList[i].productType == '3'){ //话务推荐
                                    dataActiveList[i].src = './static/img/banner_cs3.jpg'
                                }else if(dataActiveList[i].productType == '4'){ //信用推荐
                                    dataActiveList[i].src = './static/img/banner_cs4.jpg'
                                }else if(dataActiveList[i].productType == '5'){ //终端推荐
                                    dataActiveList[i].src = './static/img/banner_cs5.jpg'
                                }else if(dataActiveList[i].productType == '6'){ //流量推荐
                                    dataActiveList[i].src = './static/img/banner_cs6.jpg'
                                }else if(dataActiveList[i].productType == '7'){ //宽带推荐
                                    dataActiveList[i].src = './static/img/banner_cs7.jpg'
                                }else if(dataActiveList[i].productType == '8'){ //促销推荐
                                    dataActiveList[i].src = './static/img/banner_cs8.jpg'
                                }else if(dataActiveList[i].productType == '10'){ //产品推荐
                                    dataActiveList[i].src = './static/img/banner_cs10.jpg'
                                }else if(dataActiveList[i].productType == '13'){ //全省统一营销活动
                                    dataActiveList[i].src = './static/img/banner_cs13.jpg'
                                }else if(dataActiveList[i].productType == '14'){ //应用分发
                                    dataActiveList[i].src = './static/img/banner_cs14.jpg'
                                }else if(dataActiveList[i].productType == '15'){ //流量内容推荐
                                    dataActiveList[i].src = './static/img/banner_cs15.jpg'
                                }else if(dataActiveList[i].productType == '16'){ //流量促销包
                                    dataActiveList[i].src = './static/img/banner_cs16.jpg'
                                }else{ //其他
                                    dataActiveList[i].src = './static/img/banner_cs00.jpg'
                                }
                            }
                          //合并bannerData和dataActiveList
                          _this.bannerData = _this.bannerData.concat(dataActiveList);
                        }else{
                          //合并
                            _this.bannerData = _this.bannerData.concat([{src:'./static/img/banner_cs_default.jpg'}]);
                        }
                    }else{
                        _this.$toast('活动推荐查询失败');
                    }
                }).catch((err)=>{
                });
            },
            getRestAssured(){
                let url = '/xsb/personBusiness/customerView/h5qryhdhnum?telnum='+this.telnum;
                this.$http.get(url).then((res)=>{
                  let {retCode,retMsg,data} = res.data;
                  if(retCode == '0'){
                    console.info('data',data);
                      if(data.restAssured == 'true'){
                        this.restAssured = true;
                      }else {
                        this.restAssured = false;
                      }
                  }
                }).catch((response)=>{
                  console.info(response);
                })
            },
            //banner点击
            bannerCk(item){
                if(item.bid && item.bid === '99'){
                    let featureType = "fea_realTimeMatching";//业务类型
                    let param = {
                        busiType:featureType
                    }
                    this.$http.post('/xsb/ability/businessLimit/h5QryBusiPermission',param).then(res => {
                        if(res.data.retCode == '0'){
                            this.$router.push({
                                path:'/realTimeMatching',
                                query:{
                                    userPhone:this.telnum
                                }
                            });
                        } else {
                            this.$router.push('/');//活动受理
                            this.$router.push({
                                name:'ActiveRecommd',
                                query:{
                                    userPhone:this.telnum,
                                    srcFrom:'csView',
                                    source:"2"
                                }
                            });
                        }
                    })

                } else {
                    this.bannerPopTitle = item.activeName;
                    this.bannerPopDesc = item.activeDesc;
                    this.bannerPopCanDo = item.canDo;
                    this.bannerPopFlag = true;
                    this.bannerItem = item;
                }
            },
            //关闭banner弹框
            closeBannerPop(){
                this.bannerPopFlag = false;
            },
            //banner 办理
            bannerDo(){
                // let webUrl = Storage.get('webUrl');
                // let jqType = Storage.session.get('jqData').authtype;
                // let url =`${webUrl}/psmarket.do?nextStep=h5index&telnum=${this.telnum}&operid=${this.uinfo.crmId}&regionId=${this.uinfo.region}&app_id=APP_00001&authtype=${jqType}&viewFlg=4&piciid=${this.bannerItem.productId}&piciname=${this.bannerItem.productIdExp5}&dangciid=${this.bannerItem.productIdExp1}&dangciname=${this.bannerItem.productIdExp6}`;

                // url = iEncrpt(url); //如果需要对url后面的参数加密，请调用iEncrpt
                // window.location.href=url;

                let bannerItem = this.bannerItem;
                this.$router.push({
                    path: '/personReward',
                    query: {
                        actId: bannerItem.productId,
                        actName: bannerItem.productIdExp5,
                        levelId: bannerItem.productIdExp1,
                        levelName: bannerItem.productIdExp6,
                        packType:'2',
                        srcFrom:'csView'
                    }
                });
            },
            //查询主体资费套餐
            getMainTariff() {
                this.notActiveMainTariffFlag = false;
                let _this = this;
                let url = `/xsb/personBusiness/mainProductChange/h5GetMainProdInfo?telnum=${this.telnum}`;
                this.$http.get(url).then(res => {
                    let result = res.data;
                    if('0'== result.retCode) {
                        _this.orderMProdList = result.data.infoList||[];
                        if(_this.orderMProdList && _this.orderMProdList.length>0) {
                            for(let i=0;i<_this.orderMProdList.length;i++) {
                                if(_this.orderMProdList[i].status==0) {
                                    _this.notActiveMainTariffFlag = true;
                                    break;
                                }
                            }
                        }
                    } else {
                        // _this.$alert(result.retMsg||'查询主体资费套餐失败');
                    }
                }).catch((response) => {

                });
            },
             //新用户校验接口 FSOP2BDS1450 2021.08.19
            newCustomQry(){
                this.$http.get(`/xsb/personBusiness/preTelEnterNet/h5qryNewCustomByTelnum?telnum=${this.telnum}`).then((res) => {
                    let { retCode, retMsg, data } = res.data;
                    if(retCode == '0'){
                        if(data.customerBusi && data.customerBusi.versionList){
                            this.versionList = data.customerBusi.versionList;
                            if (this.versionList.s000000087461 == '是' || this.versionList.s000000087471 == '是'){
                              this.versionList.s000000087461 = '是';
                            }
                            this.detailDetailShow = '1';
                        }
                       //TODO 新用户查询提示
                        if(data.result == '1' || data.result == '2' || data.result == '3'){
                          this.customDesc = data.resultMsg;
                          this.detailDetailShow = '1';
                        }else{
                          this.$alert(retMsg || '新用户校验查询失败');
                        }
                    }else{
                        this.$alert(retMsg || '新客户查询失败');
                    }
                }).catch((res) => {
                    //this.$log('h5qryNewCustom',BUSI_TYPE,res,3);
                });
            },
          //打开流量明细
          clickLiuLiang(){
            this.liuLiangFlag=true;

            //当前月
            if(!this.activeMonth) {
              let data = new Date();
              let y = data.getFullYear();
              let m = data.getMonth() + 1;
              let z=data.getDate();
              m = m < 10 ? "0" + m : m;
              this.activeMonth = y + "" + m;
              this.activeDay = z;
            }
            this.getPlans();
          },
          //按月查询手机号的流量使用情况
          getPlans() {
            let url = '/xsb/personBusiness/customerView/h5GetMemberVoiceFlux';
            let parme = {
              "monthDate": this.activeMonth,
              "servnums": this.telnum
            }
            this.$http.post(url, parme).then((res) => {
              if (res.data.retCode == '0') {
                let dailyList=res.data.data;
                if(dailyList.length>0){
                  this.dailyFlux=dailyList[0].flux.dailyFlux;
                  if(this.dailyFlux) {
                    for (let i = 0; this.dailyFlux.length > i; i++) {
                      if (this.dailyFlux[i].day == this.activeDay) {
                        this.todayFlux = this.dailyFlux[i].fluxVal;
                      }
                    }
                  }else{
                    this.$toast('获取每日流量明细失败');
                  }
                }
              } else {
                this.$toast(res.data.retMsg || '获取流量失败');
              }
            }).catch((response) => {
              this.$toast('获取流量网络超时');
            })
          },
          //关闭流量明细
          closeLiuLiang(){
            this.liuLiangFlag=false;
          },
          //判断是否有营销工作台权限
          showMarketingWorkbench() {
            qryBusiPermission('csview_openRecommend',this.$http).then((res)=> {
              if(res.data.retCode == '0'){
                this.showMarketingRecommend = true;//弹框
              } else {
                this.showMarketingRecommend = false;//不弹框
              }
            })
          },
          goAreaPage(){
              this.updateMenuChain(this.CONSTVAL.MENU_CHAIN.PREFECTURE_AREA, 3);
              menuLogRecord({})

              let region = this.uinfo.region;//m
              this.clickCount('spec_area');
              this.tokenAreaPage(region,this.uinfo);
          },
          clickCount(funcName){
            let url = `/xsb/personBusiness/funcCount/h5countFuncClick?funcName=${funcName}`;
            this.$http.get(url).then( res => {
            })
          },
          moveBtn(event, refdom){
            event.preventDefault();
            let touch = event.targetTouches[0] || event.changedTouches[0];
            let left = touch.pageX;
            let top = touch.pageY;
            if(left < 0){//移动图标不超出最左边
              left = 0;
            }else if(left + 50 > this.windowWidth){//50为移动图标的宽度
              left = this.windowWidth - 50;
            }
            if(top < 0){//移动图标不超出最上边
              top = 0;
            }else if(top + 50  > this.windowHeigth){//50为移动图标的高度
              top = this.windowHeigth - 50 ;//60为底部菜单的高度
            }
            let btnDom = this.$refs[refdom];
            btnDom.style.left = left + 'px';
            btnDom.style.top = top + 'px';
          },
            //打开购物车
            goShoppingCart(){
                //埋点
                this.updateMenuChain(this.CONSTVAL.MENU_CHAIN.SHOPPING_CART, 3);
                menuLogRecord({})

                this.$router.push({
                    path: '/shoppingCartManage',
                    query: {
                        srcFrom: 'csView',
                    }
                });
            },
          //查询集团归属
          getVGroup(){
            let param = {
              'telnum': this.telnum
            }
            this.$http.post('/xsb/personBusiness/groupVNet/h5GroupBelongInf', param).then((res) => {

              if ('0' === res.data.retCode) {
                this.groupBelongInf = res.data.data;

                if(this.groupBelongInf.paymode===""||this.groupBelongInf.paymode===undefined||this.groupBelongInf.paymode===null){
                  this.paymode='-';
                }else {
                  let date=this.groupBelongInf.startDate
                  this.paymodeTime=date.substring(0, 4)+'/'+date.substring(4, 6)+'/'+date.substring(6, 8);
                  this.paymode = this.groupBelongInf.paymode === "1"?"否":"是";
                }

              } else {

              }
            }).catch((response) => {
            })
          },

          getTelGroup(){
            let param = {
              'telnumber': this.telnum,
              'region':this.uinfo.region
            }
            this.$http.post('/xsb/personBusiness/groupVNet/h5qryTelnumpayInf', param).then((res) => {
              if ('0' === res.data.retCode) {
                 let data = res.data.data;

                if(data.paymentrelaflag===""||data.paymentrelaflag===undefined||data.paymentrelaflag===null){
                  this.paymentrela='-';
                }else {
                  this.paymentrela = data.paymentrelaflag === "0"?"否":"是";
                }
              } else {
                this.paymentrela='-';
              }
            }).catch((response) => {
              this.paymentrela='-';
            })
          }
        },
        components: {
            Slide,
            BusinessAreaInner,
            CsViewTitle,
            HistoryList,
            LabelPop,
            LiuLiangDetail,
            Market,
            TerminalPrecisionMarketing,
            RecommendedScript
        },
        filters:{
            //电话号码中间几位模糊化
            starTel(val){
                if(!val){
                    return '***';
                } else  {
                    let reg = /^(\d{3})\d*(\d{4})$/;
                    return val.replace(reg,'$1****$2')
                }
            },
            starName(name){
                if(!name){
                    return '***';
                } else  {
                    return name.substr(0,1) + new Array(name.length).join('*');
                }
            },
          checkApplyDate(applyDate){
              if(applyDate){
                return dateFormat(chgStrToDate(applyDate), "yyyy-MM-dd hh:mm:ss");
              }else{
                return applyDate;
              }

          },
        },
      activated() {
        // 关闭截屏权限
        ClientJs.screenShot(0);
      }
    }
</script>

<style scoped lang="less">

    .icon-guanbi{display: block;position: absolute;width: 26px;height: 26px;top:4px;right:5px;
        text-align: center;line-height: 26px;
    }
    .banner-filter{position: fixed;top:0px;left:0px;bottom:0px;right:0px;
        background: rgba(0,0,0,0.5);z-index: 50;
    }
    .cs-vfpop{position: absolute;left:50%;top:50%;background: #fff;z-index: 51;
        border-radius: 8px;
        padding-top: 25px;
        width: 80%;
        transform: translate(-50%,-50%);
    }
    .cs-vfpop-title{height: auto;
        line-height: 25px;
        text-align: center;
        font-size: 18px;
        color: #333;
        margin-top: 10px;
        margin-bottom: 10px;
        margin-left: 30px;
        margin-right: 30px;
    }
    .cs-vfpop-main{height: auto;overflow: hidden;margin-left:20px;margin-right: 20px;
        line-height: 22px;color:#969696;font-size: 16px;margin-bottom: 10px;padding-bottom: 30px;
    }

    .cs-vfpop-bottom{height: 50px;overflow: hidden;border-top:1px solid #ddd;}
    .cs-vb-ok{display: block;text-align: center;height: 50px;line-height: 50px;
        font-size: 16px;color:#26a2ff;
    }
    .cs-vm-txt{height: 25px;line-height: 25px;font-size: 16px;color:#999;
        margin-top:5px;margin-bottom: 15px;
    }
    .cs-vm-ipt{height: 26px;overflow: hidden;border:1px solid #dedede;border-radius: 6px;}

    .cs-vm-ipt.err{border:1px solid red;}
    .cvi-tel{height: 24px;margin-top:1px;font-size: 14px;color:#333;text-indent: 10px;
        outline: none;background: none;width: 100%;
    }

    .banner-cs-title{position: absolute;top:0px;left:0px;right:0px;bottom:0px;
        text-align: center;font-size: 20px;color:#fff;line-height: 92px;font-weight: bold;
        text-shadow: 1px 2px 6px #ccc;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;
    }


    .csViewIn {
        background: #fff;
        overflow: auto;
        position: absolute;
        top: 0px;
        left: 0px;
        right: 0px;
        bottom: 0px;
    }

    .cs-header {
        height: auto;
        overflow: hidden;
        margin-left: 16px;
        margin-right: 16px;
        position: relative;
        margin-top: 10px;
        flex-shrink: 0;
    }

    .cs-htel {
        height: 30px;
        line-height: 30px;
        font-weight: 600;
        color: rgba(40, 40, 40, 1);
        font-size: 22px;
    }

    .cs-hname {
        height: 20px;
        font-size: 14px;
        font-weight: 400;
        color: rgba(40, 40, 40, 1);
        line-height: 20px;
        margin-top: 12px;
    }

    .cs-hlocal {
        height: auto;
        font-size: 14px;
        font-weight: 400;
        color: rgba(40, 40, 40, 1);
        line-height: 20px;
        margin-top: 6px;

    }

    .cs-btn-jq {
        width: 50px;
        height: 32px;
        position: absolute;
        top: 0px;
        right: 35px;
        line-height: 32px;
        text-align: center;
        background: rgba(0, 122, 255, 1);
        border-radius: 4px;
        color: #fff;
        border: 1px solid rgba(0, 122, 255, 1);
        font-size: 14px;
    }

    .cs-btn-close {
        width: 20px;
        height: 20px;
        position: absolute;
        right: 0px;
        top: 8px;
        line-height: 20px;
        text-align: center;
    }


    .iconclose {
        color: #B2AEAE;
    }

    .cs-hn-txt {
        display: block;
        float: left;
    }

    .cs-hn-tag {
        display: block;
        float: left;
        width: 57px;
        height: 19px;
        border-radius: 2px;
        border: 1px solid rgba(206, 134, 16, 1);
        text-align: center;
        line-height: 19px;
        color: #CE8610;
        font-size: 12px;
        margin-left: 10px;
    }



    .cs-label-tag {
      display: block;
      float: right;
      width: 57px;
      height: 19px;
      border-radius: 2px;
      text-align: center;
      line-height: 19px;
      color: #CE8610;
      font-size: 12px;
      margin-left: 10px;
      margin-right: 30px;
      border: 1px solid #CE8610;
    }


    .refresh-btn{
        height:30px;
        background:rgba(0,122,255,1);
        border-radius:15px;
        font-size:14px;
        color:#fff;
        display: inline-block;
        padding: 0 12px;
        &.grey-btn{
            background-color: #ccc;
        }
    }
    .cs-meal {
        height: auto;
        margin-left: 16px;
        margin-right: 16px;
        margin-top: 12px;
    }
    .cs-all-views{
      margin: 16px;
      border: 1px solid #D2E8FF;
      font-size: 16px;
      border-radius: 8px;
      .cs-all-views-title{
        background: rgba(231, 242, 253, 1);
        color: rgba(27, 103, 186, 1);
        padding: 10px;
        border-radius: 8px 8px 0px 0px;
        font-weight: bold;
        display: flex;
        align-items: center;
        .title-img{
          width: 25px;
          height: 22px;
        }
      }
      .cs-all-views-content{
        border-radius: 0px 0px 8px 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 25px 15px 25px;
        background: rgba(245, 249, 255, 1);
        .content-title{
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          font-size: 14px;
          font-weight: bold;
          .content-title-img{
            width: 20px;
            height: 20px;
          }
          .content-title-txt{
            margin-top: 5px;
          }
        }
        .content-data{
          display: flex;
          justify-content: space-around;
          align-items: center;
          width: 70%;
          .data-item{
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            .data-item-key{
              color:rgba(98, 102, 129, 1);
              font-size: 10px;
              margin-top: 8px;
            }
            .data-item-val{
              font-weight: 500;
            }
            .red{
              color: rgba(224, 40, 40, 1);
            }
          }
        }
        .content-chart-data{
          display: flex;
          justify-content: space-around;
          align-items: center;
          width: 80%;
          height: 100%;
          .data-item{
            display: flex;
            align-items: center;
            .charts{
              width: 50px;
              height: 50px;
            }
            .item-title{
              color: #626681;
              font-size: 10px;
              margin-left: 8px;
            }
          }
        }
      }
      .div-line{
        height: 1px;
        background: #D2E8FF;
        width: 100%;
      }
    }

    .cs-mcard {
        height: auto;
        position: relative;
        /* background: url(../../assets/img/mealbg.jpg) no-repeat; */
        background-repeat:no-repeat;
        background-size: cover;
        box-shadow: 2px 2px 6px 0px rgba(139, 174, 255, 1);
        border-radius: 9px;
    }

    .cs-mc-name {
        overflow: hidden;
        margin-left: 14px;
        margin-right: 14px;
        line-height: 26px;
        font-size: 14px;
        font-weight: 600;
        color: #fff;
        height:26px;
        background:rgba(74,116,212,1);
        border-radius:13px;
        margin-top: 13px;
        padding: 3px 8px;
        display: inline-block;
    }
    .cs-mc-subprod {
        display: flex;
        font-size:12px;
        font-weight:400;
        color:rgba(255,255,255,1);
        margin-left: 14px;
        margin-right: 14px;
        padding: 4px 0;
    }
    .cs-mc-subprod:first-child{margin-top:4px}

    .cs-mc-info {
        height: 60px;
        margin-left: 14px;
        margin-right: 14px;
        display: flex;
        border-top: 1px solid #9bb3ec;
        margin-top: 8px;
    }

    .cs-mi-lis {
        height: 100%;
        flex-grow: 1;
    }

    .cs-ml-val {
        display: block;
        height: 20px;
        font-size: 14px;
        font-weight: 600;
        text-align: center;
        color: rgba(255, 255, 255, 1);
        line-height: 20px;
        margin-top: 8px;
    }

    .cs-ml-key {
        display: block;
        height: 17px;
        font-size: 12px;
        font-weight: 400;
        text-align: center;
        color: rgba(255, 255, 255, 1);
        line-height: 17px;
        margin-top: 4px;
    }

    .cs-mview {
        height: auto;
        overflow: hidden;
        display: block;
        margin-top: 20px;
        margin-bottom: 20px;
    }

    .cs-mv-li {
        display: block;
        float: left;
        width: 50%;
        border-right: 1px solid #F0F0F0;
        box-sizing: border-box;
        height: auto;
        overflow: hidden;
    }

    .cs-mvl-key {
        height: 17px;
        font-size: 12px;
        font-weight: 400;
        display: block;
        color: rgba(101, 101, 101, 1);
        line-height: 17px;
        margin-top: 5px;
    }

    .cs-mvl-val {
        height: 20px;
        font-size: 14px;
        font-weight: 600;
        display: block;
        color: rgba(82, 82, 82, 1);
        line-height: 20px;
        margin-bottom: 5px;
    }

    .cs-mv-li:nth-child(even) {
        border-right: none;
    }

    .cs-mv-li:nth-child(even) .cs-mvl-key {
        text-indent: 10px;
    }

    .cs-mv-li:nth-child(even) .cs-mvl-val {
        text-indent: 10px;
    }

    .cs-mc-consume {
        width: 90px;
        height: 36px;
        position: absolute;
        top: 0px;
        right: 0px;
        background: #fff;
        border-radius: 0px 3px 3px 32px;
        font-size: 14px;
        font-weight: 400;
        color: rgba(0, 122, 255, 1);
        line-height: 36px;
        text-align: center;
    }

    .cs-vinfo {
        height: auto;
        overflow: hidden;
        margin-top: 10px;
    }

    .cs-vinfo-lis {
        height: auto;
        overflow: hidden;
        border-bottom: 1px solid #F0F0F0;
        padding-bottom: 10px;
    }

    .cs-vinfo-lis:last-child {
        border-bottom: none;
    }

   /*  .cs-vl-tit {
        height: 20px;
        line-height: 20px;
        display: flex;
        justify-content: center;
        margin-top: 10px;
    }

    .cs-vl-iconleft {
        width: 20px;
        height: 20px;
        display: block;
        background: url(../../assets/img/lefttag.png) no-repeat center;
        background-size: 75% 35%;
    }

    .cs-vl-iconrig {
        width: 20px;
        height: 20px;
        display: block;
        background: url(../../assets/img/righttag.png) no-repeat center;
        background-size: 75% 35%;
    }
 */
    .cs-vl-txt {
        width: auto;
        height: 20px;
        font-size: 14px;
        font-weight: 600;
        display: block;
        margin-left: 4px;
        margin-right: 4px;
        color: rgba(0, 122, 255, 1);
    }

    .cs-vl-info {
        height: 50px;
        overflow: hidden;
        margin-left: 16px;
        margin-right: 16px;
        display: flex;
        flex-direction: row;
        margin-top: 10px;
    }

    .cs-vli-lis {
        flex-grow: 1;
        text-align: center;
        height: 50px;
    }

    .cs-vll-val {
        height: 20px;
        font-size: 14px;
        font-weight: 400;
        display: block;
        color: rgba(69, 69, 69, 1);
        margin-top: 2px;
        line-height: 20px;
    }

    .cs-vll-key {
        height: 20px;
        font-size: 12px;
        font-weight: 400;
        display: block;
        margin-top: 2px;
        color: rgba(187, 187, 187, 1);
        line-height: 20px;
    }

    .cs-kd-title {
        height: 20px;
        font-size: 14px;
        margin-top: 2px;
        font-weight: 600;
        color: #3F3F3F;
        line-height: 20px;
        margin-left: 16px;
        margin-right: 16px;
    }

    .cs-kd-title.center {
        text-align: center;
    }

    .cs-kd-info {
        height: auto;
        overflow: hidden;
        margin-left: 16px;
        margin-right: 16px;
        display: block;
    }

    .cs-jt-info {
        height: auto;
        overflow: hidden;
        margin-left: 16px;
        margin-right: 16px;
        display: block;
        margin-top: 6px;
    }

    .cs-kdi-li {
        display: flex;
        height: auto;
        overflow: hidden;
        margin-top: 10px;
    }

    .cs-kdil-left {
        flex-grow: 1;
        overflow:hidden;
    }

    .cs-kdil-rig {
        width: auto;
        height: auto;
        flex-shrink: 0;
    }

    .cs-kr-val {
        height: 20px;
        font-size: 14px;
        font-weight: 400;
        text-align: right;
        color: rgba(123, 123, 123, 1);
        display: block;
        line-height: 20px;
    }

    .cs-kr-key {
        height: 18px;
        font-size: 12px;
        font-weight: 400;
        text-align: right;
        display: block;
        color: rgba(187, 187, 187, 1);
        line-height: 18px;
    }

    .cs-kdl-name {
        height: 18px;
        font-size: 12px;
        font-weight: 400;
        color: rgba(187, 187, 187, 1);
        line-height: 18px;
    }

    .cs-kdl-txt {
        height: 20px;
        font-size: 14px;
        font-weight: 400;
        color: rgba(123, 123, 123, 1);
        line-height: 20px;
        position:relative;
        .cs-kdl-title{
            display: block;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            box-sizing: border-box;
            padding: 0 0 10px 21px;
        }
    }

    .iconkd {
        color: #007AFF;
        position:absolute;
        left:0;
    }

    .cs-kr-key.due {
        color: #F5A623;
    }

    .cs-kk-timeout{color: #F5A623;}

    .cs-vli-btn {
        height: 30px;
        border-radius: 16px;
        /* border: 1px solid rgba(0, 122, 255, 1); */
        border: 1px solid #007aff;
        font-size: 14px;
        font-weight: 400;
        /* color: rgba(0, 122, 255, 1); */
        color:#007aff;
        line-height: 30px;
        text-align: center;
        margin-top: 9px;
        margin-left: 10px;
        margin-right: 10px;
    }

    .cs-fam-li {
        height: 35px;
        line-height: 35px;
        border-bottom: 1px solid #F0F0F0;
        overflow: hidden;
    }

    .cs-fl-left {
        height: 100%;
        width: auto;
        float: left;
        font-size: 14px;
        font-weight: 400;
        color: rgba(115, 115, 115, 1);
    }

    .cs-fl-state {
        width: 60px;
        height: 100%;
        text-align: right;
        font-weight: 400;
        color: rgba(115, 115, 115, 1);
        font-size: 14px;
        float: right;
    }

    .cs-fl-hx {
        width: 30px;
        height: 20px;
        margin-left: 4px;
        background: rgba(245, 166, 35, 1);
        float: left;
        text-align: center;
        border-radius: 3px;
        margin-top: 7px;
        line-height: 20px;
        color: #fff;
        font-size: 12px;
    }

    .business-list-btn {
        height: 40px;
        background: rgba(255, 255, 255, 1);
        position: fixed;
        bottom: 0px;
        width: 100%;
        left: 0px;
        box-shadow: 0px -4px 12px 0px rgba(184, 184, 184, 0.5);
        text-align: center;
    }

    .bs-lb-txt {
        display: inline-block;
        height: 100%;
        line-height: 40px;
        font-size: 14px;
        font-weight: 600;
        color: rgba(112, 112, 112, 1);
    }

    .bs-lb-icon {
        display: inline-block;
        width: 20px;
        height: 20px;
        line-height: 20px;
        color: #007AFF;
        font-size: 14px;
    }

    .cs-banner {
        height: auto;
        border-bottom: 1px solid #F0F0F0;
        border-top: 1px solid #F0F0F0;
    }

    .cs-bannernei {
        margin-left: 16px;
        margin-right: 16px;
        margin-top: 16px;
        overflow: hidden;
        padding-bottom: 30px;
    }

    .cs-businessarea {
        height:auto;
        overflow: hidden;
    }

    .cs-scroll-show {
        height: auto;
        position: fixed;
        top: 0px;
        left: 0px;
        right: 0px;
        border-bottom: 1px solid #007AFF;
        background: #fff;
        padding-bottom: 10px;
        z-index: 10;
        box-shadow: 0px 4px 7px 0px rgba(215, 215, 215, 0.5);
        transform: translate(0px, -130%);
        transition: all .5s linear;
    }

    .cs-scroll-show.active {
        animation: slideIn .5s both linear;
    }

    @keyframes slideIn {
        0% {
            transform: translate(0px, -100%);
        }
        100% {
            transform: translate(0px, 0%);
        }
    }

    @keyframes fadeOut {
        0% {
            opacity: 1;
        }
        100% {
            opacity: 0;
        }
    }

    .cs-main-tariff {
        margin: 0 16px;
        display: flex;
        flex-direction: column;
        .cs-mtariff-li {
            padding: 8px;
            background: #f5f5f5;
            border-radius: 13px;
            margin: 3px 0;
            .cs-mtariff-item {
                padding: 3px 0;
                .cs-mtariff-label {
                    width:42px;
                    height:20px;
                    font-size:14px;
                    font-weight:400;
                    color:rgba(187,187,187,1);
                    line-height:20px;
                    flex-grow: 0;
                    flex-shrink: 0;
                }
                .cs-mtariff-text {
                    height:20px;
                    font-size:14px;
                    font-weight:400;
                    color:rgba(69,69,69,1);
                    line-height:20px;
                    flex-grow: 1;
                    flex-shrink: 1;
                }
            }
        }
    }

    .eject-box{
        position: fixed;
        left: 0;
        bottom: 0;
        top: 0;
        right: 0;
        opacity: 0.5;
        background: #000;
        z-index: 1000;
    }
    .eject-window{
        position: absolute;
        width: 80%;
        left: 10%;
        transform: translateY(-50%);
        top: 40%;
        height:auto;
        z-index: 1001;
        .eject-detail-tittle{
            width: 100%;
            height: auto;
            min-height: 30px;
            font-size: 16px;
            font-weight: 600;
            color: #3F4040;
            text-align: center;
            border-top-right-radius: 10px;
            border-top-left-radius: 10px;
            line-height: 39px;
            background-color: white;
            border-bottom: 1px #E9E9E9 solid;
        }
        .eject-detail-content{
            width: 100%;
            height: auto;
            max-height: 268px;
            background: #fff;
            padding: 10px 10px 17px 10px;
            font-size: 13px;
            line-height: 20px;
            box-sizing: border-box;
            overflow: auto;
        }
        .eject-detail-bottom{
            width: 100%;
            background: #fff;
            font-size: 16px;
            font-weight:400;
            color:rgba(16,142,233,1);
            line-height: 39px;
            text-align: center;
            box-sizing: border-box;
            overflow: auto;
            border-bottom-right-radius: 10px;
            border-bottom-left-radius: 10px;
            border-top: 1px #E9E9E9 solid;
            letter-spacing: 1px;
        }
    }

    .refresh-btn-new{
        width: 60px;
        height: 20px;
        position: absolute;
        right: 25px;
        top: 8px;
        line-height: 20px;
        text-align: center;
        font-size: 14px;
        color: #1E1E1E;
        .freshbtn{
            color: #007AFF;
        }
    }

    .cs-custominfo-date{
      margin-left: 16px;
      margin-right: 16px;
      margin-top: 12px;
      height: auto;
      background-color: #F7F7F7;
      border-radius: 9px;
      .cs-custominfo {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;

        .cs-custominfo-detail {
          width: 60%;

          .cs-custominfo-telnum {
            padding: 20px 0px 5px 20px;
            font-size: 22px;
            font-weight: 600;
            display: flex;

            .cs-custominfo-an {
              background: linear-gradient(to bottom, #FFD861, #FFBE33);
              font-size: 12px;
              color: white;
              border-radius: 3px;
              width: 20px;
              height: 20px;
              line-height: 20px;
              text-align: center;
              font-weight: normal;
              margin-left: 3px;
            }
          }


          .bottom-line {
            display: block;
            width: 100%;
          }

          .cs-userdetail {
            padding: 4px 5px 10px 20px;

            .cs-username {
              font-size: 18px;
              font-weight: 400;
            }

            .cs-hn-tag-new {
              width: 57px;
              height: 19px;
              border-radius: 4px;
              text-align: center;
              line-height: 19px;
              color: #CE8610;
              font-size: 12px;
              border: 1px solid #CE8610;
              padding: 2px 7px;
              margin-left: 4px;
            }
          }
        }


        .cs-cstominfo-qry {
          width: 40%;
          display: flex;
          justify-content: center;
          flex-direction: column;
          align-items: center;
          background-image: url('../../../static/img/new-custom-qrybg.png');
          background-size: cover;

          .new-custom-box {
            display: flex;
            justify-content: center;
            flex-direction: column;
            align-items: center;
            padding: 6px 21px;
            border-radius: 12px;
            background-color: rgba(255, 255, 255, 0.55);
          }

          .new-custom-label {
            width: 29px;
            height: 26px;
          }

          .new-custom-txt {
            color: #2957C1;
            font-size: 12px;
            margin-top: 6px;
          }
        }
      }
      .dotted-line{
        border-top: 1px dashed #e0e0e0;
      }
      .applyDate-line{
        font-size: 12px;
        letter-spacing: 1px;
        color: #838383;
        padding: 10px 20px;
      }
    }

    .fixed-button{
      position:fixed;
      left:84%;
      bottom:65px;
      border-radius:50%;
      background:rgba(0,122,255,0);
      color:#fff;
      text-align:center;
      z-index: 999;
      .gificon{
        width:44px;
        height:44px;
      }
      .gifCart{
        width:54px;
        height:54px;
      }
    }

    .fixed-smart-xin{
      bottom:25px;
    }
     .pad-guide{
       bottom: 90px;
       left: 83%;
       height: 60px;
    }

    .cs-group{
      display: flex;
      margin-left: 16px;
      margin-right: 16px;

     }

    .cs-group-text{
      margin-top: 10px;
      font-size: 14px;
      color: rgba(115, 115, 115, 1);
     }

</style>
