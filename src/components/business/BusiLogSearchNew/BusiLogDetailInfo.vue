<template>
  <div class="wrapper">
    <Header :tsTitleTxt="detailTitle" backType="custom" @emGoPrev="goPrev"></Header>
    <div class="tab-staging wrapper-medias">
      <span :class="{active:showSuccess,'sub-title':!showSuccess}" @click="chooseSuccess">成功</span>
      <span :class="{active:!showSuccess,'sub-title':showSuccess}" @click="chooseFail">失败</span>
    </div>
    <div class="list-container">
      <div class="showContainer" v-show="showSuccess && successList.length > 0" v-for="(successItem,idx) in successList" :key="idx">
        <!-- 成功订单的展示 -->
        <!-- 非购物车部分 -->
        <div class="normal-part">
          <div class="order-card">
            <div class="left-part">
              <span class="common-line" v-show="successItem.msisdn">
                <span class="haoma">号码</span>
                <span class="phoneNum">{{successItem.msisdn}}</span>
              </span>
              <span class="common-line" v-show="successItem.operName">
            <span class="line-title">操作员</span>
            <span class="line-prop">{{successItem.operName}}({{successItem.createOper}})</span>
          </span>
              <!-- 2021/2/3 17:35 新增：有则显示商品名称 -->
              <span class="common-line" v-show="successItem.offername">
            <span class="line-title">商品名称</span>
            <span class="line-prop">{{successItem.offername}}</span>
          </span>
              <span class="common-line"  v-show="successItem.outSrl">
            <span class="line-title">外部流水号</span>
            <span class="line-prop">{{successItem.outSrl}}</span>
          </span>
              <span class="common-line">
            <span class="line-title">支付类型</span>
            <span class="line-prop">{{successItem.payType | payTypeFilter(successItem.payStatus,successItem.busiType)}}</span>
          </span>
              <span class="common-line">
            <span class="line-title">支付金额</span>
            <span class="line-prop">{{successItem.amount/100}}元</span>
          </span>
              <span class="common-line">
            <span class="line-title">受理时间</span>
            <span class="line-prop">{{successItem.createDate}}</span>
          </span>
            </div>
            <div class="right-part">
              <i class="iconfont check-circle iconPos"></i>
              <!-- 2023.7.5 cyy 宽带开通、完美一单增加宽带轨迹查询 -->
              <button class="buttonPos" v-if="successItem.busiType=='mband_prod_kaitong'" @click="searchOrderId(successItem.msisdn)">宽带轨迹</button>
            </div>
          </div>
        </div>
        <!-- 购物车部分 2023.7.5 cyy 购物车增加宽带轨迹查询 -->
        <div class="shopping-part" v-if="successItem.busiType=='shopping_cart'">
          <div class="shopping-part-top" @click="showProdsList(successItem)">
            <span class="shopping-part-title">详 情</span>
            <div class="iconfont jiantouxiangshang iconDown" :class="{'rotate':!successItem.showProds}"></div>
          </div>
          <div class="shopping-part-content" v-if="successItem.showProds && successItem.orderList && successItem.orderList.length != 0" v-for="(item,idx) in successItem.orderList" :key="idx">
            <div class="shopping-left-part">
              <div class="left-content">
                <span class="left-title">商品名称：</span><span class="left-text">{{ item.offerName || '--' }}</span>
              </div>
              <div class="left-content">
                <span class="left-title">业务类型：</span><span class="left-text">{{ item.busiTypeStr || '--' }}</span>
              </div>
            </div>
            <!-- 宽带开通、完美一单可查询宽带轨迹 -->
            <div class="shopping-right-part" v-if="item.busiType=='mband_prod_kaitong'">
              <button class="right-buttonPos" @click="searchOrderId(item.msisdn)">宽带轨迹</button>
            </div>
          </div>
          <div class="no-data" v-show="successItem.showProds && successItem.showNoData">
            <img class="no-data-img" src="static/img/emptybox.png">
            <span class="no-data-text">暂无数据</span>
          </div>
        </div>
      </div>
      <!-- 失败订单的展示 -->
      <div class="order-card" v-show="!showSuccess && failList.length > 0" v-for="(failItem,idx) in failList" :key="idx">
        <div class="left-part">
          <span class="common-line">
            <span class="haoma">号码</span>
            <span class="phoneNum">{{failItem.msisdn}}</span>
          </span>
          <span class="common-line" v-show="failItem.operName">
            <span class="line-title">操作员</span>
            <span class="line-prop">{{failItem.operName}}({{failItem.createOper}})</span>
          </span>
          <span class="common-line" v-show="failItem.offername">
            <span class="line-title">商品名称</span>
            <span class="line-prop">{{failItem.offername}}</span>
          </span>
          <span class="common-line" v-show="failItem.outSrl">
            <span class="line-title">外部流水号</span>
            <span class="line-prop">{{failItem.outSrl}}</span>
          </span>
          <span class="common-line">
            <span class="line-title">支付类型</span>
            <span class="line-prop">{{failItem.payType | payTypeFilter(failItem.payStatus,failItem.busiType)}}</span>
          </span>
          <span class="common-line">
            <span class="line-title">支付金额</span>
            <span class="line-prop">{{failItem.amount/100}}元</span>
          </span>
          <span class="common-line">
            <span class="line-title">受理时间</span>
            <span class="line-prop">{{failItem.createDate}}</span>
          </span>
        </div>
        <div class="right-part">
          <i class="iconfont guanbi1 iconPos"></i>
        </div>
      </div>
    </div>

    <div class="noData" v-show="showNone">
      <NoDataPage tipTxt="暂无数据"></NoDataPage>
    </div>
  </div>
</template>

<script>
  import Header from 'components/common/Header.vue';
  import NoDataPage from 'components/common/NoDataPage';
  import {dateFormat} from '@/base/utils';
  import Storage from '@/base/storage'

  export default{
    components:{Header,NoDataPage},
    data(){
      return{
        detailTitle: '业务日志查询',//默认标题
        showSuccess: true,//是否展示成功订单flag
        showNone: false,//是否展示缺省页
        successList: [],//成功订单的列表
        failList: [],//失败订单的列表
        /*以下为通过route.query透传数据*/
        isToday: false,
        srcFrom: '',
        startDate: '',
        endDate: '',
        typeTotal: {},//业务日志查询总计
        stationId: '',
        isLeader:false,
        orgId:'',
        currentInfo:{},
        crmOrgId:''
      }
    },
    created() {
      //透传数据记录
      this.stationId = Storage.session.get('userInfo').stationId;
      this.orgId=Storage.session.get('userInfo').orgId;
      this.typeTotal = this.$route.query.typeTotal;
      this.currentInfo= this.$route.query.currentInfo;
      if(this.typeTotal.busiName){
        this.detailTitle = this.typeTotal.busiName + '总量';
      }
      this.isToday = this.$route.query.isToday;
      this.srcFrom = this.$route.query.srcFrom;
      this.startDate = this.$route.query.beginDate;
      this.endDate = this.$route.query.endDate;
      this.isLeader=this.$route.query.isLeader;
      this.crmOrgId=this.$route.query.crmOrgId;

    //进入页面获取成功与失败的订单
    if(this.isLeader){
      this.intiOrg();
    }else{
      this.initNorMal();
    }
  },
  methods:{
    //2023.7.5 cyy 展示购物车订单项
    showProdsList(item) {
      if (!item.showProds) {
        //已经加载过一次的不用重复请求
        if (item.hasLoaded) {
          item.showProds = !item.showProds;
          this.$forceUpdate();
          return;
        }
        let req = {
          'orderId':item.orderId,
        };
        let url = `/xsb/gridCenter/businessLog/h5getShoppingCartItem`;
        this.$http.post(url,req).then(res =>{
          let {retCode,retMsg,data} = res.data;
          if('0' == retCode){
            item.orderList = data;
            console.info('orderList',item.orderList)
            item.showProds = !item.showProds;
            item.hasLoaded = true;
            if (data == null || data.length == 0) {
              item.showNoData = true;
            }
            this.$forceUpdate();
            return;
          }else{
            this.$alert(retMsg || "成功订单查询失败");
          }
          this.chooseSuccess();
        }).catch((response) => {
        })
      } else {
        item.showProds = !item.showProds;
        this.$forceUpdate();
      }
    },
    goPrev(){
      this.$router.push({
        path: '/BusiLogIndex',
        query: {
          isToday:this.isToday,
          beginDate:this.$route.query.beginDate,
          endDate:this.$route.query.endDate,
          srcFrom:this.srcFrom
        }
      })
    },
    intiOrg(){
      if(this.typeTotal.busiType !== null && this.typeTotal.busiType !== '' && this.typeTotal.busiType !== undefined){
        this.qryBusiLogSuccessListByOrg();
        this.qryBusiLogFailListByOrg();
      }else{
        this.$alert("未获取到业务类型，无法获取业务日志，请重试")
      }

      },
    initNorMal(){
      this.qryBusiLogSuccessList();
      this.qryBusiLogFailList();
    },
    //选择成功标签时，判断是否缺省
    chooseSuccess(){
      this.showNone = false;
      this.showSuccess = true;
      if(this.successList.length == 0){
        this.showNone = true;
      }
    },
    //选择失败标签时，判断是否缺省
    chooseFail(){
      this.showNone = false;
      this.showSuccess = false;
      if(this.failList.length == 0){
        this.showNone = true;
      }
    },
    //获取成功订单明细
    qryBusiLogSuccessList(){
      let sDate = dateFormat(new Date(this.startDate),'yyyyMMdd');
      let eDate = dateFormat(new Date(this.endDate),'yyyyMMdd');
      let req = {
        'stationId': this.stationId,
        'startDate': sDate,
        'endDate': eDate,
        'busiType': this.typeTotal.busiType,
        'state': '0'
      };
      if(!req.busiType) {
          this.$alert("业务类型【busiType】参数缺失");
          return;
      }
      let url = `/xsb/personBusiness/newbusilogsearch/h5qryNewBusiLogList`;
      this.$http.post(url,req).then(res =>{
        let {retCode,retMsg,data} = res.data;
        if('0' == retCode){
          if(data == null || data.length == 0){//订单列表为空是正常情况，无需提示，需展示缺省页
            this.successList = [];
          }else{
            this.successList = data;
          }
        }else{
          this.$alert(retMsg || "成功订单查询失败");
        }
        this.chooseSuccess();
      }).catch((response) => {
      })
    },
    //获取失败订单明细
    qryBusiLogFailList(){
      let sDate = dateFormat(new Date(this.startDate),'yyyyMMdd');
      let eDate = dateFormat(new Date(this.endDate),'yyyyMMdd');
      let req = {
        'stationId': this.stationId,
        'startDate': sDate,
        'endDate': eDate,
        'busiType': this.typeTotal.busiType,
        'state': '1'
      };
        if(!req.busiType) {
            this.$alert("业务类型【busiType】参数缺失");
            return;
        }
      let reqStr = JSON.stringify(req)
      let url = `/xsb/personBusiness/newbusilogsearch/h5qryNewBusiLogList`;
      this.$http.post(url,req).then(res =>{
        let {retCode,retMsg,data} = res.data;
        if('0' == retCode){
          if(data == null || data.length == 0){//订单列表为空是正常情况，无需提示，需展示缺省页
            this.failList = [];
          }else{
            this.failList = data;
          }
        }else{
          this.$alert(res.data.retMsg || "失败订单查询失败");
        }
      }).catch((response) => {
      })
    },
    qryBusiLogSuccessListByOrg(){
      let sDate = dateFormat(new Date(this.startDate),'yyyyMMdd');
      let eDate = dateFormat(new Date(this.endDate),'yyyyMMdd');
      console.info(this.currentInfo);
      let req = {
        'stationId': this.stationId,
        'chooseStaffId':this.currentInfo.staffId,
        'orgId':this.orgId,
        'startDate': sDate,
        'endDate': eDate,
        'busiType': this.typeTotal.busiType,
        'state': '0',
        'chooseStaffName':this.currentInfo.userName,
        'chooseCrmId':this.currentInfo.crmId,
        'busiName':this.typeTotal.busiName,
        'crmOrgId':this.crmOrgId
      };
      let url = `/xsb/personBusiness/newbusilogsearch/h5qryBusiLogListByOrgId`;
      this.$http.post(url,req).then(res =>{
        let {retCode,retMsg,data} = res.data;
        if('0' == retCode){
          if(data == null || data.length == 0){//订单列表为空是正常情况，无需提示，需展示缺省页
            this.successList = [];
          }else{
            this.successList = data;
          }
        }else{
          this.$alert(retMsg || "成功订单查询失败");
        }
        this.chooseSuccess();
      }).catch((response) => {
      })
    },
    //获取失败订单明细(组织机构)
    qryBusiLogFailListByOrg(){
      let sDate = dateFormat(new Date(this.startDate),'yyyyMMdd');
      let eDate = dateFormat(new Date(this.endDate),'yyyyMMdd');
      let req = {
        'stationId': this.stationId,
        'chooseStaffId':this.currentInfo.staffId,
        'orgId':this.orgId,
        'startDate': sDate,
        'endDate': eDate,
        'busiType': this.typeTotal.busiType,
        'state': '1',
        'chooseStaffName':this.currentInfo.userName,
        'chooseCrmId':this.currentInfo.crmId,
        'busiName':this.typeTotal.busiName,
        'crmOrgId':this.crmOrgId
      };
      let reqStr = JSON.stringify(req)
      let url = `/xsb/personBusiness/newbusilogsearch/h5qryBusiLogListByOrgId`;
      this.$http.post(url,req).then(res =>{
        let {retCode,retMsg,data} = res.data;
        if('0' == retCode){
          if(data == null || data.length == 0){//订单列表为空是正常情况，无需提示，需展示缺省页
            this.failList = [];
          }else{
            this.failList = data;
          }
        }else{
          this.$alert(retMsg || "失败订单查询失败");
        }
      }).catch((response) => {
      })
    },
    //根据手机号查询订单号，宽带开通办理成功的订单需要查看安装轨迹时调用
    searchOrderId(msisdn){
      let sDate = dateFormat(new Date(this.startDate),'yyyyMMdd');
      let eDate = dateFormat(new Date(this.endDate),'yyyyMMdd');
      let stime =  sDate+"000000";
      let etime =  eDate+"235959";
      let url = '/xsb/personBusiness/busilogsearch/h5qryOrder';
      let params = {
        'startDate':stime,
        'endDate':etime,
        'serviceCode':'10002',
        'telnum':msisdn,
      };
      this.$http.post(url, params).then(res => {
        let result = res.data;
        if('0'== result.retCode ) {
          //this.suppList = result.data;
          this.nextPage(result.data);
        } else {
          this.$alert(result.retMsg||'查询订单信息失败');
        }
      }).catch((response) => {
        this.$alert('查询订单信息网络请求失败');
      });
    },
    //宽带开通详细页
    nextPage(orderDetail){
      this.$router.push({
        path: '/busiKuandai',
        query: {
          typeTotal: this.typeTotal,
          beginDate:this.startDate,
          endDate:this.endDate,
          isToday:this.isToday,
          srcFrom: this.srcFrom,
          orderDetail: orderDetail,//订单详情
        }
      })
    }
    },
    filters:{
      payTypeFilter(payType,payStatus,busiType) {
        let payTypeStr = '现金支付';
        if(!payType){
          return payTypeStr;
        }
        try{
          switch (payType) {
            case '1':
              switch (payStatus) {
                case '2':
                  payTypeStr = '电子支付(支付成功)';
                  break;
                case '-1':
                  payTypeStr = '电子支付(支付失败)';
                  break;
                case '4':
                  payTypeStr = '电子支付(已撤销)';
                  break;
                case '5':
                  payTypeStr = '电子支付(已冲正)';
                  break;
                default:
                  if(busiType =='Calyx'){
                    payTypeStr = '电子支付';
                  }else{
                    payTypeStr = '电子支付(待支付)';
                  }
              }
              break;
            case '2': payTypeStr = '话费支付';break;
            default:
          }
        } catch(e) {
          return '现金支付'
        }
        return payTypeStr;
      }
    }
  }
</script>

<style scoped>
.tab-staging{
  display: flex;
  width: 100%;
  height:40px;
  line-height:40px;
  font-size:14px;
  position: fixed;
  top: 44px;
  border-top: 1px solid #EAEAEA;
}

.active{
  text-align:center;
  font-size:14px;
  font-weight:600;
  background:#fff;
  color:#1681FB;
  border-bottom:1px solid #187BEC;
  box-sizing: border-box;
  flex:1;
}

.sub-title{
  text-align:center;
  font-size:14px;
  font-weight:600;
  color:#3D3D3D;
  background:#F8F8F8;
  border-bottom: 1px solid #D8D8D8;
  flex:1;
}
.list-container{
  padding-top: 84px;
}
.showContainer{
  width: 100%;
  display: flex;
  flex-direction: column;

  justify-content: center;

  .shopping-part{
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;

    .shopping-part-top{
      background-color: #FFFFFF;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      line-height: 3;
      color: #187BEC;
      border-bottom: 1px solid #D8D8D8;
      .shopping-part-title{
        font-size: 14px;
        font-weight: 500;
        margin-right: 10px;
      }
      .iconDown{
        font-size: 14px;
      }
    }
    .shopping-part-content:not(:first-child) {


    }
    .shopping-part-content:is(:first-child) {
    }
    .shopping-part-content{
      background-color: #FFFFFF;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      padding: 15px;
      color:rgba(102,102,102,1);
      border-bottom: 1px solid #d2d2d2;
      width:85%;
      border-radius: 5px;
      margin-bottom: 10px;
      margin-top: 5px;
      .shopping-left-part{
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        line-height: 2;
        .left-content{
        //display: flex;
        //justify-content: flex-start;
          .left-title{
            font-weight: bold;
          //width: 90px;
          }
        }

      }
      .shopping-right-part{
        .right-buttonPos{
          border: 1px solid #1680F9;
          border-radius: 15px;
          width: 84px;
          height: 30px;
          background: none;
          color: #1680F9;
          font-size: 13px;
        }
      }
    }
    .no-data{
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      margin-top: 5px;
      margin-bottom: 10px;
      width: 85%;
      border-radius: 8px;
      background-color: #ffffff;
      .no-data-img{
        width: 85%;
      }
      .no-data-text{
        color: #6b6b6b;
        text-align: center;
        width: 85%;
        padding-bottom: 20px;
      }
    }
  }
}

.order-card{
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #ECECEC;
  padding-bottom: 12px;
  padding-top: 4px;
  background-color: #FFFFFF;
}

.left-part{
  display: flex;
  flex-direction: column;
}

.common-line{
  margin-top: 8px;
  margin-left: 15px;
}

.haoma{
  width:32px;
  height:18px;
  border-radius:4px;
  border:1px solid rgba(132,191,255,1);
  padding: 2px 6px 2px 6px;
  font-size:10px;
  font-weight:400;
  color:rgba(72,141,216,1);
  line-height:14px;
}

.phoneNum{
  margin-left: 3px;
  font-size:18px;
  font-weight:500;
  color:rgba(35,35,35,1);
  line-height:25px;
}

.line-title{
  font-size:12px;
  font-weight:400;
  color:rgba(102,102,102,1);
  line-height:17px;
}

.line-prop{
  font-size:14px;
  font-weight:500;
  color:rgba(35,35,35,1);
  line-height:20px;
  margin-left: 5px;
}

.check-circle{
  color: #73D06D;
  font-size: 36px;
}

.guanbi1{
  color: #FF4141;
  font-size: 38px;
}

.right-part{
  padding-top: 60px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.iconPos{
  margin-right: 28px;
  margin-left: 20px;
}

.buttonPos{
  border: 1px solid #1680F9;
  border-radius: 15px;
  width: 84px;
  height: 30px;
  background: none;
  color: #1680F9;
  font-size: 13px;
  margin-right: 12px;
}

.noData{
  margin-top: 20px;
}
</style>
