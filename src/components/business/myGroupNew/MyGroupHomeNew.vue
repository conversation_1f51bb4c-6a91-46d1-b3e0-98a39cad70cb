<template>
    <div class="wrapper">
        <Header tsTitleTxt="集团查询"></Header>
        <div class="member-box">
            <div>
                <div class='belong-content'>
                    <div :class="{'current-type':belongType == 1}" @click='queryGroup(1)'>归属集团<div class="border-line"></div></div>
                    <div :class="{'current-type':belongType == 2}" @click='queryGroup(2)'>全部集团<div class="border-line"></div></div>
                </div>

                <div class="netcom-head">
                    <div class="head-number">
                        <span class="number-name" @click="showsChoose">{{qrType == '1' ? '集团名称' :'集团编号'}}<i class="iconfont shangzhankai-copy"></i></span>
                        <input class="number-input"  v-model="qryGroupName" type="text" :placeholder="qrType=='1'?'集团名称':'集团编号'"/>
                        <span class="iconfont shanchu icon-delete" v-show="qryGroupName" @click.stop="deles"></span>
                        <span class='search-button' @click='queryGroup("")'>搜索</span>
                    </div>
                    <!--                <span class="search-btn" @click="queryGroup">搜索</span>-->
                </div>
            </div>

            <div class="ma-group" v-show="groupList && groupList.length > 0">
                <mt-loadmore
                :top-method="loadTop"
                :bottom-method="loadBottom"
                :bottom-all-loaded="allLoaded"
                :auto-fill="false"
                topPullText="刷新"
                topDropText="释放更新"
                topLoadingText="刷新中···"
                bottomPullText="加载更多"
                bottomDropText=""
                bottomLoadingText="加载中···"
                ref="loadmore"
                bottomDistance="20">
                    <ul>
                        <li class="ma-glis"
                            v-for="(item,index) in groupList"
                            :key="index" @click="goGroupDetail(item)">
                            <div class="ma-gl-groupinfo">
                                <div class="ma-glis-title">
                                    <span class="ma-gt-txt">{{item.groupName}}</span>
                                    <span class="ma-gt-t a-color">{{item.corpscope}}</span>
                                    <span class="ma-gt-t ma-gt-state" v-if='item.regStatus'>{{item.regStatus}}</span>
                                    <span class="ma-gt-t ma-gt-state" v-if='item.status'>{{item.status | getRegStatus}}</span>

                                </div>

                                <div class="ma-gli-info">
                                    <span class="ma-gi-key">集团编号：</span>
                                    <span class="ma-gi-val">{{item.groupId}}</span>
                                </div>
                                <div class="ma-gli-info" v-if='item.address'>
                                    <span class="ma-gi-key">所属区域：</span>
                                    <span class="ma-gi-val">{{item.address}}</span>
                                </div>
                                <div class="ma-gli-info" v-if='item.custManager'>
                                    <span class="ma-gi-key">客户经理：</span>
                                    <span class="ma-gi-val">{{item.custManager}}</span>
                                </div>
                                <!--右侧箭头-->
                                <span class="iconfont youjiantou icon-ma-jt2"></span>
                            </div>
                        </li>
                    </ul>
                    <span class="more-data" v-show="moreData">上拉加载数据</span>
                </mt-loadmore>
            </div>
            <NoDataPage class="nodata" v-show="!groupList || groupList.length < 1" tipTxt="暂无记录"></NoDataPage>
        </div>
    </div>
</template>

<script>
    import Header from 'components/common/Header.vue'
    import NoDataPage from 'components/common/NoDataPage.vue';
    import NlDropdown from 'components/common/NlDropdown/dropdown.js'

    export default {
        components: {Header, NoDataPage,NlDropdown},
        data() {
            return {
                qrType:'1',//判断是否是集团名称还是Id
                qryGroupName: '', //查询集团名称
                qryGroupType: '', // 查询集团类型
                groupList: '',//集团列表
                pageNum: 1,
                allLoaded:false,
                moreData:false,
                zkTypeList:[{id:'1',label:'集团名称'},{id:'2',label:'集团编号'}],
                belongType: 1, // 1:归属集团 2:全部集团
            }
        },
        created() {
            this.pageNum = 1;
            this.getGroupList();
        },
        filters: {
            getRegStatus(val) {
                if (val == 'stcmLtnt') {
                    return '潜在'
                }else if(val == 'stcmNml'){
                    return '在网'
                }else if(val == 'stcmInv'){
                    return '作废'
                }else if(val == 'stcmLost'){
                    return '离网'
                }else if(val == 'stcmPreInv'){
                    return '集团预销户'
                }else{
                    return val
                }
            }
        },
        methods: {
            // 查询集团信息
            getGroupList() {
                if(this.belongType == 2){
                    if (this.qryGroupName != '') {  //判断当前集团搜索输入框中是否存在文字
                        let groupName= '',groupId='';
                        this.qryGroupType = '';
                        if(this.qrType == '1' && this.qryGroupName){
                            groupName = this.qryGroupName;
                        }else if(this.qrType == '2' && this.qryGroupName){
                            groupId = this.qryGroupName;
                        }
                        let url = `/xsb/personBusiness/groupMaintain/h5GetGroupCustInfo?groupName=${groupName}&groupId=${groupId}`
                        this.$http.get(url).then((res) => {
                            let { retCode, retMsg, data } = res.data
                            if (retCode == '0') {  //将查询到的集团存放在数组中
                                this.groupList = data
                            } else {
                                this.groupList = []
                                this.$alert(retMsg || '暂未查到集团数据')
                            }
                        }).catch((response) => {
                        })
                    } else {
                        this.$toast('请输入集团名称')
                    }
                    this.allLoaded = true;
                }else{
                    let groupName= '',groupId='';
                    this.qryGroupType = '';
                    if(this.qrType == '1' && this.qryGroupName){
                        groupName = this.qryGroupName;
                    }else if(this.qrType == '2' && this.qryGroupName){
                        groupId = this.qryGroupName;
                    }else{
                        this.qryGroupType = 'A1';
                    }
                    let url = `/xsb/personBusiness/groupVNet/h5GroupInfoByFsop?groupName=${groupName}&currentPage=${this.pageNum}&corpscope=${this.qryGroupType}&groupId=${groupId}`;
                    this.$http.get(url).then(res => {
                        let {retCode,data,retMsg} = res.data;
                        if (retCode == '0') {
                            let groupListTemp = data;
                            if(!groupListTemp || groupListTemp.length < 1){
                                this.moreData = false;
                                if(this.pageNum > 1) {
                                    this.$toast('没有更多数据');
                                }else{
                                    if (!groupName && !groupId) {
                                        this.$alert('默认查询A类集团，其它集团请根据集团名字模糊搜索！')
                                    }else{
                                        this.$alert('名下搜索不到符合条件的集团，请核实！')
                                    }
                                }
                            }else{
                                this.moreData = true;
                                if (this.pageNum == 1) {
                                    this.groupList = [];
                                    this.groupList = groupListTemp;
                                } else {
                                    this.groupList.push(...groupListTemp);
                                }
                            }
                        } else {
                            this.moreData = false;
                            this.groupList = [];
                            this.$alert(retMsg || '搜索集团失败');
                        }
                    }).catch((response) => {
                        this.moreData = false;
                        this.groupList = [];
                        this.$alert(`搜索集团异常网络连接失败${response}`);
                    });
                }

            },
            //清空
            deles(){
                this.qryGroupName = '';
                this.pageNum = 1;
                this.groupList = [];
                this.getGroupList();
            },
            //点击选择
            showsChoose(){
                NlDropdown({
                    confirmBtn: false,
                    datalist: this.zkTypeList,
                }, (retVal) => {
                    this.qryGroupName = '';
                    this.qrType = retVal.id;
                });
            },
            // 搜索查询
            queryGroup(belongType) {
                if(belongType) {
                    this.belongType = belongType;
                }
                this.pageNum = 1;
                this.groupList = [];
                this.getGroupList();
            },
            // 跳转集团明细
            goGroupDetail(item) {
                this.$router.push({
                    path: '/myGroupDetailNew',
                    query: {
                        linkPhone:item.linkPhone,
                        linkMan:item.linkMan,
                        groupId: item.groupId,
                        custId: item.custId,
                        groupName: item.groupName,
                        groupAddress: item.address,
                        groupType: item.corpscope,
                        custManagerStaff: item.custManagerStaff
                    }
                });
            },
            //上拉刷新
            loadTop() {
                this.$refs.loadmore.onTopLoaded();
                this.pageNum = 1;
                this.getGroupList();
            },
            //下拉加载更多
            loadBottom() {
                this.$refs.loadmore.onBottomLoaded();
                this.pageNum++;
                this.getGroupList();
            }
        }
    }
</script>
<style lang="less" scoped>
    .wrapper{
        height:100%;
        overflow: hidden;
    }

    .member-box{
        display:flex;
        flex-direction: column;
        height:100%
    }

    .belong-content {
        margin-top:44px;
        display: flex;
        justify-content: space-around;
        align-items: center;
        height: 40px;
        background-color: #fff;
        border-top: 1px solid #F0F0F0;
        padding: 0 10%;

        > div {
            position: relative;
            line-height: 36px;
            color:#454545;
            font-weight: 500;
            font-size: 14px;

            &.current-type {
                color: #1D6DDC;

                .border-line {
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: 4px;
                    background: -webkit-gradient(linear, left top, right top, from(#0272FE), to(#5DAEF9));
                    background: linear-gradient(to right, #0272FE, #5DAEF9);
                    margin: 0 auto;
                    border-radius: 27px;
                    width: 80%;
                }
            }


        }

    }

    .netcom-head{
        z-index:100;
        box-shadow:0px 4px 10px 0px rgba(209,209,209,0.5);
        background:#ffffff;
        display:inline-flex;
        padding:10px 12px;
        align-items: center;
        //border-top: 1px solid #F0F0F0;
        box-sizing: border-box;
        width: 100%;
    }

    .head-number{
        height:28px;
        background:#F2F2F2;
        border-radius:8px;
        display:flex;
        flex:1;
        align-items: center;
        position:relative;
        height: 33px;
        line-height: 32px;
        padding: 0 12px;
        position: relative;
        border: 1px solid #9DC2EB;
        border-radius: 5px;
        background-color: #fff;
        .number-name{
            font-size:12px;
            height: 28px;
            line-height: 28px;
            color:#232323;
            padding:0 8px;
            flex-shrink: 0;
            i{
                font-size:14px;
                color:#000000;
                vertical-align:middle;
            }
        }
        .number-input{
            flex:1;
            height:28px;
            -webkit-tap-highlight-color: transparent;
            font-size:12px;
            background:transparent;
            outline:none;
            line-height: 28px;
        }
        .shanchu{
            position: absolute;
            right: 3.5rem;
            top: 50%;
            color: #B2B2B2;
            width: 20px;
            //height: 20px;
            //line-height: 20px;
            transform: translateY(-50%);
            font-size: 12px;
        }
        .search-button{
            font-size: 14px;
            color: #1681FB;
            line-height: 14px;
            padding: 20px 10px;
            position: absolute;
            right: 12px;
        }
    }

    .search-btn{
        margin-left:10px;
        background:#1681FB;
        font-size:14px;
        color:#fff;
        text-align: center;
        width:55px;
        padding:6px 0;
        border-radius:16px;
        border:1px solid rgba(22,129,251,1);
        box-sizing: border-box;
        display: block;
    }

    .ma-group {
        height: auto;
        overflow-y: auto;
        display: block;
        flex-grow: 1;
        -webkit-overflow-scrolling: touch;
    }

    .ma-glis {
        display: block;
        padding-top: 12px;
        overflow: hidden;
        background: #fff;
    }

    .ma-gl-groupinfo {
        position: relative;
        overflow: hidden;
        border-bottom: 1px solid #EAEAEA;
        padding:0 0 8px 16px;
        box-sizing:border-box;
    }

    .ma-glis-title {
        font-size: 16px;
        font-weight: 600;
        color: rgba(0, 122, 255, 1);
        display: flex;
        align-items: center;
    }

    .ma-gt-txt {
        max-width: 74%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        color:#363636;
        font-weight: 600;
    }
    .ma-gt-t{
        padding:4px 8px;
        font-size: 12px;
        border-radius: 6px;
        text-align: center;
        margin-left: 6px;
        display: inline-block;
        color: #fff;
    }

    .ma-gt-state {
        padding:3px 6px !important;
        color: #1680F9;
        border: 1px solid rgba(22, 128, 249, 1);
        box-sizing: border-box;
    }

    .ma-gli-info {
        overflow: hidden;
        margin:8px 45px 0 0;
        display: flex;
    }

    .ma-gi-key {
        width: 75px;
        height: 20px;
        font-size: 14px;
        font-weight: 400;
        color: rgba(174, 174, 175, 1);
        line-height: 20px;
    }

    .ma-gi-val {
        line-height: 20px;
        font-size: 14px;
        color: #484848;
    }

    .icon-ma-jt2 {
        color: #333;
        width: 20px;
        height: 20px;
        font-size:12px;
        text-align: center;
        position: absolute;
        top: 50%;
        right: 16px;
        transform: translateY(-50%);
    }

    .a-color {
        background: #FF4D52;
    }

    .nodata {
        margin-top: 60px;
    }
    .more-data{
        font-size: 12px;
        color: #8a8686;
        text-align: center;
        width: 100%;
        display: block;
        margin: 8px 0;
    }
</style>
