<template>
  <div class='component-wrapper'>
    <Header backType='custom' tsTitleTxt='集团账户查询' @emGoPrev='goPrev'></Header>
    <div class='flexs' v-if='!showGroup'>
      <div>
        <div class='list-wrapper'>
          <div class='group-info' @click='showGroup = true'>{{ groupName }}({{ groupId }})<span
            class='iconfont xingzhuanggangwei'></span></div>

          <div class='account-search'>
            <span class='iconfont sousuo'></span>
            <input class='' placeholder='请输入账户名称或账户编码'  v-model='searchContent'/>
            <div class='choose-range'>
              <span class='all checkAll' @click='search'>搜索</span>
              <!--              <span :class="{'checkAll':!isAll}">欠费</span>-->
            </div>
          </div>
        </div>
        <div v-show='showAccountList && showAccountList.length > 0' class='ma-group'>
          <div>
            <mt-loadmore
              :top-method='loadTop'
              :bottom-method='loadBottom'
              :bottom-all-loaded='allLoaded'
              :auto-fill='false'
              topPullText='刷新'
              topDropText='释放更新'
              topLoadingText='刷新中···'
              bottomPullText='加载更多'
              bottomDropText='释放加载'
              bottomLoadingText='加载中···'
              ref='loadmore'
            >
              <ul class='account-list'>
                <li :class='{"check-item":item.isCheck}' v-for='(item,index) in showAccountList' :key='index'>
                  <div class='content' @click='chooseAccount(item)'>
                    <img v-show='item.isCheck' class='account-img' src='@/assets/img/account.png' />
                    <div>
                      <div class='right-content'>
                        <div class='product-name'>
                          <span class='order-num'>{{ item.accountId }}</span>
                        </div>
                        <div>
                          <span v-show='item.balance < 0' class='status'></span>
                        </div>
                      </div>
                      <div class='people-info'>
                        <span class='title-name'>账户名称：</span>
                        <span class='content-text'>{{ item.accountName }}</span>
                      </div>
                      <div class='people-info' :class='{"content-bottom":!item.showDetail}'>
                        <span class='title-name'>账户余额：</span>
                        <span class='content-text'><span class='money-number'>{{ item.balance && item.balance / 1000
                          }}</span>元</span>
                      </div>
                    </div>
                    <span class='iconfont check' :class="{'checkbox':item.isCheck , 'checkbox11':!item.isCheck}"></span>
                  </div>
                  <div class='account-detail' v-show='item.showDetail'>
                    <ul class='account-month'>
                      <li :class="{'choose-month':chooseIndex == i}" v-for='(el,i) in months' :key='i'
                          @click='chooseMonth(item,el,i)'>
                        <div>
                          <span>{{ el.month }}月</span>
                          <span>{{ el.year }}</span>
                        </div>
                      </li>
                      <li @click="showYearMonthPicker"><img src='@/assets/img/calcel.png' /></li>
                    </ul>
                    <ul class='account-detail' v-if='item.billDetail'>
                      <li class='real-pay'>实际应付金额（元）</li>
                      <li class='real-pay-amount'>{{ item.billDetail.totalFee && item.billDetail.totalFee / 1000 }}</li>
                      <li class='month-detail' @click='showAccountDetail(item)'><span>当月账单详情</span><span
                        class='iconfont youjiantou'></span></li>
                      <li class='pay-item'>
                        本月他人代付金额（元）：<span>{{ item.billDetail.otherPay && item.billDetail.otherPay / 1000
                        }}</span></li>
                      <li class='pay-item'>
                        本月集团代付金额（元）：<span>{{ item.billDetail.groupPay && item.billDetail.groupPay / 1000
                        }}</span></li>
                      <li class='pay-item'>月租费（元)：<span>{{ item.billDetail.rentFee && item.billDetail.rentFee / 1000
                        }}</span></li>
                      <li class='pay-item content-bottom'>
                        活动返费（元）：<span>{{ item.billDetail.giftWrioffFee && item.billDetail.giftWrioffFee / 1000
                        }}</span></li>
                      <li class='submit-history' @click='showHistory(item)'>缴费记录</li>
                    </ul>
                  </div>
                  <img v-show='!item.showDetail' class='xiala' @click='showDetail(item)' src='@/assets/img/xiala.png' />
                  <img v-show='item.showDetail' class='xiala' @click='showDetail(item)' src='@/assets/img/shouqi.png' />

                </li>
              </ul>
              <span class='more-data' v-show='moreData'>上拉加载数据</span>
            </mt-loadmore>
          </div>
        </div>
        <NlButton class='button' enableTip='代 客 充 值' @click='recharge()'></NlButton>

        <NoDataPage v-show='!showAccountList || showAccountList.length < 1' class='nodata'
                    tipTxt='暂无记录'></NoDataPage>
      </div>
    </div>
    <GroupList
      ref='groupListRef'
      v-show='showGroup'
      :onlyShowGroup='true'
      :isBelong='true'
      @chooseGroup='chooseGroup'></GroupList>

  </div>
</template>
<script>
import Header from 'components/common/Header.vue'
import NoDataPage from 'components/common/NoDataPage.vue'
import { dateFormat, chgStrToDate } from '@/base/utils'
import NlDatePicker from 'components/common/NlDatePick/datePicker.js'
import NlButton from 'components/common/NlButton'
import GroupList from 'components/business/jikeBusinessOrder/jikeGroupList.vue'

export default {
  name: 'groupRealNameList',
  components: { NoDataPage, Header, NlDatePicker, NlButton, GroupList },
  filters: {
    getTime(val) {
      if (val.length != 8) {
        return dateFormat(chgStrToDate(val, 'yyyyMMddHHmmss'), 'yyyy-MM-dd hh:mm')
      } else {
        return dateFormat(chgStrToDate(val, true), 'yyyy-MM-dd')
      }
    }
  },
  data() {
    return {
      // 集团列表数据
      groupName: '',
      groupId: '',
      isAll: true,
      accountList: [],
      months: [],
      showAccountList: [],
      hideAccountList: [],
      moreData: true,
      billDetail: {},
      currentMonth: '',
      accountInfo: {},
      showGroup: true,
      chooseIndex: 0,
      allLoaded: false,
      searchContent: '',
      pageIndex: '1'
    }
  },
  created() {
    this.showGroup = true
    const currentDate = new Date()
    const month = currentDate.getMonth() + 1;
    this.currentMonth = currentDate.getFullYear() + '' + (month < 10 ? '0' + month : month);
    console.log(this.currentMonth)
    for (let i = 0; i < 6; i++) {
      const tempDate = new Date(currentDate) // 复制当前日期，避免修改原日期对象
      tempDate.setMonth(tempDate.getMonth() - i) // 设置月份为当前月份减去i个月
      const year = tempDate.getFullYear()
      const month = (tempDate.getMonth() + 1).toString().padStart(2, '0') // 月份从0开始，所以加1并格式化为两位数
      this.months.push({ year: year, month: month })
    }
    if (this.$route.query.groupId) {
      this.groupName = this.$route.query.groupName
      this.groupId = this.$route.query.groupId
      this.showGroup = false
      this.qryGroupAccountList()
    }

  },
  mounted: function() {

  },
  methods: {
    showYearMonthPicker() {
      NlDatePicker({
        startDate: new Date(),
        onlyOne: true,
        tsMinDate: new Date('2001/01/01'),
				tsMaxDate: new Date(),
        format:'yyyy-MM'
      }, (retVal) => {
        console.log(retVal)
        this.startDate = retVal.startDate;
      });
    },
    // 返回上一级
    goPrev() {
      this.$router.push('/groupAccountMenu')
    },
    showDetail(item) {
      let flag = !item.showDetail
      this.showAccountList.forEach(item => this.$set(item, 'showDetail', false))
      this.$set(item, 'showDetail', flag)
      if (!item.billDetail) {
        this.getBillDetailList(item, this.currentMonth)
        this.getBillBal(item, this.currentMonth)
      }
    },
    chooseAccount(item) {
      let flag = !item.isCheck
      this.showAccountList.forEach(item => this.$set(item, 'isCheck', false))
      this.$set(item, 'isCheck', flag)
      if (flag) {
        this.accountInfo = item
      }
    },
    chooseMonth(item, el, i) {
      this.currentMonth = el.year + '' + el.month
      this.chooseIndex = i
      this.getBillDetailList(item, this.currentMonth)
    },
    showHistory(item) {
      this.$router.push({
        path: '/groupRechargeList',
        query: {
          accountInfo: item,
          groupName: this.groupName,
          groupId: this.groupId
        }
      })
    },
    search() {
      let data = this.hideAccountList
      if (!this.searchContent) {
        this.showAccountList = data.slice(0, 5)
        console.log(this.showAccountList)
      } else {
        this.showAccountList = this.hideAccountList.filter(item => {
          return item.accountId == this.searchContent || ~item.accountName.indexOf(this.searchContent)
        })
        console.log(this.showAccountList)

      }
    },
    //查询账号
    qryGroupAccountList() {
      let url = `/xsb/personBusiness/groupAccount/h5GroupAccountListByGroupId?groupId=${this.groupId}`
      this.$http.get(url).then(async res => {
        let { retCode, retMsg, data } = res.data
        if (retCode == '0') {
          if (data) {
            let accounts = data.slice(0, 5)
            let balances = await this.queryGroupBalance(accounts)
            if (balances) {
              balances.forEach(item => {
                for (let i = 0; i < accounts.length; i++) {
                  if (item.accountId == accounts[i].accountId) {
                    accounts[i].balance = item.balance
                  }
                }
              })
            }
            this.showAccountList = accounts
            this.hideAccountList = data
            if (this.hideAccountList && this.hideAccountList.length > 0) {
              this.moreData = true
              this.allLoaded = false
            }
          }
        } else {
          this.accountList = []
          this.$alert(retMsg || '查询集团账户列表失败')
        }
      }).catch(e => {
        this.$alert(e || '查询集团账户列表网络请求失败，请重试')
      })
    },
    //查询账号
    queryGroupBalance(accounts) {
      let accountIds = accounts.map(account => account.accountId).join(',')
      let param = {
        groupId: this.groupId,
        accountIds: accountIds,
        isDeductDebt: '1',
        subjectType: '4'
      }
      let url = `/xsb/gridCenter/zqGroup/h5queryGroupBalance`
      return this.$http.post(url, param).then(res => {
        let { retCode, retMsg, data } = res.data
        if (retCode == '0') {
          return data
        } else {
          this.$alert(retMsg || '查询集团账户列表失败')
          return null
        }
      }).catch(e => {
        this.$alert(e || '查询集团账户列表网络请求失败，请重试')
        return null
      })
    },
    //查询余额流动
    qryBalanceFlow(item, month) {
      let param = {
        accountId: item.accountId,
        cycle: month,
        qryType: '0'
      }
      this.$http.post('/xsb/personBusiness/groupAccount/h5BalanceFlowLogList', param).then(res => {
        if (res.data.retCode == '0') {
          this.$set(item, 'billDetail', data[0])
          this.balanceFlowList = res.data.data
        } else {
          this.balanceFlowList = []
          this.$alert(res.data.retMsg)
        }
      })
    },
    getBillBal(item, month) {
      let url = '/xsb/personBusiness/groupAccount/h5BillBalInfo'
      let param = {
        acctId: item.accountId,
        cycle: month
      }
      this.$http.post(url, param).then(res => {
        if (res.data.retCode == '0') {
          this.$set(item, 'billBal', res.data.data.balanceDetailList[0])
        } else {
          // this.$toast(res.data.retMsg || '账户结余查询失败');
        }
      })
    },
    getBillDetailList(item, month) {
      let param = {
        acctId: item.accountId,
        subsid: '',
        startCycle: month,
        endCycle: month,
        isBadDebt: '1',
        qryType: '2',
        billLevel: '3',
        billType: '1'
      }
      this.$http.post('/xsb/personBusiness/groupAccount/h5BillDetailList', param).then(res => {
        let { retCode, retMsg, data } = res.data
        if (retCode == '0') {
          this.$set(item, 'billDetail', data[0])
          this.$set(item, 'billDetail1', data[1])
          this.$set(item, 'billDetail2', data[2])
        } else {
          this.billDetail = []
          this.$alert(res.data.retMsg)
        }
      })
    },
    async loadTop() {
      // 加载更多数据
      this.$refs.loadmore.onTopLoaded()
      this.pageIndex = 1
      this.qryGroupAccountList()
    },
    async loadBottom() {
      // 加载更多数据
      this.$refs.loadmore.onBottomLoaded()
      this.pageIndex++
      console.log(this.pageIndex, 5 * (this.pageIndex - 1))
      let accounts = this.hideAccountList.slice(5 * (this.pageIndex - 1), 5 * (this.pageIndex - 1) + 5)
      if (accounts && accounts.length > 0) {
        let balances = await this.queryGroupBalance(accounts)
        if (balances) {
          balances.forEach(item => {
            for (let i = 0; i < accounts.length; i++) {
              if (item.accountId == accounts[i].accountId) {
                accounts[i].balance = item.balance
              }
            }
          })
        }
        this.showAccountList.push(...accounts)
        if (this.hideAccountList && this.hideAccountList.length > 0 && this.showAccountList.length < this.hideAccountList.length) {
          this.moreData = true
          this.allLoaded = false
        }
      } else {
        this.$toast('暂无更多数据')
        this.moreData = false
        this.allLoaded = true
      }
    },
    recharge() {
      // this.$toast('该功能暂未上线')
      // return
      if (!this.accountInfo.accountId) {
        this.$toast('请选择账户')
        return
      }
      this.accountInfo.groupName = this.groupName
      this.accountInfo.groupId = this.groupId
      this.$router.push({
        path: '/groupRecharge',
        query: {
          accountInfo: this.accountInfo
        }
      })
    },
    showAccountDetail(item) {
      console.log(item)
      this.$router.push({
        path: '/groupAccountDetail',
        query: {
          accountInfo: item,
          groupName: this.groupName,
          groupId: this.groupId,
          srcFrom: '/groupAccountQuery'
        }
      })
    },
    chooseGroup(group) {
      if (group.groupId) {
        this.showGroup = !this.showGroup
        this.groupName = group.groupName
        this.groupId = group.groupId
        this.qryGroupAccountList()
      } else if (!group.groupId) {
        this.goPrev()
      }
    }
  }


}
</script>
<style lang='less' scoped>
.component-wrapper {
  overflow: hidden;
  height: 100%;
  background-color: #fff;

  /deep/ .gl-title-txt {
    font-weight: 500;
  }
}

.flexs {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #FFFFFF;

  > div {
    height: 100%;
  }

  .list-wrapper {
    margin-top: 52px;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #454545;
    line-height: 16px;
    font-style: normal;
    text-transform: none;
    border-top: 1px solid #ccc;
    padding: 20px 20px 5px;
    background: linear-gradient(180deg, #F7FBFF 0%, #FFFFFF 100%);
    box-shadow: 0px 3px 10px 0px rgba(86, 125, 244, 0.05);
    border-radius: 0px 0px 0px 0px;

    .group-info {
      font-size: 16px;
      color: #2B486C;
      line-height: 20px;
      text-align: left;
      margin-bottom: 18px;
      font-weight: 500;

      .iconfont {
        margin-left: 9px;
        font-size: 14px;
        color: #0079FD;
      }
    }

    .account-search {
      background: #FFFFFF;
      border-radius: 4px 4px 4px 4px;
      border: 1px solid #9DC2EB;
      padding: 8px 5px;
      height: 22px;
      line-height: 22px;

      .sousuo {
        float: left;
        margin: 0 5px 0 9px;
        line-height: 22px;
      }

      ::-webkit-input-placeholder { /* WebKit browsers */
        color: #BBBBBB;
      }

      :-ms-input-placeholder { /* Internet Explorer 10+ */
        color: #BBBBBB;
      }

      input {
        font-size: 14px;
        color: #474545;
        border: none;
        outline: none;
      }

      span {
        line-height: 14px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        color: #ACACAC;

        &.checkAll {
          color: #1681FB;
        }
      }

      .all {
        margin-right: 11px;

        &:before {
          content: '';
          position: absolute;
          width: 1px;
          height: 15px;
          right: 33px;
          top: 5px;
          background: #D8D8D8;
        }
      }

      .choose-range {
        display: inline-block;
        position: relative;
        float: right;
      }
    }

    .choose-time {
      background-color: #fff;
      padding: 10px 10px;
      font-size: 14px;
      border-radius: 8px;
      //color: rgba(0, 0, 0, 0.45);
      line-height: 20px;
      display: flex;
      align-items: baseline;
      justify-content: space-around;
      color: #26a2ff;
      font-size: 15px;
      color: #1D6DDC;
      line-height: 16px;
      text-align: center;
      font-style: normal;
      text-transform: none;


      .split-span {
        font-size: 16px;
        color: #cdcdcd;
      }

      .filter-button {
        color: #ed8a1d;
      }

      .filter-number {
        margin-left: -3px;
        position: absolute;
        color: white;
        font-size: 8px;
        background-color: #ff8117;
        top: 82px;
        text-align: center;
        border-radius: 24px;
        padding: 4px;
        white-space: nowrap;
        display: inline-block;
        height: 4px;
        line-height: 4px;
      }

      .group-name {
        color: #565656;
        font-size: 14px;
        width: 64%;
        text-indent: 0.55rem;
        outline: none;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      img {
        width: 12px;
        vertical-align: text-top;
      }
    }

    .sum-order-content {
      height: 32px;
      background-color: #fff;
      padding: 12px 10px 0 10px;
      border-radius: 10px;
    }

    .sum-order-filter {
      display: flex;
      justify-content: space-between;
      width: 100%;
      white-space: nowrap;


    }

    .split-line {
      border-bottom: 1px dashed #B0DEF0;
      margin: 10px 5px 15px 5px;
    }

    .sum-order-data {
      display: flex;
      justify-content: space-between;
      margin: 0 10px;

      .title-name {
        font-size: 14px;
        color: #565656;;
        line-height: 19px;

        padding-bottom: 12px;
        width: 33%;
        text-align: center;

        &.choose {
          color: #26a2ff;
        }

        .border-line {
          bottom: 0;
          left: 0;
          right: 0;
          height: 4px;
          background: -webkit-gradient(linear, left top, right top, from(#0272FE), to(#5DAEF9));
          background: linear-gradient(to right, #0272FE, #5DAEF9);
          margin: 0 auto;
          border-radius: 27px;
          width: 35%;
          position: relative;
          top: 4px;
        }
      }

      .content-text {
        color: #404040;
        line-height: 19px;
      }

      .sum-number {
        font-size: 12px;
        color: #0E64C2;
        line-height: 31px;
        text-align: left;
        font-style: normal;
        text-transform: none;

        span {
          font-size: 24px;
          margin-right: 2px;

        }
      }

    }

  }


  .search-btn {
    background: #1681FB;
    color: #fff;
    text-align: center;
    width: 55px;
    border-radius: 16px;
    border: 1px solid #1681fb;
    box-sizing: border-box;
    position: absolute;
    float: right;
    top: 230px;
    right: 30px;
    font-size: 13px;
    line-height: 24px;
    font-style: normal;
    text-transform: none;
  }

  .ma-group {
    height: 100%;
    overflow-y: auto;
    display: block;
    flex-grow: 1;
    -webkit-overflow-scrolling: touch;
    padding: 0px 20px 10px;
    font-size: 14px;
    line-height: 20px;
    background-color: #FAFAFA;

    .account-list {
      display: flex;
      flex-direction: column;
      margin-bottom: 220px;

      > li {
        margin: 10px 0;
        display: flex;
        background: #fff;
        border-radius: 8px;
        padding: 10px;
        align-items: baseline;
        position: relative;
        box-sizing: border-box;
        width: 100%;
        line-height: 24px;
        //overflow: scroll;
        box-shadow: 0px 0px 8px 0px #eaeaea;
        flex-direction: column;
        overflow: hidden;

        &.check-item {
          box-shadow: 0px 0px 8px 0px rgba(167, 167, 167, 0.2312);
          border-radius: 8px 8px 8px 8px;
          border: 1px solid #0079FD;
          background: #EFF4FF;
        }

        .content {
          display: flex;
          flex-direction: row;
          align-items: baseline;
          width: 100%;

          > div {
            width: 100%;
          }
        }

        .account-detail {
          width: 100%;
          margin-top: 10px;
        }

        .xiala {
          width: 10px;
          left: calc(50% - 10px);
          padding: 10px;
          position: absolute;
          bottom: 0px;
        }

        .account-month {
          display: flex;
          justify-content: space-between;
          align-items: center;
          line-height: 16px;
          padding: 10px 0 6px;
          border-bottom: 1px solid #EEEEEE;

          > li {
            padding: 4px;

            span {
              display: block;
              text-align: center;
            }

            span:first-child {
              font-size: 14px;
              color: #3D3D3D;
            }

            span:last-child {
              font-size: 10px;
              color: #9E9E9E;
            }

            &.choose-month {
              color: #ffffff !important;
              background-color: #0079FD;
              border-radius: 5px;

              span:first-child {
                font-size: 14px;
                color: #fff;
              }

              span:last-child {
                font-size: 10px;
                color: #fff;
              }
            }


          }
        }

        .account-detail {
          position: relative;

          .real-pay {
            font-size: 12px;
            color: #3D3D3D;
            line-height: 14px;
          }

          .real-pay-amount {
            font-size: 20px;
            color: #0079FD;
          }

          .pay-item {
            font-size: 12px;
            color: #929292;
            line-height: 18px;

            span {
              color: #5A7597;
            }
          }

          .month-detail {
            position: absolute;
            right: 0px;
            font-size: 12px;
            top: 10px;
            color: #0079FD;

            span:last-child {
              font-size: 8px;
              vertical-align: top;
              margin-left: 2px;
            }
          }

          .submit-history {
            background: #0079FD;
            border-radius: 144px 144px 144px 144px;
            color: #fff;
            position: absolute;
            right: 0px;
            bottom: 0px;
            font-size: 12px;
            padding: 1px 8px;
          }
        }
      }

      .account-img {
        width: 20px;
        margin-right: 10px;
      }

      .blue-circle {
        flex: 0 0 6px;
        background: #4B74FF;
        border-radius: 50px;
        margin-right: 3px;
        height: 6px;
        position: relative;
        bottom: 2px;
      }

      .content-bottom {
        margin-bottom: 10px;
      }

      .people-info {

        width: 90%;
        display: flex;

        > div {
          white-space: nowrap;
        }


        .split {
          width: 1px;
          height: 10px;
          background-color: #bcb2b2;
          display: inline-block;
          margin: 0 0.02rem;
        }

        .title-name {
          color: #979797;
          white-space: nowrap;
          //color: #2e2e2e;
        }

        .content-text {
          color: #404040;
        }

        .money-number {
          color: #0079FD;
          margin-right: 2px;
        }

        span {
          font-size: 14px;
          //color: #2e2e2e;
          color: #747474;
        }

        a {
          text-decoration: none;
          display: inline-block;

          .gl-sublist-dasval-tel {
            color: #26a2ff;
            font-size: 14px;
            line-height: 20px;
            height: auto;
            display: block;
            text-align: justify;
            border: none;
          }
        }

        .xingzhuang7 {
          font-size: 13px;
          color: #26a2ff;
        }
      }

      .right-content {
        display: flex;
        flex-direction: row;
        width: 100%;
        justify-content: space-between;
        margin-bottom: 2px;

        > span {
          font-size: 12px;
          color: #8d8d8d;
        }

        .status::before {
          content: "已欠费";
          position: absolute;
          top: 0;
          right: 0;
          background: red;
          color: white;
          font-size: 12px;
          padding: 6px 20px 0px;
          transform: rotate(45deg) translate(20%, -65%);

          &.done {
            color: #79d668;
            background-color: #e1fff0;
          }
        }


        .product-name {
          font-size: 14px;
          color: #404040;
          line-height: 13px;
          display: flex;
          align-items: center;

          .order-num {
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            font-size: 16px;
            color: #007AFF;
            line-height: 19px;
            text-align: left;
            font-style: normal;
            text-transform: none;
          }


        }

        .sum-number {
          font-size: 12px;
          color: #4D4D4D;
          line-height: 25px;

          span :first-child {
            font-size: 23px;
            font-family: inherit;

          }
        }

        .total-number {
          font-size: 12px;
          color: #969696;
          line-height: 12px;
          text-align: left;

        }


      }
    }


    .ma-glis.active .icon-ma-jt2 {
      transform: rotate(-180deg);
      color: #007AFF;
    }

    .ma-glis.active {
      display: block;
    }

    .more-data {
      font-size: 12px;
      color: #8a8686;
      text-align: center;
      width: 100%;
      display: block;
      margin: 8px 0;
    }
  }

  .nodata {
    margin-top: 10px;
  }
}

.share-img {
  position: absolute;
  width: 18px;
  right: 10px;
  top: 46%;
}


/deep/ .mint-spinner-snake {
  height: 11px !important;
  width: 11px !important;
  border-style: solid;
  border-width: 2px;
  display: inline-block;
}

.order-count {
  font-weight: 400;
  font-size: 12px;
  color: #9E9E9E;
  line-height: 20px;
  font-style: normal;
  text-transform: none;
  background-color: #fbfbfb;
  padding: 5px;
  text-align: center;
}

.you {
  top: 50%;
  color: #5882F5;
  font-size: 16px;
  background-color: #e9f4ff;
  border-radius: 50%;
  position: absolute;
  right: 4%;
  width: 18px;
  height: 18px;
  text-align: center;
  line-height: 18px;
}

.check {
  position: absolute;
  top: 35px;
  right: 20px;
  color: #0079FD;
  font-size: 20px;

  &.checkbox11 {
    color: #929292;
  }
}

.button {
  box-shadow: 0px -4px 12px 0px #ECF0FA;
  border-radius: 0px 0px 0px 0px;

  /deep/ .primary {
    border-radius: 5px 5px 5px 5px;
    margin: 0 6%;
    width: 88%;
  }
}

.back-img {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0.5;
  z-index: 2000;
  background: #000;
}
</style>
