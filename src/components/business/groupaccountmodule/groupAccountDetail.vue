<template>
  <div class='component-wrapper'>
    <Header backType='custom' tsTitleTxt='集团账户明细' @emGoPrev='goPrev'></Header>
    <div class='flexs'>
      <div>
        <div class='list-wrapper'>
          <img src='static/img/account-bg.png' />
          <div>
            <div class='group-info'>{{ groupName }}({{ groupId }})</div>
            <div class='group-account'>账户编码：{{ accountInfo.accountId }}</div>
            <div class='group-account'>账户名称：{{ accountInfo.accountName }}</div>
            <div class='group-account' v-show='accountInfo.balance'>账户余额(元)：{{ accountInfo.balance  / 1000}}</div>
            <div class='current-month'><span class='iconfont xingzhuanggangwei'></span>{{ currentMonth }}</div>
          </div>
        </div>
        <div class='code-content'>
          <div class='code-title'>总金额(元)：<span>{{ accountInfo.billDetail && accountInfo.billDetail.totalFee / 1000
            }}</span>
            <img src='static/img/rng-icon.png' /></div>
          <div class='code-content'>
            <div class='pay-type'>
              本月他人代付金额(元)：<span>{{ accountInfo.billDetail && accountInfo.billDetail.otherPay / 1000 }}</span></div>
            <div class='pay-type'>
              本月集团代付金额(元)：<span>{{ accountInfo.billDetail && accountInfo.billDetail.groupPay / 1000 }}</span></div>
            <div class='pay-type'>月租费(元)：<span>{{ accountInfo.billDetail && accountInfo.billDetail.rentFee / 1000 }}</span>
            </div>
          </div>
        </div>

        <div class='code-content'>
          <div class='code-title'>账单明细(元)：<img src='static/img/rng-icon.png' /></div>
          <div class='bill-title'>
            <div class='item-name'>费用项</div>
            <div class='item-total'>原价</div>
            <div class='item-free'>减免</div>
            <div class='item-fact'>实际消费</div>
          </div>
          <div class='bill-split'></div>
          <div class='bil-content'>
            <div class='bill-item' v-for='(item,index) in accountInfo.billDetail.feeDetailList.slice(0,1)' :key='index'>
              <div class='item-name'>{{ item.feeName }}<span class='iconfont a-bianzu143x' style='    font-size: 12px;
    margin-left: 2px;
    color: #bdbdbd;'></span></div>
              <div class='item-total'>{{ addAmount(item.fee,item.disc) / 1000 }}</div>
              <div class='item-free'>{{ item.disc / 1000 }}</div>
              <div class='item-fact'>{{ item.fee / 1000 }}</div>
            </div>

          </div>

        </div>
        <div v-show='dd'>
          <div>
            <div class='internet-tv-detail'></div>
            <div class='internet-window'>
              <div class='internet-detail-tittle'>费用项明细</div>
              <div class='internet-datail-list' >
                <div class='bill-title' style='    display: flex
;
    flex-direction: row;'>
                  <div class='item-name' style='padding:0px;'>科目</div>
                  <div class='item-name' style='padding:0px;width: 193%;'>费用项</div>
                  <div class='item-total' style='padding:0px'>原价</div>
                  <div class='item-free' style='padding:0px'>减免</div>
                  <div class='item-fact' style='padding:0px'>实际消费</div>
                </div>
                <div class='bill-split'></div>
                <div class='bil-content' style='padding: 0px;'>
                  <div class='bill-item' v-for='(item,index) in accountInfo.billDetail.feeDetailList' :key='index' style='display: flex
;
    flex-direction: row;'>
                    <div class='item-name' style='padding:0px;width: 24%;    font-size: 12px !important;'>{{ index == 0 ? '一级' : (index == 1 ? '二级' : '三级') }}</div>
                    <div class='item-name' style='padding:0px;     display: inline-block;   font-size: 12px !important;'>{{ index == 2 ? '企业宽带300M酒店悦亨增强套餐(上限40条)' : item.feeName }}
                      <span
                        class='iconfont a-bianzu143x'
                        @click='toggleDetail(index)'
                        style='font-size: 12px; margin-left: 2px; color: #bdbdbd;'
                        v-if='2 === index'>
        </span>
                      <!-- Add this popup description -->
                    </div>
                    <div class='item-total' style='padding:0px;    font-size: 12px !important;'>{{ addAmount(item.fee,item.disc) / 1000 }}</div>
                    <div class='item-free' style='padding:0px;    font-size: 12px !important;'>{{ item.disc / 1000 }}</div>
                    <div class='item-fact' style='padding:0px;    font-size: 12px !important;'>{{ item.fee / 1000 }}</div>
                  </div>

                </div>
                <div
                  v-if='true'
                  class='detail-popup'
                  style='position: absolute;
    background: white;
    padding: 8px;
    border: 1px solid rgb(238, 238, 238);
    box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 8px;
    z-index: 100;
    margin-top: 5px;
    width: 72%;
    margin: 0 auto;
    margin-left: 66px;
          '>
                  服务内容包含:企业宽带(300M)20条，和商务TV20套，云电脑2核4G
                </div>
              </div>
              <div class='internet-detail-bottom' @click='dd=false'>关闭</div>
            </div>
          </div>
        </div>
        <NlButton class='button' enableTip='代 客 充 值' @click='recharge()'></NlButton>
      </div>
    </div>
  </div>
</template>
<script>
import Header from 'components/common/Header.vue'
import NoDataPage from 'components/common/NoDataPage.vue'
import { dateFormat, chgStrToDate } from '@/base/utils'
import NlDatePicker from 'components/common/NlDatePick/datePicker.js'
import Storage from '@/base/storage'
import NlButton from 'components/common/NlButton'
import QRCode from '@/base/qrcode.js'

export default {
  name: 'groupRealNameList',
  components: { NoDataPage, Header, NlDatePicker, NlButton },
  filters: {
    getTime(val) {
      if (val.length != 8) {
        return dateFormat(chgStrToDate(val, 'yyyyMMddHHmmss'), 'yyyy-MM-dd hh:mm')
      } else {
        return dateFormat(chgStrToDate(val, true), 'yyyy-MM-dd')
      }
    }
  },
  data() {
    return {
      accountInfo: {},
      payTypeList: [
        { 'iconfont': 'zhifubao', 'payType': 'ALIPAY', 'txt': '支付宝支付', id: '4' },
        { 'iconfont': 'weixinzhifu', 'payType': 'WECHAT', 'txt': '微信支付', id: '3' },
        { 'iconfont': 'yidonghebaozhifu', 'payType': 'CMPAY', 'txt': '和包支付', id: '5' },
        { 'iconfont': 'py_yinlian', 'payType': 'QUICKPAY', 'txt': '银联云闪付', id: '7' }
      ],
      currentPayType: { 'iconfont': 'zhifubao', 'payType': 'ALIPAY', 'txt': '支付宝支付' },
      payAmount: '',
      payUrl: '',
      step: 1,
      payType: '4',
      uinfo: {},
      orderId: '',
      currentMonth: '',
      groupName: '',
      groupId: '',
      billDetail2: {},
      dd:true,
      activeDetailIndex: null,

    }
  },
  created() {
    this.accountInfo = this.$route.query.accountInfo
    console.log(this.accountInfo)
    this.groupId = this.$route.query.groupId
    this.groupName = this.$route.query.groupName
    this.uinfo = Storage.session.get('userInfo')
    const currentDate = new Date()
    this.currentMonth = dateFormat(new Date(new Date() - 30 * 24 * 60 * 60 * 1000), 'yyyy/MM')   //获取月份
    console.log(this.currentMonth)
    // this.queryGroupaccountInfo()
  },
  methods: {
    // 返回上一级
    goPrev() {
      if (this.$route.query.srcFrom == '/groupAccountQuery') {
        this.$router.push({
          path: this.$route.query.srcFrom,
          query: {
            groupId: this.$route.query.groupId,
            groupName: this.$route.query.groupName
          }
        })
      } else if (this.$route.query.srcFrom == '/groupDeptDetail') {
        this.$router.push('/groupDeptList')
      } else {
        history.go(-1)
      }
    },
      toggleDetail(index) {
          this.activeDetailIndex = this.activeDetailIndex === index ? null : index
      },
      showDetail() {
          this.dd = false
          this.$messagebox.alert('企业宽带300M酒店悦享计出套餐（上限20条）', '明细描述').then((action) => {

          })
      },
    recharge() {
      // this.$toast('该功能暂未上线')
      // return
      let account = {
        groupName: this.groupName,
        groupId: this.groupId,
        accountId: this.accountInfo.accountId,
        accountName: this.accountInfo.accountName,
        accountInfo: this.accountInfo
      }

      this.$router.push({
        path: '/groupRecharge',
        query: {
          account: this.accountInfo,
          accountInfo: account,
          srcFrom: '/groupAccountQuery'
        }
      })
    },
    queryGroupaccountInfo() {
      let param = {
        acctId: this.accountInfo.accountId,
        subsid: '',
        startCycle: this.currentMonth,
        endCycle: this.currentMonth,
        isBadDebt: '1',
        qryType: '2',
        billLevel: '1',
        billType: '1'

      }
      let url = `/xsb/personBusiness/groupAccount/h5BillDetailList`
      this.$http.post(url, param).then(res => {
        let { retCode, retMsg, data } = res.data
        if (retCode == '0') {
          this.accountInfo = data
        } else {
          this.$alert(retMsg || '查询集团账户列表失败')
        }
      }).catch(e => {
        this.$alert(e || '查询集团账户列表网络请求失败，请重试')
      })
    },
    addAmount(amount1, amount2) {
      return parseInt(amount1) + parseInt(amount2)
    }

  }

}
</script>
<style lang='less' scoped>
.component-wrapper {
  overflow: hidden;
  height: 100%;

  /deep/ .gl-title-txt {
    font-weight: 500;
  }
}

.flexs {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #FFFFFF;

  > div {
    height: 100%;
    background: #F4F8FB;
  }

  .list-wrapper {
    margin-top: 52px;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #454545;
    line-height: 16px;
    font-style: normal;
    text-transform: none;
    border-top: 1px solid #ccc;
    padding: 20px;
    background: linear-gradient(315deg, #69ACEC 0%, #0D5DF6 100%);
    display: flex;
    align-items: center;
    padding: 30px 10px 15px 0px;
    justify-content: center;

    > div {
      line-height: 30px;
      color: #fff;
    }

    .group-info {
      font-size: 16px;
      font-weight: 500;
    }

    .group-account {
      font-size: 14px;
    }

    .current-month {
      background: rgba(255, 255, 255, 0.22);
      border-radius: 171px 171px 171px 171px;
      width: 80px;
      padding: 3px 10px;
      line-height: 20px;
      margin: 4px 0;

      .xingzhuanggangwei {
        font-size: 10px;
        margin-right: 5px;
        vertical-align: top;
      }

      spam {
        margin-right: 2px
      }
    }

    .account-search {
      background: #FFFFFF;
      border-radius: 4px 4px 4px 4px;
      border: 1px solid #9DC2EB;
      padding: 8px 5px;
      height: 22px;
      line-height: 22px;

      .sousuo {
        float: left;
        margin: 0 5px 0 9px;
        line-height: 22px;
      }

      ::-webkit-input-placeholder { /* WebKit browsers */
        color: #BBBBBB;
      }

      :-ms-input-placeholder { /* Internet Explorer 10+ */
        color: #BBBBBB;
      }

      input {
        font-size: 14px;
        color: #474545;
        border: none;
        outline: none;
      }

      span {
        line-height: 14px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        color: #ACACAC;

        &.checkAll {
          color: #1681FB;
        }
      }

      .all {
        margin-right: 11px;

        &:before {
          content: '';
          position: absolute;
          width: 1px;
          height: 15px;
          right: 33px;
          top: 5px;
          background: #D8D8D8;
        }
      }

      .choose-range {
        display: inline-block;
        position: relative;
        float: right;
      }
    }

    .choose-time {
      background-color: #fff;
      padding: 10px 10px;
      font-size: 14px;
      border-radius: 8px;
      //color: rgba(0, 0, 0, 0.45);
      line-height: 20px;
      display: flex;
      align-items: baseline;
      justify-content: space-around;
      color: #26a2ff;
      font-size: 15px;
      color: #1D6DDC;
      line-height: 16px;
      text-align: center;
      font-style: normal;
      text-transform: none;


      .split-span {
        font-size: 16px;
        color: #cdcdcd;
      }

      .filter-button {
        color: #ed8a1d;
      }

      .filter-number {
        margin-left: -3px;
        position: absolute;
        color: white;
        font-size: 8px;
        background-color: #ff8117;
        top: 82px;
        text-align: center;
        border-radius: 24px;
        padding: 4px;
        white-space: nowrap;
        display: inline-block;
        height: 4px;
        line-height: 4px;
      }

      .group-name {
        color: #565656;
        font-size: 14px;
        width: 64%;
        text-indent: 0.55rem;
        outline: none;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      img {
        width: 12px;
        vertical-align: text-top;
      }
    }

    .sum-order-content {
      height: 32px;
      background-color: #fff;
      padding: 12px 10px 0 10px;
      border-radius: 10px;
    }

    .sum-order-filter {
      display: flex;
      justify-content: space-between;
      width: 100%;
      white-space: nowrap;


    }

    .split-line {
      border-bottom: 1px dashed #B0DEF0;
      margin: 10px 5px 15px 5px;
    }

    .sum-order-data {
      display: flex;
      justify-content: space-between;
      margin: 0 10px;

      .title-name {
        font-size: 14px;
        color: #565656;;
        line-height: 19px;

        padding-bottom: 12px;
        width: 33%;
        text-align: center;

        &.choose {
          color: #26a2ff;
        }

        .border-line {
          bottom: 0;
          left: 0;
          right: 0;
          height: 4px;
          background: -webkit-gradient(linear, left top, right top, from(#0272FE), to(#5DAEF9));
          background: linear-gradient(to right, #0272FE, #5DAEF9);
          margin: 0 auto;
          border-radius: 27px;
          width: 35%;
          position: relative;
          top: 4px;
        }
      }

      .content-text {
        color: #404040;
        line-height: 19px;
      }

      .sum-number {
        font-size: 12px;
        color: #0E64C2;
        line-height: 31px;
        text-align: left;
        font-style: normal;
        text-transform: none;

        span {
          font-size: 24px;
          margin-right: 2px;

        }
      }

    }

  }

  .amount-add {
    background: #FFFFFF;
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.05);
    border-radius: 8px 8px 8px 8px;
    margin: 12px;
    padding: 16px;

    .amount-title {
      font-size: 14px;
      color: #3D3D3D;
    }

    .amount-content {
      border-bottom: 1px solid #D8D8D8;
      padding: 10px 0;
      margin: 10px 0 20px;

      img {
        vertical-align: middle;
      }

      input {
        outline: none;
        font-size: 14px;
      }

      input::-webkit-input-placeholder {
        color: #BBBBBB;
      }
    }

    .pay-title {
      font-size: 14px;
      color: #3D3D3D;
    }

    .amount-tip {
      font-size: 10px;
      color: #FC7F04;
    }
  }

  .search-btn {
    background: #1681FB;
    color: #fff;
    text-align: center;
    width: 55px;
    border-radius: 16px;
    border: 1px solid #1681fb;
    box-sizing: border-box;
    position: absolute;
    float: right;
    top: 230px;
    right: 30px;
    font-size: 13px;
    line-height: 24px;
    font-style: normal;
    text-transform: none;
  }

  .ma-group {
    height: 100%;
    overflow-y: auto;
    display: block;
    flex-grow: 1;
    -webkit-overflow-scrolling: touch;
    padding: 0px 20px 10px;
    font-size: 14px;
    line-height: 20px;
    background-color: #FAFAFA;

    .account-list {
      display: flex;
      flex-direction: column;
      margin-bottom: 182px;

      > li {
        margin: 10px 0;
        display: flex;
        background: #fff;
        border-radius: 8px;
        padding: 10px;
        align-items: baseline;
        position: relative;
        box-sizing: border-box;
        width: 100%;
        line-height: 24px;
        //overflow: scroll;
        box-shadow: 0px 0px 8px 0px #eaeaea;
        flex-direction: column;
        overflow: hidden;

        &.check-item {
          box-shadow: 0px 0px 8px 0px rgba(167, 167, 167, 0.2312);
          border-radius: 8px 8px 8px 8px;
          border: 1px solid #0079FD;
          background: #EFF4FF;
        }

        .content {
          display: flex;
          flex-direction: row;
          align-items: baseline;
          width: 100%;

          > div {
            width: 100%;
          }
        }

        .account-detail {
          width: 100%;
          margin-top: 10px;
        }

        .xiala {
          width: 10px;
          left: calc(50% - 10px);
          padding: 10px;
          position: absolute;
          bottom: 0px;
        }

        .account-month {
          display: flex;
          justify-content: space-between;
          align-items: center;
          line-height: 16px;
          padding: 10px 0 6px;
          border-bottom: 1px solid #EEEEEE;

          > li {
            span {
              display: block;
              text-align: center;
            }

            span:first-child {
              font-size: 14px;
              color: #3D3D3D;
            }

            span:last-child {
              font-size: 10px;
              color: #9E9E9E;
            }

            img {

            }
          }
        }

        .account-detail {
          position: relative;

          .real-pay {
            font-size: 12px;
            color: #3D3D3D;
            line-height: 14px;
          }

          .real-pay-amount {
            font-size: 20px;
            color: #0079FD;
          }

          .pay-item {
            font-size: 12px;
            color: #929292;
            line-height: 18px;

            span {
              color: #5A7597;
            }
          }

          .month-detail {
            position: absolute;
            right: 0px;
            font-size: 12px;
            top: 10px;
            color: #0079FD;

            span:last-child {
              font-size: 8px;
              vertical-align: top;
              margin-left: 2px;
            }
          }

          .submit-history {
            background: #0079FD;
            border-radius: 144px 144px 144px 144px;
            color: #fff;
            position: absolute;
            right: 0px;
            bottom: 0px;
            font-size: 12px;
            padding: 1px 8px;
          }
        }
      }

      .account-img {
        width: 20px;
        margin-right: 10px;
      }

      .blue-circle {
        flex: 0 0 6px;
        background: #4B74FF;
        border-radius: 50px;
        margin-right: 3px;
        height: 6px;
        position: relative;
        bottom: 2px;
      }

      .content-bottom {
        margin-bottom: 10px;
      }

      .people-info {

        width: 90%;
        display: flex;

        > div {
          white-space: nowrap;
        }


        .split {
          width: 1px;
          height: 10px;
          background-color: #bcb2b2;
          display: inline-block;
          margin: 0 0.02rem;
        }

        .title-name {
          color: #979797;
          white-space: nowrap;
          //color: #2e2e2e;
        }

        .content-text {
          color: #404040;
        }

        .money-number {
          color: #0079FD;
          margin-right: 2px;
        }

        span {
          font-size: 14px;
          //color: #2e2e2e;
          color: #747474;
        }

        a {
          text-decoration: none;
          display: inline-block;

          .gl-sublist-dasval-tel {
            color: #26a2ff;
            font-size: 14px;
            line-height: 20px;
            height: auto;
            display: block;
            text-align: justify;
            border: none;
          }
        }

        .xingzhuang7 {
          font-size: 13px;
          color: #26a2ff;
        }
      }

      .right-content {
        display: flex;
        flex-direction: row;
        width: 100%;
        justify-content: space-between;
        margin-bottom: 2px;

        > span {
          font-size: 12px;
          color: #8d8d8d;
        }

        .status::before {
          content: "已欠费";
          position: absolute;
          top: 0;
          right: 0;
          background: red;
          color: white;
          font-size: 12px;
          padding: 6px 20px 0px;
          transform: rotate(45deg) translate(20%, -65%);

          &.done {
            color: #79d668;
            background-color: #e1fff0;
          }
        }


        .product-name {
          font-size: 14px;
          color: #404040;
          line-height: 13px;
          display: flex;
          align-items: center;

          .order-num {
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            font-size: 16px;
            color: #007AFF;
            line-height: 19px;
            text-align: left;
            font-style: normal;
            text-transform: none;
          }


        }

        .sum-number {
          font-size: 12px;
          color: #4D4D4D;
          line-height: 25px;

          span :first-child {
            font-size: 23px;
            font-family: inherit;

          }
        }

        .total-number {
          font-size: 12px;
          color: #969696;
          line-height: 12px;
          text-align: left;

        }


      }
    }


    .ma-glis.active .icon-ma-jt2 {
      transform: rotate(-180deg);
      color: #007AFF;
    }

    .ma-glis.active {
      display: block;
    }

    .more-data {
      font-size: 12px;
      color: #8a8686;
      text-align: center;
      width: 100%;
      display: block;
      margin: 8px 0;
    }
  }

  .nodata {
    margin-top: 10px;
  }
}

.share-img {
  position: absolute;
  width: 18px;
  right: 10px;
  top: 46%;
}


/deep/ .mint-spinner-snake {
  height: 11px !important;
  width: 11px !important;
  border-style: solid;
  border-width: 2px;
  display: inline-block;
}

.order-count {
  font-weight: 400;
  font-size: 12px;
  color: #9E9E9E;
  line-height: 20px;
  font-style: normal;
  text-transform: none;
  background-color: #fbfbfb;
  padding: 5px;
  text-align: center;
}

.you {
  top: 50%;
  color: #5882F5;
  font-size: 16px;
  background-color: #e9f4ff;
  border-radius: 50%;
  position: absolute;
  right: 4%;
  width: 18px;
  height: 18px;
  text-align: center;
  line-height: 18px;
}

.check {
  position: absolute;
  top: 35px;
  right: 20px;
  color: #0079FD;
  font-size: 20px;

  &.checkbox11 {
    color: #929292;
  }
}

.pay-button {
  width: 100%;
  box-shadow: 0px -4px 12px 0px #ECF0FA;
  border-radius: 0px 0px 0px 0px;
  background: #fff;
  height: 76px;
  left: 0;
  right: 0;
  bottom: 0px;
  position: fixed;

  .button-div {
    width: 50%;

    &:last-child {
      left: auto;
    }

    /deep/ .primary {
      border-radius: 5px 5px 5px 5px;
    }
  }


}

.button {
  box-shadow: 0px -4px 12px 0px #ECF0FA;
  border-radius: 0px 0px 0px 0px;

  /deep/ .primary {
    border-radius: 5px 5px 5px 5px;
    margin: 0 6%;
    width: 88%;
  }
}

.bandwidthPay {
  margin-top: 19px;
}

.bandwidthPay li {
  display: flex;
  justify-content: space-between;
  background: #fff;
  font-size: 14px;
  color: #646464;
  line-height: 37px;

  .xianjin {
    color: #F5A623;
  }

  .xianshangzhifu {
    color: #3AC280;
  }

  .huafeizhifu {
    color: #007AFF;
  }

  .weixinzhifu {
    color: #3AC280;
  }

  .zhifubao {
    color: #1296DB;
  }

  .yidonghebaozhifu {
    color: #D3136F;
  }

  .luyouqi {
    color: brown;
  }
}

.bandwidthPay li i {
  margin-right: 10px;
  font-size: 16px;
}

.radio .checkboxround0 {
  color: #979797
}

.radio .checkboxround1 {
  color: #1680F9
}

.bil-content {
  background: #FFFFFF;
  border-radius: 20px 20px 20px 20px;
  position: relative;
  padding: 10px 0px 10px 40px;

  .bill-item {
    border-radius: 20px 20px 0px 0px;
    height: 22px;
    font-size: 12px;
    color: #3D3D3D;
    display: flex;
    text-align: center;
    margin-bottom: 8px;
    line-height: 12px;

    .item-name {
      width: 30%;
      text-align: left;
    }

    .item-total {
      width: 20%;
      color: #0079FD;
    }

    .item-free {
      width: 20%;
      color: #0079FD;
      color: #E46942;

    }

    .item-fact {
      width: 20%;
      color: #0079FD;

    }
  }

}

.code-content {
  margin: 20px;
  background: #FFFFFF;
  border-radius: 20px 20px 20px 20px;
  position: relative;

  &.previous-month {
    .code-title {
    }
  }

  .bill-split {
    border-bottom: 1px solid rgba(0, 121, 253, 0.19);
    margin-top: 4px;
  }

  .bill-title {
    border-bottom: 1px solid rgba(0, 121, 253, 0.19);
    font-size: 12px;
    color: #3D3D3D;
    padding: 10px 0px 10px 40px;
    display: flex;
    text-align: center;


    .item-name {
      text-align: left;
      width: 30%
    }

    .item-total {
      width: 20%

    }

    .item-free {
      width: 20%;

    }

    .item-fact {
      width: 20%
    }

  }


  .code-title {
    background: linear-gradient(257deg, #58A2FC 0%, rgba(255, 255, 255, 0) 100%);
    padding: 16px;
    border-radius: 20px 20px 0px 0px;

    span {
      color: #0079FD;
      margin-top: 4px;
    }

    img {
      position: absolute;
      right: 24px;
      width: 20px;
    }

  }

  .code-content {
    .pay-type {
      border-radius: 20px 20px 0px 0px;
      height: 22px;
      font-size: 14px;
      font-size: 12px;
      color: #3D3D3D;
      margin-left: 23px;

      span {
        position: absolute;
        right: 0px;
        color: #5A7597;
      }
    }


    .code {
      display: flex;
      justify-content: center;
    }
  }

  .pay-status {
    text-align: center;
    font-size: 12px;
    color: #0079FD;
    line-height: 14px;
    padding-bottom: 20px;

    span {
      width: 5px;
      height: 8px;
      color: #0079FD;
    }
  }
}


.internet-window {
  position: fixed;
  top: 43%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  width: 77%;
  height: 43%;
  background-color: #fff;
  z-index: 1001;
  border-start-start-radius: 10px;
  border-start-end-radius: 10px;

  .internet-detail-tittle {
    width: 100%;
    height: 35px;
    font-size: 15px;
    font-weight: 400;
    color: #3f4040;
    text-align: center;
    border-top-right-radius: 10px;
    border-top-left-radius: 10px;
    line-height: 35px;
    background-color: white;
    border-bottom: 1px #e9e9e9 solid;
  }

  .internet-detail-bottom {
    width: 100%;
    background: #fff;
    font-size: 16px;
    font-weight: 400;
    color: #3699ff;
    line-height: 35px;
    text-align: center;
    border-top: 1px #e9e9e9 solid;
    bottom: 0px;
    position: relative;
    padding: 5px 0px;
    border-end-end-radius: 10px;
    border-end-start-radius: 10px;
  }

  .internet-datail-list {
    overflow: scroll;
    height: 80%;
    padding: 3px 0px;

    div {
      width: 100%;
      height: auto;
      background: #fff;
      font-size: 14px;
      box-sizing: border-box;
      padding: 5px 20px 0px 20px;
      color: #868686;
      overflow: hidden;
      line-height: 17px;
      display: flex;
      flex-direction: column;

      span:first-child {
        flex-shrink: 0;
        color: #949292
      }

      span {
        color: #5c5c5c;
        line-height: 19px;
      }
    }
  }
}

.internet-tv-detail {
  position: fixed;
  left: 0;
  bottom: 0;
  top: 0;
  right: 0;
  opacity: 0.5;
  background: #000;
  z-index: 1000;
}
</style>
