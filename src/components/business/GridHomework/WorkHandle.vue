<template>
  <div class="wrapper" v-show='pageShow'>
      <div class="headtop wrapper-medias">
          <div class="gl-title">
              <!--后退按钮-->
              <span class="iconfont zuojiantou gl-back" @click="goback"></span>
              <!--标题-->
              <span class="gl-title-txt">任务处理</span>
              <!--右侧文字按钮-->
              <span class="iconfont yiban txt-btn" v-show="taskType == '22'" @click="operTrans">转派</span>
          </div>

      </div>

    <div class="line"></div>
    <div class="tabar">
      <div :class="{choosed:isShow=='1'}" @click="isShows(1)">任务详情</div>
      <div :class="{choosed:isShow=='2'}" @click="isShows(2)">任务流转</div>
    </div>
    <div class="detail" v-show="isShow=='1'">
      <div class="title">
        <span class="log"></span>
        <span class="text">基本信息</span>
      </div>
      <div class="content" >
        <div>
          <span class="ques">创建人员：</span>
          <span class="answ">{{msg.operName}}</span>
        </div>
        <div>
          <span class="ques">创建时间：</span>
          <span class="answ">{{msg.operDate}}</span>
        </div>
        <div>
          <span class="ques">标题：</span>
          <span class="answ">{{msg.taskName}}</span>
        </div>
        <div>
          <span class="ques">类型：</span>
          <span class="answ">{{msg.taskType | changeTaskType}}</span>
        </div>
        <div  v-show="msg.taskType && msg.taskType != '22'">
          <span class="ques">内容：</span>
          <!--          <span class="answ">{{msg.operCont}}</span>-->
          <div class="adaptCont" v-show="msg.operCont != '' || sTempId != ''">
              <!--待修改先写样式-->
            <p v-html="msg.operCont" v-show="sTempId == ''"></p>
              <div v-show="sTempId != ''">
                  <div  style="font-size:14px"
                        class="sm-mlis"
                        v-for="(item,index) in sTemplateDetail.titleList"
                        :key="index">
                      <div class="questionc">{{index+1}}.{{item.titleName}}</div>
                      <TemplateInfoMatch
                          :tsFormType="item.putType"
                          :tsKey="item.tempKey"
                          :tsValue="item.tempValue"
                          :tsTempValueCheck="item.tempValueCheck"
                          :tsTempKeyCheck="item.tempKeyCheck"
                      ></TemplateInfoMatch>
                  </div>
              </div>
          </div>
        </div>
        <div :class="{'dataClass' :taskType == 9 || taskType == 12 }">
          <div class="ques" >执行时间段：</div>
          <div class="answ answt">{{msg.startDate}}～{{msg.endDate}}</div>
        </div>
      </div>
        <div style="padding: 10px 0;text-align: center"  v-show="msg.taskType == '6' || msg.taskType == '7' || msg.taskType == '8' || msg.taskType == '20' ||msg.taskType == '21'">
            <div class="work-bottom" :class="{active:workStatus == '1'}" @click="goBusiWork">详情</div>
        </div>

        <div class="title" v-show="taskType == '22'">
            <span class="log"></span>
            <span class="text">任务介绍</span>
        </div>
        <div class="task-introduce" v-show="taskType == '22' && msg.operCont">
            {{msg.operCont}}
        </div>


        <div v-show="objectList.length > 0 ">
            <div class="title-table" >
                <div>
                    <span class="log"></span>
                    <span class="text">产能分析</span>
                </div>
                <div>
                    <span class="tasknum">任务总产能<span class="num">{{allCnt}}</span></span>

                </div>

            </div>

            <div class="table-box">
                <table border="1" class="table-wrap">
                    <thead class="head-line">
                    <tr>
                        <th width="22%">对象名称</th>
                        <th width="15%">总量</th>
                        <th width="15%">新入网</th>
                        <th width="15%">电视新增</th>
                        <th width="15%">宽带新增</th>
                        <th width="18%">FTTR新增</th>
                    </tr>
                    </thead>
                    <tbody class="table-body">
                    <tr v-for="(item, index) in objectList" :key="index">
                        <td width="22%">{{ item.objectName}}</td>
                        <td width="15%">{{ item.cntBusImport}}</td>
                        <td width="15%">{{ item.cntBusUserNew}}</td>
                        <td width="15%">{{ item.cntBusKdNew}}</td>
                        <td width="15%">{{ item.cntBusTvNew}}</td>
                        <td width="18%">{{ item.cntBusFttrNew}}</td>
                    </tr>
                    </tbody>
                </table>

            </div>

        </div>


      <div class="title" v-if="taskType != '9' && taskType != '12' && taskType != '22'">
        <span class="log"></span>
        <span class="text">处理</span>
      </div>

      <div class="dealTitle" v-if="taskType == '11' || taskType == '14'">
        <span class="left"></span>
        <span class="right">已关联计划</span>
      </div>

      <div class="lists" v-if="taskType == '11' || taskType == '14'">
        <div class="list" v-for="items in planList" :key="items.planId">
          <div class="companyName">
            <span>{{ items.planName }}</span>
            <span class="status1 status" v-if="items.finishStatus == '1'">待补充</span>
            <span class="status2 status" v-if="items.finishStatus == '2'">待执行</span>
            <span class="status3 status" v-if="items.finishStatus == '3'">已执行</span>
            <span class="status4 status" v-if="items.finishStatus == '4'">已失效</span>
          </div>
          <div>指定执行时间：{{ items.planStartdate }}</div>
          <div class="persons">指定执行人：{{ items.planPerson }}</div>
<!--          <div class="detail" @click="toPlanCarry(items)">详情 ></div>-->
          <div class="remove" @click="unbound(items)">
            <span class="iconfont click"></span>
            <span>解绑定</span>
          </div>
        </div>
      </div>

      <div class="dealTitle" v-if="taskType == '11' || taskType == '14'">
        <span class="left"></span>
        <span class="right">去关联计划</span>
      </div>

      <div class="plan" v-if="taskType == '11' || taskType == '14'">
        <span class="planFunc left" @click="newPlan"> <span class="iconfont jiahao2 icon" ></span>制定计划</span>
        <span class="planFunc right" @click="relativePlan"><span class="iconfont guanlian icon" ></span>关联已有计划</span>
      </div>

      <div class="dealTitle" v-if="taskType != '9'&& taskType != '12' && taskType != '22'">
        <span class="left"></span>
        <span class="right">去反馈</span>
      </div>

      <div  style="font-size:14px"
            class="sm-mlis"
            v-for="(item,index) in sixMustDo.smList"
            :key="index"
            @click="getCurrMustDo(item,index)"
            v-if="taskType != '9' && taskType != '12' && taskType != '22'">
            <div class="questionc">
              {{index+1}}.{{item.titleName}}
              <span v-show="item.tempOption == 2" class="isMust">*</span>
            </div>
            <AlienNetWorkMustDo
              :tsFormType="item.putType"
              :tsKey="item.tempKey"
              :tsValue="item.tempValue"
              :tsIsShowIpt="item.isInput"
              :tsDate="item.planDate"
              :tsMustUserValue="item.mustUserValue"
              :tsMustUserInput="item.mustUserInput"
              :tsMustFlag="item.mustFlag"
              :dakatext="dakatext"
              :isPhoto="isPhoto"
              @emCheckCk="smCheckCk"
              @emSmCheckIptChange="smCheckIptChange"
              @emRadioCk="radioCk"
              @emSmRadioIptChange="radioIptChange"
              @emSelectChange="selectChange"
              @emSmSlideIptChange="selectIptChange"
              @emInptChange="inputChange"
              @emChangeDate="emChangeDate"
              @emDaka="dakaClick"
              @emPhoto="photoClick"
            ></AlienNetWorkMustDo>
          </div>

        <div  class="feed-wrapper" v-if="feedBackFlag">
            <Header :tsTitleTxt="feedBackName" backType="custom" @emGoPrev="goPrevWork"></Header>
            <div class="feed-info">
                <div  class="sm-mlis"
                      v-for="(item,index) in sixMustDo.smList"
                      :key="index"
                      @click="getCurrMustDo(item,index)">
                    <div class="questionc">
                        {{index+1}}.{{item.titleName}}
                        <span v-show="item.tempOption == 2" class="isMust">*</span>
                    </div>
                    <AlienNetWorkMustDo
                        :tsFormType="item.putType"
                        :tsKey="item.tempKey"
                        :tsValue="item.tempValue"
                        :tsIsShowIpt="item.isInput"
                        :tsDate="item.planDate"
                        :tsMustUserValue="item.mustUserValue"
                        :tsMustUserInput="item.mustUserInput"
                        :tsMustFlag="item.mustFlag"
                        :dakatext="dakatext"
                        :isPhoto="isPhoto"
                        @emCheckCk="smCheckCk"
                        @emSmCheckIptChange="smCheckIptChange"
                        @emRadioCk="radioCk"
                        @emSmRadioIptChange="radioIptChange"
                        @emSelectChange="selectChange"
                        @emSmSlideIptChange="selectIptChange"
                        @emInptChange="inputChange"
                        @emChangeDate="emChangeDate"
                        @emDaka="dakaClick"
                        @emPhoto="photoClick"
                    ></AlienNetWorkMustDo>
                </div>
            </div>

            <div class="feed-bottom">
                <div class="feed-bottom-dd" @click="feedbackSec">反馈</div>
            </div>
        </div>


      <div class="button wrapper-medias"  v-show="taskType != '22'">
          <div @click="transfer">转派</div>
        <div @click="feedBack" v-if="taskType == 9 || taskType == 12">已阅</div>
        <div @click="feedBack" v-else>反馈</div>
      </div>


    </div>
    <div class="turning" v-show="isShow=='2'">
      <div class="flows">
        <div class="flow" v-for="items in flow" :key='items.activeId' v-show="items.activeId!='4' && items.activeId!='5'">
          <div class="log" v-if="items.activeId=='1'">
            <img src="static/img/work-bc.png" class="score-icon">
            <div class="explain-icon">{{items.activeId | changeActiveId}}</div>
          </div>
          <div class="log" v-else-if="items.activeId=='2'">
            <img src="static/img/work-fb.png" class="score-icon">
            <div class="explain-icon">{{items.activeId | changeActiveId}}</div>
          </div>
          <div class="log" v-else-if="items.activeId=='3'">
            <img src="static/img/work-sh.png" class="score-iconsh">
            <div class="explain-iconsh">{{items.activeId | changeActiveId}}</div>
          </div>
          <div class="msg">
            <div class="operName">{{items.operName}}</div>
            <div class="operOrgid">{{items.operOrgName}}</div>
          </div>
          <div class="date">{{items.operDate}}</div>
        </div>
      </div>
        <div class="feedbacks">
            <div class="feedback" v-for="items in flow" :key='items.activeId' v-show="items.activeId=='5'">
                <div class="head">
                    <div>
                        <span class="operName">{{items.operName}}</span>
                        <span class="fk" style="color: #007AFF;border: 1px solid #007AFF">转派</span>
                    </div>
                    <div class="orgidDate">
                        <div class="operOrgid">{{items.operOrgName}}</div>
                        <div class="date">{{items.operDate}}</div>
                    </div>
                </div>
                <div style="padding:0 12px 12px;font-size: 12px">
                    <span>下一执行人：{{items.turntoName}}</span>
                </div>
            </div>
        </div>
      <div class="feedbacks">
        <div class="feedback" v-for="items in flow" :key='items.activeId' v-show="items.activeId=='4'">
          <div class="head">
            <div>
              <span class="operName">{{items.operName}}</span>
              <span class="fk">反馈</span>
            </div>
            <div class="orgidDate">
              <div class="operOrgid">{{items.operOrgName}}</div>
              <div class="date">{{items.operDate}}</div>
            </div>
          </div>
          <div class="standard" v-for="(item,index) in items.titleList" :key=index>
            <div class="qus">{{index+1}}、{{item.titleName}}：</div>
            <div class="answ" v-if="item.putType != '6' && item.putType != '7'">{{item.tempKey}}</div>
              <div class="answ" v-if="item.putType == '6'">{{item.tempKey | localText}}</div>
              <div class="answ" v-if="item.putType == '7'">{{item.tempKey | photoText}}</div>
          </div>
        </div>
      </div>
    </div>
    <mt-datetime-picker type="datetime" ref="yuyueDate" v-model="dateval" @confirm="formatDateVal"></mt-datetime-picker>
      <div class="camera-wrap" v-show="showCameraFlg">
          <WorkHandlePhoto :showCameraFlg="showCameraFlg"
                           @emClose="getClose"
                           @emSign="getClose"></WorkHandlePhoto>
      </div>
      <TransferDetail ref="executeRef" v-if="transferFlag" @emCloseInit="closeExecute" @emSubmit="getExecute" :orgaId="orgaIds" :headerName="'转派人员选择'"></TransferDetail>
      <div class="message-show">
          <div class="message-box" v-show="isShowMessageBox">
              <div class="message-content">
                  <h3>温馨提示</h3>
                  <div class="content" :class="{center:locationText=='定位中'}">
                      {{locationText}}<span v-loading="locationText =='定位中'" :circle-size="16" class="connecting-btn-load"></span>
                  </div>
              </div>
              <div class="btn-group" v-show="locationText !='定位中'">
                  <button class="btn-default btn-cancel"  @click.stop="messageCancel">重新定位</button>
                  <button class="btn-primary btn-confirm needsclick" @click.stop="dakaComfirm()" >确定</button>
              </div>
          </div>
          <div class="mask" @click="cancelMask" v-show="isShowMessageBox"></div>
      </div>

      <TransferDetailNew v-if="transFlagNew" @emCloseTrans="closeTrans"  @emSubmitTrans="submitTrans"></TransferDetailNew>
  </div>
</template>

<script>
import Header from "components/common/Header.vue";
import AlienNetWorkMustDo from "components/business/GridHomework/MustDoList.vue";
import { dateFormat } from "@/base/utils";
import ClientJs from "@/base/clientjs";
import NlDropdown from "components/common/NlDropdown/dropdown.js";
import Storage from "@/base/storage.js";
import WorkHandlePhoto from "components/business/GridHomework/WorkHandlePhoto.vue"
import {Indicator} from 'mint-ui'
import TransferDetail from'components/business/GridHomework/TransferDetail.vue'
import TemplateInfoMatch from "components/business/GridHomework/TemplateInfoMatch.vue";
import TransferDetailNew from'components/business/GridHomework/TransferDetailNew.vue'
import {BASE64} from '@/base/coding'
export default {
  data() {
    return {
      query:'',
      isShow:"1",
      sixParamList: [],
      sixMustDo: {
        smList:[],//六必做查询数据
        currListIndex: null, //哪条点击了
        currItem: {}, //点击那条数据
        submitBtnFlag: false ,//六必做提交按钮是否高亮能点击
      },
      flow:[],
      dateval:"",
      changeDateFlag: "",
      planDate:"",
      msg:'',//作业明细
      initial:[],
        taskOperationId:'',
        subId:'',
        taskWorkId:'',
        workStatus:'1',
        workType:'',
        taskOperationName:'',
        busiWorkFlag:'',
        locationParam:'',
        showCameraFlg:false,
        orgaIds:'',
        transferFlag:false,
        isShowMessageBox:false,
        dakatext:"打卡",
      planList : [],
      taskType : "",
      taskId : "",
      sceneId:"",
      sceneId2: "",
      isPhoto: "拍照",
      submitList: [],

        sTempId : "",//下发模板ID
        sTemplateDetail: {
            titleList:[],
        },

      isReady: false,
      timer: true,
      objectList:[],
        allCnt:'0',

        transFlagNew:false,
        taskObjList:[],//对象列表
        feedBackFlag:false,
        feedBackName:'',
        workOrder:'',
        pageShow: false,
    }
  },
  components: {Header,AlienNetWorkMustDo,WorkHandlePhoto,TransferDetail,TemplateInfoMatch,TransferDetailNew},
  computed: {
      //弹出框的信息
      locationText(){
          if(this.locationParam.address){
              return this.locationParam.address
          }
          return '定位中';
      },
  },
  methods: {
      goback(){
          Storage.session.set('photoStrList','');
        if (this.$route.query.comeFrom && this.$route.query.comeFrom == 'workStage') {
            this.$router.push({
                path: '/',
            })
        } else {
            history.go(-1)
        }
      },
    isShows(index){
      this.isShow=index;
    },
    choseReason() {
      let _this = this;
      NlDropdown(
        {
          confirmBtn: false,
          datalist: _this.reasons
        },
        function(retVal) {
          //获取返回回调
          _this.reason = retVal.label;
        }
      );
    },
    //多选点击
    smCheckCk(item) {
      this.sixMustParam(item);
    },
    //多选项的输入框值
    smCheckIptChange(item) {
      //this.currIptTxt = item;
      this.sixMustInputParam(item);
    },
    //下拉select选择
    selectChange(item) {
      this.sixMustParam(item);
    },
    //下拉项的输入框值改变
    selectIptChange(item) {
      //this.currIptTxt = item;
      //this.sixMustParam(this.currSelectItem,item);
      this.sixMustInputParam(item);
    },
    //单选框
    radioCk(item) {
      this.sixMustParam(item);
    },
    //单选项的输入框值改变
    radioIptChange(item) {
      this.sixMustInputParam(item);
    },
    //输入框
    inputChange(d) {
      //console.log(d);
      //this.sixListCurrVal = d;
      this.sixMustParam(d);
    },
    //时间
    changeDate() {
      this.$refs.yuyueDate.open();
    },
    formatDateVal(val) {
      this.planDate = dateFormat(val, "yyyy-MM-dd hh:mm:ss");
      // this.startDate = this.planDate;
      let obj = this.sixMustDo.smList;
      obj[this.sixMustDo.currListIndex].planDate = this.planDate;
      this.sixMustDo.smList = obj;
      this.sixMustParam({ value: [this.planDate], key: [] });
    },
    emChangeDate(val) {
      this.changeDateFlag = val;
    },
    //确定哪一条点击了
    getCurrMustDo(item, index) {
        this.sixMustDo.currListIndex = index;
        this.sixMustDo.currItem = item;
    },
    //六必做提交参数获取
    sixMustParam(selectItem) {
      //console.log(selectItem.value);
      //sixParamList
      setTimeout(() => {
        let pObj = {
          titleId: this.sixMustDo.currItem.titleId,
          titleName: this.sixMustDo.currItem.titleName,
          putType: this.sixMustDo.currItem.putType,
          tempValue: selectItem.value == undefined ? "" : selectItem.value.join(","),
          tempKey: selectItem.key == undefined ? "" : selectItem.key.join(","),
        };
        this.sixParamList[this.sixMustDo.currListIndex].titleId = pObj.titleId;
        this.sixParamList[this.sixMustDo.currListIndex].titleName = pObj.titleName;
        this.sixParamList[this.sixMustDo.currListIndex].putType = pObj.putType;
        this.sixParamList[this.sixMustDo.currListIndex].tempValue = pObj.tempValue;
        this.sixParamList[this.sixMustDo.currListIndex].tempKey = pObj.tempKey;
        this.initial[this.sixMustDo.currListIndex].tempValue=pObj.tempValue;
        this.submitList.push(this.sixMustDo.currListIndex);
      },0);
    },
    //六必做各项输入框值获取
    sixMustInputParam(inputTxt) {
      let _this = this;
      _this.sixParamList[_this.sixMustDo.currListIndex].inputValue = inputTxt;
      _this.submitList.push(this.sixMustDo.currListIndex);
    },
    btnCk() {
      this.sixDoSubMit();
    },
    //获取流程数据
    getFlow(){
      let url=`/xsb/gridCenter/gridHomework/h5qryWorkExecutionFlow`;
      let parmes={
        msisdn:this.userInfo.servNumber,
        taskId:this.$route.query.taskId,
      }
      this.$http.post(url,parmes).then((res) => {
          if (res.data.retCode == 0) {
              this.flow=res.data.data.operList;
          } else {
              this.$alert(res.data.retMsg);
          }
      }).catch((respose) => {
      })
    },
    getInformation(){
      let url=`/xsb/gridCenter/gridHomework/h5qryWorkDetail`;
      let parmes={
        msisdn:this.userInfo.servNumber,
        taskId:this.$route.query.taskId,
      }
      this.$http.post(url,parmes).then((res) => {
        this.isReady = true
        this.pageShow = true
          if (res.data.retCode == 0) {
              this.msg=res.data.data;
              this.taskOperationId=this.msg.taskOperationId;
              this.taskOperationName = this.msg.taskOperationName;
              this.subId=this.msg.subId;
              this.taskWorkId=this.msg.workId;
              this.workStatus=this.msg.workStatus;
              this.workType=this.msg.workType;
              this.taskType=this.msg.taskType;
              this.sceneId=this.msg.sceneId;
              this.sceneId2=this.msg.sceneId2;
              //9-预警阅知 , 12-管理阅知 ,22-商铺上下线  (用于反馈)
              if (this.taskType != '9' && this.taskType != '12' && this.taskType != '22'){
                this.isReady = false
                this.getTemp();
              }
              if (this.taskType == '11' || this.taskType == '14'){
                this.isReady = false
                this.getTaskPkgPerformList();
              }
              if(this.msg.stempId){
                  this.isReady = false
                  this.sTempId = this.msg.stempId;
                  this.getTemplateDetail()
              }
              //获取22-商铺上下线 对象下发明细
              if(this.taskType == '22'){
                this.handleShopTask();
              }
          } else {
              this.$alert(res.data.retMsg);
          }
      }).catch((respose) => {
        this.pageShow = true
      })
    },
    // 商铺上下线处理
    handleShopTask() {
      const srcFrom = this.$route.query.srcFrom
      if (srcFrom == 'shopBusiMap') {
        if (this.$route.query.handleType == 1) {
          const item = {
            tempId: this.$route.query.tempId,
            workOrder: this.$route.query.workOrder,
            tempName: decodeURIComponent(atob(this.$route.query.tempName))
          }
          this.doFeedBack(item);
        }
      } else {
        this.$router.replace({
          path: '/shopBusiMap',
          query: {
            taskId: this.$route.query.taskId,
            taskName: btoa(encodeURIComponent(this.msg.taskName)),
            comeFrom: this.$route.query.comFrom,
          }
        })
      }
    },
      //获取下发模板信息
      getTemplateDetail(){
          let param = {
              msisdn:this.userInfo.servNumber,
              taskId:this.$route.query.taskId,
          }
          this.$http.post('/xsb/gridCenter/gridHomework/h5QryGridTaskTemp',param).then(res => {
              let{retCode,retMsg,data} = res.data;
              if(retCode == '0'){
                  this.sTemplateDetail.titleList = data.titleList;
                  this.sTemplateDetail.titleList.forEach(items=>{
                      // items.tempName=data.tempName;
                      if(items.tempValue){
                          items.tempValue=items.tempValue.replace(/\|/g,",");
                      }
                      if(items.tempKey){
                          items.tempKey=items.tempKey.replace(/\|/g,",");
                      }

                      if(items.tempValueCheck){
                          for(let i = 0;i <= items.tempValueCheck.length;i++){
                              if(items.tempValueCheck[i] == "|"){
                                  items.tempValueCheck=items.tempValueCheck.replace(/\|/g,",");
                              }
                          }
                      }else{
                          items.tempValueCheck = "";
                      }
                      if(items.tempKeyCheck){
                          for(let i = 0;i <= items.tempKeyCheck.length;i++){
                              if(items.tempKeyCheck[i] == "|"){
                                  items.tempKeyCheck=items.tempKeyCheck.replace(/\|/g,",");
                              }
                          }
                      }else {
                          items.tempKeyCheck = "";
                      }

                      if(items.putType == '8'){
                          let arr = {
                              objectId:items.titleId,
                              objectName:items.titleName,
                              cntBusImport:'',
                              cntBusUserNew:'',
                              cntBusKdNew:'',
                              cntBusTvNew:'',
                              cntBusFttrNew:'',

                          }
                          this.objectList.push(arr)
                      }
                  })

                  console.info("objectList",this.objectList);
                  if(this.objectList.length > 0){
                      for(let i = 0; i<= this.objectList.length-1 ;i++){
                          this.getObjectInfo(i,this.objectList[i]);
                      }
                  }
              }else {
                  this.$alert(retMsg || '作业下发模板内容查询失败')
              }

              this.isReady = true
          }).catch((response) => {
              this.$alert(`作业下发模板内容接口网络连接失败${response}`);
          })
      },

      getObjectInfo(index,item){
          let param = {
              objectId: item.objectId,
              taskId:this.$route.query.taskId
          }
          this.$http.post('/xsb/gridCenter/gridHomework/h5QryGridWarnJob',param).then(res => {
              if(res.data.retCode == '0'){
                  item.cntBusImport = res.data.data.cntBusImport;
                  item.cntBusUserNew = res.data.data.cntBusUserNew;
                  item.cntBusKdNew = res.data.data.cntBusKdNew;
                  item.cntBusTvNew = res.data.data.cntBusTvNew;
                  item.cntBusFttrNew = res.data.data.cntBusFttrNew;

                  this.allCnt = Number(this.allCnt) + Number(item.cntBusImport);
              }
              console.info("objectList",this.objectList);
              console.info("this.allCnt",this.allCnt);
          })
      },

     //获取模版信息
    getTemp(){
      let url = `/xsb/gridCenter/gridHomework/h5QryHomeworkTempList?msisdn=${this.userInfo.servNumber}&tempId=${this.msg.tempId}`;
      this.$http.get(url).then(res => {
        if(res.data.retCode == '0'){
          this.sixMustDo.smList=res.data.data.tempList[0].titleList;
          this.sixMustDo.smList.forEach(items=>{
            items.tempId=this.msg.tempId;
          })
          this.sixMustDo.smList.forEach(items=>{
            items.mustFlag=0;
            items.mustUserValue="";
            items.mustUserInput="",
            items.isInput=0;
            items.tempName=res.data.data.tempList[0].tempName;
            items.tempValue=items.tempValue.replace(/\|/g,",");
            items.tempKey=items.tempKey.replace(/\|/g,",");
            items.planDate = "请选择";
          })
          this.sixParamList=this.sixMustDo.smList;
          for(let i=0;i<this.sixParamList.length;i++){
            this.initial.push({tempValue:""})
          }
        } else {
          this.$alert(res.data.retMsg);
        }

        this.isReady = true
      })
    },
    //反馈
      feedBack(){
          if (this.isReady) {
            console.info('可以点击');

            if(this.msg.taskType == '6' || this.msg.taskType == '7' || this.msg.taskType == '8'){
              this.finishBusiWork('1');
            }else{
                this.feedbackSec();
            }
          } else {
             if (this.timer) {
              this.timer = false
              const timeout = setTimeout(() => {
                console.info('不可点击倒计时');
                this.isReady = true

                // if(this.msg.taskType == '6' || this.msg.taskType == '7' || this.msg.taskType == '8'){
                //   this.finishBusiWork('1');
                // }else{
                //     this.feedbackSec();
                // }

                clearTimeout(timeout)
              }, 5000)
             }
          }
      },
    feedbackSec(){
      this.sixMustDo.smList.forEach(items=>{
        delete items.mustFlag;
        delete items.mustUserValue;
        delete items.mustUserInput;
        delete items.isInput;
        items.tempValue=items.tempValue.replace(/,/g,"|");
        items.tempKey=items.tempKey.replace(/,/g,"|");
      })
      let url=`/xsb/gridCenter/gridHomework/h5workFeedback`;

      this.sixParamList.forEach((items, index) => {
          if(this.submitList.indexOf(index) == -1) {
              items.tempValue = "";
              items.tempKey = "";
          }
      })
        let workOrder = ''
        if(this.taskType == '22'){
            if (!this.orgaIds) {
                this.$alert("当前用户无归属区域，请联系渠道中心进行配置");
                return;
            }
            // 商铺商机
            workOrder = this.workOrder;
            url = '/xsb/gridCenter/gridHomework/h5ShopBusiFeedback'
        }else {
            this.workOrder = '';
        }
      let parmes={
   	    msisdn: this.userInfo.servNumber,
   	    taskId: this.$route.query.taskId,
   	    activeId: "4",
   	    operName: this.userInfo.operatorName,
      	operDate: dateFormat(new Date(), "yyyy-MM-dd hh:mm:ss"),
   	    griddingCode: this.orgaIds,
   	    tempInfos: JSON.stringify(this.sixParamList),
        /* yesenwei补充: 触发能开反馈接口所需参数 */
        taskType: this.msg.taskType,
        endDate: this.msg.endDate,
        operCont: this.msg.operCont,
        executeInfo: JSON.stringify(this.msg.executeInfo),
          workOrder:workOrder
      }
        if (this.taskType == '22') {
            parmes.tempList = this.sixParamList;
            parmes.workOrderSub = workOrder;
        }

      console.info(parmes);

      for(let i=0;i<this.initial.length;i++) {
            if(this.initial[i].tempValue =="" && this.sixMustDo.smList[i].tempOption == 2) {
                this.$alert("请完成第"+(i+1)+"项必填项");
                return
            }
        }

      let isConntinue = true
      for (let i = 0; i < this.sixParamList.length; i++) {
        const {putType, tempKey, tempValue} = this.sixParamList[i]

        if (putType == '4' && tempValue) {
          if (tempKey == '' || tempKey == null) {
            this.$alert('提交信息异常，请退出后重试！');
            isConntinue = false
            break
          }
        }

        if(this.taskType == '22' && this.sixParamList[i].titleName.indexOf("摸排结果") > -1 && putType == '1'){
            if(tempValue.length < 10){
                this.$alert('摸排结果内容需大于10字');
                return
            }
        }


      }

      console.info(this.sixParamList, '六必做数据');
      console.info(isConntinue, '未通过');
      if (!isConntinue) return
      console.info(isConntinue, '通过');

        this.$http.post(url,parmes).then(res => {
        if(res.data.retCode == '0'){
          this.$toast("反馈成功");

          if(this.taskType == '22'){
              this.taskObjList.forEach((item) => {
                  if(item.workOrder  == this.workOrder){
                      item.isFeedFlag = true;
                      item.feedButtomName = '已反馈'
                  }
              })
              this.goPrevWork()
          }else {
              if (this.$route.query.comeFrom) {
                  if(this.$route.query.comeFrom == 'workStage'){
                      this.$router.push({
                          path: '/',
                      })
                  }
                  if(this.$route.query.comeFrom == 'myTaskListNew'){
                      this.$router.push({
                          path: '/MyTaskList',
                          query: {
                              state: '1'
                          }
                      })
                  }
              } else {
                  this.$router.push("/GridWorkMain");
              }
          }
        } else {
          this.$alert(res.data.retMsg);
        }
      })
    },
      finishBusiWork(item,object){
        let _this= this;
          let url = '/xsb/personBusiness/customizate/h5SignInOutSubmit';//热点场景
          let param={
              workId: this.taskWorkId,
              objectId: this.taskOperationId,
              lat: Storage.get('latitude'),                      //经度
              lon: Storage.get('longitude'),                      //纬度
              location: Storage.get('location'),
              locationStatus: "1",                     //0 签到，1签出
              distance:'0',
              channelId:'53'
          }
          if (this.msg.taskType == '7'){//集团
              url = '/xsb/personBusiness/my/h5SignInOutSearch';//热点场景
              param={
                  locationStatus:'1',
                  groupId:this.taskOperationId,
                  lat:Storage.get('latitude'),
                  lon:Storage.get('longitude'),
                  location:Storage.get('location'),
                  workId: this.taskWorkId,
                  distance:'0',
                  operationType: '1',
                  groupName:this.taskOperationName,
                  latBefore:Storage.get('latitude'),
                  lonBefore:Storage.get('longitude'),
                  subId:this.subId
              }
          }else if(this.msg.taskType == '8'){//小区
              url = '/xsb/personBusiness/villageMarket/h5SignInOut';//热点场景
              param={
                  workId: this.taskWorkId,
                  villageId: this.taskOperationId,
                  lat: Storage.get('latitude'),                      //经度
                  lon: Storage.get('longitude'),                      //纬度
                  location: Storage.get('location'),
                  locationTime: new Date().getTime(),      //timestamp类型
                  locationStatus: "1",                     //0 签到，1签出
                  operationType:'1',
                  villageName:this.taskOperationName,
                  distance: '0',
                  latBefore:Storage.get('latitude'),
                  lonBefore:Storage.get('longitude'),
                  operatorName: Storage.session.get('userInfo').operatorName
              }
          }
          this.$http.post(url, param)
              .then((res) => {
                  if (res.data.retCode == '0') {
                      //_this.$toast('任务签出成功');
                      this.busiWorkFlag = '0';
                      //决策地图
                      let param1 = {
                          workId:  this.taskWorkId,
                          latitude:Storage.get('latitude'),
                          longitude: Storage.get('longitude'),
                          location: Storage.get('location'),
                          locationTime: new Date().getTime(),
                          locationStatus: "1",
                          distance:'0',
                          operationType: '1'
                      };
                      _this.$http.post('/xsb/personBusiness/college/h5SignSync', param1)
                          .then((res) => {
                              if (res.data.retCode == '0') {

                              }else {
                                  _this.$alert('决策地图提交信息失败'+ res.data.retMsg);
                              }
                          })
                  } else {
                      //_this.$toast('任务签出失败');
                      this.busiWorkFlag = '-1';
                  }
                  if (item == '1'){
                      this.feedbackSec();
                  }else if (item == '2'){
                      this.transferSubmit(object);
                  }

              })
      },
      goBusiWork(){
        if (this.workStatus == '1'){
            return ;
        }
          if (this.msg.taskType == '8'){//小区
              this.$router.push({
                  path: '/workHandle',
                  query: {
                      workIdFlag:true,
                      workId: this.taskWorkId,
                      villageId: this.taskOperationId,
                      gobackFlag:"gridWork"
                  }
              })
          }else if(this.msg.taskType == '7' && this.workType == '1'){//集团
              this.$router.push({
                  path: `/groupDetailNew`,
                  query: {
                      workIdFlag:true,
                      taskId: this.taskWorkId,
                      flag: '1',
                      groupId:this.taskOperationId,
                      gobackFlag:"gridWork"
                  }
              })
          }else if(this.msg.taskType == '7' && this.workType == '2'){//集团个人
              this.$router.push({
                  path: `/groupDetailNew`,
                  query: {
                      workIdFlag:true,
                      taskId: this.taskWorkId,
                      flag: '2',
                      groupId:this.taskOperationId,
                      gobackFlag:"gridWork"
                  }
              })
          }else if(this.msg.taskType == '6'){//热点
              this.$router.push({
                  path: `/customSceneDetail`,
                  query: {
                      workIdFlag:true,
                      workId: this.taskWorkId,
                      objectId: this.taskOperationId,
                      gobackFlag:"gridWork"
                  }
              })
          }else if(this.msg.taskType == '20'  || this.msg.taskType == '21'){//楼宇商机、楼宇走访
              this.$router.push({
                  path: `/buildingViewDetail`,
                  query: {
                      gobackFlag:"gridWork",
                      objectId : this.taskOperationId,
                      workId : this.taskWorkId
                  }
              })
          }else if(this.msg.taskType == '22'){//沿街商铺
              this.$router.push({
                  path: `/shopsViewDetail`,
                  query: {
                      objectId: this.taskOperationId,
                      channelId:"95"
                  }
              })
          }
      },
      dakaClick(){
          if (this.locationParam != '' && this.initial[this.sixMustDo.currListIndex].tempValue =='1'){
              return;
          }
          this.isShowMessageBox = true;
          ClientJs.getLocation('', 'dakaClick');
      },
      photoClick(){
          this.showCameraFlg = true;//打开拍照组件
      },
      //关闭组件
      getClose(){
          this.showCameraFlg = false;
          if(Storage.session.get('photoStrList')) {
            this.isPhoto = "已拍照";
            this.submitList.push(this.sixMustDo.currListIndex);
            this.sixParamList[this.sixMustDo.currListIndex].tempKey = Storage.session.get('photoStrList');
            this.sixParamList[this.sixMustDo.currListIndex].tempValue = Storage.session.get('photoStrList');
            this.initial[this.sixMustDo.currListIndex].tempValue='1';
          }
      },
      transfer(){
          this.transferFlag = true;
      },
      closeExecute(){
          this.transferFlag = false;
      },
      getExecute(item){
          if(this.msg.taskType == '6' || this.msg.taskType == '7' || this.msg.taskType == '8'){
              this.finishBusiWork('2',item);
          }else{
              this.transferSubmit(item);
          }
      },
      transferSubmit(item){
          let param = {
              taskId:this.$route.query.taskId,
              taskName:this.msg.taskName,
              activeId:'1',
              msisdnGet:item.execMsisdn,
              nameGet:item.execName,
              operDate:dateFormat(new Date(),'yyyy/MM/dd hh:mm:ss'),
              content:'转派至'+item.execName,
              msisdnPut:Storage.session.get("userInfo").servNumber,
              namePut:Storage.session.get("userInfo").operatorName
          }
          this.$http.post('/xsb/gridCenter/gridHomework/h5TransferSubmit',param).then(res => {
              if (res.data.retCode == '0'){
                  this.$toast("转派成功");
                  if (this.$route.query.comeFrom && this.$route.query.comeFrom == 'workStage') {
                      history.go(-1);
                  } else {
                      this.$router.push("/GridWorkMain");
                  }
              }else{
                  this.$alert(res.data.retMsg)
              }
          })
      },
      //重新定位按钮
      messageCancel(){
          this.locationParam={};
          if(this.clientType == 'ANDROID'){
              this.isShowMessageBox = false;
          }else{
              this.isShowMessageBox = true;
          }
          ClientJs.getLocation('','dakaClick');
      },
      //确定
      dakaComfirm(){
          this.dakatext = '已打卡';
          this.isShowMessageBox = false;
          this.sixParamList[this.sixMustDo.currListIndex].tempKey = this.locationParam.latitude + '|' + this.locationParam.longitude + '|' +this.locationParam.address;
          this.sixParamList[this.sixMustDo.currListIndex].tempValue = this.locationParam.latitude + '|' + this.locationParam.longitude + '|' +this.locationParam.address;
      },
      cancelMask(){
          this.isShowMessageBox = false;
      },
      initData() {
          //当前网格得分排名查询 获取网格信息
          let url = `/xsb/personBusiness/gridViewSecond/h5QryScoreAndRank?telnum=${this.userInfo.servNumber}`;
          this.$http.get(url).then((res) => {
              if (res.data.retCode == "0") {
                  let data = res.data.data;
                  this.orgaIds = data.orgaId2;
              } else {
                  this.$alert(res.data.retMsg || "查询当前网格基本信息接口调用出错");
              }
          });
      },
    //获取任务包列表
    getTaskPkgPerformList() {
      let url = "/xsb/personBusiness/gridTaskPackage/h5QryTaskPkgPerformList";
      let params = {
        msisdn: this.userInfo.servNumber,
        taskpinId: this.taskId,
        itemClassid: this.sceneId
      }
      this.$http.post(url, params).then(res => {
        let {retCode,retMsg,data} = res.data;
        if (retCode == '0') {//成功的情况
          this.planList = data.planList;
        } else {
          this.$alert(retMsg || '请求失败');
        }

        this.isReady = true
      }).catch(err => {
        this.$alert(err)
      })
    },
    //解除绑定
    unbound(items) {
      let planList = [{
        planId:items.planId,
        planName:items.planName,
        planStartdate:items.planStartdate,
        planPerson:items.planPerson,
        finishStatus:items.finishStatus,
        execStartdate:items.execStartdate,
        execPerson:items.execPerson
      }]
      let url = "/xsb/personBusiness/gridTaskPackage/h5createTaskPkgAndPlanRel";
      let params = {
        itemClassid: this.sceneId,
        taskpinId: this.taskId,
        gridId: this.orgaIds,
        msisdn: this.userInfo.servNumber,
        planIds: items.planId + "",
        operDate: dateFormat(new Date(), ("yyyy/MM/dd hh:mm:ss")),
        operType: "2",
        sourceClass:"2",
        planList:JSON.stringify(planList)
      }
      this.$http.post(url, params).then(res => {
        let {retCode,retMsg} = res.data;
        if (retCode == '0') {//成功的情况
          this.getTaskPkgPerformList()
          this.$alert('解绑成功');
        } else {
          this.$alert(retMsg || '请求失败');
        }
      }).catch(err => {
        this.$alert(err)
      })
    },
    //新建计划
    newPlan() {
      if(this.sceneId == "1") {
        this.$router.push({
          path: "villagePlan",
          query: {
            itemClassid: this.sceneId,
            taskpinId: this.taskId,
            gridId: this.orgaIds,
            flag: 'taskPackage',
            sourceClass:'2',
          }
        })
      }else if(this.sceneId == "2" || this.sceneId == "3") {
        this.$router.push({
          path: "drawPlan",
          query: {
            itemClassid: this.sceneId,
            taskpinId: this.taskId,
            gridId: this.orgaIds,
            flag: 'taskPackage',
            sourceClass:'2',
          }
        })
      }else if(this.sceneId == "4") {
        this.$router.push({
          path: "customScenePlan",
          query: {
            itemClassid: this.sceneId,
            taskpinId: this.taskId,
            gridId: this.orgaIds,
            flag: 'taskPackage',
            sourceClass:'2',
          }
        })
      }
    },
    //关联已有计划
    relativePlan() {
      this.$router.push({
        path: "taskPkgPerformList",
        query: {
          taskpinId: this.taskId,
          itemClassid: this.sceneId,
          gridId: this.orgaIds,
          maxNum:this.cntQ,
          haveNum:this.cntP,
          sourceClass:"2",
        }
      })
    },
    //计划详情跳转
    /*toPlanCarry(items) {
      this.$router.push({
        path: "planCarry",
        query: {
          taskpinId: this.taskId,
          planId: items.planId,
          planName: items.planName,
          planStartdate: items.planStartdate,
          planPerson: items.planPerson
        }
      })
    },*/

      operTrans(){
          this.transFlagNew = true;
      },
      closeTrans(){
          this.transFlagNew = false;
      },

      submitTrans(data){
          console.info("data",data);
          Vue.set(data, 'execMsisdn', data.turntoMsisdn);
          Vue.set(data, 'execName', data.turntoName);
          //按老接口转派
          this.transferSubmit(data);
      },
      //获取对象列表
      getTaskObjList(){
          this.taskObjList = [];
          let param = {
              msisdn: this.userInfo.servNumber,
              taskId:this.$route.query.taskId
          }
          this.$http.post('/xsb/gridCenter/gridHomework/h5GridTaskSendTempObjectShopupQuery',param).then(res => {
              if(res.data.retCode == '0'){
                  this.taskObjList = res.data.data.objectList;
                  this.taskObjList.forEach((item) => {

                      if(item.objectAttr4 && item.objectAttr4 == '1'){
                          Vue.set(item, 'isFeedFlag', true);
                          Vue.set(item, 'feedButtomName', '已反馈');
                      }else{
                          Vue.set(item, 'isFeedFlag', false);
                          Vue.set(item, 'feedButtomName', '反馈');
                      }


                  })
              }
              console.info("taskObjList",this.taskObjList);
          })
      },
      goPrevWork(){
          if (this.$route.query.srcFrom == 'shopBusiMap') {
            this.goback();
            return
          }
          this.feedBackFlag = false;
      },
      doFeedBack(item){
          this.feedBackFlag = true;
          this.feedBackName = item.tempName;
          this.workOrder = item.workOrder;
          this.sixMustDo.smList = [];
          this.initial = [];

          let url = `/xsb/gridCenter/gridHomework/h5QryHomeworkTempList?msisdn=${this.userInfo.servNumber}&tempId=${item.tempId}`;
          this.$http.get(url).then(res => {
              if(res.data.retCode == '0'){
                  this.sixMustDo.smList=res.data.data.tempList[0].titleList;
                  this.sixMustDo.smList.forEach(items=>{
                      items.tempId=item.tempId;
                  })
                  this.sixMustDo.smList.forEach(items=>{
                      items.mustFlag=0;
                      items.mustUserValue="";
                      items.mustUserInput="";
                          items.isInput=0;
                      items.tempName=res.data.data.tempList[0].tempName;
                      items.tempValue=items.tempValue.replace(/\|/g,",");
                      items.tempKey=items.tempKey.replace(/\|/g,",");
                      items.planDate = "请选择";
                  })
                  this.sixParamList=this.sixMustDo.smList;
                  for(let i=0;i<this.sixParamList.length;i++){
                      this.initial.push({tempValue:""})
                  }
              } else {
                  this.$alert(res.data.retMsg);
              }

              this.isReady = true
          })

      },
      // 跳转sub网格通页面
      goSubWgt(){
        let jsonString =JSON.stringify(this.taskObjList)
        let subWgtUrl = '/subWgt?path=dotMarker&taskObjList=' + BASE64.encode(encodeURIComponent(jsonString, 'utf-8'))
        this.$router.push(subWgtUrl)
      }
  },
    mounted(){
        window['dakaClick'] = (result) => {
            this.locationParam = result;
            this.submitList.push(this.sixMustDo.currListIndex);
            this.initial[this.sixMustDo.currListIndex].tempValue='1';
        };
    },
   watch: {
    changeDateFlag() {
      this.changeDate();
    },
       locationText:function(newVal,oldVal){
           if(this.clientType == 'ANDROID' && newVal !='定位中'){
               this.isShowMessageBox=true;
           }
       },
  },
  created() {
    this.planDate = "请选择";
    this.dateval = dateFormat(new Date(), "yyyy-MM-dd");
    this.userInfo = Storage.session.get("userInfo");
    this.orgaIds = Storage.session.get('orgaIds');
    if (!this.orgaIds) {
      this.initData();
    }
    this.taskId = this.$route.query.taskId;
    if (this.taskType == '11'|| this.taskType == '14'){
      this.getTaskPkgPerformList();
    }
    this.getFlow();
    this.getInformation();

  },
  filters:{
    changeActiveId(val){
      if(val==1){
        return "保存"
      }else if(val==2){
        return "发布"
      }else if(val==3){
        return "审核"
      }else if(val==4){
        return "反馈"
      }
    },
    changeTaskType(val){
      let homeworkTypeList=[
        {id:'7',label:'集团任务'},
        {id:'8',label:'小区任务'},
        {id:'6',label:'热点任务'},
        {id:'1',label:'任务'},
        {id:'2',label:'考核'},
        {id:'3',label:'考勤'},
        {id:'4',label:'会议'},
        {id:'5',label:'走访'},
        {id:'9',label:'预警阅知'},
        {id:'10',label:'预警反馈'},
        {id:'11',label:'预警任务'},
        {id:'12',label:'管理阅知'},
        {id:'13',label:'管理反馈'},
        {id:'14',label:'管理任务'},
        {id:'15',label:'培训'},
        {id:'16',label:'评比'},
        {id:'17',label:'检查'},
        {id:'18',label:'报表'},
        {id:'19',label:'党建活动'},
        {id:'20',label:'楼宇商机'},
        {id:'21',label:'楼宇走访'},
        {id:'22',label:'商铺上下线'}
      ];

      let homeworkTypeItem = homeworkTypeList.filter(items =>{
        return items.id == val;
      });
      let label;
      if(homeworkTypeItem.length > 0) {
        label = homeworkTypeItem[0].label;
      }
      return label;
    },
      localText(val){
        if(val != '' && val!='null' && val!=null){
            let arr = val.split('|');
            return arr[2]+'(经度:'+arr[0]+'纬度:'+arr[1]+')'
        }
      },
      photoText(val){
          if(val != '' && val!='null' && val!=null){
              let arr = val.split('|');
              let arr1 = [];
              arr.forEach(item =>{
                  arr1.push(item+'.jpg');
              })
              return arr1.join(',')
          }
      }
  },
};
</script>
<style lang='less' scoped>
.line{
  width:100%;
  height:1px;
  margin-top:44px;
  background-color: #EAEAEA;
}
.tabar{
    display:flex;
    &>div{
       width:50%;
       height:40px;
       text-align:center;
       line-height: 40px;
       background-color: #F8F8F8;
       font-size:14px;
       font-family:PingFangTC-Semibold,PingFangTC;
       font-weight:600;
       color:rgba(61,61,61,1);
       box-sizing: border-box;
    }
    .choosed{
        color:#1681FB;
        border-bottom: 2px solid #1681FB;
        background-color: #fff;
    }
}
.detail{
  background-color: #fff;
  overflow: hidden;
  margin-bottom:100px;
}
.turning{
  margin-bottom:100px;
}
.title{
  position: relative;
  margin:15px 0 10px 12px;
  display:flex;
  align-items: center;
  .log{
    display:inline-block;
    width:6px;
    height:19px;
    background-color:#007AFF;
  }
  .text{
    font-size:16px;
    font-family:PingFangSC-Medium,PingFang SC;
    font-weight:500;
    color:rgba(67,67,67,1);
    margin-left:5px;
  }
  .sub_wgt{
    position: absolute;
    display: flex;
    align-items: center;
    right: 20px;
    img{
      width: 30px;
    }
  }
}
.content{
  margin-left:12px;
  margin-right:12px;
  &>div{
    margin-top:10px;
  }
  .ques{
    font-size:14px;
    font-family:PingFangTC-Regular,PingFangTC;
    font-weight:400;
    color:rgba(174,174,175,1);
  }
  .answ{
    font-size:14px;
    font-family:PingFangTC-Regular,PingFangTC;
    font-weight:400;
    color:rgba(72,72,72,1);
  }
  .answt{
    margin-top:10px;
  }
}
.button{
  width:100%;
  height:68px;
  background-color: #fff;
  position:fixed;
  bottom:0;
    display: flex;
  &>div{
    background-color: #1681FB;
    border:0;
    height:44px;
    line-height: 44px;
      margin: 12px 20px ;
    color:#fff;
    letter-spacing: 2px;
    border-radius:22px;
    text-align:center;
    font-size:14px;
    font-family:PingFangSC-Regular,PingFang SC;
      width: 44%;
  }
    &>div:first-child{
        background: #fff;
        color: #000;
        border: 1px solid #ccc;
        height: 42px;
    }
}
.flows{
  margin:0 12px;
  overflow: hidden;
  .flow{
    width:100%;
    height:61px;
    box-shadow:0px 2px 4px 0px rgba(160,160,160,0.5);
    border-radius:8px;
    background-color: #fff;
    display:flex;
    align-items: center;
    position: relative;
    margin-top:12px;
    .log{
      width:42px;
      height:42px;
      border-right:1px solid #D2D2D2;
      display:flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      .score-icon{
        width:24px;
        height:auto;
      }
      .explain-icon{
        font-size:10px;
        color:#1681FB;
        margin-top:4px;
      }
      .explain-iconsh{
        font-size:10px;
        color:#D7993B;
        margin-top:4px;
      }
      .score-iconsh{
        width:24px;
        height:24px;
      }
    }
    .msg{
      margin-left:10px;
    }
    .operName{
      font-size:14px;
      font-family:PingFangSC-Semibold,PingFang SC;
      font-weight:600;
      color:rgba(80,80,80,1);
    }
    .operOrgid{
      font-size:12px;
      font-family:PingFangSC-Regular,PingFang SC;
      font-weight:400;
      color:rgba(155,155,155,1);
      margin-top:8px;
    }
    .date{
      position:absolute;
      font-size:12px;
      font-family:PingFangSC-Regular,PingFang SC;
      font-weight:400;
      color:rgba(123,123,123,1);
      right:10px;
      top:10px;
    }
  }
}
.feedbacks{
  margin:0 12px;
  overflow: hidden;
  .feedback{
    margin-top:12px;
    background-color: #fff;
    box-shadow:0px 2px 4px 0px rgba(160,160,160,0.5);
    border-radius:8px;
    overflow: hidden;
    .head{
      display:flex;
      margin:12px;
      justify-content: space-between;
      align-items: center;
      .orgidDate{
        display:flex;
        flex-direction: column;
        justify-content: end;
      }
      .operName{
        font-size:14px;
        font-family:PingFangSC-Semibold,PingFang SC;
        font-weight:600;
        color:rgba(80,80,80,1);
      }
      .fk{
        display:inline-block;
        color:rgba(48,192,44,1);
        border-radius:4px;
        border:1px solid rgba(48,192,44,1);
        font-size:12px;
        width:36px;
        height:17px;
        text-align: center;
        line-height: 17px;
        margin-left:5px;
        box-sizing: border-box;
      }
      .operOrgid{
        font-size:14px;
        font-family:PingFangSC-Regular,PingFang SC;
        font-weight:400;
        color:rgba(77,121,171,1);
      }
      .date{
        font-size:12px;
        font-family:PingFangSC-Regular,PingFang SC;
        font-weight:400;
        color:rgba(123,123,123,1);
        margin-top:3px;
      }
    }
    .standard{
      margin:12px;
      .qus{
        font-size:14px;
        font-family:PingFangSC-Regular,PingFang SC;
        font-weight:400;
        color:rgba(154,154,154,1);
      }
      .answ{
        font-size:14px;
        font-family:PingFangSC-Regular,PingFang SC;
        font-weight:400;
        color:rgba(80,80,80,1);
        line-height:20px;
        margin-top:6px;
        text-indent: 1rem;
      }
    }
  }
}
.sm-mlis{
  margin:5px 12px;
}
.questionc{
  font-size:14px;
  font-family:PingFangSC-Regular,PingFang SC;
  color:rgba(35,35,35,1);
  line-height:24px;
  .isMust {
      color: red;
  }
}
.work-bottom{
    padding: 5px;
    font-size: 14px;
    border-radius: 20px;
    color: #1681FB;
    border: 1px #1681FB solid;
    display:inline-block;
}
.work-bottom.active{
    color:#ccc;
}
.camera-wrap {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    background: #ECF0FA;
    overflow: auto;
}
.message-show .mask{
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    opacity: 0.5;
    background: #000;
    z-index: 201;
}
.message-box{
    position: fixed;
    top: 50%;
    left: 50%;
    -webkit-transform: translate3d(-50%, -50%, 0);
    transform: translate3d(-50%, -50%, 0);
    background-color: #fff;
    width: 85%;
    border-radius: 3px;
    font-size: 16px;
    -webkit-user-select: none;
    overflow: hidden;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-transition: .2s;
    transition: .2s;
    z-index:999;
}
.message-box .message-content h3{
    padding-top:15px;
    text-align: center;
    font-size: 16px;
    font-weight: 700;
    color: #333;
}
.center{
    text-align:center;
}
.message-content .content{
    padding: 20px 20px 15px;
    border-bottom: 1px solid #ddd;
    min-height: 36px;
    line-height:26px;
    position: relative;
}
.connecting-btn-load{
    display:inline-block;
    vertical-align:-2px;
    width:32px;
    padding-right: 8px;
}
.btn-group{
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    height: 40px;
    line-height: 40px;
}
.btn-group .btn-default,
.btn-group .btn-primary{
    font-size:16px;
    line-height: 35px;
    display: block;
    background-color: #fff;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    //flex: 1;
    margin: 0;
    border: 0;
}
.btn-group .btn-default.btn-cancel {
    width: 50%;
    border-right: 1px solid #ddd;
}
.btn-group .btn-primary.btn-confirm{
    color:#26a2ff;
    width: 50%;
}
.adaptCont{
  margin-top:10px;
  //height: 100px;
  padding:10px;
  background-color: #EFEFEF;
  overflow: scroll;
  /*max-height: 129px;*/
}

.lists {
  /*flex: 1;*/
  /*overflow: scroll;*/
  padding: 0 15px;
  margin: 9px 0 0 0;
  .list {
    font-size: 14px;
    height: 80px;
    border-bottom: 1px solid #E9E9E9;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    color: #999999;
    position: relative;
    .companyName {
      color: #222;
      width: 80%;
      .status {
        display: inline-block;
        margin-left: 4px;
        border-radius: 8px;
        font-size: 12px;
        padding: 4px 8px;
        transform: scale(0.8);
      }
      .status1 {
        border: 1px solid #FF9801;
        color: #FF9801;
      }
      .status2 {
        border: 1px solid #007AFF;
        color: #007AFF;
      }
      .status3 {
        border: 1px solid #18BE32;
        color: #18BE32;
      }
      .status4 {
        border: 1px solid grey;
        color: grey;
      }
    }
    .detail {
      position: absolute;
      bottom: 8px;
      right: 0;
      font-size: 12px;
      color: #007AFF;
    }
    .persons {
      margin-right: 40px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      padding-top: 1px;
    }
    .remove {
      width: 60px;
      height: 18px;
      background: #FF9801;
      border-radius: 5px;
      color: #fff;
      position: absolute;
      font-size: 12px;
      right: 0;
      top: 11px;
    }
  }
}
  .dealTitle{
    font-size: 14px;
    display: flex;
    align-items: center;
    margin: 15px 0 0px 12px;
    .left{
      width: 5px;
      border: 3px solid #15D172;
      height: 5px;
      margin-right: 5px;
      border-radius: 100%;
    }
  }

  .plan{
    margin-top: 5px;
    display: flex;
    align-items: center;
    width: 100%;
    justify-content: space-evenly;
    line-height: 42px;
    height: 42px;
    .planFunc{
      width: 29%;
      align-items: center;
      text-align: center;
      height: 26px;
      font-size: 14px;
      line-height: 24px;
      border-radius: 8px;
    }

    .left{
      border: 1px solid #32D685;
      color: #32D685;
    }

    .right{
      border: 1px solid #247CFF;
      color: #247CFF;
    }

    .icon{
      padding-right: 3px;
    }
  }

  .dataClass{
    margin-bottom: 6px;
  }
.title-table{
    margin-left:12px;
    margin-top:15px;
    display:flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    line-height: 22px;
    .log{
        display:inline-block;
        width:6px;
        height:22px;
        background-color:#007AFF;
        position: absolute;
    }
    .text{
        font-size:16px;
        font-weight:500;
        color:rgba(67,67,67,1);
        margin-left:5px;
        line-height: 22px;
        padding-left: 7px;
    }
    .tasknum{
        margin-right: 10px;
        font-size:14px;
        line-height: 22px;

        .num{
            font-size:18px;
            color: #007AFF;
        }
    }

}

.table-box {
    padding: 12px;
    box-sizing: border-box;

    .table-wrap{
        width: 100%;
        font-size: 12px;
        text-align: left;
    }
}


.head-line{

    height: 30px;
    vertical-align: middle;
    line-height: 30px;
    background-color: #d5e5ff;
}
.table-body tr{
    height: 30px;
    line-height: 30px;
    td:first-child{
        color: #0c77f2;
    }
}
.table-body tr:nth-child(2n){
    background-color: #E4E4E4;

}



.headtop {
    height: auto;
    overflow: hidden;
    position: fixed;
    top: 0px;
    left: 0px;
    right: 0px;
    z-index: 1;
}

.gl-title {
    height: 44px;
    text-align: center;
    position: relative;
    background: #fff;
}

.gl-back {
    position: absolute;
    left: 0px;
    top: 50%;
    font-size: 20px;
    padding: 8px;
    transform: translateY(-50%);
}

.gl-back:before {
    color: #007aff;
}

.gl-title-txt {
    color: @color-TextTitle;
    display: inline-block;
    margin-top: 14px;
}

.txt-btn {
    width: auto;
    position: absolute;
    top: 0;
    right: 10px;
    color: #2574ff;
    line-height: 44px;
    font-size: 14px;
    margin: 0 13px 0 0;
}
    .task-introduce{

        margin: 10px 0 10px 12px;
        line-height: 23px;
        font-size: 14px;
        color: #484848;

    }
.obj-list-info{
    border-bottom: 1px solid #DDDDDD;
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    margin: 0 12px;
    line-height: 44px;
    height: 44px;

    .buttom-back{
        background-color: #1681FB;
        border:0;
        height:22px;
        line-height: 22px;
        margin: 12px 10px ;
        color:#fff;
        letter-spacing: 2px;
        border-radius:15px;
        text-align:center;
        font-size:14px;
        padding: 1px 15px;

        &.active{
            background-color: #CDCDCD;
        }
    }
}

    .feed-wrapper{
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        overflow: hidden;
        background: #fff;
        z-index: 99;

        .feed-info{
            margin-top: 44px;
            height:calc(100vh - 104px);
            overflow: auto;
            background:#ffffff;
        }

        .feed-bottom {
            position: fixed;
            bottom: 10px;
            left: 0;
            right: 0;
            margin: 0 25px;
            height: 60px;
            .feed-bottom-dd {
                display: block;
                width: 100%;
                height: 44px;
                background-color: #1681fb;
                box-shadow: 3px 4px 10px 0px rgba(197, 197, 197, 0.54);
                border-radius: 22px;
                color: #fff;
                font-size: 14px;
                letter-spacing: 4px;
                margin: 10px 0;
                border: 0;
                line-height: 44px;
                text-align: center;
            }
        }
    }

</style>
