<template>
    <div>
        <div class='title-line'>
            <div class='label'>
                <span>经营状态</span>
                <span class='must-do'>*</span>
            </div>
            <div class='value-btn' @click='runStatusSelect'>
                <span class='value-text' :class="{'active': runStatus }">{{ runStatusText ? runStatusText : '请选择' }}</span>
                <span class="iconfont youjiantou2 icon-btn"></span>
            </div>
        </div>
        <div class='title-line'>
            <div class='label'>
                <span>有线宽带运营商</span>
                <span class='must-do'>*</span>
            </div>
            <div class='value-btn' @click='broadbandSelect'>
                <span class='value-text' :class="{'active': broadband }">{{ broadbandText ? broadbandText : '请选择' }}</span>
                <span class="iconfont youjiantou2 icon-btn"></span>
            </div>
        </div>
        <div class='title-line' v-show='broadband == 1'>
            <div class='label'>
                <span>宽带号码</span>
                <span class='must-do'>*</span>
            </div>
            <div class='value-btns input-scan'>
                <input v-model="kdMsisdn" placeholder="请输入" maxlength='50' @blur='kdMsisdnBlur'>
                <div class='scan-btn'>
                    <span @click='scanBroadband'>{{ scanType == 1 ? '扫描光猫' : '扫描机顶盒' }}</span>
                    <span class="iconfont jiantou2 icon-btn" @click='showScanType = !showScanType'></span>
                </div>
                <div v-show='showScanType' class='scan-btn scan-select'>
                    <span v-show='scanType == 2' @click='switchScanType(1)'>{{ '扫描光猫' }}</span>
                    <span v-show='scanType == 1' @click='switchScanType(2)'>{{ '扫描机顶盒' }}</span>
                </div>
            </div>
        </div>
        <div class='title-line' v-show='broadband != 1'>
            <div class='label'>
                <span>是否有宽带到期时间</span>
                <span class='must-do'>*</span>
            </div>
            <div class='value-btn' @click='isKdEndDateSelect'>
                <span class='value-text' :class="{'active': isKdEndDate }">{{ isKdEndDateText ? isKdEndDateText : '请选择' }}</span>
                <span class="iconfont youjiantou2 icon-btn"></span>
            </div>
        </div>
        <div class='title-line' v-show='isKdEndDate == 1'>
            <div class='label'>
                <span>宽带到期时间</span>
                <span class='must-do'>*</span>
            </div>
            <div v-show='broadband != 1' class='value-btn' @click='kdEndDateSelect'>
                <span class='value-text' :class="{'active': kdEndDate }">{{ kdEndDate ? kdEndDate : '请选择' }}</span>
                <span class="iconfont youjiantou2 icon-btn"></span>
            </div>
            <div v-show='broadband == 1' class='value-btn'>
                <span class='value-text active'>{{ kdEndDate }}</span>
            </div>
        </div>
    </div>
</template>

<script>
import ClientJs from '../../../../base/clientjs'
import Storage from "@/base/storage.js"
import { chgStrToDate, dateFormat } from "@/base/utils"
import NlDropdown from 'components/common/NlDropdown/dropdown.js'
import NlDatePicker from "components/common/NlDatePick/datePicker.js"
import cloneDeep from 'lodash/cloneDeep'

export default {
    name: 'ShopMpResult',
    props: ['shopInfo', 'submitShopInfoCb'],
    data() {
        return {
            uinfo: Storage.session.get("userInfo"),
            runStatus: '', // 经营状态
            runStatusText: '', // 经营状态文本
            runStatusOptions: [
                { id: '1', label: '正常营业' },
                { id: '2', label: '停业' },
                { id: '3', label: '已关闭' },
                { id: '4', label: '装修中' },
                { id: '0', label: '未知' }
            ],
            broadband: '', // 宽带运营商
            broadbandText: '', // 宽带运营商文本
            broadbandOptions: [
                { id: '1', label: '移动' },
                { id: '2', label: '联通' },
                { id: '3', label: '电信' },
                { id: '4', label: '其他' },
                { id: '5', label: '广电' }
            ],
            isKdEndDate: '', // 是否有宽带到期时间
            isKdEndDateText: '', // 是否有宽带到期时间文本
            isKdEndDateOptions: [
                { id: '1', label: '是' },
                { id: '0', label: '否' }
            ],
            kdEndDate: '', // 宽带到期时间
            minDate: chgStrToDate('2000-01-01'),
            maxDate: chgStrToDate('2099-12-31'),
            startDate: new Date(),
            showScanType: false, // 是否展示扫描选择
            scanType: 1, // 扫描方式 1-扫描光猫 2-扫描机顶盒
            kdMsisdn: '', // 宽带账号
            mpInfoBak: {}, // 摸排信息备份
            shopData: null, // 商铺数据
        }
    },
    methods: {
        // 运营状态选择
        runStatusSelect() {
            NlDropdown({
                confirmBtn: false,
                datalist: this.runStatusOptions
            }, (res) => {
                this.runStatus = res.id;
                this.runStatusText = res.label;
            })
        },
        // 宽带运营商选择
        broadbandSelect() {
            NlDropdown({
                confirmBtn: false,
                datalist: this.broadbandOptions
            }, (res) => {
                if (this.broadband != res.id) {
                    this.isKdEndDate = ''
                    this.isKdEndDateText = ''
                    this.kdEndDate = ''
                }
                this.broadband = res.id;
                this.broadbandText = res.label;
            })
        },
        // 是否有宽带到期时间选择
        isKdEndDateSelect() {
            NlDropdown({
                confirmBtn: false,
                datalist: this.isKdEndDateOptions
            }, (res) => {
                if (res.id == '0') {
                    this.kdEndDate = ''
                }
                this.isKdEndDate = res.id;
                this.isKdEndDateText = res.label;
            })
        },
        // 宽带到期时间选择
        kdEndDateSelect() {
            NlDatePicker({
                onlyOne: true,
                format: 'yyyy-MM-dd',
                startDate: this.startDate,
                tsMinDate: this.minDate,
                tsMaxDate: this.maxDate
            }, (retVal) => {
                this.kdEndDate = dateFormat(chgStrToDate(retVal.startDate), 'yyyy-MM-dd');
            });
        },
        kdMsisdnBlur() {
            this.kdEndDate = '';
            this.isKdEndDate = '0'
            if (this.kdMsisdn) {
                this.queryKdEndDate(this.kdMsisdn)
            }
        },
        // 切换扫描方式
        switchScanType(type) {
            this.scanType = type;
            this.showScanType = false;
            this.kdMsisdn = '';
            this.kdEndDate = '';
            this.isKdEndDate = '0'
            this.scanBroadband();
        },
        // 扫描宽带
        scanBroadband() {
            if (this.scanType == 1) {
                // 扫描光猫
                const json = JSON.stringify({scanType: '2', callbackFunName: 'scanCallBack'})
                ClientJs.scanBarcode(json);
            } else {
                // 扫描机顶盒
                ClientJs.openCameraAndShow("1", "paizhaoCb");
            }
        },

        // 光猫SN查询宽带用户及设备端口
        async queryKdInfoBySnOrMac(code) {
            const param = {
                crmId: this.uinfo.crmId, // crm工号
                region: this.uinfo.region, // 地市
                sn: code // 光猫SN
            }
            const url = '/xsb/gridCenter/gridMap/h5QueryBuinessInfoBySnOrMac';
            const res = await this.$http.post(url, param);
            let { retCode, retMsg, data } = res.data
            if (retCode == '0') {
                console.info("h5QueryBuinessInfoBySnOrMac: ", data)
                if (!data.accNbr) {
                    this.$alert('光猫SN查询宽带用户及设备端口失败(accNbr=null)');
                    return;
                }
                this.kdMsisdn = data.accNbr;
                await this.queryKdEndDate(this.kdMsisdn);
            } else {
                this.$alert(retMsg || '光猫SN查询宽带用户及设备端口失败');
            }
        },
        // 查询宽带到期时间
        async queryKdEndDate(kdMsisdn) {
            const param = {
                crmId: this.uinfo.crmId, // crm工号
                region: this.uinfo.region, // 地市
                msisdn: kdMsisdn // 光猫SN
            }
            const url = `/xsb/gridCenter/gridMap/h5BroadbandInfoQuery`;
            const res = await this.$http.post(url, param);
            let { retCode, retMsg, data } = res.data
            if (retCode == '0') {
                if(data.broadBrand == '1') {
                    // 宽带到期时间存在
                    const expireDate = data.expireDate ? data.expireDate : '20991231';
                    this.kdEndDate = dateFormat(chgStrToDate(expireDate), 'yyyy-MM-dd');
                    this.isKdEndDate = '1';
                } else {
                    this.$alert('此宽带账号未开通移动宽带');
                }
            } else {
                this.$alert(retMsg || '查询宽带到期时间失败');
            }
        },
        // ocr识别宽带账号
        async barcodeDetection(base64) {
            const param = {
                crmId: this.uinfo.crmId, // crm工号
                region: this.uinfo.region, // 地市
                category: '02',
                style_number: new Date().getTime(),
                image: base64,
                unEncrpt: true // 不加密
            }
            const url = '/xsb/chatTools/marEmpower/h5BarcodeDetection';
            const res = await this.$http.post(url, param);
            let { retCode, retMsg, data } = res.data;
            if (retCode == '0') {
                await this.queryKdNum(data.content)
            } else {
                this.$alert(retMsg || 'ocr识别宽带账号失败');
            }
        },
        // 查询宽带账号
        async queryKdNum(goodsImei) {
            const param = {
                crmId: this.uinfo.crmId, // crm工号
                region: this.uinfo.region, // 地市
                goodsImei: goodsImei
            }
            const url = '/xsb/gridCenter/gridMap/h5querySetTopBox'
            const res = await this.$http.post(url, param);
            let { retCode, retMsg, data } = res.data;
            if (retCode == '0') {
                this.kdMsisdn = data.customerMobile;
                await this.queryKdEndDate(this.kdMsisdn)
            } else {
                this.$alert(retMsg || 'ocr识别宽带账号失败');
            }
        },
        // 检查数据
        async checkData() {
            if (!this.runStatus) {
                this.$alert('请选择运营状态');
                return false;
            }
            if (!this.broadband) {
                this.$alert('请选择有线宽带运营商');
                return false;
            }

            if (this.broadband == '1') {
                // 移动宽带
                if (!this.kdMsisdn) {
                    this.$alert('请输入宽带账号');
                    return false;
                }
            }

            if (this.broadband != '1') {
                // 非移动宽带
                if (!this.isKdEndDate) {
                    this.$alert('请选择是否有宽带到期时间');
                    return false;
                }

                if (this.isKdEndDate == '1' && !this.kdEndDate) {
                    this.$alert('请选择宽带到期时间');
                    return false;
                }
            }
            // 提交商铺信息
            if (this.isMpInfoChange()) {
                return await this.submitShopInfo();
            }
            return true;
        },
        async submit() {
            const flag = await this.checkData();
            if (!flag) return null;
            const tempKey = '经营状态|有线宽带运营商|宽带账号|是否有宽带到期时间|宽带到期时间'
            const tempValue = this.runStatus + '|'
                + this.broadband + '|'
                + this.kdMsisdn + '|'
                + this.isKdEndDate + '|'
                + (this.kdEndDate ? dateFormat(chgStrToDate(this.kdEndDate), 'yyyyMMdd') : '');
            return { tempKey, tempValue };
        },
        // 备份摸排信息
        backupMpInfo() {
            this.mpInfoBak.runStatus = this.runStatus;
            this.mpInfoBak.broadband = this.broadband;
            this.mpInfoBak.kdMsisdn = this.kdMsisdn;
            this.mpInfoBak.isKdEndDate = this.isKdEndDate;
            this.mpInfoBak.kdEndDate = this.kdEndDate;
        },
        // 摸排信息是否变更
        isMpInfoChange() {
            for (let attr in this.mpInfoBak) {
                if (this.mpInfoBak[attr] != this[attr]) return true;
            }
            return false;
        },
        // 提交商铺信息
        async submitShopInfo() {
            if (!this.shopData) return false;
            this.shopData.streamSeq = new Date().getTime();
            this.shopData.operType = '2';
            this.shopData.dataSource = '3';
            this.shopData.crmId = this.uinfo.crmId;
            this.shopData.region = this.uinfo.region;
            this.shopData.operId = this.uinfo.servNumber;
            this.shopData.runStatus = this.runStatus;
            this.shopData.broadbandInfo = JSON.stringify({
                broadband: this.broadband,
                kdMsisdn: this.kdMsisdn,
                iskdEndDate: this.isKdEndDate,
                kdEndDate: this.kdEndDate ? dateFormat(chgStrToDate(this.kdEndDate), 'yyyyMMdd') : '',
            });
            console.info('h5shopInfoSubmit param', this.shopData);
            const url = '/xsb/gridCenter/gridMap/h5shopInfoSubmit';
            const res = await this.$http.post(url, this.shopData);
            let { retCode, retMsg } = res.data;
            if (retCode == '0') {
                this.backupMpInfo();
                return true;
            } else {
                this.$alert(retMsg || '商铺摸排信息保存失败');
                return false;
            }
        }
    },
    beforeMount() {
      if (this.shopInfo) {
          this.shopData = cloneDeep(this.shopInfo);
          const runStatus = this.shopInfo.runStatus
          this.runStatus = runStatus ? runStatus : '';
          const runStatusOp = this.runStatusOptions.find(item => item.id == runStatus);
          this.runStatusText = runStatusOp ? runStatusOp.label : '';

          const kdInfo = this.shopInfo.broadbandInfo;
          if (kdInfo) {
              this.broadband = kdInfo.broadband ? kdInfo.broadband : '';
              const broadbandOp = this.broadbandOptions.find(item => item.id == this.broadband);
              this.broadbandText = broadbandOp ? broadbandOp.label : '';

              this.isKdEndDate = kdInfo.iskdEndDate ? kdInfo.iskdEndDate : '';
              const isKdEndDateOp = this.isKdEndDateOptions.find(item => item.id == this.isKdEndDate);
              this.isKdEndDateText = isKdEndDateOp ? isKdEndDateOp.label : '';

              this.kdMsisdn = kdInfo.kdMsisdn ? kdInfo.kdMsisdn : '';
              this.kdEndDate = kdInfo.kdEndDate ? dateFormat(chgStrToDate(kdInfo.kdEndDate), 'yyyy-MM-dd') : '';
          }
          const shopInfo = this.shopInfo.shopInfo;
          this.shopData.shopInfo = shopInfo ? JSON.stringify(shopInfo) : shopInfo;
      }
      this.backupMpInfo();
    },
    mounted() {
        window['scanCallBack'] = (res) => {
            console.info("scanCallBack: " + res);
            const codeInfo = JSON.parse(res);
            if (codeInfo && codeInfo.scanCode) {
                this.queryKdInfoBySnOrMac(codeInfo.scanCode);
            } else {
                this.$alert('扫描光猫失败，请重试');
            }
        };

        window['paizhaoCb'] = (res) => {
            this.barcodeDetection(res.fileImage)
        };
    }
}
</script>

<style lang='less' scoped>
.title-line {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 12px 0;
    border-bottom: 1px solid #DDDDDD;
    color: #3D3D3D;
    font-size: 12px;
    align-items: center;

    &:first-child {
        padding-top: 6px;
    }

    .must-do {
        color: red;
    }

    .label {
        margin-top: 2px;
    }

    .value-btn {
        display: flex;
        align-items: center;
    }

    .value-text {
        color: #BFBFBF;
    }

    .active {
        color: #3D3D3D;
    }

    .icon-btn {
        font-size: 12px;
        color: #3D3D3D;
        margin-left: 2px;
    }
}

.input-scan {
    position: relative;
    input {
        outline: none;
        background: none;
        border: none;
        font-size: 13px;
        height: 20px;
        line-height: 20px;
        width: 100px;
        color: #3D3D3D;
        text-align: right;
    }
    input::placeholder {
        color: #BFBFBF;
        font-size: 12px;
    }
    .scan-btn {
        display: inline-block;
        width: 92px;
        height: 20px;
        line-height: 20px;
        text-align: center;
        padding: 3px 0px;
        background-color: #1989fa;
        font-size: 12px;
        color: white;

        .icon-btn {
            font-size: 11px;
            color: white;
            margin-left: 5px;
        }
    }

    .scan-select {
        position: absolute;
        border-radius: 0;
        right: 0;
        top: 26px;
    }
}
</style>
