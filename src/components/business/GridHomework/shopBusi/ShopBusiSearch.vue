<template>
    <div class='main-wrapper'>
        <div class="head-top">
            <div class="head-title">
                <span class="iconfont zuojiantou head-back" @click='goPrev'></span>
                <input v-model='searchKey' class='search-input' placeholder='请输入商铺名称'/>
                <span class="iconfont sousuo search-icon" @click='searchShop'></span>
            </div>
        </div>
        <div class='shop-list'>
            <div class='shop-item' v-for='(shop, index) in searchList' :key='index' @click='shopMarkerClick(shop.objectId)'>
                <div class='shop-img-wrap'>
                    <div class='shop-img-div'>
                        <img class='shop-img' src="static/mapGrid/default_shop.png"/>
                        <span class='shop-icon feedback-icon' :class="{'unfeedback': shop.feedback != '1'}">{{ shop.feedback == '1' ? '已反馈' : '未反馈' }}</span>
                    </div>
                </div>
                <div class='shop-content'>
                    <div class='shop-title'>
                        <span class='shop-name'>{{ shop.objectName }}</span>
                    </div>
                    <div class='shop-icons'>
                        <span v-for='(item, index) in shop.busiTypeNameList' :key='index' class='shop-icon busi-icon'>{{ item }}</span>
                    </div>
                </div>
                <div class='feedback-wrapper'>
                    <div class='feedback-btn'>
                        <span class='iconfont bianji feedback'></span>
                        <span class='feedback-txt' @click.stop='goFeedbackPage(shop.objectId)'>反馈</span>
                    </div>
                    <div class='shop-distance'>
                        <img src='@/assets/img/gride/location.png'/>
                        <span>{{ shop.distance }}</span>
                    </div>
                </div>
            </div>
            <NoDataPage v-if='!searchList || searchList.length == 0'></NoDataPage>
        </div>
    </div>
</template>

<script>

import Header from 'components/common/Header.vue'
import NoDataPage from 'components/common/NoDataPage'

export default {
    components: { Header, NoDataPage },
    props: ['shopList'],
    data() {
        return {
            searchKey: '', // 搜索关键字
            searchList: [], // 搜索列表
        }
    },
    methods: {
        // 返回
        goPrev() {
            this.$emit('emClose')
            this.clear();
        },
        // 清空搜索结果
        clear() {
            this.searchKey = '';
            this.searchList = [];
        },
        // 搜索商铺
        searchShop() {
            if(!this.searchKey.trim()) {
                this.clear();
                this.$alert('请输入商铺名称');
                return;
            }
            this.searchList = this.shopList.filter(item => item.objectName.search(this.searchKey.trim()) !== -1);
        },
        // 商铺点击
        shopMarkerClick(shopId) {
            this.goPrev();
            this.$emit('shopMarkerClick', shopId)
        },
        // 快速反馈
        goFeedbackPage(shopId) {
            this.goPrev();
            this.$emit('goFeedbackPage', shopId)
        }
    }
}
</script>

<style lang='less' scoped>
.main-wrapper {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    overflow: hidden;
    background: #fff;
    z-index: 999;
}
.head-top {
    width: 100%;
    height: auto;
    overflow: hidden;
    position: fixed;
    top: 0px;
    left: 0px;
    z-index: 200;

    .head-title {
        height: 44px;
        position: relative;
        background: #fff;
        border-bottom: 1px solid #EDEDED;
    }

    .head-back {
        position: absolute;
        left: 0px;
        top: 50%;
        font-size: 20px;
        padding: 8px;
        transform: translateY(-50%);
    }
    .search-input {
        position: absolute;
        left: 40px;
        font-size: 14px;
        outline: none;
        border:none;
        height: 21px;
        top: 12px;
        width: 50%;
        color: #707070;
    }
    .search-icon {
        position: absolute;
        right: 10px;
        top: 9px;
        font-size: 26px;
        color: #007AFF;
    }
}

.shop-list {
    margin-top: 55px;
    height: calc(100% - 55px);
    overflow-y: auto;
    padding: 0 12px;

    .shop-item {
        display: flex;
        flex-direction: row;
        padding: 8px 0px;
        border-bottom: 1px solid #F0F0F0;
        &:first-child {
            padding-top: 5px;
        }
    }

    .shop-img-wrap {
        display: flex;
        justify-content: center;
        align-items: center;

        .shop-img-div {
            position: relative;
        }
        .feedback-icon {
            position: absolute;
            right: 0;
            top: 0;
            font-size: 10px;
            color: #FFFFFF;
            background: #38B232;
            border-radius: 0 0 0 6px;
            padding: 2px 3px;
        }
    }

    .shop-img {
        width: 55px;
        height: 52px;
    }

    .shop-content {
        margin-left: 8px;
        margin-top: 3px;
        width: calc(100% - 145px);

        .shop-title {
            display: flex;
            align-items: center;
        }

        .shop-name {
            font-size: 16px;
            font-weight: 600;
            color: #1871D2;
            width: calc(100% - 5px);
            max-width: calc(100% - 5px);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .shop-icons {
            margin-top: 3px;
        }
        .shop-icon {
            display: inline-block;
            font-size: 12px;
            border-radius: 5px;
            padding: 0 8px;
            height: 20px;
            line-height: 20px;
            text-align: center;
            margin-top: 8px;
        }

        .busi-icon {
            background: rgba(227, 136, 62, 0.1);
            color: #E88800;
            margin-right: 5px;
        }
    }

    .feedback-wrapper {
        margin-left: 15px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .feedback-btn {
            display: flex;
            align-items: center;
            height: 28px;
            padding: 0px 12px;
            border-radius: 14px;
            background: #007AFF;
            font-size: 12px;
            color: #FFFFFF;

            .feedback {
                font-size: 12px;
            }

            .feedback-txt {
                margin-left: 5px;
                font-size: 12px;
            }
        }
    }
}
.shop-distance {
    display: flex;
    align-items: center;
    margin-top: 6px;

    img {
        width: 8px;
        height: 11px;
    }

    span {
        font-size: 12px;
        color: #5A7597;
        margin-left: 5px;
    }
}
.unfeedback {
    background: #FC7F04 !important;
}
</style>
