<template>
    <div class='main-wrapper'>
        <div class="head-top">
            <div class="head-title">
                <span class="iconfont zuojiantou head-back" @click='goPrev'></span>
                <span class="head-title-txt">{{ taskInfo.shopName }}-商机反馈</span>
            </div>
        </div>
        <div class='busi-type-list'>
            <div v-for='(item, index) in workSubList' :key='index'
                  class='busi-type' :class="{'busi-type-active': item.workOrder == curWorkOrder }"
                  @click='switchTemp(item)'>
                <span>{{ item.busiType }}</span>
                <span v-if='item.status == 1' class='feed-text'>已反馈</span>
            </div>
        </div>
        <div v-show='item.workOrder == curWorkOrder' v-for='(item, index) in tempList' :key='index' class='feed-temp'>
            <FeedbackTemp :ref="'feedbackTemp' + item.workOrder"
                          :templateData='item.titleList'
                          :busiType='item.busiType'
                          :shopInfo='shopInfo'
                          :objLatLng='shopLatLng'
                          :signDistance='150'>
            </FeedbackTemp>
        </div>
        <div class="feed-bottom">
            <div v-if='workStatus != 1' class="feed-btn" @click='submit'>反馈</div>
            <div v-if='workStatus == 1' class="feed-btn feed-done">已反馈</div>
<!--            <div v-if='isKdBtnShow' class="feed-btn">宽带办理</div>-->
        </div>
    </div>
</template>

<script>
import Storage from "@/base/storage.js"
import { chgStrToDate, dateFormat } from "@/base/utils"
import FeedbackTemp from'components/business/GridHomework/shopBusi/FeedbackTemp.vue'

export default {
    name: 'ShopBusiFeedback',
    components: { FeedbackTemp },
    data() {
        return {
            uinfo: Storage.session.get("userInfo"),
            taskInfo: {}, // 任务信息
            workSubList: [], // 子工单列表
            tempList: [], // 工单模板
            gridId: '', // 网格编码
            shopInfo: null, // 商铺信息
            curWorkOrder: '', // 当前工单
            shopLatLng: null, // 当前商铺经纬度
            isKdBtnShow: false, // 是否展示宽带办理按钮
            workStatus: 0, // 当前模板反馈状态 0: 未反馈 1：已反馈
        }
    },
    methods: {
        // 返回
        goPrev() {
            history.go(-1);
        },
        // 切换工单模板
        switchTemp(work) {
            this.checkBaseData(work.objClassId);
            console.info("objClassId" + work.objClassId)
            this.isKdBtnShow = (work.objClassId == 4 && this.isKdEndDateIn3Month()) ? true : false
            this.curWorkOrder = work.workOrder;
            this.workStatus = work.status;
            const flag = this.tempList.find(item => item.workOrder == work.workOrder);
            if (!flag) {
                this.getTempInfo(work);
            }
        },
        // 获取模板信息
        async getTempInfo(work) {
            const url = `/xsb/gridCenter/gridHomework/h5QryHomeworkTempList?msisdn=${this.uinfo.servNumber}&tempId=${work.tempId}`;
            const res = await this.$http.get(url);
            let { retCode, retMsg, data } = res.data
            if (retCode === '0') {
                const tempList = data.tempList;
                this.tempList.push({
                    workOrder: work.workOrder,
                    tempId: work.tempId,
                    busiType: work.objClassId, // 商机类型编号
                    tempName: tempList[0].tempName,
                    titleList: tempList[0].titleList
                })
            } else {
                this.$alert(retMsg || '获取反馈模板失败');
            }
        },

        // 检查基础数据
        checkBaseData(busiType) {
            if (!this.gridId) {
                this.$alert("当前用户无归属区域，请联系渠道中心进行配置");
                return false;
            }
            if (!this.shopInfo) {
                this.$alert("查询商铺信息失败");
                return false
            }

            if (busiType == 1 && this.shopInfo.isMp != '1')  {
                // 上线商铺
                this.$alert("请先完成商铺信息采集");
                return false
            }
            return true;
        },
        // 检查反馈数据
        checkFeedbackData(feedBackData) {
            if (!feedBackData) return false;
            const temp = this.tempList.find(item => item.workOrder == this.curWorkOrder);
            for (let item of feedBackData) {
                item.tempId = temp.tempId;
                item.tempName = temp.tempName;
            }
            return true;
        },
        // 提交数据
        async submit() {
            const workSub = this.workSubList.find(item => item.workOrder == this.curWorkOrder);
            if(!this.checkBaseData(workSub.objClassId)) return;
            // 提交商机反馈数据
            const feedBackData = await this.$refs['feedbackTemp' + this.curWorkOrder][0].submit();
            if (!this.checkFeedbackData(feedBackData)) return;
            await this.feedbackBusi(feedBackData, workSub)
        },
        // 反馈商机
        async feedbackBusi(feedBackData, workSub) {
            const param = {
                crmId: this.uinfo.crmId, // crm工号
                region: this.uinfo.region, // 地市
                msisdn: this.uinfo.servNumber, // 用户手机号
                taskId: this.taskInfo.taskId, // 任务编码
                activeId: '4', // 任务状态 1-草稿 2-发布 3-审核 4-反馈
                operName: this.uinfo.operatorName, // 操作人员姓名（任务责任人）
                operDate: dateFormat(new Date(), "yyyy-MM-dd hh:mm:ss"), // 操作时间（任务完成时间）
                griddingCode: this.gridId, // 网格编码
                tempList: feedBackData, // 模板反馈数据
                workOrder: this.taskInfo.workOrder, // 工单编码
                workOrderSub: workSub.workOrder // 子工单编码
            }
            console.info('h5ShopBusiFeedback param: ', param)
            const url = '/xsb/gridCenter/gridHomework/h5ShopBusiFeedback';
            const res = await this.$http.post(url, param);
            let { retCode, retMsg } = res.data
            if (retCode == '0') {
                this.$set(workSub, 'status', 1);
                this.workStatus = 1;
                this.$alert('商铺商机反馈成功');
            } else {
                this.$alert(retMsg || '商铺商机反馈失败');
            }
        },
        // 查询网格编码
        async queryGridId() {
            const gridId = Storage.session.get('orgaIds');
            if (gridId) return gridId;
            //当前网格得分排名查询 获取网格信息
            const url = `/xsb/personBusiness/gridViewSecond/h5QryScoreAndRank?telnum=${this.uinfo.servNumber}`;
            const res = await this.$http.get(url);
            let { retCode, data } = res.data
            if (retCode === '0') {
                return data.orgaId2;
            }
            return null;
        },
        // 查询商铺信息
        async queryShopInfo(shopId) {
            const param = {
                crmId: this.uinfo.crmId, // crm工号
                region: this.uinfo.region, // 地市
                streamSeq: new Date().getTime(),
                dataSource: '2',
                operId: this.uinfo.servNumber,
                shopId: shopId,//商铺ID
                regionId: this.uinfo.region,
            };
            const url = '/xsb/gridCenter/gridMap/h5queryShopInfo';
            const res = await this.$http.post(url, param);
            let { retCode, data } = res.data
            if (retCode === '0') {
                return data;
            }
            return null;
        },
        // 判断宽带到期时间是否在3个月内
        isKdEndDateIn3Month() {
            if (!this.shopInfo || !this.shopInfo.broadbandInfo) return false;
            const kdEndDate = this.shopInfo.broadbandInfo.kdEndDate;
            if (!kdEndDate) return false;
            const diffDays = this.getDiffDays(chgStrToDate(kdEndDate))
            console.info('diffDays' + diffDays)
            return diffDays <= 90;
        },
        // 判断日期与当前时间相差多少天
        getDiffDays(date) {
            const now = new Date();
            let diff = now - chgStrToDate(date)
            diff = diff / (1000 * 60 * 60 * 24)
            return diff;
        },
    },
    async mounted() {
        this.gridId = await this.queryGridId();
        const body = this.$route.query.body;
        const json = decodeURIComponent(atob(body));
        this.taskInfo = JSON.parse(json);
        this.workSubList = this.taskInfo.workSubList;
        this.shopInfo = await this.queryShopInfo(this.taskInfo.shopId);
        if (this.shopInfo && this.shopInfo.longitude && this.shopInfo.latitude) {
            this.shopLatLng = [this.shopInfo.longitude, this.shopInfo.latitude]
        }
        this.switchTemp(this.workSubList[0])
    }
}
</script>

<style lang='less' scoped>
.main-wrapper {
    width: 100%;
    height: 100%;
    background: #fff;
    overflow: hidden;
}

.head-top {
    width: 100%;
    height: auto;
    overflow: hidden;
    position: fixed;
    top: 0px;
    left: 0px;
    border-bottom: 1px solid #EDEDED;

    .head-title {
        height: 44px;
        text-align: center;
        position: relative;
        background: #fff;
    }

    .head-back {
        position: absolute;
        left: 0px;
        top: 50%;
        font-size: 20px;
        padding: 8px;
        transform: translateY(-50%);
    }

    .head-title-txt {
        width: 260px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        color: #3D3D3D;
        font-size: 16px;
        display: inline-block;
        margin-top: 14px;
    }
}

.busi-type-list {
    display: flex;
    flex-direction: row;
    background-color: #fff;
    padding: 10px 12px;
    align-items: center;
    margin-top: 45px;
    box-shadow: 0px 4px 10px 0px rgba(0,0,0,0.08);

    .busi-type {
        position: relative;
        width: 75px;
        padding: 8px 0px;
        text-align: center;
        font-size: 13px;
        color: #535353;
        background: #EBF4FF;
        border-radius: 8px;
        margin-left: 15px;

        &:first-child {
            margin-left: 10px;
        }
    }

    .busi-type-active {
        background: #007AFF;
        color: #FFFFFF;
    }

    .feed-text {
        display: inline-block;
        position: absolute;
        right: 0;
        top: 0;
        width: 35px;
        height: 13px;
        line-height: 13px;
        font-size: 8px;
        color: #FFF;
        background: #00A51C;
        border-radius: 0px 8px 0px 8px ;
    }
}

.feed-temp {
    height: calc(100% - 162px);
    overflow-y: auto;
}

.feed-bottom {
    position: fixed;
    bottom: 10px;
    left: 25px;
    right: 25px;

    .feed-btn {
        height: 38px;
        line-height: 38px;
        margin: 10px 0;
        font-size: 14px;
        color: #fff;
        text-align: center;
        letter-spacing: 4px;
        background-color: #1681fb;
        border-radius: 22px;
    }

    .feed-done {
        background-color: #999a9b;
    }
}
</style>
