<template>
    <div class='temp-wrapper'>
        <div v-for='(title, index) in titleList' :key='index'>
            <div class="title-name" @click='show'>
                <span>{{ index + 1 }}.</span>
                <span>{{title.titleName}}</span>
                <span v-show="title.tempOption == 2" class="must-do">*</span>
            </div>
            <!-- 输入框 -->
            <div class='title-line border-line temp-input' v-if='title.putType == 1'>
                <input type="text" placeholder="请输入" @click='inputClick(title)' @input='inputChange'/>
            </div>
            <!-- 多选 -->
            <div class='title-line temp-checkbox' v-if='title.putType == 2'>
                <div class='cb-item' :class="{'active': item.isCheck}" v-for="(item, index) in title.selectOptions" :key="index" @click='checkboxClick(title, item)'>
                    <span class='cb-txt-span'>{{ item.label }}</span>
                    <span class="iconfont" :class="[item.isCheck ? 'checkbox2' : 'checkbox11']"></span>
                </div>
            </div>
            <!-- 单选 -->
            <div class='title-line temp-checkbox' v-if='title.putType == 3'>
                <div class='cb-item' :class="{'active': title.tempValue == item.id}" v-for="(item,index) in title.selectOptions" :key="index" @click='radioClick(title, item)'>
                    <span class='cb-txt-span'>{{ item.label }}</span>
                    <span class="iconfont duihao cb-btn"></span>
                </div>
            </div>
            <!-- 下拉框 -->
            <div class='title-line border-line temp-textbtn' v-if='title.putType == 4'>
                <div class='temp-text'>{{ title.tempKey ? title.tempKey : '请选择' }}</div>
                <div class='temp-btn' @click='dropDown(title)'>选择</div>
            </div>
            <!-- 日期 -->
            <div class='title-line border-line temp-textbtn' v-if='title.putType == 5'>
                <div class='temp-text'>{{ title.tempValue ? title.tempValue : '请选择' }}</div>
                <div class='temp-btn' @click='selectDate(title)'>选择</div>
            </div>
            <!-- 打卡 -->
            <div class='title-line border-line temp-textbtn' v-if='title.putType == 6'>
                <div class='temp-text'>{{ title.isSign ? '已打卡' : '请打卡' }}</div>
                <div class='temp-btn' @click='signIn(title)'>打卡</div>
            </div>
            <!-- 拍照 -->
            <div class='title-line border-line temp-textbtn' v-if='title.putType == 7'>
                <div class='temp-text'>{{ title.isPhoto ? '已拍照' : '请拍照' }}</div>
                <div class='temp-btn' @click='photo(title)'>拍照</div>
            </div>
            <!-- 摸排结果 -->
            <div class='title-line' v-if='title.putType == 11'>
                <ShopMpResult ref='shopMpResult' :shopInfo='shopInfo' :submitShopInfoCb='submitShopInfo'></ShopMpResult>
            </div>
        </div>
    </div>
</template>

<script>
import cloneDeep from 'lodash/cloneDeep'
import ClientJs from '../../../../base/clientjs'
import { chgStrToDate, dateFormat } from "@/base/utils"
import Storage from "@/base/storage.js"
import NlDropdown from 'components/common/NlDropdown/dropdown.js'
import NlDatePicker from "components/common/NlDatePick/datePicker.js"
import ShopMpResult from'components/business/GridHomework/shopBusi/ShopMpResult.vue'

export default {
    name: 'FeedbackTemp',
    components: { ShopMpResult },
    props: ['templateData', 'shopInfo', 'busiType', 'objLatLng', 'signDistance'],
    data() {
        return {
            uinfo: Storage.session.get("userInfo"),
            curTitle: null,
            titleList: [], // 模板信息列表
            minDate: chgStrToDate('2000-01-01'),
            maxDate: chgStrToDate('2099-12-31'),
            startDate: new Date(),
            shopData: null, // 商铺数据
            ywdqList: ['3', '5', '6', '7', '8'] // 异网到期
        }
    },
    methods: {
        // 输入框点击
        inputClick(title) {
            this.curTitle = title;
        },
        // 输入框输入
        inputChange(event) {
            this.curTitle.tempValue = event.target.value;
            this.curTitle.tempKey = event.target.value;
        },
        // 多选框
        checkboxClick(title, item) {
            this.$set(item, 'isCheck', !item.isCheck);
            const values = []
            const keys = []
            title.selectOptions.forEach(option => {
                if (option.isCheck) {
                    values.push(option.id)
                    keys.push(option.label)
                }
            })
            title.tempValue = values.join(',');
            title.tempKey = keys.join(',');
        },
        // 单选
        radioClick(title, item) {
            this.$set(title, 'tempValue', item.id);
            title.tempKey = item.label;

            if (this.busiType) {
                this.shopRadioClick(title, item);
            }
        },

        // 日期选择
        selectDate(title) {
            NlDatePicker({
                onlyOne: true,
                format: title.format ? title.format : 'yyyy-MM-dd',
                startDate: this.startDate,
                tsMinDate: this.minDate,
                tsMaxDate: this.maxDate
            }, (retVal) => {
                this.$set(title, 'tempValue', retVal.startDate);
                title.tempKey = retVal.startDate;
            });
        },
        // 下拉
        dropDown(title) {
            NlDropdown({
                confirmBtn: false,
                datalist: title.selectOptions
            }, (res) => {
                this.$set(title, 'tempKey', res.label);
                title.tempValue = res.id
            })
        },
        // 拍照
        photo(title) {
            this.curTitle = title;
            ClientJs.openCameraAndShow('1', 'photo')
        },
        // 打卡
        signIn(title) {
            this.curTitle = title;
            ClientJs.getLocation('', 'getLocationInfoCb');
        },
        async locationSuccess(result) {
            if (this.objLatLng && this.signDistance) {
                // 判断签到距离
                const latlng = [result.longitude, result.latitude]
                const distance = await this.getDistance(latlng, this.objLatLng);
                if (distance > this.signDistance) {
                    this.$alert(`距离超过${this.signDistance}米`);
                    return;
                }
            }
            this.$messagebox({
                title: '温馨提示',
                message: result.address,
                showCancelButton: true,
                cancelButtonText: '重新定位',
                confirmButtonText: '确认'
            }).then(async action => {
                if (action == 'confirm') {
                    if (this.busiType) {
                        const flag = await this.shopSign(result.longitude, result.latitude, result.address)
                        if (!flag) return;
                    }
                    this.$set(this.curTitle, 'isSign', true)
                    const value = result.latitude + '|' + result.longitude + '|' + result.address
                    this.curTitle.tempValue = value;
                    this.curTitle.tempKey = value;
                } else {
                    ClientJs.getLocation('', 'getLocationInfoCb');
                }
            })
        },
        // 获取距离
        async getDistance(latlng1, latlng2) {
            const url = `/xsb/personBusiness/my/h5GetDistance?lng1=${latlng1[0]}&lat1=${latlng1[1]}&lng2=${latlng2[0]}&lat2=${latlng2[1]}`
            const res = await this.$http.get(url);
            return res.data;
        },
        // 定位失败
        locationError() {
            this.$messagebox({
                title: '温馨提示',
                message: '定位失败，请稍后重试',
                showCancelButton: true,
                cancelButtonText: '取消',
                confirmButtonText: '重新定位'
            }).then(action => {
                if (action == 'confirm') {
                    ClientJs.getLocation('', 'getLocationInfoCb');
                }
            })
        },
        // 商铺签到
        async shopSign(longitude, latitude, address) {
            if (this.busiType == 4 && this.isKdEndDateIn3Month()) {
                // 本网到期
                if (!this.shopInfo) return false
                return await this.shopSignSubmit(longitude, latitude, address);
            }
            return true;
        },
        // 商铺签到提交
        async shopSignSubmit(longitude, latitude, address) {
            const param = {
                operMsisdn: this.uinfo.servNumber, // 手机号码
                operName: this.uinfo.operatorName,//
                operId: this.uinfo.crmId,// 操作人工号
                sceneId: this.shopInfo.shopId,// 商铺id
                sceneName: this.shopInfo.shopName,// 场景名称
                sceneType: 4, // 场景类型 1:楼宇 2:园区 3:酒店 4:商铺 5:道路
                regionId: this.shopInfo.regionId,// 地市编码
                areaId: this.shopInfo.areaId,// 区县编码
                gridId: this.shopInfo.gridId,// 网格编码
                roadId: this.shopInfo.roadId,// 网格编码
                signInLatitude: latitude,// 签到维度
                signInLongitude: longitude,// 签到经度
                signInAddress: address,// 签到地址
                mpResultType: '1',
                nextTime: dateFormat(new Date(), 'yyyyMMddhhmmss'),
                isIntent: '0',
                remark: '商机反馈签到'
            }
            console.info(param)
            const url = '/xsb/gridCenter/gridMap/h5GridSignInSubmit';
            const res = await this.$http.post(url, param);
            let { retCode, retMsg } = res.data;
            if (retCode == '0') {
                return await this.shopSignSubmit2(longitude, latitude, address)
            } else {
                this.$alert(retMsg || '签到提交失败');
            }
        },
        // 商铺签到提交
        async shopSignSubmit2(longitude, latitude, address) {
            const param = {
                streamSeq: new Date().getTime(),
                crmId: this.uinfo.crmId,
                region: this.uinfo.region,
                operId: this.uinfo.crmId,
                operMsisdn: this.uinfo.servNumber,
                operName: this.uinfo.operatorName,
                operTime: dateFormat(new Date(), 'yyyyMMddhhmmss'),
                regionId: this.shopInfo.regionId,
                regionName: this.shopInfo.regionName,
                areaId: this.shopInfo.areaId,
                areaName: this.shopInfo.areaName,
                gridId: this.shopInfo.gridId,
                gridName: this.shopInfo.gridName,
                roadId: this.shopInfo.roadId,
                roadName: this.shopInfo.roadName,
                shopId: this.shopInfo.shopId,
                shopName: this.shopInfo.shopName,
                address: address,
                latitude: latitude,
                longitude: longitude,
                mpResultType: '1',
                nextTime: dateFormat(new Date(), 'yyyyMMddhhmmss'),
                isIntent: '0',
                remark: '商机反馈签到'
            }
            const url = '/xsb/gridCenter/gridMap/h5signSubmit';
            const res = await this.$http.post(url, param);
            let { retCode, retMsg } = res.data;
            if (retCode == '0') {
                return true;
            } else {
                this.$alert(retMsg || '签到提交失败');
                return false;
            }
        },
        // 商铺单选
        shopRadioClick(title, val) {
            if (this.busiType == 2 && title.titleName == '摸排结果') {
                // 下线商铺
                const status = this.titleList.find(item => item.titleName == '商铺状态');
                if (!status) return;
                if (val.id == 131 || val.label == '属实') {
                    this.$set(status, 'selectOptions', [{id: '3', label: '已关闭'}, {id: '5', label: '招租'}]);
                } else {
                    this.$set(status, 'selectOptions', [{id: '1', label: '正常营业'}, {id: '2', label: '暂停营业'}, {id: '4', label: '装修中'}]);
                }
                this.$set(status, 'tempKey', '');
                this.$set(status, 'tempValue', '');
            }
        },

        // 处理模板标题
        handleTitle(data) {
            const title = {
                titleName: data.titleName,
                putType: data.putType,
                tempOption: data.tempOption,
                tempKey: '',
                tempValue: '',
                isSign: false,
                isCheck: false,
                isPhoto: false
            }
            const type = data.putType;
            if (type == '2' || type == '3' || type == '4') {
                // 多选框、选择框、下拉框
                const selectOptions = []
                const ids = data.tempValue.split('|');
                const labels = data.tempKey.split('|');
                for (let i = 0; i < ids.length; i++) {
                    selectOptions.push({ id: ids[i], label: labels[i] })
                }
                title.selectOptions = selectOptions;
            }
            if (type == '5') {
                title.format = data.tempKey;
            }
            return title;
        },
        // 上传照片
        async uploadPic(img) {
            const param = {
                photoStr: img,
                comeFrom: 'gridSandShop',
                unEncrpt: true
            };
            const url = '/xsb/personBusiness/gridSand/h5GridSandPhotoSub';
            const res = await this.$http.post(url, param);
            let { retCode, retMsg, data } = res.data;
            if (retCode == '0') {
                return data
            } else {
                this.$alert(retMsg || '照片上传失败');
                return '';
            }
        },
        // 提交商铺运营状态
        async submitShopRunStatus(runStatus) {
            if (!this.shopData) return false;
            this.shopData.streamSeq = new Date().getTime();
            this.shopData.operType = '2';
            this.shopData.dataSource = '3';
            this.shopData.crmId = this.uinfo.crmId;
            this.shopData.region = this.uinfo.region;
            this.shopData.operId = this.uinfo.servNumber;
            this.shopData.runStatus = runStatus;
            console.info('submitShopRunStatus', this.shopData);
            const url = '/xsb/gridCenter/gridMap/h5shopInfoSubmit';
            const res = await this.$http.post(url, this.shopData);
            let { retCode, retMsg } = res.data;
            if (retCode == '0') {
                return true;
            } else {
                this.$alert(retMsg || '商铺摸排信息保存失败');
                return false;
            }
        },
        async checkShopData(title) {
            if (this.busiType == 1 && title.putType == 11) {
                // 上线商铺-摸排结果
                const mpResult = await this.$refs.shopMpResult[0].submit();
                if (!mpResult) return false;
                title.tempKey = mpResult.tempKey;
                title.tempValue = mpResult.tempValue;
            }

            if (this.busiType == 2 && title.titleName == '商铺状态' && title.tempValueBak != title.tempValue) {
                // 商铺下线-商铺状态
                const flag = await this.submitShopRunStatus(title.tempValue)
                if (!flag) return false;
                title.tempValueBak = title.tempValue;
            }
            const isYwdq = this.ywdqList.find(item => item == this.busiType);
            if (isYwdq && title.titleName == '结果分类') {
                // 异网到期-结果分类
                if (title.tempKey == '待继续跟踪' || title.tempValue == '132') {
                    this.$alert('待继续跟踪，暂不能提交反馈');
                    return false;
                }

                if (title.tempKey == '已本网替换' || title.tempValue == '133') {
                    if (this.getBroadband() != '1') {
                        this.$alert('宽带账号非本网，请核实');
                        return false;
                    }
                }
            }
            return true;
        },
        // 获取宽带运营商
        getBroadband() {
            if (!this.shopInfo || !this.shopInfo.broadbandInfo) return '';
            return this.shopInfo.broadbandInfo.broadband
        },
        // 判断宽带到期时间是否在3个月内
        isKdEndDateIn3Month() {
            if (!this.shopInfo || !this.shopInfo.broadbandInfo) return false;
            const kdEndDate = this.shopInfo.broadbandInfo.kdEndDate;
            if (!kdEndDate) return false;
            const diffDays = this.getDiffDays(chgStrToDate(kdEndDate))
            return diffDays <= 90;
        },
        // 判断日期与当前时间相差多少天
        getDiffDays(date) {
            const now = new Date();
            let diff = now - chgStrToDate(date)
            diff = diff / (1000 * 60 * 60 * 24)
            return diff;
        },
        // 打印日志
        show() {
            console.info(this.titleList)
        },
        // 检查数据
        async checkData() {
            for (let title of this.titleList) {
                if (title.tempOption == 2 && !title.tempValue && title.putType != 11) {
                    // 必填项
                    this.$messagebox({ title: '温馨提示', message: `请完成【${title.titleName}】`})
                    return false;
                }

                if (title.putType == 7 && title.tempValue && !title.tempKey) {
                    // 拍照上传照片
                    const picId = await this.uploadPic(title.tempValue)
                    if (!picId) return false;
                    title.tempKey = picId;
                    title.tempValue = picId;
                }

                if (this.busiType) {
                    const flag = await this.checkShopData(title);
                    if (!flag) return false;
                }
            }
            return true;
        },
        // 提交数据
        async submit() {
            const flag = await this.checkData();
            if (!flag) return null;
            const feedbackData = [];
            for (let title of this.titleList) {
                feedbackData.push({
                    titleName: title.titleName,
                    putType: title.putType,
                    tempKey: title.tempKey,
                    tempValue: title.tempValue
                })
            }
            return feedbackData;
        }
    },
    beforeMount() {
        if (this.shopInfo) {
            this.shopData = cloneDeep(this.shopInfo)
            const shopInfo = this.shopInfo.shopInfo;
            this.shopData.shopInfo = shopInfo ? JSON.stringify(shopInfo) : shopInfo;
            const broadbandInfo = this.shopInfo.broadbandInfo;
            this.shopData.broadbandInfo = broadbandInfo ? JSON.stringify(broadbandInfo) : broadbandInfo;
        }
        for (let data of this.templateData) {
            this.titleList.push(this.handleTitle(data))
        }
    },
    mounted() {
        window['getLocationInfoCb'] = (result) => {
            if (result && result.longitude && result.latitude) {
                this.locationSuccess(result)
            } else {
                this.locationError()
            }
        };
        window['photo'] = (info) => {
            this.$set(this.curTitle, 'isPhoto', true)
            this.curTitle.tempValue  = info.fileImage;
            this.curTitle.tempKey = ''; // 照片编码
        }
    }
}
</script>

<style lang='less' scoped>
.temp-wrapper {
    padding: 0px 12px 12px 12px;
}

.title-name {
    font-size: 14px;
    color: #3D3D3D;
    line-height: 16px;
    font-weight: 500;
    margin-top: 20px;

    .must-do {
        line-height: 12px;
        color: red;
    }
}
.title-line {
    margin-top: 12px;
    margin-left: 16px;
    margin-right: 16px;
}

.border-line {
    border-bottom: 1px solid #DDDDDD;
    padding-bottom: 8px;
}

.temp-input {
    input {
        outline: none;
        background: none;
        border: none;
        font-size: 13px;
        height: 20px;
        line-height: 20px;
        width: 100%;
        color: #3D3D3D;
    }
}

.temp-checkbox {
    .cb-item {
        display: flex;
        justify-content: space-between;
        border-bottom: 1px solid #DDDDDD;
        font-size: 12px;
        color: #3D3D3D;
        padding-top: 10px;
        padding-bottom: 10px;
        align-content: center;

        &:first-child {
            padding-top: 6px;
        }

        span {
            height: 20px;
            line-height: 20px;
        }
    }

    .active {
        color: #007AFF;
    }
}

.temp-textbtn {
    display: flex;
    font-size: 12px;
    color: #3D3D3D;
    justify-content: space-between;
    flex-direction: row;

    .temp-text {
        font-size: 13px;
        margin-top: 10px;
    }

    .temp-btn {
        padding: 3px 8px;
        border-radius: 5px;
        color: #fff;
        background: #1680F9;
        height: 20px;
        line-height: 20px;
    }
}
</style>
