<template>
    <div class="main-wrapper">
        <div class="head-top">
            <div class="head-title">
                <span class="iconfont zuojiantou head-back" @click='goBack'></span>
                <span class="head-title-txt">道路商机任务</span>
                <span class="iconfont sousuo search-icon" @click='searchShow=true'></span>
            </div>
            <div class='feedback-search'>
                <span class='feedback' :class="{'feedback-active': !feedbackType}" @click="feedbackFilter('')">全部商铺</span>
                <span class='feedback' :class="{'feedback-active': feedbackType == '0'}" @click="feedbackFilter('0')">未反馈</span>
                <span class='feedback' :class="{'feedback-active': feedbackType == '1'}" @click="feedbackFilter('1')">已反馈</span>
                <span class='feedback busi' :class="{'busi-active': busiType != ''}" @click='busiFilterShow = !busiFilterShow'>{{ busiTypeName }}<span class='iconfont xiala xl-icon'></span></span>
            </div>
        </div>
        <div v-show='busiFilterShow' class='busi-list'>
            <ul>
                <li v-for='(item, index) in busiTypeList' :class="{'busi-active': busiType == item.value}" :key='index' @click='busiFilter(item.value, item.text)'>
                    {{ item.text }}
                </li>
            </ul>
        </div>
        <div class='task-btns'>
            <div class='task-btn' @click='transShow=true'>
                <span class="iconfont zhuanpai task-icon"></span>
                <span class="task-text">任务转派</span>
            </div>
            <div class='task-btn' @click='goTaskPage'>
                <span class="iconfont xiangqing1 task-icon"></span>
                <span class="task-text">任务详情</span>
            </div>
        </div>
        <div v-show='!busiFilterShow' class='dingwei' @click='getPosition'>
            <span class='iconfont a-zu5638101x dingwei-icon'></span>
        </div>
        <map-container class="map-class" @load="mapLoad"></map-container>
        <!-- 商铺列表 -->
        <div class='shop-list-wrapper' v-show='shopListShow'>
            <div class='shop-item' v-for='(shop, index) in shopList' :key='index' @click='shopMarkerClick(shop.objectId)'>
                <div class='shop-img-wrap'>
                    <div class='shop-img-div'>
                        <img class='shop-img' src="static/mapGrid/default_shop.png"/>
                        <span class='shop-icon feedback-icon' :class="{'unfeedback': shop.feedback != '1'}">{{ shop.feedback == '1' ? '已反馈' : '未反馈' }}</span>
                    </div>
                </div>
                <div class='shop-content'>
                    <div class='shop-title'>
                        <span class='shop-name'>{{ shop.objectName }}</span>
                    </div>
                    <div class='shop-icons'>
                        <span v-for='(item, index) in shop.busiTypeNameList' :key='index' class='shop-icon busi-icon'>{{ item }}</span>
                    </div>
                </div>
                <div class='feedback-wrapper'>
                    <div class='feedback-btn'>
                        <span class='iconfont bianji feedback'></span>
                        <span class='feedback-txt' @click.stop='goFeedbackPage(shop.objectId)'>反馈</span>
                    </div>
                    <div class='shop-distance'>
                        <img src='@/assets/img/gride/location.png'/>
                        <span>{{ shop.distance }}</span>
                    </div>
                </div>
            </div>
            <NoDataPage v-if='!shopList || shopList.length == 0'></NoDataPage>
        </div>
        <!-- 商铺详情 -->
        <div class='shop-wrapper' v-show='!shopListShow'>
            <div class='shop-detail'>
                <div>
                    <img class='shop-img' :src="shopInfo.picBase64"/>
                </div>
                <div class='shop-content'>
                    <div class='shop-name'>{{ shopInfo.shopName }}</div>
                    <div class='shop-type'>
                        <span :class="[shopInfo.isMp == '1' ? 'blue-type' : 'gray-type']">{{ shopInfo.isMp == '1' ? '已' : '未' }}摸排</span>
                        <span class='orange-type'>{{ getBroadBand(shopInfo.broadband) }}</span>
                    </div>
                    <div class='shop-distance'>
                        <img src='@/assets/img/gride/location.png'/>
                        <span>距离{{ shopInfo.distance }}</span>
                    </div>
                    <div class='shop-addr'>{{ shopInfo.address }}</div>
                </div>
                <div class='shop-btn'>
                    <a :href="'tel:' + shopInfo.lxMobile" v-if='shopInfo.lxMobile'>
                        <div>
                            <img src="@/assets/img/gride/dianhua.png"/>
                            <span>电话</span>
                        </div>
                    </a>
                    <div @click='goNaviPage'>
                        <img src="@/assets/img/gride/daohang.png"/>
                        <span>导航</span>
                    </div>
                </div>
                <div class='feedback' :class="{'unfeedback': shopInfo.feedback != '1'}">{{ shopInfo.feedback == '1' ? '已反馈' : '未反馈' }}</div>
            </div>
            <div class='shop-btns'>
                <div class='shop-skip-btn shop-btn-spst' @click='goShopDetailPage'>
                    <img src="@/assets/img/gride/spst.png"/>
                    <span>商铺视图</span>
                </div>
                <div class='shop-skip-btn shop-btn-ksfk' @click='goFeedbackPage(shopInfo.shopId)'>
                    <span class="iconfont bianji btn-ksfk"></span>
                    <span class='txt-ksfk'>快速反馈</span>
                </div>
            </div>
        </div>
        <div class='return-list-btn' v-show='!shopListShow' @click='shopListShow = true'>
            <span class="iconfont zuojiantou return-icon"></span>
            <span class='return-txt'>返回列表</span>
        </div>
        <div v-show='!shopListShow' class='bottom-mask'></div>
        <TransferDetailNew v-show="transShow" @emCloseTrans="transShow=false" @emSubmitTrans="submitTrans"></TransferDetailNew>
        <ShopSearch v-show='searchShow' :shopList='shopListBak' @shopMarkerClick='shopMarkerClick' @goFeedbackPage='goFeedbackPage' @emClose='searchShow=false'></ShopSearch>
    </div>
</template>

<script>
import MapContainer from '../../../map/MapContainer.vue'
import ClientJs from '../../../../base/clientjs'
import Storage from "@/base/storage.js"
import NoDataPage from 'components/common/NoDataPage.vue'
import TransferDetailNew from'components/business/GridHomework/TransferDetailNew.vue'
import ShopSearch from'components/business/GridHomework/shopBusi/ShopBusiSearch.vue'
import { dateFormat } from "@/base/utils"

export default {
    components: { MapContainer, NoDataPage, TransferDetailNew, ShopSearch },
    name: 'ShopBusiMap',
    data() {
        return {
            uinfo: Storage.session.get("userInfo"),
            map: null,
            shopLabelsLayer: null,
            shopMakerMap: new Map(),
            curShopId: null, // 当前点击的商铺ID
            myLatLng: null, // 当前位置经纬度
            myLocationMarker: null, // 我的位置图标
            shopListShow: true, // 是否展示商铺列表
            transShow: false, // 是否展示转派页面
            searchShow: false, // 是否展示搜索页面
            shopList: [], // 商铺列表
            shopListBak: [], // 商铺列表备份
            shopInfo: {}, // 商铺详情
            shopInfoMap: new Map(), // 商铺详情缓存
            feedbackType: '', // 反馈类型：1：已反馈 0：未反馈
            busiType: '', // 商机类型
            busiTypeName: '全部商机', // 商机类型
            busiFilterShow: false, // 商机筛选展示
            broadBandList: [{text: '移动', value: '1'}, {text: '联通', value: '2'}, {text: '电信', value: '3'}, {text: '其他', value: '4'}, {text: '广电', value: '5'}], // 宽带类型
            busiTypeList: [{text: '全部商机', value: ''}, {text: '上线商铺', value: '1'}, {text: '下线商铺', value: '2'}, {text: '异网到期', value: '3'}, {text: '本网到期', value: '4'}],
            ywdqList: ['3', '5', '6', '7', '8'] // 异网到期
        }
    },
    methods: {
        //获取对象列表
        async getTaskObjList() {
            const param = {
                msisdn: this.uinfo.servNumber,
                taskId: this.$route.query.taskId
            }
            const url = '/xsb/gridCenter/gridHomework/h5GridTaskSendTempObjectShopupQuery';
            const res = await this.$http.post(url, param);
            let { retCode, retMsg, data } = res.data
            if (retCode === '0') {
                const objList = data.objectList;
                if (objList && objList.length > 0) {
                    for(let obj of objList) {
                        const lnglat = obj.objectAttr1.split(',');
                        obj.longitude = lnglat[0];
                        obj.latitude = lnglat[1];
                        obj.latlng = [obj.longitude, obj.latitude];
                        obj.feedback = obj.objectAttr4;
                        obj.busiTypeList = obj.objectAttr5 ? obj.objectAttr5.split("|") : [];
                        obj.busiTypeNameList = obj.objectAttr6 ? obj.objectAttr6.split("|") : [];
                        this.setBusiType(obj.busiTypeList, obj.busiTypeNameList);
                        obj.distance = this.getDistance(obj.latlng);
                    }
                    this.shopList = data.objectList;
                    this.shopListBak = data.objectList;
                    this.moveMapTo(this.shopList[0].latlng);
                    this.addShopListMarker(this.shopList);
                }
            } else {
                this.$alert(retMsg || '获取商铺列表失败');
            }
        },
        // 设置商机类型
        setBusiType(busiTypeList, busiTypeNameList) {
            for (let i = 0; i < busiTypeList.length; i++) {
                const flag = this.ywdqList.find(item => busiTypeList[i] == item);
                if (flag) {
                    // 异网到期
                    busiTypeList[i] = '3';
                    busiTypeNameList[i] = '异网到期'
                }
            }
        },
        // 获取商铺详情
        async getShopDetails(shop) {
            const shopInfo = this.shopInfoMap.get(shop.objectId);
            if (shopInfo) {
                shopInfo.distance = this.getDistance(shopInfo.latlng);
                this.shopInfo = shopInfo;
                this.shopListShow = false;
                return;
            }
            const url = '/xsb/gridCenter/gridMap/h5queryShopInfo';
            const params = {
                streamSeq: new Date().getTime(),// 唯一流水号
                dataSource: '2',
                operId: this.uinfo.servNumber,// 手机号码
                regionId: this.uinfo.region, // 地市编码
                shopId: shop.objectId,//商铺ID
            };
            const res = await this.$http.post(url, params);
            let { retCode, retMsg, data } = res.data
            if (retCode === '0') {
                this.shopInfo = data;
                this.shopInfo.latlng = shop.latlng;
                this.shopInfo.feedback = shop.feedback;
                this.shopInfo.workOrder = shop.workOrder;
                this.shopInfo.tempName = shop.tempName;
                this.shopInfo.tempId = shop.tempId;
                this.shopInfo.distance = this.getDistance(shop.latlng);
                this.shopInfo.picBase64 = await this.getOBSImg(this.shopInfo.picId);
                this.shopInfoMap.set(shop.objectId, this.shopInfo)
                this.shopListShow = false;
            } else {
                this.$alert(retMsg || '获取商铺详情失败');
            }
        },
        // 筛选商铺
        filterShop() {
            let shops = this.shopListBak;
            if (this.feedbackType) {
                shops = shops.filter(item => item.feedback == this.feedbackType)
            }
            if (this.busiType) {
                shops = shops.filter(item => {
                    for (let busiType of item.busiTypeList) {
                        if (busiType == this.busiType) return true;
                    }
                    return false;
                })
            }
            this.shopListShow = true;
            this.shopList = shops;
            this.addShopListMarker(this.shopList);
        },
        // 反馈筛选
        feedbackFilter(val) {
            if (this.feedbackType == val) return;
            this.feedbackType = val;
            this.filterShop();
        },
        // 商机筛选
        busiFilter(val, name) {
            if (this.busiType == val) {
                this.busiType = '';
                this.busiTypeName = '全部商机';
                this.filterShop();
                return;
            }
            this.busiType = val;
            this.busiTypeName = name;
            this.filterShop();
        },
        // 转派提交
        async submitTrans(data) {
            const params = {
                taskId: this.$route.query.taskId,
                taskName: decodeURIComponent(atob(this.$route.query.taskName)),
                activeId: '1',
                msisdnGet: data.turntoMsisdn,
                nameGet: data.turntoName,
                operDate: dateFormat(new Date(),'yyyy/MM/dd hh:mm:ss'),
                content: '转派至' + data.turntoName,
                msisdnPut: this.uinfo.servNumber,
                namePut: this.uinfo.operatorName
            }
            const url = '/xsb/gridCenter/gridHomework/h5TransferSubmit';
            const res = await this.$http.post(url, params);
            let { retCode, retMsg} = res.data
            if (retCode == '0') {
                this.$alert('任务转派成功');
                this.goBack();
            } else {
                this.$alert(retMsg || '任务转派失败');
            }
        },
        // 下载图片
        async getOBSImg(picId) {
            if (!picId) {
                return 'static/mapGrid/default_shop.png';
            }
            const url = '/xsb/gridCenter/gridMap/h5getPic';
            const params = {
                picId: picId,
                fileType: 'qdyy'
            };
            const res = await this.$http.post(url, params);
            let { retCode, data } = res.data
            if (retCode == '0') {
                let mimeType = 'image/jpeg';
                if (picId.endsWith('.png')) {
                    mimeType = 'image/png';
                } else if (picId.endsWith('.jpg') || picId.endsWith('.jpeg')) {
                    mimeType = 'image/jpeg';
                }
                // 拼接完整的Base64图片URL
                return `data:${mimeType};base64,${data}`;
            } else {
                return 'static/mapGrid/default_shop.png';
            }
        },
        // 获取距离
        getDistance(latlng) {
            try {
                const distance = AMap.GeometryUtil.distance(latlng, this.myLatLng);
                return distance >= 1000 ? Number(distance / 1000).toFixed(2) + 'km' : parseInt(distance) + 'm';
            } catch (e) {
                console.error('计算距离失败');
                console.error(e);
                return '--m'
            }
        },
        // 重新计算距离
        reCalcDistance() {
            for (let shopBak of this.shopListBak) {
                shopBak.distance = this.getDistance(shopBak.latlng);
            }
            if (this.shopListShow) {
                for (let shop of this.shopList) {
                    Vue.set(shop, 'distance', this.getDistance(shop.latlng));
                }
            } else {
                Vue.set(this.shopInfo, 'distance', this.getDistance(shop.latlng));
            }
        },
        // 获取宽带信息
        getBroadBand(val) {
            let obj = this.broadBandList.find(item => item.value === val);
            return obj ? obj.text : '其他';
        },
        // 地图加载完成
        mapLoad(mapInstance) {
            this.map = mapInstance;
            this.initMap();
            // 测试
            // const position = new AMap.LngLat(118.785381,32.083484);
            // this.addMyLocationMarker(position);
            this.getPosition();
            this.getTaskObjList();
        },
        // 初始化地图
        initMap() {
            // 添加图层
            this.shopLabelsLayer = new AMap.LabelsLayer({
                collision: false,
                allowCollision: false,
            });
            this.map.add(this.shopLabelsLayer);
            // 只展示道路和建筑
            this.map.setFeatures(['bg', 'road', 'building'])
        },
        // 商铺列表地图打点
        addShopListMarker(shopList) {
            this.shopLabelsLayer.clear();
            this.shopMakerMap.clear();
            for(let shop of shopList) {
                this.addShopMarker(shop)
            }
        },
        // 商铺打点
        addShopMarker(shop, size = [56, 61], offset = [-10, -5], fontSize = 12, zIndex = 3) {
            if (!shop) return;
            const shopMarker = new AMap.LabelMarker({
                name: shop.objectName,
                position: [shop.longitude, shop.latitude],
                zIndex: zIndex,
                icon: {
                    image: shop.feedback == '1' ? 'static/mapGrid/shop_yfk.png' : 'static/mapGrid/shop_wfk.png',
                    size: size,
                    anchor: 'bottom-center'
                },
                text: {
                    content: shop.objectName.length > 4 ? shop.objectName.substring(0, 4) : shop.objectName,
                    direction: 'right',
                    offset: offset,
                    style: {
                        fontSize: fontSize,
                        fillColor: '#404040',
                        strokeColor: '#FFFFFF',
                        strokeWidth: 2,
                        fold: true,
                    }
                },
                extData: {
                    shopId: shop.objectId,
                }
            });
            shopMarker.on('click', (e) => this.shopMarkerClick(e.target.getOptions().extData.shopId));
            this.shopMakerMap.set(shop.objectId, shopMarker);
            this.shopLabelsLayer.add(shopMarker);
        },
        // 商铺打点点击
        shopMarkerClick(shopId) {
            // 获取商铺详情
            const shop = this.shopListBak.find(item => item.objectId == shopId);
            this.getShopDetails(shop);

            // 地图中心点移动到当前商铺
            this.moveMapTo(shop.latlng)

            if (this.curShopId == shopId) {
                return;
            }
            // 将先前点击的商铺点缩小
            if (this.curShopId) {
                this.removeShopMaker(this.curShopId)
                const preShop = this.shopList.find(item => item.objectId == this.curShopId);
                this.addShopMarker(preShop);
            }

            // 将当前点击的商铺放大
            this.removeShopMaker(shopId)
            this.addShopMarker(shop, [82, 89], [-14, -6], 13,4);
            this.curShopId = shopId;
        },
        // 移除商铺打点
        removeShopMaker(shopId) {
            const shopMaker = this.shopMakerMap.get(shopId);
            if (shopMaker) {
                this.shopLabelsLayer.remove(shopMaker);
            }
        },
        // 移动地图
        moveMapTo(latlng) {
            this.map.panTo(latlng);
            if (this.map.getZoom() < 17) {
                this.map.setZoom(17);
            }
        },
        // 获取当前定位
        getPosition() {
            ClientJs.getLocation('', 'getLocationInfoCb');
        },
        // 添加我的位置图标
        addMyLocationMarker(position) {
            if (!position || !position.getLng() || !position.getLat()) {
                console.error("获取当前位置失败")
                return;
            }
            this.myLatLng = [position.getLng(), position.getLat()]
            this.reCalcDistance();
            if (this.myLocationMarker) {
                this.map.remove(this.myLocationMarker);
                this.myLocationMarker = null;
            }

            this.myLocationMarker = new AMap.Marker({
                position: position,
                content: '<img style="width: 57px; height: 57px;" src="static/mapGrid/default.png">',
                anchor: 'center',
                zIndex: 2
            });
            this.map.add(this.myLocationMarker);
        },
        // 跳转反馈页
        goFeedbackPage(shopId) {
            const shop = this.shopListBak.find(item => item.objectId == shopId);
            this.$router.push({
                path: '/workHandle',
                query: {
                    taskId: this.$route.query.taskId || '1',
                    comeFrom: this.$route.query.comFrom,
                    srcFrom: 'shopBusiMap',
                    handleType: 1, // 1 反馈 2详情
                    workOrder: shop.workOrder,
                    tempName: btoa(encodeURIComponent(shop.tempName)),
                    tempId: shop.tempId
                }
            })
            // const shop = this.shopListBak.find(item => item.objectId == shopId);
            // const workSubList = []; // 子工单
            // const workList = shop.workSubList || [];
            // for (let work of workList) {
            //     const subWork = {}
            //     subWork.workOrder = work.workOrderSub;
            //     subWork.tempId = work.tempIdSub;
            //     subWork.tempName = work.tempNameSub
            //     subWork.status = work.statusSub;
            //     subWork.objClassId = work.objClassIdSub;
            //     subWork.objClassName = work.objClassNameSub;
            //     const flag = this.ywdqList.find(item => work.objClassIdSub == item);
            //     subWork.busiType = flag ? '异网到期' : work.objClassNameSub;
            //     workSubList.push(subWork);
            // }
            // const body = {
            //     shopId: shop.objectId, // 商铺编码
            //     shopName: shop.objectName, // 商铺名称
            //     taskId: this.$route.query.taskId, // 任务编码
            //     workOrder: shop.workOrder, // 工单编码
            //     workSubList: workSubList // 子工单
            // }
            // const json = JSON.stringify(body);
            // this.$router.push('/shopBusiFeedback?body=' + btoa(encodeURIComponent(json)));
            //
            // this.jobTaskTurn(body)
        },
        // 商铺商机转化校验信息采集接口
        async jobTaskTurn(taskInfo) {
            const param = {
                crmId: this.uinfo.crmId, // crm工号
                region: this.uinfo.region, // 地市
                taskId: taskInfo.taskId, // 任务编码
                workOrder: taskInfo.workOrder, // 工单编码
                objectId: taskInfo.shopId, // 商铺编码
                objectName: taskInfo.shopName, // 商铺名称
                isPageClick: '是', // 页面是否点击
                checkStatus: '成功', // 页面校验结果
                workOrderSub: '1',
                objClassIdSub: '1',
                objClassNameSub: '1'
            }
            console.info('h5GridJobTaskTurnToInfo param', param)
            const url = '/xsb/gridCenter/gridHomework/h5GridJobTaskTurnToInfo';
            const res = await this.$http.post(url, param);
            let { retCode, retMsg } = res.data
            if (retCode == '0') {
                console.info('商铺商机转化校验信息采集接口调用成功')
            } else {
                console.error(retMsg || '商铺商机转化校验信息采集接口调用失败');
            }
        },
        // 跳转任务页
        goTaskPage() {
            this.$router.push({
                path: '/workHandle',
                query: {
                    taskId: this.$route.query.taskId,
                    comeFrom: this.$route.query.comFrom,
                    srcFrom: 'shopBusiMap'
                }
            })
        },
        // 跳转商铺详情页
        goShopDetailPage() {
            const url = `/subWgt?path=shopDetail&orgaId=${this.shopInfo.shopId}&longitude=${this.myLatLng[0]}&latitude=${this.myLatLng[1]}&gobackFlag=ald`;
            console.info(url);
            this.$router.push(url);
        },
        // 跳转导航页
        goNaviPage() {
            const url = `subWgt?path=drive?type=2&shopLongitude=${this.shopInfo.latlng[0]}&shopLatitude=${this.shopInfo.latlng[1]}&shopName=${btoa(encodeURIComponent(this.shopInfo.shopName))}`;
            this.$router.push(url)
        },
        // 返回
        goBack() {
            history.go(-1)
        }
    },
    mounted() {
        window['getLocationInfoCb'] = (result) => {
            console.info(result)
            if (result && result.longitude && result.latitude) {
                const position = new AMap.LngLat(result.longitude, result.latitude);
                this.addMyLocationMarker(position);
            }
        }
    }
}
</script>

<style lang='less' scoped>
.main-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
}
.head-top {
    width: 100%;
    height: auto;
    overflow: hidden;
    position: fixed;
    top: 0px;
    left: 0px;
    z-index: 200;
    box-shadow: 0px 4px 10px 0px rgba(0,0,0,0.08);

    .head-title {
        height: 44px;
        text-align: center;
        position: relative;
        background: #fff;
        border-bottom: 1px solid #EDEDED;
    }

    .head-back {
        position: absolute;
        left: 0px;
        top: 50%;
        font-size: 20px;
        padding: 8px;
        transform: translateY(-50%);
    }

    .head-title-txt {
        color: #3D3D3D;
        font-size: 16px;
        display: inline-block;
        margin-top: 14px;
    }

    .search-icon {
        position: absolute;
        right: 10px;
        bottom: 10px;
        font-size: 26px;
        color: #007AFF;
    }
}

.feedback-search {
    display: flex;
    background-color: #fff;
    padding: 8px 12px;
    justify-content: space-between;
    align-items: center;

    .feedback {
        display: inline-block;
        width: 75px;
        padding: 8px 0px;
        text-align: center;
        font-size: 13px;
        color: #535353;
        background: #EBF4FF;
        border-radius: 8px 8px 8px 8px;
        margin-right: 8px;
    }

    .feedback-active {
        background: #007AFF;
        color: #FFFFFF;
    }

    .busi {
        width: 95px;
        background: #FFFFFF;
        border: 1px solid #DFDFDF;
        padding: 0;
        height: 28px;
        line-height: 28px;

        .xl-icon {
            font-size: 10px;
            color: #007AFF;
            margin-left: 5px;
            font-weight: 700;
        }
    }
}

.busi-list {
    position: absolute;
    top: 101px;
    z-index: 200;
    right: 25px;

    ul {
        background: #FFFFFF;
        font-size: 13px;
        color: #3D3D3D;
        border-radius: 8px;
        padding-top: 5px;

        li {
            width: 85px;
            padding: 10px 0px;
            text-align: center;
        }
    }
}

.busi-active {
    color: #007AFF !important;
    font-weight: 500 !important;
}

.task-btns {
    position: absolute;
    left: 10px;
    top: 101px;
    z-index: 200;

    .task-btn {
        display: flex;
        align-items: center;
        height: 30px;
        line-height: 30px;
        background: #FFFFFF;
        border-radius: 8px;
        font-size: 12px;
        color: #007AFF;
        padding: 0 6px;
        margin-bottom: 8px;
    }

    .task-text {
        font-size: 12px;
        margin-left: 5px;
    }

    .task-icon {
        font-size: 12px;
    }
}

.map-class {
    position: absolute
}

.shop-list-wrapper {
    position: fixed;
    height: 30%;
    background: #FFFFFF;
    left: 10px;
    right: 10px;
    bottom: 5px;
    z-index: 200;
    box-shadow: 0px 8px 10px 0px rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    padding: 8px 12px;
    overflow-y: auto;

    .shop-item {
        display: flex;
        flex-direction: row;
        padding: 8px 0px;
        border-bottom: 1px solid #F0F0F0;
        &:first-child {
            padding-top: 5px;
        }
    }

    .shop-img-wrap {
        display: flex;
        justify-content: center;
        align-items: center;

        .shop-img-div {
            position: relative;
        }

        .feedback-icon {
            position: absolute;
            right: 0;
            top: 0;
            font-size: 10px;
            color: #FFFFFF;
            background: #38B232;
            border-radius: 0 0 0 6px;
            padding: 2px 3px;
        }
    }

    .shop-img {
        width: 55px;
        height: 52px;
    }

    .shop-content {
        margin-left: 8px;
        margin-top: 3px;
        width: calc(100% - 145px);

        .shop-title {
            display: flex;
            align-items: center;
        }

        .shop-name {
            font-size: 16px;
            font-weight: 600;
            color: #1871D2;
            width: calc(100% - 5px);
            max-width: calc(100% - 5px);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .shop-icons {
            margin-top: 3px;
        }
        .shop-icon {
            display: inline-block;
            font-size: 11px;
            border-radius: 5px;
            padding: 0 8px;
            height: 20px;
            line-height: 20px;
            text-align: center;
            margin-top: 8px;
        }

        .busi-icon {
            background: rgba(227, 136, 62, 0.1);
            color: #E88800;
            margin-right: 5px;
        }
    }

    .feedback-wrapper {
        margin-left: 15px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .feedback-btn {
            display: flex;
            align-items: center;
            height: 28px;
            padding: 0px 12px;
            border-radius: 14px;
            background: #007AFF;
            font-size: 12px;
            color: #FFFFFF;

            .feedback {
                font-size: 12px;
            }

            .feedback-txt {
                margin-left: 5px;
                font-size: 12px;
            }
        }
    }
}

.shop-wrapper {
    position: fixed;
    left: 15px;
    right: 15px;
    bottom: 72px;
    box-shadow: 0px 8px 10px 0px rgba(0, 0, 0, 0.3);
    background: #FFFFFF;
    padding: 18px 12px 12px 12px;
    border-radius: 6px;

    .shop-detail {
        display: flex;
        flex-direction: row;
    }

    .shop-img {
        width: 86px;
        height: 100%;
    }

    .shop-content {
        // flex: 1;
        margin-left: 8px;
        width: calc(100% - 182px);
    }

    .shop-name {
        width: 100%;
        font-weight: 600;
        font-size: 16px;
        color: #000000;
        line-height: 20px;
    }
    .shop-type {
        margin-top: 5px;
        display: flex;
        flex-direction: row;
        span {
            display: inline-block;
            width: 60px;
            height: 20px;
            line-height: 20px;
            border-radius: 5px;
            font-size: 12px;
            text-align: center;
            &:last-child {
                margin-left: 5px;
            }
        }
        .blue-type {
            background: rgba(62,127,227,0.1);
            color: #3E7FE3;
        }
        .gray-type {
            color: #808080;
            background: rgba(186, 186, 186, 0.23);
        }
        .orange-type {
            background: rgba(227,136,62,0.1);
            color: #E88800;
        }
    }

    .shop-addr {
        margin-top: 5px;
        font-size: 12px;
        color: #929292;
    }

    .feedback {
        position: absolute;
        top: 0;
        right: 0;
        height: 20px;
        line-height: 20px;
        font-size: 11px;
        color: #FFFFFF;
        background: #38B232;
        padding: 0px 12px;
        border-radius: 0px 6px 0px 0px;
    }
    .shop-btn {
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-top: 18px;

        div {
            display: flex;
            flex-direction: column;
            text-align: center;

            &:last-child {
                margin-left: 12px;
            }

            img {
                width: 32px;
                height: 32px;
            }

            span {
                font-size: 10px;
                color: #5A7597;
                margin-top: 5px;
                text-decoration: none !important;
            }
        }
    }

    .shop-btns {
        display: flex;
        flex-direction: row;
        margin-top: 12px;

        .shop-skip-btn {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 12px;
            padding: 6px 0px;
        }

        .shop-btn-spst {
            background: #ECF5FF;
            border-radius: 8px;
            color: #1365BF;
            margin-right: 8px;
            img {
                width: 15px;
                height: 16px;
            }
            span {
                display: inline-block;
                line-height: 16px;
                margin-left: 5px;
            }
        }

        .shop-btn-ksfk {
            background: #007AFF;
            border-radius: 8px;
            color: #FFFFFF;
            margin-left: 8px;

            .btn-ksfk {
                font-size: 13px;
            }
            .txt-ksfk {
                display: inline-block;
                line-height: 13px;
                margin-left: 5px;
            }
        }
    }
}


.return-list-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    position: fixed;
    left: 15px;
    right: 15px;
    bottom: 30px;
    background: rgba(0,33,68,0.6);
    height: 32px;
    color: #FFFFFF;
    font-size: 14px;
    border-radius: 6px;

    .return-icon {
        font-size: 13px;
    }
    .return-txt {
        height: 16px;
        line-height: 16px;
        margin-left: 5px;
    }
}

.unfeedback {
    background: #FC7F04 !important;
}

.shop-distance {
    display: flex;
    align-items: center;
    margin-top: 6px;

    img {
        width: 8px;
        height: 11px;
    }

    span {
        font-size: 12px;
        color: #5A7597;
        margin-left: 5px;
    }
}

.dingwei {
    position: absolute;
    top: 101px;
    right: 10px;
    z-index: 100;
    background: #FFF;
    padding: 4px 6px;
    border-radius: 6px;

    .dingwei-icon {
        font-size: 20px;
        color: #222;
    }
}

.bottom-mask {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 20px;
    background: #FFF;
    z-index: 999;
}

a {
    text-decoration: none;
}
</style>
