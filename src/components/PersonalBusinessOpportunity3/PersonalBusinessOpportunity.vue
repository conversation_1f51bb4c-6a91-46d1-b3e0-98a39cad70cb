<template>
    <div class='pbo-wrap'>
        <Header tsTitleTxt="AI商机助理" backType="custom"  tsBtnTxt="商机录入" @emBtnCk="goEnterPage"  @emGoPrev="goPrev"/>


        <mt-popup
            v-model="showDialogFlag"
            position="bottom">
            <div class="chooseDialog" v-if="showDialogFlag">
                <div class="title">请选择本次录入的商机类型 <span @click="showDialogFlag = false" class="iconfont guanbi"></span></div>

                <div class="btnlist">
                    <div @click="goEnterPage('1')">融合服务</div>
                    <div @click="goEnterPage('2')">业务商机</div>
                </div>
            </div>
        </mt-popup>

        <div class='search-box' >
            <div class='search-input' >
                <div class='search-input1'><input type='text' @input='inputConsole' v-model='teltext' :placeholder="'请输入号码'"/></div>
                <span class='iconfont sousuo1' ></span>
                <span class='sou' @click='getListMy()'>搜索</span>
                <span class='iconfont guanbi1' @click="teltext = '';getListMy()"></span>
            </div>
            <div class='drop-drown' >
                <div class='item' @click='changeSearchType0()'>{{type0.label}}<span class='iconfont xiala'></span></div>
                <div class='item' @click='changeSearchType1()'>{{type1.label}}<span class='iconfont xiala'></span></div>
                <div class='item' @click='changeSearchTime1'>{{chooseTime1? '创建时间': '创建时间'}}<span class='iconfont xiala'></span></div>
            </div>
        </div>



        <div class='just-box'>
            <div class='lists-box' >
                <div class='list-item' v-for='(item,idx) in mylist' :key='idx' >

                    <div class='line1'>
                        <span class='tel'><img src="../../assets/img/grsj.png" />{{item.createTel == userInfo.servNumber? item.busiTel : maskPhoneNumber(item.busiTel) }}
                                    <div class='xiansuo' v-show='item.messageVerify != "true"  && item.busiType == 1'>线索</div>

                        </span>
                        <span class='tag tag2'  v-show='item.buopStatus == 2'>跟进中</span>
                        <span class='tag tag0'  v-show='item.buopStatus == 5'>待接单</span>
                        <span class='tag tag3'  v-show='item.buopStatus == 6'>已结单</span>
                        <span class='tag tag0'  v-show='item.buopStatus == 7'>待补充</span>
                        <span class='tag tag0'  v-show='item.buopStatus == 8'>待指派</span>
                        <span class='tag tag0'  v-show='item.buopStatus == 9'>超时结单</span>
                        <span class='tag tag7'  v-show='item.buopStatus == 10'>待人工稽核</span>
                        <span class='tag tag5'  v-show='item.buopStatus == 11'>人工稽核失败</span>
                        <span class='tag tag33' v-show='item.buopStatus == 12'>成功结单</span>
                        <span class='tag tag6'  v-show='item.buopStatus == 13'>失败结单</span>
                        <span class='tag tag7'  v-show='item.buopStatus == 15'>审核待补充</span>
                        <span class='tag tag7'  v-show='item.buopStatus == 16'>补单待审核</span>
                    </div>
                    <div class='tags-box' >
                        <div class='tags tags1' v-show='item.busiType'>{{item.busiType == 1 ? '融合服务' : '业务商机'}}</div>
                        <div class='tags' v-show='item.busiExpireTime' :class='getDateStatus(item.busiExpireTime) && getDateStatus(item.busiExpireTime).indexOf("-")>0? "tags2" : "tags3"'>{{getDateStatus(item.busiExpireTime)}}</div>
                        <div class='tags' v-show='item.serviceDate' :class='getDateStatus2(item.serviceDate) && getDateStatus2(item.serviceDate).indexOf("-")>0? "tags2" : "tags3"'>{{getDateStatus2(item.serviceDate)}}</div>
                        <div class='tags' v-show='item.businServiceDate' :class='getDateStatus2(item.businServiceDate) && getDateStatus2(item.businServiceDate).indexOf("-")>0? "tags2" : "tags3"'>{{getDateStatus2(item.businServiceDate)}}</div>
                    </div>
                    <div class='line2' v-show='item.createOper && (item.createTel != item.followTel)'>
                        <span class='title'>创建人：</span>
                        <span class='value'>{{ item.createOper }}<span v-show='item.createTel'>({{ item.createTel }})</span></span>
                    </div>
                    <div class='line2' v-show='item.createOper && item.followTel &&  (item.createTel == item.followTel)'>
                        <span class='title'>创建/跟进人：</span>
                        <span class='value'>{{ item.followBy ? item.followBy : '--' }}<span v-show='item.followTel'>({{ item.followTel }})</span></span>
                    </div>
                    <div class='line2' v-show='item.createDate'>
                        <span class='title'>创建时间：</span>
                        <span class='value'>{{item.createDate}}</span>
                    </div>
                    <div class='line2' v-show='item.reportDate'>
                        <span class='title'>到岗时间：</span>
                        <span class='value'>{{item.reportDate}}</span>
                    </div>
                    <div class='line2' v-show='item.followTel &&  (item.createTel != item.followTel)'>
                        <span class='title'>当前跟进人：</span>
                        <span class='value'>{{ item.followBy ? item.followBy : '--' }}<span v-show='item.followTel'>({{ item.followTel }})</span></span>
                    </div>
                    <div class='box-btn' >
                        <div class='control-item' @click='goWeihu(item,0)' ><span class='iconfont xingzhuang5'></span>查看</div>
                        <div class='control-item' v-if='userInfo.servNumber == item.followTel' v-show='item.buopStatus == 7' @click='goWeihu(item,1)' ><span class='iconfont xingzhuang5'></span>补充</div>
                    </div>
                </div>
                <no-data-page  v-if='mylist && mylist.length == 0'/>
            </div>
        </div>

    </div>
</template>

<script>
import Header from 'components/common/Header.vue'
import Storage from '@/base/storage.js'
import NlDropdown from 'components/common/NlDropdown/dropdownForBusinessOpportunity.js'
import NlDatePicker from "components/common/NlDatePick2/datePicker.js";
import NoDataPage from '../common/NoDataPage'
import {chgStrToDate, dateFormat} from '@/base/utils';
import ClientJs from '@/base/clientjs'
import ImageObj from '@/base/request/ImageServReq';
const HttpManager = require("@/base/MOAhttpUtil/HttpManager.js");
import Vconsole from 'vconsole'
export default {
    name: 'PersonalBusinessOpportunity',
    components: { NoDataPage, Header},
    data(){
        return{
            formInfo: {
                busiTel: '',
                followtype: '',
                followresult: '',
                thebase64: '',
                filereson: '',
                detailRemark: '',
                resontext: ''
            },
            showDialogFlag: false,
            oldForm: {},
            jiedanFlag: false,
            mylist: [],
            emptylist: [],
            countyId: '',
            suanzhanglist: [],
            teltext: '',
            teltext2: '',
            teltext3: '',
            topType: 1,
            topType2: 1,
            userInfo: {},
            type0: { id: '', label: '商机类型' },
            type1: { id: '', label: '商机状态' },
            inputtype1: {id: '1', label: '商机号码' },
            inputtype2: {id: '1', label: '商机号码' },
            startTime1: '',
            endTime1: '',
            startTime2: '',
            endTime2: '',
            startTime3: '',
            endTime3: '',
            chooseTime1: '',
            chooseTime2: '',
            chooseTime3: '',
            createTime4: '',
            followTime4: '',
            comFormType1: '',
            comFormType2: '',
            showExecute: false, // 指派
            showExecute2: false, // 转派
            orgId: '',
            gangwei: '',
            chooseid: '',
            chooseid2: '',
            chooseid3: '',
            chooseid4: '',
            chooseitem: '',
            chooseitem2: '',
            zhuanpaiParam: {},
            chooseitem1Person: {},
            chooseitem2Person: {},
            choosereson: '',
            showreson2:false,  // 转派弹窗
            showreson3: false, // 释放弹窗
            zhipaiparam: {},
            orgId2: ''

        }
    },

    mounted() {
        this.userInfo = Storage.session.get('userInfo')
        this.gangwei = this.userInfo.stationId == '9988020401021550'
        this.getListMy()
        // //当前网格得分排名查询 获取网格信息
        let arr = [
            {"region":'11',"longRegion":"1000512"}, //苏州
            {"region":'12',"longRegion":"1000517"}, //淮安
            {"region":'13',"longRegion":"1000527"}, //宿迁
            {"region":'14',"longRegion":"1000250"}, //南京
            {"region":'15',"longRegion":"1000518"}, //连云港
            {"region":'16',"longRegion":"1000516"}, //徐州
            {"region":'17',"longRegion":"1000519"}, //常州
            {"region":'18',"longRegion":"1000511"}, //镇江
            {"region":'19',"longRegion":"1000510"}, //无锡
            {"region":'20',"longRegion":"1000513"}, //南通
            {"region":'21',"longRegion":"1000523"}, //泰州
            {"region":'22',"longRegion":"1000515"}, //盐城
            {"region":'23',"longRegion":"1000514"}, //扬州
            {"region":'99',"longRegion":"1000250"} //江苏省
        ]
        for (let i = 0 ;i <arr.length;i++){
            if (arr[i].region== this.userInfo.region){
                if (arr[i].longRegion == '2000250') {
                    this.orgId = '1000250'
                } else {
                    this.orgId = arr[i].longRegion
                }
            }
        }

    },
    methods: {
        inputConsole(){
            if (this.teltext == '打开控制台'){
                new Vconsole()
            }
        },
        more24Hour(item){
            let startTime = new Date(item.releaseTime)
            let endTime = new Date()
            const diffTime = Math.abs(endTime - startTime); // 计算时间差
            const diffHours = diffTime / (1000 * 60 * 60); // 将毫秒转换为小时
            if (diffHours > 24 && this.countyId && this.countyId == item.countyId) {
                return true
            }
        },
        getDateStatus(inputDate) {
            if (!inputDate) return
            // 当前日期
            const now = new Date();
            const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

            // 解析输入日期
            const dateParts = inputDate.split(' ')[0].split('-');
            const timeParts = inputDate.split(' ')[1].split(':') || ['00', '00', '00'];
            const targetDate = new Date(
                parseInt(dateParts[0]),
                parseInt(dateParts[1]) - 1,
                parseInt(dateParts[2]),
                parseInt(timeParts[0]),
                parseInt(timeParts[1]),
                parseInt(timeParts[2])
            );
            const targetDay = new Date(targetDate.getFullYear(), targetDate.getMonth(), targetDate.getDate());

            // 计算日期差
            const timeDiff = targetDay.getTime() - today.getTime();
            const dayDiff = timeDiff / (1000 * 60 * 60 * 24);

            // 判断状态
            if (dayDiff < 0) {
                return '合约已到期';
            } else if (dayDiff === 0) {
                return '今天合约到期';
            } else if (dayDiff <= 6) {  // 本周内（今天+6天）
                return '本周合约到期';
            } else if (dayDiff <= 30) {  // 一个月内
                return '近一个月合约到期';
            } else {
                return `${dateParts[0]}-${dateParts[1]}-${dateParts[2]}合约到期`;
            }
        },
        getDateStatus2(inputDate) {

            if (!inputDate) return
            // 当前日期
            const now = new Date();
            const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

            // 解析输入日期
            const dateParts = inputDate.split(' ')[0].split('-');
            const timeParts = inputDate.split(' ')[1].split(':') || ['00', '00', '00'];
            const targetDate = new Date(
                parseInt(dateParts[0]),
                parseInt(dateParts[1]) - 1,
                parseInt(dateParts[2]),
                parseInt(timeParts[0]),
                parseInt(timeParts[1]),
                parseInt(timeParts[2])
            );
            const targetDay = new Date(targetDate.getFullYear(), targetDate.getMonth(), targetDate.getDate());

            // 计算日期差
            const timeDiff = targetDay.getTime() - today.getTime();
            const dayDiff = timeDiff / (1000 * 60 * 60 * 24);

            // 判断状态
            if (dayDiff < 0) {
                return '预约已超期';
            } else if (dayDiff === 0) {
                return '今天预约服务';
            } else if (dayDiff <= 6) {  // 本周内（今天+6天）
                return '本周预约服务';
            } else if (dayDiff <= 30) {  // 一个月内
                return '近一个月预约服务';
            } else {
                return `${dateParts[0]}-${dateParts[1]}-${dateParts[2]}预约服务`;
            }
        },
        goPrev(){
            console.log('moaBridge',moaBridge)
            console.log('window',window)
            console.log('window.moaBridge',window.moaBridge)
            window.moaBridge.backToHome()
        },
        goEnterPage(val){
            if (val == 1) {
                this.$router.push({
                    path: '/PotentialuserMaintenance3',
                    query: {
                        showbtn: 1,
                        add: 1,
                        editType: 'add'

                    }
                })
            }else if (val == 2) {
                this.$router.push({
                    path: '/BusinessOpportMaintenance3',
                    query: {
                        showbtn: 1,
                        editType: 'add',
                    }
                })
            } else {
                this.showDialogFlag = !this.showDialogFlag
            }

        },

        changeSearchType0(){
            let self = this;
            NlDropdown({
                confirmBtn: false,
                defaultValue: { id: '', label: '全部类型' },
                datalist: [
                    { id: '', label: '全部类型' },
                    { id: '1', label: '融合服务' },
                    { id: '2', label: '业务商机' },
                ]
            }, function (res) {
                self.type0 = res;
                self.type1 = { id: '', label: '商机状态' }
                self.getListMy()

            });
        },
        changeSearchType1(){
            let self = this;
            let arr = []
            if (self.type0.id == 2) {
                // 业务
                arr = [
                    { id: '', label: '全部状态' },
                    { id: '2', label: '跟进中' },
                    { id: '5', label: '待接单' },
                    { id: '7', label: '待补充' },
                    { id: '10', label: '待地市稽核' },
                    { id: '13', label: '失败结单' },
                    { id: '15', label: '审核待补充' },
                    { id: '16', label: '补单待审核' },
                ]

            } else {
                // 个人/全部
                arr = [
                    { id: '', label: '全部状态' },
                    { id: '7', label: '待补充' },
                    { id: '2', label: '跟进中' },
                    { id: '5', label: '待接单' },
                    { id: '10', label: '待地市稽核' },
                    { id: '11', label: '人工稽核失败' },
                    { id: '12', label: '成功结单' },
                    { id: '13', label: '失败结单' },
                    { id: '15', label: '审核待补充' },
                    { id: '16', label: '补单待审核' },
                ]
            }
            NlDropdown({
                confirmBtn: false,
                datalist: arr,
                defaultValue: { id: '', label: '全部状态' },
            }, function (res) {
                self.type1 = res;
                self.getListMy()
            });
        },

        changecomFormType1(){
            let self = this;
            NlDropdown({
                confirmBtn: false,
                datalist: [
                    { id: '', label: '商机来源' },
                    { id: '1', label: 'O了' },
                    { id: '2', label: '阿拉盯' },
                ]
            }, function (res) {
                self.chooseTime1 = res.label;
                self.getListMy()
            });
        },
        changeSearchTime1(){
            let self = this;
            NlDatePicker({
                dateType:'date',
                title: '创建时间',
                startDate: self.startTime,
                endDate: self.endTime,
                tsMinDate: chgStrToDate("2024/06/01",false)
            }, (retVal) => {
                //获取返回回调
                if(retVal.startDate==""||retVal.endDate==""){
                    console.info(retVal.startDate)
                    if(retVal.startDate) {
                        self.startTime1=retVal.startDate.replace(/\//g,"-") + ' 00:00:00';
                    } else {
                        self.startTime1 = ''
                    }
                    if(retVal.endDate) {
                        self.endTime1=retVal.endDate.replace(/\//g,"-")  + ' 23:59:59';

                    } else {
                        self.endTime1 = ''
                    }
                    self.chooseTime1=retVal.startDate+"~"+retVal.endDate;
                    self.getListMy()
                }else{
                    console.info(retVal.startDate)
                    self.startTime1=retVal.startDate.replace(/\//g,"-") + ' 00:00:00';
                    self.endTime1=retVal.endDate.replace(/\//g,"-")  + ' 23:59:59';
                    self.chooseTime1=retVal.startDate+"~"+retVal.endDate;
                    self.getListMy()

                }
            });
        },
        getListMy() {
            this.mylist = []
            let url = '/xsb/gridCenter/familyDiagnosis/h5QueryOlBusi'
            let params = {
                "operateTel": this.userInfo.servNumber, //当前操作人号码
                "busiTel": this.teltext ,  //商机号码
                "busiType": this.type0.id, //
                "buopStatus": this.type1.id,//商机状态
                "createStartTime": this.startTime1,//创建开始时间
                "createEndTime": this.endTime1//创建结束时间
            }
            console.info('getListMy--请求',params)
            HttpManager.requestPost(url, params).then((res) => {
                console.log('getListMy----返回',res)

                if (res.data.retCode == '0') {
                    this.mylist = res.data.data
                } else {
                    this.mylist = []
                }
            })
        },

        maskPhoneNumber(phoneNumber) {
            if (!phoneNumber) return
            phoneNumber = phoneNumber + ''

            if (phoneNumber.length == 10) {
                return phoneNumber.replace(/(\d{3})\d{3}(\d{4})/, '$1***$2');
            }
            if (phoneNumber.length == 11) {
                return phoneNumber.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
            }
            if (phoneNumber.length == 12) {
                return phoneNumber.replace(/(\d{3})\d{5}(\d{4})/, '$1*****$2');
            }
        },
        formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return
            const year = dateTimeStr.substring(0, 4);
            const month = dateTimeStr.substring(4, 6) - 1; // JS中月份是从0开始的
            const day = dateTimeStr.substring(6, 8);
            const hour = dateTimeStr.substring(8, 10);
            const minute = dateTimeStr.substring(10, 12);
            const second = dateTimeStr.substring(12, 14);

            const date = new Date(Date.UTC(year, month, day, hour, minute, second));
            let str = date.toISOString().substring(0, 19).replace(/T/g," ")
            return str; // 获取日期和时间部分，去掉时间区
        },
        goWeihu(item,val){
            if (val == 0) {
                if(item.busiType == 1) {
                    this.$router.push({
                        path: '/PotentialuserMaintenance3',
                        query: {
                            editType: 'view' ,
                            buopId: item.buopId,
                            busiTel: item.busiTel,
                            billid: item.releBillId,
                            homeId: item.homeId,
                            showbtn: 0,
                            buchong: 0,
                            item: JSON.stringify(item)
                        }
                    })
                } else{
                    this.$router.push({
                        path: '/BusinessOpportMaintenance3',
                        query: {
                            editType: 'view',
                            showbtn : 0,
                            itemObj: JSON.stringify(item),
                        }
                    })
                }
            } else {
                if(item.busiType == 1) {
                    this.$router.push({
                        path: '/PotentialuserMaintenance3',
                        query: {
                            editType: 'supplement' ,
                            buopId: item.buopId,
                            busiTel: item.busiTel,
                            billid: item.releBillId,
                            homeId: item.homeId,
                            showbtn: 1,
                            buchong: 1,
                            item: JSON.stringify(item)
                        }
                    })
                } else{
                    this.$router.push({
                        path: '/BusinessOpportMaintenance3',
                        query: {
                            editType: 'supplement',
                            showbtn : 1,
                            itemObj: JSON.stringify(item),
                        }
                    })
                }
            }


        },

    },

}
</script>

<style scoped lang='less'>
.pbo-wrap {
    padding: 50px 15px;
    height: 100vh;
    overflow: auto;
    box-sizing: border-box;
    background: #F5F6F7;
    .tab-header {
        position: fixed;
        left: 0;
        top: 44px;
        width: 100vw;
        line-height: 44px;
        z-index: 333;
        display: flex;
        .tab-item {
            flex: 1;
            font-size: 15px;
            text-align: center;
            border-bottom: 1px solid #bbb;
            &.active {
                color: #0b7ffe;
                font-weight: 700;
                border-bottom: 1px solid #0b7ffe;
                background: #f4feff;
            }
        }
    }
    .search-box {
        position: fixed;
        left: 0;
        top: 45px;
        width: 100vw;
        box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.2);
        padding: 5px 10px 15px;
        box-sizing: border-box;
        z-index: 333;
        background: linear-gradient( 180deg, #F7FBFF 0%, #FFFFFF 100%);
        box-shadow: 0px 3px 10px 0px rgba(86,125,244,0.05);
        border-radius: 0px 0px 0px 0px;
        .tab-header2 {
            width: 100%;
            line-height: 30px;
            z-index: 333;
            display: flex;
            .tab-item {
                flex: 1;
                font-size: 14px;
                text-align: center;
                border-bottom: 1px solid #bbb;
                &.active {
                    color: #0b7ffe;
                    border-bottom: 1px solid #0b7ffe;
                }
            }
        }
        .tab-sj {
            width: 100%;
            margin: 0 auto;
            display: flex;
            margin-bottom: 20px;
            .item {
                flex: 1;
                text-align: center;
                line-height: 28px;
                font-size: 16px;
                border-radius: 9px;
                position: relative;
                &.active {
                    color: #0b7ffe;
                    &::before {
                        content: '';
                        position: absolute;
                        width: 110px;
                        height: 4px;
                        background: #0b7ffe;
                        left: 50%;
                        bottom: -5px;
                        border-radius: 4px;
                        transform: translateX(-50%);
                    }
                }
            }

        }
        .search-input {
            margin: 0 auto;
            display: flex;
            border: 1px solid #9DC2EB;
            border-radius: 6px;
            margin-top: 15px;
            overflow: hidden;
            position: relative;
            width: calc(100% - 20px);
            .sousuo1 {
                position: absolute;
                left: 10px;
                top: 9px;
            }
            .sou {
                position: absolute;
                right: 5px;
                font-size: 14px;
                color: #0b7ffe;
                top: 10px;
            }
            .one-line {
                position: absolute;
                left: 100px;
                top: 7px;
                width: 1px;
                height: 16px;
                background: #929292;
            }
            .ssss {
                left: 6px !important;
                top: 7px;
            }
            .guanbi1 {
                position: absolute;
                right: 36px;
                top: 9px;
            }
            .search-down {
                width: 100px;
                text-align: center;
                font-size: 12px;
                line-height: 30px;
                background: #f4feff;
                color: #007AFF;
                box-shadow: 0px 0px 7px 5px rgba(0, 0, 0, 0.1);
            }
            .search-input1 {
                flex: 1;
            }
            .xiala {
                font-size: 8px !important;
            }
            input {
                width: calc(100% - 15px);
                height: 36px;
                padding: 0 70px 0 30px;
                box-sizing: border-box;
                font-size: 14px;
                outline: 0;
            }
        }
        .drop-drown {
            width: 100%;
            display: flex;
            margin-top: 15px;
            padding-left: 15px;
            color: #007AFF;


            .item {
                width: 40%;
                text-align: left;
                font-size: 14px;
                .xiala {
                    font-size: 10px;
                    margin-left: 5px;
                }
            }
        }
    }
    .lists-box {
        position: fixed;
        left: 0;
        top: 180px;
        width: 100vw;
        padding: 10px 20px 80px;
        box-sizing: border-box;
        z-index: 66;
        height: calc(100vh -  180px);
        overflow: auto;
        background: #F5F6F7;

        .list-item {
            padding: 10px;
            border-radius: 8px;
            position: relative;
            margin-bottom: 20px;
            background: #fff;
            border: 2px solid #fff;
            overflow: hidden;
            box-shadow: 0px 0px 8px 0px rgba(167,167,167,0.2312);
            .control {
                background: #fff;
                font-size: 12px;
                line-height: 26px;
                color: #0b7ffe;
                padding: 6px;
                border-radius: 6px;
                width: 80px;
                text-align: center;
                position: absolute;
                right: 33px;
                top: 10px;
                box-shadow: 0px 0px 7px 5px rgba(0, 0, 0, 0.1);
                z-index: 99999;
                .youjiantou {
                    font-size: 10px;
                    vertical-align: top;
                    line-height: 26px;
                }
            }
            .gengduo {
                font-size: 18px;
                color: #0b7ffe;
                position: absolute;
                right: 10px;
                top: 10px;
            }
            .line1 {
                margin-bottom: 8px;
                padding-left: 5px;

                img {
                    width: 20px;
                    vertical-align: middle;
                    margin-right: 4px;
                }
                .tel {
                    font-size: 18px;
                    vertical-align: middle;
                    font-weight: 700;
                    display: inline-block;
                    .xiansuo {
                        display: inline-block;
                        position: absolute;
                        font-weight: 400;
                        background: #ffd4d4;
                        padding: 2px 4px;
                        color: red;
                        top: 5px;
                        border-radius: 3px;
                        font-size: 10px;
                    }
                }
                .tag {
                    font-size: 12px;
                    vertical-align: middle;
                    margin-left: 5px;
                    padding: 2px 3px;
                    border-radius: 3px;
                    position: absolute;
                    right: 0;
                    top: 0;
                    width: 53px;
                    height: 20px;
                    border-radius: 0 0 0 16px;
                    padding-left: 10px;
                    padding-top: 4px;
                    box-sizing: border-box;
                }
                .tag0 {
                    background: #F1F1F1;
                    color: #434343;
                }
                .tag1 {
                    background: #FFEBDB;
                    color: #FC7F04;
                }
                .tag2 {
                    background: #E6EEFF;
                    color: #3F7AFF;
                }
                .tag3 {
                    background: green;
                    color: #fff;
                }
                .tag33 {
                    background: green;
                    color: #fff;
                    width: 66px !important;

                }

                .tag4 {
                    background: #ffdabd;
                    width: 66px !important;
                    color: #d96c00;
                }

                .tag7 {
                    background: #ffdabd;
                    width: 77px !important;
                    color: #d96c00;
                }

                .tag5 {
                    background: #ffc0c0;
                    width: 88px !important;
                    color: red;
                }

                .tag6 {
                    background: #ffc0c0;
                    width: 66px !important;
                    color: red;
                }
            }
            .line2 {
                font-size: 14px;
                line-height: 24px;
                padding-left: 5px;
                .title {
                    color: #727272;
                    font-size: 14px;

                }
                .value {
                    color: #404040;
                }
            }

        }
    }
    .lists-box2 {
        top: 200px;
        z-index: 66;
        height: calc(100vh -  200px);
        overflow: auto;
    }

}
.lists {
    width: 100%;
    position: fixed;
    top: 166px;
    height: 100%;
    background: #F9F9F9;
    overflow: auto;
    .list {
        position: relative;
        margin: 16px 12px;
        background-color: #fff;
        padding: 15px;
        border-radius: 8px;
        box-shadow: 0px 4px 8px 0px rgba(204, 204, 204, 0.5);
        font-size: 14px;
        .createTimeShow {
            margin: 10px 0;
        }
        .title {
            font-size: 14px;
            color: #868686;
        }
        .content {
            font-size: 14px;
            color: #1D1D1D;
        }
        .tel {
            font-weight: 600;
            color: #232323;
        }

        .audit-flag {
            display: flex;
            align-items: center;
            position: absolute;
            top: 5px;
            right: 10px;

            .iconfont {
                margin-right: 3px;
            }

            .wenhao1-font-size {
                font-size: 14px;
            }

            .checkbox2-font-size {
                font-size: 15px;
            }
        }

        .audit-yet-color {
            color:#4B7902;
        }

        .audit-wait-color {
            color: #B8741A;
        }

        .audit-fail-color {
            color: red;
        }

        .tels {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            .chengyuan2 {
                font-size: 20px;
            }
            .highLight {
                color: #318DFF;
            }
            .lowLight {
                color: #999;
            }
        }
        .indexShow {
            position: absolute;
            top: 0;
            left: 0;
            width: 25px;
            height: 15px;
            background-color: #3E9BFF;
            color: #fff;
            text-align: center;
            line-height: 15px;
            border-radius: 8px 0 8px 0;
            font-size: 12px;
        }
    }
    .jiedan {
        background: #ECECEC;
    }

}
.dislog {
    position: fixed;
    width: 85%;
    padding: 6px 0 0;
    border-radius:4px;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
    z-index: 555;
    background: #fff;
    text-align: center;
    box-shadow: 0px 0px 7px 5px rgba(0, 0, 0, 0.02);
    margin-top: -40%;

    .title {
        font-size: 14px;
        font-weight: 700;
        line-height: 34px;

    }
    .reson-box {
        display: flex;
        margin-top: 5px;
        .chooseone {
            flex: 1;
            font-size: 14px;
            line-height: 32px;
            position: relative;
            color: #929292;
            .duihao1 {
                position: absolute;
                top: -1px;
                color: green;
            }
        }
        .yuan {
            width: 14px;
            height: 14px;
            display: inline-block;
            border-radius: 100%;
            vertical-align: middle;
            border: 1px solid #929292;
            margin-right: 5px;
            text-align: left;
        }
    }
    .reson-box2 {
        display: flex;
        border-top: 1px solid #bbb;
        margin-top: 25px;
        .chooseone {
            flex: 1;
            font-size: 16px;
            line-height: 32px;
            position: relative;
            line-height: 36px;

            &:first-child {
                border-right: 1px solid #bbb;
            }
            &:last-child {
                color: #0b7ffe;
            }
        }
    }
}
.hide-box {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0,0,0,.5);
    z-index: 444;
}
.hide-box2 {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0,0,0,.5);
    z-index: 444;
}
.just-box {
    height: 0;
}
.box-btn {
    width: 100%;
    overflow: hidden;
    .control-item {
        float: right;
        margin: 5px;
        width: calc(25% - 10px);
        background: #007AFF;
        text-align: center;
        color: #fff;
        padding: 6px 0;
        font-size: 12px;
        border-radius: 4px;
        .iconfont {
            font-size: 12px !important;
            margin-right: 3px;
        }
    }
}
.jiedan-box {
    position: fixed;
    left: 0;
    bottom: 0;
    padding: 15px;
    background: #fff;
    box-sizing: border-box;
    width: 100vw;
    z-index: 998;
    .title {
        text-align: center;
        font-size: 18px;
        font-weight: 700;

    }
}
.pop-textarea-border {
    border: 1px solid #bbb;
    position: relative;

}

.detail-remark {
    margin-top: 10px;
}
textarea {
    width: 100%;
    height: 60px;
    font-weight: 400;
    color: black;
    resize: none;
    outline: none;
    border: none;
    margin-top: 10px;
    background: #fff;
    padding: 5px;
    box-sizing: border-box;
    border: 1px solid #bbb;
    font-size: 12px;
}
.pop-textarea {
    font-size: 12px;
    height: 80px;
    overflow: hidden;
    outline: none;
    border: 0;
    resize: none;
    margin-top: 0;
}

.pop-textarea-num {
    position: absolute;
    bottom: 5px;
    right: 10px;
    font-size: 12px;
    color: #7d7d7d;
}

textarea::-webkit-input-placeholder,input::-webkit-input-placeholder { /* Chrome/Safari/Opera */
    color: rgb(158, 155, 155);
    font-size: 14px;
}

input::-moz-placeholder { /* Firefox 19+ */
    color: rgb(158, 155, 155);
    font-size: 14px;

}

input:-ms-input-placeholder { /* IE 10+ 注意这里只有一个冒号 */
    color: rgb(158, 155, 155);
    font-size: 14px;

}
.btn {
    width: 100%;
    border-radius: 7px;
    line-height: 36px;
    margin: 25px 0 10px;
    background: #0b7ffe;
    color: #fff;
    text-align: center;
    font-size: 14px;
}
.action-step-detail {
    line-height: 36px;
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    .action-step-text {
        white-space: nowrap;
    }
    input {
        text-align: right;
        outline: none;
        font-size: 12px;
    }
    .chooseTextStyle {
        color:#9E9B9B;
        line-height: 24px
    }
    .action-step-detail-btnlist {
        text-align: right !important;
        span {
            display: inline-block;
            margin: 0 2px;
            padding: 0px 3px;
            line-height: 20px;
            border: 1px solid #ebe9e9;
            background: #ebe9e9;
            font-size: 13px;
            border-radius: 3px;
            min-width: 56px;
            text-align: center;
            box-sizing: border-box;
        }
    }

    .xiangji {
        font-size: 14px;
        color: rgb(16, 142, 233);
        margin-right: 3px;
    }

    .jiantou-copy-copy {
        font-size: 14px;
        color: #9E9B9B;
    }

    .red-info {
        color: red;
    }

}
.img {
    overflow: hidden;
    width: 100%;
    img {
        float: right;
        width: 140px;
        height: 70px;

    }
}
.active-span {
    background: #0b7ffe !important;
    color: #fff;
}
.img-box {
    width: 100%;
    overflow: hidden;
    img {
        width: 80px;
        height: auto;
        float: right;
    }
}
.chooseDialog {
    z-index: 456;
    background: #fff;
    padding: 12px;
    box-shadow: 2px 2px 6px 0px #747576;
    width: 100%;
    box-sizing: border-box;

    .guanbi {
        float: right;
    }
    .title {
        font-size: 18px;
        line-height: 26px;
        color: #000;
        text-align: center;
    }

    .info {
        font-size: 14px;
        line-height: 22px;
        margin-top: 8px;
    }

    .btnlist {
        margin-top: 8px;
        display: flex;
        width: 100%;
        margin-bottom: 50px;
        margin-top: 15px;

        div {
            display: inline-block;
            background: #1681FB;
            text-align: center;
            padding: 12px 15px;
            box-sizing: border-box;
            margin-top: 10px;
            border-radius: 5px;
            color: #fff;
            font-size: 15px;
            width: calc(50% - 15px);
            margin-right: 10px;
            flex: 1;
        }
    }
}
.hide-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0,0,0,.5);
    z-index: 444;
}
.mint-popup {
    width: 100%;
}
.myCheckbox2 {
    /* 隐藏原生的复选框 */
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    /* 自定义样式 */
    width: 12px;
    height: 12px;
    background-color: #f0f0f0; /* 背景颜色 */
    border: 1px solid #ccc; /* 边框颜色 */
    border-radius: 1px; /* 圆角 */
    position: relative;
    margin-top: -3px;
}
.myCheckbox2:checked::before {
    content: '√';
    display: block;
    width: 12px;
    height: 12px;
    background-color:#89bcf4; /* 选中时的对勾颜色 */
    color: #fff;
    font-weight: 700;
    font-size: 12px;
    text-align: center;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
.tags-box {
    margin: 10px 0;
    .tags {
        background: #fff;
        line-height: 12px;
        font-size: 10px;
        padding: 2px 7px;
        border: 1px solid #717171;
        border-radius: 3px;
        color: #717171;
        display: inline-block;
    }
    .tags2 {
        border:1px solid #0b7fff;
        color: #0b7fff;
    }
    .tags3 {
        border:1px solid red;
        color: red;
    }
}
</style>
