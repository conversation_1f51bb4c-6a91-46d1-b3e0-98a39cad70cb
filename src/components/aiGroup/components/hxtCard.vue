<!--聊天对话-->
<template>
  <div>
    <div class="top-title">
      <img width="100%" src="../../../assets/img/aiGroup/hxt/hxt-title.png" />
    </div>
    <div class="scroll-box bg-white padding-lr-sm padding-tb">
      <div class="flex justify-between align-center">
        <div>
          <i class="iconfont jituan3 text-blue" />
          <span class="text-lg text-bold">{{ propData.commName }}</span>
        </div>
        <div v-show="isRlt" class="text-bold">覆盖率<span
          class="text-D81D1D text-xxl">{{ propData.reliableRate ? (propData.reliableRate * 100).toFixed(1) : '--'
          }}%</span></div>
      </div>
      <div class="hxt-box relative" @click.stop="imgClick">
        <!--户型图-->
        <img v-if="!isRlt" class="img" :id="`hxt-img-${index}`"
             :src="propData.pics" />
        <!--热力图-->
        <img v-else class="img" :src="propData.wifiMap" />
        <!--覆盖点图片-->
        <div v-show="!loading" v-for="(overlay, index) in overlays" :key="index">
          <img @click.stop="(event)=>event.stopPropagation()" class="absolute overlay-img"
               :style="getOverlayStyle(overlay)" width="32" height="36"
               src="../../../assets/img/aiGroup/hxt/dot-icon.png">
        </div>
      </div>
      <template v-if="!isRlt">
        <div class="flex justify-between align-center margin-top-sm text-bold">
          <div>
            <span>已添加设备</span>
            <span class="text-blue padding-left-xs text-xl">{{ overlays.length }}</span>
          </div>
          <div v-show="overlays.length" class="text-red" @click="overlaysClear">
            <i class="shanchu1 iconfont" />
            清空
          </div>
        </div>
        <ul class="device-tab padding-tb-sm flex  align-center">
          <li class="device-item margin-lr-xs flex justify-center align-center">
            <div class="text-blue" @click="openSelectPop">
              <i class="jiahao- iconfont"></i>
            </div>
          </li>
          <li
            v-for="(item, index) in deviceData"
            :key="index"
            class="device-item margin-lr-xs relative"
          >
            <div class="text-center">
              <div class='cu-tag badge bg-blue' v-show="item.count">{{ item.count > 99 ? '99+' : item.count }}</div>
              <img width="44" height="44" src="../../../assets/img/aiGroup/hxt/lyq.png" />
              <div class="text-sm padding-top-xs ">{{ item.label }}</div>
            </div>
          </li>
        </ul>
      </template>
      <div v-else class="padding-tb"><img class="img" src="../../../assets/img/aiGroup/hxt/rlt-legend.png" /></div>
      <div class="flex flex-direction" @click="generateBtn">
        <button class="cu-btn bg-blue lg">
          <em class='iconfont lujing23r margin-right-xs padding-top-xs'></em>
          {{ isRlt ? '重新' : '' }}生成热力图
        </button>
      </div>
    </div>
    <!--选择设备弹框-->
    <selectPop
      :options="deviceData"
      v-model="selectPopModal"
      @onConfirm="(e)=> curDevice = e"
    ></selectPop>
  </div>
</template>

<script>
import Storage from '@/base/storage'
import selectPop from '../popup/selectPop.vue'
import { h5QueryWifiCover } from '../../request'

export default {
  name: 'sellCard',
  components: { selectPop },
  mixins: [],
  props: {
    // 当前展示数据
    propData: {
      type: Object,
      default: function() {
        return {}
      }
    },
    // 当前展示索引
    index: {
      type: [String, Number],
      default: ''
    },
    // sessionId
    sessionId: {
      type: String,
      default: ''
    },
    // 当前展示组件索引
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      userInfo: Storage.session.get('userInfo') || {},//操作员信息
      deviceData: [
        { label: '普通路由器', count: 0, value: '1' },
        { label: '光路由', count: 0, value: '2' },
        { label: '光网关', count: 0, value: '3' }
      ],// 设备列表数据
      selectPopModal: false, // 选择设备弹框显示隐藏
      isAdd: false,// 是否添加
      overlays: [], // 存储所有覆盖图片的位置信息
      curDevice: null, // 当前选中设备类型
      isRlt: false // 是否添加热力图
    }
  },
  computed: {},
  methods: {
    // 户型图点击事件
    imgClick(event) {
      if (this.curDevice) {
        const rect = event.target.getBoundingClientRect()
        const x = event.clientX - rect.left// 点击位置相对于图片的X坐标
        const y = event.clientY - rect.top // 点击位置相对于图片的Y坐标
        // 添加新的设备
        if (this.isAdd) {
          this.overlays.push({ x, y, curDevice: this.curDevice })
          // 当前设备数量++
          this.deviceData.find(item => item.value === this.curDevice).count++
          this.isAdd = false
        } else {
          this.overlays[this.overlays.length - 1] = { x, y, curDevice: this.curDevice }
          this.$forceUpdate()
        }
      }
    },
    // 打开添加设备弹框
    openSelectPop() {
      this.selectPopModal = true
      this.isAdd = true
    },
    // 清空覆盖物
    overlaysClear() {
      this.$messagebox({
        title: '温馨提示',
        message: '确认清空嘛？',
        cancelButtonText: '取消',
        showCancelButton: true,
        confirmButtonText: '确认'
      }).then((action) => {
        // 确认
        if (action === 'confirm') {
          this.overlays = []
          this.deviceData.map(item => item.count = 0)
        }
      })
    },
    // 获取打点图片的样式
    getOverlayStyle(overlay) {
      return {
        left: `${overlay.x - 16}px`, // 减去图标宽度一半
        top: `${overlay.y - 36}px`// 减去图标高度一半
      }
    },
    // 生成热力图
    generateBtn() {
      // 生成热力图
      if (!this.isRlt) {
        // 获取户型的尺寸
        let hxtImg = document.getElementById(`hxt-img-${this.index}`)
        if (hxtImg) {
          if (this.overlays.length) {
            let hxtImgWidth = hxtImg.width
            let hxtImgHeight = hxtImg.height
            // 拿到overlays内所有数据和图片尺寸的比例
            let routersList = this.overlays.map(item => {
              return {
                x: item.x / hxtImgWidth,
                y: item.y / hxtImgHeight,
                power: '20'
              }
            })
            let data = {
              srcArea: this.propData.srcArea,//面积
              obsPlanId: this.propData.obsPlanId,//户型 ID
              // 设备位置坐标
              routersList: routersList
            }
            this.h5QueryWifiCover(data)
            this.isRlt = !this.isRlt
          } else {
            this.$toast({ message: '请放置已选设备到户型图上！', className: 'ai-toast-o-r' })
          }
        }
      } else {
        this.isRlt = !this.isRlt
        // 清空已经添加的设备
        this.overlays = []
        this.deviceData.map(item => item.count = 0)
      }
      this.curDevice = null
    },
    // 生成热力图
    async h5QueryWifiCover(item) {
      this.loading = true
      let data = {
        sessionId: this.sessionId,
        ...item
      }
      console.info('生成热力图请求参数：', data)
      const res = await h5QueryWifiCover(data)
      this.loading = false
      if (res) {
        if (res !== '303') {
          console.info('生成热力图返回信息：', res)
          this.$emit('changeCur',this.index,res)
        } else {
          this.$emit('sessionIdChange', data)
        }

      }
    }
  },
  created() {

  },
  mounted() {
  },
  watch: {}
}
</script>

<style scoped>
@import "../../../base/less/public.less";

.iconfont {
  font-size: 14px;
}

.scroll-box {
  border-radius: 0 0 10px 10px;

  .img {
    width: 100%;
  }
}

.scroll-box > div {
  margin-left: 5px;
  margin-right: 5px;
}

.hxt-box {
  margin-top: 12px;
  padding: 0;
  border: 1px solid #e5e5e5;

  .overlay-img {
    z-index: 10;
  }
}

.device-tab {
  overflow-x: auto; /* 启用水平滚动 */
  white-space: nowrap; /* 防止内容换行 */
  -webkit-overflow-scrolling: touch; /* 启用弹性滚动，优化移动端滚动体验 */
  scrollbar-width: none;
}

.device-item {
  width: 71px;
  height: 71px;
  background: #FFFFFF;
  border-radius: 9px;
  border: 1px solid #007AFF;
}

.device-tab::-webkit-scrollbar {
  display: none;
}

.lujing23r {
  font-size: 18px;
  transform: rotate(-135deg);
  display: inline-block;
}

.jiahao- {
  font-size: 40px;
}

.text-D81D1D {
  color: #D81D1D;
}

.ai-toast-o-r {
  z-index: 100001;
}
</style>
