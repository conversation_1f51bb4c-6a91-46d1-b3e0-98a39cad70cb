<!--客户画像思考卡片-->
<template>
  <div class='think-card'>
    <div class='think-title flex justify-between align-center'>
      <div class='flex align-center'>
        <span v-if="loading" class='iconfont jiazai'></span>
        AI思考过程
      </div>
      <div v-if="!loading" @click="changeMore">
        <i class="iconfont jiantou2" v-if="thinkHide"></i>
        <i class="iconfont jiantouxiangshang" v-else></i>
      </div>
    </div>
    <div class='think-list' v-if="!thinkHide">
      <div class='think-item' v-for='(think,tIndex) in thinkList' :key="tIndex">
        <div :class="tIndex!=thinkList.length-1?'border-left':''">
          <div class="circle-box2"></div>
          <div class='item-title'>{{ think.title }}</div>
          <div class='item-text' v-html="think.printedText"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  name: 'thinkCard',
  components: {},
  mixins: [],
  props: {
    // 当前展示数据
    thinkList: {
      type: Object,
      default: () => ([])
    },
    // 加载中
    loading: {
      type: Boolean,
      default: false
    },
    // 是否隐藏
    thinkHide: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  computed: {},
  methods: {
    changeMore() {
      this.$emit('changeMore')
    }
  },
  created() {
  },
  mounted() {
  },
  watch: {}
}
</script>

<style scoped lang='less'>
.think-card {
  box-shadow: 0px 8px 10px 0px rgba(0, 0, 0, 0.06);
  border-radius: 16px;
  background: white;
  padding: 1px;

  .think-title {
    padding: 12px 14px;
    border-radius: 16px;
    background: #f1f7ff;

    .jiantou2, .jiantouxiangshang {
      color: #afd3fc;
    }
  }

  .think-list {
    padding-top: 15px;

    .think-item {
      position: relative;
      padding: 0 24px;

      .circle-box2 {
        position: absolute;
        left: 17px;
        background: #FFFFFF;
        border: 4px solid #007aff;
        border-radius: 50%;
        width: 7px;
        height: 7px;
        z-index: 99;
      }

      .item-title {
        padding-left: 15px;
        font-weight: 600;
      }

      .item-text {
        box-sizing: border-box;
        color: #bababa;
        padding: 8px 0 15px 15px;
        line-height: 1.5;
      }

      .border-left {
        border-left: 1px solid #afd3fc;
      }
    }
  }

  .flex {
    display: flex;
  }

  .align-center {
    align-items: center;
  }

  .justify-between {
    justify-content: space-between;
  }
}

.jiazai {
  display: inline-block;
  font-size: 18px;
  animation: rotate 1.8s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
