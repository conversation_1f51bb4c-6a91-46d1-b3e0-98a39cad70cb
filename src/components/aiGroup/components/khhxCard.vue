<!--客户画像-->
<template>
  <div class="text-3D3D3D" v-if="cardShow">
    <think-card :think-list="propData.thinkList" :thinkHide="propData.thinkHide" @changeMore="changeMore" />
    <!--客户画像-->
    <div class="bg-white card-box radius1 margin-top-sm">
      <div class="card-top flex justify-between align-center padding-lr">
        <div class="text-lg text-blue text-bold">
          <div v-if="curItem">
            <i class="iconfont" :class="curItem.icon" />
            {{ curItem.oneText }}
          </div>
        </div>
        <div class="text-3D3D3D">{{ formatPhone(propData.phone) }}</div>
      </div>
      <div class="padding-tb-sm padding-lr bg-white radius1">
        <div class="card-1" v-if="propData.tabValue==1">
          <div class="flex justify-between align-center padding-left-xs">
            <div class="text-bold flex align-center">
              <img src="../../../assets/img/aiGroup/khhx/card-title.png" width="12" />
              <span class="margin-left-xs">客户画像</span>
            </div>
            <div>
              <div class='cu-tag round light bg-FAF5FF text-blue' v-show="!propData.haveDetail"
                   @click="viewDetails">
                查看详情>
              </div>
            </div>
          </div>
          <div class="flex justify-between align-center">
            <ul class="flex flex-wrap">
              <li v-for="(item,index) in propData.tagData" :key="item" class="margin-top-sm margin-lr-xs"
                  :class="index%2?'' :''">
                <span class='padding-lr-lg cu-tag round light bg-blue'>{{ item }}</span>
              </li>
            </ul>
            <div><img width="100"
                      src="../../../assets/img/aiGroup/khhx/khhx.png" />
            </div>
          </div>
          <div class="padding-left-xs">
            <div class="flex padding-tb">
              <img src="static/img/ydlogo.png" width="20" height="20" />
              <span class="text-bold margin-left-xs">{{ propData.package.mainPackage }}</span>
            </div>
            <div class="">
              <ul class="grid col-3 padding-bottom">
                <li>
                  <ul class="margin-right-xs introduce-box">
                    <li>
                      <i class="iconfont a-bianzu143x text-red" />
                      <span class="text-C7590D text-bold">
                      <span class="text-xxl">{{ propData.package.flow || '--' }}</span>
                      <span class="text-sm">GB</span>
                    </span>
                    </li>
                    <li>
                      <div class="width-100">
                        <div class="cu-progress xs round">
                          <div class="round"
                               :class="propData.package.flowPer>=0.9?'bg-red':'bg-orange'"
                               :style="[{ width:`${propData.package.flowPer *100}%`}]"></div>
                        </div>
                      </div>
                    </li>
                    <li class="line-height">
                      <span>上月流量</span>
                    </li>
                  </ul>
                </li>
                <li>
                  <ul class="margin-lr-xs introduce-box">
                    <li>
                    <span class="text-474747 text-bold">
                      <span class="text-xxl">{{ propData.package.call || '--' }}</span>
                       <span class="">分钟</span>
                    </span>
                    </li>
                    <li>
                      <div class="width-100">
                        <div class="cu-progress xs round">
                          <div class=" round"
                               :class="propData.package.callPer>=0.9?'bg-red':'bg-gray'"
                               :style="[{ width:`${propData.package.callPer * 100}%`}]"></div>
                        </div>
                      </div>
                    </li>
                    <li class="line-height">上月语音</li>
                  </ul>
                </li>
                <li>
                  <ul class="margin-left-xs introduce-box">
                    <li>
                    <span class="text-474747 text-bold">
                      <span>
                        {{ propData.package.isBroadband == 1 ? '已开通' : propData.package.isBroadband == 0 ? '未开通' : '--'
                        }}
                      </span>
                    </span>
                    </li>
                    <li class="line-height">宽带</li>
                  </ul>
                </li>
              </ul>
              <div class="flex padding-bottom-sm">
                <div class="padding-right-xl text-center">
                  <img width="40" height="40" src="../../../assets/img/aiGroup/khhx/ai-icon.png" />
                  <div class="text-blue text-xl">总结</div>
                </div>
                <div class="text-sm line-height-2" v-html="propData.portrait"></div>
              </div>
            </div>
          </div>
        </div>
        <div class="card-2" v-if="propData.tabValue==2">
          <div class="swipe-box" v-if="propData.qrybillnew.servnumber || propData.qryUserShareResource.servnumber">
            <mt-swipe :auto="0">
              <mt-swipe-item v-if="propData.qryUserShareResource.servnumber">
                <div class="text-bold flex align-center">
                  <img src="../../../assets/img/aiGroup/khhx/card-title.png" width="12" />
                  <span class="margin-left-xs">家庭共享套餐</span>
                </div>
                <ul class="grid col-2 margin-top ">
                  <li>
                    <div class="item-1 margin-right-xs padding-xs flex align-center">
                      <div>
                        <img width="50" src="../../../assets/img/aiGroup/khhx/jthx-1.png" />
                      </div>
                      <div class="padding-left-sm text-content width">
                        <div class="text-bold">
                          流量<span class="text-sm">(GB)</span>
                        </div>
                        <div class="text-bold">{{ propData.qryUserShareResource.flowSummary.usedata
                          }}/{{ propData.qryUserShareResource.flowSummary.alldata }}
                        </div>
                        <div class="width-100">
                          <div class="cu-progress sm round">
                            <div class="round bg-orange"
                                 :style="[{ width:`${propData.qryUserShareResource.flowSummary.usedata/propData.qryUserShareResource.flowSummary.alldata *100}%`}]"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </li>
                  <li>
                    <div class="item-2 margin-left-xs padding-xs flex align-center">
                      <div>
                        <img width="50" src="../../../assets/img/aiGroup/khhx/jthx-2.png" />
                      </div>
                      <div class="padding-left-sm text-content width">
                        <div class="text-bold">
                          语音<span class="text-sm">(分钟)</span>
                        </div>
                        <div class="text-bold">{{ propData.qryUserShareResource.voiceSummary.usedata
                          }}/{{ propData.qryUserShareResource.voiceSummary.alldata }}
                        </div>
                        <div class="width-100">
                          <div class="cu-progress sm round">
                            <div class="round bg-blue"
                                 :style="[{ width:`${propData.qryUserShareResource.voiceSummary.usedata/propData.qryUserShareResource.voiceSummary.alldata *100}%`}]"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </li>
                </ul>
                <div>
                  <div class="padding-tb-sm border-bottom flex">
                    <div class="basis-df text-bold">号码</div>
                    <div class="basis-xs1 text-right text-orange">共享流量</div>
                    <div class="basis-xs1 text-right text-blue">共享语音</div>
                  </div>
                  <div class="flex align-center padding-top"
                       v-for="(sonItem,sonIndex) in propData.qryUserShareResource.family_list"
                       :key="sonIndex">
                    <div class="basis-df flex align-center">
                      <div class="text-bold padding-right-sm">
                        {{ sonItem.service_number }}
                      </div>
                      <div v-if="sonItem.card_type=='1'" class='cu-tag cu-tag1 radius line-orange'>共享副号</div>
                      <div v-if="sonItem.card_type=='0'" class='cu-tag cu-tag1 radius line-blue'>主号</div>
                    </div>
                    <div class="basis-xs1 text-right text-bold">
                      {{ sonItem.flowUsed }}
                      <span class="text-sm">GB</span>
                    </div>
                    <div class="basis-xs1 text-right text-bold">
                      {{ sonItem.voiceUsed }}
                      <span class="text-sm">分钟</span>
                    </div>
                  </div>
                </div>
              </mt-swipe-item>
              <mt-swipe-item v-if="propData.qrybillnew.servnumber">
                <div class="text-bold flex align-center">
                  <img src="../../../assets/img/aiGroup/khhx/card-title.png" width="12" />
                  <span class="margin-left-xs">家庭代付费</span>
                </div>
                <ul class="margin-top-sm light bg-blue padding-sm flex text-content">
                  <li class="margin-right">
                    <span class="text-3D3D3D text-bold">当月</span>
                    {{ propData.qrybillnew.totalfee }}
                    <span class="text-sm">元</span>
                  </li>
                  <li class="margin-right"><span
                    class="text-3D3D3D text-bold">本机</span>
                    {{ propData.qrybillnew.mainTotalfee }}
                    <span class="text-sm">元</span>
                  </li>
                  <li><span class="text-3D3D3D text-bold">代付</span>
                    {{ propData.qrybillnew.replacefee }}
                    <span class="text-sm">元</span>
                  </li>
                </ul>
                <div>
                  <div class="padding-tb-sm border-bottom flex ">
                    <div class="basis-xl1 text-bold">号码</div>
                    <div class="basis-xs1 text-blue text-right">代付金额</div>
                  </div>
                  <div class="flex padding-top">
                    <div class="basis-xl1 flex align-center">
                      <div class="text-bold padding-right-sm">{{ propData.qrybillnew.servnumber }}</div>
                      <div class='cu-tag cu-tag1 radius line-blue'>主号(本机)</div>
                    </div>
                    <div class="basis-xs1 text-right text-bold">
                      {{ propData.qrybillnew.mainTotalfee }}
                      <span class="text-sm">元</span>
                    </div>
                  </div>
                  <div class="flex padding-top" v-for="(sonItem,sonIndex) in propData.qrybillnew.subAccountList"
                       :key="sonIndex">
                    <div class="basis-xl1 flex align-center">
                      <div class="text-bold padding-right-sm">{{ sonItem.servnumber }}</div>
                      <div class='cu-tag cu-tag1 radius line-orange'>成员</div>
                    </div>
                    <div class="basis-xs1 text-right text-bold">
                      {{ sonItem.fee }}
                      <span class="text-sm">元</span>
                    </div>
                  </div>
                </div>
              </mt-swipe-item>
            </mt-swipe>
          </div>
        </div>
        <div class="card-3" v-if="propData.tabValue==3">
          <template v-if="propData.UserTrajectory.showList && propData.UserTrajectory.showList.length">
            <div class="flex justify-between">
              <div class="text-bold flex align-center">
                <img src="../../../assets/img/aiGroup/khhx/card-title.png" width="12" />
                <span class="margin-left-xs">时间筛选</span>
              </div>
              <tab-com :propStyle="{background: '#EBF2FF',color: '#1D6CFF'}"
                       v-model="propData.UserTrajectory.filterVal" tab-type="card"
                       @input="filterChange"
                       :list="[ { text: '近三个月', value: 1 },{ text: '近六个月', value: 2 }]" />
            </div>
            <div class="plan-box margin-top">
              <div v-for="(item,index) in propData.UserTrajectory.showList" :key="index">
                <div class="plan-item flex relative">
                  <div class="circle-box1 absolute"></div>
                  <div class="padding-left-sm text-sm"
                       :class="index!==propData.UserTrajectory.showList.length-1?'border-left':''">
                    <div class="text-bold" style="width: 80px">{{ item.time1 }}</div>
                    <div class="text-content padding-top-xs padding-bottom">
                      <div>{{ item.item2 }}</div>
                      <div class='cu-tag radius margin-top-xs'
                           :class="item.channelTypeName.includes('费')?'line-orange':'line-blue'">
                        {{ item.channelTypeName }}
                      </div>
                    </div>
                  </div>
                  <div>
                    <div class="text-bold">
                      <span>{{ item.businessType }}</span>
                      <span class="text-blue margin-left-sm">{{ item.adstract }}</span>
                    </div>
                    <div class="text-content padding-top-xs padding-bottom">
                      {{ item.description }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </div>
        <div class="card-4" v-if="propData.tabValue==4">
          <div class="text-content relative flex">
            <div class="circle-box2 absolute"></div>
            <div class="padding-left">
              根据用户过去3个月的行为数据，该用户表现出明显的周末活跃特征，建议在周五下午发送关怀信息。
            </div>
          </div>
          <div class="padding-top-sm">
            <textarea class="text-df" placeholder="可输入需要诊断的方向，我们可以针对性调整诊断焦点"></textarea>
          </div>
          <div class="padding-top-sm flex justify-end">
            <div class="text-center">
              <button class="cu-btn  bg-blue">重新诊断</button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="flex flex-wrap text-3D3D3D">
      <div v-show="item.value!=propData.tabValue" v-for="(item,index) in tabList" :key="index"
           @click="tabValChange(item)"
           class='cu-tag-item padding-lr-sm padding-tb-xs flex align-center margin-top-sm margin-right-sm'>
        <img src="../../../assets/img/aiGroup/khhx/btn-icon.png" width="15" />
        <span class="padding-left-xs">{{ item.tabText }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import Storage from '@/base/storage'
import thinkCard from './thinkCard.vue'
import tabCom from '../../common/tabCom.vue'
import NoDataPage from 'components/common/NoDataPage.vue'

export default {
  name: 'kkhxCard',
  components: { NoDataPage, thinkCard, tabCom },
  mixins: [],
  props: {
    // 当前展示数据
    propData: {
      type: Object,
      default: () => ({})
    },
    // 当前展示卡片索引
    index: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      userInfo: Storage.session.get('userInfo') || {}, //操作员信息
      tabList: [
        { oneText: '基础画像', value: 1, tabText: '查看我的基础画像', icon: 'user' },
        { oneText: '家庭画像', value: 2, tabText: '查看我的家庭', icon: 'xingzhuang3x' },
        { oneText: '用户轨迹', value: 3, tabText: '查看我的用户轨迹', icon: 'quyuww' }
        // { oneText: 'AI问诊', value: 4, tabText: '帮我分析分析（AI问诊）', icon: 'xingzhuang21' }
      ],
    }
  },
  computed: {
    // 当前卡片
    curItem() {
      return this.tabList.find(item => item.value == this.propData.tabValue)
    },
    // 卡片是否展示
    cardShow() {
      switch (this.propData.tabValue) {
        case 2:
          return !!(this.propData.qrybillnew.servnumber || this.propData.qryUserShareResource.servnumber)
        case 3:
          return !!(this.propData.UserTrajectory.showList && this.propData.UserTrajectory.showList.length);
        default:
          return true
      }
    }
  },
  methods: {
    // 查看详情
    viewDetails() {
      this.$emit('viewDetails', this.index)
    },
    // 切换tab
    tabValChange(item) {
      let text = ''
      switch (item.value) {
        case 4:
          text = `帮我诊断下${this.propData.phone}该用户的情况`
          break
        default:
          text = `我想查看${this.propData.phone}的${item.oneText}`
          break
      }
      this.$emit('chatChange', text)
    },
    formatPhone(phone) {
      // 先判断是否为有效的11位数字手机号
      if (!/^\d{11}$/.test(phone)) {
        return ''
      }
      // 截取前3位 + **** + 后4位
      return phone.slice(0, 3) + '****' + phone.slice(7)
    },
    changeMore() {
      this.$emit('changeMore', this.index)
    },
    filterChange(val) {
      let list = this.propData.UserTrajectory.lastSixMonthsData
      // 近三月
      if (val == 1) {
        list = this.propData.UserTrajectory.lastThreeMonthsData
      }
      this.$emit('listChange', this.index, list)
    }
  },
  created() {
  },
  mounted() {
  },
  watch: {}
}
</script>

<style scoped lang='less'>
@import "../../../base/less/public.less";


.bg-FAF5FF {
  background-color: #FAF5FF;
  color: #007AFF
}

.card-box {
  box-shadow: 0 8px 10px 0 rgba(0, 0, 0, 0.06);
  background: url('../../../assets/img/aiGroup/khhx/card-top.png') no-repeat;
  background-size: 100% 70px;

  .card-top {
    height: 50px;
  }
}

.bg-blue {
  background: #1D6CFF;
}

.bg-blue.light {
  background: #EFF6FF;
  color: #007AFF;
}

.text-blue {
  color: #007AFF
}

.grid.col-3 > li {
  width: 32%
}

.introduce-box {
  .a-bianzu143x {
    font-size: 14px;
  }

  .line-height {
    line-height: 1.5;
  }
}

.bg-red.light {
  width: calc(100vw - 100px);
}

.text-C7590D {
  color: #C7590D;
}

.cu-tag.bg-red {
  height: 17px;
  line-height: 17px;
}

.line-height-2 {
  line-height: 1.4;
}

.border-bottom {
  border-bottom: 1px solid #EDEDED;
}

.line-blue {
  color: #1D6CFF;
  background: #D5EAFF;
}

.card-1 {
  .cu-progress.xs {
    height: 7px;
    background: #D4D4D4;

    .bg-gray {
      background: #959595;
    }
  }
}

.card-2 {

  .swipe-box {
    height: 300px;
  }

  .cu-progress.sm {
    height: 9px;
    background: #D4D4D4;
  }

  .basis-xl1 {
    width: 75%;
  }

  .basis-xs1 {
    width: 25%;
  }

  .basis-df {
    width: 50%;
  }

  .item-1 {
    background: linear-gradient(180deg, #FFF6ED 0%, #FFFFFF 100%);
  }

  .item-2 {
    background: linear-gradient(180deg, #EDF6FF 0%, #FFFFFF 100%);
  }

  .width {
    width: 65%;
  }
}

.plan-item {
  .border-left {
    border-left: 1px solid #EDEDED;
  }

  .cu-tag {
    padding: 0 4px;
  }
}

.circle-box1, .circle-box2 {
  background: #FFFFFF;
  border: 2px solid #1D6CFF;
  border-radius: 50%;

  z-index: 99;
}

.circle-box1 {
  width: 14px;
  height: 14px;
  left: -7px;
  top: -2px;
}

.circle-box2 {
  top: 4px;
  width: 10px;
  height: 10px;
}

.card-4 {
  textarea {
    border: none;
    width: 100%;
    background: #F7F7F7;
    padding: 7px 9px;
  }
}

/deep/ .mint-swipe-items-wrap {
  overflow-y: auto;
  height: 90%;
}

.radius {
  border-radius: 4px;
}

.radius1 {
  border-radius: 16px;
}

.cu-tag-item {
  background: linear-gradient(180deg, #FFFFFF 0%, #EBF5FE 100%);
  border-radius: 152px;
  border: 1px solid #FFFFFF;
  color: #373737;
}

.cu-tag1 {
  padding: 0 4px;
}

</style>
