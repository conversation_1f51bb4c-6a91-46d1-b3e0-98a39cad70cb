<!--国漫套餐-->
<template>
  <div class='chat-container'>
    <!--顶部tab切换-->
    <ai-top-com :uinfo="uinfo" needRecord ref="aiTopCom" @goPrev="goPrev" />
    <!-- 消息区域 -->
    <div class='messages-wrap' ref='messagesContainer'>
      <div class="recommend-box">
        <div @click="sendMessage(recommend.key1)" class="recommend-tip tab-item">{{ recommend.key1 }}</div>
        <div @click="sendMessage(recommend.key2)" class="recommend-tip tab-item">{{ recommend.key2 }}</div>
        <div @click="sendMessage(recommend.key3)" class="recommend-tip tab-item">{{ recommend.key3 }}</div>
        <div @click="sendMessage(recommend.key4)" class="recommend-tip tab-item">{{ recommend.key4 }}</div>
        <div @click="sendMessage(recommend.key5)" class="recommend-tip tab-item">{{ recommend.key5 }}</div>
      </div>
      <!-- 对话内容-->
      <div
        v-for='(msg, index) in gmChatList'
        :key='index'
        class='message-bubble'
        :class="{ 'user-message': msg.isUser, 'bot-message': msg.bot,'ai-message':msg.ai }"
      >
        <!--智能选号组件-->
        <component v-if='msg.ai'
                   :is='msg.currentComponent'
                   :propData='msg'
                   :index="index"
                   :phone="phone"
                   @setGmChat="setGmChat"
                   @goAnswer="goAnswer"
                   :key='index'></component>
        <!--普通打字-->
        <div class='content' v-else v-html="msg.content"></div>
      </div>
      <!-- 流式的套餐列列表展示-->
      <div v-if="cardList.length">
        <!--ai回复相关信息-->
        <div class="ai-reply margin-bottom">
          <!-- 用户画像-->
          <div>
            <!--        <span class="text-blue text-bold">长期</span>出行<span class="text-orange text-bold">美国</span>，推荐您以下搭配-->
          </div>
          <!--推荐列表-->
          <div v-for="(item,index) in cardList" :key="index">
            <div class="padding-bottom">
              <div class="flex padding-bottom">
                <i class='iconfont tuijian1 text-blue padding-right-xs'></i>
                <div class="text-4C4C4C" v-html="item.reason"></div>
              </div>
              <div class="sell-item">
                <!-- 流量包-->
                <div>
                  <div :class="item.cardLoading ? 'box-loading' : `box-${index%2}`">
                    <div class="text-5E5E5E">{{ item.name }}</div>
                    <div class="text-orange margin-top-sm" v-show="!item.cardLoading">{{ item.price }}元/{{ item.days
                      }}天
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!--加载圈-->
      <div class='message-bubble bot-message' v-else-if='loading && !printedText.length'>
        <div class='content flex align-center'>
          生成中...
          <span class='iconfont jiazai'></span>
        </div>
      </div>
      <!--流式回答-->
      <div class='message-bubble bot-message' v-else-if="printedText.length">
        <div class='content'>
          <template v-if="['<question>','<tips>','<portrait>','<message>'].some(item=>printedText.includes(item))">
            <span v-html="printedText"></span>
            <template v-if="printedText.includes('<tips>')">
              ...
              <span class='iconfont jiazai'></span>
            </template>
          </template>

        </div>
      </div>
    </div>
    <!-- 输入区域 -->
    <ai-bottom-com :inputText.sync="inputText" :inputSwitch.sync="inputSwitch" :loading="loading"
                   @sendMessage="sendMessage"></ai-bottom-com>
  </div>
</template>

<script>
import ClientJs from '@/base/clientjs'
import Storage from '@/base/storage'
import sellCard from './components/sellCard.vue' // 推荐卡片
import { h5RoamPackageAgent } from '../request/index'
import { getCurrentTag, getTagContent, groupArrayByTwo, handleCardContent, removeTipsTags } from './utils'
import { getUuid } from '../../thirdComponents/qywx/utils'
import aiTopCom from '../common/aiCom/aiTopCom.vue'
import aiBottomCom from '../common/aiCom/aiBottomCom.vue'
// import Vconsole from 'vconsole'
//
// new Vconsole()
export default {
  mixins: [],
  components: { sellCard, aiTopCom, aiBottomCom },
  data() {
    return {
      uinfo: Storage.session.get('userInfo') || {},
      phone: '',// 手机号
      gmChatList: Storage.session.get('gmChatList') || [
        { 'bot': true, 'content': '我是您的国漫推荐助手，请告诉我您的需求。' }
        // { 'isUser': true, 'content': '测试' },
        // { 'ai': true, 'content': '测试1', 'currentComponent': 'sellCard' },
        // { 'isUser': true, 'content': '测试' },
        // { 'ai': true, 'content': '测试222222', 'currentComponent': 'sellCard' }
      ], // 对话列表
      sessionId: Storage.session.get('sessionId') || getUuid(String(new Date().getTime())), // 会话标识
      // sessionId: '1745979018180DD96E001F45BB443', // 会话标识
      inputText: '',// 输入文字
      inputText2: '',// 暂存输入文字
      tryCount: '',// 尝试次数
      inputSwitch: false,
      loading: false, // 正在加载
      printedText: '', // 打字
      cardList: [
        // { reason: '测测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试试' }
      ],
      hasPushedPortrait: false, // 设置标志变量为true，表示已经push过用户画像内容
      recommend: {
        key1: '我要去香港和澳门旅行5天，买什么出境包最合适？',
        key2: '推荐最近的国漫优惠活动！',
        key3: '购买后怎么激活，多久生效，要不要换手机卡？',
        key4: '国漫流量能共享给副卡使用吗？',
        key5: '包天不限量套餐'
      } // 快捷推荐列表
    }
  },
  methods: {
    // 返回上一页
    goPrev() {
      // 移除sessionId
      Storage.session.remove('sessionId')
      // 移除套餐列表
      Storage.session.remove('gmChatList')
    },
    // 输入发送
    sendMessage(inputText) {
      if (!inputText.trim() || this.inputSwitch || this.loading) return
      // 用户消息
      this.gmChatList.push({
        content: inputText,
        isUser: true
      })
      // 滚到底部
      this.scrollToBottom()
      // 保存一下（出错时重新发送使用）
      this.inputText2 = inputText
      this.getKeyWord(inputText)
      this.inputText = ''
    },
    // 滚到底部
    scrollToBottom() {
      this.$nextTick(() => {
        if (this.$refs.messagesContainer) {
          const container = this.$refs.messagesContainer
          container.scrollTop = container.scrollHeight + 1000
        }
      })
    },
    // 存储当前对话
    setGmChat() {
      Storage.session.set('gmChatList', this.gmChatList)
    },
    goAnswer(text) {
      if (this.loading) return
      this.inputText = text
      this.sendMessage(text)
    },
    // 获取AI回复
    getKeyWord(inputTextTmp) {
      this.loading = true
      let tokenId = this.uinfo.tokenid.replace(/\+/g, '%2B').replace(/&/g, '%26').replace(/\//g, '%2F').replace(/=/g, '%3D')
      let params = {
        channelId: '1',//渠道 1：网格通，2：掌厅
        sessionId: this.sessionId,//会话ID
        serviceNumber: this.phone,// 手机号码
        operId: this.uinfo.crmId || this.uinfo.servNumber,//操作人ID
        answer: inputTextTmp ? inputTextTmp : this.phone,//用户发送的消息
        packageNum: '4',//推荐的套餐数  默认3个
        staffId: this.uinfo.staffId,
        region: this.uinfo.region,
        crmId: this.uinfo.crmId || this.uinfo.servNumber,
        servNumber: this.uinfo.servNumber,
        tknZigZa: tokenId
      }
      this.streamData(params)
    },
    // 初始化方法
    init() {
      // 获取顶部推荐菜单列表
      this.$refs.aiTopCom.init().then(() => {
        this.$nextTick(() => {
          if (this.gmChatList.length) this.scrollToBottom()
        })
      })
    },
    async streamData(params) {
      let allText = ''
      let hasPushedPortrait = false
      let packageItem = false
      let packageIndex = 0
      console.info('流式对话请求参数', params)
      // console.info(data)


      const response = await h5RoamPackageAgent(params, this.uinfo)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      const reader = response.body.getReader()
      const decoder = new TextDecoder('utf-8')

      while (true) {
        const { done, value } = await reader.read()
        // 请求结束
        if (done) {
          this.loading = false
          let newStr = allText.split('\n').join('')
          console.info('newStr', newStr)
          // 防止接口返回错误
          if (newStr.includes('error')) {
            this.tryCount++
            if (this.tryCount > 10) {
              this.tryCount = 0
            } else {
              this.getKeyWord(this.inputText2)
            }
            return
          }
          if (this.cardList.length) {
            this.gmChatList.push({
              ai: true,
              currentComponent: 'sellCard',
              packageList: groupArrayByTwo(handleCardContent(newStr))
            })
            this.$forceUpdate()
          } else if (newStr.includes('question')) {
            this.gmChatList.push({
              bot: true,
              content: getTagContent(newStr, 'question')
            })
          } else if (newStr.includes('message')) {
            this.gmChatList.push({
              bot: true,
              content: getTagContent(newStr, 'message')
            })
          }
          // 清空卡片列表
          this.cardList = []
          // 提取sessionId
          if (allText.includes('<session_id>')) {
            this.sessionId = getTagContent(newStr, 'session_id')
            Storage.session.set('sessionId', this.sessionId)
          }
          this.printedText = ''
          break
        }
        let chunk2 = decoder.decode(value, { stream: true })
        // 去掉data: 去掉:keep-alive
        let chunk = chunk2.split('\n').join('').replace(/data: /g, '').replace(/:keep-alive/g, '')
        console.info('流式返回输出：',chunk)
        allText += chunk
        // 根据标签处理前端需要展示的数据
        if (chunk.includes('<tips>') || chunk.includes('<message>')) {
          this.printedText = chunk
        } else if (allText.includes('<question>')) {
          // 去掉tips标签和标签内容
          this.printedText = removeTipsTags(this.printedText)
          this.printedText += chunk
        } else if (getCurrentTag(allText) === '<portrait>') {
          this.printedText = removeTipsTags(this.printedText)
          this.printedText += chunk
        }
        // 开始回复套餐包
        else if (getCurrentTag(allText) === '<reason>') {
          if (!packageItem) {
            this.cardList[packageIndex] = {
              reason: '',
              cardLoading: true
            }
            packageItem = true
            this.$forceUpdate()
          }
          if (chunk !== '>') {
            this.cardList[packageIndex].reason += chunk
            this.cardList[packageIndex].reason = this.cardList[packageIndex].reason.replace(/>/g, '')
            this.$forceUpdate()
          }
        } else if (getCurrentTag(allText) === '</sub_url>') {
          if (this.cardList[packageIndex]) {
            this.cardList[packageIndex].cardLoading = false
          }
          packageItem = false
        }

        // 套餐包结束 处理页面显示
        if (allText.includes('</package_item>')) {
          // 提取所有package_item标签内的内容
          const packageItemContents = allText.split('\n').join('').match(/<package_item>[\s\S]*?<\/package_item>/g)
          // 去除 <package_item> 和 </package_item> 标签并按行分割
          if (packageItemContents) {
            packageItemContents.forEach((content, index) => {
              const xmlString = content.replace(/<package_item>|<\/package_item>/g, '').trim()
              let tagStack = []
              let currentTag = ''
              let tagValue = ''
              const result = {}
              for (let i = 0; i < xmlString.length; i++) {
                const char = xmlString[i]
                if (char === '<') {
                  if (xmlString.slice(i, i + 2) === '</') {
                    // 遇到结束标签
                    const endTag = xmlString.slice(i + 2, xmlString.indexOf('>', i))
                    if (tagStack.length > 0 && tagStack[tagStack.length - 1] === endTag) {
                      // 标签匹配成功
                      result[endTag] = tagValue.trim()
                      tagStack.pop()
                      tagValue = ''
                    }
                    i = xmlString.indexOf('>', i)
                  } else {
                    // 遇到开始标签
                    currentTag = xmlString.slice(i + 1, xmlString.indexOf('>', i))
                    tagStack.push(currentTag)
                    i = xmlString.indexOf('>', i)
                  }
                } else {
                  // 收集标签内的值
                  if (tagStack.length > 0) {
                    tagValue += char
                  }
                }
              }
              // 判断当前列表内有没有该套餐包，有不追加，没有追加
              if (this.cardList.every(item => item.code !== result.code)) {
                this.cardList[packageIndex] = result
                packageIndex++
              }
            })
          }

        }
        // 提取用户画像
        if (allText.includes('</portrait>') && !hasPushedPortrait) {
          if (allText.match(/<portrait>(.*?)<\/portrait>/) && allText.match(/<portrait>(.*?)<\/portrait>/)[1]) {
            let portrait = allText.match(/<portrait>(.*?)<\/portrait>/)[1]
            // 添加用户画像
            this.gmChatList.push({
              content: portrait,
              bot: true
            })
            hasPushedPortrait = true // 设置标志变量为true，表示已经push过用户画像内容
          }
        }
        this.scrollToBottom()
      }
      // console.info('智能体请求参数', params)
      // const response = await this.$http.post(`/xsb/chatTools/agent/h5RoamPackageAgent`, params)
      // console.info(response.data.data)
      // this.commPost(response.data.data)
    }
  },
  created() {
    // 保存当前sessionId
    Storage.session.set('sessionId', this.sessionId)
  },
  mounted() {
    window['getUserInfoFromAi'] = (result) => {
      console.info('getUserInfoFromAi', result)
      let res = result.userInfo
      this.uinfo = JSON.parse(res)
      initTokenAfterBack(this.$http, this.uinfo) //重置token
      this.init()
    }

    let goBackFlag = this.$route.query.gobackFlag
    if (goBackFlag === 'webview') {
      ClientJs.getSysInfo('getUserInfoFromAi')
    } else {
      this.init()
    }
  },
}
</script>

<style scoped lang='less'>
.chat-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
  font-size: 14px;
}

.tab-item {
  display: inline-block;
  border-radius: 20px;
  padding: 5px 15px;
  background: linear-gradient(70deg, #FFFFFF 0%, #D5E7FF 100%);
  margin: 0 4px;
  color: #535353;

  .banner-title {
    margin-left: 5px;
  }
}

.messages-wrap {
  flex: 1;
  overflow-y: auto;
  background: linear-gradient(184deg, #D5E7FF 0%, #FFFFFF 100%);
  padding: 0 12px 12px 12px;
  scroll-behavior: smooth;
}

.message-bubble {
  max-width: 99%;
  margin-bottom: 15px;
  position: relative;
  width: fit-content;
}

.user-message {
  margin-left: auto;
  max-width: 90%;
}

.bot-message {
  margin-right: auto;
}

.jiazai {
  display: inline-block;
  font-size: 18px;
  animation: rotate 1.8s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.ai-message {
  width: 90%;
}

.recommend-box {
  //padding: 0 5px;

  .recommend-tip {
    color: #3966af;
    margin-bottom: 10px;
    background: white;
    text-shadow: 0 1px 0 rgba(0, 0, 0, 0.1);
    padding: 8px 15px;
  }
}

.content {
  padding: 8px 13px;
  border-radius: 8px;
  line-height: 1.5;
  word-break: break-word;
}

.user-message .content {
  background: #768EDF;
  color: white;
}

.bot-message .content {
  background: white;
  color: #333;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.padding-tb {
  padding-top: 15px;
  padding-bottom: 15px;
}

.flex {
  display: flex;
}

.ai-reply {
  background: white;
  padding: 15px 13px 0 13px;
  border-radius: 8px;

  .sell-item {
    .iconfont {
      font-size: 18px;
    }
  }

  .box-0, .box-1, .box-loading {
    padding: 14px 12px;
    height: 71px;
  }

  .box-0 {
    background: url("../../assets/img/aiGroup/bg-1.png");
    background-size: 100% 100%;
  }

  .box-1 {
    background: url("../../assets/img/aiGroup/bg-2.png");
    background-size: 100% 100%;
  }

  .box-loading {
    background: url("../../assets/img/aiGroup/bg-loading.gif");
    background-size: 100% 100%;
  }
}

.bg-blue {
  background: #007AFF;
}

.padding-bottom {
  padding-bottom: 15px;
}

.padding-right-xs {
  padding-right: 5px;
}

.text-blue {
  color: #007AFF
}

.text-orange {
  color: #FC7F04;
  font-weight: 600;
}

.text-5E5E5E {
  color: #3D3D3D;
  font-size: 18px;
  font-weight: 600;
  width: 80%;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.text-4C4C4C {
  color: #4C4C4C;
  line-height: 20px;
}

.margin-top-sm {
  margin-top: 10px;
}

.tuijian1 {
  font-size: 20px;
}

.align-center {
  align-items: center;
}

</style>
