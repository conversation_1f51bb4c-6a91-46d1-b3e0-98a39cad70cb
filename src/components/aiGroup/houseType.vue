<!--户型图-->
<template>
  <div class='chat-container'>
    <!--顶部tab切换-->
    <ai-top-com :uinfo="uinfo" ref="aiTopCom" />
    <!-- 消息区域 -->
    <div class='messages-wrap' ref='messagesContainer'>
      <!-- 对话内容-->
      <div
        v-for='(msg, index) in hxtChatList'
        :key='index'
        class='message-bubble'
        :class="{ 'user-message': msg.isUser, 'bot-message': msg.bot,'ai-message':msg.ai }"
      >
        <!--智能选号组件-->
        <component ref="componentCard" v-if='msg.ai'
                   :is='msg.currentComponent'
                   :propData='msg.propData'
                   @changeCur="changeCur"
                   :sessionId='sessionId'
                   :loading.sync="loading"
                   @sessionIdChange="sessionIdChange"
                   :index="index"
                   :key='index'></component>
        <!--普通打字-->
        <div class='content' v-else v-html="msg.content"></div>
      </div>
      <!--加载圈-->
      <div class='message-bubble bot-message' v-if='loading'>
        <div class='content flex align-center'>
          生成中...
          <span class='iconfont jiazai'></span>
        </div>
      </div>
    </div>
    <!-- 输入区域 -->
    <ai-bottom-com :inputText.sync="inputText" :inputSwitch.sync="inputSwitch" :loading="loading"
                   @sendMessage="sendMessage"></ai-bottom-com>
    <!--户型图选择弹框-->
    <hxt-select @loadMore="loadMore" :allLoaded="allLoaded" v-model="hxtSelectModal" @onConfirm="hxtConfirm"
                :hxtList="hxtList"></hxt-select>
  </div>
</template>

<script>
import ClientJs from '@/base/clientjs'
import { BASE64 } from '@/base/coding'
import Storage from '@/base/storage'
import { h5GetSessionId, h5QueryCity, h5QueryFloorPlan, h5UnifiedCapability, h5UpdateSessionId } from '../request/index'
import aiTopCom from '../common/aiCom/aiTopCom.vue'
import aiBottomCom from '../common/aiCom/aiBottomCom.vue'
import hxtCard from './components/hxtCard.vue'
import hxtSelect from './popup/hxtSelect.vue'
import json from './json/index.json'
// import Vconsole from 'vconsole'
// new Vconsole()

export default {
  mixins: [],
  components: { aiTopCom, aiBottomCom, hxtCard, hxtSelect },
  data() {
    return {
      uinfo: Storage.session.get('userInfo') || {},
      hxtChatList: Storage.session.get('hxtChatList') || [
        // { 'isUser': true, 'content': '中海城南公馆' },
        {
          'bot': true,
          'content': '您好！请输入小区名称和城市名称生成户型图'
        }
        // { 'isUser': true, 'content': '测试' },
        // { 'ai': true, 'content': '测试222222', 'currentComponent': 'hxtCard' }
      ], // 对话列表
      sessionId:  '', // 会话标识
      inputText: '',// 输入文字
      inputText2: '',// 暂存输入文字
      hxtSelectModal: false,
      tryCount: '',// 尝试次数
      inputSwitch: false,
      loading: false, // 正在加载
      houseInfo: {},// 小区相关信息
      start: 1,// 请求页数
      allLoaded: false,// 是否全部加载完毕
      cityList: json.cityList, // 城市列表
      hxtList: [] // 户型图列表
    }
  },
  methods: {
    // 输入发送
    async sendMessage(inputText) {
      if (!inputText.trim() || this.inputSwitch || this.loading) return
      // 用户消息
      this.hxtChatList.push({
        content: inputText,
        isUser: true
      })
      // 保存一下（出错时重新发送使用）
      this.inputText2 = inputText
      // 先获取小区名称和城市编码
      const param = {
        crmId: this.uinfo.crmId || this.uinfo.servNumber,
        region: this.uinfo.region,
        keyword: inputText,
        // capabilityType: '1',
        // capabilityCode: '63e35501567649859e1dfa941d718b44',
        // authToken: 'f85a1a26a2fb4603a1811ccd51db216b'
        agentId:'100003'
      }
      let res = await h5UnifiedCapability(param)
      // res = '{"houseName":"莫愁新寓","cityName":"南京"}'
      console.info('获取小区名称城市名称返回：', res)
      if (res) {
        let houseInfo
        try {
          // 尝试直接解析JSON
          houseInfo = JSON.parse(res)
        } catch (e) {
          // 尝试从错误信息中提取JSON
          const jsonString = res.substring(res.indexOf('{'), res.lastIndexOf('}') + 1)
          try {
            houseInfo = JSON.parse(jsonString)
          } catch (e) {
            this.hxtChatList.push({ bot: true, content: '请输入完整的小区名称和城市名称' })
            return
          }
        }
        await this.getHouseInfo(houseInfo)
      } else {
        this.hxtChatList.push({ bot: true, content: '获取小区名称和城市名称失败' })
      }
      this.scrollToBottom()
    },
    // 处理小区和城市名称
    async getHouseInfo(houseInfo) {
      let item = { bot: true, content: '请输入完整的小区名称和城市名称' }
      console.info('解析字符串', houseInfo)
      if (houseInfo.houseName && houseInfo.cityName) {
        let cityItem = this.cityList.find(item => houseInfo.cityName.includes(item.name) || item.name.includes(houseInfo.cityName) || item.name == houseInfo.cityName)
        if (cityItem) {
          this.houseInfo = {
            q: houseInfo.houseName,//小区名称
            cityid: cityItem.cityid//城市编码
          }
          // 获取户型图
          let data = {
            ...this.houseInfo,
            start: this.start//起始页
          }
          await this.h5QueryFloorPlan(data)
          this.inputText = ''
        } else {
          this.hxtChatList.push(item)
        }
      } else {
        this.hxtChatList.push(item)
      }
    },
    // 滚到底部
    scrollToBottom() {
      this.$nextTick(() => {
        if (this.$refs.messagesContainer) {
          const container = this.$refs.messagesContainer
          container.scrollTop = container.scrollHeight + 1000
        }
      })
    },
    // 确认选择户型图
    hxtConfirm(item) {
      this.hxtChatList.push({
        ai: true,
        currentComponent: 'hxtCard',
        propData: item
      })
      this.scrollToBottom()
    },
    // 生成热力图
    changeCur(index,res){
      this.hxtChatList[index].propData = { ...this.hxtChatList[index].propData, ...res }
    },
    // 初始化方法
    init() {
      // 获取顶部推荐菜单列表
      this.$refs.aiTopCom.getRecommendIcon()
      this.getSessionId(false)
      let k = this.$route.query.k
      if (k) {
        let str = BASE64.decode(decodeURIComponent(k))
        this.sendMessage(str)
      }
    },
    // 获取sessionId
    async getSessionId(isUpdate=true) {
      let res
      if(isUpdate){
        // 刷新
        console.info('刷新sessionId请求参数：',this.sessionId )
        res = await h5UpdateSessionId({ oldSession: this.sessionId })
      }else{
        // 第一次获取
         res = await h5GetSessionId({ appKey: 'D6K8sTOPG47aQm23H', appSecret: '15fcca82760b465fa444a92a' })
      }
      if (res) {
        this.sessionId = res.sessionId
        console.info('获取sessionId', res)
        return res
      }
    },
    // 处理sessionId失效
    sessionIdChange(data) {
      this.getSessionId(true).then((res) => {
        if (res) {
          // 重新生成热力图
          this.$refs.componentCard.h5QueryWifiCover(data)
        }
      })
    },
    // 加载更多
    loadMore() {
      this.start++
      // 获取户型图
      let data = {
        ...this.houseInfo,
        start: this.start//起始页
      }
      this.h5QueryFloorPlan(data)
    },
    // 获取城市列表
    async h5QueryCity() {
      const res = await h5QueryCity({ sessionId: this.sessionId })
      console.info('获取城市列表返回', res)
      if (res) {
        this.cityList = res.flatMap(province =>
          province.cities.map(city => ({
            ...city,
            province: province.province
          }))
        )
        console.info('this.cityList', this.cityList)
      }
    },
    // 生成户型图
    async h5QueryFloorPlan(data) {
      this.loading = true
      let data1 = Object.assign(data, {
        sessionId: this.sessionId,
        num: '500'//每页数量
      })
      console.info('生成户型图参数：', data1)
      const res = await h5QueryFloorPlan(data1)
      this.loading = false
      console.info('生成户型图返回：', res)
      if (res === '303') {
        // 登录超时
        this.getSessionId(true).then((res) => {
          if (res) {
            // 获取户型图
            let data = {
              ...this.houseInfo,
              start: this.start//起始页
            }
            this.h5QueryFloorPlan(data)
          }
        })
      } else if (res && res.length) {
        // 第一页
        if (this.start == 1) {
          this.hxtList = []
          // 只有一个
          if (res.length === 1) {
            this.hxtConfirm(res[0])
          } else {
            this.hxtSelectModal = true
            this.hxtList = res
          }
        } else {
          // 其他页
          this.hxtList = [...this.hxtList, ...res]
        }
        // 是否全部加载完毕
        this.allLoaded = res.length < 500
      } else {
        this.hxtChatList.push({
          bot: true,
          content: '生成户型图失败'
        })
      }
    }
  },
  created() {
  },
  mounted() {
    window['getUserInfoFromAi'] = (result) => {
      let res = result.userInfo
      this.uinfo = JSON.parse(res)
      initTokenAfterBack(this.$http, this.uinfo) //重置token
      this.init()
    }
    let goBackFlag = this.$route.query.gobackFlag
    if (goBackFlag === 'webview') {
      ClientJs.getSysInfo('getUserInfoFromAi')
    } else {
      this.init()
    }

  }
}
</script>

<style scoped lang='less'>
.chat-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
  font-size: 14px;
}

.messages-wrap {
  flex: 1;
  overflow-y: auto;
  background: linear-gradient(184deg, #D5E7FF 0%, #FFFFFF 100%);
  padding: 0 12px 12px 12px;
  scroll-behavior: smooth;
}

.message-bubble {
  max-width: 99%;
  margin-bottom: 15px;
  position: relative;
  width: fit-content;
}

.user-message {
  margin-left: auto;
  max-width: 90%;
}

.bot-message {
  margin-right: auto;
}

.jiazai {
  font-size: 18px;
  animation: rotate 1.8s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.ai-message {
  width: 90%;
}

.content {
  padding: 8px 13px;
  border-radius: 8px;
  word-break: break-word;
}

.user-message .content {
  line-height: 1.5;
  background: #768EDF;
  color: white;
}

.bot-message .content {
  line-height: 1.5;
  background: white;
  color: #333;
}

.flex {
  display: flex;
}

.align-center {
  align-items: center;
}
</style>
