<template>
  <div class='chat-container'>
    <!--顶部tab切换-->
    <ai-top-com :uinfo="uinfo" ref="aiTopCom" />
    <!-- 消息区域 -->
    <div ref='messagesContainer' class='messages-wrap'>
      <img v-if="!khChatList.length" src="../../assets/img/aiGroup/khhx/tip.png" width="100%" />
      <!-- 对话内容-->
      <div
        v-for='(msg, index) in khChatList'
        :key='index'
        :class="{ 'user-message': msg.isUser, 'bot-message': msg.bot,'ai-message':msg.ai }"
        class='message-bubble'
      >
        <!--组件-->
        <component :is='msg.currentComponent'
                   v-if='msg.ai'
                   :key='index'
                   :index="index"
                   :propData='msg.propData'
                   @chatChange="sendMessage"
                   @changeMore="changeMore"
                   @listChange="listChange"
                   @viewDetails="viewDetails"></component>
        <!--普通打字-->
        <div v-else class='content' v-html="msg.content"></div>
      </div>
      <div v-if="loading">
        <!--加载圈-->
        <div v-if='!printedText.length' class='message-bubble bot-message'>
          <div class='content flex align-center'>
            生成中...
            <span class='iconfont jiazai'></span>
          </div>
        </div>
        <!-- 思考盒子-->
        <thinkCard :loading="loading" v-else-if="thinkList.length && thinkCardShow" :thinkList="thinkList"></thinkCard>
        <!--流式回答-->
        <div v-else-if="printedText.length" class='message-bubble bot-message'>
          <div class='content'>
            <span v-html="printedText"></span>
          </div>
        </div>
      </div>
    </div>
    <!-- 输入区域 -->
    <ai-bottom-com :inputSwitch.sync="inputSwitch" :inputText.sync="inputText"
                   :loading="loading"
                   @sendMessage="sendMessage"></ai-bottom-com>
  </div>
</template>

<script>
import ClientJs from '@/base/clientjs'
import Storage from '@/base/storage'
import { h5AiPackageInsightNew, h5alreadyOpened, h5CustMaintainBusiInfoQuey, h5queryLabelByTel } from '../request/index'
import { formatNumber, getTagContent, khhxTag, khhxTag2, parseText, parseValue, renderedMarkdown } from './utils'
import { getUuid } from '../../thirdComponents/qywx/utils'
import aiTopCom from '../common/aiCom/aiTopCom.vue'
import aiBottomCom from '../common/aiCom/aiBottomCom.vue'
import hxxqCard from './components/hxxqCard.vue'
import khhxCard from './components/khhxCard.vue'
import thinkCard from './components/thinkCard.vue'
// import mock from './json/index.json'
// import Vconsole from 'vconsole'
// new Vconsole()
export default {
  mixins: [],
  components: { aiTopCom, aiBottomCom, hxxqCard, khhxCard, thinkCard },
  data() {
    return {
      // —— 用户基础信息 ——
      uinfo: Storage.session.get('userInfo') || {},
      sessionId: getUuid(String(new Date().getTime())), // 会话唯一标识
      // —— 对话相关 ——
      khChatList: Storage.session.get('khChatList') || [], // 对话列表（从缓存恢复）
      inputText: '', // 当前输入内容
      inputText2: '', // 暂存输入（用于错误重试）
      tryCount: '', // 重试次数计数器
      inputSwitch: false, // 输入开关（控制输入状态）
      // —— 加载与展示状态 ——
      loading: false, // 接口请求加载中
      printedText: '', // 打字机效果文本
      BasicProfile: '',// 基础信息
      tagStr: '', // 用户标签原始字符串
      UserTrajectoryStr: ``, //  用户轨迹初始模板
      FamilyPortraitStr: ``,  // 家庭共享套餐
      //页面展示核心数据
      propData: {
        tagData: [], // 标签列表（用于展示）
        portrait: '', // 用户画像文本
        // 套餐信息子对象
        package: {
          mainPackage: '', // 主套餐名称
          flow: null, // 流量数据
          flowPer: 0, // 流量使用百分比
          call: null, // 通话数据
          callPer: 0, // 语音使用百分比
          isBroadband: null, // 是否办理宽带（0=否，1=是）
          charge: null // 实际收费金额
        },
        phone: '', // 当前查询的手机号码
        haveDetail: false, // 是否已加载画像详情
        chartData: {}, // 图表数据（流量/语音统计）
        tabValue: null, // 标签页切换值（1=基础信息，2=家庭画像，3=用户轨迹）
        // 家庭相关数据
        qryUserShareResource: {}, // 家庭共享数据
        qrybillnew: {}, // 家庭代付数据
        UserTrajectory: {}, // 用户轨迹
        aiInquiry: {} // AI问诊数据
      },
      thinkList: [],// 思考列表
      token_tool: '', // 总结
      thinkCardShow: true // 是否展示思考卡片
    }
  },
  methods: {
    renderedMarkdown, // 外部引入的markdown渲染方法（保持原样）
    /**
     * 输入发送：用户发送查询指令
     * @param {string} inputText - 用户输入内容
     */
    sendMessage(inputText) {
      // 拦截无效输入（空内容、输入锁定、加载中）
      if (!inputText.trim() || this.inputSwitch || this.loading) return

      // 追加用户消息到对话列表
      this.khChatList.push({
        content: inputText,
        isUser: true
      })

      // 滚动到对话底部
      this.scrollToBottom()
      // 暂存输入（用于后续错误重试）
      this.inputText2 = inputText
      // 触发AI查询
      this.getKeyWord(inputText)
      // 清空输入框
      this.inputText = ''
    },
    // 滚到底部
    scrollToBottom() {
      this.$nextTick(() => {
        if (this.$refs.messagesContainer) {
          const container = this.$refs.messagesContainer
          container.scrollTop = container.scrollHeight + 1000
        }
      })
    },

    /**
     * 查看画像详情：追加详情卡片到对话列表
     * @param {number} index - 当前卡片在对话列表的索引
     */
    viewDetails(index) {
      // 标记当前卡片已加载详情
      if (this.khChatList[index] && this.khChatList[index].propData) {
        this.khChatList[index].propData.haveDetail = true
        // 追加用户"查看详情"的交互记录
        this.khChatList.push({
          content: `查看${this.khChatList[index].propData.phone}的画像详情`,
          isUser: true
        })
        // 追加详情卡片（深拷贝避免引用污染）
        this.khChatList.push({
          ai: true,
          currentComponent: 'hxxqCard',
          propData: JSON.parse(JSON.stringify(this.khChatList[index].propData))
        })
        // 滚动到底部
        this.scrollToBottom()
      }
    },

    /**
     * 查看思考过程：切换显示/隐藏状态
     * @param {number} index - 当前卡片在对话列表的索引
     */
    changeMore(index) {
      if (this.khChatList[index] && this.khChatList[index].propData) {
        this.khChatList[index].propData.thinkHide = !this.khChatList[index].propData.thinkHide
      }
    },
    listChange(index, showList) {
      if (this.khChatList[index] && this.khChatList[index].propData) {
        this.khChatList[index].propData.UserTrajectory.showList = showList
      }
    },
    /**
     * 获取AI回复：组装参数并触发流式请求
     * @param {string} inputTextTmp - 用户输入的查询文本
     */
    getKeyWord(inputTextTmp) {
      this.loading = true
      let params = {
        userInput: inputTextTmp,
        phoneNumber: this.propData.phone,
        channelId: '1',
        region: this.uinfo.region, // 地市信息（取自用户信息）
        sessionId: this.sessionId,
        channelName: '网格通'
      }

      // 触发流式数据请求
      this.streamData(params)
    },
    // 初始化方法
    init() {
      if (this.$refs.aiTopCom) {
        this.$refs.aiTopCom.getRecommendIcon()
      }
    },
    // 异动用户业务信息查询接口
    async h5CustMaintainBusiInfoQuey(servNumber) {
      let params = {
        dataSource: '2',
        operMsisdn: this.uinfo.servNumber,
        operId: this.uinfo.crmId,
        custMsisdn: servNumber
      }
      const res = await h5CustMaintainBusiInfoQuey(params)
      if (res) {
        // 主套名称
        console.info('异动用户业务信息查询接口', res)
        this.propData.chartData = res.userAttrList.filter(item => item.userInfoName.includes('DOU') || item.userInfoName.includes('主叫分钟'))
        let mainPackageItem = res.userAttrList.find(item => item.userInfoName == '主套名称（当月）' && item.userInfoValue)
        if (mainPackageItem) {
          this.propData.package.mainPackage = mainPackageItem.userInfoValue
        }
        // 流量
        let flowItem = res.userAttrList.find(item => item.userInfoName == 'DOU（上月）' && item.userInfoValue)
        if (flowItem) {
          this.propData.package.flow = parseValue(flowItem.userInfoValue)
        }
        // 通话
        let callItem = res.userAttrList.find(item => item.userInfoName == '主叫分钟（上月）' && item.userInfoValue)
        if (callItem) {
          this.propData.package.call = parseValue(callItem.userInfoValue)
        }
        // 实际收费
        let chargeItem = res.userAttrList.find(item => item.userInfoName == '主套实收（上月）' && item.userInfoValue)
        if (chargeItem) {
          this.propData.package.charge = parseText(chargeItem.userInfoValue)
        }
      }
    },
    // 客户三看
    async h5queryLabelByTel(inputTextTmp) {
      const res = await h5queryLabelByTel(inputTextTmp)
      if (res) {
        console.info('客户三看h5queryLabelByTel', res)
        if (res.versionList) {
          // 流量
          if (res.versionList.s000000096142) {
            this.propData.package.flowPer = res.versionList.s000000096142
          }
          // 语音
          if (res.versionList.s000000096152) {
            this.propData.package.callPer = res.versionList.s000000096152
          }
        }
      }
    },
    // 查宽带
    async h5alreadyOpened(inputTextTmp) {
      const res = await h5alreadyOpened(inputTextTmp)
      if (res) {
        // 是否开通宽带 0：否，1：是
        this.propData.package.isBroadband = res.isMband
        console.info('h5alreadyOpened', res)
      } else {
        this.propData.package.isBroadband = null
      }
    },

    /**
     * 流式数据请求：处理AI接口的流式返回，实时更新UI
     * @param {object} params - AI查询参数
     */
    async streamData(params) {
      // 状态管理对象：集中管理流式处理所需的所有状态
      const streamState = {
        allText: '',          // 累积所有有效文本内容
        buffer: '',           // 缓冲区：暂存待解析的片段
        bracketBalance: 0,    // 括号平衡计数器（处理嵌套JSON）
        inString: false,      // 是否处于字符串内部（避免误判字符串中的{}）
        stringQuote: ''       // 记录当前字符串的引号类型（单/双引号）
      }

      // 初始化状态
      this.token_tool = ''
      this.thinkList = []
      this.printedText = ''
      console.info('流式对话请求参数', JSON.stringify(params))

      try {
        // 发起流式请求
        const response = await h5AiPackageInsightNew(params, this.uinfo)
        if (!response.ok) {
          this.khChatList.push({ bot: true, content: `HTTP错误！状态码: ${response.status}` })
          this.loading = false // 异常时关闭加载状态
          throw new Error(`HTTP错误！状态码: ${response.status}`)
        }

        // 获取读取器和解码器
        const reader = response.body.getReader()
        const decoder = new TextDecoder('utf-8')

        // 循环读取流式数据
        while (true) {
          const readResult = await reader.read()
          const done = readResult.done
          const value = readResult.value

          // 流结束：处理收尾逻辑
          if (done) {
            this.loading = false
            this.finalizeBuffer(streamState.buffer, streamState.allText) // 处理缓冲区剩余内容
            break
          }

          // 解码并清洗片段（移除无效字符）
          let chunk = decoder.decode(value, { stream: true })
          chunk = this.cleanStreamChunk(chunk)
          if (!chunk) continue // 跳过空片段
          // 逐字符处理：精准判断JSON边界
          for (let i = 0; i < chunk.length; i++) {
            const char = chunk.charAt(i)

            this.updateStringState(char, streamState)
            this.updateBracketBalance(char, streamState)
            streamState.buffer += char

            // 尝试解析完整JSON对象
            if (this.isJsonReady(streamState)) {
              this.tryParseJsonChunk(streamState)
            }
          }
        }
      } catch (error) {
        // 统一错误处理
        this.handleStreamError('接口返回异常，请稍后重试')
        console.error('流式数据处理异常:', error)
      }
    },

    /**
     * 辅助方法：清理流数据块
     */
    cleanStreamChunk(rawChunk) {
      return rawChunk
        .replace(/\n/g, '')           // 移除换行
        .replace(/data: /g, '')       // 移除流式前缀
        .replace(/:keep-alive/g, '')  // 移除保活标识
        .replace('[DONE]', '')        // 移除结束标识
        .trim()

    },
    /**
     * 辅助方法：更新字符串状态（判断是否在字符串内部）
     */
    updateStringState(char, streamState) {
      // 开始字符串
      if ((char === '"' || char === '\'') && !streamState.inString) {
        streamState.inString = true
        streamState.stringQuote = char
        return
      }

      // 结束字符串（非转义的引号）
      if (char === streamState.stringQuote && streamState.inString) {
        const prevChar = streamState.buffer.length > 0 ? streamState.buffer.charAt(streamState.buffer.length - 1) : ''
        if (prevChar !== '\\') {
          streamState.inString = false
          streamState.stringQuote = ''
        }
      }
    },

    /**
     * 辅助方法：更新括号平衡计数
     */
    updateBracketBalance(char, streamState) {
      if (!streamState.inString) {
        if (char === '{') {
          streamState.bracketBalance++
        }
        if (char === '}') {
          streamState.bracketBalance = Math.max(0, streamState.bracketBalance - 1)
        }
      }
    },

    /**
     * 辅助方法：判断是否可以尝试解析JSON
     */
    isJsonReady(streamState) {
      return streamState.bracketBalance === 0
        && !streamState.inString
        && streamState.buffer.includes('{')
    },

    /**
     * 辅助方法：尝试解析JSON片段
     */
    tryParseJsonChunk(streamState) {
      try {
        const jsonData = JSON.parse(streamState.buffer)
        this.processParsedJson(jsonData, streamState)
        // 解析成功：重置缓冲区
        streamState.buffer = ''
        streamState.bracketBalance = 0
      } catch (e) {
        this.handleParseError(streamState, e)
      }
    },

    /**
     * 辅助方法：处理解析成功的JSON数据
     */
    processParsedJson(jsonData, streamState) {
      // 处理错误信息
      if (jsonData.error) {
        this.khChatList.push({
          bot: true,
          content: jsonData.error.msg || '发生未知错误'
        })
        return
      }
      // 处理内容数据
      if (jsonData.content) {
        const contentStr = this.switchContent(jsonData)
        streamState.allText += contentStr
        // 更新思考列表
        if (this.thinkList.length > 0) {
          const lastThinkItem = this.thinkList[this.thinkList.length - 1]
          if (lastThinkItem) {
            lastThinkItem.printedText += contentStr
          }
        }
        this.printedText += contentStr
        // 更新打印文本
      }
      // 滚动到底部
      this.scrollToBottom()
    },

    /**
     * 辅助方法：处理解析错误
     */
    handleParseError(streamState, error) {
      // 处理连续的JSON对象（如"}{"分隔的情况）
      if (streamState.buffer.includes('}{')) {
        const splitIndex = streamState.buffer.indexOf('}{') + 1
        const firstPart = streamState.buffer.slice(0, splitIndex)
        streamState.buffer = streamState.buffer.slice(splitIndex)

        try {
          const jsonData = JSON.parse(firstPart)
          this.processParsedJson(jsonData, streamState)
          streamState.bracketBalance = 0
        } catch (splitErr) {
          console.warn('连续JSON分割后解析失败:', firstPart, splitErr)
        }
      } else {
        console.warn('JSON片段暂不完整，继续积累:', streamState.buffer, error)
      }
    },

    /**
     * 辅助方法：统一处理流错误
     */
    handleStreamError(message) {
      this.khChatList.push({ bot: true, content: message })
      this.loading = false // 确保加载状态关闭
    },

    /**
     * 内容分发：根据AI返回的内容类型，执行对应逻辑
     * @param {object} jsonData - AI返回的单条JSON数据
     * @returns {string} 处理后的文本内容
     */
    switchContent(jsonData) {
      const content = jsonData.content
      console.info('流式返回：', jsonData)
      // 大模型总结
      if (jsonData.type == 'token_tool') {
        this.token_tool += jsonData.content || ''
      }
      // 非对象类型直接返回文本
      if (typeof content !== 'object') return content || ''
      if (!content.type) return ''
      // 根据内容类型分发处理
      switch (content.type) {
        case 'ServiceNumber': // 手机号提取
          this.propData.phone = content.content
          return ''
        case 'Tag': // 用户标签：暂存原始字符串
          this.propData.tabValue = 1 // 切换到基础信息tab
          this.tagStr = content.content
          this.handleCard()
          return ''
        case 'BasicProfile': // 基础信息：返回内容用于展示
          this.BasicProfile += content.content
          return content.content
        case 'LabelDisplay':
          this.thinkList.push({
            title: content.content,
            printedText: ''
          })
          return ''
        case 'FamilyPortrait': // 家庭画像：（家庭共享+家庭带付费都在这里面）
          this.propData.tabValue = 2 // 切换到家庭画像tab
          this.FamilyPortraitStr = content.content
          this.handleCard()
          return ''
        case 'UserTrajectory': // 用户轨迹：暂存原始字符串并切换tab
          this.propData.tabValue = 3 // 切换到用户轨迹tab
          this.UserTrajectoryStr = content.content
          this.handleCard()
          return ''
        default:
          return ''
      }
    },

    /**
     * 流结束收尾：处理缓冲区剩余内容，按当前tab解析对应业务数据
     * @param {string} buffer - 流结束后缓冲区剩余内容
     * @param {string} allText - 流期间累积的所有文本
     */
    finalizeBuffer(buffer, allText) {
      // 处理缓冲区最后残留的内容（合并到总文本）
      this.handleRemainingBuffer(buffer, allText)
      if (!allText.length) return

      // 清洗客户画像文本并检查错误重试
      allText = allText.replace(/\n/g, '').replace(/:keep-alive/g, '')

      // 错误重试逻辑（包含error标识则重试，最多10次）
      if (this.handleErrorRetry(allText)) return

      const errItem = { bot: true, content: this.token_tool.length ? this.token_tool : allText }
      this.khChatList.push(errItem)
      this.thinkCardShow = true
      setTimeout(() => {
        // 重置临时数据
        this.tagStr = ``
        this.BasicProfile = ``
        this.UserTrajectoryStr = ``
        this.FamilyPortraitStr = ``
        this.propData.tabValue = null
        this.propData.qryUserShareResource = {}
        this.propData.qrybillnew = {}
        this.propData.UserTrajectory = {}
        this.propData.thinkList = []
        this.printedText = ''
        this.token_tool = ''
      })
      // 滚动到底部并重置打字机文本
      this.scrollToBottom()
    },

    // 处理卡片
    async handleCard() {
      this.thinkCardShow = false
      this.printedText = ''
      const errItem = { bot: true, content: '数据解析失败，请重试' }
      let isError = false // 标记是否发生解析错误
      // 根据当前tab页解析对应数据
      switch (this.propData.tabValue) {
        case 1: // 基础信息tab：解析用户画像和标签
          isError = !this.parseBasicProfile()
          // 判断有没有换手机号
          await this.h5CustMaintainBusiInfoQuey(this.propData.phone) // 套餐/流量/通话
          await this.h5queryLabelByTel(this.propData.phone) // 流量/语音百分比
          await this.h5alreadyOpened(this.propData.phone) // 宽带开通状态
          break
        case 2: // 家庭画像tab：解析家庭共享和代付数据
          isError = !this.parseFamilyData()
          break
        case 3: // 用户轨迹tab：解析轨迹数据
          isError = !this.parseUserTrajectory()
          break
        default:
          console.warn('未知tabValue，不解析数据')
          break
      }
      // 根据解析结果添加对话内容（错误提示/正常卡片）
      this.khChatList.push(isError ? errItem : {
        ai: true,
        currentComponent: 'khhxCard',
        propData: JSON.parse(JSON.stringify({
          ...this.propData,
          thinkHide: true,
          thinkList: this.thinkList
        }))
      })
    },
    /**
     * 处理缓冲区剩余内容：尝试解析最后片段，合并到总文本
     * @param {string} buffer - 剩余缓冲区内容
     * @param {string} allText - 总文本
     */
    handleRemainingBuffer(buffer, allText) {
      if (!buffer.trim()) return
      try {
        const lastJson = JSON.parse(buffer)
        if (lastJson.content) {
          allText += lastJson.content
          this.printedText += lastJson.content
        }
      } catch (e) {
        console.warn('流结束残留片段解析失败，直接拼接:', buffer, e)
        allText += buffer
      }
    },

    /**
     * 错误重试判断：若文本含error标识，触发重试逻辑
     * @returns {boolean} 是否触发了重试
     */
    handleErrorRetry(allText) {
      if (!allText.includes('error')) return false

      this.tryCount++
      if (this.tryCount <= 10) {
        this.getKeyWord(this.inputText2) // 重试上次查询
      } else {
        this.tryCount = 0 // 超过最大次数，重置计数器
      }
      return true // 已触发重试，终止后续解析
    },

    /**
     * 解析基础信息（tabValue=1）：用户画像和标签
     * @returns {boolean} 解析是否成功
     */
    parseBasicProfile() {
      try {
        console.info('BasicProfile', this.BasicProfile)
        console.info('tagStr', this.tagStr)
        const portrait = getTagContent(this.BasicProfile, 'portrait')
        // 处理用户标签（若存在）
        if (this.tagStr) {
          const tagJson = JSON.parse(this.tagStr)
          this.propData.portrait = portrait
          this.propData.tagData = khhxTag2(tagJson)
          this.propData.tagData2 = khhxTag(tagJson)
        }
        // 无标签时仅处理画像
        else if (portrait) {
          this.propData.portrait = portrait
          this.propData.tagData = []
          this.propData.tagData2 = []
        }
        return true // 解析成功
      } catch (e) {
        console.error('基础信息解析失败:', e)
        return false // 解析失败
      }
    },

    /**
     * 解析家庭数据（tabValue=2）：家庭共享和代付数据
     * @returns {boolean} 解析是否成功
     */
    parseFamilyData() {
      let isSuccess = true // 整体解析结果

      // 解析家庭共享数据
      try {
        const qryUserShareResource = getTagContent(this.FamilyPortraitStr, 'qryUserShareResource')
        // console.info('家庭共享数据：', qryUserShareResource)
        if (qryUserShareResource) {
          const shareJson = JSON.parse(qryUserShareResource)
          let shareDetailInfo = null
          if (shareJson.share_detail_list) {
            shareDetailInfo = shareJson.share_detail_list.share_detail_info
          }
          if (shareDetailInfo) {
            this.propData.qryUserShareResource = this.processData(shareDetailInfo)
          }
        }
      } catch (e) {
        console.error('家庭共享数据解析失败:', e)
        isSuccess = false
      }

      // 解析家庭代付数据
      try {
        const qrybillnewStr = getTagContent(this.FamilyPortraitStr, 'qrybillnew')
        this.handleFamilyPayment(qrybillnewStr)
      } catch (e) {
        console.error('家庭代付数据解析失败:', e)
        isSuccess = false
      }

      return isSuccess
    },

    /**
     * 解析用户轨迹（tabValue=3）：轨迹数据格式化
     * @returns {boolean} 解析是否成功
     */
    parseUserTrajectory() {
      try {
        const UserTrajectoryStr = getTagContent(this.UserTrajectoryStr, 'getUserTrajectory')
        if (UserTrajectoryStr) {
          const trajectoryJson = JSON.parse(UserTrajectoryStr)
          if (trajectoryJson.dataList && trajectoryJson.dataList.length) {
            // 删除账单信息
            let list = trajectoryJson.dataList.filter(item => {
              if (item.businessType !== '用户账单信息') {
                const timeArr = (item.createDate || '').split(' ')
                item.time1 = timeArr[0] || '' // 日期（如2025-06-01）
                item.item2 = timeArr[1] || '' // 时间（如00:00:00）
                return item
              }
            })
            // 获取当前时间
            const now = new Date()
            // 计算最近三个月的起始时间（当前时间减去 3 个月）
            const threeMonthsAgo = new Date(now)
            threeMonthsAgo.setMonth(now.getMonth() - 3)
            // 计算最近六个月的起始时间（当前时间减去 6 个月）
            const sixMonthsAgo = new Date(now)
            sixMonthsAgo.setMonth(now.getMonth() - 6)
            // 筛选最近三个月的数据
            const lastThreeMonthsData = list.filter(item => {
              const createTime = new Date(item.createDate)
              return createTime >= threeMonthsAgo && createTime <= now
            })
            // 筛选最近六个月的数据
            const lastSixMonthsData = list.filter(item => {
              const createTime = new Date(item.createDate)
              return createTime >= sixMonthsAgo && createTime <= now
            })
            console.info('trajectoryJson.dataList', trajectoryJson.dataList)
            console.info('lastThreeMonthsData', lastThreeMonthsData)
            console.info('lastSixMonthsData', lastSixMonthsData)
            this.propData.UserTrajectory = {
              filterVal: 1,
              showList: lastThreeMonthsData,
              lastThreeMonthsData,
              lastSixMonthsData
            }
          }
        }
        return true // 解析成功
      } catch (e) {
        console.error('用户轨迹解析失败:', e)
        return false // 解析失败
      }
    },

    /**
     * 家庭代付数据处理：解析qrybillnew标签，计算replacefee，分离主副号
     */
    handleFamilyPayment(qrybillnewStr) {
      // console.info('家庭代付费：', qrybillnewStr)
      try {
        // 1. 解析代付原始JSON
        const qrybillnew = JSON.parse(qrybillnewStr)

        // 2. 逐层获取bill数据
        let bill = {}
        if (qrybillnew && qrybillnew.bill_list && qrybillnew.bill_list.bill) {
          bill = qrybillnew.bill_list.bill
        }

        // 3. 处理明细列表（确保为数组）
        let feedetail = []
        if (bill && bill.feedetail_list && bill.feedetail_list.feedetail) {
          // 兼容后端返回单对象的情况
          feedetail = Array.isArray(bill.feedetail_list.feedetail)
            ? bill.feedetail_list.feedetail
            : [bill.feedetail_list.feedetail]
        }
        if (!feedetail.length) return
        // 4. 分离主副号（使用feename判断）+ 计算费用合计
        const subAccountList = [] // 副号列表（feename含"代****付费"模式）
        let subTotalFee = 0 // 副号fee合计
        let mainTotalfee = 0 // 主号fee合计

        // 副号匹配正则：匹配"代+11位手机号+付费"格式
        const subAccountPattern = /代.*付费/

        for (let i = 0; i < feedetail.length; i++) {
          const item = feedetail[i]
          // 跳过非对象数据
          if (!item || typeof item !== 'object') continue

          // 拷贝原始字段
          const extendedData = {}
          for (const key in item) {
            if (item.hasOwnProperty(key)) {
              extendedData[key] = item[key]
            }
          }

          // 获取feename并判断是否为副号
          let feename = ''
          if (item.feename !== undefined && item.feename !== null && typeof item.feename === 'string') {
            feename = item.feename
          }

          // 判断逻辑：是否符合"代***********付费"格式
          const isSubAccount = subAccountPattern.test(feename)

          // 处理费用
          let feeNum = 0
          if (item.fee !== undefined && item.fee !== null) {
            feeNum = Number(item.fee)
          }
          const validFee = isNaN(feeNum) ? 0 : feeNum

          if (isSubAccount) {
            // 副号逻辑：提取手机号，累加费用
            const phoneMatch = feename.match(/1\d{10}/)
            extendedData.servnumber = (phoneMatch && phoneMatch[0]) ? phoneMatch[0] : ''
            subTotalFee += validFee
            extendedData.fee = formatNumber(validFee / 1000)
            subAccountList.push(extendedData)
          } else {
            mainTotalfee += validFee
          }
        }

        // 5. 组装最终代付数据
        const resultData = {}
        // 总费用（格式化）
        resultData.totalfee = bill.totalfee !== undefined
          ? formatNumber(bill.totalfee / 1000)
          : formatNumber((mainTotalfee + subTotalFee) / 1000)
        // 副号合计fee（格式化）
        resultData.replacefee = formatNumber(subTotalFee / 1000)
        // 主号合计fee（新增）
        resultData.mainTotalfee = formatNumber(mainTotalfee / 1000)
        // 主号号码
        resultData.servnumber = this.propData.phone || ''
        // 副号列表
        resultData.subAccountList = subAccountList
        // console.info('家庭代付整理后数据：', resultData)
        // 赋值到propData
        this.propData.qrybillnew = resultData

      } catch (e) {
        console.error('家庭代付数据处理失败:', e)
      }
    },

    /**
     * 家庭共享数据处理：解析流量/语音共享信息
     * @param {array} share_detail_info - 共享详情列表
     * @returns {object} 处理后的共享数据
     */
    processData(share_detail_info) {
      // 1. 区分流量（typeid=3）和语音（typeid=7）数据
      let flowInfo = {}
      let voiceInfo = {}
      if (share_detail_info && Array.isArray(share_detail_info)) {
        for (let i = 0; i < share_detail_info.length; i++) {
          const item = share_detail_info[i]
          if (item && item.typeid === '3') {
            flowInfo = item
          } else if (item && item.typeid === '7') {
            voiceInfo = item
          }
        }
      }

      // 2. 流量汇总（单位GB，dataunit=4=字节转GB）
      const flowSummary = { unit: 'GB' }
      if (flowInfo) {
        flowSummary.usedata = this.convertUnit(flowInfo.usedata, flowInfo.dataunit)
        flowSummary.alldata = this.convertUnit(flowInfo.alldata, flowInfo.dataunit)
        flowSummary.leftdata = this.convertUnit(flowInfo.leftdata, flowInfo.dataunit)
      } else {
        flowSummary.usedata = flowSummary.alldata = flowSummary.leftdata = '0'
      }

      // 3. 语音汇总（单位分钟，dataunit=3无需转换）
      const voiceSummary = { unit: '分钟' }
      if (voiceInfo) {
        voiceSummary.usedata = voiceInfo.usedata || '0'
        voiceSummary.alldata = voiceInfo.alldata || '0'
        voiceSummary.leftdata = voiceInfo.leftdata || '0'
      } else {
        voiceSummary.usedata = voiceSummary.alldata = voiceSummary.leftdata = '0'
      }

      // 4. 构建成员数据映射（仅从语音取card_type）
      const flowUseMap = {} // 流量使用量：key=手机号
      const voiceUseMap = {} // 语音使用量：key=手机号
      const cardTypeMap = {} // card_type：key=手机号
      const originalOrder = [] // 成员原始顺序（从语音数据取）

      // 4.1 处理流量成员数据
      if (flowInfo.share_use_list && flowInfo.share_use_list.share_use_info) {
        const flowUseInfo = flowInfo.share_use_list.share_use_info
        if (Array.isArray(flowUseInfo)) {
          for (let i = 0; i < flowUseInfo.length; i++) {
            const item = flowUseInfo[i]
            if (item && item.service_number) {
              flowUseMap[item.service_number] = this.convertUnit(item.usedata, flowInfo.dataunit)
            }
          }
        }
      }

      // 4.2 处理语音成员数据（含card_type和原始顺序）
      if (voiceInfo.share_use_list && voiceInfo.share_use_list.share_use_info) {
        const voiceUseInfo = voiceInfo.share_use_list.share_use_info
        if (Array.isArray(voiceUseInfo)) {
          for (let i = 0; i < voiceUseInfo.length; i++) {
            const item = voiceUseInfo[i]
            if (item && item.service_number) {
              // 记录原始顺序（避免重复）
              let isDuplicate = false
              for (let j = 0; j < originalOrder.length; j++) {
                if (originalOrder[j] === item.service_number) {
                  isDuplicate = true
                  break
                }
              }
              if (!isDuplicate) {
                originalOrder.push(item.service_number)
              }
              // 语音使用量
              voiceUseMap[item.service_number] = item.usedata || '0'
              // card_type（原始值）
              cardTypeMap[item.service_number] = item.card_type || '0'
            }
          }
        }
      }

      // 5. 构建成员列表（按card_type=1优先排序，保持原始顺序）
      const family_list = []
      for (let i = 0; i < originalOrder.length; i++) {
        const phone = originalOrder[i]
        family_list.push({
          service_number: phone,
          flowUsed: flowUseMap[phone] || '0',
          voiceUsed: voiceUseMap[phone] || '0',
          card_type: cardTypeMap[phone] || '0'
        })
      }

      // 排序：card_type=1的排在后面
      family_list.sort(function(a, b) {
        const typeA = a.card_type
        const typeB = b.card_type
        if (typeA === '1' && typeB !== '1') return 1
        if (typeA !== '1' && typeB === '1') return -1
        return 0
      })

      // 返回处理结果
      return { flowSummary, voiceSummary, family_list, servnumber: this.propData.phone || '' }
    },

    /**
     * 单位转换：流量字节转GB，语音保持原单位
     * @param {string/number} value - 待转换的值
     * @param {string} dataunit - 单位标识
     * @returns {string} 转换后的值
     */
    convertUnit(value, dataunit) {
      // 处理空值/非数字
      if (!value || isNaN(Number(value))) return '0'
      const numValue = Number(value)

      // dataunit=4：字节 → GB
      if (dataunit === '4') {
        return formatNumber((numValue / (1024 * 1024)))
      }
      // 其他情况：直接返回原值
      return formatNumber(numValue)
    }
  },
  created() {
  },
  mounted() {
    window['getUserInfoFromAi'] = (result) => {
      let res = result.userInfo
      this.uinfo = JSON.parse(res)
      initTokenAfterBack(this.$http, this.uinfo) //重置token
      this.init()
    }
    let goBackFlag = this.$route.query.gobackFlag
    if (goBackFlag === 'webview') {
      ClientJs.getSysInfo('getUserInfoFromAi')
    } else {
      this.init()
    }

    // this.UserTrajectoryStr = mock.dataList
    // this.propData.tabValue = 3
    // this.parseUserTrajectory()
    // // 根据解析结果添加对话内容（错误提示/正常卡片）
    // this.khChatList.push({
    //   ai: true,
    //   currentComponent: 'khhxCard',
    //   propData: JSON.parse(JSON.stringify({
    //     ...this.propData,
    //     thinkHide: true,
    //     thinkList: this.thinkList
    //   }))
    // })
  }
}
</script>

<style scoped lang='less'>
.chat-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
  font-size: 14px;
}

.messages-wrap {
  flex: 1;
  overflow-y: auto;
  background: linear-gradient(180deg, #DAE9FF 0%, #FFFFFF 48%, #CEE7F5 100%);
  padding: 12px;
  scroll-behavior: smooth;
}

.message-bubble {
  max-width: 99%;
  margin-bottom: 15px;
  position: relative;
  width: fit-content;
}

.user-message {
  margin-left: auto;
  max-width: 90%;
}

.bot-message {
  margin-right: auto;
}

.jiazai {
  display: inline-block;
  font-size: 18px;
  animation: rotate 1.8s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.ai-message {
  width: 95%;
}

.content {
  padding: 8px 13px;
  border-radius: 8px;
  word-break: break-word;
}

.user-message .content {
  line-height: 1.5;
  background: #768EDF;
  color: white;
}

.bot-message .content {
  line-height: 1.5;
  background: white;
  color: #333;
}

.flex {
  display: flex;
}

.align-center {
  align-items: center;
}
</style>
