<!--营业厅知识问答-->
<template>
  <div class='chat-container'>
    <!--顶部tab切换-->
    <ai-top-com :uinfo="uinfo" ref="aiTopCom" />
    <!-- 消息区域 -->
    <div class='messages-wrap' ref='messagesContainer'>
      <img style="margin-bottom: 10px" width="100%" src="../../assets/img/aiGroup/yyt/tip.png" />
      <!-- 对话内容-->
      <div
        v-for='(msg, index) in yytChatList'
        :key='index'
        class='message-bubble'
        :class="{ 'user-message': msg.isUser, 'bot-message': msg.bot,'ai-message':msg.ai }"
      >
        <!--智能选号组件-->
        <!--        <component ref="componentCard" v-if='msg.ai'-->
        <!--                   :is='msg.currentComponent'-->
        <!--                   :propData='msg.propData'-->
        <!--                   :loading.sync="loading"-->
        <!--                   @changeTopicInfo="changeTopicInfo"-->
        <!--                   :index="index"-->
        <!--                   :key='index'></component>-->
        <!--普通打字-->
        <div class='content' v-html="renderedMarkdown(msg.content)"></div>
      </div>
      <!--加载圈-->
      <div class='message-bubble bot-message' v-if='loading && !printedText.length'>
        <div class='content flex align-center'>
          生成中...
          <span class='iconfont jiazai'></span>
        </div>
      </div>
      <!--流式回答-->
      <div class='message-bubble bot-message' v-else-if="printedText.length">
        <div class='content flex'>
          <div v-html="renderedMarkdown(printedText)"></div>
        </div>
      </div>
    </div>
    <!-- 输入区域 -->
    <ai-bottom-com :inputText.sync="inputText" :inputSwitch.sync="inputSwitch" :loading="loading"
                   @sendMessage="sendMessage"></ai-bottom-com>

  </div>
</template>

<script>
import ClientJs from '@/base/clientjs'
import Storage from '@/base/storage'
import { h5UnifiedCapabilitySse } from '../request/index'
import aiTopCom from '../common/aiCom/aiTopCom.vue'
import aiBottomCom from '../common/aiCom/aiBottomCom.vue'
import { renderedMarkdown } from './utils'

// import Vconsole from 'vconsole'
// new Vconsole()

export default {
  mixins: [],
  components: { aiTopCom, aiBottomCom },
  data() {
    return {
      uinfo: Storage.session.get('userInfo') || {},
      yytChatList: Storage.session.get('yytChatList') || [
        // { 'isUser': true, 'content': '中海城南公馆' },
        // {
        //   'bot': true,
        //   'content': '您好！欢迎进入AI每日一练'
        // }
        // { 'isUser': true, 'content': '测试' },
        // { 'ai': true, 'content': '测试222222', 'currentComponent': 'topicCard' }
      ], // 对话列表
      inputText: '',// 输入文字
      inputText2: '',// 暂存输入文字
      inputSwitch: false,
      printedText: '',// 打字
      loading: false // 正在加载
    }
  },
  methods: {
    renderedMarkdown,
    // 输入发送
    async sendMessage(inputText) {
      if (!inputText.trim() || this.inputSwitch || this.loading) return
      // 用户消息
      this.yytChatList.push({
        content: inputText,
        isUser: true
      })
      this.loading = true
      this.scrollToBottom()
      // 保存一下（出错时重新发送使用）
      this.inputText2 = inputText
      const params = {
        crmId: this.uinfo.crmId || this.uinfo.servNumber,
        region: this.uinfo.region,
        keyword: inputText,
        // capabilityType: '2',
        // capabilityCode: '6efcb676feb84d9cae289ec28fa12f86',
        // authToken: '72201b76431142f99804e21d00a2bde2',
        ifCallback: false,
        agentId:'100004'
      }
      let allText = ''
      console.info('流式对话请求参数', params)
      const response = await h5UnifiedCapabilitySse(params, this.uinfo)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      const reader = response.body.getReader()
      const decoder = new TextDecoder('utf-8')
      while (true) {
        const { done, value } = await reader.read()
        let chunk2 = decoder.decode(value, { stream: true })
        // 去掉data: 去掉:keep-alive
        let chunk = chunk2.split('\n').join('').replace(/data:/g, '').replace(/:keep-alive/g, '')
        // 去掉 <br />
        console.info('chunkchunk', chunk)
        if (!chunk.includes('CALLBACK')) {
          this.printedText += chunk
          this.printedText = this.printedText.replace(/<br\s*\/?>/gi, '  \n')
          this.scrollToBottom()
        }
        allText += chunk
        // 请求结束
        if (done) {
          this.loading = false
          console.info('allText', allText)
          // 对话
          this.yytChatList.push({ bot: true, content: this.printedText })
          this.printedText = ''
          this.scrollToBottom()
          break
        }
      }
    },
    // 滚到底部
    scrollToBottom() {
      this.$nextTick(() => {
        if (this.$refs.messagesContainer) {
          const container = this.$refs.messagesContainer
          container.scrollTop = container.scrollHeight + 1000
        }
      })
    },
    // 初始化方法
    init() {
      // 获取顶部推荐菜单列表
      this.$refs.aiTopCom.getRecommendIcon()
    }
  },
  created() {
  },
  mounted() {
    window['getUserInfoFromAi'] = (result) => {
      let res = result.userInfo
      this.uinfo = JSON.parse(res)
      console.info('this.uinfo', this.uinfo)
      initTokenAfterBack(this.$http, this.uinfo) //重置token
      this.init()
    }
    let goBackFlag = this.$route.query.gobackFlag
    if (goBackFlag === 'webview') {
      ClientJs.getSysInfo('getUserInfoFromAi')
    } else {
      this.init()
    }
  }
}
</script>

<style scoped lang='less'>
.chat-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
  font-size: 14px;
}

.messages-wrap {
  flex: 1;
  overflow-y: auto;
  background: linear-gradient(184deg, #D5E7FF 0%, #FFFFFF 100%);
  padding: 0 12px 12px 12px;
  scroll-behavior: smooth;
}

.message-bubble {
  max-width: 99%;
  margin-bottom: 15px;
  position: relative;
  width: fit-content;
}

.user-message {
  margin-left: auto;
  max-width: 90%;
}

.bot-message {
  margin-right: auto;
}

.jiazai {
  font-size: 18px;
  animation: rotate 1.8s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.ai-message {
  width: 90%;
}

.content {
  padding: 8px 13px;
  border-radius: 8px;
  word-break: break-word;
}

.user-message .content {
  line-height: 1.5;
  background: #768EDF;
  color: white;
}

.bot-message .content {
  line-height: 1.5;
  background: white;
  color: #333;
}

.flex {
  display: flex;
}

.align-center {
  align-items: center;
}
</style>
