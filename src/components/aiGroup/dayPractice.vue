<!--每日一练-->
<template>
  <div class='chat-container'>
    <!--顶部tab切换-->
    <ai-top-com :uinfo="uinfo" ref="aiTopCom" />
    <div class="relative">
      <img width="100%" src="../../assets/img/aiGroup/mryl/tip.png" />
      <div class="absolute">
        <mt-button type="primary" size="small" @click="sendMessage('',1)">
          <i class="iconfont xiangqing1"></i>
          出题
        </mt-button>
      </div>
    </div>
    <!-- 消息区域 -->
    <div class='messages-wrap' ref='messagesContainer'>
      <!-- 对话内容-->
      <div
        v-for='(msg, index) in lxChatList'
        :key='index'
        class='message-bubble'
        :class="{ 'user-message': msg.isUser, 'bot-message': msg.bot,'ai-message':msg.ai }"
      >
        <!--智能选号组件-->
        <component ref="componentCard" v-if='msg.ai'
                   :is='msg.currentComponent'
                   :propData='msg.propData'
                   :loading.sync="loading"
                   @changeTopicInfo="changeTopicInfo"
                   :index="index"
                   :key='index'></component>
        <!--普通打字-->
        <div class='content' v-else v-html="renderedMarkdown(msg.content)"></div>
      </div>
      <!--加载圈-->
      <div class='message-bubble bot-message' v-if='loading && !printedText.length'>
        <div class='content flex align-center'>
          生成中...
          <span class='iconfont jiazai'></span>
        </div>
      </div>
      <!--流式回答-->
      <div class='message-bubble bot-message' v-else-if="printedText.length">
        <div class='content flex'>
          <div v-html="renderedMarkdown(printedText)"></div>
        </div>
      </div>
    </div>
    <!-- 输入区域 -->
    <ai-bottom-com :inputText.sync="inputText" :inputSwitch.sync="inputSwitch" :loading="loading"
                   @sendMessage="sendMessage"></ai-bottom-com>

  </div>
</template>

<script>
import ClientJs from '@/base/clientjs'
import Storage from '@/base/storage'
import { dateFormat } from '@/base/utils'
import { h5UnifiedCapabilitySse } from '../request/index'
import aiTopCom from '../common/aiCom/aiTopCom.vue'
import aiBottomCom from '../common/aiCom/aiBottomCom.vue'
import topicCard from './components/topicCard.vue'
import { renderedMarkdown } from './utils'
// import Vconsole from 'vconsole'
// new Vconsole()

export default {
  mixins: [],
  components: { aiTopCom, aiBottomCom, topicCard },
  data() {
    return {
      uinfo: Storage.session.get('userInfo') || {},
      lxChatList: Storage.session.get('lxChatList') || [
        // { 'isUser': true, 'content': '中海城南公馆' },
        // {
        //   'bot': true,
        //   'content': '您好！欢迎进入AI每日一练'
        // }
        // { 'isUser': true, 'content': '测试' },
        // { 'ai': true, 'content': '测试222222', 'currentComponent': 'topicCard' }
      ], // 对话列表
      inputText: '',// 输入文字
      inputText2: '',// 暂存输入文字
      inputSwitch: false,
      printedText: '',// 打字
      loading: false // 正在加载
    }
  },
  methods: {
    renderedMarkdown,
    // 输入发送
    async sendMessage(inputText, type = 2) {
      if (this.inputSwitch || this.loading) return
      if (type === 2) {
        if (!inputText.trim()) return
        // 用户消息
        this.lxChatList.push({
          content: inputText,
          isUser: true
        })
      }
      this.loading = true
      this.scrollToBottom()
      // 保存一下（出错时重新发送使用）
      this.inputText2 = inputText
      const params = {
        crmId: this.uinfo.crmId || this.uinfo.servNumber,
        region: this.uinfo.region,
        keyword: type === 2 ? inputText : dateFormat(new Date(), ('yyyy-MM-dd hh:mm:ss')),
        // capabilityType: type,
        // capabilityCode: type === 2 ? '606ca28d3b8a4938a9748e6698d6eea1' : '0971f0a1ad4c46708392ffafae14be4a',
        // authToken: '72201b76431142f99804e21d00a2bde2',
        ifCallback: false,
        agentId: type === 2 ? '100006' : '100005',
      }
      let allText = ''
      console.info('流式对话请求参数', params)
      const response = await h5UnifiedCapabilitySse(params, this.uinfo)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      const reader = response.body.getReader()
      const decoder = new TextDecoder('utf-8')
      while (true) {
        const { done, value } = await reader.read()
        let chunk2 = decoder.decode(value, { stream: true })
        // 去掉data: 去掉:keep-alive
        let chunk = chunk2.split('\n').join('').replace(/data:/g, '').replace(/:keep-alive/g, '').replace(/ /g, '')
        console.info('chunkchunk', chunk)
        if (type === 2) {
          if (!chunk.includes('CALLBACK')) {
            this.printedText += chunk
            this.printedText = this.printedText.replace(/<br\s*\/?>/gi, '  \n')
            this.scrollToBottom()
          }
        }
        allText += chunk
        // 请求结束
        if (done) {
          this.loading = false
          console.info('allText', allText)
          // 出题
          if (type === 1) {
            let topicList
            try {
              allText = allText.replace(/<br \/>/g, '')
              // 尝试直接解析JSON
              topicList = JSON.parse(allText)
            } catch (e) {
              // 尝试从错误信息中提取JSON
              const jsonString = allText.substring(allText.indexOf('['), allText.lastIndexOf(']') + 1)
              try {
                topicList = JSON.parse(jsonString)
              } catch (e) {
                this.lxChatList.push({ bot: true, content: '获取题目出错，请稍后重试' })
                return
              }
            }
            await this.getTopicInfo(topicList)
          } else {
            // 对话
            this.lxChatList.push({ bot: true, content: this.printedText })
            this.printedText = ''
            this.scrollToBottom()
          }
          break
        }
      }
    },
    // 处理题目
    async getTopicInfo(topicList) {
      if (topicList && topicList.length) {
        topicList.forEach(item => {
          // 题目存在
          if (item.question) {
            // 处理一下判断题目
            if (item.type.includes('判断')) {
              item.options = {
                A: '正确',
                B: '错误'
              }
              item.answer = item.answer === '正确' ? 'A' : 'B'
            }
            console.info('题目项：', item)
            this.lxChatList.push({
              'ai': true,
              'currentComponent': 'topicCard',
              'propData': item
            })
          }
        })
      }
    },
    changeTopicInfo(index, data) {
      this.lxChatList[index].propData = { ...this.lxChatList[index].propData, ...data }
    },
    // 滚到底部
    scrollToBottom() {
      this.$nextTick(() => {
        if (this.$refs.messagesContainer) {
          const container = this.$refs.messagesContainer
          container.scrollTop = container.scrollHeight + 1000
        }
      })
    },
    // 初始化方法
    init() {
      // 获取顶部推荐菜单列表
      this.$refs.aiTopCom.getRecommendIcon()
    }
  },
  created() {
  },
  mounted() {
    window['getUserInfoFromAi'] = (result) => {
      let res = result.userInfo
      this.uinfo = JSON.parse(res)
      initTokenAfterBack(this.$http, this.uinfo) //重置token
      this.init()
    }
    let goBackFlag = this.$route.query.gobackFlag
    if (goBackFlag === 'webview') {
      ClientJs.getSysInfo('getUserInfoFromAi')
    } else {
      this.init()
    }
  }
}
</script>

<style scoped lang='less'>
.chat-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
  font-size: 14px;
}

.messages-wrap {
  flex: 1;
  overflow-y: auto;
  background: linear-gradient(184deg, #D5E7FF 0%, #FFFFFF 100%);
  padding: 0 12px 12px 12px;
  scroll-behavior: smooth;
}

.message-bubble {
  max-width: 99%;
  margin-bottom: 15px;
  position: relative;
  width: fit-content;
}

.user-message {
  margin-left: auto;
  max-width: 90%;
}

.bot-message {
  margin-right: auto;
}

.jiazai {
  font-size: 18px;
  animation: rotate 1.8s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.ai-message {
  width: 90%;
}

.content {
  padding: 8px 13px;
  border-radius: 8px;
  word-break: break-word;
}

.user-message .content {
  line-height: 1.5;
  background: #768EDF;
  color: white;
}

.bot-message .content {
  line-height: 1.5;
  background: white;
  color: #333;
}

.flex {
  display: flex;
}

.align-center {
  align-items: center;
}

.relative {
  position: relative;
  padding: 0 12px 12px 12px;
  background: #D5E7FF;
}

.absolute {
  position: absolute;
  bottom: 24px;
  right: 20px;
}

/deep/ .mint-button--primary {
  background: #0079FD;
  border-radius: 12px;
  height: 26px;
  line-height: 26px;
  padding: 0 16px;
}

.xiangqing1 {
  font-size: 14px;
}
</style>
