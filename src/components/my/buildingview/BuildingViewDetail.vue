<template>
  <div class="wrap-bv">
    <div class="head wrapper-medias">
      <div class="gl-title">
        <!--后退按钮-->
        <span class="iconfont zuojiantou gl-back"  @click="goPrev"></span>
        <!--标题-->
        <span class="gl-title-txt">楼宇视图</span>
        <!--右侧文字按钮-->
        <!--                <span class="txt-btn" @click="btnCk" v-show="shouquan && interfaceLoadComplete">授权</span>-->
        <span class="iconfont diandiandian txt-btn" @click.stop="doMoreInput" v-show="shouquan || endCheck" ></span>
        <!--右侧文字按钮-->
        <span class="daka-btn" @click="goDaka">打卡日志</span>
      </div>
    </div>

    <div class="model" v-show="moreInputCheck">
      <div class="plan-model" v-show="shouquan || endCheck">
        <span class="sanjian" ></span>
        <span class="more-input" @click="btnCk" v-show="shouquan && interfaceLoadComplete">授权</span>
        <div class="bar" v-show="shouquan && endCheck"></div>
        <span class="more-input" @click="endNow" v-show="endCheck">立即结束</span>
      </div>
    </div>
    <div class="top-frame">
      <div class="info-area">
        <div class="building-name">
          {{buildingDetail.objectName}}
          <span class="go_daohang" @click="handleGoNavigation">导航前往<i class="iconfont you"></i></span>
        </div>
        <span class='building-attribute' @click='goBuildingProperty'>楼宇属性</span>
        <div class="building-detailed" @click="goBuildingDetail()"><span class="iconfont shituxiangqing icon-signin"></span>详细</div>
      </div>
    </div>
    <div class="sign-photo-area">
      <div class="sign-area">
        <div class="text-font">营销签到/签出</div>
        <div class="sign-status" v-if='signCount == 0'>
          <span v-show="!isSign">未签到</span><span v-show="isSign" style="color: #26B34E;">已签到</span>&nbsp;/&nbsp;
          <span v-show="!signOutFlag">未签出</span><span v-show="signOutFlag"  style="color: #26B34E;">已签出</span>
        </div>
        <div class="sign-status" v-if='signCount > 0'>
            <span style="color: #26B34E;">已签到</span>&nbsp;/&nbsp;
            <span style="color: #26B34E;">已签出</span>
        </div>
        <div v-if='signCount == 0' class="sign-bottom" :class="{active:isSign,gry:signOutFlag}" @click="signInOut">
          <span class="bottom-txt" :class="{active:isSign,gry:signOutFlag}">{{getSignTxt}}</span>
        </div>
        <div v-if='signCount > 0' class="sign-bottom gry" @click="signInOut">
           <span class="bottom-txt gry">完成</span>
        </div>
      </div>
      <div class="photo-area">
        <div class="text-font">营销拍照</div>
        <div class="explanation-text">留下实景记录</div>
        <div class="photo-icon" @click="cameraCk"><span class="iconfont xiangji icon-xiangji"></span></div>
      </div>
      <!-- 签出弹框组件-->
      <signOut @submitCb="doSignOut" v-model="signOutVisible"></signOut>
    </div>
    <div class="bottom-area" style="padding-top: 10px">
      <div class="menu-bottom" @click="goMustDo">
        <span class="iconfont yingxiao3 icon-yingxiao"></span>
        <div style="margin-left: 40px">
          <div class="text-font">营销必做</div>
          <div class="explanation-text">必须要做的事项</div>
        </div>
      </div>
      <div class="menu-bottom" @click="busiReportShow=true">
          <img src="../../../assets/img/shangbao.png"/>
        <div style="margin-left: 40px">
          <div class="text-font">商机上报</div>
          <div class="explanation-text">楼宇商机上报</div>
        </div>
      </div>
    </div>
    <div class="bottom-area">
        <div class="menu-bottom" @click="busiManagerShow=true">
            <img src="../../../assets/img/busi_manager.png"/>
            <div style="margin-left: 40px">
                <div class="text-font">商机管理</div>
                <div class="explanation-text">楼宇商机管理</div>
            </div>
        </div>
      <div class="menu-bottom"  @click="goPerBusiRec">
        <span class="iconfont gerenwutuijian icon-gerenwutuijian"></span>
        <div style="margin-left: 40px">
          <div class="text-font">个人业务推荐</div>
          <div class="explanation-text">优质套餐上线</div>
        </div>
      </div>
    </div>
    <div class="bottom-area">
        <div class="menu-bottom" @click="openMarketingNote">
            <span class="iconfont suishouji icon-suishouji"></span>
            <div style="margin-left: 40px">
                <div class="text-font">营销随手记</div>
                <div class="explanation-text">快捷记录</div>
            </div>
        </div>
      <div class="menu-bottom" @click="goDaosanjiao">
        <span class="iconfont daosanjiaohui icon-daosanjiao"></span>
        <div style="margin-left: 40px">
          <div class="text-font">倒三角</div>
          <div class="explanation-text">工单处理</div>
        </div>
      </div>
    </div>
      <div class="bottom-area">
          <div class="menu-bottom" @click="goShopDiscounts">
              <img src="static/img/shopDiscounts-square.png" alt="" class="shop-discounts">
              <div style="margin-left: 40px">
                  <div class="text-font">商铺优惠</div>
                  <div class="explanation-text">附近优惠信息查看</div>
              </div>
          </div>
      </div>
    <!--关联集团模块-->
    <div class='association-group' v-show='!showCameraFlg'>
      <div class='association-group-head'>
        <div class='association-group-info'>
                   <span>
                    <span class='group-title-icons'></span>
                    <span class='group-title-text'>关联集团</span>
                    <span class='had-record'></span>
                    <span class='record-text'>已建档</span>
                    <span class='not-record'></span>
                    <span class='record-text'>未建档</span>
                   </span>
          <span class='add-association-group' @click='addNewGroup'><span class='add-association-group-icon'>+</span>新增</span>
        </div>
        <!--搜索-->
        <div class="search-wrapper">
          <div class="ipt-wrapper">
            <span class="iconfont sousuo1"></span>
            <input class="seach-ipt" v-model="searchName" maxlength="11" placeholder="请输入集团或商户名称"/>
            <span class="query-btn" @click="search()">搜索</span>
            <span v-show='searchName || !showFloorList' class="iconfont shan clear-btn" @click="clearSearch"></span>
          </div>
        </div>
        <!--筛选-->
        <div class='filter-criteria'>
          <div class="filter-criteria-left">
            <div @click="isNlDropdown('isJd')" class='filter-text' :class="{'filter-state':isJd!='9'}">
              {{isJd=='9'?'建档状态':isJd=='1'?'已建档':'未建档'}}
              <span class='iconfont xiala filter-icon-state'></span>
            </div>
            <div @click="isNlDropdown('isExcept')" class='filter-text padding-left-sm' :class="{'filter-state':isExcept}">
              {{isExcept?isExcept=='1'?'本人看管':'非本人看管':'看管状态'}}
              <span class='iconfont xiala filter-icon-state'></span>
            </div>
            <div @click="isNlDropdown('isTrue')" class='filter-text padding-left-sm' :class="{'filter-state':isTrue}">
              {{isTrue?isTrue=='1'?'已验真':'未验真':'验真状态'}}
              <span class='iconfont xiala filter-icon-state'></span>
            </div>
          </div>
          <div class='filter-text text-right' @click='morePopupVisible = true' :class="{'filter-state':(businessAddBeg && businessAddEnd) || (emuBeg && emuEnd)}">
            高级筛选<span class='iconfont xiala filter-icon-state'></span>
          </div>
        </div>
        <building-popup @filterChange="filterChange" v-model="morePopupVisible"></building-popup>
      </div>
      <!--列表数据-->
      <div class='association-group-body' v-show='floorListInfo.length>0'>
        <div class='floor-list' v-if='showFloorList'>
          <ul>
            <li class='floor-infos' :class='{"floor-infos-focus":floorIndex==index}' v-for='(item,index) in floorListInfo' :key='index' @click='reqFloorListWay(item,index)'>{{item.floor}}楼({{item.cntCust}})</li>
          </ul>
        </div>
        <div class='association-list' :class='{"search-list": !showFloorList}'>
          <ul v-show='businessFloorListInfo.length>0' style="max-height: calc(100vh - 186px);overflow: auto;">
            <li class='association-infos' v-for='(item,index) in businessFloorListInfo' :key='index'>
              <div class='association-title'>
                                <span class='association-title-left' @click='toGroupDetailNew(item.customerId,item.isJd,item.isDuty)'>
                                    <span :class='{"had-record":item.isJd==1,"not-record":item.isJd==0}'></span>
                                    <span class='association-text'>{{item.customerName}}</span>
                                    <span class='iconfont youjiantou2 association-icon' v-show='item.isJd==1&&item.isDuty==1'></span>
                                </span>
                <span class='iconfont diandiandian txt-more' v-show='item.isJd==1' @click='showMoreOperate(item)'></span>
                <span class='iconfont diandiandian txt-more' v-show='item.isJd==0' @click='showMoreOperate(item)'></span>
              </div>
              <div>
                <span class="group-sign" v-show="!showFloorList && item.floor">{{item.floor}}楼</span>
                <span class="group-sign" v-show="item.busiType">{{item.busiType | busiTypeFilter}}</span>
                <span class="group-sign" v-show="item.inDustryName">{{item.inDustryName}}</span>
                <span class="group-sign" v-show="item.isTrue == '1'">已验真</span>
                <span class="group-sign" v-show="item.isExcept == '1'">本人看管</span>
                <span class="group-sign group-sign1" v-show="item.isOwe == '1'">已欠费</span>
              </div>

              <div class='household-number-title' v-show='item.room'>户室编号：<span class='household-number'>{{item.room}}</span></div>
              <div class='group-number-title'>
                  <span class='household-number'>集团编码：{{item.customerId}}</span>
              </div>
            </li>
          </ul>
          <img :src="getImg('super_empty')" class='association-list-img' v-show='businessFloorListInfo.length==0'>
          <p v-show='businessFloorListInfo.length==0' class='association-list-img-text'>暂无数据</p>
        </div>
      </div>
      <div class='association-group-img' v-show='floorListInfo.length==0'>
        <img :src="getImg('super_empty')"  class='association-list-img'>
        <p class='association-group-img-text'>暂无数据</p>
      </div>
    </div>
    <div class="camera-wrap" v-show="showCameraFlg">
      <CustomScenePhoto :comeFrom="cameraFrom"
                        :axiosParam="axiosParam"
                        :showCameraFlg="showCameraFlg"
                        :alPhotoCount="alPhotoCount"
                        :photoCount="photoCount"
                        @emClose="getClose"
                        @emSign="signCameraCb"
                        @emActivity="activityCameraCb"></CustomScenePhoto>
    </div>
    <div class="message-show">
      <div class="message-box pad-msg" v-show="isShowMessageBox">
        <div class="message-content">
          <h3 class="title">温馨提示</h3>
          <div class="content" :class="{center:locationText=='定位中'}">
            {{locationText}}<span v-loading="locationText =='定位中'" :circle-size="16" class="connecting-btn-load"></span>
          </div>
        </div>
        <!--定位成功 -->
        <div class="btn-group" v-show="locationFinish && locationSuccess">
          <button class="btn-default btn-cancel"  @click.stop="messageCancel">重新定位</button>
          <button class="btn-primary btn-confirm needsclick" @click.stop="messageConfirm">{{getSignTxt}}</button>
        </div>
        <!--定位失败-->
        <div class="btn-group" v-show="locationFinish && !locationSuccess">
          <button class="btn-default btn-cancel"  @click.stop="messageCancel">重新定位</button>
          <button class="btn-primary btn-confirm needsclick" @click.stop="isShowMessageBox = false">取消</button>
        </div>
      </div>
      <div class="mask wrapper-medias" @click="cancelMask" v-show="isShowMessageBox"></div>
    </div>
    <div class="message-show">
      <div class="message-box pad-msg" v-show="isOutOfDistance">
        <div class="message-content">
          <h3 class="title">温馨提示</h3>
          <div class="content" style="text-align:center;">
            距离{{isPark==1?'园区':'楼宇'}}超过{{signDistance}}米，请前往{{isPark==1?'园区':'楼宇'}}或者确认楼宇属性页中{{isPark==1?'园区':'楼宇'}}地址是否正确。
          </div>
        </div>
        <div class="btn-group" v-show="!fromSubWgt">
          <button class="btn-default btn-cancel-out" :class="{active:!allowAddressChange || !isResponsible || outdisSign}" v-show="(allowAddressChange && isResponsible) || !outdisSign" @click.stop="cancelBox">取消</button>
          <button class="btn-default btn-cancel-out" style="width: 100%;" @click.stop="cancelBox" v-show="(!allowAddressChange&&outdisSign) || (!isResponsible&&outdisSign)">取消</button>
          <button class="btn-default btn-update"  @click.stop="signInOutConfirm('update')" v-show="isResponsible&&allowAddressChange&&(addressChangeTimes>alAddressChangeTimes)" :class="{maxactive:outdisSign}">确定并更新地址</button>
          <button class="btn-default btn-update" v-show="isResponsible&&allowAddressChange&&!(addressChangeTimes>alAddressChangeTimes)" :class="{maxactive:outdisSign}" @click.stop="groupPointSync()">确定并更新地址</button>
          <button class="btn-primary btn-confirm-out needsclick" :class="{active:!allowAddressChange || !isResponsible}"  @click.stop="signInOutConfirm('confirm')"  v-show="!outdisSign">确定</button>
        </div>
        <div class="btn-group1" v-show="fromSubWgt">
          <button class="btn-primary" @click.stop="isOutOfDistance = false">确定</button>
        </div>
      </div>
      <div class="mask wrapper-medias" @click="cancelBox" v-show="isOutOfDistance"></div>
    </div>
    <!--随手记-->
    <div v-show="showMarketingNote" class="marketing-note-back wrapper-medias" @click.stop="cancleMarketingNote"></div>
    <div class="marketing-note" v-show="showMarketingNote">
      <div class="marketing-note-wittle">营销随手记</div>

      <textarea v-model="MarketingNoteText" placeholder="请出入想要记录的信息" class="marketing-note-textarea"></textarea>

      <div class="bottom">
        <button class="redeploy" @click="cancleMarketingNote">取消</button>
        <button class="submit" @click="submitMarketingNote()">提交</button>
      </div>
    </div>


    <div v-show="showEndDetail" class="end-detail" @click.stop="cancleEndBox"></div>
    <div class="end-box" v-show="showEndDetail">
      <div class="end-box-tittle">立即结束界面</div>
      <div class="end-box-top">
        请选择结束原因：
      </div>
      <div class="content">
        <div class="end-box-choose" @click="endChoose(1)">
          <div class="left">
            <span class="noChoose" v-show="!(endCheckChoose == 1)"></span>
            <span class="iconfont wancheng" v-show="endCheckChoose == 1"></span>
          </div>
          <div class="right">任务已授权，需结束。</div>
        </div>
        <div class="end-box-choose" @click="endChoose(2)">
          <div class="left">
            <span class="noChoose" v-show="!(endCheckChoose == 2)"></span>
            <span class="iconfont wancheng" v-show="(endCheckChoose == 2)"></span>
          </div>
          <div class="right">任务有误，需结束。</div>
        </div>
        <div class="end-box-choose" @click="endChoose(3)">
          <div class="left">
            <span class="noChoose" v-show="!(endCheckChoose == 3)"></span>
            <span class="iconfont wancheng" v-show="(endCheckChoose == 3)"></span>
          </div>
          <div class="right">其他原因</div>
        </div>

        <div class="end-box-content" v-show="endCheckChoose == 3">
          <textarea v-model="endNoteText" placeholder="请输入结束原因" class="end-note-textarea"></textarea>
        </div>

        <div class="end-time" v-show="endCheckChoose == 3">
          <div>
            <span class = "end-time-left">请选择下次营销时间</span>
          </div>
          <div @click="timeChoose()" >
            <span class = "end-time-right"  style="color:#ccc">{{markTime | changeEndTime }}</span>
            <span class="iconfont jiantou-copy-copy"></span>
          </div>
        </div>

      </div>

      <div class="end-bottom">
        <button class="end-redeploy" @click="cancleEndBox">取消</button>
        <button class="end-submit" @click="submitEndNote()">提交</button>
      </div>
    </div>
    <SecurityCheck v-if="securityCheckShow"
                   @emClose="closeSecurityCheck"
                   :planId="planId"
                   :stepNum="this.stepNum"
    ></SecurityCheck>
    <div class="chooseDialog" v-if="showDialog">
      <div class="title">提示信息</div>
      <div class="info">水牌信息识别中，请耐心等待</div>
    </div>
    <div class="hidebox" v-if="showDialog"></div>
    <!--水牌识别错误提醒弹框-->
    <error-pic v-model="errorPicVisible"></error-pic>

    <!-- 我的个人码 -->
    <div class="camera-wrap" v-show="qrCodeVisible">
      <clock-in-qr-code v-if="qrCodeVisible" :qrCodeVisible="qrCodeVisible" :signDistance="signDistance" :qrCodeForm="qrCodeForm" :workId="workId" @closeQrCode="closeQrCode" @qrCodeSignOut="qrCodeSignOut" />
    </div>

    <!-- 个人码浮标 -->
    <div class="qr_code_layer" v-show="isShowQrCode" @click="goQrCode">
        <i class="iconfont erweima"></i>
    </div>
      <!-- 商机上报 -->
      <div class='busi-mask' v-show='busiReportShow'>
          <div class="overlay-wrapper">
              <div class="block">
                  <div class="header">
                      <span>选择上报类型</span>
                      <span class='iconfont guanbi close-btn' @click='busiReportShow=false'></span>
                  </div>
                  <div class="busi-items">
                      <div class="busi-item" @click="goBuildingbusi">集团商机上报</div>
                      <div class="busi-item" @click="goBusiPage(0)">融合服务</div>
                  </div>
                  <div class="busi-items">
                      <div class="busi-item" @click="goBusiPage(1)">团单商机</div>
                      <div class="busi-item" @click="goBusiPage(2)">业务商机</div>
                  </div>
              </div>
          </div>
      </div>
      <!-- 商机管理 -->
      <div class='busi-mask' v-show='busiManagerShow'>
          <div class="overlay-wrapper">
              <div class="block">
                  <div class="header">
                      <span>选择商机类型</span>
                      <span class='iconfont guanbi close-btn' @click='busiManagerShow=false'></span>
                  </div>
                  <div class="busi-items">
                      <div class="busi-item" @click="goGroupBusiManagePage">集团商机管理</div>
                      <div class="busi-item" @click='goBusiPage(3)'>个人商机管理</div>
                  </div>
              </div>
          </div>
      </div>
  </div>
</template>

<script>
import Header from "components/common/Header.vue";
import Storage from "@/base/storage.js";
import ClientJs from '@/base/clientjs';
import {dateFormat,chgStrToDate} from '@/base/utils';
import CustomScenePhoto from 'components/my/customScene/CustomScenePhoto.vue'
import BuildingViewMustDo from 'components/my/buildingview/BuildingViewMustDo.vue'
import NlDatePicker from "components/common/NlDatePick/datePicker.js";
import {getRealUrl} from "@/base/utils";
import {tokenFromAldMixin} from '@/base/mixin'
import SecurityCheck from 'components/my/villageLoading/SecurityCheck.vue'
import NlDropdown from "components/common/NlDropdown/dropdown.js";
import {LONG_CITY_LIST} from '@/base/config'
import buildingPopup from './components/buildingPopup.vue'
import signOut from '../components/signOut.vue'
import { h5GridSignInSubmit, h5GridSignOutSubmit, h5GridTodayUnSignOutQuery } from '../../request'
import errorPic from './components/errorPic.vue'
import clockInQrCode from 'components/common/clockInQrCode/clockInQrCode.vue'
import {menuLogRecord} from '@/base/request/commonReq.js'
import { menuChain } from '@/base/mixins/menuChainMixin'
import { skipCommom } from '../../desk/WorkStageNew/commonBusiCk.js'

export default {
  mixins:[tokenFromAldMixin, menuChain, skipCommom],
  components: { buildingPopup,signOut,errorPic, Header,BuildingViewMustDo,CustomScenePhoto,SecurityCheck,clockInQrCode},
  data(){
    return{
      showDialog: false,
      workId:'',
      objectId:'',//场景编号
      buildingDetail:'',//楼宇详情
      buildingDetail2:{},//楼宇详情
      latitude:'',
      longitude:'',
      hasLat:false,//是否有经纬度
      isSign:false,//签到标识
      signOutFlag:false,//签出标示
      isCamera:false,//是否已拍照
      userInfo:{},
      clientType:'',//
      buildingAddress:'',
      signDistance:'',//签到距离控制开关 0为不控制
      targetUser:'',//获取目标用户距离开关 0为不控制
      signOutLimitTime:'',//签出时间限制开关 0为不控制
      photoCount:0,//限制拍照数量 0为不控制
      outdisSign:false,//是否超出范围不允许签到
      signOperation:false,//先签到再进行其他操作开关
      interfaceLoadComplete:false,//接口加载完成
      workComplete:false,
      isShowMessageBox:false,							//弹出框控制
      locationParam:{},								//签到参数
      signOutLocationParam:{},						//签出参数
      getDistanceLocationParam:{},
      distance:'',//距离
      isOutOfDistance:false,//
      signInTime:'',//签到时间
      alPhotoCount:0,//已拍照片数量
      showCameraFlg:false,//
      cameraFrom: 'sign',//拍照触发来源 sign为签到处
      axiosParam: {},//拍照需要传的参数
      currActivityIsPhoto:false,//必做是否拍照
      currActivityIsComplete:false,//必做是否完成
      sixMustDo: {
        smList: [],                   //六必做查询数据
        currListIndex: null,          //哪条点击了
        currItem: {},                 //点击那条数据
        submitBtnFlag: false,            //六必做提交按钮是否高亮能点击
      },
      sixParamList: [],                        //六必做提交参数列表
      mustDoLoadComplete:false,//必做项是否加载完成
      showMustDoFlag:false,
      showMarketingNote:false,//展示随手记
      MarketingNoteText:'',//随手记文本
      shouquan:true,
      bindTimes:'',
      unfinishedWorkId:'',
      allowAddressChange:false,
      addressChangeTimes:'1',
      alAddressChangeTimes:'1',
      isResponsible:false,
      latBefore:'',
      lonBefore:'',
      locationBefore:'',
      pointLocationInfo:{},
      showEndDetail: false,    //立即执行  弹框
      endNoteText: '', //立即结束内容
      markTime: '',  //下次营销时间
      moreInputCheck: false,   //展开商机
      endCheck: false,  //是否展示立即结束
      endCheckChoose: '', //结束原因选择
      securityCheckShow:false,//展示安全检查
      planId:"",
      SafeCheckSwitch: false, //安全检查开关
      showFloorList: true, // 是否展示楼层列表
      channelId:"60",//渠道编码楼宇场景渠道：60 ，物理楼宇渠道：92
      searchName:'',//搜索名称
      detailInfo:[],//楼宇详情信息
      detailInfoList:[],//楼宇详情信息列表
      buildingInfoMoreFlag:false,
      buildingInfoMore:true,//展开详情标识
      addNewGroupList:[{id:0,label: '拍摄水牌'},{id:1,label: '关联已有集团'},{id:2,label: '单个新增'}],//新增选项列表
      gridId:'',//网格编码
      isJd: 9, //建档状态 [{id:9,label:'全部'},{id:1,label: '已建档'},{id:0,label:'未建档'}]
      floorListInfo:[],//楼层数据
      businessFloorListInfo:[],//楼层企业数据
      floorIndex:0,
      currentFloor:'',//当前楼层
      businessAddBeg:'',//企业新增开始时间
      businessAddEnd:'',//企业新增结束时间
      emuBeg:'',//异网到期开始时间
      emuEnd:'',//异网到期结束时间
      cusGroupType: '', // 客群标签
      isExcept: null,// 是否剔除他人看管 1:是0:否
      isTrue: null,//是否验真 1:是 0:否
      isOwe: '99', // 是否欠费 1：是 0：否 99: 全部
      hadRecordGroupList:[{id:1,label: '信息维护'},{id:4,label: '成员维护'},{id:2,label: '集团业务推荐'},{id:3,label: '商机上报'},{id:6,label: '产品推荐'}],//已建档选项列表
      notRecordGroupList:[{id:1,label: '信息维护'},{id:4,label: '成员维护'},{id:2,label: '集团建档'},{id:6,label: '产品推荐'}],//未建档选项列表
      topFlag:false,
      locationFinish: true, // 定位是否结束
      locationSuccess: false, // 定位是否成功
      fromSubWgt: false, // 是否从沙盘跳转
      isPark: null, // 是否园区 1：园区 0：楼宇
      sceneTypeMap: [{id: "A", value: "农、林、牧、渔业"}, {id: "B", value: "采矿业"}, {id: "C", value: "制造业"},
        {id: "D", value: "电力、燃气及水的生产和供应业"}, {id: "E", value: "建筑业"}, {id: "F", value: "交通运输、仓储和邮政业"},
        {id: "G", value: "信息传输、计算机服务和软件业"}, {id: "H", value: "批发和零售业"}, {id: "I", value: "住宿和餐饮业"},
        {id: "J", value: "金融业"}, {id: "J", value: "金融业"}, {id: "J", value: "金融业"},
        {id: "J", value: "金融业"}, {id: "K", value: "房地产业"}, {id: "L", value: "租赁和商务服务业"},
        {id: "M", value: "科学研究、技术服务和地质勘查业"}, {id: "N", value: "水利、环境和公共设施管理业"}, {id: "O", value: "居民服务和其他服务业"},
        {id: "N", value: "水利、环境和公共设施管理业"}, {id: "O", value: "居民服务和其他服务业"}, {id: "P", value: "教育"},
        {id: "Q", value: "卫生、社会保障和社会福利业"}, {id: "R", value: "文化、体育和娱乐业"}, {id: "S", value: "公共管理、社会保障和社会组织"},
        {id: "T", value: "国际组织"}, {id: "U", value: "国防"}],
      morePopupVisible : false,// 更多筛选弹层
      errorPicVisible : false,// 水牌错误提示弹框页面
      signOutVisible:false,//签出弹窗
      signInId:null,// 签到id，存在=>签出，不存在=>签到（不用做页面判断显示，签出要使用该id）
      signOutStr:"",// 签出字符（记录一下）
      signCount: 0, // 今日签到次数
      mustDateFlag:false,//根据任务开始时间限制签到开关
      mustDate:"",//任务开始时间

      qrCodeVisible: false, // 我的二维码页
      isShowQrCode: false, // 是否展示二维码浮标
      qrCodeForm: {}, // 个人码明细
      busiReportShow: false, // 商机上报是否展示
      busiManagerShow: false, // 商机管理是否展示
      busiReportRoutes: [
        'PotentialuserMaintenance?editType=add&showbtn=1', // 融合服务(商机信息录入)
        'groupOrdersBusiness?editType=add', // 团单商机单录入
        'BusinessOpportMaintenance?editType=add&showbtn=1', // 业务商机信息录入
        'PersonalBusinessOpportunity?gobackFlag=ald' // 个人商机管理(Ai商机管理)
      ]
    }
  },

  mounted() {
    window['shuiPaiOpenCameraOne'] = (result) => {
      if (result.fileImage) {
        this.getShuiPaiInfo(result.fileImage)
      }
    };
    window['getLocationInfoCb'] = (result) => {
      this.locationParam = result;
      this.locationFinish = true;
      this.locationSuccess = result && result.address && result.latitude && result.longitude;
      //this.getLocationInfoCb(result)
    };
    // 签出
    window['getLocationInfoCbSignOut'] = (result) => {
      //this.getLocationInfoCbSignOut(result)
      this.signOutLocationParam = result;
      this.locationFinish = true;
      this.locationSuccess = result && result.address && result.latitude && result.longitude;
    };
    window['getLocationInfoGetDistance'] = (result) => {
      //this.getLocationInfoCbSignOut(result)
      this.getDistanceLocationParam = result;
    };
    window['getUserInfoCB'] = (result) => {

      let res = result.userInfo;
      let uinfo = JSON.parse(res);
      this.userInfo = uinfo;
      initTokenAfterBack(this.$http,uinfo);//edit by qhuang at 2021/11/29

      //this.workId = this.$route.query.workId;
      Storage.session.set('workId', this.workId);
      let workIdList = this.workId.split('-');
      this.planId = workIdList[0];
      ClientJs.getLocation('','getLocationInfoGetDistance');
      let url = `/xsb/personBusiness/villageMarket/h5qryWorkIdForSearchJt?villageId=${this.objectId}`;
      this.$http.get(url)
        .then((res) => {
          if(res.data.retCode == 0){
            this.workId = this.$route.query.workId && this.$route.query.workId != '' ? this.$route.query.workId:res.data.data;
            Storage.session.set('workId', this.workId);
            if(this.$route.query.channelId == '92'){
              this.channelId = '92';
            }else{
              this.channelId = '60';
            }
            this.getBuildingDetail();
            this.getBuildingRealInfo();
            this.getControl();
            this.getWorkComplete();
            //this.selectSecurityInfo();
            this.getBuildingDetailInfo()
            this.reqFloorList()
            this.getMustDateFlag();//限制签到任务开始时间
            // 判断当前有没有签到
            this.h5GridTodayUnSignOutQuery()

            this.getQrCodeInfo() // 查询个人二维码
          }
        })

      // 埋点
      // this.updateMenuChain('Sub_Build', 1)
      this.initFirstMenuLevel(this.CONSTVAL.MENU_CHAIN.SUB_BUILD)
      // this.updateMenuChain('Sub_Build_Detail', 2)
      this.updateMenuChain(this.CONSTVAL.MENU_CHAIN.SUB_BUILD_DETAIL, 2)
      menuLogRecord({})
    }
  },
  filters: {
    //立即结束时间
    changeEndTime(val){
      if (val != '' && val != null){
        return val;
      }else{
        return "请选择";
      }
    },
    busiTypeFilter(key) {
      switch (key) {
        case '1':
          return '异号异网';
        case '2':
          return '我号异网';
        case '3':
          return '裸宽未融合';
      }
    },
  },
  created(){
    this.clientType=/android|harmony/gi.test(navigator.userAgent)?'ANDROID':'IOS';
    this.userInfo = Storage.session.get("userInfo");
    this.objectId = this.$route.query.objectId;
    // 是否从沙盘地图跳转
    this.fromSubWgt = this.$route.query.businessSource == 'subWgt'
    this.isPark = this.$route.query.isPark;
    this.workId = this.$route.query.workId;
    if(this.workId == ''){
      this.workId = Storage.session.get("workId");
    }
    Storage.session.set('workId', this.workId);
    // var pattern = /(\d*)(_\d*)/;
    // this.shouquan = !pattern.test(this.workId);
    let oa =this.userInfo.oa;
    //工作台中实际传的workId
    let count=0;
    for(let i=0;i<this.workId.length;i++){
      if(this.workId[i]=="_"){
        count++
      }
    }
    if(count == 0){
      this.shouquan = true;
    }else if(count == 1&& oa != ''&& oa != null ){
      this.shouquan = true;
    }else{
      this.shouquan = false;
    }

    let workIdList = this.workId.split('-');
    this.planId = workIdList[0];

    var pattern = /(\d*)(_\d*)/;
    this.shouquan = !pattern.test(this.workId);
    /*if(this.workId == ''){
                this.shouquan  = false;
            }*/
    //判断是否展示立即结束
    if(this.workId == '' || this.workId == null){
      this.shouquan  = false;
      this.endCheck  = false;
    }else{
      if(!this.isSign){
        this.endCheck  = true;
      }
    }
    let gobackFlag = this.$route.query.gobackFlag;
    if(this.$route.query.channelId == '92'){
      this.channelId = '92';
    }else{
      this.channelId = '60';
    }
    //第三方页面跳过来的
    if(gobackFlag === 'webview'){
      ClientJs.getSysInfo('getUserInfoCB');
    }else {
      this.userInfo = Storage.session.get('userInfo');//JSON.parse(sessionStorage.getItem('userInfo'));
      this.getBuildingDetail();
      // 获取楼宇采集信息
      this.getBuildingRealInfo();
      this.getControl();
      this.getWorkComplete();
      //this.selectSecurityInfo();
      ClientJs.getLocation('','getLocationInfoGetDistance');
      this.getBuildingDetailInfo()
      this.reqFloorList()
      this.getMustDateFlag();//限制签到任务开始时间
      // 判断当前有没有签到
      this.h5GridTodayUnSignOutQuery()

      this.getQrCodeInfo() // 查询个人二维码

      // 埋点
      // this.updateMenuChain('Sub_Build', 1)
      this.initFirstMenuLevel(this.CONSTVAL.MENU_CHAIN.SUB_BUILD)
      // this.updateMenuChain('Sub_Build_Detail', 2)
      this.updateMenuChain(this.CONSTVAL.MENU_CHAIN.SUB_BUILD_DETAIL, 2)
      menuLogRecord({})
    }

  },
  computed:{
    //弹出框的信息
    locationText(){
      if (!this.locationFinish) {
        return '定位中'
      }
      if (!this.locationSuccess) {
        return '定位获取失败，请前往信号强环境重新打卡'
      }
      if(this.isSign && this.signOutLocationParam.address){
        return this.signOutLocationParam.address
      }else if(!this.isSign && this.locationParam.address){
        return this.locationParam.address
      }
    },
    //签到还是签出
    getSignTxt() {
      if (this.signOutFlag){
        return '完成';
      }else {
        //0已签到  1未签到
        return this.isSign ? '签出' : '签到';
      }
    }
  },
  watch:{
    isSign:function(newVal,oldVal){
      if(!newVal){
        if(this.workId != '' && this.workId != null){
          this.endCheck  = true;
        }
      }else{
        this.endCheck = false;
      }
    }
  },
  methods:{
    //获取图片
    getImg(index) {
      return `static/img/${index}.jpg`;
    },
    //楼层列表信息查询
    reqFloorList(){
      let url='/xsb/personBusiness/buildingView/h5businessBuildingFloorListQuery'
      let param={
        buildId:this.objectId,
        isJd:this.isJd,
        searchKey:this.searchName,
        addBeg:this.businessAddBeg,
        addEnd:this.businessAddEnd,
        emuBeg:this.emuBeg,
        emuEnd:this.emuEnd,
        isStreet: '0', // 是否包含沿街商铺 1：是  0：否
        isExcept: this.isExcept,// 是否剔除他人看管 1:是0:否
        isTrue:this.isTrue,// 是否验真 1:是 0:否
      }
      this.floorIndex=0
      this.$http.post(url,param).then((res)=>{
        if (res.data.retCode=='0'){
          this.floorListInfo=res.data.data||[]
          if (this.floorListInfo.length>0){
            this.currentFloor=this.floorListInfo[0].floor
            this.reqBusinessFloorList(this.floorListInfo[0].floor,this.floorListInfo[0].cntCust)
          }
        }else {
          this.floorListInfo=[]
          this.$alert("查询楼层信息失败"+res.data.retMsg)
        }
      })
    },
    //拉起商机上报
    selectGroupCK(item){
      let workUrl = '&url=' + getRealUrl('mapUrl')+`%2FjtopApp%2Fmobile%2Fworkflow%2FsendFlow.do%3Fprocess_version_id%3DJTOP.SJGL.99.SJCLDDLC.1.1.0%26type%3D1`
        + `%26sceId%3D3`
        + `%26objId%3D` + item.customerId;
      this.tokenFromAld('jtyjDb','-1',Storage.session.get('userInfo'),'',workUrl);
    },
    //点击查询楼层信息
    reqFloorListWay(item,index){
      this.floorIndex=index
      this.currentFloor=item.floor
      this.reqBusinessFloorList(item.floor,item.cntCust)
    },
    //楼层企业列表查询
    reqBusinessFloorList(floor,cntCust){
      let url='/xsb/personBusiness/buildingView/h5businessBuildingFloorEnterpriseListQry'
      let param={
        buildId:this.objectId,
        floor:floor,
        isJd:this.isJd,
        pageSize:cntCust,
        pageNum:'1',
        searchKey:this.searchName,
        userId: this.userInfo.crmId,
        addBeg:this.businessAddBeg,
        addEnd:this.businessAddEnd,
        emuBeg:this.emuBeg,
        emuEnd:this.emuEnd,
        cusGroupType: this.cusGroupType,
        isStreet: '0', // 是否包含沿街商铺 1：是  0：否
        isExcept: this.isExcept,// 是否剔除他人看管 1:是0:否
        isTrue:this.isTrue,// 是否验真 1:是 0:否
        isOwe: this.isOwe, // 是否欠费 1：是 0：否 99: 全部
      }
      this.$http.post(url,param).then((res)=>{
        if (res.data.retCode=='0'){
          this.businessFloorListInfo=res.data.data.customerList||[]
        }else {
          this.businessFloorListInfo=[]
          this.$alert('查询企业信息失败'+res.data.retMsg)
        }
      })
    },
    //楼宇详细信息
    getBuildingDetailInfo(){
      let  buildId = "";
      let  villageId = "";
      let url = "";
      if(this.channelId == '92'){
        //物理楼宇
        buildId = this.objectId
        url= `/xsb/personBusiness/buildingView/h5PhyBuildingDetails?buildId=${buildId}`;
      }else{
        //楼宇
        villageId = this.objectId
        url = `/xsb/personBusiness/villageMarket/h5GetVillageDetailInfo?villageId=${villageId}&channelId=60`;
      }
      this.$http.get(url)
        .then(res => {
          if (res.data.retCode == "0") {
            let data = res.data.data;
            this.detailInfo = data.villageDetailInfos || [];
            if (this.detailInfo.length==0 || !this.detailInfo){
              this.$alert('此楼宇暂无详细信息')
            }
            if (this.detailInfo.length>0){
              for (let i=0;i<this.detailInfo.length;i++){
                this.detailInfo[i].firstMenuInfo=this.detailInfo[i].firstMenuInfo||[]
                for (let j=0;j<this.detailInfo[i].firstMenuInfo.length;j++){
                  this.detailInfoList.push(this.detailInfo[i].firstMenuInfo[j])
                }
              }
              for (let k=0;k<this.detailInfoList.length;k++){
                if (this.detailInfoList[k].secondMenuName=='网格编码'){
                  this.gridId=this.detailInfoList[k].secondMenuInfo
                }
                if (this.detailInfoList[k].secondMenuName=='楼宇场景类型') {
                  this.signDistance = this.detailInfoList[k].secondMenuInfo == '园区' ? '1000' : '150';
                }
              }
            }
            if (this.detailInfoList.length>8){
              this.detailInfoList.splice(8)
              this.buildingInfoMoreFlag=true
            }
          }
        })
    },
    //水牌信息识别接口
    getShuiPaiInfo(imgBase) {
      this.showDialog = true

      let uinfoTel =  Storage.session.get('userInfo').servNumber;

      let url = `/xsb/personBusiness/buildingView/h5getWaterPlateInfo`;
      let param = {
        buildId: this.objectId,
        waterCardPhotoStr: imgBase,
        operMsisdn: uinfoTel
      }
      param['unEncrpt'] = true;

      this.$http.post(url,param).then((res) => {
        this.showDialog = false
        if (res.data.retCode == "0") {
          // 识别出来的列表存在
          if (res.data.data.groupInfoList && res.data.data.groupInfoList.length){
            Storage.session.set('ShuiPaiInfo',res.data.data);
            this.$router.push({
              path: '/buildingOCRidentification',
              query: {
                buildId: this.objectId,
                operMsisdn: uinfoTel
              }
            });
          }else{
            // 不存在弹出错误提示弹框
            this.errorPicVisible = true
          }
        }else{
          this.$alert(res.data.retMsg);
        }
      }).catch(() => {
        this.showDialog = false

        this.$alert(res.data.retMsg);
      })
    },
    //新增
    addNewGroup() {
      let _this=this
      NlDropdown({
        confirmBtn: false,
        datalist:_this.addNewGroupList
      },function(retVal){
        // 拍摄水排
        if (retVal.id == 0){
          ClientJs.openCameraAndShow('1','shuiPaiOpenCameraOne');
        }
        //关联已有集团
        if (retVal.id==1){
          _this.$router.push({
            path:'/buildingGroupChoose',
            query:{
              buildId:_this.objectId,
              gridId:_this.gridId
            }
          })
        }
        //单个新增
        if (retVal.id==2){
          _this.$router.push({
            path:'/buildingNewGroup',
            query:{
              buildId:_this.objectId
            }
          })
        }
      })
    },
    // 确认参数筛选
    filterChange(e) {
      // 替换 /
      this.businessAddBeg = e.businessAddBeg.replace(/\//g,'');
      this.businessAddEnd = e.businessAddEnd.replace(/\//g,'');
      this.emuBeg = e.emuBeg.replace(/\//g,'');
      this.emuEnd = e.emuEnd.replace(/\//g,'');
      this.isOwe = e.isOwe;
      this.cusGroupType = e.cusGroupType;
      this.reqFloorList()
    },
    // 下拉筛选
    isNlDropdown(key){
      let _this=this
      let arrMap = {
        isJd:[{id:'9',label:'全部'},{id:'1',label: '已建档'},{id:'0',label:'未建档'}],
        isTrue:[{id:null,label:'全部'},{id:'1',label: '已验真'},{id:'0',label:'未验真'}], // 是否验真 1:是 0:否
        isExcept:[{id:null,label:'全部'},{id:'1',label: '本人看管'},{id:'0',label:'非本人看管'}] // 是否剔除他人看管 1:是0:否
      }
      NlDropdown({
        confirmBtn: false,
        datalist:arrMap[key]
      },function(retVal){
        _this[key]=retVal.id
        _this.reqFloorList()
      })
    },
    //搜索
    search(){
      if (!this.searchName.trim() && this.showFloorList) {
          this.searchName = '';
          this.$toast('请输入集团或商户名称');
          return;
      }
      if (this.searchName) {
          this.showFloorList = false;
          this.reqBusinessFloorList(99, 1000)
      } else {
          this.clearSearch()
      }
    },
    // 清空搜索
    clearSearch() {
        this.searchName = '';
        if (!this.showFloorList) {
            this.showFloorList = true;
            this.reqFloorList();
        }
    },
    //跳转集团视图
    toGroupDetailNew(customerId,isJd,isDuty){
      if (isJd==1&&isDuty==1){
        let url = `/subWgt?path=archiveGroup&groupId=${customerId}&workId=&workType=1&gobackFlag=ald`
        this.$router.push(url)
      }
    },
    //展开集团更多功能
    showMoreOperate(item) {
      let _this=this
      //已建档
      if (item.isJd==1){
        if (item.isDelete==1){
          if (!this.hadRecordGroupList[4]){
            this.hadRecordGroupList.push({id:5,label: '删除'})
          }
        }
        NlDropdown({
          confirmBtn: false,
          datalist:_this.hadRecordGroupList
        },function(retVal){
          //信息维护
          if (retVal.id==1){
            _this.$router.push({
              path:'/buildingGroupMaintain',
              query:{
                customerId:item.customerId,
                isJd:item.isJd,
                buildId:_this.objectId,
                gridId:_this.gridId,
                goBackData:JSON.stringify(_this.$route.query)
              }
            })
          }
          if(retVal.id==4){
            _this.$router.push({
              path:'/memMaintenance',
              query:{
                customerId:item.customerId,
                buildId:_this.objectId,
              }
            })
          }

          //集团业务推荐
          if(retVal.id==2){
            let param = {
              channelId:_this.channelId,
              workId:_this.workId,
              objectId:item.customerId,
              objectType:'1',
              queryType:'1'
            }
            _this.$http.post('/xsb/personBusiness/buildingView/h5QryBusiRecommendDetail',param).then((res) => {
              if(res.data.retCode == '0'){
                _this.$router.push({
                  path: '/bussinessRecommend',
                  query: {
                    objectId:res.data.data.objectId,
                    workId:res.data.data.workId,
                    activeList:JSON.stringify(res.data.data.activieList),
                    groupName:res.data.data.objectName,
                    busiOppId:'',
                    groupLongIdFlag:"1",
                    channelId:_this.channelId
                  }
                });
              }else{
                _this.$alert(res.data.retMsg);
              }
            });
          }
          //商机上报
          if (retVal.id==3){
            _this.selectGroupCK(item)
          }
          // 产品推荐
          if (retVal.id == 6) {
            _this.goRecommendProducts(item);
          }
          //删除
          if (retVal.id==5){
            _this.$messagebox({
              title: '温馨提示',
              message: '是否确定删除',
              showCancelButton: true,
              confirmButtonText:"确定",
              cancelButtonText:"取消"
            }).then(action => {
              if(action == 'confirm'){
                let url='/xsb/personBusiness/buildingView/h5BuildingCompanyAction'
                let param={
                  userId:Storage.session.get('userInfo').crmId,
                  collectType:'3',//删除
                  buildId:_this.objectId,
                  customerId:item.customerId,
                  customerName:item.customerName,
                  customerAddress:item.customerAddress,
                  longitude:_this.longitude,
                  latitude:_this.latitude,
                  isJd:item.isJd,
                  floor:_this.currentFloor,
                  room:item.room
                }
                _this.$http.post(url,param).then((res)=>{
                  if (res.data.retCode=='0') {
                    _this.reqFloorList()
                  }else {
                    _this.$alert('删除企业失败'+res.data.retMsg)
                  }
                })
              }
            })
          }
        })
      }
      else {
        if (item.isDelete==1){
          if (!this.notRecordGroupList[3]){
            this.notRecordGroupList.push({id:5,label: '删除'})
          }
        }
        NlDropdown({
          confirmBtn: false,
          datalist:_this.notRecordGroupList
        },function(retVal){
          //信息维护
          if (retVal.id==1){
            let customerId=item.customerId||''
            _this.$router.push({
              path:'/buildingGroupMaintain',
              query:{
                customerId:customerId,
                isJd:item.isJd,
                buildId:_this.objectId,
                gridId:_this.gridId
              }
            })
          }
          if(retVal.id==4){
            _this.$router.push({
              path:'/memMaintenance',
              query:{
                customerId:item.customerId,
                buildId:_this.objectId,
              }
            })
          }
          //集团建档
          if(retVal.id==2){
            const param = {
              sceneType: '1', // 场景类型 1：楼园 2：酒店 3：沿街 4：连锁
              sceneId: _this.objectId, // 场景编码
              sceneName: _this.buildingDetail.objectName, // 场景名称
              customerId: item.customerId, // 楼园企业编码
              customerName: item.customerName, // 楼园企业名称
            }
            _this.$router.push({
              path: '/groupFiling',
              query: {
                srcFrom: '',
                createGroupBack: btoa(encodeURIComponent(JSON.stringify(param), 'utf-8')),  // 关联建档
                businessSource: 'aldSand'
              }
            })
          }
          // 产品推荐
          if (retVal.id == 6) {
            _this.goRecommendProducts(item);
          }
          //删除
          if (retVal.id==5){
            let customerId=item.customerId||''
            let customerAddress=item.customerAddress||''
            _this.$messagebox({
              title: '温馨提示',
              message: '是否确定删除',
              showCancelButton: true,
              confirmButtonText:"确定",
              cancelButtonText:"取消"
            }).then(action => {
              if(action == 'confirm'){
                let url='/xsb/personBusiness/buildingView/h5BuildingCompanyAction'
                let param={
                  userId:Storage.session.get('userInfo').crmId,
                  collectType:'3',//3删除
                  buildId:_this.objectId,
                  customerId:customerId,
                  customerName:item.customerName,
                  customerAddress:customerAddress,
                  longitude:_this.longitude,
                  latitude:_this.latitude,
                  isJd:item.isJd,
                  floor:_this.currentFloor,
                  room:item.room
                }
                _this.$http.post(url,param).then((res)=>{
                  if (res.data.retCode=='0') {
                    _this.reqFloorList()
                  }else {
                    _this.$alert('删除企业失败'+res.data.retMsg)
                  }
                })
              }
            })
          }
        })
      }
    },
    // 跳转产品推荐
    goRecommendProducts(item) {
      // 埋点
      // this.updateMenuChain('Sub_Build_Recommend', 3)
      this.updateMenuChain(this.CONSTVAL.MENU_CHAIN.SUB_BUILD_RECOMMEND, 3)
      menuLogRecord({})

      const url = `&scenename=企业&amapClassNameBig=${item.amapClassNameBig}&amapClassNameMid=${item.amapClassNameMid}&amapClassNameSmall=${item.amapClassNameSmall}&groupAddress=${item.customerAddress}`;
      this.tokenFromAld('jsisi','-1',Storage.session.get('userInfo'),'', url);
    },
    //跳转楼宇属性
    goBuildingProperty() {
      this.$router.push({
        path: '/buildingRealInfoCollection',
        query: {
          objectId:this.objectId,
          workId:this.workId,
          buildingName:this.buildingDetail.objectName,
          bindTimes:this.bindTimes
        }
      })
    },
    goPrev(){
      Storage.session.set('workId','');
      let gobackFlag = this.$route.query.gobackFlag;
      //第三方页面跳过来的
      if(gobackFlag === 'webview'){
        ClientJs.closeCallBack('FSOP');
      }else if (gobackFlag == 'gridWork' || gobackFlag == 'ald' ){
        history.go(-1);
      }else if (gobackFlag == 'PhyBuildingOpportunityDetails' ){
        this.$router.push({
          path: '/PhyBuildingOpportunityDetails',
          query: {
            objectId: this.objectId,
            workId: this.workId,
            returnFlag:this.$route.query.returnFlag
          }
        })}else{
          if(Storage.session.get("filterInfo")){
              this.$router.push({
                  path: '/MyTaskList'
              })
          }else {
              this.$router.push({
                  path: '/',
              })
          }
      }
      //history.go(-1);
    },
    goBuildingDetail(){
      this.$router.push({
        name:'BuildingViewInfo',
        query: {
          villageId:this.objectId,
          workId:this.workId,
          channelId:this.channelId
        }
      })
    },
    //查询是否有未完成的任务
    getWorkComplete(){
      let url = `/xsb/personBusiness/villageMarket/h5HasNotFinishedTask?channelId=`+this.channelId+`&workId=`+this.workId;
      this.$http.get(url)
        .then((res) => {
          if(res.data.retCode == '0'){
            this.workComplete = res.data.data.flag == '1';
            this.unfinishedWorkId = res.data.data.workId;
            this.interfaceLoadComplete = true;
          }else{
            this.$alert('查询是否有未完成的任务错误'+res.data.retMsg);
          }
        })
    },
    getBuildingRealInfo() {
      let _this = this;
      let url = '/xsb/personBusiness/buildingView/h5QryBuildingRealInfoCollection';
      let param = { buildId: this.objectId }
      this.$http.post(url,param).then((res) => {
        let { retCode, data} = res.data;
        if (retCode == '0') {
          _this.latitude = data.buildLatitude;
          _this.latBefore = data.buildLatitude;
          _this.longitude = data.buildLongitude;
          _this.lonBefore = data.buildLongitude;
          _this.buildingDetail2 = data
        }
      })
    },
    //获取详情
    getBuildingDetail() {
      let _this = this;
      let workId = Storage.session.get('workId');
      let param = {
        workId:workId,
        objectId:this.objectId,
        channelId: this.channelId
      };
      this.$http.post('/xsb/personBusiness/customizate/h5QryHotSceneInfoDetail', param)
        .then((res) => {
          if (res.data.retCode == '0') {
            _this.buildingDetail = res.data.data;
            for (let  i =0 ;i<_this.buildingDetail.infos.length ; i++) {
              if (_this.buildingDetail.infos[i].infoName == '纬度'){
                // _this.latitude = _this.buildingDetail.infos[i].infoValue;
                // _this.latBefore = _this.buildingDetail.infos[i].infoValue;
                _this.buildingDetail.infos.splice(i,1);
              }
            }
            for (let  y =0 ;y<_this.buildingDetail.infos.length ; y++) {
              if (_this.buildingDetail.infos[y].infoName == '经度') {
                // _this.longitude = _this.buildingDetail.infos[y].infoValue;
                // _this.lonBefore = _this.buildingDetail.infos[y].infoValue
                _this.buildingDetail.infos.splice(y,1);
              }
            }
            for (let  c =0 ;c<_this.buildingDetail.infos.length ; c++) {
              if (_this.buildingDetail.infos[c].infoName == '建筑物地址'){
                _this.locationBefore = _this.buildingDetail.infos[c].infoValue;
                _this.buildingDetail.infos.splice(c,1);
              }
            }
            // _this.hasLat = _this.latitude != '' && _this.latitude != null && _this.longitude != '' && _this.longitude != null;
            _this.isSign = _this.buildingDetail.isSign == '0';
            _this.isCamera = _this.buildingDetail.isPhoto == '0';
            _this.isResponsible = _this.buildingDetail.isResponsible == '1'
          } else {
            _this.$alert('查询明细信息失败，'+ res.data.retMsg );
          }
        })
    },
    getControl(){
      this.$http.get('/xsb/personBusiness/buildingView/h5GetControl').then((res)=>{
        if (res.data.retCode == '0'){
          // this.signDistance = res.data.data.signDistance;
          this.targetUser = res.data.data.targetUser;
          this.signOutLimitTime = res.data.data.signOutLimitTime;
          this.bindTimes = res.data.data.bindTimes;
          //地址变更次数控制
          if (res.data.data.allowAddressChange != '0'){
            this.addressChangeTimes = res.data.data.allowAddressChange;
            this.allowAddressChange = true;
            let url1 = `/xsb/personBusiness/my/h5GetChangeTimes?objectId=${this.objectId}&objectIdType=4`;
            this.$http.get(url1)
              .then((res1) => {
                if (res1.data.retCode == '0') {
                  this.alAddressChangeTimes = res1.data.data;
                }else{
                  this.$alert("获取已变更地址次数错误"+res1.data.retMsg);
                }
              })
          }

          this.photoCount = res.data.data.photoCount;
          this.outdisSign = res.data.data.outdisSign == '1';
          this.signOperation = res.data.data.signOperation == '1';
        }
      })
    },
    getMustDateFlag(){
      let param = {
        switchType: 'task_mustdate_flag'
      }
      this.$http.post('/xsb/personBusiness/gridSand/h5GridMarketSwitch', param).then((res) => {
        if (res.data.retCode == 0) {
          this.mustDateFlag = true;
          //存在权限，获取当前对象，今天存在的最新的任务
          this.getMustTaskInfo();
        }else {
          this.mustDateFlag = false;
        }
      })
    },
    getMustTaskInfo(){
      //workId
      let currentWorkId = '';
      if(Storage.session.get('workId')){
        currentWorkId = Storage.session.get('workId');
      }
      let param = {
        telNum:Storage.session.get('userInfo').servNumber,
        subId:this.objectId,
        workId:currentWorkId  //存在就用workId查询
      }
      this.$http.post('/xsb/personBusiness/villageMarket/h5qryWorkInfoBuSubId', param).then((res) => {
        console.info("当天任务查询结果",res.data)
        if (res.data.retCode == 0) {
          if(res.data.data){
            this.mustDate = res.data.data.mustDate;
            if(currentWorkId == ''){
              this.workId = res.data.data.workId;
              Storage.session.set('workId',this.workId)
              let workIdList = this.workId.split('-');
              this.planId = workIdList[0];
            }
          }else {
            this.mustDate = ''
          }
        }else {
          this.$alert(res.data.retMsg)
        }
      })
    },
    judgeTime(val) {
      console.info("判断时间")
      if(val.indexOf('.') == '-1'){
        val = val.replace('.', '-0');
      }
      const givenTime = new Date(val.replace(/-/g, '/'));
      console.info("givenTime",val);

      // 检查日期是否有效
      if (isNaN(givenTime)) {
        this.$alert("callStartTime时间处理失败");
      }
      // 获取当前时间并创建一个 Date 对象
      const currentTime = new Date()

      // 将当前日期的时间部分设置为0，以便只比较日期部分
      currentTime.setHours(0, 0, 0, 0);
      // 将给定日期的时间部分也设置为0
      givenTime.setHours(0, 0, 0, 0)
      console.info("currentTime.setHours",currentTime);
      console.info("givenTime.setHours",givenTime);

      if(givenTime <= currentTime){
        console.info("是当天时间");
        return true;
      }else {
        console.info("不是当天时间");
        return false;
      }
    },
    //签到签出
    async signInOut() {
      if (this.signCount > 0) {
          this.$messagebox({ title: '温馨提示', message: `本${this.isPark == '0' ? '楼宇' : '园区'}今日您已打卡`, showCancelButton: false})
          return;
      }
      // 埋点
      // this.updateMenuChain('Sub_Build_Sign', 3)
      this.updateMenuChain(this.CONSTVAL.MENU_CHAIN.SUB_BUILD_SIGN, 3)
      menuLogRecord({})
      if(!this.isSign) {//签到
        if (!this.interfaceLoadComplete) {
          return;
        }
        // 签到前再判断一次是否已打卡
        await this.signCountQuery();
        if (this.signCount > 0) {
           this.$messagebox({ title: '温馨提示', message: `本${this.isPark == '0' ? '楼宇' : '园区'}今日您已打卡`, showCancelButton: false})
           return;
        }
        if (this.workComplete) {
          //this.$alert('当前还有未签出的任务，请先将任务完成');
          this.$messagebox({
            title: '温馨提示',
            message: '当前还有未签出的任务，点击确定跳转',
            showCancelButton: true,
            confirmButtonText:"确定",
            cancelButtonText:"取消"
          }).then(action => {
            if(action == 'confirm'){
              this.goUnfinishedWork();
            }
          })
          return;
        }
        //先判断是否查询到任务
        if(this.mustDate){
          //判断是否存在
          if(this.mustDateFlag && !this.judgeTime(this.mustDate)) {
            //判断当前时间有没有到任务开始时间，没到提示不给签到
            this.$messagebox({
              title: '温馨提示',
              message: '当前时间未达到任务指定开始时间',
              showCancelButton: false,
              confirmButtonText: "确定"
            })
            return;
          }
        }

        this.locationParam = {};
        this.isShowMessageBox = true;
        this.locationFinish = false;
        this.locationSuccess = false;
        ClientJs.getLocation('', 'getLocationInfoCb');
      }else{//签出
        //计算已拍照数量
        if (this.photoCount != '0') {
          let url = `/xsb/personBusiness/villageMarket/h5CountPhotoNum?workId=${Storage.session.get('workId')}&type=building`;
          this.$http.get(url)
            .then((res) => {
              if (res.data.retCode == '0') {
                this.alPhotoCount = res.data.data;
                this.signOutCount();
              } else {
                this.$alert('查询已拍照张数失败' + res.data.retMsg);
              }
            })
        }else{
          this.signOutCount();
        }
      }
    },
    goUnfinishedWork(){
      this.$http.get('/xsb/personBusiness/villageMarket/h5GetUnfinishedWorkInfo?workId='+this.unfinishedWorkId)
        .then((res) =>{
          if (res.data.retCode == '0'){
            let gobackFlag = this.$route.query.gobackFlag;
            if(gobackFlag === 'webview'){
              sessionStorage.setItem('activityNum',0);
              this.routeList(res.data.data,'webview');
            } else{
              sessionStorage.setItem('activityNum',0);
              this.routeList(res.data.data,'');
            }
          }
        })
    },
    routeList(item,flag){
      if (item.worktypecode == '1004'){//小区
        this.$router.push({
          path: '/villageDetail',
          query: {
            workIdFlag:true,
            workId: item.workid,
            villageId: item.subid,
            gobackFlag:flag
          }
        })
      }else if(item.worktypecode == '1001'){//集团
        this.$router.push({
          path: `/groupDetailNew`,
          query: {
            workIdFlag:true,
            taskId: item.workid,
            flag: '1',
            groupId:item.subid,
            gobackFlag:flag
          }
        })
      }else if(item.worktypecode == '1009'){//集团个人
        this.$router.push({
          path: `/groupDetailNew`,
          query: {
            workIdFlag:true,
            taskId: item.workid,
            flag: '2',
            groupId:item.subid,
            gobackFlag:flag
          }
        })
      } else if(item.worktypecode == '1012'){//热点
        this.$router.push({
          path: `/customSceneDetail`,
          query: {
            workIdFlag:true,
            workId: item.workid,
            objectId: item.subid,
            gobackFlag:flag
          }
        })
      }else if(item.worktypecode == '1008'){//高校
        this.$router.push({
          path: '/CollegeDetail',
          query: {
            workId: item.workid,
            collegeId: item.subid,
            gobackFlag:flag
          }
        })
      }else if(item.worktypecode == '1024'){//楼宇
        this.$router.push({
          path: '/',
        })
        setTimeout(()=>{
          this.$router.push({
            path: '/buildingViewDetail',
            query: {
              objectId : item.subid,
              workId : item.workid
            }
          })
        },500)
      }else if(item.worktypecode == '1025'){//清单集团
        this.$router.push({
          path: '/listGroupDetail',
          query: {
            workIdFlag:true,
            taskId: item.workid,
            groupId:item.subid
          }
        })
      }else if(item.worktypecode == '1045'){//栅格任务
        this.$router.push({
          path: '/customSceneDetail',
          query: {
            workIdFlag:true,
            workId: item.workid,
            objectId: item.subid,
            channelId: "78"
          }
        })
      }else if(item.worktypecode == '1058'){//物理楼宇
        this.$router.push({
          path: '/',
        })
        setTimeout(()=>{
          this.$router.push({
            path: '/buildingViewDetail',
            query: {
              objectId : item.subid,
              workId : item.workid,
              channelId:'92'
            }
          })
        },500)
      }else if(item.worktypecode == '1059'){//沿街商铺
        this.$router.push({
          path: `/shopsNew`,
          query: {
            workIdFlag:true,
            workId: item.workid,
            objectId: item.subid,
            channelId:"95"
          }
        })
      }else if(item.worktypecode == '1060'){//1060泛住宿任务
        this.$router.push({
          path: `/shopsViewDetail`,
          query: {
            workIdFlag:true,
            workId: item.workid,
            objectId: item.subid,
            channelId:"96"
          }
        })
      }else if(item.worktypecode == '1063'){//1063乡村任务
        this.$router.push({
          path: `/shopsNew`,
          query: {
            workIdFlag:true,
            workId: item.workid,
            objectId: item.subid,
            channelId:"97"
          }
        })
      } else if (item.worktypecode == '1066') {
        //1066道路任务
        this.$router.push({
          path: `/corridorViewDetail`,
          query: {
            workIdFlag: true,
            workId: item.workid,
            objectId: item.subid,
            channelId: '104',
          },
        })
      }
    },
    //签出
    signOutCount() {
      let _this = this;
      if (_this.alPhotoCount > _this.photoCount || _this.alPhotoCount == _this.photoCount) {
        _this.signOutLocationParam = {};
        this.isShowMessageBox = true;
        this.locationFinish = false;
        this.locationSuccess = false;
        ClientJs.getLocation('', 'getLocationInfoCbSignOut');
      } else {
        this.$alert('拍照数量未达到要求，当前拍照数量 ' + this.alPhotoCount);
      }
    },
    //重新定位按钮
    messageCancel(){
      if(this.isSign){//签出
        this.signOutLocationParam={};
        this.isShowMessageBox = true;
        this.locationFinish = false;
        this.locationSuccess = false;
        ClientJs.getLocation('','getLocationInfoCbSignOut');
      }else{
        this.locationParam={};
        this.isShowMessageBox = true;
        this.locationFinish = false;
        this.locationSuccess = false;
        ClientJs.getLocation('','getLocationInfoCb');
      }
    },
    //签到签出按钮
    messageConfirm(){
      this.isShowMessageBox = false;
      let longitude = '';
      let latitude = '';
      if (!this.isSign){//签到
        longitude = this.locationParam.longitude;
        latitude = this.locationParam.latitude;
        this.pointLocationInfo = this.locationParam;
      }else{//签出
        longitude = this.signOutLocationParam.longitude;
        latitude = this.signOutLocationParam.latitude;
        this.pointLocationInfo = this.signOutLocationParam;
      }

      if (this.fromSubWgt) {
        // 从sugWgt跳转
        this.signDistance = this.isPark === '0' ? 150 : 1000;
      }
      this.hasLat = (this.latitude && this.longitude) ? true : false;
      this.$http.get('/xsb/personBusiness/my/h5GetDistance?lng1='+longitude+'&lat1='+latitude+'&lng2='+this.longitude+'&lat2='+this.latitude)
        .then((response) => {
          this.distance = response.data;
          let signDistanceFlag = false;
          if (Number(this.distance) > Number(this.signDistance)) {
            signDistanceFlag = true;
          }
          if (this.hasLat && this.signDistance != '0' && signDistanceFlag) {
            this.isOutOfDistance = true;
          } else {
            this.signInOutConfirm();
          }
          this.outdisSign = this.isOutOfDistance && this.outdisSign;
        });
    },
    signInOutConfirm(flag){
      this.signOutStr = flag
      if (!this.hasLat){//没有经纬度信息
        this.distance = 0;
      }
      if (this.isSign){//签出
        if (this.signOutLimitTime!='0'){
          //查询签到时间
          this.getSignInTime();
          let now = new Date().getTime();
          let dif = now - this.signInTime;
          if (dif > this.signOutLimitTime){
            // this.doSignOut(flag);
            this.signOutVisible = true
          }else{
            //this.$alert("当前任务需签到20分钟后才可以签出");
            this.isOutOfDistance = false;
            this.$messagebox({
              title: '温馨提示',
              message: '当前任务签到后未满指定时间，是否签出',
              showCancelButton: true,
              confirmButtonText:"确定",
              cancelButtonText:"取消"
            }).then(action => {
              if(action == 'confirm'){
                // this.doSignOut(flag);
                this.signOutVisible = true
              }
            })
          }
        }else {
          // this.doSignOut(flag);
          this.signOutVisible = true
        }
      }else if (!this.isSign){//签到
        this.doSignIn(flag);
      }
    },
    getSignInTime(){
      let url = `/xsb/personBusiness/customizate/h5GetSignInTime?workId=${Storage.session.get('workId')}&objectId=${this.buildingDetail.objectId}`;
      this.$http.get(url)
        .then((res) => {
          if (res.data.retCode == '0') {
            if (res.data.data != null) {
              this.signInTime = chgStrToDate(res.data.data,true).getTime();
            }
          }else{
            this.$alert("获取此任务签到时间错误"+res.data.retMsg);
          }
        })
    },
    //签出 获取地理位置的返回事件
    doSignOut(e) {
      let flag = this.signOutStr
      let _this = this;
      let location, lat, lon = 0;
      let operationType = 1;
      if (flag == 'update' || !_this.lonBefore || !_this.latBefore){
        operationType = 2;
      }
      //若非管理员 默认不能变更
      if (!this.isResponsible){
        operationType = 1;
      }
      location = _this.signOutLocationParam.address;//位置
      lat =_this.signOutLocationParam.latitude;//经度
      lon =_this.signOutLocationParam.longitude;//纬度
      //获取到位置信息
      if (location) {
        if (!_this.signOutFlag && _this.isSign) {//  表示未签出
          //判断是否已经拍照
          let param = {
            workId: Storage.session.get('workId'),
            objectId: _this.buildingDetail.objectId,
            objectName:_this.buildingDetail.objectName,
            lat: lat,                      //经度
            lon: lon,                      //纬度
            location: location,
            locationStatus: "1",                     //0 签到，1签出
            distance:_this.distance,
            channelId: _this.channelId,
            lonBefore:_this.lonBefore,
            latBefore:_this.latBefore,
            operationType:operationType,
            operatorName: Storage.session.get('userInfo').operatorName
          };
          _this.$http.post('/xsb/personBusiness/customizate/h5SignInOutSubmit', param)
            .then((res) => {
              if (res.data.retCode == '0') {
                _this.$toast('签出成功');
                _this.signOutFlag = true;

                this.handleQrCodeState() // 变更个人码状态

                if(!lat){
                  this.$alert("签到签出纬度不能为空")
                  return
                }
                if(!lon){
                  this.$alert("签到签出精度不能为空")
                  return
                }

                //决策地图
                let param1 = {
                  workId:  Storage.session.get('workId'),
                  latitude:lat,
                  longitude: lon,
                  location: location,
                  locationTime: new Date().getTime(),
                  locationStatus: "1",
                  distance:_this.distance,
                  operationType:operationType
                };
                _this.$http.post('/xsb/personBusiness/college/h5SignSync', param1)
                  .then((res) => {
                    if (res.data.retCode == '0') {

                    }else {
                      _this.$alert('决策地图提交信息失败'+ res.data.retMsg);
                    }
                  })
                //记录地址变更信息表
                if (operationType == 2) {
                  let param2 = {
                    objectId: this.objectId,
                    objectIdType: '4',
                    oldLon: this.longitude,
                    oldLat: this.latitude,
                    newLon: _this.signOutLocationParam.longitude,
                    newLat: _this.signOutLocationParam.latitude
                  };
                  _this.$http.post('/xsb/personBusiness/my/h5ChangeAddressRecord', param2)
                    .then((res) => {
                      if (res.data.retCode == '0') {
                        _this.longitude = _this.signOutLocationParam.longitude;
                        _this.latitude = _this.signOutLocationParam.latitude;
                        _this.signDistanceFlag = false;
                        _this.alAddressChangeTimes = _this.alAddressChangeTimes +1;
                      } else {
                        _this.$alert('记录地址变更信息表' + res.data.retMsg);
                      }
                    })
                }
                this.isOutOfDistance = false;
              } else {
                _this.$alert(res.data.retMsg || '签出失败');
              }
            })
          // 执行一下签出接口
          let params = {
            signId: this.signInId,// 打卡唯一编码
            signOutLatitude: lat,//签出纬度
            signOutLongitude: lon,// 签出经度
            signOutAddress: location,//签出地址
            mpResultType: e.mpResultType,//  摸排结果类型：1：商机收集；2：现场营销；3：其他
            nextTime: e.nextTime,//下次拜访时间
            isIntent: e.isIntent,//客户是否有意向：0：否；1：是
            remark: e.remark,// 备注
          }
          console.info('签出接口',params)
          h5GridSignOutSubmit(params).then(res=>{
            if (res) {
              // 重新加载是否签到状态
              this.h5GridTodayUnSignOutQuery()
            }
          })
        } else {
          _this.$alert('您已经签出');
        }
      }
    },
    //签到
    doSignIn(flag) {
      let _this = this;
      let operationType = 1;
      if (flag == 'update' || !_this.lonBefore || !_this.latBefore){
        operationType = 2;
      }
      let location, lat, lon, locationStatus = 0;
      location = _this.locationParam.address;//位置
      lat = _this.locationParam.latitude;//经度
      lon = _this.locationParam.longitude;//纬度
      if (_this.isSign) {//已经签到
        locationStatus = 1;//签出
      }
      //若非管理员 默认不能变更
      if (!this.isResponsible){
        operationType = 1;
      }
      //获取到位置信息
      if (location) {
        let param = {
          workId: Storage.session.get('workId'),
          objectId: _this.buildingDetail.objectId,
          lat: lat,                      //经度
          lon: lon,                      //纬度
          location: location,
          locationStatus: "0",                   //0 签到，1签出
          distance:_this.distance,
          channelId: _this.channelId,
          lonBefore:_this.lonBefore,
          latBefore:_this.latBefore,
          operationType:operationType,
          objectName:_this.buildingDetail.objectName,
          operatorName: Storage.session.get('userInfo').operatorName
        };
        _this.$http.post('/xsb/personBusiness/customizate/h5SignInOutSubmit', param)
          .then((res) => {
            if (res.data.retCode == '0') {
              if (res.data.data != "" && res.data.data != null) {
                if(Storage.session.get('workId') == '') {
                  Storage.session.set('workId', res.data.data.workId);
                }
                _this.workId = res.data.data.workId;
                _this.buildingDetail.workId = res.data.data.workId;
              }

              //安全检查信息入表
              if(this.SafeCheckSwitch) {
                if(Storage.session.get('workId') && Storage.session.get('workId') != ''){
                  let workId = Storage.session.get('workId');
                  let workIdList = workId.split('-');
                  this.planId = workIdList[0];
                }
                let url = "/xsb/personBusiness/villageMarket/h5SelectSecurityInfo?planId="+this.planId;
                this.$http.get(url).then(res => {
                  let {retCode,retMsg,data} = res.data;
                  if (retCode == '0') {
                    if(data == null){
                      this.$http.get("/xsb/personBusiness/villageMarket/h5UpdateSecurityInfo?planId="+this.planId).then(res1 => {
                        if (res1.data.retCode == '0') {
                          this.securityCheckShow = true;
                        }else{
                          this.$alert(res1.data.retMsg || '安全检查信息入库失败')
                        }
                      })
                    }else if(data.finishFlag == "0" && (data.crmId == this.crmId)) {
                      this.securityCheckShow = true;
                    }else {
                      this.stepNum = "5";
                      this.securityCheckShow = true;
                    }
                  } else {
                    this.$alert(retMsg || '获取安全检查信息请求失败');
                  }
                })
              }


              _this.$toast('签到成功');
              _this.isSign = true;
              _this.signInTime = new Date().getTime();
              _this.handleSubmitQrCode() // 保存个人码

              if(!lat){
                this.$alert("签到签出纬度不能为空")
                return
              }
              if(!lon){
                this.$alert("签到签出精度不能为空")
                return
              }
              //决策地图
              let param1 = {
                workId:  Storage.session.get('workId'),
                latitude:lat,
                longitude: lon,
                location: location,
                locationTime: new Date().getTime(),
                locationStatus: "0",
                distance:this.distance,
                operationType:operationType
              };
              _this.$http.post('/xsb/personBusiness/college/h5SignSync', param1)
                .then((res) => {
                  if (res.data.retCode == '0') {

                  }else {
                    _this.$alert('决策地图提交信息失败'+ res.data.retMsg);
                  }
                })
                .catch((response) => {
                });

              //记录地址变更信息表
              if (operationType == 2) {
                let param2 = {
                  objectId: this.objectId,
                  objectIdType: '4',
                  oldLon: this.longitude,
                  oldLat: this.latitude,
                  newLon: _this.locationParam.longitude,
                  newLat: _this.locationParam.latitude
                };
                _this.$http.post('/xsb/personBusiness/my/h5ChangeAddressRecord', param2)
                  .then((res) => {
                    if (res.data.retCode == '0') {
                      _this.longitude = _this.locationParam.longitude;
                      _this.latitude = _this.locationParam.latitude;
                      _this.signDistanceFlag = false;
                      _this.alAddressChangeTimes = _this.alAddressChangeTimes +1;
                    } else {
                      _this.$alert('记录地址变更信息表' + res.data.retMsg);
                    }
                  })
              }
              this.isOutOfDistance = false;
            } else {
              _this.$alert('签到失败，'+ res.data.retMsg );
            }
          })
        // 执行一下签到接口
        this.h5GridSignInSubmit(lat,lon,location)
      }
    },
    //摄像头点击
    cameraCk() {
      if(!this.workId){
        this.$alert("请先完成签到");
        return;
      }
      this.axiosParam = {
        workId: Storage.session.get('workId'),
        objectId: this.objectId,
        type:'1',
        submitFlag:'sign',
        submitType:'building',
        channelId: this.channelId
      }
      //this.cameraFrom = 'sign';
      this.showCameraFlg = true;//打开拍照组件
    },
    //关闭组件
    getClose(){
      this.showCameraFlg = false;
    },
    //签名拍照返回
    signCameraCb(cnt) {
      this.showCameraFlg = false;
      this.isCamera = !!cnt;
    },
    //活动拍照返回
    activityCameraCb(cnt) {
      this.showCameraFlg = false;
    },
    cancelMask(){
      this.isShowMessageBox = false;
    },
    cancelBox(){
      this.isOutOfDistance = false;
    },
    getMustDo() {
      let _this = this;
      let url = `/xsb/personBusiness/customizate/h5QryMustdoSteps?workId=${this.workId}&objectId=${this.objectId}&channelId=`+this.channelId;
      this.$http.get(url).then((res) => {
        let d = res.data;
        if (res.data.retCode == '0') {
          //当前活动是否已经拍照
          _this.currActivityIsPhoto = d.data.isPhoto == '0' ? true : false;
          //设置是否完成flag
          _this.currActivityIsComplete = d.data.workFlag == '0' ? true : false;

          _this.sixMustDo.smList = d.data.mustInfos;
          for (let x = 0;x<_this.sixMustDo.smList.length;x++){
            _this.sixMustDo.smList[x].isInput = _this.sixMustDo.smList[x].isInput == '1' ? '0':'1';
            if (_this.sixMustDo.smList[x].mustKey == null){
              _this.sixMustDo.smList[x].mustKey = '';
              _this.sixMustDo.smList[x].mustValue = '';
            }
          }

          let paramObj = {
            mustId: '',              //唯一标识
            mustName: '',            //必做项
            mustType: 0,             //输入框类型  1输入框 2多选框 3单选框 4下拉框
            mustValue: '',           //输入值
            mustKey: '',             //输入展示值
          };
          if(_this.sixMustDo.smList.length > 0){
            for (var i = 0; i < _this.sixMustDo.smList.length; i++) {
              _this.sixParamList.push(paramObj);
            }
          }
          this.mustDoLoadComplete = true;

          setTimeout(()=>{
            this.$router.push({
              name:'BuildingViewMustDo',
              query: {
                objectId:this.objectId,
                workId:this.workId,
                sixMustDo:this.sixMustDo,
                sixParamList:this.sixParamList,
                channelId:this.channelId
              }
            })
          },200)
        } else {
          _this.$alert('必做查询信息失败，'+ res.data.retMsg );
        }
      });
    },
    goMustDo(){
      if (!this.isSign){
        this.$alert("请先完成签到");
        return;
      }
      this.getMustDo();

    },
    goPerBusiRec(){
      if(!this.isSign){
        this.$alert("请先完成签到");
        return false;
      }
      this.$router.push({
        name:'PersonBusiResidentUser',
        query: {
          buildId:this.objectId,
          workId:this.workId,
          channelId:this.channelId
        }
      })
    },
    goDaosanjiao(){
      let workUrl = '&url=' + getRealUrl('mapUrl')+`%2FcmopApp%2Fmobile%2Fdsjnew%2FshowModule.do%3Fmodule_level%3D2%26object_id%3D`+this.objectId+`%26region_id%3D`+Storage.session.get('userInfo').region;
      this.tokenFromAld('cmopDb','-1',Storage.session.get('userInfo'),'',workUrl);
    },
    goBuildingbusi(){
      let workUrl = '&url=' + getRealUrl('mapUrl')+`%2FjtopApp%2Fmobile%2Fpremisesview%2Fframe.do%3Fobject_id%3D${this.objectId}%26region_id%3D${Storage.session.get('userInfo').region}`;
      //let workUrl = '&url=' + getRealUrl('mapUrl')+`%2FjtopApp%2Fmobile%2Fportal%2FloadUrl.do%3Fobject_id%3D${this.objectId}%26region_id%3D${Storage.session.get('userInfo').region}`;
      this.tokenFromAld('jtyjDb','-1',Storage.session.get('userInfo'),'',workUrl);
    },
    openMarketingNote(){
      if (!this.workId){
        this.$alert("请先完成签到");
        return;
      }
      // this.showMarketingNote = true;
      this.$router.push({
        path:'marketingJotNote',
        query: {
          objectId:this.objectId,
          workId:this.workId,
          channelId:this.channelId
        }
      });
    },
    cancleMarketingNote(){
      this.showMarketingNote = false;
    },
    submitMarketingNote(){
      let _this = this;
      if(this.MarketingNoteText == ''){
        this.$messagebox('温馨提示', '请输入内容');
      }else{
        let param = {
          objectId:_this.objectId,
          workId:Storage.session.get('workId'),
          remark:this.MarketingNoteText,
          channelId:this.channelId
        };
        this.$http.post('/xsb/personBusiness/customizate/h5HandwritingSyn',param).then((res) => {
          let d = res.data;
          if(res.data.retCode == '0'){
            _this.$toast('提交成功');
            _this.MarketingNoteText = '';
            _this.showMarketingNote = false;
          }else{
            _this.$toast('提交失败');
          }
        });
      }
    },

    // 商铺优惠
    goShopDiscounts() {
      this.$router.push({
        path: 'shopDiscounts',
        query: {
          objectId: this.objectId,
          workId: this.workId,
          channelId: this.channelId,
        },
      })
    },

    btnCk() {
      if (!this.interfaceLoadComplete){
        return;
      }
      this.$router.push({
        path:'authorIntegration',
        query: {
          objectId:this.objectId,
          workId:this.workId,
          objectName:this.buildingDetail.objectName,
          channelId:this.channelId
        }
      })
    },
    //跳转位置变更申请
    groupPointSync(){
      let locationInfoBefore = {
        longitude:this.lonBefore,
        latitude:this.latBefore,
        location:this.locationBefore
      }

      let locationInfo = {
        longitude:this.pointLocationInfo.longitude,
        latitude:this.pointLocationInfo.latitude,
        location:this.pointLocationInfo.address
      }
      this.$router.push({
        name:'BuildingPointInfoSync',
        query: {
          buildingId : this.objectId,
          buildingName : this.buildingDetail.objectName,
          locationInfo : JSON.stringify(locationInfo),
          locationInfoBefore : JSON.stringify(locationInfoBefore)
        }
      })
    },
    //立即结束
    endNow(){
      this.moreInputCheck = false;
      this.showEndDetail = true;
    },
    //取消立即结束弹框
    cancleEndBox(){
      this.showEndDetail = false;
    },
    //立即结束时间组件
    timeChoose(){
      let _this = this;
      NlDatePicker({
        onlyOne: true,
        startDate: dateFormat(new Date(new Date().getTime()+1000*24*60*60),"yyyy-MM-dd"),
        tsMinDate:new Date(new Date().getTime()+1000*24*60*60),
        dateType:'date',
        format:'yyyy-MM-dd',
      }, function(retVal) {
        _this.markTime = retVal.startDate.replace(/\-/g, "");
      });
    },
    //立即结束
    submitEndNote(){
      if (this.endCheckChoose == 1){
        this.endNoteText = '任务已授权，需结束。';
        this.markTime = '';
      } else if (this.endCheckChoose == 2){
        this.endNoteText = '任务有误，需结束。';
        this.markTime = '';
      } else if (this.endCheckChoose ==3){
        if (this.endNoteText == '' || this.endNoteText == null){
          return this.$alert("请输入结束原因");
        }
        if (this.markTime == '' || this.markTime == null){
          return this.$alert("请选择下次营销时间");
        }
      }
      let params = {
        "channelId": this.channelId,
        "workId": this.workId,
        "objectId" : this.objectId,
        "operType" : "2",
        "reason" : this.endNoteText,
        "markTime" : this.markTime
      }
      let url = "/xsb/personBusiness/busiOpyRemind/h5QryTaskNotice";
      this.$http.post(url, params).then(res => {
        let {retCode,retMsg,data} = res.data;
        if (retCode == '0') {//成功的情况
          this.$alert('结束成功');
          history.go(-1);
        } else {
          this.$alert(retMsg || '请求失败');
        }
      }).catch(err => {
        this.$alert(err)
      })
    },

    doMoreInput(){
      if(this.moreInputCheck){
        this.moreInputCheck = false;
      } else{
        this.moreInputCheck = true;
      }
    },
    //选择结束情况
    endChoose(item){
      if (item == 1){
        this.endCheckChoose = 1;
      } else if (item == 2){
        this.endCheckChoose = 2;
      } else if (item == 3){
        this.endCheckChoose = 3;
      }
    },
    closeSecurityCheck(){
      this.securityCheckShow = false;
    },
    selectSecurityInfo(){
      let url = "/xsb/personBusiness/villageMarket/h5SafeCheckSwitch"; //安全检查开关接口
      this.$http.get(url).then(res => {
        let {retCode} = res.data;
        if (retCode == '0') {
          this.SafeCheckSwitch = true;
          let url = "/xsb/personBusiness/villageMarket/h5SelectSecurityInfo?planId="+this.planId;
          this.$http.get(url).then(res => {
            let {retCode,retMsg,data} = res.data;
            if (retCode == '0') {
              if(data && data.crmId == this.crmId && data.finishFlag == '0'){
                this.securityCheckShow = true;
              }
            } else {
              this.$alert(retMsg || '获取安全检查信息请求失败');
            }
          })
        } else {
          return
        }
      })
    },

    // 导航前往
    handleGoNavigation() {
      const curRegion = Storage.session.get('userInfo').region
      // Storage.session.get('userInfo').region
      const regionFind = LONG_CITY_LIST.find((v) => v.shortId == curRegion)
      let regionId = regionFind ? regionFind.id : '1000250'

      let subWgtUrl = `/subWgt?path=drive?type=2&areaLevel=1&areaId=${regionId}&shopLongitude=${this.lonBefore}&shopLatitude=${this.latBefore}&shopName=${btoa(
        encodeURIComponent(this.buildingDetail.objectName)
      )}&gobackFlag=webview`
      console.info(subWgtUrl);

      this.$router.push(subWgtUrl)
    },
    // 查询当前有没有签到
    async h5GridTodayUnSignOutQuery(){
      let params = {
        operMsisdn: this.userInfo.servNumber, // 手机号码
        sceneId: this.objectId,// 楼宇编码
        sceneType: 1, // 场景类型 1:楼宇 2:园区 3:酒店 4:商铺 5:道路
      }
      const res = await h5GridTodayUnSignOutQuery(params)
      console.info('查询签到',res)
      this.signInId = res ? res.signId : ""
      if (!this.signInId) {
          // 当前操作人员没有进行签到，查询当前楼宇的签到次数(判断楼宇是否已有人打卡，楼宇限制一天只能打卡一次)
          await this.signCountQuery();
      } else {
          this.isSign = true; // 已签到
          this.signOutFlag = false; // 未签出
      }
    },
    // 查询当前此楼宇的签到次数
    async signCountQuery() {
        const param = {
            sceneId: this.objectId,// 楼宇编码
            sceneType: 1, // 场景类型 1:楼宇 2:园区 3:酒店 4:商铺 5:道路
            operMsisdn: this.userInfo.servNumber, // 手机号码
        }
        const url = '/xsb/gridCenter/gridMap/h5GridSignCountQuery'
        const res = await this.$http.post(url, param)
        let { retCode, data } = res.data
        if (retCode === '0') {
            this.signCount = data ? data : 0;
        }
    },
    // 签到
    async h5GridSignInSubmit(latitude,longitude,address) {
      let params = {
        operMsisdn: this.userInfo.servNumber, // 手机号码
        operName: this.userInfo.operatorName,//
        operId: this.userInfo.crmId,// 操作人工号
        sceneId: this.objectId,// 楼宇id
        sceneName: this.buildingDetail2.buildName,// 场景名称
        sceneType: 1, // 场景类型 1:楼宇 2:园区 3:酒店 4:商铺 5:道路
        regionId: this.buildingDetail2.regionId,// 地市编码
        areaId: this.buildingDetail2.areaId,// 区县编码
        gridId: this.buildingDetail2.gridId,// 网格编码
        roadId: this.buildingDetail2.roadId,// 网格编码
        signInLatitude: latitude,// 签到经度
        signInLongitude: longitude,// 签到纬度
        signInAddress: address,// 签到地址
      }
      console.info('签到请求',params)
      const res = await h5GridSignInSubmit(params)
      if (res) {
        // 重新加载是否签到状态
        await this.h5GridTodayUnSignOutQuery()
      }
    },
    // 拉起打卡日志页面
    goDaka(){
      let subWgtUrl = `/subWgt?path=signLogList&sceneType=1&sceneId=${this.objectId}`
      this.$router.push(subWgtUrl)
    },

    // 打开我的二维码页
    goQrCode() {
      this.qrCodeVisible = true
    },

    // 关闭我的二维码页
    closeQrCode() {
      this.qrCodeVisible = false
    },

    // 二维码去签出
    qrCodeSignOut() {
      this.closeQrCode()
      this.signInOut()
    },

    // 保存个人二维码
    async handleSubmitQrCode() {
      let uinfo = Storage.session.get('userInfo')
      let url = '/xsb/gridCenter/personCode/h5AddPersonCodeInfo'
      this.qrCodeForm = {
        objectId: this.objectId,
        workId: this.workId ? this.workId : Storage.session.get('workId'),
        channelIdV: "92",
        scenariosName: this.buildingDetail.objectName,
        signName: uinfo.operatorName,
        signTelnum: uinfo.servNumber,
        signCrmId: uinfo.crmId,
        location: this.locationParam.address,
        signDistance: this.signDistance ? this.signDistance : 0,
        createdTime: this.signInTime ? this.signInTime : new Date(),
        linkAddress: '',
      }
      console.info(this.qrCodeForm, '保存个人二维码入参');

      const res = await this.$http.post(url, this.qrCodeForm)
      console.info(res, '保存个人二维码');

      if (res.data.retCode == '0') {
        this.isShowQrCode = true
      }
    },

    // 变更二维码有效状态
    async handleQrCodeState() {
      let uinfo = Storage.session.get('userInfo')
      const url = '/xsb/gridCenter/personCode/h5PersonCodeSignOut'
      console.info(
        {
          signCrmId: uinfo.crmId,
          workId: this.workId ? this.workId : Storage.session.get('workId'),
        },
        '变更二维码有效状态入参'
      )

      const res = await this.$http.post(url, {
        signCrmId: uinfo.crmId,
        workId: this.workId ? this.workId : Storage.session.get('workId'),
      })
      console.info(res, '变更二维码有效状态')

      const { retCode } = res.data
      if (retCode == '0') {
        this.isShowQrCode = false
        this.qrCodeVisible = false
        this.qrCodeForm = {}
      }
    },

    // 查询个人码明细
    async getQrCodeInfo() {
      let uinfo = Storage.session.get('userInfo')
      let workIdParam = this.workId ? this.workId : Storage.session.get('workId')
      console.info('查询个人码明细workIdParam', workIdParam);

      if (!workIdParam) return

      const params = {
        signCrmId: uinfo.crmId,
        workId: workIdParam,
        channelIdV: '92',
      }
      console.info(params, '查询个人码明细入参');

      let url = '/xsb/gridCenter/personCode/h5QryPersonCodeInfo'
      const res = await this.$http.post(url, params)
      console.info(res, '查询个人码明细');

      const { retCode, retMsg, data } = res.data

      if (retCode == '0') {
        if (data) {
          this.isShowQrCode = data.channelIdV == '92' ? true : false
          this.qrCodeForm = data.channelIdV == '92' ? data : {}
        } else {
          this.isShowQrCode = false
          this.qrCodeForm = {}
        }
      } else {
        this.isShowQrCode = false
        this.qrCodeForm = {}
      }
    },
    // 集团商机管理
    goGroupBusiManagePage() {
       // 去商机管理
       const item = {
           privId: '3625',
           privName: '商机管理',
           opId: 'jtopApp',
           opParentid: 'pgop'
       }
       this.goBusinessPage(item, 'shopsViewDetail')
    },
    goBusiPage(idx) {
       const url = this.busiReportRoutes[idx];
       this.$router.push(url);
    }
  }
}
</script>

<style lang="less" scoped>
@import '../../../base/less/variable.less';

.wrap-bv{
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  background: #F4F4F6;
  overflow: auto;
}
.top-frame{
  width: 100%;
  padding: 15px 0 0;
  box-sizing: border-box;
  background-color: #fff;
  margin-top: 44px;
}
.info-area{
  background-image:url("../../../../static/img/buildViewBg.png");
  width: 91%;
  height: 78px;
  background-repeat:no-repeat;
  background-size:100% 100%;
  padding: 15px;
  margin: 0 auto;
  box-sizing: border-box;
  position: relative;
}
.info-building-detail {
  width: 91%;
  margin: 18px auto 0;
  box-sizing: border-box;
  padding-bottom: 5px;
  .building-detail-titel {
    display: inline-block;
    width: 49%;
    font-size: 14px;
    color: #7B7B7B;
    margin-bottom: 10px;
    text-align: initial;
    vertical-align: top;
    word-break: break-all;
  }
  .building-detail-text {
    color: #000000;
  }
}
.building-detail-img-wrap {
  text-align: center;
}
.building-detail-img {
  width: 10px;
  height: 6px;
}
.building-detail-img-rotate {
  transform: rotate(180deg);
}
.building-detailed {
  color: #fff;
  font-size: 14px;
  width: 20%;
  margin-top: 5px;
}
.icon-signin {
  width: 16px;
  height: 16px;
  line-height: 25px;
  color: #fff;
  flex-shrink: 0;
  font-size: 14px;
  margin-right: 5px;
}
.building-name{
  font-size: 16px;
  color: #FFFFFF;

  .go_daohang {
    font-size: 12px;
    font-weight: normal;
    margin-left: 5px;

    .you {
      font-size: 12px;
    }
  }
}
.building-attribute {
  float: right;
  width: 64px;
  height: 30px;
  background: rgba(236,153,29,0.72);
  border-radius: 88px;
  font-size: 12px;
  text-align: center;
  color: #fff;
  line-height: 30px;
}
.building-add{
  position: absolute;
  bottom: 10px;
  left: 10px;
  padding: 3px 15px;
  border-radius: 10px;
  background-color: #fff;
  opacity: 70%;
  max-width: 160px;
  font-size: 14px;
  color: #1C1C1C;
}
.building-info-area{
  position: absolute;
  right: 0;
  height: 156px;
  width: 130px;
  top: 0;
  padding: 15px;
  box-sizing: border-box;
}
.info-li{
  justify-content: center;
  height: 43px;
  display: flex;
  flex-direction: column;
}
.info-name{
  color: #fff;
  font-size: 12px;
  display: block;
}
.info-value{
  color: #fff;
  font-size: 17px;
  display: block;
  text-shadow: 0px 3px 2px rgba(57, 57, 57, 0.23);
  font-weight: 400;
  margin-top: 2px;
}
.sign-photo-area{
  height: 70px;
  width: 91%;
  padding: 15px 15px  15px  30px;
  margin: 13px auto 0;
  display: flex;
  background-color: #fff;
  box-sizing: border-box;
  box-shadow: 0px 4px 8px 0px rgba(185, 185, 185, 0.14);
}
.sign-area{
  width: 50%;
  height: 100%;
  float: left;
  position: relative;
}
.text-font{
  font-size: 14px;
  font-weight: 600;
  color: #333333;
  line-height: 20px;
}
.sign-status{
  font-size: 10px;
  font-weight: 400;
  color: #9D9D9D;
  line-height: 14px;
  margin-top: 5px;
}
.sign-bottom{
  height: 30px;
  width: 30px;
  border-radius: 20px;
  border: 3px #448FED solid;
  position: absolute;
  top: 0px;
  right: 15px;
}
.sign-bottom.active{
  border: 3px #26B34E solid;
}
.sign-bottom.gry{
  border: 3px #7d7d7d solid;
}
.bottom-txt{
  font-size: 12px;
  color: #448FED;
  line-height: 31px;
  margin-left: 3px;
}
.bottom-txt.active{
  color: #26B34E;
}
.bottom-txt.gry{
  color: #7d7d7d;
}
.photo-area{
  border-left: 1px #ccc solid;
  width: 45%;
  height: 100%;
  float: right;
  position: relative;
  padding-left: 15px;
}
.explanation-text{
  display: block;
  font-weight: 400;
  color: #333333;
  line-height: 17px;
  font-size: 12px;
  margin-top: 5px;
}
.photo-icon{
  width: 38px;
  height: 38px;
  border: 1px #ABD0FF solid;
  border-radius: 20px;
  position: absolute;
  top: 0px;
  right: 10px;
  line-height: 38px;
  text-align: center;
}
.icon-xiangji{
  font-size: 18px;
  color: #448FED;
}
.message-show .mask{
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: 0.5;
  background: #000;
}
.message-box{
  position: fixed;
  top: 50%;
  left: 50%;
  -webkit-transform: translate3d(-50%, -50%, 0);
  transform: translate3d(-50%, -50%, 0);
  background-color: #fff;
  width: 85%;
  border-radius: 3px;
  font-size: 16px;
  -webkit-user-select: none;
  overflow: hidden;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-transition: .2s;
  transition: .2s;
  z-index:999;
}
.message-box .message-content h3{
  padding-top:15px;
  text-align: center;
  font-size: 16px;
  font-weight: 700;
  color: #333;
}
.message-content .content{
  padding: 20px 20px 15px;
  border-bottom: 1px solid #ddd;
  min-height: 36px;
  line-height:26px;
  position: relative;
}
.connecting-btn-load{
  display:inline-block;
  vertical-align:-2px;
  width:32px;
  padding-right: 8px;
}
.btn-group{
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 40px;
  line-height: 40px;
}

.btn-group1{
  display: -webkit-box;
  display: -ms-flexbox;
  height: 40px;
  line-height: 40px;
}
.btn-group1 .btn-primary{
  font-size:16px;
  line-height: 35px;
  display: block;
  background-color: #fff;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  //flex: 1;
  margin: 0;
  border: 0;
}

.btn-group .btn-default,
.btn-group .btn-primary{
  font-size:16px;
  line-height: 35px;
  display: block;
  background-color: #fff;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  //flex: 1;
  margin: 0;
  border: 0;
}
.btn-group .btn-default.btn-cancel {
  width: 50%;
  border-right: 1px solid #ddd;
}
.btn-group .btn-primary.btn-confirm{
  color:#26a2ff;
  width: 50%;
}
.camera-wrap {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  background: #ECF0FA;
  overflow: auto;
}
.bottom-area{
  width: 100%;
  padding: 0 10px;
  box-sizing: border-box;
  display: flex;
}
.association-group {
  width: 91%;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.05);
  margin: 7px auto 16px;
  /*height: calc(100vh - 62px);*/
}
.association-group-head {
  padding: 16px;
  border-bottom: 1px solid #F0F0F0;
  .association-group-info {
    display: flex;
    justify-content: space-between;
    height: 16px;
    .group-title-icons {
      display: inline-block;
      width: 4px;
      height: 14px;
      background: #007AFF;
      border-radius: 1px;
      vertical-align: middle;
    }
    .group-title-text {
      font-size: 14px;
      color: #000000;
      margin-right: 24px;
      vertical-align: middle;
    }
    .had-record {
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: #22BF47;
      vertical-align: middle;
    }
    .not-record {
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: #EC991D;
      margin-left: 10px;
      vertical-align: middle;
    }
    .record-text {
      font-size: 10px;
      color: #474747;
      vertical-align: middle;
    }
    .add-association-group {
      font-size: 12px;
      color: #1681FB;
      line-height: 16px;
    }
    .add-association-group-icon {
      font-size: 14px;
    }
  }
}
.association-group-body {
  max-height: calc(100vh - 180px);
  .floor-list {
    display: inline-block;
    width: 19%;
    min-height: 120px;
    border-right: 1px solid #F0F0F0;
    .floor-infos {
      width: 100%;
      height: 35px;
      font-size: 12px;
      text-align: center;
      line-height: 35px;
      color: #9E9E9E;
    }
    .floor-infos-focus {
      background-color: #EAF4FF;
      color: #007AFF;
    }
  }
  .association-list {
    display: inline-block;
    width: 79%;
    vertical-align: top;
    min-height: 120px;
    overflow: auto;
    .association-infos {
      padding-top: 9px;
      padding-left: 12px;
      .association-title {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
      }
      .association-title-left {
        flex: 1;
      }
      .had-record {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #22BF47;
        vertical-align: middle;
      }
      .not-record {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #EC991D;
        vertical-align: middle;
      }
      .association-text {
        vertical-align: middle;
        font-size: 14px;
        color: #000000;
      }
      .association-icon {
        vertical-align: middle;
        font-size: 14px;
        color: #1681FB;
      }
      .txt-more {
        width: 16px;
        height: 16px;
        margin-left: 10px;
        transform: rotate(90deg);
      }
      .household-number-title {
        margin-bottom: 10px;
        font-size: 12px;
        color: #626161;
      }
      .group-sign{
        background: #ffffff;
        border-radius: 5px;
        font-size: 12px;
        color: #1681fb;
        border: 1px solid #1681fb;
        margin-bottom: 10px;
        padding: 3px 3px;
        display: inline-flex;
      }
      .group-sign1 {
        color: #EC991D;
        border: 1px solid #EC991D;
      }
      .group-number-title {
        padding-bottom: 10px;
        font-size: 12px;
        color: #626161;
        border-bottom: 1px solid #F0F0F0;
      }
      .household-number {
        color: #000;
        word-break: break-all;
      }
    }
    .association-list-img {
      width: 50%;
      height: 50%;
      margin-left: 25%;
    }
    .association-list-img-text {
      text-align: center;
      font-size: 14px;
      line-height: 50px;
      margin-bottom: 60px;
      color: #000;
    }
  }

  .search-list {
      width: 98%;
  }
}
.association-group-img {
  width: 100%;
  height: 240px;
  text-align: center;
  .association-list-img {
    width: 50%;
    height: 50%;
    margin-top: 45px;
  }
  .association-group-img-text {
    font-size: 16px;
    color: #000;
  }
}
.menu-bottom{
  margin: 7px;
  padding: 15px 0 15px 15px;
  box-sizing: border-box;
  width: 46%;
  height: 70px;
  box-shadow: 0px 0px 13px 0px rgba(200, 200, 200, 0.39);
  background: #FFF;
  border-radius: 10px;
  position: relative;
  .shop-discounts {
    position: absolute;
    width: 34px;
    height: 40px;
  }
  img {
      display: inline-block;
      position: absolute;
      width: 34px;
      height: 40px;
  }
}
.icon-yingxiao{
  padding: 12px 7px 10px;
  color: #fff;
  background-color: #448FED;
  border-radius: 7px;
  position: absolute;
  font-size: 18px;
}
.icon-xinxicaiji{
  padding: 12px 8px 10px;
  color: #fff;
  background-color: #448FED;
  border-radius: 7px;
  position: absolute;
  font-size: 18px;
}
.icon-jituantuijian{
  padding: 9px 6px;
  color: #fff;
  background-color: #F29A4B;
  border-radius: 7px;
  position: absolute;
  font-size: 22px;
}
.icon-gerenwutuijian{
  padding: 11px 6px 9px 8px;
  color: #fff;
  background-color: #F29A4B;
  border-radius: 7px;
  position: absolute;
  font-size: 20px;
}
.icon-suishouji{
  padding: 9px 6px;
  color: #fff;
  background-color: #43D1C7;
  border-radius: 7px;
  position: absolute;
  font-size: 22px;
}
.icon-daosanjiao{
  padding: 9px 6px;
  color: #fff;
  background-color: rgb(204,204,102);
  border-radius: 7px;
  position: absolute;
  font-size: 22px;
}
.icon-shangji{
  padding: 9px 4px 11px 5px;
  color: #fff;
  background-color: #43D1C7;
  border-radius: 7px;
  position: absolute;
  font-size: 20px;
}
.marketing-note-back{
  position: fixed;
  left: 0;
  bottom: 0;
  top: 0;
  right: 0;
  opacity: 0.5;
  background: #000;
  z-index: 1000;
}
.marketing-note{
  position: absolute;
  height:auto;
  z-index: 1001;
  bottom: 0;
  width: 100%;
  height: 300px;
  background: #fff;
  border-top-left-radius: 25px;
  border-top-right-radius: 25px;
  text-align: center;
}
.bottom {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 60px;
  background: rgba(255, 255, 255, 1);
  display: flex;
  justify-content: space-around;
  align-items: center;

  button {
    display: block;
    height: 40px;
    border-radius: 22px;
    font-size: 16px;
    letter-spacing: 4px;
    color: rgba(255, 255, 255, 1);
    width: 40%;
    border: 0;
  }

  .submit {
    background: rgba(22, 129, 251, 1);
  }

  .redeploy {
    background: rgba(221, 221, 221, 1);
    color: #333;
  }
}
.marketing-note-textarea{
  font-size: 14px;
  height: 155px;
  overflow: hidden;
  outline: none;
  border: 0;
  resize: none;
  background: #F1F1F1;
  border-radius: 10px;
  width: 84%;
  padding: 15px;
}
.marketing-note-wittle{
  font-size: 16px;
  line-height: 46px;
}
.head {
  height: auto;
  overflow: hidden;
  position: fixed;
  top: 0px;
  left: 0px;
  right: 0px;
  z-index: 1;
}

.gl-title {
  height: 44px;
  text-align: center;
  position: relative;
  background: #fff;
}

.gl-back {
  position: absolute;
  left: 0px;
  top: 50%;
  font-size: 20px;
  padding: 8px;
  transform: translateY(-50%);
}

.gl-back:before {
  color: #007AFF;
}

.gl-title-txt {
  color: @color-TextTitle;
  display: inline-block;
  margin-top: 14px;
}


.txt-btn {
  width: auto;
  position: absolute;
  top: 0px;
  right: 10px;
  color: @color-TextNormal;
  line-height: 44px;
  font-size: 22px;
  transform: rotate(90deg);
  color: #007AFF;
}
.daka-btn{
  position: absolute;
  top:16px;
  right: 50px;
  font-size:12px;
}
.btn-group .btn-default.btn-cancel-out {
  width: 21%;
  border-right: 1px solid #ddd;
}
.btn-group .btn-default.btn-cancel-out.active{
  width: 50%;
}
.btn-group .btn-default.btn-update {
  width: 58%;
  border-right: 1px solid #ddd;
  color: #D88600;
  margin-top: 1px;
}
.btn-group .btn-default.btn-update.active {
  color:#918F8F;
}
.btn-group .btn-default.btn-update.maxactive{
  width: 50%;
}
.btn-group .btn-primary.btn-confirm-out{
  color:#26a2ff;
  width: 21%;
}
.btn-group .btn-primary.btn-confirm-out.active{
  width: 50%;
}


.end-detail{
  position: fixed;
  left: 0;
  bottom: 0;
  top: 0;
  right: 0;
  opacity: 0.5;
  background: #000;
  z-index: 1000;
}

.end-box{
  position: absolute;
  height:auto;
  z-index: 1001;
  bottom: 0;
  width: 100%;
  /*height: 300px;*/
  background: #fff;
  border-top-left-radius: 25px;
  border-top-right-radius: 25px;

  .end-box-tittle{
    font-size: 16px;
    line-height: 46px;
    text-align: center;
  }
  .end-box-top{
    margin: 0 0 10px 0;
    padding: 6px 0 0 15px;
    font-size: 16px;
  }
  .content{
    padding: 6px 0 0 15px;
    display: flex;
    flex-direction: column;
    margin-bottom: 60px;
    font-size: 16px;
    .end-box-choose{
      margin: 0 0 10px 0;
      display: inline-flex;
      height: 17px;
      .left{
        margin: 0 6px 0 0;
        .noChoose {
          width: 14px;
          height: 14px;
          border: 1px solid #333;
          border-radius: 50%;
          display: inline-block;
        }
        .wancheng {
          font-size: 16px;
          color: #6BCE59;
        }
      }

    }
  }
  .end-box-content{
    .end-note-textarea{
      font-size: 14px;
      height: 127px;
      overflow: hidden;
      outline: none;
      border: 0;
      resize: none;
      background: #F1F1F1;
      border-radius: 10px;
      width: 84%;
      padding: 15px;
    }
  }

  .end-bottom {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 60px;
    background: rgba(255, 255, 255, 1);
    display: flex;
    justify-content: space-around;
    align-items: center;

    button {
      display: block;
      height: 40px;
      border-radius: 22px;
      font-size: 16px;
      letter-spacing: 4px;
      color: rgba(255, 255, 255, 1);
      width: 40%;
      border: 0;
    }

    .end-submit {
      background: rgba(22, 129, 251, 1);
    }

    .end-redeploy {
      background: rgba(221, 221, 221, 1);
      color: #333;
    }
  }

  .end-time{
    margin: 10px 27px 6px 17px;
    display: flex;
    justify-content: space-between;
    font-size: 15px;
  }
}

.jiantou-copy-copy{
  font-size: 13px;
  color: #0E0E0E;
}

.model{
  margin: 54px 10px 0 10px;
  background-color: #fff;
  border-radius: 6px;
  width: 100%;
  position: fixed;
  z-index: 1;
  &>div {
    margin: 0 7px;
  }
}

.plan-model{
  position: absolute;
  right: 8px;
  top: -16px;
  width:81px;
  background:#6380B1;
  border-top-left-radius:10px;
  border-top-right-radius:10px;
  border-bottom-right-radius:10px;
  border-bottom-left-radius:10px;
  z-index: 2;
  padding:6px 0;
  box-sizing: border-box;
  text-align: center;
  .sanjian{
    width:8px;
    height:8px;
    background:#6380B1;
    position:absolute;
    top:-3px;
    right:23px;
    transform:rotate(45deg);
  }
  .more-input{
    width: 28px;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #FFFFFF;
    line-height: 25px;
    margin: 3px;
  }
  .bar{
    width: 68px;
    height: 1px;
    background: #fff;
    margin-block:6px;
    margin-left: 6px;
  }
}
.search-wrapper {
  height: 50px;
  padding: 12px 0;
  box-sizing: border-box;
  background: #fff;

  .ipt-wrapper {
    width: 100%;
    height: 32px;
    line-height: 32px;
    border-radius: 16px;
    background: rgba(241, 241, 241, 1);
    padding: 0 12px;
    box-sizing: border-box;
    position: relative;

    .seach-ipt {
      font-size: 12px;
      width: 100%;
      height: 28px;
      padding-left: 20px;
      padding-right: 40px;
      background: rgba(241, 241, 241, 1);
      box-sizing: border-box;

      &:focus {
        outline: none;
      }
    }

    .sousuo1 {
      position: absolute;
      left: 12px;
      top: 50%;
      color: #828282;
      transform: translateY(-50%);
    }

    .query-btn {
      position: absolute;
      top: 50%;
      color: #007aff;
      right: 12px;
      transform: translateY(-50%);
      font-size: 12px;
    }
    .clear-btn {
        position: absolute;
        right: 56px;
        color: #9E9E9E;
    }
  }
}
.filter-criteria {
  margin-top: 8px;
  display: flex;
  justify-content: space-between;
  .filter-criteria-left{
    display: flex;
    width: 75%;
    overflow-y: hidden;
    overflow-x: auto; /* 启用水平滚动 */
    white-space: nowrap; /* 防止内容换行 */
    .padding-left-sm{
      padding-left:10px;
    }
  }
  .filter-text {
    font-size: 12px;
    color: #9E9E9E;
  }
  .active{
    border: 1px #1680F9 solid;
    color:#1680F9;
    height: 14px;
  }
  .filter-state {
    color: #007AFF;
  }
  .filter-icon {
    font-size: 12px;
    color: #9E9E9E;
    margin-left: 3px;
    vertical-align: middle;
  }
  .filter-icon-state {
    font-size: 10px;
    //color: #007AFF;
    margin-left: 1px;
    vertical-align: middle;
  }
  .text-right{
    text-align: right;
  }
}

.topFlag {
  position: fixed;
  top: 40px;
}
.chooseDialog {
  position: fixed;
  z-index: 999;
  background: #fff;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  padding: 12px;
  border-radius: 8px;
  box-shadow: 2px 2px 6px 0px #747576;
  width: 260px;

  .title {
    font-size: 18px;
    line-height: 26px;
    color: #0b7fff;
    text-align: center;
    border-bottom: 2px solid #1681FB;
  }

  .info {
    padding: 30px 0;
    font-size: 14px;
    line-height: 30px;
    margin-top: 8px;
    text-align: center;
  }

}
.hidebox {
  top: 0;
  position: fixed;
  z-index: 998;
  width: 100vw;
  height: 100vh;
  background: rgba(0,0,0,.3);
}

.qr_code_layer {
  position: fixed;
  bottom: 50px;
  right: 0;
  z-index: 999;
  width: 65px;
  height: 50px;
  border-radius: 35px 0 0 35px;
  background: #fff;
  text-align: center;
  line-height: 50px;
  box-shadow: 0px 3px 0px 3px rgba(0, 0, 0, 0.07);

  .erweima {
    font-size: 30px;
    color: green;
  }
}
.busi-mask {
    position: fixed;
    left: 0;
    bottom: 0;
    top: 0;
    right: 0;
    background: rgba(0, 0, 0, .7);
    z-index: 999;
}

.overlay-wrapper {
    position: relative;
    height: 100%;
    width: 100%;
    display: flex;
    justify-content: center;

    .block {
        position: absolute;
        top: 36%;
        width: 80%;
        height: auto;
        padding: 12px 15px 15px 15px;
        background: #FFF;
        border-radius: 6px;
    }
    .header {
        text-align: center;
        font-size: 15px;
        margin-bottom: 10px;
        color: #222;
        font-weight: 500;
        .close-btn {
            float: right;
            font-size: 13px;
            color: #222;
        }
    }

    .busi-items {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        margin-top: 10px;
        div {
            flex: 1;
            font-size: 14px;
            height: 36px;
            line-height: 36px;
            text-align: center;
            color: #0D80FD;
            background-color: #e0edf8;
            border-radius: 5px;

            &:first-child {
                margin-right: 10px;
            }
            &:last-child {
                margin-left: 10px;
            }
        }
    }
}
</style>
