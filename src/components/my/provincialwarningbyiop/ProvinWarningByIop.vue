<template>
    <div class="pwd-wrapper">

        <Header tsTitleTxt="客户脉动感知服务系统" backType="custom" @emGoPrev="goPrev"></Header>

        <div class="main-info">
            <!--任务主体信息-->
            <div class="task-details">
                <!--客户信息-->
                <div class="task-user">
                    <div class="task-left">
                        <img src="static/img/yiban.png" class="task-img" alt='' v-if="warnLevel == 0"/>
                        <img src="static/img/jinji.png" class="task-img" alt='' v-if="warnLevel == 1"/>
                        <img src="static/img/feichangjinji.png" class="task-img" alt='' v-if="warnLevel == 2"/>
                        <div class="top-right">
                            <span class="iconfont shijian2 timedeal"></span>
                            {{limitDate |dateShow('yyyy/MM/dd hh:mm',true) }}</div>
                        <div class="task-center">
                            <div class="user-info" @click="goPerson">
                                <span class="user-num">{{mobile | starTel}}</span>
                                <span class="iconfont youjiantou2 jiantou2icon"></span>
                            </div>

                            <div class="task-number">
                                <span class="label-info-type" v-if="userValue">{{userValue}}</span>
                                <span class="label-info-type" v-if="areaName">{{areaName}}</span>
                                <span class="label-info-type" v-if="custName">{{custName}}</span>
                                <span class="label-info-type" v-if="ysydDtl">{{ysydDtl}}</span>
                            </div>
                        </div>
                    </div>
                    <div>
                        <div class="iconfont xingzhuang7 iconwaihu" @click="showButtom">
                            <div class="choose-box" v-if="showFlag">
                                <div class="plan-model">
                                    <div class="butto-waihu" @click.stop="benjiCall">本机外呼</div>
                                    <div class="butto-waihu" :class="{'txt-grey':!showCallButtomFlag}"  @click.stop="waiHuProWarn">中间号外呼</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!--业务预警信息-->
                <div class="yw-details">
                    <div v-for="(item,index) in yjLabelDtl" :key="index" class="yw-list">
                        <div class="first-info">
                            <div class="first-left">
                                <span class="first-before"></span>
                                <span class="first-type">
                                    {{item.labelName}}
                                    <span class="first-time" v-show="item.createTime">({{item.createTime |dateShow('yyyy/MM/dd hh:mm',true) }})</span>
                                </span>
                            </div>
                        </div>
                        <div class="info-deal">
                            <div class="move-label-info" v-show="item.source">
                                <span class="yw-name">来源:&nbsp;&nbsp;</span>
                                <span class="yw-label">{{item.source}}</span>
                            </div>
                            <div class="move-label-info" v-show="item.category">
                                <span class="yw-name">类别:&nbsp;&nbsp;</span>
                                <span class="yw-label">{{item.category}}</span>
                            </div>
                        </div>

                        <div class="move-label-info" v-show="item.labelRemark">
                            <span class="yw-name">备注:&nbsp;&nbsp;</span>
                            <span class="yw-label">{{item.labelRemark}}</span>
                        </div>

                        <div v-if="item.yjLabelAttr && item.yjLabelAttr.length > 0" v-for="(itm1,idx1) in item.yjLabelAttr" :key="idx1">
                            <div class="move-label-info" v-show="itm1.yjLabelValue">
                                <span class="yw-name">{{itm1.yjLabelName}}:&nbsp;&nbsp;</span>
                                <span class="yw-label">{{itm1.yjLabelValue}}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--历史跟进信息-->
            <div class="move-title" v-show="yjTaskHis && yjTaskHis.length > 0">
                <div class="move-title-info">工单轨迹（{{yjTaskHis.length}}）</div>
                <div class="move-history" @click="goHistory('1','工单轨迹')" v-show="yjTaskHis.length > 1">
                    查看更多
                    <span class="iconfont youjiantou2 jiantou2icon"></span>
                </div>
            </div>
            <div class="his-info" v-show="yjTaskHis && yjTaskHis.length > 0">
                <div v-for="(item,index) in yjTaskHis.slice(0,1)" :key="index" class="his-list">
                    <div class="his-xiang">
                        <div class="his-first">
                            <span class="iconfont checkboxround0 icon-checkbox checkbox2"></span>
                            <span class="his-time" v-show="item.dealDate">{{item.dealDate | timeFiler}}&nbsp;,&nbsp;</span>

                            <span v-show="item.dealName || item.dealMobile">
                                <span class="his-time" v-show="item.dealName">{{item.dealName}}</span>
                                <span class="his-time" v-show="item.dealMobile">({{item.dealMobile}})</span>
                            </span>
                            <span v-show="!(item.dealName || item.dealMobile)">
                                 <span class="his-time" v-show="item.dealOperator">{{item.dealOperator}}</span>
                            </span>
                        </div>

                        <div class="his-label-info" v-show="item.warnType">
                            <span class="his-name">预警类型:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.warnType}}</span>
                        </div>
                        <div class="his-label-info" v-show="item.submitSource">
                            <span class="his-name">处理渠道:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.submitSource | submitSourceFilter}}</span>
                        </div>
                        <div class="his-label-info" v-show="item.dealAction">
                            <span class="his-name">处理动作:&nbsp;&nbsp;</span>
                            <span class="his-label" v-show="item.dealAction == '0' && item.dealActionFeedback == '0'">立单</span>
                            <span class="his-label" v-show="item.dealAction == '1'">转派</span>
                            <span class="his-label" v-show="item.dealAction == '2' && item.dealActionFeedback == '1'">跟进</span>
                            <span class="his-label" v-show="item.dealAction == '2' && item.dealActionFeedback == '2'">结单</span>
                            <span class="his-label" v-show="item.dealAction == '2' && item.dealActionFeedback == '4'">IOP自动结单</span>
                            <span class="his-label" v-show="item.dealAction == '3'">追加反馈</span>
                            <span class="his-label" v-show="item.dealAction == '4'">一体化外呼</span>
                            <span class="his-label" v-show="item.dealAction == '5'">阿拉盯跟进</span>
                            <span class="his-label" v-show="item.dealAction == '6'">在线跟进</span>
                            <span class="his-label" v-show="item.dealAction == '7'">在线撤单</span>
                            <span class="his-label" v-show="item.dealAction == '8'">工单调派</span>
                            <span class="his-label" v-show="item.dealAction == '9'">添加意见</span>
                        </div>

                        <!--在线系统-->
                        <div class="his-label-info"  v-show="item.reminderRemark">
                            <span class="his-name">催办提醒内容:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.reminderRemark}}</span>
                        </div>
                        <div class="his-label-info"  v-show="item.userDemandRemark">
                            <span class="his-name">追加诉求内容:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.userDemandRemark}}</span>
                        </div>

                        <!--一体化系统-->
                        <div class="his-label-info"  v-show="item.processResultId">
                            <span class="his-name">子任务单办理结果名称:&nbsp;&nbsp;</span>
                            <span class="his-label" v-show="item.processResultId == '0'">外呼失败</span>
                            <span class="his-label" v-show="item.processResultId == '1'">外呼成功</span>
                        </div>
                        <div class="his-label-info"  v-show="item.callState">
                            <span class="his-name">是否接通:&nbsp;&nbsp;</span>
                            <span class="his-label" v-show="item.callState == '0'">未接通</span>
                            <span class="his-label" v-show="item.callState == '1'">接通</span>
                        </div>
                        <div class="his-label-info"  v-show="item.talkTime">
                            <span class="his-name">通话时长:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.talkTime}}s</span>
                        </div>
                        <div class="his-label-info"  v-show="item.offeringName">
                            <span class="his-name">实际办理产品:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.offeringName}}</span>
                        </div>
                        <div class="his-label-info"  v-show="item.yjFailReason">
                            <span class="his-name">预警外呼失败原因:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.yjFailReason | yjFailReasonFilter }}</span>
                        </div>

                        <div class="his-label-info" v-show="item.followMsisdn">
                            <span class="his-name">跟进人：</span>
                            <span class="his-label">{{item.followName}}<span v-show="item.followMsisdn">({{item.followMsisdn}})</span></span>
                        </div>
                        <div class="his-label-info"  v-show="item.followDealType">
                            <span class="his-name">预约方式:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.followDealType}}</span>
                        </div>
                        <div class="his-label-info"  v-show="item.dueTime">
                            <span class="his-name">预约时间:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.dueTime | timeFiler}}</span>
                        </div>
                        <div class="his-label-info"  v-show="item.visitAddr">
                            <span class="his-name">上门地址:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.visitAddr}}</span>
                        </div>
                        <div class="his-label-info"  v-show="item.dueHallName">
                            <span class="his-name">预约厅店:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.dueHallName}}</span>
                        </div>
                        <div class="his-label-info" v-show="item.contactsName">
                            <span class="his-name">客户侧联系人：</span>
                            <span class="his-label">{{item.contactsName}}<span v-show="item.contactsMsisdn">({{item.contactsMsisdn}})</span></span>
                        </div>


                        <div class="his-label-info" v-show="item.zpReceiveMobile">
                            <span class="his-name" v-show="item.dealAction == '1'">转派接收人：</span>
                            <span class="his-name" v-show="item.dealAction == '2'">上门协同人员：</span>
                            <span class="his-name" v-show="item.dealAction == '8'">调配接收人：</span>
                            <span class="his-label">{{item.zpReceiveName}}<span v-show="item.zpReceiveMobile">({{item.zpReceiveMobile}})</span></span>
                        </div>
                        <div class="his-label-info"  v-show="item.dealType">
                            <span class="his-name">处理方式:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.dealType}}</span>
                        </div>
                        <div class="his-label-info"  v-show="item.clockAddress">
                            <span class="his-name">上门地址:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.clockAddress}}</span>
                        </div>
                        <div class="his-label-info"  v-show="item.distance">
                            <span class="his-name">结单距离:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.distance}}米</span>
                        </div>
                        <div class="his-label-info" v-show="item.abnormalFlag">
                            <span class="his-name">异动情况:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.abnormalFlag}}</span>
                        </div>
                        <div class="his-label-info" v-show="item.abnormalType">
                            <span class="his-name">异动类型:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.abnormalType}}</span>
                        </div>

                        <div v-show="item.evidenceList && item.evidenceList.length > 0" v-for="(itm1,idx1) in item.evidenceList" :key="idx1">
                            <div class="his-label-info" v-show="itm1.evidenceType">
                                <span class="his-name">取证类型{{idx1+1}}:&nbsp;&nbsp;</span>
                                <span class="his-label">{{itm1.evidenceType}}</span>
                            </div>
                            <div class="his-label-info" v-show="itm1.evidenceOtherRemark">
                                <span class="his-name">补充说明{{idx1+1}}:&nbsp;&nbsp;</span>
                                <span class="his-label">{{itm1.evidenceOtherRemark}}</span>
                            </div>
                            <div class="his-label-info" v-if="itm1.evidenceKeys">
                                <span class="his-name">取证上传信息{{idx1+1}}:&nbsp;&nbsp;</span>
                                <span class="his-label" v-show="itm1.audioShowList && itm1.audioShowList.length > 0">{{itm1.audioShowList.join(",")}}</span>
                                <span>
                                    <div>
                                        <ul class="photo-ul">
                                            <li class="photo-li" :key="idx"
                                                v-for="(item1,idx) in itm1.photoShowList"
                                                v-show="item1.indexOf('http') > 0 " @click="zoomStorePhoto(item1)">
                                                <div class="img-wrap">
                                                    <img :src="item1" :ref="'YW'+idx"/>
                                                </div>
                                            </li>
                                        </ul>
                                        <ul class="video-ul">
                                            <li class="video-li" :key="idx"
                                                v-for="(item1,idx) in itm1.videoShowList"
                                                v-show="item1.indexOf('http') > 0 ">
                                                <div class="img-wrap">
                                                    <video ref="videoRef"
                                                           style="width:100%;max-height:200px;min-height: 200px; background: #000"
                                                           controls
                                                           controlslist="nodownload">
                                                        <source type="video/mp4" :src="item1"/>
                                                    </video>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                </span>
                            </div>

                        </div>


                        <div class="his-label-info" v-show="item.rightFlag">
                            <span class="his-name">预警信息是否准确:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.rightFlag}}</span>
                        </div>
                        <div class="his-label-info" v-show="item.abnormalBasis">
                            <span class="his-name">判断依据:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.abnormalBasis}}</span>
                        </div>
                        <div class="his-label-info" v-show="item.dealResult">
                            <span class="his-name">维护结果:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.dealResult}}</span>
                        </div>
                        <div class="his-label-info" v-show="item.fakerDesc">
                            <span class="his-name">反杀详情:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.fakerDesc}}</span>
                        </div>
                        <div class="his-label-info" v-show="item.fakerOtherDesc">
                            <span class="his-name">反杀详情说明:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.fakerOtherDesc}}</span>
                        </div>
                        <div class="his-label-info" v-if="item.fakerKeys">
                            <span class="his-name">反杀证据:&nbsp;&nbsp;</span>
                            <span class="his-label" v-show="item.audioShowList22 && item.audioShowList22.length > 0">{{item.audioShowList22.join(",")}}</span>
                            <span>
                                <div>
                                    <ul class="photo-ul">
                                        <li class="photo-li" :key="idx"
                                            v-for="(item1,idx) in item.photoShowList22"
                                            v-show="item1.indexOf('http') > 0 " @click="zoomStorePhoto(item1)">
                                            <div class="img-wrap">
                                                <img :src="item1" :ref="'YW'+idx"/>
                                            </div>
                                        </li>
                                    </ul>
                                    <ul class="video-ul">
                                        <li class="video-li" :key="idx"
                                            v-for="(item1,idx) in item.videoShowList22"
                                            v-show="item1.indexOf('http') > 0 ">
                                            <div class="img-wrap">
                                                <video ref="videoRef"
                                                       style="width:100%;max-height:200px;min-height: 200px; background: #000"
                                                       controls
                                                       controlslist="nodownload">
                                                    <source type="video/mp4" :src="item1"/>
                                                </video>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                            </span>
                        </div>


                        <div class="his-label-info" v-show="item.defeatReason">
                            <span class="his-name">失败原因大类:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.defeatReason}}</span>
                        </div>
                        <div class="his-label-info" v-show="item.defeatRemark">
                            <span class="his-name">失败原因小类:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.defeatRemark}}</span>
                        </div>
                        <div class="his-label-info" v-show="item.defeatOtherDesc">
                            <span class="his-name">失败原因说明:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.defeatOtherDesc}}</span>
                        </div>

                        <!--失败原因-->
                        <div v-show="item.defeatReasonList && item.defeatReasonList.length > 0" v-for="(itm2,idx2) in item.defeatReasonList" :key="idx2">
                            <div class="his-label-info" v-show="itm2.defeatReason">
                                <span class="his-name">失败原因{{idx2+1}}:&nbsp;&nbsp;</span>
                                <span class="his-label">{{itm2.defeatReason}}-{{itm2.defeatRemark}}</span>
                            </div>
                            <div class="his-label-info" v-show="itm2.defeatOtherDesc">
                                <span class="his-name">失败原因补充说明{{idx2+1}}:&nbsp;&nbsp;</span>
                                <span class="his-label">{{itm2.defeatOtherDesc}}</span>
                            </div>
                        </div>

                        <!--图片-->
                        <div class="his-label-info" v-if="item.photoKeys">
                            <span class="his-name">照片:&nbsp;&nbsp;</span>
                            <span>
                                <div>
                                    <ul class="photo-ul">
                                        <li class="photo-li" :key="idx"
                                            v-for="(item1,idx) in item.photoKeys.split(',')"
                                            v-show="item1.indexOf('http') > 0" @click="zoomStorePhoto(item1)">
                                            <div class="img-wrap">
                                                <img :src="item1" :ref="'YW'+idx"/>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                            </span>
                        </div>

                        <!--最新推荐政策-->
                        <div class="his-label-info" v-show="item.latestRecommendPolicy">
                            <span class="his-name">最新推荐政策:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.latestRecommendPolicy}}</span>
                        </div>
                        <div class="his-label-info" v-show="item.customerIsAccept">
                            <span class="his-name">客户是否接受:&nbsp;&nbsp;</span>
                            <span class="his-label" v-if="item.customerIsAccept == '1'">是</span>
                            <span class="his-label" v-else>否</span>
                        </div>

                        <div class="his-label-info" v-show="item.remark">
                            <span class="his-name" v-show="item.dealAction == '1'">转派意见:&nbsp;&nbsp;</span>
                            <span class="his-name" v-show="item.dealAction == '2'">处理反馈意见:&nbsp;&nbsp;</span>
                            <span class="his-name" v-show="item.dealAction == '3'">追加反馈信息:&nbsp;&nbsp;</span>
                            <span class="his-name" v-show="item.dealAction == '5'">跟进备注:&nbsp;&nbsp;</span>
                            <span class="his-name" v-show="item.dealAction == '8'">调配意见:&nbsp;&nbsp;</span>
                            <span class="his-name" v-show="item.dealAction == '9'">处理意见:&nbsp;&nbsp;</span>
                            <span class="his-name" v-show="!item.dealAction">备注:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.remark}}</span>
                        </div>
                        <div class="his-label-info" v-if="item.zjlFileKeys">
                            <span class="his-name">证据链:&nbsp;&nbsp;</span>
                            <span class="his-label" v-show="item.audioShowList && item.audioShowList.length > 0">{{item.audioShowList.join(",")}}</span>
                            <span>
                                <div>
                                    <ul class="photo-ul">
                                        <li class="photo-li" :key="idx"
                                            v-for="(item1,idx) in item.photoShowList"
                                            v-show="item1.indexOf('http') > 0 " @click="zoomStorePhoto(item1)">
                                            <div class="img-wrap">
                                                <img :src="item1" :ref="'YW'+idx"/>
                                            </div>
                                        </li>
                                    </ul>
                                    <ul class="video-ul">
                                        <li class="video-li" :key="idx"
                                            v-for="(item1,idx) in item.videoShowList"
                                            v-show="item1.indexOf('http') > 0 ">
                                            <div class="img-wrap">
                                                <video ref="videoRef"
                                                       style="width:100%;max-height:200px;min-height: 200px; background: #000"
                                                       controls
                                                       controlslist="nodownload">
                                                    <source type="video/mp4" :src="item1"/>
                                                </video>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                            </span>
                        </div>


                    </div>
                </div>
            </div>

            <!--历史轨迹-->
            <div class="move-title">
                <div class="move-title-info">业务切片</div>
                <div class="move-history" @click="goHistory('2','业务切片')">
                    查看明细
                    <span class="iconfont youjiantou2 jiantou2icon"></span>
                </div>
            </div>

            <!--降档挽留-->
            <div class="move-title" v-show="showDownFlg">
                <div class="move-title-info">挽留政策</div>
                <div class="move-history" @click="goDownInfo" v-show="showDownFlg">
                    查看明细
                    <span class="iconfont youjiantou2 jiantou2icon"></span>
                </div>
            </div>

            <!--本次跟进处理-->
            <div class="move-title">
                <div class="move-title-info">本次跟进处理</div>
            </div>
            <div class="order-handle">
                <div class="order-tar" >
                    <div :class="{choosed:isShow == '3'}" @click="isShows('3')" ><span class="iconfont zhuanpai" ></span>转派</div>
                    <div :class="{choosed:isShow == '1'}" @click="isShows('1')" ><span class="iconfont yuchuli" ></span>跟进</div>
                    <div :class="{choosed:isShow == '2'}" @click="isShows('2')"><span class="iconfont chulifankui"></span>结单</div>
                </div>
                <!--转派-->
                <div v-show="isShow == '3'">
                    <div class="handle-details">
                        <div class="handle-xiang">
                            <div class="handle-label" :class="{active:true}">选择派单接收人<span>*</span></div>
                            <div class="handle-value" @click="transfer()">
                                <span class="zi-ti" v-show="zpReceiveName == ''">请选择</span>
                                <span class="zi-ti">{{zpReceiveName}}</span>
                                <span class="iconfont jiantou-copy-copy"></span>
                            </div>
                        </div>
                    </div>
                </div>
                <!--结单-->
                <div v-show="isShow == '2'">
                    <div class="handle-details">
                        <div class="handle-xiang">
                            <div class="handle-label" :class="{active:true}">处理方式<span>*</span></div>
                            <div class="handle-value" >
                                <span v-for="(item,index) in dealTypeList" :key="index" @click.stop="chooseDealType(item)">
                                    <span class="iconfont checkboxround0 icon-checkbox" :class="{'checkbox2':item.chooseFlag}"></span>
                                    <span class="choose-zoti">{{item.label}}</span>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div v-show="dealType.indexOf('上门') >= 0">
                        <div class="handle-details" >
                            <div class="handle-xiang">
                                <div class="handle-label">上门地址<span>*</span></div>
                                <div class="handle-value" style="width: 70%;">
                                    <input class="benren none" type="text" placeholder="请输入" v-model="clockAddress" disabled>
                                    <span class="iconfont zb fresh" @click="openLocal"></span>
                                </div>
                            </div>
                        </div>
                        <div class="handle-details">
                            <div class="handle-xiang">
                                <div class="handle-label">现场照片<span>*</span></div>
                                <div class="handle-value" @click="openCamTwo">
                                    <span class="zi-ti" v-show="!isCameraTwo">未拍照</span>
                                    <span class="zi-ti" v-show="isCameraTwo">已拍照</span>
                                    <span class="iconfont zu iconphtot"></span>
                                </div>
                            </div>
                        </div>
                        <div class="handle-details">
                            <div class="handle-xiang">
                                <div class="handle-label">上门协同人员</div>
                                <div class="handle-value" @click="transferByDeal()">
                                    <span class="zi-ti" v-show="smDealName == ''">请选择</span>
                                    <span class="zi-ti">{{smDealName}}</span>
                                    <span class="iconfont jiantou-copy-copy"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!--跟进-->
                <div v-show="isShow == '1'">
                    <div class="handle-details" >
                        <div class="handle-xiang">
                            <div class="handle-label">跟进人<span>*</span></div>
                            <div class="handle-value" >
                                <input class="input-text" type="text" placeholder="请输入" v-model="followName">
                            </div>
                        </div>
                    </div>
                    <div class="handle-details" >
                        <div class="handle-xiang">
                            <div class="handle-label">跟进人电话<span>*</span></div>
                            <div class="handle-value" >
                                <input class="input-text" type="tel" placeholder="请输入" v-model="followMsisdn" maxlength="11" @input="numJude('1')">
                            </div>
                        </div>
                    </div>
                    <div class="handle-details" >
                        <div class="handle-xiang">
                            <div class="handle-label">客户侧联系人</div>
                            <div class="handle-value" >
                                <input class="input-text" type="text" placeholder="请输入" v-model="contactsName">
                            </div>
                        </div>
                    </div>
                    <div class="handle-details" >
                        <div class="handle-xiang">
                            <div class="handle-label">客户侧联系电话</div>
                            <div class="handle-value" >
                                <input class="input-text" type="tel" placeholder="请输入" v-model="contactsMsisdn" maxlength="11" @input="numJude('2')">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--转派-->
            <div class="order-handle" v-show="isShow == '3'">
                <div class="handle-details">
                    <div class="handle-xiang">
                        <div class="handle-label" :class="{active:true}">异动情况</div>
                        <div class="handle-value" >
                            <button class="flagButton" :class="abnormalFlag2== '异动'?'checkflag': ''" @click.stop="changeIsYc2('异动')">异动</button>
                            <button class="flagButton" :class="abnormalFlag2== '无异动'?'checkflag': ''" @click.stop="changeIsYc2('无异动')">无异动</button>
                        </div>
                    </div>
                </div>
                <div v-show="abnormalFlag2 == '异动'">
                    <!--下拉框-->
                    <div class="handle-details" >
                        <div class="handle-xiang">
                            <div class="handle-label">异动类型</div>
                            <div class="xialakuang-111" @click="errorTypeChoose2">
                                <span class="zi-ti" v-if="abnormalType2 != ''">{{abnormalType2}}</span>
                                <span class="zi-ti" v-else>请选择异动类型</span>
                                <span class="iconfont jiantou-copy-copy iconphtot11"></span>
                            </div>
                        </div>
                    </div>

                    <div class="handle-details">
                        <div class="handle-xiang">
                            <div class="handle-label">异动取证</div>
                            <div class="xialakuang-111" @click="addEviByTher">
                                <span class="iconfont jiahao1 iconphtot11"></span>
                            </div>
                        </div>
                    </div>
                    <div v-for="(item,index) in eviListByTher" :key="index">
                        <div class="handle-details">
                            <div class="handle-xiang">
                                <div class="handle-label">
                                    <span class="iconfont check_box shanchu-icon" @click.stop="deleteEvi(index)"></span>取证类型
                                </div>
                                <div class="xialakuang-111" @click="evidenceTypeChoose(item)">
                                    <span class="zi-ti" v-if="item.evidenceType != ''">{{item.evidenceType}}</span>
                                    <span class="zi-ti" v-else>请选择取证类型</span>
                                    <span class="iconfont jiantou-copy-copy iconphtot11"></span>
                                </div>
                            </div>
                        </div>
                        <div class="handle-details" v-show="item.evidenceType == '其他'">
                            <div class="handle-xiang">
                                <div class="handle-label">
                                    <span class="iconfont check_box wihte-icon"></span>取证类型说明
                                </div>
                                <div class="handle-value">
                                    <input class="input-text" type="text" v-model="item.evidenceOtherRemark" maxlength="50" placeholder="请输入具体取证类型">
                                </div>
                            </div>
                        </div>
                        <div class="handle-details">
                            <div class="handle-xiang">
                                <div class="handle-label">
                                    <span class="iconfont check_box wihte-icon"></span>取证上传
                                </div>
                                <div class="handle-value" @click="openCamEvi(item,index)">
                                    <span class="zi-ti" v-show="!item.isCamera">未拍摄</span>
                                    <span class="zi-ti" v-show="item.isCamera">已拍摄</span>
                                    <span class="iconfont jiantou-copy-copy iconphtot11"></span>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>

                <div v-show="abnormalFlag2 == '无异动'">
                    <div class="handle-details">
                        <div class="handle-xiang">
                            <div class="handle-label">预警信息是否准确</div>
                            <div class="handle-value" >
                                <button class="flagButton" :class="rightFlag2== '准确'?'checkflag': ''" @click.stop="changeIsRightFlag2('准确')">准确</button>
                                <button class="flagButton" :class="rightFlag2== '不准确'?'checkflag': ''" @click.stop="changeIsRightFlag2('不准确')">不准确</button>
                            </div>
                        </div>
                    </div>
                    <div class="handle-details">
                        <div class="remark" style="margin-bottom: 40px">
                            <div class="text-abl">{{abnormalBasis2.length}}/500</div>
                            <textarea type="text" placeholder="请简述您判断的依据" v-model="abnormalBasis2" maxlength="500"></textarea>
                            <div class="button" @touchstart='handlerTouchstart' @touchend='handlerTouchend'>
                                <div>语音录入<span class="luyin iconfont"></span></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="order-handle" v-show="isShow == '3'">
                <div class="handle-details" >
                    <div class="handle-xiang">
                        <div class="handle-label" :class="{active:true}">最新推荐政策</div>
                    </div>
                    <div class="remark">
                        <div class="text-abl">{{latestRecommendPolicy3.length}}/500</div>
                        <textarea type="text" placeholder="请输入" v-model="latestRecommendPolicy3" maxlength="500"></textarea>
                    </div>
                </div>
                <div class="handle-details">
                    <div class="handle-xiang">
                        <div class="handle-label">客户是否接受</div>
                        <div class="handle-value" >
                            <button class="flagButton" :class="customerIsAccept3== '1'?'checkflag': ''" @click.stop="chooseCusAcc3('1')">是</button>
                            <button class="flagButton" :class="customerIsAccept3== '0'?'checkflag': ''" @click.stop="chooseCusAcc3('0')">否</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="order-handle" v-show="isShow == '3'">
                <div class="handle-details">
                    <div class="handle-xiang">
                        <div class="handle-label" :class="{active:true}">转派意见</div>
                        <div class="xialakuang-111" @click="commonZpChoose()">
                            <span class="zi-ti" v-if="commonZhuanPaiRemark != ''">{{commonZhuanPaiRemark}}</span>
                            <span class="zi-ti" v-else>请选择常用转派意见</span>
                            <span class="iconfont jiantou-copy-copy iconphtot11"></span>
                        </div>
                    </div>
                    <div class="remark">
                        <div class="text-abl">{{transSuss.length}}/500</div>
                        <textarea type="text" placeholder="请输入转派意见" v-model="transSuss" maxlength="500"></textarea>
                    </div>
                    <div class="common-flag" @click="clickZpFlag()">
                        <i class="iconfont"  :class="[zhuanpaiRemarkFlag ? 'check-box': 'fuxuankuangweixuanzhong']"></i>
                        <span>设为常用转派意见</span>
                    </div>
                </div>

                <div class="end-bottom">
                    <button class="end-submit" :class="{active:isClickByTher}" @click="submitByTher">提&nbsp;&nbsp;交</button>
                </div>
            </div>

            <!--结单-->
            <div class="order-handle" v-show="isShow == '2'">
                <div class="handle-details">
                    <div class="handle-xiang">
                        <div class="handle-label" :class="{active:true}">异动情况<span>*</span></div>
                        <div class="handle-value" >
                            <button class="flagButton" :class="abnormalFlag== '异动'?'checkflag': ''" @click.stop="changeIsYc('异动')">异动</button>
                            <button class="flagButton" :class="abnormalFlag== '无异动'?'checkflag': ''" @click.stop="changeIsYc('无异动')">无异动</button>
                        </div>
                    </div>
                </div>
                <div v-show="abnormalFlag == '异动'">
                    <!--下拉框-->
                    <div class="handle-details" >
                        <div class="handle-xiang">
                            <div class="handle-label">异动类型<span>*</span></div>
                            <div class="xialakuang-111" @click="errorTypeChoose">
                                <span class="zi-ti" v-if="abnormalType != ''">{{abnormalType}}</span>
                                <span class="zi-ti" v-else>请选择异动类型</span>
                                <span class="iconfont jiantou-copy-copy iconphtot11"></span>
                            </div>
                        </div>
                    </div>

                    <div class="handle-details">
                        <div class="handle-xiang">
                            <div class="handle-label">异动取证</div>
                            <div class="xialakuang-111" @click="addEvi">
                                <span class="iconfont jiahao1 iconphtot11"></span>
                            </div>
                        </div>
                    </div>
                    <div v-for="(item,index) in eviList" :key="index">
                        <div class="handle-details">
                            <div class="handle-xiang">
                                <div class="handle-label">
                                    <span class="iconfont check_box shanchu-icon" @click.stop="deleteEviByDeal(index)"></span>取证类型
                                </div>
                                <div class="xialakuang-111" @click="evidenceTypeChoose2(item)">
                                    <span class="zi-ti" v-if="item.evidenceType != ''">{{item.evidenceType}}</span>
                                    <span class="zi-ti" v-else>请选择取证类型</span>
                                    <span class="iconfont jiantou-copy-copy iconphtot11"></span>
                                </div>
                            </div>
                        </div>
                        <div class="handle-details" v-show="item.evidenceType == '其他'">
                            <div class="handle-xiang">
                                <div class="handle-label">
                                    <span class="iconfont check_box wihte-icon"></span>取证类型说明
                                </div>
                                <div class="handle-value">
                                    <input class="input-text" type="text" v-model="item.evidenceOtherRemark" maxlength="50" placeholder="请输入具体取证类型">
                                </div>
                            </div>
                        </div>
                        <div class="handle-details">
                            <div class="handle-xiang">
                                <div class="handle-label">
                                    <span class="iconfont check_box wihte-icon"></span>取证上传
                                </div>
                                <div class="handle-value" @click="openCamEviByDeal(item,index)">
                                    <span class="zi-ti" v-show="!item.isCamera">未拍摄</span>
                                    <span class="zi-ti" v-show="item.isCamera">已拍摄</span>
                                    <span class="iconfont jiantou-copy-copy iconphtot11"></span>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>

                <div v-show="abnormalFlag == '无异动'">
                    <div class="handle-details">
                        <div class="handle-xiang">
                            <div class="handle-label">预警信息是否准确<span>*</span></div>
                            <div class="handle-value" >
                                <button class="flagButton" :class="rightFlag== '准确'?'checkflag': ''" @click.stop="changeIsRightFlag('准确')">准确</button>
                                <button class="flagButton" :class="rightFlag== '不准确'?'checkflag': ''" @click.stop="changeIsRightFlag('不准确')">不准确</button>
                            </div>
                        </div>
                    </div>
                    <div class="handle-details">
                        <div class="remark" style="margin-bottom: 40px">
                            <div class="text-abl">{{abnormalBasis.length}}/500</div>
                            <textarea type="text" placeholder="请简述您判断的依据" v-model="abnormalBasis" maxlength="500"></textarea>
                            <div class="button" @touchstart='handlerTouchstart' @touchend='handlerTouchend'>
                                <div>语音录入<span class="luyin iconfont"></span></div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
            <div class="order-handle" v-show="isShow == '2'">
                <div class="handle-details" >
                    <div class="handle-xiang">
                        <div class="handle-label" :class="{active:true}">最新推荐政策<span>*</span></div>
                    </div>
                    <div class="remark">
                        <div class="text-abl">{{latestRecommendPolicy2.length}}/500</div>
                        <textarea type="text" placeholder="请输入" v-model="latestRecommendPolicy2" maxlength="500"></textarea>
                    </div>
                </div>
                <div class="handle-details">
                    <div class="handle-xiang">
                        <div class="handle-label">客户是否接受<span>*</span></div>
                        <div class="handle-value" >
                            <button class="flagButton" :class="customerIsAccept2== '1'?'checkflag': ''" @click.stop="chooseCusAcc2('1')">是</button>
                            <button class="flagButton" :class="customerIsAccept2== '0'?'checkflag': ''" @click.stop="chooseCusAcc2('0')">否</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="order-handle" v-show="isShow == '2'">
                <div class="handle-details" >
                    <div class="handle-xiang">
                        <div class="handle-label" :class="{active:true}">维护结果<span>*</span></div>
                        <div class="xialakuang-111" @click="maintanceResChoose">
                            <span class="zi-ti" v-if="dealResult != ''">{{dealResult}}</span>
                            <span class="zi-ti" v-else>请选择维护结果</span>
                            <span class="iconfont jiantou-copy-copy iconphtot11"></span>
                        </div>
                    </div>
                </div>
                <div v-show="dealResult == '维护失败'">
                    <div class="handle-details" >
                        <div class="handle-xiang">
                            <div class="handle-label">失败原因<span>*</span></div>
                            <div class="xialakuang-111" @click="defeatReasonChoose">
                                <span class="zi-ti" v-if="!isChooseFail">请选择失败原因</span>
                                <span class="zi-ti" v-else>已选择</span>
                                <span class="iconfont jiantou-copy-copy iconphtot11"></span>
                            </div>
                        </div>
                    </div>

<!--                    <div class="handle-details" v-show="defeatReason == '其他' && defeatRemark == '其他'">-->
<!--                        <div class="handle-xiang">-->
<!--                            <div class="handle-label">失败原因说明<span>*</span></div>-->
<!--                            <div class="handle-value">-->
<!--                                <input class="input-text" type="text" v-model="defeatOtherDesc" maxlength="50" placeholder="请输入失败原因说明">-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->

                </div>

                <div v-show="dealResult == '维护成功且反杀'">
                    <div class="handle-details" >
                        <div class="handle-xiang">
                            <div class="handle-label">反杀详情<span>*</span></div>
                            <div class="xialakuang-111" @click="fakerDescChoose">
                                <span class="zi-ti" v-if="fakerDesc != ''">{{fakerDesc}}</span>
                                <span class="zi-ti" v-else>请选择反杀详情</span>
                                <span class="iconfont jiantou-copy-copy iconphtot11"></span>
                            </div>
                        </div>
                    </div>

                    <div class="handle-details" v-show="fakerDesc == '其他'">
                        <div class="handle-xiang">
                            <div class="handle-label">反杀详情说明<span>*</span></div>
                            <div class="handle-value">
                                <input class="input-text" type="text" v-model="fakerOtherDesc" maxlength="50" placeholder="请输入具体反杀详情">
                            </div>
                        </div>
                    </div>

                    <div class="handle-details">
                        <div class="handle-xiang">
                            <div class="handle-label">反杀证据上传<span>*</span></div>
                            <div class="handle-value" @click="openCamFour">
                                <span class="zi-ti" v-show="!isCameraFour">未拍摄</span>
                                <span class="zi-ti" v-show="isCameraFour">已拍摄</span>
                                <span class="iconfont jiantou-copy-copy iconphtot11"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <div v-show="dealResult != ''">
                    <div class="handle-details">
                        <div class="handle-xiang">
                            <div class="handle-label">证据链上传</div>
                            <div class="handle-value" @click="openCamThree">
                                <span class="zi-ti" v-show="!isCameraThree">未拍摄</span>
                                <span class="zi-ti" v-show="isCameraThree">已拍摄</span>
                                <span class="iconfont jiantou-copy-copy iconphtot11"></span>
                            </div>
                        </div>
                    </div>
                    <div class="handle-details">
                        <div class="handle-xiang">
                            <div class="handle-label">处理反馈意见</div>
                            <div class="xialakuang-111" @click="commonFkChoose()">
                                <span class="zi-ti" v-if="fkRemarkCommon != ''">{{fkRemarkCommon}}</span>
                                <span class="zi-ti" v-else>请选择常用处理反馈意见</span>
                                <span class="iconfont jiantou-copy-copy iconphtot11"></span>
                            </div>
                        </div>
                        <div class="remark">
                            <div class="text-abl">{{clfkRemark.length}}/500</div>
                            <textarea type="text" placeholder="请输入备注信息" v-model="clfkRemark" maxlength="500"></textarea>
                        </div>
                        <div class="common-flag" @click="clickfkFlag()">
                            <i class="iconfont"  :class="[fankuiRemarkFlag ? 'check-box': 'fuxuankuangweixuanzhong']"></i>
                            <span>设为常用处理反馈意见</span>
                        </div>
                    </div>
                </div>
                <div class="end-bottom-deal">
                    <button class="end-submit-com" :class="{active:isClickByDeal}" @click="submitByDeal">提&nbsp;&nbsp;交</button>
                </div>
            </div>

            <!--跟进-->
            <div class="order-handle" v-show="isShow == '1'">
                <div class="handle-details">
                    <div class="handle-xiang">
                        <div class="handle-label" :class="{active:true}">预约方式</div>
                        <div class="handle-value" >
                            <button class="flagButton" :class="followDealType== '上门'?'checkflag': ''" @click.stop="chooseAppointType('上门')">上门</button>
                            <button class="flagButton" :class="followDealType== '电话'?'checkflag': ''" @click.stop="chooseAppointType('电话')">电话</button>
                            <button class="flagButton" :class="followDealType== '到厅'?'checkflag': ''" @click.stop="chooseAppointType('到厅')">到厅</button>
                        </div>
                    </div>
                </div>
                <!--下拉框-->
                <div class="handle-details" v-show="followDealType">
                    <div class="handle-xiang">
                        <div class="handle-label">预约时间<span>*</span></div>
                        <div class="xialakuang-111" @click="chooseAppointTime()">
                            <span class="zi-ti" v-if="dueTime != ''">{{dueTime}}</span>
                            <span class="zi-ti" v-else>请选择</span>
                            <span class="iconfont jiantou-copy-copy iconphtot11"></span>
                        </div>
                    </div>
                </div>
                <!--9级地址选择-->
                <div class="handle-details" v-show="followDealType== '上门'">
                    <div class="handle-xiang">
                        <div class="handle-label">上门地址<span>*</span></div>
                        <div class="xialakuang-111" @click="openAddChoose()">
                            <span class="zi-ti" v-if="visitAddr != ''">{{visitAddr}}</span>
                            <span class="zi-ti" v-else>请选择</span>
                            <span class="iconfont jiantou-copy-copy iconphtot11"></span>
                        </div>
                    </div>
                </div>
                <!--营业厅选择-->
                <div class="handle-details" v-show="followDealType== '到厅'">
                    <div class="handle-xiang">
                        <div class="handle-label">预约厅店<span>*</span></div>
                        <div class="xialakuang-111" @click="openHallChoose()">
                            <span class="zi-ti" v-if="dueHallName != ''">{{dueHallName}}</span>
                            <span class="zi-ti" v-else>请选择</span>
                            <span class="iconfont jiantou-copy-copy iconphtot11"></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="order-handle" v-show="isShow == '1'">
                <div class="handle-details" >
                    <div class="handle-xiang">
                        <div class="handle-label" :class="{active:true}">最新推荐政策</div>
                    </div>
                    <div class="remark">
                        <div class="text-abl">{{latestRecommendPolicy1.length}}/500</div>
                        <textarea type="text" placeholder="请输入" v-model="latestRecommendPolicy1" maxlength="500"></textarea>
                    </div>
                </div>
                <div class="handle-details">
                    <div class="handle-xiang">
                        <div class="handle-label">客户是否接受</div>
                        <div class="handle-value" >
                            <button class="flagButton" :class="customerIsAccept1== '1'?'checkflag': ''" @click.stop="chooseCusAcc1('1')">是</button>
                            <button class="flagButton" :class="customerIsAccept1== '0'?'checkflag': ''" @click.stop="chooseCusAcc1('0')">否</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="order-handle" v-show="isShow == '1'">
                <div class="handle-details">
                    <div class="handle-xiang">
                        <div class="handle-label" :class="{active:true}">跟进备注</div>
                    </div>
                    <div class="remark">
                        <div class="text-abl">{{followRemark.length}}/500</div>
                        <textarea type="text" placeholder="请输入跟进备注" v-model="followRemark" maxlength="500"></textarea>
                    </div>
                </div>

                <div class="end-bottom">
                    <button class="end-submit" :class="{active:isClickByDeal}" @click="submitByFollow">提&nbsp;&nbsp;交</button>
                </div>
            </div>
        </div>

        <!--省级预警组件-->
        <TransferSjyjIop ref="executeRef" v-if="transferFlag"
                         @emCloseInit="closeExecute"
                         @emSubmit="getExecute"
                         :orgaId="cityId"
                         :isTransFer="'1'"
                         :countryId="countryId"
                         :headerName="'转派人员选择'"></TransferSjyjIop>


        <TransferSjyjIop ref="executeRef" v-if="transferByDealFlag"
                         @emCloseInit="closeExecuteTwo"
                         @emSubmit="getExecuteTwo"
                         :orgaId="cityId"
                         :isTransFer="'2'"
                         :countryId="countryId"
                         :headerName="'上门协同人员选择'"></TransferSjyjIop>

        <!--选取位置-->
        <MapLocal v-if="showMapFlag"
                  :isComponent="isComponent"
                  @emCloseMap="closeMap"
                  @emComfirmMap="confirmMap"></MapLocal>


        <!--放大图片-->
        <div class="big-img-wrap" v-show="bigImgFlg">
            <span class="close-btn iconfont guanbi1" @click="bigImgFlg=false"></span>
            <img class="big-img" ref="bigImg" @click="bigImgFlg=false"/>
        </div>

        <!--拍照组件--><!--处理反馈-->
        <div class="camera-wrap" v-if="clfkShowCameraFlg">
            <WarnCameraToIop :smsPhotoList="clfkPhoList"
                             :photoMainId="clfkPhotoMainId"
                             @emClose="getCloseCLFK"
                             :showCameraFlg="clfkShowCameraFlg"
                             @emSign="signCameraCbCLFK"
                             @getPhotoMainId="getCLFKPhotoMainId"/>
        </div>

        <!--取证组件-->
        <div class="camera-wrap" v-if="videoAndPhotoFlag">
            <EviToIopComponent  :showCameraFlg="videoAndPhotoFlag"
                                :videoMainId="videoMainId"
                                @emClose="getEvidencePrev"
                                @emSign="getEvidenceClose"
                                @getVideoMainId="getVideoMainId"/>
        </div>
        <!--处理反馈取证-->
        <div class="camera-wrap" v-if="videoAndPhotoDealFlag">
            <EviToIopComponent  :showCameraFlg="videoAndPhotoDealFlag"
                                :videoMainId="videoMainIdByDeal"
                                :followList="eviList[currentIndexByDeal].evidenceKeys"
                                @emClose="getEvidencePrev2"
                                @emSign="getEvidenceClose2"
                                @getVideoMainId="getVideoMainId2"/>
        </div>
        <!--证据链-->
        <div class="camera-wrap" v-if="eviKeyFlag">
            <EviToIopComponent  :showCameraFlg="eviKeyFlag"
                                :videoMainId="evlKeyMainId"
                                :followList="zjlFileKeys"
                                @emClose="getEvidencePrev3"
                                @emSign="getEvidenceClose3"
                                @getVideoMainId="getVideoMainId3"/>
        </div>
        <!--反杀详情-->
        <div class="camera-wrap" v-if="fakerKeysFlag">
            <EviToIopComponent  :showCameraFlg="fakerKeysFlag"
                                :videoMainId="fakerKeysMainId"
                                :followList="fakerKeys"
                                @emClose="getEvidencePrev4"
                                @emSign="getEvidenceClose4"
                                @getVideoMainId="getVideoMainId4"/>
        </div>

        <!--失败原因组件-->
        <div class="camera-wrap" v-if="showChooseFlag">
            <FailReasonChoose  :showChooseFlag="showChooseFlag"
                               :hisFailList="defeatReasonList"
                                @emGoClose="failChooseClose"
                                @emFailSubmit="failSubmit"/>
        </div>

        <div v-if="hallShow" class="camera-wrap">
            <BusiHallChoose @emGoClose="colseHallChoose"
                            @emSubmit="chooseHallInfo"></BusiHallChoose>
        </div>

        <div class='hide-box' v-show='senceStatusFlag' @click.stop="resertChoose"></div>
        <div class="task-search-box wrapper-medias" v-show="senceStatusFlag">
            <div class="top">
                <div class="lie">
                    <div class="choose-item" :class="chooseOneId == item.id ? 'active': ''"
                         v-for="(item,index) in chooseIdList" :key="index" @click.stop="chooseLevel1(item)">
                        <span class="font-wenzi">{{ item.label }}</span>
                        <span class="iconfont youjiantou1"></span>
                    </div>
                </div>

                <div class="lie" v-if="senceStatusTwoList.length> 0">
                    <div class="choose-item" :class="chooseTwoId == item.id? 'active': ''"
                         v-for="(item,index) in senceStatusTwoList" :key="index" @click.stop="chooseLevel2(item)">
                        <span class="font-wenzi font-wenzi3">{{ item.label }}</span>
                    </div>
                </div>
            </div>
            <div class="bottom">
                <div class="chongzhi" @click.stop="resertChoose">取  消</div>
                <div class="queren" @click.stop="sureChoose">确  认</div>
            </div>
        </div>

        <div class="dialog-box">
            <img src="../../../assets/img/chatloading.gif"/>
            <div class="recwave-box1">正在录音识别中...</div>
        </div>

        <!--定位提示-->
        <div class="zidong" v-if="isShowLocal">
            <div class="eject-box"></div>
            <div class="eject-window loading">
                <div class="eject-detail-tittle">
                    位置信息获取中，请耐心等待！
                    <span v-loading="true" :circle-size="20" class="connecting-btn-load"></span>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import { decrptParam } from '@/base/encrptH5.js'
    import Header from 'components/common/Header.vue'
    import ClientJs from '@/base/clientjs';
    import Storage from '@/base/storage'
    import {chgStrToDate,dateFormat} from '@/base/utils';
    import { zhuoWangCallJs } from '@/base/zhuoWangCall.js'
    import DBStorage from '@/base/indexDb.js'
    import RecorderManger from '@/base/chatUtil/RecorderManager.js'
    import ImageObj from '@/base/request/ImageServReq'
    import NlDatePicker from "components/common/NlDatePick/datePicker.js";
    import NlDropdown from "components/common/NlDropdown/dropdown.js";
    import TransferSjyjIop from'components/my/provincialwarningbyiop/TransferSjyjIop.vue';
    import WarnCameraToIop from "components/my/provincialwarningbyiop/WarnCameraToIop.vue";
    import EviToIopComponent from "components/my/provincialwarningbyiop/EviToIopComponent.vue";
    import MapLocal from'components/map/index.vue';
    import FailReasonChoose from "components/my/provincialwarningbyiop/FailReasonChoose.vue";
    import VisitAddress from 'components/my/provincialwarningbyiop/VisitAddress.js'
    import BusiHallChoose from "components/my/provincialwarningbyiop/BusiHallChoose.vue";

    export default {
        name: 'ProvinWarningByIop',
        components: { Header ,TransferSjyjIop,WarnCameraToIop,EviToIopComponent,MapLocal,FailReasonChoose,BusiHallChoose},
        mixins: [zhuoWangCallJs],
        data() {
            return {
                srcCorm:"",            //页面来源
                workId:"",             //任务编码
                subId:"",              //工单编号
                warnType:"",              //工单编号
                warnLevel:"",              //预警等级
                limitDate:"",              //到期时间
                authId: '',  //外呼工号
                telB: '',     //用户手机号码
                telX: '',    //客户经理虚拟号码
                bindId: '',    //绑定编码
                // 城市长编码
                LONG_CITY_LIST: [
                    { id: '1000250', shortId: '14', label: '南京' },
                    { id: '1000510', shortId: '19', label: '无锡' },
                    { id: '1000511', shortId: '18', label: '镇江' },
                    { id: '1000512', shortId: '11', label: '苏州' },
                    { id: '1000513', shortId: '20', label: '南通' },
                    { id: '1000514', shortId: '23', label: '扬州' },
                    { id: '1000515', shortId: '22', label: '盐城' },
                    { id: '1000516', shortId: '16', label: '徐州' },
                    { id: '1000517', shortId: '12', label: '淮安' },
                    { id: '1000518', shortId: '15', label: '连云港' },
                    { id: '1000519', shortId: '17', label: '常州' },
                    { id: '1000523', shortId: '21', label: '泰州' },
                    { id: '1000527', shortId: '13', label: '宿迁' },
                    { id: '2000250', shortId: '99', label: '全省' }
                ],

                cityId:'',            //归属地市编码

                orgaIds:'',           //归属组织机构编码
                orgaNames:'',         //归属组织机构名称
                countryId:'',         //区县编码
                userInfo:{},          //用户信息


                mobile:"",             //预警客户手机号码
                userValue:"",          //用户价值
                areaName:"",           //网格名称
                ysydDtl:"",            //疑似异动
                custName:"",            //集团名称
                yjLabelDtl:[],         //预警标签明细信息
                yjTaskHis:[],          //历史跟进信息
                hisXianInt: 0,        //历史轨迹数量
                bigImgFlg: false,        //放大照片

                isShow:'3',    //处理类型    2:处理反馈  3:转派
                zpReceiveName:'',        //转派接收人姓名
                zpReceiveMobile:'',     //转派接收人手机号
                zpReceiveOperator:'',   //转派接收人统一人员工号
                transSuss:"",        //转派意见
                transferFlag:false,   //转派开关
                abnormalFlag2:'',         //转派是否异常
                abnormalType2:"",    //转派异常类型
                rightFlag2:'',  //转派预警信息是否准确
                abnormalBasis2:'',  //转派判断依据
                errorList:[
                    {id:"",label:"请选择异常类型"},
                    {id:"DX",label:"DX"},
                    {id:"LT",label:"LT"},
                    {id:"GD",label:"GD"},
                    {id:"社会",label:"社会"},
                ],
                evidenceTypeList:[
                    {id:"",label:"取证类型"},
                    {id:"LY、LH收集",label:"LY、LH收集"},
                    {id:"代客取证",label:"代客取证"},
                    {id:"其他",label:"其他"},
                ],
                eviListByTher:[{
                    evidenceType:'',
                    evidenceKeys:'',
                    evidenceOtherRemark:'',
                    isCamera:false,
                    compoentId:'eviListByTher'+new Date().getTime(),
                }],     //取证列表
                currentIndex:'',
                videoMainId:'',
                videoAndPhotoFlag : false,//取证类型
                isClickByTher:false,


                dealTypeList:[
                    {id:"1",label:"电话",chooseFlag:false},
                    {id:"2",label:"上门",chooseFlag:false},
                    {id:"3",label:"进厅",chooseFlag:false},
                ],
                dealType:'',     //处理方式
                clockAddress:"",       //打卡地址
                clockLongitude:"",     //打卡经度
                clockLatitude:"",      //打卡纬度
                showMapFlag:false,
                isComponent:true,
                clfkShowCameraFlg: false,   // 打开拍照组件开关
                isCameraTwo: false,        //是否已经拍照，true 已拍
                clfkPhoList:[],          //处理反馈图片
                clfkPhotoMainId:'clfkPhotoMid'+new Date().getTime(),      //处理反馈唯一建
                smDealName:'',    //上门处理人姓名
                smDealMobile:'', //上门处理人号码
                transferByDealFlag:'',//上门处理flag
                abnormalFlag:'',         //是否异常
                abnormalType:"",    //异常类型
                rightFlag:'',  //预警信息是否准确
                abnormalBasis:'',  //判断依据
                eviList:[{
                    evidenceType:'',
                    evidenceKeys:'',
                    evidenceOtherRemark:'',
                    isCamera:false,
                    compoentId:'eviList'+new Date().getTime(),
                }],     //取证列表
                currentIndexByDeal:'',
                videoMainIdByDeal:'',
                videoAndPhotoDealFlag : false,//取证类型
                isClickByDeal:false,
                dealResult:'',    //维护结果
                maintanceResList:[
                    {id:"",label:"请选择维护结果"},
                    {id:"维护成功",label:"维护成功"},
                    {id:"维护成功且反杀",label:"维护成功且反杀"},
                    {id:"维护失败",label:"维护失败"},
                ],
                clfkRemark:'',               //处理反馈备注
                eviKeyFlag:false,
                evlKeyMainId:'evlKeyMainId'+new Date().getTime(),
                isCameraThree:false,
                zjlFileKeys:'',
                senceStatusFlag:false,
                defeatReason:'',//失败原因大类
                defeatRemark:'',//失败原因小类
                defeatOtherDesc:'',//失败原因补充说明
                chooseTwoId:'',//失败原因小类
                chooseOneId:'',//失败原因小类
                chooseIdList:[
                    {id:'资费原因',label:'资费原因',
                        children:[
                            {id:'消费上升、消费不明、乱扣费',label:'消费上升、消费不明、乱扣费'},
                            {id:'资费过高、比友商贵',label:'资费过高、比友商贵'}]},
                    {id:'自身原因',label:'自身原因',
                        children:[
                            {id:'前期活动到期未接盘，资费骤升',label:'前期活动到期未接盘，资费骤升'},
                            {id:'多号在用，近期自己或家人新办友商卡，本卡用少',label:'多号在用，近期自己或家人新办友商卡，本卡用少'},
                            {id:'用户拒绝沟通(含中途挂断后不再接听)',label:'用户拒绝沟通(含中途挂断后不再接听)'},
                            {id:'人员流动(本地换址)',label:'人员流动(本地换址)'},
                            {id:'人员流动(去了外地)',label:'人员流动(去了外地)'},
                            {id:'再无使用需求',label:'再无使用需求'},
                            ]},
                    {id:'受人影响',label:'受人影响',
                        children:[
                            {id:'有亲友在友商工作',label:'有亲友在友商工作'},
                            {id:'身边有异网低资费',label:'身边有异网低资费'},
                            {id:'家人用友商，转做代付',label:'家人用友商，转做代付'},
                            {id:'家里人代操作',label:'家里人代操作'},
                            {id:'友商代操作',label:'友商代操作'},
                        ]},
                    {id:'宽带原因',label:'宽带原因',
                        children:[
                            {id:'速度慢、卡顿、未覆盖、对安防电视不满',label:'速度慢、卡顿、未覆盖、对安防电视不满'},
                            {id:'用友商宽带、新办友商宽带',label:'用友商宽带、新办友商宽带'},
                            {id:'重新办了移动宽带',label:'重新办了移动宽带'},
                        ]},
                    {id:'其他',label:'其他',
                        children:[
                            {id:'未上门 ，不是网格内用户',label:'未上门 ，不是网格内用户'},
                            {id:'电话打不通、关机、销户、空号',label:'电话打不通、关机、销户、空号'},
                            {id:'其他',label:'其他'},
                            ]},
                ],
                senceStatusTwoList:[],
                fakerDescList:[
                    {id:'',label:'请选择反杀详情'},
                    {id:'友商宽带拆机',label:'友商宽带拆机'},
                    {id:'友商号码欠费或弃卡',label:'友商号码欠费或弃卡'},
                    {id:'宽带和号码均拆',label:'宽带和号码均拆'},
                    {id:'其他',label:'其他'},
                ],
                fakerDesc:'',
                fakerOtherDesc:'',
                fakerKeys:'',
                isCameraFour:false,
                fakerKeysFlag:false,
                fakerKeysMainId:'fakerKeysMainId'+new Date().getTime(),

                rec: '',
                speakingFlg: false,
                iosPermissionFlg: '',


                zhuanpaiRemarkFlag:false ,//转派常用意见
                commonZhuanPaiRemark:'',
                commonZpList:[],

                fankuiRemarkFlag:false,//反馈常用意见
                fkRemarkCommon:'',
                commonFkList:[],

                showDownFlg: false,//是否展示
                retentionAuthority:false,//降档挽留权限

                showChooseFlag:false,
                defeatReasonList:[],//失败原因
                isChooseFail:false,

                showFlag:false,  //外呼弹窗
                showCallButtomFlag:false,

                followName:'',   //跟进人电弧
                followMsisdn:'',    //跟进人电话
                followDealType:'',   //预约方式
                dueTime:'',   //上门时间
                visitAddr:"",
                hallShow:false,  //营业厅选择
                dueHallId:'',
                dueHallName:'',
                contactsName:'', //客户侧联系人
                contactsMsisdn:'',  //客户测试号码

                isShowLocal:false,//定位加载圈圈
                followRemark:'', //跟进备注
                latestRecommendPolicy1:'',  //跟进最新推荐客户政策
                customerIsAccept1:'',

                latestRecommendPolicy2:'', //结单最新推荐客户政策
                customerIsAccept2:'',

                latestRecommendPolicy3:'', //转派最新推荐客户政策
                customerIsAccept3:'',
            }
        },
        created(){
            this.workId = this.$route.query.workId;
            this.subId = this.$route.query.subId;
            this.warnType = this.$route.query.warnType;
            this.srcCorm = this.$route.query.srcCorm;
            this.warnLevel= this.$route.query.warnLevel;
            this.limitDate= this.$route.query.limitDate;
            this.initData();  //查询预警明细信息

            this.getOrgaInfo(); //获取组织机构编码
            this.getCommon(); //常用意见查询
            this.crmQryEmployeeRole();//降档挽留权限查询
            this.qryConfigCall();//查询外呼工号配置
            ClientJs.getAudioPermiss('getAudioPermissfun')
        },
        mounted() {
            let self = this
            //初始化数据示例
            self.dbStorage = DBStorage.getInstance({
                busiType: 'provinWarn'
            })

            if (/android|harmony/gi.test(navigator.userAgent)) {
                let str1 = localStorage.getItem('getAudioPermissfun')
                if (str1) {
                    this.rec = new RecorderManger({},'', this.getRecordResultCbFn);
                } else {
                    window['getAudioPermissfun'] = (res) => {
                        localStorage.setItem('getAudioPermissfun', 1)
                        this.rec = new RecorderManger({}, '', this.getRecordResultCbFn);
                    }
                }
            } else {
                this.rec = new RecorderManger({}, '', this.getRecordResultCbFn);
            }

            window['getLocationWithWarnDeal'] = (result) => {
                console.info("getLocationWithWarnDeal result",result);
                this.submitCmopByTher(result);
            };

            window['getLocationWithFollow'] = (result) => {
                console.info("getLocationWithFollow result",result);
                this.isShowLocal = false;
                this.submitCmopWithFollow(result);
            };

            window['getLocationWithHandel'] = (result) => {
                console.info("getLocationWithHandel result",result);
                this.isShowLocal = false;
                this.submitCmopWithHandel(result);
            };
        },
        methods: {
            goPrev() {
                this.srcCorm = this.$route.query.srcCorm;
                if(this.srcCorm == 'myTaskListNew'){
                    this.$router.push({
                        path: '/MyTaskList'
                    })
                }else if(this.srcCorm == 'workStage'){
                    this.$router.push('/');
                }else if(this.srcCorm == 'warnOrderList'){
                    this.$router.push({
                        path: '/warnOrderList',
                    })
                }else {
                    history.go(-1)
                }
                this.clearAttacted();
                //证据链上传水印
                Storage.session.remove('shuiYinByEvi');
                //预处理及处理反馈水印
                Storage.session.remove('shuiYinByWarning');
                Storage.session.remove('objPhotoList');
                Storage.session.remove('failResonByWarn');
            },
            async clearAttacted(){
                await this.dbStorage.clear();
            },
            showButtom(){
                this.showFlag = !this.showFlag;
            },
            //判断是不是宽带已经销户
            judeBoard(){
                if(this.warnType.indexOf('宽带销户') >= 0){
                    //查询宽带是否销户
                    this.getAddress();
                }
            },
            //获取安装地址接口
            getAddress(){
                let param = {};
                param.telnum = this.mobile;
                let url = `/xsb/personBusiness/guhua/h5GetBanbInfo`;
                this.$http.post(url, param).then((res) => {
                    let d = res.data;
                    if (d.retCode == '0'){
                        console.info('d',d)
                        this.bandInfo = d.data.bandInfo;
                        if (!this.bandInfo.addrId) {
                            //无宽带
                            this.$messagebox.alert('用户已销宽', '温馨提示').then(action => {
                                this.goPrev();
                            })
                        }
                        console.info('this.bandInfo',this.bandInfo)
                    }else{
                        this.$alert(d.retMsg||"获取宽带安装信息失败");
                    }
                });
            },

            qryConfigCall(){
                let _this = this;
                let paramCfg = {
                    telB: Storage.session.get('userInfo').servNumber
                }
                //查询外呼工号配置
                _this.$http.post('/xsb/gridCenter/outCallByzw/h5GetOutOperInfo', paramCfg).then((res) => {
                    console.info("查询配置111",paramCfg)
                    if (res.data.retCode == '0') {
                        if(res.data.data && res.data.data.subAppId){
                            _this.showCallButtomFlag = true;
                        }else {
                            _this.showCallButtomFlag = false;
                            return
                        }

                        if(res.data.data && res.data.data.subAppKey){
                            _this.showCallButtomFlag = true;
                        }else {
                            _this.showCallButtomFlag = false;
                            return
                        }

                    } else {
                        _this.showCallButtomFlag = false;
                    }
                })
            },
            //本机外呼
            benjiCall(){
                this.callUser(false);
            },
            //中间号外呼
            waiHuProWarn(){
                if(!this.showCallButtomFlag){
                    return
                }


                let userId = '';
                let workId = this.workId;
                let senceType = 'shengJiWarn';
                // this.WaiHuStart(this.mobile,userId,workId,senceType);
                this.qryConfigBuWaihu(this.mobile,userId,workId,senceType);
            },
            //本机外呼
            callUser(waihuFlag){
                if(waihuFlag){
                    //外呼的是虚拟号
                    console.info("代客投诉预警外呼成功--卓望",this.telX)
                    ClientJs.openCallPhoneView(this.telX);
                }else {
                    //外呼的是手机号码
                    console.info("代客投诉预警外呼成功--客户端",this.mobile)
                    ClientJs.openCallPhoneView(this.mobile);
                }

            },
            getOrgaInfo(){
                this.userInfo = Storage.session.get('userInfo')
                for (let i = 0; i < this.LONG_CITY_LIST.length; i++) {
                    if (this.userInfo.region == this.LONG_CITY_LIST[i].shortId) {
                        this.cityId = this.LONG_CITY_LIST[i].id;
                        // this.orgaNames = this.LONG_CITY_LIST[i].label;
                    }
                }

                // //当前网格得分排名查询 获取网格信息
                // let url = `/xsb/personBusiness/gridViewSecond/h5QryScoreAndRank?telnum=${this.userInfo.servNumber}`;
                // this.$http.get(url).then((res) => {
                //     if (res.data.retCode == "0") {
                //         let data = res.data.data;
                //         this.orgaIds = data.orgaId2;
                //         this.orgaNames = data.orgaName2;
                //         this.getOrgLevel(this.orgaIds);
                //     } else {
                //         this.$alert(res.data.retMsg || "查询当前网格基本信息接口调用出错");
                //     }
                //     console.info("操作员归属",this.orgaIds+this.orgaNames);
                // })
            },
            getOrgLevel(orgaIds){
                let url2 = `/xsb/personBusiness/gridViewSecond/h5QryAreak?orgaid=${orgaIds}`
                this.$http.get(url2).then((res) => {
                    if (res.data.retCode == '0') {

                        let data = res.data.data
                        console.info(' data[0].orgalevel ', data)
                        let orgalevel = data[0].orgalevel
                        console.info('this.orgaLevel ', orgalevel)
                        if(orgalevel == 3){
                            this.countryId = data[0].parentid
                            console.info("操作员归属区县",this.countryId);
                        }
                    } else {
                        this.$alert(res.data.retMsg || '查询当前网格层级调用出错')
                    }
                })
            },
            initData(){
                this.yjLabelDtl = []
                let param = {
                    workId : this.workId,
                    subId : this.subId
                }
                this.$http.post('/xsb/gridCenter/warningOrder/h5YjInfoQueryToIop',param).then(res => {
                    let{retCode,retMsg,data} = res.data;
                    console.info("data",res.data)
                    if(retCode == '0'){
                        //用户手机号码
                        if(data.mobile){
                            this.mobile = data.mobile;
                            this.getHisInfo();//获取历史跟进信息
                            //判断是不是宽带已经销户
                            this.judeBoard();
                        }
                        if(data.userValue){
                            this.userValue = data.userValue;
                        }
                        if(data.areaName){
                            this.areaName = data.areaName;
                        }
                        if(data.ysydDtl){
                            this.ysydDtl = data.ysydDtl;
                        }
                        if(data.custName){
                            this.custName = data.custName;
                        }
                        //用户业务标签信息
                        if(data.yjLabelDtl && data.yjLabelDtl.length > 0){
                            this.yjLabelDtl = data.yjLabelDtl;
                        }
                    }else {
                        this.$alert(retMsg || 'IOP-预警明细信息查询失败');
                    }
                })
            },
            //获取历史信息
            getHisInfo(){
                //获取当前工单的历史跟进情况
                this.getHis();
            },
            getHis(){
                this.yjTaskHis = [];
                let param = {
                    mobile : this.mobile,
                    subId : this.subId,
                    queryType : '1',
                }
                this.$http.post('/xsb/gridCenter/warningOrder/h5YjHisQueryToIop',param).then(res => {
                    let{retCode,retMsg,data} = res.data;
                    console.info("h5YjInfoQueryToIop",res.data)
                    if(retCode == '0'){
                        //历史预警数据
                        if(data.yjTaskHis && data.yjTaskHis.length > 0){
                            this.yjTaskHis = data.yjTaskHis;

                            for(let i = 0; i < this.yjTaskHis.length; i++){

                                //照片obs的key
                                if(this.yjTaskHis[i].photoKeys){
                                    let url = '/xsb/personBusiness/queryCalculate/h5DownBillFollowUpPic'
                                    let theArr1 = this.yjTaskHis[i].photoKeys.split(',')
                                    for (let j = 0; j < theArr1.length; j++) {
                                        ImageObj.getImgUrl(theArr1[j], {"comeFrom": "provinceWarning"}, url).then(res => {
                                            if (this.yjTaskHis[i].photoKeys) {
                                                this.yjTaskHis[i].photoKeys = this.yjTaskHis[i].photoKeys + ',' + res
                                            } else {
                                                this.yjTaskHis[i].photoKeys = res
                                            }

                                        })
                                    }
                                }

                                //证据链
                                if(this.yjTaskHis[i].zjlFileKeys) {
                                    Vue.set(this.yjTaskHis[i], "photoShowList", []);
                                    Vue.set(this.yjTaskHis[i], "videoShowList", []);
                                    Vue.set(this.yjTaskHis[i], "audioShowList", []);
                                    let url = '/xsb/personBusiness/queryCalculate/h5DownBillFollowUpPic'
                                    let theArr2 = this.yjTaskHis[i].zjlFileKeys.split(',')
                                    for (let j = 0; j < theArr2.length; j++) {
                                        //视频
                                        if(theArr2[j].indexOf(".mp4") >= 0){
                                            ImageObj.getImgUrl(theArr2[j], {"comeFrom": "provinceWarning"}, url).then(res => {
                                                this.yjTaskHis[i].videoShowList.push(res);
                                            })
                                        }else if(theArr2[j].indexOf(".jpg") >=0 ){
                                            //图片
                                            ImageObj.getImgUrl(theArr2[j], {"comeFrom": "provinceWarning"}, url).then(res => {
                                                this.yjTaskHis[i].photoShowList.push(res);
                                            })
                                        }else {
                                            let audioNameArr =theArr2[j].split("_");
                                            this.yjTaskHis[i].audioShowList.push(audioNameArr[2])
                                        }
                                    }
                                }
                                //反杀证据
                                if(this.yjTaskHis[i].fakerKeys) {
                                    Vue.set(this.yjTaskHis[i], "photoShowList22", []);
                                    Vue.set(this.yjTaskHis[i], "videoShowList22", []);
                                    Vue.set(this.yjTaskHis[i], "audioShowList22", []);
                                    let url = '/xsb/personBusiness/queryCalculate/h5DownBillFollowUpPic'
                                    let theArr3 = this.yjTaskHis[i].fakerKeys.split(',')
                                    for (let j = 0; j < theArr3.length; j++) {
                                        //视频
                                        if(theArr3[j].indexOf(".mp4") >= 0){
                                            ImageObj.getImgUrl(theArr3[j], {"comeFrom": "provinceWarning"}, url).then(res => {
                                                this.yjTaskHis[i].videoShowList22.push(res);
                                            })
                                        }else if(theArr3[j].indexOf(".jpg") >=0 ){
                                            //图片
                                            ImageObj.getImgUrl(theArr3[j], {"comeFrom": "provinceWarning"}, url).then(res => {
                                                this.yjTaskHis[i].photoShowList22.push(res);
                                            })
                                        }else {
                                            let audioNameArr = theArr3[j].split("_");
                                            this.yjTaskHis[i].audioShowList22.push(audioNameArr[2])
                                        }
                                    }
                                }

                                //异动取证List
                                if(this.yjTaskHis[i].evidenceList && this.yjTaskHis[i].evidenceList.length > 0){
                                    for (let x = 0; x < this.yjTaskHis[i].evidenceList.length; x++){
                                        if(this.yjTaskHis[i].evidenceList[x].evidenceKeys){
                                            Vue.set(this.yjTaskHis[i].evidenceList[x], "photoShowList", []);
                                            Vue.set(this.yjTaskHis[i].evidenceList[x], "videoShowList", []);
                                            Vue.set(this.yjTaskHis[i].evidenceList[x], "audioShowList", []);
                                            let url = '/xsb/personBusiness/queryCalculate/h5DownBillFollowUpPic'
                                            let theArr4 = this.yjTaskHis[i].evidenceList[x].evidenceKeys.split(',')
                                            for (let j = 0; j < theArr4.length; j++) {
                                                //视频
                                                if(theArr4[j].indexOf(".mp4") >= 0){
                                                    ImageObj.getImgUrl(theArr4[j], {"comeFrom": "provinceWarning"}, url).then(res => {
                                                        this.yjTaskHis[i].evidenceList[x].videoShowList.push(res);
                                                    })
                                                }else if(theArr4[j].indexOf(".jpg") >=0 ){
                                                    //图片
                                                    ImageObj.getImgUrl(theArr4[j], {"comeFrom": "provinceWarning"}, url).then(res => {
                                                        this.yjTaskHis[i].evidenceList[x].photoShowList.push(res);
                                                    })
                                                }else {
                                                    let audioNameArr = theArr4[j].split("_");
                                                    this.yjTaskHis[i].evidenceList[x].audioShowList.push(audioNameArr[2])
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                            //判断是不是当前跟进的
                            let obj = data.yjTaskHis[0];
                            if(obj.dealAction == '5' && obj.followMsisdn == Storage.session.get('userInfo').servNumber){
                                if(obj.followMsisdn){
                                    this.followMsisdn = obj.followMsisdn
                                }
                                if(obj.followName){
                                    this.followName = obj.followName
                                }

                                if(obj.followDealType){
                                    this.followDealType = obj.followDealType
                                    if(obj.dueTime){
                                        this.dueTime = dateFormat(chgStrToDate(obj.dueTime), ("yyyy-MM-dd hh:mm:ss"))
                                    }
                                    if(obj.followDealType == '上门'){
                                        if(obj.visitAddr){
                                            this.visitAddr = obj.visitAddr
                                        }
                                        this.dueHallId = ''
                                        this.dueHallName = ''
                                    }
                                    if(obj.followDealType == '到厅'){
                                        if(obj.dueHallId){
                                            this.dueHallId = obj.dueHallId
                                            this.dueHallName = obj.dueHallName
                                        }
                                        this.visitAddr = ''
                                    }
                                }

                                if(obj.contactsName){
                                    this.contactsName = obj.contactsName
                                }
                                if(obj.contactsMsisdn){
                                    this.contactsMsisdn = obj.contactsMsisdn
                                }

                                //跟进 最新推荐政策及跟进备注内容
                                if(obj.latestRecommendPolicy){
                                    this.latestRecommendPolicy1 = obj.latestRecommendPolicy
                                }
                                if(obj.customerIsAccept){
                                    this.customerIsAccept1 = obj.customerIsAccept
                                }
                                if(obj.remark){
                                    this.followRemark = obj.remark
                                }

                            }
                        }
                    }else {
                        this.$alert(retMsg || 'IOP-用户预警跟进历史查询失败');
                    }
                })
            },
            goPerson(){
                this.srcCorm = this.$route.query.srcCorm;
                this.$router.push({
                    path: '/marketingAssistant',
                    query: {
                        telnum: this.mobile,
                        conform:"provinWarningByIop",
                        workId:this.workId,
                        subId:this.subId,
                        warnLevel:this.warnLevel,
                        limitDate:this.limitDate,
                        warnType:this.warnType,
                        srcCorm:this.srcCorm,
                        queryType:'2',
                    },
                })
            },
            goHistory(item,titleName){
                this.$router.push({
                    path: '/warnFollowListToIop',
                    query: {
                        mobile: this.mobile,
                        workId:this.workId,
                        subId:this.subId,
                        queryType:item,
                        titleName:titleName
                    },
                })
            },
            //放大照片
            zoomStorePhoto(url) {
                this.$refs.bigImg.src = url
                this.bigImgFlg = true
            },
            transfer(){
                this.transferFlag = true;
            },
            closeExecute(){
                this.transferFlag = false;
            },
            getExecute(item){
                console.info("转派",item);
                this.zpReceiveName = item.execName;
                this.zpReceiveMobile = item.execMsisdn;
                this.transferFlag = false;
            },
            changeIsYc2(item){
                if(this.abnormalFlag2 == item){
                    this.abnormalFlag2 = '';
                }else{
                    this.abnormalFlag2 = item;
                }
            },
            errorTypeChoose2(){
                let _this = this;
                NlDropdown({
                        confirmBtn: false,
                        datalist: this.errorList
                    },
                    function (retVal) {
                        //获取返回回调
                        _this.abnormalType2 = retVal.id;
                    })
            },
            addEviByTher(){
                let itm = {
                    evidenceType:'',
                    evidenceKeys:'',
                    evidenceOtherRemark:'',
                    isCamera:false,
                    compoentId:'eviListByTher'+new Date().getTime(),
                };
                this.eviListByTher.push(itm);
                console.info("this.eviListByTher addEviByTher",this.eviListByTher)
            },
            deleteEvi(index){
                this.eviListByTher.splice(index, 1);
                console.info("this.eviListByTher deleteEvi",this.eviListByTher)
            },
            evidenceTypeChoose(item){
                let _this = this;
                NlDropdown({
                        confirmBtn: false,
                        datalist: _this.evidenceTypeList
                    },
                    function (retVal) {
                        //获取返回回调
                        item.evidenceType = retVal.id;
                        console.info("item",item)
                    })
            },
            openCamEvi(item,index){
                this.videoAndPhotoFlag = true;
                this.videoMainId = item.compoentId;
                this.currentIndex = index;
                console.info("openCamEvi", this.videoMainId,this.currentIndex);
            },
            getEvidencePrev(){
                this.videoAndPhotoFlag = false;
            },
            getEvidenceClose(VideoName,photoStrList,audioName){
                this.videoAndPhotoFlag = false;
                let evidenceVideoList = VideoName ? VideoName : ''   // ---视频
                let evidencePhotoList = photoStrList ? photoStrList : ''   //--照片
                let evidenceAudioList = audioName ? audioName : ''   //--录音
                if(evidenceVideoList != '' || evidencePhotoList != '' || evidenceAudioList != ''){
                    this.eviListByTher[this.currentIndex].isCamera = true;
                }else {
                    this.eviListByTher[this.currentIndex].isCamera = false;
                }
                console.info("取证视频",evidenceVideoList);
                console.info("取证图片",evidencePhotoList);
                console.info("取证录音",evidenceAudioList);

                let quzhengkey = '';
                if(evidenceVideoList != ''){
                    quzhengkey = evidenceVideoList;
                    if(evidencePhotoList != ''){
                        quzhengkey = evidenceVideoList + ','+evidencePhotoList;
                        if(evidenceAudioList != ''){
                            quzhengkey = quzhengkey +','+evidenceAudioList;
                        }
                    }else {
                        if(evidenceAudioList != ''){
                            quzhengkey = quzhengkey +','+evidenceAudioList;
                        }
                    }
                }else {
                    if(evidencePhotoList != ''){
                        quzhengkey = evidencePhotoList;
                        if(evidenceAudioList != ''){
                            quzhengkey = quzhengkey + ','+ evidenceAudioList;
                        }
                    }else{
                        if(evidenceAudioList != ''){
                            quzhengkey = evidenceAudioList;
                        }
                    }
                }
                this.eviListByTher[this.currentIndex].evidenceKeys = quzhengkey;
            },
            getVideoMainId(videoMainId){
                this.videoMainId = videoMainId;
                console.info("取证类型 videoMainId",this.videoMainId)
                console.info("取证类型 videoMainId",this.eviListByTher)
            },


            changeIsRightFlag2(item){
                if(this.rightFlag2 == item){
                    this.rightFlag2 = '';
                }else{
                    this.rightFlag2 = item;
                }
            },

            submitByTher(){
                //防止重复点击
                if (this.isClickByTher) {
                    return;
                }
                if(this.zpReceiveMobile == ''){
                    this.$alert("请选择转派人员")
                    return
                }
                //把 转派模块下的“异动情况”整个改成非必填
                // if(this.abnormalFlag2 == ''){
                //     this.$alert("请选择异动情况")
                //     return
                // }
                if(this.abnormalFlag2 == '异动'){
                    this.abnormalBasis2 = '';
                    this.rightFlag2 = '';

                    // if(this.abnormalType2 ==''){
                    //     this.$alert("请选择异动类型")
                    //     return
                    // }else {
                    //     this.abnormalBasis2 = '';
                    //     this.rightFlag2 = '';
                    // }
                }else if(this.abnormalFlag2 == '无异动'){
                    this.abnormalType2 = '';
                    this.eviListByTher = [];

                    // if(this.rightFlag2 == ''){
                    //     this.$alert("请选择预警信息是否准确")
                    //     return
                    // }else {
                    //     this.abnormalType2 = '';
                    //     this.eviListByTher = [];
                    // }
                }

                if(this.eviListByTher && this.eviListByTher.length > 0){
                    for (let i= 0; i< this.eviListByTher.length;i++){
                        let aaaa = i + 1;
                        //取证类型上传了，但是取证类别没选
                        if(this.eviListByTher[i].evidenceKeys && this.eviListByTher[i].evidenceType == ''){
                            this.$alert("第"+aaaa+"条取证项，请选择取证类型")
                            return
                        }
                        if(this.eviListByTher[i].evidenceType){
                            if(this.eviListByTher[i].evidenceType == '其他'){
                                if(this.eviListByTher[i].evidenceOtherRemark == ''){
                                    this.$alert("第"+aaaa+"条取证项，请输入具体取证说明")
                                    return
                                }
                            }
                            if(this.eviListByTher[i].evidenceKeys == ''){
                                this.$alert("第"+aaaa+"条取证项，请上传取证信息")
                                return
                            }
                        }
                    }
                }

                this.isClickByTher = true;
                this.submitCmopByTher();
                //获取用户当前位置 --去掉
                // ClientJs.getLocation('', 'getLocationWithWarnDeal');
            },
            submitCmopByTher(locationParam){
                let arr = [];

                if(this.eviListByTher && this.eviListByTher.length > 0){
                    for (let i= 0; i< this.eviListByTher.length;i++){
                        let aaaa = i + 1;
                        //取证类型上传了，但是取证类别没选
                        if(this.eviListByTher[i].evidenceKeys && this.eviListByTher[i].evidenceType == ''){
                            this.$alert("第"+aaaa+"条取证项，请选择取证类型")
                            return
                        }
                        if(this.eviListByTher[i].evidenceType){
                            if(this.eviListByTher[i].evidenceType == '其他'){
                                if(this.eviListByTher[i].evidenceOtherRemark == ''){
                                    this.$alert("第"+aaaa+"条取证项，请输入具体取证说明")
                                    return
                                }
                            }
                            if(this.eviListByTher[i].evidenceKeys == ''){
                                this.$alert("第"+aaaa+"条取证项，请上传取证信息")
                                return
                            }
                        }

                        if(this.eviListByTher[i].evidenceType){
                            arr.push(this.eviListByTher[i]);
                        }
                    }
                }


                let params = {
                    workId:this.workId,
                    subId:this.subId,
                    mobile:this.mobile,
                    warnType:this.warnType,
                    submitSource:'1',
                    dealAction:'1',
                    // location:locationParam.address,
                    // longitude:locationParam.longitude,
                    // latitude:locationParam.latitude,
                    zpReceiveName:this.zpReceiveName,
                    zpReceiveMobile:this.zpReceiveMobile,
                    dealName:Storage.session.get('userInfo').operatorName,
                    dealMobile:Storage.session.get('userInfo').servNumber,
                    dealOperator:Storage.session.get('userInfo').staffId,
                    dealDate:dateFormat(new Date(), "yyyyMMdd hh:mm:ss"),
                    abnormalFlag:this.abnormalFlag2,
                    abnormalType:this.abnormalType2,
                    evidenceList:arr.length > 0?JSON.stringify(arr):null,
                    abnormalBasis:this.abnormalBasis2,
                    rightFlag:this.rightFlag2,
                    remark:this.transSuss,//转派备注
                    latestRecommendPolicy:this.latestRecommendPolicy3,
                    customerIsAccept:this.customerIsAccept3
                }
                console.info("转派提交报文",params);

                this.submitCmop(params);
            },
            submitCmop(params){
                this.$http.post('/xsb/gridCenter/warningOrder/h5YjDealToIop',params).then(res => {
                    let{retCode,retMsg} = res.data;
                    console.info("h5YjDealToIop",res.data)
                    if (retCode == '0'){
                        //常用意见提交
                        this.commonRemarkSub();
                        if(this.isShow == '3'){
                            this.$toast("转派成功");
                            this.isClickByTher = true;
                            this.goPrev();
                        }
                        if(this.isShow == '2'){
                            if(params.dealActionFeedback == '1'){//跟进
                                this.$toast("提交成功");
                                this.isClickByDeal = true;
                                this.clearAttacted();
                                //证据链上传水印
                                Storage.session.remove('shuiYinByEvi');
                                //预处理及处理反馈水印
                                Storage.session.remove('shuiYinByWarning');
                                Storage.session.remove('objPhotoList');
                                Storage.session.remove('failResonByWarn');
                                this.goShuaixin();
                            }
                            if(params.dealActionFeedback == '2'){//结单
                                this.$toast("提交成功");
                                this.isClickByDeal = true;
                                if(this.dealResult != '维护失败'){
                                    this.goLihua();
                                }else{
                                    this.goPrev();
                                }

                            }
                        }
                        //跟进，页面刷新
                        if(this.isShow == '1'){
                            this.$toast("提交成功");
                            this.isClickByDeal = true;
                            this.goShuaixin();
                        }
                    }else {
                        this.$alert(retMsg || '经分-预警处理提交接口失败');
                    }
                })
            },
            //页面刷新
            goShuaixin(){
                //页面刷新
                console.info("页面刷新");
                this.$router.push({
                    path: '/kongBaiPage',
                })
                setTimeout(()=>{
                    this.$router.push({
                        path: '/provinWarningByIop',
                        query: {
                            workId : this.workId,
                            subId : this.subId,
                            warnLevel : this.warnLevel,
                            limitDate : this.limitDate,
                            warnType : this.warnType,
                            srcCorm : this.srcCorm,
                        }
                    })
                },300)
            },
            //放礼花页面
            goLihua() {
                this.srcCorm = this.$route.query.srcCorm;
                this.$router.push({
                    path: '/liHuaBack',
                    query: {
                        telnum: this.mobile,
                        conform:"provinWarningByIop",
                        workId:this.workId,
                        subId:this.subId,
                        warnLevel:this.warnLevel,
                        limitDate:this.limitDate,
                        mobile:this.mobile,
                        warnType:this.warnType,
                        srcCorm:this.srcCorm
                    },
                })
                this.clearAttacted();
                //证据链上传水印
                Storage.session.remove('shuiYinByEvi');
                //预处理及处理反馈水印
                Storage.session.remove('shuiYinByWarning');
                Storage.session.remove('objPhotoList');
                Storage.session.remove('failResonByWarn');
            },
            isShows(item){
                this.isShow = item;
                //跟进中，跟进人默认是当前用户
                if(this.isShow === '1'){
                    if(this.followMsisdn === ''){
                        this.followMsisdn = Storage.session.get('userInfo').servNumber;
                        this.followName = Storage.session.get('userInfo').operatorName;
                    }
                }
            },
            //格式校验
            numJude(flag){
                var reg1 = /^[\d]*$/;
                //校验跟进人手机号
                if(flag == '1'){
                    if(!reg1.test(this.followMsisdn)) {
                        this.$toast("请输入数字");
                        this.followMsisdn = this.followMsisdn.replace(/[^0-9]/ig, '')
                    }
                }
                //校验客户侧电话
                if(flag == '2'){
                    if(!reg1.test(this.contactsMsisdn)) {
                        this.$toast("请输入数字");
                        this.contactsMsisdn = this.contactsMsisdn.replace(/[^0-9]/ig, '')
                    }
                }
            },
            chooseAppointType(item){
                if(this.followDealType === item){
                    this.followDealType = ''
                }else {
                    this.followDealType = item
                }
            },
            chooseCusAcc1(item){
                if(this.customerIsAccept1 === item){
                    this.customerIsAccept1 = ''
                }else {
                    this.customerIsAccept1 = item
                }
            },
            chooseCusAcc2(item){
                if(this.customerIsAccept2 === item){
                    this.customerIsAccept2 = ''
                }else {
                    this.customerIsAccept2 = item
                }
            },
            chooseCusAcc3(item){
                if(this.customerIsAccept3 === item){
                    this.customerIsAccept3 = ''
                }else {
                    this.customerIsAccept3 = item
                }
            },
            //时间选择
            chooseAppointTime(){
                let self = this
                NlDatePicker({
                    startDate: self.dueTime == '' ? dateFormat(new Date(), "yyyy-MM-dd hh:mm:ss") : dateFormat(chgStrToDate(self.dueTime), ("yyyy-MM-dd hh:mm:ss")),
                    dateType: 'datetime',
                    format: 'yyyy-MM-dd hh:mm:ss',
                    onlyOne: true,
                }, (retVal) => {
                    self.dueTime = retVal.startDate
                });
            },
            //9级地址选择
            openAddChoose(){
                let self = this;
                VisitAddress({
                    popFlag: true,
                    telnum: self.mobile,
                    title: '上门地址选择'
                }, function (obj) {
                    console.info("openAddChoose",obj);
                    self.visitAddr = obj.fullName || obj.villageAddress;
                });
            },
            //营业厅选择
            openHallChoose(){
                this.hallShow = true
            },
            colseHallChoose(){
                this.hallShow = false
            },
            chooseHallInfo(name,id){
                console.info("营业厅选择",name,id)
                this.hallShow = false
                this.dueHallName = name
                this.dueHallId = id
                this.hallShow = false
            },
            submitByFollow(){
                //防止重复点击
                if(this.isClickByDeal){
                    return;
                }
                if(this.followName == ''){
                    this.$alert("请输入跟进人姓名");
                    return;
                }
                if(this.followMsisdn == ''){
                    this.$alert("请输入跟进人电话");
                    return;
                }
                var reg2 = /^1(?:3\d|4[4-9]|5[0-35-9]|6[67]|7[013-8]|8\d|9\d)\d{8}$/;
                if(!reg2.test(this.followMsisdn)){
                    this.$alert("请输入正确的跟进人电话");
                    this.followMsisdn = "";
                    return;
                }
                if(this.followDealType == ''){
                    //预约方式下采集置空
                    this.dueHallName = '';
                    this.dueHallId = '';
                    this.visitAddr = '';
                    this.dueTime = '';
                }else {
                    if(this.dueTime == ''){
                        this.$alert("请选择预约时间");
                        return;
                    }

                    if(this.followDealType == '电话'){
                        this.dueHallName = '';
                        this.dueHallId = '';
                        this.visitAddr = '';
                    }
                    if(this.followDealType == '上门'){
                        if(this.visitAddr == ''){
                            this.$alert("请选择上门地址")
                            return;
                        }else {
                            //上门下的相关采集置空
                            this.dueHallName = '';
                            this.dueHallId = '';
                        }
                    }
                    if(this.followDealType == '到厅'){
                        if(this.dueHallId == ''){
                            this.$alert("请选择预约厅店")
                            return;
                        }else {
                            //到厅下的相关采集置空
                            this.visitAddr = '';
                        }
                    }

                }

                this.isClickByDeal = true;
                this.isShowLocal = true;
                //获取用户当前位置
                ClientJs.getLocation('', 'getLocationWithFollow');
            },
            //更进提交报文
            submitCmopWithFollow(locationParam){
                let params = {
                    workId:this.workId,
                    subId:this.subId,
                    mobile:this.mobile,
                    warnType:this.warnType,
                    submitSource:'1',
                    dealAction:'5',//跟进
                    followDealType:this.followDealType,
                    followName:this.followName,
                    followMsisdn:this.followMsisdn,
                    dueTime:this.dueTime ? dateFormat(chgStrToDate(this.dueTime), ("yyyyMMdd hh:mm:ss")) :'',
                    visitAddr:this.visitAddr,
                    contactsName:this.contactsName,
                    contactsMsisdn:this.contactsMsisdn,
                    dueHallId:this.dueHallId,
                    dueHallName:this.dueHallName,
                    location:locationParam.address,
                    longitude:locationParam.longitude,
                    latitude:locationParam.latitude,
                    dealName:Storage.session.get('userInfo').operatorName,
                    dealMobile:Storage.session.get('userInfo').servNumber,
                    dealOperator:Storage.session.get('userInfo').staffId,
                    dealDate:dateFormat(new Date(), "yyyyMMdd hh:mm:ss"),
                    latestRecommendPolicy:this.latestRecommendPolicy1,
                    customerIsAccept:this.customerIsAccept1,
                    remark:this.followRemark
                }
                console.info("跟进提交报文",params);

                this.submitCmop(params);
            },
            //处理反馈
            submitByDeal(){
                //防止重复点击
                if(this.isClickByDeal){
                    return;
                }

                if(this.dealType == ''){
                    this.$alert("请选择处理方式")
                    return
                }else {
                    if(this.dealType.indexOf('上门') > -1){
                        if(this.clockAddress == ''){
                            this.$alert("请输入上门地址")
                            return
                        }
                        if(this.clfkPhoList.length == 0){
                            this.$alert("请上传现场照片")
                            return
                        }
                        // if(this.smDealName == ''){
                        //     this.$alert("请选择上门协同人员")
                        //     return
                        // }
                    }else {
                        this.clockAddress = '';
                        this.clockLongitude = '';
                        this.clockLatitude = '';
                        this.photoKeys = '';
                        this.smDealName = '';
                        this.smDealMobile = '';
                    }
                }
                if(this.abnormalFlag == ''){
                    this.$alert("请选择异动情况")
                    return
                }
                if(this.abnormalFlag == '异动'){
                    if(this.abnormalType ==''){
                        this.$alert("请选择异动类型")
                        return
                    }else {
                        this.abnormalBasis = '';
                        this.rightFlag = '';
                    }
                }else if(this.abnormalFlag == '无异动'){
                    if(this.rightFlag == ''){
                        this.$alert("请选择预警信息是否准确")
                        return
                    }else {
                        this.abnormalType = '';
                        this.eviList = [];
                    }
                }



                if(this.eviList && this.eviList.length > 0){
                    for (let i= 0; i< this.eviList.length;i++){
                        let aaaa = i + 1;
                        //取证类型上传了，但是取证类别没选
                        if(this.eviList[i].evidenceKeys && this.eviList[i].evidenceType == ''){
                            this.$alert("第"+aaaa+"条取证项，请选择取证类型")
                            return
                        }
                        if(this.eviList[i].evidenceType){
                            if(this.eviList[i].evidenceType == '其他'){
                                if(this.eviList[i].evidenceOtherRemark == ''){
                                    this.$alert("第"+aaaa+"条取证项，请输入具体取证说明")
                                    return
                                }
                            }
                            if(this.eviList[i].evidenceKeys == ''){
                                this.$alert("第"+aaaa+"条取证项，请上传取证信息")
                                return
                            }
                        }
                    }
                }

                //最新推荐政策校验
                if(this.latestRecommendPolicy2 == ''){
                    this.$alert("请输入最新推荐政策")
                    return
                }
                if(this.customerIsAccept2 == ''){
                    this.$alert("请选择客户是否接受")
                    return
                }



                if(this.dealResult == ''){
                    this.$alert("请选择维护结果")
                    return
                }
                if(this.dealResult == '维护失败'){
                    if(this.defeatReasonList.length == 0){
                        this.$alert("请选择失败原因")
                        return
                    }else {
                        this.fakerDesc = '';
                        this.fakerKeys = '';
                        this.fakerOtherDesc = '';
                    }
                }else if(this.dealResult == '维护成功且反杀'){
                    if(this.fakerDesc == ''){
                        this.$alert("请选择反杀详情")
                        return
                    }else{
                        if(this.fakerDesc == '其他' && this.fakerOtherDesc == ''){
                            this.$alert("请填写反杀详情说明")
                            return
                        }
                    }
                    if(this.fakerKeys == ''){
                        this.$alert("请上传反杀证据")
                        return
                    }else {
                        this.defeatReason = '';
                        this.defeatRemark = '';
                        this.defeatOtherDesc = '';
                        this.defeatReasonList = []
                    }
                }else {//维护成功
                    this.fakerDesc = '';
                    this.fakerKeys = '';
                    this.fakerOtherDesc = '';

                    this.defeatReason = '';
                    this.defeatRemark = '';
                    this.defeatOtherDesc = '';
                    this.defeatReasonList = []
                }
                this.isClickByDeal = true;
                this.isShowLocal = true;
                //获取用户当前位置
                ClientJs.getLocation('', 'getLocationWithHandel');




            },
            submitCmopWithHandel(locationParam){
                let arr = [];
                if(this.eviList && this.eviList.length > 0){
                    for (let i= 0; i< this.eviList.length;i++){
                        let aaaa = i + 1;
                        //取证类型上传了，但是取证类别没选
                        if(this.eviList[i].evidenceKeys && this.eviList[i].evidenceType == ''){
                            this.$alert("第"+aaaa+"条取证项，请选择取证类型")
                            return
                        }

                        if(this.eviList[i].evidenceType){
                            if(this.eviList[i].evidenceType == '其他'){
                               if(this.eviList[i].evidenceOtherRemark == ''){
                                   this.$alert("第"+aaaa+"条取证项，请输入具体取证说明")
                                   return
                               }
                            }
                            if(this.eviList[i].evidenceKeys == ''){
                                this.$alert("第"+aaaa+"条取证项，请上传取证信息")
                                return
                            }
                            if(this.eviList[i].evidenceKeys){
                                arr.push(this.eviList[i]);
                            }
                        }
                    }
                }

                let params = {
                    workId:this.workId,
                    subId:this.subId,
                    mobile:this.mobile,
                    warnType:this.warnType,
                    submitSource:'1',
                    dealAction:'2',//处理反馈
                    dealActionFeedback:'2',//结单
                    location:locationParam.address,
                    longitude:locationParam.longitude,
                    latitude:locationParam.latitude,
                    dealName:Storage.session.get('userInfo').operatorName,
                    dealMobile:Storage.session.get('userInfo').servNumber,
                    dealOperator:Storage.session.get('userInfo').staffId,
                    dealDate:dateFormat(new Date(), "yyyyMMdd hh:mm:ss"),
                    dealType:this.dealType,
                    clockAddress:this.clockAddress,
                    clockLongitude:this.clockLongitude,
                    clockLatitude:this.clockLatitude,
                    photoKeys:this.clfkPhoList.join(","),
                    zpReceiveName:this.smDealName,
                    zpReceiveMobile:this.smDealMobile,
                    abnormalFlag:this.abnormalFlag,
                    abnormalType:this.abnormalType,
                    evidenceList:arr.length > 0?JSON.stringify(arr):null,
                    abnormalBasis:this.abnormalBasis,
                    rightFlag:this.rightFlag,
                    dealResult:this.dealResult,
                    fakerDesc:this.fakerDesc,
                    fakerKeys:this.fakerKeys,
                    fakerOtherDesc:this.fakerOtherDesc,
                    defeatReason:this.defeatReason,
                    defeatRemark:this.defeatRemark,
                    defeatOtherDesc:this.defeatOtherDesc,
                    zjlFileKeys:this.zjlFileKeys,
                    remark:this.clfkRemark,//转派备注
                    defeatReasonList: this.defeatReasonList.length > 0 ? JSON.stringify(this.defeatReasonList) : null,
                    latestRecommendPolicy:this.latestRecommendPolicy2,
                    customerIsAccept:this.customerIsAccept2
                }
                console.info("结单提交报文",params);

                this.submitCmop(params);
            },


            chooseDealType(item){
                if(!item.chooseFlag){
                    item.chooseFlag = true;
                }else {
                    item.chooseFlag = false;
                }

                let choosed = [];
                for (let i=0;i < this.dealTypeList.length;i++){
                    if(this.dealTypeList[i].chooseFlag){
                        choosed.push(this.dealTypeList[i].label);
                    }
                }
                this.dealType = choosed.join(',');
                console.info("choosed",choosed);
                console.info("this.dealType",this.dealType);

            },
            openLocal(){
                this.showMapFlag = true;
            },
            closeMap(){
                this.showMapFlag = false;
            },
            //获取上门地址信息
            confirmMap(data){
                console.info("map data" , data)
                this.clockLongitude = data.markLnglatVal[0];
                this.clockLatitude = data.markLnglatVal[1];
                this.clockAddress = data.markAddress;
            },
            // 打开拍照组件
            openCamTwo(){
                this.clfkShowCameraFlg = !this.clfkShowCameraFlg;
            },
            getCloseCLFK(){
                this.clfkShowCameraFlg = false;
            },
            signCameraCbCLFK(cnt,smsPhotoList){
                this.clfkShowCameraFlg = false;
                this.isCameraTwo = !!cnt;
                this.clfkPhoList = smsPhotoList;
                console.info("处理反馈 signCameraCb signCameraCbCLFK",this.clfkPhoList)
            },
            getCLFKPhotoMainId(photoMainId){
                this.clfkPhotoMainId = photoMainId;
                console.info("处理反馈 signCameraCb getCLFKPhotoMainId",this.clfkPhotoMainId)
            },
            transferByDeal(){
                this.transferByDealFlag = true;
            },
            closeExecuteTwo(){
                this.transferByDealFlag = false;
            },
            getExecuteTwo(item){
                console.info("上门协同人员",item);
                this.smDealName = item.execName;
                this.smDealMobile = item.execMsisdn;
                this.transferByDealFlag = false;
            },
            changeIsYc(item){
                this.abnormalFlag = item;
            },
            errorTypeChoose(){
                let _this = this;
                NlDropdown({
                        confirmBtn: false,
                        datalist: this.errorList
                    },
                    function (retVal) {
                        //获取返回回调
                        _this.abnormalType = retVal.id;
                    })
            },
            changeIsRightFlag(item){
                this.rightFlag = item;
            },
            addEvi(){
                let itm = {
                    evidenceType:'',
                    evidenceKeys:'',
                    evidenceOtherRemark:'',
                    isCamera:false,
                    compoentId:'eviList'+new Date().getTime(),
                };
                this.eviList.push(itm);
                console.info("this.eviList addEvi",this.eviList)
            },
            deleteEviByDeal(index){
                this.eviList.splice(index, 1);
                console.info("this.eviList deleteEviByDeal",this.eviList)
            },
            evidenceTypeChoose2(item){
                let _this = this;
                NlDropdown({
                        confirmBtn: false,
                        datalist: _this.evidenceTypeList
                    },
                    function (retVal) {
                        //获取返回回调
                        item.evidenceType = retVal.id;
                        console.info("item",item)
                    })
            },
            openCamEviByDeal(item,index){
                this.videoAndPhotoDealFlag = true;
                this.videoMainIdByDeal = item.compoentId;
                this.currentIndexByDeal = index;
                console.info("openCamEviByDeal", this.videoMainIdByDeal,this.currentIndexByDeal);
            },
            getEvidencePrev2(){
                this.videoAndPhotoDealFlag = false;
            },
            getEvidenceClose2(VideoName,photoStrList,audioName){
                this.videoAndPhotoDealFlag = false;
                let evidenceVideoList = VideoName ? VideoName : ''   // ---视频
                let evidencePhotoList = photoStrList ? photoStrList : ''   //--照片
                let evidenceAudioList = audioName ? audioName : ''   //--录音
                if(evidenceVideoList != '' || evidencePhotoList != '' || evidenceAudioList != ''){
                    this.eviList[this.currentIndexByDeal].isCamera = true;
                }else {
                    this.eviList[this.currentIndexByDeal].isCamera = false;
                }
                console.info("取证视频",evidenceVideoList);
                console.info("取证图片",evidencePhotoList);
                console.info("取证录音",evidenceAudioList);

                let quzhengkey = '';
                if(evidenceVideoList != ''){
                    quzhengkey = evidenceVideoList;
                    if(evidencePhotoList != ''){
                        quzhengkey = evidenceVideoList + ','+evidencePhotoList;
                        if(evidenceAudioList != ''){
                            quzhengkey = quzhengkey +','+evidenceAudioList;
                        }
                    }else {
                        if(evidenceAudioList != ''){
                            quzhengkey = quzhengkey +','+evidenceAudioList;
                        }
                    }
                }else {
                    if(evidencePhotoList != ''){
                        quzhengkey = evidencePhotoList;
                        if(evidenceAudioList != ''){
                            quzhengkey = quzhengkey + ','+ evidenceAudioList;
                        }
                    }else{
                        if(evidenceAudioList != ''){
                            quzhengkey = evidenceAudioList;
                        }
                    }
                }
                this.eviList[this.currentIndexByDeal].evidenceKeys = quzhengkey;
            },
            getVideoMainId2(videoMainId){
                this.videoMainIdByDeal = videoMainId;
                console.info("取证类型 videoMainIdByDeal",this.videoMainIdByDeal)
                console.info("取证类型 videoMainId",this.eviList)
            },
            handlerTouchstart(e) {
                e.preventDefault();
                //ios主动获取录音权限
                if (!this.rec && !(/android|harmony/gi.test(navigator.userAgent))) {
                    this.rec = new RecorderManger({}, '', this.getRecordResultCbFn);
                    return;
                }
                this.iosPermissionFlg = true;
                this.speakingFlg = true;
                this.rec.startRecorder();
                document.querySelector('.dialog-box').style.top = '42px'
            },
            handlerTouchend() {
                if(!this.iosPermissionFlg) return
                this.rec.endRecorder();
                this.speakingFlg = false;
                document.querySelector('.dialog-box').style.top = '-300px'

            },
            //录音回调
            getRecordResultCbFn(obj) {
                if (obj.code == '-1') {
                    this.$toast(obj.voiceTxt)
                    return
                }
                if (obj.voiceTxt) {
                    this.abnormalBasis = obj.voiceTxt;
                    this.abnormalBasis2 = obj.voiceTxt;
                }
            },
            maintanceResChoose(){
                let _this = this;
                NlDropdown({
                        confirmBtn: false,
                        datalist: this.maintanceResList
                    },
                    function (retVal) {
                        //获取返回回调
                        _this.dealResult = retVal.id;
                        console.info("dealResult",_this.dealResult);

                    })
            },
            openCamThree(){
                this.eviKeyFlag = true;
            },
            getEvidencePrev3(){
                this.eviKeyFlag = false;
            },
            getEvidenceClose3(VideoName,photoStrList,audioName){
                this.eviKeyFlag = false;
                let evidenceVideoList = VideoName ? VideoName : ''   // ---视频
                let evidencePhotoList = photoStrList ? photoStrList : ''   //--照片
                let evidenceAudioList = audioName ? audioName : ''   //--录音
                if(evidenceVideoList != '' || evidencePhotoList != '' || evidenceAudioList != ''){
                    this.isCameraThree = true;
                }else {
                    this.isCameraThree = false;
                }
                console.info("证据链视频",evidenceVideoList);
                console.info("证据链图片",evidencePhotoList);
                console.info("证据链录音",evidenceAudioList);
                let quzhengkey = '';
                if(evidenceVideoList != ''){
                    quzhengkey = evidenceVideoList;
                    if(evidencePhotoList != ''){
                        quzhengkey = evidenceVideoList + ','+evidencePhotoList;
                        if(evidenceAudioList != ''){
                            quzhengkey = quzhengkey +','+evidenceAudioList;
                        }
                    }else {
                        if(evidenceAudioList != ''){
                            quzhengkey = quzhengkey +','+evidenceAudioList;
                        }
                    }
                }else {
                    if(evidencePhotoList != ''){
                        quzhengkey = evidencePhotoList;
                        if(evidenceAudioList != ''){
                            quzhengkey = quzhengkey + ','+ evidenceAudioList;
                        }
                    }else{
                        if(evidenceAudioList != ''){
                            quzhengkey = evidenceAudioList;
                        }
                    }
                }
                this.zjlFileKeys = quzhengkey;
            },
            getVideoMainId3(videoMainId){
                this.evlKeyMainId = videoMainId;
                console.info("取证类型 evlKeyMainId",this.evlKeyMainId)
                console.info("取证类型 zjlFileKeys",this.zjlFileKeys)
            },
            failChooseClose(){
                this.showChooseFlag = false;
            },
            failSubmit(list){
                if(list && list.length > 0){
                    this.defeatReasonList = list;
                    this.isChooseFail = true;
                }else {
                    this.defeatReasonList = [];
                    this.isChooseFail = false;
                }
                console.info("failSubmit",list,this.defeatReasonList);
                this.showChooseFlag = false;
            },
            defeatReasonChoose(){
                // this.senceStatusFlag = true;
                this.showChooseFlag = true;
            },
            //取消
            resertChoose(){
                this.senceStatusFlag = false;
            },
            chooseLevel1(item){
                this.chooseOneId = item.id;
                this.senceStatusTwoList = item.children;
            },
            chooseLevel2(item){
                this.chooseTwoId = item.id;
            },
            sureChoose(){
                this.defeatReason = this.chooseOneId;
                this.defeatRemark = this.chooseTwoId;
                this.senceStatusFlag = false;
            },
            fakerDescChoose(){
                let _this = this;
                NlDropdown({
                        confirmBtn: false,
                        datalist: this.fakerDescList
                    },
                    function (retVal) {
                        //获取返回回调
                        _this.fakerDesc = retVal.id;
                        console.info("fakerDesc",_this.fakerDesc);

                    })
            },
            openCamFour(){
                this.fakerKeysFlag = true;
            },
            getEvidencePrev4(){
                this.fakerKeysFlag = false;
            },
            getEvidenceClose4(VideoName,photoStrList,audioName){
                this.fakerKeysFlag = false;
                let evidenceVideoList = VideoName ? VideoName : ''   // ---视频
                let evidencePhotoList = photoStrList ? photoStrList : ''   //--照片
                let evidenceAudioList = audioName ? audioName : ''   //--录音
                if(evidenceVideoList != '' || evidencePhotoList != '' || evidenceAudioList != ''){
                    this.isCameraFour = true;
                }else {
                    this.isCameraFour = false;
                }
                console.info("反杀详情视频",evidenceVideoList);
                console.info("反杀详情图片",evidencePhotoList);
                console.info("反杀详情录音",evidenceAudioList);
                let quzhengkey = '';
                if(evidenceVideoList != ''){
                    quzhengkey = evidenceVideoList;
                    if(evidencePhotoList != ''){
                        quzhengkey = evidenceVideoList + ','+evidencePhotoList;
                        if(evidenceAudioList != ''){
                            quzhengkey = quzhengkey +','+evidenceAudioList;
                        }
                    }else {
                        if(evidenceAudioList != ''){
                            quzhengkey = quzhengkey +','+evidenceAudioList;
                        }
                    }
                }else {
                    if(evidencePhotoList != ''){
                        quzhengkey = evidencePhotoList;
                        if(evidenceAudioList != ''){
                            quzhengkey = quzhengkey + ','+ evidenceAudioList;
                        }
                    }else{
                        if(evidenceAudioList != ''){
                            quzhengkey = evidenceAudioList;
                        }
                    }
                }
                this.fakerKeys = quzhengkey;
            },
            getVideoMainId4(videoMainId){
                this.fakerKeysMainId = videoMainId;
                console.info("反杀详情 fakerKeysMainId",this.fakerKeysMainId)
                console.info("反杀详情 fakerKeys",this.fakerKeys)
            },

            clickfkFlag(){
                this.fankuiRemarkFlag = !this.fankuiRemarkFlag
            },
            commonFkChoose(){
                let _this = this;
                NlDropdown({
                        confirmBtn: false,
                        datalist: _this.commonFkList
                    },
                    function (retVal) {
                        //获取返回回调
                        _this.fkRemarkCommon = retVal.id;
                        _this.clfkRemark = _this.fkRemarkCommon;
                    })
            },
            clickZpFlag(){
                this.zhuanpaiRemarkFlag = !this.zhuanpaiRemarkFlag;
            },
            commonZpChoose(){
                let _this = this;
                NlDropdown({
                        confirmBtn: false,
                        datalist: _this.commonZpList
                    },
                    function (retVal) {
                        //获取返回回调
                        _this.commonZhuanPaiRemark = retVal.id;
                        _this.transSuss = _this.commonZhuanPaiRemark;
                    })
            },
            commonRemarkSub(){
                let param = {}
                //跟进，直接返回
                if(this.isShow == '1'){
                    return
                }

                //转派
                if(this.isShow == '3'){
                    if(this.zhuanpaiRemarkFlag){
                        param = {
                            servNumber:Storage.session.get('userInfo').servNumber,
                            operName:Storage.session.get('userInfo').operatorName,
                            opintionDesc:this.transSuss,
                            operType:'转派'
                        }
                    }else {
                        return
                    }
                }
                if(this.isShow == '2'){
                    if(this.fankuiRemarkFlag){
                        param = {
                            servNumber:Storage.session.get('userInfo').servNumber,
                            operName:Storage.session.get('userInfo').operatorName,
                            opintionDesc:this.clfkRemark,
                            operType:'处理反馈'
                        }
                    }else {
                        return
                    }
                }

                this.$http.post('/xsb/gridCenter/warningOrder/h5InsertWarnOpintions',param).then(res => {
                    let{retCode,retMsg} = res.data;
                    if (retCode == '0'){
                        this.$toast('提交成功');
                    }else {
                        this.$toast(retMsg || '常用转派意见录入失败');
                    }
                })
            },

            getCommon(){
                this.CommonList("转派");
                this.CommonList("处理反馈");
            },
            CommonList(item){
                let param = {
                    servNumber:Storage.session.get('userInfo').servNumber,
                    operType:item
                }
                this.$http.post('/xsb/gridCenter/warningOrder/h5QryOpintionList',param).then(res => {
                    let{retCode,retMsg,data} = res.data;
                    if (retCode == '0'){
                        if(item == '转派'){
                            if(data && data.length > 0){
                                this.commonZpList = [{
                                    id:'',
                                    label:'请选择常用转派意见',
                                }]
                                data.forEach(item=>{
                                    let obj = {
                                        id:item.opintionDesc,
                                        label:item.opintionDesc,
                                    }
                                    this.commonZpList.push(obj);
                                })
                            }
                        }
                        if(item == '处理反馈'){
                            if(data && data.length > 0){
                                this.commonFkList = [{
                                    id:'',
                                    label:'请选择常用处理反馈意见',
                                }]
                                data.forEach(item=>{
                                    let obj = {
                                        id:item.opintionDesc,
                                        label:item.opintionDesc,
                                    }
                                    this.commonFkList.push(obj);
                                })
                            }
                        }
                    }else {
                        this.$toast(retMsg || '查询意见列表失败');
                    }
                })
            },

            crmQryEmployeeRole(){
                this.$http.post('/xsb/personBusiness/activeRecommend/h5qyrEmployeeRole').then((res) => {
                    let { retCode, data, retMsg } = res.data
                    // data 0-无权限或角色 1-有权限或角色
                    if (retCode === '0') {
                        this.showDownFlg = true;
                        this.retentionAuthority = data;
                    }
                })
            },
            goDownInfo(){
                let url = '/xsb/personBusiness/customerView/h5QryCgetusercust'
                this.$http.post(url, { telnum: this.mobile }).then(res => {
                    let resData = res.data
                    if (typeof (resData) != 'object') {//不是对象的话就是加密串
                        resData = decrptParam(res.data)
                        resData = JSON.parse(resData)
                    }
                    let { retCode, data } = resData
                    if (retCode == '0') {
                        let realstatecode = data.realstatecode
                        //港澳台胞证的
                        let certType2List = ['HKMCPassport', 'DriverIC', 'Passport', 'StudentID', 'TaiBaoZheng', 'UnionSocietyCredit']
                        let flag2 = certType2List.indexOf(data.certType)//判断证件类型
                        //需要实名制审核通过
                        if ((realstatecode === '4') || (realstatecode === '1' && flag2 > -1)) {
                            let obj = {
                                jqType: '0',   //0:服务密码，1:验证码，2:身份证
                                result: '0',             //鉴权结果，1为成功
                                authtype: '',
                                userName: data.userName,
                                telnum: this.mobile,
                                idCardWay: true,
                                userCity: data.userCity,//用户地市
                                userId: data.user_id //用户标识
                            }
                            Storage.session.set('jqData', obj);
                            this.$router.push({
                                path: '/downshiftRetention',
                                query: {
                                    retentionAuthority: this.retentionAuthority
                                }
                            })
                        }else {
                            this.$messagebox.alert('该号码状态是未审核，请使用实名制后再进行业务受理', '温馨提示').then((res) => {

                            })
                        }
                    } else {
                        this.$messagebox.alert('暂无此号码', '温馨提示').then((res) => {

                        })
                    }
                })
            },
        },
        filters: {
            //电话号码中间几位模糊化
            starTel(val) {
                if (!val) {
                    return '***'
                } else {
                    let reg = /^(\d{3})\d*(\d{4})$/
                    return val.replace(reg, '$1****$2')
                }
            },
            timeFiler(val){
                if(val){
                    return dateFormat(chgStrToDate(val), "yyyy-MM-dd hh:mm:ss");
                }else {
                    return '';
                }
            },
            yjFailReasonFilter(val){
                if(val == '0'){
                    return '呼叫3次，未联系上用户';
                }else if(val == '1'){
                    return '中途挂机';
                }else if(val == '2'){
                    return '已与客户沟通，无异动意愿';
                }else if(val == '3'){
                    return '与用户沟通，活动限制无法满足诉求';
                }else if(val == '4'){
                    return '与用户沟通，明确拒绝表示已经办理DX、LT等号卡、宽带';
                }else if(val == '5'){
                    return '存在异动情况，根据实际需求办理8/18套餐';
                }else if(val == '6'){
                    return '不存在异动情况，根据用户实际需求办理8/18套餐';
                }else {
                    return '';
                }
            },
            submitSourceFilter(val){
                if(val == '1'){
                    return '阿拉盯';
                }else if(val == '2'){
                    return '网格通PC';
                }else if(val == '3'){
                    return 'IOP';
                }else if(val == '4'){
                    return '在线';
                }else {
                    return '阿拉盯';
                }
            },

        }
    }
</script>

<style lang="less" scoped>
    @import "../../../base/less/variable.less";
    @import "../../../base/less/mixin.less";

    .pwd-wrapper {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        overflow: hidden;
    }

    .main-info {
        margin-top: 44px;
        border-top: 1px solid #E1E1E1;
        height: calc(100vh - 60px);
        overflow: auto;
        padding-bottom: 60px;
        box-sizing: border-box;

        .task-details{
            margin:10px;
            background-color: #fff;
            line-height: 23px;
            height: auto;
            box-shadow: 0 0 13px 0 rgba(200, 200, 200, 0.3946);

            .task-user {
                padding: 10px;
                background: linear-gradient(90deg, #FFFFFF 0%, #FFDBDB 100%);
                display: flex;
                align-items: center;
                font-size: 14px;
                justify-content: space-between;
                position: relative;

                .task-left {
                    display: flex;
                    align-items: center;

                    .task-img{
                        width: 8vh;
                    }

                    .top-right{
                        display: inline-block;
                        color: red;
                        padding: 0 12px 0 6px;
                        position: absolute;
                        right: 0;
                        top: 2px;
                        border-radius: 2px 4px 0 15px;
                        text-align: center;
                        width: 130px;
                        font-size: 14px;
                        font-weight: 600;

                        .timedeal{
                            font-size: 12px;
                        }
                    }

                    .task-center {
                        margin-left: 10px;

                        .user-info {
                            display: flex;
                            height: 30px;
                            align-items: center;

                            .user-num {
                                font-size: 16px;
                            }
                            .jiantou2icon{
                                font-size: 16px;
                                color: #117efb;
                            }
                        }
                    }

                    .task-number{
                        color: #1f1f1f;

                        font-size: 12px;
                        font-weight: 400;
                        word-break: break-all;

                        .label-info-type{
                            padding: 0 5px;
                            background: #fff;
                            border: 1px solid #fff;
                            border-radius: 2px;
                            margin-right: 5px;
                            display: inline-block;
                            margin-bottom: 5px;
                        }

                        .label-sign{
                            display: inline-block;
                            padding-right: 10px;
                        }
                    }
                }

                .iconwaihu{
                    font-size: 16px;
                    border: 1px solid #ffffff;
                    padding: 7px;
                    background: #ffffff;
                    color: #757575;
                    border-radius: 16px;
                    position: relative;

                    .choose-box{

                        margin: 54px 10px 0 10px;
                        background-color: #fff;
                        border-radius: 6px;
                        width: 100%;
                        position: absolute;
                        z-index: 1;
                        left: 10px;



                        .plan-model{
                            font-size: 14px;
                            position: absolute;
                            right: 11px;
                            top: -33px;
                            width: 130px;
                            background: #fff;
                            border-top-left-radius: 10px;
                            border-top-right-radius: 10px;
                            border-bottom-right-radius: 10px;
                            border-bottom-left-radius: 10px;
                            z-index: 2;
                            padding: 6px 0;
                            box-sizing: border-box;
                            text-align: center;
                            line-height: 32px;
                            box-shadow: 2px 2px 6px 0px rgba(0, 0, 0, 0.2);

                            .butto-waihu{
                                border-radius: 5px;
                                letter-spacing: 2px;
                                color: rgba(255, 255, 255, 1);
                                border: 1px solid rgba(221, 221, 221, 1);
                                background: rgba(22, 129, 251, 1);
                                margin: 5px 15px;

                                &.txt-grey{
                                    background:#ccc
                                }
                            }
                        }
                    }

                    .choose-box::after {
                        content: '';
                        position: absolute;
                        width: 0;
                        height: 0;
                        border-style: solid;
                        border-width:0 6px 12px 6px;
                        border-color: transparent transparent  #fff transparent;
                        top: -45px;
                        right: 30px;
                        border-left: 6px solid transparent;
                        border-right: 6px solid transparent;
                        filter: drop-shadow(5px 5px 10px rgba(0, 0, 0, 0.5));
                    }
                }
            }

            .yw-details{
                padding: 0 10px;

                .yw-list:last-child {
                    border-bottom:none;
                }

                .yw-list{
                    border-bottom: 1px solid #e5e5e5;
                    padding: 10px 0;

                    .first-info{
                        position: relative;
                        display: flex;
                        justify-content: space-between;

                        .first-left{

                            .first-before{
                                content: '';
                                width: 5px;
                                height: 5px;
                                border-radius: 6px;
                                position: absolute;
                                top: 8px;
                                background: #e32f2b;
                                border: 2px solid #e32f2b;
                            }

                            .first-type{
                                font-size: 14px;
                                color: #CA0D0D;
                                margin-left: 15px;

                                .first-time{
                                    font-size: 10px;
                                    color: #1681fb;
                                }
                            }

                        }
                    }

                    .info-deal{
                        display: flex;
                        justify-content: space-between;
                        white-space: nowrap;
                    }

                    .move-label-info{
                        font-size: 12px;
                        font-weight: 400;
                        word-break: break-all;

                        .yw-name{
                            color: #2F2F2F;
                        }
                        .yw-label{
                            color: #838383;
                            display: inline-flex;
                            max-width: 65%;
                            word-break: break-all;
                        }
                    }
                }


            }
        }

        .move-title{
            display: flex;
            justify-content: space-between;
            line-height: 16px;
            margin: 10px;

            .move-title-info{
                font-size: 14px;
                font-weight: 600;
                color: #000;
                display: flex;

                &:before{
                    display:inline-block;
                    content:'';
                    height:16px;
                    width: 3px;
                    background: #007aff;
                    border-radius: 1px;
                    margin-right: 5px;
                }
            }

            .move-history{
                font-size: 12px;
                font-weight: 600;
                color: #117efb;

                .jiantou2icon{
                    font-size: 12px;
                }
            }


        }

        .his-info{
            margin:10px;
            background-color: #fff;
            line-height: 23px;
            height: auto;
            box-shadow: 0 0 13px 0 rgba(200, 200, 200, 0.3946);


            .his-list{
                padding: 10px 10px;
                position: relative;

                .his-xiang{
                    font-size: 12px;
                    font-weight: 400;

                    .icon-checkbox {
                        font-size: 12px;
                        color: #31b84e;
                    }
                    .checkbox2{
                        color: #31b84e;
                    }
                    .his-time{
                        color:#838383 ;
                    }


                    .his-label-info{
                        font-size: 12px;
                        font-weight: 400;
                        word-break: break-all;

                        .his-name{
                            color: #2F2F2F;
                        }

                        .video-ul{
                            width: 100%;
                            box-sizing: border-box;
                            margin: 10px;
                            padding-right: 20px;

                            &:after {
                                .clearfloat();
                            }

                            .video-li {
                                float: left;
                                width: 100%;
                                /*height: 5.25rem;*/
                                overflow: hidden;
                                box-sizing: border-box;
                                margin: 0.2rem 0;
                                text-align: center;

                                .img-wrap {
                                    /*width: 100%;*/
                                    /*height: 10.25rem;*/
                                    /*display: inline-block;*/
                                    /*position: relative;*/

                                    video {
                                        width: 100%;
                                        height: 100%;
                                        border-radius: 4px;
                                    }
                                }
                            }
                        }

                        .photo-ul {
                            width: 100%;
                            box-sizing: border-box;
                            padding-right: 20px;
                            /* display:flex;
                            flex-wrap:wrap;
                            justify-content:space-between; */

                            &:after {
                                .clearfloat();
                            }

                            .photo-li {
                                float: left;
                                width: 33.333%;
                                height: 5.25rem;
                                overflow: hidden;
                                box-sizing: border-box;
                                margin: 0.2rem 0;
                                text-align: center;

                                .check-wrap {
                                    position: absolute;
                                    right: 8px;
                                    top: 8px;
                                    width: 1rem;
                                    height: 1rem;
                                    border-radius: 50%;
                                    color: #FFF;

                                }

                                .fenzu1 {
                                    color: #1681FB;
                                    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.74);
                                }

                                .juxing {
                                    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.74);
                                }

                                .img-wrap {
                                    width: 5.25rem;
                                    height: 5.25rem;
                                    display: inline-block;
                                    position: relative;

                                    img {
                                        width: 100%;
                                        height: 100%;
                                        border-radius: 4px;
                                    }
                                }

                                &.empty {
                                    color: #444;
                                    box-shadow: none;
                                    text-align: center;
                                    line-height: 6.25rem;
                                    font-size: 48px;

                                    .add-span {
                                        background: rgba(238, 238, 238, 1);
                                        width: 6.25rem;
                                        height: 6.25rem;
                                        border-radius: 4px;
                                        display: inline-block;
                                        text-align: center;
                                    }
                                }

                                /*&:nth-child(3n){
                                    text-align:right;
                                }
                                &:nth-child(3n+1){
                                    text-align:left;
                                }*/
                            }

                        }

                        .his-label{
                            color: #838383;
                            display: inline-flex;
                            max-width: 65%;
                            word-break: break-all;
                        }
                    }

                }
            }
        }

        .order-handle{
            margin: 10px;
            background: #fff;
            box-shadow: 0 0 13px 0 rgba(200, 200, 200, 0.3946);

            .order-tar {
                display: flex;
                border-bottom: 1px solid #C8C8C8;

                & > div {
                    flex: 1;
                    height: 40px;
                    text-align: center;
                    line-height: 40px;
                    background-color: #F8F8F8;
                    font-size: 14px;
                    font-weight: 600;
                    color: #6c6c6c;
                    box-sizing: border-box;

                    & > span{
                        font-size: 14px;
                        margin-right: 2px;
                    }
                }

                .choosed {
                    color: #1681FB;
                    border-bottom: 2px solid #1681FB;
                    background-color: #fff;
                }
            }

            .handle-details{
                font-size: 14px;
                border-bottom: 1px solid #E8E8E8;


                .handle-xiang {
                    height: 40px;
                    line-height: 40px;
                    margin: 0 16px 0 16px;
                    display: flex;
                    justify-content: space-between;

                    .handle-label{
                        color: #616161;
                        font-size: 12px;
                        display: flex;
                        align-items: center;

                        &.active{
                            color:#000000;
                            font-weight: 600;
                        }

                        .shanchu-icon{
                            font-size: 14px;
                            color: #d9001b;
                            margin-right: 10px;
                        }

                        .wihte-icon{
                            color:#ffffff;
                            margin-right: 10px;
                        }

                        &>span{
                            color: #EF2F12;
                        }
                    }

                    .xialakuang-111{
                        color: #bebcbc;
                        font-size: 13px;
                        height: 30px;
                        text-align: right;
                        width: 70%;
                        white-space: nowrap;
                        overflow-x: auto;
                        overflow-y: hidden;

                        .zi-ti{
                            font-size: 12px;
                            color: #333;
                        }


                        .iconphtot11{
                            font-size: 12px;
                            color: #1681fb;
                        }
                    }

                    .handle-value {
                        color: #bebcbc;
                        font-size: 13px;
                        height: 30px;
                        text-align: right;
                        width: 65%;

                        .jiantou-copy-copy {
                            font-size: 12px;
                            color: #007AFF;
                        }

                        .zi-ti{
                            font-size: 12px;
                            color: #333;
                        }

                        .iconphtot{
                            font-size: 12px;
                            color: #1681fb;
                        }

                        .flagButton {
                            /*min-width: 50px;*/
                            height: 24px;
                            margin-top: 8px;
                            font-size: 10px;
                            line-height: 24px;
                            color: #fff;
                            border-radius: 5px;
                            border: none;
                            background: #c9c9c9;
                            margin-left: 5px;

                            &.checkflag {
                                background: #0b7fff !important;
                            }
                        }

                        .icon-checkbox{
                            font-size: 12px;
                        }
                        .checkbox2 {
                            color: #1680F9;
                        }
                        .choose-zoti{
                            font-size: 12px;
                            line-height: 24px;
                            margin-right: 10px;
                            color: #444444;
                        }
                        .benren{
                            width: 90%;
                            font-size: 12px;
                            color: #333;
                            text-align: right;
                            background:#fff;
                        }

                        .fresh{
                            font-size: 14px;
                            color: #1681fb;
                        }

                        .input-text{
                            font-size: 12px;
                            width: 100%;
                            text-align: right;
                        }
                    }
                }

                .remark{
                    position: relative;
                    margin:10px;

                    .text-abl{
                        position: absolute;
                        right: 10px;
                        bottom: 10px;
                        font-size: 12px;
                        color: #929292;
                        z-index: 9;
                    }

                    .button {
                        float: right;
                        font-size: 12px;
                        color: #3e82f7;
                        margin-top: 10px;
                    }

                    textarea {
                        width: 100%;
                        border: 1px solid #999;
                        border-radius: 6px;
                        box-sizing: border-box;
                        height: 100px;
                        padding: 10px 10px;
                        font-size: 12px;
                    }
                }

                .common-flag{
                    color: #7f7f7f;
                    font-size: 12px;
                    text-align: right;
                    margin: 10px;

                    i{
                        font-size: 13px;
                        color: #979797;

                        &.check-box {
                            color: #5375FF
                        }
                    }
                }
            }

            .end-bottom-deal{
                width: 100%;
                height: 60px;
                background: rgba(255, 255, 255, 1);
                display: flex;
                justify-content: space-around;
                align-items: center;

                button {
                    display: block;
                    height: 40px;
                    border-radius: 25px;
                    font-size: 16px;
                    letter-spacing: 4px;
                    color: rgba(255, 255, 255, 1);
                    width: 90%;
                    border: 1px solid rgba(221, 221, 221, 1);;
                }

                .end-submit-follow {
                    background:#ccc;

                    &.active{
                        background:#ccc
                    }
                }

                .end-submit-com{
                    background: rgba(22, 129, 251, 1);

                    &.active{
                        background:#ccc
                    }
                }
            }

            .end-bottom{
                width: 100%;
                height: 60px;
                background: rgba(255, 255, 255, 1);
                display: flex;
                justify-content: space-around;
                align-items: center;

                button {
                    display: block;
                    height: 40px;
                    border-radius: 25px;
                    font-size: 16px;
                    letter-spacing: 4px;
                    color: rgba(255, 255, 255, 1);
                    width: 90%;
                    border: 1px solid rgba(221, 221, 221, 1);;
                }

                .end-submit {
                    background: rgba(22, 129, 251, 1);

                    &.active{
                        background:#ccc
                    }
                }
            }
        }
    }

    .big-img-wrap {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        background: #fff;
        z-index: 99;
        display: flex;
        align-items: center;

        .close-btn {
            position: absolute;
            right: 0.5rem;
            top: 0.5rem;
            font-size: 28px;
            padding: 0.5rem;
        }

        img {
            width: 100%;
        }
    }

    .camera-wrap {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 1000;
        background: #ECF0FA;
        overflow: auto;
    }

    .dialog-box {
        position: fixed;
        top: -300px;
        left: 50%;
        transform: translateX(-50%);
        width: 70vw;
        padding: 40px 20px;
        box-sizing: border-box;
        background: #f5f6f6;
        border-radius: 8px;
        text-align: center;
        z-index: 777;
        img {
            width: 100%;
            margin-bottom: 30px;
        }

    }

    .task-search-box {
        width: 100%;
        height: 360px;
        padding: 15px 0;
        overflow: hidden;
        background: #fff;
        position: fixed;
        left: 0;
        bottom: 0;
        border: 1px solid #E5E5E5;
        z-index: 1000;
        box-sizing: border-box;

        .top {
            width: 100%;
            height: 280px;
            overflow: hidden;
            display: flex;

            .lie {
                flex: 1;
                border-right: 1px solid #eee;
                height: 280px;
                box-sizing: border-box;
                padding: 0 5px;
                overflow: auto;

                .choose-item {
                    font-size: 14px;
                    padding: 0 15px 0 5px;
                    color: #888;
                    overflow: hidden;
                    position: relative;

                    margin: 5px 0;

                    .font-wenzi {
                        width: calc(100% - 7px);
                        //overflow: hidden;
                        //white-space: nowrap;
                        //text-overflow: ellipsis;
                        display: inline-block;
                        line-height: 36px;
                        white-space: nowrap;
                        overflow: auto;
                    }

                    .font-wenzi3 {
                        width: 100%;
                    }

                    .youjiantou1 {
                        font-size: 16px;
                        line-height: 36px;
                        float: right;
                        width: 16px;
                        position: absolute;
                        right: 5px;
                        top: 50%;
                        margin-top: -2px;
                        transform: translateY(-50%);
                    }
                }

                .active {
                    background: #D9EBFF;
                    color: #0b7fff;
                    border-radius: 4px;

                    .font-wenzi {
                        color: #0b7fff;

                    }
                }
            }

            .lie:first-child {
                flex: unset;
                width: 33%;
            }

            .lie:last-child {
                border: none;
            }

        }

        .bottom {
            width: 100%;
            margin-top: 11px;
            text-align: center;

            .chongzhi {
                display: inline-block;
                margin: 5px 25px;
                line-height: 32px;
                border-radius: 32px;
                background: #fff;
                border: 2px solid #0b7fff;
                box-sizing: border-box;
                color: #0b7fff;
                font-size: 14px;
                width: 120px;
            }

            .queren {
                display: inline-block;
                margin: 5px 25px;
                line-height: 36px;
                border-radius: 36px;
                background: #0b7fff;
                color: #fff;
                font-size: 14px;
                width: 120px;

            }
        }
    }
    .hide-box {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: rgba(0, 0, 0, .5);
    }

    .eject-box {
        position: fixed;
        left: 0;
        bottom: 0;
        top: 0;
        right: 0;
        opacity: 0.5;
        background: #000;
        z-index: 1000;
    }


    .eject-window.loading {
        width: 88% !important;
        border-radius: 10px;
        overflow: hidden;
        transform: translate(-50%, -50%);
        left: 50%;
    }


    .eject-window {
        position: fixed;
        width: 80%;
        left: 10%;
        transform: translateY(-50%);
        top: 50%;
        height: auto;
        z-index: 1001;

        .eject-detail-tittle {
            text-align: center;
            border-top-right-radius: 10px;
            border-top-left-radius: 10px;
            background-color: white;
            border-bottom: 1px #E9E9E9 solid;
            font-size: 16px;
            font-weight: 400;
            color: rgba(74, 74, 74, 1);
            line-height: 23px;
            padding: 20px 0;

            .connecting-btn-load {
                display: inline-block;
                vertical-align: -2px;
                width: 32px;
                padding-right: 8px;

            }
        }
    }
</style>
