<template>
    <div class="vd">
        <Header tsTitleTxt="客户脉动感知服务系统" backType="custom" @emGoPrev="goPrev"></Header>

        <div class='fix-content'>
            <!--tab切换-->
            <div class="order-tar">
                <div :class="{choosed:queryType == '2'}" @click="tabChoose('2')">待处理<span>({{ daiChuLiCnt }})</span></div>
                <div :class="{choosed:queryType == '3'}" @click="tabChoose('3')">已处理<span>({{ completeCnt }})</span></div>
                <div :class="{choosed:queryType == '4'}" @click="tabChoose('4')" v-if="viewFlag == 1">我的管辖<span>({{ totalNum }})</span></div>
            </div>
            <!--搜索框-->
            <div class='search-wrapper' v-show="queryType !== '4'">
                <div class='ipt-wrapper'>
                    <span class='iconfont sousuo1'></span>
                    <input class='seach-ipt' v-model='taskName' placeholder='请输入预警号码'/>
                    <span class='query-btn' @click='search()'>搜索</span>
                </div>

                <!--类型筛选框-->
                <div class='tabr-content'>
                    <div @click='chooseWarnType'>
                        <span :style="warnType? 'color: #3E7FE3': ''">{{ warnTypeName | warnTypeCheck }}</span>
                        <span class='iconfont jiantou11 size change-direct'></span>
                    </div>
                    <div @click='choseEmergent'>
                        <span :style="emergentId? 'color: #3E7FE3': ''">{{ emergentName | emergentCheck }}</span>
                        <span class='iconfont jiantou11 size change-direct'></span>
                    </div>
                    <div @click='choseTimeRange'>
                        <span :style="chooseType? 'color: #3E7FE3': ''">{{ timeRangeName | timeRangeCheck }}</span>
                        <span class='iconfont jiantou11 size change-direct'></span>
                    </div>

                    <div class="box-icon">
                        <div class="iconfont shejimulu"  @click.stop="paixFlag = !paixFlag">
                            <div class="paixu-box" v-show="paixFlag">
                                <div :class="sortFlag == 'arpu'? 'active' : ''" @click="changeSort('arpu')">
                                    按照ARPU值排序
                                    <i class="iconfont iconDown sort-pic" :class="{'active':arpuSort== 1}"></i>
                                    <i class="iconfont iconUp sort-pic" :class="{'active':arpuSort== 2}" style="margin-left: -11px;"></i>
                                </div>
                                <div :class="sortFlag == 'time'? 'active' : ''" @click="changeSort('time')">
                                    按照任务结束时间排序
                                    <i class="iconfont iconDown sort-pic" :class="{'active':timeSort== 1}"></i>
                                    <i class="iconfont iconUp sort-pic" :class="{'active':timeSort== 2}" style="margin-left: -11px;"></i>
                                </div>
                                <div :class="sortFlag == 'distance'? 'active' : ''" @click="changeSort('distance')">
                                    按照任务优先级排序
                                    <i class="iconfont iconDown sort-pic" :class="{'active':urgentSort==1}"></i>
                                    <i class="iconfont iconUp sort-pic" :class="{'active':urgentSort==2}" style="margin-left: -11px;"></i>
                                </div>
                            </div>
                        </div>
                        <div class="iconfont shaixuan icon-sx" @click.stop="commonFilter = !commonFilter"></div>
                    </div>
                </div>
            </div>
            <div class="jf-listfilter" v-show="queryType === '4'">
                <div class="sou-first" >
                    <div class="zuzhi">
                        <span class="iconfont zb zbtu"></span>
                        <span class="grid-diqu-add" @click="goChooseArea(orgaId)">{{orgaName}}</span>
                        <span class="iconfont youjiantou jiantu"></span>
                    </div>
                    <div class='sou-wrapper'>
                        <span class='iconfont sousuo1 sou1111'></span>
                        <input class='seach-sou' v-model='mobile' placeholder='请输入预警号码'/>
                        <span class='query-sou' @click='searchMobile()'>搜索</span>
                    </div>
                </div>

                <!--类型筛选框-->
                <div class="jf-content">
                    <div @click='taskStatusCheck'>
                        <span :style="taskStatus? 'color: #3E7FE3': ''">{{ taskStatusName | taskStatusCheck }}</span>
                        <span class='iconfont jiantou11 size change-direct'></span>
                    </div>
                    <div @click='isOpenBroadbandCheck'>
                        <span :style="isOpenBroadband? 'color: #3E7FE3': ''">{{ isOpenBroadbandName | isOpenBroadbandCheck }}</span>
                        <span class='iconfont jiantou11 size change-direct'></span>
                    </div>
                    <div @click='unusualChangeCheck'>
                        <span :style="unusualChange? 'color: #3E7FE3': ''">{{ unusualChangeName | unusualChangeCheck }}</span>
                        <span class='iconfont jiantou11 size change-direct'></span>
                    </div>
                    <div>
                        <div class="iconfont shaixuan icon-sx" @click="showFilter = !showFilter"></div>
                    </div>
                </div>
            </div>

        </div>
        <!--工单主体-->
        <div v-if="queryType !== '4'">
            <div class="task-list" v-show="taskList.length > 0">
                <mt-loadmore :top-method='loadTop' :bottom-method='loadBottom' :auto-fill='false' topPullText='刷新' topDropText='释放更新' topLoadingText='刷新中···' ref='loadmore'>
                    <div class='contents'>
                        <div class='content-list' v-for='item in taskList' :key='item.groupId' >

                            <div class='content-right' @click='toDetail(item)'>
                                <div class='content-time'>
                                    <img v-if='item.taskState == 2 || item.taskState == 4' src='../../../../static/img/done-1.png'/>
                                    <img v-if='item.taskState == 0 || item.taskState == 3' src='../../../../static/img/done-2.png'/>
                                    <img v-if='item.taskState == 6' src='../../../../static/img/done-3.png'/>
                                    <span>{{ item.taskTime|dateShow('yyyy/MM/dd hh:mm',true) }}</span>
                                    <span v-if='item.limitDate'>
                                        <span class='iconfont shijianxuanzejiantou'></span>
                                        <span class="chaoshi" v-if="judeLimit(item.limitDate)">{{ item.limitDate|dateShow('yyyy/MM/dd hh:mm',true) }}</span>
                                        <span v-if="!judeLimit(item.limitDate)">{{ item.limitDate|dateShow('yyyy/MM/dd hh:mm',true) }}</span>
                                    </span>
                                </div>

                                <div>
                                    <span class='top-left' v-if='item.emergent == 1 '>紧急</span>
                                    <span class='top-left urgent' v-if='item.emergent == 2 '>非常紧急</span>
                                    <span class='top-left yibanconf' v-if='item.emergent == 0 '>一般</span>
                                </div>

                                <div class='top'>
                                    <div class='top-right'>
                                        <span>{{ item.taskName | starTel }}</span>
                                        <span v-show="item.arpu" class="apru-common" :class="item.arpu >= 300 ? 'active' : ''">ARPU:&nbsp;{{item.arpu}}元</span>
                                    </div>
                                </div>
                                <div class='center' v-if="item.remark">
                                    <div class='name' v-for="(itm1,index1) in item.remark.split(',')" :key="index1">
                                        <span class='name-yisi' v-if="itm1.indexOf('疑似异动') >= 0">疑似异动</span>
                                        <span class='name-other' v-else>{{itm1}}</span>
                                    </div>
                                </div>
                                <div class="center" v-if="!item.remark && (item.workTypeCode == '1091' || item.workTypeCode == '1098')">
                                    <div class='name'>
                                        <span class='name-other' v-show="item.workTypeCode == '1091' && item.taskId.indexOf('a') >= 0">携出异动</span>
                                        <span class='name-other' v-show="item.workTypeCode == '1091' && item.taskId.indexOf('b') >= 0">宽带异动</span>
                                        <span class='name-other' v-show="item.workTypeCode == '1091' && item.taskId.indexOf('c') >= 0">降档异动</span>
                                        <span class='name-other' v-show="item.workTypeCode == '1098'">降档异动</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </mt-loadmore>
            </div>
            <NoDataPage class='nodata' tipTxt='暂无数据' v-if='taskList.length==0'></NoDataPage>
        </div>
        <div v-if="queryType === '4'">
            <div class="warnorder-list" v-show="dataList.length > 0">
                <mt-loadmore :top-method='loadTopWarn' :bottom-method='loadBottomWarn' :auto-fill='false' topPullText='刷新' topDropText='释放更新' topLoadingText='刷新中···' ref='loadmorewarn'>
                    <div class="warn-main">
                        <div class="warn-info" v-for='item in dataList' :key='item.orderId'>
                            <div class="info-main" @click="toInfo(item)">
                                <div class="info-title">
                                    <span>{{item.mobile | starTel}}</span>
                                    <span class='top-sign'>{{item.taskStatus | taskStatusFilter}}</span>
                                </div>
                                <!--标签-->
                                <div class="list-sign">
                                    <div class="sign-name" v-if="item.urgencyDegree">
                                        <span class="name-color blue" v-if="item.urgencyDegree == '1'">一般</span>
                                        <span class="name-color orange" v-if="item.urgencyDegree == '2'">紧急</span>
                                        <span class="name-color red" v-if="item.urgencyDegree == '3'">非常紧急</span>
                                    </div>
                                    <div class="sign-name" v-if="item.isOpenBroadband == '1'">
                                        <span class="name-color orange" v-if="item.isOpenBroadband == '1'">宽带</span>
                                    </div>
                                    <div class="sign-name" v-if="item.unusualChange">
                                        <span class="name-color orange" v-if="item.unusualChange">{{ item.unusualChange }}</span>
                                    </div>
                                    <div class="sign-name" v-if="item.labelCnt" @click.stop="showSign(item)">
                                        <div class="sign-box" v-if="item.showSignFlag">
                                            <div class="plan-model">
                                                <div class="butto-waihu">{{ item.labelName }}</div>
                                            </div>
                                        </div>
                                        <span class="name-color blue" v-if="item.labelCnt < 5">标签:&nbsp;{{item.labelCnt}}</span>
                                        <span class="name-color red" v-if="item.labelCnt >= 5">标签:&nbsp;{{item.labelCnt}}</span>
                                    </div>
                                    <div class="sign-name" v-if="item.arpu">
                                        <span class="name-color blue" v-if="item.arpu < 300">ARPU:&nbsp;{{item.arpu}}元</span>
                                        <span class="name-color red" v-if="item.arpu >= 300">ARPU:&nbsp;{{item.arpu}}元</span>
                                    </div>
                                </div>
                                <div class="info-hang" v-if="item.orderId">
                                    <span class="info-name">工单编号:&nbsp;&nbsp;</span>
                                    <span class="info-label">{{item.orderId}}</span>
                                </div>
                                <div class="info-hang" v-if="item.regionId || item.yxdyName || item.areaName">
                                    <span class="info-name">工单归属:&nbsp;&nbsp;</span>
                                    <span class="info-label">
                                        <span v-show="item.regionId">{{item.regionId | regionName}}</span>
                                        <span v-show="item.yxdyName">-{{item.yxdyName}}</span>
                                        <span v-show="item.areaName">-{{item.areaName}}</span>
                                    </span>
                                </div>
                                <div class="info-hang" v-if="item.receiverName || item.receiverMobile">
                                    <span class="info-name">阿拉盯处理人:&nbsp;&nbsp;</span>
                                    <span class="info-label">
                                        <span v-show="item.receiverName">{{item.receiverName}}</span>
                                        <span v-show="item.receiverMobile">({{item.receiverMobile}})</span>
                                    </span>
                                </div>
                                <div class="info-hang" v-if="item.createTime">
                                    <span class="info-name">录单时间:&nbsp;&nbsp;</span>
                                    <span class="info-label">{{item.createTime}}</span>
                                </div>
                                <div class="info-hang" v-if="item.aldSendTime">
                                    <span class="info-name">任务推送时间:&nbsp;&nbsp;</span>
                                    <span class="info-label">{{item.aldSendTime}}</span>
                                </div>
                                <div class="info-hang" v-if="item.dealLimit">
                                    <span class="info-name">处理时限:&nbsp;&nbsp;</span>
                                    <span class="info-label chaoshi" v-if="judeLimit(item.dealLimit)">{{item.dealLimit}} &nbsp;&nbsp;已超时</span>
                                    <span class="info-label" v-else>{{item.dealLimit}}</span>
                                </div>
                                <div class="info-hang" v-if="item.dealResult || item.dealType">
                                    <span class="info-name">结单处理:&nbsp;&nbsp;</span>
                                    <span class="info-label">{{item.dealResult | dealResultFiler}}-{{item.dealType | dealTypeFiler}}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </mt-loadmore>
            </div>
            <NoDataPage class='nodata' tipTxt='暂无数据' v-if='dataList.length==0'></NoDataPage>
        </div>

        <!--筛选弹窗-非管理员-->
        <div class="hide-box wrapper-medias" v-show="commonFilter" @click="closeHide()"></div>
        <div class="dialog common" v-show="commonFilter">
            <div class="filter-info-list common-list">
                <div class="dialog_item">
                    <div class="label">预警类型</div>
                    <div class="value">
                        <div class="item" :class="{choosed:warnType === item.id}" v-for="(item,index) in warnTypeList" :key="index" @click="chooseWarnType2(item)" v-if="item.id !== ''">
                            <span>{{ item.label }}</span>
                        </div>
                    </div>
                </div>
                <div class="dialog_item">
                    <div class="label">预警等级</div>
                    <div class="value">
                        <div class="item" :class="{choosed:emergentId === item.id}" v-for="(item,index) in emergentList" :key="index" @click="choseEmergent2(item)" v-if="item.id !== ''">
                            <span>{{ item.label }}</span>
                        </div>
                    </div>
                </div>
                <div class="dialog_item1">
                    <div class="label">预警标签</div>
                    <input class='input-value' placeholder='请输入(支持模糊匹配)' v-model='remarkReq'>
                </div>
                <div class="dialog_item">
                    <div class="label">时间范围</div>
                    <div class="value">
                        <div class="item" :class="{choosed:chooseType === item.id}" v-for="(item,index) in timeRangeList" :key="index" @click="choseTimeRange2(item)" v-if="item.id !== ''">
                            <span>{{ item.label }}</span>
                        </div>
                    </div>
                </div>
                <div class="dialog_item1" v-if="chooseType === '4'">
                    <div class="label">工单时间</div>
                    <div class="input-value3" v-show="!startDate" @click.stop="timeChoose()">开始时间</div>
                    <div class='input-value2' v-show="startDate" @click.stop="timeChoose()">{{ startDate }}</div>
                    <span style="padding: 0 10px;">~</span>
                    <div class='input-value3' v-show="!endDate" @click.stop="timeChoose()">结束时间</div>
                    <div class='input-value2' v-show="endDate" @click.stop="timeChoose()">{{ endDate }}</div>
                </div>
                <div class="dialog_item1">
                    <div class="label">ARPU值</div>
                    <input class='input-value2' type="tel" placeholder='下限(正整数)' v-model='arpuDown' @input="numJude('arpuDown')">
                    <span style="padding: 0 10px;">~</span>
                    <input class='input-value2' type="tel" placeholder='上限(正整数)' v-model='arpuUp' @input="numJude('arpuUp')">
                </div>
            </div>
            <div class="bottom">
                <div class="chongzhi" @click.stop="resert">重  置</div>
                <div class="queren" @click.stop="sureOrderQuery">完  成</div>
            </div>
        </div>
        <!--筛选弹窗-管理员--->
        <div class="hide-box wrapper-medias" v-show="showFilter" @click="closeHide()"></div>
        <div class="dialog" v-show="showFilter">
            <div class="filter-info-list">
                <div class="dialog_item">
                    <div class="label">工单状态</div>
                    <div class="value">
                        <div class="item" :class="{choosed:taskStatus === item.id}" v-for="(item,index) in taskStatusList" :key="index" @click="chooseTaskStatus(item)" v-if="item.id !== ''">
                            <span>{{item.label}}</span>
                        </div>
                    </div>
                </div>
                <div class="dialog_item">
                    <div class="label">是否宽带</div>
                    <div class="value">
                        <div class="item" :class="{choosed:isOpenBroadband === item.id}" v-for="(item,index) in isOpenBroadbandList" :key="index" @click="chooseIsOpenBroadband(item)" v-if="item.id !== ''">
                            <span>{{item.label}}</span>
                        </div>
                    </div>
                </div>
                <div class="dialog_item">
                    <div class="label">是否异动</div>
                    <div class="value">
                        <div class="item" :class="{choosed:unusualChange === item.id}" v-for="(item,index) in unusualChangeList" :key="index" @click="chooseUnusualChange(item)" v-if="item.id !== ''">
                            <span>{{item.label}}</span>
                        </div>
                    </div>
                </div>
                <div class="dialog_item">
                    <div class="label">处理结果</div>
                    <div class="value">
                        <div class="item" :class="{choosed:dealResult === item.id}" v-for="(item,index) in dealResultList" :key="index" @click="chooseDealResult(item)">
                            <span>{{item.label}}</span>
                        </div>
                    </div>
                </div>
                <div class="dialog_item">
                    <div class="label">处理方式</div>
                    <div class="value">
                        <div class="item" :class="{choosed:dealType === item.id}" v-for="(item,index) in dealTypeList" :key="index" @click="chooseDealType(item)">
                            <span>{{item.label}}</span>
                        </div>
                    </div>
                </div>
                <div class="dialog_item1">
                    <div class="label">工单编码</div>
                    <input class='input-value' placeholder='请输入(仅支持精确匹配)' v-model='orderId'>
                </div>
                <div class="dialog_item1">
                    <div class="label">工单处理人</div>
                    <input class='input-value' placeholder='请输入(仅支持精确匹配)' v-model='aladdinReceiver'>
                </div>
                <div class="dialog_item1">
                    <div class="label">预警标签</div>
                    <input class='input-value' placeholder='请输入(支持模糊匹配)' v-model='labelName'>
                </div>
                <div class="dialog_item1">
                    <div class="label">创建时间</div>
                    <div class="input-value3" v-show="!createDateBegin" @click.stop="beTimeChoose">开始时间</div>
                    <div class='input-value2' v-show="createDateBegin" @click.stop="beTimeChoose">{{ createDateBegin }}</div>
                    <span style="padding: 0 10px;">~</span>
                    <div class='input-value3' v-show="!createDateEnd" @click.stop="beTimeChoose">结束时间</div>
                    <div class='input-value2' v-show="createDateEnd" @click.stop="beTimeChoose">{{ createDateEnd }}</div>
                </div>
                <div class="dialog_item1">
                    <div class="label">ARPU值</div>
                    <input class='input-value2' type="tel" placeholder='下限(正整数)' v-model='arpuLimit' @input="numJude('arpuLimit')">
                    <span style="padding: 0 10px;">~</span>
                    <input class='input-value2' type="tel" placeholder='上限(正整数)' v-model='arpuTop' @input="numJude('arpuTop')">
                </div>
                <div class="dialog_item1" style="margin-bottom: 40px;">
                    <div class="label">命中标签数</div>
                    <input class='input-value' type="tel" placeholder='请输入(正整数)' v-model='labelCnt' @input="numJude('labelCnt')">
                </div>
            </div>
            <div class="bottom">
                <div class="chongzhi" @click.stop="resertChoose">重  置</div>
                <div class="queren" @click.stop="sureChoose">完  成</div>
            </div>
        </div>
    </div>
</template>

<script>
    import Header from 'components/common/Header.vue'
    import NoDataPage from '../../common/NoDataPage'
    import NlDropdown from 'components/common/NlDropdown/dropdown.js'
    import NlDatePicker from 'components/common/NlDatePick/datePicker.js'
    import { dateFormat, chgStrToDate } from '@/base/utils'
    import Storage from '@/base/storage.js'

    export default {
        name: 'WarnOrderList',
        components: { Header, NoDataPage },
        data() {
            return {
                srcFrom:'',//来源
                queryType: '2',  //任务类型  2、待处理   3、已处理
                daiChuLiCnt:'0',
                completeCnt:'0',
                taskName: '',   //搜索内容
                warnType:'',    //预警类型
                warnTypeName:'预警类型', // 预警类型名称
                warnTypeList:[
                    { id: '', label: '预警类型' },
                    { id: '1099', label: '风险客户预警' },
                    // { id: '1098', label: '降档异动预警' },
                    { id: '1091', label: '降档异动预警' } ,
                    { id: '1094', label: '代客投诉预警' },
                ],
                remarkReq:'',    //预警标签
                chooseSignList:[],  //选中的标签
                chooseFlag:false,  //多选flag
                emergentId: '',           //紧急程度  0 一般  1 紧急  2 非常紧急
                emergentName: '',           //紧急程度  0 一般  1 紧急  2 非常紧急
                emergentList: [
                    { id: '', label: '预警等级' },
                    { id: '2', label: '非常紧急' },
                    { id: '1', label: '紧急' },
                    { id: '0', label: '一般' }
                ],
                timeRangeName: '',
                timeRangeList: [
                    { id: '', label: '时间范围' },
                    { id: '1', label: '今日' },
                    { id: '2', label: '本周' },
                    { id: '3', label: '本月' },
                    { id: '4', label: '其他' }],
                chooseTime:'',//时间选择  回显的使用的
                startDate: '',//开始时间
                endDate: '',//结束时间
                chooseType: '', //当前选择的时间类型  1 今日  2 本周   3 本月  4 其他

                taskList:'',  //预警工单列表

                commonFilter:false,
                paixFlag: false,
                sortFlag:'arpu',
                sortTab: '3',//排序：1时间为主 2：紧急程度为主  3:ARPU值
                timeSort: '2',//时间排序，1：倒叙，2：正序
                urgentSort: '1',//距离排序：1：倒叙，2：正序
                arpuSort:'1',//ARPU值 1：倒叙，2：正序
                arpuDown:"", //ARPU值下限
                arpuUp:'', //ARPU值上限

                viewFlag:0, //是否展示我的管辖
                showFilter:false,
                orgaId: '', //当前网格编码
                orgaName: '', //当前网格名称
                mobile:'',  //预警号码
                taskStatus:'', //工单状态
                taskStatusName:'工单状态',
                taskStatusList:[
                    { id: '', label: '工单状态' },
                    { id: '1', label: '已推送在线' },
                    { id: '3', label: '已推送阿拉盯' } ,
                    { id: '4', label: '已完结' },
                ],
                isOpenBroadband:'',//是否宽带
                isOpenBroadbandName:'是否宽带',
                isOpenBroadbandList:[
                    { id: '', label: '是否宽带' },
                    { id: '1', label: '是' },
                    { id: '0', label: '否' },
                ],
                unusualChange:'',//是否异动
                unusualChangeName:'是否异动',
                unusualChangeList:[
                    { id: '', label: '是否异动' },
                    { id: '1', label: '是' },
                    { id: '0', label: '否' },
                ],
                dealResult:'',//处理结果
                dealResultList:[
                    { id: '1', label: '在线结单' },
                    { id: '2', label: '阿拉盯结单' },
                ],
                dealType:"",//处理方式
                dealTypeList:[
                    { id: '1', label: '在线外呼' },
                    { id: '2', label: '在线直接结单' },
                    { id: '3', label: '阿拉盯上门' },
                    { id: '4', label: '阿拉盯电话' },
                    { id: '5', label: '阿拉盯到厅' },
                ],
                orderId:"",//工单编码
                aladdinReceiver:"",//工单处理人
                labelName:'',//预警标签
                createDateBegin:'', //工单创建日期开始
                createDateEnd:'',  //工单创建日期结束
                labelCnt:'',  //命中标签数
                arpuLimit:"", //ARPU值下限
                arpuTop:'', //ARPU值上限
                totalNum:'0', //我的管辖总数
                dataList:[],  //返回的列表
                pageNum:1,
                pageSize:10
            }
        },
        created() {
            if(Storage.session.get("warnFilterInfo")){
                this.queryType  = Storage.session.get("warnFilterInfo").queryType;
                this.taskName  = Storage.session.get("warnFilterInfo").taskName;
                this.warnTypeName  = Storage.session.get("warnFilterInfo").warnTypeName;
                this.warnType  = Storage.session.get("warnFilterInfo").warnType;
                this.emergentId  = Storage.session.get("warnFilterInfo").emergentId;
                this.emergentName  = Storage.session.get("warnFilterInfo").emergentName;
                this.chooseType  = Storage.session.get("warnFilterInfo").chooseType;
                this.timeRangeName  = Storage.session.get("warnFilterInfo").timeRangeName;
                this.chooseTime  = Storage.session.get("warnFilterInfo").chooseTime;
                this.startDate  = Storage.session.get("warnFilterInfo").startDate;
                this.endDate  = Storage.session.get("warnFilterInfo").endDate;
                this.remarkReq  = Storage.session.get("warnFilterInfo").remarkReq;
                this.arpuDown  = Storage.session.get("warnFilterInfo").arpuDown;
                this.arpuUp  = Storage.session.get("warnFilterInfo").arpuUp;
                this.sortFlag  = Storage.session.get("warnFilterInfo").sortFlag;
                this.sortTab  = Storage.session.get("warnFilterInfo").sortTab;
                this.timeSort  = Storage.session.get("warnFilterInfo").timeSort;
                this.urgentSort  = Storage.session.get("warnFilterInfo").urgentSort;
                this.arpuSort  = Storage.session.get("warnFilterInfo").arpuSort;


                this.orgaId = Storage.session.get("warnFilterInfo").orgaId;
                this.orgaName = Storage.session.get("warnFilterInfo").orgaName;
                this.mobile = Storage.session.get("warnFilterInfo").mobile;
                this.orderId = Storage.session.get("warnFilterInfo").orderId;
                this.createDateBegin = Storage.session.get("warnFilterInfo").createDateBegin;
                this.createDateBegin = Storage.session.get("warnFilterInfo").createDateBegin;
                this.labelName = Storage.session.get("warnFilterInfo").labelName;
                this.taskStatus = Storage.session.get("warnFilterInfo").taskStatus;
                this.dealResult = Storage.session.get("warnFilterInfo").dealResult;
                this.dealType = Storage.session.get("warnFilterInfo").dealType;
                this.arpuLimit = Storage.session.get("warnFilterInfo").arpuLimit;
                this.arpuTop = Storage.session.get("warnFilterInfo").arpuTop;
                this.isOpenBroadband = Storage.session.get("warnFilterInfo").isOpenBroadband;
                this.unusualChange = Storage.session.get("warnFilterInfo").unusualChange;
                this.pageNum = Storage.session.get("warnFilterInfo").pageNum;
                this.pageSize = Storage.session.get("warnFilterInfo").pageSize;
                this.labelCnt = Storage.session.get("warnFilterInfo").labelCnt;
                this.aladdinReceiver = Storage.session.get("warnFilterInfo").aladdinReceiver;
            }else{
                if(this.$route.query.queryType){
                    this.queryType = this.$route.query.queryType;
                }
            }
            //查询预警工单
            this.getWarnOrderList()

            //判断有没有查看我的管辖权限
            this.getGxFlag();
        },
        filters:{
            //电话号码中间几位模糊化
            starTel(val) {
                if (!val) {
                    return '***'
                } else {
                    //取手机号
                    let phone = val.slice(0, 11)
                    //手机号后面部分
                    let suffix = val.replace(phone, "");
                    if(phone.length == 11){
                        let reg = /^(\d{3})\d*(\d{4})$/
                        return phone.replace(reg, '$1****$2') + suffix
                    }else {
                        return val
                    }

                }
            },
            dealResultFiler(val){
                if(val == '1'){
                    return '在线结单'
                }else if(val == '2'){
                    return '阿拉盯结单'
                }else {
                    return ''
                }
            },
            dealTypeFiler(val){
                if(val == '1'){
                    return '在线外呼'
                }else if(val == '2'){
                    return '在线直接结单'
                }else if(val == '3'){
                    return '阿拉盯上门'
                }else if(val == '4'){
                    return '阿拉盯电话'
                }else if(val == '5'){
                    return '阿拉盯到厅'
                }else {
                    return ''
                }
            },
            warnTypeCheck(val){
                if (val == '' || val == null) {
                    return '预警类型'
                } else {
                    return val
                }
            },
            emergentCheck(val) {
                if (val == '' || val == null) {
                    return '预警等级'
                } else {
                    return val
                }
            },
            timeRangeCheck(val) {
                if (val == '' || val == null) {
                    return '时间范围'
                } else {
                    return val
                }
            },
            taskStatusFilter(val){
                if(val == '1'){
                    return '已推送在线'
                }else if(val == '2'){
                    return '待推送阿拉盯'
                }else if(val == '3'){
                    return '已推送阿拉盯'
                }else if(val == '4'){
                    return '已完结'
                }else if(val == '0'){
                    return '待推送在线'
                }else if(val == '-1'){
                    return '推送在线失败'
                }else if(val == '-2'){
                    return '推送阿拉盯失败'
                }else if(val == '99'){
                    return '处理中'
                }else {
                    return ''
                }
            },
            regionName(val){
                const LONG_CITY_LIST = [
                    {id: '1000250', shortId: '14', label: '南京'},
                    {id: '1000510', shortId: '19', label: '无锡'},
                    {id: '1000511', shortId: '18', label: '镇江'},
                    {id: '1000512', shortId: '11', label: '苏州'},
                    {id: '1000513', shortId: '20', label: '南通'},
                    {id: '1000514', shortId: '23', label: '扬州'},
                    {id: '1000515', shortId: '22', label: '盐城'},
                    {id: '1000516', shortId: '16', label: '徐州'},
                    {id: '1000517', shortId: '12', label: '淮安'},
                    {id: '1000518', shortId: '15', label: '连云港'},
                    {id: '1000519', shortId: '17', label: '常州'},
                    {id: '1000523', shortId: '21', label: '泰州'},
                    {id: '1000527', shortId: '13', label: '宿迁'},
                    {id: '2000250', shortId: '99', label: '江苏省'}
                ]
                for (let i = 0;i < LONG_CITY_LIST.length;i++) {
                    if (val == LONG_CITY_LIST[i].shortId) {
                        name =  LONG_CITY_LIST[i].label
                    }
                }
                return name
            }
        },
        methods: {
            goPrev() {
                this.$router.push({
                    name: 'WorkStage',
                    query: {}
                })
                Storage.session.remove('warnFilterInfo');
            },
            //切换导航栏
            tabChoose(state) {
                this.queryType = state
                if(this.queryType === '4'){
                    console.info("tabChoose orgaId",this.orgaId)
                    //判断有没有组织机构
                    if(this.orgaId){
                        this.pageNum = 1
                        this.getJfList(false);
                    }else{ //没有就根据手机号获取
                        this.getOrgaInfo();
                    }
                }else {
                    this.getWarnOrderList()
                }
                this.paixFlag = false;
                this.showFilter =false;
                this.commonFilter = false;

            },
            //搜索方法
            search() {
                this.getWarnOrderList()
            },
            //选择预警类型
            chooseWarnType(){
                let _this = this

                NlDropdown({
                        confirmBtn: false,
                        datalist: this.warnTypeList
                    },
                    function(retVal) {
                        _this.warnType = retVal.id
                        _this.warnTypeName = retVal.label
                        _this.getWarnOrderList()

                    })
            },
            chooseWarnType2(item){
                if(this.warnType === item.id){
                    this.warnType = ''
                    this.warnTypeName = '预警类型'
                }else {
                    this.warnType = item.id
                    this.warnTypeName = item.label
                }
            },
            //选择预警等级
            choseEmergent() {
                let _this = this
                NlDropdown({
                        confirmBtn: false,
                        datalist: this.emergentList
                    },
                    function(retVal) {
                        _this.emergentId = retVal.id
                        _this.emergentName = retVal.label
                        _this.getWarnOrderList()

                    })
            },
            choseEmergent2(item){
                if(this.emergentId === item.id){
                    this.emergentId = ''
                    this.emergentName = '预警等级'
                }else {
                    this.emergentId = item.id
                    this.emergentName = item.label
                }
            },
            //选择时间范围
            choseTimeRange() {
                let _this = this
                NlDropdown({
                        confirmBtn: false,
                        datalist: this.timeRangeList
                    },
                    retVal => {
                        _this.chooseType = retVal.id
                        _this.timeRangeName = retVal.label
                        if (retVal.id != '4') {
                            _this.chooseTime = ''
                            _this.taskList = []
                            _this.getWarnOrderList()

                        } else {
                            _this.timeChoose('filter')
                        }
                    })
            },
            choseTimeRange2(item){
                if(this.chooseType === item.id){
                    this.chooseType = ''
                    this.timeRangeName = '时间范围'
                }else {
                    this.chooseType = item.id
                    this.timeRangeName = item.label
                }
            },
            //选择时间
            timeChoose(filter) {
                let _this = this
                NlDatePicker({
                    startDate: dateFormat(new Date(new Date().getTime() + 1000 * 24 * 60 * 60), 'yyyy-MM-dd'),
                    endDate: _this.endDate,
                    tsMinDate: new Date('2019-01-01'),
                    dateType: 'date',
                    format: 'yyyy-MM-dd'
                }, (retVal) => {
                    //获取返回回调
                    if (retVal.startDate == '') {
                        _this.$toast('未选择开始时间请重新选择')
                    } else {
                        _this.startDate = retVal.startDate
                        _this.endDate = retVal.endDate
                        _this.chooseTime = ' : ' + retVal.startDate + '~' + retVal.endDate
                        if(filter === 'filter'){
                            _this.getWarnOrderList()
                        }

                    }
                })
            },

            // 下拉刷新
            loadTop() {
                this.$refs.loadmore.onTopLoaded()
                this.getWarnOrderList()
            },
            // 上拉加载
            loadBottom() {
                this.$refs.loadmore.onBottomLoaded()
                this.getWarnOrderList()
            },

            //时间排序
            changeSort(flag) {
                this.sortFlag = flag;
                if(this.sortFlag ===  'time'){
                    this.sortTab = 1

                    if(this.timeSort == 1){
                        this.timeSort = 2;
                    }else {
                        this.timeSort = 1;
                    }
                }
                if(this.sortFlag ===  'distance'){
                    this.sortTab = 2

                    if(this.urgentSort == 1){
                        this.urgentSort = 2;
                    }else {
                        this.urgentSort = 1;
                    }
                }

                if(this.sortFlag ===  'arpu'){
                    this.sortTab = 3

                    if(this.arpuSort == 1){
                        this.arpuSort = 2;
                    }else {
                        this.arpuSort = 1;
                    }
                }
                //调用查询任务接口
                this.getWarnOrderList()
            },

            //重置
            resert(){
                this.taskName  = '';
                this.warnTypeName  = '预警类型';
                this.warnType  = '';
                this.emergentId  = '';
                this.emergentName  = '预警等级';
                this.chooseType  = '';
                this.timeRangeName  = '时间范围';
                this.chooseTime  = '';
                this.startDate  = '';
                this.endDate  = '';
                this.arpuDown= '';
                this.arpuUp= '';

                this.commonFilter = false
                this.getWarnOrderList();
            },
            sureOrderQuery(){
                if(this.arpuUp && this.arpuDown){
                    if(Number(this.arpuDown) > Number(this.arpuUp)){
                        this.$alert("ARPU值上限不能小于ARPU值下限")
                        return;
                    }
                }
                this.commonFilter = false
                this.getWarnOrderList();
            },

            getWarnOrderList(){
                this.taskList = []

                let _this = this
                if (_this.chooseType != 4) { //时间是其他
                    _this.startDate = ''
                    _this.endDate = ''
                    if (_this.chooseType == 1) {   //获取当天日期
                        _this.startDate = this.getDayDate()
                        _this.endDate = this.getDayDate()
                    } else if (_this.chooseType == 2) {   //获取本周日期
                        _this.startDate = this.getWeekDate('s', 0)
                        _this.endDate = this.getWeekDate('e', 0)
                    } else if (_this.chooseType == 3) {   //获取本月信息
                        _this.startDate = this.getMonthDate('s', 0)
                        _this.endDate = this.getMonthDate('e', 0)
                    }
                }

                if (_this.startDate != '') {
                    if(_this.startDate.indexOf('00:00:00') == -1){
                        _this.startDate = _this.startDate + ' 00:00:00'
                    }
                }
                if (_this.endDate != '') {
                    if(_this.endDate.indexOf('23:59:59') == -1){
                        _this.endDate = _this.endDate + ' 23:59:59'
                    }
                }
                let workTypeCode = ['1091','1094','1099']
                if(this.warnType != ''){
                    workTypeCode = this.warnType.split(',')
                }

                let url = '/xsb/personBusiness/myTask/h5QryWarnTaskList'
                let params = {
                    queryType: _this.queryType,
                    taskName: _this.taskName,
                    page: '1',
                    size: "999999",
                    workTypeCode: workTypeCode,
                    remarkReq:_this.remarkReq,
                    emergent: _this.emergentId,
                    dateType: _this.chooseType,
                    startDate: _this.startDate,
                    endDate: _this.endDate,
                    arpuDown : this.arpuDown,
                    arpuUp : this.arpuUp,
                    sortTab:this.sortTab,
                    timeSort: this.timeSort,
                    urgentSort:this.urgentSort,
                    arpuSort:this.arpuSort,
                }
                console.info("param",params)
                this.$http
                    .post(url, params).then(res => {
                    console.log('h5QryWarnTaskList',res.data)

                    if (res.data.retCode == 0) {
                        if(res.data.data && res.data.data.daiChuLiCnt){
                            this.daiChuLiCnt = res.data.data.daiChuLiCnt
                        }
                        if(res.data.data && res.data.data.completeCnt){
                            this.completeCnt = res.data.data.completeCnt
                        }

                        if (res.data.data && res.data.data.pageResult && res.data.data.pageResult.length > 0) {
                            this.taskList = res.data.data.pageResult
                        }
                    } else {
                        this.$toast(res.data.retMsg)
                    }
                })
            },
            toDetail(item){
                //储存筛选条件
                this.commitFilter();

                if(item.taskState == '6'){
                    return
                }else if(item.taskState == '0'){
                    if(item.workTypeCode == '1094'){
                        this.$router.push({
                            path: '/provinWarningDeal',
                            query: {
                                workId:item.taskId,
                                subId:item.groupId,
                                srcCorm:"warnOrderList"
                            }
                        })
                    }else if(item.workTypeCode === '1091'){
                        let orderType = '';//预警类型 1：携出异动  2：宽带异动
                        let type = item.taskId.split('-');
                        console.info("type",type)
                        if(type[0] == 'a'){
                            orderType = '1';
                        }else if(type[0] == 'b'){
                            orderType = '2';
                        }else if(type[0] == 'c'){
                            orderType = '3';
                        }else {
                            return;
                        }

                        this.$router.push({
                            path: '/personMovementAlert',
                            query: {
                                workId:item.taskId,
                                orderId:item.groupId,
                                orderType:orderType,
                                srcCorm:"warnOrderList"
                            }
                        })

                    }else if(item.workTypeCode == '1098'){
                        this.$router.push({
                            path: '/personMovementAlert',
                            query: {
                                workId:item.taskId,
                                orderId:item.groupId,
                                orderType:'3',//3、降档/异动
                                srcCorm:"warnOrderList"
                            }
                        })
                    }else if(item.workTypeCode == '1099'){
                        this.$router.push({
                            path: '/provinWarningByIop',
                            query: {
                                workId:item.taskId,
                                subId:item.groupId,
                                srcCorm:"warnOrderList",
                                warnLevel:item.emergent,
                                limitDate:item.limitDate,
                                warnType:item.remark,
                            }
                        })
                    }
                }else {
                    if(item.workTypeCode == '1094'){
                        this.$router.push({
                            path: '/warningDealComplete',
                            query: {
                                workId:item.taskId,
                                subId:item.groupId,
                                srcCorm:"warnOrderList"
                            }
                        })
                    }else if(item.workTypeCode == '1091'){
                        let orderType = '';//预警类型 1：携出异动  2：宽带异动
                        let type = item.taskId.split('-');
                        console.info("type",type)
                        if(type[0] == 'a'){
                            orderType = '1';
                        }else if(type[0] == 'b'){
                            orderType = '2';
                        }else if(type[0] == 'c'){
                            orderType = '3';
                        }else {
                            return;
                        }

                        this.$router.push({
                            path: '/alertHandleHis',
                            query: {
                                workId:item.taskId,
                                orderId:item.groupId,
                                orderType:orderType,
                                srcCorm:"warnOrderList"
                            }
                        })
                    }else if(item.workTypeCode == '1098'){
                        this.$router.push({
                            path: '/alertHandleHis',
                            query: {
                                workId:item.taskId,
                                orderId:item.groupId,
                                orderType:'3',//3、降档/异动
                                srcCorm:"warnOrderList"
                            }
                        })
                    }else if(item.workTypeCode == '1099'){
                        this.$router.push({
                            path: '/warnHisToIop',
                            query: {
                                workId:item.taskId,
                                subId:item.groupId,
                                srcCorm:"warnOrderList",
                                warnLevel:item.emergent,
                                limitDate:item.limitDate,
                                warnType:item.remark,
                            }
                        })
                    }
                }
            },
            //获取当天时间
            // dates为数字类型，0代表今日,-1代表昨日，1代表明日，返回yyyy-mm-dd格式字符串，
            // dates不传默认代表今日。

            getDayDate() {
                var timenow = new Date()
                var y = timenow.getFullYear()//年
                var m = timenow.getMonth() + 1//月
                m = m < 10 ? '0' + m : m
                var d = timenow.getDate()//日
                d = d < 10 ? ('0' + d) : d
                var time = y + '-' + m + '-' + d
                return time
            },
            //获取当周信息
            // type为字符串类型，有两种选择，"s"代表开始,"e"代表结束，
            // dates为数字类型，不传或0代表本周，-1代表上周，1代表下周
            getWeekDate(type, dates) {
                var now = new Date()
                var nowTime = now.getTime()
                var day = now.getDay()
                var longTime = 24 * 60 * 60 * 1000
                var n = longTime * 7 * (dates || 0)
                if (type == 's') {
                    var dd = nowTime - (day - 1) * longTime + n
                }

                if (type == 'e') {
                    var dd = nowTime + (7 - day) * longTime + n
                }

                dd = new Date(dd)
                var y = dd.getFullYear()
                var m = dd.getMonth() + 1
                var d = dd.getDate()
                m = m < 10 ? '0' + m : m
                d = d < 10 ? '0' + d : d
                var day = y + '-' + m + '-' + d
                return day
            },
            //获取月信息
            // type为字符串类型，有两种选择，"s"代表开始,"e"代表结束，
            // months为数字类型，不传或0代表本月，-1代表上月，1代表下月
            getMonthDate(type, months) {
                var d = new Date()
                var year = d.getFullYear()
                var month = d.getMonth() + 1
                if (Math.abs(months) > 12) {
                    months = months % 12
                }

                if (months != 0) {
                    if (month + months > 12) {
                        year++
                        month = (month + months) % 12
                    } else if (month + months < 1) {
                        year--
                        month = 12 + month + months
                    } else {
                        month = month + months
                    }

                }

                month = month < 10 ? '0' + month : month
                var date = d.getDate()
                var firstday = year + '-' + month + '-' + '01'
                var lastday = ''
                // if (month == "01" || month == "03" || month == "05" || month == "07" || month == "08" || month == "10" || month == "12") {
                let arr = ['01', '03', '05', '07', '08', '10', '12']
                if (arr.indexOf(month) != -1) {
                    lastday = year + '-' + month + '-' + 31
                } else if (month == '02') {
                    if ((year % 4 == 0 && year % 100 != 0) || (year % 100 == 0 && year % 400 == 0)) {
                        lastday = year + '-' + month + '-' + 29
                    } else {
                        lastday = year + '-' + month + '-' + 28
                    }

                } else {
                    lastday = year + '-' + month + '-' + 30
                }

                var day = ''
                if (type == 's') {
                    day = firstday
                } else {
                    day = lastday
                }

                return day
            },
            showSign(item){
                if(item.showSignFlag){
                    item.showSignFlag = false
                }else {
                    Vue.set(item, "showSignFlag", true);
                }
            },
            getGxFlag() {
                let param2 = {
                    'region': Storage.session.get('userInfo').region,
                    'unLoadFlg': true,//屏蔽加载圈
                    'operatorTel': Storage.session.get('userInfo').servNumber
                }
                this.$http.post('/xsb/gridCenter/familyDiagnosis/h5QryCalculateBusiCoard', param2).then((res) => {
                    let body = res.data
                    console.info("h5QryCalculateBusiCoard body",body)
                    if (body.retCode === '0') {
                        //1 有权限  0权限
                        this.viewFlag = body.data.viewFlag;
                        if(this.viewFlag == 1){
                            //判断有没有组织机构
                            if(this.orgaId){
                                this.pageNum = 1
                                this.getJfList(false);
                            }else{ //没有就根据手机号获取
                                this.getOrgaInfo();
                            }
                        }
                    }
                })
            },
            //获取组织结构
            getOrgaInfo() {
                let servNumber = Storage.session.get('userInfo').servNumber
                let url = `/xsb/personBusiness/gridViewSecond/h5QryScoreAndRank?telnum=${servNumber}`
                this.$http.get(url).then((res) => {
                    if (res.data.retCode == '0') {
                        let data = res.data.data
                        console.log('this.data ', data)
                        this.orgaId = data.orgaId
                        this.orgaName = data.orgaName
                        //获取组织层级
                        url = `/xsb/personBusiness/gridViewSecond/h5QryAreak?orgaid=${this.orgaId}`
                        this.$http.get(url).then((res) => {
                            if (res.data.retCode == '0') {
                                let data = res.data.data
                                console.info(' data[0].orgalevel ', data)
                                console.info('this.orgaLevel ', data[0].orgalevel)
                                Storage.session.set('orgaLevelWarn', data[0].orgalevel)
                                //调用列表接口
                                this.pageNum = 1
                                this.getJfList(false);
                            }else {
                                this.$alert(res.data.retMsg || '查询当前网格层级调用出错')
                            }
                        })
                    }
                })
            },
            // 下拉刷新
            loadTopWarn(){
                this.$refs.loadmorewarn.onTopLoaded()
                this.pageNum = 1
                this.getJfList(false)
            },
            // 上拉加载
            loadBottomWarn() {
                this.$refs.loadmorewarn.onBottomLoaded()
                this.pageNum++
                this.getJfList(true)
            },
            //获取经分列表
            getJfList(flag){
                if(!flag){
                    this.totalNum = '0'
                    this.dataList = [];
                }
                let params = {
                    regionId : Storage.session.get('userInfo').region,
                    yxdyId :  Storage.session.get('orgaLevelWarn') === '2' ? this.orgaId : '',
                    areaId : Storage.session.get('orgaLevelWarn') === '3' ? this.orgaId : '',
                    mobile :this.mobile,
                    orderId :this.orderId,
                    createDateBegin : this.createDateBegin ? dateFormat(chgStrToDate(this.createDateBegin), 'yyyyMMdd') : '',
                    createDateEnd : this.createDateEnd ? dateFormat(chgStrToDate(this.createDateEnd), 'yyyyMMdd') : '',
                    labelName:this.labelName,
                    taskStatus:this.taskStatus,
                    dealResult:this.dealResult,
                    dealType:this.dealType,
                    arpuLimit:this.arpuLimit,
                    arpuTop:this.arpuTop,
                    isOpenBroadband:this.isOpenBroadband,
                    labelCnt:this.labelCnt,
                    unusualChange:this.unusualChange,
                    pageNum:this.pageNum,
                    pageSize:this.pageSize,
                    isDownLoad:'0',
                    aladdinReceiver:this.aladdinReceiver,
                }
                console.info("IOP 预警列表查询",params)
                this.$http.post('/xsb/gridCenter/warningOrder/h5JfOrderListQuery',params).then((res) => {
                    if (res.data.retCode == 0) {
                        this.totalNum = res.data.data.totalNum
                        if(this.dataList && this.dataList.length > 0){
                            this.dataList = this.dataList.concat(res.data.data.dataList)
                        }else {
                            this.dataList = res.data.data.dataList;
                        }
                        console.info("this.dataList",this.dataList)
                    } else {
                        this.$alert(res.data.retMsg || '预警明细信息查询调用出错')
                    }
                })
            },
            //存储查询条件
            commitFilter(){
                let warnFilterInfo = {
                    queryType : this.queryType,
                    taskName : this.taskName,
                    warnTypeName : this.warnTypeName,
                    warnType : this.warnType,
                    emergentId : this.emergentId,
                    emergentName : this.emergentName,
                    chooseType : this.chooseType,
                    timeRangeName : this.timeRangeName,
                    chooseTime : this.chooseTime,
                    startDate : this.startDate,
                    endDate : this.endDate,
                    remarkReq:this.remarkReq,
                    arpuDown:this.arpuDown,
                    arpuUp:this.arpuUp,
                    sortFlag:this.sortFlag,
                    sortTab: this.sortTab,
                    timeSort: this.timeSort,
                    urgentSort: this.urgentSort,
                    arpuSort:this.arpuSort,

                    orgaId:this.orgaId,
                    orgaName:this.orgaName,
                    mobile :this.mobile,
                    orderId :this.orderId,
                    createDateBegin : this.createDateBegin,
                    createDateEnd : this.createDateBegin,
                    labelName:this.labelName,
                    taskStatus:this.taskStatus,
                    dealResult:this.dealResult,
                    dealType:this.dealType,
                    arpuLimit:this.arpuLimit,
                    arpuTop:this.arpuTop,
                    isOpenBroadband:this.isOpenBroadband,
                    labelCnt:this.labelCnt,
                    unusualChange:this.unusualChange,
                    pageNum:this.pageNum,
                    pageSize:this.pageSize,
                    aladdinReceiver:this.aladdinReceiver,
                }
                Storage.session.set("warnFilterInfo",warnFilterInfo);
                console.info("warnFilterInfo",Storage.session.get("warnFilterInfo"))
            },
            toInfo(item){
                this.commitFilter();
                this.$router.push({
                    path: '/warnHisToIop',
                    query: {
                        workId:item.workId,
                        subId:item.orderId,
                        srcCorm:"warnOrderList",
                        warnLevel:item.urgencyDegree - 1,
                        limitDate:item.dealLimit,
                        warnType:item.labelName,
                        isManaager:item.taskStatus === '3'?'manager':'', //已经推送阿拉盯的工单提供调配功能
                        receiverMobile:item.receiverMobile,//阿拉盯工单处理人
                        isReback:'reback',//添加意见
                    }
                })
            },
            goChooseArea(item) {
                this.commitFilter();

                this.$router.push({
                    path: '/GridSelect', //网格地址查询页
                    query: {
                        orgaid: item,
                        backPage:'warnOrderList',
                    }
                })
            },
            //搜索
            searchMobile(){
                this.pageNum = 1
                this.getJfList(false);
            },
            taskStatusCheck(){
                let _this = this
                NlDropdown({
                        confirmBtn: false,
                        datalist: this.taskStatusList
                    },
                    function(retVal) {
                        _this.taskStatus = retVal.id
                        _this.taskStatusName = retVal.label
                        console.info('_this.taskStatus',_this.taskStatus)
                        _this.pageNum = 1
                        _this.getJfList(false);
                    })
            },
            chooseTaskStatus(item){
                if(this.taskStatus === item.id){
                    this.taskStatus = ''
                    this.taskStatusName = '工单状态'
                }else {
                    this.taskStatus = item.id
                    this.taskStatusName = item.label
                }

            },
            isOpenBroadbandCheck(){
                let _this = this
                NlDropdown({
                        confirmBtn: false,
                        datalist: this.isOpenBroadbandList
                    },
                    function(retVal) {
                        _this.isOpenBroadband = retVal.id
                        _this.isOpenBroadbandName = retVal.label
                        console.info('_this.isOpenBroadband',_this.isOpenBroadband)
                        _this.pageNum = 1
                        _this.getJfList(false);
                    })
            },
            chooseIsOpenBroadband(item){
                if(this.isOpenBroadband === item.id){
                    this.isOpenBroadband = ''
                    this.isOpenBroadbandName = '是否宽带'
                }else {
                    this.isOpenBroadband = item.id
                    this.isOpenBroadbandName = item.label
                }
            },
            unusualChangeCheck(){
                let _this = this
                NlDropdown({
                        confirmBtn: false,
                        datalist: this.unusualChangeList
                    },
                    function(retVal) {
                        _this.unusualChange = retVal.id
                        _this.unusualChangeName = retVal.label
                        console.info('_this.unusualChange',_this.unusualChange)
                        _this.pageNum = 1
                        _this.getJfList(false);
                    })
            },
            chooseUnusualChange(item){
                if(this.unusualChange === item.id ){
                    this.unusualChange = ''
                    this.unusualChangeName = '是否异动'
                }else {
                    this.unusualChange = item.id
                    this.unusualChangeName = item.label
                }
            },
            chooseDealResult(item){
                if(this.dealResult === item.id ){
                    this.dealResult = ''
                }else{
                    this.dealResult = item.id
                }
            },
            chooseDealType(item){
                if(this.dealType === item.id){
                    this.dealType = ''
                }else {
                    this.dealType = item.id
                }
            },
            beTimeChoose(){
                console.info(11111)
                let _this = this
                NlDatePicker({
                    startDate: dateFormat(new Date(new Date().getTime() + 1000 * 24 * 60 * 60), 'yyyy-MM-dd'),
                    endDate: _this.createDateEnd,
                    tsMinDate: new Date('2019-01-01'),
                    dateType: 'date',
                    format: 'yyyy-MM-dd'
                }, (retVal) => {
                    console.info("retVal",retVal)
                    //获取返回回调
                    if (retVal.startDate == '') {
                        _this.$toast('未选择开始时间请重新选择')
                    } else {
                        _this.createDateBegin = retVal.startDate
                        _this.createDateEnd = retVal.endDate
                    }
                })
            },

            //格式校验
            numJude(flag){
                var reg1 = /^[\d]*$/;
                if(flag == 'arpuLimit'){
                    if(!reg1.test(this.arpuLimit)) {
                        this.$toast("请输入数字");
                        this.arpuLimit = this.arpuLimit.replace(/[^0-9]/ig, '')
                    }
                }
                if(flag == 'arpuTop'){
                    if(!reg1.test(this.arpuTop)) {
                        this.$toast("请输入数字");
                        this.arpuTop = this.arpuTop.replace(/[^0-9]/ig, '')
                    }
                }
                if(flag == 'labelCnt'){
                    if(!reg1.test(this.labelCnt)) {
                        this.$toast("请输入数字");
                        this.labelCnt = this.labelCnt.replace(/[^0-9]/ig, '')
                    }
                }
                if(flag == 'arpuDown'){
                    if(!reg1.test(this.arpuDown)) {
                        this.$toast("请输入数字");
                        this.arpuDown = this.arpuDown.replace(/[^0-9]/ig, '')
                    }
                }
                if(flag == 'arpuUp'){
                    if(!reg1.test(this.arpuUp)) {
                        this.$toast("请输入数字");
                        this.arpuUp = this.arpuUp.replace(/[^0-9]/ig, '')
                    }
                }
            },

            //重置
            resertChoose(){
                this.mobile = ''
                this.taskStatus = ''
                this.taskStatusName = '工单状态'
                this.isOpenBroadband = ''
                this.isOpenBroadbandName = '是否宽带'
                this.unusualChange = ''
                this.unusualChangeName = '是否异动'
                this.dealResult = ''
                this.dealType = ''
                this.orderId = ''
                this.aladdinReceiver = ''
                this.labelName = ''
                this.createDateBegin = ''
                this.createDateEnd = ''
                this.labelCnt = ''
                this.arpuLimit = ''
                this.arpuTop = ''
                this.showFilter = false
                this.pageNum = 1
                this.getJfList(false);
            },
            sureChoose(){
                console.info("this.arpuTop",this.arpuTop)
                console.info("this.arpuLimit",this.arpuLimit)
                if(this.arpuTop && this.arpuLimit){
                    if(Number(this.arpuLimit) > Number(this.arpuTop)){
                        this.$alert("ARPU值上限不能小于ARPU值下限")
                        return;
                    }

                }
                this.showFilter = false
                this.pageNum = 1
                this.getJfList(false);
            },

            //灰度取消弹窗
            closeHide() {
                this.showFilter = false
                this.commonFilter =false
            },

            //处理时间与当前时间比较
            judeLimit(timeStr){
                // 将字符串时间转换为时间戳（毫秒）
                const targetTime = new Date(timeStr).getTime();

                // 检查时间格式是否有效
                if (isNaN(targetTime)) {
                    throw new Error("无效的时间格式，请使用类似'2025-08-16 15:12:00'的格式");
                }

                // 获取当前时间戳（毫秒）
                const currentTime = new Date().getTime();

                // 比较时间戳：目标时间 < 当前时间 → 返回true
                return targetTime < currentTime;
            },
        }

    }
</script>

<style scoped lang='less'>
    .vd {
        position: relative;
        display: flex;
        flex-direction: column;
        height: 100%;
        overflow: hidden;
    }

    .fix-content {
        background: linear-gradient(180deg, #F7FBFF 0%, #FFFFFF 100%);
        box-shadow: 0 3px 10px 0 rgba(86, 125, 244, 0.05);
        margin-top: 44px;

        .order-tar {
            display: flex;
            border-bottom: 1px solid #C8C8C8;
            border-top: 1px solid #C8C8C8;

            & > div {
                flex: 1;
                height: 40px;
                text-align: center;
                line-height: 40px;
                background-color: #F8F8F8;
                font-size: 14px;
                font-weight: 600;
                color: #6c6c6c;
                box-sizing: border-box;

                & > span {
                    font-size: 14px;
                    margin-right: 2px;
                }
            }

            .choosed {
                color: #1681FB;
                border-bottom: 2px solid #1681FB;
                background-color: #fff;
            }
        }

        .search-wrapper {
            padding: 12px 12px;
            box-sizing: border-box;
            background: #F7FBFF;

            .ipt-wrapper {
                height: 33px;
                line-height: 32px;
                padding: 0 12px;
                position: relative;
                border: 1px solid #9DC2EB;
                border-radius: 5px;
                background-color: #fff;

                .seach-ipt {
                    font-size: 12px;
                    width: 100%;
                    height: 28px;
                    line-height: 28px;
                    padding-left: 20px;
                    padding-right: 40px;
                    box-sizing: border-box;

                    &:focus {
                        outline: none;
                    }
                }

                .sousuo1 {
                    position: absolute;
                    left: 12px;
                    top: 50%;
                    color: #828282;
                    transform: translateY(-50%);
                }

                .query-btn {
                    position: absolute;
                    top: 50%;
                    right: 12px;
                    transform: translateY(-50%);
                    font-size: 12px;
                    color: #1681FB;
                }
            }

            .tabr-content {
                width: 100%;
                display: flex;
                justify-content: space-between;
                position: relative;
                color: #2E2E2E;
                font-size: 16px;
                margin-top: 12px;

                .size {
                    font-size: 8px;
                    color: #c4c4c4;
                    display: inline-block;
                }

                .change-direct {
                    transform: rotate(180deg);
                }

                .colorb {
                    color: #0166FF;
                }

                .timer {
                    margin-right: 13px;
                }

                .box-icon{
                    display: inline-flex;

                    .icon-sx{
                        font-size: 14px;
                        position: relative;
                        color: #0D80FD;
                    }

                    .shejimulu {
                        font-size: 14px;
                        position: relative;
                        color: #0D80FD;
                        margin-right: 10px;

                        .paixu-box {
                            color: #000;
                            width: 200px;
                            padding: 10px;
                            background: #fff;
                            border-radius: 5px;
                            box-shadow: 2px 2px 6px 0px rgba(139, 174, 255, 1);
                            position: absolute;
                            right: 10px;
                            top: 25px;
                            line-height: 32px;


                            z-index: 999;

                            .active {
                                color: #0D80FD;
                            }

                            .sort-pic {
                                font-weight: 500;
                                color: #000000;

                                &.active {
                                    color: #1D63EB;
                                }
                            }
                        }
                    }
                }
            }

            .tabr-content > div {
                line-height: 24px;
                font-size: 12px;
                font-weight: 500;
                color: rgba(82, 82, 82, 1);
            }


        }

        .jf-listfilter{
            padding: 12px 12px;
            box-sizing: border-box;
            background: #F7FBFF;

            .sou-first{
                display: flex;
                align-items: center;
                justify-content: space-between;

                .zuzhi{
                    font-size: 12px;
                    width: 50%;

                    .grid-diqu-add{
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        max-width: 98px;
                        display: inline-block;
                        overflow: hidden;
                    }
                    .zbtu{
                        font-size: 16px;
                        color: #2a68ff;
                    }
                    .jiantu{
                        font-size: 14px;
                        color: #2a68ff;
                    }
                }

                .sou-wrapper{
                    height: 33px;
                    line-height: 32px;
                    padding: 0 12px;
                    position: relative;
                    border: 1px solid #9DC2EB;
                    border-radius: 5px;
                    background-color: #fff;
                    width: 50%;

                    .seach-sou {
                        font-size: 12px;
                        width: 100%;
                        height: 28px;
                        line-height: 28px;
                        padding-left: 20px;
                        padding-right: 40px;
                        box-sizing: border-box;

                        &:focus {
                            outline: none;
                        }
                    }

                    .sousuo1 {
                        position: absolute;
                        left: 12px;
                        top: 50%;
                        color: #828282;
                        transform: translateY(-50%);
                    }

                    .query-sou {
                        position: absolute;
                        top: 50%;
                        right: 12px;
                        transform: translateY(-50%);
                        font-size: 12px;
                        color: #1681FB;
                    }
                }
            }

            .jf-content{
                width: 100%;
                display: flex;
                justify-content: space-between;
                position: relative;
                color: #2E2E2E;
                font-size: 16px;
                margin-top: 12px;

                .size {
                    font-size: 8px;
                    color: #c4c4c4;
                    display: inline-block;
                }

                .shejimulu{
                    font-size: 14px;
                    position: relative;
                    color: #0D80FD;
                }

                .icon-sx{
                    font-size: 14px;
                    position: relative;
                    color: #0D80FD;
                }

                .change-direct {
                    transform: rotate(180deg);
                }
            }

            .jf-content > div {
                line-height: 24px;
                font-size: 12px;
                font-weight: 500;
                color: rgba(82, 82, 82, 1);
            }
        }


    }

    .task-list {
        flex: 1;
        height: 100vh;
        overflow: auto;
        padding-bottom: 10px;
    }


    .contents {
        /*padding: 10px 20px 0 20px;*/
        margin: 20px 10px 300px 10px;

        .content-list {
            background: #FAFAFA;
            margin-top: 10px;

            .content-time {
                margin: 0 0 8px 2px;
                font-size: 12px;
                color: #4D4D4D;

                img {
                    vertical-align: bottom;
                    width: 15px;
                }

                .shijianxuanzejiantou {
                    font-size: 13px;
                    line-height: 12px;
                    vertical-align: bottom;
                }
                .chaoshi{
                    color: #d9001b;
                }
            }

            .content-right {
                background-color: #fff;
                border-radius: 10px;
                padding: 10px;
                display: inline-flex;
                flex: 1;
                box-sizing: border-box;
                font-size: 14px;
                flex-direction: column;
                box-shadow: 0 3px 10px 0 rgba(86, 125, 244, 0.05);
                position: relative;
                width: 100%;


                .name-right {
                    flex: 1;
                    color: #5A7597;
                    white-space: nowrap;
                    overflow: auto;
                }

                .top-left {
                    display: inline-block;
                    color: #ffffff;
                    padding: 0 12px 0 6px;
                    background-color: #f59a23;
                    position: absolute;
                    right: 0;
                    top: 0;
                    border-radius: 2px 4px 0 15px;
                    font-size: 12px;
                    font-weight: 400;
                    width: 42px;
                    text-align: center;
                    line-height: 23px;

                    &.urgent {
                        width: 60px;
                        background-color: #d9001b;
                        font-size: 12px;
                    }
                    &.yibanconf {
                        background-color: #0c7fff;
                    }
                }

                .top {
                    display: flex;
                    justify-content: space-between;
                    margin: 4px 0 4px 0;
                    height: 23px;
                    position: relative;

                    .top-right {
                        line-height: 24px;
                        font-weight: 600;
                        font-size: 14px;

                        .apru-common{
                            position: absolute;
                            right:0;
                            font-size: 12px;
                            font-weight: 400;
                        }

                        .active{
                            color: #d9001b;
                        }
                    }

                    .shezhi1{
                        position: absolute;
                        right: 3px;
                        top: 0;
                        border-radius: 2px 4px 0 15px;
                        font-size: 12px;
                        color: #fff;
                        font-weight: 100;
                    }


                    .top-time {
                        color: #959595;
                        font-size: 12px;

                        .top-symbol {
                            text-align: center;
                        }
                    }
                }

                .center {
                    line-height: 23px;

                    .name {
                        display: inline-block;
                        font-size: 12px;
                        .name-yisi{
                            line-height: 32px;
                            color: #c0906a;
                            background: #fbd5a3;
                            padding: 3px 5px;
                            border: 1px solid #fff;
                            border-radius: 4px;
                            margin-right: 5px;
                        }

                        .name-other{
                            line-height: 32px;
                            background: #ccdefc;
                            color: #4087f2;
                            padding: 3px 5px;
                            border: 1px solid #fff;
                            border-radius: 4px;
                            margin-right: 5px;
                        }
                    }
                }

            }
        }
    }


    /deep/ .mint-loadmore-top {
        margin-top: -59px;
    }

    .warnorder-list{
        flex: 1;
        height: 100vh;
        overflow: auto;
        padding-bottom: 40px;
    }
    .warn-main{
        margin: 20px 10px 300px 10px;

        .warn-info{
            background: #FAFAFA;
            margin-top: 10px;

            .info-main{
                padding: 10px;
                position: relative;

                .info-title{
                    line-height: 24px;
                    font-weight: 600;
                    font-size: 14px;
                }

                .list-sign{
                    line-height: 23px;

                    .sign-name{
                        display: inline-block;
                        position: relative;

                        .sign-box{
                            margin: 54px 10px 0 10px;
                            background-color: #ccc;
                            border-radius: 6px;
                            width: 100%;
                            position: absolute;
                            z-index: 1;
                            left: 50px;
                            .plan-model {
                                font-size: 14px;
                                position: absolute;
                                right: 11px;
                                top: -18px;
                                max-width: 200px;
                                min-width: 150px;
                                background: #ccc;
                                border-top-left-radius: 5px;
                                border-top-right-radius: 5px;
                                border-bottom-right-radius: 5px;
                                border-bottom-left-radius: 5px;
                                z-index: 2;
                                box-sizing: border-box;
                                text-align: center;
                                line-height: 23px;
                                box-shadow: 2px 2px 6px 0px rgba(0, 0, 0, 0.2);
                                padding: 3px;
                                .butto-waihu {
                                    color: rgba(255, 255, 255, 1);
                                }
                            }
                        }

                        .sign-box::after {
                            content: '';
                            position: absolute;
                            width: 0;
                            height: 0;
                            border-style: solid;
                            border-width:0 6px 12px 6px;
                            border-color: transparent transparent  #ccc transparent;
                            top: -30px;
                            right: 80px;
                            border-left: 6px solid transparent;
                            border-right: 6px solid transparent;
                            filter: drop-shadow(5px 5px 10px rgba(0, 0, 0, 0.5));
                        }

                        .name-color{
                            line-height: 32px;
                            padding: 3px 5px;
                            border: 1px solid #fff;
                            border-radius: 4px;
                            font-size: 12px;
                        }
                        .blue{
                            color: #4087f2;
                            background: #ccdefc;
                        }
                        .orange{
                            color: #c0906a;
                            background: #fbd5a3;
                        }
                        .red{
                            color: #d9001b;
                            background: #fccccc;
                        }
                    }
                }

                .top-sign{
                    display: inline-block;
                    color: #ffffff;
                    padding: 0 12px 0 6px;
                    position: absolute;
                    right: 0;
                    top: 0;
                    border-radius: 2px 4px 0 15px;
                    font-weight: 400;
                    text-align: center;
                    max-width: 100px;
                    background-color: #0c7fff;
                    font-size: 12px;
                }

                .info-hang{
                    font-size: 14px;
                    font-weight: 400;
                    word-break: break-all;
                    line-height: 23px;
                    .info-name{
                        color: #838383;
                        vertical-align: top;
                    }

                    .info-label{
                        color: #2F2F2F;
                        display: inline-block;
                        max-width: 65%;
                        word-break: break-all;
                        white-space: pre-line;
                    }
                    .chaoshi{
                        color: #d9001b;
                    }
                }

            }

        }
    }


    .nodata {
        margin-top: 40px;
    }

    .hide-box {
        width: 100%;
        height: 100vh;
        position: fixed;
        top:150px;
        left: 0;
        z-index: 77;
        background: rgba(0, 0, 0, .3);

    }

    .dialog {
        position: absolute;
        top: 148px;
        left: 0;
        height: 400px;
        width: 100vw;
        background-color: #F7FBFF;
        padding: 5px;
        box-sizing: border-box;
        z-index: 99;

        .filter-info-list{
            overflow-y: auto;
            height: 340px;

            .dialog_item {
                display: flex;
                //align-items: center;
                //gap: 20px;
                width: 100%;
                font-size: 12px;
                margin-bottom: 10px;

                .label {
                    width: 80px;
                    color: #6c6c6c;
                    padding-top: 10px;
                }

                .value {
                    width: calc(100% - 105px);
                    display: flex;
                    flex-wrap: wrap;
                    align-items: center;
                    gap: 10px;

                    .item {
                        padding: 10px;
                        background-color: #f4f4f4;
                        border-radius: 3px;
                    }
                    .choosed {
                        color: #fff;
                        background-color: #1681FB;
                    }
                }
            }

            .dialog_item1{
                display: flex;
                align-items: center;
                //gap: 20px;
                width: 100%;
                font-size: 12px;
                margin-bottom: 10px;

                .label {
                    width: 80px;
                    color: #6c6c6c;
                }

                .input-value3{
                    width: 33%;
                    display: flex;
                    flex-wrap: wrap;
                    align-items: center;
                    gap: 10px;
                    padding: 10px 0;
                    border-bottom: 1px solid #e9e9e9;
                    font-size: 12px;
                    background-color: #F7FBFF;
                    color: #6c6c6c;
                }
                .input-value2{
                    width: 33%;
                    display: flex;
                    flex-wrap: wrap;
                    align-items: center;
                    gap: 10px;
                    padding: 10px 0;
                    border-bottom: 1px solid #e9e9e9;
                    font-size: 12px;
                    background-color: #F7FBFF;
                }

                .input-value{
                    width: calc(100% - 105px);
                    display: flex;
                    flex-wrap: wrap;
                    align-items: center;
                    gap: 10px;
                    padding: 10px 0;
                    border-bottom: 1px solid #e9e9e9;
                    font-size: 12px;
                    background-color: #F7FBFF;
                }
            }
        }

        .common-list{
            height: 230px;
        }

        .bottom {
            //width: 100%;
            margin-top: 11px;
            text-align: center;
            padding: 0 25px;
            display: flex;
            justify-content: space-between;

            .chongzhi {
                display: inline-block;
                //margin: 5px 25px;
                line-height: 32px;
                border-radius: 32px;
                background: #fff;
                border: 2px solid #0b7fff;
                box-sizing: border-box;
                color: #0b7fff;
                font-size: 14px;
                width: 45%;
            }

            .queren {
                display: inline-block;
                //margin: 5px 25px;
                line-height: 36px;
                border-radius: 36px;
                background: #0b7fff;
                color: #fff;
                font-size: 14px;
                width: 45%;

            }
        }
    }

    .common{
        height: 300px;
    }
</style>
