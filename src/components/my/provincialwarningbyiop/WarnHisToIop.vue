<template>
    <div class="pwd-wrapper">
        <Header tsTitleTxt="客户脉动感知服务系统" backType="custom" @emGoPrev="goPrev"></Header>

        <div class="main-info">

            <!--任务主体信息-->
            <div class="task-details">
                <!--客户信息-->
                <div class="task-user">
                    <div class="task-left">
                        <img src="static/img/yiban.png" class="task-img" alt='' v-if="warnLevel == 0"/>
                        <img src="static/img/jinji.png" class="task-img" alt='' v-if="warnLevel == 1"/>
                        <img src="static/img/feichangjinji.png" class="task-img" alt='' v-if="warnLevel == 2"/>
                        <div class="top-right" v-if="limitDate">
                            <span class="iconfont shijian2 timedeal"></span>
                            {{limitDate |dateShow('yyyy/MM/dd hh:mm',true) }}
                        </div>
                        <div class="task-center">
                            <div class="user-info" @click="goPerson">
                                <span class="user-num">{{mobile | starTel}}</span>
                                <span class="iconfont youjiantou2 jiantou2icon"></span>
                            </div>

                            <div class="task-number">
                                <span class="label-info-type" v-if="userValue">{{userValue}}</span>
                                <span class="label-info-type" v-if="areaName">{{areaName}}</span>
                                <span class="label-info-type" v-if="custName">{{custName}}</span>
                                <span class="label-info-type" v-if="ysydDtl">{{ysydDtl}}</span>
                            </div>
                        </div>
                    </div>
                    <div v-if="addFeedBackFlag">
                        <div class="iconfont xingzhuang7 iconwaihu" @click="showButtom">
                            <div class="choose-box" v-if="showFlag">
                                <div class="plan-model">
                                    <div class="butto-waihu" @click.stop="benjiCall">本机外呼</div>
                                    <div class="butto-waihu" :class="{'txt-grey':!showCallButtomFlag}"  @click.stop="waiHuProWarn">中间号外呼</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!--业务预警信息-->
                <div class="yw-details">
                    <div v-for="(item,index) in yjLabelDtl" :key="index" class="yw-list">
                        <div class="first-info">
                            <div class="first-left">
                                <span class="first-before"></span>
                                <span class="first-type">
                                    {{item.labelName}}
                                    <span class="first-time" v-show="item.createTime">({{item.createTime |dateShow('yyyy/MM/dd hh:mm',true) }})</span>
                                </span>
                            </div>
                        </div>
                        <div class="info-deal">
                            <div class="move-label-info" v-show="item.source">
                                <span class="yw-name">来源:&nbsp;&nbsp;</span>
                                <span class="yw-label">{{item.source}}</span>
                            </div>
                            <div class="move-label-info" v-show="item.category">
                                <span class="yw-name">类别:&nbsp;&nbsp;</span>
                                <span class="yw-label">{{item.category}}</span>
                            </div>
                        </div>

                        <div class="move-label-info" v-show="item.labelRemark">
                            <span class="yw-name">备注:&nbsp;&nbsp;</span>
                            <span class="yw-label">{{item.labelRemark}}</span>
                        </div>

                        <div v-if="item.yjLabelAttr && item.yjLabelAttr.length > 0" v-for="(itm1,idx1) in item.yjLabelAttr" :key="idx1">
                            <div class="move-label-info" v-show="itm1.yjLabelValue">
                                <span class="yw-name">{{itm1.yjLabelName}}:&nbsp;&nbsp;</span>
                                <span class="yw-label">{{itm1.yjLabelValue}}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--历史跟进信息-->
            <div class="move-title" v-show="yjTaskHis && yjTaskHis.length > 0">
                <div class="move-title-info">工单轨迹（{{yjTaskHis.length}}）</div>
                <div class="move-history" @click="goHistory('1','工单轨迹')" v-show="yjTaskHis.length > 1">
                    查看更多
                    <span class="iconfont youjiantou2 jiantou2icon"></span>
                </div>
            </div>
            <div class="his-info" v-show="yjTaskHis && yjTaskHis.length > 0">
                <div v-for="(item,index) in yjTaskHis.slice(0,1)" :key="index" class="his-list">
                    <div class="his-xiang">
                        <div class="his-first">
                            <span class="iconfont checkboxround0 icon-checkbox checkbox2"></span>
                            <span class="his-time" v-show="item.dealDate">{{item.dealDate | timeFiler}}&nbsp;,&nbsp;</span>

                            <span v-show="item.dealName || item.dealMobile">
                                <span class="his-time" v-show="item.dealName">{{item.dealName}}</span>
                                <span class="his-time" v-show="item.dealMobile">({{item.dealMobile}})</span>
                            </span>
                            <span v-show="!(item.dealName || item.dealMobile)">
                                 <span class="his-time" v-show="item.dealOperator">{{item.dealOperator}}</span>
                            </span>
                        </div>

                        <div class="his-label-info" v-show="item.warnType">
                            <span class="his-name">预警类型:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.warnType}}</span>
                        </div>
                        <div class="his-label-info" v-show="item.submitSource">
                            <span class="his-name">处理渠道:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.submitSource | submitSourceFilter}}</span>
                        </div>
                        <div class="his-label-info" v-show="item.dealAction">
                            <span class="his-name">处理动作:&nbsp;&nbsp;</span>
                            <span class="his-label" v-show="item.dealAction == '0' && item.dealActionFeedback == '0'">立单</span>
                            <span class="his-label" v-show="item.dealAction == '1'">转派</span>
                            <span class="his-label" v-show="item.dealAction == '2' && item.dealActionFeedback == '1'">跟进</span>
                            <span class="his-label" v-show="item.dealAction == '2' && item.dealActionFeedback == '2'">结单</span>
                            <span class="his-label" v-show="item.dealAction == '2' && item.dealActionFeedback == '4'">IOP自动结单</span>
                            <span class="his-label" v-show="item.dealAction == '3'">追加反馈</span>
                            <span class="his-label" v-show="item.dealAction == '4'">一体化外呼</span>
                            <span class="his-label" v-show="item.dealAction == '5'">阿拉盯跟进</span>
                            <span class="his-label" v-show="item.dealAction == '6'">在线跟进</span>
                            <span class="his-label" v-show="item.dealAction == '7'">在线撤单</span>
                            <span class="his-label" v-show="item.dealAction == '8'">工单调派</span>
                            <span class="his-label" v-show="item.dealAction == '9'">添加意见</span>
                        </div>

                        <!--在线系统-->
                        <div class="his-label-info"  v-show="item.reminderRemark">
                            <span class="his-name">催办提醒内容:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.reminderRemark}}</span>
                        </div>
                        <div class="his-label-info"  v-show="item.userDemandRemark">
                            <span class="his-name">追加诉求内容:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.userDemandRemark}}</span>
                        </div>

                        <!--一体化系统-->
                        <div class="his-label-info"  v-show="item.processResultId">
                            <span class="his-name">子任务单办理结果名称:&nbsp;&nbsp;</span>
                            <span class="his-label" v-show="item.processResultId == '0'">外呼失败</span>
                            <span class="his-label" v-show="item.processResultId == '1'">外呼成功</span>
                        </div>
                        <div class="his-label-info"  v-show="item.callState">
                            <span class="his-name">是否接通:&nbsp;&nbsp;</span>
                            <span class="his-label" v-show="item.callState == '0'">未接通</span>
                            <span class="his-label" v-show="item.callState == '1'">接通</span>
                        </div>
                        <div class="his-label-info"  v-show="item.talkTime">
                            <span class="his-name">通话时长:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.talkTime}}s</span>
                        </div>
                        <div class="his-label-info"  v-show="item.offeringName">
                            <span class="his-name">实际办理产品:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.offeringName}}</span>
                        </div>
                        <div class="his-label-info"  v-show="item.yjFailReason">
                            <span class="his-name">预警外呼失败原因:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.yjFailReason | yjFailReasonFilter }}</span>
                        </div>

                        <div class="his-label-info" v-show="item.followMsisdn">
                            <span class="his-name">跟进人：</span>
                            <span class="his-label">{{item.followName}}<span v-show="item.followMsisdn">({{item.followMsisdn}})</span></span>
                        </div>
                        <div class="his-label-info"  v-show="item.followDealType">
                            <span class="his-name">预约方式:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.followDealType}}</span>
                        </div>
                        <div class="his-label-info"  v-show="item.dueTime">
                            <span class="his-name">预约时间:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.dueTime | timeFiler}}</span>
                        </div>
                        <div class="his-label-info"  v-show="item.visitAddr">
                            <span class="his-name">上门地址:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.visitAddr}}</span>
                        </div>
                        <div class="his-label-info"  v-show="item.dueHallName">
                            <span class="his-name">预约厅店:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.dueHallName}}</span>
                        </div>
                        <div class="his-label-info" v-show="item.contactsName">
                            <span class="his-name">客户侧联系人：</span>
                            <span class="his-label">{{item.contactsName}}<span v-show="item.contactsMsisdn">({{item.contactsMsisdn}})</span></span>
                        </div>

                        <div class="his-label-info" v-show="item.zpReceiveMobile">
                            <span class="his-name" v-show="item.dealAction == '1'">转派接收人：</span>
                            <span class="his-name" v-show="item.dealAction == '2'">上门协同人员：</span>
                            <span class="his-name" v-show="item.dealAction == '8'">调配接收人：</span>
                            <span class="his-label">{{item.zpReceiveName}}<span v-show="item.zpReceiveMobile">({{item.zpReceiveMobile}})</span></span>
                        </div>
                        <div class="his-label-info"  v-show="item.dealType">
                            <span class="his-name">处理方式:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.dealType}}</span>
                        </div>
                        <div class="his-label-info"  v-show="item.clockAddress">
                            <span class="his-name">上门地址:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.clockAddress}}</span>
                        </div>
                        <div class="his-label-info"  v-show="item.distance">
                            <span class="his-name">结单距离:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.distance}}米</span>
                        </div>
                        <div class="his-label-info" v-show="item.abnormalFlag">
                            <span class="his-name">异动情况:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.abnormalFlag}}</span>
                        </div>
                        <div class="his-label-info" v-show="item.abnormalType">
                            <span class="his-name">异动类型:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.abnormalType}}</span>
                        </div>

                        <div v-show="item.evidenceList && item.evidenceList.length > 0" v-for="(itm1,idx1) in item.evidenceList" :key="idx1">
                            <div class="his-label-info" v-show="itm1.evidenceType">
                                <span class="his-name">取证类型{{idx1+1}}:&nbsp;&nbsp;</span>
                                <span class="his-label">{{itm1.evidenceType}}</span>
                            </div>
                            <div class="his-label-info" v-show="itm1.evidenceOtherRemark">
                                <span class="his-name">补充说明{{idx1+1}}:&nbsp;&nbsp;</span>
                                <span class="his-label">{{itm1.evidenceOtherRemark}}</span>
                            </div>
                            <div class="his-label-info" v-if="itm1.evidenceKeys">
                                <span class="his-name">取证上传信息{{idx1+1}}:&nbsp;&nbsp;</span>
                                <span class="his-label" v-show="itm1.audioShowList && itm1.audioShowList.length > 0">{{itm1.audioShowList.join(",")}}</span>
                                <span>
                                    <div>
                                        <ul class="photo-ul">
                                            <li class="photo-li" :key="idx"
                                                v-for="(item1,idx) in itm1.photoShowList"
                                                v-show="item1.indexOf('http') > 0 " @click="zoomStorePhoto(item1)">
                                                <div class="img-wrap">
                                                    <img :src="item1" :ref="'YW'+idx"/>
                                                </div>
                                            </li>
                                        </ul>
                                        <ul class="video-ul">
                                            <li class="video-li" :key="idx"
                                                v-for="(item1,idx) in itm1.videoShowList"
                                                v-show="item1.indexOf('http') > 0 ">
                                                <div class="img-wrap">
                                                    <video ref="videoRef"
                                                           style="width:100%;max-height:200px;min-height: 200px; background: #000"
                                                           controls
                                                           controlslist="nodownload">
                                                        <source type="video/mp4" :src="item1"/>
                                                    </video>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                </span>
                            </div>

                        </div>


                        <div class="his-label-info" v-show="item.rightFlag && item.abnormalFlag == '无异动'">
                            <span class="his-name">预警信息是否准确:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.rightFlag}}</span>
                        </div>
                        <div class="his-label-info" v-show="item.abnormalBasis && (item.abnormalFlag == '无异动' || item.abnormalFlag == '疑似')">
                            <span class="his-name">判断依据:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.abnormalBasis}}</span>
                        </div>
                        <div class="his-label-info" v-show="item.dealResult">
                            <span class="his-name">维护结果:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.dealResult}}</span>
                        </div>
                        <div class="his-label-info" v-show="item.fakerDesc">
                            <span class="his-name">反杀详情:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.fakerDesc}}</span>
                        </div>
                        <div class="his-label-info" v-show="item.fakerOtherDesc">
                            <span class="his-name">反杀详情说明:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.fakerOtherDesc}}</span>
                        </div>
                        <div class="his-label-info" v-if="item.fakerKeys">
                            <span class="his-name">反杀证据:&nbsp;&nbsp;</span>
                            <span class="his-label" v-show="item.audioShowList22 && item.audioShowList22.length > 0">{{item.audioShowList22.join(",")}}</span>
                            <span>
                                <div>
                                    <ul class="photo-ul">
                                        <li class="photo-li" :key="idx"
                                            v-for="(item1,idx) in item.photoShowList22"
                                            v-show="item1.indexOf('http') > 0 " @click="zoomStorePhoto(item1)">
                                            <div class="img-wrap">
                                                <img :src="item1" :ref="'YW'+idx"/>
                                            </div>
                                        </li>
                                    </ul>
                                    <ul class="video-ul">
                                        <li class="video-li" :key="idx"
                                            v-for="(item1,idx) in item.videoShowList22"
                                            v-show="item1.indexOf('http') > 0 ">
                                            <div class="img-wrap">
                                                <video ref="videoRef"
                                                       style="width:100%;max-height:200px;min-height: 200px; background: #000"
                                                       controls
                                                       controlslist="nodownload">
                                                    <source type="video/mp4" :src="item1"/>
                                                </video>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                            </span>
                        </div>


                        <div class="his-label-info" v-show="item.defeatReason">
                            <span class="his-name">失败原因大类:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.defeatReason}}</span>
                        </div>
                        <div class="his-label-info" v-show="item.defeatRemark">
                            <span class="his-name">失败原因小类:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.defeatRemark}}</span>
                        </div>
                        <div class="his-label-info" v-show="item.defeatOtherDesc">
                            <span class="his-name">失败原因说明:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.defeatOtherDesc}}</span>
                        </div>

                        <!--失败原因-->
                        <div v-show="item.defeatReasonList && item.defeatReasonList.length > 0" v-for="(itm2,idx2) in item.defeatReasonList" :key="idx2">
                            <div class="his-label-info" v-show="itm2.defeatReason">
                                <span class="his-name">失败原因{{idx2+1}}:&nbsp;&nbsp;</span>
                                <span class="his-label">{{itm2.defeatReason}}-{{itm2.defeatRemark}}</span>
                            </div>
                            <div class="his-label-info" v-show="itm2.defeatOtherDesc">
                                <span class="his-name">失败原因补充说明{{idx2+1}}:&nbsp;&nbsp;</span>
                                <span class="his-label">{{itm2.defeatOtherDesc}}</span>
                            </div>
                        </div>

                        <!--图片-->
                        <div class="his-label-info" v-if="item.photoKeys">
                            <span class="his-name">照片:&nbsp;&nbsp;</span>
                            <span>
                                <div>
                                    <ul class="photo-ul">
                                        <li class="photo-li" :key="idx"
                                            v-for="(item1,idx) in item.photoKeys.split(',')"
                                            v-show="item1.indexOf('http') > 0" @click="zoomStorePhoto(item1)">
                                            <div class="img-wrap">
                                                <img :src="item1" :ref="'YW'+idx"/>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                            </span>
                        </div>

                        <!--最新推荐政策-->
                        <div class="his-label-info" v-show="item.latestRecommendPolicy">
                            <span class="his-name">最新推荐政策:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.latestRecommendPolicy}}</span>
                        </div>
                        <div class="his-label-info" v-show="item.customerIsAccept">
                            <span class="his-name">客户是否接受:&nbsp;&nbsp;</span>
                            <span class="his-label" v-if="item.customerIsAccept == '1'">是</span>
                            <span class="his-label" v-else>否</span>
                        </div>
                        <div class="his-label-info" v-show="item.remark">
                            <span class="his-name" v-show="item.dealAction == '1'">转派意见:&nbsp;&nbsp;</span>
                            <span class="his-name" v-show="item.dealAction == '2'">处理反馈意见:&nbsp;&nbsp;</span>
                            <span class="his-name" v-show="item.dealAction == '3'">追加反馈信息:&nbsp;&nbsp;</span>
                            <span class="his-name" v-show="item.dealAction == '5'">跟进备注:&nbsp;&nbsp;</span>
                            <span class="his-name" v-show="item.dealAction == '8'">调配意见:&nbsp;&nbsp;</span>
                            <span class="his-name" v-show="item.dealAction == '9'">处理意见:&nbsp;&nbsp;</span>
                            <span class="his-name" v-show="!item.dealAction">备注:&nbsp;&nbsp;</span>
                            <span class="his-label">{{item.remark}}</span>
                        </div>
                        <div class="his-label-info" v-if="item.zjlFileKeys">
                            <span class="his-name">证据链:&nbsp;&nbsp;</span>
                            <span class="his-label" v-show="item.audioShowList && item.audioShowList.length > 0">{{item.audioShowList.join(",")}}</span>
                            <span>
                                <div>
                                    <ul class="photo-ul">
                                        <li class="photo-li" :key="idx"
                                            v-for="(item1,idx) in item.photoShowList"
                                            v-show="item1.indexOf('http') > 0 " @click="zoomStorePhoto(item1)">
                                            <div class="img-wrap">
                                                <img :src="item1" :ref="'YW'+idx"/>
                                            </div>
                                        </li>
                                    </ul>
                                    <ul class="video-ul">
                                        <li class="video-li" :key="idx"
                                            v-for="(item1,idx) in item.videoShowList"
                                            v-show="item1.indexOf('http') > 0 ">
                                            <div class="img-wrap">
                                                <video ref="videoRef"
                                                       style="width:100%;max-height:200px;min-height: 200px; background: #000"
                                                       controls
                                                       controlslist="nodownload">
                                                    <source type="video/mp4" :src="item1"/>
                                                </video>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                            </span>
                        </div>


                    </div>
                </div>
            </div>

            <!--历史轨迹-->
            <div class="move-title">
                <div class="move-title-info">业务切片</div>
                <div class="move-history" @click="goHistory('2','业务切片')">
                    查看明细
                    <span class="iconfont youjiantou2 jiantou2icon"></span>
                </div>
            </div>

            <!--降档挽留-->
            <div class="move-title" v-show="showDownFlg">
                <div class="move-title-info">挽留政策</div>
                <div class="move-history" @click="goDownInfo" v-show="showDownFlg">
                    查看明细
                    <span class="iconfont youjiantou2 jiantou2icon"></span>
                </div>
            </div>

            <!--追加反馈-->
            <div class="move-title" v-show="addFeedBackFlag">
                <div class="move-title-info">追加反馈</div>
            </div>
            <div class="order-handle" :class="{chudi:isReback === 'reback'}" v-show="addFeedBackFlag">
                <div class="handle-details">
                    <div class="handle-xiang">
                        <div class="handle-label">取证上传</div>
                        <div class="handle-value" @click="openCamThree()">
                            <span class="zi-ti" v-show="!isCameraThree">未拍摄</span>
                            <span class="zi-ti" v-show="isCameraThree">已拍摄</span>
                            <span class="iconfont jiantou-copy-copy iconphtot11"></span>
                        </div>
                    </div>
                </div>
                <div class="handle-details">
                    <div class="handle-xiang">
                        <div class="handle-label">反馈信息</div>
                    </div>
                    <div class="remark">
                        <div class="text-abl">{{backInfo.length}}/500</div>
                        <textarea type="text" placeholder="请输入反馈信息" v-model="backInfo" maxlength="500"></textarea>
                    </div>
                </div>

                <div class="end-bottom" v-show="addFeedBackFlag">
                    <button class="end-submit" :class="{active:isClick}" @click="submit">提交</button>
                </div>
            </div>

            <!--工单处理-->
            <div v-show="isReback === 'reback'">
                <div class="move-title">
                    <div class="move-title-info">工单处理</div>
                </div>
                <div class="order-handle">
                    <div class="order-tar" v-if="isManaager ==='manager'">
                        <div :class="{choosed:isShow === '1'}" @click="isShows('1')" ><span class="iconfont yuchuli" ></span>工单调配</div>
                        <div :class="{choosed:isShow === '2'}" @click="isShows('2')"><span class="iconfont chulifankui"></span>添加意见</div>
                    </div>
                    <!--工单调配-->
                    <div v-show="isShow === '1'">
                        <div class="handle-details">
                            <div class="handle-xiang">
                                <div class="handle-label" :class="{active:true}">选择派单接收人<span>*</span></div>
                                <div class="handle-value" @click="transfer()">
                                    <span class="zi-ti" v-show="zpReceiveName == ''">请选择</span>
                                    <span class="zi-ti">{{zpReceiveName}}</span>
                                    <span class="iconfont jiantou-copy-copy"></span>
                                </div>
                            </div>
                        </div>

                        <div class="handle-details">
                            <div class="handle-xiang">
                                <div class="handle-label">调配意见</div>
                                <div class="xialakuang-111" @click="commomGdtpChoose()">
                                    <span class="zi-ti" v-if="gdtpCommon != ''">{{gdtpCommon}}</span>
                                    <span class="zi-ti" v-else>请选择常用调配意见</span>
                                    <span class="iconfont jiantou-copy-copy iconphtot11"></span>
                                </div>
                            </div>
                            <div class="remark">
                                <div class="text-abl">{{gdtpRemark.length}}/500</div>
                                <textarea type="text" placeholder="请输入调配意见" v-model="gdtpRemark" maxlength="500"></textarea>
                            </div>
                            <div class="common-flag" @click="clickGdtpFlag()">
                                <i class="iconfont"  :class="[gdtpRemarkFlag ? 'check-box': 'fuxuankuangweixuanzhong']"></i>
                                <span>设为常用调配意见</span>
                            </div>
                        </div>
                        <div class="end-bottom">
                            <button class="end-submit" :class="{active:isTpClick}" @click="submitTiaoPei">提交</button>
                        </div>
                    </div>

                    <!--添加意见-->
                    <div v-show="isShow === '2'">
                        <div class="handle-details">
                            <div class="handle-xiang">
                                <div class="handle-label">处理意见</div>
                                <div class="xialakuang-111" @click="clComRemChoose()">
                                    <span class="zi-ti" v-if="clComRem != ''">{{clComRem}}</span>
                                    <span class="zi-ti" v-else>请选择常用处理意见</span>
                                    <span class="iconfont jiantou-copy-copy iconphtot11"></span>
                                </div>
                            </div>
                            <div class="remark">
                                <div class="text-abl">{{clRemark.length}}/500</div>
                                <textarea type="text" placeholder="请输入处理意见" v-model="clRemark" maxlength="500"></textarea>
                            </div>
                            <div class="common-flag" @click="clickClFlag()">
                                <i class="iconfont"  :class="[clRemarkFlag ? 'check-box': 'fuxuankuangweixuanzhong']"></i>
                                <span>设为常用处理意见</span>
                            </div>
                        </div>
                        <div class="end-bottom">
                            <button class="end-submit" :class="{active:isClClick}" @click="submitChuLi">提交</button>
                        </div>
                    </div>
                </div>
            </div>

        </div>

        <!--放大图片-->
        <div class="big-img-wrap" v-show="bigImgFlg">
            <span class="close-btn iconfont guanbi1" @click="bigImgFlg=false"></span>
            <img class="big-img" ref="bigImg" @click="bigImgFlg=false"/>
        </div>

        <!--调配人员选择组件-->
        <TransferSjyjIop ref="executeRef" v-if="transferFlag"
                         @emCloseInit="closeExecute"
                         @emSubmit="getExecute"
                         :orgaId="cityId"
                         :isTransFer="'1'"
                         :countryId="countryId"
                         :headerName="'调配人员选择'"></TransferSjyjIop>
        <!--取证提示-->
        <div class="camera-wrap" v-if="eviKeyFlag">
            <EviToIopComponent  :showCameraFlg="eviKeyFlag"
                                :videoMainId="evlKeyMainId"
                                :followList="zjlFileKeys"
                                @emClose="getEvidencePrev3"
                                @emSign="getEvidenceClose3"
                                @getVideoMainId="getVideoMainId3"/>
        </div>
    </div>

</template>

<script>
    import Header from 'components/common/Header.vue'
    import ClientJs from '@/base/clientjs';
    import Storage from '@/base/storage'
    import {chgStrToDate,dateFormat} from '@/base/utils';
    import { zhuoWangCallJs } from '@/base/zhuoWangCall.js'
    import ImageObj from '@/base/request/ImageServReq'
    import { decrptParam } from '@/base/encrptH5.js'
    import NlDropdown from "components/common/NlDropdown/dropdown.js";
    import EviToIopComponent from "components/my/provincialwarningbyiop/EviToIopComponent.vue";
    import TransferSjyjIop from'components/my/provincialwarningbyiop/TransferSjyjIop.vue';

    export default {
        name: 'WarnHisToIop',
        components: { Header,EviToIopComponent,TransferSjyjIop },
        mixins: [zhuoWangCallJs],
        data() {
            return {
                srcCorm:"",            //页面来源
                workId:"",             //任务编码
                subId:"",              //工单编号
                warnType:"",              //预警备注
                warnLevel:"",              //预警等级
                limitDate:"",              //到期时间
                authId: '',  //外呼工号
                telB: '',     //用户手机号码
                telX: '',    //客户经理虚拟号码
                bindId: '',    //绑定编码
                userInfo:{},          //用户信息

                mobile:"",             //预警客户手机号码
                userValue:"",          //用户价值
                areaName:"",           //网格名称
                ysydDtl:"",            //疑似异动
                custName:"",            //集团名称
                yjLabelDtl:[],         //预警标签明细信息
                yjTaskHis:[],          //历史跟进信息
                hisXianInt: 0,        //历史轨迹数量
                bigImgFlg: false,        //放大照片

                backInfo:'',  //反馈信息
                isClick:false,  //按钮是否点击
                addFeedBackFlag:false,  //追加处理反馈权限

                showDownFlg: false,//是否展示
                retentionAuthority:false,//降档挽留权限

                showFlag:false,  //外呼弹窗
                showCallButtomFlag:false,
                isManaager:'',//管理员进来有这个
                isReback:'',//展示添加意见

                isCameraThree:false,
                eviKeyFlag:false,
                evlKeyMainId:'zjfkEvlKeyMainId'+new Date().getTime(),
                zjlFileKeys:'',

                // 城市长编码
                LONG_CITY_LIST: [
                    { id: '1000250', shortId: '14', label: '南京' },
                    { id: '1000510', shortId: '19', label: '无锡' },
                    { id: '1000511', shortId: '18', label: '镇江' },
                    { id: '1000512', shortId: '11', label: '苏州' },
                    { id: '1000513', shortId: '20', label: '南通' },
                    { id: '1000514', shortId: '23', label: '扬州' },
                    { id: '1000515', shortId: '22', label: '盐城' },
                    { id: '1000516', shortId: '16', label: '徐州' },
                    { id: '1000517', shortId: '12', label: '淮安' },
                    { id: '1000518', shortId: '15', label: '连云港' },
                    { id: '1000519', shortId: '17', label: '常州' },
                    { id: '1000523', shortId: '21', label: '泰州' },
                    { id: '1000527', shortId: '13', label: '宿迁' },
                    { id: '2000250', shortId: '99', label: '全省' }
                ],

                cityId:'',            //归属地市编码
                countryId:'',         //区县编码


                isShow:"1",//1:工单调配  2：添加意见

                zpReceiveName:'',        //转派接收人姓名
                zpReceiveMobile:'',     //转派接收人手机号
                zpReceiveOperator:'',   //转派接收人统一人员工号
                transferFlag:false,   //转派开关

                gdtpList:[],//工单调配处理意见
                gdtpCommon:'',  //工单常用调配意见
                gdtpRemark:'',  //工单调配意见
                gdtpRemarkFlag:false,//工单常用意见选中
                isTpClick:false,

                tjyjList:[],//常用处理意见
                clComRem:'',  //常用处理意见
                clRemark:'',  //处理意见
                clRemarkFlag:false,  //处理意见常用选择
                isClClick:false,
            }
        },
        created(){
            this.workId = this.$route.query.workId;
            this.subId = this.$route.query.subId;
            this.warnType = this.$route.query.warnType;
            this.srcCorm = this.$route.query.srcCorm;
            this.warnLevel= this.$route.query.warnLevel;
            this.limitDate= this.$route.query.limitDate;
            this.initData();  //查询预警明细信息
            this.qryConfigCall();//查询外呼工号配置
            this.crmQryEmployeeRole();//降档挽留权限查询

            //从列表我的管辖进来，必有田间意见
            if(this.$route.query.isReback && this.$route.query.isReback === 'reback'){
                this.isReback = this.$route.query.isReback;
                //添加意见  常用意见查询
                this.commonList("添加意见");

                //判断有没有工单调配权限
                if(this.$route.query.isManaager && this.$route.query.isManaager === 'manager'){
                    this.isManaager = this.$route.query.isManaager;
                    //工单调配  常用意见查询
                    this.commonList("工单调配");
                    //获取转派归属机构信息
                    this.getOrgaInfo();
                }else {
                    this.isShow = '2';
                }
            }


        },
        methods: {
            goPrev() {
                this.srcCorm = this.$route.query.srcCorm;
                if(this.srcCorm == 'myTaskListNew'){
                    this.$router.push({
                        path: '/MyTaskList'
                    })
                }else if(this.srcCorm == 'workStage'){
                    this.$router.push('/');
                }else if(this.srcCorm == 'warnOrderList'){
                    this.$router.push({
                        path: '/warnOrderList',
                    })
                }else if(this.srcCorm == 'MaintainOrderManagement'){
                    this.$router.push({
                        path: '/MaintainOrderManagement',
                        query: {
                            yixian:this.$route.query.yixian,
                            gobackFlag:this.$route.query.gobackFlag,
                        }
                    })
                }else {
                    history.go(-1)
                }
            },
            goPerson(){
                this.srcCorm = this.$route.query.srcCorm;
                this.$router.push({
                    path: '/marketingAssistant',
                    query: {
                        telnum: this.mobile,
                        conform:"warnHisToIop",
                        workId:this.workId,
                        subId:this.subId,
                        warnLevel:this.warnLevel,
                        limitDate:this.limitDate,
                        warnType:this.warnType,
                        srcCorm:this.srcCorm,
                        yixian:this.$route.query.yixian,
                        gobackFlag:this.$route.query.gobackFlag,
                    },
                })
            },
            showButtom(){
                this.showFlag = !this.showFlag;
            },
            qryConfigCall(){
                let _this = this;
                let paramCfg = {
                    telB: Storage.session.get('userInfo').servNumber
                }
                //查询外呼工号配置
                _this.$http.post('/xsb/gridCenter/outCallByzw/h5GetOutOperInfo', paramCfg).then((res) => {
                    console.info("查询配置111",paramCfg)
                    if (res.data.retCode == '0') {
                        if(res.data.data && res.data.data.subAppId){
                            _this.showCallButtomFlag = true;
                        }else {
                            _this.showCallButtomFlag = false;
                            return
                        }

                        if(res.data.data && res.data.data.subAppKey){
                            _this.showCallButtomFlag = true;
                        }else {
                            _this.showCallButtomFlag = false;
                            return
                        }

                    } else {
                        _this.showCallButtomFlag = false;
                    }
                })
            },
            //本机外呼
            benjiCall(){
                this.callUser(false);
            },
            //中间号外呼
            waiHuProWarn(){
                if(!this.showCallButtomFlag){
                    return
                }

                let userId = '';
                let workId = this.workId;
                let senceType = 'shengJiWarn';
                // this.WaiHuStart(this.mobile,userId,workId,senceType);
                this.qryConfigBuWaihu(this.mobile,userId,workId,senceType);
            },
            //本机外呼
            callUser(waihuFlag){
                if(waihuFlag){
                    //外呼的是虚拟号
                    console.info("代客投诉预警外呼成功--卓望",this.telX)
                    ClientJs.openCallPhoneView(this.telX);
                }else {
                    //外呼的是手机号码
                    console.info("代客投诉预警外呼成功--客户端",this.mobile)
                    ClientJs.openCallPhoneView(this.mobile);
                }

            },
            initData(){
                this.yjLabelDtl = []
                let param = {
                    workId : this.workId,
                    subId : this.subId,
                    orderType: this.$route.query.orderType ? this.$route.query.orderType: '',
                }
                this.$http.post('/xsb/gridCenter/warningOrder/h5YjInfoQueryToIop',param).then(res => {
                    let{retCode,retMsg,data} = res.data;
                    console.info("data",res.data)
                    if(retCode == '0'){
                        //用户手机号码
                        if(data.mobile){
                            this.mobile = data.mobile;
                            let isLastOperator = false;
                            //如果最后处理人 是当前用户  存在追加反馈权限
                            if(data.lastOperator && data.lastOperator == Storage.session.get('userInfo').servNumber){
                                isLastOperator = true;
                            }
                            console.info(" data.lastDealer", data.lastOperator,isLastOperator)

                            this.getHisInfo(isLastOperator);//获取历史跟进信息
                        }


                        if(data.userValue){
                            this.userValue = data.userValue;
                        }
                        if(data.areaName){
                            this.areaName = data.areaName;
                        }
                        if(data.ysydDtl){
                            this.ysydDtl = data.ysydDtl;
                        }
                        if(data.custName){
                            this.custName = data.custName;
                        }
                        //用户业务标签信息
                        if(data.yjLabelDtl && data.yjLabelDtl.length > 0){
                            this.yjLabelDtl = data.yjLabelDtl;
                        }
                    }else {
                        this.$alert(retMsg || 'IOP-预警明细信息查询失败');
                    }
                })
            },
            //获取历史信息
            getHisInfo(isLastOperator){
                //获取当前工单的历史跟进情况
                this.getHis(isLastOperator);
            },
            getHis(isLastOperator){
                this.yjTaskHis = [];
                let param = {
                    mobile : this.mobile,
                    subId : this.subId,
                    queryType : '1',
                }
                this.$http.post('/xsb/gridCenter/warningOrder/h5YjHisQueryToIop',param).then(res => {
                    let{retCode,retMsg,data} = res.data;
                    console.info("h5YjInfoQueryToIop",res.data)
                    if(retCode == '0'){
                        //历史预警数据
                        if(data.yjTaskHis && data.yjTaskHis.length > 0){
                            this.yjTaskHis = data.yjTaskHis;

                            for(let i = 0; i < this.yjTaskHis.length; i++){

                                //照片obs的key
                                if(this.yjTaskHis[i].photoKeys){
                                    let url = '/xsb/personBusiness/queryCalculate/h5DownBillFollowUpPic'
                                    let theArr1 = this.yjTaskHis[i].photoKeys.split(',')
                                    for (let j = 0; j < theArr1.length; j++) {
                                        ImageObj.getImgUrl(theArr1[j], {"comeFrom": "provinceWarning"}, url).then(res => {
                                            if (this.yjTaskHis[i].photoKeys) {
                                                this.yjTaskHis[i].photoKeys = this.yjTaskHis[i].photoKeys + ',' + res
                                            } else {
                                                this.yjTaskHis[i].photoKeys = res
                                            }

                                        })
                                    }
                                }

                                //证据链
                                if(this.yjTaskHis[i].zjlFileKeys) {
                                    Vue.set(this.yjTaskHis[i], "photoShowList", []);
                                    Vue.set(this.yjTaskHis[i], "videoShowList", []);
                                    Vue.set(this.yjTaskHis[i], "audioShowList", []);
                                    let url = '/xsb/personBusiness/queryCalculate/h5DownBillFollowUpPic'
                                    let theArr2 = this.yjTaskHis[i].zjlFileKeys.split(',')
                                    for (let j = 0; j < theArr2.length; j++) {
                                        //视频
                                        if(theArr2[j].indexOf(".mp4") >= 0){
                                            ImageObj.getImgUrl(theArr2[j], {"comeFrom": "provinceWarning"}, url).then(res => {
                                                this.yjTaskHis[i].videoShowList.push(res);
                                            })
                                        }else if(theArr2[j].indexOf(".jpg") >= 0){
                                            //图片
                                            ImageObj.getImgUrl(theArr2[j], {"comeFrom": "provinceWarning"}, url).then(res => {
                                                this.yjTaskHis[i].photoShowList.push(res);
                                            })
                                        }else {
                                            let audioNameArr =theArr2[j].split("_");
                                            this.yjTaskHis[i].audioShowList.push(audioNameArr[2])
                                        }
                                    }
                                }
                                //反杀证据
                                if(this.yjTaskHis[i].fakerKeys) {
                                    Vue.set(this.yjTaskHis[i], "photoShowList22", []);
                                    Vue.set(this.yjTaskHis[i], "videoShowList22", []);
                                    Vue.set(this.yjTaskHis[i], "audioShowList22", []);
                                    let url = '/xsb/personBusiness/queryCalculate/h5DownBillFollowUpPic'
                                    let theArr3 = this.yjTaskHis[i].fakerKeys.split(',')
                                    for (let j = 0; j < theArr3.length; j++) {
                                        //视频
                                        if(theArr3[j].indexOf(".mp4") >= 0){
                                            ImageObj.getImgUrl(theArr3[j], {"comeFrom": "provinceWarning"}, url).then(res => {
                                                this.yjTaskHis[i].videoShowList22.push(res);
                                            })
                                        }else if(theArr3[j].indexOf(".jpg") >= 0){
                                            //图片
                                            ImageObj.getImgUrl(theArr3[j], {"comeFrom": "provinceWarning"}, url).then(res => {
                                                this.yjTaskHis[i].photoShowList22.push(res);
                                            })
                                        }else {
                                            let audioNameArr = theArr3[j].split("_");
                                            this.yjTaskHis[i].audioShowList22.push(audioNameArr[2])
                                        }
                                    }
                                }

                                //异动取证List
                                if(this.yjTaskHis[i].evidenceList && this.yjTaskHis[i].evidenceList.length > 0){
                                    for (let x = 0; x < this.yjTaskHis[i].evidenceList.length; x++){
                                        if(this.yjTaskHis[i].evidenceList[x].evidenceKeys){
                                            Vue.set(this.yjTaskHis[i].evidenceList[x], "photoShowList", []);
                                            Vue.set(this.yjTaskHis[i].evidenceList[x], "videoShowList", []);
                                            Vue.set(this.yjTaskHis[i].evidenceList[x], "audioShowList", []);
                                            let url = '/xsb/personBusiness/queryCalculate/h5DownBillFollowUpPic'
                                            let theArr4 = this.yjTaskHis[i].evidenceList[x].evidenceKeys.split(',')
                                            for (let j = 0; j < theArr4.length; j++) {
                                                //视频
                                                if(theArr4[j].indexOf(".mp4") >= 0){
                                                    ImageObj.getImgUrl(theArr4[j], {"comeFrom": "provinceWarning"}, url).then(res => {
                                                        this.yjTaskHis[i].evidenceList[x].videoShowList.push(res);
                                                    })
                                                }else if(theArr4[j].indexOf(".jpg") >= 0){
                                                    //图片
                                                    ImageObj.getImgUrl(theArr4[j], {"comeFrom": "provinceWarning"}, url).then(res => {
                                                        this.yjTaskHis[i].evidenceList[x].photoShowList.push(res);
                                                    })
                                                }else {
                                                    let audioNameArr = theArr4[j].split("_");
                                                    this.yjTaskHis[i].evidenceList[x].audioShowList.push(audioNameArr[2])
                                                }
                                            }
                                        }
                                    }
                                }

                            }


                            //当前处理人是最后责任人
                            if(isLastOperator){
                                let newerTime = '';
                                for (const task of this.yjTaskHis) {
                                    if (task.dealAction == 2 && task.dealActionFeedback == 2) {
                                        newerTime = task.dealDate;
                                        break;
                                    }
                                }
                                console.info("newerTime",newerTime);
                                if(newerTime != ''){
                                    this.getJudeTime(newerTime);
                                }
                            }
                        }
                    }else {
                        this.$alert(retMsg || 'IOP-用户预警跟进历史查询失败');
                    }
                })
            },
            getJudeTime(newerTime){
                this.$http.post('/xsb/gridCenter/warningOrder/h5QryAddFeedBackTime').then(res => {
                    let{data} = res.data;
                    let num = +data;
                    console.info("h5QryAddFeedBackTime",res.data)
                    // 将输入的日期字符串转换为标准日期格式
                    const formattedDateStr = `${newerTime.slice(0, 4)}-${newerTime.slice(4, 6)}-${newerTime.slice(6, 8)}T${newerTime.slice(9)}`;
                    const inputDate = new Date(formattedDateStr);
                    // 获取当前时间
                    const currentDate = new Date();
                    // 计算时间差（毫秒）
                    const timeDifference = currentDate - inputDate;
                    // 30 天的毫秒数
                    const thirtyDaysInMs = num * 24 * 60 * 60 * 1000;
                    // 判断时间差是否超过 30 天
                    if(timeDifference <= thirtyDaysInMs){
                        this.addFeedBackFlag = true;
                    }
                })
            },
            goHistory(item,titleName){
                this.$router.push({
                    path: '/warnFollowListToIop',
                    query: {
                        mobile: this.mobile,
                        workId:this.workId,
                        subId:this.subId,
                        queryType:item,//1：跟进情况（限制本subId），2：历史轨迹（剔除本subId）
                        titleName:titleName
                    },
                })
            },

            openCamThree(){
                this.eviKeyFlag = true;
            },
            getEvidencePrev3(){
                this.eviKeyFlag = false;
            },
            getEvidenceClose3(VideoName,photoStrList,audioName){
                this.eviKeyFlag = false;
                let evidenceVideoList = VideoName ? VideoName : ''   // ---视频
                let evidencePhotoList = photoStrList ? photoStrList : ''   //--照片
                let evidenceAudioList = audioName ? audioName : ''   //--录音
                if(evidenceVideoList != '' || evidencePhotoList != '' || evidenceAudioList != ''){
                    this.isCameraThree = true;
                }else {
                    this.isCameraThree = false;
                }
                console.info("证据链视频",evidenceVideoList);
                console.info("证据链图片",evidencePhotoList);
                console.info("证据链录音",evidenceAudioList);
                let quzhengkey = '';
                if(evidenceVideoList != ''){
                    quzhengkey = evidenceVideoList;
                    if(evidencePhotoList != ''){
                        quzhengkey = evidenceVideoList + ','+evidencePhotoList;
                        if(evidenceAudioList != ''){
                            quzhengkey = quzhengkey +','+evidenceAudioList;
                        }
                    }else {
                        if(evidenceAudioList != ''){
                            quzhengkey = quzhengkey +','+evidenceAudioList;
                        }
                    }
                }else {
                    if(evidencePhotoList != ''){
                        quzhengkey = evidencePhotoList;
                        if(evidenceAudioList != ''){
                            quzhengkey = quzhengkey + ','+ evidenceAudioList;
                        }
                    }else{
                        if(evidenceAudioList != ''){
                            quzhengkey = evidenceAudioList;
                        }
                    }
                }
                this.zjlFileKeys = quzhengkey;
            },
            getVideoMainId3(videoMainId){
                this.evlKeyMainId = videoMainId;
                console.info("已完结 取证类型 evlKeyMainId",this.evlKeyMainId)
                console.info("已完结 取证类型 zjlFileKeys",this.zjlFileKeys)
            },

            //提交处理
            submit(){
                //防止重复点击
                if (this.isClick) {
                    return;
                }
                this.submitCmop();
                //获取用户当前位置
                // ClientJs.getLocation('', 'getLocationWithWarnHis');

            },
            submitCmop(locationParam){
                //追加反馈
                let params = {
                    workId:this.workId,
                    subId:this.subId,
                    warnType:this.warnType,
                    submitSource:'1',
                    mobile:this.mobile,
                    dealAction:'3',
                    // location:locationParam.address,
                    // longitude:locationParam.longitude,
                    // latitude:locationParam.latitude,
                    dealName:Storage.session.get('userInfo').operatorName,
                    dealMobile:Storage.session.get('userInfo').servNumber,
                    dealOperator:Storage.session.get('userInfo').staffId,
                    dealDate:dateFormat(new Date(), "yyyyMMdd hh:mm:ss"),
                    remark:this.backInfo,
                    zjlFileKeys:this.zjlFileKeys, //取证上传

                }
                console.info("追加反馈提交报文",params);

                this.$http.post('/xsb/gridCenter/warningOrder/h5YjDealToIop',params).then(res => {
                    let{retCode,retMsg} = res.data;
                    console.info("h5YjDealToIop",res.data)
                    if (retCode == '0'){
                        this.$toast("提交成功");
                        this.backInfo = '';
                        //页面刷新
                        this.initData();
                    }else {
                        this.$alert(retMsg || '经分-预警处理提交接口');
                    }
                })
            },
            //工单调配提交
            submitTiaoPei(){
                if(this.isTpClick){
                    return;
                }
                if(this.zpReceiveMobile === ''){
                    this.$alert("请选择调配接收人")
                    return
                }
                this.isTpClick = true;
                let params = {
                    workId:this.workId,
                    subId:this.subId,
                    warnType:this.warnType,
                    submitSource:'1',  //阿拉盯固定渠道
                    mobile:this.mobile,
                    dealAction:'8',  //调派
                    dealName:Storage.session.get('userInfo').operatorName,
                    dealMobile:Storage.session.get('userInfo').servNumber,
                    dealOperator:Storage.session.get('userInfo').staffId,
                    dealDate:dateFormat(new Date(), "yyyyMMdd hh:mm:ss"),
                    zpReceiveName:this.zpReceiveName,      //调派人员姓名，手机号
                    zpReceiveMobile:this.zpReceiveMobile, //调派人员
                    oldReceiveMobile:this.$route.query.receiverMobile, //原阿拉盯处理人手机号
                    remark:this.gdtpRemark,
                }
                console.info("工单调配提交 提交报文",params);
                this.subMethod(params);
            },
            subMethod(params){
                this.$http.post('/xsb/gridCenter/warningOrder/h5YjDealToIop',params).then(res => {
                    let{retCode,retMsg} = res.data;
                    console.info("h5YjDealToIop",res.data)
                    if (retCode == '0'){
                        //常用意见提交
                        this.commonReSub();
                        this.$toast("提交成功");
                        //页面刷新
                        this.goPrev();
                    }else {
                        this.$alert(retMsg || '经分-预警处理提交接口');
                    }
                })
            },
            //处理意见提交
            submitChuLi(){
                if(this.isClClick){
                    return;
                }
                this.isClClick = true;
                let params = {
                    workId:this.workId,
                    subId:this.subId,
                    warnType:this.warnType,
                    submitSource:'1',
                    mobile:this.mobile,
                    dealAction:'9',  //添加意见
                    dealName:Storage.session.get('userInfo').operatorName,
                    dealMobile:Storage.session.get('userInfo').servNumber,
                    dealOperator:Storage.session.get('userInfo').staffId,
                    dealDate:dateFormat(new Date(), "yyyyMMdd hh:mm:ss"),
                    remark:this.clRemark,  //意见内容
                }
                console.info("处理意见提交 提交报文",params);
                this.subMethod(params);
            },
            isShows(item){
                this.isShow = item;
            },
            transfer(){
                this.transferFlag = true;
            },
            closeExecute(){
                this.transferFlag = false;
            },
            getExecute(item){
                console.info("工单调配",item);
                this.zpReceiveName = item.execName;
                this.zpReceiveMobile = item.execMsisdn;
                this.transferFlag = false;
            },
            getOrgaInfo(){
                this.userInfo = Storage.session.get('userInfo')
                for (let i = 0; i < this.LONG_CITY_LIST.length; i++) {
                    if (this.userInfo.region == this.LONG_CITY_LIST[i].shortId) {
                        this.cityId = this.LONG_CITY_LIST[i].id;
                        // this.orgaNames = this.LONG_CITY_LIST[i].label;
                    }
                }
            },

            clickGdtpFlag(){
                this.gdtpRemarkFlag = !this.gdtpRemarkFlag;
            },
            commomGdtpChoose(){
                let _this = this;
                NlDropdown({
                        confirmBtn: false,
                        datalist: _this.gdtpList
                    },
                    function (retVal) {
                        //获取返回回调
                        _this.gdtpCommon = retVal.id;
                        _this.gdtpRemark = _this.gdtpCommon;
                    })
            },
            clickClFlag(){
                this.clRemarkFlag = !this.clRemarkFlag;
            },
            clComRemChoose(){
                let _this = this;
                NlDropdown({
                        confirmBtn: false,
                        datalist: _this.tjyjList
                    },
                    function (retVal) {
                        //获取返回回调
                        _this.clComRem = retVal.id;
                        _this.clRemark = _this.clComRem;
                    })
            },
            //查用意见提交
            commonReSub(){
                let param = {}
                //工单调配
                if(this.isShow === '1'){
                    if(this.gdtpRemarkFlag && this.gdtpRemark){
                        param = {
                            servNumber:Storage.session.get('userInfo').servNumber,
                            operName:Storage.session.get('userInfo').operatorName,
                            opintionDesc:this.gdtpRemark,
                            operType:'工单调配'
                        }
                    }else {
                        return
                    }
                }
                //添加意见
                if(this.isShow === '2'){
                    if(this.clRemarkFlag && this.clRemark){
                        param = {
                            servNumber:Storage.session.get('userInfo').servNumber,
                            operName:Storage.session.get('userInfo').operatorName,
                            opintionDesc:this.clRemark,
                            operType:'添加意见'
                        }
                    }else {
                        return
                    }
                }
                this.$http.post('/xsb/gridCenter/warningOrder/h5InsertWarnOpintions',param).then(res => {
                    let{retCode,retMsg} = res.data;
                    if (retCode == '0'){
                        this.$toast('提交成功');
                    }else {
                        this.$toast(retMsg || '常用转派意见录入失败');
                    }
                })
            },
            commonList(item){
                let param = {
                    servNumber:Storage.session.get('userInfo').servNumber,
                    operType:item
                }
                this.$http.post('/xsb/gridCenter/warningOrder/h5QryOpintionList',param).then(res => {
                    let{retCode,retMsg,data} = res.data;
                    if (retCode == '0'){
                        if(item == '工单调配'){
                            if(data && data.length > 0){
                                this.gdtpList = [{
                                    id:'',
                                    label:'请选择常用调配意见',
                                }]
                                data.forEach(item=>{
                                    let obj = {
                                        id:item.opintionDesc,
                                        label:item.opintionDesc,
                                    }
                                    this.gdtpList.push(obj);
                                })
                            }
                        }
                        if(item == '添加意见'){
                            if(data && data.length > 0){
                                this.tjyjList = [{
                                    id:'',
                                    label:'请选择常用处理意见',
                                }]
                                data.forEach(item=>{
                                    let obj = {
                                        id:item.opintionDesc,
                                        label:item.opintionDesc,
                                    }
                                    this.tjyjList.push(obj);
                                })
                            }
                        }
                    }else {
                        this.$toast(retMsg || '查询意见列表失败');
                    }
                })
            },


            crmQryEmployeeRole(){
                this.$http.post('/xsb/personBusiness/activeRecommend/h5qyrEmployeeRole').then((res) => {
                    let { retCode, data, retMsg } = res.data
                    // data 0-无权限或角色 1-有权限或角色
                    if (retCode === '0') {
                        this.showDownFlg = true;
                        this.retentionAuthority = data;
                    }
                })
            },
            goDownInfo(){
                let url = '/xsb/personBusiness/customerView/h5QryCgetusercust'
                this.$http.post(url, { telnum: this.mobile }).then(res => {
                    let resData = res.data
                    if (typeof (resData) != 'object') {//不是对象的话就是加密串
                        resData = decrptParam(res.data)
                        resData = JSON.parse(resData)
                    }
                    let { retCode, data } = resData
                    if (retCode == '0') {
                        let realstatecode = data.realstatecode
                        //港澳台胞证的
                        let certType2List = ['HKMCPassport', 'DriverIC', 'Passport', 'StudentID', 'TaiBaoZheng', 'UnionSocietyCredit']
                        let flag2 = certType2List.indexOf(data.certType)//判断证件类型
                        //需要实名制审核通过
                        if ((realstatecode === '4') || (realstatecode === '1' && flag2 > -1)) {
                            let obj = {
                                jqType: '0',   //0:服务密码，1:验证码，2:身份证
                                result: '0',             //鉴权结果，1为成功
                                authtype: '',
                                userName: data.userName,
                                telnum: this.mobile,
                                idCardWay: true,
                                userCity: data.userCity,//用户地市
                                userId: data.user_id //用户标识
                            }
                            Storage.session.set('jqData', obj);
                            this.$router.push({
                                path: '/downshiftRetention',
                                query: {
                                    retentionAuthority: this.retentionAuthority
                                }
                            })
                        }else {
                            this.$messagebox.alert('该号码状态是未审核，请使用实名制后再进行业务受理', '温馨提示').then((res) => {

                            })
                        }
                    } else {
                        this.$messagebox.alert('暂无此号码', '温馨提示').then((res) => {

                        })
                    }
                })
            },
            //放大照片
            zoomStorePhoto(url) {
                this.$refs.bigImg.src = url
                this.bigImgFlg = true
            },
        },
        mounted(){
            window['getLocationWithWarnHis'] = (result) => {
                console.info("getLocationWithWarnHis result",result);
                this.submitCmop(result);
            };
        },
        filters: {
            //电话号码中间几位模糊化
            starTel(val) {
                if (!val) {
                    return '***'
                } else {
                    let reg = /^(\d{3})\d*(\d{4})$/
                    return val.replace(reg, '$1****$2')
                }
            },
            timeFiler(val){
                if(val){
                    return dateFormat(chgStrToDate(val), "yyyy-MM-dd hh:mm:ss");
                }else {
                    return '';
                }
            },
            yjFailReasonFilter(val){
                if(val == '0'){
                    return '呼叫3次，未联系上用户';
                }else if(val == '1'){
                    return '中途挂机';
                }else if(val == '2'){
                    return '已与客户沟通，无异动意愿';
                }else if(val == '3'){
                    return '与用户沟通，活动限制无法满足诉求';
                }else if(val == '4'){
                    return '与用户沟通，明确拒绝表示已经办理DX、LT等号卡、宽带';
                }else if(val == '5'){
                    return '存在异动情况，根据实际需求办理8/18套餐';
                }else if(val == '6'){
                    return '不存在异动情况，根据用户实际需求办理8/18套餐';
                }else {
                    return '';
                }
            },
            submitSourceFilter(val){
                if(val == '1'){
                    return '阿拉盯';
                }else if(val == '2'){
                    return '网格通PC';
                }else if(val == '3'){
                    return 'IOP';
                }else if(val == '4'){
                    return '在线';
                }else {
                    return '阿拉盯';
                }
            },
        }
    }
</script>

<style lang="less" scoped>
    @import "../../../base/less/variable.less";
    @import "../../../base/less/mixin.less";

    .pwd-wrapper {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        overflow: hidden;
    }

    .main-info {
        margin-top: 44px;
        border-top: 1px solid #E1E1E1;
        height: calc(100vh - 60px);
        overflow: auto;
        padding-bottom: 60px;
        box-sizing: border-box;

        .task-details{
            margin:10px;
            background-color: #fff;
            line-height: 23px;
            height: auto;
            box-shadow: 0 0 13px 0 rgba(200, 200, 200, 0.3946);

            .task-user {
                padding: 10px;
                background: linear-gradient(90deg, #FFFFFF 0%, #FFDBDB 100%);
                display: flex;
                align-items: center;
                font-size: 14px;
                justify-content: space-between;
                position: relative;

                .task-left {
                    display: flex;
                    align-items: center;

                    .task-img{
                        width: 8vh;
                    }

                    .top-right{
                        display: inline-block;
                        color: red;
                        padding: 0 12px 0 6px;
                        position: absolute;
                        right: 0;
                        top: 2px;
                        border-radius: 2px 4px 0 15px;
                        text-align: center;
                        width: 130px;
                        font-size: 14px;
                        font-weight: 600;

                        .timedeal{
                            font-size: 12px;
                        }
                    }

                    .task-center {
                        margin-left: 10px;

                        .user-info {
                            display: flex;
                            height: 30px;
                            align-items: center;

                            .user-num {
                                font-size: 16px;
                            }
                            .jiantou2icon{
                                font-size: 16px;
                                color: #117efb;
                            }
                        }
                    }

                    .task-number{
                        color: #1f1f1f;

                        font-size: 12px;
                        font-weight: 400;
                        word-break: break-all;

                        .label-info-type{
                            padding: 0 5px;
                            background: #fff;
                            border: 1px solid #fff;
                            border-radius: 2px;
                            margin-right: 5px;
                            display: inline-block;
                            margin-bottom: 5px;
                        }

                        .label-sign{
                            display: inline-block;
                            padding-right: 10px;
                        }
                    }
                }

                .iconwaihu{
                    font-size: 16px;
                    border: 1px solid #ffffff;
                    padding: 7px;
                    background: #ffffff;
                    color: #757575;
                    border-radius: 16px;
                    position: relative;
                    .choose-box{

                        margin: 54px 10px 0 10px;
                        background-color: #fff;
                        border-radius: 6px;
                        width: 100%;
                        position: absolute;
                        z-index: 1;
                        left: 10px;



                        .plan-model{
                            font-size: 14px;
                            position: absolute;
                            right: 11px;
                            top: -33px;
                            width: 130px;
                            background: #fff;
                            border-top-left-radius: 10px;
                            border-top-right-radius: 10px;
                            border-bottom-right-radius: 10px;
                            border-bottom-left-radius: 10px;
                            z-index: 2;
                            padding: 6px 0;
                            box-sizing: border-box;
                            text-align: center;
                            line-height: 32px;
                            box-shadow: 2px 2px 6px 0px rgba(0, 0, 0, 0.2);

                            .butto-waihu{
                                border-radius: 5px;
                                letter-spacing: 2px;
                                color: rgba(255, 255, 255, 1);
                                border: 1px solid rgba(221, 221, 221, 1);
                                background: rgba(22, 129, 251, 1);
                                margin: 5px 15px;

                                &.txt-grey{
                                    background:#ccc
                                }
                            }
                        }
                    }

                    .choose-box::after {
                        content: '';
                        position: absolute;
                        width: 0;
                        height: 0;
                        border-style: solid;
                        border-width:0 6px 12px 6px;
                        border-color: transparent transparent  #fff transparent;
                        top: -45px;
                        right: 30px;
                        border-left: 6px solid transparent;
                        border-right: 6px solid transparent;
                        filter: drop-shadow(5px 5px 10px rgba(0, 0, 0, 0.5));
                    }
                }
            }

            .yw-details{
                padding: 0 10px;

                .yw-list:last-child {
                    border-bottom:none;
                }

                .yw-list{
                    border-bottom: 1px solid #e5e5e5;
                    padding: 10px 0;

                    .first-info{
                        position: relative;
                        display: flex;
                        justify-content: space-between;

                        .first-left{

                            .first-before{
                                content: '';
                                width: 5px;
                                height: 5px;
                                border-radius: 6px;
                                position: absolute;
                                top: 8px;
                                background: #e32f2b;
                                border: 2px solid #e32f2b;
                            }

                            .first-type{
                                font-size: 14px;
                                color: #CA0D0D;
                                line-height: 21px;
                                margin-left: 15px;

                                .first-time{
                                    font-size: 10px;
                                    color: #1681fb;
                                }
                            }

                        }
                        .first-right{
                            font-size: 12px;
                            color: #ffffff;
                            border: 1px solid #faefeb;
                            border-radius: 3px;

                            &.red {
                                background: #E5531A;
                            }

                            &.orange{
                                background:#FC7F04;
                            }

                            &.blue{
                                background: #007aff;
                            }

                            &.green{
                                background: #35D4A6;
                            }
                        }
                    }

                    .info-deal{
                        display: flex;
                        justify-content: space-between;
                        white-space: nowrap;
                    }

                    .move-label-info{
                        font-size: 12px;
                        font-weight: 400;
                        word-break: break-all;

                        .yw-name{
                            color: #2F2F2F;
                        }
                        .yw-label{
                            color: #838383;
                            display: inline-flex;
                            width: 65%;
                            word-break: break-all;
                        }
                    }
                }


            }
        }

        .move-title{
            display: flex;
            justify-content: space-between;
            line-height: 16px;
            margin: 10px;

            .move-title-info{
                font-size: 14px;
                font-weight: 600;
                color: #000;
                display: flex;

                &:before{
                    display:inline-block;
                    content:'';
                    height:16px;
                    width: 3px;
                    background: #007aff;
                    border-radius: 1px;
                    margin-right: 5px;
                }
            }

            .move-history{
                font-size: 12px;
                font-weight: 600;
                color: #117efb;

                .jiantou2icon{
                    font-size: 12px;
                }
            }


        }

        .his-info{
            margin:10px;
            background-color: #fff;
            line-height: 23px;
            height: auto;
            box-shadow: 0 0 13px 0 rgba(200, 200, 200, 0.3946);


            .his-list{
                padding: 10px 10px;
                position: relative;

                .his-xiang{
                    font-size: 12px;
                    font-weight: 400;

                    .icon-checkbox {
                        font-size: 12px;
                        color: #31b84e;
                    }
                    .checkbox2{
                        color: #31b84e;
                    }
                    .his-time{
                        color:#838383 ;
                    }


                    .his-label-info{
                        font-size: 12px;
                        font-weight: 400;
                        word-break: break-all;

                        .his-name{
                            color: #2F2F2F;
                        }

                        .video-ul{
                            width: 100%;
                            box-sizing: border-box;
                            margin: 10px;
                            padding-right: 20px;

                            &:after {
                                .clearfloat();
                            }

                            .video-li {
                                float: left;
                                width: 100%;
                                /*height: 5.25rem;*/
                                overflow: hidden;
                                box-sizing: border-box;
                                margin: 0.2rem 0;
                                text-align: center;

                                .img-wrap {
                                    /*width: 100%;*/
                                    /*height: 10.25rem;*/
                                    /*display: inline-block;*/
                                    /*position: relative;*/

                                    video {
                                        width: 100%;
                                        height: 100%;
                                        border-radius: 4px;
                                    }
                                }
                            }
                        }

                        .photo-ul {
                            width: 100%;
                            box-sizing: border-box;
                            padding-right: 20px;
                            /* display:flex;
                            flex-wrap:wrap;
                            justify-content:space-between; */

                            &:after {
                                .clearfloat();
                            }

                            .photo-li {
                                float: left;
                                width: 33.333%;
                                height: 5.25rem;
                                overflow: hidden;
                                box-sizing: border-box;
                                margin: 0.2rem 0;
                                text-align: center;

                                .check-wrap {
                                    position: absolute;
                                    right: 8px;
                                    top: 8px;
                                    width: 1rem;
                                    height: 1rem;
                                    border-radius: 50%;
                                    color: #FFF;

                                }

                                .fenzu1 {
                                    color: #1681FB;
                                    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.74);
                                }

                                .juxing {
                                    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.74);
                                }

                                .img-wrap {
                                    width: 5.25rem;
                                    height: 5.25rem;
                                    display: inline-block;
                                    position: relative;

                                    img {
                                        width: 100%;
                                        height: 100%;
                                        border-radius: 4px;
                                    }
                                }

                                &.empty {
                                    color: #444;
                                    box-shadow: none;
                                    text-align: center;
                                    line-height: 6.25rem;
                                    font-size: 48px;

                                    .add-span {
                                        background: rgba(238, 238, 238, 1);
                                        width: 6.25rem;
                                        height: 6.25rem;
                                        border-radius: 4px;
                                        display: inline-block;
                                        text-align: center;
                                    }
                                }

                            }

                        }

                        .his-label{
                            color: #838383;
                            display: inline-flex;
                            max-width: 65%;
                            word-break: break-all;
                        }
                    }

                }
            }
        }

        .order-handle {
            margin: 10px;
            margin-bottom: 60px;
            background: #fff;
            box-shadow: 0 0 13px 0 rgba(200, 200, 200, 0.3946);

            .order-tar {
                display: flex;
                border-bottom: 1px solid #C8C8C8;

                & > div {
                    flex: 1;
                    height: 40px;
                    text-align: center;
                    line-height: 40px;
                    background-color: #F8F8F8;
                    font-size: 14px;
                    font-weight: 600;
                    color: #6c6c6c;
                    box-sizing: border-box;

                    & > span{
                        font-size: 14px;
                        margin-right: 2px;
                    }
                }

                .choosed {
                    color: #1681FB;
                    border-bottom: 2px solid #1681FB;
                    background-color: #fff;
                }
            }

            .handle-details{
                font-size: 14px;
                border-bottom: 1px solid #E8E8E8;

                .handle-xiang{
                    height: 40px;
                    line-height: 40px;
                    margin: 0 16px 0 16px;
                    display: flex;
                    justify-content: space-between;


                    .handle-label{
                        color: #616161;
                        font-size: 12px;
                        display: flex;
                        align-items: center;

                        .wihte-icon{
                            color:#ffffff;
                            margin-right: 10px;
                        }

                        &>span{
                            color: #EF2F12;
                        }
                    }
                    .handle-value {
                        color: #bebcbc;
                        font-size: 13px;
                        height: 30px;
                        text-align: right;
                        width: 65%;

                        .jiantou-copy-copy {
                            font-size: 12px;
                            color: #007AFF;
                        }

                        .iconphtot11{
                            font-size: 12px;
                            color: #1681fb;
                        }

                        .zi-ti{
                            font-size: 12px;
                            color: #333;
                        }
                    }

                    .xialakuang-111{
                        color: #bebcbc;
                        font-size: 13px;
                        height: 30px;
                        text-align: right;
                        width: 70%;
                        white-space: nowrap;
                        overflow-x: auto;
                        overflow-y: hidden;

                        .zi-ti{
                            font-size: 12px;
                            color: #333;
                        }


                        .iconphtot11{
                            font-size: 12px;
                            color: #1681fb;
                        }
                    }
                }

                .remark{
                    position: relative;
                    margin:10px;

                    .text-abl{
                        position: absolute;
                        right: 10px;
                        bottom: 10px;
                        font-size: 12px;
                        color: #929292;
                        z-index: 9;
                    }

                    .button {
                        float: right;
                        font-size: 12px;
                        color: #3e82f7;
                        margin-top: 10px;
                    }

                    textarea {
                        width: 100%;
                        border: 1px solid #999;
                        border-radius: 6px;
                        box-sizing: border-box;
                        height: 100px;
                        padding: 10px 10px;
                        font-size: 12px;
                    }
                }

                .common-flag{
                    color: #7f7f7f;
                    font-size: 12px;
                    text-align: right;
                    margin: 10px;

                    i{
                        font-size: 13px;
                        color: #979797;

                        &.check-box {
                            color: #5375FF
                        }
                    }
                }
            }

            .end-bottom {
                width: 100%;
                height: 60px;
                background: rgba(255, 255, 255, 1);
                display: flex;
                justify-content: space-around;
                align-items: center;

                button {
                    display: block;
                    height: 40px;
                    border-radius: 25px;
                    font-size: 16px;
                    letter-spacing: 4px;
                    color: rgba(255, 255, 255, 1);
                    width: 90%;
                    border: 1px solid rgba(221, 221, 221, 1);;
                }

                .end-submit {
                    background: rgba(22, 129, 251, 1);

                    &.active{
                        background:#ccc
                    }
                }
            }
        }

        .chudi{
            margin-bottom: 0;
        }
    }

    .big-img-wrap {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        background: #fff;
        z-index: 99;
        display: flex;
        align-items: center;

        .close-btn {
            position: absolute;
            right: 0.5rem;
            top: 0.5rem;
            font-size: 28px;
            padding: 0.5rem;
        }

        img {
            width: 100%;
        }
    }

    .camera-wrap {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 1000;
        background: #ECF0FA;
        overflow: auto;
    }


</style>
