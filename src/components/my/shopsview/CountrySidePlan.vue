<template>
    <div class="vp">
        <!--标题-->
        <Header :tsTitleTxt="headObj.title" backType="custom" @emGoPrev="goPrev"></Header>
        <!--计划制定主体-->
        <div class="plan-main" v-show="selectPopFlag">

            <!--乡村标题-->
            <div class="plan-objname">
                <span class="iconfont xingzhuang20 title-icon"></span>
                <span class="title-objname">{{objectName}}</span>
            </div>

            <!--计划开始时间-->
            <div class="p-s-lis flex">
                <div class="p-sl-txt p-sl-tstime">开始时间</div>
                <div @click="selectStartTime()">
                    <span class="time-detail">{{rangeStartValue}}</span>
                    <span class="iconfont jiantou-copy-copy"></span>
                </div>
            </div>

            <!--计划结束时间-->
            <div class="p-s-lis flex">
                <div class="p-sl-txt p-sl-tstime">结束时间</div>
                <div @click="selectEndTime()" >
                    <span class="time-detail">{{rangeEndValue}}</span>
                    <span class="iconfont jiantou-copy-copy"></span>
                </div>
            </div>

            <!--执行人员选择-->
            <div class="authorize"  >
                <div class="authorize-txt" @click="authorize">
                    执行人员选择
                    <span class="benren" v-show="benren"> 本人</span>
                    <span class="iconfont jiantou-copy-copy icon-authorize"></span>
                </div>
            </div>

            <!--输入框-->
            <div class="pop-textarea-border">
                <textarea v-model="textarea" placeholder="备注： 融炉、融金... " class="pop-textarea" maxlength="120"></textarea>
                <span class="pop-textarea-num"><span :style="textCount">{{textarea.length}}</span><span>/120</span></span>
            </div>

            <!--底部按钮-->
            <div class="pop-bottom">
                <div class="pop-blis" @click="goPrev">关   闭</div>
                <div class="pop-blis-right" @click="submitPlan">确   定</div>
            </div>
        </div>

        <!--授权组件-->
        <div class = "authorize-tanhuang" v-show="authorizeTanchuang">
            <PlanExecutive :channelId="channelId" :objectId ="groupIdArr[0]" @closeMain = "closeMain" @selectAuthorization = "selectAuthorization"></PlanExecutive>
        </div>
    </div>
</template>

<script>
    import Header from '../../common/Header.vue'
    import WeekPlan from "../../common/WeekPlan";
    import Pop from "../../common/Pop";
    import NlDatePicker from "components/common/NlDatePick/datePicker.js";
    import {dateFormat,chgStrToDate,getRealUrl} from '@/base/utils'
    import PlanExecutive from 'components/my/customScene/PlanExecutive.vue'
    import Storage from '@/base/storage'

    export default {
        data(){
            return{
                headObj: {
                    title: '我的计划',
                    btnTxt: '提交',
                },
                objectName:"",//对象名称

                rangeStartValue:dateFormat(new Date(),"yyyy-MM-dd hh:mm"),                                       //开始时间值
                rangeEndValue:dateFormat(new Date(),"yyyy-MM-dd hh:mm"),
                benren: false,                                    //本人显示开关
                authorizeTanchuang:false,                            //授权弹窗开关
                groupIdArr: [],                       //集团编码
                channelId:"",                         //渠道编码
                selectData:[],                                   //已选择的授权人列表
                textarea:'',                                    //文本域内容
                selectPopFlag:true,                                //请选择框是否显示

                currDate: '',                            //当天日期
                orgaId: '', //网格编码
                gridId:"",                                   //网格编码
                gridName:"",                                 //网格名称
                planDate: '',                         //计划时间
            }
        },
        created() {
            this.objectName = this.$route.query.objectName;
            this.groupIdArr.push(this.$route.query.objectId);
            this.channelId = this.$route.query.channelId;
            this.gridId = this.$route.query.gridId;
            this.gridName = this.$route.query.gridName;
            //获取用户信息
            this.getUserInfo();
            //获取当天日期
            this.currDate = this.getTodayDate();

            //获取网格信息
            this.initData();
        },
        computed:{
            textCount(){
                if (this.textarea.length>120){
                    return {"color":"#FF2F2F"};
                }else{
                    return {"color":"#7d7d7d"};
                }
            }
        },
        methods:{
            //获取useInfo
            getUserInfo(){
                this.selectData = []; //初始化
                this.userInfo = Storage.session.get('userInfo');//JSON.parse(sessionStorage.getItem('userInfo'));
                this.selectData.push({'operatorId':this.userInfo.crmId,'operatorName':this.userInfo.operatorName});
                if (this.selectData.length == 1){
                    if(this.selectData.operatorId = this.userInfo.crmId){
                        this.benren = true;
                    }
                }else {this.benren = false;}
            },

            //获取今天日期如：20190313
            getTodayDate() {
                let d = new Date();
                let y = d.getFullYear();
                let m = (d.getMonth() + 1) < 10 ? '0' + (d.getMonth() + 1) : String(d.getMonth() + 1);
                let days = d.getDate() < 10 ? '0' + d.getDate() : String(d.getDate());
                return y + m + days;
            },

            //当前网格得分排名查询 获取网格信息
            initData() {
                let url = `/xsb/personBusiness/gridViewSecond/h5QryScoreAndRank?telnum=${this.userInfo.servNumber}`;
                this.$http.get(url).then((res) => {
                    if (res.data.retCode == "0") {
                        let data = res.data.data;
                        this.orgaId = data.orgaId;
                    } else {
                        this.$alert(res.data.retMsg || "查询当前网格基本信息接口调用出错");
                    }
                });
            },







            goPrev(){
                //重置参数
                this.rangeStartValue = dateFormat(new Date(),"yyyy-MM-dd hh:mm") ;
                this.rangeEndValue = dateFormat(new Date(),"yyyy-MM-dd hh:mm") ;
                this.textarea = '';
                this.selectData=[];
                this.selectData.push({'operatorId':this.userInfo.crmId,'operatorName':this.userInfo.operatorName});

                history.go(-1);
            },
            //改变开始时间
            selectStartTime(){
                NlDatePicker({
                    startDate: this.rangeStartValue == '' ? dateFormat(new Date(),"yyyy-MM-dd hh:mm") : this.rangeStartValue,
                    tsMinDate:new Date(),
                    dateType:'datetime',
                    format:'yyyy-MM-dd hh:mm',
                    onlyOne:true
                },(retVal) =>{
                    this.rangeStartValue = retVal.startDate;
                });
            },
            //改变结束时间
            selectEndTime(){
                NlDatePicker({
                    startDate: this.rangeEndValue == '' ? dateFormat(new Date(),"yyyy-MM-dd hh:mm") : this.rangeEndValue,
                    tsMinDate:new Date(),
                    dateType:'datetime',
                    format:'yyyy-MM-dd hh:mm',
                    onlyOne:true
                },(retVal) =>{
                    this.rangeEndValue = retVal.startDate;
                });
            },
            //授权打开
            authorize(){
                this.authorizeTanchuang = true;
                this.selectPopFlag = false;
            },
            closeMain(flag){
                this.authorizeTanchuang = flag;
                if(!this.authorizeTanchuang){
                    this.selectPopFlag = true;
                }
            },
            //执行人数据传输
            selectAuthorization(data){
                this.selectData = []; //初始化
                this.selectData = data;
                if (this.selectData.length == 1){
                    if(this.selectData[0].operatorId == this.userInfo.crmId){
                        this.benren = true;
                    }
                }else {this.benren = false;}

            },

            //提交
            submitPlan(){
                let startTime = '';
                let endTime = '';
                let _this = this;
                if (!(_this.rangeEndValue > _this.rangeStartValue)) {
                    this.$alert("结束时间必须大于开始时间")
                    return;
                }
                startTime = _this.rangeStartValue.replace(RegExp("-", "g"),'')+':00';
                endTime = _this.rangeEndValue.replace(RegExp("-", "g"),'')+':00'

                //当前时间赋值
                if (_this.planDate == '') {
                    _this.planDate = _this.getTodayDate();
                }

                var userId = "";
                let users = [];
                for (var i = 0; i < _this.selectData.length; i++) {
                    userId += _this.selectData[i].operatorId + ',';
                    users.push({
                        "userId": _this.selectData[i].operatorId,
                        "userMobile": ''
                    });
                }
                if (userId.length > 0) {
                    userId = userId.substr(0, userId.length - 1);
                }

                let param = {
                    planDate: _this.planDate,
                    objectId: "" + _this.groupIdArr[0],
                    objectName:_this.objectName,
                    startTime: startTime,
                    endTime: endTime,
                    operType: "0",
                    source: "1",
                    remark: _this.textarea,
                    users: JSON.stringify(users),
                    channelId:'97',
                    gridId:_this.gridId,
                    gridName:_this.gridName
                };
                this.$http.post('/xsb/personBusiness/customizate/h5SynchroHotScenePlan', param).then((res) =>{
                    if (res.data.retCode == '0') {
                        _this.$toast('添加成功');
                        this.goPrev();
                    }
                })
            }

        },
        components: {
            Header, WeekPlan, Pop,NlDatePicker,PlanExecutive
        },
    }
</script>

<style scoped lang="less">
    @import '../../../base/less/variable.less';
    .vp {
        display: flex;
        flex-direction: column;
        /*height: 100vh;*/
        position: absolute;top:0;left:0;right:0;bottom:0;
    }
    .plan-main{
        margin-top: 44px;
        height: 100%;
        width: 100%;
        background: #fff;
    }
    .plan-objname{
        margin: 5px 0 15px 20px;

        .title-icon{
            font-size: 19px;
            color: #007aff;
        }
        .title-objname{
            font-size: 16px;
            font-weight: 600;
        }

    }
    .p-s-lis{
        height: 24px;
        overflow: hidden;
        margin-left:20px;
        margin-right: 20px;
        margin-top:10px;
    }
    .p-s-lis.flex{
        display: flex;
        justify-content: space-between
    }

    .p-sl-txt{
        width: auto;
        height: 24px;
        line-height: 24px;
        float:left;
        font-size:16px;
        font-weight:400;
        color:rgba(48,48,48,1);
    }
    .p-sl-tstime{
        flex-shrink: 0;
    }
    .time-detail{
        color:#108EE9;
        line-height: 24px
    }


    .authorize{
        height: 24px;
        overflow: hidden;
        margin-left:20px;
        margin-right: 20px;
        margin-top:10px;
    }
    .authorize-txt{
        width: 100%;
        height: 24px;
        line-height: 24px;
        font-size:16px;
        font-weight:400;
        color:rgba(48,48,48,1);
    }
    .benren{
        float:right;
        color: #108EE9;
        position:relative;
        padding-right: 20px;
    }
    .icon-authorize{
        float: right;
        font-size: 16px;
        position: absolute;
        right: 20px;
        line-height: 24px;
    }
    .pop-textarea-border{
        position:relative;
        border-radius:10px;
        border:1px solid rgba(216,216,216,1);
        width:86%;
        margin:10px 7%;
        box-sizing: border-box;
        padding: 10px 10px 20px;
    }
    .pop-textarea{
        font-size: 14px;
        height:100px;
        overflow: hidden;
        outline: none;
        width: 96%;
        border: 0;
        resize: none;
    }
    .pop-textarea-num {
        position: absolute;
        bottom: 5px;
        right: 10px;
        font-size: 12px;
    }
    .pop-bottom{height: 66px;border-top: 1px solid #D5D5D5;bottom:0;
        position: fixed;
        z-index: 100;
        width: 100%;
        display: flex;}
    .pop-blis{
        margin-top: 10px;
        margin-left: 13px;
        width:45%;
        height:44px;
        box-shadow:3px 3px 14px 0px rgba(215,215,215,0.5);
        border-radius:22px;
        border:1px solid rgba(204,204,204,1);
        line-height: 46px;
        text-align: center;
    }
    .pop-blis-right{
        margin-top: 10px;
        margin-left: 12px;
        width:45%;
        height:44px;
        border-radius:22px;
        line-height: 46px;
        text-align: center;
        color:#fff;
        background:#1681FB;
    }


</style>
