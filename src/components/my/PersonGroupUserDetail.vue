<template>
    <div class="user-dt" @click="pageCkClose">
        <!--<Header :tsTitleTxt="headObj.title"></Header>-->
        <div v-show="!showCallOrder">
            <div class="head wrapper-medias">
                <div class="gl-title">
                    <!--后退按钮-->
                    <span class="iconfont zuojiantou gl-back"  @click="goPrev"></span>
                    <!--标题-->
                    <span class="gl-title-txt">用户明细</span>
                </div>
            </div>
            <div class="ud-main" @scroll="mScroll">
                <div class="ud-tel">
                    <div class="ud-tel-inner">
                        <span class="iconfont xingzhuang2 iconxz2"></span>
                        <span>{{telNum | blurTelNum}}</span>
                    </div>
                </div>
                <!--悬浮-->
                <div class="ud-tel-float" v-show="telFloatFlag" :class="{fla:telFloatFlag}">
                    <div class="ud-tel-inner">
                        <span class="iconfont xingzhuang2 iconxz2"></span>
                        <span>{{telNum | blurTelNum}}</span>
                    </div>
                </div>
              <div v-show="showuserid">
<!--                <div class="ud-menu" v-show="prodName ==''" >{{mainMeal}}</div>-->
                <div class="ud-menu" >{{prodName | filProd(mainMeal)}}</div>
<!--                <div class="ud-menu" v-show="prodName !=''" >{{mainMeal}}({{// prodName}})</div>-->
                <div class="ud-menu-lis">
                    <div class="ud-mlis-crt">
                        <canvas id="myChart1" width="480" height="480" style="height: 160px"></canvas>
                        <span class="pl-mvisit-line"></span>
                        <canvas id="myChart2" width="480" height="480"></canvas>
                    </div>
                </div>
                <div class="wc-bannerblock" style="" v-show="showBanner">
                    <div class="cs-bannernei">
                        <slide ref="slide"
                               :autoPlay="true"
                               :loop="true"
                               :showDot="true"
                               :dataList="bannerData"
                               :interval="4000">
                            <div v-for="(item,index) in bannerData"
                                 @click="bannerCk(item)"
                                 :key="index" style="position: relative;">
                                <a href="javascript:;" style="border-radius: 8px;">
                                    <img :src="item.src"/>
                                </a>
                                <span class="banner-cs-title">{{item.activeName}}</span>
                            </div>
                        </slide>
                    </div>
                </div>
                <div class="ud-menu-lis" v-show="activeRespSwitch.activeName">
                    <div class="ud-mlis-title active">反馈信息</div>
                    <div class="ud-mlis-resp">
                        <div class="ud-table">
                            <span class="ud-mlis-txt">活动名称：{{activeResp.activeName}}</span>
                            <span class="ud-mlis-txt">反馈时间：{{activeResp.markTime}}</span>
                            <span class="ud-mlis-txt">反馈意向：<span class="ud-mlis-txt-blue">{{activeRespResult}}({{activeResp.reason}})</span></span>
                        </div>
                    </div>
                </div>

                  <div class="menber-biaoqian" @click="goMember" v-show="channelId == '12'">
                      <div class="menber-detail">
                          客户画像&标签
                          <span class="iconfont youjiantou" style="font-size: 12px"></span>
                      </div>
                  </div>

                <div class="vd-tab" v-show="currActivityInfo.activityId && currActivityInfo.activityId != ''" :class="{fla:tabFloatFlag}">
                    <ul class="vd-tab-ul" v-if="userActivityList&&userActivityList.length>0">
                        <li class="vd-tabli"
                            :class="{active:businessIndex == index}"
                            @click="businessTabCk(item,index)"
                            v-for="(item,index) in userActivityList" :key="index">{{item.businessName}}
                        </li>
                    </ul>
                    <ul class="vd-tab-ul" v-else>
                        <li class="vd-tabli"
                            :class="{active:businessIndex == index}"
                            @click="businessTabCk(item,index)"
                            v-for="(item,index) in userActivityList" :key="index">{{item.businessName}}
                        </li>
                    </ul>
                    <div class="vd-tslidebtn" @click.stop="slideBtnCk" :class="{active:slideFlag}">
                        <span class="iconfont jiantou2 iconjt2"></span>
                    </div>

                    <!--下拉显示-->
                    <div class="vd-tab-slide" v-show="slideFlag">
                        <ul class="vd-tabsul">
                            <li class="vd-tblis" @click="businessTabCk(item,index)"
                                v-for="(item,index) in userActivityList" :key="index">{{item.businessName}}
                            </li>
                        </ul>
                    </div>
                </div>
              </div>

                <div class="ud-active" :class="{martop45:tabFloatFlag}">
    <!--                <div class="ud-at-title">{{currActivityInfo.activityName}}</div>-->
    <!--                <div class="ud-at-detail">{{currActivityInfo.activityDesc}}</div>-->
                    <div class="more-menu-content">
                        <div class="msg" v-show="currActivityInfo.activityDesc">
                            <p>【营销话术】：</p>
                            <p>{{currActivityInfo.activityDesc}}</p>
                        </div>
                        <div v-for="(item,idx) in currActivityInfo.prodList" :key="item.activityId" v-show="currActivityInfo.prodList.length > 0">
                            <p>【推荐产品{{ idx+1 }}】：{{item.prodName}}</p>
                            <p>{{item.prodDesc}}</p>
                        </div>
                    </div>

                    <div class="ud-at-btns">
                        <!--短信-->
                        <!--<div class="btn-msg" @click="sendMsgCk">
                            <span class="iconfont duanxin icondx"></span>
                        </div>-->

                      <!--上门-->
                      <div class="btn-shangmen" v-show="channelId == '76'||channelId == '1'" @click="visitCk" :class="{unable:!visitIconFlag}">
                          <span class="iconfont shangmen iconshangmen"></span>
                      </div>
                        <!--外呼-->
                        <div class="btn-waihu needsclick" @click="doWaiHu(currActivityInfo.activityId,currActivityInfo.activityName)">
                            <span class="iconfont waihu iconwaihu"></span>
                            <div class="waihu-menu-wrap" v-show="showWhMenu" :style="{backgroundImage:'url(static/img/wh_bg.png)'}">
                                <span class="iconfont yanzhengma" @click.stop="validateWh">&nbsp;验证密码</span>
                                <span class="shuxian">|</span>
                                <span class="iconfont xingzhuang5" @click.stop="getWhResult">&nbsp;查看结果</span>
                            </div>
                        </div>

                        <!--业务办理-->
                        <div class="btn-yewubanli" @click="businessManage">
                            <span class="iconfont yewu iconyewubl"></span>
                        </div>
                    </div>
                </div>
                <div class="ud-active martop10">
                    <div class="ud-at-title">办理反馈</div>

                    <div class="vs-intent">
                        <div class="vs-inttent-select">
                            <ul>
                                <li class="vs-is-lis" :class="{active:reasonIndex == 1}" @click="reasonCk(1)">同意</li>
                                <li class="vs-is-lis" :class="{active:reasonIndex == 2}" @click="reasonCk(2)">考虑</li>
                                <li class="vs-is-lis" :class="{active:reasonIndex == 3}" @click="reasonCk(3)">拒绝</li>
                            </ul>
                        </div>
                    </div>

                    <div class="vs-intent" v-show="reasonShowFlag && currActivityInfo.activityId">
                        <div class="vs-rs-lis" @click="showReasonDropdown">
                            <div class="vs-rlistxt">{{reasonListTxt}}</div>
                            <span class="iconfont youjiantou iconyjt"></span>
                        </div>
                    </div>

                    <div class="vs-desc">
                        <div class="ml-area">
                            <textarea placeholder="请输入具体备注信息" maxlength="100" v-model="textareaVal"
                                      @input="textareaChange"></textarea>
                            <div class="ml-txt-count">{{remnant}}/100</div>
                        </div>
                    </div>
                    <div class="vs-btn">
                        <div class="btn-style-blue add-visit-submit"
                             :class="{active:subMitBtnFlag}"
                             @click="activitySubmit">提交
                        </div>
                    </div>

                </div>
            </div>

            <!--banner 弹框提示-->
            <div class="banner-filter" v-show="bannerPopFlag">
                <div class="cs-vfpop">
                    <span class="iconfont guanbi icon-guanbi" @click="closeBannerPop"></span>
                    <div class="cs-vfpop-title">{{bannerPopTitle}}</div>
                    <div class="cs-vfpop-main">{{bannerPopDesc}}</div>
                    <div class="cs-vfpop-bottom" v-show="bannerPopCanDo == '0'">
                        <span class="cs-vb-ok" @click="bannerDo">办理</span>
                    </div>
                </div>
            </div>


            <div class="first-box"  v-show="showGoOrder">
              <div class="first-box-main">
                    <span class="iconfont qiandao icon"></span>
                    <div class="first-box-textarea">
                        <span class="textarea-text">提交成功！</span>
                        <span class="divider"></span>
                        <span class="textarea-text active">是否转OAO号卡配送订单?</span>
                    </div>
                    <div class="double-bottom">
                        <span class="double-bottom-item" @click="goOrder(false)">否</span>
                        <span class="double-bottom-item active" @click="goOrder(true)">是</span>
                    </div>
              </div>
            </div>
            <div class="back-img" v-show="showGoOrder"></div>
         </div>
        <outboundCallOrder v-show="showCallOrder" @goPrev="goPrev"></outboundCallOrder>

    </div>
</template>

<script>

    import Header from '../common/Header.vue'
    import NoDataPage from '../common/NoDataPage.vue'
    import NlDropdown from 'components/common/NlDropdown/dropdown.js'
    import Storage from '@/base/storage'
    import {dateFormat} from '@/base/utils'
    import Slide from 'components/common/Slide.vue'
    import Authenct from 'components/common/Authenticate/Authenticate'
    import {needNumMixin} from '@/base/mixin'
    import outboundCallOrder from 'components/business/outcall/OutboundCallOrder'


    export default {
        mixins: [needNumMixin],
        data() {
            return {
                bannerData:[
                    {href:'javascript:;',src:'./static/business/banner1.png'}
                ],
                bannerSty:['center','',' right','black'],//banner图片中的文字样式 center(标题居中) right（标题居右） black
                headObj: {
                    title: '用户明细'
                },
                billMonth: [],           //历史账单月份
                billVal: [],           //历史账单值
                fluxMonth: [],           //历史流量月份
                fluxVal: [],             //历史流量值
                fluxCrtVal: [],             //历史流量值,图表使用，单位：M
                telNum: '',                  //手机号
                userActivityList: [],            //用户活动列表
                mainMeal: '',                    //主体业务名称
                prodName: '',                    //主体套餐名称
                businessIndex: 0,                //当前业务默认第一个选中
                currActivityInfo: {},            //当前业务信息
                reasonIndex: null,               //原因项tab点击序号
                reasonListIndex: null,               //原因项列表点击序号
                reasonList: [],                  //原因项列表
                textareaVal: '',                 //备注信息
                remnant: 0,                      //计算器
                reasonListTxt: '理由选择',               //当前原因项文字
                subMitBtnFlag: false,            //提交按钮是否能点击
                slideFlag: false,                //活动下拉框
                zkTypeList: [],
                curZkType: {id: '0', label: '请选择'},//当前选择的下拉框
                reasonShowFlag: false,               //选择项是否显示
                visitIconFlag: false,                //上门按钮状态
                telFloatFlag: false,                     //浮动的手机号码元素是否显示
                tabFloatFlag: false,                     //tab浮动元素是否显示
                whSession: '',           //外呼登录的session
                userInfo:{},                    //用户信息
                adddressTxt:'',                     //家庭住址
                showWhMenu:false,//展示验密及查看结果
                callId:'',//通话唯一标识
                isVerifyResult:'0',//是否验密 （0，没有验密初始状态，1：开始验密；2，验密成功，-2：验密失败
                workId :'',
                addressId:'',
                clickActiveList:[],
                activeResp:[], //最后一次活动反馈
                activeRespResult:'',//活动反馈的反馈意向
                activeRespSwitch:true,//活动反馈模块开关
                isCall:1 ,//是否可以外呼 0否
                bannerPopFlag:false,                //banner弹框是否显示
                bannerPopTitle:'',                  //banner弹框标题
                bannerPopDesc:'',                   //banner弹框简介
                bannerPopCanDo:'',                  //banner弹框是否要办理
                bannerItem:{},                      //banner Obj
                showBanner:true,
                showuserid:true,                    //根据userid判断是否显示
                channelId:'',
                submitFlag:false,
                hasSubmitFlag:false,//是否已提交标识
                villageId:'',  //小区ID
                villageName:'',  //小区名称
                submit: 0, //提交次数
                showGoOrder:false,//是否跳转预约单提示
                showCallOrder:false,//是否显示预约单

            }
        },
        filters: {
            blurTelNum(value) {
                //手机号打码
                let reg = /^(\d{3})(\d{4})(\d{4})$/;
                let val = value.replace(reg, '$1****$3');
                return val;
            },
          filProd(prodName, mainMeal) {
            if (prodName == '') {
              if (mainMeal.indexOf("未知") != '-1') {
                return "";
              } else if (mainMeal.indexOf("未知") == '-1') {
                return mainMeal;
              }
            } else if (prodName != '') {
              if (prodName.indexOf("未知") != '-1') {
                if (mainMeal.indexOf("未知") != '-1') {
                  return "";
                } else if (mainMeal.indexOf("未知") == '-1') {
                  return mainMeal;
                }
              } else if (prodName.indexOf("未知") == '-1') {
                if (mainMeal.indexOf("未知") != '-1') {
                  return prodName;
                } else if (mainMeal.indexOf("未知") == '-1') {
                  return mainMeal + "(" + prodName + ")";
                }
              }
            }
          }
        },
        watch: {
            reasonListTxt(newName, oldName) {
                if (newName != '理由选择' && newName != '') {
                    this.subMitBtnFlag = true;
                }
            },
        },
        created: function () {
            if (Storage.session.get("submitFlag")) {
                this.submitFlag = Storage.session.get("submitFlag");
            }
            this.workId = this.$route.query.workId;
            Storage.session.set('workId',this.$route.query.workId);
            this.channelId = this.$route.query.channelId;
            this.getUserTelNum();
            this.getPersonalActivityInfo();
            if (Storage.session.get('clickActiveList') && Storage.session.get('clickActiveList') != '') {
                this.clickActiveList = Storage.session.get("clickActiveList");
            }else {}
            this.getActives();
        },
        mounted() {

        },
        methods: {
            goMember(){
                this.$router.push({
                    path: "/memberLabel",
                    query: {
                        msisdn:this.$route.query.userPhone,
                    }
                })
            },

            //banner点击
            bannerCk(item){
                this.bannerPopTitle = item.activeName;
                this.bannerPopDesc = item.activeDesc;
                this.bannerPopCanDo = item.canDo;
                this.bannerPopFlag = true;
                this.bannerItem = item;
            },

            //获取轮播数据
            getActives(){
                let _this = this;
                let telNum = this.telNum;
                let jqType ="04";
                let url = '/xsb/personBusiness/activeRecommend/h5getActivity?telnum='+telNum+'&authtype='+jqType;
                this.$http.get(url).then((res)=>{

                    if(res.data.retCode == '0'){
                        let dataActiveList = res.data.data;
                        if(dataActiveList.length > 0){
                            for(var i=0;i<dataActiveList.length;i++){
                                if(dataActiveList[i].productType == '2'){ //预警信息
                                    dataActiveList[i].src = './static/img/banner_cs2.jpg'
                                }else if(dataActiveList[i].productType == '3'){ //话务推荐
                                    dataActiveList[i].src = './static/img/banner_cs3.jpg'
                                }else if(dataActiveList[i].productType == '4'){ //信用推荐
                                    dataActiveList[i].src = './static/img/banner_cs4.jpg'
                                }else if(dataActiveList[i].productType == '5'){ //终端推荐
                                    dataActiveList[i].src = './static/img/banner_cs5.jpg'
                                }else if(dataActiveList[i].productType == '6'){ //流量推荐
                                    dataActiveList[i].src = './static/img/banner_cs6.jpg'
                                }else if(dataActiveList[i].productType == '7'){ //宽带推荐
                                    dataActiveList[i].src = './static/img/banner_cs7.jpg'
                                }else if(dataActiveList[i].productType == '8'){ //促销推荐
                                    dataActiveList[i].src = './static/img/banner_cs8.jpg'
                                }else if(dataActiveList[i].productType == '10'){ //产品推荐
                                    dataActiveList[i].src = './static/img/banner_cs10.jpg'
                                }else if(dataActiveList[i].productType == '13'){ //全省统一营销活动
                                    dataActiveList[i].src = './static/img/banner_cs13.jpg'
                                }else if(dataActiveList[i].productType == '14'){ //应用分发
                                    dataActiveList[i].src = './static/img/banner_cs14.jpg'
                                }else if(dataActiveList[i].productType == '15'){ //流量内容推荐
                                    dataActiveList[i].src = './static/img/banner_cs15.jpg'
                                }else if(dataActiveList[i].productType == '16'){ //流量促销包
                                    dataActiveList[i].src = './static/img/banner_cs16.jpg'
                                }else{ //其他
                                    dataActiveList[i].src = './static/img/banner_cs00.jpg'
                                }
                            }
                            _this.bannerData = dataActiveList;
                        }else{
                            _this.bannerData = [{src:'./static/img/banner_cs_default.jpg'}];
                            this.showBanner = false;
                        }
                    }else{
                        _this.$toast('活动推荐查询失败');
                    }
                }).catch((err)=>{
                });
            },
            goPrev(flag) {
                if(flag == 1){
                    this.showGoOrder = false;
                    this.showCallOrder = false;
                    return;
                }
                if (this.callId){
                  if (!this.hasSubmitFlag){
                    this.$toast('请先完成反馈操作再离开当前页面');
                    return;
                  }
                }
                Storage.session.set("submitFlag",'');
                history.go(-1);
            },
            //获取用户手机号
            getUserTelNum() {
                this.telNum = this.$route.query.userPhone;
                this.userInfo = Storage.session.get('userInfo');
            },
            //个人活动明细信息查询 、近3月资费、近3月流量使用
            getPersonalActivityInfo() {
                let _this = this;
                let userPhone = this.$route.query.userPhone;
                let url =`/xsb/personBusiness/my/h5GetUserDetail?userPhone=${userPhone}`;
                this.$http.get(url)
                    .then((res) => {
                        if (res.data.retCode == '0') {
                            let d = res.data.data;
                            _this.villageId = d.villageId;
                            _this.villageName = d.villageName;
                            let userId = d.userId;
                            if(userId==undefined||userId==''){
                                this.showuserid = false;
                            }
                            _this.isCall = d.isCall;
                            _this.addressId = d.addressId;
                            if (d.activityData != null && d.activityData != [] && d.activityData != '') {
                                _this.userActivityList = d.activityData;
                                _this.currActivityInfo = _this.userActivityList[0];
                            }
                            _this.visitIconFlag = d.hasFamilyAddr == '0' ? true : false;
                            _this.adddressTxt = d.familyAddr || '';
                            _this.mainMeal = res.data.data.businessInfo.mainBusiness;
                            _this.prodName = res.data.data.businessInfo.prodName;
                            //历史账单 数据获取
                            let month = new Date().getMonth()+1;
                            let billList = [];
                            billList.unshift(
                                {
                                    'mouth':(month-3)>0 ? month-3 : ((month-3) == -2 ? 10:((month-3) == -1 ? 11:12)),
                                    'val':(d.businessInfo.bill1 == '' ? 0 : d.businessInfo.bill1)
                                },
                                {
                                    'mouth':(month-2)>0 ? month-2 : ((month-2) == -1 ? 11 : 12),
                                    'val':(d.businessInfo.bill2 == '' ? 0 : d.businessInfo.bill2)
                                },
                                {
                                    'mouth':(month-1)>0 ? month-1 : 12,
                                    'val':(d.businessInfo.bill3 == '' ? 0 : d.businessInfo.bill3)
                                }
                            );
                            for (var i = 0; i < billList.length; i++) {
                                _this.billMonth.push(billList[i].mouth + '月');
                                _this.billVal.push(billList[i].val);
                            }
                            //历史流量 数据获取
                            let flowList = [];
                            flowList.unshift(
                                {
                                    'mouth':(month-3)>0 ? month-3 : ((month-3) == -2 ? 10:((month-3) == -1 ? 11:12)),
                                    'val':(d.businessInfo.flow1 == '' ?  '0M' : d.businessInfo.flow1)
                                },
                                {
                                    'mouth':(month-2)>0 ? month-2 : ((month-2) == -1 ? 11 : 12),
                                    'val':(d.businessInfo.flow2 == '' ?  '0M' : d.businessInfo.flow2)
                                },
                                {
                                    'mouth':(month-1)>0 ? month-1 : 12,
                                    'val':(d.businessInfo.flow3 == '' ?  '0M' : d.businessInfo.flow3)
                                }
                            );
                            for (var i = 0; i < flowList.length; i++) {
                                _this.fluxMonth.push(flowList[i].mouth + '月');
                                _this.fluxVal.push(flowList[i].val);
                            }
                            //把流量单位统一
                            for (var i = 0; i < _this.fluxVal.length; i++) {
                                let crtVal = 0;
                                if (_this.fluxVal[i].substr(_this.fluxVal[i].length - 1) == 'G') {
                                    crtVal = parseFloat(_this.fluxVal[i].substr(0, _this.fluxVal[i].length - 1)) * 1024;
                                } else {
                                    crtVal = parseFloat(_this.fluxVal[i].substr(0, _this.fluxVal[i].length - 1));
                                }
                                _this.fluxCrtVal.push(crtVal);
                            }

                            //最近一次活动反馈
                            if (d.activeResp == null || d.activeResp == [] || d.activeResp == '' || d.activeResp == 'null'){
                                this.activeRespSwitch = false;
                            }else {
                                this.activeRespSwitch = true;
                                _this.activeResp = d.activeResp;
                                if ('1' == _this.activeResp.result){
                                    _this.activeRespResult = '同意';
                                }else if ('2' == _this.activeResp.result){
                                    _this.activeRespResult = '考虑';
                                }else if ('3' == _this.activeResp.result){
                                    _this.activeRespResult = '拒绝';
                                }
                            }

                            _this.setChart();
                        } else {
                            _this.$toast('查询失败');
                        }
                    })
                    .catch((response) => {
                    });
            },
            //设置图表
            setChart() {
                let _this = this;
                let billValcopy = _this.billVal;
                let myChart1Ymax = parseFloat(billValcopy[0]);
                for (let i = 0; i < billValcopy.length - 1; i++) {
                    myChart1Ymax = myChart1Ymax < parseFloat(billValcopy[i+1]) ? parseFloat(billValcopy[i+1]) : myChart1Ymax;
                }
                let myChart1Ymin = parseFloat(billValcopy[0]);
                for (let i = 0; i < billValcopy.length - 1; i++) {
                    myChart1Ymin = myChart1Ymin > parseFloat(billValcopy[i+1]) ? parseFloat(billValcopy[i+1]) : myChart1Ymin
                }

                let flowValcopy = _this.fluxVal;
                let myChart2Ymax = parseFloat(flowValcopy[0]);
                for (let i = 0; i < flowValcopy.length - 1; i++) {
                    myChart2Ymax = myChart2Ymax < parseFloat(flowValcopy[i+1]) ? parseFloat(flowValcopy[i+1]) : myChart2Ymax
                }
                let myChart2Ymin = parseFloat(flowValcopy[0]);
                for (let i = 0; i < flowValcopy.length - 1; i++) {
                    myChart2Ymin = myChart2Ymin > parseFloat(flowValcopy[i+1]) ? parseFloat(flowValcopy[i+1]) : myChart2Ymin
                }
                Chart.pluginService.register({
                    beforeRender: function (chart) {
                        if (chart.config.options.showAllTooltips) {
                            // create an array of tooltips
                            // we can't use the chart tooltip because there is only one tooltip per chart
                            chart.pluginTooltips = [];
                            chart.config.data.datasets.forEach(function (dataset, i) {
                                chart.getDatasetMeta(i).data.forEach(function (sector, j) {
                                    chart.pluginTooltips.push(new Chart.Tooltip({
                                        _chart: chart.chart,
                                        _chartInstance: chart,
                                        _data: chart.data,
                                        _options: chart.options,
                                        _active: [sector]
                                    }, chart));
                                });
                            });

                            // turn off normal tooltips
                            chart.options.tooltips.enabled = false;
                        }
                    },
                    afterDraw: function (chart, easing) {
                        if (chart.config.options.showAllTooltips) {
                            // we don't want the permanent tooltips to animate, so don't do anything till the animation runs atleast once
                            if (!chart.allTooltipsOnce) {
                                if (easing !== 1)
                                    return;
                                chart.allTooltipsOnce = true;
                            }

                            // turn on tooltips
                            chart.options.tooltips.enabled = true;
                            Chart.helpers.each(chart.pluginTooltips, function (tooltip) {
                                tooltip.initialize();
                                tooltip.update();
                                // we don't actually need this since we are not animating tooltips
                                tooltip.pivot();
                                tooltip.transition(easing).draw();
                            });
                            chart.options.tooltips.enabled = false;
                        }
                    }
                });
                var ctxzd = document.getElementById('myChart1').getContext('2d');
                var ctxll = document.getElementById('myChart2').getContext('2d');
                //历史账单
                var myChart1 = new Chart(ctxzd, {
                    type: 'line',
                    data: {
                        labels: _this.billMonth,
                        datasets: [{
                            label: '# of Votes',
                            data: _this.billVal,
                            //backgroundColor: ['rgba(255, 99, 132, 0)'],
                            backgroundColor: 'rgba(255, 99, 132, 0)',
                            borderColor: '#8ACBFF',
                            borderWidth: 2,
                            pointBackgroundColor: '#3776FF',
                            pointBorderColor: '#3776FF',
                            pointBorderWidth: '1',

                        }]
                    },
                    options: {
                        title: {
                            display:true,
                            position:'bottom',
                            fontSize:12,
                            text:'历史账单',
                        },
                        tooltips: {
                            backgroundColor: 'rgba(0, 0, 0, 0)',
                            titleColor: 'rgba(0, 0, 0, 0)',
                            bodyColor: '#3675FF',                    //新版本bodyFontColor
                            position: 'nearest',
                            titleMarginBottom: -16,
                            bodyFontSize: 12,
                            callbacks: {
                                label:
                                    function (tooltipItem, data) {
                                    //var i = tooltipItem.index;
                                    //console.info(data)
                                    //var label = data.datasets[0].data[i];
                                    return '';
                                }
                            }
                        },
                        showAllTooltips: true,
                        scales: {
                            xAxes: [{
                                borderColor: ['rgba(255, 99, 132, 0)'],
                                gridLines: {
                                    display: false,
                                    //color:'#ffffff'
                                }
                            }],
                            yAxes: [{
                                //display: false,
                                gridLines: {
                                    display: false
                                },
                                ticks: {
                                    max:Math.ceil(Math.round(myChart1Ymax)/10)*10,
                                    min:Math.floor(Math.round(myChart1Ymin)/10)*10,
                                    stepSize: Math.round((Math.ceil(Math.round(myChart1Ymax)/10)*10-Math.floor(Math.round(myChart1Ymin)/10)*10)/2)+Math.floor(Math.round(myChart1Ymin)/10)*10,
                                    callback: function (value) {
                                        return  Math.round(value)+'元';
                                    }
                                }
                            }]
                        },
                        legend: {
                            display: false,
                        }
                    }
                });

                //历史流量
                var myChart2 = new Chart(ctxll, {
                    type: 'line',
                    data: {
                        labels: _this.fluxMonth,
                        datasets: [{
                            label: '# of Votes',
                            data: _this.fluxCrtVal,
                            backgroundColor: 'rgba(255, 99, 132, 0)',
                            borderColor: '#D99422',
                            borderWidth: 2,
                            pointBackgroundColor: '#D99422',
                            pointBorderColor: '#D99422',
                            pointBorderWidth: '1',

                        }]
                    },
                    options: {
                        title: {
                            display:true,
                            position:'bottom',
                            fontSize:12,
                            text:'历史流量',
                        },
                        tooltips: {
                            backgroundColor: 'rgba(0, 0, 0, 0)',
                            titleColor: 'rgba(0, 0, 0, 0)',
                            bodyColor: '#D99422',                    //新版本bodyFontColor
                            position: 'nearest',
                            titleMarginBottom: -10,
                            callbacks: {
                                label:
                                    function (tooltipItem, data) {
                                    /*var i = tooltipItem.index;
                                    //console.info(data.datasets)
                                    var label = data.datasets[0].data[i];
                                    let res = 0;
                                    if (label > 1024) {
                                        res = parseFloat(label / 1024).toFixed(2);
                                        return res + "G";
                                    } else {
                                        return label + "M";
                                    }*/
                                    return '';
                                }
                            }
                        },
                        showAllTooltips: true,
                        scales: {
                            xAxes: [{
                                borderColor: ['rgba(255, 99, 132, 0)'],
                                gridLines: {
                                    display: false,
                                    //color:'#ffffff'
                                }
                            }],
                            yAxes: [{
                                //display: false,
                                gridLines: {
                                    display: false
                                },
                                ticks: {
                                    max:Math.ceil(Math.round(myChart2Ymax)/10000)*10000,
                                    min:Math.floor(Math.round(myChart2Ymin)/10000)*10000,
                                    stepSize:Math.round((Math.ceil(Math.round(myChart2Ymax)/10000)*10000-Math.floor(Math.round(myChart2Ymin)/10000)*10000)/2)+Math.floor(Math.round(myChart2Ymin)/10000)*10000,
                                    callback: function (value) {
                                        return  Math.round(value/1000)+'G';
                                    }
                                }
                            }]
                        },
                        legend: {
                            display: false,
                        }
                    }
                });
            },
            //业务tab点击
            businessTabCk(item, index) {
                this.businessIndex = index;
                this.currActivityInfo = this.userActivityList[index];
                this.reasonIndex = null;
                this.reasonList = [];
                this.reasonShowFlag = false;        //切换时 原因项隐藏
                //this.subMitBtnFlag = false;
                //清空原因项选择状态
                this.clearReasonState();
            },
            //原因项tab点击
            reasonCk(index) {
                let userId = this.$route.query.userId;
                console.info('userId:',userId);
                this.reasonIndex = index;
                this.reasonListIndex = null;
                this.zkTypeList = [];
                this.reasonListTxt = '理由选择';
                //同意
                if (index == 1) {
                    this.reasonShowFlag = false;
                    this.reasonList = [];
                    this.subMitBtnFlag = true;
                }
                //考虑原因
                if (index == 2) {
                    if(userId==undefined||userId==''||!Object.keys(this.currActivityInfo).length >0){
                        this.reasonShowFlag = false;
                        this.reasonList = [];
                        this.subMitBtnFlag = true;
                    }else {
                        this.reasonShowFlag = true;
                        //如果没有考虑原因项
                        if (this.currActivityInfo.consider == '') {
                            this.subMitBtnFlag = true;
                            this.reasonShowFlag = false;        //选择元素隐藏
                        } else {
                            this.reasonShowFlag = true;        //选择元素显示
                            this.reasonList = this.currActivityInfo.consider.split('∫');
                            for (var i = 0; i < this.reasonList.length; i++) {
                                let obj = {
                                    id: i,
                                    label: this.reasonList[i]
                                };
                                this.zkTypeList.push(obj);
                            }
                            this.subMitBtnFlag = false;
                        }
                    }

                }
                //拒绝原因
                if (index == 3) {
                    if(userId==undefined||userId==''||!Object.keys(this.currActivityInfo).length >0){
                        this.reasonShowFlag = false;
                        this.reasonList = [];
                        this.subMitBtnFlag = true;
                    }else {
                        this.reasonShowFlag = true;
                        //如果没有拒绝原因项
                        if (this.currActivityInfo.refuse == '') {
                            this.subMitBtnFlag = true;
                            this.reasonShowFlag = false;        //选择元素隐藏
                        } else {
                            this.reasonShowFlag = true;        //选择元素显示
                            this.reasonList = this.currActivityInfo.refuse.split('∫');
                            for (var i = 0; i < this.reasonList.length; i++) {
                                let obj = {
                                    id: i,
                                    label: this.reasonList[i]
                                };
                                this.zkTypeList.push(obj);
                            }
                            this.subMitBtnFlag = false;
                        }
                    }
                }
            },

          //获取开关 second_main_switch
          getSwitch(){
            return this.$http.get('/xsb/personBusiness/secondMaintenance/h5QrySecondMainSwitch');
          },

            //活动提交按钮点击
            async activitySubmit() {
               let n = await this.getSwitch();
              if (n.data.retCode != '0') {
                if (!this.submitFlag){
                  this.$alert('你未做任何营销动作，无法进行反馈操作');
                  return;
                }
              }
                let _this = this;
                let workId = Storage.session.get('workId');
                //let workId = _this.workId;
                let groupId = this.$route.query.groupId;
                let userPhone = this.$route.query.userPhone;
                let userId = this.$route.query.userId;
                let groupName = this.$route.query.groupName;
                let activityId = this.$route.query.activityId;

                //点击提交在对应的session里面添加是否提交submitFlg 开关字段
                let perSessionContent = {};
                let key = 'perActivity_'+groupId+'_'+activityId;
                perSessionContent = Storage.session.get(key);
                if(perSessionContent && perSessionContent.targetUserList){
                    for (var i = 0; i < perSessionContent.targetUserList.length; i++){
                        if(perSessionContent.targetUserList[i].msisdn == userPhone){
                            //item = perSessionContent.activityList[i];
                            if(!(perSessionContent.targetUserList[i].submitFlg)) {
                                Vue.set(perSessionContent.targetUserList[i], "submitFlg", true);
                            }
                            if(perSessionContent.targetUserList[i].clickUserFlg){
                                Vue.set(perSessionContent.targetUserList[i], "clickUserFlg", false);
                            }
                        }
                    }
                }
                Storage.session.set(key,perSessionContent);

                let stepId = '';
                if (!Object.keys(this.currActivityInfo).length >0){
                    activityId = '8888';
                    stepId = '9999';
                }else{
                    activityId = this.$route.query.activityId;
                    stepId = this.currActivityInfo.stepId;
                }

                let param = {
                    "workId": workId,
                    "groupId": groupId,                              //小区编码
                    "activityId": activityId,                             //活动id
                    "intent": this.reasonIndex,                       //选择的意向 同意1 考虑2 拒绝3
                    "reason": this.reasonListTxt,                    //原因项
                    "desc": this.textareaVal,                      //备注
                    "stepId": stepId,
                    "workType":2,
                    "userId":userId,
                    "msisdn" :userPhone,
                    "channelId":'12'
                };
                let paramr = {
                    "channelId": this.$route.query.channelId,
                    "workId": workId,
                    "objectId": groupId,                              //小区编码
                    "activityId": this.currActivityInfo.activityId || activityId,     //活动id
                    "intent": this.reasonIndex,                       //选择的意向 同意1 考虑2 拒绝3
                    "reason": this.reasonListTxt,                    //原因项
                    "desc": this.textareaVal,                      //备注
                    "stepId": stepId,
                    "workType":2,
                    "userId":userId,
                    "msisdn" :userPhone
                };
                //如果符合条件，提交
                if (this.subMitBtnFlag) {
                    let uploadURL ='';
                    if(this.$route.query.channelId=='53' || this.$route.query.channelId=='76'|| this.$route.query.channelId=='78'|| this.channelId=='1'){
                        uploadURL ='/xsb/personBusiness/secondConfirm/h5ActivityFeedBackSubmit';
                        param=paramr;
                    }else{
                        uploadURL = '/xsb/personBusiness/my/h5GroupVisitSyncSearch';
                    }

                    if (this.submit > 0){
                      this.$messagebox({
                        title: '提示',
                        message: '已反馈过营销结果，确认重新反馈吗？',
                        showCancelButton: true,
                        showConfirmButton: true,
                      }).then(action =>{
                        if (action == 'confirm'){
                          this.submitInvoking(uploadURL, param);
                        }
                      })
                    }else{
                      this.submitInvoking(uploadURL, param);
                    }
                }
            },

          submitInvoking(uploadURL,param){
            this.$http.post(uploadURL, param)
              .then((res) => {
                if (res.data.retCode == '0') {
                  if ( res.data.data != "" && res.data.data != null) {
                    //返回的DATA中的字段就是workId
                    if(Storage.session.get('workId') == '') {
                      Storage.session.set('workId', res.data.data);
                    }
                    /*if(_this.workId == '') {
                      _this.workId = res.data.data;
                    }*/
                  }

                  this.hasSubmitFlag = true;
                  this.submit = this.submit + 1;
                  let isOa = Storage.session.get('userInfo').oa;
                  if(this.reasonIndex == 1 && typeof isOa != 'undefined'){
                    this.showGoOrder = true;
                  }else{
                    _this.$toast('提交成功');
                  }
                } else {
                  this.$toast('提交失败');
                }
              })
              .catch((response) => {
              });
          },

            goOrder(flag){
                if(flag){
                    this.showCallOrder = true;
                }else{
                    this.showGoOrder = false;
                }
            },

            //文本域计数器
            textareaChange() {
                var txtVal = this.textareaVal.length;
                this.remnant = txtVal;
            },

            //原因项点击
            reasonListCk(item, index) {
                this.reasonListIndex = index;
                this.reasonListTxt = item;
                this.subMitBtnFlag = true;
            },
            //清空原因选择项
            clearReasonState() {
                this.reasonIndex = null;
                this.reasonListTxt = '';
                this.textareaVal = '';
                this.subMitBtnFlag = false;
            },
            //下拉按钮点击
            slideBtnCk() {
                this.slideFlag = !this.slideFlag;
            },
            //页面空白处点击
            pageCkClose() {
                this.slideFlag = false;
            },
            //下滑框选择
            showReasonDropdown() {
                var self = this;
                NlDropdown({
                    confirmBtn: false,
                    datalist: self.zkTypeList,
                    defaultValue: self.curZkType
                }, function (retVal) {
                    self.curZkType = retVal;
                    self.reasonListTxt = retVal.label;
                });
            },
            //上门
            visitCk(){
              if (this.visitIconFlag) {
                this.submitFlag = true;
                Storage.session.set("submitFlag",this.submitFlag);
                let myDate = new Date();
                this.clickActiveList.push({'activeType': '3', 'activeTime': dateFormat(myDate,"yyyy-MM-dd hh:mm:ss")});
                Storage.session.set("clickActiveList", this.clickActiveList);

                this.$router.push({
                  name: 'FamilyDetailNew',
                  query: {
                    workId: Storage.session.get('workId'),
                    //workId: this.workId,
                    villageId: this.villageId,
                    userPhone: this.$route.query.userPhone,
                    villageName: this.villageName,
                    activityType: '',
                    familyAdds: this.adddressTxt,
                    addressId: this.addressId,
                    businessName: '信息采集',
                    activityId: this.currActivityInfo.activityId
                  }
                });
              }
            },

            getCallId(value,activityId,activityName){
                let channelId = '';
                if(this.$route.query.channelId=='53'){
                    channelId='53';
                }else if(this.$route.query.channelId=='76'){
                    channelId='76';
                }else if(this.$route.query.channelId=='78'){
                    channelId='78';
                }else if(this.$route.query.channelId=='1'){
                    channelId='1';
                }else{
                    channelId = '12'
                }
                let myDate = new Date();
                let url = '/xsb/ability/ictoutcall/h5getCallId';
                let self = this;
                let param = {
                    callerNumber: value,//主叫号码(输入的)
                    calledNum: self.telNum,//被叫号码
                    appkey: 'FSOP',//渠道来源 FSOP
                    sessionId: self.whSession,//登录会话
                    activeId: activityId,//活动编码
                    businessId: '',//营业厅编码
                    userId: this.$route.query.userId ? this.$route.query.userId : '',//用户编码
                    activeName: activityName,//活动名称
                    seqId: '',//渠道流水号
                    channelId:channelId,
                    workId:this.workId,
                    objectId:this.$route.query.groupId,
                    stepId: this.currActivityInfo.stepId
                }
                //2.	呼出以及获取callId
                self.$http.post(url, param).then((res) => {
                    let {retCode,retMsg,data} = res.data;
                    if(retCode == '0'){
                        self.callId = data;//通话唯一标识
                        this.submitFlag = true;
                        Storage.session.set("submitFlag",this.submitFlag);
                        //营销活动数据
                        this.clickActiveList.push({'activeType': '2', 'activeTime': dateFormat(myDate,"yyyy-MM-dd hh:mm:ss"),'callId': self.callId,'callPhone': value});
                        Storage.session.set("clickActiveList", this.clickActiveList);
                        //展示验密及查看结果
                        self.showWhMenu = true;
                    } else {//重新登录
                        self.$http.post('/xsb/ability/ictoutcall/h5login', {callerNumber: value}).then((res) => {
                            let {retCode,retMsg,data} = res.data;
                            if (retCode == '0') {
                                self.whSession = data;
                                Storage.session.set('whSession',data)
                                //2.	呼出以及获取callId
                                self.$http.post(url, param).then((res) => {
                                    let {retCode,retMsg,data} = res.data;
                                    if(retCode == '0'){
                                        self.callId = data;//通话唯一标识
                                        this.submitFlag = true;
                                        Storage.session.set("submitFlag",this.submitFlag);
                                        //营销活动数据
                                        this.clickActiveList.push({'activeType': '2', 'activeTime': dateFormat(myDate,"yyyy-MM-dd hh:mm:ss"),'callId': self.callId,'callPhone': value});
                                        Storage.session.set("clickActiveList", this.clickActiveList);

                                        //展示验密及查看结果
                                        self.showWhMenu = true;
                                    }else {
                                        self.$alert(retMsg || '当前用户无法呼叫')
                                    }
                                });
                            } else {
                                self.$alert(retMsg || '当前用户无法呼叫')
                            }
                        });
                    }
                })
            },
            //外呼
            doWaiHu(activityId, activityName) {
/*                let timeNow = new Date();
                if (timeNow.getHours() < 8 || timeNow.getHours() > 20){
                    this.$alert("只可在每天的 8：00 至 19：59 之间进行外呼");
                    return;
                }*/
                if(this.isCall == 1) {
                    let myDate = new Date();
                    let self = this;
                    let defaultIptVal = Storage.session.get('callerNumber') || '';
                    let iptOption = {}
                    if (defaultIptVal) {
                        iptOption.inputValue = defaultIptVal;
                    }
                    let url = `/xsb/ability/ictoutcall/h5login`;
                    this.$messagebox.prompt('请输入手机号', '温馨提示', iptOption).then(({value, action}) => {
                        if (action == 'confirm' && /^((1)+\d{10})$/.test(value)) {
                            Storage.session.set('callerNumber', value);
                            let whSession = Storage.session.get('whSession') || '';
                            if (defaultIptVal == value && whSession) {//已经登录过 不再登录
                                self.whSession = whSession;
                                self.getCallId(value, activityId, activityName);
                            } else {
                                self.$http.post(url, {callerNumber: value}).then((res) => {
                                    let {retCode, retMsg, data} = res.data;
                                    if (retCode == '0') {
                                        self.whSession = data;
                                        Storage.session.set('whSession', data);
                                        self.getCallId(value, activityId, activityName);
                                    } else {
                                        this.clickActiveList.push({
                                            'activeType': '2',
                                            'activeTime': dateFormat(myDate, "yyyy-MM-dd hh:mm:ss"),
                                            'callId': '',
                                            'callPhone': value
                                        });
                                        Storage.session.set("clickActiveList", this.clickActiveList);
                                        self.$alert(retMsg || '当前用户无法呼叫');
                                    }
                                });
                            }
                        } else {
                            if (action == 'confirm') {
                                self.$toast('请输入正确的手机号');
                            }
                        }
                    });
                }else {
                    this.$alert("此号码不能被外呼");
                }
            },
            //查看验密结果
            getWhResult(){
                let url = `/xsb/ability/ictoutcall/h5getResult`;
                let self = this;
                self.$http.post(url, {callId: self.callId}).then((res) => {
                    let {retCode,retMsg,data} = res.data;
                    console.log(">>>>>>>>>>>>>>>>>>>>>res.data"+res);
                    if (retCode == '0') {
                        self.isVerifyResult = data.isVerify;//是否验密 （0，没有验密初始状态，1：开始验密；2，验密成功，-2：验密失败）
                        if(self.isVerifyResult == '0'){
                            self.$alert('没有验密');
                        } else if(self.isVerifyResult == '1'){
                            self.$alert('开始验密');
                        } else if(self.isVerifyResult == '2'){

                            let obj = {
                                jqType: '4',   //0:服务密码，1:验证码，2:身份证
                                result: '1',             //鉴权结果，1为成功
                                authtype:'06',
                                telnum: self.telNum
                            };
                            Storage.session.set('jqData',obj);
                            self.$alert('验密成功');
                        } else if(self.isVerifyResult == '-2'){
                            self.$alert('验密失败');
                        } else {
                            self.$alert('验密失败');
                        }
                    } else {
                        self.isVerifyResult = '-2';
                        self.$alert(retMsg || '验密失败');
                    }
                });
            },
            //验密 呼叫转移
            validateWh(){
                let url = `/xsb/ability/ictoutcall/h5transfer`;
                let self = this;
                let param = {
                    callId: self.callId,
                    sessionId:self.whSession,
                    calledNum:self.telNum
                }
                self.$http.post(url, param).then((res) => {
                    console.info(res);
                    let {retCode,retMsg,data} = res.data;
                    if (retCode == '0') {
                    } else {
                        self.$alert(retMsg || '验密失败')
                    }
                });
            },
            //scroll事件
            mScroll(e) {
                let scrollTop = e.target.scrollTop;
                if (scrollTop >= 50) {
                    this.telFloatFlag = true;
                } else {
                    this.telFloatFlag = false;
                }

                if (scrollTop >= 380) {
                    this.tabFloatFlag = true;
                } else {
                    this.tabFloatFlag = false;
                }
            },
            //短信
            sendMsgCk() {
                /*this.submitFlag = true;
                Storage.session.set("submitFlag",this.submitFlag);
                let myDate = new Date();
                this.clickActiveList.push({'activeType':'1','activeTime': dateFormat(myDate,"yyyy-MM-dd hh:mm:ss")});
                Storage.session.set("clickActiveList",this.clickActiveList);
                ClientJs.openMessageView(this.telNum, this.currActivityInfo.messageInfo);*/
                this.$alert("功能升级改造中，敬请期待!");
            },
            //业务办理
            businessManage(){
                this.submitFlag = true;
                Storage.session.set("submitFlag",this.submitFlag);
                let myDate = new Date();
                this.clickActiveList.push({'activeType':'4','activeTime':dateFormat(myDate,"yyyy-MM-dd hh:mm:ss")});
                Storage.session.set("clickActiveList",this.clickActiveList);

                if(this.isVerifyResult == '2'){//外呼验密成功
                    this.$router.push({
                        name:'CsViewIn',
                        query:{
                            telnum:this.telNum
                        }
                    });
                } else {
                    let self = this;
                    // let webUrl = Storage.get('webUrl');
                    // let url = `${webUrl}/page5/identify/jianquanJQ_out.jsp?operid=${this.userInfo.crmId}&regionId=${this.userInfo.region}&telnum=${this.telNum}&priv_id=1001`;
                    // ClientJs.openWebKit(url, '', '0', '', '', '', '', '', '', '', '');
                    Authenct({
                        readOnlyFlag:true,
                        telnum: self.telNum,
                        popFlag:true,
                        idCardWay:false,
                        dimTelnumFlg:true,//手机号中间4位模糊化加上这行
                    },function (obj){
                        // self.$router.push('/personPici')
                      self.$router.push({
                        name:'CsViewIn',
                        query:{
                          telnum:self.telNum,
                          srcFrom:'detail',
                        }
                      });
                    });
                }


            },
            //关闭banner弹框
            closeBannerPop(){
                this.bannerPopFlag = false;
            },
            //banner 办理
            bannerDo(){
                let bannerItem = this.bannerItem;
                this.$router.push({
                    path: '/personReward',
                    query: {
                        actId: bannerItem.productId,
                        actName: bannerItem.productIdExp5,
                        levelId: bannerItem.productIdExp1,
                        levelName: bannerItem.productIdExp6,
                        packType:'2',
                        srcFrom:'csView'
                    }
                });
            }
        },
        components: {
            Header,
            NoDataPage,
            Slide,
            outboundCallOrder
        }
    }
</script>

<style scoped lang="less">
    @import '../../base/less/variable.less';

    .ud-main {
        position: absolute;
        top: 44px;
        left: 0px;
        right: 0px;
        bottom: 0px;
        overflow: auto;
    }

    .ud-tel-float {
        height: 50px;
        background: #E8F5F8;
        position: fixed;
        top: 44px;
        left: 0px;
        right: 0px;
        box-shadow: 0px 3px 10px #d8dcdc;
    }

    .ud-tel-float.fla {
        animation: telFla .6s linear both;
    }

    @keyframes telFla {
        0% {
            opacity: 0;
        }
        100% {
            opacity: 1;
        }
    }

    .ud-tel-float .ud-tel-inner {
        margin: 10px auto;
        font-size: 18px;
    }

    .vs-intent {
        height: auto;
        overflow: hidden;
    }

    .vs-inttent-select {
        height: auto;
        overflow: hidden;
        margin-left: 18px;
        margin-top: 10px;
    }

    .vs-is-lis {
        display: block;
        float: left;
        width: 48px;
        height: 30px;
        text-align: center;
        border: 1px solid #E9E9E9;
        margin-right: 16px;
        color: #8F8F8F;
        line-height: 30px;
        font-size: 14px;
    }

    .vs-is-lis.active {
        border: 1px solid rgba(22, 129, 251, 1);
        color: #1681FB;
    }

    .vs-reason {
        height: auto;
        overflow: hidden;
        border-top: 1px solid #E4E4E4;
        margin: 12px;
        margin-bottom: 0px;
    }

    .vs-reason ul {
        display: block;
        height: auto;
        overflow: hidden;
    }

    .vs-reason .vs-reason-lis {
        height: auto;
        overflow: hidden;
        line-height: 16px;
        font-size: 11px;
        color: #666666;
        position: relative;
    }

    .vs-rea-txt {
        height: auto;
        margin-right: 20px;
        padding-top: 8px;
        padding-bottom: 8px;
        line-height: 18px;
    }

    .vs-rea-select {
        width: 16px;
        height: 16px;
        position: absolute;
        top: 50%;
        margin-top: -8px;
        right: 0px;
    }

    .vs-desc {
        height: auto;
        overflow: hidden;

        border-top: 1px solid #DDDDDD;
        border-bottom: 1px solid #DDDDDD;
        margin-left: 12px;
        margin-right: 12px;
        margin-top: 5px;
    }

    .vs-rs-lis {
        height: 40px;
        line-height: 40px;
        font-size: 14px;
        color: #000;
        text-indent: 12px;
        display: flex;
        margin-top: 5px;
    }

    .vs-rlistxt {
        flex-grow: 1;
    }

    .iconyjt {
        width: 30px;
        height: 40px;
        flex-shrink: 0;
        font-size: 14px;
        line-height: 40px;
        text-align: center;
        margin-right: 10px;
    }

    .vd-tab-slide {
        height: auto;
        background: #fff;
        position: absolute;
        top: 41px;
        left: 0px;
        right: 0px;
        z-index: 50;
        box-shadow: 0px 4px 10px #969090;
    }

    .ml-area {

        height: 70px;
        padding: 10px;
        position: relative;
    }

    .vd-tabsul {
        display: block;
        height: auto;
        overflow: hidden;
    }

    .ml-area textarea {
        border: none;
        background: none;
        width: 100%;
        height: 90%;
        resize: none;
        font-size: 12px;
        color: #666;
        outline: none;
    }

    .ml-txt-count {
        position: absolute;
        height: 18px;
        line-height: 18px;
        bottom: 0px;
        right: 10px;
        color: #bbb;
        font-size: 12px;
    }

    .btn-style-blue {
        height: 44px;

        background: #c8c9ca;
        border-radius: 22px;
        text-align: center;
        line-height: 44px;
        font-size: 14px;
        letter-spacing: 4px;
        color: #fff;
        margin-left: 15px;
        margin-right: 15px;
        margin-top: 14px;
    }

    .btn-style-blue.active {
        background: rgba(22, 129, 251, 1);
    }

    .ml-txt-count {
        position: absolute;
        height: 18px;
        line-height: 18px;
        bottom: 0px;
        right: 10px;
        color: #bbb;
        font-size: 12px;
    }

    .vs-btn {
        height: 69px;
        overflow: hidden;
    }

    .vd-tblis {
        display: block;
        height: 40px;
        line-height: 40px;
        text-align: center;
        width: 33.33%;
        font-size: 14px;
        float: left;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .user-dt {
        height: auto;
        background: #ECF0FA;
    }

    .ud-tel {
        height: 50px;
        background: #fff;
        margin-top: 0px;
        border-top: 1px solid #EAEAEA;
    }

    .ud-tel-inner {
        width: 166px;
        height: 34px;
        background: rgba(232, 245, 248, 1);
        border-radius: 100px;
        margin: 8px auto;
        line-height: 34px;
        text-align: center;
        font-size: 14px;
        font-weight: 400;
        color: rgba(63, 115, 144, 1);
    }

    .ud-menu {
        /*height: 32px;*/
        padding: 0 10px 0 10px;
        /* background: rgba(246, 246, 246, 1);
        color: rgba(67, 70, 73, 1); */
        color: #3776FF;
        background:#fff;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
        text-indent: 12px;
    }

    .ud-menu-lis {
        /*         background: #fff;
                overflow: hidden; */
        background: rgba(255, 255, 255, 1);
        border-radius:8px;
        margin: 0 10px;
    }

    .ud-mlis-title {
        height: 20px;
        font-size: 14px;
        font-weight: 600;
        color: rgba(72, 72, 72, 1);
        text-indent: 12px;
        line-height: 20px;
        margin-top: 10px;
        margin-bottom: 10px;
        display: flex;
    }
    .ud-mlis-title.active {
        padding: 10px 0px 0px 10px;
        margin-bottom: -5px;
    }
    .ud-mlis-resp{
        height: 90px;
        //background: rgba(255, 255, 255, 1);
        //box-shadow: 0px 0px 16px 0px rgba(219, 219, 219, 0.5);
        border-radius: 8px;
        margin: 0px 12px 12px 12px;
    }
    .ud-table{
        padding:5px 10px;
    }
    .ud-mlis-txt{
        display: block;
        height:14px;
        font-size:14px;
        color:rgba(79,80,80,1);
        line-height:14px;
        margin-top: 10px;
        .ud-mlis-txt-blue{color: #3776FF;}
    }

    .ud-mlis-crt {
        height: 150px;
        //background: rgba(255, 255, 255, 1);
        //box-shadow: 0px 0px 16px 0px rgba(219, 219, 219, 0.5);
        border-radius: 8px;
        margin: 10px 0px 0px 5px;
        width: 160px;
        display: flex;
        padding: 10px 0;
    }

    .vd-tab {
        height: 40px;
        background: #fff;
        border-bottom: 1px solid #E8E8E8;
        position: relative;
    }

    .vd-tab.fla {
        position: fixed;
        top: 95px;
        left: 0px;
        right: 0px;
    }

    .vd-tab-ul {
        height: 100%;
        margin-left: 16px;
        margin-right: 40px;
        display: flex;
        flex-wrap: nowrap;
        overflow: auto;
        margin-top: 10px;
    }

    .vd-tabli {
        width: auto;
        display: block;
        float: left;
        height: 100%;
        font-size: 16px;
        font-weight: 400;
        color: rgba(51, 51, 52, 1);
        margin-right: 10px;
        white-space: nowrap;
        line-height: 40px;
    }

    .vd-tabli.active {
        color: #007AFF;
        border-bottom: 2px solid #007AFF;
        box-sizing: border-box;
    }

    .vd-tslidebtn {
        width: 40px;
        height: 40px;
        position: absolute;
        top: 0px;
        right: 0px;
        background: #EEEEEE;
        text-align: center;
        line-height: 44px;
    }

    .iconjt2 {
        font-size: 14px;
        color: #000;
        width: 20px;
        height: 20px;
        line-height: 20px;
        text-align: center;
        display: block;
        margin: 10px auto;
    }

    .vd-tslidebtn.active .iconjt2 {
        transform: rotate(-180deg);
        color: #007AFF;
        transition: all .3s linear;
    }

    .ud-active {
        background: #fff;
        /* overflow: hidden; */
    }

    .ud-at-title {
        height: 20px;
        font-size: 14px;
        font-weight: 600;
        color: rgba(51, 51, 52, 1);
        line-height: 20px;
        padding-top: 10px;
        text-indent: 12px;
    }

    .ud-at-detail {
        margin: 12px;
        font-size: 14px;
        font-weight: 400;
        color: rgba(110, 114, 118, 1);
        line-height: 20px;
        margin-top: 6px;
    }

    .ud-at-btns {
        height: 70px;
        /* overflow: hidden; */
        display: flex;
        justify-content: space-around;
        padding-bottom: 1rem;
    }

    .btn-msg {
        width: 50px;
        height: 50px;
        position: relative;
        background: linear-gradient(163deg, rgba(92, 234, 103, 1) 0%, rgba(59, 186, 66, 1) 100%);
        border-radius: 50%;
        line-height: 62px;
        text-align: center;
        margin-bottom: 20px;
    }

    .icondx {
        width: 30px;
        height: 30px;
        font-size: 25px;
        color: #fff;
        line-height: 30px;
        text-align: center;
        display: block;
        margin: 11px auto;
        margin-bottom: 0px;
    }

    .iconwaihu {
        width: 30px;
        height: 30px;
        font-size: 25px;
        color: #fff;
        line-height: 30px;
        text-align: center;
        display: block;
        margin: 11px auto;
        margin-bottom: 0px;
    }

    .iconshangmen {
        width: 30px;
        height: 30px;
        font-size: 25px;
        color: #fff;
        line-height: 30px;
        text-align: center;
        display: block;
        margin: 11px auto;
        margin-bottom: 0px;
    }

    .btn-msg:before {
        content: '短信';
        position: absolute;
        width: 80px;
        height: 20px;
        left: 50%;
        margin-left: -40px;
        line-height: 20px;
        font-size: 12px;
        font-weight: 400;
        color: rgba(156, 156, 156, 1);
        bottom: -24px;
    }

    .btn-waihu {
        width: 50px;
        position: relative;
        height: 50px;
        background: linear-gradient(159deg, rgba(110, 175, 255, 1) 0%, rgba(22, 129, 251, 1) 100%);
        border-radius: 50%;
        line-height: 50px;
        text-align: center;
    }
    .waihu-menu-wrap{
        position: absolute;
        height:81px;
        line-height:86px;
        top:50px;
        left:50%;
        color:#fff;
        width: 226px;
        font-size:14px;
        background-repeat: no-repeat;
        background-position: center;
        transform:translateX(-50%);
        background-size:226px 81px;
        .iconfont{
            font-size:14px;

        }
        .shuxian{
            color:#1274E9;
            padding:0 4px;
        }
    }
    .btn-waihu:before {
        content: '外呼';
        position: absolute;
        width: 80px;
        height: 20px;
        left: 50%;
        margin-left: -40px;
        line-height: 20px;
        font-size: 12px;
        font-weight: 400;
        color: rgba(156, 156, 156, 1);
        bottom: -24px;
    }

    .btn-shangmen {
        width: 50px;
        height: 50px;
        position: relative;
        background: linear-gradient(149deg, rgba(239, 171, 90, 1) 0%, rgba(239, 118, 39, 1) 100%);
        border-radius: 50%;
        line-height: 50px;
        text-align: center;
    }

    .btn-shangmen.unable {
        background: #ccc;
    }

    .btn-shangmen:before {
        content: '上门';
        position: absolute;
        width: 80px;
        height: 20px;
        left: 50%;
        margin-left: -40px;
        line-height: 20px;
        font-size: 12px;
        font-weight: 400;
        color: rgba(156, 156, 156, 1);
        bottom: -24px;
    }

    .btn-yewubanli {
        width: 50px;
        height: 50px;
        position: relative;
        background: linear-gradient(149deg, rgba(239, 171, 90, 1) 0%, rgba(239, 118, 39, 1) 100%);
        border-radius: 50%;
        line-height: 50px;
        text-align: center;
    }

    .btn-yewubanli:before {
        content: '业务办理';
        position: absolute;
        width: 80px;
        height: 20px;
        left: 50%;
        margin-left: -40px;
        line-height: 20px;
        font-size: 12px;
        font-weight: 400;
        color: rgba(156, 156, 156, 1);
        bottom: -24px;
    }

    .iconyewubl {
        color: #fff;
        font-size: 20px;
    }

    .iconSelect {
        color: #1681FB;
    }

    .martop10 {
        margin-top: 10px;
    }

    .martop45 {
        margin-top: 42px;
    }
    .head {
        height: auto;
        overflow: hidden;
        position: fixed;
        top: 0px;
        left: 0px;
        right: 0px;
        z-index: 99;
    }
    .gl-title {
        height: 44px;
        text-align: center;
        position: relative;
        background: #fff;
    }
    .gl-back {
        position: absolute;
        left: 0px;
        top: 50%;
        font-size: 20px;
        padding: 8px;
        transform: translateY(-50%);
    }
    .gl-back:before {
        color: #007AFF;
    }
    .gl-title-txt {
        color: @color-TextTitle;
        display: inline-block;
        margin-top: 14px;
    }
    .pl-mvisit-line{
        color:#E8E8E8;
        margin-left: 10px;
        border-left: 1px solid;
    }
    .wc-bannerblock{
        height: auto;overflow: hidden;border-top:1px solid #F0F0F0;//background: #fff;
        border-bottom:1px solid #F0F0F0;/* margin-top:10px; */flex-shrink: 0; margin: 10px;
        border-radius: 8px;
    }
    .cs-bannernei {
        //margin-left: 16px;
        //margin-right: 16px;
        //margin-top: 16px;
        overflow: hidden;
        padding-bottom: 30px;
    }
    .banner-cs-title{position: absolute;top:0px;left:0px;right:0px;bottom:0px;
        text-align: center;font-size: 20px;color:#fff;line-height: 92px;font-weight: bold;
        text-shadow: 1px 2px 6px #ccc;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;
    }
    .banner-filter{position: fixed;top:0px;left:0px;bottom:0px;right:0px;
        background: rgba(0,0,0,0.5);z-index: 50;
    }
    .cs-vfpop{position: absolute;left:50%;top:50%;background: #fff;z-index: 51;
        border-radius: 8px;
        padding-top: 25px;
        width: 80%;
        transform: translate(-50%,-50%);
    }
    .icon-guanbi{display: block;position: absolute;width: 26px;height: 26px;top:4px;right:5px;
        text-align: center;line-height: 26px;
    }
    .cs-vfpop-title{height: auto;
        line-height: 25px;
        text-align: center;
        font-size: 18px;
        color: #333;
        margin-top: 10px;
        margin-bottom: 10px;
        margin-left: 30px;
        margin-right: 30px;
    }
    .cs-vfpop-main{height: auto;overflow: hidden;margin-left:20px;margin-right: 20px;
        line-height: 22px;color:#969696;font-size: 16px;margin-bottom: 10px;padding-bottom: 30px;
    }

    .cs-vfpop-bottom{height: 50px;overflow: hidden;border-top:1px solid #ddd;}
    .cs-vb-ok{display: block;text-align: center;height: 50px;line-height: 50px;
        font-size: 16px;color:#26a2ff;
    }

.first-box{
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        padding: 42px;
        right: 0;
        left: 0;
        z-index: 2001;
    }
    .first-box-main{
        padding: 36px 10px 10px;
        box-sizing: border-box;
        background: #fff;
        border-radius: 15px;
        position: relative;
    }
    .first-box-main.active{
        padding: 0 20px 20px;
    }
    .icon{
        font-size: 40px;
        padding: 20px;
        border-radius: 40px;
        background: #0C78FA;
        color: #fff;
        position: absolute;
        top: -40px;
        left: 50%;
        transform: translateX(-50%);
    }
    .icon.active{
        background: #F43A45;
    }
    .icon-guanbi{
        font-size: 13px;
        position: absolute;
        top: 15px;
        right: 15px;
        font-weight: bolder;
    }
    .icon-guanbi{
        top: 19px;
        right: 18px;
    }
    .first-box-textarea{
        display: flex;
        flex-direction: column;
        align-items: center;
    }
    .first-box-textarea.active{
        padding: 0 15px;
    }
    .textarea-text{
        font-size: 15px;
        font-weight: 500;
        color: #222;
        padding: 15px 0;
        line-height: 30px;
    }
    .divider{
        border-bottom: 1px solid #EEEEEE;
        width: 100%;
    }
    .textarea-text.active{
        font-size: 13px;
        color: #898989;
        font-weight: 500;
    }
    .double-bottom{
        width: 100%;
        display: flex;
        justify-content: space-between;
        padding: 15px 0 5px;
    }
    .double-bottom-item{
        display: flex;
        width: 45%;
        border-radius: 6px;
        background: #fff;
        font-size: 16px;
        color: #222;
        height: 35px;
        justify-content: center;
        align-items: center;
        border: 1px solid #c1c1c1;
    }
    .double-bottom-item.active{
        background: #0C78FA;
        border: 0;
        color: #fff;
    }
    .single-bottom{
        width: 100%;
        display: flex;
        justify-content: center;
        padding: 15px 0 5px;
    }
    .single-bottom-item{
        display: flex;
        width: 50%;
        border-radius: 6px;
        background: #0C78FA;
        font-size: 16px;
        color: #fff;
        height: 40px;
        justify-content: center;
        align-items: center;
    }
    .back-img{
        position: fixed;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        opacity: 0.5;
        z-index: 2000;
        background: #000;
    }
    .menber-biaoqian{
        height: 20px;
        width: 115px;
        padding: 10px 30px;
        border: 1px solid #007aff;
        background: #007aff;
        border-radius: 20px;
        margin: 10px auto;
        text-align: center;
    }
    .menber-detail{
        color: #fff;
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
    }
    .more-menu-content {
        border-top: 1px solid #d1d1ce;
        padding: 10px 10px;
        font-size: 12px;
        div {
            margin-bottom: 10px;
            p {
                line-height: 20px;
                display: inline-flex;
                width: 95%;
                word-break: break-all;
            }
        }
    }
</style>
