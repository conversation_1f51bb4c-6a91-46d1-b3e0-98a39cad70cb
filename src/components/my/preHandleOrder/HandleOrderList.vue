<template>
  <div>
    <Header tsTitleTxt="订单列表" backType = 'custom' @emGoPrev="goPrev"></Header>
    <div class="wrapper">
      <div class="staging-top wrapper-medias">
        <div class="search-box">
          <input class="box" v-model="query" placeholder="请输入用户号码"/>
          <div class="sousuo-icon">
            <i class="iconfont sousuo" @click="getList(0)"></i>
          </div>
        </div>
        <div class="whole">
          <span class="countDesc">总订单数：<span class="count">{{total}}</span></span>
          <span class="drop">
            <div @click="selectState()">
              <span>{{stateShow}}</span>
              <span class="iconfont f11" v-show="!showChoosePanel"></span>
              <span class="iconfont f11-copy " v-show="showChoosePanel"></span>
            </div>
          </span>
        </div>
      </div>
      <div class="state-choose-panel">
        <div v-show="showChoosePanel" class="state-choose">
          <div class="dropMenu" >
            <div class="triangle"></div>
            <div class="dropState" :class="{active:orderState=='0'}" @click="chooseState('0')">全部</div>
            <div class="dropState" :class="{active:orderState=='1'}" @click="chooseState('1')">待处理</div>
            <div class="dropState" :class="{active:orderState=='2'}" @click="chooseState('2')">进行中</div>
            <div class="dropState" :class="{active:orderState=='3'}" @click="chooseState('3')">已处理</div>
          </div>
        </div>
      </div>
      <div class="cont">
          <div>
            <mt-loadmore
                :top-method="loadTop"
                :bottom-method="loadBottom"
                :bottom-all-loaded="tsAllLoaded"
                :auto-fill="false"
                topPullText="刷新"
                topDropText="释放更新"
                topLoadingText="刷新中···"
                bottomPullText="加载更多"
                bottomDropText="释放加载"
                bottomLoadingText="加载中···"
                ref="loadmore">
                  <ul class="drop-ul">
                      <li class="item-li" v-for="(item,index) in listData" :key="index" @click.stop="goDetail(item)">
                        <div class="item-sub">
                          <div>
                            <span class="sub-id">{{item.phoneNumber}}</span>
                            <span class="sub-state"
                                  :class="{'yellow-state': ~yellowState.indexOf(item.state),
                                           'green-state': ~greenState.indexOf(item.state),
                                           'red-state': ~redState.indexOf(item.state)}">
                              {{stateDesc(item)}}
                            </span>
                            <span class="sub-time">{{item.createDate | dateShow('MM/dd hh:mm',false)}}</span>
                          </div>
                          <div class="sub-line">
                            <span class="sub-businame">{{item.busiName}}</span>
                          </div>
                          <div class="sub-line">
                            <span class="sub-businame">订单号:{{item.orderId}}</span>

                            <span v-show="btnDesc(item)!=''"><button :class="{'remind':item.state==1,'green':item.state=='3'}" @click.stop="doSubmit(item)">{{btnDesc(item)}}</button></span>
                            <span v-show="judgeDate(item.modifyDate,item.state)"><button class="red" @click.stop="deleteOrder(item.orderId)" >删除</button></span>

                          </div>
                        </div>
                      </li>
                  </ul>
            </mt-loadmore>
          </div>
        <NoDataPage class="nodata" v-show="!listData || listData.length < 1" tipTxt="暂无记录"></NoDataPage>
      </div>
    </div>
  </div>
</template>
<script>
	import Header from 'components/common/Header.vue'
	import NoDataPage from 'components/common/NoDataPage.vue'
  import {BTN_CONFIG_COMMON,BTN_CONFIG_BUSI} from '@/base/BtnConfig5G.js'
 import Storage from '@/base/storage';
  export default {
    components:{Header,NoDataPage},
    data() {
      return {
        query: '', //查询
        showChoosePanel: false,//是否展示状态选择框
        orderState: '',//要查询的状态
        listData:[],//预受理单数据
        pageNum:'1',//默认页数
        pageSize:'5',//默认加载个数
        total:0,//数据总条数
        stateShow: '',//当前筛选的状态
        tsAllLoaded: false,//是否加载完了
        source:'',
        qwInfo: {}, //企微信息
        qwCustNumber: "", //企微客户手机号
        qwCustId: "", // 企微客户id
      }
    },
    created(){
      this.orderState = this.$route.query.state;
      this.uinfo = Storage.session.get('userInfo');
      this.qwInfo = Storage.session.get('qwInfo');
      this.qwCustNumber = Storage.session.get("qw_custNumber");
      this.qwCustId = Storage.session.get("qw_custId");
      console.log('========this.qwCustNumber' + this.qwCustNumber);
      console.log('========this.qwCustId' + this.qwCustId);

      this.yellowState = ['3', '20', '21'];//已确认为黄色
      this.greenState = ['0','4','6','7'];//已发送、办理成功、远程办理归档和现场办理归档为绿色
      this.redState = ['-1','2','5', '22'];//发送失败、已取消、办理失败为红色
      switch(this.orderState){
        case '0':
          this.stateShow = '全部';break;
        case '1':
          this.stateShow = '待处理';break;
        case '2':
          this.stateShow = '进行中';break;
        case '3':
          this.stateShow = '已处理';break;
        default:
          this.stateShow = '全部';
      }
      if(window.triggerFrom && window.triggerFrom == 'qw') {
        this.source = 'qw';
      }
      this.getList(0);
    },
    methods: {
      selectState() {
        if (this.showChoosePanel) {
          this.showChoosePanel = false;
        } else {
          this.showChoosePanel = true;
        }
      },
      chooseState(item) {
        this.orderState = item;
        switch(this.orderState){
          case '0':
            this.stateShow = '全部';break;
          case '1':
            this.stateShow = '待处理';break;
          case '2':
            this.stateShow = '进行中';break;
          case '3':
            this.stateShow = '已处理';break;
          default:
            this.stateShow = '全部';
        }
        this.showChoosePanel = false;
        this.getList(0);
      },
      loadTop(){//刷新
        this.$refs.loadmore.onTopLoaded();
        this.getList(0);
      },
      loadBottom(){//加载
        this.$refs.loadmore.onBottomLoaded();
        this.getList(1);
      },
      //获取列表-mode:模式：0-刷新 1-加载
      getList(mode){
          if(!this.uinfo.crmId) {
              this.$alert('查询预受理订单列表失败，操作员工号不能为空');
              return;
          }
          if(mode == 0){
              this.tsAllLoaded = false;
              this.listData = [];
              this.pageNum = 1;
          }else{
              this.pageNum++;
          }
          let url = `/xsb/personBusiness/fifthGHandle/h5GetHandleOrderList?pageNum=${this.pageNum}&pageSize=${this.pageSize}&state=${this.orderState}&phoneNumber=${this.query}&source=${this.source}`;
          this.$http.get(url).then(res => {
              let {retCode,data,retMsg} = res.data;
              if (retCode == '0') {
                 this.total=data.total;
                 this.$toast(retMsg);
                 if(mode == 0){//刷新
                    this.listData = data.pageData;
                 }else{//加载
                    data.pageData.forEach(info => {
                        this.listData.push(info);
                    })
                    //判断是否查询完了
                    if(this.pageNum*this.pageSize >= this.total){
                        this.tsAllLoaded = true;
                    }
                 }
              } else {
                  this.$alert(retMsg || '获取列表失败');
              }
          }).catch((response) => {
              this.$alert(`获取列表网络连接失败${response}`);
          })
      },
      //状态描述
      stateDesc(item){
        let state = item.state;
        let busiType = item.busiType;
        let descStr='';
        switch(state){
         case "-1": descStr = "发送失败";break;
         case "0" : descStr = "已发送";break;
         case "1" : descStr = "待确认";break;
         case "2" : descStr = "已取消";break;
         case "3" : descStr = "已签字";break;
         case "4" :
            descStr = "办理成功";
            if( busiType.indexOf('supp')!='-1'){
              descStr = "用户已签字";
            }
            break;
         case "5" : descStr = "办理失败";break;
         case "6" :
         case "7" :
            descStr = "归档";
            break;
         case "12":
            descStr = "已过期";
            break;
         case "13":
            descStr = "已撤回";
            break;
          case "20":
            descStr = "待支付";
            break;
          case "21":
            descStr = "支付中";
            break;
          case "22":
            descStr = "支付失败";
            break;
         default :
            descStr = "未知状态"
        }
        return descStr;
      },
      //按钮描述（根据业务类型和状态生成不同的按钮）
      btnDesc(item){
        //先遍历common配置来展示按钮
        let state = item.state;
        let btnDesc = '';
        let busiType = item.busiType
        BTN_CONFIG_COMMON.forEach( config => {
          if(state == config.status){//根据状态
            btnDesc = config.btnTxt;
            //顺便初始化当前按钮该调用的方法名
            item.submitMethod = config.btnMethod;
          }
        })
        //再遍历业务类型配置来覆盖按钮名称
        for( let key in BTN_CONFIG_BUSI){
          if(~busiType.indexOf(key)){//如果匹配得上配置
            BTN_CONFIG_BUSI[key].forEach( config => {
              if(state == config.status){//根据状态
                btnDesc = config.btnTxt;
                //顺便初始化当前按钮该调用的方法名
                item.submitMethod = config.btnMethod;
              }
            })
            break;
          }
        }
        return btnDesc;
      },
      //返回“我的”
      goPrev(){
        // 企微来的跳回企微
        if(this.source==='qw'){
          this.$router.push('/RemoteReceptionMenu')
          return;
        }
          this.$router.push('/my')
      },
      //跳转预受理订单详情
      goDetail(detail){
          this.$router.push({
              name: 'PreHandleOrderDetail',
              query: {
                  'detail': detail
              }
          })
      },
      //跳转预受理订单详情(给动态方法使用)
      goDetailDynamic(that,detail){
        that.$router.push({
            name: 'PreHandleOrderDetail',
            query: {
                'detail': detail
            }
        })
      },
      //根据状态发起不同提交请求
      doSubmit(item){
        let state = item.state;
        let busiType = item.busiType;
        //调用该调用的方法
        this.$options.methods[item.submitMethod](this,item);
      },
      //重新发送5G消息
      resend5gMessage(that,item){
          console.log("==========item.source==========",item.source);
          let sendReq = JSON.parse(item.sendParam);
          let sendUrl = '/xsb/personBusiness/fifthGMessage/h5useSendFifthGMsg';
          let msg = '重新发送5G消息';

          if(item.source==='qw_mini'){//add by xiaqt 20210210 来源是qw_mini，则请求3.1微信消息推送接口。
            sendReq = {
              busiType: item.busiType, //业务类型
              busiName: item.busiName, //业务名称
              orderId: item.orderId,//订单号
              qwGroupId: this.qwInfo.qwGroupId, //企业微信群ID,当来源是qw时有值
              qwOperId: this.qwInfo.qwOperId, //企业微信账号,当来源是qw时有值
              qwCustNumber: this.qwCustNumber, //企业微信客户手机号,当来源是qw时有值
              qwCustId: this.qwCustId, //企业微信客户账号,当来源是qw时有值
            }
            sendUrl = '/xsb/personBusiness/fifthGMessage/h5useSendWxMsg';
            msg = '微信消息推送';
          }
            console.log("==========sendReq==========",sendReq);
            console.log("==========sendUrl==========",sendUrl);

          that.$http.post(sendUrl,sendReq).then( res => {
              let {retCode,retMsg,data} = res.data;
              if(retCode == '0' && data.contributionId){//发送成功
                  let messageId = data.contributionId;
                  //调用更新关联的消息id
                  let updStateUrl = `/xsb/personBusiness/fifthGHandle/h5updOrder?orderId=${item.orderId}&state=0&messageId=${messageId}`;
                  that.$http.get(updStateUrl).then(res => {
                      let {retCode,retMsg,data} = res.data;
                      if(retCode == '0'){//更新成功
                          that.$toast(msg+'成功');
                          item.state = '0';
                      }else{
                          that.$toast(retMsg||msg+'失败');
                      }
                  })
              }else{
                  that.$alert(retMsg||msg+'失败');
              }
          }).catch(e => {
            that.$alert(msg+'异常:' + e);
          })
      },
      //刷新发送结果
      qrySendState(that,item){
          let qryUrl = `/xsb/personBusiness/fifthGHandle/h5qrySendMsgState?address=${item.phoneNumber}&senderMsgID=${item.messageId}&crmId=${item.crmId}&orderId=${item.orderId}&source=${item.source}`;
          that.$http.get(qryUrl).then(res => {
              let {retCode,retMsg,data} = res.data;
              if(retCode == '0'){//刷新结果成功
                  item.state = `${data}`;
                  that.$toast('刷新发送结果成功');
              }else{
                  that.$toast(retMsg || '刷新发送结果失败');
              }
          })
      },
      //下单
      submitOrder(that,item){

          let url = `/xsb/personBusiness/fifthGHandle/h5commonSubmit?orderId=${item.orderId}`;
          that.$http.get(url).then(res => {
              let {retCode,retMsg,data} = res.data;
              if(retCode == '0'){
                  that.$toast(retMsg || '办理成功');
                  item.state = '4';
              } else {
                  that.$alert(retMsg || '添加失败');
                  item.state = '5';
              }
              //根据业务处理结果更新预受理订单表的状态
              let updUrl = `/xsb/personBusiness/fifthGHandle/h5updOrder?orderId=${item.orderId}&state=${item.state}`;
              that.$http.get(updUrl);
          })
      },
      //删除订单
      deleteOrder(orderId){
        this.$messagebox.confirm('是否删除订单?', '温馨提示')
            .then(action =>{
              if(action == 'confirm'){
                this.delOrder(orderId);
              }
            }).catch(function (action) {

            });
      },
      delOrder(orderId){
          let updStateUrl = `/xsb/personBusiness/fifthGHandle/h5updHandleOrder`;
          let param = {
            "isDelete":1,
            "orderId":orderId
          }
          this.$http.post(updStateUrl,param).then( res => {
              let {retCode,retMsg,data} = res.data;
              if(retCode == '0'){
                  //刷新列表
                  this.getList(0);
              }else{
                  this.$alert(retMsg||'删除订单失败');
              }
          }).catch(e => {
            this.$alert('删除订单异常:' + e);
          })

      }
      ,
      //判断订单是否可以删除，状态为6和7或者超过3天未处理
      judgeDate(modifyDate,state){
        const nowTime = new Date();
        const time = new Date(modifyDate);
        let range = 24 * 60 * 60 * 1000 * 3;
        let flag = nowTime - time > range;
        if(state==6 || state==7 || flag){
          return true;
        }else{
          return false;
        }
      }
    }

  }
</script>
<style lang="less" scoped>
  .wrapper {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .iconfont{
    font-size: 6px;
  }

  .drop-ul{
    min-height: 180px;
  }

  .staging-top {
    background: -webkit-linear-gradient(bottom, #1170DB, #0079FD);
    height: 140px;
    border-top: 1px solid #E8E8E8;
    text-align: center;
    margin-top: 44px;
    left: 0;
    right: 0;
    z-index: 1;
    box-shadow: 0px 4px 10px 0px rgba(209, 209, 209, 0.5);
    border-radius: 0 0 18px 18px;
  }

  .search-box {
    margin: 1.5rem 20px 0px 20px;
    width: auto;

    .box {
      height: 35px;
      width: 100%;
      border-radius: 6px;
      text-indent: 20px;
    }

    .box::-webkit-input-placeholder {
      color: #999999;
      font-size: 14px;
    }

    .sousuo-icon{
      position: relative;
      top: -28px;
      width: 100%;
      .sousuo {
        position: absolute;
        color: #B2B2B2;
        font-size: 22px;
        right: 5px;
      }
    }


  }

  .whole {
    margin: 1rem 20px 0px 20px;
    color: #FFFFFF;

    .countDesc {
      font-size: 14px;
      float: left;
      margin-bottom: 5px;
    }

    .count {
      font-size: 15px;
    }

    .drop {
      font-size: 14px;
      float: right;
      margin-bottom: 5px;
    }
  }

.triangle {
    margin-top: -17px;
    margin-left: 42px;
    left: 20px;
    width: 0;
    height: 5px;
    border-left: 15px solid transparent;
    border-right: 15px solid transparent;
    border-bottom: 15px solid #4C4C4C;
    opacity: 0.9;
  }

  .dropMenu {
    position: absolute;
    width: 100px;
    height: auto;
    border-radius: 5px;
    background-color: #4C4C4C;
    margin-right: 15px;
    opacity: 0.9;
    text-align: center;
    color: #FFFFFF;
    font-size: 14px;
    left:260px;
    .dropState {
        line-height: 30px;
        height: 30px;
        margin-top: 5px;
      &.active {
        background-color: #919191;
      }
    }
  }

  .cont {
    z-index: 2;
    margin: -40px 20px 0px 20px;

    .item-li {
      margin-bottom: 15px;
      background-color: #FFFFFF;
      border-radius: 5px;
      padding: 10px ;

      .sub-id {
        color: #222222;
        font-weight: bold;
        font-size: 16px;
        letter-spacing: 0.7px
      }

      .sub-state {
        border: 1px solid #1F7AFF;
        color: #1F7AFF;
        font-size: 11px;
        width: auto;
        height: auto;
        margin-left: 10px;
        padding: 1px 5px;
        border-radius: 3px;
      }

      .yellow-state{
        border: 1px solid #F5A623;
        color: #F5A623;
      }

      .green-state{
        border: 1px solid #18BE32;
        color: #18BE32;
      }

      .red-state{
        border: 1px solid #F23737;
        color: #F23737;
      }

      .sub-time {
        float: right;
        font-size: 14px;
        font-weight: 600;
      }

      .sub-businame {
        color: #999999;
        font-size: 14px;
      }

      button {
        height: 25px;
        width: auto;
        font-size: 12px;
        color: white;
        background-color: #0079FD;
        border: none;
        border-radius: 5px;
        letter-spacing: 1px;
        padding: 2px 7px;
        word-break: keep-all;
      }
      .remind{
        background-color: #FFA823;
      }
      .green{
        background-color: #18BE32;
      }
      .red{
        background-color: red;
      }

      .sub-line{
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 10px;
      }
    }
  }

  .state-choose{
    position: absolute;
    z-index: 999;
  }
  .state-choose-panel{
      position: relative;
      top: -30px;
  }

  @media (max-width: 320px) {
      .state-choose{left: -50px;}
  }
  @media (max-width: 360px) and (min-width: 321px) {
      .state-choose{left: -25px;}
  }
  @media (max-width: 424px) and (min-width: 390px){
      .state-choose{left: 45px;}
  }
  @media (min-width: 425px){
      .state-choose{left: 75px;}
  }
</style>
