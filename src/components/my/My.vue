<template>
    <div class="my-md">
        <div class="my-top">
            <div class="my-top-name" @click="cancelAccount">我的</div>
            <div class="my-top-warning" @touchstart="getStartTime" @touchend.prevent="showMenuList">
                <!-- <span class="iconfont news iconwarning"></span>
                <span class="my-wnum">2</span> -->
            </div>
        </div>
        <div class="marcard-wrap" :class="{'bg':stationFlg}">
            <div class="my-card" :style="{background:'url(static/img/cardline.png) no-repeat center center,linear-gradient(147deg,#5196FF 0%, #2A68FF 100%)'}">
                <div class="my-card-inner">
                    <div class="my-cd-name">{{userInfo.operatorName | tuoMingName(myInfoTuoMingFlag)}}</div>
                    <div class="my-cd-station" @click="showStationList">
                        {{userInfo.stationName}}<i class="iconfont xingzhuanggangwei"></i>

                    </div>
                    <div class="my-cd-local">
                        <p>{{getLocation}}</p><span class="iconfont dingwei-icon dingweiweizhi" @click="getClientTrueLocal()"></span>
                    </div>
                    <div class="my-cd-num">{{userInfo.crmId | tuoMingCrmId(myInfoTuoMingFlag)}}/{{userInfo.servNumber | tuoMingNumber(myInfoTuoMingFlag)}}</div>
                </div>
                <div class="my-cd-planbtn" @click="planMenu = !planMenu" v-show="planMenuButton">
                    <span class="iconfont renwujihua iconrwjh"></span>
                    <span class="my-cp-txt">制定计划</span>
                </div>
                <div class="plan-model" v-show="planMenu">
                    <span class="sanjian"></span>
                    <ul class="icon-ul">
                        <li @click="planCk('village')" style="padding-top: 5px" v-show="planMenuShowArray['2']">
                            <div class="icon-wrapper xiaoqu-div">
                                <span class="iconfont xiaoquzhaofang"></span>
                            </div>
                            <span class="txt">小区</span>
                        </li>
                        <li @click="planCk('jike')" style="padding-top: 5px" v-show="planMenuShowArray['1']">
                            <div class="icon-wrapper jituan-div">
                                <span class="iconfont jituan"></span>
                            </div>
                            <span class="txt">集团</span>
                        </li>
                        <li @click="planCk('school')" style="padding-top: 5px" v-show="planMenuShowArray['5']">
                            <div class="icon-wrapper school-div">
                                <span class="iconfont xuexiao1"></span>
                            </div>
                            <span class="txt">高校</span>
                        </li>
                        <li @click="planCk('hot')" style="padding-top: 5px" v-show="planMenuShowArray['8']">
                            <div class="icon-wrapper hot-div">
                                <span class="iconfont qudao1"></span>
                            </div>
                            <span class="txt">热点</span>
                        </li>
                        <li @click="planCk('building')" style="padding-top: 5px" v-show="planMenuShowArray['6']">
                            <div class="icon-wrapper building-div">
                                <span class="iconfont louyu"></span>
                            </div>
                            <span class="txt">楼宇</span>
                        </li>
<!--                        <li @click="planCk('listGroup')" style="padding-top: 5px" v-show="planMenuShowArray['7']">-->
<!--                            <div class="icon-wrapper listGroup-div">-->
<!--                                <span class="iconfont fayuangaikuang"></span>-->
<!--                            </div>-->
<!--                            <span class="txt">清单集团</span>-->
<!--                        </li>-->
                        <li @click="planCk('shopAlong')" style="padding-top: 5px" v-show="planMenuShowArray['9']">
                            <div class="icon-wrapper yanjie-div">
                                <span class="iconfont xingzhuang17"></span>
                            </div>
                            <span class="txt">沿街</span>
                        </li>
                        <li @click="planCk('hotel')" style="padding-top: 5px">
                            <div class="icon-wrapper jiudian-div">
                                <span class="iconfont icon-yxj-city"></span>
                            </div>
                            <span class="txt">酒店</span>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="student-wrapper" v-show="relTelnum && selectStationId == CONSTVAL.STATION_SCHOOL">
                <img src="static/img/my-student.png" class="student-img">
                <span class="student-tel">动感大使：{{relTelnum}}</span>
            </div>
          <div class="student-wrapper" v-show="relTelnum && selectStationId != CONSTVAL.STATION_SCHOOL && selectStationId != CONSTVAL.STATION_JKSX ">
            <img src="static/img/my-student.png" class="student-img">
            <span class="student-tel">协销工号：{{relTelnum}}</span>
          </div>
          <div class="student-wrapper" v-show="relTelnum && selectStationId == CONSTVAL.STATION_JKSX">
            <img src="static/img/my-student.png" class="student-img">
            <span class="student-tel">客户经理工号：{{relTelnum}}</span>
          </div>
            <div v-show="stationFlg" class="station-wrap">
                <ul class="station-ul">
                    <li :key="idx"
                        v-for="(item,idx) in stationsList"
                        @click="selectStation(item)">
                        <span :class="{'active':selectStationId==item.stationId}">{{item.stationName}}</span>
                    </li>
                </ul>
                <div class="rel-tel-wrapper" v-show="relTelTitle">
                    <p class="title">{{relTelTitle}}</p>
                    <div class="ipt-wrapper" :class="{'empty-tel':emptyTel}">
                        <input type="tel" @focus="clearEmptyFlg" v-model="relTelnum" placeholder="请输入"/>
                    </div>
                </div>
                <p class="station-btn" @click="changeStation">确&nbsp;&nbsp;认</p>
            </div>
        </div>
        <div class="myx" >
            <!-- 两个li间距41px -->
            <div class="myorder">
                <ul class="myorder-ul">
                    <div class="myorder-title">我的订单</div>
                    <li class="myorder-li" style="margin-left :-10px" @click="goOrder('0')">
                        <img src="static/img/myorder1.png" class="icon-img"><p class="myorder-txt">&nbsp;&nbsp;全部</p>
                    </li>
                    <li class="myorder-li" @click="goOrder('1')">
                        <img src="static/img/myorder2.png" class="icon-img"><p class="myorder-txt">待处理</p>
                    </li>
                    <li class="myorder-li" @click="goOrder('2')">
                        <img src="static/img/myorder3.png" class="icon-img"><p class="myorder-txt">进行中</p>
                    </li>
                    <li class="myorder-li" @click="goOrder('3')">
                        <img src="static/img/myorder4.png" class="icon-img"><p class="myorder-txt">已处理</p>
                    </li>
                </ul>
            </div>
                  <!-- 两个li间距41px -->
            <div class="mytask">
                <ul class="mytask-ul">
                    <div class="mytask-title">我的任务</div>
                    <li class="mytask-li"  style="margin-left:-17px" @click="goTask('1')">
                        <img src="static/img/mytask_1.png" class="icon-img" >
                        <span class="myorder-txt-task" >全部
                            (<span class="myorder-txt-num">{{allNUm}}</span>)
                        </span>
                    </li>
                    <li class="mytask-li"  @click="goTask('2')">
                        <img src="static/img/mytask_3.png" class="icon-img" >
                        <span class="myorder-txt-task" >待处理
                            (<span class="myorder-txt-num">{{daichuli}}</span>)
                        </span>
                    </li>
                    <li class="mytask-li" style="margin-left:-14px" @click="goTask('3')">
                        <img src="static/img/mytask_2.png" class="icon-img" >
                        <span class="myorder-txt-task" >已处理
                            (<span class="myorder-txt-num">{{complete}}</span>)
                        </span>
                    </li>
                </ul>
            </div>
        </div>
        <div class="my-fn">
            <ul>

                <li class="my-fn-lis" @click="goAbout">
                    <div class="my-fl-icon">
                        <span class="iconfont a-Group427319075 iconrizhi"></span>
                    </div>
                    <div class="my-fl-txt">关于</div>
                    <div class="my-fl-target">
                        <span class="iconfont youjiantou iconrjt"></span>
                    </div>
                </li>
                <li class="my-fn-lis" @click="goSmartXin(userInfo)">
                    <div class="my-fl-icon">
                        <span class="iconfont zhinengxiaoxin"></span>
                    </div>
                    <div class="my-fl-txt">智能小信</div>
                    <div class="my-fl-target">
                        <span class="iconfont youjiantou iconrjt"></span>
                    </div>
                </li>

                <li class="my-fn-lis" @click='openFloatIconDialog'>
                    <div class="my-fl-icon">
                        <span class="iconfont gongzuotaijihuaanniu"></span>
                    </div>
                    <div class="my-fl-txt">悬浮按钮控制</div>
                  <div class="my-fl-target">
                    <span class="iconfont youjiantou iconrjt"></span>
                  </div>
                </li>

                <li class="my-fn-lis needsclick" @click="userRegister">
                    <div class="my-fl-icon">
                        <span class="iconfont shualianzhuce"></span>
                    </div>
                    <div class="my-fl-txt">刷脸注册</div>
                    <div class="my-fl-target" v-if="faceCount=='0'">
                        <span class="iconfont youjiantou iconrjt"></span>
                    </div>
                    <div class="my-txt" v-else>
                        已注册
                    </div>
                </li>

                <li class="my-fn-lis" v-show="yearBillFlg" @click="goBillPgae">
                    <div class="my-fl-icon">
                        <span class="iconfont yewu2"></span>
                    </div>
                    <div class="my-fl-txt">我的年度账单</div>
                    <div class="my-fl-target">
                        <span class="iconfont youjiantou iconrjt"></span>
                    </div>
                </li>
                <li class="my-fn-lis" v-show="isAndroid">
                    <div class="my-fl-icon">
                        <span class="iconfont xitongfanhuijinshouye"></span>
                    </div>
                    <div class="my-fl-txt">系统返回进首页</div>
                   <div class="my-fl-target2">
                        <mt-switch v-model="isBackExit" @change="setGoBackAndExit"></mt-switch>
                    </div>
                </li>
                <li class="my-fn-lis" @click="userLoginOut()">
                    <div class="my-fl-icon">
                        <span class="iconfont zhuxiaodenglu iconzhuxiao"></span>
                    </div>
                    <div class="my-fl-txt">注销登录</div>
                    <div class="my-fl-target">
                        <span class="iconfont youjiantou iconrjt"></span>
                    </div>
                </li>
<!--                 <li class="my-fn-lis" @click="test()">
                    <div class="my-fl-icon">
                        <span class="iconfont dengchu iconzhuxiao"></span>
                    </div>
                    <div class="my-fl-txt">选号入网（测试 ）</div>
                    <div class="my-fl-target">
                        <span class="iconfont youjiantou iconrjt"></span>
                    </div>
                </li> -->

            </ul>
        </div>
      <float-icon ref='floatIconRef' v-if='floatIconFlg'></float-icon>
    </div>
</template>

<script>
    import ClientJs from '@/base/clientjs'
    import {iEncrpt,iEncrptParam} from '@/base/encrptH5.js'
    import Storage from '@/base/storage'
    import Authenct from 'components/common/Authenticate/Authenticate'
    import NlDropdown from 'components/common/NlDropdown/dropdown.js'
    import {smartMixin} from '@/base/mixin'
    import { menuChain } from '@/base/mixins/menuChainMixin'
    import { menuLogRecord } from '@/base/request/commonReq.js'
    import axios from 'axios'
    const CryptoJS = require('crypto-js');
    import FloatIcon from './floatIconDialog/FloatIcon';


    export default {
        mixins: [smartMixin,menuChain],
        data() {
            return {
              pickerValue: '',
              userInfo: {},
              menuCount: 0,
              planMenu: false,//是否展示计划菜单
              stationFlg: false,//默认不展示岗位列表
              stationsList: [],//岗位列表
              selectStationId: '',//选中的岗位ID
              selectStationName: '',//选中的岗位名称
              relTelnum: '',//岗位关联的手机号或者工号
              emptyTel: false,//是否输入岗位关联的手机号或者工号
              faceCount: '0',//是否刷脸注册过，默认为没有注册过
              isAndroid: false,//判断是否为安卓端登录
              isBackExit: 0,//安卓端系统返回进首页0-关闭，1开启
              zkTypeList: [
                {id : 'yupeihaoMainTest', label: '预配号测试界面'},
                { id: 'inTransitDeviceReceive', label: '在途宽带设备补领', needAuth: true, hasPwd: '0', idCardWay: true },
                // { id: 'promotionRewardShopCart?promotionId=9100007370016&srcFrom=business&isDabao=X', label: '促销机卡互锁测试' },
                { id: 'invoiceDialogue', label: 'AI开票' },
                { id: 'keepTokenTestPage', label: '外围渠道保持长会话' },
                { id: 'thirdAudit', label: '二级未稽核查询' },
                { id: 'bandRealNameRecord', label: '宽带实名认证补录' },
                { id: 'joinTheNetworkTest?iccserial=21323&order_id=2025021747924854&operid=14150759&isUseDXFlag=1&iccid=FFFFFFFFFFFFFFFFFFFF&phoneNum=***********&cardno=21323&imsi=000000000000000', label: '无卡预配测试界面' },
                { id: 'incentiveDetail?flag=1', label: '激励明细' },
                { id: 'broadbandNumChange', label: '宽带换号', needAuth: true, hasPwd: '0', idCardWay: true },
                { id: 'networkAccessFusion?srcFrom=business', label: '融合快选（新入网）'},
                { id: 'testFaceCheck', label: '刷脸测试' },
                { id: 'perfectFusionQuickSelect', label: '完美一单融合快选', needAuth: true, hasPwd: '0', idCardWay: true },
                { id: 'mscHomePage', label: '品专店合伙人'},
                { id: 'jikeOrderSum', label: '订单查询'},
                { id: 'obsDemo', label: 'obsDemo'},
                { id: 'aiZhiTokenTest', label: '爱知TOKENtest'},
                // { id: "enterNetFusion?isHide=1", label: '入网融合'},
                { id: 'faceCheckDemo', label: '人证比对测试'},
                { id: 'twoWayUserOrderListPre?ams_account={"accnbr":"***********","daiweiId":"nt-lingsn","region":"11","comeFrom":"jksx","menuId":""}', label: '工单测试拉起' },
                { id: 'internetTvOpen?tvSort=1&srcFrom=twoPeopleCsViewIn', label: '电视非购物车', needAuth: true, hasPwd: '0', idCardWay: true },
              { id: 'qwSaoMa', label: '扫码' },
               { id: 'groupMachineList?groupId=***********&srcFrom=QueryGroup', label: '停复机测试'},
                {id:'zeroMenuJikeShare?oneId=**********|********',label: '生成订单二维码测试'},
                {id:'allProductOrder',label: '产品查询'},
                {id:'buryPoint',label: '埋点测试'},
                { id: 'inventoryAudit', label: '库存稽核' },
                { id: 'authTest', label: 'obs修复' },
                // { id: 'xqKuanDaiPre', label: '小区宽带（预受理）' },
                // { id: 'GroupSuppleMenu', label: '集团补资料入口' },
                // { id: 'netOrderGrabSheet', label: '电渠网约单' },
                // { id: 'yearBill2022', label: '年终总结' },
                { id: 'csViewInNew', label: '客户视图新', needAuth: true, hasPwd: '1', idCardWay: true },
                // { id: 'transferPageTest', label: '拉起测试' },
                // { id: 'groupFiling', label: '集团建档' },
                { id: 'groupDetailInfoTest', label: '查询集团详情' },
                { id: 'storeContractQry', label: '专卖店合约查询', needAuth: true, hasPwd: '0', idCardWay: true },
                { id: 'revenueQuery', label: '营业厅营收款查询' },
                { id: 'groupSelection', label: '集团组网' },
                // { id: 'imsGroupMenu', label: '固话开通' },
                { id: 'faceCheckTest', label: '刷脸测试' },
                // { id: 'SmartHomeNew', label: '全屋智能新', needAuth: true, hasPwd: '0' },
                // { id: 'mobileCall', label: '小信诊断' },
                { id: 'smartMock', label: '测试跳转' },
                // { id: 'indoorQuality', label: '户内质量检测工具' },
                { id: 'mBandTvFusion', label: '宽带电视融合', needAuth: true, hasPwd: '0', idCardWay: true },
                { id: 'finished', label: '甩单' },
                // { id: 'osjump', label: '一键投诉（OS）' },
                {
                  id: 'grpIMSBNTrueName?actionType=1&batchNo=12313213&batchNumbers=12313213131321',
                  label: '固话资料完善'
                },
                // { id: 'personCardComparison', label: '过户', needAuth: true, hasPwd: '0', idCardWay: true },
                { id: 'vaultAuth?busiType=test', label: '金库授权' },
                { id: 'yuPeiHaoTest', label: '预配号test' },
                // {id: 'netInterestsOpen',label:'组网权益开通', needAuth:true, hasPwd:'0', idCardWay:true},
                // {id: 'yiJiNewNew',label:'移机（爱知）',needAuth:true,hasPwd:'1',idCardWay:true},
                { id: 'personSwitch', label: '用户切换' },
                  { id: 'areaTest2', label: '地市拉起阿拉盯测试' },
                  { id: 'PersonalBusinessOpportunity2', label: 'O了商机' },
                  { id: 'h5QueryUserCityByIp', label: '没有归属运营商处理' },
                {
                  id: 'grpIMSBNTrueName?actionType=0&batchNo=12313213&batchNumbers=12313213131321',
                  label: '固化开通测试',
                  needAuth: false
                },
                {id: 'secondAudit', label: '二级营收稽核'},
                /*                    {id: 'jiKeinstallMaintain', label:'集客装维随销',needAuth:false},
                                    {id: 'secondAudit', label: '二级营收稽核'},
                                    {id: 'inventoryAudit', label: '库存稽核'},
                                            {id: 'authReadCard', label: '授权认证'},
                                    {id: 'voiceRemoteImei', label: '语音遥控器' ,needAuth:true,hasPwd:'0',idCardWay:true},
                                  {id: 'instantCustH5Menu',label:'即客H5'},
                                    {id: 'articleList', label: '学习专区'},*/
                { id: 'newCorporationDetails?gobackFlag=webview&groupId=2019110710000996', label: '清单摸排' },
                //     {id: 'softTerminalBook', label: '软终端', needAuth:true,hasPwd:'0',idCardWay:true},
                // { id: 'recordVideo', label: '视频录制' },
                     // {id: 'customerCareEnquiry', label: '客户关怀查询'},
                //     {id:'promotionEntry5G',label:'促销活动5G',needAuth:true,hasPwd:'0'},
                //     {id:'promotionEntry',label:'促销活动',needAuth:true,hasPwd:'0'},
                //     {id: 'callOutRecord',label: '外呼记录查询',needAuth: false},
                // {
                //   id: 'carryingCardBack?iccserial=10219520021780920000&order_id=2022102525025648&operid=14906088&source=1',
                //   label: '协转开户测试',
                //   needAuth: false
                // },
                // {
                //   id: 'cardBack?iccserial=1022ACC0002857090000&order_id=2022092224179875&operid=14115206',
                //   label: '选号入网开户测试',
                //   needAuth: false
                // },
                // {
                //   id: 'cardBack?iccserial=1021AC20034856560000&order_id=82022091710141623488&operid=14909147',
                //   label: '电渠入网开户测试',
                //   needAuth: false
                // },
                // {
                //   id: 'groupCardBack?iccserial=1022AC20003986470000&order_id=2023020727493564&operid=14102597',
                //   label: '集团证件入网开户测试',
                //   needAuth: false
                // },{id:'joinTheNetwork?order_id=2023041029188199',label:'无卡预配'},
                //     {id:'happyIndex',label:'无忧购机',needAuth:false},
                //     {id:'happyList',label:'无忧购机订单列表',needAuth:false},
                { id: 'kuandaiKaiTong', label: '宽带开通', needAuth: true, hasPwd: '0', idCardWay: true },
                //     {id:'marketEntry?type=terminal',label:'终端营销案',needAuth:true,hasPwd:'0'},
                //     {id:'token_okr',label:'okr'},
                //     {id:'xqKuanDai',label:'小区宽带'},
                //     {id:'paySuccess?order_id=202003161405090',label:'支付成功回调'},
                //     {id:'carryingCardBack?order_id=2019111410004886&iccserial=10179150107064380000',label:'身份证展示页面'},
                //     {id:'down',label:'无纸化下载'},
                //     // {id:'market',label:'融合受理',needAuth:true,hasPwd:'0'},
                //     {id:'relativeKaiTong',label:'亲情网开通',needAuth:true},
                //     {id:'relativeWeiHu',label:'亲情网维护',needAuth:true},
                //     {id:'relativeFuhaoExit',label:'亲情网副号退出',needAuth:true},
                //     {id:'groupImsList',label:'ims集团列表',needAuth:false},
                //     {id:'groupTrueName',label:'集团补资料',needAuth:false},
                //     {id:'groupCard',label:'集团读证',needAuth:false},
                //     {id:'businessBack',label:'宽带回退',needAuth:false},
                //     {id:'pcSign',label:'PC端签名',needAuth:false},
                //     {id: 'payTest', label: '支付能力测试', needAuth: false},
                //     {id: 'creditPayMarket', label: '花呗营销案', needAuth: false},
                //     {id: 'shareMore', label: '多终端', needAuth: true},
                //     {id: 'familyNetWork', label: '家庭组网', needAuth: true},
                //     {id: 'manageIndex', label: '泛渠道管理', needAuth: false},
                //     {id: 'heJiaGuHuaChooseTel?hasHejia=1&hasIms=0', label: '和家固话（新）开通ims', needAuth: true},
                //     {id: 'heJiaGuHuaChooseTel?hasHejia=0&hasIms=0', label: '和家固话（新）开通和家', needAuth: true},
                //     {id: 'groupHome', label: '我的集团', needAuth: false},
                //     {id: 'paperLess?busiArray=%5B%7B"recoid"%3A"200259595291746398","busiType"%3A"order_enter_net"%7D%5D&srcFrom=csView', label: '电渠抢单无纸化', needAuth: false},
                //     {id: 'groupDetailNew?flag=2&groupId=1419022002775090&taskId=ALD20200616982293-1419022002775090-1001161544&workIdFlag=true', label: '集团视图(个人)', needAuth: false},
                //     {id: 'groupDetailNew?flag=1&groupId=1490000025574364&taskId=ALD20200616982286-1490000025574364-1001161511&workIdFlag=true', label: '集团视图', needAuth: false},
                //     {id: 'schoolMarket?stuTelnum=12312340001&stuName=11', label: '校园预约', needAuth: false},
                // {id: 'smartNetWork', label: '智能组网', needAuth: true },
                // {id: 'writeCard', label: '写卡', needAuth: false},
                // {id: 'yijiNew', label: '移机', needAuth: true},
                { id: 'collegeWelcomeArea', label: '高校专区', needAuth: false },
                // {id: 'oneNumMoreBand', label: '一号多宽', needAuth: true,hasPwd:'0'},
                // {id: 'myGb', label: '政企我的', needAuth: false,hasPwd:'0'},
                // {id: 'adminMyGb', label: '政企客户经理', needAuth: false,hasPwd:'0'},
                // {id: 'invoiceList', label: '开具发票', needAuth: true,hasPwd:'0'},
                // {id: 'grabSheetNew?doGradSheetRefresh=1', label: '电渠抢单(新)', needAuth: false},
                // {id: 'accountcancel', label:'销户',needAuth:false},
                // {id: 'accountReopen', label:'销户重开',needAuth:false},
                { id: 'installMaintain', label: '掌上家客', needAuth: false },
                { id: 'vcardQueryInfo', label: '物联网卡基础信息查询' },
                // { id: 'vcardQueryAll', label: '物联网卡一键查询' },
                // { id: 'perfectOne', label: '完美一单', needAuth: true, hasPwd: '0', idCardWay: true },
                { id: 'reportZone', label: '报表专区' },
                // {id:'chFusionAccept',label:'CH融合受理',needAuth:true,hasPwd:'0',idCardWay:true},
                // {id:'cbFusionAcceptSelect',label:'CB融合受理',needAuth:true,hasPwd:'0',idCardWay:true},
                //  {id:'setTopBoxChange',label:'机顶盒更换',needAuth:true,hasPwd:'0',idCardWay:true},
                { id: 'refershWechatToken', label: '小程序刷新token' },
                { id: 'hostingPlatform',label:'托管平台接口调用'},
                { id: 'authSingleOn', label:'H5一键授权登录'},
                { id: 'xiaoshouyi', label: '销售易' },
                { id: 'dictList', label: 'dict渠道管理' },
                {id:'gateWayTest',label:'阿拉盯模拟拉外围'},
                {id:'routerPage',label:'路由跳转页面'},
                {id:'busiReportAgent',label:'商机上报智能体'}
              ],
              startTime: '',//点击时间
              planMenuShowArray: {
                "1": true,
                "2": true,
                "3": true,
                "4": true,
                "5": true,
                "6": true,
                "7": true,
                "8": true,
                "9": true
              },
              yearBillFlg: false,//默认不展示年度账单
              planMenuButton: true, //制定计划按钮是否出现
              stationfilterList: [], //需要过滤的岗位列表
              allNUm: "0",
              daichuli: "0",
              jumpOSUrl: "",
              complete: "0",
              floatIconFlg:false,
              regionList:[{id:'11',label:'苏州'},{id:'12',label:'淮安'},{id:'13',label:'宿迁'},{id:'14',label:'南京'},{id:'15',label:'连云港'}
                ,{id:'16',label:'徐州'},{id:'17',label:'常州'},{id:'18',label:'镇江'},{id:'19',label:'无锡'},{id:'20',label:'南通'},
                {id:'21',label:'泰州'},{id:'22',label:'盐城'},{id:'23',label:'扬州'},{id:'99',label:'江苏省'}],
              myInfoTuoMingFlag:true,//,脱敏
            }
        },
        mounted(){
            this.getUserInfo();
            this.checkIfRegist();
            // if(!this.CONSTVAL.GIT_UPLOAD_SWITCH.NB_VAS20210128_98){//阿拉盯年度账单开关打开
            this.showYearBill();
            // }
            this.checkCurVersion();
            this.getGoBackAndExitStatus();
            window['backHomeAndExit'] = (result) => {
                this.isBackExit = result.backHomeAndExit == '0' ? 0 : 1;
            };
          // 截屏权限回调方法
          window['screenShotCallbackfunc'] = (result) => {
            console.log('截屏方法结果', result);
          };
        },
        activated(){
            //便利店改造
           this.getUserInfo();
           this.showYearBill();
            //清除调用链业务菜单流水，防止被错误采集 add by qhuang 20221025
            this.$pointLesslog && this.$pointLesslog.setChainBusiSeq('');
          // 关闭截屏权限
          ClientJs.screenShot(0);
        },
        methods: {
            // 打开悬浮图标控制
            openFloatIconDialog(){
              this.floatIconFlg = true;
            },
            closeDialog(){
              this.floatIconFlg = false;
            },
            getClientTrueLocal(){
                ClientJs.getLocation('','getLocationInfoCb');
            },
            //查询操作者任务状态数量
            qryTaskNum(){
                this.userInfo = Storage.session.get('userInfo');
                let param = {
                    staffId:this.userInfo.staffId,
                    servNumber:this.userInfo.servNumber
                }
                this.$http.post('/xsb/personBusiness/myTask/h5QryTaskNum',param).then(res =>{
                    let{retCode,retMsg,data} = res.data;
                    if(retCode == '0'){
                        this.allNUm = data.allNUm;
                        this.daichuli = data.daichuli;
                        this.complete = data.complete;
                    }else{
                        this.$toast(retMsg || '查询操作者任务状态数量查询失败');
                    }
                })
            },

            //注销账户，点击提示（2个弹窗）
            cancelAccount(){
                this.$messagebox.confirm('确认注销账户吗？','温馨提示')
                .then((action) => {
                    this.$messagebox.confirm('请至渠道中心进行注销','温馨提示')
                    .then((action) => {

                    }).catch(() => {});
                }).catch(() => {});
            },
            checkIfRegist(){
                //查询是否刷脸注册过
                let isRegistFlg = Storage.get(`faceRegisterFlg_${this.userInfo.servNumber}`);
                if(!isRegistFlg){
                    //查询注册记录
                    let url = '/xsb/ability/faceCollect/h5qryFaceInfo';
                    this.$http.get(url).then(res => {
                        let {retCode,retMsg,data} = res.data;

                        if(retCode == '0'){
                            this.faceCount = data.faceCount;//注册次数
                        } else {
                            this.$toast(retMsg || '查询人脸注册记录失败')
                        }
                        if(this.faceCount > 0){
                            Storage.set(`faceRegisterFlg_${this.userInfo.servNumber}`,true);
                        }
                    });
                } else {
                    this.faceCount = 1;
                }
            },
            //用户注册
            userRegister(){
                // if(this.faceCount == '0'){
                    //跳刷脸注册的页面 20200426 修改为无限注册
                    this.$router.push('/faceRegister?srcFrom=my');
                // }
            },
            getStartTime(){
                this.startTime = new Date().getTime();
            },

          getJumpOSUrl(){
            let _this=this;
            this.$http.get('/xsb/personBusiness/jikeHall/h5getOSJumpUrl').then((res)=>{
              if(res.data.retCode == '0'){
                _this.jumpOSUrl = res.data.data;
                _this.jumpToOSPlatForm();
              } else {
                _this.$alert('获取跳转OS一体化系统服务平台的地址失败');
              }
            }).catch((response)=>{
            })
          },

          //跳转OS一体化系统服务平台
          jumpToOSPlatForm(){
            let uinfo = Storage.session.get('userInfo');
            let param={
              "province":"250",
              "region":uinfo.region,
              "operatorName":uinfo.operatorName,
              "phoneNumber":uinfo.servNumber,
              "operId":uinfo.crmId,
              "unEncrpt": true
            };
            let url = '/group/business/contractInfo/h5getComplaintTokenFromOS';
            this.$http.post(url, param).then((res) => {
              let { retCode, retMsg, data } = res.data
              if (retCode == '0') {
                let token = data.token //从OS获取的token
                let jumpUrl = this.jumpOSUrl + token+"&noCheck=false";
                console.info(jumpUrl)
                this.service = axios.create({
                  headers: {
                    'Content-Type': 'application/json;charset=UTF-8',
                    'Access-Control-Expose-Headers': 'Authorization',
                    'Access-Control-Allow-Origin': '*',
                  },
                })
                this.service({
                  method: 'get',
                  url: jumpUrl,
                  headers: {
                    'mesh-network-bus-type': data.busType,
                    'x-sg-scenario-code': '',
                    'x-sg-scenario-version': '',
                    'x-sg-ability-code': '',
                    'x-sg-api-code': data.apiCode,
                    'x-sg-api-version': data.apiVersion,
                    'x-sg-app-key': data.appKey,
                    'x-sg-dest-app-key': '',
                    'x-sg-timestamp': data.timestamp,
                    'x-sg-scenario-id': data.scenarioId,
                    'x-sg-message-id': data.messageId,
                    'x-sg-route-type': '',
                    'x-sg-route-value': data.routeValue,
                    'x-sg-test': data.test,
                    'x-sg-md5-secret': data.md5Secret,
                    'x-sg-spanid': data.spanid,
                    'noCheck': 'false',
                  },
                }).then((res) => {
                  console.info(res)
                  let location = res.headers.location
                  console.info(location)
                  ClientJs.openWebKit(encodeURIComponent(location), '', '1', '0', '', '', '', '', '', '', '')
                })
              } else {
                this.$alert(retMsg)
              }
            })
          },
            h5QueryUserCityByIp(){
                this.$http.post('/xsb/gridCenter/familyDiagnosis/h5QueryUserCityByIp')
            },
            showMenuList(){

                const nowTime = new Date().getTime();
                if(nowTime - this.lastClickTime < 3000){
                    this.menuCount ++;
                } else {
                    this.menuCount = 0;
                }
                this.lastClickTime = nowTime;


                if(this.menuCount >= 3 || nowTime - this.startTime > 1000 ) {
                    this.menuCount = 0;
                    let self = this;
                    NlDropdown({
                        confirmBtn: false,
                        datalist: self.zkTypeList
                        }, (retVal) => {
                      if(retVal.id=='routerPage'){
                        self.$router.push({
                          path:"/routerPage",
                        });
                        return;
                      }
                      if(retVal.id=='busiReportAgent'){
                          self.$router.push({
                                path:"/busiReportAgent",
                            });
                          return;
                      }
                        if(retVal.id=='h5QueryUserCityByIp'){
                            this.h5QueryUserCityByIp()
                            return;
                        }
                      if(retVal.id=='netOrderGrabSheet'){
                        self.$router.push({
                          path:"/netOrderGrabSheet",
                          query: {
                            doGradSheetRefresh: '1' // 直接返回，不刷新
                          }
                        });
                        return;
                      }
                      if(retVal.id=='osjump'){
                        this.getJumpOSUrl()
                      }
                      if(retVal.id == 'xiaoshouyi'){
                        let url = "/xsb/personBusiness/grabsheet/h5searchOrgid";
                        let paramObj = {
                          region: this.region,
                          crmId: this.crmId,
                        };
                        this.$http.post(url, paramObj).then(res => {
                          let result = res.data;
                          if ('0' == result.retCode) {
                            let orgName = result.data.orgName;
                            let url1 = "/xsb/personBusiness/xiaoshouyi/h5hcyLogin";
                            let obj = {
                              "func": "hcyLogin:wgtSetToken",
                              "timestamp": Date.now().toString(),
                              "userName": this.userInfo.servNumber,
                              "busicode": "jsydyp001",
                            };
                            console.info('xsy param',obj)
                            this.$http.post(url1, obj).then((res) => {
                              let { retCode, retMsg, data } = res.data
                              console.info('xsy data',data)
                              if (retCode == '0') {
                                let bc = 'jsydyp001';
                                // let un = this.aesEncrypt(this.userInfo.servNumber, "ovmlaQvu7iTGuv4sHRWz");
                                let un = data.username;
                                let cn = orgName;
                                let pn = this.regionList.find(item => item.id == this.userInfo.region).label;
                                let en = data.en;
                                let xsyToken = data.xsyToken;
                                let opUrl = `https://pcweb.mmarket.com/ydyp/index.html?bc=${bc}&un=${un}&cn=${cn}&pn=${pn}&en=${en}&xsyToken=${xsyToken}`;
                                console.info('opUrl',opUrl);
                                ClientJs.openWebKit(encodeURIComponent(opUrl), '', '0', '', '', '', '', '', '', '', '');//没有头的链接
                              } else {
                                this.$alert(retMsg);
                              }
                            })
                          } else {
                            this.$alert(result.retMsg);
                          }
                        });
                      }
                        if(retVal.id=='installMaintain'){
                            this.userInfo.dwUser='nj-suntao';
                            this.userInfo.dwMobile='13621597771';
                            this.userInfo.dwName='小明';
                            this.userInfo.dwUserRole='dw_1002';
                            this.userInfo.comeFromc='ams';
                             Storage.session.set('userInfoNew',this.userInfo);
                             Storage.session.set('userInfo',this.userInfo);
                             self.$router.push('/installMaintain');
                        }
                        if (retVal.id=='vcardQueryInfo'){
                          Storage.session.set('VCardFlag',true);
                          self.$router.push(`/${retVal.id}`);
                        }
                        if (retVal.id=='vcardQueryAll'){
                          let par = {
                            'region': '14',
                            'msisdn': '913201172609903852',
                            'crmId': '14'
                          }
                          this.$http.post("/xsb/personBusiness/iotCard/h5queryAll", par).then(res => {
                            if (res.data.retCode == '0') {

                            }
                          });
                        }
                      if (retVal.id.indexOf('grpIMSBNTrueName') != -1) {
                        let groupInfo = {
                          'groupName': '测试测试',
                          'groupId': '913201172609903852',
                          'address': '地址得自DWA'
                        }
                        Storage.session.set('groupInfo', groupInfo);
                      }

                      if (retVal.id == 'reportZone') {
                        self.$router.push({
                            path: '/reportZone',
                        })
                      }

                      if (retVal.needAuth) {
                        console.info(retVal)
                        Authenct({
                          popFlag: true,
                          hasPwd: retVal.hasPwd,
                          telnum: retVal.telnum,
                          idCardWay: retVal.idCardWay
                        }, function(obj) {
                          self.$router.push(`/${retVal.id}`);
                        });
                      } else {
                        Storage.session.set('choosed', '20071413451467732818');
                        let uinfo = this.userInfo;
                        if (retVal.id == 'token_okr') {
                          if (!uinfo.oa) {
                            this.$alert('请联系管理员配置OA账号');
                            return;
                          }
                          let client = /android|harmony/gi.test(navigator.userAgent) ? 'ANDROID' : 'IOS'
                          let url = `/xsb/api-user/tokenAuthen/h5genToken.do?opId=okr&prviId=0&phoneNumber=${uinfo.servNumber}`;
                          url += `&regionId=${uinfo.region}&operId=${uinfo.staffId}&stationId=${uinfo.stationId}&clientType=${client}`;
                          this.$http.get(url).then((response) => {
                            let { retCode, retMsg, data } = response.data;
                            console.info(data);
                            if (retCode === '0') {
                              let opUrl = data.opUrl;
                              ClientJs.openWebKit(encodeURIComponent(opUrl), '', '0', '', '', '', '', '', '', '', '');
                            } else {
                              this.$alert(retMsg || '拉起TOKEN_OKR失败')
                            }
                          }).catch((response) => {
                          });
                        } else {
                          self.$router.push(`/${retVal.id}`)
                        }
                      }
                    });
                }
            },
            //任务计划按钮点击
            planCk(flg) {
                if(flg === 'hot'){//热点
                  Storage.session.set("makePLanFlag","my");
                  this.$router.push('/customScenePlan');
                } else if(flg === 'village'){//小区
                    this.$http.get(`/xsb/personBusiness/stall/h5StallCreateSwitch`).then((response) => {
                        let {retCode,retMsg,data} = response.data;
                        Storage.session.set("makePLanFlag","my");
                        if (retCode === '0') {
                            this.$router.push({name: 'VillagePlanNew'});
                        } else {
                            this.$router.push({name: 'VillagePlan'});
                        }
                    })
                } else if(flg === 'school'){//高校
                    this.$router.push({name: 'CollegePlan'});
                } else if(flg === 'jike'){//集客
                  Storage.session.set("makePLanFlag","my");
                    let param0 = {
                        switchType: 'group_fanghzuang_flag'
                    }
                    this.$http.post('/xsb/personBusiness/gridSand/h5GridMarketSwitch', param0).then((res) => {
                        if (res.data.retCode == 0) {
                            this.$router.push({
                                path: '/DrawPlanNew'
                            });
                        }else {
                            this.$router.push({name: 'DrawPlan'});
                        }
                    }).catch(() => {
                        this.$router.push({name: 'DrawPlan'});
                    })

                } else if(flg === 'building'){//集客
                    this.$router.push({name: 'BuildingViewPlan'});
                } else if(flg === 'listGroup'){//清单集团
                    this.$router.push({name: 'ListGroupPlan'});
                } else if(flg === 'shopAlong'){//沿街
                  this.$router.push({name: 'streetListNew'});
                } else if(flg === 'hotel'){ // 酒店
                  this.$router.push({name: 'hotelPlan'});
                }
              this.planMenu = false;
                // //客户经理进入集客制定计划
                // if(this.selectStationId == this.CONSTVAL.STATION_KHJL){
                //     this.$router.push({name: 'DrawPlan'});
                // } else if(this.selectStationId == this.CONSTVAL.STATION_JIATING
                //         || this.selectStationId == this.CONSTVAL.STATION_SHEQU
                //         || this.selectStationId == this.CONSTVAL.STATION_QUDAO){
                //     //家庭营销、社区营销、渠道经理岗位可以制定小区计划
                //     this.$router.push({name: 'VillagePlan'});
                // }else if(this.selectStationId == this.CONSTVAL.STATION_SCHOOL){//校园岗位
                //     this.$router.push({name: 'CollegePlan'});
                // } else {
                //     //临时添加默认都跳集客制定计划
                //     this.$router.push({name: 'VillagePlan'});
                // }
            },
            //切换岗位
            showStationList(){
                this.stationFlg = !this.stationFlg;
            },
            userLoginOut(){
                this.$messagebox.confirm('确认注销登录吗？','温馨提示')
                .then((action) => {
                    ClientJs.userLoginOut();
                }).catch(() => {});
            },
            getUserInfo(){
                this.userInfo = Storage.session.get('userInfo');
                this.stationsList = this.userInfo.stations;
                this.selectStationId = this.userInfo.stationId;
                this.selectStationName = this.userInfo.stationName;
                this.relTelnum = this.userInfo.relTelnum;
                this.stationfilterList = this.userInfo.amsStationList;
                this.changeplanMenu();
                //初始化为集客随销 或者校园营销
                if(this.selectStationId == this.CONSTVAL.STATION_JKSX || this.selectStationId == this.CONSTVAL.STATION_SCHOOL){
                    if(!this.relTelnum){
                        this.stationFlg = true;
                    }
                }
            },

            //吐槽专区
            feedBack(){
                let webUrl = Storage.get('webUrl');
                let url = `${webUrl}/feedBack.do?nextStep=h5judgeUser&userId=${this.userInfo.crmId}&region=${this.userInfo.region}`;
                ClientJs.openWebKit(iEncrpt(url), '', '0', '', '', '', '', '', '', '', '');
            },
            selectStation(item){
              if(item.stationId == this.CONSTVAL.STATION_JKSX) {
                this.$messagebox
                ({
                  $type: 'prompt',
                  title: '温馨提示',
                  message: '客户经理工号(必填)',
                  closeOnClickModal: false,   //点击model背景层不关闭MessageBox
                  showCancelButton: true,
                  inputValidator: function(v) {
                    if (!v || v == '') {
                      return '请输入客户经理工号';
                    }
                  },
                  showInput: true,
                  inputType: 'tel'
                }).then(({ value, action }) => {
                  this.relTelnum = value;
                  this.selectStationId = item.stationId;
                  this.selectStationName = item.stationName;
                  this.changeStation();
                }).catch(() => {
                });
              }else{
                this.relTelnum = '';
                this.selectStationId = item.stationId;
                this.selectStationName = item.stationName;
              }
            },
            //岗位关联手机号的输入验证置false
            clearEmptyFlg(){
                this.emptyTel = false;
            },
            //岗位列表中的确认按钮
            changeStation(){
                // //判断是否是pad情况，如果是则判断岗位角色
                // if(Storage.session.get('isPad') =='1'){
                //     if(this.selectStationId != this.CONSTVAL.STATION_GB && this.selectStationId != this.CONSTVAL.STATION_AGB
                //     && this.selectStationId != this.CONSTVAL.STATION_KHJL && this.selectStationId != this.CONSTVAL.STATION_HUWAI){
                //         this.$alert('暂未开放');
                //         return;
                //     }
                // }

                //如果是集客随销 或者 校园营销
                if(this.selectStationId == this.CONSTVAL.STATION_JKSX || this.selectStationId == this.CONSTVAL.STATION_SCHOOL){
                    if(!this.relTelnum){
                        let msg = '请输入客户经理工号';
                        if(this.selectStationId == this.CONSTVAL.STATION_SCHOOL){
                            msg = '请输入手机号';
                        }
                        this.emptyTel = true;
                        this.$alert(msg);
                        return;
                    }
                    if(this.selectStationId == this.CONSTVAL.STATION_SCHOOL){
                        if(this.relTelnum == this.userInfo.servNumber){//输入的手机号和登录的手机号一样
                            //不做处理
                        } else if(/^((1)+\d{10})$/.test(this.relTelnum)){//
                            //请求服务端，调用CRM接口判断此号码是否有效
                            this.checkStudentPhone()
                            return;
                        } else {
                            this.$alert('请输入正确的手机号');
                            return;
                        }
                      this.switchStation();
                    }else{
                      this.getRelTelnumInfo();
                    }
                } else {
                    //协销人员的录入
                    if (this.stationfilterList && this.stationfilterList.length > 0) {
                        let i = 0;
                        for(;i<this.stationfilterList.length; i++){
                            if(this.selectStationId ==  this.stationfilterList[i].value){
                                break;
                            }
                        }
                        if(i == this.stationfilterList.length && this.selectStationId != this.stationfilterList[i-1].value){
                            this.relTelnum = '';
                        }
                    }else{
                        this.relTelnum = '';
                    }
                    if(this.relTelnum){
                      this.getRelTelnumInfo();
                    }else{
                      this.switchStation();
                    }
                }
            },
          //操作员信息
          getRelTelnumInfo(){
            let xiexiaoName="协销工号";
            if(this.selectStationId == this.CONSTVAL.STATION_JKSX){
              xiexiaoName="客户经理工号";
            }
            let url = `/xsb/personBusiness/chooseTelEnterNet/h5QryRelTelnumInfo?relTelnum=${this.relTelnum}`;
            this.$http.get(url).then((res)=>{
                let data =res.data;
                if(data.retCode == '0'){
                  let d = data.data;
                  let name = d.opratorName;
                  let opratorName =name;
                  if(name){
                    if(name.length > 2){
                      opratorName =name[0] + '*'.repeat(name.length - 2) + name.slice(-1)
                    }else{
                      opratorName=  '*' + name.slice(-1);
                    }
                  }
                  this.$messagebox({
                    title: '温馨提示',
                    message: '当前输入的'+xiexiaoName+'【'+this.relTelnum+'】姓名是：'+opratorName+'，请确认是否继续？',
                    showConfirmButton: true,
                    closeOnClickModal: false,
                    showCancelButton: true,
                    closeOnPressEscape: false,
                    confirmButtonText: '是',
                    cancelButtonText: '否',
                  }).then((action) => {
                    if (action == 'confirm') {
                      this.switchStation();
                    }else{
                      this.relTelnum = this.userInfo.relTelnum;
                      this.selectStationId = this.userInfo.stationId;
                      this.selectStationName = this.userInfo.stationName;
                    }
                  });
                }else{
                  this.$messagebox({
                    title: '温馨提示',
                    message: '当前输入的'+xiexiaoName+'【'+this.relTelnum+'】查询不到人员信息，请确认是否继续？',
                    showConfirmButton: true,
                    closeOnClickModal: false,
                    showCancelButton: true,
                    closeOnPressEscape: false,
                    confirmButtonText: '是',
                    cancelButtonText: '否',
                  }).then((action) => {
                    if (action == 'confirm') {
                      this.switchStation();
                    }else{
                      this.relTelnum = this.userInfo.relTelnum;
                      this.selectStationId = this.userInfo.stationId;
                      this.selectStationName = this.userInfo.stationName;
                    }
                  });
                }

              }).catch((err)=>{
              this.$messagebox({
                title: '温馨提示',
                message: '当前输入的'+xiexiaoName+'【'+this.relTelnum+'】查询不到人员信息，请确认是否继续？',
                showConfirmButton: true,
                closeOnClickModal: false,
                showCancelButton: true,
                closeOnPressEscape: false,
                confirmButtonText: '是',
                cancelButtonText: '否',
              }).then((action) => {
                if (action == 'confirm') {
                  this.switchStation();
                }else{
                  this.relTelnum = this.userInfo.relTelnum;
                  this.selectStationId = this.userInfo.stationId;
                  this.selectStationName = this.userInfo.stationName;
                }
              });
            });
          },

            //调用服务端切换岗位
            switchStation(){
                this.stationFlg = false;
                let oldStationId = this.userInfo.stationId;
                let url = `/xsb/api-user/user/h5switchStation?stationId=${this.selectStationId}&imei=${this.userInfo.imei}`;
                this.$http.get(url).then((res) => {
                    let data = res.data;
                    if(data.retCode == '0'){
                        //2023.10.27 cyy 如果原来是装维随销、装维随销（新）,代维信息清空
                        if (oldStationId == '10000601' || oldStationId == '10009102') {
                          this.userInfo.dwUser = '';
                          this.userInfo.dwMobile = '';
                          this.userInfo.dwName = '';
                        }
                        this.userInfo.stationId = this.selectStationId;
                        this.userInfo.stationName = this.selectStationName;
                        this.userInfo.crmId = data.data.crmId;
                        this.userInfo.relTelnum = this.relTelnum;
                        this.userInfo.viewInfoList = data.data.viewInfoList;
                        Storage.session.set('userInfoNew',this.userInfo);
                        Storage.session.set('userInfo',this.userInfo);
                        //2025.04.24 wjx 增加调用客户端更新userInfo的岗位编码
                        this.updateStationInfo(this.selectStationId)
                        //2023.10.18 cyy 对于装维随销、装维随销（新）岗位，塞入装维人员信息
                        if(this.userInfo.stationId == '10000601' || this.userInfo.stationId == '10009102'){
                            this.qryZwPerson();
                        }
                        if(this.userInfo.stationId==this.CONSTVAL.STATION_BLD){//便利店业主跳到首页
                            this.$router.push("/bldMaintain");
                        }else if(this.userInfo.stationId==this.CONSTVAL.STATION_GB){//政企室经理
                            this.$router.push("/adminGovernment");
                        }else if(this.userInfo.stationId==this.CONSTVAL.STATION_AGB){//政企客户经理
                            this.$router.push("/governmentIndex");
                        }
                        /*保存切换的stationId*/
                        this.$store.commit('stationChangeModule/rewriteStation',this.userInfo.stationId);

                        this.changeplanMenu();
                        if(this.userInfo.crmId){
                            this.qryTaskNum();
                        }

                        this.handleStationSwitch()
                        //更新悬浮按钮权限
                        this.$store.dispatch('floatIconModule/getMenuList');
                        //更新home办理灵犀助手权限
                        this.$store.dispatch('stationChangeModule/getHomeBtnMenuList');
                    }else{
                        this.$alert(data.retMsg || '切换岗位服务异常');
                    }
                })
            },

            // 当前岗位开关控制
            async handleStationSwitch() {
                const res = await this.$http.post('/xsb/api-user/menu/h5GetStationSwitch', {
                    stationId: this.userInfo.stationId
                })
                console.info(res, '当前岗位开关控制');

                if (res) {
                    const data = res.data

                    if (data && data.retCode == '0') {
                    // 有权限
                    Storage.session.set('stationSwitch', 1)
                    } else {
                    // 无权限
                    Storage.session.set('stationSwitch', 0)
                    }
                }
            },
            //2023.10.18 cyy 查询装维人员信息
            qryZwPerson() {
              let param = {
                xs_mobile: this.userInfo.servNumber,//行商手机号
                regionId: this.userInfo.region,//地市
              };
              let url = '/xsb/personBusiness/zwOrder/h5queryZwPerson';
              this.$http.post(url, param).then((res) => {
                if (res.data.retCode == '0') {
                  if(res.data.data && res.data.data.zwryList &&  res.data.data.zwryList.length > 0) {
                    this.userInfo.dwUser = res.data.data.zwryList[0]?res.data.data.zwryList[0].zw_id:'';
                    this.userInfo.dwMobile = res.data.data.zwryList[0]?res.data.data.zwryList[0].zw_mobile:'';
                    this.userInfo.dwName = res.data.data.zwryList[0]?res.data.data.zwryList[0].zw_name:'';
                    Storage.session.set('userInfo',this.userInfo);
                    Storage.session.set('userInfoNew',this.userInfo);
                  }
                }
              }).catch((err) => {
              })
            },
            //请求服务端，调用CRM接口判断此号码是否有效
            checkStudentPhone(){
                //是否校验学生手机号开关
                let param = {
                    busiType:'studentPhoneCheck_jq'
                }
                let self = this;
                this.$http.post('/xsb/ability/businessLimit/h5QryBusiPermission',param).then(res => {
                    //注意 retCode=0 不校验号码
                    if(res.data.retCode != '0'){
                        param = {
                            regionId:this.userInfo.region,
                            operId:this.userInfo.crmId,
                            phone:this.relTelnum
                        }
                        //校验学生手机号
                        self.$http.post('/xsb/api-user/user/h5verifyStudentPhone',param).then(res => {
                            if(res.data.retCode == '0'){
                                self.switchStation();
                            } else {
                                self.$alert(res.data.retMsg || '学生手机号校验失败')
                            }
                        })
                    } else {
                        self.switchStation();
                    }
                })
            },
            //跳关于
            goAbout(){
                this.$router.push({name: 'About'});
            },
            test(){
                let vconDom = document.getElementById('__vconsole');
                vconDom.classList.remove('hide');
                this.$router.push('/realNameCertify');
            },
            updateStationInfo(newStationId){
              if(newStationId){
                ClientJs.updateStationId(newStationId);
              }else{
                console.log('未获取到最新岗位编码')
              }
            },
            //判断点击计划制定后菜单的显示
            changeplanMenu() {
                let url = "/xsb/api-user/viewType/h5QryViewType";
                let params = {
                    stationId: this.userInfo.stationId
                }
                this.$http.post(url, params).then(res => {
                    if(res.data.retCode == "0"){
                        if(res.data.data.key == '0') {
                            return;
                        }else if(res.data.data.key == '1') {
                            this.planMenuShowArray = {"1": false, "2": false, "3": false, "4": false, "5": false, "6": false, "7": false, "8": false,"9":false};
                            if(res.data.data.viewTypeList.indexOf("4") != -1) {
                                res.data.data.viewTypeList.splice(res.data.data.viewTypeList.indexOf("4"),1)
                            }
                            if(res.data.data.viewTypeList.indexOf("3") != -1) {
                                res.data.data.viewTypeList.splice(res.data.data.viewTypeList.indexOf("3"),1)
                            }
                            if(res.data.data.viewTypeList.length > 0) {
                                this.planMenuButton = true;
                                res.data.data.viewTypeList.forEach(items => {
                                    this.planMenuShowArray[items] = true;
                                })
                            }else {
                                this.planMenuButton = false;
                            }
                        }
                    }else {
                        // this.$alert(res.data.retMsg || "请求失败")
                    }
                });
            },
            //是否展示年度账单
            goBillPgae(){
                let isPaperlessParam = {
                 busiType: 'fea_yearBill2023'
                }
                this.$http.post('/xsb/ability/businessLimit/h5QryBusiPermission',isPaperlessParam).then((res)=>{
                    if(res.data.retCode == '0'){
                        this.$router.push('/yearBill2023?srcFrom=my');
                    } else {
                        this.$alert(res.data.retMsg || '暂无权限');
                    }
                }).catch((respose) => {
                    this.$alert(`暂无权限${respose}`);
                });
            },
            showYearBill(){
                let url = `/xsb/api-user/menu/h5SearchMenuByName`;
              let searchParam ={
                stationId:this.userInfo.stationId,
                menuName:"年度账单",
                noInsert:"1",
              }
                this.$http.post(url,searchParam).then(res => {
                    if (res.data.retCode == '0') {
                        let menuList = res.data.data;
                        if(menuList && menuList.length > 0){
                            this.yearBillFlg = true;
                        }
                    }
                }).catch((respose) => {
                    console.info(`获取菜单列表网络请求失败,${respose}`);
                });
            },
            //跳转 我的任务
            goTask(state){
              this.updateMenuChain('task', 2)
              this.updateMenuChain('', 3)
            //   let menuParam = {
            //     stationId: this.userInfo.stationId,
            //     serverNumber: this.userInfo.servNumber,
            //     clickTime: dateFormat(new Date(), ('yyyy-MM-dd hh:mm:ss')),
            //   }
              menuLogRecord({})
              if (state != 4){
                this.$router.push({
                  name: "MyTaskList",
                  query: {
                    state: state
                  }
                })
              }else {
                return;
              }
            },
            //add by yesenwei at 2021/05/18 ： 跳转预受理订单列表
            goOrder(state){
                this.$router.push({
                    name: "HandleOrderList",
                    query: {
                        state: state
                    }
                })
            },
            getGoBackAndExitStatus(){
                if(this.isAndroid){
                    ClientJs.getGoBackAndExit('backHomeAndExit');
                }
            },
            setGoBackAndExit(){
                ClientJs.setGoBackAndExit(this.isBackExit ? 1 : 0);
            },
            //调用客户端获取当前版本
            checkCurVersion(){
                if(/android|harmony/gi.test(navigator.userAgent)){
                    var res = window.WebViewFunc.getAppVersion();
                    var obj = eval('(' + res + ')');
                    let AndroidVersion = obj.verison.split('.').join('');
                    console.log(AndroidVersion)
                    if(AndroidVersion >= 22271){
                        this.isAndroid = true;
                    }

                }
            },
            gopage(){
                let url = `http://*************:8080/xsbh5.html#/commonPage?srcFrom=webview&gobackFlag=webview&body=JTdCJTIycGFnZVR5cGUlMjI6JTIwJTIyMyUyMiwlMjJtZW51SWQlMjI6JTIwJTIyMjkyOCUyMiwlMjJwYWdlUm91dGUlMjI6JTIwJTIycGdvcCUyMiwlMjJwYXJhbU1hcCUyMjolN0IlMjJwYWdlTmFtZSUyMjolMjAlMjIlRTklOUIlODYlRTUlOUIlQTIlRTclOEUlQjAlRTUlOUMlQkElRTclQUUlQTElRTclOTAlODYlMjIsJTIyY2hpbGRQYWdlUm91dGUlMjI6JTIwJTIyanRvcEFwcCUyMiU3RCU3RA==`;
                // let url = `${this.ipUrl}/xsbh5.html#${route}?srcFrom=webview&gobackFlag=webview&source=region`+ param;
                ClientJs.openWebKit(url, '', '0', '', '', '', '', '', '', '', '');
            },
          myInfoTuoMingQuery() {
            let param = {}
            this.$http.post('/xsb/paperless/locationLimit/h5myInfoTuoMingQuery', param).then(res => {
              let { retCode, retMsg, data } = res.data
              if (retCode == '0') {
                this.myInfoTuoMingFlag = true;
                Storage.session.set('myInfoTuoMingFlag','1');
              } else {
                this.myInfoTuoMingFlag = false;
                Storage.session.set('myInfoTuoMingFlag','2');
              }
            }).catch((err) => {
              this.myInfoTuoMingFlag = false;
              Storage.session.set('myInfoTuoMingFlag','2');
            })
          }
        },
        computed:{
             //获取vuex中存的定位信息
            getLocation(){
                let self = this;
                let loc = this.$store.getters.locationTxt;
                if (!loc && loc != 'empty') {//初始化进来的时候  取不到定位信息
                    setTimeout(() => {
                        if(!Storage.get('location') || Storage.get('location')=='empty'){//没有获取到定位
                            this.getLocationFromAldParent();//从服务端获取
                        } else {
                            self.$store.commit('SET_LOCAL', Storage.get('location'));
                        }
                    }, 1000)
                }
                if(loc == 'empty'){
                    return '请点击刷新'
                } else {
                    return loc;
                }
            },
            //集客随销 9988022838662806   校园营销  9988021245683406
            relTelTitle(){
                if(this.selectStationId == this.CONSTVAL.STATION_JKSX){//集客随销
                    return '客户经理工号(必填)';
                } else if(this.selectStationId == this.CONSTVAL.STATION_SCHOOL) {
                    return '手机号(必填)';
                } else {
                    //协销人员录入
                    if(this.stationfilterList && this.stationfilterList.length > 0){
                        for(let i = 0; i<this.stationfilterList.length; i++){
                            if(this.selectStationId ==  this.stationfilterList[i].value){
                                return this.stationfilterList[i].dictName;
                            }
                        }
                    }
                    return '';
                }
            }

        },

      filters: {
        tuoMingName(val,myInfoTuoMingFlag) {
          if (myInfoTuoMingFlag && val) {
            let name = val[0];
            for (let i = 1; i < val.length; i++) {
              name += "*";
            }
            return name;
          }else{
            return val;
          }
        },
        tuoMingCrmId(val,myInfoTuoMingFlag){
          if(myInfoTuoMingFlag && val){
            return val.slice(0, -2) +"**"
          }else{
            return val;
          }
        },
        tuoMingNumber(val,myInfoTuoMingFlag){
          if(myInfoTuoMingFlag && val){
            return val.substring(0, 3) + "****" + val.substring(7, 12);
          }else{
            return val;
          }
        },
      },
        created(){
          const savedData = localStorage.getItem('testData');
          this.$alert(savedData)
            this.updateMenuChain('my', 1)
            //查询是否脱敏
            let myInfoTuoMingSum = Storage.session.get('myInfoTuoMingFlag');
            if(myInfoTuoMingSum){
              if(myInfoTuoMingSum =='1'){
                this.myInfoTuoMingFlag = true;
              }else{
                this.myInfoTuoMingFlag = false;
              }
            }else{
              this.myInfoTuoMingQuery();
            }

            //查询任务数量
            this.qryTaskNum();
            //清除调用链业务菜单流水，防止被错误采集 add by qhuang 20221025
            this.$pointLesslog && this.$pointLesslog.setChainBusiSeq('');
          // 关闭截屏权限
          ClientJs.screenShot(0);
        },
        components: {
          FloatIcon
        }
    }
</script>

<style scoped lang="less">
    @import "../../base/less/variable.less";
    @import "../../base/less/mixin.less";
    .my-md {
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        background: #fff;
        flex-direction: column;
        overflow:auto;
    }

    .my-top {
        height: 32px;
        line-height: 32px;
        margin-top: 20px;
        margin-bottom: 25px;
        flex:0 1;
    }

    .my-top-name {
        height: 32px;
        float: left;
        font-size: 23px;
        margin-left: 16px;
        font-weight: 600;
        color: rgba(88, 88, 88, 1);
    }

    .my-top-warning {
        width: 40px;
        height: 30px;
        float: right;
        margin-top: 0px;
        margin-right: 16px;
        position: relative;
        line-height: 22px;
        text-align: center;
    }

    .iconwarning {
        color: #585858;
        font-size: 22px;
    }

    .my-wnum {
        width: 16px;
        height: 16px;
        display: block;
        top: -1px;
        right: -5px;
        text-align: center;
        line-height: 16px;
        font-size: 12px;
        color: #fff;
        background: #FF7070;
        border-radius: 50%;
        position: absolute;
    }
    .marcard-wrap{
        &.bg{
            box-shadow:0px 4px 18px 0px rgba(193,193,193,0.5);
            border-radius:0px 0px 16px 16px;
        }
    }
    .my-card {
        margin:0 1rem;
        height: 150px;
        /* background: linear-gradient(147deg, rgba(81, 150, 255, 1) 0%, rgba(42, 104, 255, 1) 100%); */
        box-shadow: 3px 3px 12px 0px rgba(122, 192, 255, 1);
        border-radius: 16px;
        opacity: 0.95;
        position: relative;
        /* background: url(../../assets/img/cardline.png) no-repeat center center,
                    linear-gradient(147deg,#5196FF 0%, #2A68FF 100%); */
        background-size: 100%;
    }

    .my-card-inner {
        position: absolute;
        top: 0px;
        left: 1.375rem;
        right: 1rem;
        bottom: 0;
        overflow: hidden;
        z-index: 2;
    }

    .my-cd-name {
        height: 32px;
        line-height: 32px;
        font-size: 24px;
        color: #fff;
        margin-top: 1.4rem;
    }
    .my-cd-station {
        font-size:16px;
        font-weight:600;
        color:#fff;
        line-height: 28px;
        height: 32px;
        i{
            padding-left:0.5rem;
        }
    }
    .my-cd-local {
        max-height: 32px;
        display:table;
        font-size:14px;
        color: #fff;
        display: flex;
        justify-content: space-between;
        p{
            display:table-cell;
            line-height: 16px;
            vertical-align: middle;
        }
        .dingwei-icon{
            // padding: 4px;
            // border: 1px solid #fff;
            // border-radius: 50%;
            line-height: 16px;
            margin-left: 5px;
            vertical-align: middle;
        }
    }

    .my-cd-num {
        height: 32px;
        line-height: 32px;
        font-size: 16px;
        color: #fff;
    }

    .myx{
        box-shadow: 0px 2px 9px 0px rgba(67, 67, 67, 0.17);
        height: auto;
        background: #FFFFFF;
        border-radius: 8px;
        margin:0 1rem;
        margin-top: 23px;
    }
    .myorder{
        height: auto;
        background: #FFFFFF;
        border-radius: 8px;
        margin:0 1rem;
        margin-top: 11px;
        border-bottom: 1px solid #F7F7F7 ;
    }
    .myorder-title{
        width: 48px;
        height: 17px;
        font-size: 12px;
        font-weight: 600;
        color: #999;
        line-height: 17px;
        margin:0 1rem;
        margin-left: 1px;
    }
    .myorder-ul{
        margin-top: 10px;
        width: 97%;
        display: flex;
        flex-wrap: nowrap;
        justify-content: space-between;
        align-items: center;
    }
    .myorder-li{
        display: block;
        margin-left: -2px;
        margin-top: 5px;
    }
    .icon-img {
        // margin-left: 7px;
        width: 25px;
        height: 25px;
        display: block;
        margin: 0 auto;
        margin-bottom: 2px;
    }
    .myorder-txt{
        transform:scale(0.5);
        height: 22px;
        font-weight: 400;
        color: #505050;
        line-height: 11px;
        margin-left: -17px;
        font-size: 20px;
    }
    .myorder-txt-task{
        font-size: 12px;
        transform: scale(0.5);
        /*margin-top: 6px;*/
        vertical-align: text-top;
        width: 100%;
        -webkit-transform: scale(0.8);
        display: inline-block;
    }
    .myorder-txt-num{
        display: inline-block;
       line-height: 11px;
    }

    .mytask{
      height: auto;
      background: #FFFFFF;
      border-radius: 8px;
        margin: 10px 1rem 0 1rem;
    }
    .mytask-title{
      width: 48px;
      height: 17px;
      font-size: 12px;
      font-weight: 600;
      color: #999;
      line-height: 17px;
      margin:0 1rem;
      margin-top: -5px;
      margin-left: 1px;
    }
    .mytask-ul{
      height: 44px;
      width: 97%;
      display: flex;
      flex-wrap: nowrap;
      justify-content: space-between;
      align-items: center;
    }
    .mytask-li{
      margin-left: 15px;
      .none{
        width: 34px;
      };
      margin-left :-8px ;
      /*display:flex*/
        flex: 1;
        text-align: center;
    }

    .mytask-txt{
      transform:scale(0.5);
      height: 22px;
      font-weight: 400;
      color: #505050;
      line-height: 11px;
      margin-left: -11px;
      font-size: 15px;
    }



    .my-fn {
        height: auto;
        margin: 15px;
        /*overflow: auto;*/
        flex-grow: 1;
        margin-left: 20px;
    }

    .my-fn ul {
        display: block;
        overflow: hidden;
    }

    .my-fn-lis {
        height: 50px;
        line-height: 50px;
        border-bottom: 1px solid #F0F0F0;
        display: flex;
        overflow: hidden;
        .iconfont{
            font-size: 20px;
        }

    }

    .my-fl-icon {
        width: 20px;
        height: 20px;
        margin-top: 15px;
        line-height: 20px;
        .bianzu2,.anniu{
            font-size: 20px;
        }

    }

    .my-fl-txt {
        flex-grow: 1;
        flex-shrink: 0;
        font-size: 14px;
        color: #585858;
        margin-left: 15px;
    }

    .my-fl-target {
        width: 20px;
        height: 20px;
        margin-top: 15px;
        line-height: 20px;
        color: #A6AAB2;
    }
    .my-fl-target2{
        margin-top:10px;
        .iconfont{
            font-size: 24px;
        }
    }
    .iconrizhi {
        color: #585858;
        font-size: 24px;
    }

    .iconyxlj {
        color: #585858;
        font-size: 20px;
    }

    .iconzhuxiao {
        color: #585858;
        font-size: 20px;
    }

    .my-cd-planbtn {
        width: 86px;
        height: 30px;
        background: rgba(255, 255, 255, 1);
        border-radius: 16px;
        position: absolute;
        right: 1rem;
        top: 1.4rem;
        line-height: 31px;
        text-align: center;
        z-index: 3;
    }
    .plan-model{
        position: absolute;
        right: 1rem;
        top: 3.6rem;
        width:200px;
        background:#fff;
        height:auto;
        border-top-left-radius:35px;
        border-bottom-right-radius:35px;
        z-index: 3;
        padding:6px 0;
        box-sizing: border-box;
        .sanjian{
            width:8px;
            height:8px;
            background:#fff;
            position:absolute;
            top:-3px;
            right:40px;
            transform:rotate(45deg);
        }
        .icon-ul{
            width:100%;
            li{
                width:25%;
                float:left;
                height:48px;
                text-align: center;
                .txt{
                    color:#5B5B5B;
                    font-size:10px;
                    display:block;
                    margin-top:2px;
                }
            }
        }
    }
    .student-wrapper{
        margin-top:-30px;
        margin-left:2rem;
        margin-right:2rem;
        height:68px;
        background:rgba(255,242,227,1);
        box-shadow:0px 6px 5px 0px rgba(246,238,225,1);
        border-radius:8px;
        border:1px solid;
        border-image:linear-gradient(117deg, rgba(255,255,255,1), rgba(255,216,172,1)) 1 1;
        .student-img{
            padding:0 0.6rem 0 1rem;
            height:24px;
            vertical-align: middle;
        }
        .student-tel{
            line-height:96px;
            color:#AF822B;
            font-size:14px;
        }
    }
    .iconrwjh {
        color: #3473FE;
        font-size: 16px;
        display: block;
        height: 20px;
        line-height: 20px;
        float: left;
        margin-top: 6px;
        margin-left: 10px;
    }

    .my-cp-txt {
        display: block;
        font-size: 12px;
        color: #3473FE;
        height: 20px;
        line-height: 20px;
        float: left;
        margin-top: 6px;
        margin-left: 3px;
    }
    .station-wrap{
        margin-top:1.25rem;
        max-height:250px;
        z-index:2000;
        position: relative;
        background:#fff;
        overflow:auto;
        .station-ul{
            margin-bottom:0.75rem;
            li{
                float:left;
                width:50%;
                height:2rem;
                line-height: 2rem;
                font-size:14px;
                color:#5D5D5D;
                box-sizing:border-box;
                margin-bottom: 0.5rem;
                text-align: center;
                &:nth-child(2n-1){
                    padding-left:1rem;
                    padding-right:0.375rem;
                }
                &:nth-child(2n){
                    padding-left:0.375rem;
                    padding-right:1rem;
                }
                span{
                    display:inline-block;
                    width:100%;
                    border-radius:16px;
                    border:1px solid rgba(216,216,216,1);
                    .ellipsis();
                    &.active{
                        border:1px solid #4283FF;
                        color:#1D63EB;
                    }
                }
            }
            &:after {
            .clearfloat();
            }
        }
        .station-btn{
            /* border-top:1px solid #F2F2F2; */
/*             height:2.25rem;
            line-height: 2.25rem; */
            font-size:16px;
            font-weight:400;
            color:#007AFF;
            text-align:center;
            margin:1.5rem 0 0.75rem 0;
        }
    }
    .rel-tel-wrapper{
        .title{
            font-size:14px;
            color:#F43A45;
            text-align: center;
            margin:1rem 1rem 0.5rem 1rem;
        }
        .ipt-wrapper{
            height:38px;
            background:rgba(255,255,255,1);
            margin:0 1rem;
            input{
                height:38px;
                width:100%;
                box-sizing: border-box;
                border-radius:4px;
                border:1px solid rgba(233,233,233,1);
                padding-left:0.5rem;
                &:focus{
                    outline: none;
                }
            }
            &.empty-tel{
                input{
                    border:1px solid #F43A45;
                }
            }
        }
    }

.icon-wrapper{
    width:34px;
    height:34px;
    border-radius:50%;
    color:#fff;
    line-height: 34px;
    text-align: center;
    display:inline-block;


    .iconfont{
        font-size:20px;
    }
    &.school-div{
        background:linear-gradient(141deg,rgba(255,163,163,1) 0%,rgba(252,118,118,1) 100%);
        box-shadow:0px 4px 8px 0px rgba(242,122,122,0.61);
    }
    &.jituan-div{
        background:linear-gradient(137deg,rgba(255,227,72,1) 0%,rgba(255,156,81,1) 100%);
        box-shadow:0px 4px 8px 0px rgba(230,181,38,0.54);
    }
    &.xiaoqu-div{
        background:linear-gradient(145deg,rgba(255,157,209,1) 0%,rgba(255,91,184,1) 100%);
        box-shadow:0px 4px 8px 0px rgba(240,115,183,0.58);
    }
    &.hot-div{
        background:linear-gradient(145deg,rgba(95,234,187,1) 0%,rgba(49,187,98,1) 100%);
        box-shadow:0px 4px 6px 0px rgba(108,206,141,0.68);
    }
    &.building-div{
        background-image: linear-gradient(137deg, #F5B07B 0%, #A26C57 100%);
        box-shadow: 0 4px 8px 0 rgba(207,181,181,0.61);
    }
    &.listGroup-div{
        background-image: linear-gradient(137deg, rgb(64,177,254) 0%, rgb(0,153,255) 100%);
        box-shadow: 0 4px 8px 0 rgba(0,153,255,0.61);
    }
    &.yanjie-div{
        background-image: linear-gradient(137deg, #72b704 0%, #477202 100%);
        box-shadow: 0 4px 8px 0 #72b70485;
        .iconfont.xingzhuang17 {
            font-size: 15px !important;
        }
    }
    &.jiudian-div {
        background-image: linear-gradient(137deg, #0750cd 0%, #0845f3 100%);
        .iconfont.xingzhuang17 {
          font-size: 15px !important;
        }
    }
}
.my-txt{
    font-size: 14px;
    color: #1681fb;
    padding-right: 4px;
}
</style>
