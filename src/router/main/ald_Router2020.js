//阿拉盯忘记密码
import Resetpassword from 'components/my/Resetpassword.vue'
// const Resetpassword = () => import('components/my/Resetpassword.vue')
//经验分享
const ArticleList = () => import('components/business/StudyCenter/ArticleList.vue')
const StudyCenterDetail = () => import('components/business/StudyCenter/StudyCenterDetai.vue')
//信用购分期营销案
const CreditStandard = () => import('components/business/creditbuy/CreditStandard.vue')
const CreditChild = () => import('components/business/creditbuy/CreditChild.vue')
const CreditDetail = () => import('components/business/creditbuy/CreditDetail.vue')
const CreditStandardOrder = () => import('components/business/creditbuy/CreditStandardOrder.vue')
const CreditBuyStandard = () => import('components/business/creditbuy/CreditBuyStandard.vue')

//网格化视图 第二版
const GridSelect = () => import('components/business/gridview/GridSelect.vue')
const GridViewHome = () => import('components/business/gridview/GridViewSecond.vue')
const GridBusiVol = () => import('components/business/gridview/GridBusiVol.vue')
const GridTask = () => import('components/business/gridview/GridTask.vue')
const OperTaskDetail = () => import('components/business/gridview/OperTaskDetail.vue')
const GridIndex = () => import('components/business/gridview/GridIndex.vue')
const GridReport = () => import('components/business/gridview/GridReport.vue')
const GridReportDetail = () => import('components/business/gridview/GridReportDetail.vue')
const GridReportTable = () => import('components/business/gridview/GridReportTable.vue')
const GridViewThird = () => import('components/business/gridview/gridViewThird.vue')

const GridWarn = () => import('components/business/gridview/gridWarn.vue')

const HorizontalTable = () => import('components/business/gridview/HorizontalTable.vue')
const Authorization =()=>import('components/business/gridview/authorization.vue')
const GridService =()=>import('components/business/gridview/GridService.vue')


//泛渠道管理
const ManageIndex = () => import('@/panComponents/menu/management/ManageIndex.vue')
const AddNewPanc = () => import('@/panComponents/menu/management/AddNewPanc.vue')
const AddDictNewPanc = () => import('@/panComponents/menu/management/dict/AddDictNewPanc.vue')
const DictList = () => import('@/panComponents/menu/management/dict/DictList.vue')
const UpdateDictInfo = () => import('@/panComponents/menu/management/dict/UpdateDictInfo.vue')
const UpdatePanc = () => import('@/panComponents/menu/management/UpdatePanc.vue')
const AddSecretPanc = () => import('@/panComponents/menu/management/AddSecretPanc.vue')
const AddHeBaoAld = () => import('components/tools/activeRecommd/AddHeBaoAld.vue')
const DealerList = () => import('@/panComponents/menu/management/aidinghuo/DealerList.vue')
const AddDealer = () => import('@/panComponents/menu/management/aidinghuo/AddDealer.vue')
const UpdateDealer = () => import('@/panComponents/menu/management/aidinghuo/UpdateDealer.vue')
//泛渠道解锁账号
const UnlockPanc = () => import('components/tools/unlockPanc/UnlockPanc.vue')

//中断营销案
const DiscontinueMain = () => import('components/market/DiscontinueMain.vue')
const DiscontinuePici = () => import('components/market/DiscontinuePici.vue')
const DiscontinueLevel = () => import('components/market/DiscontinueLevel.vue')
const DiscontinueReward = () => import('components/market/DiscontinueReward.vue')
const DiscontinueCalcFee = () => import('components/market/DiscontinueCalcFee.vue')

//集团彩铃
const RingGroupList = () => import('components/business/jituancailing/GroupList')
const RingMemberAdd = () => import('components/business/jituancailing/MemberAdd')
const RingMemberUpdate = () => import('components/business/jituancailing/MemberUpdate')

//和家固话新
const HeJiaGuHuaChooseTel = () => import('components/business/HeJiaGuHua/HeJiaGuHuaChooseTel.vue')
const HeJiaGuHuaTrueName = () => import('components/business/HeJiaGuHua/HeJiaGuHuaTrueName.vue')
const HeJiaGuHuaGoodOrder = () => import('components/business/HeJiaGuHua/HeJiaGuHuaGoodOrder.vue')

//我的集团
const GroupHome = () => import('components/business/myGroup/GroupHome.vue')
const GroupDetail = () => import('components/business/myGroup/GroupDetail.vue')
const PayHistory = () => import('components/business/myGroup/PayHistory.vue')
const GroupMember = () => import('components/business/myGroup/GroupMember.vue')
const GroupAccount = () => import('components/business/myGroup/GroupAccount.vue')
const GroupArrearsDetail = () => import('components/business/myGroup/GroupArrearsDetail.vue')
const GroupAccountBill = () => import('components/business/myGroup/GroupAccountBill.vue')
const GroupFeeDetail = () => import('components/business/myGroup/GroupFeeDetail.vue')
const GroupOrderList = () => import('components/business/myGroup/GroupOrderList.vue')
const GroupOrderDetail = () => import('components/business/myGroup/GroupOrderDetail.vue')
const GroupProduct = () => import('components/business/myGroup/GroupProduct.vue')
const GroupProductSearch = () => import('components/business/myGroup/GroupProductSearch.vue')
const GroupProductDetail = () => import('components/business/myGroup/GroupProductDetail.vue')
const GroupVNetMember = () => import('components/business/myGroup/GroupVNetMember.vue')
const GroupVNetMemberAdd = () => import('components/business/myGroup/GroupVNetMemberAdd.vue')
const GroupVNetMemberRevise = () => import('components/business/myGroup/GroupVNetMemberRevise.vue')
const GroupRingtoneAdd = () => import('components/business/myGroup/GroupRingtoneAdd.vue')
const GroupRingToneRevise = () => import('components/business/myGroup/GroupRingToneRevise.vue')
const GroupRingTongMember = () => import('components/business/myGroup/GroupRingTongMember.vue')
const GroupOrderListSearch = () => import('components/business/myGroup/GroupOrderListSearch.vue')

//支付
const DepositDetail = () => import('components/business/DepositMoney/DepositDetail.vue')

const DepositDetailNew = () => import('components/business/DepositMoney/DepositDetailNew.vue')
//便利店无纸化
const StorePaperless = () => import('components/business/storepaperless/StoreOrders.vue')
const StoreSubmmit = () => import('components/business/storepaperless/StoreSubmmit.vue')
//设备领取
const DeviceReceive = () => import('components/business/devicereceive/devicereceive.vue')

//欠费缴费
const ArrearsPay = () => import('components/tools/arrearsPay/ArrearsPay.vue')
const ArrearsPayDetail = () => import('components/tools/arrearsPay/ArrearsPayDetail.vue')
//(泛渠道）imei号解除绑定
const PancImeiPhone = () => import('components/tools/activeRecommd/pancImeiPhone.vue')
//快捷包配置
const QuickPackageDispose = () => import('components/business/quickPackage/quickPackageDispose.vue')
const BroadBandCheck = () => import('components/business/quickPackage/broadBandCheck.vue')
const Appreciation = () => import('components/business/quickPackage/appreciation.vue')
const Marketing = () => import('components/business/quickPackage/marketing.vue')
// 泛渠道使用范围白名单
const PancRangeWhiteList = () => import('components/business/PancRangeWhiteList.vue')
//集团业务推荐
const BussinessRecommend = () => import('components/my/myGroupNew/bussinessRecommend.vue')
//个人业务推荐
const PersonalBussinessRecommend = () => import('components/my/myGroupNew/PersonalBussinessRecommend.vue')

const BussinessStep = () => import('components/my/myGroupNew/bussinessStep.vue')
//清单集团
const CorporationList = () => import('components/business/corporation/corporationList.vue')
const CorporationConf = () => import('components/business/corporation/corporationConf.vue')

const UpdatePancApp = () => import('@/panComponents/menu/management/UpdatePancApp.vue')

//集团改造
const GroupSixMustDo = () => import('components/my/myGroupNew/GroupSixMustDo.vue')
const GroupPerBusiReccommend = () => import('components/my/myGroupNew/GroupPerBusiReccommend.vue')
const GroupAlienNetWorkInfo = () => import('components/my/myGroupNew/GroupAlienNetWorkInfo.vue')
const GroupDetailNew = () => import('components/my/myGroupNew/GroupDetailNew.vue')
const GroupBusiReport = ()=> import('components/my/myGroupNew/GroupBusiReport.vue')
const MaskTask = ()=> import('components/my/myGroupNew/MaskTask.vue')

//客户资料变更
const GroupChangeInfo = () => import('components/business/groupUserChange/GroupChangeInfo.vue')
//刷脸登录白名单
const FaceLoginWhite = () => import('components/tools/activeRecommd/FaceLoginWhite.vue')
//实名补拍照
const RepairPhoto = () => import('components/tools/repairPhoto/RepairPhoto.vue')

//刷脸注册前的登录页面
const LoginForFace = () => import('components/my/LoginForFace.vue');

//话费充值
const Deposit = () => import('components/business/DepositMoney/Deposit.vue')

//支付清单
const DepositList = () => import('components/business/DepositMoney/DepositList.vue')

const DepositListNew = () => import('components/business/DepositMoney/DepositListNew.vue')

//校园营销案预约
const SchoolMarket = () => import('components/business/schoolbook/SchoolMarket.vue')
const SchoolMarketOrder = () => import('components/business/schoolbook/SchoolMarketOrder.vue')

//一网通修改密码
const GroupSelf = () => import('components/business/GroupPlatForm/GroupSelf.vue')
const NetcomList = () => import('components/business/NetcomReset/NetcomList.vue')
const NetcomSearch = () => import('components/business/NetcomReset/NetcomSearch.vue')
const NetcomDetail = () => import('components/business/NetcomReset/NetcomDetail.vue')

//一网通自服务
const OneNetcomList = () => import('components/business/GroupPlatForm/OneNetcomList.vue')
const OneNetcomDetail = () => import('components/business/GroupPlatForm/OneNetcomDetail.vue')

// 免填单和超时补录合并
const SupplementAll = () => import('components/business/SupplementAll.vue')
const SupplementTransform = () => import('components/business/SupplementTransform.vue')

// 花呗标准营销案
const CreditPayStandard = () => import('components/business/creditPay/CreditPayStandard.vue')
const CreditPaySign = () => import('components/business/creditPay/CreditPaySign.vue')

//电渠抢单补开户充值
const EchanelPay = () => import('components/business/eChannelGrabOrder/EchanelPay.vue')

// 便利店手机话费充值
const StoreDeposit = () => import('components/business/DepositMoney/StoreDeposit.vue')
//便利店首页
const BldMaintain =() => import('components/area/bld/BldMaintain.vue')
const MyBld =() => import('components/area/bld/MyBld.vue')
const BottomBld =() => import('components/area/bld/bottomBld.vue')
const BldArea =() => import('components/area/Area.vue')
//甩单
const Finished = () => import('components/business/throwMonad/finished.vue')
const Profession = () => import('components/business/throwMonad/profession.vue')
//促销中心
const PromotionEntry = () => import('components/business/promotioncenter/PromotionEntry.vue')
const PromotionPici = () => import('components/business/promotioncenter/PromotionPici.vue')
const PromotionReward = () => import('components/business/promotioncenter/PromotionReward.vue')
const PromotionOrder = () => import('components/business/promotioncenter/PromotionOrder.vue')
const PromotionSubmit = () => import('components/business/promotioncenter/PromotionSubmit.vue')
const PromotionBuyQrCode = () => import('components/business/promotioncenter/PromotionBuyQrCode.vue')
const PromotionBuySign = () => import('components/business/promotioncenter/PromotionBuySign.vue')
const PromotionBuyWithdraw = () => import('components/business/promotioncenter/PromotionBuyWithdraw.vue')
const PromotionZhiMaSign = () => import('components/business/promotioncenter/PromotionZhiMaSign.vue')

const PromotionPiciShopCart = () => import('components/business/promotioncenter/PromotionPiciShopCart.vue')
const PromotionRewardShopCart = () => import('components/business/promotioncenter/PromotionRewardShopCart.vue')

//终端裸售
const Terminalscaling = () => import('components/business/terminalnakedsale/terminalscaling.vue')
const Terminalsalesubmit = () => import('components/business/terminalnakedsale/terminalsalesubmit.vue')
//集客订单日志
//const GroupBusiLogDetail = () => import('components/business/BusiLogSearch/GroupBusiLogDetail.vue')
const FamilyOpportunity = () => import('components/business/OpportunityManage/FamilyOpportunity.vue')
//组织关系
const OrganizationRelation = () => import('components/business/gridview/OrganizationRelation.vue')

//运营收入可视化
//const OperationDetail=()=>import('components/business/HistoryLog/OperationDetail.vue')
//网格商机
const MeshOpportunity=()=>import('components/business/OpportunityManage/meshOpportunity.vue')
const NetGain=()=>import('components/business/OpportunityManage/NetGain.vue')
const AlreadyScrape=()=>import('components/business/OpportunityManage/AlreadyScrape.vue')
//政企商机录入
const GovernmentBusiLog=()=>import('components/business/BusiLogSearch/GovermentBusiLog.vue')
const OpportunityProgress = () => import('components/business/OpportunityManage/OpportunityProgress.vue')

const GroupBusiLogDetail = () => import('components/business/BusiLogSearch/GroupBusiLogDetail.vue')

//集团证件入网
const GroupInfoAccess = () => import('components/business/GroupCard/GroupInfoAccess.vue')

const GroupInfoModify = () => import('components/business/GroupCard/groupEnterInfoChange/GroupInfoModify.vue')
const GroupAccessList = () => import('components/business/GroupCard/GroupAccessList.vue')
const GroupSelectMeal = () => import('components/business/GroupCard/GroupSelectMeal.vue')
const GroupMealDetail = () => import('components/business/GroupCard/GroupMealDetail.vue')
const GroupChargeDetail = () => import('components/business/GroupCard/GroupChargeDetail.vue')
const GroupCardBack = () => import('components/business/GroupCard/GroupCardBack.vue')
const GroupSelectNumber = () => import('components/business/GroupCard/GroupSelectNumber.vue')
//免填单补录
const GroupInfoSupplyAdd = () => import('components/business/GroupCard/GroupInfoSupplyAdd.vue')



//信用购分期营销案和无忧购机合并
const NewCreditStandard = () => import('components/business/newcreditbuy/NewCreditStandard.vue')
const NewCreditChild = () => import('components/business/newcreditbuy/NewCreditChild.vue')
const NewCreditDetail = () => import('components/business/newcreditbuy/NewCreditDetail.vue')
const NewCreditStandardOrder = () => import('components/business/newcreditbuy/NewCreditStandardOrder.vue')
const NewCreditBuyStandard = () => import('components/business/newcreditbuy/NewCreditBuyStandard.vue')
const NewCreditBuyWithdraw = () => import('components/business/newcreditbuy/NewCreditBuyWithdraw.vue')
const NewCreditBuySign = () => import('components/business/newcreditbuy/NewCreditBuySign.vue')
const NewHappyIndex = () => import('components/business/newcreditbuy/NewHappyIndex.vue')
const NewHappyBuySign = () => import('components/business/newcreditbuy/NewHappyBuySign.vue')

//(新)业务日志查询
const BusiLogIndex = () => import('components/business/BusiLogSearchNew/BusiLogIndex.vue')
const BusiLogDetailInfo = () => import('components/business/BusiLogSearchNew/BusiLogDetailInfo.vue')
const KuandaiTrajectory = () => import('components/business/BusiLogSearchNew/KuandaiTrajectory.vue')
//考勤配置
const AttendanceDeploy = () => import('components/business/Attendance/AttendanceDeploy.vue')
const AttendanceRule = () => import('components/business/Attendance/AttendanceRule.vue')
const AttendanceRecord = () => import('components/business/Attendance/AttendanceRecord.vue')
const AttendanceDetail = () => import('components/business/Attendance/AttendanceDetail.vue')

//网格作业
const GridWorkMain = () => import('components/business/GridHomework/GridWorkMain.vue')
const BacklogWork = () => import('components/business/GridHomework/BacklogWork.vue')
const WorkHandle = () => import('components/business/GridHomework/WorkHandle.vue')
const WorkCheck = () => import('components/business/GridHomework/WorkCheck.vue')
const shopBusiMap = () => import('components/business/GridHomework/shopBusi/ShopBusiMap.vue')
const shopBusiFeddBack = () => import('components/business/GridHomework/shopBusi/ShopBusiFeedback.vue')
//融合受理
const SelectPackage = () => import('components/business/fuseHandle/SelectPackage.vue')
const CalculatePackage = () => import('components/business/fuseHandle/CalculatePackage.vue')
//网格作业创建
const NewGridHomework = () => import('components/business/GridHomework/NewGridHomework.vue')
const GridHomeworkTemp = () => import('components/business/GridHomework/GridHomeworkTemp.vue')
const HomeworkExecute = () => import('components/business/GridHomework/HomeworkExecute.vue')
const Underway = () => import('components/business/GridHomework/Underway.vue')

//装维协同
const InstallCoordinate = () => import('components/business/installationCoordination/InstallCoordinate.vue')
const InstallListSearch= () => import('components/business/installationCoordination/InstallListSearch.vue')
const InstallToDoList= () => import('components/business/installationCoordination/InstallToDoList.vue')
const InstallToDoListDetail = () => import('components/business/installationCoordination/InstallToDoListDetail.vue')
const GroupInstallListDetail = () => import('components/business/installationCoordination/GroupInstallListDetail.vue')

//电渠开户活动推荐
const GrabKaiHuActivity = () =>import('components/business/eChannelGrabOrder/grabKaiHuActivity.vue')
//学习专区（考试）
const Exam = () => import('components/business/StudyCenter/ExamNew.vue')

//工单内容
const WorkOrders =() =>import('components/business/WorkOrder/WorkOrders.vue')
const WorkDetails =() =>import('components/business/WorkOrder/WorkDetails.vue')

//宽带移机
const YiJiNew = () => import('components/business/yiji/YiJiNew.vue')

// 乡情网
const HometownAdd =() =>import('components/business/jituanvwang/HometownAdd.vue')

//甩单
const DropOrder =() =>import('components/business/DropOrder.vue')
//激励明细统计(厂促)
 const OrderDetailed =() =>import('components/business/IncentiveDetail/orderDetailed.vue')

//楼宇视图
const BuildingViewPlan =() =>import('components/my/buildingview/BuildingViewPlan.vue')
const BuildingViewDetail =() =>import('components/my/buildingview/BuildingViewDetail.vue')
const BuildingInfoCollection =() =>import('components/my/buildingview/BuildingInfoCollection.vue')
const GroupBusiRec =() =>import('components/my/buildingview/GroupBusiRec.vue')
const PersonBusiRec =() =>import('components/my/buildingview/PersonBusiRec.vue')
const PersonBusiResidentUser = () => import('components/my/buildingview/PersonBusiResidentUser.vue')
const PersonBusiRecDetail =() =>import('components/my/buildingview/PersonBusiRecDetail.vue')
const BuildingBusiReport =() =>import('components/my/buildingview/BuildingBusiReport.vue')
const BuildingViewInfo =() =>import('components/my/buildingview/BuildingViewInfo.vue')
const BuildingViewMustDo =() =>import('components/my/buildingview/BuildingViewMustDo.vue')
const InfoCollectionComp =() =>import('components/my/buildingview/InfoCollectionComp.vue')

//证件预占解除
const YuCancellation =() =>import('components/business/YuCancellation.vue')

//工作台搜索菜单页面
const SearchMenu =() =>import('components/desk/SearchMenu.vue')

//v网自维和彩铃
const VNetDimension  = () => import('components/business/VNetSelf/VNetDimension.vue')
const ShortSignal = () => import('components/business/VNetSelf/ShortSignal.vue')
const VNetShort = () =>  import('components/business/VNetSelf/VNetShort.vue')
const VNetLongShortNumber = () =>  import('components/business/VNetSelf/VNetLongShortNumber.vue')
//不满意维护页面
const UnSatisfyList =() =>import('components/business/unSatisfy/UnSatisfyList.vue')
const UnSatisfyDetail =() =>import('components/business/unSatisfy/UnSatisfyDetail.vue')

// 高校迎新专区
const CollegeWelcomeArea =() =>import('components/my/highSchool/CollegeWelcomeArea.vue')
const BusiInterAnalyse =() =>import('components/my/highSchool/BusiInterAnalyse.vue')
const PeopleFlowMonitor =() =>import('components/my/highSchool/PeopleFlowMonitor.vue')
const CollegeWelcomeBusiDetail =() =>import('components/my/highSchool/CollegeWelcomeBusiDetail.vue')
// 系统观测
const HostMonitor =() =>import('components/my/highSchool/HostMonitor.vue')
//业务预约（代维）
const salesappointment = () => import('components/area/cmopInstall/amsBusiLog/salesappointment.vue')
const amsbusilog = () => import('components/area/cmopInstall/amsBusiLog/AmsBusiLog.vue')
//里程
const GridManaReport = () => import('components/business/gridview/GridManaReport.vue')
//泛渠道的免填单补录功能
const SupplementPanc = () => import('components/business/SupplementPanc.vue')
const SuppSubmitPanc = () => import('components/business/SuppSubmitPanc.vue')
const SuppCommonPanc = () => import('components/business/SuppCommonPanc.vue')

//日志采集
const MyLogList =() =>import('components/tools/daka/MyLogList.vue')
//活动推荐（代维）
const DaiWeiActiveRecommd =() =>import('components/tools/activeRecommd/daiWeiActiveRecommd.vue')

//集团地址变更确认
const GroupPointInfoSync =() =>import('components/my/GroupPointInfoSync.vue')
//楼宇地址变更确认
const BuildingPointInfoSync = () =>import('components/my/buildingview/BuildingPointInfoSync.vue')
//清单集团视图
const ListGroupPlan =() =>import('components/my/listGroup/ListGroupPlan.vue')
const ListGroupDetail =() =>import('components/my/listGroup/ListGroupDetail.vue')
const ListGroupDetailRead =() =>import('components/my/listGroup/ListGroupDetailRead.vue')
const ListGroupBusiRec =() =>import('components/my/listGroup/ListGroupBusiRec.vue')
const ListGroupBusiStep =() =>import('components/my/listGroup/ListGroupBusiStep.vue')
const ListGroupCenterChoose =() =>import('components/my/listGroup/ListGroupCenterChoose.vue')
const ListGroupBusiReport =() =>import('components/my/listGroup/ListGroupBusiReport.vue')
const ListGroupAlienNetWork =() =>import('components/my/listGroup/ListGroupAlienNetWork.vue')


//渠道资源（网格）
const ChannelList =() =>import('components/business/ChannelResource/ChannelList.vue')
const ChannelListDetail =() =>import('components/business/ChannelResource/ChannelListDetail.vue')

//一号多宽
const OneNumMoreBand = () => import('components/business/BandTvMarket/OneNumMoreBand.vue')
const MoreBandKaiTong = () => import('components/business/BandTvMarket/MoreBandKaiTong.vue')

// 政企客户经理
const PersonalView = () => import('components/business/zhengqi/PersonalView.vue')

//政企集团视图
const JiKeGroupHome =() => import('components/business/zhengqi/jituanshitu/JiKeGroupHome.vue')
const JiKeGroupDetail =() => import('components/business/zhengqi/jituanshitu/JiKeGroupDetail.vue')
const JiKeGroupMember =() => import('components/business/zhengqi/jituanshitu/JiKeGroupMember.vue')
const JiKeGroupOrderList =() => import('components/business/zhengqi/jituanshitu/JIKeGroupOrderList.vue')
const JiKeGroupProduct =() => import('components/business/zhengqi/jituanshitu/JiKeGroupProduct.vue')
const JiKeGroupProductDetail =() =>import('components/business/zhengqi/jituanshitu/JiKeGroupProductDetail.vue')
const JiKeGroupProductSearch =() =>import('components/business/zhengqi/jituanshitu/JiKeGroupProductSearch.vue')
const JiKeSearchJt =() =>import('components/business/zhengqi/jituanshitu/JiKeSearchJt.vue')
const JiKeFullOrderList =() =>import('components/business/zhengqi/jituanshitu/JiKeFullOrderList.vue')

//政企计划任务
const ZhengQiDrawPlan =() =>import('components/business/zhengqi/ZhengQiDrawPlan.vue')
const YxTask = () =>import('components/business/zhengqi/yxTask.vue')
//政企
const GovernmentIndex =() =>import('components/business/zhengqi/GovernmentIndex.vue')
const MyGb =() =>import('components/business/zhengqi/MyGb.vue')
const GovernmentBusniess =() =>import('components/business/zhengqi/GovernmentBusniess.vue')
const GovernmentSet =() =>import('components/business/zhengqi/GovernmentSet.vue')
const GovernAdv =() =>import('components/business/zhengqi/GovernAdv.vue')
// 政企集团业务明细
const ZhengQiBusiLogIndex =() =>import('components/business/zhengqi/BusiLogDetail/BusiLogIndex.vue')
const ZhengQiBusiLogDetail =() =>import('components/business/zhengqi/BusiLogDetail/BusiLogDetailInfo.vue')
// 政企集团营销过程
const ZhengQiBusiRecommend =() =>import('components/business/zhengqi/BusiRecommend.vue')

// 新窗口打开
const FrameBox =() =>import('components/common/FrameBox/FrameBox.vue')

// 电渠抢单（新）
const GrabSheetNew =() =>import('components/business/grabsheet/GrabSheetNew.vue')
const SheetDetailNew =() =>import('components/business/grabsheet/SheetDetailNew.vue')
const SheetListNew =() =>import('components/business/grabsheet/SheetListNew.vue')
const PaiDanListNew =() =>import('components/business/grabsheet/PaiDanListNew.vue')
const PullSheetList =() =>import('components/business/grabsheet/PullSheetList.vue')
const GrabSheetReport =() =>import('components/business/grabsheet/GrabSheetReport.vue')
const GrabHorizontalTable =() =>import('components/business/grabsheet/GrabHorizontalTable.vue')
const ReserveSheetDetail =() =>import('components/business/grabsheet/ReserveSheetDetail.vue')
//网格通
const GrabSheetNewForWg =() =>import('components/business/grabsheet/GrabSheetNewForWg.vue')
const SheetListNewForWg =() =>import('components/business/grabsheet/SheetListNewForWg.vue')
const PullSheetListForWg =() =>import('components/business/grabsheet/PullSheetListForWg.vue')
const SheetDetailNewForWg =() =>import('components/business/grabsheet/SheetDetailNewForWg.vue')
const ReserveSheetDetailForWg =() =>import('components/business/grabsheet/ReserveSheetDetailForWg.vue')
const PaiDanListNewForWg =() =>import('components/business/grabsheet/PaiDanListNewForWg.vue')


//政企管理员
const AdminGovernment =() =>import('components/business/zhengqi/AdminGover/AdminGovernment.vue')
const AdminGoverBusniess =() =>import('components/business/zhengqi/AdminGover/AdminGoverBusniess.vue')
const AdminMyGb =() =>import('components/business/zhengqi/AdminGover/AdminMyGb.vue')
const AdminYxTask =() =>import('components/business/zhengqi/AdminGover/AdminYxTask.vue')

//全家wifi
const AllHomeWifi =() =>import('components/business/smartNetwork/AllHomeWifi.vue')
//全国亲情网(集团验收)
const QinQingKaiTong =() =>import('components/business/groupAcceptance/QinQingKaiTong.vue')
//增值产品开通(集团验收)
const ZengZhiKaiTong =() =>import('components/business/groupAcceptance/ZengZhiKaiTong.vue')

//业务预处理
const BusiPreDealing =() =>import('components/business/BandTvMarket/BusiPreDealing.vue')
//极简下单
const EasyOverBook =() =>import('components/business/BandTvMarket/EasyOverBook.vue')
//开具发票
const InvoiceList = () => import('components/business/invoices/invoiceList.vue')
const InvoiceDetail = () => import('components/business/invoices/invoiceDetail.vue')
//物流查询
const LogisticsInquiry = () => import('components/business/logisticsinquiry/LogisticsInquiry.vue')

//携号转网快速开户页面
const FastCarryingCardBack =() =>import('components/business/CarryingNumber/FastCarryingCardBack.vue')

//扫街管理
const StoreInfoCollectList = () => import('components/business/storeinfocollection/StoreList.vue')
const StoreManager = () => import('components/business/storeinfocollection/StoreManager.vue')
//集团商机采集
const GroupBusiChanceCollect = () => import('components/business/storeinfocollection/GroupBusiChanceCollect.vue')
//增值产品开通(融合受理)
const MarketProductZz =() =>import('components/business/groupAcceptance/MarketProductZz.vue')
//集团业务日志详细
const GroupBusiLogNew = () =>import('components/business/BusiLogSearchNew/GroupBusiLogNew.vue')

//投诉工单查询
const ComplainOrder= () => import('components/business/complaintorder/ComplainOrder.vue')
const ComplainOrderList= () => import('components/business/complaintorder/ComplainOrderList.vue')
const ComplainOrderListDetail = () => import('components/business/complaintorder/ComplainOrderListDetail.vue')
//投诉工单公共查询
// const ComplainOrderSearch = () => import('components/business/complaintorder/ComplainOrderSearch.vue')
//网格长看板
const FamilyIndicators = () => import('components/business/GridBoard/FamilyIndicators.vue')
const IndicatorsFiltrate = () => import('components/business/GridBoard/IndicatorsFiltrate.vue')
const VillageToPut = () => import('components/business/GridBoard/VillageToPut.vue')

//网格长看板
const HouseholdTask = () => import('components/business/GridBoard/HouseholdTask.vue')

//应急补资料
const YuPeiHaoUrgent = () => import('components/yupeihao/YuPeiHaoUrgent.vue')

//客户关怀查询
const CustomerCareEnquiry = () => import('components/tools/CustomerCareEnquiry.vue')
//日志搜索
const LogStatistics = () => import('components/business/LogCollection/LogStatistics.vue')
const LogList = () => import('components/business/LogCollection/LogList.vue')
//掌厅预约
const ServiceReserve = () => import('components/business/serviceHallreserve/ServiceReserve.vue')


export default[
    {
        path: '/resetpassword',
        name: 'Resetpassword',
        component: Resetpassword
    },
    {
        path: '/creditStandard',
        name: 'CreditStandard',
        component: CreditStandard
    },
    {
        path: '/creditChild',
        name: 'CreditChild',
        component: CreditChild,
        meta: {
            keepAlive: true
        }
    },
    {
        path: '/creditDetail',
        name: 'CreditDetail',
        component: CreditDetail
    },
    {
        path: '/creditStandardOrder',
        name: 'CreditStandardOrder',
        component: CreditStandardOrder
    },
    {
        path: '/creditBuyStandard',
        name: 'CreditBuyStandard',
        component: CreditBuyStandard
    },
    {
        path: '/ArticleList',
        component: ArticleList
    },
    {
        path: '/studyCenterDetail',
        name: 'StudyCenterDetail',
        component: StudyCenterDetail,
    },
    {
        path: '/gridViewHome',
        name: 'GridViewHome',
        component: GridViewHome,
    },
    {
        path: '/gridSelect',
        name: 'GridSelect',
        component: GridSelect
    },
    {
        path: '/gridBusiVol',
        name: 'GridBusiVol',
        component: GridBusiVol
    },
    {
        path: '/gridTask',
        name: 'GridTask',
        component: GridTask
    }, {
        path: '/manageIndex',
        name: 'ManageIndex',
        component: ManageIndex
    },
    {
        path: '/addNewPanc',
        name: 'AddNewPanc',
        component: AddNewPanc
    },
    {
      path: '/AddDictNewPanc',
      name: 'AddDictNewPanc',
      component: AddDictNewPanc
    },
    {
      path: '/dictList',
      name: 'DictList',
      component: DictList
    },
    {
        path: '/updateDictInfo',
        name: 'UpdateDictInfo',
        component: UpdateDictInfo
    },
    {
      path:'/dealerList',
      name: 'DealerList',
      component: DealerList
    },
    {
      path: '/addDealer',
      name: 'AddDealer',
      component: AddDealer
    },
    {
      path: '/updateDealer',
      name: 'UpdateDealer',
      component: UpdateDealer
    },
    {
        path: '/addSecretPanc',
        name: 'AddSecretPanc',
        component: AddSecretPanc
    },
  {
    path:'/addHeBaoAld',
    name: 'AddHeBaoAld',
    component: AddHeBaoAld
  },
    {
        path: '/updatePanc',
        name: 'UpdatePanc',
        component: UpdatePanc
    },
    {
        path: '/operTaskDetail',
        name: 'OperTaskDetail',
        component: OperTaskDetail
    },
    {
        path: '/gridIndex',
        name: 'GridIndex',
        component: GridIndex
    },
    {
        path: '/discontinueMain',
        name: 'DiscontinueMain',
        component: DiscontinueMain
    },
    {
        path: '/discontinuePici',
        name: 'DiscontinuePici',
        component: DiscontinuePici
    },
    {
        path: '/discontinueLevel',
        name: 'DiscontinueLevel',
        component: DiscontinueLevel
    },
    {
        path: '/discontinueReward',
        name: 'DiscontinueReward',
        component: DiscontinueReward
    },
    {
        path: '/discontinueCalcFee',
        name: 'DiscontinueCalcFee',
        component: DiscontinueCalcFee
    }
    , {
        path: '/ringGroupList',
        name: 'RingGroupList',
        component: RingGroupList
    },
    {
        path: '/RingMemberAdd',
        name: 'RingMemberAdd',
        component: RingMemberAdd
    },
    {
        path: '/ringMemberUpdate',
        name: 'RingMemberUpdate',
        component: RingMemberUpdate
    },
    {
        path: '/heJiaGuHuaChooseTel',
        name: 'HeJiaGuHuaChooseTel',
        component: HeJiaGuHuaChooseTel
    },
    {
        path: '/heJiaGuHuaTrueName',
        name: 'HeJiaGuHuaTrueName',
        component: HeJiaGuHuaTrueName
    },
    {
        path: '/heJiaGuHuaGoodOrder',
        name: 'HeJiaGuHuaGoodOrder',
        component: HeJiaGuHuaGoodOrder
    }, {
        path: '/depositDetail',
        name: 'DepositDetail',
        component: DepositDetail,
    },{
    path: '/depositDetailNew',
    name: 'DepositDetailNew',
    component: DepositDetailNew,
  },
    {
        path: '/groupAccount',
        name: 'GroupAccount',
        component: GroupAccount
    },
    {
        path: '/groupArrearsDetail',
        name: 'GroupArrearsDetail',
        component: GroupArrearsDetail
    },
    {
        path: '/groupAccountBill',
        name: 'GroupAccountBill',
        component: GroupAccountBill
    }
    ,
    {
        path: '/groupFeeDetail',
        name: 'GroupFeeDetail',
        component: GroupFeeDetail
    },
    {
        path: '/groupHome',
        name: 'GroupHome',
        component: GroupHome
    },
    {
        path: '/groupDetail',
        name: 'GroupDetail',
        component: GroupDetail
    },
    {
        path: '/payHistory',
        name: 'PayHistory',
        component: PayHistory
    },
    {
        path: '/groupMember',
        name: 'GroupMember',
        component: GroupMember
    }, {
        path: '/groupOrderList',
        name: 'GroupOrderList',
        component: GroupOrderList
    },
    {
        path: '/groupOrderDetail',
        name: 'GroupOrderDetail',
        component: GroupOrderDetail
    },
    {
        path: '/groupProduct',
        name: 'GroupProduct',
        component: GroupProduct
    },
    {
        path: '/groupProductSearch',
        name: 'GroupProductSearch',
        component: GroupProductSearch
    },
    {
        path: '/groupProductDetail',
        name: 'GroupProductDetail',
        component: GroupProductDetail
    },

    {
        path: '/groupVNetMember',
        name: 'GroupVNetMember',
        component: GroupVNetMember
    },
    {
        path: '/groupVNetMemberAdd',
        name: 'GroupVNetMemberAdd',
        component: GroupVNetMemberAdd
    },
    {
        path: '/groupVNetMemberRevise',
        name: 'GroupVNetMemberRevise',
        component: GroupVNetMemberRevise
    },
    {
        path: '/groupRingtoneAdd',
        name: 'GroupRingtoneAdd',
        component: GroupRingtoneAdd
    },
    {
        path: '/groupRingToneRevise',
        name: 'GroupRingToneRevise',
        component: GroupRingToneRevise
    },
    {
        path: '/groupRingTongMember',
        name: 'GroupRingTongMember',
        component: GroupRingTongMember
    },
    {
        path:'/groupOrderListSearch',
        name:'GroupOrderListSearch',
        component:GroupOrderListSearch
    },
    {
        path: '/storepaperless',
        name: 'StorePaperless',
        component: StorePaperless
    },
    {
        path: '/storesubmmit',
        name: 'StoreSubmmit',
        component: StoreSubmmit
    },
    {
        path: '/devicereceive',
        name: 'devicereceive',
        component: DeviceReceive
    },
    {
        path: '/gridReport',
        name: 'GridReport',
        component: GridReport
    },
    {
        path: '/gridReportDetail',
        name: 'GridReportDetail',
        component: GridReportDetail
    },
    {
        path: '/gridReportTable',
        name: 'GridReportTable',
        component: GridReportTable
    },
    {
        path: '/arrearsPay',
        name: 'ArrearsPay',
        component: ArrearsPay
    },
    {
        path: '/pancImeiPhone',
        name: 'pancImeiPhone',
        component: PancImeiPhone
    },
    {
        path: '/arrearsPayDetail',
        name: 'ArrearsPayDetail',
        component: ArrearsPayDetail
    },
    {
        path: '/gridWarn',
        name: 'gridWarn',
        component: GridWarn
    },
    {
        path: '/quickPackageDispose',
        name: 'quickPackageDispose',
        component: QuickPackageDispose
    },
    {
        path: '/broadBandCheck',
        name: 'broadBandCheck',
        component: BroadBandCheck
    },
    {
        path: '/horizontalTable',
        name: 'HorizontalTable',
        component: HorizontalTable
    },
    {
        path: '/unlockPanc',
        name: 'UnlockPanc',
        component: UnlockPanc
    },
    {
        path: '/pancRangeWhiteList',
        name: 'PancRangeWhiteList',
        component: PancRangeWhiteList
    },
    {
        path: '/appreciation',
        name: 'Appreciation',
        component: Appreciation
    },
    {
        path: '/marketing',
        name: 'Marketing',
        component: Marketing
    },
    {
        path: '/bussinessRecommend',
        name: 'BussinessRecommend',
        component: BussinessRecommend
    },
  {
    path: '/personalBussinessRecommend',
    name: 'PersonalBussinessRecommend',
    component: PersonalBussinessRecommend
  },
    {
        path: '/corporationList',
        name: 'CorporationList',
        component: CorporationList
    },
    {
        path:'/authorization',
        name:'Authorization',
        component:Authorization
    },
    {
        path:'/corporationConf',
        name:'CorporationConf',
        component:CorporationConf
    },
    {
        path:'/bussinessStep',
        name:'BussinessStep',
        component:BussinessStep
    },
    {
        path:'/updatePancApp',
        name:'UpdatePancApp',
        component:UpdatePancApp
    },
    {
        path:'/groupChangeInfo',
        name:'GroupChangeInfo',
        component:GroupChangeInfo
    },
    {
        path:'/repairPhoto',
        name:'RepairPhoto',
        component:RepairPhoto
    },
    {
       path:'/faceLoginWhite',
       name:'FaceLoginWhite',
       component:FaceLoginWhite
    },
    {
        path:'/loginForFace',
        component:LoginForFace
    },
    {
        path:'/deposit',
        name:'Deposit',
        component:Deposit,
    },
    {
        path: '/depositList',
        name: 'DepositList',
        component:DepositList
    },{
    path:'/depositListNew',
    name:'DepositListNew',
    component:DepositListNew
  },
    {
        path:'/groupSixMustDo',
        name:'GroupSixMustDo',
        component:GroupSixMustDo
    },
    {
        path:'/groupPerBusiReccommend',
        name:'GroupPerBusiReccommend',
        component:GroupPerBusiReccommend
    },
    {
        path:'/groupAlienNetWorkInfo',
        name:'GroupAlienNetWorkInfo',
        component:GroupAlienNetWorkInfo
    },
    {
        path:'/groupDetailNew',
        name:'GroupDetailNew',
        component:GroupDetailNew
    },
    {
        path:'/groupBusiReport',
        name:'GroupBusiReport',
        component:GroupBusiReport
    },
    {
        path:'/schoolMarket',
        name:'SchoolMarket',
        component:SchoolMarket
    },
    {
        path:'/schoolMarketOrder',
        name:'SchoolMarketOrder',
        component:SchoolMarketOrder
    },
    {
        path: '/netcomList',
        name: 'NetcomList',
        component: NetcomList
    },
    {
        path: '/netcomSearch',
        name: 'NetcomSearch',
        component: NetcomSearch
    },
    {
        path: '/netcomDetail',
        name: 'NetcomDetail',
        component: NetcomDetail
    },
    {
        path: '/groupSelf',
        name: 'GroupSelf',
        component: GroupSelf
    },
    {
        path: '/oneNetcomlist',
        name: 'OneNetcomList',
        component: OneNetcomList
    },
    {
        path: '/oneNetcomDetail',
        name:'OneNetcomDetail',
        component:OneNetcomDetail
    },
    {
        path: '/supplementAll',
        name:'SupplementAll',
        component:SupplementAll
    },
    {
        path: '/supplementTransform',
        name:'SupplementTransform',
        component:SupplementTransform
    },
    {
        path: '/creditPayStandard',
        name:'CreditPayStandard',
        component:CreditPayStandard
    },
    {
        path: '/creditPaySign',
        name:'CreditPaySign',
        component:CreditPaySign
    },
    {
        path: '/finished',
        name:'Finished',
        component:Finished
    },
    {
        path: '/profession',
        name:'Profession',
        component:Profession
    },
    // {
    //     path:'/operationDetail',
    //     name:'OperationDetail',
    //     component:OperationDetail
    // },
    {
        path: '/echanelPay',
        name:'EchanelPay',
        component:EchanelPay
    },
    {
        path: '/storeDeposit',
        name:'StoreDeposit',
        component:StoreDeposit
    },
    {
        path: '/bldMaintain',
        name:'BldMaintain',
        component:BldMaintain,
        meta: {
            keepAlive: true
        }
    },
    {
        path: '/bottomBld',
        name:'BottomBld',
        component:BottomBld
    },
    {
        path: '/myBld',
        name:'MyBld',
        component:MyBld,
        meta: {
            keepAlive: true
        }
    },
    {
        path: '/bldArea',
        name:'BldArea',
        component:BldArea
    },
	{
        path: '/promotionEntry',
        name:'PromotionEntry',
        component:PromotionEntry
    },
    {
        path: '/promotionPici',
        name:'PromotionPici',
        component:PromotionPici
    },
    {
        path: '/promotionReward',
        name:'PromotionReward',
        component:PromotionReward
    },
    {
       path: '/promotionOrder',
       name:'PromotionOrder',
       component:PromotionOrder
    },
    {
       path: '/promotionSubmit',
       name:'PromotionSubmit',
       component:PromotionSubmit
    },
    {
       path: '/promotionBuyQrCode',
       name:'PromotionBuyQrCode',
       component:PromotionBuyQrCode
    },
    {
       path: '/promotionBuySign',
       name:'PromotionBuySign',
       component:PromotionBuySign
    },
    {
       path: '/promotionBuyWithdraw',
       name:'PromotionBuyWithdraw',
       component:PromotionBuyWithdraw
    },
  {
    path: '/promotionZhiMaSign',
    name:'PromotionZhiMaSign',
    component:PromotionZhiMaSign
  },  {
    path: '/promotionPiciShopCart',
    name:'PromotionPiciShopCart',
    component:PromotionPiciShopCart
  },
  {
    path: '/promotionRewardShopCart',
    name:'PromotionRewardShopCart',
    component:PromotionRewardShopCart
  },
    {
        path: '/terminalscaling',
        name:'Terminalscaling',
        component:Terminalscaling
    },
    {
        path: '/terminalsalesubmit',
        name:'Terminalsalesubmit',
        component:Terminalsalesubmit
    },
    {
        path: '/meshOpportunity',
        name:'MeshOpportunity',
        component:MeshOpportunity
    },
    {
        path: '/netGain',
        name:'NetGain',
        component:NetGain
    },
    {
        path: '/opportunityProgress',
        name: 'OpportunityProgress',
        component: OpportunityProgress
    },
    {
        path: '/alreadyScrape',
        name:'AlreadyScrape',
        component:AlreadyScrape
    },
    {
        path: '/govermentBusiLog',
        name:'GovernmentBusiLog',
        component:GovernmentBusiLog
    },
    {
        path: '/gridViewThird',
        name:'GridViewThird',
        component:GridViewThird
    },
    {
        path:'/groupInfoAccess',
        name:'GroupInfoAccess',
        component:GroupInfoAccess
    },
  {
    path:'/groupInfoModify',
    name:'GroupInfoModify',
    component:GroupInfoModify
  },
    {
        path:'/groupAccessList',
        name:'GroupAccessList',
        component:GroupAccessList
    },
    {
        path:'/groupSelectMeal',
        name:'GroupSelectMeal',
        component:GroupSelectMeal
    },
    {
        path:'/groupMealDetail',
        name:'GroupMealDetail',
        component:GroupMealDetail
    },
    {
        path:'/groupChargeDetail',
        name:'GroupChargeDetail',
        component:GroupChargeDetail
    },
    {
        path:'/groupCardBack',
        name:'GroupCardBack',
        component:GroupCardBack
    },
    {
        path:'/groupSelectNumber',
        name:'GroupSelectNumber',
        component:GroupSelectNumber
    },
    // {
    //     path: '/groupBusiLogDetail',
    //     name:'GroupBusiLogDetail',
    //     component:GroupBusiLogDetail
    // },
    {
        path:'/familyOpportunity',
        name:'FamilyOpportunity',
        component:FamilyOpportunity
    },
    {
        path:'/attendanceDeploy',
        name:'AttendanceDeploy',
        component:AttendanceDeploy
    },
    {
        path:'/attendanceRule',
        name:'AttendanceRule',
        component:AttendanceRule
    },
    {
        path:'/attendanceRecord',
        name:'AttendanceRecord',
        component:AttendanceRecord
    },
    {
        path:'/attendanceDetail',
        name:'AttendanceDetail',
        component:AttendanceDetail
    },
    {
        path: '/organizationRelation',
        name: 'OrganizationRelation',
        component: OrganizationRelation
    },
    {
        path: '/newCreditStandard',
        name: 'NewCreditStandard',
        component: NewCreditStandard
    },
    {
        path: '/newCreditChild',
        name: 'NewCreditChild',
        component: NewCreditChild,
        meta: {
            keepAlive: true
        }
    },
    {
        path: '/newCreditDetail',
        name: 'NewCreditDetail',
        component: NewCreditDetail
    },
    {
        path: '/newCreditStandardOrder',
        name: 'NewCreditStandardOrder',
        component: NewCreditStandardOrder
    },
    {
        path: '/newCreditBuyStandard',
        name: 'NewCreditBuyStandard',
        component: NewCreditBuyStandard
    },
    {
        path: '/newCreditBuyWithdraw',
        name: 'NewCreditBuyWithdraw',
        component: NewCreditBuyWithdraw
    },
    {
        path: '/newCreditBuySign',
        name: 'NewCreditBuySign',
        component: NewCreditBuySign
    },
    {
        path: '/newHappyIndex',
        name: 'NewHappyIndex',
        component: NewHappyIndex
    },
    {
        path: '/newHappyBuySign',
        name: 'NewHappyBuySign',
        component: NewHappyBuySign
    },
    {
        path: '/gridWorkMain',
        name: 'GridWorkMain',
        component: GridWorkMain
    },
    {
        path: '/backlogWork',
        name: 'BacklogWork',
        component:BacklogWork 
    },
    {
       path: '/workHandle',
       name: 'WorkHandle',
       component:WorkHandle
    },
    {
        path: '/shopBusiMap',
        name: 'shopBusiMap',
        component: shopBusiMap
    },
    {
        // 商铺商机反馈
        path: '/shopBusiFeedback',
        name: 'shopBusiFeedback',
        component: shopBusiFeddBack
    },
    {
       path: '/workCheck',
       name: 'WorkCheck',
       component:WorkCheck
    },
    {
        path:'/newGridHomework',
        name:'NewGridHomework',
        component: NewGridHomework
    },
    {
        path:'/underway',
        name:'Underway',
        component: Underway
    },
    {
        path: '/gridHomeworkTemp',
        name:'GridHomeworkTemp',
        component: GridHomeworkTemp
    },
    {
        path: '/homeworkExecute',
        name: 'HomeworkExecute',
        component: HomeworkExecute
    },
    {
        path: '/busiLogIndex',
        name: 'BusiLogIndex',
        component: BusiLogIndex
    },
    {
        path: '/busiLogDetailInfo',
        name: 'BusiLogDetailInfo',
        component: BusiLogDetailInfo
    },
    {
        path: '/kuandaiTrajectory',
        name: 'KuandaiTrajectory',
        component: KuandaiTrajectory
    },
    {
        path: '/selectPackage',
        name: 'SelectPackage',
        component: SelectPackage
    },
    {
        path: '/calculatePackage',
        name: 'CalculatePackage',
        component: CalculatePackage
    },
    {
        path: '/installCoordinate',
        name: 'InstallCoordinate',
        component: InstallCoordinate
    },
    {
        path: '/installListSearch',
        name: 'InstallListSearch',
        component: InstallListSearch
    },
    {
        path: '/installToDoList',
        name: 'InstallToDoList',
        component: InstallToDoList
    },
    {
        path: '/installToDoListDetail',
        name: 'InstallToDoListDetail',
        component: InstallToDoListDetail
    },
    {
        path: '/groupInstallListDetail',
        name: 'GroupInstallListDetail',
        component: GroupInstallListDetail
    },
    {
        path: '/grabKaiHuActivity',
        name: 'GrabKaiHuActivity',
        component: GrabKaiHuActivity
    },
    {
        path: '/exam',
        name: 'Exam',
        component: Exam
    },
    {
        path: '/workOrders',
        name:'WorkOrders',
        component:WorkOrders
    },
    {
        path: '/workDetails',
        name:'WorkDetails',
        component:WorkDetails
    },
    {
        path: '/yijiNew',
        name: 'YiJiNew',
        component: YiJiNew
    },
    {
        path: '/hometownAdd',
        name:'HometownAdd',
        component:HometownAdd
    },
    {
        path:'/dropOrder',
        name:'DropOrder',
        component:DropOrder
    },
    {
         path: '/orderDetailed',
         name: 'OrderDetailed',
         component: OrderDetailed
    },
    {
        path: '/buildingViewPlan',
        name:'BuildingViewPlan',
        component:BuildingViewPlan
    },
    {
        path: '/buildingViewDetail',
        name:'BuildingViewDetail',
        component:BuildingViewDetail
    },
    {
        path: '/buildingInfoCollection',
        name:'BuildingInfoCollection',
        component:BuildingInfoCollection
    },
    {
        path: '/groupBusiRec',
        name:'GroupBusiRec',
        component:GroupBusiRec
    },
    {
        path: '/personBusiRec',
        name:'PersonBusiRec',
        component:PersonBusiRec
    },
    {
        path: '/personBusiResidentUser',
        name:'PersonBusiResidentUser',
        component:PersonBusiResidentUser
    },
    {
        path: '/personBusiRecDetail',
        name:'PersonBusiRecDetail',
        component:PersonBusiRecDetail
    },
    {
        path: '/buildingBusiReport',
        name:'BuildingBusiReport',
        component:BuildingBusiReport
    },
    {
        path: '/buildingViewInfo',
        name:'BuildingViewInfo',
        component:BuildingViewInfo
    },
    {
        path: '/buildingViewMustDo',
        name:'BuildingViewMustDo',
        component:BuildingViewMustDo
    },
    {
        path:'/yuCancellation',
        name:'YuCancellation',
        component:YuCancellation
    },
    {
        path:'/searchMenu',
        name:'SearchMenu',
        component:SearchMenu
    },
    {
        path:'/vNetDimension',
        name:'VNetDimension',
        component:VNetDimension
    },
    {
        path:'/shortSignal',
        name:'ShortSignal',
        component:ShortSignal
    },
    {
        path:'/vNetShort',
        name:'VNetShort',
        component:VNetShort
    },
   {
     path:'/vNetLongShortNumber',
     name:'VNetLongShortNumber',
     component:VNetLongShortNumber
   },
    {
        path:'/unSatisfyList',
        name:'UnSatisfyList',
        component:UnSatisfyList
    },
    {
        path:'/unSatisfyDetail',
        name:'UnSatisfyDetail',
        component:UnSatisfyDetail
    },
    {
        path:'/collegeWelcomeArea',
        name:'CollegeWelcomeArea',
        component:CollegeWelcomeArea
    },
    {
        path:'/busiInterAnalyse',
        name:'BusiInterAnalyse',
        component:BusiInterAnalyse
    },
    {
        path:'/peopleFlowMonitor',
        name:'PeopleFlowMonitor',
        component:PeopleFlowMonitor
    },
    {
        path:'/infoCollectionComp',
        name:'InfoCollectionComp',
        component:InfoCollectionComp
    },
    {
        path:'/collegeWelcomeBusiDetail',
        name:'CollegeWelcomeBusiDetail',
        component:CollegeWelcomeBusiDetail
    },
    {
        path: '/salesappointment',
        name: 'salesappointment',
        component: salesappointment
    },
    {
        path: '/amsbusilog',
        name: 'amsbusilog',
        component: amsbusilog
    },
    {
        path:'/gridService',
        name:'GridService',
        component:GridService
    },
    {
        path:'/hostMonitor',
        name:'HostMonitor',
        component:HostMonitor
    },
    {
        path:'/gridManaReport',
        name:'GridManaReport',
        component:GridManaReport
    },
    {
        path: '/supplementPanc',
        name: 'SupplementPanc',
        component: SupplementPanc
    },
    {
        path: '/suppSubmitPanc',
        name: 'SuppSubmitPanc',
        component: SuppSubmitPanc
    },
    {
        path: '/suppCommonPanc',
        name: 'SuppCommonPanc',
        component: SuppCommonPanc
    },
    {
        path: '/groupPointInfoSync',
        name: 'GroupPointInfoSync',
        component: GroupPointInfoSync
    },
    {
        path:'/buildingPointInfoSync',
        name:'BuildingPointInfoSync',
        component: BuildingPointInfoSync
    },
    {
        path: '/listGroupPlan',
        name: 'ListGroupPlan',
        component: ListGroupPlan
    },
    {
        path: '/listGroupDetail',
        name: 'ListGroupDetail',
        component: ListGroupDetail
    },
    {
        path: '/listGroupDetailRead',
        name: 'ListGroupDetailRead',
        component: ListGroupDetailRead
    },
    {
        path: '/listGroupBusiRec',
        name: 'ListGroupBusiRec',
        component: ListGroupBusiRec
    },
    {
        path: '/listGroupBusiStep',
        name: 'ListGroupBusiStep',
        component: ListGroupBusiStep
    },
    {
        path: '/listGroupCenterChoose',
        name: 'ListGroupCenterChoose',
        component: ListGroupCenterChoose
    },
    {
        path: '/listGroupBusiReport',
        name: 'ListGroupBusiReport',
        component: ListGroupBusiReport
    },
    {
        path: '/listGroupAlienNetWork',
        name: 'ListGroupAlienNetWork',
        component: ListGroupAlienNetWork
    },
    {
        path:'/myLogList',
        name:'MyLogList',
        component:MyLogList
    },
    {
        path:'/daiWeiActiveRecommd',
        name:'DaiWeiActiveRecommd',
        component:DaiWeiActiveRecommd
    },
    {
        path:'/channelList',
        name:'ChannelList',
        component:ChannelList
    },
    {
        path:'/channelListDetail',
        name:'ChannelListDetail',
        component:ChannelListDetail
    },
    {
        path: '/oneNumMoreBand',
        name: 'OneNumMoreBand',
        component: OneNumMoreBand
    },
    {
        path: '/moreBandKaiTong',
        name: 'MoreBandKaiTong',
        component: MoreBandKaiTong
    },
    {
        path:'/personalView',
        name:'PersonalView',
        component:PersonalView
    },
    {
        path:'/jiKeGroupHome',
        name:'JiKeGroupHome',
        component:JiKeGroupHome
    },
    {
        path:'/jiKeGroupDetail',
        name:'JiKeGroupDetail',
        component:JiKeGroupDetail
    },
    {
        path:'/jiKeGroupMember',
        name:'JiKeGroupMember',
        component:JiKeGroupMember
    },
    {
        path:'/jiKeGroupOrderList',
        name:'JiKeGroupOrderList',
        component:JiKeGroupOrderList
    },
    {
        path:'/jiKeGroupProduct',
        name:'JiKeGroupProduct',
        component:JiKeGroupProduct
    },
    {
        path:'/jiKeGroupProductDetail',
        name:'JiKeGroupProductDetail',
        component:JiKeGroupProductDetail
    },
    {
        path:'/jiKeGroupProductSearch',
        name:'JiKeGroupProductSearch',
        component:JiKeGroupProductSearch
    },
    {
        path:'/jiKeSearchJt',
        name:'JiKeSearchJt',
        component:JiKeSearchJt
    },
    {
        path:'/zhengQiDrawPlan',
        name:'ZhengQiDrawPlan',
        component:ZhengQiDrawPlan
    },
    {
        path:'/yxTask',
        name:'YxTask',
        component:YxTask
    },

	{
        path: '/governmentIndex',
        name: 'GovernmentIndex',
        component: GovernmentIndex
    },
    {
        path: '/myGb',
        name: 'MyGb',
        component: MyGb
    },
    {
        path:'/governmentBusniess',
        name:'GovernmentBusniess',
        component:GovernmentBusniess
    },{
        path:'/governmentSet',
        name:'GovernmentSet',
        component:GovernmentSet
    },
    {
        path:'/governAdv',
        name:'GovernAdv',
        component:GovernAdv
    },
    {
        path:'/zhengQiBusiLogIndex',
        name:'ZhengQiBusiLogIndex',
        component:ZhengQiBusiLogIndex
    },
    {
        path:'/zhengQiBusiLogDetail',
        name:'ZhengQiBusiLogDetail',
        component:ZhengQiBusiLogDetail
    },
    {
        path:'/zhengQiBusiRecommend',
        name:'ZhengQiBusiRecommend',
        component:ZhengQiBusiRecommend
    },
    {
        path:'/frameBox',
        name:'FrameBox',
        component:FrameBox
    },
    {
        path:'/grabSheetNew',
        name:'GrabSheetNew',
        component:GrabSheetNew,
        meta: {
            keepAlive: true
        }
    },
    {
        path:'/grabSheetNewForWg',
        name:'GrabSheetNewForWg',
        component:GrabSheetNewForWg,
        meta: {
            keepAlive: true
        }
    },
    {
        path:'/sheetDetailNew',
        name:'SheetDetailNew',
        component:SheetDetailNew
    },
    {
        path:'/sheetDetailNewForWg',
        name:'SheetDetailNewForWg',
        component:SheetDetailNewForWg
    },
    {
        path:'/sheetListNew',
        name:'SheetListNew',
        component:SheetListNew
    },
    {
        path:'/sheetListNewForWg',
        name:'SheetListNewForWg',
        component:SheetListNewForWg
    },
    {
        path: '/groupBusiLogDetail',
        name:'GroupBusiLogDetail',
        component:GroupBusiLogDetail
    },
    {
        path:'/paiDanListNew',
        name:'PaiDanListNew',
        component:PaiDanListNew
    },
    {
        path:'/paiDanListNewForWg',
        name:'PaiDanListNewForWg',
        component:PaiDanListNewForWg
    },
    {
        path:'/adminGovernment',
        name:'AdminGovernment',
        component:AdminGovernment
    },
    {
        path:'/adminGoverBusniess',
        name:'AdminGoverBusniess',
        component:AdminGoverBusniess
    },
    {
        path:'/adminMyGb',
        name:'AdminMyGb',
        component:AdminMyGb
    },
    {
        path:'/adminYxTask',
        name:'AdminYxTask',
        component:AdminYxTask
    },
    {
      path: '/jiKeFullOrderList',
      name:'JiKeFullOrderList',
      component: JiKeFullOrderList
    },{
      path:'/allHomeWifi',
      name:'AllHomeWifi',
      component:AllHomeWifi
   },{
      path:'/qinQingKaiTong',
      name:'QinQingKaiTong',
      component:QinQingKaiTong
   },{
      path:'/zengZhiKaiTong',
      name:'ZengZhiKaiTong',
      component:ZengZhiKaiTong
   },
   {
       path:'/busiPreDealing',
       name:'BusiPreDealing',
       component:BusiPreDealing
   },
   {
       path:'/easyOverBook',
       name:'EasyOverBook',
       component:EasyOverBook
   },
    {
        path: '/invoiceList',
        name: 'InvoiceList',
        component: InvoiceList
    },
    {
        path: '/InvoiceDetail',
        name: 'InvoiceDetail',
        component: InvoiceDetail
    },
    {
        path: '/pullSheetList',
        name: 'PullSheetList',
        component: PullSheetList
    },
    {
        path: '/pullSheetListForWg',
        name: 'PullSheetListForWg',
        component: PullSheetListForWg
    },
    {
        path: '/logisticsInquiry',
        name: 'LogisticsInquiry',
        component: LogisticsInquiry
    },
    {
      path:'/fastCarryingCardBack',
      name:'FastCarryingCardBack',
      component:FastCarryingCardBack
    },
      {
        path:'/maskTask',
        name:'MaskTask',
        component:MaskTask
      },
    {
        path: '/grabSheetReport',
        name: 'GrabSheetReport',
        component: GrabSheetReport
    },
    {
        path: '/grabHorizontalTable',
        name: 'GrabHorizontalTable',
        component: GrabHorizontalTable
    },
    {
      path:'/storeInfoCollectList',
      name:'StoreInfoCollectList',
      component: StoreInfoCollectList
    },
    {
        path:'/storeManager',
        name:'StoreManager',
        component: StoreManager
    },
	{
        path: '/groupBusiChanceCollect',
        name: 'GroupBusiChanceCollect',
        component: GroupBusiChanceCollect
    } ,
    {
        path: '/marketProductZz',
        name: 'MarketProductZz',
        component: MarketProductZz
    },
	{
        path:'/complainOrder',
        name:'ComplainOrder',
        component:ComplainOrder
    },{
        path:'/complainOrderList',
        name:'ComplainOrderList',
        component:ComplainOrderList
    },{
        path:'/complainOrderListDetail',
        name:'ComplainOrderListDetail',
        component:ComplainOrderListDetail
    },{
        path:'/groupBusiLogNew',
        name:'GroupBusiLogNew',
        component: GroupBusiLogNew
    },
    {
        path: '/familyIndicators',
        name: 'FamilyIndicators',
        component: FamilyIndicators
    },
    {
        path: '/indicatorsFiltrate',
        name: 'IndicatorsFiltrate',
        component: IndicatorsFiltrate
    },
    {
        path: '/villageToPut',
        name: 'VillageToPut',
        component: VillageToPut
    },
    {
      path: '/householdTask',
      name: 'HouseholdTask',
      component: HouseholdTask
    },
    {
        path:'/yuPeiHaoUrgent',
        name:'YuPeiHaoUrgent',
        component: YuPeiHaoUrgent
    },
  {
    path:'/customerCareEnquiry',
    name:'CustomerCareEnquiry',
    component: CustomerCareEnquiry
  },
    {
        path:'/reserveSheetDetail',
        name:'ReserveSheetDetail',
        component: ReserveSheetDetail
    },
    {
        path:'/reserveSheetDetailForWg',
        name:'ReserveSheetDetailForWg',
        component: ReserveSheetDetailForWg
    },
    {

        path: '/logStatistics',
        name: 'LogStatistics',
        component: LogStatistics
    },
    {
        path: '/logList',
        name: 'LogList',
        component: LogList
    },
    {
        path:'/serviceReserve',
        name:'ServiceReserve',
        component: ServiceReserve
    },
  //终端裸售维修换新
  {
    path:'/terminalsaleWeiXi',
    name: 'TerminalsaleWeiXi',
    component: () => import('components/business/terminalnakedsale/terminalsaleWeiXi.vue')
  },
  {
    path: '/groupInfoSupplyAdd',
    name: 'GroupInfoSupplyAdd',
    component: GroupInfoSupplyAdd
  },
  {
    //地图测试
    path:'/MapContainer',
    name:'MapContainer',
    component:() => import('../../components/map/index.vue')
  },
]
