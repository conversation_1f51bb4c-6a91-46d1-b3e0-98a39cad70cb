export default [
  {
    path: '/yupeihaoMain',
    name: 'yupeihaoMain',
    component: () => import('components/business/yupeihaoMerge/Yupeihao-main.vue')
  },
  {//预配号融合版本的的提交界面
    path: '/submitNetwork',
    name: 'submitNetwork',
    component: () => import('components/business/yupeihaoMerge/SubmitNetwork.vue')
  },
  {
      path: '/PersonOver',
      name: 'PersonOver',
      component: () => import('components/PersonalBusinessOpportunity/PersonOver.vue')
  },
    // 商机上报智能体
    {
        path: '/busiReportAgent',
        name: 'BusiReportAgent',
        component: () => import('components/PersonalBusinessOpportunity/agent/BusiReportAgent.vue')
    },
  // 路由拉起页面
  {
    path: '/routerPage',
    name: 'routerPage',
    component: () => import('components/aiGroup/routerPage.vue')
  },
  // 国漫套餐
  {
    path: '/gmHome',
    name: 'gmHome',
    component: () => import('components/aiGroup/gmHome.vue')
  },
  // 客户洞察
  {
    path: '/clientInsight',
    name: 'clientInsight',
    component: () => import('components/aiGroup/clientInsight.vue')
  },
  // 户型图
  {
    path: '/houseType',
    name: 'houseType',
    component: () => import('components/aiGroup/houseType.vue')
  },
  {
    path: '/invoiceDialogue',
    name: 'InvoiceDialogue',
    component: () => import('components/business/AIInvoicing/InvoiceDialogue.vue')
  },
  // 每日一练
  {
    path: '/dayPractice',
    name: 'dayPractice',
    component: () => import('components/aiGroup/dayPractice.vue')
  },
  // 营业厅知识库
  {
    path: '/businessHall',
    name: 'businessHall',
    component: () => import('components/aiGroup/businessHall.vue')
  }, {
    path: '/inTransitDeviceReceive',
    name: 'InTransitDeviceReceive',
    component: () => import('components/business/BandTvMarket/InTransitDeviceReceive.vue')
  }, {
    path: '/aiTuiJian',
    name: 'aiTuiJian',
    component: () => import('components/business/xuanhaoruwang/AiTuiJian.vue')
  },
    {
        path: '/PersonAddAndOver',
        name: 'PersonAddAndOver',
        component: () => import('components/PersonalBusinessOpportunity/PersonAddAndOver.vue')
    },
    {
        path: '/hisInfoList',
        name: 'hisInfoList',
        component: () => import('components/PersonalBusinessOpportunity/hisInfoList.vue')
    },
    {
        path: '/GroupWarnOrderList',
        name: 'GroupWarnOrderList',
        component: () => import('components/my/groupWarningTask/GroupWarnOrderList.vue')
    },
    {
      path: '/yupeihaoMainTest',
      name: 'yupeihaoMainTest',
      component: () => import('components/business/yupeihaoMerge/yupeihao-test.vue')
    },
    {
        path: '/commonTaskTemple2',
        name: 'commonTaskTemple2',
        component: () => import('components/my/myTask/commonTaskTemple2.vue')
    },
    {
        path: '/groupPersonTransfer',
        name: 'groupPersonTransfer',
        component: () => import('components/business/GroupCard/groupEnterInfoChange/groupPersonTransfer.vue')
    },
    {
        path: '/imsDataModify',
        name: 'imsDataModify',
        component: () => import('components/area/areaBusiness/imsDataModify.vue')
    },
    {
      path: '/keepTokenTestPage',
      name: 'KeepTokenTestPage',
      component: () => import('components/desk/jiaKeInstallMain/keepTokenTestPage.vue')
    },
    {
        path: '/PersonOverNew',
        name: 'PersonOverNew',
        component: () => import('components/PersonalBusinessOpportunity/PersonOverNew.vue')
    },
    {
        path: '/PersonAddAndOverNew',
        name: 'PersonAddAndOverNew',
        component: () => import('components/PersonalBusinessOpportunity/PersonAddAndOverNew.vue')
    },
    {
      path: '/oneClickOrderQuery',
      name: 'oneClickOrderQuery',
      component: () => import('components/business/fusionShoppingCart/orderquery/oneClickOrderQuery.vue')
    },
]
