export default [
  //双向协同业务
  {
    path: '/twoWayUserList',
    name: 'twoWayUserList',
    component: () => import('components/business/twowayteamwork/userList.vue')
  },
  //双向协同工单页面查询
  {
    path: '/twoWayUserOrderList',
    name: 'twoWayUserOrderList',
    component: () => import('components/business/twowayteamwork/userOrderList.vue')
  },
  {
    path: '/twoWayOrderDetail',
    name: 'twoWayOrderDetail',
    component: () => import('components/business/twowayteamwork/orderDetail.vue')
  },
  //双向协同客户视图
  {
    path: '/twoPeopleCsViewIn',
    name: 'twoPeopleCsViewIn',
    component: () => import('components/business/twowayteamwork/csViewInNew.vue')
  },
  {
    path: '/xqKuanDaiPre',
    name: 'XqKuanDaiPre',
    component: () => import('components/business/BandTvMarket/XqKuanDaiPre.vue')
  },
  // 外呼
  {
    path: '/outbound',
    name: 'outbound',
    component: () => import('components/common/OutboundNew/Outbound.vue')
  },
  {
    path: '/authTest',
    name: 'AuthTest',
    component: () => import('components/test/AuthTest.vue')
  },
  // 特殊选号入网(无人证比对、视频采集)
  {
    path: '/realNameCertifyNoCheck',
    name: 'realNameCertifyNoCheck',
    component: () => import('components/business/xuanHaoRuWangNoCheck/RealNameCertify.vue')
  },
  {
    path: '/selectNumberNoCheck',
    name: 'selectNumberNoCheck',
    component: () => import('components/business/xuanHaoRuWangNoCheck/SelectNumber.vue')
  },
  {
    path: '/mealDetailNoCheck',
    name: 'mealDetailNoCheck',
    component: () => import('components/business/xuanHaoRuWangNoCheck/MealDetail.vue')
  },
  {
    path: '/selectMealNoCheck',
    name: 'selectMealNoCheck',
    component: () => import('components/business/xuanHaoRuWangNoCheck/SelectMeal.vue')
  },
  {
    path: '/chargeDetailNoCheck',
    name: 'chargeDetailNoCheck',
    component: () => import('components/business/xuanHaoRuWangNoCheck/ChargeDetail.vue')
  },
  {
    path: '/cardBackNoCheck',
    name: 'cardBackNoCheck',
    component: () => import('components/business/xuanHaoRuWangNoCheck/CardBack.vue')
  },
  // 特殊预配号(无人证比对、视频采集)
  {
    path: '/yuPeiHaoNoCheck',
    name: 'yuPeiHaoNoCheck',
    component: () => import('components/yupeihao/YuPeiHaoNoCheck.vue')
  },
  // 业务回退新
  {
    path: '/businessBackNew',
    name: 'businessBackNew',
    component: () => import('components/business/BusinessBackNew.vue')
  },
  //终端销售商机
  {
    path: '/terminalOrderList',
    name: 'TerminalOrderList',
    component: () => import('components/business/TerminalNicheSales/TerminalOrderList.vue'),
    meta: {keepAlive: true}
  },
  {
    path: '/terminalSubmit',
    name: 'TerminalSubmit',
    component: () => import('components/business/TerminalNicheSales/TerminalSubmit.vue')
  },
  {
    path: '/terminalSales',
    name: 'TerminalSales',
    component: () => import('components/business/TerminalNicheSales/TerminalSales.vue')
  },
  {
    path: '/zxReservationSheet',
    name: 'zxReservationSheet',
    component: () => import('components/business/dqReservation/zxReservationSheet.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: '/zxReservationDetail',
    name: 'zxReservationDetail',
    component: () => import('components/business/dqReservation/zxReservationDetail.vue')
  },
  // 报表专区
  {
    path: '/reportZone',
    name: 'reportZone',
    component: () => import('components/business/ReportZone/ReportZone.vue')
  },
  //成员维护
  {
    path: '/memMaintenance',
    name: 'MemMaintenance',
    component: () => import('components/my/buildingview/MemMaintenance.vue')
  },
  // 终端4升5
  {
    path: '/terminalUpgrade',
    name: 'terminalUpgrade',
    component: () => import('components/business/TerminalUpgrade/TerminalUpgrade.vue')
  },
  // 终端4升5转化记录
  {
    path: '/conversionRecord',
    name: 'conversionRecord',
    component: () => import('components/business/TerminalUpgrade/ConversionRecord.vue')
  },
  // 激励明细统计新
  {
    path: '/analyzeDetail',
    name: 'analyzeDetail',
    component: () => import('components/business/IncentiveDetail/analyzeDetail.vue')
  },
  {
    path: '/twoWayActiveRecommd',
    name: 'twoWayActiveRecommd',
    component: () => import('components/business/twowayteamwork/activeRecommd.vue')
  }, //融合快选包列表
  {
    path: '/employeeFastOfferManage',
    name: 'EmployeeFastOfferManage',
    component: () => import('components/business/ShoppingCart/EmployeeFastOfferManage.vue')
  },
  {
    path: '/dqEquipmentOrderSheet',
    name: 'DqEquipmentOrderSheet',
    component: () => import('components/business/DqEquipmentRecovery/DqEquipmentOrderSheet.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: '/eqOrderListSheet',
    name: 'EqOrderListSheet',
    component: () => import('components/business/DqEquipmentRecovery/EqOrderListSheet.vue')
  },
  {
    path: '/eqOrderPaidan',
    name: 'EqOrderPaidan',
    component: () => import('components/business/DqEquipmentRecovery/EqOrderPaidan.vue')
  },
  {
    path: '/eqOrderPullSheet',
    name: 'EqOrderPullSheet',
    component: () => import('components/business/DqEquipmentRecovery/EqOrderPullSheet.vue')
  },
  {
    path: '/eqOrderSheetDetail',
    name: 'EqOrderSheetDetail',
    component: () => import('components/business/DqEquipmentRecovery/EqOrderSheetDetail.vue')
  },
  {
    path: '/twoWayUserOrderListPre',
    name: 'twoWayUserOrderListPre',
    component: () => import('components/business/twowayteamwork/userOrderListPre.vue')
  },
  {
    path: '/fusionCar',
    name: 'fusionCar',
    component: () => import('components/business/fusionShoppingCart/fusionCar.vue')
  },
  {
    path: '/fusionPlaceOrder',
    name: 'fusionPlaceOrder',
    component: () => import('components/business/fusionShoppingCart/fusionPlaceOrder.vue')
  },
  {
    path: '/fusionSubmit',
    name: 'fusionSubmit',
    component: () => import('components/business/fusionShoppingCart/fusionSubmit.vue')
  },
  {
    path: '/AccomInfoCollectionNew',
    name: 'AccomInfoCollectionNew',
    component: () => import('components/my/shopsview/AccomInfoCollectionNew.vue')
  },
    {
        path: '/authenticationClient',
        name: 'AuthenticationClient',
        component: () => import('components/desk/AuthenticationClient.vue')
    },
    {
        path: '/groupFeedBack',
        name: 'GroupFeedBack',
        component: () => import('components/my/myGroupNew/GroupFeedBack.vue')
    },
    {
        path: '/accInfoCollect',
        name: 'AccInfoCollect',
        component: () => import('components/my/shopsview/AccInfoCollect.vue')
    },
    {
        path: '/shopsNew',
        name: 'ShopsNew',
        component: () => import('components/my/shopsview/ShopsNew.vue')
    },
  {
    path: '/buildingOCRidentification',
    name: 'buildingOCRidentification',
    component: () => import('components/my/buildingview/buildingOCRidentification.vue')
  },
  // 充值记录查询
  {
    path: '/rechargeRecordQuery',
    name: 'RechargeRecordQuery',
    component: () => import('components/business/RechargeRecordQuery/RechargeRecordQuery.vue')
  },
  {
    path: '/netOrderManageList',
    name: 'NetOrderManageList',
    component: () => import('components/business/NetOrderManage/NetOrderManageList.vue'),
    meta: {
      keepAlive: true
    },
  },
  // 非二代证入网
  {
    path: '/otherCardRealNameCertify',
    name: 'OtherCardRealNameCertify',
    component: () => import('components/business/xuanhaoruwang/OtherCardRealNameCertify.vue')
  },
  {
    path: '/netOrderManageDetail',
    name: 'NetOrderManageDetail',
    component: () => import('components/business/NetOrderManage/NetOrderManageDetail.vue'),
    meta: {
      keepAlive: true
    }
  },
  {
    path: '/AboutList',
    name: 'AboutList',
    component: () => import('components/my/AboutList.vue')
  },
  // 使用第三方SDKS
  {
    path: '/usingSDKS',
    name: 'UsingSDKS',
    component: () => import('components/my/UsingSDKS.vue')
  },
  // 个人隐私清单
  {
    path: '/usingPerInfo',
    name: 'UsingPerInfo',
    component: () => import('components/my/UsingPerInfo.vue')
  },
  //装维协同单
  {
    path: '/zhuangweiCollaborateOrder',
    name: 'ZhuangweiCollaborateOrder',
    component: () => import('components/business/zhuangweiCollaborateOrder/ZhuangweiCollaborateOrder.vue')
  },
  // 紧急业务办理
  {
    path: '/urgentBusiness',
    name: 'urgentBusiness',
    component: () => import('components/business/UrgentBusiness/UrgentBusiness.vue')
  },
  // 商机活动管理
  {
    path: '/terminalRecommendConfig',
    name: 'terminalRecommendConfig',
    component: () => import('components/business/TerminalRecommend/TerminalActiveManage.vue')

  },
  // 云电脑
  {
    path: '/cloudComputer',
    name: 'cloudComputer',
    component: () => import('components/business/securityService/CloudComputerEntry.vue')
  },
  // 业务（新）
  {
    path: '/businessNew',
    name: 'businessNew',
    component: () => import('components/business/BusinessNew/BusinessNew.vue'),
    meta: {
      keepAlive: true
    }
  },
  // 工具（新）
  {
    path: '/toolsNew',
    name: 'toolsNew',
    component: () => import('components/tools/toolsNew/ToolsNew.vue'),
    meta: {
      keepAlive: true
    }
  },
  //刷新微信小程序accessToken
  {
    path: '/refershWechatToken',
    name: 'RefershWechatToken',
    component: () => import('components/business/EnterpriseVX/RefershWechatToken.vue')
  },
	// 物联网入网资料线上化
	{
		path: '/ioTDataCollectionMenu',
		name: 'IoTDataCollectionMenu',
		component: () => import('components/business/IoTDataCollection/IoTDataCollectionMenu.vue')
	},
	// 物联网入网资料线上化表单
	{
		path: '/ioTDataCollection',
		name: 'IoTDataCollection',
		component: () => import('components/business/IoTDataCollection/IoTDataCollection.vue')
	},
  {
    path: '/ioTDataCollectionNew',
    name: 'ioTDataCollectionNew',
    component: () => import('components/business/IoTDataCollection/ioTDataCollectionNew.vue')
  },
	// 物联网入网资料线上化表单
	{
		path: '/ioTDataList',
		name: 'IoTDataList',
		component: () => import('components/business/IoTDataCollection/IoTDataList.vue')
	},
    // 物联网资料PDF预览
    {
        path: '/pdfsPreview',
        name: 'PdfsPreview',
        component: () => import('components/business/IoTDataCollection/PdfsPreview.vue')
    },
	// 埋点接口测试
	{
		path: '/buryPoint',
		name: 'buryPoint',
		component: () => import('components/business/IoTDataCollection/buryPoint.vue')
	},
  {
    path: '/netWorkEntryCart',
    name: 'netWorkEntryCart',
    component: () => import('components/business/securityService/NetWorkEntryCartNew.vue'),
  },
  // 安防购物车
  {
    path: '/securityEntryShopCart',
    name: 'securityEntryShopCart',
    component: () => import('components/business/securityService/SecurityEntryShopCart.vue')
  },
  // 宽带变更购物车
  {
    path:'/bandChangeCart',
    name: 'bandChangeCart',
    component: () => import('components/business/BandTvMarket/BandChangeCart.vue')
  },
  //和力云预约单
  {
    path: '/hlyReservationSheet',
    name: 'hlyReservationSheet',
    component: () => import('components/business/dqReservation/heliyun/hlyReservationSheet.vue'),
    meta: {
      keepAlive: true
    }
  },
  //和力云预约单详情
  {
    path: '/hlyReservationDetail',
    name: 'hlyReservationDetail',
    component: () => import('components/business/dqReservation/heliyun/hlyReservationDetail.vue')
  },
  //预约单融合菜单管理
  {
    path: '/reservationFrom',
    name: 'reservationFrom',
    component: () => import('components/business/NetOrder/comm/ReservationFrom.vue')
  }, {
    path: '/faceCheckDemo',
    name: 'faceCheckDemo',
    component: () => import('components/test/FaceCheckDemo.vue')
  },
  {
    path: '/notcardyupeihao',
    name: 'notcardyupeihao',
    component: () => import('components/common/notcardyupeihao/notcradyupeihao.vue'),
  },
  {
    path: '/opurlTest',
    name: 'opurlTest',
    component: () => import('components/my/OpurlTest.vue'),
  },
    {
        path: '/NewCommunityMarketingActivities',
        name: 'NewCommunityMarketingActivities',
        component: () => import('components/business/developmentofcommunityplan/NewCommunityMarketingActivities.vue')
    },
    {
        path: '/Newtargetcustomergroup',
        name: 'Newtargetcustomergroup',
        component: () => import('components/business/developmentofcommunityplan/Newtargetcustomergroup.vue')
    },
    {
        path: '/VillageMarAct',
        name: 'VillageMarAct',
        component: () => import('components/business/developmentofcommunityplan/VillageMarAct.vue')
    },
    {
        path: '/CompetitionDetails',
        name: 'CompetitionDetails',
        component: () => import('components/business/StudyCenter/CompetitionDetails.vue')
    },
  // 日志查询
  {
    path:'/orderLogList',
    name: 'orderLogList',
    component: () => import('components/business/orderLog/orderLogList.vue')
  },
  {
    path:'/orderLogDetail',
    name: 'orderLogDetail',
    component: () => import('components/business/orderLog/orderLogDetail.vue')
  },
  //和力云管理员派单详情
  {
    path: '/hlyReservationPre',
    name: 'hlyReservationPre',
    component: () => import('components/business/dqReservation/heliyun/hlyReservationPre.vue')
  },
  //和力云管理员派单
  {
    path: '/hlyReservationPaidan',
    name: 'hlyReservationPaidan',
    component: () => import('components/business/dqReservation/heliyun/hlyReservationPaidan.vue')
  },
  //和力云拉回
  {
    path: '/hlyReservationPullSheet',
    name: 'hlyReservationPullSheet',
    component: () => import('components/business/dqReservation/heliyun/hlyReservationPullSheet.vue')
  },
  //家客业务融合退订
  {
    path: '/familyBusinessBack',
    name: 'familyBusinessBack',
    component: () => import('components/business/familybusinessback/FamilyBusinessBack.vue')
  },
  {
    path:'/subWgt',
    component: () => import('components/common/subWgt.vue')
  },
  // 协销甩单
  {
    path: '/dumpOrder',
    name: 'dumpOrder',
    component: () => import('components/business/dumpOrder/DumpOrderList.vue')
  }, {
    path: '/enterNetFusion',
    name: 'enterNetFusion',
    component: () => import('components/business/enternetfusion/EnterNetFusion.vue')
  },
  //双向协同无单甩单
  {
    path:'/noOrderRejection',
    name:'noOrderRejection',
    component:() => import('components/business/twowayteamwork/noOrderRejection.vue')
  },
  // 电子支付
  {
    path: '/electronicPay',
    name: 'electronicPay',
    component: () => import('components/common/ElectronicPay/Pay.vue')
  },
  {
    path: '/electronicPayList',
    name: 'electronicPayList',
    component: () => import('components/common/ElectronicPay/PayOrderList.vue')
  },
  {
    path:'/groupEnterNetThrowOrder',
    component: () => import('components/business/GroupCard/groupEnterNetThrow/GroupEnterNetThrowOrder.vue')
  },
  {
    path:'/groupThrowList',
    component: () => import('components/business/GroupCard/groupEnterNetThrow/GroupThrowList.vue')
  },
  // 云电脑办理
  {
    path: '/cloudComputerCart',
    name: 'cloudComputerCart',
    component: () => import('components/business/cloudComputer/CloudComputer.vue')
  },
  // 爱知TOKENtest
  {
    path: '/aiZhiTokenTest',
    name: 'aiZhiTokenTest',
    component: () => import('components/business/demotest/AiZhiTokenTest.vue')
  },
  // obs Demo
  {
    path: '/obsDemo',
    name: 'obsDemo',
    component: () => import('components/business/demotest/ObsDemo.vue')
  },
  {
    path: '/proofPass',
    name: 'proofPass',
    component: () => import('components/common/zmUpload/proofPass.vue')
  },
  {
    path: '/netcomYiJi',
    name: 'NetcomYiJi',
    component: () => import('components/business/OneNetGroup/NetcomYiJi.vue')
  },
  {
    path: '/queryGroupOneNet',
    name: 'queryGroupOneNet',
    component: () => import('components/business/oneNetYiJi/queryGroupOneNet.vue')
  },
  {
    path: '/oneNetYiJi',
    name: 'oneNetYiJi',
    component: () => import('components/business/oneNetYiJi/oneNetYiJi.vue')
  },
  {
    path: '/oneNetList',
    name: 'oneNetList',
    component: () => import('components/business/oneNetYiJi/oneNetList.vue')
  },
  // OAO营销
  {
    path: '/oAOMarketing',
    name: 'OAOMarketing',
    component: () => import('components/business/OAOMarketing/OAOMarketing.vue'),
    meta: {keepAlive: true}
  },
  {
    path: '/suppCommonAllAcross',
    name: 'suppCommonAllAcross',
    component: () => import('components/business/supplement/SuppCommonAll.vue')
  },
  {
    path: '/supplementTransformAcross',
    name: 'supplementTransformAcross',
    component: () => import('components/business/supplement/SupplementTransform.vue')
  },
  {
    path: '/groupThrowListOrder',
    name: 'groupThrowListOrder',
    component: () => import('components/business/GroupCard/groupEnterNetThrow/GroupThrowListOrder.vue')
  },
  {
    path: '/groupEnterThrowMenu',
    name: 'groupEnterThrowMenu',
    component: () => import('components/business/GroupCard/groupEnterNetThrow/groupEnterThrowMenu.vue')
  },
  {
    path: '/businessOrder',
    name: 'businessOrder',
    component: () => import('components/business/fusionShoppingCart/businessOrder.vue')
  },
  {
    path: '/BusiCustomConfig',
    name: 'BusiCustomConfig',
    component: () => import('components/business/fusionShoppingCart/BusiCustomConfig.vue')
  },
  {
    path: '/businessBackChange',
    name: 'businessBackChange',
    component: () => import('components/business/businessBack/BusinessBackNew.vue')
  },
  {
    path: '/mscHomePage',
    name: 'MscHomePage',
    component: () => import('components/firstPage/MscHomePage.vue')
  },
  {
    path: '/mSCorderDetailed',
    name: 'MSCorderDetailed',
    component: () => import('components/firstPage/MSCorderDetailed.vue')
  },
  {
    path: '/mscArticleList',
    name: 'MscArticleList',
    component: () => import('components/firstPage/MscArticleList.vue')
  },
  {
    path: '/mscBusiLogIndex',
    name: 'MSCBusiLogIndex',
    component: () => import('components/firstPage/MSCBusiLogIndex.vue')
  },
  {
    path: '/mscBusiLogDetailInfo',
    name: 'MscBusiLogDetailInfo',
    component: () => import('components/firstPage/MscBusiLogDetailInfo.vue')
  },
  {
    path: '/mscBusiKuandai',
    name: 'MscBusiKuandai',
    component: () => import('components/firstPage/MscBusiKuandai.vue')
  },
  {
    path: '/mscKuandaiTrajectory',
    name: 'MscKuandaiTrajectory',
    component: () => import('components/firstPage/MscKuandaiTrajectory.vue')
  },{
    path: '/mscOpinion',
    name: 'MscOpinion',
    component: () => import('components/firstPage/MscOpinion.vue')
  }, {
    path: '/mscPromotionPici',
    name: 'MscPromotionPici',
    component: () => import('components/firstPage/MscPromotionPici.vue')
  },
  {
    path:'/photoRepair',
    name:'photoRepair',
    component:() => import('components/tools/repairPhoto/PhotoRepair.vue')
  },
  {
      path:'/questionnaire',
      name:'questionnaire',
      component:() => import('components/business/StudyCenter/questionnaire.vue')
  },
  {
      path:'/questionnaireList',
      name:'questionnaireList',
      component:() => import('components/business/StudyCenter/questionnaireList.vue')
  },
  {
      path:'/screenRecordList',
      name:'screenRecordList',
      component:() => import('components/business/HegemonyScreen/recordList.vue')
  },
  {
    path: '/perfectFusionQuickSelect',
    name:'PerfectFusionQuickSelect',
    component:()=> import('components/business/perfectorder/PerfectFusionQuickSelect.vue')
  },
  { //降档挽留
    path:'/downshiftRetention',
    name:'downshiftRetention',
    component:() => import('components/tools/activeRecommd/DownshiftRetention.vue')
  },{
    //全家享低消
    path:'/familyLowConsumption',
    name:'familyLowConsumption',
    component:() => import('components/business/familylowconsumption/FamilyLowConsumptionNew.vue')
  },
  {
    path: '/yuPeiHaoGroupEnter',
    name: 'yuPeiHaoGroupEnter',
    component: () => import('components/business/GroupCard/yuPeiHaoGroupEnter/yuPeiHaoGroupEnter.vue')
  },
  {
    path: '/groupEnterMenu',
    name: 'groupEnterMenu',
    component: () => import('components/business/GroupCard/groupEnterMenu.vue')
  }, {
    path: '/workHorizontalTable',
    name: 'WorkHorizontalTable',
    component: () => import('components/desk/WorkStageXin/WorkHorizontalTable.vue')
  }, {
    path: '/workReportDetail',
    name: 'WorkReportDetail',
    component: () => import('components/desk/WorkStageXin/WorkReportDetail.vue')
  },
  {
    path: '/groupRealNameList',
    name: 'groupRealNameList',
    component: () => import('components/business/groupRealName/groupRealNameList.vue')
  },
  {
    path: '/groupRealName',
    name: 'groupRealName',
    component: () => import('components/business/groupRealName/groupRealName.vue')
  },
  {
    path: '/springBoard',
    name: 'springBoard',
    component: () => import('components/common/SpringBoard.vue')
  },
  // 移动爱家预算费
  {
    path: '/familyConsumptionPredict',
    name: 'familyConsumptionPredict',
    component: () => import('components/business/familylowconsumption/FamilyConsumptionPredict.vue')
  },
  { //16岁下入网组件
    path:'/sixRuwang',
    name:'sixRuwang',
    component:() => import('components/business/newNetworkAccess/SixRuwang.vue')
  },
  //16周岁下选号入网
  {
    path: '/supplementaryInformation',
    name:'supplementaryInformation',
    component:() => import('components/business/newNetworkAccess/SupplementaryInformation.vue')
  },
  //地市专区
  {
    path: '/installArea',
    name: 'installArea',
    component: () => import('components/area/cmopInstall/InstallArea.vue')
  },
  //增值语音
  {
    path: '/zengZhiCartYuYin',
    name: 'zengZhiCartYuYin',
    component: () => import('components/business/ShoppingCart/ZengZhiCartYuYin.vue')
  },
  {
    path: '/jikeOrderList',
    name: 'jikeOrderList',
    component: () => import('components/business/jikeBusinessOrder/jikeOrderList.vue')
  },
  {
    path: '/jikeOrderSum',
    name: 'jikeOrderSum',
    component: () => import('components/business/jikeBusinessOrder/jikeOrderSum.vue')
  },
  {
      path: '/competitionResults',
      name: 'competitionResults',
      component: () => import('components/business/StudyCenter/competitionResults.vue')
  },
  //目标库导入
  {
    path:'/targetImport',
    name: 'targetImport',
    component: () => import('components/business/TargetImport.vue')
  },  //登陆刷脸查找
  {
    path:'/loginFaceRecord',
    name: 'loginFaceRecord',
    component: () => import('components/tools/face/LoginFaceRecord.vue')
  },
  // OAO地市营销
  {
    path: '/oAOMarketingRegion',
    name: 'OAOMarketingRegion',
    component: () => import('components/business/OAOMarketing/OAOMarketingRegion.vue'),
    meta: {keepAlive: true}
  },
  {
      path: '/drawPlanNew',
      name: 'drawPlanNew',
      component: () => import('components/my/drawPlanNew/index')
  },
    {
        path: '/demoPage',
        name: 'demoPage',
        component: () => import('components/demoPage/index.vue')
    },
    {
        path: '/pordCase',
        name: 'PordCase',
        component: () => import('components/demoPage/PordCase.vue')
    },
    {
        path: '/groupWarnOrder',
        name: 'GroupWarnOrder',
        component: () => import('components/my/groupWarningTask/GroupWarnOrder.vue')
    },
    {
        path: '/personBusiInfo',
        name: 'PersonBusiInfo',
        component: () => import('components/my/groupWarningTask/PersonBusiInfo.vue')
    },
    {
        path: '/personMovementAlert',
        name: 'PersonMovementAlert',
        component: () => import('components/my/groupWarningTask/PersonMovementAlert.vue')
    },
    {
        path: '/alertHandleHis',
        name: 'AlertHandleHis',
        component: () => import('components/my/groupWarningTask/AlertHandleHis.vue')
    },
    {
        path: '/historyOrder',
        name: 'HistoryOrder',
        component: () => import('components/my/groupWarningTask/HistoryOrder.vue')
    },
    {
        path: '/provinWarningDeal',
        name: 'ProvinWarningDeal',
        component: () => import('components/my/provincialwarning/ProvinWarningDeal.vue')
    },
    {
        path: '/warningDealComplete',
        name: 'WarningDealComplete',
        component: () => import('components/my/provincialwarning/WarningDealComplete.vue')
    },
    {
        path: '/warnOrderList',
        name: 'WarnOrderList',
        component: () => import('components/my/provincialwarningbyiop/WarnOrderList.vue')
    },
    {
        path: '/provinWarningByIop',
        name: 'ProvinWarningByIop',
        component: () => import('components/my/provincialwarningbyiop/ProvinWarningByIop.vue')
    },
    {
        path: '/warnHisToIop',
        name: 'WarnHisToIop',
        component: () => import('components/my/provincialwarningbyiop/WarnHisToIop.vue')
    },
    {
        path: '/warnFollowListToIop',
        name: 'WarnFollowListToIop',
        component: () => import('components/my/provincialwarningbyiop/WarnFollowListToIop.vue')
    },
    {
        path: '/liHuaBack',
        name: 'LiHuaBack',
        component: () => import('components/my/provincialwarningbyiop/LiHuaBack.vue')
    },
    {
        path: '/kongBaiPage',
        name: 'KongBaiPage',
        component: () => import('components/my/provincialwarningbyiop/KongBaiPage.vue')
    },
    {
        path: '/warningRecord',
        name: 'WarningRecord',
        component: () => import('components/my/provincialwarning/WarningRecord.vue')
    },
  {
    path: '/zqWorkStage',
    name: 'zqWorkStage',
    component: () => import('components/desk/WorkStageNew/zqWorkStage.vue'),
    meta: {
      keepAlive: false
    }
  },
  {
    path: '/myGroupData',
    name: 'myGroupData',
    component: () => import('components/desk/WorkStageNew/myGroupData.vue'),
  },
  {
    path: '/subWgtUnionAuth',
    name: 'SubWgtAuthenticationClient',
    component: () => import('components/desk/SubWgtAuthenticationClient.vue')
  },
  //新已开服务
  {
    path:'/newYiKai',
    name: 'newYiKai',
    component: () => import('components/business/yikai/NewYiKai.vue')
  },
  //用户画像
  {
    path: '/userFeature',
    name: 'userFeature',
    component: () => import('components/business/userfeature/UserFeature.vue')
  },
  {
      path: '/studyHome',
      name: 'studyHome',
      component: () => import('components/business/oneClickLearning/studyHome.vue'),

  },
  {
    path: '/searchStudy',
    name: 'searchStudy',
    component: () => import('components/business/oneClickLearning/searchStudy.vue'),

  },
    {
        path: '/personList',
        name: 'PersonList',
        component: () => import('components/business/oneClickLearning/PersonList.vue')
    },
  {
      path: '/AIclassRoom',
      name: 'AIclassRoom',
      component: () => import('components/business/oneClickLearning/AIclassRoom.vue')
  },
  {
      path: '/fiveVideoHome',
      name: 'fiveVideoHome',
      component: () => import('components/business/oneClickLearning/fiveVideoHome.vue')
  },
  {
      path: '/fiveVideoAll',
      name: 'fiveVideoAll',
      component: () => import('components/business/oneClickLearning/fiveVideoAll.vue')
  },
  {
    path: '/GridEmpowerment',
    name: 'GridEmpowerment',
    component: () => import('components/business/oneClickLearning/GridEmpowerment.vue'),
  },
  {
    path: '/GridEmpowermentDetail',
    name: 'GridEmpowermentDetail',
    component: () => import('components/business/oneClickLearning/GridEmpowermentDetail.vue'),
  },
  {
    path: '/GridEmpowermentVedioDetail',
    name: 'GridEmpowermentVedioDetail',
    component: () => import('components/business/oneClickLearning/GridEmpowermentVedioDetail.vue'),
  },
  {
    path: '/GridEmpowermentFileDetail',
    name: 'GridEmpowermentFileDetail',
    component: () => import('components/business/oneClickLearning/GridEmpowermentFileDetail.vue'),
  },
  {
      path: '/otherList',
      name: 'otherList',
      component: () => import('components/business/oneClickLearning/otherList.vue')
  },//排队取号
  {
    path:'/inlineNum',
    name: 'inlineNum',
    component: () => import('components/business/inlinenum/InlineNum.vue')
  },
  //客户群查询
  {
    path:'/customGroupSearch',
    name: 'customGroupSearch',
    component: () => import('components/business/CustomGroupSearch.vue')
  }, //多向协同无单甩单
  {
    path: '/multiOrderRejection',
    name: 'multiOrderRejection',
    component: () => import('components/business/multiwayteamwork/multiOrderRejection.vue')
  }, {
    path: '/multiOrderList',
    name: 'multiOrderList',
    component: () => import('components/business/multiwayteamwork/multiOrderList.vue')
  }, {
    path: '/multiOrderDetail',
    name: 'multiOrderDetail',
    component: () => import('components/business/multiwayteamwork/multiOrderDetail.vue')
  }, {
    path: '/groupNetRealName',
    name: 'groupNetRealName',
    component: () => import('components/business/groupNetRealName/groupNetRealName.vue')
  }, {
    path: '/businessZone',
    name: 'businessZone',
    component: () => import('components/business/businessZone/businessZone.vue')
  },

  {
    path: '/drawPlanList',
    name: 'drawPlanList',
    component: () => import('components/desk/WorkStageNew/drawPlanList.vue'),
  },
  {
    path: '/myNotice',
    name: 'myNotice',
    component: () => import('components/desk/WorkStageNew/myNotice.vue'),
  },
  {
    path: '/jikeGroupList',
    name: 'jikeGroupList',
    component: () => import('components/desk/WorkStageNew/jikeGroupList.vue'),
  },
  {
    path: '/faultReport',
    name: 'faultReport',
    component: () => import('components/business/faultreport/faultReport.vue'),
  },
  {
    path: '/zeroMenuShare',
    name: 'zeroMenuShare',
    component: () => import('components/business/zeroMenuShare/ZeroMenuShare.vue'),
  }, {
    path: '/networkAccessFusion',
    name: 'NetworkAccessFusion',
    component: () => import('components/business/perfectorder/NetworkAccessFusion.vue')
  },
  {
    path: '/zeroMenuJikeShare',
    name: 'zeroMenuJikeShare',
    component: () => import('components/business/zeroMenuShare/ZeroMenuJikeShare.vue'),
  },
  {
    path: '/zeroMenuJikeShare',
    name: 'zeroMenuJikeShare',
    component: () => import('components/business/zeroMenuShare/ZeroMenuJikeShare.vue'),
  },
  {
    path: '/testFaceCheck',
    name: 'testFaceCheck',
    component: () => import('components/common/testFaceCheck.vue'),
  },
  {
    path: '/specialGroupMachineList',
    name: 'specialGroupMachineList',
    component: () => import('components/business/SpecialStopResumeMachine/SpecialGroupMachineList.vue'),
  },
  {
    path: '/specialQueryGroup',
    name: 'specialQueryGroup',
    component: () => import('components/business/SpecialStopResumeMachine/SpecialQueryGroup.vue'),
  },
  {
    path: '/specialStopMachine',
    name: 'specialStopMachine',
    component: () => import('components/business/SpecialStopResumeMachine/SpecialStopMachine.vue'),
  }
  ,{
    path: '/allProductOrder',
    name: 'allProductOrder',
    component: () => import('components/business/fusionShoppingCart/allProductOrder.vue'),
  }
  ,{
    path: '/groupRealNameHistory',
    name: 'groupRealNameHistory',
    component: () => import('components/business/groupRealName/groupRealNameHistory.vue'),
  },
  {
    path: '/MaintainOrderManagement',
    name: 'MaintainOrderManagement',
    component: () => import('components/business/MaintainOrderManagement/MaintainOrderManagement.vue')
  },
  {
    path: '/Allocation',
    name: 'Allocation',
    component: () => import('components/my/groupWarningTask/Allocation.vue')

  }
  ,{
    path: '/faultReportList',
    name: 'faultReportList',
    component: () => import('components/business/faultreport/faultReportList.vue'),
  },
  {
    path: '/faultReportDetail',
    name: 'faultReportDetail',
    component: () => import('components/business/faultreport/faultReportDetail.vue')
  }
  ,{
    path: '/faultReportList',
    name: 'faultReportList',
    component: () => import('components/business/faultreport/faultReportList.vue'),
  },
  {
    path: '/faultReportDetail',
    name: 'faultReportDetail',
    component: () => import('components/business/faultreport/faultReportDetail.vue')
  },//存量维系单
  {
    path:'/stockMaintainOrder',
    name: 'stockMaintainOrder',
    component: () => import('components/business/stockMaintainOrder/StockMaintainOrder.vue')
  },//宽带维系
  {
    path:'/bandMaintain',
    name: 'bandMaintain',
    component: () => import('components/business/bandQuantization/BandMaintain.vue')
  },//客户维系单中转
  {
    path:'/workOrderEnterForward',
    name: 'workOrderEnterForward',
    component: () => import('components/business/bandQuantization/WorkOrderEnterForward.vue')
  }, //营业员日结
  {
    path:'/salerDailyBalance',
    name: 'salerDailyBalance',
    component: () => import('components/business/SalerDailyBalance.vue')
  }, //营收款上缴
  {
    path:'/saleBalancePaid',
    name: 'saleBalancePaid',
    component: () => import('components/business/SaleBalancePaid.vue')
  },
  {
    path: '/constructionOrderRecheckDetail',
    name: 'constructionOrderRecheckDetail',
    component: () => import('components/business/ConstructionOrder/ConstructionOrderRecheckDetail.vue'),
  },{
    path: '/constructionOrderRecheckList',
    name: 'constructionOrderRecheckList',
    component: () => import('components/business/ConstructionOrder/ConstructionOrderRecheckList.vue'),
  },//终端精准营销
  {
    path: '/zhongDuanFeature',
    name: 'zhongDuanFeature',
    component: () => import('components/business/userfeature/ZhongDuanFeature.vue')
  },
  { //小移快修
    path:'/quickRepair',
    name: 'quickRepair',
    component: () => import('components/business/QuickRepair/quickRepair.vue')
  },
    {
        path: '/userDetailByzw',
        name: 'UserDetailByzw',
        component: () => import('components/my/outcall/UserDetailByzw.vue')

    },
    {
        path: '/CompareCalculateAdd',
        name: 'CompareCalculateAdd',
        component: () => import('components/business/CompareCalculateAdd/CompareCalculateAdd.vue')

    },
    {
        path: '/CompareCalculateDetail',
        name: 'CompareCalculateDetail',
        component: () => import('components/business/CompareCalculateAdd/CompareCalculateDetail.vue')

    },
    {
        path:'/MarketingTask',
        name: 'MarketingTask',
        component: () => import('components/my/myTask/MarketingTask.vue')
    },
    {
      path:'/DedicatedLineMyGroup',
      name: 'DedicatedLineMyGroup',
      component: () => import('components/business/DedicatedLineComplaintsFaults/DedicatedLineMyGroup.vue')
    },
    {
      path:'/DedicatedLineGridOrder',
      name: 'DedicatedLineGridOrder',
      component: () => import('components/business/DedicatedLineComplaintsFaults/DedicatedLineGridOrder.vue')
    },
    {
      path:'/DedicatedLineFailOrder',
      name: 'DedicatedLineFailOrder',
      component: () => import('components/business/DedicatedLineComplaintsFaults/DedicatedLineFailOrder.vue')
    },
    {
      path:'/DedicatedLineComplaintOrder',
      name: 'DedicatedLineComplaintOrder',
      component: () => import('components/business/DedicatedLineComplaintsFaults/DedicatedLineComplaintOrder.vue')
    },
    {
      path:'/DedicatedLineTestMyGroup',
      name: 'DedicatedLineTestMyGroup',
      component: () => import('components/business/DedicatedLineComplaintsFaults/DedicatedLineTestMyGroup.vue')
    }, {
      path: '/competeReportDetail',
      name: 'CompeteReportDetail',
      component: () => import('components/desk/CompeteReportDetail.vue')
    },
    {
      path:'/authSingleOn',
      name: 'authSingleOn',
      component: () => import('components/business/scanEnterpriseWeChat/AuthSingleOn.vue')
    },
    {
        path: '/PersonalBusinessOpportunity',
        name: 'PersonalBusinessOpportunity',
        component: () => import('components/PersonalBusinessOpportunity/PersonalBusinessOpportunity.vue')
    },
    {
        path: '/PotentialuserMaintenance',
        name: 'PotentialuserMaintenance',
        component: () => import('components/PersonalBusinessOpportunity/PotentialuserMaintenance.vue')
    }, {
      path: '/competeHorizontalTable',
      name: 'CompeteHorizontalTable',
      component: () => import('components/desk/CompeteHorizontalTable.vue')
    },
    {
      path:'/outH5MidPage',//家客装维
      name: 'OutH5MidPage',
      component: () => import('components/desk/jiaKeInstallMain/outH5MidPage.vue'),
    },
    {
      path: '/createdLateFee',
      name: 'createdLateFee',
      component: () => import('components/business/LateFeeWaiver/CreatedLateFee.vue'),
    },
    {
      path: '/lateFeeWaiverList',
      name: 'lateFeeWaiverList',
      component: () => import('components/business/LateFeeWaiver/LateFeeWaiverList.vue'),
    },  //设备信息录入
    {
      path: '/equipmentImport',
      name: 'equipmentImport',
      component: () => import('components/business/EquipmentImport.vue')
    },
    {
      path: '/provinceImsSelectNumber',
      name: 'provinceImsSelectNumber',
      component: () => import('components/business/GroupImsBatchNew/provinceImsSelectNumber.vue')
    },
    {
      path: '/busiGroupFiling',
      name: 'busiGroupFiling',
      component: () => import('components/business/groupUserChange/BusiGroupFiling.vue')
    },
    //新全家享低消
    {
      path:'/familyLowConsumptionNew',
      name:'familyLowConsumptionNew',
      component:() => import('components/business/familylowconsumption/FamilyLowConsumptionNew.vue')
    }, {
      path: '/incentiveDetail',
      name: 'IncentiveDetail',
      component: () => import('components/desk/incentivedetail/IncentiveDetail.vue')
    }, {
      path: '/broadbandNumChange',
      name: 'BroadbandNumChange',
      component: () => import('components/business/BandTvMarket/BroadbandNumChange.vue')
    },
    // 个人企业微信上传二维码
    {
      path:'/QRCodeUpload',
      name: 'QRCodeUpload',
      component: () => import('components/business/PersonalQW/QRCodeUpload.vue')
    },
    {
      path:'/joinTheNetworkTest',
      name: 'joinTheNetworkTest',
      component: () => import('components/business/noCardyupeihao/joinTheNetworkTest.vue')
    },{
      path: '/wangYouLuRu',
      name: 'wangYouLuRu',
      component: () => import('components/business/wangYou/WangYouLuRu.vue'),
   }, {
      path: '/wangYouLieBiao',
      name: 'wangYouLieBiao',
      component: () => import('components/business/wangYou/WangYouLieBiao.vue'),
   },
  {
    path: '/groupAccountQuery',
    name: 'groupAccountQuery',
    component: () => import('components/business/groupaccountmodule/groupAccountQuery.vue')
  },
  {
    path: '/groupRecharge',
    name: 'groupRecharge',
    component: () => import('components/business/groupaccountmodule/groupRecharge.vue')
  },
  {
    path: '/groupDeptList',
    name: 'groupDeptList',
    component: () => import('components/business/groupaccountmodule/groupDeptList.vue')
  },
  {
    path: '/groupDeptDetail',
    name: 'groupDeptDetail',
    component: () => import('components/business/groupaccountmodule/groupDeptDetail.vue')
  },
  {
    path: '/groupAccountDetail',
    name: 'groupAccountDetail',
    component: () => import('components/business/groupaccountmodule/groupAccountDetail.vue')
  },
    // 商客专区首页
    {
      path:'/skzqHome',
      name: 'skzqHome',
      component: () => import('components/desk/skzq/skzqHome.vue'),
      meta: {
        keepAlive: false
      }
    },
    // 商客专区菜单页面
    {
      path:'/skzqMenu',
      name: 'skzqMenu',
      component: () => import('components/desk/skzq/skzqHome.vue'),
      meta: {
        keepAlive: false
      }
    },
    // 摸排任务列表
    {
      path:'/mpTaskList',
      name: 'mpTaskList',
      component: () => import('components/desk/skzq/mpTaskList.vue')
    },//辅助办理认证
  {
    path:'/assistProcess',
    name: 'assistProcess',
    component: () => import('components/business/assistprocess/AssistProcess.vue')
  },
  {
    path: '/diffCommMenu',
    name: 'diffCommMenu',
    component: () => import('components/business/groupaccountmodule/DiffCommMenu.vue')
  },
  {
    path: '/carringReservationApply',
    name: 'carringReservationApply',
    component: () => import('components/business/CarringReservation/CarringReservationApply.vue')
  },
  {
    path: '/carringReservation',
    name: 'carringReservation',
    component: () => import('components/business/CarringReservation/CarringReservation.vue')
  },
  {
    path: '/reservationApply',
    name: 'reservationApply',
    component: () => import('components/business/CarringReservation/ReservationApply.vue')
  },
  {
    path: '/groupRechargeList',
    name: 'groupRechargeList',
    component: () => import('components/business/groupaccountmodule/groupRechargeList.vue')
  },
  {
    path: '/tocShopping',
    name: 'tocShopping',
    component: () => import('components/business/fusionShoppingCart/tocShopping.vue')
  },
  {
    path: '/luckyHornChargeDetail',
    name: 'luckyHornChargeDetail',
    component: () => import('components/business/xuanhaoruwang/LuckyHornChargeDetail.vue'),
  },
  {
    path: '/selectActivity',
    name: 'selectActivity',
    component: () => import('components/business/xuanhaoruwang/SelectActivity.vue'),
  },
    {
        path: '/aiSparringHomePage',
        name: 'aiSparringHomePage',
        component: () => import('components/business/aiSparring/SparringHomePage.vue')
    },
    {
        path: '/aiSparringChat',
        name: 'aiSparringChat',
        component: () => import('components/business/aiSparring/chatpage.vue')
    },
    {
        path: '/chatRecord', // ai陪练记录
        name: 'chatRecord',
        component: () => import('components/business/aiSparring/chatRecord.vue')
    },
    {
        path: '/chatDetail',// ai陪练记录详情
        name: 'chatDetail',
        component: () => import('components/business/aiSparring/chatDetail.vue')
    },
    {
        path: '/PotentialuserMaintenance2',
        name: 'PotentialuserMaintenance2',
        component: () => import('components/PersonalBusinessOpportunity2/PotentialuserMaintenance.vue'),
    },
    {
        path: '/PersonalBusinessOpportunity2',
        name: 'PersonalBusinessOpportunity2',
        component: () => import('components/PersonalBusinessOpportunity2/PersonalBusinessOpportunity.vue')
    },
  {
    path: '/userEvaluate', // 用户评价
    name: 'userEvaluate',
    component: () => import('components/business/userEvaluate/userEvaluate.vue'),
  },
  {
    path: '/marketingAssistant',
    name: 'MarketingAssistant',
    component: () => import('components/desk/MarketingAssistant/MarketingAssistant.vue')
  },
    {
        path: '/MarketLogPage',
        name: 'MarketLogPage',
        component: () => import('components/business/MarketLog/MarketLogPage.vue')

    },
    {
        path: '/LogHis',
        name: 'LogHis',
        component: () => import('components/business/MarketLog/LogHis.vue')

    },  //集团目标库维护
  {
    path:'/groupTargetMaintain',
    name: 'groupTargetMaintain',
    component: () => import('components/business/GroupTargetMaintain.vue')
  },
    {
        path: '/BusinessOpportMaintenance',
        name: 'BusinessOpportMaintenance',
        component: () => import('components/PersonalBusinessOpportunity/BusinessOpportMaintenance.vue')
    },
    {
        path: '/BusinessOpportMaintenance2',
        name: 'BusinessOpportMaintenance2',
        component: () => import('components/PersonalBusinessOpportunity2/BusinessOpportMaintenance.vue')
    },
    {
        path: '/bandRealNameRecord',
        name: 'BandRealNameRecord',
        component: () => import('components/business/BandTvMarket/BandRealNameRecord.vue')
    },{
      path: '/groupCampManagement',
      name: 'GroupCampManagement',
      component: () => import('components/business/GroupCampOn/GroupCampManagement.vue')
  },
    {
        path: '/groupOrdersBusiness',
        name: 'groupOrdersBusiness',
        component: () => import('components/PersonalBusinessOpportunity/groupOrdersBusiness.vue')
    },
  {
    path: '/thirdAudit',
    name: 'ThirdAudit',
    component: () => import('components/tools/activeRecommd/ThirdAudit.vue')
  },
  // 专家风采
  {
    path: '/expertStyle',
    name: 'expertStyle',
    component: () => import('components/business/expertStyle/expertStyle.vue')
  },
  {
      path: '/expertStyleDetail',
      name: 'expertStyleDetail',
      component: () => import('components/business/expertStyle/expertStyleDetail.vue')
  },
  {
      path: '/expertStyleSearch',
      name: 'expertStyleSearch',
      component: () => import('components/business/expertStyle/expertStyleSearch.vue')
  },//双向协同新
  {
    path: '/cooperateNew',
    name: 'cooperateNew',
    component: () => import('components/business/cooperatenew/CooperateNew.vue')
  },//商机录入
  {
    path: '/businessImport',
    name: 'businessImport',
    component: () => import('components/business/cooperatenew/BusinessImport.vue')
  },//商机单查询
  {
    path: '/businessQry',
    name: 'businessQry',
    component: () => import('components/business/cooperatenew/BusinessQry.vue')
  },
  {
      path: '/groupSelectChoose',
      name: 'groupSelectChoose',
      component: () => import('components/business/userEvaluate/GroupSelectChoose.vue')
  },
  {
    path:'/stockMaintainOrderNew',
    name: 'stockMaintainOrderNew',
    component: () => import('components/business/stockMaintainOrder/StockMaintainOrderNew.vue')
  },
  {
      path: '/userEvaluateList',
      name: 'userEvaluateList',
      component: () => import('components/business/userEvaluate/userEvaluateList.vue')
  },
  {
    path: '/commonTaskTemple',
    name: 'commonTaskTemple',
    component: () => import('components/my/myTask/commonTaskTemple.vue')
  },
  {
      path: '/groupRealNameAppoints',
      name: 'groupRealNameAppoints',
      component: () => import('@/components/business/groupRealName/groupRealNameAppoints.vue')
  },
  {
      path:'/hostingPlatform',
      name:'HostingPlatform',
      component: () => import('@/components/business/EnterpriseVX/HostingPlatform.vue'),
  },
  {
    path: '/terminalInventory',
    name: 'terminalInventory',
    component: () => import('components/business/TerminalInventory/terminalInventoryList.vue')
  },
    {
        path:'/notifyWebKitWithCallBack',
        name: 'notifyWebKitWithCallBack',
        component: () => import('components/area/notifyWebKitWithCallBack.vue')
    },
]
