import {getEnc<PERSON><PERSON>,getJm<PERSON><PERSON>,getGroup<PERSON><PERSON>} from '@/base/utils.js'

import JiaMi from "./JiaMiUtil.js";

// const jiaMi = new JiaMi({ initKey: getEncKey("abcdefghijklmnop"),desKey: getJmKey("abcdefghijklmnop") });
const jiaMi = new JiaMi({ initKey: getEncKey(),desKey:getJmKey(),oldKey:getGroupKey()});


const iEncrpt = jiaMi.iEncrpt;
const iEncrptParam = jiaMi.iEncrptParam;
const iEncrptParamMap = jiaMi.iEncrptParamMap;
const decrptParam = jiaMi.decrptParam;
const digitSign = jiaMi.digitSign;
const digitAldSign = jiaMi.digitAldSign;
const aesEncryptAllParams = jiaMi.aesEncryptAllParams;
const iEncrptParamN = jiaMi.iEncrptParamN;
const decrptParamN = jiaMi.decrptParamN;


export {iEncrpt,iEncrptParam,iEncrptParamMap,decrptParam,digitSign,digitAldSign,aesEncryptAllParams,iEncrptParamN,decrptParamN}


