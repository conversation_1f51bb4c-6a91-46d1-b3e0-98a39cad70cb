import Storage from './storage'

export const VillageAddrGISMixin = {
  data() {
    return {
      uinfo: {},
      businessType: '1',  //1:家客 2:一网通
      orderId: "",        //唯一流水
    }
  },

  created() {
    this.uinfo = Storage.session.get('userInfo');
  },

  methods: {

    //获取GIS选址token
    getGisAddrToken() {
      let url = `/xsb/personBusiness/businessOpen/h5qryGisAddrToken`
      let param = {
        msisdn: this.telnum,
      }
      this.$http.post(url, param).then(res => {
        let {retCode, retMsg, data} = res.data;
        if (retCode === '0') {
          if (data.orderId) {
            this.orderId = data.orderId;
          }
          //获取地图选址URL
          this.getGisUrl(data.token);
        } else {
          this.$alert(retMsg || '获取GIS地址token失败')
        }
      });
    },

    getGisUrl(token) {
      let url = `https://211.103.0.39:58004/rm8/gotoNewGisSelect.html?`;
      url += `token=${encodeURIComponent(token)}`;
      url += `&type=${encodeURIComponent(this.businessType)}`;
      url += `&x=${encodeURIComponent(Storage.get('longitude') || '')}`;
      url += `&y=${encodeURIComponent(Storage.get('latitude') || '')}`;
      url += `&vendor=1`;
      console.info('完整的GIS URL:', url);
      //拉起GIS地图选址页面
      let pointDesc = '拉起地图选址页面';
      this.openWebKitWithStatus(url, {}, pointDesc);
    },

    /**
     * 更新GIS地址记录
     * @param addressId 地址编码
     * @param retCode 响应码
     * @param retMsg 响应信息
     */
    updateGisAddrRecord(addressId, retCode, retMsg) {
      if (!this.orderId) {
        console.info("orderId为空，无法更新GIS地址记录");
        return;
      }
      let url = `/xsb/personBusiness/businessOpen/h5updateGisAddrRecord`;
      let params = {
        addressId: addressId,
        orderId: this.orderId,
      }
      if (retCode !== '0') {
        params.callBackMsg = retMsg;
      }
      this.$http.post(url, params, { unLoadFlg: true }).then(res => {
        let { retCode } = res.data;
        if (retCode !== '0') {
          console.info('更新GIS地址记录失败');
        }
      }).catch(reason => {
        console.info('更新GIS地址记录异常, error: ', reason);
      })
    },

    gisValidate(retObj) {
      let result = {
        retCode: "-1",
        retMsg: ""
      };
      if (!retObj.addrId) {
        result.retMsg = "地址id【addrId】为空";
      } else if (!retObj.addrName) {
        result.retMsg = "地址全称【addrName】为空";
      } else if (!retObj.districtId) {
        result.retMsg = "小区id【districtId】为空";
      } else if (!retObj.districtName) {
        result.retMsg = "小区名称【districtName】为空";
      } else if (!retObj.radiusType) {
        result.retMsg = "Radius归属【radiusType】】为空";
      } else if (!retObj.callFor) {
        result.retMsg = "区县编码【callFor】为空";
      } else if (!retObj.gimsUserType) {
        result.retMsg = "用户类型【gimsUserType】为空";
      } else if (!retObj.gimsAreaType) {
        result.retMsg = "区域类型【gimsAreaType】为空";
      } else if (!retObj.networkType) {
        result.retMsg = "网络接入方式【networkType】为空";
      } else {
        result.retCode = "0";
        result.retMsg = "Success";
      }
      return result;
    }
  },

  mounted() {
    window['valueCallBack'] = (res) => {
      console.info("===========GIS地图回调==============");
      console.info("res: ", res);
      let gisResObj = JSON.parse(res);
      if (!gisResObj) {
        this.$alert("地图选址回调失败，请重新选址");
        return;
      }
      // 确保selectVillage已初始化
      this.selectVillage = {};
      // 处理返回的GIS数据
      this.selectVillage = gisResObj;
      this.selectVillage.villageAddress =  gisResObj.districtName || '';  //小区地址
      this.selectVillage.fullName =  gisResObj.addrName || '';        //完整地址
      this.selectVillage.villageId =  gisResObj.districtId || '';         //小区编号
      this.selectVillage.countryId =  gisResObj.callFor || '';            //区县编码
      this.selectVillage.villageName =  gisResObj.districtName || '';     //小区名称
      this.selectVillage.addressId =  gisResObj.addrId || '';             //地址编码
      this.selectVillage.otherAgent =  gisResObj.supplyType || '';        //资源提供方:1 移动2 广电3 铁通4 其他运营商5 复合运营商
      this.selectVillage.constMgr =  gisResObj.factoryType || '';         //施工管理方:1 移动2 广电3 铁通4 其他运营商5 复合运营商
      this.selectVillage.radiusFor =  gisResObj.radiusType || '';         //Radius归属:1 移动2 广电3 铁通4 其他运营商5 复合运营商
      this.selectVillage.userType =  gisResObj.gimsUserType || '';        //用户类型 字典值：1-13
      this.selectVillage.planCategory =  gisResObj.gimsAreaType || '';    //区域类型 新增，1市区城区，2市辖乡镇，3市辖村，4县城城区，5县辖乡镇，6县辖村，7其他
      this.selectVillage.villageY =  gisResObj.geo_x || '';               //经度
      this.selectVillage.villageX =  gisResObj.geo_y || '';               //纬度
      this.selectVillage.addressName =  gisResObj.addrName || '';         //地址全称
      this.selectVillage.isCoverpoint =  "0";                       //是否是末级地址
      this.selectVillage.status =  "1";
      this.selectVillage.businessStatus =  "0";
      this.selectVillage.businessSpeed =  "";                       //带宽
      this.selectVillage.ponType =  gisResObj.ponType || '';              //PON类型 GPON/EPON，可为空
      this.selectVillage.needConstruction =  gisResObj.needConstruction || '';  //是否需要施工
      this.needConstruction =  gisResObj.needConstruction || '';          //是否需要施工
      this.selectVillage.limitTime =  "";                           //当日装
      this.selectVillage.gimsNetworkType =  gisResObj.networkType || '';  //网络类型
      this.selectVillage.netWorkType =  gisResObj.networkType || '';      //网络类型
      this.netWorkType =  gisResObj.networkType || '';  //网络类型
      this.selectVillage.rangeType =  gisResObj.rangeType || '';

      console.info("GIS处理后数据：", this.selectVillage);
      // 验证GIS返回数据
      let { retCode, retMsg } = this.gisValidate(gisResObj);
      if (retCode === "0") {
        // 确保所有必要属性存在后再调用提交方法
        this.submitAddr();
      } else {
        this.$alert(retMsg);
      }
      //更新GIS地图选址信息
      this.updateGisAddrRecord(gisResObj.addrId, retCode, retMsg);
    }
  }
}
