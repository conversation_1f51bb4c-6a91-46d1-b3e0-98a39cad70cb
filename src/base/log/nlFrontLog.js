//import _axios from 'axios';
import _axios from '@/base/nlAxios.js'
import {dateFormat} from '@/base/utils'
import Storage from '@/base/storage'
const timeThresholds = 60000;//达到一分钟即把日志上传
const countThresholds = 20;//20条把日志上传到服务端
const serverUrl = '/xsb/ability/trace/h5receiveTrace';//服务端日志URL
const switchUrl = '/xsb/ability/trace/h5qryTraceDictInfo';//服务端获取日志开关的URL
import CryptoJS from 'crypto-js'
const VALIDATE_STRICT_URL = '/xsb/api-user/tokenAuthUrlCfg/h5qryValStrictUrl';//服务端获取强校验token的URL

//import {iEncrptParam} from '@/base/encrptH5.js'

let traceSeqUrl = '/xsb/ability/common/h5getSeqByName?name=seq_ald_trace';//查询trace主键seq
let curSeqId = '';//当前业务的主键
let LOG_CALL_CONFIG = {};
class NlSmartLog{
    constructor(){
        this.logQueue = [];//日志队列
        this.timeOutObj = null;//定时器实例
        this.logSwitchMap = {};//日志开关MAP
        this.logReqUrlArr = [];//需要采集的异步请求URL数组
        this.logEventArr = [];//需要采集的异步请求URL数组
        this.curBusiType = '';//当前记录的业务类型
        this.otherParamObj = {};
        this.strictUrlMap = {};//需要强校验token的URL列表
        this.initSwitch();
    }
    //初始化日志开关配置
    initSwitch =()=>{
        let url = '/xsb/ability/businessLimit/h5QryBusiPermission';
        let param = {
            'busiType':'call_chain' //调用链开关
        };
        _axios.post(url,param).then((res)=>{
            if(res.data.retCode == '0'){//调用链日志开
                _axios.post(switchUrl).then(res => {
                    let {retCode,data} = res.data;
                    if(retCode == 0){//服务端返回成功
                        this.logSwitchMap = data;//开关配置数组
                        this.initLogConfig();//初始化开关和页面的字典
                    }
                })
            }
        });
        //请求需要强校验的接口URL
        _axios.post(VALIDATE_STRICT_URL,{}).then((res)=>{
            let {retCode,data} = res.data;
            if(retCode == '0'){//调用链日志开
                let urlList = data.urlList;
                for(let i=0; i < urlList.length; i++){
                    let item = urlList[i];
                    this.strictUrlMap[item.md5Value]='1';
                }
            }
        });
        
    }
    //初始化开关和页面的字典
    initLogConfig(){        
        for(let key in this.logSwitchMap){
            //key对应日志调用链中的业务编号
            let subArr = this.logSwitchMap[key];//当前业务要采集的步骤数组
            if(subArr && subArr.length > 0){//采集步骤不为空
                let pname = subArr[0].busiPageName;//采集业务的routename
                if(pname){//配置了路由页面名称的情况
                    LOG_CALL_CONFIG[pname] = key;//组织成：PromotionEntry:true这样的map
                }
            }
        }
    }
    //日志上传到服务器
    pushLogToServer() {
        if(this.logQueue.length == 0){
            return;
        }
        this.clearTimeOutObj();
        //取日志队列的前countThresholds条，如果不足countThresholds，则会取全部
        let curLogArr = this.logQueue.splice(0,countThresholds);
        //封装上传给服务器的参数
        let param = {
            logContent:JSON.stringify(curLogArr),
            unLoadFlg:true//屏蔽加载圈
        }
        console.info(curLogArr);

        _axios.post(serverUrl,param).then(res => {
            this.refreshTimeOutObj();//刷新定时器
        });
    }
    //事件类型的参数封装
    _orgEventParam(param){
        console.info(param)
    }
    //异步请求的参数封装
    _orgRequestParam(param){
        console.info(param);
    }
    //组装参数对象
    _orgAxiosParam(param,stepId,type){
        //唯一主键|操作员主键|业务主键|客户主键|操作时间（精确到毫秒）
        let createTime = dateFormat(new Date(),"yyyy-MM-dd hh:mm:ss");
        let retArr = [];
        retArr.push(curSeqId);//唯一主键
        retArr.push(this.uinfo.staffId);//操作员主键staffId
        retArr.push(this.uinfo.imei?this.uinfo.imei:'');//操作员imei
        retArr.push(this.curBusiType);//业务主键100001
        retArr.push(stepId);//步骤编号stepId 10000001
        retArr.push(this.telnum)//客户主键：customId
        retArr.push(createTime);//操作时间（精确到毫秒）
        // let otherParamObj = this.otherParamObj;//下次添加上一级页面和当前页面及相关的业务参数
        // retArr.push(otherParamObj.fromPage);
        // retArr.push(otherParamObj.toPage);
        
        let retInfo = retArr.join('|');
        return retInfo;
    }

    addHeaderLog(rUrl,cfg){
        //判断是否在强校验URL中
        let md5Url = CryptoJS.MD5(rUrl).toString().toUpperCase();
        if(this.strictUrlMap[md5Url]){
            cfg.headers.validateType = 'strict';
        }
        //如果日志开关配置的业务类型或者步骤为空，则不处理返回
        if(!this.curBusiType ||!this.logSwitchMap[this.curBusiType] || this.logSwitchMap[this.curBusiType].length == 0){
            return;
        }
        let tmpArr = this.logSwitchMap[this.curBusiType];
        let stepId = '';
        if(~(this.logReqUrlArr.join('^').indexOf(rUrl))){//是采集的URL
            for(let i = 0; i < tmpArr.length; i++){//循环获取URL的步骤
                if(~rUrl.indexOf(tmpArr[i].stepValue)){
                    stepId = tmpArr[i].stepId;
                    break;
                }
            }
            let u = this.uinfo;
            cfg.headers.stepId = stepId;//步骤编号
            cfg.headers.staffId = u.staffId;//操作员主键
            cfg.headers.imei = u.imei==void 0 ?'':u.imei;//imei号
            cfg.headers.busiId = this.curBusiType;//业务编号
            cfg.headers.seq = curSeqId;//唯一主键
            cfg.headers.customId = this.telnum;//客户手机号
            // console.info(cfg.headers);
        }
    }

    //添加日志
    addLog = (param,type) => {
        //如果日志开关配置的业务类型或者步骤为空，则不处理返回
        if(!this.curBusiType ||!this.logSwitchMap[this.curBusiType]|| this.logSwitchMap[this.curBusiType].length == 0){
            return;
        }
        let stepId = '';
        if(type == 1){
            let stepClass = param.stepId;//点击DOM对象上的class
            let reg = /logstep_(\d+)?/;
            stepClass = (stepClass.match(reg))[0];//匹配以logstep_开头的class
            stepId = stepClass.substr('logstep_'.length);//步骤编号
        }
        
        //判断要不要采集log
        if(!this._checkIfCollect(param,type,stepId)){
            return;
        }
        if(type == 1){//事件类型的日志
            // console.info('----添加日志---当前业务：' + this.curBusiType);
            //根据参数类型封装
            let info = this._orgAxiosParam(param,stepId,type);
            this.logQueue.push(info);//添加到队列中
        }
        //日志条数大于阀值就推送到服务器
        if(this.logQueue.length >= countThresholds){
            this.pushLogToServer();//上传到服务器
        }
    }
    //添加手机号参数 需要在afterEach清除参数
    beforeEach = (telnum)=>{
        this.telnum = telnum;
    }
    //清除手机号
    afterEach = () => {
        this.telnum = '';
    }
    //初始化业务
    initLog(bid,fromPage='/',toPage){
        this.curBusiType = bid;//业务编号
        this.otherParamObj.fromPage = fromPage;//上一级页面
        this.otherParamObj.toPage = toPage;//当前页面
        if(!this.logSwitchMap[bid] || this.logSwitchMap[bid].length == 0){
            return;
        }
        //初始化定时器
        this.refreshTimeOutObj();
        let jqData = Storage.session.get('jqData');
        this.telnum = jqData.telnum;//客户手机号
        this.uinfo = Storage.session.get('userInfo');
        this.logReqUrlArr = [];
        this.getTraceSeq();//请求服务端获取唯一主键
        let arr = this.logSwitchMap[bid];
        for(let i = 0; i < arr.length; i++) {
            if(arr[i].type == '2'){//采集的类型为异步请求
                this.logReqUrlArr.push(arr[i].stepValue);
            } else if(arr[i].type == '1'){//采集的类型为点击事件
                this.logEventArr.push(arr[i].stepId);
            }
        }
    }
    //清除日志
    destroyLog(){
        this.logQueue = [];//日志队列
        this.clearTimeOutObj()//定时器实例
        this.logReqUrlArr = [];//需要采集的异步请求URL数组
        this.curBusiType = '';//当前记录的业务类型
    }
    //查询trace主键seq
    getTraceSeq(){
        // let p = {name:'seq_ald_trace'};
        _axios.post(traceSeqUrl).then(res => {
            let {retCode,retMsg,data} = res.data;
            if(retCode == '0'){
                curSeqId = data;//服务端返回的SEQ
            } else {
                console.info(retMsg);
            }
        });
    }
    //清除定时器
    clearTimeOutObj() {
        if(this.timeOutObj){//先判断定时器存在不
            this.timeOutObj = null;
        }
    }

    //刷新定时器
    refreshTimeOutObj() {
        this.clearTimeOutObj();//先清空定时器
        let self = this;
        this.timeOutObj = setTimeout(()=>{
            console.info('上传日志阀值：'+timeThresholds);
            self.pushLogToServer();
        },timeThresholds);
    }
    //判断点击事件或者异步请求要不要采集到日志调用链中
    _checkIfCollect(param,type,stepId){
        let retFlg = false;
        if(type == '1'){//点击事件，判断步骤编号
            if(stepId && ~this.logEventArr.join('^').indexOf(stepId)){//stepId不为空并且在采集范围内
                retFlg = true;
            }
        }
        
        return retFlg;
    }

}

const getInstance = () => {
    this.instance = new NlSmartLog();
    window['SAMRTLOG'] = this.instance;
}



export {
    getInstance,
    LOG_CALL_CONFIG
}