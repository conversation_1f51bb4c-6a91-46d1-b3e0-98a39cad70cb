.downloadTip{
    width:210px;
    height:38px;
    background:rgba(0,38,82,0.7);
    border-radius:19px;
    text-align: center;
    line-height: 38px;
    font-size:15px;
    font-weight:400;
    color:rgba(255,255,255,1);
    position: fixed;
    top:50%;
    left:50%;
    margin-top:50px;
    margin-left:-105px;

}
.dl-title{
    height:22px;
    font-size:16px;
    font-weight:400;
    color:rgba(60,60,60,1);
    line-height:22px;
    margin-top:8px;
}
.download {
    position: absolute;
    top: 0px;
    left: 0px;
    bottom: 0px;
    right: 0px;
    overflow: auto;
}

.dl-main {
    margin: 24px;
    overflow: hidden;
    position:relative;
}
.dl-tip{
    position:absolute;
    right:0.5rem;
    top:0.5rem;
    width:218px;
    z-index: 22;
}
.dl-mhead {
    height: auto;
    overflow: hidden;
}

.dl-mh-logo {
    width: 86px;
    height: 86px;
    float: left;
}

.dl-mh-right {
    margin-left: 104px;
    height: auto;
    overflow: hidden;
}

.dl-mhr-title {
    height: 25px;
    line-height: 25px;
    font-size: 18px;
    color: #555;
}

.dl-mhr-tv {
    font-size: 12px;
    color: #6C6C6C;
    position: absolute;
    top: 30px;
    width: 100px;
    left: 50%;
    margin-left: -50px;
}

.dl-mhr-desc {
    font-size: 14px;
    color: #555;
    line-height: 20px;
}

.dl-mhr-btns {
    height: auto;
    overflow: hidden;
    margin-top: 8px;
    padding-bottom: 30px;
}

.dl-btn {
    height: 32px;
    width: 94px;
    float: left;
    border-radius: 20px;
    position: relative;
    line-height: 32px;
    text-align: center;
}

.iconapple {
    color: #26B739;
    width: 26px;
    height: 26px;
    line-height: 26px;
    text-align: center;
    font-size: 25px;
    margin-top: 3px;
    display: block;
    float: left;
    margin-left: 20px;
}

.dl-btn.apple {
    font-size: 14px;
    margin-right: 8px;
}

.dl-btn-txt {
    height: 26px;
    line-height: 26px;
    display: block;
    float: left;
    margin-top: 3px;
}

.dl-btn.apple {
    border: 1px solid rgba(23, 185, 47, 1);
}

.dl-btn.android {
    border: 1px solid #3C7DFF;
}

.iconandroid {
    color: #3C7DFF;
    width: 26px;
    height: 26px;
    line-height: 26px;
    text-align: center;
    font-size: 16px;
    margin-top: 3px;
    display: block;
    float: left;
    margin-left: 20px;
}

.dl-btn.apple .dl-btn-txt {
    color: #26B739;
    font-size: 14px;
}

.dl-btn.android .dl-btn-txt {
    color: #3C7DFF;
    font-size: 14px;
}

.dl-minfo {
    height: auto;
    overflow: hidden;
}

.dl-mf-title {
    height: 20px;
    line-height: 20px;
    color: #3C3C3C;
    font-size: 16px;
    font-weight: bold;
}

.dl-mf-ul {
    display: block;
    overflow: hidden;
    margin-top: 0px;
}

.dl-mf-ul li {
    display: block;
    overflow: hidden;
    height: auto;
    font-size: 14px;
    font-weight: 400;
    color: rgba(60, 60, 60, 1);
    line-height: 22px;
}

.dl-mf-size {
    font-size: 14px;
    font-weight: 400;
    margin-top: 8px;
    color: #838383;
    line-height: 20px;
}
.dl-mf-size.martop0{margin-top:0px;}

.marleft16 {
    margin-left: 16px;
}

.dl-ios-resolve {
    font-size: 14px;
    font-weight: 400;
    margin-top: 16px;
    color: rgba(38, 183, 57, 1);
    line-height: 20px;
}

.dl-mpic {
    height: auto;
    overflow: hidden;
    margin-top: 30px;
}

.dl-piclis {
    width: 48%;
    float: left;
    margin-left: 1%;
    margin-right: 1%;
}

.pop-filter {
    position: fixed;
    top: 0px;
    left: 0px;
    bottom: 0px;
    right: 0px;
    background: rgba(0, 0, 0, 0.6);
    z-index: 50;
}

.pop-resolve {
    position: fixed;
    top: 10px;
    left: 10px;
    right: 10px;
    bottom: 0px;
    background: #fff;
    display: flex;
    flex-direction: column;
    z-index: 51;
}

.pop-rs-head {
    height: auto;
    padding-top: 10px;
    padding-bottom: 10px;
    font-size: 14px;
    font-weight: 400;
    color: rgba(56, 56, 56, 1);
    line-height: 20px;
    text-indent: 10px;
    border-bottom: 1px solid #C8C8C8;
    position: relative;
    flex-shrink: 0;
}

.pop-close {
    width: 30px;
    height: 20px;
    display: block;
    position: absolute;
    top: 10px;
    right: 10px;
    line-height: 20px;
    text-align: center;
}

.iconguanbi {
    text-align: center;
    line-height: 20px;
    font-size: 16px;
    color: #B2AEAE;
    width: 20px;
    height: 20px;
    display: block;
}

.pop-rs-inner {
    flex-grow: 1;
    overflow: auto;
    padding-bottom: 20px;
}

.pop-plis {
    font-size: 12px;
    font-weight: 400;
    color: rgba(56, 56, 56, 1);
    line-height: 18px;
}

.pop-appledesc {
    height: auto;
    overflow: hidden;
    margin-top: 16px;
    margin-left: 16px;
    margin-right: 16px;
}

.pinfo-img {
    width: 80%;
    margin: 30px auto;
}

.resolve-lisimg {
    width: 80%;
    margin: 20px auto;
}

.pop-appleresolve {
    height: auto;
    overflow: hidden;
    margin-top: 16px;
    margin-left: 16px;
    margin-right: 16px;
}

.pop-resolve.fla {
    animation: moveToTop .6s linear both;
}

.dl-btn a {
    display: block;
    width: 100%;
    height: 100%;
}

@keyframes moveToTop {
    0% {
        transform: translate(0, 100%);
    }
    100% {
        transform: translate(0, 0%);
    }
}
.copyright-info{
    color: #838383;
    text-align: center;
    font-size: 12px;
}
.copyright-info .info{
    line-height: 20px;
    margin: 0;
    padding: 0;
    height: 20px;
    color: #838383;
    text-decoration: none;
}