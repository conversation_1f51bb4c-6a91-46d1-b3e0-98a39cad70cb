@import "./variable.less";
@import "./mixin.less";
@import "./animate.less";
@import "./public.less";

/*样式重置*/
html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, embed,
figure, figcaption, footer, header, hgroup,
menu, nav, output, ruby, section, summary,
time, mark, audio, video,input {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  vertical-align: baseline;

  -webkit-touch-callout:none;
  user-select:none;
}


/* HTML5 display-role reset for older browsers */
article, aside, details, figcaption, figure,
footer, header, hgroup, menu, nav, section {
  display: block;
}

body {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-tap-highlight-color:transparent;
  line-height: 1;
  -webkit-overflow-scrolling: touch;
}

ol, ul {
  list-style: none;
}

blockquote, q {
  quotes: none;
}

blockquote:before, blockquote:after,
q:before, q:after {
  content: '';
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

html,
body {
  height: 100%;
  font-family: Helvetica, sans-serif;
  overflow: auto;
}

button{
  outline: none;
}
input, textarea, select, button{
  font-size:16px;
}
/* 阴影背景 */

/*正文样式*/
.flex-layout {
  display: flex;
  display: -webkit-flex;
  flex-direction: column;
}

.html-wrap {
  background-color: @color-BG;
  height: 100%;
  width: 100%;
  overflow: auto;
}

.mint-indicator-wrapper{
  z-index: 999;
}
.mint-msgbox-message{
  text-align:left!important;
}
.mint-msgbox-input input{
  box-sizing:border-box!important;
}
.mint-swipe-indicators{
  bottom:5px;
}
.mint-swipe-indicator.is-active{
  background: @color-TextPrimary;
  opacity: 0.8;
}

//工作台动画
@keyframes load{
  0%{transform:rotate(0)}
  100%{transform:rotate(360deg)}
}
.load {
  animation: load 1s linear 2;
  transform-origin: center center;
}

.nl-loading-parent--relative {
	position:relative!important
}
.nl-loading-parent--hidden {
	overflow:hidden!important
}
.nl-loading-mask {
	position:absolute;
	z-index:2000;
	background-color:rgba(255,255,255,.9);
	margin:0;
	top:0;
	right:0;
	bottom:0;
	left:0;
	-webkit-transition:opacity .3s;
	transition:opacity .3s
}
.nl-loading-mask.is-fullscreen {
	position:fixed
}
.nl-loading-mask.is-fullscreen .nl-loading-spinner {
	margin-top:-25px
}
.nl-loading-mask.is-fullscreen .nl-loading-spinner .circular {
	height:50px;
	width:50px
}
.nl-loading-spinner {
	top:50%;
	margin-top:-21px;
	width:100%;
	text-align:center;
	position:absolute
}
.nl-loading-spinner .circular {
	height:42px;
	width:42px;
	-webkit-animation:loading-rotate 2s linear infinite;
	animation:loading-rotate 2s linear infinite
}
.nl-loading-spinner .path {
	-webkit-animation:loading-dash 1.5s ease-in-out infinite;
	animation:loading-dash 1.5s ease-in-out infinite;
	stroke-dasharray:90,150;
	stroke-dashoffset:0;
	stroke-width:2;
	stroke:#409EFF;
	stroke-linecap:round
}
.nl-loading-spinner i {
	color:#409EFF
}
.nl-loading-fade-enter,.nl-loading-fade-leave-active {
	opacity:0
}
@-webkit-keyframes loading-rotate {
	100% {
    -webkit-transform:rotate(360deg);
    transform:rotate(360deg)
  }
}
@keyframes loading-rotate {
    100% {
    -webkit-transform:rotate(360deg);
    transform:rotate(360deg)
  }
}
@-webkit-keyframes loading-dash {
	0% {
    stroke-dasharray:1,200;
    stroke-dashoffset:0
  }
  50% {
    stroke-dasharray:90,150;
    stroke-dashoffset:-40px
  }
  100% {
    stroke-dasharray:90,150;
    stroke-dashoffset:-120px
  }
}
@keyframes loading-dash {
	0% {
    stroke-dasharray:1,200;
    stroke-dashoffset:0
  }
  50% {
    stroke-dasharray:90,150;
    stroke-dashoffset:-40px
  }
  100% {
    stroke-dasharray:90,150;
    stroke-dashoffset:-120px
  }
}



.alert-title{
  color:#2A68FF;
}
.hide{
  display:none;
}
