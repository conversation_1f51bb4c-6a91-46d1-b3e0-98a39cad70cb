@import "./style.less";

.mint-datetime{
  width:45% !important;
  transform: translate3d(11%,0,0);
}
.mint-loadmore-text{
  font-size:16px;
}
.v-modal{
  width: 45%;
  right: 0;
  left: unset;
}
/*样式错乱*/
.mint-msgbox{
    margin-left: -21% !important;
    margin-top: -17% !important;
    width: 50%;
}
/*针对fixed出错产生的样式*/
.wrapper-medias{
    width:45% !important;
    left:unset !important;
}
.authenct .pop-main{
  width:50% !important;
}
.head{
  font-size: 18px;
}
.gl-title{
  font-size:18px;
}
.img-txt{
  font-size: 18px !important;
}
.business-banner .activities-banner{
  width:100% !important;
}
.cs-vfpop{
  width: 45% !important;
}
/*针对fixed出错产生的偏左的样式*/
.wrapper-medias-xiala{
  width:45% !important;
  left:unset !important;
  right: 0 !important;
}
//针对签到签出的部分弹出框
.pad-msg{
  width: 40% !important;
  left: unset !important;
  transform: translate3d(6%, -50%, 0) !important;
}
//针对首页的拖动框
.pad-guide{
  left:unset !important;
  text-align: right !important;
  right:45%;
}

.pad-toolsline{
  width: 45vw !important; 
}

//NlDatePicker
.ipt-box-wrap{
  width:45%;
  left:unset !important;
  padding:0 !important;
}
.ipt-box-wrap input{
  width: 20% !important;
  flex:1 !important;
}