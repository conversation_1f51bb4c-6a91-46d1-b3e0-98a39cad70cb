//mixin
.clearfloat { /*清楚浮动*/
    display: block;
    content: '';
    clear: both;
    visibility: hidden;
  }
  
  .title { /*标题*/
    font-size: 16px;
    color: @color-TextTitle;
  }
  
  .ellipsis { /*点点点省略*/
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  
  .background-linear-gradient(@rotate:90deg,@startColor:#44D5F9,@endColor:#236DD9) { /* 渐变背景 */
    background: -webkit-linear-gradient(@rotate, @startColor, @endColor); /* Safari 5.1 - 6.0 */
    background: -o-linear-gradient(@rotate, @startColor, @endColor); /* Opera 11.1 - 12.0 */
    background: -moz-linear-gradient(@rotate, @startColor, @endColor); /* Firefox 3.6 - 15 */
    background: linear-gradient(@rotate, @startColor, @endColor); /* 标准的语法 */
  }
  
  .box-shadow(@color:#d0e0e8) {
    box-shadow: 4px 4px 12px @color;
  }