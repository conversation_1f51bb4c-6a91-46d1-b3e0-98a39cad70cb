@import "./variable.less";
@import "./mixin.less";
@import "./animate.less";

/*样式重置*/
html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, embed,
figure, figcaption, footer, header, hgroup,
menu, nav, output, ruby, section, summary,
time, mark, audio, video,input {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  vertical-align: baseline;

  -webkit-touch-callout:none;
  user-select:none;
}


/* HTML5 display-role reset for older browsers */
article, aside, details, figcaption, figure,
footer, header, hgroup, menu, nav, section {
  display: block;
}

body {
  line-height: 1;
  -webkit-overflow-scrolling: touch;
}

ol, ul {
  list-style: none;
}

blockquote, q {
  quotes: none;
}

blockquote:before, blockquote:after,
q:before, q:after {
  content: '';
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

html,
body {
  height: 100%;
  font-family: Helvetica, sans-serif;
  overflow: auto;
}

button{
  outline: none;
}
input, textarea, select, button{
  font-size:16px;
}
/* 阴影背景 */

/*正文样式*/
.flex-layout {
  display: flex;
  display: -webkit-flex;
  flex-direction: column;
}

.html-wrap {
  background-color: @color-BG;
  height: 100%;
  width: 100%;
  overflow: auto;
}
.topWrap{
    flex-grow: 1;
    overflow: auto;
    display: flex;
    display: -webkit-flex;
    flex-direction: column;
    .header-wrap{
      background-color:#fff;
      flex:0 1;
      height: 46px;
      line-height: 46px;
      .header{
          font-size:16px;
          text-align:center;
          color:#232323;
      }
    }
    .decoration-line{
        background:linear-gradient(45deg,rgba(68,213,249,1) 0%,rgba(35,109,217,1) 100%);
        box-shadow:0px 4px 7px 0px rgba(215,215,215,0.5);
        height:1px;
        border:0;
        width:100vw;
    }
}
.work-info-wrap {
  background-color: white;
  box-sizing: border-box;
  padding-bottom: 6px;
  position: relative;
  flex-shrink: 0;
  z-index: 997;
  transition:all .3s linear;
  &.hide{
    transform: translateY(-300px);
    height: 0;
    padding-bottom: 0;
  }
  &.show{
    transform: translateY(0)
  }
  &:after {
    .clearfloat();
  }
  .decoration-line {
    position: absolute;
    bottom: -1px;
    left: 0;
    height: 1px;
    width: 100%;
    .background-linear-gradient(90deg, #44D5F9, #236DD9);
  }
}

//信息展示区第一层
.main-info-box {
  position:relative;
  padding: 16px 0;
  &:after {
    .clearfloat();
  }
}

//左侧登录块
.login-info-box {
  float: left;
  width: 33%;
  /* padding: 0 16px; */
  padding: 0 0 0 16px;
  box-sizing: border-box;

  //登录名
  .login-info {
    margin-top: 4px;
    margin-bottom: 10px;
    span {
      display: inline-block;
      font-size: 24px;
      color: #232323;
      max-width: 72px;
      .ellipsis();
      font-weight: bold;
      vertical-align: middle;
    }
    .iconfont {
      font-weight: normal;
      font-size: 16px;
      vertical-align: middle;
    }

  }
  //登录按钮
  .log-btn {
    background: #8AABBD;
    box-shadow: 0 2px 4px 0 #CECECE;
    border-radius: 12px;
    color: white;
    padding: 4px 8px;
    display: inline-block;
    text-align: center;
    span {
      font-size: 12px;
      vertical-align: middle;
      padding-right: 4px;
    }
    .iconfont {
      font-size: 16px;
      vertical-align: middle;
    }
  }

}

//右侧数据块
.date-show-right {
  position: absolute;
  padding: 0 16px;
  box-sizing: border-box;
  left:33%;
  top:16px;
  bottom:0;
  width: 67%;
  border-left: 1px solid #F5F5F5;
  .date-show-box {
    .background-linear-gradient(45deg, #5196FF, #2A68FF);
    border-radius: 60px;
    /* .box-shadow(#4889FC); */
    box-shadow: 3px 3px 12px #7ac0ff;
    padding: 14px 8px;
    &:after {
      .clearfloat();
    }
    li {
      float: left;
      width: 33.33%;
      color: white;
      text-align: center;
      .data-text {
        font-size: 26px;
        color: #FFFFFF;
      }
      .data-title {
        font-size: 12px;
        color: #FFFFFF;
        font-weight: normal;
      }
    }
  }
}
.simple-head-wrap{
  color:#424242;
  font-size:30px;
  padding:0 16px;
  background-color:#fff;
  display:flex;
  display: -webkit-flex;
  font-weight:600;
  line-height: 60px;
  height: 60px;
  position: relative;
  z-index:99;
  &:after {
      .clearfloat();
  }
  .decoration-line {
      position: absolute;
      bottom: 0px;
      left: 0;
      height: 1px;
      width: 100%;
      .background-linear-gradient(90deg, #44D5F9, #236DD9);
  }
  .login-name {
      flex:0 1 auto;
      width:28%;
      position:relative;
      font-size:24px;
      &:after{
          content:'';
          position:absolute;
          width:2px;
          height:35px;
          background-color:@line-F5;
          border:0;
          top:13px;
          left:80%;
      }
  }
  .mid-num{
      flex:1 1 auto;
      display:flex;
      display: -webkit-flex;
      text-align:center;
      span{
          flex:1 1 auto;
      }
  }
  .up-arrow{
      flex:0 1 auto;
      width:45px;
      span{
          display:inline-block;
          width:100%;
          height:24px;
          background-color:@color-TextPrimary;
          color:#fff;
          border-radius:12px;
          text-align: center;
          margin-top: 10px;
          position:relative;
          i{
              position:absolute;
              left:50%;
              top: 0px;
              line-height: 24px;
              transform:translateX(-50%);
          }
      }
  }
}
.state-right{
  padding: 0 16px;
  background-color:#fff;
  margin-left:-16px;
  left:31%;
  position:absolute;
  right: 0;
  top: 0;
  bottom: 0;
  z-index:99;
  position: absolute;
  overflow:hidden;
  transition:all .4s linear;
  transform: translateX(-800px);
  &.show-state{
      transform: translateX(0px);
  }
}
//定位
.location-box {
  display: block;
  padding: 0 16px;
  box-sizing: border-box;
  height:30px;
  line-height:30px;
  position: relative;
  .dingweiweizhi {
    color: @color-IconNormal;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 16px;
    font-size: 20px;
  }
  .location-text {
    display: block;
    box-sizing: border-box;
    padding: 0 0 0 28px;
    height:30px;
    overflow:hidden;
    color: @color-TextContent;
    font-size: 14px;
    //.ellipsis();
  }
}

.info-box {
  display: block;
  padding: 0 16px;
  box-sizing: border-box;
  height:24px;
  line-height:24px;
  position: relative;
  .iconfont {
    .location-box .dingweiweizhi();
    font-size: 16px;
  }
  .infomation-text {
    .location-box .location-text();
    padding-right: 40px;

  }
  .more-btn {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: @color-TextContent;
    font-size: 14px;
  }
}

//工作台内容
.work-content-wrap {
  flex-grow: 1;
  overflow: auto;
  position: relative;
  .mid-wrap{
    /*overflow:hidden;height:100%;*/
  }
}
.no-data-wrap{
  height: 200px;
  border-radius: 8px;
  background-color: #fff;
  margin: 8px 16px;
  color:#737373;
  font-size:14px;
  text-align:center;
  display:flex;
  display: -webkit-flex;
  flex-direction:column;
  justify-content:center;
  align-items:center;
  img{
      width:50vw;
  }
}
//功能模块
.Common-functional-modules {
  /* margin: 12px 16px; */
  padding: 12px;
  box-sizing: border-box;
  height:110px;
  /* border-radius: 8px; */
  /* border-bottom: 1px solid #f5f5f5; */
  background-color: white;
  /* .box-shadow(); */
  .seciton-title {
    .title();
    position: relative;
    margin-bottom: 8px;
    .edit-btn {
      position: absolute;
      right: 0;
      font-weight: normal;
      font-size: 14px;
      color: @color-TextNormal;
      padding: 4px;
    }
  }
  .icon-list {
    display: flex;
    flex-wrap: wrap;
    &:after {
      .clearfloat();
    }
    text-align: center;
    li {
      //float: left;
      width: 25%;
      /* margin-top:8px; */
      .icon-img {
        width: 50%;
        margin-bottom: 4px;
      }
      .icon-name {
        display: block;
        font-size: 12px;
        color: @color-TextContent;
        .ellipsis();
      }
    }
    li:nth-child(n+5){
      margin-top:16px;
    }
  }
}

//工作台输入客户电话模块
.telnum-wrap{
  height:55px;
  padding: 0.5rem 0.75rem;
  box-sizing: border-box;
  background-color: white;
  position:relative;
  .input-wrap{
    display:flex;
    align-items: center;
    .title{
      font-weight:600;
      color:@color-TextPrimary;
      font-size:14px;
      padding-right:0.5rem;
      padding-left: 0.5rem;
      border-right: 1px solid #AED5FF;
      /* transition: all 0.5s linear; */
      animation: fanzhuan 0.5s linear;
      .iconfont{
        font-size: 12px;
        padding-left: 6px;
      }
    }
    .line{
      width:1px;
      height:16px;
    }
    /* height:30px;
    border-radius:4px;
    border:0;
    border:1px solid rgba(209,209,209,1);
    position:relative;
    line-height: 30px; */
    position:relative;
    height:38px;
    background:rgba(242,247,255,1);
    border-radius:19px;
    border:1px solid rgba(174,213,255,1);
    input{
      height: 28px;
      padding: 0;
      padding-left:1rem;
      font-size: 14px;
      border:0;
      background:rgba(242,247,255,1);
      &:focus{
        outline:none;
      }
    }
    .btn{
      position:absolute;
      right: 1rem;
      top:11px;
      color:#007AFF;
      font-weight:400;
      font-size:14px;
      border:none;
      .iconfont{
        padding-right:8px;
        font-size: 18px;
      }
    }
  }
}

//待办内容区域
.todo-wrap {
  padding-top: 12px;
  position:relative;
  .navbar-wrapper{font-size:14px;line-height:20px; padding: 0 16px;}
}

.tab-menu { /*tab 盒子*/
  margin: 16px 16px 0;
  position: relative;
  .menu-list { /*tab list*/
    padding-right: 40px;
    box-sizing: border-box;
    font-size: 14px;
    &:after {
      .clearfloat();
    }
    li {
      float: left;
      margin-right: 12px;
      color: @color-TextNormal;
    }
  }
  .filter-btn { /*紧急按钮*/
    position: absolute;
    right: 0;
    top: 0;
    font-size: 14px;
    color: @color-TextNormal;
  }
}

.todo-item {
  margin: 12px 16px;
  background-color: white;
  border-radius: 8px;
  .box-shadow();
  padding: 12px;
  .todo-title { /*todo标题*/
    font-size: 16px;
    color: @color-TextTitle;
    position: relative;
    margin-bottom: 8px;
    .todo-time {
      position: absolute;
      right: 0;
      top: 0;
      font-weight: normal;
      font-size: 12px;
      color: @color-TextContent;
    }
  }
  .todo-box { /*todo行内块*/
    position: relative;
    .todo-number {
      font-size: 24px;
      color: @color-TextPrimary;
    }
    .todo-progress-text {
      font-size: 12px;
      color: #4EA7D9;
      position: absolute;
      top: 12px;
      right: 55vw;
    }
    .progress-box {
      margin-top: 8px;
      border-radius: 10px;
      height: 2px;
      background-color: #D8D8D8;
      width: 30vw;
      .progress-line {
        display: block;
        width: 87%;
        background-color: @color-IconBlue;
        height: 2px;
      }
    }
    .todo-text {
      font-size: 12px;
      color: @color-TextContent;
      font-weight: normal;
    }
    .todo-tips {
      position: absolute;
      right: 0;
      top: 0;
      .iconfont {
        color: @color-TextWarning;
        vertical-align: middle;
      }
      .tips-text {
        font-size: 12px;
        color: @color-TextWarning;
        vertical-align: middle;
      }
    }
  }
}

//底部导航菜单 存在空余空间不放大
.menu-ul {
  flex-shrink: 0;
  flex-grow: 0;
  background-color: #FBFBFB;
  border: 0 solid #E5E5E5;
  box-shadow: 0 -2px 6px 0 rgba(229, 229, 229, 0.50);
  border-top: 1px solid #eee;
  padding: 8px 0;
  li {
    float: left;
    width: 20%;
    box-sizing: border-box;
    text-align: center;
    &.selected .iconfont {
      color: @color-IconBlue;
    }
    .menu-text {
      display: block;
      text-align: center;
      font-size: 12px;
      color: @color-TextContent;
      margin-top: 4px;
      transform:scale(0.9);
      width:100%;
    }
    .iconfont {
      font-size: 20px;
      color: #d5dbe6;
    }
    .round-line {
      position: relative;
      img {
        width: 43px;
        display: block;
        margin: 0 auto;
      }
      .iconfont{
        position: absolute;
        left: 50%;
        top:50%;
        transform: translate(-50%,-50%);
        font-size: 24px;
      }
    }
  }
}
.mint-indicator-wrapper{
  z-index: 2999;
}
.mint-msgbox-message{
  text-align:left!important;
  line-height: 24px;
  word-break: break-all;

}
.mint-msgbox-input input{
  box-sizing:border-box!important;
}
.mint-swipe-indicators{
  bottom:5px;
}
.mint-swipe-indicator.is-active{
  background: @color-TextPrimary;
  opacity: 0.8;
}

//工作台动画
@keyframes load{
  0%{transform:rotate(0)}
  100%{transform:rotate(360deg)}
}
.load {
  animation: load 1s linear 2;
  transform-origin: center center;
}

.nl-loading-parent--relative {
	position:relative!important
}
.nl-loading-parent--hidden {
	overflow:hidden!important
}
.nl-loading-mask {
	position:absolute;
	z-index:2000;
	background-color:rgba(255,255,255,.9);
	margin:0;
	top:0;
	right:0;
	bottom:0;
	left:0;
	-webkit-transition:opacity .3s;
	transition:opacity .3s
}
.nl-loading-mask.is-fullscreen {
	position:fixed
}
.nl-loading-mask.is-fullscreen .nl-loading-spinner {
	margin-top:-25px
}
.nl-loading-mask.is-fullscreen .nl-loading-spinner .circular {
	height:50px;
	width:50px
}
.nl-loading-spinner {
	top:50%;
	margin-top:-21px;
	width:100%;
	text-align:center;
	position:absolute
}
.nl-loading-spinner .circular {
	height:42px;
	width:42px;
	-webkit-animation:loading-rotate 2s linear infinite;
	animation:loading-rotate 2s linear infinite
}
.nl-loading-spinner .path {
	-webkit-animation:loading-dash 1.5s ease-in-out infinite;
	animation:loading-dash 1.5s ease-in-out infinite;
	stroke-dasharray:90,150;
	stroke-dashoffset:0;
	stroke-width:2;
	stroke:#409EFF;
	stroke-linecap:round
}
.nl-loading-spinner i {
	color:#409EFF
}
.nl-loading-fade-enter,.nl-loading-fade-leave-active {
	opacity:0
}
@-webkit-keyframes loading-rotate {
	100% {
    -webkit-transform:rotate(360deg);
    transform:rotate(360deg)
  }
}
@keyframes loading-rotate {
    100% {
    -webkit-transform:rotate(360deg);
    transform:rotate(360deg)
  }
}
@-webkit-keyframes loading-dash {
	0% {
    stroke-dasharray:1,200;
    stroke-dashoffset:0
  }
  50% {
    stroke-dasharray:90,150;
    stroke-dashoffset:-40px
  }
  100% {
    stroke-dasharray:90,150;
    stroke-dashoffset:-120px
  }
}
@keyframes loading-dash {
	0% {
    stroke-dasharray:1,200;
    stroke-dashoffset:0
  }
  50% {
    stroke-dasharray:90,150;
    stroke-dashoffset:-40px
  }
  100% {
    stroke-dasharray:90,150;
    stroke-dashoffset:-120px
  }
}
.content-box {
  border-top: 1px solid #eee;
  width: 100%;
  background-color: white;
  position: relative;
  display: inline-flex;
  display: -webkit-inline-flex;
  flex-grow: 1;
  -webkit-flex-grow: 1;
  overflow: auto;
  background-color: #ECF0FA;
}

.menu-link-content {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  display: -webkit-flex;
  flex-direction: row;
  -webkit-flex-direction: row;
}

.side-menu {
  position: relative;
  overflow: auto;
  height: 100%;
  width: 26vw;
  flex-shrink: 0;
  -webkit-flex-shrink: 0;
  background: #F4F4F4;
}

.side-menu > li.selected {
  background-color: white;
  font-size: 14px;
  color: #4A90E2;
}

.side-menu > li {
  width: 100%;
  height: 58px;
  line-height: 58px;
  text-align: center;
  font-size: 14px;
  color: #262626;
  padding: 0 6px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  box-sizing: border-box;
  position: relative;
}

.custom-view {
  display: flex;
  display: -webkit-flex;
  flex-direction: row;
  -webkit-flex-direction: row;
  padding: 10px;
  background-color: white;
  .box-shadow()
}

.custom-input {
  width: 283px;
  height: 38px;
  border-radius: 4px;
  border: 1px solid rgba(209, 209, 209, 1);
  padding-left: 8px;
  flex-grow: 1;
  flex-shrink: 1;
  -webkit-flex-grow: 1;
  -webkit-flex-shrink: 1;
  outline: none;
  font-size: 14px;
  box-sizing: border-box;
}

.btn-border {
  height: 38px;
  line-height: 38px;
  padding: 0 8px;
  color: #007AFF;
  border-radius: 4px;
  border: 1px solid rgba(0, 122, 255, 1);
  margin-left: 8px;
  flex-shrink: 0;
  -webkit-flex-shrink: 0;
  flex-grow: 0;
  -webkit-flex-grow: 0;
  box-sizing: border-box;
}

.Common-functional-modules.block {
  margin: 0;
  border-radius: 0;
}

.business-area {
  width: 100%;
  height: 100%;
}

.business-list {
  padding-top: 16px;
  overflow: hidden;
}

.business-list li {
  width: 33.33%;
  float: left;
  text-align: center;
  margin-bottom: 16px;
}

.business-list li img {
  width: 46%;
  display: block;
  margin: 0 auto;
  min-width: 36px;
}

.business-icon-name {
  margin-top: 4px;
  font-size: 12px;
  font-weight: 400;
  display: block;
  color: #666666;
  line-height: 17px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  padding: 0 4px;
  box-sizing: border-box;
}

.hot-recommend {
  margin: 0 10px;
  position: relative;
  padding: 16px 0;
  border-bottom: 1px solid #F0F0F0;
}

.recommend-img {
  width: 60px;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
}

.recommend-text {
  width: 100%;
  padding: 0 60px 0 60px;
  box-sizing: border-box;
}

.recommend-title {
  font-size: 14px;
  font-weight: 600;
  color: rgba(51, 51, 52, 1);
  line-height: 20px;
}

.recommend-secondary {
  font-size: 12px;
  font-weight: 400;
  color: rgba(135, 135, 135, 1);
  line-height: 17px;
}

.zone-content-wrap {
  flex-grow: 1;
  -webkit-flex-grow: 1;
  display: flex;
  display: -webkit-flex;
  flex-direction: column;
  -webkit-flex-direction: column;
  background-color: #ECF0FA;
}

.activities-list {
  width: 100%;
  padding: 16px;
}

.activities-list li {
  padding: 16px;
  background-color: white;
  border-radius: 8px;
  margin-bottom: 16px;
}

.activities-banner {
  width: 100%;
  display: block;
  margin-top: 8px;
  border-radius: 4px;
}

.activities-title {
  font-size: 14px;
  font-weight: 600;
  color: #333334;
  line-height: 17px;
}

.activities-secondary {
  font-size: 12px;
  color: #A0A0A0;
}
.alert-title{
  color:#2A68FF;
}
.hide{
  display:none;
}
@import "./overwrite.less";
.popContent{
  margin: .2rem;
  word-break: keep-all;
}
.popContent::before {
  content: '';
  border: solid transparent;
  border-width: 8px 5px;
  border-top-color: #FFFFFF;
  position: absolute;
  left:  calc(50% - 5px);
  top: 100%;
}

.BMap_cpyCtrl{
  display: none;
}
.anchorBL{
  display: none;
}
.anchorTR{
  display: none;
}
.BMapLabel{
  max-width:1000px;
}
.request-toast{
  z-index:9999999
}
