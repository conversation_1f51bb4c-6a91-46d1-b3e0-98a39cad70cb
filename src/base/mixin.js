import ClientJs from './clientjs'
import {dateFormat} from './utils.js'
import Storage from '@/base/storage.js'
import {cmpClientVersion,getRealUrl} from '@/base/utils';
import {qryAutoConfigByFeature,insertTraceRecorder} from  '@/base/request/commonReq.js';
import {Indicator} from 'mint-ui'
import ImageObj from '@/base/request/ImageServReq';

export const smartMixin = {
    methods:{
        //跳智能小信
        async goSmartXin(uInfo){
            let loginCode = uInfo.crmId;// 账号(crm账号)：loginCode =123455
            if(!loginCode){
                loginCode = uInfo.servNumber;
            }
            let loginName = encodeURI(uInfo.operatorName);// 操作员姓名：loginName =张三
            let region = uInfo.region;// 对应的地市：region =14
            let mobile = uInfo.servNumber;// 手机号：mobile =12812312312
            let indicate = '';// 验证码：indicate =xxxxxxxxxxxxx

            // 非客户经理客户端参数 channel=xs ，position=ald
            let channel = 'xs';// 行商渠道标识: channel=xs
            let position = 'ald';//其它情况客户端参数
            // 如果是客户经理岗位，客户端参数channel=jk ，position=manager
            if(uInfo.stationName && ~uInfo.stationName.indexOf('客户经理')){
                channel = 'jk';
                position = 'manager';
            }
          let token='';
          token=await this.getToken(uInfo);
            //验证码为loginCode + mobile + region + key 进行md5进行签名（key值可以设值xs)。
            indicate = loginCode + mobile + region + 'xs';
            this.$http.get('/xsb/ability/encrypt/h5XXMd5Encrypt?param=' + indicate).then(res=>{
                let {retCode,retMsg,data} = res.data;
                if(retCode == '0'){
                    indicate = data;//md5加密后的值
                    //亚信智能小信的链接
                    let url = `${Storage.get('webUrl')}/xt/XsMobile/loginMobile?loginCode=${loginCode}&loginName=${loginName}`;
                    url += `&mobile=${mobile}&region=${region}&channel=${channel}&indicate=${indicate}&position=${position}&token=${token}`;
                    console.info(url);
                    ClientJs.openWebKit(url, '', '0', '', '', '', '', '', '', '', '');
                } else {
                    this.$alert(retMsg || 'MD5算法失败')
                }
            });

        },
       getToken(uInfo){
          let url='/xsb/api-user/tokenAuthen/h5generateToken';
          let params={
            phoneNumber:uInfo.servNumber,
            regionId:uInfo.region,
            channelId:'5'
          }
         return this.$http.post(url,params).then(res=>{
          let {retCode,retMsg,data} = res.data;
            if(retCode=='0'){
             return data.token
            }else{
              this.$alert(retMsg || '获取token失败')
              return ''
            }
        })
      }
    }
}

export const tokenFromAldMixin = {
    methods:{
        //通过阿拉盯拉起外围菜单
        tokenFromAld(opId,prviId='-1',uinfo,title,urlParam){
            if(!prviId){//如果没有传菜单编码，设置默认值
                prviId = '-1';
            }

            let client = /android|harmony/gi.test(navigator.userAgent)?'ANDROID':'IOS';
            let url = `/xsb/api-user/tokenAuthen/h5genToken.do?opId=${opId}&prviId=${prviId}&clientType=${client}`;
            url += `&regionId=${uinfo.region}&operId=${uinfo.staffId}&stationId=${uinfo.stationId}&phoneNumber=${uinfo.servNumber}`;
            this.$http.get(url).then((response)=>{
                let {retCode,retMsg,data} = response.data;
                let opUrl = data.opUrl;
                if(retCode == '0'){
                    if(urlParam){
                        opUrl = data.opUrl + urlParam;
                    }
                    if(title){//有头
                        ClientJs.openWebKit(encodeURIComponent(opUrl),title,'1', '0', '', '', '', '', '', '', '')
                    } else {
                        ClientJs.openWebKit(encodeURIComponent(opUrl), '', '0', '', '', '', '', '', '', '', '');//没有头的链接

                    }
                } else {
                    this.$alert(retMsg || 'TOKEN拉起菜单失败');
                }
            }).catch((err)=>{
                this.$alert('TOKEN拉起菜单失败,' + err );
            })
        },
        tokenFromAldFrame(opId,prviId='-1',uinfo,title,urlParam){
          if(!prviId){//如果没有传菜单编码，设置默认值
              prviId = '-1';
          }

          let client = /android|harmony/gi.test(navigator.userAgent)?'ANDROID':'IOS';
          let url = `/xsb/api-user/tokenAuthen/h5genToken.do?opId=${opId}&prviId=${prviId}&clientType=${client}`;
          url += `&regionId=${uinfo.region}&operId=${uinfo.staffId}&stationId=${uinfo.stationId}&phoneNumber=${uinfo.servNumber}`;
          this.$http.get(url).then((response)=>{
              let {retCode,retMsg,data} = response.data;
              let opUrl = data.opUrl;
              if(retCode == '0'){
                  if(urlParam){
                      opUrl = data.opUrl + urlParam;
                  }
                  this.$router.push({
                      path: 'gateWayBox',
                      query: {
                        opSrc:opId,
                        hasBottom:0,
                        pTitle:title,
                      }
                })
              } else {
                  this.$alert(retMsg || 'FRM拉起菜单失败');
              }
          }).catch((err)=>{
              this.$alert('FRM拉起菜单失败,' + err );
          })
      },
        tokenAreaPage(region,uinfo, isDaka = false,otherData){
            if (region == '21' && isDaka) {
                // let url = getRealUrl('taiZUrl'); // @/base/utils.js
                // 记录拉起外围
                this.opMenuCollect('tzApp');
                let workUrl = '&toModule=/mobile/ald/empinfo/frame.do'
                this.tokenFromAld('tzApp','-1',uinfo,'地市专区',workUrl);
            }

            if(region == '21' && !isDaka){//泰州
                // let url = getRealUrl('tzUrl');
                // url += `&phoneNumber=${uinfo.servNumber}&operName=${uinfo.operatorName}`
                // ClientJs.openWebKit(url, '地市专区', '1', '0', '', '', '', '', '', '', '');
                // 记录拉起外围
                this.opMenuCollect('tzApp');
                let workUrl = `&phoneNumber=${uinfo.servNumber}&operName=${uinfo.operatorName}`
                this.tokenFromAld('tzApp','-1',uinfo,'地市专区',workUrl);
            }else if(region == '18'){//镇江
                // 记录拉起外围
                this.opMenuCollect('zjApp');
                this.tokenFromAld('zjApp','-1',uinfo,'地市专区');
            }else if(region == '20'){//南通
                // 记录拉起外围
                this.opMenuCollect('ntgrid');
                this.tokenFromAld('ntgrid','-1',uinfo);
            }else if(region == '12'){//淮安地市
              if(isDaka){
                // 记录拉起外围
                this.opMenuCollect('haydAi');
                this.tokenFromAld('haydAi','-1',uinfo);
              }else{
                // 记录拉起外围
                this.opMenuCollect('haydApp');
                this.tokenFromAld('haydApp','-1',uinfo);
              }
            }else if(region == '11'){//苏州地市
                // 记录拉起外围
                this.opMenuCollect('poineer');
                this.tokenFromAld('poineer','-1',uinfo);
            }else if(region == '19'){//无锡
                // 记录拉起外围
                this.opMenuCollect('wxImpRpt');
                this.tokenFromAld('wxImpRpt','19001',uinfo);
            }else if(region == '22'){//盐城
                // 记录拉起外围
                this.opMenuCollect('ycApp');
                this.tokenFromAld('ycApp','-1',uinfo)
            }else if(region == '15'){//连云港
                // 记录拉起外围
                this.opMenuCollect('lygApp');
                this.tokenFromAld('lygApp','-1',uinfo)
            } else if(region == '23'){//扬州地市专区接入：op_id:yzApp，region：23
                // 记录拉起外围
                this.opMenuCollect('yzApp');
                 this.tokenFromAld('yzApp','-1',uinfo)
            } else if(region == '17'){//常州地市专区接入：op_id:czApp
                // 记录拉起外围
                this.opMenuCollect('czApp');
                this.chooseOpenMethod(uinfo);
            } else if(region == '14'){//南京地市专区
                // 记录拉起外围
                this.opMenuCollect('njapp');
                this.tokenFromAld('njapp','-1',uinfo,'地市专区');
            } else if(region == '16'){//徐州地市专区
                // 记录拉起外围
                this.opMenuCollect('xzApp');
                this.tokenFromAld('xzApp','-1',uinfo,'地市专区');
            } else if(region == '13'){//宿迁地市专区
                // 记录拉起外围
                this.opMenuCollect('sqApp');
                if(otherData =="suqian1"){
                  let workUrl = '&source=omms'
                  this.tokenFromAld('sqApp','-1',uinfo,'地市专区',workUrl);
                }else if(isDaka){
                  let workUrl = '&source=ai'
                  this.tokenFromAld('sqApp','-1',uinfo,'地市专区',workUrl);
                }else{
                  this.tokenFromAld('sqApp','-1',uinfo,'地市专区');
                }
            }
        },
        //判断拉起方式
        chooseOpenMethod(uinfo){
          let servNumber = uinfo.servNumber;
            qryAutoConfigByFeature('ability','frameView_flag','checkFlag').then(res=>{
              let {retCode,data} = res.data;
              if(retCode === '0' && ~data.indexOf(servNumber)){
                //开关开启了，使用frameView方式打开
                this.tokenFromAldFrame('czApp1','-1',uinfo);
              }else{
                //使用原来的openWebkit方式打开
                this.tokenFromAld('czApp','-1',uinfo);
              }
            }).catch(error=>{
              console.info(error)
            })

        },
        tokenFromMenu(item,uinfo){
            let privId = item.privId || item.funcId;
            let webUrl = Storage.get('webUrl');
            let client = /android|harmony/gi.test(navigator.userAgent)?'ANDROID':'IOS';

            let url = `${webUrl}/iportal/tokenAuthen/genToken.do?opId=${item.opId}&prviId=${privId}&phoneNumber=${uinfo.servNumber}`;
            url += `&regionId=${uinfo.region}&operId=${uinfo.staffId}&stationid=${uinfo.stationId}&client_type=${client}`;

            if(privId === '145'){//打卡
                url += '&phoneNumber=' + uinfo.servNumber;
            }
            if(item.opParentid != 'fsop'){//非阿拉盯渠道
                if(item.opParentid == 'pgop' || item.opParentid=='jkddzx' || item.opParentid=='DZXY'){
                    url = `/xsb/api-user/tokenAuthen/h5genToken.do?opId=${item.opId}&prviId=${privId}&clientType=${client}`;
                    url += `&regionId=${uinfo.region}&operId=${uinfo.staffId}&stationId=${uinfo.stationId}&phoneNumber=${uinfo.servNumber}`;
                }
                this.$http.get(url).then((response)=>{
                    let data = response.data;
                    let opUrl = '';
                    if(item.opParentid == 'pgop' || item.opParentid=='jkddzx' || item.opParentid=='DZXY'){
                        opUrl = data.data.opUrl;
                    }else{
                        opUrl = data.opUrl;
                    }
                    console.info(opUrl);
                    ClientJs.openWebKit(encodeURIComponent(opUrl), '', '0', '', '', '', '', '', '', '', '');
                }).catch((response)=>{
                })
            } else {
                this.tokenFromAld(item.opId,privId,uinfo,'');
            }
        },
        // 拉起外围菜单入表
        opMenuCollect(privId) {
          // try { // 凑参数
          //   const uinfo = Storage.session.get('userInfo')
          //   const params = {
          //     stationId: uinfo.stationId,
          //     privId: privId,
          //     area: '',
          //     type: 'out_open'
          //   }
          //   this.$http.post('/xsb/api-user/commonFunc/h5commCollect', params).catch((response)=>{
          //     console.log('拉起外围请求失败', response)
          //   })
          // } catch (e) {
          //   console.log('拉起外围入表失败', e)
          // }
        }
    }
}

//底部菜单切换需要判断校园营销岗位、集客随销岗位有没有输入关联的手机号或者工号
export const needNumMixin = {
    methods:{
        checkStationTelNum(stationId,relTelnum){
            let retFlg = true;
            if(stationId == this.CONSTVAL.STATION_JKSX|| stationId ==this.CONSTVAL.STATION_SCHOOL){
                //你当前是集客随销岗位，XXX
                let msg = '';
                if(stationId == this.CONSTVAL.STATION_JKSX){
                    msg = '你当前是 <span class="alert-title">集客随销岗位</span> ，请前往【我的】输入关联的工号';
                } else {
                    msg = '你当前是 <span class="alert-title">校园营销岗位</span> ，请前往【我的】输入关联的手机号';
                }
                if(!relTelnum){
                    this.uinfo = Storage.session.get('userInfo');
                    relTelnum = Storage.session.get('userInfo').relTelnum;
                }
                if(!relTelnum){
                    retFlg = false;
                    this.$messagebox({
                        title: '温馨提示',
                        message: msg,
                        showCancelButton: false,
                        closeOnPressEscape: false,
                        closeOnClickModal: false
                    }).then(action =>{
                        this.$router.push('/my');
                    });
                }
            }
            return retFlg;
        }
    }
}

//获取定位信息
export const clientLocalMixin = {
    methods:{
        getClientTrueLocal(){
            ClientJs.getLocation('','getLocationInfoCb');
        },
        //获取地理位置的返回事件
        getLocationInfoCb(obj){
            let location;
            location = obj.address;//位置
            if(!obj.location){//没获取到客户端返回的地理位置信息
                if(!Storage.get('location') || Storage.get('location')=='empty'){//手机H5也没有缓存过的位置信息
                    this.getLocationFromAldParent();
                }
            } else {//客户端返回了地理位置信息
                Storage.set('location',obj.location || 'empty');
                Storage.set('latitude',obj.latitude || 'empty');
                Storage.set('longitude',obj.longitude || 'empty');
            }
            //没获取到位置信息
            if(!location){
                this.$alert('未获取到位置信息，无法签到');
            } else {
                this.$store.commit('SET_LOCAL',location);
            }
        },
        //如果没有从客户端获取到地理位置信息，则从服务端获取
        getLocationFromAldParent(){
            let uinfo = Storage.session.get('userInfo');
            //staffId 唯一编码 phoneNumber 行商账号 imei imsi账号 ${uinfo.imei}(先不传)
            let url = `/xsb/api-user/location/h5qryLocation?staffId=${uinfo.staffId}&phoneNumber=${uinfo.servNumber}&imei=`;
            this.$http.get(url).then(res => {
                let {retCode,retMsg,locationInfo} = res.data;
                if(retCode == '0'){
                    Storage.set('location',locationInfo.location || 'empty');
                    Storage.set('latitude',locationInfo.latitude || 'empty');
                    Storage.set('longitude',locationInfo.longitude || 'empty');
                } else {
                    this.$alert(retMsg || '获取地理位置信息失败')
                }
            })
        }
    },
    mounted(){
        // 将getLocationInfoCb方法绑定到window下面，提供给外部调用
        window['getLocationInfoCb'] = (result) => {
            this.getLocationInfoCb(result)
        }
    }
}

export const clientFromOutMixin = {
    data(){
        return{
            location:'月球',//定位信息
            latitude:'0.1',// 纬度
            longitude:'0.1',// 经度
            webUrl:'',
            uinfo:{}
        }
    },
    mounted(){
        // 将getLocationInfoCb方法绑定到window下面，提供给外部调用
        window['getSysInfoCB'] = (obj) => {
            this.webUrl = obj.serverUrl;
            this.location = obj.address;//位置
            this.latitude = obj.latitude;//经度
            this.longitude = obj.longitude;//纬度
            let res = obj.userInfo;
            let uinfo = JSON.parse(res);
            initTokenAfterBack(this.$http,uinfo);//edit by qhuang at 2021/11/29
            // this.$http.defaults.headers.tokenid = uinfo.tokenid;
            // Storage.session.set('tokenId',uinfo.tokenid);
            // let oldUinfo = Storage.session.get('userInfo');
            // let relTelnum = oldUinfo && oldUinfo.relTelnum;//学生手机号
            // if(relTelnum){
            //     uinfo.relTelnum = relTelnum;
            // }
            // Storage.session.set('userInfo',uinfo);
            this.uinfo = uinfo;
            Storage.set('webUrl',this.webUrl);
            this.doSth();
        };

        //获取操作员基本信息
        ClientJs.getSysInfo('getSysInfoCB');


    }
}


//读取身份证相关的
export const clientLanyaMixin = {
    data() {
        return {
            marketTimer:null,
            nfcFlg:false,
            isAndroid:/android|harmony/gi.test(navigator.userAgent),
            curLanya:{id:'0',label:'请选择'},//当前选择的蓝牙设备
            readSfzFlg:false,
            nowDate:'',//当前日期
            limitDate:'',//限制年龄  16岁
            saveSfzFlg:false,
            sfzAttachId:'',//身份证附件ID
            headId:'',//身份证头像ID
            signImgs:'',//签名图片
            pseq:'',//拍照返回 证件欲占返回的
            netSrl:'',//拍照返回 提交的主键
			verifySimilarity:'',//拍照返回，比对相似度
            isScanning:false,//是否正在扫描蓝牙
            isReadingFlg:true,//是否正在读取身份证
            busiType:'',//业务类型 选号入网业务类型：enter_net，物联网卡业务：iot_enter_net
            sfzCameraImageIpdFlg:true,//没有生僻字，不需要上传拍摄的照片
            rareWordPicId:'',//有生僻字后上传拍摄的照片的附件编号
            sfzObj:{
                sfzId:'',
                sfzName:'',
                sfzAddr:'',
                usAddr:'',
                sfzStartDate:'',
                sfzEndDate:'',
                nation:'',
                sex:'',
                idCardType:'',
                idCardValidity:'',
                birthYear:'',//年
                birthMonth:'',//月
                birthDay:'',//日
                idCardOrganization:''
            },
            tmpIsPublicSrc:0,//默认为非公用（预配号）
            isRare:'0',//0 无生僻字  1 有生僻字
            sfzCameraImage:'',//拍的身份证图片
            sfzClientPath:'',//照片在手机客户端的路径
            clientRetSfzId:'',//客户端返回
            idcardParam:'',//因一证多名加的新透传参数
            versionSupNfc:'2.20.64',//支持nfc版本为2.20.71
            onnetNum:'',//在网号码数
            threeMonthNum:'', //近3个月的新入网号码数
            samedaydiffchnlIn7days:'', //省内号码数
            insideprovSubscount:'', //省外号码数
            outsideprovSubscount:'', //近7天在同一天、不同渠道入网2个及以上的号码数
            usernum:'',//在网号码数（全网）
            uploadZmFlag: false, // 是否需要上传收入证明
            stopUsers:'',//当前强制停机用户数
            forbidUsers:'',//当前禁止业务半停用户数
            stop180Users:'',//近180天内强制停机次数
            forbid180Users:'',//近180天内禁止业务半停次数
            drop180Users:'',//近180天内销户用户数
            sfzObjShengPi:{
                isRareFlag:'0',
                sfzNameList:[],
            },
            applyId:'',//一老一小审批单号
        }
    },
    methods:{
        //是否支持NFC
        isSupportNFC(){
            if(/android|harmony/gi.test(navigator.userAgent)){
                window.upload.isSupportNFC("isNFC");
            }
        },
        //调用客户端扫描匹配过的蓝牙设备
        clientScan(){
            this.isReadingFlg = true;
            this.isScanning = true;//正在扫描蓝牙设备
            ClientJs.autoReadDevise("autoPpCallBack");
        },
        //展示所有的蓝牙设备
        showLanyaDevice(){
            this.isReadingFlg = true;
            this.readSfzagainFlg = false;
            this.clearSfzInfo();
            ClientJs.showLanyaDevice("getIdentify","isSaveCallBack",this.tmpIsPublicSrc,this.idcardParam)
        },
        //读取nfc按钮事件
        readSfzNfc(){
            //获取版本号
            if(!this.cmpVersion()){
                this.$messagebox({
                    title: '温馨提示',
                    message: '当前阿拉盯版本暂不支NFC读取功能，请到【关于】页面下载最新app',
                    showCancelButton: false,
                    confirmButtonText: '确认'
                }).then((action) => {
                this.$router.push('/about')
                return;
              });
            }else{
            this.isReadingFlg = true;
            let tmpFlg = false;
            if(this.tmpIsPublicSrc == '1'){
                tmpFlg = true;
            }
             this.clearSfzInfo();//清除上次读取的证件信息
            if(window.upload.nfcReadIdCardPassthrough){//因一证多名需求增加了透传给服务端的参数
                window.upload.nfcReadIdCardPassthrough("getIdentify","isSaveCallBack",tmpFlg,this.idcardParam);
            } else {
                window.upload.nfcReadIdCard("getIdentify","isSaveCallBack",tmpFlg);
            }
        }
        },
         //nfc读取判断版本大于2.20.64
         cmpVersion(){
            let curV = Storage.session.get('clientVersion');//从缓存中获取客户端版本
            let canNfc=true;
            if(!curV){
                //从客户端版本获取版本信息
                var res = window.WebViewFunc.getAppVersion();
                var obj = eval('(' + res + ')');
                curV = obj.verison;
                Storage.session.set('clientVersion',obj.verison);
            }
            //判断是阿拉丁还是泛渠道
            if(curV&&curV.substr(0,1)=='2'){
                canNfc=cmpClientVersion(curV,this.versionSupNfc);
            }
            return canNfc;
        },
        //点击拍照
        paizhao(uinfo,callBackFn,noticeCallBackFn){

            let sfzObj = this.sfzObj;
            if(!sfzObj.sfzId){//如果没有证件号，则不再继续办理
                this.$alert('身份证数据留存失败，请重新读取身份证');
                return;
            }
            let jqDataRegion="";
            //补换卡，取用户地市
            if(this.busiType =='changecard_prod'){
                jqDataRegion=Storage.session.get('jqData').userCity;
            }

            let param = {
                userName:sfzObj.sfzName,//证件名
                address:sfzObj.sfzAddr,//地址
                certId:sfzObj.sfzId,//证件号
                deviceType:this.curLanya.id,//读证设备类型
                deviceName:this.curLanya.label,//读证设备名称
                phoneType:sfzObj.phoneType,//手机型号
                certeffDate:sfzObj.sfzStartDate,//生效时间
                certexpDate:sfzObj.sfzEndDate,//失效时间
                telNumber:this.telNumber,//预配号
                idCardType:sfzObj.idCardType,//证件类型
                headId:this.headId,//身份证头像流水
                staffId: uinfo.staffId,
                region: uinfo.region,
                crmId: uinfo.crmId,
                busiType:this.busiType,//业务类型 选号入网业务类型：enter_net，物联网卡业务：iot_enter_net
                servNumber: uinfo.servNumber,
                jqDataRegion:jqDataRegion,
            }
            if(this.busiType == 'iot_enter_net'){//13位物联网卡号人证比对通过不了
                param.telNumber = '';
            }
            // 特殊选号入网/预配号补资料使用 选号入网/预配号是否进行人证比对标识 1:不进行
            if (uinfo.isNoCheck == '1') {
                param.isNoCheck = '1';
            }
            console.info(param);
            //noticeCallBack 立即回调 通知拍照结果
            //新旧人像框判断
            this.checkOpenNewCamera("2.24.40").then((res) => {
                if (res) {
                    console.info("新人像框开启 ")
                    ClientJs.openCameraPortrait(JSON.stringify(param), callBackFn || "mCallBackMethod", noticeCallBackFn || "noticeCallBack", this.tmpIsPublicSrc);
                }else{
                    ClientJs.openCamera(JSON.stringify(param), callBackFn || "mCallBackMethod", noticeCallBackFn || "noticeCallBack", this.tmpIsPublicSrc);

                }
            });
        },
        //查询是否开启
        async checkOpenNewCamera(compareVersionNew){
            let curV = Storage.get('appVersion');//从缓存中获取客户端版本
            let flag= cmpClientVersion(curV,compareVersionNew)
            if(flag) {
                let param = {
                    busiType: "open_new_camera",
                };
                await this.$http.post('/xsb/ability/businessLimit/h5QryBusiPermission', param).then((res) => {
                    let {retCode} = res.data;
                    if (retCode == '0') {
                        flag = true;
                    } else {
                        flag = false;
                    }
                });
            }
            return flag;
        },

        //读取身份证
        readSfz(){

            // //TODO
            // let sfzResult = {
            //     retCode:'0',
            //     retMsg:'',
            //     deviceType:'100',
            //     deviceName:'华大100',
            //     readIdNo:'321029200112098827',
            //     readName:'我姓名',
            //     readAddress:'身份证地址',
            //     certeffdate:'20100102',
            //     certexpdate:'20250102',
            //     idCardSex:'柯尔克孜族',
            //     idCardNation:'男',
            //     phone_type:'',
            //     idCardType:'IdCard'
            // };
            // let saveResult = {
            //     retCode:'0',
            //     retMsg:'预留身份证信息失败'
            // };

            // setTimeout(function(){getIdentify(JSON.stringify(sfzResult))},2000);
            // setTimeout(function(){isSaveCallBack(JSON.stringify(saveResult))},4000);
            // return;

            this.isReadingFlg = true;
            if(this.curLanya.id == "5"){//当前连接的是手机NFC
                let tmpFlg = false;
                if(this.tmpIsPublicSrc == '1'){
                    tmpFlg = true;
                }

                if(window.upload.nfcReadIdCardPassthrough){//因一证多名需求增加了透传给服务端的参数
                    window.upload.nfcReadIdCardPassthrough("getIdentify","isSaveCallBack",tmpFlg,this.idcardParam);
                } else {
                    window.upload.nfcReadIdCard("getIdentify","isSaveCallBack",tmpFlg);
                }
            } else {
                //蓝牙设备读取
                ClientJs.readIdCard("getIdentify","isSaveCallBack",this.tmpIsPublicSrc,this.idcardParam);
            }
        },
        //add by qhuang 20190620 身份证上有生僻字，需要拍下身份证照片
        openCameraAndShow(){
            ClientJs.openCameraAndShow("1","getSfzCameraPicCb")
        },
        clearSfzInfo(){
            let sfzObj = this.sfzObj;
            sfzObj.sfzId = '';//读取到的证件号
            sfzObj.sfzName = '';//读取到的名字
            sfzObj.sfzAddr = '';//读取到的地址
            sfzObj.usAddr = '';//用户常住地址默认为身份证地址
            sfzObj.sfzStartDate = '';//身份证有效期begin
            sfzObj.sfzEndDate = '';//身份证有效期end
            sfzObj.phoneType = '';
            sfzObj.nation = '';
            sfzObj.sex = '';
            sfzObj.idCardType = '';
            sfzObj.idCardValidity = '';
            sfzObj.idCardOrganization = '';
            sfzObj.birthYear = '';//年
            sfzObj.birthMonth = '';//月
            sfzObj.birthDay = '';//日
            sfzObj.passNumber = '';//港澳台参数
            sfzObj.issuesNums = '';//港澳台参数
            this.saveSfzFlg = false;
            this.sfzAttachId = '';//身份证附件ID
            this.headId = '';//身份证头像ID
            this.uploadZmFlag = false;
        },
        //签名
        sign(){
            ClientJs.qianming('signCallBack');
        },
        //重点业务投诉标签
        queryLabelUnify(){
            if(this.sfzObj.sfzId) {
                let param = {
                    certId: this.sfzObj.sfzId,
                };
                let url = `/xsb/ability/qryAccessNum/h5queryLabelUnify`;
                this.$http.post(url,param).then(res => {
                    let {retCode,retMsg,data} = res.data;
                    if (retCode == '0') {
                       let flag = data.s000000107981;
                       if(flag =='是'){
                           this.$alert('重要友好客户！请注意服务态度、业务办理规范及关键要素告知，' +
                             '严禁出现过度营销、服务推等行为。请主动询问客户手机网络、宽带上网、资费营销方面感知及问题，' +
                             '通过“客户感知收集”录入系统引导客户参与后续满意度外呼调查，并给予好评。');
                       }
                    } else {
                        this.$alert(retMsg || '重点业务投诉标签查询失败');
                    }
                }).catch((response)=>{
                    this.$alert(response || '重点业务投诉标签网络超时');
                })
            }
        },

        //客户关怀查询接口
        qrySubsCertIdNum(){
          if(this.sfzObj.sfzId) {
            let url = `/xsb/ability/qryAccessNum/h5qrySubsCertIdNum?certId=${this.sfzObj.sfzId}`;
            this.$http.get(url).then(res => {
              let {retCode,retMsg,data} = res.data;
              if (retCode == '0') {
                this.onnetNum = data.onnetNum;
                this.threeMonthNum = data.threeMonthNum;
                this.samedaydiffchnlIn7days = data.samedaydiffchnlIn7days;
                this.insideprovSubscount = data.insideprovSubscount;
                this.outsideprovSubscount = data.outsideprovSubscount;
              } else {
                this.$alert(retMsg || '客户证件名下用户数查询接口失败');
                this.onnetNum ='';
                this.threeMonthNum = '';
                this.samedaydiffchnlIn7days = '';
                this.insideprovSubscount = '';
                this.outsideprovSubscount = '';
              }
            }).catch((response)=>{
              this.$alert(response || '获取客户证件名下用户数查询接口网络超时');
              this.onnetNum ='';
              this.threeMonthNum = '';
              this.samedaydiffchnlIn7days = '';
              this.insideprovSubscount = '';
              this.outsideprovSubscount = '';
            })
          }
        },

      qryCertHandlingBroadband() {
        let url = `/xsb/personBusiness/xqkd/h5qryCertHandlingBroadband`;
        let param = {
          groupCertificateType: "BusinessLicence",
          groupCertificateId: this.licenseId,
          certificateType: "IdCard",
          certificateId: this.sfzObj.sfzId,
        }
        this.licenseInfo = {};
        this.$http.post(url, param).then(res => {
          let {retCode, data} = res.data;
          if (retCode === '0') {
            this.havenHandle = true;
            this.licenseInfo = data;
          }
        })
      },

      checkCardId() {
        let url = `/xsb/gridCenter/citySpecific/h5getCusNetInfoAndCheck`;
        let param = {
          msisdn: this.userTelNumber,
          needCheck: "1",
          userCertId: this.sfzObj.sfzId
        };
        this.$http.post(url, param).then(res => {
          let { retCode, retMsg } = res.data;
          if (retCode !== '0') {
            this.readSfzFlg = false;
            this.readSfzagainFlg = true;
            this.$alert(retMsg || '使用人证件信息校验失败！');
          }
        }).catch(reason => {
          this.readSfzFlg = false;
          this.readSfzagainFlg = true;
          this.$alert(`使用人证件信息校验异常，error: ${reason}`);
        })
      },

      idcheck() {
        if (this.sfzObj.sfzId) {
          let param = {
            certId: this.sfzObj.sfzId,
            idCardType: this.sfzObj.idCardType,
            userName: this.sfzObj.sfzName,
            unLoadFlg:true,
          };
          let url = `/xsb/ability/qryAccessNum/h5idcheck`;
          this.$http.post(url, param).then(res => {
            let {retCode, retMsg, data} = res.data;
            if (retCode == '0') {
              this.usernum = data;
            } else {
              this.$toast(retMsg || '查询证件下信息接口失败');
              this.usernum = '';
            }
          }).catch((response) => {
            this.$toast(response || '查询证件下信息接口网络超时');
            this.usernum = '';
          })
        }
      },
      //查询用户销户号码数f
      grpusersInfo(){
        if (this.sfzObj.sfzId) {
          let param = {
            certId: this.sfzObj.sfzId,
            idCardType: this.sfzObj.idCardType,
            unLoadFlg:true,
          };
          let url = `/xsb/ability/qryAccessNum/h5grpusersInfo`;
          this.$http.post(url, param).then(res => {
            let {retCode, retMsg, data} = res.data;
            if (retCode == '0') {
                this.stopUsers = data.stopUsers;//当前强制停机用户数
                this.forbidUsers = data.forbidUsers;//当前禁止业务半停用户数
                this.stop180Users = data.stop180Users;//近180天内强制停机次数
                this.forbid180Users = data.forbid180Users;//近180天内禁止业务半停次数
                this.drop180Users = data.drop180Users;//近180天内销户用户数
            } else {
              this.$toast(retMsg || '查询该证件下的用户入网信息及停复机信息');
                this.stopUsers = '';
                this.forbidUsers = '';
                this.stop180Users = '';
                this.forbid180Users = '';
                this.drop180Users = '';
            }
          }).catch((response) => {
            this.$toast(response || '查询该证件下的用户入网信息及停复机信息');
              this.stopUsers = '';
              this.forbidUsers = '';
              this.stop180Users = '';
              this.forbid180Users = '';
              this.drop180Users = '';
          })
        }
      },

      idchackClearData(){
        this.usernum = '';
        this.onnetNum ='';
        this.threeMonthNum = '';
        this.samedaydiffchnlIn7days = '';
        this.insideprovSubscount = '';
        this.outsideprovSubscount = '';
      },

      //点击按钮
      pseqRelieve(){
        let userName = this.sfzObj.sfzName;
        let certId = this.sfzObj.sfzId;
        let idCardType = this.sfzObj.idCardType;
        let url = `/xsb/personBusiness/preTelEnterNet/h5pseqRelieve?userName=${userName}&certId=${certId}&idCardType=${idCardType}`;
        this.$http.get(url)
          .then((res) => {
            if (res.data.retCode == '0') {
              this.$alert('预占解除成功');
              this.switch = false;
            }else{
              this.$alert('预占解除失败'+res.data.retMsg);
              this.switch = false;
            }
          })
          .catch((response) => {
          });
      },

      //获取base64
      rareVueBasCheck(){
        let paramX = {
          sfzName: this.sfzObj.sfzName,
          sfzAddr: this.sfzObj.sfzAddr,
          unLoadFlg:true
        };
        let urlx = `/xsb/gridCenter/rareVue/h5rareVueBase`;
        this.$http.post(urlx,paramX).then(res => {
          let {retCode,retMsg,data} = res.data;
          if (retCode == '0') {
            //生僻字测试
            this.sfzObjShengPi.isRareFlag ='1';
            this.sfzObjShengPi.sfzNameList =  data.sfzNameList;
            this.sfzObjShengPi.sfzAddrList =  data.sfzAddrList;

            for(let x = 0;x < this.sfzObjShengPi.sfzNameList.length ;x ++){
              let self = this
              if(this.sfzObjShengPi.sfzNameList[x].font =='1'){
                let attachId =this.sfzObjShengPi.sfzNameList[x].img;
                ImageObj.getImgUrl(attachId).then(res => {
                  self.$refs[`rareImg_${attachId}`][0].src = res
                })
              }
            }
            for(let x = 0;x < this.sfzObjShengPi.sfzAddrList.length ;x ++){
              let self = this
              if(this.sfzObjShengPi.sfzAddrList[x].font =='1'){
                let attachId =this.sfzObjShengPi.sfzAddrList[x].img;
                ImageObj.getImgUrl(attachId).then(res => {
                  self.$refs[`rareImg_${attachId}`][0].src = res
                })
              }
            }
          }
        })
      },
      getIdentifyNew(result){
        this.isReadingFlg = false;
        this.closeLoading();
        var info = result.replace(new RegExp('\n', 'g'), '\\n');
        try {
          info = JSON.parse(info);
        } catch (err) {
          try {
            info = eval("(" + info + ")");
          } catch (err2) {
            this.$alert('身份证信息读取异常，请更换设备');
            return false;
          }
        }
        var retCode = info.retCode;
        var retMsg = info.retMsg;
        this.curLanya = {id: info.deviceType, label: info.deviceName};
        if (retCode == "0") { // 获取成功
          let sfzObj = this.sfzObj;
          this.readSfzFlg = true;
          this.readSfzagainFlg = false; // 暂时不能重新读取身份证
          sfzObj.sfzId = info.readIdNo.replace(/(^\s*)|(\s*$)/g, ""); // 读取到的证件号
          sfzObj.sfzName = info.readName.replace(/(^\s*)|(\s*$)/g, ""); // 读取到的名字
          sfzObj.sfzAddr = info.readAddress.replace(/(^\s*)|(\s*$)/g, ""); // 读取到的地址
          sfzObj.usAddr = info.readAddress.replace(/(^\s*)|(\s*$)/g, ""); // 用户常住地址默认为身份证地址
          sfzObj.sfzStartDate = info.certeffdate;//身份证有效期begin
          sfzObj.sfzEndDate = info.certexpdate.replace(/\s/g,'');//身份证有效期end
          if(sfzObj.sfzEndDate == '长期'){//如果是长期处理成20991231
            sfzObj.sfzEndDate = '20991231'
          }
          sfzObj.sex = info.idCardSex.replace(/(^\s*)|(\s*$)/g, "");//读取到的性别
          sfzObj.nation = info.idCardNation.replace(/(^\s*)|(\s*$)/g, "");//读取到的民族
          sfzObj.phoneType = info.phone_type;
          sfzObj.idCardType = info.idCardType || 'IdCard';//证件类型
          sfzObj.idCardValidity = info.idCardValidity;//有效期
          sfzObj.idCardOrganization = info.idCardOrganization;//签发机关
          sfzObj.birthYear = info.idCardBirth_Year;//年
          sfzObj.birthMonth = info.idCardBirth_Month;//月
          sfzObj.birthDay = info.idCardBirth_Day;//日
          let idCardType = sfzObj.idCardType;//证件类型
          if(idCardType == 'IdCardTW' || idCardType == 'IdCardGA'){//台湾居住证 港澳居住证
            sfzObj.passNumber = info.passNumber;
            sfzObj.issuesNums = info.issuesNums;
          }
          // 判断16岁限制入网
          let idcardStr = sfzObj.sfzId;
          if (idcardStr && idcardStr.length > 14) {
            let userDate = idcardStr.substr(6, 8);
            let dateFor16 = dateFormat(new Date(new Date().getFullYear() - 16, new Date().getMonth(), new Date().getDate()), 'yyyyMMdd');
            let dateFor18 = dateFormat(new Date(new Date().getFullYear() - 18, new Date().getMonth(), new Date().getDate()), 'yyyyMMdd');
            let dateFor60 = dateFormat(new Date(new Date().getFullYear() - 60, new Date().getMonth(), new Date().getDate()), 'yyyyMMdd');

            // 16-18周岁和60岁以上用户在每日第二次办理入网业务时需要进行审批
            if ((userDate > dateFor18 && userDate <= dateFor16) || userDate < dateFor60) {
              if (this.busiType === this.CONSTVAL.BUSI_TYPE_ENTER // 选号入网
                || (this.busiType === 'true_name_record' && !this.isDianQuFlag) // 预配号
              ) {
                // 查询用户是否需要审批
                this.oneOldOneYoungVerification()
                  .then(() => {
                    console.info('审批通过，继续后续逻辑');
                    this.processAfterVerification(userDate);
                  })
                  .catch(error => {
                    console.info('审批未通过:', error.message);
                    // 终止后续逻辑，不调用processAfterVerification
                    this.readSfzagainFlg = true;
                    this.readSfzFlg = false;
                  });
              } else {
                // 不需要审批，继续执行后续逻辑
                this.processAfterVerification(userDate);
              }
            } else {
              // 年龄不在需要审批的范围内，继续执行后续逻辑
              this.processAfterVerification(userDate);
            }
          }
          //判断身份证是否失效
          let certexpdate_card = info.certexpdate.trim();//身份证失效时间
          if (certexpdate_card && !isNaN(certexpdate_card)) {
            if (certexpdate_card < this.nowDate) {
              this.readSfzFlg = false;
              this.readSfzagainFlg = true;
              this.$alert('该用户身份证已失效，身份证有效期结束时间为：' + certexpdate_card);
              return;
            }
          }
          if (this.businessFlag === "3" && this.sfzObj.sfzId !== this.newUserSfzId) {
            this.readSfzFlg = false;
            this.readSfzagainFlg = true;
            this.$alert("该客户手机号，未绑定当前身份证。");
            return;
          }
          if (this.businessFlag && this.businessFlag === '1') {
            this.qryCertHandlingBroadband();
          }
          if (this.businessFlag && this.businessFlag === '4') {
            this.checkCardId();
          }
        } else {
          this.readSfzFlg = false;
          this.clearSfzInfo();
          this.$toast(retMsg);
        }
        //该蓝牙设备去服务端校验是否在黑名单里
        this.deviceBlackByBusi();
      },
      // 查询用户是否需要一老一小审批
      oneOldOneYoungVerification() {
        return new Promise((resolve, reject) => {
          let param = {
            certId: this.sfzObj.sfzId,
            idCardType: this.sfzObj.idCardType,
            unLoadFlg: true
          }
          let url = `/xsb/gridCenter/oneOldOneYoung/h5oneOldOneYoungVerification`;
          this.$http.post(url, param).then(res => {
            let { retCode, retMsg, data } = res.data;
            if (retCode === '0') {
              resolve(true);
            } else if (retCode === '1' || retCode === '2') {
                if(retCode === '1'){
                 let cratedMsg = '为加强16-18岁、60岁以上的新入网用户入网审核，当前证件非当日首次入网，需后台审批。请确认是否创建审批单';
                 retMsg = retMsg.replace(/:.*$/, ':'+cratedMsg);
                }else{
                  let cratedMsg = '审核单不通过，请确认是否重新创建审批单';
                  retMsg = retMsg.replace(/:.*$/, ':'+cratedMsg);
                }
                this.$messagebox({
                  title: '温馨提示',
                  message: retMsg,
                  showCancelButton: true,
                  cancelButtonText: '否',
                  confirmButtonText: '是',
                  closeOnClickModal: false
                }).then(async action => {
                  if (action === 'confirm') {
                    // 用户点击确定，继续后续操作进行订单创建
                    let response = await this.createoneOldOneYoungOrder();
                    if (response) {
                      let cratedCode = response.data.retCode;
                      let createdMsg = response.data.retMsg;
                      let createdData = response.data.data;
                      if (cratedCode === '0') {
                        this.$messagebox({
                          title: '提示',
                          message: '批单创建成功，点击确定前往审批单页面',
                          showCancelButton: false,
                          confirmButtonText: '确定',
                          closeOnClickModal: false
                        }).then(action => {
                          if (action === 'confirm') {
                            this.$router.push({
                              path: '/lateFeeWaiverList',
                              query: {
                               source : 'QR'
                              }
                            });
                          }
                        });
                      } else {
                        this.$alert(createdMsg || '一老一小审批单创建失败');
                        reject(new Error(createdMsg || '一老一小审批单创建失败'));
                      }
                    }
                    resolve(true);
                  } else {
                    // 用户点击取消，终止操作
                    reject(new Error('用户取消了审批'));
                    this.paizhaoStep = 'init';
                  }
                });
              } else if (retCode === '3') {
                let cratedMsg = `审核单审核中，请确认是否查看审批单状态`;
                retMsg = retMsg.replace(/:.*$/, ':'+cratedMsg);
                this.$messagebox({
                  title: '提示',
                  message: retMsg,
                  showCancelButton: true,
                  confirmButtonText: '是',
                  cancelButtonText: '否',
                  closeOnClickModal: false
                }).then(action => {
                  if (action === 'confirm') {
                    this.$router.push({
                      path: '/lateFeeWaiverList',
                      query: {
                        source : 'QR'
                      }
                    });
                  }else {
                    this.paizhaoStep = 'init';
                  }
                });
                reject(new Error(retMsg ||"审批单审核中"));
              } else if (retCode === '4') {
                this.applyId = data;
                resolve(true);
              } else {
                this.$alert(retMsg || '当日入网校验失败');
                reject(new Error(retMsg || '当日入网校验失败'));
              }
          }).catch((response) => {
            this.$alert('当日入网校验网络异常');
            console.info('oneOldOneYoungVerification:error' + response);
            reject(new Error('日入网校验网络异常'));
          });
        });
      },
      /**
       * 创建一老一小审批单
       * @returns {Promise} - 返回一个 Promise 对象，包含 HTTP POST 请求的结果
       */
      createoneOldOneYoungOrder(){
          let url = `/xsb/gridCenter/oneOldOneYoung/h5oneOldOneYoungCreate`;
          let param = {
            certId: this.sfzObj.sfzId,
            idCardType: this.sfzObj.idCardType,
          }
          return this.$http.post(url, param)
      },
      // 将后续逻辑提取到一个单独的方法中
      processAfterVerification(userDate) {
        if (this.sixRuwangFlag) {
          let now = new Date();
          // 16岁到18岁之间的用户需要上传收入证明
          let beforeDate = dateFormat(new Date(now.getFullYear() - 18,now.getMonth(),now.getDate()),'yyyyMMdd');
          let afterDate = dateFormat(new Date(now.getFullYear() - 16,now.getMonth(),now.getDate()),'yyyyMMdd');
          if(userDate > beforeDate && userDate <= afterDate && !this.isGuardian){
            this.$messagebox({
              title: '温馨提示',
              message: '该客户为16-18周岁的用户，需要上传收入证明',
              showCancelButton: true,
              confirmButtonText: '确认',
              cancelButtonText: '取消',
            })
            this.uploadZmFlag = true;
          }else if (userDate > this.limitDate ) {
            if(!this.isGuardian) {
              this.$messagebox({
                title: '温馨提示',
                message: '该用户为16周岁以下用户，需要录入监护人信息',
                showCancelButton: true,
                confirmButtonText: '确认',
                cancelButtonText: '取消',
              }).then((action) => {
                if (action === 'confirm') {//跳信用购标准
                  this.$router.push({
                    path: '/sixRuwang',
                    query: {
                      semFrom: this.srcFrom,
                      networkType: this.networkType,//入网类型
                      telNum : this.telNumber,
                    }
                  })
                }else {
                  this.readSfzagainFlg = true;
                  this.readSfzFlg = false;
                }
              })

            }else {
              this.readSfzagainFlg = true;
              this.readSfzFlg = false;
              this.$alert('用户出生日期：' + userDate + '，16岁以下用户限制入网！');
            }
          }else {
            this.uploadZmFlag = false;
          }
        } else {
          if (userDate > this.limitDate) {
            this.readSfzagainFlg = true;
            this.readSfzFlg = false;
            this.$alert('用户出生日期：' + userDate + '，16岁以下用户限制入网！');
          }
          let now = new Date();
          // 16岁到18岁之间的用户需要上传收入证明
          let beforeDate = dateFormat(new Date(now.getFullYear() - 18, now.getMonth(), now.getDate()), 'yyyyMMdd');
          let afterDate = dateFormat(new Date(now.getFullYear() - 16, now.getMonth(), now.getDate()), 'yyyyMMdd');

          if (userDate > beforeDate && userDate <= afterDate) {
            this.uploadZmFlag = true;

            //判断是否是收入证明支持的入网类业务：选号入网，预配号，非二代证，无卡预配
            //选号入网 this.CONSTVAL.BUSI_TYPE_ENTER-预配号  true_name_record
            if (this.busiType == this.CONSTVAL.BUSI_TYPE_ENTER
              || (this.busiType == 'true_name_record' && !this.isDianQuFlag)
              || (this.busiType === 'transfer_user' && this.step === 2   //过户
                || this.busiType === 'RecoverSubs' )  //销户重开
            ) {
              // 判断开关是否开启
              if (this.zmSwitchFlag) {
                this.$alert('用户年龄为16-18周岁，需要上传收入证明');
              }
            }

          } else {
            this.uploadZmFlag = false;
          }

        }
        // 是否需要上传收入证明的标识
        Storage.session.set('uploadZmFlag', this.uploadZmFlag);
      }

    },
    mounted(){
        let uinfo = Storage.session.get('userInfo');
        let jqDataRegion="";
        //补换卡，取用户地市
        if(this.busiType =='changecard_prod'){
            jqDataRegion=Storage.session.get('jqData').userCity;
        }
        //透传的参数
        let idcardParam = {
            crmId:uinfo.crmId,
            region:uinfo.region,
            busiType:this.busiType,
            jqDataRegion:jqDataRegion,
        }
        this.idcardParam = JSON.stringify(idcardParam);
        this.nowDate = dateFormat(new Date(),'yyyyMMdd');
        let now = new Date();
        let year = now.getFullYear() - 16;
        this.limitDate = dateFormat(new Date(year,now.getMonth(),now.getDate()),'yyyyMMdd');
        // 将isNFC方法绑定到window下面，提供给外部调用
        window['isNFC'] = (result) => {
            let info = JSON.parse(result);
            if(info.retCode =='0'){
                this.nfcFlg = true;
            }
        };
        //客户端自动匹配设备并匹配后的回调函数
        window['autoPpCallBack'] = (result) => {
            let info = JSON.parse(result);
            console.info(info);
            var retCode = info.retCode;
            var retMsg = info.retMsg;
            var status = info.status;
            if(retCode == "0"){//获取成功
                if(status == "0"){//未匹配
                    this.curLanya = {id:'0',label:'未选择'};
                    this.$toast(retMsg);////提示未发现匹配的设备
                    this.isReadingFlg = false;
                }else if(status == "1"){ //匹配成功
                    var deviceType = info.deviceType;//设备类型 1:惠泉 2：亿数3：华大 4:神思
                    var deviceName = info.deviceName;//设备类型名称 1:惠泉 2：亿数3：华大 4:神思
                    this.curLanya = {id:deviceType,label:deviceName};//初始化为之前匹配过的蓝牙设备
                    this.isReadingFlg = true;
                    ClientJs.readIdCard("getIdentify","isSaveCallBack",this.tmpIsPublicSrc,this.idcardParam);
                }
            }else{
                this.$toast(retMsg);
                this.isReadingFlg = false;
            }
            this.isScanning = false;//扫描蓝牙设备结束
        };
        //拍照回调
        window['noticeCallBack'] = () => {
            console.info('====noticeCallBack=====');
            this.paizhaoStep = 'facevalidate';
        };
        //拍照后客户端回调人证比对结果
        window['mCallBackMethod'] = (result) => {
            console.info('==mCallBackMethod==')
            console.info(result);
            try {
                //把客户端返回的解析流
                let info = JSON.parse(result);
                console.info(info);
                this.currentStep = 'step2';//H5页面跳到展示身份证信息那页
                if(info.retCode == '0') {//人像比对成功
                  this.pseq = info.data.pseq;
                  this.netSrl = info.data.netSrl;
                  this.verifySimilarity = info.data.verifySimilarity;
                  this.checkSucess();
                } else {
                    this.paizhaoStep = '';
                    this.currentStep = 'step1';
                    this.getSfzImgFlg = false;
                    //金库授权弹窗
                    if (info.data && info.data.needAuth === '1') {
                        this.openVaultAuth(info);
                    } else {
                        //如果retMsg中 包含证件预占失败 字段
                      if (info.retMsg.indexOf('证件预占失败') > -1) {
                        this.$messagebox({
                            title: '温馨提示',
                            message: '证件预占失败，请确认是否需要预占解除',
                            showCancelButton: true,
                            cancelButtonText: '否',
                            confirmButtonText: '是',
                            closeOnClickModal: false
                          }
                        ).then((action) => {
                          if (action === 'confirm') {
                            this.pseqRelieve();
                            insertTraceRecorder("埋点-弹窗-预占失败-解除预占");//插入埋点
                          }
                        });
                      }else {
                        this.$alert(info.retMsg||'人像比对失败')
                      }
                    }
                }
            } catch (error) {
                this.$alert('人像比对返回报文解析失败：' + result);
            }


        };
        //读取身份证回调
        window['getIdentify'] = (result) => {
          //如果一老一小开关存在则走新的逻辑
          if(this.oldYangFlg) {
            this.getIdentifyNew(result);
          } else {
            this.isReadingFlg = false;
            this.closeLoading();
            var info = result.replace(new RegExp('\n', 'g'), '\\n');
            try {
              info = JSON.parse(info);
            } catch (err) {
              try {
                info = eval("(" + info + ")");
              } catch (err2) {
                this.$alert('身份证信息读取异常，请更换设备');
                return false;
              }
            }
            var retCode = info.retCode;
            var retMsg = info.retMsg;
            this.curLanya = {id: info.deviceType, label: info.deviceName};
            if (retCode == "0") {//获取成功
              let sfzObj = this.sfzObj;
              this.readSfzFlg = true;
              this.readSfzagainFlg = false;//暂时不能重新读取身份证
              sfzObj.sfzId = info.readIdNo.replace(/(^\s*)|(\s*$)/g, "");//读取到的证件号
              sfzObj.sfzName = info.readName.replace(/(^\s*)|(\s*$)/g, "");//读取到的名字
              sfzObj.sfzAddr = info.readAddress.replace(/(^\s*)|(\s*$)/g, "");//读取到的地址
              sfzObj.usAddr = info.readAddress.replace(/(^\s*)|(\s*$)/g, "");//用户常住地址默认为身份证地址
              sfzObj.sfzStartDate = info.certeffdate;//身份证有效期begin
              sfzObj.sfzEndDate = info.certexpdate.replace(/\s/g, '');//身份证有效期end
              if (sfzObj.sfzEndDate == '长期') {//如果是长期处理成20991231
                sfzObj.sfzEndDate = '20991231'
              }
              sfzObj.sex = info.idCardSex.replace(/(^\s*)|(\s*$)/g, "");//读取到的性别
              sfzObj.nation = info.idCardNation.replace(/(^\s*)|(\s*$)/g, "");//读取到的民族
              sfzObj.phoneType = info.phone_type;
              sfzObj.idCardType = info.idCardType || 'IdCard';//证件类型
              sfzObj.idCardValidity = info.idCardValidity;//有效期
              sfzObj.idCardOrganization = info.idCardOrganization;//签发机关
              sfzObj.birthYear = info.idCardBirth_Year;//年
              sfzObj.birthMonth = info.idCardBirth_Month;//月
              sfzObj.birthDay = info.idCardBirth_Day;//日
              let idCardType = sfzObj.idCardType;//证件类型
              if (idCardType == 'IdCardTW' || idCardType == 'IdCardGA') {//台湾居住证 港澳居住证
                sfzObj.passNumber = info.passNumber;
                sfzObj.issuesNums = info.issuesNums;
              }
              //判断16岁限制入网
              let idcardStr = sfzObj.sfzId;
              if (idcardStr && idcardStr.length > 14) {
                let userDate = idcardStr.substr(6, 8);
                if (this.sixRuwangFlag) {
                  let now = new Date();
                  // 16岁到18岁之间的用户需要上传收入证明
                  let beforeDate = dateFormat(new Date(now.getFullYear() - 18, now.getMonth(), now.getDate()), 'yyyyMMdd');
                  let afterDate = dateFormat(new Date(now.getFullYear() - 16, now.getMonth(), now.getDate()), 'yyyyMMdd');
                  if (userDate > beforeDate && userDate <= afterDate && !this.isGuardian) {
                    this.$messagebox({
                      title: '温馨提示',
                      message: '该客户为16-18周岁的用户，需要上传收入证明',
                      showCancelButton: true,
                      confirmButtonText: '确认',
                      cancelButtonText: '取消',
                    })
                    this.uploadZmFlag = true;
                  } else if (userDate > this.limitDate) {
                    if (!this.isGuardian) {
                      this.$messagebox({
                        title: '温馨提示',
                        message: '该用户为16周岁以下用户，需要录入监护人信息',
                        showCancelButton: true,
                        confirmButtonText: '确认',
                        cancelButtonText: '取消',
                      }).then((action) => {
                        if (action === 'confirm') {//跳信用购标准
                          this.$router.push({
                            path: '/sixRuwang',
                            query: {
                              semFrom: this.srcFrom,
                              networkType: this.networkType,//入网类型
                              telNum: this.telNumber,
                            }
                          })
                        } else {
                          this.readSfzagainFlg = true;
                          this.readSfzFlg = false;
                        }
                      })

                    } else {
                      this.readSfzagainFlg = true;
                      this.readSfzFlg = false;
                      this.$alert('用户出生日期：' + userDate + '，16岁以下用户限制入网！');
                    }
                  } else {
                    this.uploadZmFlag = false;
                  }
                } else {
                  if (userDate > this.limitDate) {
                    this.readSfzagainFlg = true;
                    this.readSfzFlg = false;
                    this.$alert('用户出生日期：' + userDate + '，16岁以下用户限制入网！');
                  }
                  let now = new Date();
                  // 16岁到18岁之间的用户需要上传收入证明
                  let beforeDate = dateFormat(new Date(now.getFullYear() - 18, now.getMonth(), now.getDate()), 'yyyyMMdd');
                  let afterDate = dateFormat(new Date(now.getFullYear() - 16, now.getMonth(), now.getDate()), 'yyyyMMdd');


                  if (userDate > beforeDate && userDate <= afterDate) {
                    this.uploadZmFlag = true;

                    //判断是否是收入证明支持的入网类业务：选号入网，预配号，非二代证，无卡预配
                    //选号入网 this.CONSTVAL.BUSI_TYPE_ENTER-预配号  true_name_record
                    if (this.busiType == this.CONSTVAL.BUSI_TYPE_ENTER
                      || (this.busiType == 'true_name_record' && !this.isDianQuFlag)
                      || (this.busiType === 'transfer_user' && this.step === 2   //过户
                        || this.busiType === 'RecoverSubs')  //销户重开
                    ) {
                      // 判断开关是否开启
                      if (this.zmSwitchFlag) {
                        this.$alert('用户年龄为16-18周岁，需要上传收入证明');
                      }
                    }

                  } else {
                    this.uploadZmFlag = false;
                  }

                }
                // 是否需要上传收入证明的标识
                Storage.session.set('uploadZmFlag', this.uploadZmFlag);
              }
              //判断身份证是否失效
              let certexpdate_card = info.certexpdate.trim();//身份证失效时间
              if (certexpdate_card && !isNaN(certexpdate_card)) {
                if (certexpdate_card < this.nowDate) {
                  this.readSfzFlg = false;
                  this.readSfzagainFlg = true;
                  this.$alert('该用户身份证已失效，身份证有效期结束时间为：' + certexpdate_card);
                  return;
                }
              }
              if (this.businessFlag === "3" && this.sfzObj.sfzId !== this.newUserSfzId) {
                this.readSfzFlg = false;
                this.readSfzagainFlg = true;
                this.$alert("该客户手机号，未绑定当前身份证。");
                return;
              }
              if (this.businessFlag && this.businessFlag === '1') {
                this.qryCertHandlingBroadband();
              }
              if (this.businessFlag && this.businessFlag === '4') {
                this.checkCardId();
              }
            } else {
              this.readSfzFlg = false;
              this.clearSfzInfo();
              this.$toast(retMsg);
            }
            //该蓝牙设备去服务端校验是否在黑名单里
            this.deviceBlackByBusi();
          }
        };
        //读完身份证后是否在服务端预留成功
        window['isSaveCallBack'] = (result) => {
            console.info('------isSaveCallBack------')
            let info = JSON.parse(result);
            console.info(info);
            this.saveSfzFlg = false;
            this.readSfzagainFlg = true;
          //获取base64
          if(this.busiType == this.CONSTVAL.BUSI_TYPE_ENTER || this.busiType == 'true_name_record'){
            this.rareVueBasCheck();
          }
            if (info.retCode == '0'){
                this.clientRetSfzId = info.idCardNo || this.sfzObj.sfzId;//20200508添加客户端传身份证过来
                if(this.clientRetSfzId != this.sfzObj.sfzId){
                    this.showPaiZhao = false;
                    this.$alert('此身份证号为' + this.clientRetSfzId + '，不是最近读取的身份证，请稍等');
                    return;
                }

                //全国一证多名提示
                if(info.retMsg && info.retMsg.includes("该证件存在全网一证多名，请确认客户名是否录入无误")){
                    this.$alert(info.retMsg);
                }

                this.saveSfzFlg = true;
              let userInfo =  Storage.session.get('userInfo');// 对应的地市：region =14
              let region = userInfo.region;
              //选号入网 this.CONSTVAL.BUSI_TYPE_ENTER-预配号  true_name_record
              if(this.busiType == this.CONSTVAL.BUSI_TYPE_ENTER
                || (this.busiType == 'true_name_record' && !this.isDianQuFlag)
                || this.busiType == 'CUSTOMER_Net_Num'|| this.busiType == 'order_enter_net'){
                  this.qrySubsCertIdNum();
                  this.idcheck();
              }
              //查询用户销户号码数
              if(this.busiType == 'true_name_record' || this.CONSTVAL.BUSI_TYPE_ENTER){
                 this.grpusersInfo();
              }
              console.info("busiType:"+this.busiType)
                if(this.busiType == 'true_name_record' || this.busiType == this.CONSTVAL.BUSI_TYPE_ENTER
                || this.busiType == this.CONSTVAL.BUSI_TYPE_NUMBER_TRANSFER_NET) {
                    //重点业务投诉标签
                    this.queryLabelUnify();
                }

                this.headId = info.headId;
                this.sfzAttachId = info.cardPicId;
                this.isRare = info.isRare || '0';//0 无生僻字  1 有生僻字 2 59个民族外的少数民族 3：生僻字加少数民族
                if(this.isRare != '0'){
                    this.sfzCameraImageIpdFlg = false;//需要上传照片
                    this.sfzCameraImage = '';
                } else {
                    this.sfzCameraImageIpdFlg = true;
                }
                //非预配号 没有营销案校验
                if(this.tmpIsPublicSrc){
                    this.paizhaoStep = '';
                } else {
                    if(!this.marketResultFlg){
                        this.paizhaoStep = 'validate';//等待营销案校验结果
                        this.marketTimer = setInterval(function (){
                            if(this.marketResultFlg){
                                this.paizhaoStep = '';
                                clearInterval(this.marketTimer);
                            }
                        }.bind(this),100)

                    }else{
                        this.paizhaoStep = '';
                    }
                }
            } else {
              this.idchackClearData();
              this.readSfzFlg = false;
              this.showPaiZhao = false;
              //提示信息需要不一样
              let messgae = "解除欠费限制";
              //获取info.retMsg中的第一个数字信息
              if ( info.retMsg.indexOf('欠费') >= 0 &&
                  (this.busiType == this.CONSTVAL.BUSI_TYPE_ENTER //选号入网
                  || (this.busiType == 'true_name_record' && !this.isDianQuFlag) //预配号
                  || (this.busiType == 'transfer_user' && this.step === 2)) //过户且是目标主
                 ) {
                let num = '';
                const regex = /欠费总额是(\d+\.\d{2})元/;
                const match = info.retMsg.match(regex);
                if (match && match[1]) {
                  num = parseFloat(match[1]).toFixed(2); // 保留两位小数
                }
                console.info('match:',match);
                this.$messagebox({
                  title: '温馨提示',
                  message: info.retMsg || '上传身份证信息失败',
                  showCancelButton: true,
                  cancelButtonText: '确认',
                  confirmButtonText: messgae,
                  closeOnClickModal: false
                }).then(action => {
                  if (action == 'confirm') {
                      this.$router.push({
                        path: '/createdLateFee',
                        query: {
                          semFrom: this.srcFrom,
                          sfzName : this.sfzObj.sfzName,
                          sfzId: this.sfzObj.sfzId,
                          idCardType:this.sfzObj.idCardType,
                          busiType:this.busiType,
                          num:num,
                        }
                      })
                  } else {

                  }
                });
              } else {
                this.$alert(info.retMsg || '上传身份证信息失败');
              }
            }
        },
        window['signCallBack'] = (info) => {
            if(info.retCode=='0'){
                this.signImgs = info.retImg;
                this.$refs.signDom.src = 'data:image/jpeg;base64,' + this.signImgs;
                console.info(this.checkInstallInfo());
                if(this.checkInstallInfo()){//有赠卡校验
                    this.submitStep = 'initTj';
                } else {
                    this.submitStep = '';
                }
            }
        },
        //拍完身份证照片后的回调
        window['getSfzCameraPicCb'] = (info) => {
            console.info(info);
            this.sfzCameraImage = info.fileImage;//照片的base64串
            this.sfzClientPath = info.filePath;//照片在手机客户端的路径
            this.$refs.sfzImg.src = 'data:image/jpeg;base64,' + info.fileImage;
            if(info.fileImage){
                let param = {
                    basePic:info.fileImage,
                    cardId:this.sfzObj.sfzId,
                    unEncrpt:true
                }
                //请求服务端上传
                let uploadURL = '/xsb/ability/rareWordUpLoad/h5upLoadImg';
                this.$http.post(uploadURL,param).then(res => {
                    console.info(res);
                    let {retCode,retMsg,data} = res.data;
                    if(retCode != '0'){
                        this.sfzCameraImageIpdFlg = false;
                        this.$alert(retMsg || '上传照片异常')
                    } else {
                        this.rareWordPicId = data.rareWordPicId;
                        this.sfzCameraImageIpdFlg = true;
                    }
                })
            }
        };
        //视频采集后客户端回调返回结果
        window['videoCollectCallBackMethod'] = (result) => {
            this.currentStep = 'step2';
            console.info(result)
            try {
              let info = result;
              var retCode = info.retCode;
              var retMsg = info.retMsg;
              let machineReadFinishTime='0';
              if(info.machineReadFinishTime !=undefined && info.machineReadFinishTime !=null && info.machineReadFinishTime !=""){
                 machineReadFinishTime=Math.ceil(info.machineReadFinishTime);
              }
              if(retCode == "0"){
                this.artificialFlag=false;
                this.videoCollectFlag = true;
                this.filePath = info.filePath;
                if(this.videoAginFlag){
                  this.$toast("重拍成功");
                  this.videoAginFlag = false;
                }else{
                  this.videoAginFlag = false;
                }
                this.videoStartAiFlag='0';
              }else if(retCode == "33"){
                Indicator.open('AI识别中...');
                this.videoCollectFlag = true;
                this.filePath = info.filePath;
                this.videoStartAiFlag='1';
                if(this.videoAginFlag){
                  //this.$toast("重拍成功");
                  this.videoAginFlag = false;
                }
                if(this.artificialAiParam.mod=='3') {
                  this.artificialAiParam.cut = machineReadFinishTime;
                }
                console.info(this.artificialAiParam)
                //AI识别
                let h5Param= this.artificialAiParam;
                let url = "/xsb/ability/videoRecord/h5ArtificialBegion";
                this.$http.post(url,h5Param).then(res => {
                  if (res.data.retCode == '0') {
                    this.artificialFlag=false;
                    this.$toast("AI识别成功，请继续办理业务！");
                  } else if (res.data.retCode == '-1') {
                    this.filePath="";
                    this.videoAginFlag = false;
                    this.$alert(res.data.retMsg ||  '视频采集失败，请重新拍摄');
                  } else {
                    this.artificialFlag=true;
                    this.$alert(res.data.retMsg || 'AI识别失败');
                  }
                  this.videoCollectAttachId=res.data.data;
                  if(this.videoCollectAttachId==null || this.videoCollectAttachId==undefined){
                    this.videoCollectAttachId="";
                  }
                }).catch((res) =>{
                  this.$alert('AI识别网络超时'+res);
                  Indicator.close();
                });
              }
              else{
                this.artificialFlag=false;
                this.videoAginFlag = false;
                this.$alert(retMsg ||  '视频采集失败，请重新拍摄');
              }
            } catch (error) {
                this.videoAginFlag = false;
                this.$alert('视频采集返回报文解析失败：' + result);
            }
        };

    }
}
