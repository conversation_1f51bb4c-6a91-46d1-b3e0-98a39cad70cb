import Storage from '@/base/storage'
import Authenct from 'components/common/uniteauth/index.js';
import {Indicator} from 'mint-ui'
/**
 * 业务提交类的
 */
export const tijiaoMixin = {
    data(){
        return {
            uinfo:{},//当前登录用户信息
            jqData:{},//鉴权信息
            busiType:'',//业务类型
            isMqd:'N',//是否需要免填单
            signImg:'',//签名base64串
            signId:'',//上传签名后的ID
            submitUrl:'',//业务提交的URL
            srcFrom:'',//页面来源 csView为客户视图，业务办理成功需要返回客户视图，其它情况返回业务菜单
            submitMsg:'办理',//提交后的提示信息
        }
    },
    methods: {
        //业务提交，参数为业务办理所需要的参数  stayFlg为true则停留在原来页面
        doSubmit(param,stayFlg){
            let jqData = this.jqData;
            let busiParam = {
                'telnum':jqData.telnum,//手机号
                'authType':jqData.authtype,//鉴权方式
                'location':Storage.get('location'),//位置信息
                'latitude':Storage.get('latitude'),//纬度
                'longitude':Storage.get('longitude'),//经度
                'staffId': this.uinfo.staffId,
                'stationId':this.uinfo.stationId,//岗位编码
                'clientType':/android|harmony/gi.test(navigator.userAgent)?'ANDROID':'IOS',
                "cardAuthSrl": jqData.cardAuthSrl
            }
            Object.assign(param,busiParam);
            //提交前判断有没有鉴权
            if(this.jqData.result !="1"){ //没鉴权的情况下
                Authenct({
                    popFlag:true,
                    idCardWay:false,
                    readOnlyFlag:true,
                    telnum:jqData.telnum,
                    jqTypeData:Storage.session.get('jqDateInfo_'+this.CONSTVAL.BUSI_TYPE_FMY_WEIHU),//若有值打开新鉴权框
                }, (obj) => {
                    console.info(this.busiType);
                    param.authType = obj.authtype;
                    this.jqData = obj;
                  //若非旧鉴权，则取新鉴权
                  if(param.authType){
                    if(param.authType!='06' && param.authType!='07' && param.authType!='00'){
                      param.authType= this.jqData.crmAuthType;
                    }
                  }
                    this.tijiaoPre(param,stayFlg);
                });
            }else{	//鉴权登录过的情况
              //若非旧鉴权，则取新鉴权
              if(param.authType){
                if(param.authType!='06' && param.authType!='07' && param.authType!='00'){
                  param.authType= this.jqData.crmAuthType;
                }
              }
                this.tijiaoPre(param,stayFlg);
            }
        },
        //提交前判断有没有免填单
        tijiaoPre(param,stayFlg){
            if (this.isMqd == 'Y') {//有免填单子
                this.hasMqdSubmit(param,stayFlg);
            } else {
                this.noMqdSubmit(param,stayFlg);
            }
        },
        //有免填单的提交
        hasMqdSubmit(tjParam,stayFlg){
            //上传签名
            Indicator.open('上传签名中');
            this.signId = '';
            let param = {
                signBase: this.signImg,
                unEncrpt: true
            }
            //1、上传签名
            this.$http.post('/xsb/ability/paperless/h5SignUpLoad', param).then(res => {
                return res;
            }).then(uploadRes => {
                let {retCode,retMsg,data} = uploadRes.data;
                if(retCode == '0'){
                    this.signId = data.signId;
                    tjParam.signId = this.signId;
                    this.noMqdSubmit(tjParam,stayFlg);
                  }else{
                    this.$alert(retMsg || '上传签名失败');
                  }
            })
        },
        //无免填单的提交 或者是上传过签名流后的提交
        noMqdSubmit(param,stayFlg){
            if(param.payType == '1'){//在线支付
                this.orderSubmit();
                return;
            }
            this.$http.post(this.submitUrl, param).then( res => {
                let {retCode,retMsg,data} = res.data;
                if (retCode == '0') {
                    if (this.isMqd == 'Y') {
                        let recoid = data;
                        if(typeof data === 'object'){
                            recoid = data.recId;
                        }
                        let tmp = {
                            recoid: recoid,
                            busiType: this.busiType
                        };
                        let commitResultList = [tmp];
                        //跳无纸化
                        this.$router.push('/paperLess?busiArray=' + JSON.stringify(commitResultList)+'&srcFrom=' + this.srcFrom);
                    } else {
                        this.$messagebox.alert(this.submitMsg + '成功', '温馨提示').then(action => {
                            // if(this.srcFrom == "csView"){
                            //     this.$router.push({
                            //         name:'CsViewIn',
                            //         query:{
                            //             telnum:this.telnum
                            //         }
                            //     });
                            // }else{
                            //     this.$router.push('/business');
                            // }
                            if(stayFlg){
                                this.qryProdAndMemberList();//刷新列表
                            } else {
                                this.$router.back();
                            }
                        })
                    }
                } else {
                  this.$alert(retMsg || this.submitMsg + '失败');
                }
            }).catch( res=> {
            this.$alert('网络请求失败.'+res);
            })
        },
        //查询是否需要免填单
        checkIfMqd(){
            let isPaperlessParam = {
                busiType: this.busiType + '_paperless'
            }
            //查询是否需要免填单
            this.$http.post('/xsb/ability/businessLimit/h5QryBusiPermission',isPaperlessParam).then((res)=>{
                if(res.data.retCode == '0'){
                    this.isMqd = 'Y';
                } else {
                    this.isMqd = 'N';
                }
            });
        },
        //签名回调
        qianmingBack(signImg){
            this.signImg = signImg;//签名的base64串
        },
    },
    created(){
        //鉴权信息
        this.jqData =  Storage.session.get('jqData');
        //当前登录用户信息
        this.uinfo = Storage.session.get('userInfo');
        this.srcFrom = this.$route.query.srcFrom;
    }
}
