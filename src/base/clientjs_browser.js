/**
 * 模拟客户端交互的接口
 */
const ClientJs = function(){}

function clientCbFn(cbFn,res){
	setTimeout(()=>{
		window[cbFn](res);
	},1000);
}
ClientJs.prototype.getSysInfo = (callbackfunc)=>{
	window.sessionStorage.setItem('clientVersion',JSON.stringify('3.20.51'));
	clientCbFn(callbackfunc,{serverUrl:'',
			location:'nanjin jiangsu province China',
			latitude:'143.43',
			longitude:'2843.454',
      ip:{
        'pdp_ip0/ipv4':"111.111.11"
      }
  })
};
ClientJs.prototype.readFaceIdCard = (cbFn,saveCb,tmpIsPublicSrc,idcarParam,idcardServerUrl)=>{
	let res1 = '{"retCode":"0","retMsg":"","deviceType":"100","deviceName":"森锐","readIdNo":"321324199906301023","readName":"liSi","readAddress":"nanjin jiangsu company","idCardOrganization":"nanjin","idCardBirth_Year":"2010","idCardBirth_Month":"09","idCardBirth_Day":"08","certeffdate":"20100102","certexpdate":"20250102","idCardSex":"han族","idCardNation":"男","phone_type":"","idCardType":"IdCard"}';
	clientCbFn(cbFn,res1);
	let res2 = '{"retCode":"0","retMsg":"","data":{"headId":"3023232323322"}}';
	clientCbFn(saveCb,res2);
};
ClientJs.prototype.openFaceCamera = (param,noticeCallBack,cbfun2,tmpIsPublicSrc,compareUrl)=>{
	clientCbFn(noticeCallBack,'');
	clientCbFn(cbfun2,'{"retCode":"0","retMsg":""}');
};
ClientJs.prototype.qianming = (cbFn)=>{
	let retJson = {
		retCode:'0',
		retImg:'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'
	}
	clientCbFn(cbFn,retJson);
};
//调用客户端扫描匹配过的蓝牙
ClientJs.prototype.autoReadDevise = (cbFn)=>{
	clientCbFn(cbFn,'{"status":"1","deviceType":"4","deviceName":"神思","retCode":"0","retMsg":"sucess"}');
};
//读取身份证
ClientJs.prototype.readIdCard = (cbFn,saveCb,tmpIsPublicSrc,idcardParam)=>{
	let res1 = '{"retCode":"0","retMsg":"","deviceType":"100","deviceName":"华大","certeffdate":"2015.05.05","certexpdate":"2025.05.05","idCardOrganization":"nj policy","sfzAddr":"yangzhou","readIdNo":"321324199906301023","readName":"lisi","readAddress":"id address jiangning","certeffdate":"20100102","birthYear":"2010","birthMonth":"02","birthDay":"04","certexpdate":"20260102","idCardSex":"男","idCardNation":"汉族","phone_type":"","idCardType":"IdCard"}';
	clientCbFn(cbFn,res1);
	let res2 = '{"retCode":"0","retMsg":"","headId":"30232328323322","cardPicId": "2019050810000931"}';
	clientCbFn(saveCb,res2);
};
//打开拍照
ClientJs.prototype.openCamera=(param,cbfn1,cbfun2,tmpIsPublicSrc)=>{
	clientCbFn(cbfn1,'{"retCode":"0","retMsg":"","data":{"pseq":"12323","netSrl":"2323223","verifySimilarity":"60"}}');
	clientCbFn(cbfun2,'');
};
//打开拍照
ClientJs.prototype.openCameraAndShow=(type,cbFn)=>{
    let retJson = {
        retCode:'0',
      fileName:'2389218391893.jpg',
        fileImage:'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'
    }
    clientCbFn(cbFn,retJson);
};
/*
* 搜索蓝牙设备列表
*/
ClientJs.prototype.connectDevice = (callbackfunc) => {
	clientCbFn(callbackfunc,'');
};
/*
* 自动搜索蓝牙并连接
*/
ClientJs.prototype.searchSimDevice = (callbackfunc)=>{
	clientCbFn(callbackfunc,{status: "1", deviceType: "4", deviceName: "神思", retCode: "0", retMsg: "sucess"});
};
/*
* 断开连接SIM卡设备
*/
ClientJs.prototype.disConnectSimDevice = (callbackfunc) =>{
	clientCbFn(callbackfunc,{retCode:"0",retMsg:"sucess"});
};
/*
* 获取SIM卡信息
*/
ClientJs.prototype.getSimCardInfo = (callbackfunc)=>{
	clientCbFn(callbackfunc,{ retCode: "0", retMsg: "FFFFFFFFFFFFFFFFFFFF|21323|243212|000000000000000"});
};
/*
* 写SIM卡
*/
ClientJs.prototype.writeCardData = (writeData,callbackfunc) => {
	clientCbFn(callbackfunc,{ retCode: "0", retMsg: "12312312|121321312|121321312|121321312"});
};

/*
* 打开webview页面
*/
ClientJs.prototype.openWebKit = (url,title,titleFlag
	,screenType,param,hasRightBtn,rightIcon
	,rightJs,hasRightBtn1,rightIcon1,rightJs1) => {
	var params = {};
	var paramStr;
	params.url = url;
	params.title=title;
	params.titleFlag=titleFlag;
	params.screenType=screenType;
	params.param=param;
	params.hasRightBtn=hasRightBtn;
	params.rightIcon=rightIcon;
	params.rightJs=rightJs;
	params.hasRightBtn1=hasRightBtn1;
	params.rightIcon1=rightIcon1;
	params.rightJs1=rightJs1;
	paramStr=JSON.stringify(params);
  console.log(paramStr)
	window.open(params.url);
};


/*
* 打开webview页面
*/
ClientJs.prototype.openWebKitNew= (url,params) => {
	window.open(url);
};

ClientJs.prototype.openWebKitWithStatus = (params)=>{
  console.log(params)

  window.open(params.url);
}

//获取非安卓版本的版本号
ClientJs.prototype.getIosOrBrowserAppVersion = () =>{

	let res = '{"retCode":"0","retMsg":"","verison":"2.20.72"}';
	setTimeout(()=>{
		window['versionBack'](res);
	},1000);

};
//视频采集
ClientJs.prototype.collectVideo=(h5Param,url,subtitle,cbFn)=>{
	let retJson = {"retCode":"0","retMsg":"视频采集xxx","filePath":"30232328323322","h5Param": "2019050810000931"};
    clientCbFn(cbFn,retJson);
};
//视频采集
ClientJs.prototype.collectVideoMachine=(h5Param,url,subtitle,cbFn)=>{
  let retJson = {"retCode":"0","retMsg":"视频采集xxx","filePath":"30232328323322","h5Param": "2019050810000931"};
  clientCbFn(cbFn,retJson);
};
//播放已采集的视频
ClientJs.prototype.playRecord=(filePathDomId)=>{
};
//清理视频
ClientJs.prototype.clearCashFile=(filePathDomId)=>{
};
//扫码
ClientJs.prototype.scanQRcode = (callbackfunc)=>{
	clientCbFn(callbackfunc,"1000242477|14095924");
  // clientCbFn(callbackfunc,"meetingid#12248");

};
//获取定位
ClientJs.prototype.getLocation=(h5Param,cbFn)=>{
	let retJson = {"retCode":"0","retMsg":"获取定位xxx","longitude":"11","latitude": "22","address": "33"};
    clientCbFn(cbFn,retJson);
};
//针对自动化流程，添加自动化mock的签名
let token = window.sessionStorage.getItem('tokenId');
if(JSON.parse(token) == 'QFOxpuDlWRM=' || window.location.host.indexOf('172') == 0){//固定的tokens
	if(!window.WebViewFunc){
		window.WebViewFunc = {}
	}
	window.WebViewFunc.getAppVersion = ()=>{
		return '{"retCode":"0","retMsg":"","verison":"2.20.82"}';
	}
}

//透传H5里的操作员工号和地市给客户端，用于刷脸注册场景
ClientJs.prototype.transInfoToClient = (param)=>{
	return {"retCode":"0","retMsg":"成功"};
}

//获取录音权限
ClientJs.prototype.getAudioPermiss = (cbFn)=>{
  let retJson = {"retCode":"0","isPermiss":"1"};
  clientCbFn(cbFn,retJson);
}
//录音
ClientJs.prototype.openVoiceRecord = (cbFn)=>{
  let retJson = '{"retCode":"0","duration":"1","voiceFile":"adfd"}';
  clientCbFn(cbFn,retJson);
}

ClientJs.prototype.getCopyShare = (cbFn)=>{
  let retJson = '{"retCode":"-1"}';
  clientCbFn(cbFn,retJson);
}
ClientJs.prototype.scanBarcode = (paramStr) =>{
	let paramJson = JSON.parse(paramStr);
	let scanType = paramJson.scanType;//1.	扫描设备标识2.	扫描SN码
	let callbackFunName = paramJson.callbackFunName;
	let retJson = '{"retCode":"0","scanCode":"test码"}';
	clientCbFn(callbackFunName,retJson);
}

//手机相册拍照
//isPreView 是否需要预览，1是，其他值否。预览时是，会将照片转换成base64字符串回调给页面
ClientJs.prototype.openFileSelect = (fileType,fileNameDomId, filePathDomId, fileIdDomId, callbackfunc, isPreView,isCompress,isFileDir)=>{
	if(this.isAndroid){
		window.WebViewFunc.openFileSelect(fileType,fileNameDomId,filePathDomId,fileIdDomId,callbackfunc,isPreView);
	}else{
		window.location = "clientrequest:openFileFeedBackSelect::"+callbackfunc+"::1::0";
	}
}
ClientJs.prototype.openZSJK = (jsonStr)=>{
  if(this.isAndroid){
    let jsonParam =JSON.parse(jsonStr);
    let callbackfunc
    if(jsonParam.type == '13' ){// 13打开杭研人脸
      callbackfunc = jsonParam.params;

    } else if( jsonParam.type == '6'){//6 表示打开家客sdk

      let params;
      try {
        if (typeof jsonParam.params === 'string'){
          params = JSON.parse(jsonParam.params);
        } else {
          params = jsonParam.params;
        }
        callbackfunc = params.callBack;

      } catch (error) {
        console.info('参数解析异常，'+jsonStr);
      }

    }

    if(callbackfunc){
      clientCbFn(callbackfunc,{retCode:'0'});
    }

  }
}
// 截屏能力开启
ClientJs.prototype.screenShot = (params, callbackfunc = 'screenShotCallbackfunc') => {
  clientCbFn(callbackfunc,{retCode:'0'});
}
// //注销登录
// userLoginOut: function(){
// 	if(this.isAndroid){
// 		if(window.WebViewFunc){
// 			window.WebViewFunc.userLoginOut();
// 		}
// 	}else{
// 		window.location="clientRequest:userLoginOut";
// 	}
// },

// /*
// * 打开GPS
// */
// getLocation: function(locType,callbackfunc){
// 	if(this.isAndroid){
// 		window.WebViewFunc.getLocation(locType,callbackfunc);
// 	}else{
// 		window.location="clientRequest:getLocation::"+locType+"::"+callbackfunc;
// 	}
// },

//  //下载并且打开附件页面
// openAttachment: function(url,fname){

// 	if(this.isAndroid){
// 		window.WebViewFunc.downloadAttachment(url,fname);
// 	}else{
// 		window.location="clientRequest:downloadAttachment::"+url+"::"+fname;
// 	}
// },



// //展示所有的蓝牙设备
// showLanyaDevice: function(cbFn,saveCb,tmpIsPublicSrc,idcardParam){
// 	if(this.isAndroid){//安卓设备
// 		let tmpFlg = false;
// 		if(tmpIsPublicSrc == '1'){
// 			tmpFlg = true;
// 		}
// 		if(window.upload.getIdNumPassthrough){
// 			window.upload.getIdNumPassthrough(cbFn,saveCb,tmpFlg,idcardParam);// 调用安卓客户端
// 		} else {
// 			window.upload.getIdNum("1",cbFn,saveCb,tmpFlg);// 调用安卓客户端
// 		}
// 	}else{//苹果设备
// 		window.location="clientrequest:getIdNum::" + cbFn +"::" + saveCb +"::"+tmpIsPublicSrc+"::"+idcardParam;
// 	}
// },

// /**
//  * 刷脸展示所有的蓝牙设备
//  * @param {读证回调H5的方法} cbFn
//  * @param {上传证件到服务端后回调H5的方法} saveCb
//  * @param {是否走公用（1则是非预配号）} tmpIsPublicSrc
//  * @param {透传的参数} idcarParam
//  * @param {上传的服务端路径} idcardServerUrl
//  */
// : function(cbFn,saveCb,tmpIsPublicSrc,idcarParam,idcardServerUrl){
// 	if(this.isAndroid){//安卓设备
// 		window.upload.getIdNumExtension(idcardServerUrl,idcarParam,cbFn,saveCb);// 调用安卓客户端
// 	}else{//苹果设备
// 		window.location="clientrequest:getIdNum::" + cbFn +"::" + saveCb +"::"+tmpIsPublicSrc+"::"+idcarParam+"::"+idcardServerUrl;
// 	}
// },



// //登录成功回调
// autologinInterface:function(cbFn){
// 	if (this.isAndroid) {
//         window.WebViewFunc.autologinInterface(cbFn);
//     } else {
//         window.location = "clientRequest:sessionAutoLogin::" + cbFn;
//     }
// },

// //调用发短信功能
// openMessageView:function(phoneNumber,messageContent){
//     if(this.isAndroid){
//         window.WebViewFunc.openMessageView(phoneNumber,messageContent);
//     }else{
//         window.location="clientrequest:openMessageView::"+phoneNumber+"::"+messageContent;
//     }
// },





// //打开拍照 1表示行商
// openCameraOne: function(photoCb){
// 	if(this.isAndroid){
// 		window.WebViewFunc.openCamera(photoCb,"1");
// 	}else{
// 		window.location="clientrequest:fsopOpenCamera::"+ photoCb + "::"+"1";
// 	}
// },



// //刷脸注册成功跳转到客户端的登录页面
// faceRegistFinished:function(){
// 	if(this.isAndroid){
// 		window.WebViewFunc.faceRegistFinished();
// 	}else{
// 		window.location="clientrequest:faceRegistFinished::";
// 	}
// },
// //返回客户端注册页面
// goClientLogin:function(){
// 	if(this.isAndroid){
// 		window.WebViewFunc.closeCallBack("");
// 	}else{
// 		window.location="clientRequest:sysback::";
// 	}
// },
// /*
// * 关闭webview页面
// */
// closeCallBack: function(params){
// 	if(!params){
// 		if(this.isAndroid){
// 			window.WebViewFunc.closeCallBack("");
// 		}else{
// 			window.location="clientRequest:closeCallBack::";
// 		}
// 	}else{
// 		//eval(params);
// 		if(this.isAndroid){
// 			window.WebViewFunc.closeCallBack(params);
// 		}else{
// 			window.location="clientRequest:closeCallBack::"+params;
// 		}
// 	}
// }

// }
//获取录音权限
ClientJs.prototype.savePhotoWithBase64Image = (cbFn)=>{
  let retJson = {"retCode":"0","isPermiss":"1"};
  clientCbFn(cbFn,retJson);
}
ClientJs.prototype.copyImageShareWeChat = (cbFn)=>{
  let retJson = {"retCode":"0","isPermiss":"1"};
  clientCbFn(cbFn,retJson);
}
// module.exports = new ClientJs();
let cjs = new ClientJs();
export default  cjs;
