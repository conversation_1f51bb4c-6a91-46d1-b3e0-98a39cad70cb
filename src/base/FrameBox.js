import ClientJs from './clientjs'
import {BASE64} from './coding'
import Storage from './storage'
/**
 * 
 */

let FrameBox = {

    //webkit打开新页面
    openBox:function(pageurl) {
        let vconsole = 1;
        let vconDom = document.getElementById('__vconsole');
        if(vconDom && vconDom.className=='hide') {
            vconsole = 0;
        }
        if(!vconDom) {
            vconsole = 0;
        }
        let url = window.location.href;
        url = url.substring(0, url.indexOf('?')) + `#/frameBox?srcFrom=webview&gobackFlag=webview&vconsole=${vconsole}&url=${BASE64.encode(pageurl)}`;
        console.info(url);
        ClientJs.openWebKit(encodeURIComponent(url), '', '0', '', '', '', '', '', '', '', '');
    },

    closeBox() {
        ClientJs.closeCallBack();
    },

    routePush(currRouteInfo) {
        let frameRoutePath = Storage.session.get("frameRoutePath");
        if(frameRoutePath==null || frameRoutePath=='') {
            frameRoutePath = [];
        }
        frameRoutePath.push({
            path: currRouteInfo.path,
            query: currRouteInfo.query
        });
        Storage.session.set("frameRoutePath", frameRoutePath);
    },

    routePop() {
        let frameRoutePath = Storage.session.get("frameRoutePath");
        if(frameRoutePath==null || frameRoutePath=='') {
            return '';
        } else {
            let routePath = frameRoutePath.pop()||'';
            Storage.session.set("frameRoutePath", frameRoutePath);
            return routePath;
        }

    },

}

export default FrameBox;
