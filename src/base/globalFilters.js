import {chgStrToDate} from '@/base/utils'

// 手机号脱敏
const desensitizedPhone = (phone) => {
  if (!phone) return ''
  return `${phone.slice(0, 3)}****${phone.slice(-4)}`
}

//姓名脱敏
const desensitizedName = (name) => {
  if (!name) return ''
  if (name.length < 3) {
    return name.slice(0, 1) + '*'
  } else {
    return name.slice(0, 1) + ('*').repeat(name.length - 2) + name.slice(-1)
  }
}

/**
 * @param {日期} val
 * @param {转换后的格式} fmt  如yyyy-MM-dd hh:mm:ss
 * @param {是否转换成今天 昨天}zhFlg
 */
const dateShow = (val, fmt, zhFlg) => {
  val = chgStrToDate(val, true)
  if (isNaN(val) && !isNaN(Date.parse(val))) {
    return val
  }

  let now = new Date()
  let nowDay = now.getDate()
  let nowMonth = now.getMonth()
  let nowYear = now.getFullYear()
  let isTian = false
  //参数中的日期是今天  明天  昨天
  if (nowYear == val.getFullYear() && nowMonth == val.getMonth() && Math.abs(nowDay - val.getDate()) <= 1) {
    isTian = true
  }
  let o = {
    'M+': val.getMonth() + 1, //月份
    'd+': val.getDate(), //日
    'h+': val.getHours(), //小时
    'm+': val.getMinutes(), //分
    's+': val.getSeconds(), //秒
    'q+': Math.floor((val.getMonth() + 3) / 3), //季度
    'S': val.getMilliseconds() //毫秒
  }

  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (val.getFullYear() + '').substr(4 - RegExp.$1.length))
  }
  for (let k in o) {
    if (new RegExp('(' + k + ')').test(fmt)) {
      if (isTian && zhFlg) {//日
        if (k == 'M+') {
          fmt = fmt.replace(RegExp.$1 + '/', '')
        } else if (k == 'd+') {
          if (o[k] === nowDay) {
            fmt = fmt.replace(RegExp.$1, '今日')
          } else if (o[k] === nowDay - 1) {
            fmt = fmt.replace(RegExp.$1, '昨日')
          } else if (o[k] === nowDay + 1) {
            fmt = fmt.replace(RegExp.$1, '明日')
          }
        } else {
          fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length)))
        }
      } else {
        fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length)))
      }
    }
  }
  return fmt
}

export default { desensitizedPhone, desensitizedName, dateShow }
