import ClientJs from './clientjs.js'

// 回调参数位置配置（方法名: 回调参数索引）



// 客户端方法集
const METHOD_CONFIG = {
	getSysInfo: {//方法名
	  exposed: true,
	  callbackIndex: 0//回调参数索引
	},
	getLocation: {
	  exposed: true,
	  callbackIndex: 1
	},
	openWebKit: {
	  exposed: true
	},
	openFileSelect: {
	  exposed: true,
	  callbackIndex: 4
	},
	openCamera: {
	  exposed: true,
	  callbackIndex: 3
	},
	submitFormWithMultiFiles: {
	  exposed: true,
	  callbackIndex: 7
	},
	closeCallBack: {
	  exposed: true
	},
	scanQRcode: {
	  exposed: true,
	  callbackIndex: 0
	},
	openCallPhoneView: {
	  exposed: true
	},
	openAttachment: {
	  exposed: true
	},
	openChatSpeech: {
	  exposed: true,
	  callbackIndex: 0
	},
	downloadAttachment: {
	  exposed: true
	},
	scanBarcode: {
	  exposed: true
	},
	downloadSavePhoto:{
		exposed:true
	},
	openVoiceRecord:{
		exposed:true,
		callbackIndex:0
	},
	closeWebKitWithCallBack:{
		exposed:true,
		callbackIndex:0
	},
	openBrowser:{
		exposed:true
	},
	getAppVersion:{
		exposed:true
	},
	openZSJK:{
		exposed:true
	},
	exportSupport:{
		exposed:true
	},
	scanCode:{
		exposed:true,
		callbackIndex:1
	},
	openAlbumAndCamera:{
		exposed:true,
		callbackIndex:1
	},
	openLongVoiceRecord:{
		exposed:true,
		callbackIndex:0
	}
  };

/**
 * 和客户端交互的接口
 */

class FrameClient{
	constructor(options={}){
		this._config = options
		this._iframeWindow = options.iframeWindow;//frame当前的window对象
		this._opId = options.opId;//外围渠道OPID
		this._replaceDomTxt = options.replaceDomTxt;//要替换的页面文本
		this._frameSrc = options.frameSrc;//当前frame的src
		this.isAndroid = /android/gi.test(navigator.appVersion);
	}
	//创建frameClient对象
	static createInstance(options){
		const _frameClient = new FrameClient(options);
		let pWin = _frameClient._iframeWindow.parent;
		/*上传百度统计*/
		pWin && pWin._hmt && pWin._hmt.push(['_trackPageview', `/${_frameClient._opId}`]);
		const frmWindow = options.iframeWindow;
		frmWindow.FrameViewFunc = {};

		// 客户端方法注册到frame对应window的FrameViewFunc对象
		Object.entries(METHOD_CONFIG).forEach(([methodName, config]) => {
			if (config.exposed) {
			frmWindow.FrameViewFunc[methodName] = _frameClient._wrapMethod(
				methodName,
				_frameClient[methodName].bind(_frameClient),
				config.callbackIndex // 传递配置参数
			);
			}
		});
		
		if(_frameClient._replaceDomTxt){//有需要替换的脚本
			_frameClient._doReplaceDom();
		}
		return _frameClient;
	}

	// 高阶函数：统一处理日志和回调
	// _wrapMethod(methodName, method) {
	_wrapMethod(methodName, method, callbackIndex) {
		return (...args) => {
		  // 1. 统一日志记录
		  this.trackLog(methodName);
	
		  // 2. 统一回调处理
		  if (callbackIndex !== undefined) {
			const callbackfunc = args[callbackIndex];
			if (typeof callbackfunc === 'string') {
			  this._registerCallback(callbackfunc);
			}
		  }
	
		  // 3. 执行原始方法
		  return method(...args);
		}
	  }

	trackLog(abilityName){
		//上报日志
		console.info('---track---',abilityName);	
		let opId = this._opId;
		/*上传百度统计*/
		_hmt && _hmt.push(['_trackEvent', 'frameClient', abilityName+'_'+opId]);
		//上传公用日志模块

	}
	//注册客户端回调方法
	_registerCallback(callbackfunc){
		window[callbackfunc] = (resData) => {
			console.info('callbackfunc from frameClient===',callbackfunc)
		  	const targetFunc = this._iframeWindow[callbackfunc];
			if (typeof targetFunc === 'function') {
				targetFunc(resData);
			} else {
				console.error(`Function ${callbackfunc} not found.`);
			}

		}
	}
	//替换外围页面上的dom内容
	_doReplaceDom(){
		let needPlaceTxt = this._replaceDomTxt;
		let newTxt = '我是网格通';
		const iframeDocument = this._iframeWindow.document;
        const allTextNodes = [];
        const walk = document.createTreeWalker(iframeDocument, NodeFilter.SHOW_TEXT);
        let node;
        while (node = walk.nextNode()) {
            allTextNodes.push(node);
        }
        allTextNodes.forEach((textNode) => {
			console.info('===start to replace dom===')
            if (textNode.textContent.trim() === needPlaceTxt) {
                const parent = textNode.parentNode;
                const newTextNode = document.createTextNode(newTxt);
                parent.replaceChild(newTextNode, textNode);
            }
			console.info('===end to replace dom===')
        });
	}

	///***************************************以下均为客户端原生方法，注册到frameView对象******************************************************************//
	//获取路由、手机设备信息
	getSysInfo(callbackfunc){
		ClientJs.getSysInfo(callbackfunc);
	}
	
	
	//打开GPS
	getLocation(locType,callbackfunc){
		ClientJs.getLocation(locType,callbackfunc);
	}
	/*
	* 打开webview页面
	*/
	openWebKit (url,title,titleFlag
		,screenType,param,hasRightBtn,rightIcon
		,rightJs,hasRightBtn1,rightIcon1,rightJs1,hasListen){
		ClientJs.openWebKit(url,title,titleFlag
			,screenType,param,hasRightBtn,rightIcon
			,rightJs,hasRightBtn1,rightIcon1,rightJs1,hasListen)
	}

	// 	//手机相册+ 拍照
	//isPreView 是否需要预览，1是，其他值否。预览时是，会将照片转换成base64字符串回调给页面
	openFileSelect(fileType,fileNameDomId, filePathDomId, fileIdDomId, callbackfunc, isPreView,isCompress,isFileDir){
		const args = Array.from(arguments);
		console.info('-----------------');
		console.info(args);

		// ClientJs.openCameraAndShow(isPreView,callbackfunc);
		//fileType: image_file表示图片， video_file表示视频
		if(!fileType){//默认打开图片
			fileType = 'image_file';
		}
		if(this.isAndroid){
			window.WebViewFunc.openFileSelect(fileType,fileNameDomId,filePathDomId,fileIdDomId,callbackfunc,isPreView,isCompress,isFileDir);
		}else{
			window.location = "clientrequest:openFileFeedBackSelect::"+callbackfunc+"::1::0";
		}
	}

	// 	//打开拍照 
	openCamera(fileIdDomId,fileNameDomId, filePathDomId,callbackfunc, isPreView,PhotoFrame,savePhoto){
		if(this.isAndroid){
			window.WebViewFunc.openCamera(fileIdDomId,fileNameDomId, filePathDomId,callbackfunc, isPreView,PhotoFrame,savePhoto)
		} else {
			window.location = "clientRequest: openCamera::" + fileIdDomId+"::" + fileNameDomId +"::" +filePathDomId +"::" +callbackfunc + "::" + isPreView +"::"+PhotoFrame+"::"+savePhoto; 
		}
	}

	// 	//图片上传 fileSavePath 文件路径数组，多个文件时以，进行分割,不需要可传空字符串
	submitFormWithMultiFiles(formid,filePaths,fileIds,fileSavePath,uploadURL,uploadParams,uploadHeader,callbackfunc){
		ClientJs.submitFormWithMultiFiles(filePaths,uploadURL,uploadParams,callbackfunc)
		
	}
	/*
	* 关闭webview页面
	*/
	closeCallBack(params){
		ClientJs.closeCallBack(params);
	}
	//客户端扫码
	scanQRcode(callbackfunc) {
		ClientJs.scanQRcode(callbackfunc);
		
	}
	//扫描二维码 jsonParam.scanType: 1.	扫描设备标识2.	扫描SN码
	scanBarcode(jsonStr) {
		console.info(jsonStr);
		let jsonParam = {};
		try{
			jsonParam = JSON.parse(jsonStr);
		} catch(e){
			alert('参数不合法:'+jsonStr)
		}

		if(jsonParam && jsonParam.callbackFunName && jsonParam.scanType){
			let callbackfunc = jsonParam.callbackFunName;
			this._registerCallback(callbackfunc);
			ClientJs.scanBarcode(jsonStr);
		} else {
			alert('参数不合法:'+jsonStr)
		}
	}
	/*
     * 调用打电话功能
     */
    openCallPhoneView(phoneNumber){
		ClientJs.openCallPhoneView(phoneNumber);
	}

	//下载并且打开附件页面
	openAttachment(url,fname){
		ClientJs.openAttachment(url,fname);
	}

	//调用语音转文字原生组件
  openChatSpeech(callbackfunc){
	ClientJs.openChatSpeech(callbackfunc);
  }
  
  //下载并打开pdf、word、excel、png等附件。
  downloadAttachment(fileUrl, fileName){
	ClientJs.openAttachment(fileUrl,fileName);
  }

 //获取网格通APP版本号 
  getAppVersion(callbackfunc){
	if (/android/gi.test(navigator.appVersion)) {
        return window.WebViewFunc.getAppVersion()
    } else {
		this._registerCallback(callbackfunc);
        window.location = 'clientrequest:getAppVersion::'+ callbackfunc;
	}
  }
  //先下载图片再保存到手机相册
  downloadSavePhoto(fileUrl){
	ClientJs.downloadSavePhoto(fileUrl);
  }
  //调用客户端相册或者拍照、录制视频功能并返回结果
  //selectType 1 拍照 2 录制视频 3 相,callbackfunc获取能力文件成功后回调h5 页面js方法
  openAlbumAndCamera(selectType,callbackfunc){
	ClientJs.openCameraVideo(selectType,callbackfunc)
  }
  //调用客户端语音录制功能并返回结果
  openVoiceRecord(callbackfunc){
	ClientJs.openVoiceRecord(callbackfunc);
  }

  //关闭web页面并传递参数
  closeWebKitWithCallBack(callbackfunc,param){
	ClientJs.closeWebKitWithCallBack(callbackfunc,param)
  }

  //打开浏览器
  openBrowser(urlParam){
	ClientJs.openBrowser(urlParam);
  }
  //拉起掌上家客
  openZSJK(jsonStr){
	console.info(jsonStr);
	if (!/android/gi.test(navigator.appVersion)) {// 仅支持安卓拉起掌上家客的功能
        return {}
    }
	let jsonParam = {};
	try{
		jsonParam = JSON.parse(jsonStr);
	} catch(e){
		alert('参数不合法:'+jsonStr)
	}
	if(jsonParam.type == '13' ){// 13打开杭研人脸 
		let callbackfunc = jsonParam.params;
		this._registerCallback(callbackfunc);//注册回调
	} else if( jsonParam.type == '6'){//6 表示打开家客sdk
		// {
		// 	"type": "6",
		// 	"params": "{\"action\": \"来自家客的值\", \"callBack\": \"来自家客callback\"}"
		//   }
		let params;
		try {
			if (typeof jsonParam.params === 'string'){
				params = JSON.parse(jsonParam.params);
			} else {
				params = jsonParam.params;
			}
			let callbackfunc = params.callBack;
			this._registerCallback(callbackfunc);//注册回调
		} catch (error) {
			console.info('参数解析异常，'+jsonStr);
		}
		
	}
	window.WebViewFunc.openZSJK(jsonStr)
  }
 //集客APP_专家坐席 
  exportSupport(jsonStr){
	if (!/android/gi.test(navigator.appVersion)) {// 仅支持安卓拉起掌上家客的功能
        return {}
    }
	window.WebViewFunc.exportSupport(jsonStr)
  }

  //掌上家客
  scanCode(type,callBack){
	if (!/android/gi.test(navigator.appVersion)) {// 仅支持安卓拉起掌上家客的功能
        return {}
    }
	window.WebViewFunc.scanCode(type,callBack)
  }

    //提供给掌上家客的录音能力
	openLongVoiceRecord(callbackfunc){
		window.WebViewFunc.openLongVoiceRecord(callbackfunc)
	}
}

export default FrameClient;
