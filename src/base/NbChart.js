// 引入 ECharts 主模块
let echarts = require('echarts/lib/echarts');

// 引入地图
require('echarts/lib/chart/map');
//引入热力图
require("echarts/lib/chart/heatmap");
require("echarts/lib/component/visualMap");

// 引入柱状图
require('echarts/lib/chart/bar');

//引入折线图
require("echarts/lib/chart/line");

// 引入提示框
require('echarts/lib/component/tooltip');

// 引入标题
require('echarts/lib/component/title');

// 引入图例
require("echarts/lib/component/legend");

// 江苏省业务办理量热力图
export function createJsBusiHeatMap(domId, busiData) {
    // 获取江苏省GeoJson
    let geoJson = require('../data/jiangsu.json');
    echarts.registerMap('jiangsu', geoJson);
    let domObj = document.getElementById(domId);
    // 基于准备好的dom，初始化echarts实例
    let myChart = echarts.init(domObj);

    myChart.setOption({
        tooltip: {
            trigger: 'item',
            formatter(params) {
                let regionData = busiData.busiRegionMap[params.name];
                let tipDom = `<div style="font-size: 16px;font-weight: 600;color: #353535;margin-bottom: 10px">${params.name}</div>` +
                    `<div style="font-size: 14px;color: #363636;margin-bottom: 2px"><span style="display: inline-block; width:100px">总业务量</span><span>${regionData.totalNum}笔</span></div>`;
                regionData.busiTypeNameNumInfoList.forEach((item, i) => {
                    let domTemp = `<div style="font-size: 14px;color: #363636;margin-bottom: 2px"><span style="display: inline-block; width:100px">${item.busiTypeName}</span><span>${item.busiNum}笔</span></div>`;
                    tipDom = tipDom + domTemp;
                });
                return tipDom;
            },
            extraCssText: "padding: 10px; width: 144px;height: auto;background: #FFFFFF;box-shadow: 4px 4px 4px 0 rgba(99, 99, 99, 0.16);border-radius: 8px;border: 1px solid #F4CC8E"
        },
        visualMap: {
            min: busiData.minBusiNum,
            max: busiData.maxBusiNum == 0 ? 10 : busiData.maxBusiNum,
            text: ['高', '低'],
            realtime: false,
            calculable: true,
            inRange: {
                color: ['lightskyblue', 'yellow', 'orangered']
            }
        },
        series: [
            {
                name: '分公司战略分布地图',
                type: 'map',
                mapType: 'jiangsu', // 自定义扩展图表类型
                label: {
                    show: true,
                    textStyle: {
                        fontSize: '10' //字体大小
                    }
                },
                data: busiData.busiRegionNameTotalList,
                itemStyle: {
                    emphasis: {
                        areaColor: null,//设置为空可使颜色不变
                    }
                }
            }
        ]
    });

    /*myChart.on('click', function (params) {
    });*/
}

// 江苏省业务办理量热力图柱状图
export function createJsBusiBar(domId, busiData) {
    let domObj = document.getElementById(domId);
    // 基于准备好的dom，初始化echarts实例
    let myChart = echarts.init(domObj);

    myChart.setOption({
        color: ['#3398DB'],
        tooltip: {
            trigger: 'axis',
            axisPointer: { // 坐标轴指示器，坐标轴触发有效
                type: 'line' // 默认为直线，可选为：'line' | 'shadow'
            }
        },
        grid: {
            left: '1%',
            right: '6%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: [
            {
                type: 'value'
            }
        ],
        yAxis: [
            {
                type: 'category',
                data: ['苏州', '淮安', '宿迁', '南京', '连云港', '徐州', '常州', '镇江', '无锡', '南通', '泰州', '盐城', '扬州'],
                axisTick: {
                    alignWithLabel: true
                }
            }

        ],
        series: [
            {
                name: '业务总量',
                type: 'bar',
                barWidth: '60%',
                data: busiData
            }
        ]
    });
}

//江苏省TOP10校园人流量柱状图
export function createJsTrafficBar(domId,option){
    let domObj = document.getElementById(domId);
    // 基于准备好的dom，初始化echarts实例 
    let myChart = echarts.init(domObj);
    // 绘制图表
    myChart.setOption({
        color: ['#3398DB'],
        tooltip: {
            trigger: 'axis',
            axisPointer: { // 坐标轴指示器，坐标轴触发有效
                type: 'line' // 默认为直线，可选为：'line' | 'shadow'
            }
        },
        grid: {
            top: '-2%',
            left: '2%',
            right: '5%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'value',
            axisTick:{show:false},
            axisLine:{
                show:false
            },
            axisLabel:{
                fontSize:'10px',
                color:'#303030'
            },
        },
        yAxis: {
            type: 'category',
            data: option.yAxisData,
            inverse: true,//倒序
            axisTick:{show:false},
            axisLine:{
                show:false
            },
            axisLabel:{
                fontSize:'10px',
                color:'#303030'
            },
        },
        series: [
            {
                type: 'bar',
                label: {
                    show: false,
                    position: 'right',
                    color:'#303030',
                    fontSize:'10px'
                },
                barWidth:5,
                itemStyle:{
                    color:'#007AFE'
                },
                data: option.seriesData
            }
        ]
    })
    window.onresize = () => {
        myChart.resize();
    }
}
// 江苏省业务办理量 图柱状图  横向
export function createJsBusiBarTrans(domId, params) {
    let domObj = document.getElementById(domId);
    // 基于准备好的dom，初始化echarts实例
    let myChart = echarts.init(domObj);

    myChart.setOption({
        color: ['#007AFE'],
        tooltip: {
            trigger: 'axis',
            axisPointer: { // 坐标轴指示器，坐标轴触发有效
                type: 'none' // 默认为直线，可选为：'line' | 'shadow'
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: [
            {
                type: 'value',
                minInterval: 1,
                axisLabel: {
                    interval:0,
                    rotate:35//角度顺时针计算的
                },
                axisLine:{
                    show:false
                },
                axisTick:{
                    show:false
                } ,//网格样式
                splitLine: {
                    show: true,
                    lineStyle:{
                        color: ['#F1F1F1'],
                        width: 1,
                        type: 'solid'
                    }
                }
            }
        ],
        yAxis: [
            {
                type: 'category',
                interval:0,
                data: ['南京大学', '东南大学', '河海大学', '南京理工大学', '南京工业大学', '南京航天航空大学', '苏州大学', '扬州大学', '江苏大学', '徐州工程学院'],
                axisTick:{
                    alignWithLabel: true,
                    show:false
                } ,
                axisLine:{
                    show:false
                },
                axisLabel:{
        /*          fontSize:12*/
                },
            }

        ],
        series: [
            {
                name: '业务总量',
                type: 'bar',
                barWidth: '35%',
                data: [10000, 5200, 20000, 33400, 39000, 33000, 22000, 20000, 40000, 60000 ]
            }
        ]
    });
}
// 江苏省业务办理量 图柱状图  纵向
export function createJsBusiBarPort(domId, params) {
    let domObj = document.getElementById(domId);
    // 基于准备好的dom，初始化echarts实例
    let myChart = echarts.init(domObj);

    myChart.setOption({
        color: ['#84C760'],
        tooltip: {
            trigger: 'axis',
            axisPointer: { // 坐标轴指示器，坐标轴触发有效
            type: 'none' ,// 默认为直线，可选为：'line' | 'shadow'
  /*              lineStyle:{
                    type: 'dashed'
                }*/
            }
        },

        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        yAxis: [
            {
                type: 'value',
                minInterval: 1,
                axisLine:{
                    show:false
                },
                axisTick:{
                    show:false
                } ,//网格样式
                splitLine: {
                    show: true,
                    lineStyle:{
                        color: ['#F1F1F1'],
                        width: 1,
                        type: 'solid'
                    }
                }
            }
        ],
        xAxis: [
            {
                type: 'category',
                data: ['苏州', '淮安', '宿迁', '南京', '连云港', '徐州', '常州', '镇江', '无锡', '南通', '泰州', '盐城', '扬州'],
                axisTick: {
                    alignWithLabel: true,
                    show:false
                },
                axisLabel: {
                    interval:0,
                    rotate:50//角度顺时针计算的
                },
                axisLine:{
                    show:false
                }

            }

        ],
        series: [
            {
                name: '业务总量',
                type: 'bar',
                barWidth: '35%',
                data: params
            }
        ]
    });
}

// 主机观测折线图
export function createHostMonitorLine(domId, hostData, dataType) {
    let domObj = document.getElementById(domId);
    // 基于准备好的dom，初始化echarts实例
    let myChart = echarts.init(domObj);

    myChart.setOption({
        title: {
            text: dataType == 0 ? 'CPU' : 'MEM'
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                label: {
                    backgroundColor: '#6a7985'
                }
            }
        },
        //color: ['#5D7092', '#5AD8A6', '#5B8FF9'],
        legend: {
            itemWidth: 13,  // 设置宽度
            itemHeight: 8, // 设置高度
            itemGap: 20, // 设置间距
            icon: 'rect',  // 矩形
            textStyle: { //图例文字的样式
                color: '#8C8C8C',
                fontSize: 12,
            },
            data: hostData.hostNameList
        },
        grid: {
            left: '0',
            right: '5%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: [
            {
                type: 'category',
                boundaryGap: false,
                axisLabel: { //x轴文字的配置
                    textStyle: {
                        color: "#9B9B9B"
                    }
                },
                data: hostData.timeList
            }
        ],
        yAxis: [
            {
                type: 'value',
                axisLabel: {//y轴文字的配置
                    textStyle: {
                        color: "#9B9B9B"
                    },
                    formatter: '{value}%'//y轴的每一个刻度值后面加上‘%’号
                },
                axisTick: { // 刻度
                    show: false
                },
                axisLine: {//y轴线的颜色以及宽度
                    show: false
                },
            }
        ],
        series: dataType == 0 ? hostData.cpuSeries : hostData.memSeries
    });
}
