import {iEncrptParamN,decrptParamN} from '@/base/AesEncrptUtil.js'

/**
 * 本地存储实现,封装localStorage和sessionStorage
 // localStorage
 storage.set(key,val)
 storage.get(key, def)

 // sessionStorage
 storage.session.set(key, val)
 storage.session.get(key, val)
 */
let store = {
  /* eslint-disable no-undef */
  version: '1.1.0',
  storage: window.localStorage,
  session: {
    storage: window.sessionStorage
  }
}

const api = {
  set(key, val) {
    if (this.disabled) {
      return
    }
    if (val === undefined) {
      return this.remove(key)
    }
    if(key == 'userInfo' || key == 'jqData'){
      if(key == 'userInfo' &&  val !== '' && val !== null && val.crmId == null){
        val.crmId = '';
      }
      this.storage.setItem(key, serializeEncrpt(val));
    } else {
      this.storage.setItem(key, serialize(val))
    }
    return val
  },

  get(key, def='') {
    if (this.disabled) {
      return def
    }
    let val = '';
    if(key == 'userInfo'|| key == 'jqData'){
      val = deserializeDecrpt(this.storage.getItem(key))
    }else {
      val = deserialize(this.storage.getItem(key))
    }
    return (val === undefined ? def : val)
  },

  has(key) {
    return this.get(key) !== undefined
  },

  remove(key) {
    if (this.disabled) {
      return
    }
    this.storage.removeItem(key)
  },

  clear() {
    if (this.disabled) {
      return
    }
    this.storage.clear()
  },

  getAll() {
    if (this.disabled) {
      return null
    }
    let ret = {}
    this.forEach((key, val) => {
      ret[key] = val
    })
    return ret
  },

  forEach(callback) {
    if (this.disabled) {
      return
    }
    for (let i = 0; i < this.storage.length; i++) {
      let key = this.storage.key(i)
      callback(key, this.get(key))
    }
  }
}

Object.assign(store, api)

Object.assign(store.session, api)

function serialize(val) {
  return JSON.stringify(val)
}

function deserialize(val) {
  if (typeof val !== 'string') {
    return undefined
  }
  try {
    return JSON.parse(val)
  } catch (e) {
    return val || undefined
  }
}

function serializeEncrpt(val) {  
  return iEncrptParamN(JSON.stringify(val),'Xqwjiu89#0uy8Io3')
}
function deserializeDecrpt(val) {
  if (typeof val !== 'string') {
    return undefined
  }
  try {
    return JSON.parse(decrptParamN(val,'Xqwjiu89#0uy8Io3'))
  } catch (e) {
    return val || undefined
  }
}

try {
  const testKey = '__storejs__'
  store.set(testKey, testKey)
  if (store.get(testKey) !== testKey) {
    store.disabled = true
  }
  store.remove(testKey)
} catch (e) {
  store.disabled = true
}

export default store
