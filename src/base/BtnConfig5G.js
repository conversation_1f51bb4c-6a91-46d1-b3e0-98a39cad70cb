/**
 * 对5G预受理的按钮配置：
 * 不同业务类型的不同状态对应不同的按钮描述和调用方法。
 */
//状态备注：-1：发送失败；0：已发送；1：待确认；2：已取消；3：已确认；4：办理成功（正常业务）/用户已签字（远程补录）；
//5：办理失败；6：远程办理归档；7：现场办理归档
const BTN_CONFIG_COMMON = [{
  "status": "-1",
  "btnTxt": "重新发送",
  "btnMethod": "resend5gMessage"
}, {
  "status": "0",
  "btnTxt": "刷新",
  "btnMethod": "qrySendState"
}, {
  "status": "1",
  "btnTxt": "再次提醒",
  "btnMethod": "resend5gMessage"
}, {
  "status": "2",
  "btnTxt": "再次下单",
  "btnMethod": "submitOrder"
}, {
  "status": "3",
  "btnTxt": "提交",
  "btnMethod": "submitOrder"
}, {
  "status": "5",
  "btnTxt": "再次下单",
  "btnMethod": "submitOrder"
}];

const BTN_CONFIG_BUSI = {
  "supp": [{
      "status": "4",
      "btnTxt": "生成电子单据",
      "btnMethod": "goDetailDynamic"
    },
    {
      "status": "6",
      "btnTxt": "查看电子单据",
      "btnMethod": "goDetailDynamic"
    }
  ],
  "promotion_center": [{
      "status": "1",
      "btnTxt": "刷新",
      "btnMethod": "qrySendState"
    }, {
      "status": "3",
      "btnTxt": "提交",
      "btnMethod": "goDetailDynamic"
    },
    {
      "status": "5",
      "btnTxt": "办理失败",
      "btnMethod": "goDetailDynamic"
    },
    {
      "status": "6",
      "btnTxt": "查看电子单据",
      "btnMethod": "goDetailDynamic"
    },
  ],
  "zhuti_prod": [
    {
      "status": "0",
      "btnTxt": "刷新",
      "btnMethod": "qrySendState"
    },{
      "status": "1",
      "btnTxt": "刷新",
      "btnMethod": "qrySendState"
    },
    {
      "status": "3",
      "btnTxt": "提交",
      "btnMethod": "goDetailDynamic"
    },
    {
      "status": "5",
      "btnTxt": "办理失败",
      "btnMethod": "goDetailDynamic"
    },
    {
      "status": "6",
      "btnTxt": "查看电子单据",
      "btnMethod": "goDetailDynamic"
    }
  ],
  "zengzhi_prod": [
    {
      "status": "0",
      "btnTxt": "刷新",
      "btnMethod": "qrySendState"
    },{
      "status": "1",
      "btnTxt": "刷新",
      "btnMethod": "qrySendState"
    },
    {
      "status": "3",
      "btnTxt": "提交",
      "btnMethod": "goDetailDynamic"
    },
    {
      "status": "5",
      "btnTxt": "办理失败",
      "btnMethod": "goDetailDynamic"
    },
    {
      "status": "6",
      "btnTxt": "查看电子单据",
      "btnMethod": "goDetailDynamic"
    }
  ],
  "mband_prod_kaitong": [
    {
      "status": "0",
      "btnTxt": "刷新",
      "btnMethod": "qrySendState"
    },
    {
      "status": "1",
      "btnTxt": "刷新",
      "btnMethod": "qrySendState"
    },
    {
      "status": "3",
      "btnTxt": "提交",
      "btnMethod": "goDetailDynamic"
    },
    {
      "status": "5",
      "btnTxt": "办理失败",
      "btnMethod": "goDetailDynamic"
    }
    ,{
      "status": "6",
      "btnTxt": "查看电子单据",
      "btnMethod": "goDetailDynamic"
    },
    {
      "status": "20",
      "btnTxt": "刷新",
      "btnMethod": "qrySendState"
    },
    {
      "status": "21",
      "btnTxt": "刷新",
      "btnMethod": "qrySendState"
    },
    {
      "status": "22",
      "btnTxt": "详情",
      "btnMethod": "goDetailDynamic"
    }
  ],
  "throwMonad": [
    {
      "status": "0",
      "btnTxt": "刷新",
      "btnMethod": "qrySendState"
    },
    {
      "status": "1",
      "btnTxt": "刷新",
      "btnMethod": "qrySendState"
    },
    {
      "status": "3",
      "btnTxt": "提交",
      "btnMethod": "goDetailDynamic"
    },
    ],
  "nettv": [
    {
      "status": "0",
      "btnTxt": "刷新",
      "btnMethod": "qrySendState"
    },
    {
      "status": "1",
      "btnTxt": "刷新",
      "btnMethod": "qrySendState"
    },
    {
      "status": "3",
      "btnTxt": "提交",
      "btnMethod": "goDetailDynamic"
    },
    {
      "status": "5",
      "btnTxt": "办理失败",
      "btnMethod": "goDetailDynamic"
    }
  ],
  "network_service": [
    {
      "status": "0",
      "btnTxt": "刷新",
      "btnMethod": "qrySendState"
    },
    {
      "status": "1",
      "btnTxt": "刷新",
      "btnMethod": "qrySendState"
    },
    {
      "status": "3",
      "btnTxt": "提交",
      "btnMethod": "goDetailDynamic"
    },
    {
      "status": "5",
      "btnTxt": "办理失败",
      "btnMethod": "goDetailDynamic"
    },
    {
      "status": "6",
      "btnTxt": "查看电子单据",
      "btnMethod": "goDetailDynamic"
    }
  ],
  "security_service": [
    {
      "status": "0",
      "btnTxt": "刷新",
      "btnMethod": "qrySendState"
    },
    {
      "status": "1",
      "btnTxt": "刷新",
      "btnMethod": "qrySendState"
    },
    {
      "status": "3",
      "btnTxt": "提交",
      "btnMethod": "goDetailDynamic"
    },
    {
      "status": "5",
      "btnTxt": "办理失败",
      "btnMethod": "goDetailDynamic"
    },
    {
      "status": "6",
      "btnTxt": "查看电子单据",
      "btnMethod": "goDetailDynamic"
    }
  ],
  "shopping_cart": [
    {
      "status": "0",
      "btnTxt": "刷新",
      "btnMethod": "qrySendState"
    },
    {
      "status": "1",
      "btnTxt": "刷新",
      "btnMethod": "qrySendState"
    },
    {
      "status": "3",
      "btnTxt": "提交",
      "btnMethod": "goDetailDynamic"
    },
    {
      "status": "5",
      "btnTxt": "办理失败",
      "btnMethod": "goDetailDynamic"
    },
    {
      "status": "6",
      "btnTxt": "查看电子单据",
      "btnMethod": "goDetailDynamic"
    }
  ],
  "perfect_order": [
    {
      "status": "0",
      "btnTxt": "刷新",
      "btnMethod": "qrySendState"
    },
    {
      "status": "1",
      "btnTxt": "刷新",
      "btnMethod": "qrySendState"
    },
    {
      "status": "3",
      "btnTxt": "提交",
      "btnMethod": "goDetailDynamic"
    },
    {
      "status": "5",
      "btnTxt": "办理失败",
      "btnMethod": "goDetailDynamic"
    },
    {
      "status": "6",
      "btnTxt": "查看电子单据",
      "btnMethod": "goDetailDynamic"
    }
  ],
  "perfect_order_zengzhi": [
    {
      "status": "0",
      "btnTxt": "刷新",
      "btnMethod": "qrySendState"
    },
    {
      "status": "1",
      "btnTxt": "刷新",
      "btnMethod": "qrySendState"
    },
    {
      "status": "3",
      "btnTxt": "提交",
      "btnMethod": "goDetailDynamic"
    },
    {
      "status": "5",
      "btnTxt": "办理失败",
      "btnMethod": "goDetailDynamic"
    },
    {
      "status": "6",
      "btnTxt": "查看电子单据",
      "btnMethod": "goDetailDynamic"
    }
  ]
}
const BTN_CONFIG_BUSI_DETAIL = {
  "supp": [{
    "status": "4",
    "btnTxt": "生成电子单据",
    "btnMethod": "composePdf"
  }, {
    "status": "6",
    "btnTxt": "查看电子单据",
    "btnMethod": "showPdfImg"
  }],
  "promotion_center": [{
      "status": "1",
      "btnTxt": "刷新",
      "btnMethod": "qrySendState"
    },
    {
      "status": "3",
      "btnTxt": "提交",
      "btnMethod": "compatibleSubmit"
    },
    {
      "status": "5",
      "btnTxt": "办理失败",
      "btnMethod": "azFail"
    },
    {
      "status": "6",
      "btnTxt": "查看电子单据",
      "btnMethod": "azCheckBill"
    },
  ],
  "zhuti_prod": [{
    "status": "0",
    "btnTxt": "刷新",
    "btnMethod": "qrySendState"
  },{
      "status": "1",
      "btnTxt": "刷新",
      "btnMethod": "qrySendState"
    },
    {
      "status": "3",
      "btnTxt": "提交",
      "btnMethod": "shoppingCartSubmit"
    },
    {
      "status": "5",
      "btnTxt": "办理失败",
      "btnMethod": "azFail"
    },
    {
      "status": "6",
      "btnTxt": "查看电子单据",
      "btnMethod": "azCheckBill"
    },
  ],
  "zengzhi_prod": [{
    "status": "0",
    "btnTxt": "刷新",
    "btnMethod": "qrySendState"
  },{
      "status": "1",
      "btnTxt": "刷新",
      "btnMethod": "qrySendState"
    },
    {
      "status": "3",
      "btnTxt": "提交",
      "btnMethod": "compatibleSubmit"
    },
    {
      "status": "5",
      "btnTxt": "再次下单",
      "btnMethod": "azFail"
    },
    {
      "status": "6",
      "btnTxt": "查看电子单据",
      "btnMethod": "azCheckBill"
    },
  ],
  "mband_prod_kaitong":[
    {
      "status": "0",
      "btnTxt": "刷新",
      "btnMethod": "qrySendState"
    },
    {
      "status": "1",
      "btnTxt": "刷新",
      "btnMethod": "qrySendState"
    },
    {
      "status": "3",
      "btnTxt": "提交",
      "btnMethod": "shoppingCartSubmit"
    },
    {
      "status": "5",
      "btnTxt": "办理失败",
      "btnMethod": "azFail"
    },
    {
      "status": "6",
      "btnTxt": "查看电子单据",
      "btnMethod": "azCheckBill"
    },
  ],
  "throwMonad": [ {
    "status": "0",
    "btnTxt": "刷新",
    "btnMethod": "qrySendState"
  },
    {
      "status": "1",
      "btnTxt": "刷新",
      "btnMethod": "qrySendState"
    },
    {
      "status": "3",
      "btnTxt": "提交",
      "btnMethod": "throwMonadSubmitOrder"
    },
  ],
  "nettv":[
    {
      "status": "0",
      "btnTxt": "刷新",
      "btnMethod": "qrySendState"
    },
    {
      "status": "1",
      "btnTxt": "刷新",
      "btnMethod": "qrySendState"
    },
    {
      "status": "3",
      "btnTxt": "提交",
      "btnMethod": "shoppingCartSubmit"
    },
    {
      "status": "5",
      "btnTxt": "办理失败",
      "btnMethod": "azFail"
    },
    {
      "status": "6",
      "btnTxt": "查看电子单据",
      "btnMethod": "azCheckBill"
    },
  ],
  "security_service":[
    {
      "status": "0",
      "btnTxt": "刷新",
      "btnMethod": "qrySendState"
    },
    {
      "status": "1",
      "btnTxt": "刷新",
      "btnMethod": "qrySendState"
    },
    {
      "status": "3",
      "btnTxt": "提交",
      "btnMethod": "cuxiaoSubmitOrder"
    },
    {
      "status": "5",
      "btnTxt": "办理失败",
      "btnMethod": "azFail"
    },
    {
      "status": "6",
      "btnTxt": "查看电子单据",
      "btnMethod": "azCheckBill"
    },
  ],
  "shopping_cart":[
    {
      "status": "0",
      "btnTxt": "刷新",
      "btnMethod": "qrySendState"
    },
    {
      "status": "1",
      "btnTxt": "刷新",
      "btnMethod": "qrySendState"
    },
    {
      "status": "3",
      "btnTxt": "提交",
      "btnMethod": "shoppingCartSubmit"
    },
    {
      "status": "5",
      "btnTxt": "办理失败",
      "btnMethod": "azFail"
    },
    {
      "status": "6",
      "btnTxt": "查看电子单据",
      "btnMethod": "azCheckBill"
    },
  ],
  "perfect_order":[
    {
      "status": "0",
      "btnTxt": "刷新",
      "btnMethod": "qrySendState"
    },
    {
      "status": "1",
      "btnTxt": "刷新",
      "btnMethod": "qrySendState"
    },
    {
      "status": "3",
      "btnTxt": "提交",
      "btnMethod": "cuxiaoSubmitOrder"
    },
    {
      "status": "5",
      "btnTxt": "办理失败",
      "btnMethod": "azFail"
    },
    {
      "status": "6",
      "btnTxt": "查看电子单据",
      "btnMethod": "azCheckBill"
    },
  ],
  "perfect_order_zengzhi":[
    {
      "status": "0",
      "btnTxt": "刷新",
      "btnMethod": "qrySendState"
    },
    {
      "status": "1",
      "btnTxt": "刷新",
      "btnMethod": "qrySendState"
    },
    {
      "status": "3",
      "btnTxt": "提交",
      "btnMethod": "cuxiaoSubmitOrder"
    },
    {
      "status": "5",
      "btnTxt": "办理失败",
      "btnMethod": "azFail"
    },
    {
      "status": "6",
      "btnTxt": "查看电子单据",
      "btnMethod": "azCheckBill"
    },
  ],
}

export {
  BTN_CONFIG_COMMON,
  BTN_CONFIG_BUSI,
  BTN_CONFIG_BUSI_DETAIL
}
