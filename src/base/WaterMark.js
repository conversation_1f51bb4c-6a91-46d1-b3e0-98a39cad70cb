//waterMark.js
let addWaterMark = (strList,id) => {

    if (document.getElementById(id) !== null) {
        document.body.removeChild(document.getElementById(id))
    }
    //创建一个画布
    let can = document.createElement('canvas')
    // 设置canvas画布大小
    can.width = 240
    can.height = 40 + 20 * strList.length;
    let cans = can.getContext('2d')
    cans.rotate(-10 * Math.PI / 180) // 水印旋转角度
    cans.font = '15px Vedana'
    cans.fillStyle = '#666666' //设置填充绘画的颜色、渐变或者模式
    cans.textAlign = 'center' //设置文本内容的当前对齐方式
    cans.textBaseline = 'Middle' //设置在绘制文本时使用的当前文本基线

    for (let i = 0; i < strList.length; i++){
      cans.fillText(strList[i], 100, can.height - 10 - 20 * i);
    }

    // cans.fillText(str, can.width / 2, can.height -50)//根据需求可添加多行水印，在方法中添加str2..
    // cans.fillText(str2, can.width / 2, can.height -30) // 在画布上绘制填色的文本（输出的文本，开始绘制文本的X坐标位置，开始绘制文本的Y坐标位置）

     let div = document.createElement('div')
    div.id = id;
    div.style.pointerEvents = 'none'
    div.style.top = '0px'
    div.style.left = '0px'
    div.style.opacity = '0.15'
    div.style.position = 'fixed'
    div.style.zIndex = '100000'
    div.style.width = document.documentElement.clientWidth + 'px'
    div.style.height = document.documentElement.clientHeight + 'px'
    div.style.background = 'url(' + can.toDataURL('image/png') + ') left top repeat'
    document.body.appendChild(div)
    return id
}

// 添加水印
export function setWaterMark(strList,domId) {
     domId = domId? domId:'1.23452384164.123412415';

     if(Array.isArray(strList)&& strList.length>0){
       let id = addWaterMark(strList,domId)
       if (document.getElementById(id) === null) {
         id = addWaterMark(strList,domId)
       }
     }
}

// 移除水印
export function clearWatermark(id = '1.23452384164.123412415') {
    if (document.getElementById(id) !== null) {
        document.body.removeChild(document.getElementById(id))
    }
}
