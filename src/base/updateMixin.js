import { getRealUrl ,dateFormat} from "@/base/utils";
import Storage from '@/base/storage'
import { getClientType } from './utils'
// 更新相关混合
export const updateMixin = {
  data() {
    return {
      downUrl: '',
      downLoadUrl: '',
      iosObj: {},
      androidObj: {},
      ios2Obj: {},
      android2Obj: {},
      version: "", //版本信息
      latestVersion: "",
    }
  },
  mounted() {
    window['versionBackTip'] = (res) =>{
      let obj = eval('(' + res + ')');
      this.version = obj.verison;
      Storage.set('clientVersion', obj.verison);
      //获取服务器版本
      this.axiosVersion();
    }
  },
  methods:{
    /**
     * 检测更新
     * @param updateFlag 更新标识 1：无论是不是4开头的，都更新至新框架
     */
    latestCheckDownload(updateFlag) {
      //获取客户端当前版本
      if(['ANDROID','harmony'].includes(getClientType())){
        var res = window.WebViewFunc.getAppVersion();
        var obj = eval('(' + res + ')');
        this.version = obj.verison;
        //获取服务器版本
        this.axiosVersion(updateFlag);
      }else{
        window.location="clientrequest:getAppVersion::versionBackTip";
      }

      //版本比较，若不是最新版本直接下载最新版
    },
    /**
     * 查询最新版本
     * @param updateFlag 更新标识 1：无论是不是4开头的，都更新至新框架
     */
    axiosVersion(updateFlag) {
      this.$http.get('/xsb/api-user/appVersionCenter/h5appVersionInfo').then(res => {
        let resData = res.data;
        if(typeof(resData) != "object"){//不是对象的话就是加密串
          resData = decrptParam(resData);
          resData = JSON.parse(resData);
        }
        let {retCode, data} = resData;
        if (retCode === "0") {
          if(data.ios){
            this.iosObj = data.ios;
          }
          if (data.android) {
            this.androidObj = data.android;
          }
          this.ios2Obj = data.ios2;
          this.android2Obj = data.android2;
          let clientType = 'ios';
          if(/android|harmony/gi.test(navigator.userAgent)){
            clientType = 'android';
          }
          // 如果是4开头的，表示是新框架，则取ios2、android2   弹窗提示更新，全部更新至最新版本（不管是不是4开头的，都更新至新框架）
          if((this.version && this.version.startsWith('4')) || (updateFlag && updateFlag === '1')) {
            clientType += '2';
          }
          this.cmpVersion(clientType);
        } else {
          this.$alert(resData.retMsg || '获取版本失败')
        }
      }).catch(err => {
        this.$alert("获取版本异常，error：" + err);
      })
    },
    //比较当前版本和服务器版本
    cmpVersion(cType) {
      let url = window.location.href;
      if(~url.indexOf(getRealUrl('hip1'))){
        //this.androidObj.url = this.androidObj.url.replace(getRealUrl('ip2'), getRealUrl('ip1'));
        //this.iosObj.url = this.iosObj.url.replace('.plist','_221.plist');
        if(this.android2Obj && this.android2Obj.url){
          this.android2Obj.url = this.android2Obj.url.replace(getRealUrl('ip2'), getRealUrl('ip1'));
        }
        if(this.ios2Obj && this.ios2Obj.url) {
          this.ios2Obj.url = this.ios2Obj.url.replace('.plist','_221.plist');
        }
      }
      this.downUrl = `${url.substring(0,url.indexOf('/index.html'))}/other.html`;
      // this.downLoadUrl = this.androidObj.url;
      // this.latestVersion = this.androidObj.versionNameReal || this.androidObj.versionName;
      // this.latestVersionDesc = this.androidObj.desc
      /*if(cType === 'ios'){
        this.latestVersion = this.iosObj.versionNameReal || this.iosObj.versionName;
        this.downLoadUrl = this.iosObj.url;
        this.latestVersionDesc = this.iosObj.desc
      }*/
      if(cType === 'ios2'){
        this.latestVersion = this.ios2Obj.versionNameReal || this.ios2Obj.versionName;
        this.downLoadUrl = this.ios2Obj.url;
        this.latestVersionDesc = this.ios2Obj.desc
      }
      if(cType === 'android2'){
        this.latestVersion = this.android2Obj.versionNameReal || this.android2Obj.versionName;
        this.downLoadUrl = this.android2Obj.url;
        this.latestVersionDesc = this.android2Obj.desc
      }
      console.info("version: ", this.version);
      console.info("latestVersion: ", this.latestVersion);
      console.info("cType: ", cType);
      // 回调是否升级函数
      this.isUpdateCallBack(this.version !== this.latestVersion);
    }
  }
}
