import JiaMi from "@/base/JiaMiUtil.js";

// const jiaMi = new JiaMi({ initKey: getEncKey("abcdefghijklmnop"),desKey: getJmKey("abcdefghijklmnop") });
const jiaMi = new JiaMi({ initKey: 'K/O3uF90QFdHp8SQ',desKey:'K/O3uF90QFdHp8SQ',oldKey:'K/O3uF90QFdHp8SQ'});


const iEncrpt = jiaMi.iEncrpt;
const iEncrptParam = jiaMi.iEncrptParam;
const iEncrptParamMap = jiaMi.iEncrptParamMap;
const decrptParam = jiaMi.decrptParam;
const digitSign = jiaMi.digitSign;
const digitAldSign = jiaMi.digitAldSign;
const aesEncryptAllParams = jiaMi.aesEncryptAllParams;
const iEncrptParamN = jiaMi.iEncrptParamN;
const decrptParamN = jiaMi.decrptParamN;


export {iEncrpt,iEncrptParam,iEncrptParamMap,decrptParam,digitSign,digitAldSign,aesEncryptAllParams,iEncrptParamN,decrptParamN}


