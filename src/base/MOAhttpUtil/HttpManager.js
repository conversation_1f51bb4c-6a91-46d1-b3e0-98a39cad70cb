import axios from 'axios'

// 阿拉盯方法加解密
import {aesEncryptAllParams} from './oldAes.js'
// 企业微信方法解密方法
import AesEncrptUtil from './AesEncrptUtil.js'
import { Indicator } from 'mint-ui'
import Storage from '@/base/storage.js'
import MessageBox from 'components/common/NlMessageBox/message-box.js';


// post请求封装方法
const requestPost = async (url, params, blob) => {
    return requestMethod(url, params, 'POST',blob)
}

// get请求封装方法
const requestGet = (url, params, blob) => {
    return requestMethod(url, params, 'GET',blob)
}


// 请求方法----阿拉盯方法加密,企业微信方法解密
const requestMethod = async (url, params, method,blob) => {
    if (method == 'GET') {
        if  (url.indexOf('unEncrpt') < 0) {
            url = aesEncryptAllParams(url)
        }

    } else {
        if  (params) {
            if (!params.unEncrpt) {
                // 正常阿拉盯方法加密-param
                params = aesEncryptAllParams(params)
            }
        }

    }
    if (process.env.NODE_ENV === 'development') {
        //开发联调环境需要配置跨域标识
        url = '/apiM/' + url
    } else {
        url = '/JsMoa/' + url
    }
    let urlNew = dealRequestUrl(Storage.get('webUrl'), url)
    Indicator.open('加载中...')
    let obj = {
        method: method,
        url: urlNew,
        data: params,
        headers: dealHeader({}, method)
    }
    if (blob) {
        obj.responseType = 'blob'
    }
    let responseAxios = await axios(obj)


    if (blob) {
        console.log('responseAxios：===>',responseAxios)

        let dataBlob = responseAxios.data
        console.log('blob请求返回：===>',dataBlob)
        console.log('blob请求返回：===>',dataBlob)
        console.log('blob请求返回：===>',dataBlob)
        console.log('blob请求返回：===>',dataBlob)
        console.log('blob请求返回：===>',dataBlob)
        return dataBlob
    }



    // response需要解析成加密串
    let data = await responseAxios.data



    // 企业微信方法解密
    let resDecryptStr = AesEncrptUtil.aesDecrypt(data)
    // 返回体组装
    responseAxios.data = JSON.parse(resDecryptStr)

    if (responseAxios.data.retCode == '9999') {
        Indicator.close()
        MessageBox.alert('会话失效，请重新登录').then(action => {})
        return responseAxios
    }
    Indicator.close()
    return responseAxios
}


//添加固定请求头
function dealHeader(header, method) {
    header['content-type'] = 'application/json'
    header['tokenId'] = Storage.session.get('tokenNew')
    header['permission'] = '1'
    header['newTokenCheck'] = '1'
    header['c03be90046a7e7f'] = 'GwRT4HjrxC9Davw'
    header['operaterPhone'] = Storage.session.get('operaterPhoneNew')
    header['imei'] = Storage.session.get('imeiNew')
    header['appId'] = '4001' // MOA专用标识
    return header
}

const getRandomStr = (range=100000) => {
    let rangeNum = parseInt(range,16);
    return (((1+Math.random())*rangeNum)|0).toString(16).substring(1);
}
//地址后面添加随机数
const dealRequestUrl = function(host, url) {
    return host + url + (~url.indexOf('?') ? '&' : '?') + 'rdom=' + getRandomStr()
}



export { requestPost, requestGet }
