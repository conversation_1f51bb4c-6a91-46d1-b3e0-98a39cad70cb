import CryptoJS from 'crypto-js'

const initKey = 'K/O3uF90QFdHp8SQ';

// 设置数据块长度
const keySize = 128;
const option = {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7,
    iv: ''
}

/**
 * 定义加密函数
 * @param {string} data - 需要加密的数据, 传过来前先进行 JSON.stringify(data);
 * @param {string} key - 加密使用的 key
 */
const aesEncrypt = (data) => {
    const key = CryptoJS.enc.Utf8.parse(initKey);
    /**
     * CipherOption, 加密的一些选项:
     *   mode: 加密模式, 可取值(CBC, CFB, CTR, CTRGladman, OFB, ECB), 都在 CryptoJS.mode 对象下
     *   padding: 填充方式, 可取值(Pkcs7, AnsiX923, Iso10126, Iso97971, ZeroPadding, NoPadding), 都在 CryptoJS.pad 对象下
     *   iv: 偏移量, mode === ECB 时, 不需要 iv
     * 返回的是一个加密对象
     */
    const cipher = CryptoJS.AES.encrypt(data, key, option);
    // 将加密后的数据转换成 Base64
    const base64Cipher = cipher.ciphertext.toString(CryptoJS.enc.Base64);
    // const resultCipher = base64Cipher.replace(/\+/g,'-').replace(/\//g,'_');
    // 返回加密后的经过处理的 Base64
    return base64Cipher;
}

/**
 * 定义解密函数
 * @param {string} encrypted - 加密的数据;
 * @param {string} key - 加密使用的 key
 */
const aesDecrypt = (encrypted) => {
    const key = CryptoJS.enc.Utf8.parse(initKey);

    // // 先将 Base64 还原一下, 因为加密的时候做了一些字符的替换
    // const restoreBase64 = encrypted.replace(/\-/g,'+').replace(/_/g,'/');
    // 这里 mode, padding, iv 一定要跟加密的时候完全一样
    // 返回的是一个解密后的对象
    // console.log('CryptoJS.enc.Base64.stringify(encrypted)****==>' + CryptoJS.enc.Base64.stringify(encrypted))

    const decipher = CryptoJS.AES.decrypt(encrypted, key, {
        iv: '',
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7
    });



    // 将解密对象转换成 UTF8 的字符串
    const resultDecipher = CryptoJS.enc.Utf8.stringify(decipher);
    console.log('aesDecrypt：resultDecipher==>' + resultDecipher)

    // 返回解密结果
    return resultDecipher;
}

var parseUrl = function (url, key) {
    var idx = url.indexOf('?');
    if (~url.indexOf('nextStep')) {
        idx = url.indexOf('&');
    }
    var newUrl;
    if (idx == -1) {
        newUrl = url;
    } else {
        newUrl = url.substr(0, idx + 1);
        var param = url.substr(idx + 1).split('&');
        for (var i = 0; i < param.length; i++) {
            param[i] = dealParam(param[i], key);
        }
        newUrl += param.join('&');

        var isAndroid = 'ios';//android or ios
        if ((/android/gi).test(navigator.appVersion)) {
            isAndroid = 'android';
        }
        //newUrl += '&isAndroid='+dealParam(isAndroid);
    }
    //console.info(newUrl);
    return newUrl;
}
const dealParam = function (val, key = initKey) {
    var idx = val.indexOf('=');
    var retVal = val.substr(0, idx + 1);
    var pVal = val.substr(idx + 1);
    if (pVal == undefined || pVal == 'undefined') {
        pVal = '';
    } else {
        pVal = aesEncrypt(pVal, key);
    }
    return retVal + pVal;
}

const iEncrpt = function (oldUrl, key = initKey) {
    return parseUrl(oldUrl, key);
};

var iEncrptParam = function (val, key = initKey) {
    if (val == undefined || val == 'undefined') {
        return '';
    } else {
        return aesEncrypt(val + '', key);
    }
}
//解密
const decrptParam = function (val, key = initKey) {
    return aesDecrypt(val + '', key);
}
const iEncrptParamMap = function (map, aesKey = initKey) {
    let value, retData = {};
    for (let key in map) {
        value = iEncrptParam(map[key], aesKey);
        retData[key] = value;
    }
    return retData;
}
//对map中的key排序
const keyCompare = function (map) {
    let retStr = '';
    let keyArr = [];
    for (let key in map) {
        keyArr.push(key);
    }
    keyArr.sort();//对key进行升序
    for (let i = 0; i < keyArr.length; i++) {
        let key = keyArr[i];
        if (map[key] == undefined || map[key] == 'undefined') {
            retStr += key;
        } else {
            retStr += key + map[key];
        }
    }
    return retStr;
}
const digitGetSign = function (url) {
    var idx = url.indexOf('?');
    var retMap = {};
    if (idx == -1) {
        return retMap;
    } else {
        var param = url.substr(idx + 1).split('&');
        for (var i = 0; i < param.length; i++) {
            let idxInner = param[i].indexOf('=');
            let retVal = param[i].substr(0, idxInner);
            let pVal = param[i].substr(idxInner + 1);
            retMap[retVal] = pVal;
        }
    }
    return keyCompare(retMap);
}
const digitPostSign = function (map) {
    return keyCompare(map);
}
const digitSign = function (salt5G, param, method, contentType) {
    let secrest = CryptoJS.SHA256('pancnl123').toString();
    //5Gchatbot的盐：********************************
    if (salt5G) {
        secrest = CryptoJS.SHA256(salt5G).toString();
    }
    secrest = secrest.toUpperCase();
    let reqParam = '';
    if (method == 'get') {
        reqParam = digitGetSign(param);//get方式的url进行签名
        // console.info(reqParam);
    } else {
        if (contentType && ~contentType.indexOf('multipart/form-data')) {
            reqParam = digitPostSign({});
        } else {
            reqParam = digitPostSign(param);
        }
    }
    // console.info(reqParam +'***,***'+ secrest);
    let retSign = CryptoJS.SHA256(reqParam + secrest).toString();
    // console.info(retSign);
    return retSign && retSign.toUpperCase();
}

const digitAldSign = function(resData){

    let retSign = CryptoJS.SHA256(resData).toString();
    return retSign && retSign.toUpperCase();
}

export default {
    aesDecrypt,
    aesEncrypt,
    digitAldSign

}


