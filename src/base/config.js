/**
 * 配置日志上报开关类
 * 功能编号（和服务端尽量保持一致）
 */
const LOG_CONFIG = {
    //预配号
    'YuPeiHao':false,
    //客户视图
    'CsView':false,
    //集客详情
    'PlanDetail':false,
    //工作台
    'WorkStage':false
};
const LONG_CITY_LIST = [
    {id:'1000250',shortId:'14',label:'南京'},
    {id:'1000510',shortId:'19',label:'无锡'},
    {id:'1000511',shortId:'18',label:'镇江'},
    {id:'1000512',shortId:'11',label:'苏州'},
    {id:'1000513',shortId:'20',label:'南通'},
    {id:'1000514',shortId:'23',label:'扬州'},
    {id:'1000515',shortId:'22',label:'盐城'},
    {id:'1000516',shortId:'16',label:'徐州'},
    {id:'1000517',shortId:'12',label:'淮安'},
    {id:'1000518',shortId:'15',label:'连云港'},
    {id:'1000519',shortId:'17',label:'常州'},
    {id:'1000523',shortId:'21',label:'泰州'},
    {id:'1000527',shortId:'13',label:'宿迁'}
]
const CONSTVAL = {
    'STATION_JKSX':'9988022838662806',//集客随销
    'STATION_ZWSX':'9988022838662806',//装维随销
    'STATION_SCHOOL':'9988021245683406',//校园营销
    'STATION_KHJL':'9988020036385338',//客户经理
    'STATION_SHEQU':'9988021754066058',//社区营销 、专业直销人员
    'STATION_JIATING':'9988019488290574',//家庭营销
    'STATION_QUDAO':'1488018962830350',//渠道经理
    'STATION_ZHICHENG':'1811480',//支撑中心管理员
    'STATION_ZHUT':'1811500',//主厅店长
    'STATION_HUWAI':'9988021979165266',//营业员户外营销
    'STATION_SUDI':'9988021979185150',//速递等第三方兼职直销人员
    'STATION_10086':'9988022513355102',//【市】内部10086执勤岗(市公司)
    'STATION_HEZUO':'1811486',//"合作厅店长"
    'STATION_DSQUDAO':'1811489',//地市渠道管理员"
    'STATION_DSSYS':'1811493',//地市系统管理员
    'STATION_BLD':'9988020224955046',//便利店业主
    'STATION_GB':'9988020036390970',//政企室经理
    'STATION_AGB':'9988020036389950',//政企客户经理
    'STATION_ZWSXN':'10009102',//装维随销（新）
    'STATION_ZYZXRYN':'10009101',//专业直销人员（新）
    'STATION_JTYXN':'10009002',//家庭营销（新)
    'STATION_YYYHWYXN':'10009001',//营业员户外营销（新）
    'STATION_JIAKEZW': '10012002',//家客装维
    'STATION_SKZQ_KHJL':'10011806',// 商客专区（商客经理）
    'STATION_SKZQ_GLY':'10012007',// 商客专区（商客管理员）
    'STATION_LYDL':'10013401',//楼园代理
    'STATION_YYDL':'10013602',//异业代理
    'STATION_SKGLY1':'10013501',//商客管理员（省）
    'STATION_SKGLY2':'10013402',//商客管理员（市）
    'STATION_SKGLY3':'10013302',//商客管理员（区县）
    'STATION_FWGZ':'10013601',//副网格长
    'STATION_GZT_KHJL':'10011406',//工作台-客户经理
    'STATION_MSC':'10010403',//品专店合伙人
    'TV_PAKETID':"2013000004",//互联网百视通产品包编码
    'BS_PAKETID':'2013000009',//省内央广银河产品包编码
    'YS_PAKETID':'2013000008',//省内央视产品包编码
    'TV_PAKETID_CP':'2013000010', //互联网百视通产品包编码-CP
    'BS_PAKETID_CP':'2013000012',//省内央广银河产品包编码-CP
    'YS_PAKETID_CP':'2013000011',//省内央视产品包编码-CP
    'BUSI_TYPE_RONGHE':'rong_market',//融合受理业务类型
    'BUSI_TYPE_ENTER':'enter_net',//选号入网业务类型
    'GROUP_TYPE_SWITCH':'group_type_add_switch',//集团建档集团类型传中文或新增locationTypeNew英文上线开关
    'BUSI_TYPE_ENTER_FUSION':'enter_net_fusion',  //新入网融合业务类型
    'BUSI_TYPE_IOT':'iot_enter_net',//物联网卡业务：iot_enter_net
    'BUSI_TYPE_MBAND':'mband_prod_kaitong',//宽带开通
    'BUSI_TYPE_MARKET':'TERMINA_SELL',//个人营销案
    'BUSI_TYPE_ZDMARKET':'TERMINAL_MARKET',//终端营销案
    "BUSI_TYPE_TV_NETTV":'nettv_prod_kaitong',//百事通电视开通
    "BUSI_TYPE_TV_NETTV_SNYGYH":'nettv_snygyh_prod_kaitong',//央广银河电视开通
    "BUSI_TYPE_TV_NETTV__SNYS":'nettv_snys_prod_kaitong',//省内央视电视开通
    "BUSI_TYPE_TV_NETTV_TWO":'nettv_prod_kaitong_two',//百事通电视开通-第二台
    "BUSI_TYPE_TV_NETTV_SNYGYH_TWO":'nettv_snygyh_prod_kaitong_two',//央广银河电视开通-第二台
    "BUSI_TYPE_TV_NETTV__SNYS_TWO":'nettv_snys_prod_kaitong_two',//省内央视电视开通-第二台
    "BUSI_TYPE_NETTV_PAPERLESS":'netTv_paperless',//互联网电视是否无纸化
    "BUSI_TYPE_NETTV_SECOND_PAPERLESS":'netTv_second_paperless',//第二台互联网电视是否无纸化
    "BUSI_TYPE_MARKET_PAPERLESS":'market_paperless',//个人营销案是否无纸化
    "BUSI_TYPE_ZHENGZHI_PAPERLESS":'zhengzhi_paperless', //产品订购
    "BUSI_TYPE_ZHENGZHI_PROD":'zengzhi_prod', //产品订购业务类型
    "BUSI_TYPE_HANDLEORDER_SHUANDAN":'handleorder_shuandan', //远程签名甩单
    "BUSI_TYPE_THROWMONAD":'throwMonad',//订单中心甩单
    "BUSI_TYPE_MBAND_PROD_CHANGE_PAPERLESS":"mband_prod_change_paperless", //宽带变更
    "BUSI_TYPE_MBAND_PROD_CHANGE":"mband_prod_change",//宽带变更类型
    "FLAG_SIMCARD":"simcard", //跳转到自己的写卡页面
    "BUSI_TYPE_FMY_KAITONG":"FAMILY_PROD_ORDER",//家庭网开通
    "BUSI_TYPE_FMY_WEIHU":"FAMILY_MEMBER_WEIHU",//家庭网维护
    "BUSI_TYPE_INSTALLMENT_ORDER":"INSTALLMENT_ORDER",//和分期
    "BUSI_TYPE_ORDER_ENTER_NET":'order_enter_net',//电渠抢单
    "BUSI_TYPE_ZHUTI_PROD":"zhuti_prod",//主体产品变更
	"BUSI_TYPE_CHANGECARD_PROD":'changecard_prod',//补换卡
  "BUSI_TYPE_CARD_FOR_CARD" :'card_for_card',  //以卡换卡
	"BUSI_TYPE_NUMBER_TRANSFER_NET":'NUMBER_TRANSFER_NET',//携号转网
  "BUSI_TYPE_NUMBER_TRANSFER_NET_APPLY":'NUMBER_TRANSFER_NET_APPLY',//携号转网预约
	"BUSI_TYPE_ORDER_NUMBER_TRANSFER_NET":'ORDER_NUMBER_TRANSFER_NET',//电渠携号转网
    "BUSU_TYPE_KM_KAITONG":"FAMILY_KEY_ORDER", // 核心成员组开通
    "BUSU_TYPE_KM_WEIHU":"KEY_MEMBER_WEIHU", // 核心成员维护
    "BUSU_TYPE_CREDIT_LOAN":"credit_loan",// 信用购
    "BUSU_TYPE_WANNENE_FUKA":"wanneng_prod", // 万能副卡
    "BUSI_TYPE_FACE":"oper_face_register",//刷脸注册
    "BUSU_TYPE_SHARE_MORE":"share_more", // 多终端共享
    "BUSI_TYPE_XQKD" : "creatembandSubs",//小区宽带
    "BUSI_TYPE_XQKD_PRE" : "creatembandSubs_pre", //小区宽带预受理
    "BUSI_TYPE_HAPPY":"worry_free_purchase",//无忧购机
    "BUSI_TYPE_END_RECACT":"end_recact",//中断接续无纸化
    "BUSI_TYPE_ARREARS_PAY":"arrears_pay",//欠费缴费支付
    "BUSI_TYPE_TEL_RECHARGE":"tel_recharge",//充值支付
    "BUSI_TYPE_GROUP_USER_CHANGE":"group_user_change",//客户资料变更

    'PRIV_ID_CREDIT':'10000003',//信用购列表菜单号
	'PRIV_ID_BAND':'10000012',//宽带开通菜单号
	'PRIV_ID_NUMBER':'10000004',//号卡菜单号
	'PRIV_ID_ENTER_NET':'10000007', //选号入网菜单号
	'PRIV_ID_PSEQ_RELIEVE':'10000021',//预占解除菜单号
    'PRIV_ID_YUPEIHAO':'10000008',//预配号菜单
    'PRIV_ID_VALUEADD':'10000026', //增值产品订购
    'PRIV_ID_MAINPRODCHANGE': '10000005',//升档(主体产品变更)菜单号
    'BUSI_TYPE_FAMILY_NET_GRP_MEM':'national_family_net_maintain_member',// 亲情网成员维护 无纸化类型
    'BUSI_TYPE_FAMILY_NET_GRP_SHORTNO':'family_net_grp_shortno',// 亲情网短号开关 无纸化类型
	'BUSI_TYPE_SCHOOL_BOOKING':'school_booking',//校园业务预约
	"BUSI_TYPE_REPAIR_PHOTO":"repair_realname_photo",//用户实名补拍照
    "BUSU_TYPE_CALYX":'Calyx', // 花呗营销案
    "BUSI_TYPE_STORE_TEL_RECHARGE":"store_tel_recharge",//充值支付
	"BUSI_TYPE_PROMOTION_CENTER":"promotion_center",//活动促销
    "BUSI_TYPE_GROUP_ENTER_NET":"group_enter_net",//集团证件入网
  "BUSI_TYPE_MOD_GROUP_ENTER_NET":"mod_group_enter_net",//集团证件入网资料修改
    "BUSI_TYPE_SMARTNET_MRKTCASE":"smartnet_mrktcase", //智能组网+营销案融合受理
    "BUSI_TYPE_ONE_NUMBER_MOREBAND":"one_num_moreBand", //一号多宽
    "BUSI_TYPE_EASY_OVER_BOOK":"easy_over_book", //极简下单
    "BUSI_TYPE_BUSI_PRE_DEALING":"busi_pre_dealing", //业务预受理
    "GIT_UPLOAD_SWITCH":{
        "NB_VAS20210128_98":false,//阿拉盯年度账单开关打开
        "NB_VAS20210204_05":true,//5G短信小程序开关打开
        "NB_VAS20210126_01":false//内部优化需求开关关闭
    },
    "BUSI_TYPE_PROMOTION_BACK":"promotion_back",//活动中断
    "BUSI_TYPE_MOVE_MBAND":"move_mbrand_az",    //宽带移机
    "BUSI_TYPE_PERFECT_ORDER":"perfect_order",  //融合受理（完美一单）
    "BUSI_TYPE_PERFECT_ORDER_NEW":"perfect_order_new",  //完美一单(新)
    "BUSI_TYPE_EDUCATION_GAME":"education_game", //家庭宽带教育、游戏加速业务
    "BUSI_TYPE_WHOLE_HOME_NETWORK":"whole_home_network", //全屋组网
    "BUSI_TYPE_SECURITY_SERVICE":"security_service", //安防服务业务
    "BUSI_TYPE_NETWORK_SERVICE":"network_service", //组网服务业务
    "BUSI_TYPE_CLOUD_COMPUTER":"cloud_computer", // 云电脑业务
    "BUSI_TYPE_INVENTORY_AUDIT":"inventory_audit", //库存稽核业务
    "BUSI_TYPE_ORDER_INTEGRATED":"order_integrated",  //融合订购业务
    "BUSI_TYPE_CHECK_MATERIAL":"check_material", //资料合规性校验和完善资料业务
    "BUSI_TYPE_REALTIME_MATCHING":"realtime_matching",  //集团增值产品
    "BUSI_TYPE_TRANSPROV_CHANGECARD":"transprov_changecard",//跨省补换卡
  "BUSI_TYPE_CREATE_ONET_ACCOUNT":"create_onet_account",//一网通账号创建

  "BUSI_TYPE_GRABSHEET_BACK_PERMISSION":"grabsheet_back_permission",//电渠抢单详情回退按钮权限开关
  "AZ_CMD_CODE":"cmdjs00300",//爱知阿拉盯渠道
  "PANC_AZ_CODE":"cmdjs00302",//爱知泛渠道
  "HSCREEN_AZ_CODE":'cmdjs00307',//霸屏通知协议
  "SMART_STORE" : "smart_store", //智慧店铺
  "SHOPPING_CART" :"shopping_cart",//购物车
  "BUSI_TYPE_ONE_CLICK_PROCESSING" :"one_click_processing",//一键办理
  "GROUP_V_NET_OPEN":"group_v_net_open",//集团V网开通
  "CONVERGED_BUSINESS":"converged_business",//融合商品加入购物车业务
  "GOVERNMENT_BUSINESS":"government_business",//政企商品加购物车业务
  "TERMINALSALE":"terminalsale",//终端商机销售
  "BUSI_TYPE_WRITECARD_DX_CHOOSE_NUM":"write_card_choose_num", //选号入网、特殊选号入网走东信开关类型
  "BUSI_TYPE_WRITECARD_DX_CARRYING_NUM":"write_card_carrying_num", //携号转网走东信开关类型
  "BUSI_TYPE_WRITECARD_DX_GROUP_ENTERNET":"write_card_group_enternet", //集团证件入网、集团入网走东信开关类型
  "BUSI_TYPE_WRITECARD_DX_ORDER_HANDS":"write_card_order_hands", //电渠抢单走东信开关类型
  "BUSI_TYPE_WRITECARD_DX_NO_CARD":"write_card_no_card", //无卡预配走东信开关类型
  "BUSI_TYPE_WRITECARD_DX_CHANGE_CARD":"change_card_write_card", //补换卡走东信开关类型
  "BUSI_TYPE_WRITECARD_DX_SWAP_CARD":"swap_cards_for_cards", //以卡换卡走东信开关类型
  "BUSI_TYPE_GRABSHEET_ENTERNET_FENCE":"grabsheet_enternet_electronic_fence", //电渠抢单入网调用电子围栏红区开关类型
    "SKZQ_ACCESS_STATIONS": ['10011806','10012007','10013401','10013602','10013501','10013402','10013302','10013601'],//可开放商客专区首页岗位编码

    // 菜单链接的常量
    MENU_CHAIN: {
        // Business related
        BUSINESS: '2',
        TOOLS: '1',
        WORK_STAGE: 'workStage',
        CUSTOMER_VIEW: 'ke-hu-shi-tu',
        GROUP_VIEW: 'ji-tuan-shi-tu',
        SEARCH: 'sou-suo',
        SHOUYE_NEW: 'shou-ye-new',
        STAR_4848: '4848',

        // Sub menus
        SUB_HOTEL: 'Sub_Hotel',
        SUB_HOTEL_DETAIL: 'Sub_Hotel_Detail',
        SUB_HOTEL_SIGN: 'Sub_Hotel_Sign',
        SUB_HOTEL_RECOMMEND: 'Sub_Hotel_Recommend',
        SUB_HOTEL_COLLECT: 'Sub_Hotel_Collect',

        SUB_BUILD: 'Sub_Build',
        SUB_BUILD_DETAIL: 'Sub_Build_Detail',
        SUB_BUILD_SIGN: 'Sub_Build_Sign',
        SUB_BUILD_RECOMMEND: 'Sub_Build_Recommend',
        SUB_BUILD_COLLECT: 'Sub_Build_Collect',
        SUB_BUILD_GRP_COLLECT: 'Sub_Build_GrpCollect',

        SUB_GROUP:'Sub_Group',//智瞳
        SUB_GRID:'Sub_Grid', //网格工作台


        // AI related
        AI_SELECTNUM: 'AI_selectnum',
        AI_INVOICE: 'AI_invoice',

        // Other
        ZERO: 'zero',
        BOARD: 'board',
        FIND_CUSTOMER: 'find-customer',
        GROUP_VIEW_DB: 'jtyjDb',
        JIAKE_HOME: 'jiake-home',
        MY: 'my',
        SKZQ_HOME: 'skzq-home',
        SPYH: 'spyh',
        HANDLE: 'handle',
        TASK: 'task',
        TZ_APP: 'tzApp',
        XXCJ: 'xxcj',
        AREA_PREFIX: 'zhuan-qu-',

        //workstage-3级
        // 金额
        AMOUNT: 'amount',
        // 扫码
        SCAN_CODE: 'scanCode',
        // 业务日志查询
        BUSINESS_LOG_QUERY: 'businessLogQuery',
        // pk掌控值
        PK_CONTROL_VALUE: 'pkControlValue',
        // 指标看板
        KPI_DASHBOARD: 'kpiDashboard',
        // 查看报表
        VIEW_REPORT: 'viewReport',
        // 网格助手
        GRID_ASSISTANT: 'gridAssistant',
        // 更多
        MORE: 'more',
        // 预警
        WARNING: 'warning',
        // 商机
        BUSINESS_OPPORTUNITY: 'businessOpportunity',
        // 学习时长
        LEARNING_DURATION: 'learningDuration',
        // 购物车
        SHOPPING_CART: 'shoppingCart',
        // 地市专区
        PREFECTURE_AREA: 'prefectureArea',

        //workstage-=2级
        // 功能入口
        FUNCTION_ENTRY: 'functionEntry',
        // 大商机
        BIG_BUSINESS_OPPORTUNITY: 'bigBusinessOpportunity',
        // AI学堂
        AI_SCHOOL: 'aiSchool'
    }
}

export {
    LOG_CONFIG,
    CONSTVAL,
    LONG_CITY_LIST
}
