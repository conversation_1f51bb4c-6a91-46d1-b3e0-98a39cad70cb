import { decrptParam } from '@/base/encrptH5.js'
import { LONG_CITY_LIST } from './config'

const SPECIAL_CHARS_REGEXP = /([\:\-\_]+(.))/g
const ASCII_CHARS = /[\x00-\xff]+/g

/**
 * 将日期转换成指定的格式，如dateFormat(new Date(),"yyyy-MM-dd hh:mm:ss") ==> 2019-07-02 08:09:04
 * @param {日期} val
 * @param {转换后的格式} fmt
 */
export function dateFormat(val, fmt) {
  if (isNaN(val) && !isNaN(Date.parse(val))) {
    return val
  }

  let o = {
    'M+': val.getMonth() + 1, //月份
    'd+': val.getDate(), //日
    'h+': val.getHours(), //小时
    'm+': val.getMinutes(), //分
    's+': val.getSeconds(), //秒
    'q+': Math.floor((val.getMonth() + 3) / 3), //季度
    'S+': val.getMilliseconds() //毫秒
  }
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (val.getFullYear() + '').substr(4 - RegExp.$1.length))
  }
  for (let k in o) {
    if (new RegExp('(' + k + ')').test(fmt)) {
      if (k === 'S+') {
        fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (('000' + o[k]).substr(('' + o[k]).length)))
      } else {
        fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length)))
      }
    }
  }
  return fmt
}

export function dateFormatter(formatter, date) {
  if (!date) return '未填写'
  date = date ? new Date(date) : new Date()
  const Y = date.getFullYear() + '',
    M = date.getMonth() + 1,
    D = date.getDate(),
    H = date.getHours(),
    m = date.getMinutes(),
    s = date.getSeconds()
  return formatter
    .replace(/YYYY|yyyy/g, Y)
    .replace(/YY|yy/g, Y.substr(2, 2))
    .replace(/MM/g, (M < 10 ? '0' : '') + M)
    .replace(/DD/g, (D < 10 ? '0' : '') + D)
    .replace(/HH|hh/g, (H < 10 ? '0' : '') + H)
    .replace(/mm/g, (m < 10 ? '0' : '') + m)
    .replace(/ss/g, (s < 10 ? '0' : '') + s)
}

/**
 * 将字符串转成日期格式
 * {@param {可以是日期格式  也可以是字符串格式yyyy-MM-dd、yyyyMMdd、yyyy/MM/dd}} param
 */
export function chgStrToDate(param, needHmi) {
  let retDate
  if (param instanceof Date) {
    if (needHmi) {
      return new Date(param.getFullYear(), param.getMonth(), param.getDate(), param.getHours(), param.getMinutes(), param.getSeconds())
    } else {
      return new Date(param.getFullYear(), param.getMonth(), param.getDate())
    }
  } else {
    let dateStr = param.replace(/-|\//g, '')
    let year = parseInt(dateStr.substr(0, 4))
    let month = parseInt(dateStr.length > 4 && dateStr.substr(4, 2)) - 1
    let day = parseInt(dateStr.length > 6 && dateStr.substr(6, 2))
    if (dateStr.length > 8) {//还有时分秒
      dateStr = dateStr.replace(/:|\s+/g, '')
      let h = parseInt(dateStr.length > 8 && dateStr.substr(8, 2)) || 0
      let m = parseInt(dateStr.length > 10 && dateStr.substr(10, 2)) || 0
      let s = parseInt(dateStr.length > 12 && dateStr.substr(12, 2)) || 0
      retDate = new Date(year, month, day, h, m, s)
    } else {
      retDate = new Date(year, month, day)
    }
  }
  return retDate
}

/**
 * 比较两个日期 aparam 比 bparam大返回1  小返回-1 相等返回0
 * @param {可以是日期格式  也可以是字符串格式yyyy-MM-dd、yyyyMMdd、yyyy/MM/dd} aparam
 * @param {可以是日期格式  也可以是字符串格式yyyy-MM-dd、yyyyMMdd、yyyy/MM/dd}  bparam
 */
export function cmpDateOrStr(aparam, bparam) {
  let retCode = 0
  let adate = chgStrToDate(aparam)
  let bdate = chgStrToDate(bparam)
  if (adate > bdate) {
    retCode = 1
  } else if (adate < bdate) {
    retCode = -1
  } else {
    retCode = 0
  }

  return retCode
}

export function getRect(el) {
  if (el instanceof window.SVGElement) {
    let rect = el.getBoundingClientRect()
    return {
      top: rect.top,
      left: rect.left,
      width: rect.width,
      height: rect.height
    }
  } else {
    return {
      top: el.offsetTop,
      left: el.offsetLeft,
      width: el.offsetWidth,
      height: el.offsetHeight
    }
  }
}

export function imageIsExist(url) {
  return new Promise((resolve) => {
    var img = new Image()
    img.onload = function() {
      if (this.complete == true) {
        resolve(true)
        img = null
      }
    }
    img.onerror = function() {
      resolve(false)
      img = null
    }
    img.src = url
  })
}


export function addClass(el, className) {

  if (!el) {
    return
  }
  el.classList.add(className)
}

export function removeClass(el, cls) {
  if (!el || !cls) return
  var classes = cls.split(' ')
  var curClass = ' ' + el.className + ' '

  for (var i = 0, j = classes.length; i < j; i++) {
    var clsName = classes[i]
    if (!clsName) continue

    if (el.classList) {
      el.classList.remove(clsName)
    } else {
      // if (hasClass(el, clsName)) {
      //   curClass = curClass.replace(' ' + clsName + ' ', ' ');
      // }
    }
  }
  if (!el.classList) {
    el.className = trim(curClass)
  }
}

export const getStyle = function(element, styleName) {
  if (!element || !styleName) return null
  styleName = camelCase(styleName)
  if (styleName === 'float') {
    styleName = 'cssFloat'
  }
  try {
    var computed = document.defaultView.getComputedStyle(element, '')
    return element.style[styleName] || computed ? computed[styleName] : null
  } catch (e) {
    return element.style[styleName]
  }
}

// \s：空格 \uFEFF：字节次序标记字符 \xA0：禁止自动换行空白符，相当于html中的&nbsp;
export const trim = function(string) {
  return (string || '').replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g, '')
}
/* istanbul ignore next */
const camelCase = function(name) {
  return name.replace(SPECIAL_CHARS_REGEXP, function(_, separator, letter, offset) {
    return offset ? letter.toUpperCase() : letter
  })
}


export const on = (function() {
  if (document.addEventListener) {
    return function(element, event, handler) {
      if (element && event && handler) {
        element.addEventListener(event, handler, false)
      }
    }
  } else {
    return function(element, event, handler) {
      if (element && event && handler) {
        element.attachEvent('on' + event, handler)
      }
    }
  }
})()

/* istanbul ignore next */
export const off = (function() {
  if (document.removeEventListener) {
    return function(element, event, handler) {
      if (element && event) {
        element.removeEventListener(event, handler, false)
      }
    }
  } else {
    return function(element, event, handler) {
      if (element && event) {
        element.detachEvent('on' + event, handler)
      }
    }
  }
})()

/* istanbul ignore next */
export const once = function(el, event, fn) {
  var listener = function() {
    if (fn) {
      fn.apply(this, arguments)
    }
    off(el, event, listener)
  }
  on(el, event, listener)
}

export function debounce(func, delay) {
  let timer
  return function(...args) {
    if (timer) {
      clearTimeout(timer)
    }
    timer = setTimeout(() => {
      func.apply(this, args)
    }, delay)
  }
}

let timeout = null

export function debounceTwo(fn, wait) {
  if (timeout !== null) clearTimeout(timeout)
  timeout = setTimeout(fn, wait)
}

//从数组_arr中删除_obj对象
export function removeObjWithArr(_arr, _obj) {
  //debugger;
  var length = _arr.length
  for (var i = 0; i < length; i++) {
    if (JSON.stringify(_arr[i]) == JSON.stringify(_obj)) {
      if (i == 0) {
        _arr.shift() //删除并返回数组的第一个元素
        return
      } else if (i == length - 1) {
        _arr.pop()  //删除并返回数组的最后一个元素
        return
      } else {
        _arr.splice(i, 1) //删除下标为i的元素
        return
      }
    }
  }
}

//从数组_arr中删除_obj对象中属性keyName相同的
export function removeObjWithKey(_arr, _obj, keyName) {
  //debugger;
  var length = _arr.length
  for (var i = 0; i < length; i++) {
    if (_arr[i][keyName] == _obj[keyName]) {
      if (i == 0) {
        _arr.shift() //删除并返回数组的第一个元素
        return
      } else if (i == length - 1) {
        _arr.pop()  //删除并返回数组的最后一个元素
        return
      } else {
        _arr.splice(i, 1) //删除下标为i的元素
        return
      }
    }
  }
}

/**
 *
 * @param {*} arr 数组
 * @param {*} key 对象中的属性
 * @param {排序  asc 升序  desc 降序} order 默认升序
 */
export function cmpArrayByKey(arr, key, order = 'asc') {
  if (!arr || arr.length < 1) {
    return
  }
  if (!arr[0][key]) {
    return
  }
  let orderNum = 1
  if (order == 'desc') {
    orderNum = -1
  }

  function compare(key) {
    return function(a, b) {
      let value1 = a[key]
      let value2 = b[key]
      if (value1 < value2) {
        return orderNum * -1
      }
      if (value1 > value2) {
        return orderNum * 1
      }
      return 0
    }
  }

  return arr.sort(compare(key))
}

export function getSessionId() {
  let c_name = 'JSESSIONID'
  let retSessionId = 'a1e044eb0903b05d1921776a2824'
  console.info(document.cookie)
  if (document.cookie.length > 0) {
    let c_start = document.cookie.indexOf(c_name + '=')
    if (c_start != -1) {
      c_start = c_start + c_name.length + 1
      let c_end = document.cookie.indexOf(';', c_start)
      if (c_end == -1) c_end = document.cookie.length
      retSessionId = unescape(document.cookie.substring(c_start, c_end))
    }
  }
  return retSessionId
}

export function getQueryVariable() {
  let query = window.location.search.substring(1)
  if (!query) {
    query = window.location.hash
    let wenIdx = query.indexOf('?')
    query = query.substr(wenIdx + 1)
  }
  //console.info(query);
  let vars = query.split('&')
  let qryMap = {}
  for (let i = 0; i < vars.length; i++) {
    var pair = vars[i].split('=')
    let value = pair[1]
    if (pair.length > 2) {//针对这样的参数 token=V8x7/bXcqAw====3
      for (let j = 2; j < pair.length; j++) {
        if (pair[j]) {
          value += pair[j]
        } else {
          value += '='
        }
      }
    }
    qryMap[pair[0]] = value
  }
  return qryMap
}

//判断是不是ASCII码
export function isAsciiChar(str) {
  return ASCII_CHARS.test(str)
}

//判断当前客户端版本curVersion 是大于在versionCode
export function cmpClientVersion(curVersion, versionCode) {
  if (!versionCode || !curVersion) {
    return false
  }
  //如果两个版本一样  则返回true
  if (curVersion == versionCode) {//必须大于versionCode
    return false
  }
  let retFlg = false
  let curArr = curVersion.split('.')//当前客户端版本分解成数组，如2.20.41  分解成[2,20,41]
  let verArr = versionCode.split('.')//被比较的版本分解成数组
  let len = Math.min(curArr.length, verArr.length)
  for (let i = 0; i < len; i++) {//循环版本号数组
    if (parseInt(curArr[i]) > parseInt(verArr[i])) {//当前版本号转换成数值后大于被比较的版本
      retFlg = true
      break
    } else if (parseInt(curArr[i]) < parseInt(verArr[i])) {//当前版本号转换成数值后小于被比较的版本
      retFlg = false
      break
    }
  }
  return retFlg
}

const urlPrefix = {
  //http://***************:8080
  center1: 'DE0B06E8E11DE0D0AA7BD76FE7D9F11A4E39446E5EECAD7BBE1609E51B10D797AEDA9F4B9DA6EE6E76C3F6350E36CA5054CFF75BAA6BB899',
  //http://**************:8080
  center2: 'DE0B06E8E11DE0D0D9C264D53E667467C463D1EB91E43886CA178292CADAC0860D87226EBF6054E740BB1C9D0F10C9EEE3DE5A11563C4A12',
  //http://wx.tzcmcc.com/tzweixin/WeiXin/ZltcRec?action=main
  tzUrl: 'DE0B06E8E11DE0D08E2A85A929D2CBC5303F9049E7A922DCFA5F6F80FF0DE4A785F87D2E9344399EFB8BC2D75C68A38AFB7C18787497DA290A77C3ED4205099CAEB112195E4CB19AD6453EAE0DE2160EB8E416EBBA018E3ADE498A16E605F07D95C41B8AB6615FB7E559488D0DBDBEA7',
  //http://221.178.251.57:8080
  // mapUrl:'DE0B06E8E11DE0D0AA7BD76FE7D9F11A4E39446E5EECAD7BBE1609E51B10D7974A3D9BECD88F5B83B235A88FBE95B199E3DE5A11563C4A12',
  mapUrl: 'DE0B06E8E11DE0D0AA7BD76FE7D9F11A4E39446E5EECAD7BBE1609E51B10D797308E50EBAC79EFCA9EBE7B86A074C883E3DE5A11563C4A12',
  //http://wap.js.10086.cn/zzd/workbench/workbenchinit
  maDian: 'DE0B06E8E11DE0D08E2A85A929D2CBC579E2D102EE7FA327389844B0C5EC06AF9A764875CF08380BEBC934D0E65571B1B132B8C91C0986E71C1BA80A074D594EE11C4601DEB0F9F95DECD611CD8A3E05EEA93ADFF074CF205993DBDA29D528B2F5F61C38BF1FD75A',
  // http://***************
  hip1: 'DE0B06E8E11DE0D0AA7BD76FE7D9F11A4E39446E5EECAD7BBE1609E51B10D797AEDA9F4B9DA6EE6E3D4E251D0A829D60',
  // **************
  ip2: '49F31D61D1482E8925613FE7AA8D88696CF8B19E4C51AB884BB7AA771041FBAE',
  // ***************
  ip1: '09EFCFD445E76E2390C7BE66084C4101F507203921EABDBCD8101EA16DC7C8EA',
  //https://xxh5.danghuan.com/jsyd_saler/index.html
  oldFoNew: 'DE0B06E8E11DE0D0C92E5C944D7E023468F9E114FB1E16169C47C397F4CB4DC2E6662777374B5434936F20560DF2588E55BAB683A4BC3E7D834544E4FA659A3375D6F89C1CEB8AD2C9B8691B9F93E109F6A69104251454E2ECC8EF908509BA19',
  //https://hysh.komect.com
  hyUrl: 'DE0B06E8E11DE0D0C92E5C944D7E0234C1B91B7D52E11BFE5AF2DB891D6F4D6B888AAE8FA42BB2A7F22D963371D31A6A',
  //https://networkorder.komect.com/reserveH5/pages/headerTest/?province=4854JX
  hotWifi: 'DE0B06E8E11DE0D0C92E5C944D7E02348E94FA5767DCE35026D5C05D4013E8D4B4E527D4FD8ED1CD5AF2DB891D6F4D6B888AAE8FA42BB2A752C02CA72617707F33A7A0A139009E058845614FD63C8F1639CC5FE1BD83178E8C71DC1301AC76C55193875884F6A1FE33D7C59177DA70D0E32820ED35F54CEAF4FEB8DFD1B27F238AFD1AE45512FA880B1866E48B6DA0AA47A600BCC2C9F2F5',
  //https://************:8080
  studyGarden: 'DE0B06E8E11DE0D0C92E5C944D7E0234E61A689A442EBD9D4629FF8542CF010A3B0ECD63843147A43378D01690D11B08A940742DD9558C4F',
  //阿波罗测试环境 https://cnr.asiainfo.com/cnr-web/aldList
  apolloTestUrl: 'DE0B06E8E11DE0D0C92E5C944D7E0234C797CD8A25E6B5B0D269AC5E49349D105C0DAA3BD9B2E22385F87D2E9344399E5680E5E19D63F37C3656842BC0038401678870151D037E31885FFBE60AA38DD1',
  //阿波罗生产环境 https://wx.apollojs.cn/cnr-web/aldList
  apolloDevUrl: 'DE0B06E8E11DE0D0C92E5C944D7E0234B8AFA862FF9330866184CBA2E8582841E8FF69E656540E02BB286656DD4A86D4556C1DCD460DC9B5F7BD3EE87E0119A5436A41C9FF2C63C80D246D433A8AE472',
  //网格风险画像：https://************:8080
  gridRisk: 'DE0B06E8E11DE0D0C92E5C944D7E0234E61A689A442EBD9D4629FF8542CF010A3B0ECD63843147A43378D01690D11B08A940742DD9558C4F',
  //https://bigdata.10086.cn/wgt/
  // smartGridUrl:"DE0B06E8E11DE0D0C92E5C944D7E0234CBA60AD66D02528ED83D1BDE3825C3812AF3FB9E1291465D9A51939B1F686B6CD2A65FFECEDF6D12"
  smartGridUrl: 'DE0B06E8E11DE0D0C92E5C944D7E0234CBA60AD66D02528ED83D1BDE3825C3812AF3FB9E1291465D9A51939B1F686B6CD2A65FFECEDF6D12151C83835664F613',
  // http://211.103.118.136:8090
  taiZUrl: 'DE0B06E8E11DE0D0AA7BD76FE7D9F11A2B52126EA54DCF3752244D9EBC0A4B0EC8AC3331D1BD57125865C84395FE6D3FE1217792BFA03BF1',
  //https://www.ycyd0515.com
  citySupportUrl: 'DE0B06E8E11DE0D0C92E5C944D7E0234363ED5B5577023A07CC663E06FAF826B480B919332DA14CB85F87D2E9344399E',
  //http://183.207.196.45:8090
  yanZUrl: 'DE0B06E8E11DE0D0D9C264D53E667467C463D1EB91E43886CA178292CADAC0863642604AA01F1D9E60D48A28544897D8C5F25E1F091365E8'
}

//获取解密后的URL
export function getRealUrl(key) {
  if (key && urlPrefix[key]) {
    return decrptParam(urlPrefix[key])
  }
}

//网址拼参数判断用？还是&号
export function appendParamToUrl(url, param) {
  if (!url) {
    return url
  }
  if (~url.indexOf('?')) {//URL链接中有?，则用&连接
    url += '&'
  } else {
    url += '?'
  }
  if (param) {
    url += param
  }
  return url
}

// 加法
export function accAdd(data1, data2) {
  var r1, r2, m, c
  try {
    r1 = data1.toString().split('.')[1].length
  } catch (e) {
    r1 = 0
  }
  try {
    r2 = data2.toString().split('.')[1].length
  } catch (e) {
    r2 = 0
  }
  c = Math.abs(r1 - r2)
  m = Math.pow(10, Math.max(r1, r2))
  if (c > 0) {
    var cm = Math.pow(10, c)
    if (r1 > r2) {
      data1 = Number(data1.toString().replace('.', ''))
      data2 = Number(data2.toString().replace('.', '')) * cm
    } else {
      data1 = Number(data1.toString().replace('.', '')) * cm
      data2 = Number(data2.toString().replace('.', ''))
    }
  } else {
    data1 = Number(data1.toString().replace('.', ''))
    data2 = Number(data2.toString().replace('.', ''))
  }
  return (data1 + data2) / m
}

// 减法
export function numSub(data1, data2) {
  var num, num1, num2
  var precision// 精度
  try {
    num1 = data1.toString().split('.')[1].length
  } catch (e) {
    num1 = 0
  }
  try {
    num2 = data2.toString().split('.')[1].length
  } catch (e) {
    num2 = 0
  }
  num = Math.pow(10, Math.max(num1, num2))
  precision = (num1 >= num2) ? num1 : num2
  return ((data1 * num - data2 * num) / num).toFixed(precision)
}


// 乘法
export function accMulti(data1, data2) {
  var baseData = 0
  try {
    baseData += data1.toString().split('.')[1].length
  } catch (e) {
  }
  try {
    baseData += data2.toString().split('.')[1].length
  } catch (e) {
  }
  return Number(data1.toString().replace('.', '')) * Number(data2.toString().replace('.', '')) / Math.pow(10, baseData)
}


// 除法
export function division(arg1, arg2) {
  var t1 = 0, t2 = 0, r1, r2
  try {
    t1 = new String(arg1).split('.')[1].length
  } catch (e) {
  }
  try {
    t2 = arg2.toString().split('.')[1].length
  } catch (e) {
  }
  r1 = Number(arg1.toString().replace('.', ''))
  r2 = Number(arg2.toString().replace('.', ''))
//放大倍数后两个数相除 后，乘以两个小数位数长度相减后的10的次幂
  var newData = accMulti((r1 / r2), Math.pow(10, t2 - t1))
//保留2个小数位数：
  return newData.toFixed(2)
}

export function getRandomStr(range = 100000) {
  let rangeNum = parseInt(range, 16)
  return (((1 + Math.random()) * rangeNum) | 0).toString(16).substring(1)
}

const _b2 = ['m0G7g', 'm0G7', 'OQ0V', '3u0v', 'g8', '4LfkZ', 'N4LfkZb']
const c10 = ['c3Y', 'lyZA', 'd3b', 'c3Y', 'N7C', 'OQGK', 'rGK', '6yk', '6y', '043A', 'kj']


//加密的密钥，用于加密请求报文
export function getEncKey(_) {
  return _b2.filter(function(e, i) {
    return i % 3 == 0
  }).join('')
}

//集客大厅的key
export function getGroupKey() {
  return '8X6Df7//@/mx2SH8'.split('').map(char => {
    let code = char.charCodeAt(0)
    if (code < 32) {
      return ' ' // 或者 return char; 如果不想替换
    }
    return String.fromCharCode(code + 1)
  }).join('')
}

export function getJmKey(input) {
  return c10.filter(function(e, i) {
    return i % 2 == 0
  }).join('')
}

// 获取当前周一和周日的日期
export function getStartAndEnd(type = '1') {
  let now = new Date()

  // 格式化日期为YYYY-MM-DD
  function formatDate(date) {
    let year = date.getFullYear()
    let month = date.getMonth() + 1 // 月份是从0开始的
    let day = date.getDate()
    return `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`
  }

  if (!type || type === '1') {
    let dayOfWeek = now.getDay() // 0 (Sunday) through 6 (Saturday)
    // 如果当前日不是周一，则向前调整日期
    if (dayOfWeek !== 1) {
      now.setDate(now.getDate() - dayOfWeek + 1)
    }
    let monday = new Date(now)
    let sunday = new Date(now)
    // 设置周日的日期
    sunday.setDate(sunday.getDate() + 6)
    return [formatDate(monday), formatDate(sunday)]
  } else if (type === '2') {
    const start = new Date(now.getFullYear(), now.getMonth(), 1) // 月初
    const end = new Date(now.getFullYear(), now.getMonth() + 1, 0) // 月末
    return [formatDate(start), formatDate(end)]
  } else if (type === '3') {
    // 近三个月
    const dates = []
    for (let i = 2; i >= 0; i--) {
      const start = new Date(now.getFullYear(), now.getMonth() - i, 1)
      const end = new Date(start)
      end.setMonth(end.getMonth() + 1, end.getDate() - 1)
      dates.push({ start: start, end: end })
    }
    return [formatDate(dates[0].start), formatDate(dates[2].end)]
  }

}
// 小数四舍五入取N位
export const formatNumber= (num,n= 2)=> {
  if(num){
    num = Number(num)
  }else{
    return num
  }
  if (Number.isInteger(num)) {
    return num;
  } else {
    return num.toFixed(n);
  }
}
// 长短编码转换
export const getRegionChange = (region) => {
  let result
  if (region) {
    if (region.length < 3) {
      let obj = LONG_CITY_LIST.find(item => item.shortId === region)
      result = obj ? obj.id : region
    } else {
      let obj = LONG_CITY_LIST.find(item => item.id === region)
      result = obj ? obj.shortId : region
    }
  }
  return result
}
// 处理数量转化成万
export const checkNum = (num) => {
  if (num) {
    // 查过万显示1w
    if (num > 10000) {
      return (num / 10000).toFixed(1) + 'w';
    } else {
      return num;
    }
  }
}
// 获取当前系统方法
export const getClientType = () => {
  // 判断当前设备类型 'ANDROID' : 'IOS'  harmony
  const u = navigator.userAgent;
  const isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1;
  const isiOS = !!u.match(/CPU.+Mac OS X/);
  const isHarmony = u.indexOf('HarmonyOS') > -1 || u.indexOf('ArkWeb') > -1;
  if (isHarmony) {
    return  'harmony';
  } else if (isAndroid) {
    return 'ANDROID';
  } else if (isiOS) {
    return  'IOS';
  } else {
    return 'ANDROID';
  }
}
export const checkStringIsNotNull = (s) => {
    s = s.trim();
    return  s !== null && s !== undefined &&
            s !== '' &&  s !== 'null' &&
            s !== 'undefined' && s !== 'NULL'  &&
            s !== 'UNDEFINED' ;
}
