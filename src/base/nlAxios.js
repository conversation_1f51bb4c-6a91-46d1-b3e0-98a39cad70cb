import axios from 'axios'
// import {iEncrpt,iEncrptParamMap} from '@/base/encrptH5.js'
import {decrptParam,digitAldSign,aesEncryptAllParams,decrptParamN} from '@/base/AesEncrptUtil.js'
import { Toast,Indicator } from 'mint-ui';
import ClientJs from '@/base/clientjs'
import Storage from '@/base/storage'
import MessageBox from 'components/common/NlMessageBox/message-box.js';
// import aldVue from '@/pages/xsbh5/index.js'
import {BASE64} from '@/base/coding'
import {getRandomStr,getGroupKey,getClientType} from '@/base/utils'



const nlAxios =  axios.create({
    transformResponse: [function (data) {
    // `transformResponse` 在传递给 then/catch 前，允许修改响应数据
    return data
    }]
});
nlAxios.defaults.timeout = 70000;//默认70秒
const RETRY = 0;//超时默认再请求一次
const RTRY_DELAY = 1000;//超时后的请求间隙

//客户端重新登录的回调
window.refreshUserInfo = function(res){
    let uinfo = JSON.parse(BASE64.decode(res));
    let oldUinfo = Storage.session.get('userInfo');
    let relTelnum = oldUinfo && oldUinfo.relTelnum;//学生手机号
    if(relTelnum){
        uinfo.relTelnum = relTelnum;
    }
    Storage.session.set('userInfo',uinfo);
    Storage.session.set('tokenId',uinfo.tokenid);

    nlAxios.defaults.headers.tokenid = uinfo.tokenid;
    nlAxios.defaults.headers.aldregion = uinfo.region+'_'+uinfo.servNumber;//新增aldregion
    nlAxios.defaults.headers.operaterPhone = uinfo.servNumber;//add by qhuang 2021-11-24登录token改造
    nlAxios.defaults.headers.imei = uinfo.imei;//add by qhuang 2021-11-24登录token改造
}

//请求拦截器
nlAxios.interceptors.request.use(
    config => {

        // console.info(aldVue.$route);
        if(config.__retryCount > 0){//重试机制触发的请求
            return config;
        }
        let method = config.method;
        let url = config.url;
        //注释by qhuang at 20231023,使用新版调用链
        // window['SAMRTLOG'] && window['SAMRTLOG'].addHeaderLog(url,config);//记录调用链
        let plog = window.Vue.$pointLesslog;
        plog && plog.addHeaderCfg(config);//无埋点调用链
        config.headers['menuChain'] = JSON.stringify(window.breadIds);// 菜单链
        let unLoadFlg = config.unLoadFlg || (config.data && config.data.unLoadFlg)
        if(method == 'get'){
            if(config.data == void 0) {
                config.data = {};//绕过axios不给get设置content-type的判断
                config.headers['Content-Type'] = "application/json"
            }
            if(!config.unLoadFlg){
                Indicator.open('加载中...');
            }

        } else {
            if(!config.data || !config.data.unLoadFlg ) {//如果data中传了unLoadFlg：true 则不展示加载圈，传false或者不传都展示
                Indicator.open('加载中...');
            }
        }
        if(process.env.NODE_ENV === 'development'){
            //开发联调环境需要配置跨域标识
            if(!~url.indexOf('/apiM')){
              if ((/android/gi).test(navigator.appVersion)) {
                // url = '/apiM' + url;
              } else {
                url = '/apiM' + url;

              }
            }


        }
        if(~url.indexOf('/xsb/api-user/appVersionCenter/h5appVersionInfo')){
        } else {

            let uinfo =  Storage.session.get('userInfo');
            let tokenId = '';
            //判断是否要参数整体加密
            // let isTakeTokenid =  Storage.session.get('isTakeTokenid');//参数携带token的开关 == '1'

            if(uinfo.tokenid ){//&& isTakeTokenid== '1'
                tokenId = uinfo.tokenid;
                //转译处理
                tokenId = tokenId.replace(/\+/g,'%2B').replace(/&/g,'%26').replace(/\//g,'%2F').replace(/=/g,'%3D')
            }

            if(method == 'get'){
                //URL链接中有?，则用&连接
                url += ~url.indexOf('?')?'&':'?';
                url += `staffId=${uinfo.staffId}&region=${uinfo.region}&crmId=${uinfo.crmId}&servNumber=${uinfo.servNumber}`;
                if(uinfo.relTelnum){
                    url += `&studentPhone=${uinfo.relTelnum}`;
                }
                if(uinfo.dwUser){//代维工号
                    url += `&dwUser=${uinfo.dwUser}`;
                }
                if(tokenId){//tokenid add by qhuang 20231101
                    url += `&tknZigZa=${tokenId}`
                }
            } else {
                if(!config.data){
                    config.data = {};
                }
                config.data.staffId = uinfo.staffId;
                config.data.region = uinfo.region;
                config.data.crmId = uinfo.crmId;
                config.data.servNumber = uinfo.servNumber;
                if(uinfo.relTelnum){
                    config.data.studentPhone = uinfo.relTelnum;
                }
                if(uinfo.dwUser){//代维工号
                    config.data.dwUser = uinfo.dwUser;
                }
                if(tokenId){//tokenid add by qhuang 20231101
                    config.data.tknZigZa = tokenId;
                }
            }

            url = encryptParam(config,url);//加密处理
        }
        url += (~url.indexOf('?')?'&':'?') + 'rdom=' + getRandomStr();//为协助后端定位问题，统一添加随机数 add by qhuang at 20231101
        config.url = url;
        config.unLoadFlg = unLoadFlg;//加载圈再单独加上去，可能被整体加密了，响应拦截就获取不到了
	    return config;
	},
	err => {
	    return Promise.reject(err)
})


//新版本加密处理
function dealEncryptParam(config,url){
    let method = config.method;//请求方式，目前用到了get、post
    let beforeStr = url;
    if(method == 'post'){
        beforeStr = config.data
    }
    try{
        let afterParam = aesEncryptAllParams(beforeStr);
        if(~url.indexOf('/group/usercenter/bbossAuth/h5bbossAuthNew')){//排除集客大厅的鉴权
            afterParam = aesEncryptAllParams(beforeStr,getGroupKey());
        }
        if(method == 'get'){
            // console.info("get加密后：：：",afterParam)
            return afterParam;
        } else {
            config.data = afterParam;
        }
    }catch(e){
        //新版加密异常，切换老的加密方式
        nlAxios.defaults.headers.c03be90046a7e7f = '';
        // application/json
        // return dealEncryptParamOld(config,url);
        console.info(e);
        return url;
    }
    return url;
}
// //老版本加密处理
// function dealEncryptParamOld(config,url){
//     let method = config.method;//请求方式，目前用到了get、post
//     if(method == 'get'){
//         url = iEncrpt(url);
//     } else {
//         config.data = iEncrptParamMap(config.data);
//     }
//     return url;
// }
//参数加密处理
function encryptParam(config,urlParam){
    let url = urlParam || config.url;
    // //判断是否要参数整体加密
    // let isEncrptAll =  Storage.session.get('isEncrptAllFlg');

    let skipEncrptFlg = false;//是否跳过加解密，默认加密，false
    skipEncrptFlg = config.method == 'get'?(config.unEncrpt||(config.data && config.data.unEncrpt)):config.data && config.data.unEncrpt;
    if(skipEncrptFlg){//跳过加解密
        return url;
    }
    // if(isEncrptAll == '1'){//整体加解密
    //如果全局加密，则在请求头给标识，用于后端全局解密
    config.headers.c03be90046a7e7f = 'GwRT4HjrxC9Davw';
    config.headers['Content-Type'] = "application/json"
    url = dealEncryptParam(config,url)
    // } else {
    //     url = dealEncryptParamOld(config,url);
    // }
    return url;
}


function validateRespSign(res){
    let resData = res.data;
    let respSign = res.headers && res.headers['respsign'];
    try{
        if(respSign){//如果服务端对返回报文签名了
            let h5Sign = digitAldSign(resData);
            // if(h5Sign == respSign){
                res.data = JSON.parse(resData);
            // } else {
            //     res.data = {'retCode':'4444','retMsg':'非法请求'}
            // }
        } else {
            res.data = JSON.parse(resData);
        }
    }catch(e){
        //无法转json,是需要相关业务代码二次解密
    }
}
// http响应拦截器
nlAxios.interceptors.response.use(res => {
    let url = res.config.url;
    let configData = res.config.data;
    if(~url.indexOf('/preTelEnterNet/h5getPaperlessStatus')
        || ~url.indexOf('/preTelEnterNet/h5PreTelCommitNew')
        || ~url.indexOf('/xsb/ability/trace/h5receiveTraceNew')
        || res.config.unLoadFlg
        ||(configData && (configData.unLoadFlg||~configData.indexOf('unLoadFlg')))){
        //不需要关闭加载圈
    } else {
        Indicator.close();// 响应成功关闭loading
    }

    let resData = res.data;
    if (typeof resData === 'string') {
        try {
          JSON.parse(resData);//明文返回的报文字符串是可以解析成json的
          //验证明文签名的情况
          validateRespSign(res);
          // return res;
        } catch (e) {
            //不可解析的表示是密文
            try{//解决阿拉盯端调用泛渠道的接口未解密，导致前端报错
                let data = '';
                if(~url.indexOf('/group/usercenter/bbossAuth/h5bbossAuthNew')){//排除集客大厅的响应处理
                    data = decrptParamN(resData,getGroupKey())
                } else {
                    data = decrptParam(resData);//aes解密，遇到旧业务des加密的会catch异常
                }

                res.data = data;
            } catch(err){
                //Toast('统一解密异常：' + err);
            }
            validateRespSign(res);
        }
    }

    //鉴权失败状态码9999
    if(res.data.retCode =='9999'){
        let alertMsg = res.data.retMsg || '会话已失效请重新登录';
        //取最后一层路径，如/group/usercenter/bbossAuth/h5bbossAuthNew，取值h5bbossAuthNew
        let urlParam = url && url.split('/').pop();
        urlParam = urlParam && urlParam.slice(0,40);
        alertMsg += '<p style="color:#ddd;font-size:12px;">'+ urlParam +'</p>';
        MessageBox.alert(alertMsg,'温馨提示')
        .then((action) => {
            //请求客户端重新登录
            ClientJs.autologinInterface('refreshUserInfo');
        }).catch(() => {});

    }
    return res;
  }, error => {
    Indicator.close();

    //获取状态码
    const status =
        (error.response &&
            error.response.status &&
            error.response.status) ||
        '';
    const data = (error.response && error.response.data) || {};
    if (data.message) {
        Toast(data.message);
        return Promise.reject(data.message);
    }

    if (error.code == 'ECONNABORTED' && error.message.indexOf('timeout') != -1) {
        // Toast('请求超时');
        return reqTimeOutRetry(error.config);
    }

    if (status === 401) {
        Toast('登录过期,请重新登录');
        return Promise.reject('登录过期,请重新登录');
    }
    let url = error && error.config && error.config.url;
    if (status === 404) {
        // Toast('接口404报错');
        return Promise.reject('接口404报错:'+url);
    }
    if (status === 500) {
        Toast('服务器错误');
        return Promise.reject('服务器报错:'+url);
    }
    return Promise.reject('未知错误:'+url+' 状态：'+status);
})

//请求超时重试机制
function reqTimeOutRetry(config){

    if(RETRY == 0){//不设置重新请求
        return Promise.reject('请求超时,'+config.url);
    }
    //保存重试次数的变量
    config.__retryCount = config.__retryCount || 0;
    //判断是否超过配置的重试次数
    if(config.__retryCount >= RETRY) {
        //超过重试次数则不再重试
        return Promise.reject('请求超时,'+config.url);
    }
    config.__retryCount += 1;

    var backoff = new Promise(function(resolve) {
        //配置的请求间隙后重新发送
        setTimeout(function() {
            resolve();
        }, RTRY_DELAY || 1);

    });
    return backoff.then(function() {
        return axios(config);
    });
}

export default nlAxios;


