//读取身份证相关的
import {dateFormat} from "./utils";
import ClientJs from './clientjs'
import Storage from '@/base/storage.js'
import {cmpClientVersion} from '@/base/utils';
import {Indicator} from 'mint-ui'

//读取身份证相关的
export const clientLanyaMixin = {
  data() {
    return {
      marketTimer:null,
      nfcFlg:false,
      isAndroid:/android|harmony/gi.test(navigator.userAgent),
      curLanya:{id:'0',label:'请选择'},//当前选择的蓝牙设备
      readSfzFlg:false,
      nowDate:'',//当前日期
      limitDate:'',//限制年龄  16岁
      saveSfzFlg:false,
      sfzAttachId:'',//身份证附件ID
      headId:'',//身份证头像ID
      signImgs:'',//签名图片
      pseq:'',//拍照返回 证件欲占返回的
      netSrl:'',//拍照返回 提交的主键
      verifySimilarity:'',//拍照返回，比对相似度
      isScanning:false,//是否正在扫描蓝牙
      isReadingFlg:true,//是否正在读取身份证
      busiType:'',//业务类型 选号入网业务类型：enter_net，物联网卡业务：iot_enter_net
      sfzCameraImageIpdFlg:true,//没有生僻字，不需要上传拍摄的照片
      rareWordPicId:'',//有生僻字后上传拍摄的照片的附件编号
      sfzObj:{
        sfzId:'',
        sfzName:'',
        sfzAddr:'',
        usAddr:'',
        sfzStartDate:'',
        sfzEndDate:'',
        nation:'',
        sex:'',
        idCardType:'',
        idCardValidity:'',
        birthYear:'',//年
        birthMonth:'',//月
        birthDay:'',//日
        idCardOrganization:''
      },
      tmpIsPublicSrc:0,//默认为非公用（预配号）
      isRare:'0',//0 无生僻字  1 有生僻字
      sfzCameraImage:'',//拍的身份证图片
      sfzClientPath:'',//照片在手机客户端的路径
      clientRetSfzId:'',//客户端返回
      idcardParam:'',//因一证多名加的新透传参数
      versionSupNfc:'2.20.64',//支持nfc版本为2.20.71
      onnetNum:'',//在网号码数
      threeMonthNum:'', //近3个月的新入网号码数
      samedaydiffchnlIn7days:'', //省内号码数
      insideprovSubscount:'', //省外号码数
      outsideprovSubscount:'', //近7天在同一天、不同渠道入网2个及以上的号码数
      usernum:'',//在网号码数（全网）
      uploadZmFlag: false, // 是否需要上传收入证明
      readSfzUrl:'',//读取身份证url
    }
  },
  methods:{
    //是否支持NFC
    isSupportNFC(){
      if(/android|harmony/gi.test(navigator.userAgent)){
        window.upload.isSupportNFC("isNFC");
      }
    },
    //调用客户端扫描匹配过的蓝牙设备
    clientScan(){
      this.isReadingFlg = true;
      this.isScanning = true;//正在扫描蓝牙设备
      ClientJs.autoReadDevise("autoPpCallBack");
    },
    //展示所有的蓝牙设备
    showLanyaDevice(){
      this.isReadingFlg = true;
      this.readSfzagainFlg = false;
      this.clearSfzInfo();
      ClientJs.showFaceLanya("getIdentify","isSaveCallBack",this.tmpIsPublicSrc,this.idcardParam,this.readSfzUrl)
    },
    //读取nfc按钮事件
    readSfzNfc(){
      //获取版本号
      if(!this.cmpVersion()){
        this.$messagebox({
          title: '温馨提示',
          message: '当前阿拉盯版本暂不支NFC读取功能，请到【关于】页面下载最新app',
          showCancelButton: false,
          confirmButtonText: '确认'
        }).then((action) => {
          this.$router.push('/about')
          return;
        });
      }else{
        this.isReadingFlg = true;
        let tmpFlg = false;
        if(this.tmpIsPublicSrc == '1'){
          tmpFlg = true;
        }
        this.clearSfzInfo();//清除上次读取的证件信息
        if(window.upload.nfcReadIdCardPassthrough){//因一证多名需求增加了透传给服务端的参数
          window.upload.nfcReadIdCardPassthrough("getIdentify","isSaveCallBack",tmpFlg,this.idcardParam);
        } else {
          window.upload.nfcReadIdCard("getIdentify","isSaveCallBack",tmpFlg);
        }
      }
    },
    //nfc读取判断版本大于2.20.64
    cmpVersion(){
      let curV = Storage.session.get('clientVersion');//从缓存中获取客户端版本
      let canNfc=true;
      if(!curV){
        //从客户端版本获取版本信息
        var res = window.WebViewFunc.getAppVersion();
        var obj = eval('(' + res + ')');
        curV = obj.verison;
        Storage.session.set('clientVersion',obj.verison);
      }
      //判断是阿拉丁还是泛渠道
      if(curV&&curV.substr(0,1)=='2'){
        canNfc=cmpClientVersion(curV,this.versionSupNfc);
      }
      return canNfc;
    },
    //点击拍照
    paizhao(uinfo,callBackFn,noticeCallBackFn){

      let sfzObj = this.sfzObj;
      if(!sfzObj.sfzId){//如果没有证件号，则不再继续办理
        this.$alert('身份证数据留存失败，请重新读取身份证');
        return;
      }
      let jqDataRegion="";
      //补换卡，取用户地市
      if(this.busiType =='changecard_prod'){
        jqDataRegion=Storage.session.get('jqData').userCity;
      }

      let param = {
        userName:sfzObj.sfzName,//证件名
        address:sfzObj.sfzAddr,//地址
        certId:sfzObj.sfzId,//证件号
        deviceType:this.curLanya.id,//读证设备类型
        deviceName:this.curLanya.label,//读证设备名称
        phoneType:sfzObj.phoneType,//手机型号
        certeffDate:sfzObj.sfzStartDate,//生效时间
        certexpDate:sfzObj.sfzEndDate,//失效时间
        telNumber:this.telNumber,//预配号
        idCardType:sfzObj.idCardType,//证件类型
        headId:this.headId,//身份证头像流水
        staffId: uinfo.staffId,
        region: uinfo.region,
        crmId: uinfo.crmId,
        busiType:this.busiType,//业务类型 选号入网业务类型：enter_net，物联网卡业务：iot_enter_net
        servNumber: uinfo.servNumber,
        jqDataRegion:jqDataRegion,
      }
      if(this.busiType == 'iot_enter_net'){//13位物联网卡号人证比对通过不了
        param.telNumber = '';
      }
      // 特殊选号入网/预配号补资料使用 选号入网/预配号是否进行人证比对标识 1:不进行
      if (uinfo.isNoCheck == '1') {
        param.isNoCheck = '1';
      }
      console.info(param);
      //noticeCallBack 立即回调 通知拍照结果
      let url = "xsb/personBusiness/preTelEnterNet/faceCheckIdCardEx"
      ClientJs.openFaceCamera(JSON.stringify(param), noticeCallBackFn || "noticeCallBack",callBackFn || "mCallBackMethod", this.tmpIsPublicSrc,url);
    },
    //查询是否开启
    async checkOpenNewCamera(compareVersionNew){
      let curV = Storage.get('appVersion');//从缓存中获取客户端版本
      let flag= cmpClientVersion(curV,compareVersionNew)
      if(flag) {
        let param = {
          busiType: "open_new_camera",
        };
        await this.$http.post('/xsb/ability/businessLimit/h5QryBusiPermission', param).then((res) => {
          let {retCode} = res.data;
          if (retCode == '0') {
            flag = true;
          } else {
            flag = false;
          }
        });
      }
      return flag;
    },

    //读取身份证
    readSfz(){
      this.isReadingFlg = true;
      //蓝牙设备读取
      ClientJs.readFaceIdCard("getIdentify", "isSaveCallBack", this.tmpIsPublicSrc, this.idcardParam,this.readSfzUrl);
    },
    //add by qhuang 20190620 身份证上有生僻字，需要拍下身份证照片
    openCameraAndShow(){
      ClientJs.openCameraAndShow("1","getSfzCameraPicCb")
    },
    clearSfzInfo(){
      let sfzObj = this.sfzObj;
      sfzObj.sfzId = '';//读取到的证件号
      sfzObj.sfzName = '';//读取到的名字
      sfzObj.sfzAddr = '';//读取到的地址
      sfzObj.usAddr = '';//用户常住地址默认为身份证地址
      sfzObj.sfzStartDate = '';//身份证有效期begin
      sfzObj.sfzEndDate = '';//身份证有效期end
      sfzObj.phoneType = '';
      sfzObj.nation = '';
      sfzObj.sex = '';
      sfzObj.idCardType = '';
      sfzObj.idCardValidity = '';
      sfzObj.idCardOrganization = '';
      sfzObj.birthYear = '';//年
      sfzObj.birthMonth = '';//月
      sfzObj.birthDay = '';//日
      sfzObj.passNumber = '';//港澳台参数
      sfzObj.issuesNums = '';//港澳台参数
      this.saveSfzFlg = false;
      this.sfzAttachId = '';//身份证附件ID
      this.headId = '';//身份证头像ID
      this.uploadZmFlag = false;
    },
    //签名
    sign(){
      ClientJs.qianming('signCallBack');
    },

    //客户关怀查询接口
    qrySubsCertIdNum(){
      if(this.sfzObj.sfzId) {
        let url = `/xsb/ability/qryAccessNum/h5qrySubsCertIdNum?certId=${this.sfzObj.sfzId}`;
        this.$http.get(url).then(res => {
          let {retCode,retMsg,data} = res.data;
          if (retCode == '0') {
            this.onnetNum = data.onnetNum;
            this.threeMonthNum = data.threeMonthNum;
            this.samedaydiffchnlIn7days = data.samedaydiffchnlIn7days;
            this.insideprovSubscount = data.insideprovSubscount;
            this.outsideprovSubscount = data.outsideprovSubscount;
          } else {
            this.$alert(retMsg || '客户证件名下用户数查询接口失败');
            this.onnetNum ='';
            this.threeMonthNum = '';
            this.samedaydiffchnlIn7days = '';
            this.insideprovSubscount = '';
            this.outsideprovSubscount = '';
          }
        }).catch((response)=>{
          this.$alert(response || '获取客户证件名下用户数查询接口网络超时');
          this.onnetNum ='';
          this.threeMonthNum = '';
          this.samedaydiffchnlIn7days = '';
          this.insideprovSubscount = '';
          this.outsideprovSubscount = '';
        })
      }
    },

    idcheck() {
      if (this.sfzObj.sfzId) {
        let param = {
          certId: this.sfzObj.sfzId,
          idCardType: this.sfzObj.idCardType,
          userName: this.sfzObj.sfzName,
          unLoadFlg:true,
        };
        let url = `/xsb/ability/qryAccessNum/h5idcheck`;
        this.$http.post(url, param).then(res => {
          let {retCode, retMsg, data} = res.data;
          if (retCode == '0') {
            this.usernum = data;
          } else {
            this.$toast(retMsg || '查询证件下信息接口失败');
            this.usernum = '';
          }
        }).catch((response) => {
          this.$toast(response || '查询证件下信息接口网络超时');
          this.usernum = '';
        })
      }
    },

    idchackClearData(){
      this.usernum = '';
      this.onnetNum ='';
      this.threeMonthNum = '';
      this.samedaydiffchnlIn7days = '';
      this.insideprovSubscount = '';
      this.outsideprovSubscount = '';
    }
  },
  mounted(){
    let uinfo = Storage.session.get('userInfo');
    let jqDataRegion="";
    //补换卡，取用户地市
    if(this.busiType =='changecard_prod'){
      jqDataRegion=Storage.session.get('jqData').userCity;
    }
    //透传的参数
    let idcardParam = {
      crmId:uinfo.crmId,
      region:uinfo.region,
      busiType:this.busiType,
      jqDataRegion:jqDataRegion,
    }
    this.idcardParam = JSON.stringify(idcardParam);
    this.nowDate = dateFormat(new Date(),'yyyyMMdd');
    let now = new Date();
    let year = now.getFullYear() - 16;
    this.limitDate = dateFormat(new Date(year,now.getMonth(),now.getDate()),'yyyyMMdd');
    // 将isNFC方法绑定到window下面，提供给外部调用
    window['isNFC'] = (result) => {
      let info = JSON.parse(result);
      if(info.retCode =='0'){
        this.nfcFlg = true;
      }
    };
    //客户端自动匹配设备并匹配后的回调函数
    window['autoPpCallBack'] = (result) => {
      let info = JSON.parse(result);
      console.info(info);
      var retCode = info.retCode;
      var retMsg = info.retMsg;
      var status = info.status;
      if(retCode == "0"){//获取成功
        if(status == "0"){//未匹配s
          this.curLanya = {id:'0',label:'未选择'};
          this.$toast(retMsg);////提示未发现匹配的设备
          this.isReadingFlg = false;
        }else if(status == "1"){ //匹配成功
          var deviceType = info.deviceType;//设备类型 1:惠泉 2：亿数3：华大 4:神思
          var deviceName = info.deviceName;//设备类型名称 1:惠泉 2：亿数3：华大 4:神思
          this.curLanya = {id:deviceType,label:deviceName};//初始化为之前匹配过的蓝牙设备
          this.isReadingFlg = true;
          console.info('idcardParam',this.idcardParam);
          ClientJs.readFaceIdCard("getIdentify","isSaveCallBack",this.tmpIsPublicSrc,this.idcardParam,this.readSfzUrl);
        }
      }else{
        this.$toast(retMsg);
        this.isReadingFlg = false;
      }
      this.isScanning = false;//扫描蓝牙设备结束
    };
    //拍照回调
    window['noticeCallBack'] = () => {
      console.info('====noticeCallBack=====');
      this.paizhaoStep = 'facevalidate';
    };
    //拍照后客户端回调人证比对结果
    window['mCallBackMethod'] = (result) => {
      console.info('==mCallBackMethod==')
      console.info(result);
      try {
        //把客户端返回的解析流
        let info = JSON.parse(result);
        console.info(info);
        this.currentStep = 'step2';//H5页面跳到展示身份证信息那页
        if(info.retCode == '0') {//人像比对成功
          this.pseq = info.data.pseq;
          this.netSrl = info.data.netSrl;
          this.verifySimilarity = info.data.verifySimilarity;
          this.checkSucess();
        } else {
          this.paizhaoStep = '';
          this.currentStep = 'step1';
          this.getSfzImgFlg = false;
          //金库授权弹窗
          if (info.data && info.data.needAuth === '1') {
            this.openVaultAuth(info);
          } else {
            this.$alert(info.retMsg||'人像比对失败')
          }
        }
      } catch (error) {
        this.$alert('人像比对返回报文解析失败：' + result);
        this.paizhaoStep = '';
        console.info(error);
      }


    };
    //读取身份证回调
    window['getIdentify'] = (result) => {
      console.info('getIdentify',result);
      this.isReadingFlg = false;
      this.closeLoading();
      var info = result.replace(new RegExp('\n', 'g'),'\\n');
      try{
        info = JSON.parse(info);
      }catch(err){
        try{
          info= eval("("+info+")");
        }catch(err2){
          this.$alert('身份证信息读取异常，请更换设备');
          console.info('err2',err2)
          return false;
        }
      }
      var retCode = info.retCode;
      var retMsg = info.retMsg;
      this.curLanya = {id:info.deviceType,label:info.deviceName};
      if(retCode == "0"){//获取成功
        let sfzObj = this.sfzObj;
        this.readSfzFlg = true;
        this.readSfzagainFlg = false;//暂时不能重新读取身份证
        sfzObj.sfzId = info.readIdNo.replace(/(^\s*)|(\s*$)/g, "");//读取到的证件号
        sfzObj.sfzName = info.readName.replace(/(^\s*)|(\s*$)/g, "");//读取到的名字
        if(sfzObj.sfzName === '无中文姓名'){
          sfzObj.sfzName  = '';
        }
        sfzObj.sfzEngName = info.idCardEngName.replace(/(^\s*)|(\s*$)/g, "");//非二代证读取到的用户英文姓名
        sfzObj.usAddr = info.readAddress.replace(/(^\s*)|(\s*$)/g, "");//用户常住地址默认为身份证地址
        sfzObj.sfzStartDate = info.certeffdate;//身份证有效期begin
        sfzObj.sfzEndDate = info.certexpdate.replace(/\s/g,'');//身份证有效期end
        if(sfzObj.sfzEndDate == '长期'){//如果是长期处理成20991231
          sfzObj.sfzEndDate = '20991231'
        }
        sfzObj.sex = info.idCardSex.replace(/(^\s*)|(\s*$)/g, "");//读取到的性别
        sfzObj.nation = info.idCardNation.replace(/(^\s*)|(\s*$)/g, "");//读取到的民族
        sfzObj.phoneType = info.phone_type;
        sfzObj.idCardType = info.idCardType || 'IdCard';//证件类型
        if( sfzObj.idCardType === 'IdCardForeigners'){
          sfzObj.idCardType = 'IdCardEx';
        }
        sfzObj.idCardValidity = info.idCardValidity;//有效期
        sfzObj.idCardOrganization = info.idCardOrganization;//签发机关
        sfzObj.birthYear = info.idCardBirth_Year;//年
        sfzObj.birthMonth = info.idCardBirth_Month;//月
        sfzObj.birthDay = info.idCardBirth_Day;//日
        let idCardType = sfzObj.idCardType;//证件类型
        if(idCardType == 'IdCardTW' || idCardType == 'IdCardGA'){//台湾居住证 港澳居住证
          sfzObj.passNumber = info.passNumber;
          sfzObj.issuesNums = info.issuesNums;
        }
        //判断16岁限制入网
        let idcardStr = sfzObj.sfzId;
        if(idcardStr && idcardStr.length > 14) {
          let userDate = idcardStr.substr(6, 8);
          if (this.sixRuwangFlag) {
            let now = new Date();
            // 16岁到18岁之间的用户需要上传收入证明
            let beforeDate = dateFormat(new Date(now.getFullYear() - 18,now.getMonth(),now.getDate()),'yyyyMMdd');
            let afterDate = dateFormat(new Date(now.getFullYear() - 16,now.getMonth(),now.getDate()),'yyyyMMdd');
            if(userDate > beforeDate && userDate <= afterDate && !this.isGuardian){
              this.$messagebox({
                title: '温馨提示',
                message: '该客户为16-18周岁的用户，需要上传收入证明',
                showCancelButton: true,
                confirmButtonText: '确认',
                cancelButtonText: '取消',
              })
              this.uploadZmFlag = true;
            }else if (userDate > this.limitDate ) {
              if(!this.isGuardian) {
                this.$messagebox({
                  title: '温馨提示',
                  message: '该用户为16周岁以下用户，需要录入监护人信息',
                  showCancelButton: true,
                  confirmButtonText: '确认',
                  cancelButtonText: '取消',
                }).then((action) => {
                  if (action === 'confirm') {//跳信用购标准
                    this.$router.push({
                      path: '/sixRuwang',
                      query: {
                        semFrom: this.srcFrom,
                        networkType: this.networkType,//入网类型
                        telNum : this.telNumber,
                      }
                    })
                  }else {
                    this.readSfzagainFlg = true;
                    this.readSfzFlg = false;
                  }
                })

              }else {
                this.readSfzagainFlg = true;
                this.readSfzFlg = false;
                this.$alert('用户出生日期：' + userDate + '，16岁以下用户限制入网！');
              }
            }else {
              this.uploadZmFlag = false;
            }
          } else {
            if (userDate > this.limitDate) {
              this.readSfzagainFlg = true;
              this.readSfzFlg = false;
              this.$alert('用户出生日期：' + userDate + '，16岁以下用户限制入网！');
            }
            let now = new Date();
            // 16岁到18岁之间的用户需要上传收入证明
            let beforeDate = dateFormat(new Date(now.getFullYear() - 18, now.getMonth(), now.getDate()), 'yyyyMMdd');
            let afterDate = dateFormat(new Date(now.getFullYear() - 16, now.getMonth(), now.getDate()), 'yyyyMMdd');


            if (userDate > beforeDate && userDate <= afterDate) {
              this.uploadZmFlag = true;

              //判断是否是收入证明支持的入网类业务：选号入网，预配号，非二代证，无卡预配
              //选号入网 this.CONSTVAL.BUSI_TYPE_ENTER-预配号  true_name_record
              if (this.busiType == this.CONSTVAL.BUSI_TYPE_ENTER
                || (this.busiType == 'true_name_record' && !this.isDianQuFlag)) {
                // 判断开关是否开启
                if (this.zmSwitchFlag) {
                  this.$alert('用户年龄为16-18周岁，需要上传收入证明');
                }
              }

            } else {
              this.uploadZmFlag = false;
            }

          }
          // 是否需要上传收入证明的标识
          Storage.session.set('uploadZmFlag', this.uploadZmFlag);
        }
        //判断身份证是否失效
        let certexpdate_card = info.certexpdate.trim();//身份证失效时间
        if(certexpdate_card && !isNaN(certexpdate_card)){
          if(certexpdate_card < this.nowDate){
            this.readSfzFlg = false;
            this.readSfzagainFlg = true;
            this.$alert('该用户身份证已失效，身份证有效期结束时间为：'+certexpdate_card);
          }
        }
      }else{
        this.readSfzFlg = false;
        this.clearSfzInfo();
        this.$toast(retMsg);
      }
      //该蓝牙设备去服务端校验是否在黑名单里
      this.deviceBlackByBusi();
    };
    //读完身份证后是否在服务端预留成功
    window['isSaveCallBack'] = (result) => {
      console.info('------isSaveCallBack------')
      let info = JSON.parse(result);
      console.info(info);
      this.saveSfzFlg = false;
      this.readSfzagainFlg = true;
      if (info.retCode == '0'){
        let data = info.data;
        this.clientRetSfzId = data.idCardNo || this.sfzObj.sfzId;//20200508添加客户端传身份证过来
        if(this.clientRetSfzId != this.sfzObj.sfzId){
            this.showPaiZhao = false;
            this.$alert('此身份证号为' + this.clientRetSfzId + '，不是最近读取的身份证，请稍等');
            return;
        }
        this.saveSfzFlg = true;
        let userInfo =  Storage.session.get('userInfo');// 对应的地市：region =14
        let region = userInfo.region;
        this.headId = data.headId;
        this.sfzAttachId = data.cardPicId;
        this.sfzObj.headId = data.headId;
        this.isRare = data.isRare || '0';//0 无生僻字  1 有生僻字 2 59个民族外的少数民族 3：生僻字加少数民族
        if(this.isRare != '0'){
          this.sfzCameraImageIpdFlg = false;//需要上传照片
          this.sfzCameraImage = '';
        } else {
          this.sfzCameraImageIpdFlg = true;
        }
        //非预配号 没有营销案校验
        this.paizhaoStep = '';

      } else {
        this.idchackClearData();
        this.readSfzFlg = false;
        this.showPaiZhao = false;
        this.$alert(info.retMsg||'上传身份证信息失败');
      }
    },
      window['signCallBack'] = (info) => {
        if(info.retCode=='0'){
          this.signImgs = info.retImg;
          this.$refs.signDom.src = 'data:image/jpeg;base64,' + this.signImgs;
          console.info(this.checkInstallInfo());
          if(this.checkInstallInfo()){//有赠卡校验
            this.submitStep = 'initTj';
          } else {
            this.submitStep = '';
          }
        }
      },
      //拍完身份证照片后的回调
      window['getSfzCameraPicCb'] = (info) => {
        console.info(info);
        this.sfzCameraImage = info.fileImage;//照片的base64串
        this.sfzClientPath = info.filePath;//照片在手机客户端的路径
        this.$refs.sfzImg.src = 'data:image/jpeg;base64,' + info.fileImage;
        if(info.fileImage){
          let param = {
            basePic:info.fileImage,
            cardId:this.sfzObj.sfzId,
            unEncrpt:true
          }
          //请求服务端上传
          let uploadURL = '/xsb/ability/rareWordUpLoad/h5upLoadImg';
          this.$http.post(uploadURL,param).then(res => {
            console.info(res);
            let {retCode,retMsg,data} = res.data;
            if(retCode != '0'){
              this.sfzCameraImageIpdFlg = false;
              this.$alert(retMsg || '上传照片异常')
            } else {
              this.rareWordPicId = data.rareWordPicId;
              this.sfzCameraImageIpdFlg = true;
            }
          })
        }
      };
    //视频采集后客户端回调返回结果
    window['videoCollectCallBackMethod'] = (result) => {
      this.currentStep = 'step2';
      console.info(result)
      try {
        let info = result;
        var retCode = info.retCode;
        var retMsg = info.retMsg;
        let machineReadFinishTime='0';
        if(info.machineReadFinishTime !=undefined && info.machineReadFinishTime !=null && info.machineReadFinishTime !=""){
          machineReadFinishTime=Math.ceil(info.machineReadFinishTime);
        }
        if(retCode == "0"){
          this.artificialFlag=false;
          this.videoCollectFlag = true;
          this.filePath = info.filePath;
          if(this.videoAginFlag){
            this.$toast("重拍成功");
            this.videoAginFlag = false;
          }else{
            this.videoAginFlag = false;
          }
          this.videoStartAiFlag='0';
        }else if(retCode == "33"){
          Indicator.open('AI识别中...');
          this.videoCollectFlag = true;
          this.filePath = info.filePath;
          this.videoStartAiFlag='1';
          if(this.videoAginFlag){
            //this.$toast("重拍成功");
            this.videoAginFlag = false;
          }
          if(this.artificialAiParam.mod=='3') {
            this.artificialAiParam.cut = machineReadFinishTime;
          }
          console.info(this.artificialAiParam)
          //AI识别
          let h5Param= this.artificialAiParam;
          let url = "/xsb/ability/videoRecord/h5ArtificialBegion";
          this.$http.post(url,h5Param).then(res => {
            if (res.data.retCode == '0') {
              this.artificialFlag=false;
              this.$toast("AI识别成功，请继续办理业务！");
            } else if (res.data.retCode == '-1') {
              this.filePath="";
              this.videoAginFlag = false;
              this.$alert(res.data.retMsg ||  '视频采集失败，请重新拍摄');
            } else {
              this.artificialFlag=true;
              this.$alert(res.data.retMsg || 'AI识别失败');
            }
            this.videoCollectAttachId=res.data.data;
            if(this.videoCollectAttachId==null || this.videoCollectAttachId==undefined){
              this.videoCollectAttachId="";
            }
          }).catch((res) =>{
            this.$alert('AI识别网络超时'+res);
            Indicator.close();
          });
        }
        else{
          this.artificialFlag=false;
          this.videoAginFlag = false;
          this.$alert(retMsg ||  '视频采集失败，请重新拍摄');
        }
      } catch (error) {
        this.videoAginFlag = false;
        this.$alert('视频采集返回报文解析失败：' + result);
      }
    };

  }
}
