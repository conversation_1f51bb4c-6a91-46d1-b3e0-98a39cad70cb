import Storage from "@/base/storage.js";
import {qryAbilityConfigByFeature} from '@/base/request/commonReq.js'
import { getClientType } from './utils'
// 远程签名参数
export const handleOrderReqmixin = {
  data() {
    return {
      userInfo: {}, //用户信息
      qwInfo: {}, //企微信息
      qwCustNumber: "", //企微客户手机号
      qwCustId: "", // 企微客户id
      telnum: "", //手机号码
      subscribeInfos: "", //已订购产品
      extraJson:{},//其他产品
      unsubscribeInfos: "", //失效产品
      orderPriceJson:'',//费用信息
      paperlessMod:0,//0走ald,1走爱知
      miniNoticeFlg:0,//用户确认走5g平台还是小程序，为1是小程序
      distinguishTypes:"ordinary",//区分普通业务和使用购物车逻辑业务(融合购物车，宽带，电视)
      remoteAuthCheck: 'AuthCheckR',
    }
  },
  created() {
    this.userInfo = Storage.session.get('userInfo');
    this.qwInfo = Storage.session.get('qwInfo');
    this.qwCustNumber = Storage.session.get("qw_custNumber");
    this.qwCustId = Storage.session.get("qw_custId");
  },
  methods: {
    /**
     * 回到调用方首页
     */
    backCallHome() {
      let content = '提交成功,稍候可在【我的】/【我的订单】里查询订单进度';
      if(this.miniNoticeFlg == 1) {
        content += `<br><span style='color: red; font-size: 10px'>请提醒用户到微信小程序“通信业务受理助手”受理</span>`;
      }
      this.$messagebox.alert(content, '温馨提示').then((action) => {
        if (window.triggerFrom && window.triggerFrom == 'qw') {
          let param = {
            busiType: "qw_wxAppletOrderList",
          };
          this.$http.post('/xsb/ability/businessLimit/h5QryBusiPermission', param).then((res) => {
            let { retCode } = res.data;
            if (retCode == '0') {
              this.$router.push('/wxAppletOrderList')
            } else {
              this.$router.push('/RemoteReceptionMenu');
            }
          });
          return;
        }
        this.$router.push({
          path: '/handleOrderList',
          query: { state: '0' },
        })
      })
    },
    // 用户确认平台---走5g平台还是小程序
    remoteSignAuth(busiType) {
      if(window.triggerFrom && window.triggerFrom == 'qw'){
        let param = {
          busiType: busiType,
        }
        this.miniNoticeFlg = 1;
      }
    },
    // 5g远程受理更改购物车逻辑，模板改为shopping_cart的
    busiTypeSwitch(busitype) {
      if(window.triggerFrom && window.triggerFrom == 'qw') return busitype;
      if ((busitype.startsWith('mband') || busitype.startsWith('nettv') || 'zhuti_prod' === busitype)){
        return 'shopping_cart'
      }else if(['promotion_center','zengzhi_prod'].includes(busitype)){
        if(this.distinguishTypes == 'shoppingCartLogic'){
          return 'shopping_cart';
        }else{
          return busitype;
        }
      }else{
        return busitype;
      }
    },
    /**
     * 获取产品列表，格式参考preHandleOrderDetail页面,offername为产品名，efftype为生效时间，offerid为产品id
     */
    async getObjdata(data) {
      this.subscribeInfos = data.subscribeInfos || data
      if (data.unsubscribeInfos) {
        this.unsubscribeInfos = data.unsubscribeInfos
      }

      //5g消息远程办理鉴权类型为AuthChkoriA，小程序为AuthCheckA
      if(window.triggerFrom && window.triggerFrom == 'qw') {
        this.remoteAuthCheck = 'AuthCheckA';
      }else{
        let resData = await qryAbilityConfigByFeature('5GAuth','isChkoriA');
        console.log('5g消息远程办理鉴权类型为AuthChkoriA',resData)
        if(resData.data.retCode =='0'){
          let regionResult = JSON.parse(resData.data.data)
          let result = regionResult[this.userInfo.region]
          if (result == '1') {
            this.remoteAuthCheck = 'AuthChkoriA'
          } else {
            this.remoteAuthCheck = 'AuthCheckR'
          }
        }
      }
    },

    /**
     * 获取其他产品，格式参考preHandleOrderDetail页面,showName为键，value为值
     */
    getExtraJson(data) {
      this.extraJson.otherinfoList = data
    },
    // 算费
    getOrderFee(data){
      this.orderPriceJson=JSON.stringify(data)
    },
    /**
     * 远程签名参数
     */
    //  busiName:业务名称
    //  busitype:业务类型
    //  submitUrl:提交地址
    //  miniNoticeFlg:用户确认走5g平台还是小程序，为1是小程序
    handleOrderReq(busiName, busitype, submitUrl) {
      //发送5G消息参数
      let sendParam = {
        busitype: this.busiTypeSwitch(busitype),
        telnum: this.telnum,
        stafferid: this.userInfo.staffId || '',//岗位工号
        stationId: this.userInfo.stationId || '',//岗位编码
        region: this.userInfo.region,
        crmId: this.userInfo.crmId,
        servNumber: this.userInfo.servNumber, //操作员手机号
        imei: this.userInfo.imei,
        //模板中参数
        busiParam: {
          prodName: this.subscribeInfos.map(prod => prod.offername || prod.offerName).join(','),
        },
        paperlessMod:this.paperlessMod,
        distinguishTypes:this.distinguishTypes
      };
      //提交参数
      let submitParam = JSON.parse(JSON.stringify(this.orderParams));//orderParams为真正提交参数，在业务提交函数中提取
      submitParam.isscmtdId = "Y";
      submitParam.stationId = this.userInfo.stationId || '';
      submitParam.longitude = Storage.get('longitude') || 'empty'
      submitParam.latitude = Storage.get('latitude') || 'empty'
      submitParam.location = Storage.get('location') || 'empty'
      submitParam.studentPhone = this.userInfo.relTelnum
      submitParam.deviceType = getClientType();
      submitParam.authtype = this.remoteAuthCheck;
      submitParam.authType = this.remoteAuthCheck;
      submitParam.authchktype = this.remoteAuthCheck;
      submitParam.authChkType = this.remoteAuthCheck;
      submitParam.authTypeNum = 2;
      submitParam.staffId= this.userInfo.staffId
      submitParam.dwUser= this.userInfo.dwUser

      //封装产品json
      let productsJson = {
        subscribeInfos: this.subscribeInfos,
        unsubscribeInfos: this.unsubscribeInfos,
        infoList: [],
      };
      // 走的是购物车则添加移客助手所需日志，产品名称、产品id
      if(this.distinguishTypes == 'shoppingCartLogic') {
        productsJson.offerInfo = {
          offerId: submitParam.offerId,
          offerName: submitParam.offerName
        }
      }
      // 非购物车添加offerInfo
      if(this.offerInfos) {
        productsJson.offerInfo = this.offerInfos;
      }
      this.subscribeInfos.forEach(prod =>
        productsJson.infoList.push({
          showName: prod.efftype ? prod.efftype : '已选产品（次月生效）', //0 立即生效 1 次日生效 2次月生效
          value: prod.offername ? prod.offername : prod.offerName,
          name: 'subscribeInfo ' + (prod.offerid ? prod.offerid : prod.offerId),
        })
      );
      productsJson.infoList.push({
        showName: '客户手机号',
        value: this.telnum,
        name: 'telnum',
      });
      //预受理请求
      let handleOrderReq = {
        //操作员手机号
        crmId: this.userInfo.crmId, //操作台工号
        region: this.userInfo.region, //地市编码
        servNumber: this.userInfo.servNumber, //操作员手机号
        operatorName: this.userInfo.operatorName, //操作人员姓名
        busiType: busitype, //业务类型
        busiName: busiName, //业务名称
        payType:this.payType,//支付类型
        sendParam: JSON.stringify(sendParam), //发送5G消息时的参数
        submitParams: JSON.stringify(submitParam), //提交参数
        submitUrl: submitUrl, //提交路径
        phoneNumber: this.telnum, //用户手机号
        productsJson: JSON.stringify(productsJson), //产品json
        extraJson: JSON.stringify(this.extraJson), //其他信息
        priceJson:this.orderPriceJson,//费用
        needPaperless: 1, //是否需要无纸化：0不需要，1需要
        state: '1',
        imei: this.userInfo.imei, //5G远程前面需要imei
        source: 'qw' || 'ald',//来源
        qwGroupId: "172817312", //企业微信群ID,当来源是qw时有值
        qwOperId: "wx82e2c31215d9a5a7", //企业微信账号,当来源是qw时有值
        qwCustNumber: '13901582505', //企业微信客户手机号,当来源是qw时有值
        qwCustId: 'zzzzzzsadasfasfaf', //企业微信客户账号,当来源是qw时有值
        qwAgentPhone:Storage.session.get("qw_agentPhone"), //分销人手机号
        qwAgentCustId:Storage.session.get("qw_agentCustId"),//分销人微信id
        miniNoticeFlg: this.miniNoticeFlg,//为1走微信小程序
      };

      //企微端家庭成员代办添加主号(增值、主体产品变更、活动促销)
      if(this.miniNoticeFlg == '1' && ['zengzhi_prod','zhuti_prod','promotion_center'].includes(handleOrderReq.busiType)){
        let jqData = Storage.session.get('jqData');
        handleOrderReq.mainTelnum = jqData.mainTelnum;
      }
      return handleOrderReq
    },
  }
}
