/**
 * 查询开关
 *
 * @param comHttp 传this.$http, 必传
 * @param feaType 开关名
 * @param unLoadFlg 是否关闭加载圈
 * @returns {*}
 */
const qrySwitch = (comHttp, feaType, unLoadFlg) => {
  let url = `/xsb/ability/businessLimit/h5QryBusiPermission`;
  let param = {
    busiType: feaType
  };
  if (unLoadFlg) {
    param.unLoadFlg = true;
  }
  return comHttp.post(url, param);
}

/**
 * 查询操作员减免权限
 *
 * @param comHttp 传this.$http, 必传
 * @param authId 减免令牌
 * @returns {*}
 */
const qryOperatorAuth = (comHttp, authId) =>{
  if (!authId) {
    authId = "6013120191213104501"
  }
  let url = `/xsb/personBusiness/businessOpen/h5qryOperAuth?authId=${authId}`;
  return comHttp.get(url);
}

/**
 * 弱密码校验
 *
 * @param comHttp 传this.$http, 必传
 * @param telnum 电话号码
 * @param password 密码
 * @param type 密码类型
 * @returns {*}
 */
const simplePasswordCheck = (comHttp, telnum, password, type) => {
  let url = `/xsb/personBusiness/BroadbandTvHandle/h5simplePasswordCheck`;
  let param = {
    password: password,
    phone: telnum,
    pwdType: type
  }
  if (!type) {
    param.pwdType = "mbrand";
  }
  return comHttp.post(url, param);
}

/**
 * 返回
 * @param router 传this.$router, 必传
 * @param srcFrom 来源页面
 * @param telNum 手机号
 */
const finalGoBack = (router, srcFrom, telNum) => {
  if (srcFrom === "csView") { //跳回客户视图
    router.push({
      name: 'CsViewIn',
      query: {
        telnum: telNum
      }
    });
  } else if (srcFrom === "tool") { //跳回工具箱
    router.push('/tools');
  } else if (this.srcFrom === "installMaintain") { //跳回AMS装维随销的首页
    router.push('/installMaintain');
  } else if (srcFrom && ~srcFrom.indexOf('/')) {
    router.push(this.srcFrom);
  } else {
    router.push('/business'); //跳回业务
  }
}

export {
  qrySwitch,
  qryOperatorAuth,
  simplePasswordCheck,
  finalGoBack
}
