import _axios from '@/base/nlAxios.js'
import MessageBox from 'components/common/NlMessageBox/message-box.js'
import Storage from '@/base/storage'
import {dateFormat} from '@/base/utils'
import ClientJs from '@/base/clientjs'

/*
* 提取公共的get请求处理逻辑
* url: 请求地址
* isShow: 是否展示错误提示信息
* message: 错误提示信息
* */
const handleAxiosGet = (url, isShow = true, message) => {
  return new Promise((resolve, reject) => {
    _axios.get(url).then(res => {
      const { retCode, retMsg, data } = res.data
      if (retCode === '0') {
        resolve(res.data)
      } else {
        showMessage(retMsg || message || '获取菜单错误', isShow)
        resolve(res.data)
      }
    }).catch(err => {
      showMessage(err || message || '获取菜单错误', isShow)
      reject(new Error(err))
    })
  })
}

/*
* 提取公共的post请求处理逻辑
* url: 请求地址
* params: 请求参数
* isShow: 是否展示错误提示信息
* message: 错误提示信息
* */
const handleAxiosPost = (url, params, isShow = true, message) => {
  return new Promise((resolve, reject) => {
    _axios.post(url, params).then(res => {
      const { retCode, retMsg, data } = res.data
      if (retCode === '0') {
        resolve(data)
      } else {
        showMessage(retMsg || message || '查询统计数据失败', isShow)
        resolve(res.data)
      }
    }).catch(err => {
      showMessage(err || message || '查询统计数据失败', isShow)
      reject(new Error(err))
    })
  })
}

const showMessage = (message, isShow) => {
  if (isShow) {
    MessageBox({
      title: '温馨提示',
      message: message
    })
  }
}

/*
*获取菜单列
* */
const h5getMenuByLevel = (stationId,leave, isShow = false, params) => {
  let url = `/xsb/api-user/menu/h5getMenuByLevel?level=${leave}&stationId=${stationId}`;
  if (params) {
    url += `&${params}`;
  }
  return handleAxiosGet(url, isShow)
}


/**
 * 提交菜单点击日志
 */
const menuLogRecord = (param) => {
  if(!window.breadIds || window.breadIds.length == 0){//没有记录到菜单链则不记日志
    return;
  }
  let url = `/xsb/toolCenter/menuLog/h5menuLogRecord`;
  const uinfo = Storage.session.get('userInfo');
  let httpParam = {
    stationId: uinfo.stationId,
    serverNumber: uinfo.servNumber,
    clickTime: dateFormat(new Date(), ('yyyy-MM-dd hh:mm:ss'))
  }
  Object.assign(httpParam,param);
  return handleAxiosPost(url, httpParam, false);
}

/**
 * 检查是否有截屏权限，存在截屏权限时，开启截屏权限
 * @param currentMenu 请求参数信息
 */
const checkScreenShot = async (currentMenu) => {
    // 获取用户信息
    const uinfo = Storage.session.get('userInfo');
    let staffId = uinfo.staffId;
    let stationId = uinfo.stationId;
    // 获取key
    let key = staffId + "_" + stationId + "_" +"screenShot";
    // 获取当前岗位当前staffid的截屏权限菜单信息
    let screenShotMenus = Storage.session.get(key);
    if (screenShotMenus == null || screenShotMenus == '') {
      // 为空时，进行值的获取
      let httpParam = {
        stationId: stationId,
        busiType: 'screenShot',
      }
      try{
          let url = `/xsb/api-user/fastEntry/h5getScreenshotMenus`;
          const res  = await _axios.post(url, httpParam);
          console.log('h5getScreenshotMenus data', res.data);
          let {retCode, data, retMsg} = res.data;
          if (retCode === '0') {
              screenShotMenus = Array.isArray(data) ? data : "";
              if (screenShotMenus !== null) {
                  // 设置当前岗位当前staffid的截屏权限菜单信息
                  Storage.session.set(key, screenShotMenus);
              }
          }
      }catch (error) {
        console.log('h5getScreenshotMenus error', error)
      }
    }
    // 判断当前菜单是否有有权限
    let currentMenuStr = String(currentMenu);
    // 有权限进行开启截屏
    if (screenShotMenus && screenShotMenus.includes(currentMenuStr)) {
        console.log(currentMenuStr + '开启截屏权限');
        ClientJs.screenShot(1);
    } else {
        ClientJs.screenShot(0);
    }
}





 /**
 * 请求服务端比较用户办理业务的身份证和读证设备读取的身份证号是否一致
 * telnum 用户手机号
 * readSfzId 设备读取的身份证号
*/
 const checkUserSfzId = (telnum,readSfzId)=>{
    let url = '/xsb/personBusiness/personInfo/h5checkUsercust'
    let param = {
        telnum:telnum,//--用户手机号
        idcard:readSfzId//设备读取的身份证号
    }
    return _axios.post(url,param)
 }

/**
  * 查询权限开关
  * busiType 业务类型
 */
const qryBusiPermission = (busiType,axios)=>{
  let url = '/xsb/ability/businessLimit/h5QryBusiPermission'
  let param = {
    busiType,
    unLoadFlg:true
  }
  return axios.post(url,param)
}

/**
 * 查询ZK配置
 * @param {服务名} key
 * @param {类型} itemKey
 * @returns
 */
const qryConfigByFeature = (key,itemKey)=>{
  let url = `/xsb/chatTools/dyconfig/h5get?key=${key}`;
  if(itemKey){
    url += `&itemKey=${itemKey}`;
  }
  return _axios.get(url);
}


/**
 * 查询ZK配置
 * @param {服务名} key
 * @param {类型} itemKey
 * @returns
 */
const qryAbilityConfigByFeature = (key,itemKey)=>{
  let url = `/xsb/ability/dyconfig/h5get?key=${key}`;
  if(itemKey){
    url += `&itemKey=${itemKey}`;
  }
  return _axios.get(url);
}

/**
 * 查询ZK配置
 * @param {查询的服务} service
 * @param {服务名} key
 * @param {类型} itemKey
 * @returns
 */
const qryAutoConfigByFeature = (service,key,itemKey)=>{
  let url = `/xsb/${service}/dyconfig/h5get?key=${key}`;
  if(itemKey){
    url += `&itemKey=${itemKey}`;
  }
  return _axios.get(url);
}



/**
 * 解析语音
 * @returns
 */
const getHttpVoiceTxt = (audioData)=>{
  let url = '/xsb/chatTools/largerModel/h5voiceToText';
  let param = {
    audioData : audioData,
    unEncrpt : true
  }
  return _axios.post(url,param);
}
/**
 * 插入轨迹
 */
const insertTraceRecorder = (traceRecorderContent) =>{
    const userInfo = Storage.session.get('userInfo');
    const param = {
    operatorTelNum: userInfo.servNumber,
    traceRecorderContent: traceRecorderContent,
    stationID: userInfo.stationId,
    source:"maidian",
    unLoadFlg:true
  };
  let url = `/xsb/gridCenter/traceRecorder/h5insertTraceRecorder`;
  _axios.post(url,param).then(res => {
    let {retCode,retMsg} = res.data;
    if (retCode === '0') {
    } else {
      console.info('insertTraceRecorder',retMsg)
    }
  }).catch(err => {
    console.info('insertTraceRecorder',err)
  })
}

export {checkUserSfzId,qryBusiPermission,qryConfigByFeature,getHttpVoiceTxt,qryAbilityConfigByFeature,qryAutoConfigByFeature,h5getMenuByLevel, menuLogRecord, checkScreenShot,insertTraceRecorder}
