import {CONSTVAL} from '@/base/config'
import MessageBox from 'components/common/NlMessageBox/message-box.js';
import { Toast,DatetimePicker } from 'mint-ui';
import Header from 'components/common/Header.vue'
const install = function(Vue, config = {}) {
    if (install.installed) return;
    Vue.config.productionTip = false
    //日期控件
    Vue.component(DatetimePicker.name, DatetimePicker);
    //头部组件
    Vue.component(Header.name, Header);
    // 调用 提示框
    Vue.prototype.$messagebox = MessageBox;
    
    //alert对话框
    Vue.prototype.$alert = AlertMsg;
    
    Vue.$toast = Vue.prototype.$toast = Toast;


    Vue.prototype.CONSTVAL = CONSTVAL;
}

export function installVue(_vue){
    install(_vue)
}

//弹出框公用方法
const AlertMsg = (msg,title) =>{
    if( title === void 0 || title === '' ){
        title = '温馨提示';
    }
    if(msg && msg.length > 300){
        msg = msg.substr(0,300)
    }
    Vue.prototype.$messagebox.alert(msg,title);
}

export const AxiosError = (error,res) =>{
    let retErr = {retCode:'',retMsg:error,data:{}};
    // if(error.code == 'ECONNABORTED' && error.message.indexOf('timeout')!=-1){
    //     return Promise.reject(retErr);

    // }else{
    // }
    return Promise.reject(retErr);
}
 //鉴权失败状态码9999
export const jq9999 = (res) => {
    if(res.data.retCode =='9999'){
        MessageBox.alert(res.data.retMsg,'温馨提示');
    } else {
        return res.data;
    }
}