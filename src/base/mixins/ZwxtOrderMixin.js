import ClientJs from '@/base/clientjs'
import Storage from '@/base/storage.js';


//装维协同单mixin公用方法
export const ZwxtOrderMixin = {
  data() {
    return {
    }
  },
  methods: {

    /**
     * 1-自动结单 2-人工结单 9-转单
     * @param {*} type 
     */
   updateZwOrder(zwOrder) {
    console.log('正在结单');
    console.info('zwOrder',zwOrder);
    let param = {
        zw_id: zwOrder.zwId,
        order_id: zwOrder.order_info_dt.order_id,
        order_status: zwOrder.endType,
        zw_mobile: zwOrder.zwMobile,
        fk_content: zwOrder.fkContent,
        zw_id_new: zwOrder.zwIdNew,
        zw_mobile_new: zwOrder.zwMobileNew,
        regionId: zwOrder.biz_info_dt.city_id,
      };
      let url = '/xsb/personBusiness/zwOrder/h5updateZwOrder';
      console.info('param',param);
      this.$http.post(url, param).then((res) => {
        if (res.data.retCode == '0') {
          console.info('结单data',res.data);
          if (zwOrder.endType == '1' || zwOrder.endType == '2') {
            this.$alert('结单成功');
          } else if (zwOrder.endType == '9') {
            this.$alert('转单成功');
          }
          this.$router.push({
            path: '/zhuangweiCollaborateOrder',
          }); 
        } else {
          this.$alert(res.data.retMsg || '失败');
          this.$router.push({
            path: '/zhuangweiCollaborateOrder',
          }); 
        }
      }).catch((err) => {
        this.$alert(err);
        this.$router.push({
          path: '/zhuangweiCollaborateOrder',
        }); 
      })
   }
    
    
  },

  mounted() {
    
    
  },
};
