import { FAM_CUS_CFG } from 'components/business/perfectorder/perfectOrderConfig'
import VillageAddress from 'components/common/VillageAddress/VillageAddress'
import { chgStrToDate, dateFormat } from '../utils'
import Storage from '../storage'

export const PerfectOrderMixin = {
  data() {
    return {
      isGoNew: false,               //是否走新接口
      step: "select",               //所在页面标识
      jqData: {},
      uinfo: {},
      telNum: "",                   //用户手机号
      userBandInfo: {},             //用户宽带信息
      userOpen: {
        userName: "",               //用户姓名
        userPhone: "",              //用户手机号
        address: "",                //用户地址
        effDate: "",                //生效时间
        isHaveDevice: ""            //是否领取过设备
      },

      templateList: [],
      addValueTemplateList: [],     //增值业务模板
      selectTemplate: {},
      havenOpenBroadband: false,    //是否开通过宽带
      offerInfo: [],
      promotionInfo: [],
      isHasTestFeeTv: false,        //是否包含支持出账模式的产品
      isHasOtherTv: false,          //是否包含不支持出账模式的电视产品
      isHasSoftTv: false,           //是否含有软终端电视产品

      instanceId: "",               //用户宽带实例编码（有线业务逻辑需要）

      propShowInfo: {
        isShowBroadbandProp: false, //是否展示宽带属性页面
        isShowMBandTestFee: false,  //是否展示宽带调测费模式
        isShowTvProp: false,        //是否展示电视属性页面
        isShowTvTestFee: false,     //是否展示电视调测费模式
        isShowSecNetProp: false,    //是否展示安防组网属性页面
        isReceiveEquip: "",         //是否领取设备 0必须领取；1不可领取；2可领取；3已领取
        showChosePonTypeList: false,//是否展示设备列表
        isHasNetwork: false,        //是否包含组网产品
      },

      installInfo: {                //安装信息
        linkName: "",               //联系人
        linkPhone: "",              //联系电话
        isTestFeeMod: "Y",          //调测费模式（宽带）Y:出账模式 N:营收模式
        isTvTestFeeMod: "Y",        //调测费模式（电视）Y:出账模式 N:营收模式
        pwd: "",                    //密码
        installDate: "",            //安装时间
        curPonType: {id: '', label: ''},
        remark: "",                 //备注
      },

      fullName: "点击选择",
      netWorkType: "",
      villageId: "",
      villageName: "",
      villageAddress: "",
      countryId: "",
      addressId: "",
      otherAgent: "",
      constMgr: "",
      radiusFor: "",
      userType: "",
      isSchoolVillage: "",
      ponType: "",
      planCategory: "",
      receiveEquipFlag: "N",      //设备领取标识Y:是； N:否
      chosePonTypeList:[],        //存放设备信息
      needConstruction:'',        //免施工属性

      isHaveAuth: false,    //调测费减免权限
      totalFee: 0,          //总费用
      kdTestDeviceFee: 0,   //宽带调测费
      tvTestDeviceFee: 0,   //电视调测费
      broadbandFeeItem: [], //宽带费用
      invoiceItem: [],      //费用项
      tvFeeItem: [],        //电视费用
      otherFeeItem: [],     //其它费用
      isTelFee: false,      //促销活动是否有费用且费用大于0

      payType: "0",         //支付方式
      newAutoType: "",      //鉴权类型（宽带中进行证件后六位校验后的鉴权方式）

      netTvDictList: [],
      labelTip: "",

      houseTypeSwitch: false,
      haveHouseTypeProd: false,
      roomHall: "",
      homeSize: "",
      communityName: "",

      submitAzUrl: `/xsb/personBusiness/oneClickPro/h5wireTemplateCreateOrderAz`,
      azSubmitUrl: `/xsb/personBusiness/oneClickPro/h5wireTemplateCreateOrderAzFinal`
    }
  },

  methods: {
    qryLabelNetTvProd() {
      let netTvDictStr = Storage.session.get("nettv_dict");
      if (netTvDictStr) {
        this.netTvDictList = netTvDictStr;
        this.qryLabelTip();
      } else {
        let url = `/xsb/personBusiness/perfectOrder/h5qryLabelNetTvProd?dictId=2016&unLoadFlg=true`
        this.$http.get(url).then(res => {
          let {retCode, retMsg, data} = res.data;
          if (retCode === "0") {
            this.netTvDictList = data;
            if (this.netTvDictList && this.netTvDictList.length > 0) {
              sessionStorage.setItem('nettv_dict', JSON.stringify(data));
              this.qryLabelTip();
            }
          } else {
            this.$toast(retMsg || "查询存量电视信息失败")
          }
        }).catch(reason => {
          this.$toast("查询存量电视信息异常，error: " + reason);
        })
      }
    },

    qryLabelTip() {
      if (this.labelTip) {
        return;
      }
      let url = `/xsb/personBusiness/perfectOrder/h5qryLabelTip`;
      let param = {
        telNum: this.telNum,
        unLoadFlg: true
      }
      this.$http.post(url, param).then(res => {
        let {retCode, data} = res.data;
        if (retCode === "0") {
          this.labelTip = data.label;
        } else {
          this.labelTip = "";
        }
      }).catch(() => {
        this.labelTip = "";
      })
    },

    getBusinessSwitch() {
      let url = `/xsb/ability/businessLimit/h5QryBusiPermission`;
      let param = {
        busiType: "go_new_interface",
        unLoadFlg: true
      };
      this.$http.post(url, param).then(res => {
        let {retCode} = res.data;
        this.isGoNew = retCode === "0";
      }).catch(() => {
        this.isGoNew = false;
      })
    },

    //获取用户宽带信息
    qryUserBandInfo() {
      let url = `/xsb/personBusiness/guhua/h5GetBanbInfo`;
      let param = {
        telnum: this.telNum,
        unLoadFlg: true //屏蔽加载圈
      }
      this.$http.post(url, param).then(res => {
        let { retCode, retMsg, data } = res.data;
        if (retCode === "0") {
          this.userBandInfo = data.bandInfo;
          //根据地址判断是否开通过宽带
          if (this.userBandInfo.addrId) {
            this.havenOpenBroadband = true;
            this.communityName = this.userBandInfo.addrName;
          }
          this.userOpen.userName = this.userName;
          this.userOpen.userPhone = this.telNum;
          this.userOpen.effDate = this.userBandInfo.effdate;
          this.userOpen.address = this.userBandInfo.addrName;
          this.userOpen.isHaveDevice = this.userBandInfo.isHaveDevice;
          //查询模板
          this.qryTemplateList();
        } else {
          this.$alert(retMsg || "获取用户宽带信息失败");
        }
      }).catch(reason => {
        this.$alert("获取用户宽带信息异常，error：" + reason);
      })
    },

    /**
     * 查询模板列表
     * @param isShot 是否是增值业务（短流程）
     */
    qryTemplateList(isShot) {
      let url = `/xsb/personBusiness/oneClickPro/h5QryCategoryList`;
      let param = {
        "categoryId": "",
        unLoadFlg: true //屏蔽加载圈
      }
      //判断是不是短流程
      if (isShot) {
        param.categoryId = FAM_CUS_CFG.TEMPLATE_REQ_PARAM.ADD_VALUE;   //增值业务（短流程）
      } else {
        //说明：宽带融合模版的目录是***********;非宽带融合模版的目录是***********
        if(this.havenOpenBroadband) {
          param.categoryId = FAM_CUS_CFG.TEMPLATE_REQ_PARAM.HAVEN_BROADBAND;
          let prodPkgList = [];
          if (this.fromPage === FAM_CUS_CFG.BUSINESS_FLAG.NET_TV) {  //互联网电视
            for (let pkgId of Object.keys(FAM_CUS_CFG.NET_TV_PKG)) {
              let item = {};
              item.offeringId = pkgId;
              prodPkgList.push(item);
            }
            param.tvProdPkgStr = JSON.stringify(prodPkgList);
          } else if (this.fromPage === FAM_CUS_CFG.BUSINESS_FLAG.SECURITY) {  //安防
            for (let pkgId of FAM_CUS_CFG.SECURITY_PKG) {
              let item = {};
              item.offeringId = pkgId;
              prodPkgList.push(item);
            }
            param.tvProdPkgStr = JSON.stringify(prodPkgList);
          } else if (this.fromPage === FAM_CUS_CFG.BUSINESS_FLAG.NETWORK) {   //组网
            for (let pkgId of FAM_CUS_CFG.NETWORK_PKG) {
              let item = {};
              item.offeringId = pkgId;
              prodPkgList.push(item);
            }
            param.tvProdPkgStr = JSON.stringify(prodPkgList);
          }
        } else {
          param.categoryId = FAM_CUS_CFG.TEMPLATE_REQ_PARAM.NO_BROADBAND;
        }
      }
      this.$http.post(url, param).then(res => {
        let { retCode, retMsg, data } = res.data;
        if (retCode === "0") {
          if (isShot) {
            for(let templateItem of data){
              if(templateItem.entryId !== FAM_CUS_CFG.TEMPLATE_REQ_PARAM.ADD_VALUE){
                //获取增值业务模板
                this.addValueTemplateList.push(templateItem);
              }
            }
          } else {
            for(let templateItem of data){
              if(templateItem.entryId !== FAM_CUS_CFG.TEMPLATE_REQ_PARAM.NO_BROADBAND
                && templateItem.entryId !== FAM_CUS_CFG.TEMPLATE_REQ_PARAM.HAVEN_BROADBAND){
                this.templateList.push(templateItem);
              }
            }
            if(this.templateList.length > 0){
              this.selectTemplate = this.templateList[0]; //// 默认第一个模版
            }
          }
        }else {
          this.$alert(retMsg || "获取模版列表失败");
        }
      }).catch(reason => {
        this.$alert("获取模版列表异常，error：" + reason);
      })
    },

    /**
     * 查询模板详情
     *
     * @param templateId 模板编码
     * @param operateFlag 操作类型（判断是选完模板点击下一步，还是进入详情页面） true:点击下一步；false:点击详情
     * @param isShot 是否是增值业务（短流程）
     */
    qryTemplateDetail(templateId, operateFlag, isShot) {
      this.showTopTab = false;
      let url = `/xsb/personBusiness/oneClickPro/h5QryWireTemlDetailById`;
      let param = {
        "temlofferingId": templateId
      }
      this.$http.post(url, param).then(res => {
        let { retCode, retMsg, data } = res.data;
        if (retCode === "0") {
          this.offerInfo = data.offerInfo;
          this.promotionInfo = data.promotionInfo;
          //判断是否选完模板点击下一步
          if (operateFlag) {
            if (this.offerInfo && this.offerInfo.length > 0) {
              let isHasBroadband = false;           //是否有宽带产品
              let isHasTv = false;         //是否有电视产品（包含软终端）
              let isHasSecurity = false;   //是否包含安防产品
              let isHasNetwork = false;    //是否包含组网产品
              let isShowLabel = false;     //是否展示存量电视弹窗
              for (let offerItem of this.offerInfo) {
                //判断是否包含宽带
                if (offerItem.offerid === FAM_CUS_CFG.BROADBAND_PKG) {
                  isHasBroadband = true;
                }
                //判断是否包含电视产品
                let tvItem = FAM_CUS_CFG.NET_TV_PKG[offerItem.offerid];
                if (tvItem) {
                  isHasTv = true;
                  if (tvItem.isHasDevice) {
                    if (tvItem.isTestFee) {
                      this.isHasTestFeeTv = true;
                    } else {
                      this.isHasOtherTv = true;
                    }
                  } else {
                    this.isHasSoftTv = true;
                  }
                }
                //判断是否包含安防产品
                if (FAM_CUS_CFG.SECURITY_PKG.includes(offerItem.offerid)) {
                  isHasSecurity = true;
                }
                //判断是否包含组网产品
                if (FAM_CUS_CFG.NETWORK_PKG.includes(offerItem.offerid)) {
                  isHasNetwork = true;
                  this.haveHouseTypeProd = FAM_CUS_CFG.FTTR_PKG.map(item => item.id).includes(offerItem.offerid)
                }
              }
              if (this.netTvDictList && this.netTvDictList.length > 0) {
                for(let offerItem of this.offerInfo) {
                  for (let labelTvItem of this.netTvDictList) {
                    if (labelTvItem.pkgId) {
                      if (labelTvItem.pkgId === offerItem.offerid) {
                        isShowLabel = true;
                        break;
                      }
                    } else {
                      if (offerItem.subofferInfo && offerItem.subofferInfo.offerInfo && offerItem.subofferInfo.offerInfo.length > 0) {
                        for (let subItem of offerItem.subofferInfo.offerInfo) {
                          let subOfferId = subItem.offerid.substring(2, subItem.offerid.length);
                          if (labelTvItem.prodId === subOfferId) {
                            isShowLabel = true;
                            break;
                          }
                        }
                        if (isShowLabel) {
                          break;
                        }
                      }
                    }
                  }
                  if (isShowLabel) {
                    break;
                  }
                }
              }
              if (isShowLabel && this.labelTip) {
                this.$messagebox({
                  title: '温馨提示',
                  message: this.labelTip,
                  showCancelButton: false,
                  closeOnClickModal: false,
                  closeOnPressEscape: false,
                  confirmButtonText: '确认'
                }).then((action) => {
                  if (action === 'confirm') {
                    //判断选择的模板是不是增值业务（短流程）
                    if (isShot) {
                      //直接算费
                      this.goFee();
                    } else {
                      //判断是否开通过宽带
                      if (this.havenOpenBroadband) {
                        //判断是否走有线业务逻辑且有宽带
                        if (!this.isGoNew && isHasBroadband) {
                          //查询用户实例
                          let url = `/xsb/personBusiness/oneClickPro/h5getMbandInstanceId?telnum=${this.telNum}`;
                          this.$http.get(url).then(res => {
                            let {retCode, retMsg, data} = res.data;
                            if (retCode === "0") {
                              this.instanceId = data.instanceId; // 宽带变更 实列ID参数
                              //跳转到属性页面
                              this.propJump(isHasTv, isHasSecurity, isHasNetwork);
                            } else {
                              this.$alert(retMsg || "查询宽带实列ID失败");
                            }
                          }).catch(reason => {
                            this.$alert( "查询宽带实列ID异常，error：" + reason);
                          })
                        } else {
                          //跳转到属性页面
                          this.propJump(isHasTv, isHasSecurity, isHasNetwork);
                        }
                      } else {
                        //判断是否包含宽带产品
                        if (isHasBroadband) {
                          this.step = "prop";
                          this.propShowInfo.isShowBroadbandProp = true;
                        } else {
                          //直接算费
                          this.goFee();
                        }
                      }
                    }
                  }
                })
              } else {
                //判断选择的模板是不是增值业务（短流程）
                if (isShot) {
                  //直接算费
                  this.goFee();
                } else {
                  //判断是否开通过宽带
                  if (this.havenOpenBroadband) {
                    //判断是否走有线业务逻辑且有宽带
                    if (!this.isGoNew && isHasBroadband) {
                      //查询用户实例
                      let url = `/xsb/personBusiness/oneClickPro/h5getMbandInstanceId?telnum=${this.telNum}`;
                      this.$http.get(url).then(res => {
                        let {retCode, retMsg, data} = res.data;
                        if (retCode === "0") {
                          this.instanceId = data.instanceId; // 宽带变更 实列ID参数
                          //跳转到属性页面
                          this.propJump(isHasTv, isHasSecurity, isHasNetwork);
                        } else {
                          this.$alert(retMsg || "查询宽带实列ID失败");
                        }
                      }).catch(reason => {
                        this.$alert( "查询宽带实列ID异常，error：" + reason);
                      })
                    } else {
                      //跳转到属性页面
                      this.propJump(isHasTv, isHasSecurity, isHasNetwork);
                    }
                  } else {
                    //判断是否包含宽带产品
                    if (isHasBroadband) {
                      this.step = "prop";
                      this.propShowInfo.isShowBroadbandProp = true;
                    } else {
                      //直接算费
                      this.goFee();
                    }
                  }
                }
              }
            } else {
              this.$alert("模版详情为空，禁止办理");
            }
          } else {
            //跳转到详情页面
            this.step = "detail";
          }
        } else {
          this.$alert(retMsg || "模板详情查询失败")
        }
      }).catch(reason => {
        this.$alert("模板详情查询异常, error: " + reason);
      });
    },

    /**
     * 跳转属性页面
     * @param isHasTv 是否有电视产品
     * @param isHasSecurity 是否有安防产品
     * @param isHasNetwork 是否有组网产品
     */
    propJump(isHasTv, isHasSecurity, isHasNetwork) {
      if (isHasTv) {
        this.propShowInfo.isShowTvProp = true;
        this.step = "prop";
        //判断是否仅有支持出账模式的产品
        this.propShowInfo.isShowTvTestFee = this.isHasTestFeeTv && !this.isHasOtherTv && !this.isHasSoftTv;
      } else if (isHasSecurity || isHasNetwork) { //判断是否包含安防组网产品
        this.step = "prop";
        this.propShowInfo.isShowSecNetProp = true;
        this.propShowInfo.isHasNetwork = isHasNetwork;
      } else {
        //直接算费
        this.goFee();
      }
    },

    /**
     * 宽带选址
     */
    openAddr() {
      //选址组件
      VillageAddress(
        {
          popFlag: true,
          telnum: this.jqData.telnum,
          title: "融合受理",
          busiType: this.CONSTVAL.BUSI_TYPE_MBAND, //传宽带的业务类型，走新接口（设备查询校验接口）
        }, (obj) => {
          this.fullName = obj.fullName || obj.villageAddress;
          this.netWorkType = obj.netWorkType;   //网络接入类型
          this.villageId = obj.villageId;       //小区id
          this.villageName = obj.villageName;   //小区名称
          this.villageAddress = obj.villageAddress; //小区地址
          this.countryId = obj.countryId;       //县市地址
          this.addressId = obj.addressId;       //地址id
          this.otherAgent = obj.otherAgent;     //资源提供方 1 移动 2 广电 3 铁通 4 其它运营商 5 复合运营商
          this.constMgr = obj.constMgr;         // 施工方 1 移动 2 广电 3 铁通 4 其它运营商 5 复合运营商
          this.radiusFor = obj.radiusFor;       //Radius归属 1 移动 2 广电 3 铁通 4 其它运营商 5 复合运营商
          this.userType = obj.userType;         //宽带用户类型
          this.communityName = obj.fullName;

          this.propShowInfo.isReceiveEquip = obj.isReceiveEquip; //是否领取设备 0必须领取；1不可领取；2可领取；3已领取
          this.needConstruction = obj.needConstruction;
          //信息初始化
          this.chosePonTypeList = [];
          this.installInfo.curPonType = {id: "", label: ""};
          this.receiveEquipFlag = "N";
          this.propShowInfo.showChosePonTypeList = false;
          if (this.propShowInfo.isReceiveEquip === "0" || this.propShowInfo.isReceiveEquip === "2") {
            let ponTypeList = obj.deviceOfferInfoList || [];
            if (ponTypeList && ponTypeList.length > 0) {
              for (let ponTypeInfo of ponTypeList) {
                let deviceObj = {};
                deviceObj.id = ponTypeInfo.deviceOfferId;
                deviceObj.label = ponTypeInfo.deviceOfferName;
                this.chosePonTypeList.push(deviceObj);
              }
              if(this.chosePonTypeList.length > 0) {
                this.installInfo.curPonType = this.chosePonTypeList[0];
                this.receiveEquipFlag = "Y";
                this.propShowInfo.showChosePonTypeList = true;
              }
            }
          }

          this.ponType = obj.ponType; //PON类型
          this.planCategory = obj.planCategory; //宽带区域类型
          //是否展示宽带调测费模式
          this.propShowInfo.isShowMBandTestFee = this.receiveEquipFlag === "Y" && !this.isHasOtherTv && !this.isHasSoftTv;
        }
      );
    },

    //算费(场景码算费接口)
    goFee() {
      if (this.isGoNew) {
        this.wireTemplateFee();
      } else {
        this.besWireCheck();
      }
    },

    /**
     * 场景码算费接口
     */
    wireTemplateFee() {
      let url = `/xsb/personBusiness/oneClickPro/h5wireTemplatePaymentInfo`;
      let param = {};
      let feeParam = {};
      feeParam.servicenumber = this.telNum;
      feeParam.temlofferingId = this.selectTemplate.entryId;
      let deviceInfo = [];
      if (this.propShowInfo.isShowBroadbandProp && this.receiveEquipFlag === "Y") {    //宽带设备
        let propList = [];
        let broadbandDeviceInfo = {};
        broadbandDeviceInfo.deviceOfferId = this.installInfo.curPonType.id;
        //设备属性信息
        let deviceClassification = {};
        deviceClassification.propcode = "deviceClassification";
        deviceClassification.propvalue = "10";
        let deviceReceive = {};
        deviceReceive.propcode = "deviceReceive";
        deviceReceive.propvalue = "3";
        let pmFamilyDeviceChg = {};
        pmFamilyDeviceChg.propcode = "PM_FamilyDeviceChg";
        pmFamilyDeviceChg.propvalue = "Y";
        let testFeeDiscountType = {};
        testFeeDiscountType.propcode = "TestFeeDiscountType";
        testFeeDiscountType.propvalue = "1";
        //判断是不是10GPON
        if (this.installInfo.curPonType.id === FAM_CUS_CFG.DEVICE_INFO.TEN_GPON) {
          deviceClassification.propvalue = "15";
        }
        propList.push(deviceClassification, deviceReceive, pmFamilyDeviceChg, testFeeDiscountType);

        if (this.propShowInfo.isShowMBandTestFee && this.installInfo.isTestFeeMod === "Y") { //出账模式
          let testFeeMode = {}
          testFeeMode.propcode = "TestFeeMode";
          testFeeMode.propvalue = "1";
          propList.push(testFeeMode);
        }
        broadbandDeviceInfo.propertylist = propList;
        deviceInfo.push(broadbandDeviceInfo);
      }
      if (this.isHasOtherTv || this.isHasTestFeeTv) {               //电视设备
        let tvDevice = {
          deviceOfferId: FAM_CUS_CFG.DEVICE_INFO.NET_TV,
          propertylist: [
            { propcode: "deviceClassification", propvalue: "1" },
            { propcode: "deviceReceive", propvalue: "3" },
            { propcode: "PM_FamilyDeviceChg", propvalue: "Y" },
            { propcode: "TestFeeDiscountType", propvalue: "1" }
          ]
        };
        if (this.propShowInfo.isShowMBandTestFee && this.installInfo.isTestFeeMod === "Y"
          || this.propShowInfo.isShowTvTestFee && this.installInfo.isTvTestFeeMod === "Y") {
          let testFeeMode = {
            propcode: "TestFeeMode",
            propvalue: "1"
          }
          tvDevice.propertylist.push(testFeeMode);
        }
        deviceInfo.push(tvDevice);
      }
      if (deviceInfo && deviceInfo.length > 0) {
        feeParam.deviceInfo = deviceInfo;
      }

      param.paymentParam = JSON.stringify(feeParam);

      this.$http.post(url, param).then(res => {
        let { retCode, retMsg, data } = res.data;
        if (retCode === "0") {
          if (data.orderItem && data.orderItem.length > 0) {
            for (let feeItem of data.orderItem) {
              if (feeItem.itempriceinfo && feeItem.itempriceinfo.length > 0) {
                for (let priceItem of feeItem.itempriceinfo) {
                  let invoice = {};
                  invoice.itemId = feeItem.itemid;
                  invoice.chargecode = priceItem.chargecode;
                  invoice.priceamount = priceItem.priceamount;
                  this.invoiceItem.push(invoice);     //封装费用项
                  //获取宽带调测费
                  if (feeItem.itemid === FAM_CUS_CFG.BROADBAND_PKG
                    || feeItem.itemid === FAM_CUS_CFG.DEVICE_INFO.HOME_GATEWAY
                    || feeItem.itemid === FAM_CUS_CFG.DEVICE_INFO.TEN_GPON) {
                    if (priceItem.chargecode === "DeviceTestFee") {
                      this.kdTestDeviceFee = parseInt(priceItem.priceamount);
                    }
                    this.broadbandFeeItem.push(priceItem);
                  } else if (feeItem.itemid === FAM_CUS_CFG.DEVICE_INFO.NET_TV) {    //获取电视调测费
                    if (priceItem.chargecode === "DeviceTestFee") {
                      this.tvTestDeviceFee = parseInt(priceItem.priceamount);
                    }
                    this.tvFeeItem.push(priceItem);
                  } else {                                                //其他费用
                    this.otherFeeItem.push(priceItem);
                  }
                }
              }
            }
          }
          //跳转到算费页面
          this.step = "fee";
          this.totalFee = parseInt(data.totalFee);
          //获取调测费减免权限
          this.isHaveAuth = data.isHaveAuth && data.isHaveAuth === "1";
          if (this.propShowInfo.isShowBroadbandProp) {
            this.getCustomInfo();
          }
        } else {
          this.$alert(retMsg || "算费失败");
        }
      }).catch(reason => {
        this.$alert("算费接口调用异常, error: " + reason);
      });
    },

    //查询客户的地市信息
    getCustomInfo(){
      let url = `/xsb/personBusiness/customerView/h5CustomerInfo?telnum=${this.jqData.telnum}&unLoadFlg=true`;
      this.$http.get(url).then((res)=>{
        let {retCode, retMsg, data} = res.data;
        if(retCode === "0"){
          if (data.certTypeName) {
            //获取证件类型
            this.$refs.fee.certTypeName = data.certTypeName;
          }
        }else{
          this.$toast(retMsg || "获取客户资料失败");
        }
      }).catch(reason => {
        this.$toast("查询客户信息异常，error: " + reason);
      });
    },

    /**
     * 证件后六位校验
     * @param lastSix 证件后六位
     */
    lastSixCheck(lastSix) {
      let url = `/xsb/personBusiness/businessOpen/h5chkLast6digitsOfCertId`;
      let param= {
        serviceNumber: this.jqData.telnum,
        last6digitsOfCertId: lastSix, //证件后六位
        beId: this.jqData.userCity  //用户地市
      }
      this.$http.post(url, param).then(res => {
        let { retCode, retMsg } = res.data;
        if (retCode === "0") {
          this.$refs.fee.isShowPrompt = false;
          if (this.jqData.authtype === "06") {  //服务密码
            this.newAutoType = "AuthCheckBWLS"  //服务密码+身份证后六位
          } else if (this.jqData.authtype === "07") { //短信验证码
            this.newAutoType = "AuthCheckAWLS"; //短信验证码+身份证后六位
          }
          this.$refs.fee.isCardCheck = true;
          //完美一单提交
          this.perfectOrderSubmit();
        } else {
          this.$alert(retMsg || "身份证后六位校验失败！");
        }
      }).catch(reason => {
        this.$alert("身份证后六位校验接口调用异常，error: " + reason);
      })
    },

    //属性封装
    getPropInfo() {
      let propInfoList = [];
      let radiusType = {};                        //Radius归属 1 移动 2 广电 3 铁通 4 其它运营商 5 复合运营商
      radiusType.propcode = "radiusType";
      radiusType.propvalue = this.radiusFor;
      let networkType = {};                       //网络接入方式
      networkType.propcode = "networkType";
      networkType.propvalue = this.netWorkType;
      let callFor = {};                           //区县编码
      callFor.propcode = "callFor";
      callFor.propvalue = this.countryId;
      let needConstruction = {};                  //是否需要施工
      needConstruction.propcode = "needConstruction";
      needConstruction.propvalue = "";
      let gimsUserType = {};                      //gims用户类型
      gimsUserType.propcode = "gimsUserType";
      gimsUserType.propvalue = this.userType;
      let gimsAreaType = {};                      //gims地址类型
      gimsAreaType.propcode = "gimsAreaType";
      gimsAreaType.propvalue = this.planCategory;
      let ponType = {};                           //PON类型
      ponType.propcode = "ponType";
      ponType.propvalue = this.ponType;
      let factoryType = {};                       //施工方 1 移动 2 广电 3 铁通 4 其它运营商 5 复合运营商
      factoryType.propcode = "factoryType";
      factoryType.propvalue = this.constMgr;
      let supplyType = {};                        //资源提供方 1 移动 2 广电 3 铁通 4 其它运营商 5 复合运营商
      supplyType.propcode = "supplyType";
      supplyType.propvalue = this.otherAgent;
      let isCurrentDayFinish = {};
      isCurrentDayFinish.propcode = "isCurrentDayFinish";
      isCurrentDayFinish.propvalue = "";
      let bandpassword = {};                      //宽带密码
      bandpassword.propcode = "bandpassword";
      bandpassword.propvalue = this.installInfo.pwd;
      //开通过宽带从接口返回中获取相关属性值
      if (!this.propShowInfo.isShowBroadbandProp) {
        radiusType.propvalue = this.userBandInfo.radiusType;
        networkType.propvalue = this.userBandInfo.netWorkType;
        gimsUserType.propvalue = this.userBandInfo.gimsUserType;
        gimsAreaType.propvalue = this.userBandInfo.gimsAreaType;
        factoryType.propvalue = this.userBandInfo.factoryType;
        supplyType.propvalue = this.userBandInfo.supplyType;
      }
      //属性值为空，不传该属性节点
      if (radiusType.propvalue) {
        propInfoList.push(radiusType);
      }
      if (networkType.propvalue) {
        propInfoList.push(networkType);
      }
      if (callFor.propvalue) {
        propInfoList.push(callFor);
      }
      if (needConstruction.propvalue) {
        propInfoList.push(needConstruction);
      }
      if (gimsUserType.propvalue) {
        propInfoList.push(gimsUserType);
      }
      if (gimsAreaType.propvalue) {
        propInfoList.push(gimsAreaType);
      }
      if (ponType.propvalue) {
        propInfoList.push(ponType);
      }
      if (factoryType.propvalue) {
        propInfoList.push(factoryType);
      }
      if (supplyType.propvalue) {
        propInfoList.push(supplyType);
      }
      if (isCurrentDayFinish.propvalue) {
        propInfoList.push(isCurrentDayFinish);
      }
      if (bandpassword.propvalue) {
        propInfoList.push(bandpassword);
      }
      return propInfoList;
    },

    perfectOrderSubmit() {
      if (this.isGoNew) {
        //场景码提交接口
        this.wireTemplateCreateOrder();
      } else {
        //有线业务提交接口
        this.besWireSubmit();
      }

    },

    /**
     * 场景码提交接口
     */
    wireTemplateCreateOrder() {
      let createOrderParam = {}
      createOrderParam.servicenumber = this.telNum;
      createOrderParam.authType = this.jqData.crmAuthType;
      //支付信息
      let payTypeDict = "";
      let payMethodDict = "";
      if(this.payType === "2"){ //话费支付
        payTypeDict = "2";
        payMethodDict = "9000";
      }else if(this.payType === '0'){ //现金支付
        payTypeDict = "1";
        payMethodDict = "1000";
      }
      let orderPayment = {}
      orderPayment.paytype = payTypeDict;
      orderPayment.paymethod = payMethodDict;
      orderPayment.payamount = this.totalFee;
      createOrderParam.orderpayment = orderPayment;
      //费用项信息
      let orderBusinessFee = {}
      orderBusinessFee.invoiceitem = this.invoiceItem;
      createOrderParam.orderbusinessfee = orderBusinessFee;
      //联系人信息
      let contactInfo = {}
      contactInfo.contactPhone = this.installInfo.linkPhone;
      contactInfo.linkMan = this.installInfo.linkName;
      contactInfo.appointDate = dateFormat(chgStrToDate(this.installInfo.installDate), "yyyyMMddhhmmss");
      contactInfo.remark = this.installInfo.remark;
      createOrderParam.contactinfo = contactInfo;
      //地址信息
      let addressInfo = {};
      if (this.propShowInfo.isShowBroadbandProp) {
        addressInfo.addrid = this.addressId;
        addressInfo.addrname = this.fullName;
        addressInfo.districtid = this.villageId;
        addressInfo.districtname = this.villageName;
      } else {
        addressInfo.addrid = this.userBandInfo.addrId;
        addressInfo.addrname = this.userBandInfo.addrName;
        addressInfo.districtid = this.userBandInfo.districtId;
        addressInfo.districtname = this.userBandInfo.districtName;
      }
      createOrderParam.addressinfo = addressInfo;
      //模板信息
      let templateInfo = {};
      templateInfo.temlofferingId = this.selectTemplate.entryId;
      //获取属性信息
      if (this.propShowInfo.isShowBroadbandProp || this.propShowInfo.isShowTvProp || this.propShowInfo.isShowSecNetProp) {
        let propList = this.getPropInfo();
        if (propList && propList.length > 0) {
          templateInfo.propertylist = propList;
        }
      }
      createOrderParam.temlofferinginfo = templateInfo;
      //设备信息（宽带、电视）
      let deviceInfo = [];
      if (this.propShowInfo.isShowBroadbandProp && this.receiveEquipFlag === "Y") {
        let broadbandDeviceInfo = {}
        broadbandDeviceInfo.deviceOfferId = this.installInfo.curPonType.id;
        let propertyList = [];
        if (this.propShowInfo.isShowMBandTestFee && this.installInfo.isTestFeeMod === "Y") {
          let testFeeMode = {};
          testFeeMode.propcode = "TestFeeMode";
          testFeeMode.propvalue = "1";
          propertyList.push(testFeeMode);
        }
        broadbandDeviceInfo.propertylist = propertyList;
        deviceInfo.push(broadbandDeviceInfo);
      }
      if (this.isHasOtherTv || this.isHasTestFeeTv) {
        let tvDeviceInfo = {}
        tvDeviceInfo.deviceOfferId = FAM_CUS_CFG.DEVICE_INFO.NET_TV;  //电视设备
        let propertyList = [];
        if (this.propShowInfo.isShowMBandTestFee && this.installInfo.isTestFeeMod === "Y"
          || this.propShowInfo.isShowTvTestFee && this.installInfo.isTvTestFeeMod === "Y") {
          let testFeeMode = {}
          testFeeMode.propcode = "TestFeeMode";
          testFeeMode.propvalue = "1";
          propertyList.push(testFeeMode)
        }
        tvDeviceInfo.propertylist = propertyList;
        deviceInfo.push(tvDeviceInfo);
      }
      if (deviceInfo && deviceInfo.length > 0) {
        createOrderParam.deviceInfo = deviceInfo;
      }

      let param = {};
      param.msisdn = this.telNum;

      param.authChkType = this.jqData.authtype;
      //宽带开通中进行证件后六位校验时，传新的鉴权方式
      if (this.propShowInfo.isShowBroadbandProp && (this.jqData.authtype === "06" || this.jqData.authtype === "07")) {
        param.authChkType = this.newAutoType;
        createOrderParam.authType = this.newAutoType;
      }
      param.createOrderParam = JSON.stringify(createOrderParam);
      param.cardAuthSrl = this.jqData.cardAuthSrl;
      param.payType = this.payType;
      param.totalFee = this.totalFee;
      param.busiType = FAM_CUS_CFG.BUSI_TYPE.PERFECT_ORDER;
      param.type = /android|harmony/gi.test(navigator.userAgent) ? 'ANDROID' : 'IOS';
      param.stationId = this.uinfo.stationId;
      param.longitude = Storage.get('longitude');
      param.latitude = Storage.get('latitude');
      param.location = Storage.get('location');
      param.operatorName = this.uinfo.operatorName;
      let offerId = this.offerInfo[0].offerid;
      let offerName = this.offerInfo[0].offerName;
      //offerId,offerName传商品包编码和名称，活动取第一个，用"_"拼接
      if (this.offerInfo.length > 1) {
        for (let i = 1; i < this.offerInfo.length; i++) {
          offerId = offerId + "_" + this.offerInfo[i].offerid;
          offerName = offerName + "€€" + this.offerInfo[i].offerName;
        }
      }
      if (this.promotionInfo && this.promotionInfo.length > 0) {
        offerId = offerId + "_" + this.promotionInfo[0].promotionId;
        offerName = offerName + "€€" + this.promotionInfo[0].promName;
      }
      //若offerId或offerName长度过长传模板编码和模板名称
      if (offerId.length > 100 || offerName.length > 500) {
        offerId = this.selectTemplate.entryId;
        offerName = this.selectTemplate.entryName;
      }
      param.offerId = offerId;
      param.offerName = offerName;
      //爱知预提交
      this.$parent.azSubmitUrl = `/xsb/personBusiness/oneClickPro/h5wireTemplateCreateOrderAzFinal`;
      this.$parent.ySubmit(this.submitAzUrl, param, true);
    },

    /*******************************************有线业务接口************************************************************/

    /**
     * 有线业务校验、提交接口属性封装
     * @param isSubmit 是否是提交
     * @returns {*[]}
     */
    wireCheckPropParam(isSubmit) {
      let orderItemPropList = [];
      let supplyTypeItem = {}             //宽带提供方
      supplyTypeItem.propcode = "supplyType";
      supplyTypeItem.svalue = this.otherAgent;
      supplyTypeItem.propacttype = "A";
      let addrIdItem = {}                 //地址编码
      addrIdItem.propcode = "addrId";
      addrIdItem.svalue = this.addressId;
      addrIdItem.propacttype = "A";
      let addrNameItem = {}               //地址名称
      addrNameItem.propcode = "addrName";
      addrNameItem.svalue = this.fullName;
      addrNameItem.propacttype = "A";
      let areaTypeItem = {}               //地区类型
      areaTypeItem.propcode = "areaType";
      areaTypeItem.svalue = this.planCategory;
      areaTypeItem.propacttype = "A";
      let contactPhoneItem = {}           //联系人电话
      contactPhoneItem.propcode = "contactPhone";
      contactPhoneItem.svalue = this.installInfo.linkPhone;
      contactPhoneItem.propacttype = "A";
      let districtIdItem = {}             //小区编码
      districtIdItem.propcode = "districtId";
      districtIdItem.svalue = this.villageId;
      districtIdItem.propacttype = "A";
      let districtNameItem = {}           //小区名称
      districtNameItem.propcode = "districtName";
      districtNameItem.svalue = this.villageName;
      districtNameItem.propacttype = "A";
      let factoryTypeItem = {}            //施工厂商类型
      factoryTypeItem.propcode = "factoryType";
      factoryTypeItem.svalue = this.constMgr;
      factoryTypeItem.propacttype = "A";
      let gimsAreaTypeItem = {}           //地区类型
      gimsAreaTypeItem.propcode = "gimsAreaType";
      gimsAreaTypeItem.svalue = this.planCategory;
      gimsAreaTypeItem.propacttype = "A";
      let gimsUserTypeItem = {}           //宽带用户类型
      gimsUserTypeItem.propcode = "gimsUserType";
      gimsUserTypeItem.svalue = this.userType;
      gimsUserTypeItem.propacttype = "A";
      let linkManItem = {}                //联系人名字
      linkManItem.propcode = "linkMan";
      linkManItem.svalue = this.installInfo.linkName;
      linkManItem.propacttype = "A";
      let netWorkTypeItem = {}            //网络类型
      netWorkTypeItem.propcode = "networkType";
      netWorkTypeItem.svalue = this.netWorkType;
      netWorkTypeItem.propacttype = "A";
      let passWordItem = {}               //宽带密码
      passWordItem.propcode = "password";
      passWordItem.svalue = this.installInfo.password;
      passWordItem.propacttype = "A";
      let radiusTypeItem = {}             //radius归属}
      radiusTypeItem.propcode = "radiusType";
      radiusTypeItem.svalue = this.radiusFor;
      radiusTypeItem.propacttype = "A";
      let rangeTypeItem = {}              //地址是否具备开通条件
      rangeTypeItem.propcode = "rangeType";
      rangeTypeItem.svalue = "1";
      rangeTypeItem.propacttype = "A";
      let userTypeItem = {}               //地址是否具备开通条件
      userTypeItem.propcode = "userType";
      userTypeItem.svalue = this.userType;
      userTypeItem.propacttype = "A";
      let ponTypeItem = {}                //地址是否具备开通条件
      ponTypeItem.propcode = "PonType";
      ponTypeItem.svalue = this.ponType;
      ponTypeItem.propacttype = "A";
      let linktelItem = {}                  //联系人手机号
      linktelItem.propcode = "linktel";
      linktelItem.svalue = this.installInfo.linkPhone;
      linktelItem.propacttype = "A";
      let mbrandpwdItem = {}                //属性编码 密码
      mbrandpwdItem.propcode = "mbrandpwd";
      mbrandpwdItem.svalue = this.installInfo.pwd;
      mbrandpwdItem.propacttype = "A";
      let installDateItem = {}              //预约安装时间
      installDateItem.propcode = "installtime";
      installDateItem.svalue = this.installInfo.installDate;
      installDateItem.propacttype = "A";
      let callForItem = {}                //县市编码
      callForItem.propcode = "callFor";
      callForItem.svalue = this.countryId;
      callForItem.propacttype = "A";
      if (!this.propShowInfo.isShowBroadbandProp) {
        supplyTypeItem.svalue = this.userBandInfo.supplyType;
        addrIdItem.svalue = this.userBandInfo.addrId;
        addrNameItem.svalue = this.userBandInfo.addrName;
        areaTypeItem.svalue = this.userBandInfo.gimsAreaType;
        districtIdItem.svalue = this.userBandInfo.districtId;
        districtNameItem.svalue = this.userBandInfo.districtName;
        factoryTypeItem.svalue = this.userBandInfo.factoryType;
        gimsAreaTypeItem.svalue = this.userBandInfo.gimsAreaType;
        gimsUserTypeItem.svalue = this.userBandInfo.gimsUserType;
        netWorkTypeItem.svalue = this.userBandInfo.netWorkType;
        radiusTypeItem.svalue = this.userBandInfo.radiusType;
        rangeTypeItem.svalue = this.userBandInfo.rangeType;
        userTypeItem.svalue = this.userBandInfo.gimsUserType;
      }
      if (supplyTypeItem.svalue) {
        orderItemPropList.push(supplyTypeItem);
      }
      if (addrIdItem.svalue) {
        orderItemPropList.push(addrIdItem)
      }
      if (addrNameItem.svalue) {
        orderItemPropList.push(addrNameItem);
      }
      if (areaTypeItem.svalue) {
        orderItemPropList.push(areaTypeItem, gimsAreaTypeItem);
      }
      if (districtIdItem.svalue) {
        orderItemPropList.push(districtIdItem);
      }
      if (districtNameItem.svalue) {
        orderItemPropList.push(districtNameItem);
      }
      if (factoryTypeItem.svalue) {
        orderItemPropList.push(factoryTypeItem);
      }
      if (gimsUserTypeItem.svalue) {
        orderItemPropList.push(gimsUserTypeItem, userTypeItem)
      }
      if (netWorkTypeItem.svalue) {
        orderItemPropList.push(netWorkTypeItem);
      }
      if (passWordItem.svalue && this.propShowInfo.isShowBroadbandProp) {
        orderItemPropList.push(passWordItem, mbrandpwdItem)
      }
      if (radiusTypeItem.svalue) {
        orderItemPropList.push(radiusTypeItem);
      }
      if (ponTypeItem.svalue) {
        orderItemPropList.push(ponTypeItem);
      }
      if (callForItem.svalue) {
        orderItemPropList.push(callForItem);
      }
      if (isSubmit) {
        let noteItem = {} //备注
        noteItem.propcode = "note";
        noteItem.svalue = this.installInfo.remark;
        noteItem.propacttype = "A";
        let notesItem = {} //备注
        notesItem.propcode = "notes";
        notesItem.svalue = this.installInfo.remark;
        notesItem.propacttype = "A";
        let amsStaff = {}      //代维工号
        amsStaff.propcode = "amsStaff";
        amsStaff.svalue = this.uinfo.dwUser;
        amsStaff.propacttype = "A";
        orderItemPropList.push(noteItem, notesItem);
        if (this.uinfo.dwUser) {
          orderItemPropList.push(amsStaff);
        }
      }
      orderItemPropList.push(contactPhoneItem, linktelItem, linkManItem, rangeTypeItem, installDateItem);

      return orderItemPropList;
    },

    /**
     * 封装业务商品信息和促销活动信息（有线业务校验、提交接口）
     * @param isSubmit 是否是提交接口
     * @returns {{orderItemList: *[], promotionList: *[]}}
     */
    getProdParam(isSubmit) {
      let result = {
        orderItemList: [],
        promotionList: []
      }

      //业务商品信息
      let orderItemList = [];
      let deviceOrderItemList = [];
      if (this.offerInfo && this.offerInfo.length > 0) {
        let tvProd = ""
        for (let offerItem of this.offerInfo) {
          let orderItemInfo = {};
          orderItemInfo.itemid = offerItem.offerid;
          orderItemInfo.actiontype = "A";
          orderItemInfo.effectmode = offerItem.effectType === "1"? "0": "2";
          //判断是不是存量宽带场景
          if (!this.propShowInfo.isShowBroadbandProp && offerItem.offerid === FAM_CUS_CFG.BROADBAND_PKG) {
            orderItemInfo.instanceid = this.instanceId;
            orderItemInfo.actiontype = "M";
          }
          if (offerItem.subofferInfo && offerItem.subofferInfo.offerInfo && offerItem.subofferInfo.offerInfo.length > 0) {
            let subOrderItem = {};
            let subOrderItemList = [];

            for (let subOfferInfo of offerItem.subofferInfo.offerInfo) {
              let offerId = subOfferInfo.offerid.substring(2, subOfferInfo.offerid.length)
              let subOrderItem = {};
              subOrderItem.itemid = offerId;
              subOrderItem.actiontype = "A";
              subOrderItem.effectmode = "";
              //判断是不是功能产品
              if (subOfferInfo.offerid.substring(0, 1) === 'N') {
                //判断是不是宽带的功能产品，且有设备
                if (offerItem.offerid === FAM_CUS_CFG.BROADBAND_PKG && this.propShowInfo.isShowBroadbandProp && this.receiveEquipFlag === "Y") {
                  let orderItemRelation = {}
                  orderItemRelation.itemid = offerId;
                  orderItemRelation.relationtype = "RE";
                  orderItemRelation.deviceId = this.installInfo.curPonType.id;
                  orderItemInfo.orderitemrelation =orderItemRelation;
                  //宽带设备信息
                  let broadbandDeviceInfo = {};
                  broadbandDeviceInfo.itemid = this.installInfo.curPonType.id;
                  broadbandDeviceInfo.actiontype = "A";
                  broadbandDeviceInfo.effectmode = "0";

                  let deviceRelation = {};
                  deviceRelation.itemid = offerId;
                  deviceRelation.relationtype = "RE";
                  deviceRelation.deviceId = "";
                  broadbandDeviceInfo.orderitemrelation = deviceRelation;

                  let devicePropItemList = [];
                  let deviceReceive = {};
                  deviceReceive.propacttype = "A";
                  deviceReceive.propcode = "deviceReceive";
                  deviceReceive.svalue = "3";

                  let deviceClassification = {};
                  deviceClassification.propacttype = "A";
                  deviceClassification.propcode = "deviceClassification";
                  deviceClassification.svalue = "10";
                  //判断是不是10GPON
                  if (this.installInfo.curPonType.id === FAM_CUS_CFG.DEVICE_INFO.TEN_GPON) {
                    deviceClassification.svalue = "15";
                  }
                  devicePropItemList.push(deviceReceive, deviceClassification);
                  //宽带出账模式
                  if (this.propShowInfo.isShowMBandTestFee && this.installInfo.isTestFeeMod === "Y") {
                    let testFeeMode = {};
                    testFeeMode.propcode = "TestFeeMode";
                    testFeeMode.svalue = "1";
                    testFeeMode.propacttype = "A";
                    devicePropItemList.push(testFeeMode);
                  }

                  broadbandDeviceInfo.orderitemprop = devicePropItemList;
                  deviceOrderItemList.push(broadbandDeviceInfo)

                } else if (FAM_CUS_CFG.NET_TV_PKG[offerItem.offerid] && FAM_CUS_CFG.NET_TV_PKG[offerItem.offerid].isHasDevice) { //判断是不是电视的功能产品，且有设备
                  tvProd = offerId;
                  let orderItemRelation = {}
                  orderItemRelation.itemid = offerId;
                  orderItemRelation.relationtype = "RE";
                  orderItemRelation.deviceId = FAM_CUS_CFG.DEVICE_INFO.NET_TV;
                  orderItemInfo.orderitemrelation =orderItemRelation;

                  //电视设备信息
                  let tvDeviceInfo = {};
                  tvDeviceInfo.itemid = FAM_CUS_CFG.DEVICE_INFO.NET_TV;
                  tvDeviceInfo.actiontype = "A";
                  tvDeviceInfo.effectmode = "0";

                  let deviceRelation = {};
                  deviceRelation.itemid = offerId;
                  deviceRelation.relationtype = "RE";
                  deviceRelation.deviceId = "";
                  tvDeviceInfo.orderitemrelation = deviceRelation;

                  let devicePropItemList = [];
                  let deviceReceive = {};
                  deviceReceive.propacttype = "A";
                  deviceReceive.propcode = "deviceReceive";
                  deviceReceive.svalue = "3";

                  let deviceClassification = {};
                  deviceClassification.propacttype = "A";
                  deviceClassification.propcode = "deviceClassification";
                  deviceClassification.svalue = "1";
                  devicePropItemList.push(deviceReceive, deviceClassification);
                  //电视出账模式
                  if (this.propShowInfo.isShowMBandTestFee && this.installInfo.isTestFeeMod === "Y"
                    || this.propShowInfo.isShowTvTestFee && this.installInfo.isTvTestFeeMod === "Y") {
                    let testFeeMode = {}
                    testFeeMode.propcode = "TestFeeMode";
                    testFeeMode.svalue = "1";
                    testFeeMode.propacttype = "A";
                    devicePropItemList.push(testFeeMode);
                  }
                  tvDeviceInfo.orderitemprop = devicePropItemList;
                  deviceOrderItemList.push(tvDeviceInfo);
                }
                let orderItemPropList = []
                if ((offerItem.offerid === FAM_CUS_CFG.BROADBAND_PKG && this.propShowInfo.isShowBroadbandProp)
                  || (FAM_CUS_CFG.NET_TV_PKG[offerItem.offerid] && this.propShowInfo.isShowTvProp)
                  || ((FAM_CUS_CFG.SECURITY_PKG.includes(offerItem.offerid) || FAM_CUS_CFG.NETWORK_PKG.includes(offerItem.offerid)) && this.propShowInfo.isShowSecNetProp)) {
                  //属性信息封装
                  orderItemPropList = this.wireCheckPropParam(isSubmit);

                  let addrReference = {};
                  addrReference.relaid = this.addressId;
                  if (!this.propShowInfo.isShowBroadbandProp) {
                    addrReference.relaid = this.userBandInfo.addrId;
                  }
                  subOrderItem.addrreference = addrReference;
                }
                let isHasFttr = FAM_CUS_CFG.FTTR_PKG.map(item => item.id).includes(offerItem.offerid);
                if (this.haveHouseTypeProd && this.houseTypeSwitch && isHasFttr) {
                  if (this.roomHall && this.homeSize) {
                    let roomHallProp = {
                      propacttype: "A",
                      propcode: "Userlayout",
                      svalue: this.roomHall
                    }

                    let roomSizeProp = {
                      propacttype: "A",
                      propcode: "UnitSize",
                      svalue: this.homeSize
                    }
                    orderItemPropList.push(roomHallProp, roomSizeProp);
                  }
                }
                subOrderItem.orderitemprop = orderItemPropList;
              }
              if (subOrderItem.itemid !== "2400000979") {
                subOrderItemList.push(subOrderItem);
              }
            }
            //在首次开通宽带中，非校园用户传979的商品，校园用户不传
            if (this.propShowInfo.isShowBroadbandProp) {
              if (this.userType !== "2" && offerItem.offerid === FAM_CUS_CFG.BROADBAND_PKG) {
                let school979 = {};
                school979.itemid = "2400000979";
                school979.actiontype = "A";
                school979.effectmode = "";
                subOrderItemList.push(school979);
              }
            }
            subOrderItem.orderitem = subOrderItemList;
            orderItemInfo.suborderitem = subOrderItem;
          }
          orderItemList.push(orderItemInfo);
        }

        if (deviceOrderItemList && deviceOrderItemList.length > 0) {
          orderItemList = orderItemList.concat(deviceOrderItemList);
        }
        result.orderItemList = orderItemList;
      }

      //促销活动信息
      if (this.promotionInfo && this.promotionInfo.length > 0) {
        let promotionList = [];
        for (let promotionItem of this.promotionInfo) {
          let promItem = {};
          promItem.promotion_id = promotionItem.promotionId;
          promItem.effect_type = promotionItem.effectType;
          if (promotionItem.promMember && promotionItem.promMember.length > 0) {
            let promMemberList = [];
            for (let promMemberItem of promotionItem.promMember) {
              let  promMemberInfo = {};
              promMemberInfo.adaptation_id = promMemberItem.adaptationId;
              promMemberInfo.member_action_type = "A";
              promMemberInfo.member_entity_id = promMemberItem.memberEntityId;
              promMemberList.push(promMemberInfo);
            }
            promItem.prom_member_list = promMemberList;
          }
          promotionList.push(promItem)
        }
        result.promotionList = promotionList;
      }
      return result;
    },

    /**
     * 有线业务校验
     */
    besWireCheck() {
      let url = `/xsb/personBusiness/oneClickPro/h5besOrderCheck`;
      let param = {};
      param.userRegion = this.jqData.userCity;  //用户地市
      param.telNum = this.telNum;               //手机号

      let checkParam = {};
      if (this.propShowInfo.isShowBroadbandProp) {
        let addressInfo = {};
        addressInfo.relaid = this.addressId;
        addressInfo.addrid = this.addressId;
        addressInfo.addrname = this.fullName;
        addressInfo.districtid = this.villageId;
        addressInfo.districtname = this.villageName;
        checkParam.addressinfo = addressInfo;
      }
      //封装业务商品信息和促销活动信息
      let {orderItemList, promotionList} = this.getProdParam(false);
      //业务商品信息
      checkParam.orderItemList = orderItemList;
      //促销活动信息
      checkParam.promotion_list = promotionList;
      param.checkStr = JSON.stringify(checkParam);
      this.$http.post(url, param).then(res => {
        let {retCode, retMsg} = res.data;
        if (retCode === "0") {
          //有线业务算费
          this.besWireFee();
        } else {
          this.$alert(retMsg || "有线业务校验失败！");
        }
      }).catch(reason => {
        this.$alert("有线业务校验异常，error:" + reason);
      })
    },

    /**
     * 有线业务算费
     */
    besWireFee() {
      this.telPayFlag = true;
      let url = `/xsb/personBusiness/oneClickPro/h5besPayMentinfoqry`;
      let param = {};
      param.serviceNumber = this.telNum;
      param.mainOfferId = "";
      param.subsId = this.jqData.userId;

      let feeParam = {};
      param.feeParam = this.telNum;

      //业务商品信息
      if (this.offerInfo && this.offerInfo.length > 0) {
        let orderItemList = [];
        let deviceOrderItemList = [];
        for (let offerItem of this.offerInfo) {
          let orderItemInfo = {};
          orderItemInfo.itemid = offerItem.offerid;
          orderItemInfo.actiontype = "A";
          orderItemInfo.effectmode = offerItem.effectType === "1"? "0": "2";

          //判断是不是存量宽带场景
          if (!this.propShowInfo.isShowBroadbandProp && offerItem.offerid === FAM_CUS_CFG.BROADBAND_PKG) {
            orderItemInfo.instanceid = this.instanceId;
            orderItemInfo.actiontype = "M";
          }

          if (offerItem.subofferInfo && offerItem.subofferInfo.offerInfo && offerItem.subofferInfo.offerInfo.length > 0) {
            let suborderItem = {};
            let subOrderItemList = [];

            for (let subOfferInfo of offerItem.subofferInfo.offerInfo) {
              let offerId = subOfferInfo.offerid.substring(2, subOfferInfo.offerid.length);
              let subOrderItem = {};
              subOrderItem.itemid = offerId;
              subOrderItem.actiontype = "A";
              subOrderItem.effectmode = "";

              //判断是不是功能产品
              if (subOfferInfo.offerid.substring(0, 1) === 'N') {
                //判断是不是宽带的功能产品，且有设备
                if (offerItem.offerid === FAM_CUS_CFG.BROADBAND_PKG && this.propShowInfo.isShowBroadbandProp && this.receiveEquipFlag === "Y") {
                  /*let orderItemRelation = {}
                  orderItemRelation.itemid = offerId;
                  orderItemRelation.relationtype = "RE";
                  orderItemRelation.deviceId = this.installInfo.curPonType.id;
                  orderItemInfo.orderitemrelation =orderItemRelation;*/
                  //宽带营收模式
                  if (this.installInfo.isTestFeeMod !== "Y") {
                    let broadbandDevice = {}
                    broadbandDevice.actiontype = "A";
                    broadbandDevice.effectmode = "0";
                    broadbandDevice.itemid = this.installInfo.curPonType.id;
                    deviceOrderItemList.push(broadbandDevice);
                  }

                } else if (FAM_CUS_CFG.NET_TV_PKG[offerItem.offerid] && FAM_CUS_CFG.NET_TV_PKG[offerItem.offerid].isHasDevice) { //判断是不是电视的功能产品，且有设备
                  let orderItemRelation = {}
                  orderItemRelation.itemid = offerId;
                  orderItemRelation.relationtype = "RE";
                  orderItemRelation.deviceId = FAM_CUS_CFG.DEVICE_INFO.NET_TV;
                  orderItemInfo.orderitemrelation =orderItemRelation;
                  //电视营收模式
                  if (this.propShowInfo.isShowMBandTestFee && this.installInfo.isTestFeeMod !== "Y"
                    || this.propShowInfo.isShowTvTestFee && this.installInfo.isTvTestFeeMod !== "Y") {
                    let tvDevice = {}
                    tvDevice.actiontype = "A";
                    tvDevice.effectmode = "0";
                    tvDevice.itemid = FAM_CUS_CFG.DEVICE_INFO.NET_TV;
                    deviceOrderItemList.push(tvDevice);
                  }
                }
              }

              if (subOrderItem.itemid !== "2400000979") {
                subOrderItemList.push(subOrderItem);
              }
            }
            //在首次开通宽带中，非校园用户传979的商品，校园用户不传
            if (this.propShowInfo.isShowBroadbandProp) {
              if (this.userType !== "2" && offerItem.offerid === FAM_CUS_CFG.BROADBAND_PKG) {
                let school979Prod = {};
                school979Prod.itemid = "2400000979";
                school979Prod.actiontype = "A";
                school979Prod.effectmode = "";
                subOrderItemList.push(school979Prod);
              }
            }
            suborderItem.orderitem = subOrderItemList;
            orderItemInfo.suborderitem = suborderItem;
          }

          orderItemList.push(orderItemInfo);
        }
        if (deviceOrderItemList && deviceOrderItemList.length > 0) {
          orderItemList = orderItemList.concat(deviceOrderItemList);
        }
        feeParam.orderitem = orderItemList;
      }

      //促销活动信息
      if (this.promotionInfo && this.promotionInfo.length > 0) {
        let promotionList = [];
        for (let promotionItem of this.promotionInfo) {
          let promItem = {};
          promItem.promotion_id = promotionItem.promotionId;
          promotionList.push(promItem)
        }
        feeParam.promotion_list = promotionList;
      }

      param.thirdTvChargeStr = JSON.stringify(feeParam);
      this.$http.post(url, param).then(res => {
        let { retCode, retMsg, data } = res.data;
        if (retCode === "0") {
          //获取算费信息
          if (data.feeInfoBoList && data.feeInfoBoList.length > 0) {
            for (let feeItem of data.feeInfoBoList) {
              let shouldBreak = false;
              if (feeItem.chargeList && feeItem.chargeList.length > 0) {
                //封装费用项，获取各个业务模块费用信息
                for (let chargeInfo of feeItem.chargeList) {
                  let invoice = {};
                  invoice.itemId = feeItem.itemid;
                  invoice.chargecode = chargeInfo.chargecode;
                  invoice.priceamount = chargeInfo.priceamount;
                  this.invoiceItem.push(invoice);     //封装费用项
                  if (feeItem.itemid === FAM_CUS_CFG.BROADBAND_PKG
                    || feeItem.itemid === FAM_CUS_CFG.DEVICE_INFO.HOME_GATEWAY
                    || feeItem.itemid === FAM_CUS_CFG.DEVICE_INFO.TEN_GPON) {
                    if (chargeInfo.chargecode === "DeviceTestFee") {  //宽带调测费
                      this.kdTestDeviceFee = parseInt(chargeInfo.priceamount);
                    }
                    this.broadbandFeeItem.push(chargeInfo);
                  } else if (feeItem.itemid === FAM_CUS_CFG.DEVICE_INFO.NET_TV) {
                    if (chargeInfo.chargecode === "DeviceTestFee") {  //电视调测费
                      this.tvTestDeviceFee = parseInt(chargeInfo.priceamount);
                    }
                    this.tvFeeItem.push(chargeInfo);
                  } else {
                    this.otherFeeItem.push(chargeInfo);
                  }
                }
                //判断促销活动是否有费用，且费用大于0
                if (this.promotionInfo && this.promotionInfo.length > 0) {
                  for (let promotionItem of this.promotionInfo) {
                    if (promotionItem.promotionId === feeItem.itemid && feeItem.chargeList && feeItem.chargeList.length > 0) {
                      for (let chargeInfo of feeItem.chargeList) {
                        if (parseInt(chargeInfo.priceamount) > 0) {
                          this.telPayFlag = false;
                          break;
                        }
                      }
                    }
                  }
                }
              }

              if (shouldBreak) {
                break;
              }
            }
          }

          //跳转到算费页面
          this.step = "fee";
          this.totalFee = parseInt(data.totalFee);
          //获取调测费减免权限
          this.qryOperaAuth();
          if (this.propShowInfo.isShowBroadbandProp) {
            this.getCustomInfo();
          }
        } else {
          this.$alert(retMsg || "有线业务算费失败！");
        }
      }).catch(reason => {
        this.$alert("有线业务算费异常，error:" + reason);
      })
    },

    /**
     * 查询是否有权限减免调测费
     */
    qryOperaAuth(){
      //减免令牌，目前写死
      let authId='6013120191213104501';
      let url = '/xsb/personBusiness/businessOpen/h5qryOperAuth?authId='+authId;
      this.$http.get(url).then((res)=>{
        let {retCode} = res.data;
        this.isHaveAuth = retCode === "0";
      }).catch(() =>{
        this.isHaveAuth = false;
      })
    },

    /**
     * 有线业务提交
     */
    besWireSubmit() {
      let url = `/xsb/personBusiness/oneClickPro/h5wiredBusinessSubmitAz`;
      let param = {};

      let besWireSubmitParam = {};
      besWireSubmitParam.authchktype = this.jqData.crmAuthType;
      //宽带开通中进行证件后六位校验时，传新的鉴权方式
      if (this.propShowInfo.isShowBroadbandProp && (this.jqData.authtype === "06" || this.jqData.authtype === "07")) {
        besWireSubmitParam.authchktype = this.newAutoType;
      }
      //费用项信息
      let orderBusinessFee = {};
      if (this.invoiceItem && this.invoiceItem.length > 0) {
        let invoiceItemList = []
        for (let invoice of this.invoiceItem) {
          if (this.isChecked && invoice.chargecode === "DeviceTestFee") {
            invoice.priceamount = "0";
          }
          invoiceItemList.push(invoice);
        }
        orderBusinessFee.invoiceitem = invoiceItemList;
        besWireSubmitParam.orderbusinessfee = JSON.stringify(orderBusinessFee);
      }
      //支付信息
      let orderPayment = {};
      let payTypeDict = "";
      let payMethodDict = "";
      if(this.payType === "2"){ //话费支付
        payTypeDict = "2";
        payMethodDict = "9000";
      }else if(this.payType === '0') { //现金支付
        payTypeDict = "1";
        payMethodDict = "1000";
      }
      orderPayment.payamount = this.totalFee;
      orderPayment.paytype = payTypeDict;
      orderPayment.paymethod = payMethodDict;
      orderPayment.ispaid = "Y";
      besWireSubmitParam.orderpayment = JSON.stringify(orderPayment);
      //地址信息
      if (this.propShowInfo.isShowBroadbandProp) {
        let addressInfo = {};
        addressInfo.relaid = this.addressId;
        addressInfo.addrid = this.addressId;
        addressInfo.addrname = this.fullName;
        addressInfo.districtid = this.villageId;
        addressInfo.districtname = this.villageName;
        besWireSubmitParam.addressinfo = JSON.stringify(addressInfo);
      }
      //封装业务商品信息和促销活动信息
      let {orderItemList, promotionList} = this.getProdParam(true);
      //商品信息
      besWireSubmitParam.orderItemList = orderItemList;
      //促销活动信息
      besWireSubmitParam.promotion_list = promotionList;
      //联系人信息
      let contactInfo = {};
      contactInfo.name = this.installInfo.linkName;
      contactInfo.phonenumber = this.installInfo.linkPhone;
      besWireSubmitParam.contactinfo = JSON.stringify(contactInfo);

      param.accessModeStr = JSON.stringify(besWireSubmitParam);

      param.userRegion = this.jqData.userCity;
      param.telnum = this.telNum;
      param.cardAuthSrl = this.jqData.cardAuthSrl;
      param.authType = this.jqData.crmAuthType;
      param.payStatus = "";
      param.totalFee = this.totalFee;
      param.templateId = this.selectTemplate.entryId;
      param.templateName = this.selectTemplate.entryName;
      param.businessType = this.CONSTVAL.BUSI_TYPE_PERFECT_ORDER;
      param.payType = this.payType;
      param.longitude = Storage.get('longitude'); //经度
      param.latitude = Storage.get('latitude');  //纬度
      param.location = Storage.get('location');  //地点
      param.stationId = this.uinfo.stationId;
      param.clientType = /android|harmony/gi.test(navigator.userAgent) ? 'ANDROID' : 'IOS';
      let offerId = this.offerInfo[0].offerid;
      let offerName = this.offerInfo[0].offerName;
      //offerId,offerName传商品包编码和名称，活动取第一个，用"_"拼接
      if (this.offerInfo.length > 1) {
        for (let i = 1; i < this.offerInfo.length; i++) {
          offerId = offerId + "_" + this.offerInfo[i].offerid;
          offerName = offerName + "€€" + this.offerInfo[i].offerName;
        }
      }
      if (this.promotionInfo && this.promotionInfo.length > 0) {
        offerId = offerId + "_" + this.promotionInfo[0].promotionId;
        offerName = offerName + "€€" + this.promotionInfo[0].promName;
      }
      //若offerId或offerName长度过长传模板编码和模板名称
      if (offerId.length > 100 || offerName.length > 500) {
        offerId = this.selectTemplate.entryId;
        offerName = this.selectTemplate.entryName;
      }
      param.offerId = offerId;
      param.offerName = offerName;

      this.$parent.azSubmitUrl = `/xsb/personBusiness/oneClickPro/h5wiredBusinessSubmitAzFinal`;
      this.$parent.ySubmit(url, param, true);
    }
  },

  watch: {
    receiveEquipFlag(val) {
      if (val === "Y") {
        this.propShowInfo.showChosePonTypeList = true;
        this.propShowInfo.isShowMBandTestFee = !this.isHasOtherTv && !this.isHasSoftTv;
      } else {
        this.propShowInfo.isShowMBandTestFee = false;
        this.propShowInfo.showChosePonTypeList = false;
      }
    },
  }
}
