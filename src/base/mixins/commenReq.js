// 5G消息远程签字受理支持甩单，查询当前操作员有没有甩单权限
import Storage from '@/base/storage';
export const Limits = {
  data() {
    return {
      limit: "", //true有权限,false无权限
    }
  },
  created() {
    //登录人信息
    this.uinfo = Storage.session.get('userInfo');
    // 查询有没有权限接口
    this.BusiPermission()
  },
  methods: {
    // 查询有没有权限接口
    BusiPermission() {
      let sendUrl = '/xsb/ability/businessLimit/h5QryBusiPermission'
      let sendReq = {
        busiType: this.CONSTVAL.BUSI_TYPE_HANDLEORDER_SHUANDAN,

      }
      sendReq.region = this.uinfo.region
      sendReq.crmId = this.uinfo.crmId
      sendReq.servNumber = this.uinfo.servNumber
      this.$http.post(sendUrl, sendReq).then(res => {
        let { retCode, retMsg, data } = res.data;
        if(retCode==0){
         this.limit=true
        }else if(retCode==-1){
          this.limit=false
        }
      })
    }
  }
}
