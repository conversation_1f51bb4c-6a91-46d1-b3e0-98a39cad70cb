import ClientJs from '@/base/clientjs'

import Storage from '@/base/storage.js';
import {ZwxtOrderMixin} from '@/base/mixins/ZwxtOrderMixin.js';
import CartUnlock from 'components/business/ShoppingCart/CartUnlockTip'
import {MessageBox} from "mint-ui";
import MenuRecommend from "components/common/MenuRecommend/MenuRecommend";
import {Indicator} from 'mint-ui';
import {ZhongDuanShopCartMixin} from 'components/business/promotioncenter/PromotionShopCartMixin';
import { getClientType } from '../utils'
import NLMessageBox from 'components/common/NLMessageBox.js';
import {insertTraceRecorder} from  '@/base/request/commonReq.js';
//爱知无纸化mixin公用方法
export const aizhiPaperMixin = {
  data() {
    return {
      localOrderId: '',//本地流水号
      crmAzOrderId: '',//crm预订单号
      supportOffsite: false,//是否支持异地办理 (补换卡、宽带、电视、促销活动)
      userInfoAz: {},//用户信息
      submitCartUrl: '/xsb/personBusiness/shopCart/h5shoppingCartBusiPreCommit',//提交预购物车地址
      assistProcessUrl: '/xsb/paperless/assistProcess/h5auxiliaryCertificateAz',//辅助中断预提交地址
      submitCartFinalUrl: '/xsb/personBusiness/shopCart/h5shoppingCartBusiFinalCommit',//提交购物车最终地址
      assistProcessFinalUrl: '/xsb/paperless/assistProcess/h5auxiliaryCertificateAzFinal',//辅助中断最终提交地址
      isShopCartAzCheckFlag:false,//是否购物车
      isAssistProcessCheckFlag:false,//是否辅助中断
      checkOid:'',//审核单号
      isVideo:'1',//是否视频采集 0是 1否
      hasExtraMethod: false, // 最终提交后是否有额外的调用方法
      isOpenElectronicPay: false, // 最终提交后是否弹出电子支付框
      electronicPayInfo: {}, // 电子支付信息
      isOpenMenuRecommend: false, // 是否打开菜单推荐框
      menuRecommendParams:{},

      deviceTempUrl:'',//设备类型回调，url暂存
      deviceTempParams:'',//设备类型回调，params暂存
      zhiBanFlagParent:false,//短信直办鉴权框
      zhiBanOrderIdParent:"",//短信直办订单号
      zhiBanParamsParent:"",//短信直办参数
    }
  },
  mixins: [ZwxtOrderMixin,ZhongDuanShopCartMixin],
  methods: {
    //购物车提交
    async shoppingCartBusiCommit(params, supportOffsite) {
      this.isShopCartAzCheckFlag=true;//是购物车
      let orgFlag = await this.qryOrgIdInAz();
      if (orgFlag) {
        //购物车默认支持异地受理
        this.supportOffsite =true;
        // if (supportOffsite) {
        //   this.supportOffsite = supportOffsite;
        // }
        this.userInfoAz = Storage.session.get('userInfo');
        //通用参数封装
        let commonParams = {
          location: Storage.get('location'),
          latitude: Storage.get('latitude'),
          longitude: Storage.get('longitude'),
          stationId: this.userInfoAz.stationId,//岗位
          type: /android|harmony/gi.test(navigator.userAgent) ? 'ANDROID' : 'IOS',//机型
          cardAuthSrl: Storage.session.get('jqData') && Storage.session.get('jqData').cardAuthSrl,//二代证鉴权流水
          operatorName: this.userInfoAz.operatorName,//操作员名称
          crmOrgId:this.userInfoAz.crmOrgId,//crm订单号
        };
        Object.assign(params, commonParams);

        if (params.orderPayment) {
          // 判断是否为指定业务且是否为电子支付，如果是，在最终提交后打开电子支付框
          let businessTypeList = [
            this.CONSTVAL.BUSI_TYPE_MBAND,
            this.CONSTVAL.BUSI_TYPE_PROMOTION_CENTER,   //促销活动
            //第一台互联网电视
            this.CONSTVAL.BUSI_TYPE_TV_NETTV,           ////百事通电视开通
            this.CONSTVAL.BUSI_TYPE_TV_NETTV_SNYGYH,    //省内央广银河
            this.CONSTVAL.BUSI_TYPE_TV_NETTV__SNYS,     //省内央视

            this.CONSTVAL.BUSI_TYPE_PROMOTION_BACK,     //活动中断
            this.CONSTVAL.BUSI_TYPE_NETWORK_SERVICE,    //组网一件办理
            this.CONSTVAL.BUSI_TYPE_SECURITY_SERVICE,   //安防一件办理
          ];
          let orderPayment = JSON.parse(params.orderPayment);
          if (orderPayment.payMethod === '2022' && businessTypeList.includes(params.busiType)) {
            // 用于电子支付页面
            this.electronicPayInfo = {
              "payType": params.payType,
              "telNum": params.serviceNum,
            }
            this.isOpenElectronicPay = true;
          }
        }
        let url = this.submitCartUrl;
        this.checkPadDistinguish(url,params);
      } else {
        this.$toast("查询操作员组织机构失败");
      }
    },

    //辅助中断提交
    async assistProcessAz(params) {
      this.isAssistProcessCheckFlag = true;//是辅助中断
      params.xsgOrderType = '0';
      params.xsgOrderId = '';
      params.xsgOrderCaseno = '';

      let orgFlag = await this.qryOrgIdInAz();
      if (orgFlag) {
        this.userInfoAz = Storage.session.get('userInfo');
        params.crmOrgId=this.userInfoAz.crmOrgId;//crm订单号
        this.doAssistProcessPost(this.assistProcessUrl, params);
      } else {
        this.$toast("查询操作员组织机构失败");
      }
    },
    //辅助中断通知接口
    async resultNotice() {
      console.info('assistObj',this.assistObj)
      let param = {
        order_id: this.assistObj.orderId,
        booking_id: this.assistObj.bookingId,
        deal_status: this.assistObj.dealStatus,
        deal_desc: this.assistObj.dealDesc,
      };
      await this.$http.post('/xsb/paperless/assistProcess/h5resultNotice', param).then((res) => {
        let {retCode,retMsg,data} = res.data
        if (retCode != '0') {
          this.$alert(retMsg || '辅助中断通知失败');
        }
      })
    },
    //参数校验
    checkAiziParam(param){
      let flag=true;
      if(param ==null || param =='' || param ==undefined){
        flag=false;
      }
      return  flag;
    },

      //打开爱知url
      openAzUrl() {
          let regionCode = this.userInfoAz.region;
          if(this.supportOffsite){
              if (Storage.session.get('jqData').userCity) {
                  regionCode = Storage.session.get('jqData').userCity;
              } else {
                  this.$toast('未获取到用户所在地市');
              }
          }
          //获取主备url
          let azurl = `${Storage.get('webUrl')}/js/front/h5/acceptAdapterCmdCode?sysAccept=${this.crmAzOrderId}&regionCode=${regionCode}&cmdCode=${this.CONSTVAL.AZ_CMD_CODE}&loginNo=${this.userInfoAz.crmId}&groupId=${this.userInfoAz.crmOrgId}&isVideo=${this.isVideo}`;
          //和力云办理宽带跳爱知页面增加短信签字
          /*if (this.srcFrom === "/hlyReservationDetail") {
            azurl = azurl + "&businessType=1";
          }*/
          let basePath = window.location.origin;
          let orgUrl = window.location.href;
          console.info("orgUrl", orgUrl);
          let extraParam = `&azSubmitUrl=${this.azSubmitUrl}&isShopCartAzCheckFlag=${this.isShopCartAzCheckFlag}`
              + `&pageName=${this.pageName}&srcFrom=${this.srcFrom}&createNumber=${this.createNumber}&paytype=${this.paytype}`
              + `&isOpenElectronicPay=${this.isOpenElectronicPay}&electronicPayInfo=${this.electronicPayInfo}`
              + `&showOAOMarketingFlag=${this.showOAOMarketingFlag}&telNumber=${this.telNumber}&privId=${this.privId}`
              + `&isOpenMenuRecommend=${this.isOpenMenuRecommend}&menuRecommendParams=${this.menuRecommendParams}`
              + `&sfzObj=${this.sfzObj}&hasExtraMethod=${this.hasExtraMethod}&azBusiType=${this.azBusiType}`
              + `&busiType=${this.busiType}`;
          azurl = basePath + `/xsbh5.html#/notifyWebKitWithCallBack?sysAccept=${this.crmAzOrderId}&regionCode=${regionCode}`
              + `&cmdCode=${this.CONSTVAL.AZ_CMD_CODE}&loginNo=${this.userInfoAz.crmId}&groupId=${this.userInfoAz.crmOrgId}`
              + `&isVideo=${this.isVideo}&orderId=${this.localOrderId}`;
          this.azTokenOpen(azurl,this.userInfoAz.crmOrgId);
      },

    //爱知TOKEN加密
    azTokenOpen(azurl,crmOrgId){
      let parm = {
        "crmOrgId": crmOrgId,//组织机构编码
      }
      let url = `/xsb/paperless/azPaperless/h5aiZhiGetToken`;
      this.$http.post(url, parm).then(res => {
        let result = res.data;
        //判断爱知TOKEN本地开关是否开启，0开启成功，1未开启
        if ('0' == result.retCode || '1' == result.retCode) {
          if('0' == result.retCode){
            let authId = result.data.authId;
            let azToken = result.data.azToken;
            //获取主备url
            azurl=azurl+`&authId=${authId}&token=${azToken}`;
          }
          //打开爱知页面
            let pointDesc = '爱知签字';
            this.openWebKitWithStatus(azurl, {}, pointDesc);
        } else {
          this.$alert(res.data.retMsg || "爱知TOKEN获取失败");
        }
      }).catch((response) => {
        this.$toast("爱知TOKEN获取异常:" + response);
      });
    },

    //获取组织机构编码
    async qryOrgIdInAzGet() {
      return this.$http.get('/xsb/personBusiness/chooseTelEnterNet/h5QryOperatorInfo');
    },
    //获取组织机构编码
    async qryOrgIdInAz() {
      let userInfoAz = Storage.session.get('userInfo');
      if (!userInfoAz.crmOrgId) {
        let orgRes = await this.qryOrgIdInAzGet();
        if (orgRes.data.retCode == '0') {
          let orgData = orgRes.data.data;
          if (!this.checkAiziParam(orgData.orgId)) {
            this.$alert('获取组织机构编号失败,暂无法办理无纸化');
            return false;
          } else {
            userInfoAz.crmOrgId = orgData.orgId;
            userInfoAz.crmStatus = orgData.status;//crm生效状态
            userInfoAz.crmOrgName =orgData.orgName;
            Storage.session.set('userInfo', userInfoAz);
            return true;
          }
        } else {
          this.$alert(orgRes.data.retMsg || '获取组织机构编号失败,暂无法办理无纸化');
          return false;
        }

      } else {
        return true;
      }
    },



    // 非购物车
    async ySubmit(url, params, supportOffsite) {
      params.xsgOrderType = '0';
      params.xsgOrderId = '';
      params.xsgOrderCaseno = '';
      if(this.srcFrom =='twoPeopleCsViewIn'){
        if(Storage.session.get('twoPeopleOrdeInfo')){
          params.twoPeopleOrderSeq =  Storage.session.get('twoPeopleOrdeInfo').twoPeopleOrderSeq;//双向协同母订单号
          params.twoPeopleSubOrderSeq =  Storage.session.get('twoPeopleOrdeInfo').twoPeopleSubOrderSeq;//双向协同子订单号
        }
      }
      // 一店两员
      if (this.srcFrom === '/twoPeopleInputWorkList') {
        params.oneShopOrderSeq = Storage.session.get('oneShopOrderSeq');
      }
      let orgFlag = await this.qryOrgIdInAz();
      if (orgFlag) {
        //支持异地受理
        if (supportOffsite) {
          this.supportOffsite = supportOffsite;
        }
        //采集过视频
        if(params.videoCheckHasVideoFlag=='Y'){
          this.isVideo='0';
        }else{
          this.isVideo='1';
        }
        this.userInfoAz = Storage.session.get('userInfo');
        params.crmOrgId=this.userInfoAz.crmOrgId;//crm订单号
        this.checkPadDistinguish(url, params);
      } else {
        this.$toast("查询操作员组织机构失败");
      }
    },
      //判断设备类型Storage
     checkPadDistinguish(url, params){
      let padDistinguishFlag = Storage.session.get('padDistinguishFlag');
       //缓存中值存在
         if(padDistinguishFlag){
           let padDistinguishTmp = getClientType();
           if(padDistinguishFlag =='2'){
             padDistinguishTmp = padDistinguishTmp +"|PAD"
           }
           params.type=padDistinguishTmp;
           params.clientType=padDistinguishTmp;
           params.deviceType=padDistinguishTmp;
           this.doPostBusiness(url, params);
         }else{
         //缓存中值不存在
           this.deviceTempUrl = url;
           this.deviceTempParams = params;
           try {
             ClientJs.getDeviceType("padDistinguishBack");
           } catch (e) {
             //手机未升级
             Storage.session.set('padDistinguishFlag','3');
             this.doPostBusiness(url, params);
           }
         }
     },

    //业务提交
    doPostBusiness(url, params) {
      // 判断是否打开菜单推荐框
      if (this.privId) {
        this.getOpenMenuRecommendFlag();
      }
      this.$http.post(url, params).then((res) => {
        let result = res.data;
        if (result.retCode == '0') {
          //判断是否短信直办
          if(params.authChkType == 'AuthCheckDXZB'){
            this.zhiBanOrderIdParent = result.data.crmOrderId;//crm订单流水号
            this.zhiBanParamsParent = params;
             this.zhiBanFlagParent = true;
          }else{
            this.crmAzOrderId = result.data.crmOrderId;//crm订单流水号
            this.localOrderId = result.data.orderId;//本地流水
            this.openAzUrl();//打开爱知url
          }
        } else {
          //购物车被锁
          if(this.isShopCartAzCheckFlag && result.retMsg.substring(result.retMsg.length-3)  == '已锁定'){
            CartUnlock({
              serviceNum:params.serviceNum,
              jieSuoShopCartFlag: true,
              jieSuoShopItem: params.shoppingCartItemList,
              jieSuoShopCartTipTxt: result.retMsg
            });
            // 群组权益管理存在retCode为2的场景,需要单独处理
          } else if(result.retCode == '2' && params.busiType == 'receive_equity') {
            //二次确认弹窗
            MessageBox.confirm('成员存在在途的权益商品，是否进行二次确认？').then(action => {
              this.closeMemConfirm();
            });
          } else {
            this.$alert(result.retMsg || '业务办理失败');
          }
        }
      }).catch((response) => {
        this.$alert('爱知无纸化网络请求失败' + response);
      });
    },

    //辅助中断提交
    async doAssistProcessPost(url, params) {
      this.$http.post(url, params).then((res) => {
        let result = res.data;
        if (result.retCode == '0') {
          console.info('result.data',result.data)
          this.crmAzOrderId = result.data.crmOrderId;//crm订单流水号
          this.localOrderId = result.data.orderId;//本地流水
          this.checkOid = result.data.checkOid;//审核单号
          console.info('this,checkOid',this.checkOid)
          this.openAzUrl();//打开爱知url
        } else {
          this.$alert(result.retMsg || '业务办理失败');
        }
      }).catch((response) => {
        this.$alert('爱知无纸化网络请求失败' + response);
      });
    },

    //免填单补录
    async suppAzSubmit(crmOrderId, yidiRegion,isSuppleVideo) {
      await this.qryOrgIdInAzSupple(crmOrderId, yidiRegion,isSuppleVideo);
    },

    //获取组织机构编码
    async qryOrgIdInAzSupple(crmOrderId, regionCode,isSuppleVideo) {
      let userInfoAz = Storage.session.get('userInfo');
      if (!userInfoAz.crmOrgId) {
        this.$http.get('/xsb/personBusiness/chooseTelEnterNet/h5QryOperatorInfo',{
          unLoadFlg:true//不展示加载圈
        }).then(res => {
          let orgRes = res;
          if (orgRes.data.retCode == '0') {
            let orgData = orgRes.data.data;
            if (!this.checkAiziParam(orgData.orgId)) {
              this.$alert('获取组织机构编号失败,暂无法办理无纸化');
              return false;
            } else {
              userInfoAz.crmOrgId = orgData.orgId;
              userInfoAz.crmStatus = orgData.status;//crm生效状态
              userInfoAz.crmOrgName =orgData.orgName;
              Storage.session.set('userInfo', userInfoAz);
              if (!regionCode) {
                regionCode = userInfoAz.region;
              }
              //获取主备url
              let azurl = `${Storage.get('webUrl')}/js/front/h5/acceptAdapterCmdCode?sysAccept=${crmOrderId}&regionCode=${regionCode}&cmdCode=${this.CONSTVAL.AZ_CMD_CODE}&loginNo=${userInfoAz.crmId}&groupId=${userInfoAz.crmOrgId}&isVideo=${isSuppleVideo}`;
              //获取TOKEN
              this.azTokenOpen(azurl,userInfoAz.crmOrgId)
            }
          } else {
            this.$alert(orgRes.data.retMsg || '获取组织机构编号失败,暂无法办理无纸化');
            return false;
          }
        });
      }else{
        if (!regionCode) {
          regionCode = userInfoAz.region;
        }
        //获取主备url
        let azurl = `${Storage.get('webUrl')}/js/front/h5/acceptAdapterCmdCode?sysAccept=${crmOrderId}&regionCode=${regionCode}&cmdCode=${this.CONSTVAL.AZ_CMD_CODE}&loginNo=${userInfoAz.crmId}&groupId=${userInfoAz.crmOrgId}&isVideo=${isSuppleVideo}`;
        //获取TOKEN
        this.azTokenOpen(azurl,userInfoAz.crmOrgId)
      }
    },

    //免填单补录-跨工号
    async suppAzSubmitAcross(crmOrderId, yidiRegion,isSuppleVideo,crossChannel) {
      await this.qryOrgIdInAzSuppleAcross(crmOrderId, yidiRegion,isSuppleVideo,crossChannel);
    },

    //获取组织机构编码-跨工号
    async qryOrgIdInAzSuppleAcross(crmOrderId, regionCode,isSuppleVideo,crossChannel) {
      let userInfoAz = Storage.session.get('userInfo');
      if (!userInfoAz.crmOrgId) {
        this.$http.get('/xsb/personBusiness/chooseTelEnterNet/h5QryOperatorInfo',{
          unLoadFlg:true//不展示加载圈
        }).then(res => {
          let orgRes = res;
          if (orgRes.data.retCode == '0') {
            let orgData = orgRes.data.data;
            if (!this.checkAiziParam(orgData.orgId)) {
              this.$alert('获取组织机构编号失败,暂无法办理无纸化');
              return false;
            } else {
              userInfoAz.crmOrgId = orgData.orgId;
              userInfoAz.crmStatus = orgData.status;//crm生效状态
              userInfoAz.crmOrgName =orgData.orgName;
              Storage.session.set('userInfo', userInfoAz);
              if (!regionCode) {
                regionCode = userInfoAz.region;
              }
              //获取主备url
              let azurl = `${Storage.get('webUrl')}/js/front/h5/acceptAdapterCmdCode?sysAccept=${crmOrderId}&regionCode=${regionCode}&cmdCode=${this.CONSTVAL.AZ_CMD_CODE}&loginNo=${userInfoAz.crmId}&groupId=${userInfoAz.crmOrgId}&isVideo=${isSuppleVideo}&isResetSign=0`;
              //判断当前是否是跨渠道工单添加跨渠道标识
              if(crossChannel != null && crossChannel != '' && crossChannel != undefined){
                azurl = azurl +'&crossChannel=0';
              }
              console.info('azuel',azurl);
              //获取TOKEN
              this.azTokenOpen(azurl,userInfoAz.crmOrgId)
            }
          } else {
            this.$alert(orgRes.data.retMsg || '获取组织机构编号失败,暂无法办理无纸化');
            return false;
          }
        });
      }else{
        if (!regionCode) {
          regionCode = userInfoAz.region;
        }
        //获取主备url
        let azurl = `${Storage.get('webUrl')}/js/front/h5/acceptAdapterCmdCode?sysAccept=${crmOrderId}&regionCode=${regionCode}&cmdCode=${this.CONSTVAL.AZ_CMD_CODE}&loginNo=${userInfoAz.crmId}&groupId=${userInfoAz.crmOrgId}&isVideo=${isSuppleVideo}&isResetSign=0`;
        //判断当前是否是跨渠道工单添加跨渠道标识
        if(crossChannel != null && crossChannel != '' && crossChannel != undefined){
          azurl = azurl +'&crossChannel=0';
        }
        console.info('azurl',azurl);

        //获取TOKEN
        this.azTokenOpen(azurl,userInfoAz.crmOrgId)
      }
    },
    // 是否打开菜单推荐框
    getOpenMenuRecommendFlag() {
      let userInfo = Storage.session.get("userInfo");
      // let privId = this.menuRecommendParams ? this.menuRecommendParams['privId'] : '';
      //便利店业主的预配号入网屏蔽掉
      let privId = this.privId;
      if(userInfo.stationId=='9988020224955046' && privId == '10019') {
        this.isOpenMenuRecommend = false;
        return;
      }
      this.menuRecommendParams.privId = privId;
      console.info('az privId',privId)
      let url = `/xsb/personBusiness/menuRecommend/h5hasMenuRecommend`;
      let params = {
        "privId": privId,
        "stationId": userInfo ? userInfo.stationId : '',
         unLoadFlg:true//屏蔽加载圈
      }
      console.info('search params:', params)
      this.$http.post(url, params).then((res) => {
        let {retCode,data} = res.data;
        if (retCode === '0') {
          console.info('打开菜单推荐：', data);
          this.isOpenMenuRecommend = data;
        }
      })
    },
    //本地流水号
    changexsgOrderId() {
      return this.localOrderId;
    },
  },

  mounted() {
    //爱知回调 telnum用户手机号
    window['callBack'] = (res) => {
      let moveMachineBack = JSON.parse(res);
      if (!this.checkAiziParam(moveMachineBack.caseNo) || !this.checkAiziParam(moveMachineBack.status)) {
        this.$alert("无纸化合成失败，请重新提交");
        return;
      }
      //判断是否是辅助中断
      if(this.isAssistProcessCheckFlag){
        let params = {
          'orderId': this.changexsgOrderId(),
          'caseNo': moveMachineBack.caseNo,
          'status': moveMachineBack.status,
          'check_oid': this.checkOid,
        };
        console.info('a params:', params);
        this.$http.post(this.assistProcessFinalUrl, params).then(res => {
          let result = res.data;
          if ('0' == result.retCode) {
            this.$messagebox({
              title: '温馨提示',
              message: this.message || '工单提交成功，点击【前往结果查询】查询审核结果',
              showCancelButton: true,
              closeOnClickModal: false,
              closeOnPressEscape: false,
              confirmButtonText: '前往结果查询'
            }).then((action) => {
              if (action === 'confirm') {
                this.goOrderCheck();
              }
            });
          } else {
            this.$alert(result.retMsg || '工单提交失败');
          }
        });
      } else {
        let url = this.azSubmitUrl;//爱知提交url
        if(this.isShopCartAzCheckFlag){
          url = this.submitCartFinalUrl;//购物车提交
        }
        let params = {
          'orderId': this.changexsgOrderId(),
          'caseNo': moveMachineBack.caseNo,
          'status': moveMachineBack.status,
        };
        console.info(this.zhiMaItemId)
        //2025.03.05 qp 芝麻信用
        if(this.zhiMaItemId){
          let userInfoZhiMa = Storage.session.get('userInfo');
          let callbackInfo ={
            'orderId': this.changexsgOrderId(),
            'caseNo': moveMachineBack.caseNo,
            'status': moveMachineBack.status,
            'crmAzOrderId':this.crmAzOrderId,
            'zhiMaShopItem':this.zhiMaItemId,
            'storeId':userInfoZhiMa.crmOrgId,
            'storeName':userInfoZhiMa.crmOrgName,
            'serverNumber': this.telnum,
          }
          this.applyPromAndFreeze(callbackInfo);
          return;
        }
        //2023.09.26 cyy 小区宽带页面
        if(this.pageName == "xqkd"){
          params.serviceNumber = this.createNumber;
          params.payType = this.paytype;
        }
        //2023.09.22 cyy 从装维协同单来
        if(this.srcFrom == "zwxtd"){
          params.source = "zwxtd";
        }
        this.$http.post(url, params).then(async res => {
          let result = res.data;
          if ('0' == result.retCode) {
            //todo 辅助中断通知订单中心
            if (this.isAssist) {
              await this.resultNotice();
            }
            // 电子支付
            if (this.isOpenElectronicPay) {
              // 正式提交后,需要等几秒才能调用生成二维码接口
              Indicator.open("正在前往支付页面...");
              setTimeout(() => {
                Indicator.close();
                this.electronicPayInfo.crmOrderId = this.crmAzOrderId;
                this.$router.push({
                  path: 'electronicPay',
                  query: {
                    orderInfo: JSON.stringify(this.electronicPayInfo),
                    isFromList: '0',
                    srcFrom: this.srcFrom
                  }
                });
              }, 5000);
            } else {
              // 仅预配号入网以及在专业直销员下才跳转营销推荐页面
              let userInfoAz = Storage.session.get('userInfo');
              if (this.showOAOMarketingFlag && (userInfoAz.stationId === '9988021754066058' || userInfoAz.stationId === '10009101')) {
                // 展示地市营销页
                this.$messagebox({
                  title: '温馨提示',
                  message: '开户成功，点击确认进入营销推荐页面',
                  showCancelButton: false,
                  closeOnClickModal: false,
                  confirmButtonText: '确认'
                }).then((action) => {
                  if (action === 'confirm') {
                    this.$router.push({
                      path: '/oAOMarketingRegion', query: {
                        tel: this.telNumber, privId: this.privId
                      }
                    })
                  }
                })
              } else if (this.isOpenMenuRecommend) {
                if (this.privId == '10019') {
                  MenuRecommend({
                    srcFrom: this.srcFrom,
                    menuRecommendParamStr: JSON.stringify(this.menuRecommendParams),
                    telnum: this.telNumber,
                    needTelnum: true,
                    sfzObj: this.sfzObj,
                    prePrivId: this.privId,
                  }, url => {
                    this.$router.push(url);
                  })
                } else {
                  MenuRecommend({
                    srcFrom: this.srcFrom,
                    menuRecommendParamStr: JSON.stringify(this.menuRecommendParams),
                    telnum: this.jqData.telnum,
                  }, url => {
                    this.$router.push(url);
                  })
                }
              } else {
                // 最终提交后,不展示弹窗,执行额外的逻辑
                if (this.hasExtraMethod) {
                  this.submitExtraMethod(result.data);
                } else {
                  //预配号单独展示
                  if (this.azBusiType == "true_name_record") {
                    this.showYuPeiFinish = true;//展示结束页
                    return;
                  }
                  this.$messagebox({
                    title: '温馨提示',
                    message: this.message || '业务办理成功，请点击确认退出',
                    showCancelButton: false,
                    closeOnClickModal: false,
                    closeOnPressEscape: false,
                    confirmButtonText: '确认'
                  }).then((action) => {
                    if (action === 'confirm') {
                      if (this.srcFrom == "csView") { //跳回客户视图
                        this.$router.push({
                          name: 'CsViewIn',
                          query: {
                            telnum: this.telnum
                          }
                        });
                      } else if (this.srcFrom == "tool") { //跳回工具箱
                        this.$router.push('/tools');
                      } else if (this.srcFrom == "installMaintain") { //跳回AMS装维随销的首页
                        this.$router.push('/installMaintain');
                      } else if (this.srcFrom == "zwxtd") {
                        //装维协同单 自动结单 cyy 2023/8/11
                        this.updateZwOrder(this.zwOrder);
                      } else if (this.srcFrom === "/hlyReservationDetail") {
                        this.$router.push('/hlyReservationSheet');
                      } else if (this.srcFrom === "sheetDetailNew") {
                        this.$router.push('/grabSheetNew?doGradSheetRefresh=1&srcFrom=business');
                      } else if (this.srcFrom) {
                        if (~this.srcFrom.indexOf('/')) {
                          this.$router.push(this.srcFrom);
                        } else {
                          this.$router.push("/" + this.srcFrom);
                        }
                      } else {
                        this.$router.push('/business'); //跳回业务
                      }
                    }
                  });
                }
              }
            }
          } else {
            // 余额不足提示
            if (result.retMsg && result.retMsg.indexOf('余额不足') > -1) {
              let _this = this;
              NLMessageBox({
                title: '温馨提示',
                message: result.retMsg,
                showCancelButton: true,
                cancelButtonText: '取消',
                confirmButtonText: '话费充值',
                confirmButtonId: 'charge_fee',
              }, function (res) {
                if (res == 'confirm') {
                  //记表
                  insertTraceRecorder("埋点-弹窗-余额不足-话费充值");//插入埋点
                  _this.$router.push(`/deposit?srcFrom=${_this.srcFrom}`);
                }
              });
            } else {
              this.$alert(result.retMsg || '业务办理失败');
            }
          }
        });
      }
    };
    //pad识别
    window['padDistinguishBack'] = (result) => {
      try {
        let padDistinguishTmp = /android|harmony/gi.test(navigator.userAgent) ? 'ANDROID' : 'IOS'
        if (padDistinguishTmp == 'IOS') {
          try {
            result = JSON.parse(result)
          } catch (e) {
            if(!result){
              Storage.session.set('padDistinguishFlag', '3');
              result={};
            }
          }
        }
        if (result.deviceType || result.deviceTpye) {
          Storage.session.set('padDistinguishFlag', result.deviceType)
          if (result.deviceType == '2' || result.deviceTpye == '2') {
            padDistinguishTmp = padDistinguishTmp + '|PAD'
          }
          this.deviceTempParams.type = padDistinguishTmp
          this.deviceTempParams.clientType = padDistinguishTmp
          this.deviceTempParams.deviceType = padDistinguishTmp
        }
        this.doPostBusiness(this.deviceTempUrl, this.deviceTempParams)
      } catch (e) {
        //手机未升级
        Storage.session.set('padDistinguishFlag', '3')
        this.doPostBusiness(this.deviceTempUrl, this.deviceTempParams)
      }
    };
  },
};

//爱知无纸化mixin公用方法
export const ShopCartrMixin = {
  data() {
    return {
      shopCartItemId: '',//购物车项编码
      addCartUrl: '/xsb/personBusiness/shopCart/h5addShoppingCartItem',//添加购物车地址
      goCartSuanFeiUrl:'/xsb/personBusiness/shopCart/h5shoppingCartBusiValidate',//购物车算费
    }
  },
  methods: {
    //添加购物车
    async addShoppingCartItem(params) {
      this.shopCartItemId = "";
      let ret = await this.addShoppingCartItemPost(params);
      let result = ret.data;
      this.gotoCartTipTxt="添加购物车成功";
      this.tipTxtError = false;
      this.warningTxtList = [];
      //2024.1.9 cyy 先只考虑单个添加购物车
      if ('0' == result.retCode) {
        //校验通过，判断是否需要提示
        if (result.data.validateresultlist && result.data.validateresultlist.length > 0) {
          this.warningTxtList = result.data.validateresultlist;
        }
        this.shopCartItemId = result.data.itemId;
        if(!this.shopCartItemId){
          this.$alert(result.retMsg + '添加购物车项编码为空');
        }
        //活动预约受理 qp 2024.7.17
        if(result.data.scenarioType =='ReserveEntry' &&  result.data.effectDate){
          this.reserveEntryFlag =true;
          let effectDateYear =result.data.effectDate.substring(0,4);
          let effectDateMoth = parseInt(result.data.effectDate.substring(4,6));
          let effectDateDay =parseInt(result.data.effectDate.substring(6,8));
          this.gotoCartTipTxt="请告知客户：本次预约办理的业务，将在"+effectDateYear+ "年"+effectDateMoth+ "月"+effectDateDay+
            "日统一办理。办理成功后您会收到成功短信。如办理时因冲突等原因导致办理失败将由工作人员与您联系，请保持电话畅通。";
        }
      }else if('6013701000001' == result.retCode){
        this.gotoCartTipTxt=result.retMsg;
        this.tipTxtError =true;
        this.gotoCartFlag= true; //前往购物车
      } else {
        this.$alert(result.retMsg || '添加购物车失败');
      }
      return this.shopCartItemId;
    },
    async addShoppingCartItemPost(params) {
      let url = this.addCartUrl;
      return this.$http.post(url, params);
    },

    //购物车算费
    goCartSuanFei(param) {
      this.$http.post(this.goCartSuanFeiUrl, param)
        .then((res) => {
          if (res.data.retCode == '0') {
            this.shopCartFeeList = res.data.data.itemFeeList;//购物车费用列表
            this.totalPrice = parseInt(res.data.data.totalPrice);
            this.showFeeFlag = true;
          } else {
            this.$alert(res.data.retMsg || '购物车算费失败');
          }
        }).catch((response) => {
        this.$alert("购物车算费异常:" + response);
      });

    },

    //购物车明细查询
    qryItemInfo(item) {
      let param = {
        serviceNum: this.serviceNum,//服务号码
        itemId:item.itemId,//购物车项目
      };
      let url = `/xsb/personBusiness/shopCart/h5qryShoppingCartItemInfo`;
      this.$http.post(url, param)
        .then((res) => {
          if (res.data.retCode == '0') {
            this.shopCartInfo=res.data.data;
            if(!this.shopCartInfo){
              this.shopCartInfo ={};
              this.$alert(res.data.retMsg || '购物车列表查询为空');
            }else{
              this.showItemInfoFlag = true;
            }
          } else {
            this.$alert(res.data.retMsg || '购物车列表查询失败');
          }
        }).catch((response) => {
        this.$toast("购物车列表查询:" + response);
      });
    },

    //关闭详情页
    closeInfo() {
      this.showItemInfoFlag = false;
    },

    //关闭算费
    closeSuanFei() {
      this.showFeeFlag = false;
    },

    //前往购物车
    gotoShopCart(){
      this.$router.push({
        path:'/shoppingCartManage',
        query:{
          srcFrom:this.srcFrom
        }
      });
    },
    //短信直办提交
    closeZhiBan(data) {
      this.zhiBanFlag = false
      if (!data) {
        return
      }
      let url = `/xsb/personBusiness/shopCart/h5zbOrderSmsVerifying`
      let params = this.zhiBanParams
      params.orderId = this.zhiBanOrderId
      params.verificationCode = data
      if (this.pageName == 'xqkd') {
        params.serviceNumber = this.createNumber
        params.payType = this.paytype
      }
      //2023.09.22 cyy 从装维协同单来
      if (this.srcFrom == 'zwxtd') {
        params.source = 'zwxtd'
      }
      console.info('search params:', params)
      this.$http.post(url, params).then((res) => {
        let result = res.data
        if ('0' == result.retCode) {
          if (this.isOpenMenuRecommend) {
            MenuRecommend({
              srcFrom: this.srcFrom,
              menuRecommendParamStr: JSON.stringify(this.menuRecommendParams),
              telnum: this.jqData.telnum
            }, url => {
              this.$router.push(url)
            })

          } else {
            // 最终提交后,不展示弹窗,执行额外的逻辑
            if (this.hasExtraMethod) {
              this.submitExtraMethod(result.data)
            } else {
              //预配号单独展示
              this.$messagebox({
                title: '温馨提示',
                message: this.message || '业务办理成功，请点击确认退出',
                showCancelButton: false,
                closeOnClickModal: false,
                closeOnPressEscape: false,
                confirmButtonText: '确认'
              }).then((action) => {
                if (action === 'confirm') {
                  if (this.srcFrom == 'csView') { //跳回客户视图
                    this.$router.push({
                      name: 'CsViewIn',
                      query: {
                        telnum: this.telnum
                      }
                    })
                  } else if (this.srcFrom == 'tool') { //跳回工具箱
                    this.$router.push('/tools')
                  } else if (this.srcFrom == 'installMaintain') { //跳回AMS装维随销的首页
                    this.$router.push('/installMaintain')
                  } else if (this.srcFrom == 'zwxtd') {
                    //装维协同单 自动结单 cyy 2023/8/11
                    this.updateZwOrder(this.zwOrder)
                  } else if (this.srcFrom === '/hlyReservationDetail') {
                    this.$router.push('/hlyReservationSheet')
                  } else if (this.srcFrom === 'sheetDetailNew') {
                    this.$router.push('/grabSheetNew?doGradSheetRefresh=1&srcFrom=business')
                  } else if (this.srcFrom) {
                    if (~this.srcFrom.indexOf('/')) {
                      this.$router.push(this.srcFrom)
                    } else {
                      this.$router.push('/' + this.srcFrom)
                    }
                  } else {
                    this.$router.push('/business') //跳回业务
                  }
                }
              })
            }
          }

        } else {
          this.$alert(result.retMsg || '业务办理失败')
        }
      })
    }
  },
}
