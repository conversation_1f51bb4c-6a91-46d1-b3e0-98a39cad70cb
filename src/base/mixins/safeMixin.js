const rs_header = 'content-type';//'content-lengths'
//js防篡改
export const tamperProofMixin = {
    methods:{
        //获取当前页面需要加载的所有JS列表
        getAllJsArray(){
            let syzybq=[]
            Array.from(window.performance.getEntriesByType("resource")).map(
            (x)=>{//遍历
                if(x.initiatorType=="script"){
                    console.info(x);
                    syzybq.push({jsName:x.name,size:x.decodedBodySize,duration:x.duration});
                }   
            });
            return syzybq;
        },
        acquireJs(url){
            return new Promise((resolve,reject)=>{
                let req = new XMLHttpRequest();
                req.open('GET', url);
                req.onload = () => {
                    if (req.status === 200) {
                        let headers = req.getAllResponseHeaders().toLowerCase();
                        let headerMap = {};
                        let arr = headers.trim().split(/[\r\n]+/);
                        arr.forEach((line) => {
                            let parts = line.split(': ')
                            let header = parts.shift()
                            let value = parts.join(': ')
                            headerMap[header] = value;
                        })
                        resolve(headerMap);
                    }else {
                        //返回 失败结果
                        reject(Error('failed失败'))
                    }
                }
                //onerror监听器，监听链接问题
                req.onerror = () => {
                    //依然返回错误 
                    reject('network error网络错误');
                }
                // req对象，发送请求
                req.send(null);
            })
        },
        //比较content-length
        cmpContentLength(mustSize,item,desc){
            let nowSize = item.size;
            if(!nowSize){
                return;
            }
            //大小不一致
            if(mustSize != nowSize){
                //上报服务端
                this.reportLogToServe(item,desc)
            }
        },
        //上报当前非法请求到服务端
        reportLogToServe(param){
            //TODO
            let reportUrl = '/xsb/api-user/viewType/h5QryViewType';
            param.visitTime = Date.now();//访问页面时间戳
            // this.$http.post(reportUrl,param).then(res => {});
            // this.$router.push('/page404');
        },
        //需要防篡的引用以下方法
        tamperProof(paramJsName,desc){
            //获取所有JS
            let jsArr = this.getAllJsArray();
            jsArr.forEach((item)=>{
                let headerMap = {};
                if(~item.jsName.indexOf(paramJsName)){
                    this.acquireJs(item.jsName).then((resp)=>{
                        headerMap = resp;
                        //获取服务器返回的头（nginx返回报文头中的标识【rs_header】）
                        let len = headerMap[rs_header];
                        if(len){
                            item.pageDesc = desc;//页面描述
                            this.cmpContentLength(len,item,desc);
                        }
                    }).catch((e)=>{
                        console.info(e)
                    });
                }
            })
        },
    }
}