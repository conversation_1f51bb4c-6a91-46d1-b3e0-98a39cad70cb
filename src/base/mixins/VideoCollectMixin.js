import ClientJs from '@/base/clientjs'
import Storage from '@/base/storage'
import {cmpClientVersion} from '@/base/utils';
import ReadChoiceWhite from 'components/tools/activeRecommd/ReadChoiceWhiteList'

//视频采集mixin公用方法
export const VideoCollectMixin = {
  data() {
    return {
      compareVersionNew: '2.22.40',//当前支持版本号
      readChoiceList: [],//选项列表
      keyNumber: '',//当前选中
      dictName: '',//当前选中选项中文名
      readTypeNumber: '',//录制类型
      videoCollectAttachId: '',//视频采集附件id
      reVideoChoiceFlag: false,//是否选择过机读弹窗选项
      filePath: '', // 已采集的视频路径
      videoAginFlag: false, //重拍按钮
      artificialFlag: false,//人工审核按钮
      artificialAiParam: {},//人工审核按请求参数
      isdaidu: '', // 是否代读  1是 0否
      videoNoCollectFlag: false,//视频采集弹窗中是否选了不采集
      videoCollectFlag:false,//是否展示视频采集弹窗 默认没有视频
      waitBusiness:"",//等待业务类型，为获取到版本，等待版本获取后再查询
    }
  },
  created() {
  },
  methods: {
    //调用客户端获取当前版本
    checkCurVersion() {
      if (Storage.get('appVersion')) {
        return;
      }
      if (/android|harmony/gi.test(navigator.userAgent)) {
        let res = window.WebViewFunc.getAppVersion();
        let obj = eval('(' + res + ')');
        Storage.set('appVersion', obj.verison);
        if(this.waitBusiness){
          this.permissionCheck(this.waitBusiness);
        }
      } else {
        ClientJs.getIosOrBrowserAppVersion();
      }
    },

    //判断视频采集权限fea
    permissionCheck(videoBusiType) {
      if(Storage.get('appVersion')){
        this.waitBusiness="";
      }else{
        this.waitBusiness=videoBusiType
        return false;
      }
      //20231214 视频采集改为黑名单
      // let permissionParam = {
      //   busiType: videoBusiType,
      // }
      // this.$http.post('/xsb/ability/businessLimit/h5QryVideoWhitePermission',permissionParam).then((res)=>{
      //   let {retCode,retMsg,data} = res.data;
      //   if(retCode == '0'){
      //     //视频采集
      //     this.videoCollectFlag=false;
      //     this.submitChoose();
      //   } else {
      //     //视频采集
      //     this.permissionWhiteCheck();
      //   }
      // }).catch(() => {
        this.permissionWhiteCheck();
      // });
    },
    //黑名单查询
    permissionWhiteCheck() {
      let params = {};
      let userInfo = Storage.session.get('userInfo');
      if (userInfo.crmId) {
        if (userInfo.videoCollectBlackFlag==undefined || userInfo.videoCollectBlackFlag ==null) {
          let authCheckBFlag = false;//服务密码白名单
          let authCheckGBlackFlag = true;//身份证鉴权黑名单
          let videoCollectBlackFlag = false;//视频采集黑名单
          let uniteAuthFlag = false;//统一鉴权框开关
          this.$http.post('/xsb/ability/businessLimit/h5whitelistQuery', params).then((res) => {
            let {retCode, data} = res.data;
            if (retCode == '0') {
              authCheckBFlag = data.authCheckBFlag;
              authCheckGBlackFlag = data.authCheckGBlackFlag;
              videoCollectBlackFlag = data.videoCollectBlackFlag;
              uniteAuthFlag = data.uniteAuthFlag;
            }
          }).finally(()=>{
            userInfo.authCheckBFlag = authCheckBFlag;
            userInfo.authCheckGBlackFlag = authCheckGBlackFlag;
            userInfo.videoCollectBlackFlag = videoCollectBlackFlag;
            userInfo.uniteAuthFlag = uniteAuthFlag;
            Storage.session.set('userInfo', userInfo);
            this.permissionVersioCheck();
          });
        }else{
          this.permissionVersioCheck();
        }
      }
    },
    //校验是否是新版本
    permissionVersioCheck() {
      //校验是否需要视频采集
      this.videoCollectFlag=  Storage.session.get('userInfo').videoCollectBlackFlag;
      // //写死测试人读
      // if("14093655" ==  Storage.session.get('userInfo').crmId){
      //   console.log("视频采集黑名单测试工号")
      //   this.videoCollectFlag=true;
      // }

      //不需要采集视频
      if(!this.videoCollectFlag){
        this.submitChoose();
      }else{
        //新的版本校验
        let curVersion = Storage.get('appVersion');
        if (cmpClientVersion(curVersion, this.compareVersionNew)) {
          this.reVideoChoiceFlag = true; //重拍
          //视频采集选项查询
          this.qryReadList();
        } else {
          this.$messagebox({
            title: '温馨提示',
            message: '当前阿拉盯版本暂不支持视频采集功能，请到【关于】页面下载最新app',
            showCancelButton: false,
            confirmButtonText: '确定'
          }).then((action) => {
            this.$router.push('/about');
            sessionStorage.removeItem('net_payType');
          });
        }
      }
    },

    //视频采集选项查询
    qryReadList() {
      let param = {
        'busiType': this.busiType,
      }
      let url = `/xsb/ability/recordVideo/h5QueryReadList`;
      let videoRetMsg="";
      this.$http.post(url, param).then(res => {
        if (res.data.retCode == '0') {
          this.readChoiceList = res.data.data;
          if (this.readChoiceList.length != 0) {
            for (let i = 0; i < this.readChoiceList.length; i++) {
              if (this.readChoiceList[i].value == '3') { //默认选中
                this.keyNumber = this.readChoiceList[i].keyNumber;
                this.dictName = this.readChoiceList[i].dictName;
              }
            }
            //视频采集选项组件开启
            this.openVideoChoice();
          } else {
            videoRetMsg = "视频录制模式列表查询为空";
          }
        } else {
          videoRetMsg = res.data.retMsg || "视频录制模式列表查询失败";
        }
      }).catch((response) => {
        videoRetMsg = "视频录制模式列表查询异常：" + response;
      }).finally(()=>{
        if(videoRetMsg) {
          this.$messagebox({
            title: '温馨提示',
            message: videoRetMsg + "【是否重新查询】",
            closeOnClickModal: false,
            closeOnPressEscape: false,
            showCancelButton: true,
            showConfirmButton: true,
            confirmButtonText: "确认",
            cancelButtonText: "取消",
          }).then((action) => {
            if (action === 'confirm') {
              this.qryReadList()
            }
          });
        }
      });
    },
    //校验是否有视频采集
    checkHasVideo(){
      if(this.videoCollectFlag && !this.videoNoCollectFlag){
        return "Y";
      }else{
        return "N";
      }

    },

    //人工审核
    artificialCheck() {
      let h5Param = this.artificialAiParam;
      h5Param.videoCollectAttachId = this.videoCollectAttachId;
      let url = "/xsb/ability/videoRecord/h5ArtificialCheck";
      this.$http.post(url, h5Param).then(res => {
        if (res.data.retCode == '0') {
          this.artificialFlag = false;
          this.$toast("人工审核成功，请继续办理业务！");
        } else {
          this.$alert(res.data.retMsg || '人工审核失败');
        }
      }).catch((respose) => {

        this.$alert('人工审核网络超时' + respose);
      })
    },

    //播放
    playVideo() {
      ClientJs.playRecord(this.filePath);
    },
    //视频采集选项组件开启
    openVideoChoice() {
      //视频采集选项
      ReadChoiceWhite({
        readChoiceList: this.readChoiceList,
        keyNumber: this.keyNumber,
        dictName: this.dictName,
        // videoCollectWhiteFlag:Storage.session.get('userInfo').videoCollectWhiteFlag, //白名单
      }, (obj) => {
        this.keyNumber = obj.keyNumber;
        this.dictName = obj.dictName;
        this.videoNoCollectFlag = obj.videoWhiteFlag; //视频不采集
        this.submitChoose(obj);
      });
    },

    //选择后重拍按钮
    reVideoChoice(readTypeNumber, dictName, readTelnum) {
      this.readTypeNumber = readTypeNumber;
      this.dictName = dictName;
      let mod;
      let readMod;
      if (readTypeNumber == '11') {//本人朗读
        this.isdaidu = '0';
        readMod = '1';
        mod = '1';
      } else if (readTypeNumber == '13') {//营业员代读（13中文 14 英文）
        this.isdaidu = '1';
        readMod = '3';
        mod = '1';
      } else if (readTypeNumber == '14') {//营业员代读（13中文 14 英文）
        this.isdaidu = '1';
        readMod = '4';
        mod = '1';
      } else if (readTypeNumber == '21') {//机读中文朗读
        this.isdaidu = '0';
        readMod = '5';
        mod = '3';
      } else if (readTypeNumber == '22') {//机读英文朗读
        this.isdaidu = '0';
        readMod = '6';
        mod = '3';
      }

      let daiduoper = "";
      if (this.isdaidu == '1') {
        daiduoper = this.uinfo.crmId;
      }
      let h5Param = {
        "region": this.uinfo.region,
        "crmId": this.uinfo.crmId,
        "workseq": this.netSrl,
        "telNum": readTelnum,
        "busiType": this.busiType,
        "isdaidu": this.isdaidu,// 代读
        "daiduoper": daiduoper,//代读人工号
        "seqtype": "2", //1 外围 2阿拉盯
        "clientType": /android|harmony/gi.test(navigator.userAgent) ? 'ANDROID' : 'IOS',
        "mod": mod,
        "readMod": readMod,
      };
      this.artificialAiParam = h5Param;
      let parentUrl = "/xsb/ability/videoRecord/videoUpload";
      let telnmNeed = (readTelnum.substring(readTelnum.length - 4)).replace(/(\w)(?=\w)/g, '$1 ');
      let subtitle = "";
      let noticeCallBack = "videoCollectCallBackMethod";
      let isRead = "0";
      let readContext = "";
      let language = "";
      let countdown = 20;
      //本人朗读
      if (readTypeNumber == '11') {
        // subtitle = "我实名办理并领取了\r\n尾号" + readTelnum.substring(readTelnum.length - 4) + "的电话卡，\r\n" +
        //   "不会转租转售，否则\r\n承担法律风险。";
        // //苏州地市修改文字
        // if(this.uinfo.region=='11'){
          subtitle = "我实名办理并领取了\r\n" + readTelnum + "的电话卡，\r\n" +
            "不会转租转售，否则\r\n承担法律风险。";
        // }
      }
      //机读中文
      if (readTypeNumber == '21') {
        // subtitle = "开始录制视频，请您知悉。\r\n您实名办理并领取了\r\n尾号为" + readTelnum.substring(readTelnum.length - 4) + "的电话卡，\r\n" +
        //   "您是否清楚该电话卡\r\n不得转租转售，出现\r\n的法律风险将由您承\r\n担，如清楚，请2秒后\r\n回答“是”。";
        // readContext = "开始录制视频，请您知悉。您实名办理并领取了尾号为" + telnmNeed + "的电话卡，" +
        //   "您是否清楚该电话卡不得转租转售，出现的法律风险将由您承担，如清楚，请2秒后回答是";
        // //苏州地市修改文字
        // if(this.uinfo.region=='11'){
          subtitle = "开始录制视频，请您知悉。\r\n您实名办理并领取了\r\n" + readTelnum + "的电话卡，\r\n" +
            "您是否清楚该电话卡\r\n不得转租转售，出现\r\n的法律风险将由您承\r\n担，如清楚，请2秒后\r\n回答“是”。";
          readContext = "开始录制视频，请您知悉。您实名办理并领取了" + readTelnum.split('').join(' ') + "的电话卡，" +
            "您是否清楚该电话卡不得转租转售，出现的法律风险将由您承担，如清楚，请2秒后回答是";
        // }
        language = "zh-CN";
        isRead = "1";
        countdown = 25;
      }
      //机读英文
      if (readTypeNumber == '22') {
        // subtitle = "start recording,\r\nPlease be informed that.\r\nYou apply and receive\r\nthe SIM card ending \r\n in " + readTelnum.substring(readTelnum - 4) + ". \r\n" +
        //   "Do you understand\r\nthat this phone card\r\nshall not be sublet or\r\nresale and the legal" +
        //   "risks\r\narising therefrom shall\r\nbe your own.\r\nPlease reply Yes ?";
        // if (/android|harmony/gi.test(navigator.userAgent)) {
        //   telnmNeed=this.getJiDuEnglish(telnmNeed);
        // }
        // readContext = "start recording,Please be informed that.You apply and receive the SIM card ending in " + telnmNeed + "." +
        //   "Do you understand that this phone card shall not be sublet or resale and the legal " +
        //   "risks arising therefrom shall be your own. Please reply Yes";
        //
        // //苏州地市修改文字
        // if(this.uinfo.region=='11'){
          subtitle = "start recording,\r\nPlease be informed that.\r\nYou apply and receive\r\nthe SIM card \r\n  " + readTelnum + ". \r\n" +
            "Do you understand\r\nthat this phone card\r\nshall not be sublet or\r\nresale and the legal" +
            "risks\r\narising therefrom shall\r\nbe your own.\r\nPlease reply Yes ?";
          let readNumber=this.getJiDuEnglishAll(readTelnum);

          readContext = "start recording,Please be informed that.You apply and receive the SIM card  " + readNumber + "." +
            "Do you understand that this phone card shall not be sublet or resale and the legal " +
            "risks arising therefrom shall be your own. Please reply Yes";
        // }

        language = "en-US";
        isRead = "1";
        countdown = 25;
      }
      //营业员代读中文
      if (readTypeNumber == '13') {
        // subtitle = "操作员说:\r\n您实名办理并领取了\r\n尾号" + readTelnum.substring(readTelnum.length - 4) + "的" +
        //   "电话卡,\r\n您是否清楚该电话卡\r\n不得转租转售，出现的\r\n法律风险将由您承担，\r\n如清楚，请回答“是”\r\n" +
        //   "客户回答:\r\n是的";
        // //苏州地市修改文字
        // if(this.uinfo.region=='11'){
          subtitle = "操作员说:\r\n您实名办理并领取了\r\n" + readTelnum + "的" +
            "电话卡,\r\n您是否清楚该电话卡\r\n不得转租转售，出现的\r\n法律风险将由您承担，\r\n如清楚，请回答“是”\r\n" +
            "客户回答:\r\n是的";
        // }
      }
      //营业员代读英文
      if (readTypeNumber == '14') {
        // subtitle = "操作员说:\r\nYou apply and receive\r\nthe SIM Card ending\r\nin " + readTelnum.substring(readTelnum.length - 4) + ".\r\n" +
        //   "Do you understand\r\nthat this phone card\r\nshall not be sublet or\r\nresale and the " +
        //   "legal risks\r\narising therefrom shall\r\nbe your own. \r\nPlease reply Yes ？\r\n客户回答:\r\nYes";
        countdown = 25;

        // //苏州地市修改文字
        // if(this.uinfo.region=='11'){
          subtitle = "操作员说:\r\nYou apply and receive\r\nthe SIM Card\r\n " + readTelnum + ".\r\n" +
            "Do you understand\r\nthat this  phone card\r\nshall not be sublet or\r\nresale and the " +
            "legal risks\r\narising therefrom shall\r\nbe your own. \r\nPlease reply Yes ？\r\n客户回答:\r\nYes";
        // }
      }
      // //代办
      // if (readTypeNumber == '12') {
      //   subtitle = "我受" + "xxxxx"+ "委托，\r\n实名办理并领取了\r\n尾号" +  readTelnum.substring( readTelnum.length - 4) + "的电话卡，\r\n" +
      //     "我将告知"+ "xxxxx" +"，\r\n不得转租转售，否则\r\n承担法律风险。";
      // }
      // if (readTypeNumber == '15') { // 过户代办
      //   subtitle = "我受" + "xxxxx" + "委托，\r\n实名办理尾号\r\n为" + readTelnum.substring(readTelnum.length - 4) + "的电话卡\r\n过户业务，\r\n" +
      //     "我将告知新机主，\r\n不得转租转售，否则\r\n承担法律风险。";
      // }
      // //机读中文
      // if (readTypeNumber == '23') { // 过户代办
      //   subtitle = "您受" +  "xxxxx" + "委托，\r\n实名办理尾号\r\n为" + readTelnum.substring(readTelnum.length - 4) + "的电话卡\r\n过户业务，\r\n" +
      //     "您将告知新机主，\r\n不得转租转售，否则\r\n承担法律风险，如清楚，请2秒后\r\n回答“是”。";
      //   readContext = "您受" +  "xxxxx" + "委托，实名办理尾号为" + telnmNeed + "的电话卡过户业务，" +
      //     "您将告知新机主，不得转租转售，否则承担法律风险，如清楚，请2秒后回答“是”。";
      //   language = "zh-CN";
      //   isRead="1";
      //   countdown=25;
      // }
      //h5Param 透传参数, parentUrl 后台路径 , subtitle 展示的文字, noticeCallBack回调函数 ,isRead是否机读,
      ClientJs.collectVideoMachine(JSON.stringify(h5Param), parentUrl, subtitle, noticeCallBack, isRead, readContext, language, countdown);
    },
    //数字转英文
    getJiDuEnglish(telnmNeed){
      let telNumList=[telnmNeed.substring(0,1) , telnmNeed.substring(2,3) , telnmNeed.substring(4,5) , telnmNeed.substring(6,7)];
      let telNumReturn="";
      for(let i=0;i<telNumList.length;i++){
        if(telNumList[i]=='0'){
          telNumReturn=telNumReturn+"zero ";
        }else if(telNumList[i]=='1'){
          telNumReturn=telNumReturn+"one ";
        }else if(telNumList[i]=='2'){
          telNumReturn=telNumReturn+"two ";
        }else if(telNumList[i]=='3'){
          telNumReturn=telNumReturn+"three ";
        }else if(telNumList[i]=='4'){
          telNumReturn=telNumReturn+"four ";
        }else if(telNumList[i]=='5'){
          telNumReturn=telNumReturn+"five ";
        }else if(telNumList[i]=='6'){
          telNumReturn=telNumReturn+"six ";
        }else if(telNumList[i]=='7'){
          telNumReturn=telNumReturn+"seven ";
        }else if(telNumList[i]=='8'){
          telNumReturn=telNumReturn+"eight ";
        }else if(telNumList[i]=='9'){
          telNumReturn=telNumReturn+"nine ";
        }
      }
      return telNumReturn;

    },

    getJiDuEnglishAll(telnmNeed){
      let telNumList=Array.from(telnmNeed);;
      let telNumReturn="";
      for(let i=0;i<telNumList.length;i++){
        if(telNumList[i]=='0'){
          telNumReturn=telNumReturn+"zero ";
        }else if(telNumList[i]=='1'){
          telNumReturn=telNumReturn+"one ";
        }else if(telNumList[i]=='2'){
          telNumReturn=telNumReturn+"two ";
        }else if(telNumList[i]=='3'){
          telNumReturn=telNumReturn+"three ";
        }else if(telNumList[i]=='4'){
          telNumReturn=telNumReturn+"four ";
        }else if(telNumList[i]=='5'){
          telNumReturn=telNumReturn+"five ";
        }else if(telNumList[i]=='6'){
          telNumReturn=telNumReturn+"six ";
        }else if(telNumList[i]=='7'){
          telNumReturn=telNumReturn+"seven ";
        }else if(telNumList[i]=='8'){
          telNumReturn=telNumReturn+"eight ";
        }else if(telNumList[i]=='9'){
          telNumReturn=telNumReturn+"nine ";
        }
      }
      console.log(telNumReturn);
      return telNumReturn;
    },
  },
  mounted() {
    window['versionBack'] = (res) => {
      let obj = eval('(' + res + ')');
      Storage.set('appVersion', obj.verison);
      if(this.waitBusiness){
        this.permissionCheck(this.waitBusiness);
      }
    }
    //调用客户端获取当前版本
    this.checkCurVersion();
  },
}
