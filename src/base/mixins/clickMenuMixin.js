import Storage from '@/base/storage.js';

const MAX_TOP_MENUS = 16;//保存16个常用菜单


//菜单点击量多的放【常用菜单】处理
export const frequentClickMixin = {
    data(){
        return{
            menuClickKey:'h5_clickMenuMap'//缓存菜单点击的key
        }
    },
    methods: {
        
        /**
         * 获取缓存里的常用菜单
         * @param {菜单类型} menuType ：tool表示常用工具  business表示常用业务菜单
         * @param {*} num  取前多少个菜单
         * @param {}filterList 不在这个菜单列表里的
         */
        getMenuFromStorage(menuType,filterList,num=8){
            try {
                let menuObjStorage = Storage.get(this.menuClickKey);
                if(!menuObjStorage){
                    return [];
                }
                let menuMap = menuObjStorage[menuType] || {};
                
                let arr = [];
                let filterIds = this.getIdsFromList(filterList);
                // 下线菜单需要通过代码来删除客户端的本地缓存 add by qhuang 20250617
                filterIds[filterIds.length] = '100236';//20250616下线家庭通信诊断菜单100236、100454,2个月后可删除这两行代码
                filterIds[filterIds.length] = '100454';
                //循环菜单MAP
                for (let key in menuMap) {
                    let value = menuMap[key];
                    
                    let ele = filterIds.find(element => element === key);
                    if(!ele){//不在filterList里
                        value.item.clickNum = value.clickNum;//用于判断是来源于智能推荐菜单
                        arr.push(value.item);
                    }
                }
            
                let menuList = arr.slice(0,num);//.slice(0, 8)
                return menuList;
            } catch (e) {
            }
            
        },
        // filterList?.map(item => item.funcId) ?? [];
        getIdsFromList(filterList){
            let filterIds = [];
            if(!filterList || filterList.length == 0){
                return filterIds;
            }
            // 获取所有funcId的集合
            for(let i = 0 ; i < filterList.length; i++){
                filterIds.push(filterList[i].funcId);
            }
            return filterIds;    

        },
        /**
         * 保存点击的菜单信息到缓存里
         * @param {菜单类型} menuType ：tool表示常用工具  business表示常用业务菜单
         * @param {菜单原子对象} menuItem  				
         *      authenType: -1
				featureType: "panc_manage"
				funcId: "100080"
				funcName:''
				hasCrmid: ''
				hasPwd: ''
				isNewFeature: 0
				opId: "fsop"
				opParentid: "fsop"
				parentId: undefined
				picPath: "100080"	
         */
        addMenuToStorage(menuType,item){
            try {
                let menuObjStorage = Storage.get(this.menuClickKey);
                if(!menuObjStorage){
                    menuObjStorage = {};
                }
                let menuMap = menuObjStorage[menuType] || {};
                let menuItem = {};
                if(item.privId){
                    menuItem = this.transformObj(item)
                } else {
                    menuItem = item;
                }
                
                let menuId = menuItem.funcId;//菜单编号
                let menuObj = menuMap[menuId];
                //检查菜单是否已经在storage缓存的列表中 
                if(!menuObj){//不在缓存列表里
                    menuMap[menuId] = {
                        'clickNum':1,//点击量为1
                        'item':menuItem
                    }
                    //删除不常用的菜单
                    this.delItemFromMap(menuMap);
                } else {//在缓存列表里
                    let count = menuObj.clickNum || 1;
                    count++;
                    menuObj.clickNum = count;
                    menuMap[menuId] = menuObj;
                }
                menuObjStorage[menuType] = menuMap;
                Storage.set(this.menuClickKey,menuObjStorage);
            } catch (e) {
                
            }  
        },
        //gridMenuList里的菜单对象是funcId等，菜单表里返回的是privId等
        transformObj(info){
            let funinfo = {};
            funinfo.funcName = info.privName;
            funinfo.funcId = info.privId;
            funinfo.hasPwd = info.hasPwd;
            funinfo.parentId = info.parentId;
            funinfo.opParentid = info.opParentid;
            funinfo.opId = info.opId;
            funinfo.picPath = info.picId;
            funinfo.hasCrmid = info.hasCrmid;
            funinfo.isNewFeature = info.isNewFeature;
            funinfo.featureType = info.featureType;
            funinfo.authenType = info.authenType;
            return funinfo;
        },
        //删除map里的对象
        delItemFromMap(menuMap){
            let menuLen = this.getMapLength(menuMap);//当前缓存列表里的菜单个数
            //判断当前缓存列表里是否大于最大值
            if(menuLen < MAX_TOP_MENUS){
                return;
            }
            // 找出具有最小count值的键  
            let minMenuKey = null;  
            let minCount = Infinity;
            //循环菜单MAP
            for (let key in menuMap) {
                if (menuMap[key].clickNum < minCount) { 
                    minCount = menuMap[key].clickNum;  
                    minMenuKey = key;  
                }  
            }
            // 如果找到了最小count值的menu键
            if (minMenuKey !== null) {
                // 删除特定键
                menuMap.delete(minMenuKey);
            }
        },
        //获取map里的对象数量
        getMapLength(map={}){
            return Object.keys(map).length;
        }
    },
}