export const menuChain = {
  props: {
    // 菜单链父级，组件传递方式
    rootLevel: {
      type: Number,
      default: -1
    }
  },
  data() {
    return {
      // 菜单链父级，路由传递方式
      dataRootLevel: -1
    }
  },
  methods: {
    // 更新菜单链
    updateMenuChain(privId, level = null, resetLowerLevels = true) {
      if(level) {
        window.updateBreadId(level, privId, resetLowerLevels);
      } else if(this.rootLevel && this.rootLevel !== -1) {
        // 组件有传父级等级使用父级等级加一
        window.updateBreadId(this.rootLevel + 1, privId, resetLowerLevels);
      } else if(this.dataRootLevel && this.dataRootLevel !== -1) {
        // 组件路由传递了等级使用路由等级加一
        window.updateBreadId(this.dataRootLevel + 1, privId, resetLowerLevels);
      }
      // 都为空，不添加
    },
    //初始化一级菜单链
    initFirstMenuLevel(privId){
      if(privId){
        this.updateMenuChain(privId,1);//初始化一级菜单入口
      }
    }
  }
}
