import Storage from '@/base/storage'

export const zhuoWangCallJs = {
    data() {
        return {
            tauthId: '',  //外呼工号
            telB: '',     //用户手机号码
            telX: '',    //客户经理虚拟号码
            telNum: '',   //客户号码
            userId:'',     //客户编码
            workId:'',     //任务编码
            uinfo:{},  //用户信息,
            senceType:'',

            subAppId:'',
            subAppKey:'',
        }
    },
    created() {
        //登录人信息
        this.uinfo = Storage.session.get('userInfo')
    },
    methods: {
        //判断权限
        WaiHuStart(telNum,userId,workId,senceType){
            this.telNum = telNum;
            this.userId = userId;
            this.workId = workId;
            this.senceType = senceType;
            let paramObj = {
                telNum:Storage.session.get('userInfo').servNumber,
                senceType:this.senceType
            }
            this.$http.post('/xsb/gridCenter/outCallByzw/h5QryOutCallFlag', paramObj).then((res) => {
                console.info("查询权限",paramObj,res.data)
                //有权限
                if (res.data.retCode == '0') {
                    this.qryConfigBuWaihu();
                } else {
                    this.callUser(false)
                }
            })
        },

        //发起卓望外呼  telNum  是客户号码
        qryConfigBuWaihu(telNum,userId,workId,senceType){
            let _this = this;
            this.telNum = telNum;
            this.userId = userId;
            this.workId = workId;
            this.senceType = senceType;

            _this.telB = Storage.session.get('userInfo').servNumber
            let paramCfg = {
                telB: Storage.session.get('userInfo').servNumber
            }
            //查询外呼工号配置
            _this.$http.post('/xsb/gridCenter/outCallByzw/h5GetOutOperInfo', paramCfg).then((res) => {
                console.info("查询配置",paramCfg)
                if (res.data.retCode == '0') {
                    _this.authId = res.data.data.authId
                    _this.telX = res.data.data.telX
                    if(res.data.data && res.data.data.subAppId){
                        _this.subAppId = res.data.data.subAppId;
                    }else {
                        _this.$alert('您的外呼工号未配置渠道号，暂无外呼权限，请先联系地市管理员')
                        return
                    }
                    if(res.data.data && res.data.data.subAppKey){
                        _this.subAppKey = res.data.data.subAppKey;
                    }else {
                        _this.$alert('您的外呼渠道号未生效，暂无外呼权限，请先联系地市管理员')
                        return
                    }
                    //查询外呼信息记录
                    _this.getOutCallRecord()
                } else {
                    _this.$alert('你还未绑定外呼工号，暂无外呼权限，请先联系地市管理员')
                }
            })
        },

        //查询外呼信息记录
        getOutCallRecord() {
            let _this = this
            let paramRecord = {
                authId: _this.authId,
                telB: Storage.session.get('userInfo').servNumber
            }
            _this.$http.post('/xsb/gridCenter/outCallByzw/h5GetOutCallInfo', paramRecord).then((res) => {
                console.info('查询外呼信息记录', paramRecord,res.data)
                if (res.data.retCode == '0') {
                    //存在记录
                    if (res.data.data) {
                        //获取bingid编码
                        //超过一分钟，1、update数据解绑 ， 2、AXB绑定  3、insert记录
                        if(_this.judgeTime(res.data.data.callStartTime)){
                            //1、update数据解绑
                            _this.updateBindInfo(res.data.data.bindId);

                        }else {
                            //没有超过一分钟，判断客户是否一致
                            let telA = '86' + _this.telNum;
                            if(res.data.data.telA == telA){
                                console.info("判断一致")
                                //一致，直接拉起客户端，手机外呼
                                //拉起客户端方法
                                _this.callUser(true)
                            }else {
                                //不一致  1、AXB解绑  2、update数据解绑 ， 3、AXB绑定  4、insert记录
                                _this.axbUnbinging(res.data.data.bindId);
                            }
                        }

                    } else {
                        //不存在记录，调用  卓望-AXB呼叫解绑接口
                        _this.AxbBinding()
                    }
                } else {
                    _this.$alert(_this.telB + '你好，查询外呼记录异常：' + res.data.retMsg)
                }
            })
        },

        //一分钟时间判断
        judgeTime(callStartTime) {
            console.info("判断时间")
            // 解析给定的时间字符串为 Date 对象
            if(callStartTime.indexOf('.') == '-1'){
                callStartTime = callStartTime.replace('.', '-0');
            }
            console.info("callStartTime",callStartTime);
            const givenTime = new Date(callStartTime); // 去掉毫秒数后的 .0
            // 检查日期是否有效
            if (isNaN(givenTime)) {
                //数据处理失败直接数据解绑 拉起卓望外呼
                console.info("callStartTime时间处理失败")
                return true;
            }
            // 获取当前时间并创建一个 Date 对象
            const currentTime = new Date()
            // 计算时间差（以毫秒为单位）
            const timeDifference = currentTime - givenTime
            // 将时间差转换为分钟
            const timeDifferenceInMinutes = timeDifference / (1000 * 60)
            console.info("currentTime",currentTime)
            console.info("givenTime",givenTime)
            console.info("timeDifference",timeDifference)
            // 判断时间差是否超过一分钟
            if (Math.abs(timeDifferenceInMinutes) > 1) {
                console.info('给定时间与当前时间的差距超过一分钟')
                return true;
            } else {
                console.info('给定时间与当前时间的差距未超过一分钟')
                return false;
            }
        },

        //卓望-AXB呼叫解绑接口
        axbUnbinging(bindId){
            let _this = this;
            let paramUnBind = {
                bindId: bindId,
                subAppId: this.subAppId,
                subAppKey: this.subAppKey,
            }
            _this.$http.post('/xsb/gridCenter/outCallByzw/h5AxbUnbinging', paramUnBind).then((res) => {
                console.info("AXB解绑",bindId)
                let { retCode, data, retMsg } = res.data;
                if(retCode == '0'){
                    //AXB解绑成功,  2、update数据解绑 ， 3、AXB绑定  4、insert记录
                    _this.updateBindInfo(bindId);
                }else {
                    _this.$alert(_this.telB + '你好，' + retMsg)
                }
            })
        },
        //update数据解绑
        updateBindInfo(bindId){
            let _this = this;
            let paramUpdate = {
                authId:_this.authId,
                telB: _this.telB,
                bindId:bindId
            }
            _this.$http.post('/xsb/gridCenter/outCallByzw/h5UpdateBindInfo', paramUpdate).then((res) => {
                console.info("数据解绑update",paramUpdate)
                let { retCode, retMsg } = res.data;
                if(retCode == '0'){
                    //数据解绑成功,  2、AXB绑定  3、insert记录
                    _this.AxbBinding();
                }else {
                    _this.$alert(_this.telB + '你好，' + retMsg)
                }
            })
        },

        //卓望-AXB呼叫绑定接口
        AxbBinding() {
            let _this = this
            let paramBind = {
                telA: '86' + _this.telNum,
                authId: _this.authId,
                expiration: '60',
                userData: '',
                subAppId: this.subAppId,
                subAppKey: this.subAppKey,
            }
            _this.$http.post('/xsb/gridCenter/outCallByzw/h5AxbBinding', paramBind).then((res) => {
                let { retCode, data, retMsg } = res.data
                console.info('卓望-AXB呼叫绑定接口', paramBind , res.data)
                if (retCode == '0') {
                    if (data.data) {
                        let requestId = data.requestId;
                        //绑定成功，保存外呼记录
                        _this.OutCallRecord(requestId,data.data.bindId)
                    } else {
                        _this.$alert(_this.telB + '你好，卓望服务异常，不存在绑定编码')
                    }
                } else {
                    _this.$alert(_this.telB + '你好，' + retMsg)
                }
            })
        },

        //外呼信息记录
        OutCallRecord(requestId,bindId) {
            let _this = this
            let paramInsert = {
                requestId:requestId,
                bindId: bindId,
                authId: _this.authId,
                telB: Storage.session.get('userInfo').servNumber,
                telX: _this.telX,
                telA: '86' + _this.telNum,
                userId:  _this.userId,
                isBind: '1',
                workId: _this.workId,
                callChannel: '1'  //1：一线；2：营业厅；
            }
            _this.$http.post('/xsb/gridCenter/outCallByzw/h5OutCallRecord', paramInsert).then((res) => {
                console.info("外呼信息记录",paramInsert)
                if (res.data.retCode == '0') {
                    //拉起客户端方法
                    _this.callUser(true)
                } else {
                    _this.$alert(_this.telB + '你好，' + res.data.retMsg)
                }
            })
        },
    }
}
