import MockClientJs from './clientjs_browser.js'
/**
 * 和客户端交互的接口
 */
//针对自动化流程，添加自动化mock的签名
let token = window.sessionStorage.getItem('tokenId');
let ClientJs = {
	longitude: '',//经度
	latitude: '',//纬度
	address: '',//定位地址
	isAndroid: (/android/gi).test(navigator.appVersion)||(/OpenHarmony/gi).test(navigator.userAgent),
	isIos:navigator.userAgent.match(/iPhone/i) || navigator.userAgent.match(/iPad/i),

	/**
	 * 获取路由、手机设备信息
	 * phonemodel 手机型号
	 * phonesystemNum 手机版本
	 * serverUrl 当前路由信息
	 * verison 版本信息
	 */
	getSysInfo:function(callbackfunc){
		if(this.isAndroid){
			if(window.WebViewFunc){
				window.WebViewFunc.getSysInfo(callbackfunc);
			}
		}else{
			window.location="clientRequest:getSysInfo::" + callbackfunc;
		}
	},
	//注销登录
	userLoginOut: function(){
		if(this.isAndroid){
			if(window.WebViewFunc){
				window.WebViewFunc.userLoginOut();
			}
		}else{
			window.location="clientRequest:userLoginOut";
		}
	},
	userLoginOutPanc: function(outType){
		if(this.isAndroid){
			if(window.WebViewFunc){
				window.WebViewFunc.userLoginOut(outType);
			}
		}else{
			window.location="clientRequest:userLoginOut::"+outType;
		}
	},

	/*
	* 打开GPS
	*/
	getLocation: function(locType,callbackfunc){
		if(this.isAndroid){
			window.WebViewFunc.getLocation(locType,callbackfunc);
		}else{
			window.location="clientRequest:getLocation::"+locType+"::"+callbackfunc;
		}
	},
	/*
	* 打开webview页面
	*/
	openWebKit : function(url,title,titleFlag
		,screenType,param,hasRightBtn,rightIcon
		,rightJs,hasRightBtn1,rightIcon1,rightJs1,hasListen){
			//Storage.get('webUrl') +
		var webUrl = window.localStorage.getItem('webUrl');
		var isMain = 1;//走现网
		if(webUrl && (~webUrl.indexOf('183')||~webUrl.indexOf('spare.'))){//走备用中心
			isMain = 0;
		}
		url += '&isMain=' +isMain;
		console.info(url+':::in webkit');
		var params = {};
		var paramStr;
		params.url = url;
		params.title=title;
		params.titleFlag=titleFlag;
		params.screenType=screenType;
		params.param=param;
		params.hasRightBtn=hasRightBtn;
		params.rightIcon=rightIcon;
		params.rightJs=rightJs;
		params.hasRightBtn1=hasRightBtn1;
		params.rightIcon1=rightIcon1;
        params.rightJs1=rightJs1;
        params.hasListen=hasListen;
		paramStr=JSON.stringify(params);
		if(this.isAndroid){
			window.WebViewFunc.openWebKit(paramStr);
		}else{
			window.location="clientRequest:openWebKit::"+paramStr;
		}
	},
	/**
	 * 打开webview页面，并返回打开页面的状态
	 * @param {页面地址} url
	 * @param {参数对象} paramObj
	 */
	openWebKitWithStatus:function(url,paramObj){
		let webUrl = window.localStorage.getItem('webUrl');
		let isMain = 1;//走现网
		if(webUrl && (~webUrl.indexOf('183')||~webUrl.indexOf('spare.'))){//走备用中心
			isMain = 0;
		}
		if(~url.indexOf('?')){
			url += '&';
		} else {
			url += '?';
		}
		url += 'isMain=' +isMain;
		paramObj.url = url;//打开的页面链接
		let paramStr = JSON.stringify(paramObj);
		if(this.isAndroid){
			window.WebViewFunc.openWebKitWithStatus(paramStr);
		}else{
			window.webkit.messageHandlers.openWebKitWithStatus.postMessage(paramStr);
		}
	},

	/*
	* 打开webview页面,优化参数
	*/
	openWebKitNew : function(url,paramObj){
		let webUrl = window.localStorage.getItem('webUrl');
		let isMain = 1;//走现网
		if(webUrl && (~webUrl.indexOf('183')||~webUrl.indexOf('spare.'))){//走备用中心
			isMain = 0;
		}
		url += '&isMain=' +isMain;
		paramObj.url = url;
		let paramStr = JSON.stringify(paramObj);
		if(this.isAndroid){
			window.WebViewFunc.openWebKit(paramStr);
		}else{
			window.location="clientRequest:openWebKit::"+paramStr;
		}
	},

	 //下载并且打开附件页面
	openAttachment: function(url,fname){

		if(this.isAndroid){
			window.WebViewFunc.downloadAttachment(url,fname);
		}else{
			window.location="clientRequest:downloadAttachment::"+url+"::"+fname;
		}
	},
	//调用客户端扫描匹配过的蓝牙
	autoReadDevise:function(cbFn){
		if(this.isAndroid){//安卓设备
			window.upload.autoReadDevise(cbFn,false);
		 }else{//苹果设备
			window.location="clientrequest:autoReadDevise::"+cbFn + "::1";
		 }
	},
	//打开拍照 1表示行商
	openCamera: function(param,noticeCallBack,cbfun2,tmpIsPublicSrc){
		console.info(tmpIsPublicSrc+"=====tmpIsPublicSrc");
		if(this.isAndroid){
			window.WebViewFunc.openCameraForALD(param,noticeCallBack,cbfun2,"1",tmpIsPublicSrc);/*  */
		}else{
			window.location="clientrequest:fsopOpenCamera::"+ param + "::" +noticeCallBack+"::" + cbfun2+"::"+"1" +"::"+tmpIsPublicSrc;
		}
	},

  //打开拍照 1表示行商
  openCameraPortrait: function(param,noticeCallBack,cbfun2,tmpIsPublicSrc){
    console.info(tmpIsPublicSrc+"=====tmpIsPublicSrc");
    if(this.isAndroid){
      window.WebViewFunc.openFigureCamera(param,noticeCallBack,cbfun2,"1",tmpIsPublicSrc);/*  */
    }else{
      let paramCamer= {
          compareParams:param,
          photoCallBackName:noticeCallBack,
          aldPhotoCallBackName:cbfun2,
          isFsopPhoto:'1',
          isAldSelectNumber:tmpIsPublicSrc,
          faceServerUrl:'',
      }
        window.webkit.messageHandlers.getFaceCamera.postMessage(paramCamer);
    }
  },
	//读取身份证
	readIdCard: function(cbFn,saveCb,tmpIsPublicSrc,idcardParam){
		console.info(tmpIsPublicSrc+"=====tmpIsPublicSrc")
		if(this.isAndroid){//安卓设备
			let tmpFlg = false;
			if(tmpIsPublicSrc == '1'){
				tmpFlg = true;
			}
			if(window.upload.readIdCardPassthrough){
				console.info('readthrough::' + idcardParam)
				window.upload.readIdCardPassthrough(cbFn,saveCb,tmpFlg,idcardParam);
			}else {
				window.upload.readIdCard(cbFn,saveCb,tmpFlg);
			}
		}else{//苹果设备
			window.location="clientrequest:readIdCard::"+cbFn +"::" + saveCb +"::"+tmpIsPublicSrc+"::"+idcardParam;
		}
	},
	//展示所有的蓝牙设备
	showLanyaDevice: function(cbFn,saveCb,tmpIsPublicSrc,idcardParam){
		if(this.isAndroid){//安卓设备
			let tmpFlg = false;
			if(tmpIsPublicSrc == '1'){
				tmpFlg = true;
			}
			if(window.upload.getIdNumPassthrough){
				window.upload.getIdNumPassthrough(cbFn,saveCb,tmpFlg,idcardParam);// 调用安卓客户端
			} else {
				window.upload.getIdNum("1",cbFn,saveCb,tmpFlg);// 调用安卓客户端
			}
		}else{//苹果设备
			window.location="clientrequest:getIdNum::" + cbFn +"::" + saveCb +"::"+tmpIsPublicSrc+"::"+idcardParam;
		}
	},

	/**
	 * 刷脸展示所有的蓝牙设备
	 * @param {读证回调H5的方法} cbFn
	 * @param {上传证件到服务端后回调H5的方法} saveCb
	 * @param {是否走公用（1则是非预配号）} tmpIsPublicSrc
	 * @param {透传的参数} idcarParam
	 * @param {上传的服务端路径} idcardServerUrl
	 */
	showFaceLanya: function(cbFn,saveCb,tmpIsPublicSrc,idcarParam,idcardServerUrl){
		if(this.isAndroid){//安卓设备
			// getIdNumExtension(String uploadUrl, String uploadParams,
			// 	String callBack,  String serverCallBack)
			window.upload.getIdNumExtension(idcardServerUrl,idcarParam,cbFn,saveCb);// 调用安卓客户端
		}else{//苹果设备
			// blueToothCallBackName 	读证成功后回调前端身份证信息
			// aldCallBackName	上传成功后回调结果 ，不解析 直接把json报文返回给前端
			// isAldSelectNumber	是否选号入网    1,是 其它否
			// idcarParam	新增字段 透传参数，必须传入 没有透传信息传{} , 客户端3des加密,服务器获取参数 key:idCardInfo
			// idcardServerUrl	新增字段 动态上传地址，非常关键字段，如果不传就走老一套逻辑     比如格式: xsb/ability/trueName/faceCheck

			window.location="clientrequest:getIdNum::" + cbFn +"::" + saveCb +"::"+tmpIsPublicSrc+"::"+idcarParam+"::"+idcardServerUrl;
		}
	},

	//读取身份证
	readFaceIdCard: function(cbFn,saveCb,tmpIsPublicSrc,idcarParam,idcardServerUrl){
		if(this.isAndroid){//安卓设备
			// let tmpFlg = false;
			// if(tmpIsPublicSrc == '1'){
			// 	tmpFlg = true;
			// }
			//String uploadUrl, String uploadParams, String callBack, final String callBack2
			window.upload.readIdCard(idcardServerUrl,idcarParam,cbFn,saveCb);
		}else{//苹果设备
			// blueToothCallBackName 	读证成功后回调前端身份证信息
			// aldCallBackName	上传成功后回调结果 ，不解析 直接把json报文返回给前端
			// isAldSelectNumber	是否选号入网    1,是 其它否
			// idcarParam	新增字段 透传参数，必须传入 没有透传信息传{} , 客户端3des加密,服务器获取参数 key:idCardInfo
			// idcardServerUrl	新增字段 动态上传地址，非常关键字段，如果不传就走老一套逻辑     比如格式: xsb/ability/trueName/faceCheck

			window.location="clientrequest:readIdCard::"+cbFn +"::" + saveCb +"::"+tmpIsPublicSrc+"::"+idcarParam+"::"+idcardServerUrl;
		}
	},
	//刷脸拍照
	openFaceCamera: function(param,noticeCallBack,cbfun2,tmpIsPublicSrc,compareUrl){
		if(this.isAndroid){
			//openCameraForALD(String compareParams, String mCallBackMethod, String cbFunc2,
			//String isFsop, String tmpPublicSrc, String compareUrl)
			window.WebViewFunc.openCameraForALD(param,cbfun2,noticeCallBack,"1",tmpIsPublicSrc,compareUrl);/*  */
		}else{
			// compareParams
			// photoCallBackName
			// aldPhotoCallBackName
			// isFsopPhoto
			// isAldSelectNumber
			// faceServerUrl
			window.location="clientrequest:fsopOpenCamera::"+ param +"::" + cbfun2+ "::" +noticeCallBack +"::"+"1" +"::"+tmpIsPublicSrc+"::"+ compareUrl;
		}
	},
	//签名
	qianming:function(cbFn){
		let type='Sign';
        if (this.isAndroid) {
            window.WebViewFunc.createImageForPhoneLocal(type,cbFn);
        } else {
            window.location = "clientRequest:createImageForPhoneLocal::" + type+"::"+ cbFn;
        }
	},

	//登录成功回调
	autologinInterface:function(cbFn){
		try{
			if (this.isAndroid) {
				//window.WebViewFunc.autologinInterface(cbFn);//没调用到，2021-12-8注销
				window.WebViewFunc.userLoginOut();
			} else {
				window.location = "clientRequest:sessionAutoLogin::" + cbFn;
				//如果在浏览器则1秒后跳404页面
				window.setTimeout(()=> {
					this.go404();
				},0);
			}
		}catch(e){
			this.go404();
		}

	},
	go404:function(){
		let url = window.location.href;
		url = url.substring(0, url.indexOf('?')) + '#/page404';
		window.history.replaceState(null, "", url);//如果没有获取到客户端重新登录的方法，则跳到404
		window.history.go(0);
	},
    //调用发短信功能
    openMessageView:function(phoneNumber,messageContent){
        if(this.isAndroid){
            window.WebViewFunc.openMessageView(phoneNumber,messageContent);
        }else{
            window.location="clientrequest:openMessageView::"+phoneNumber+"::"+messageContent;
        }
	},

	/*
	* 自动搜索蓝牙并连接
	*/
	searchSimDevice:function(callbackfunc){
		if(this.isAndroid){
			window.WebViewFunc.searchSimDevice(callbackfunc);
		}else{
			window.location="clientRequest:searchSimDevice::"+callbackfunc;
		}
	},
	/*
	* 搜索蓝牙设备列表
	*/
	connectDevice:function(callbackfunc){
		if(this.isAndroid){
			window.WebViewFunc.connectDevice(callbackfunc);
		}else{
			window.location="clientRequest:connectDevice::"+callbackfunc;
		}
	},
    /*
	* 断开连接SIM卡设备
	*/
	disConnectSimDevice:function(callbackfunc){
	   	if(this.isAndroid){
	   		window.WebViewFunc.disConnectSimDevice(callbackfunc);
	   	}else{
	   		window.location="clientRequest:disConnectSimDevice::"+callbackfunc;
	   	}
    },
	/*
	* 获取SIM卡信息
	*/
	getSimCardInfo:function(callbackfunc){
		if(this.isAndroid){
			if(window.WebViewFunc.getCardInfoNewMethord){
				window.WebViewFunc.getCardInfoNewMethord(callbackfunc);
			} else {
				alert('请升级APP');
			}
		}else{
			window.location="clientRequest:getCardInfoNewMethord::"+callbackfunc;
		}
	},
	/*
	* 写SIM卡
	*/
	writeCardData:function(writeData,callbackfunc){
		if(this.isAndroid){
			if(window.WebViewFunc.writeCardNewMethod){
				window.WebViewFunc.writeCardNewMethod(writeData,callbackfunc);
			} else {
				alert('请升级APP');
			}
		}else{
			window.location="clientRequest:writeCardNewMethod::"+writeData+"::"+callbackfunc;
		}
	},

	//打开拍照 1表示行商
	openCameraOne: function(photoCb){
		if(this.isAndroid){
			window.WebViewFunc.openCamera(photoCb,"1");
		}else{
			window.location="clientrequest:fsopOpenCamera::"+ photoCb + "::"+"1";
		}
	},
	//手机相册拍照
	//isPreView 是否需要预览，1是，其他值否。预览时是，会将照片转换成base64字符串回调给页面
	openCameraAndShow: function(isPreView,callbackfunc){
		//图片固定传值 image_file
		let fileType = 'image_file';
		let fileNameDomId = '';
		let filePathDomId = '';
		let fileIdDomId = '';
		if(this.isAndroid){
			window.WebViewFunc.openFileSelect(fileType,fileNameDomId,filePathDomId,fileIdDomId,callbackfunc,isPreView);
		}else{
			window.location = "clientrequest:openFileFeedBackSelect::"+callbackfunc+"::1::0";
		}
	},
    //获取录音文件
    openAudioFile: function(isPreView,isFileDir,callbackfunc){
        //图片固定传值 image_file
        let fileType = 'image_file';
        let fileNameDomId = '';
        let filePathDomId = '';
        let fileIdDomId = '';
        let isCompress = '';  //图片是否需要压缩，1是，其他值否，缺省时默认为1
        if(this.isAndroid){
            window.WebViewFunc.openFileSelect(fileType,fileNameDomId,filePathDomId,fileIdDomId,callbackfunc,isPreView,isCompress,isFileDir);
        }else{
            window.location = "clientrequest:openFileFeedBackSelect::"+callbackfunc+"::1::0";
        }
    },
    //手机相册录像
    //isPreView 是否需要预览，1是，其他值否。预览时是，会将照片转换成base64字符串回调给页面
    openCameraVideo: function(selectType,callbackfunc){
        if(this.isAndroid){
            window.WebViewFunc.openAlbumAndCamera(selectType,callbackfunc);
        }else{
            window.location = "clientrequest:openAlbumAndCamera::"+selectType+"::"+callbackfunc;
        }
    },


    //手机拍照(压缩)
  openCameraAndYaSuo: function(callbackfunc,CameraParams){
	  //{"params":[{"title":"相册","type":"2"},{"title":"相机","type":"1"},{"title":"取消","type":"-1"}],"width":"","height":"","fileSize":"60"}
    if(this.isAndroid){
      window.WebViewFunc.openCameraOrPhoto(callbackfunc,CameraParams);
    }else{
      window.location = "clientrequest:openCamraComperssion::"+callbackfunc+"::"+CameraParams;
    }
  },
  //手机相册拍照 （泛渠道身份证正面）
  //isPreView 是否需要预览，1是，其他值否。预览时是，会将照片转换成base64字符串回调给页面
  openIdCardFront: function(isPreView,callbackfunc){
    //图片固定传值 image_file
    let fileType = 'image_file';
    let fileNameDomId = '';
    let filePathDomId = '';
    let fileIdDomId = '';
    let phoneType = '1';
    if(this.isAndroid){
      window.WebViewFunc.openFileSelectNewCamera(fileType,fileNameDomId,filePathDomId,fileIdDomId,callbackfunc,isPreView,phoneType);
    }else{
      window.location = "clientrequest:openFileFeedBackSelect::"+callbackfunc+"::1::0::1";
    }
  },
  //手机相册拍照 （泛渠道身份证反面）
  //isPreView 是否需要预览，1是，其他值否。预览时是，会将照片转换成base64字符串回调给页面
  openIdCardBack: function(isPreView,callbackfunc){
    //图片固定传值 image_file
    let fileType = 'image_file';
    let fileNameDomId = '';
    let filePathDomId = '';
    let fileIdDomId = '';
    let phoneType = '2';
    if(this.isAndroid){
      window.WebViewFunc.openFileSelectNewCamera(fileType,fileNameDomId,filePathDomId,fileIdDomId,callbackfunc,isPreView,phoneType);
    }else{
      window.location = "clientrequest:openFileFeedBackSelect::"+callbackfunc+"::1::0::2";
    }
  },
  //手机相册拍照 （泛渠道注册人脸）
  //isPreView 是否需要预览，1是，其他值否。预览时是，会将照片转换成base64字符串回调给页面
  openRegistFace: function(isPreView,callbackfunc){
    //图片固定传值 image_file
    let fileType = 'image_file';
    let fileNameDomId = '';
    let filePathDomId = '';
    let fileIdDomId = '';
    let phoneType = '3';
    if(this.isAndroid){
      window.WebViewFunc.openFileSelectNewCamera(fileType,fileNameDomId,filePathDomId,fileIdDomId,callbackfunc,isPreView,phoneType);
    }else{
      window.location = "clientrequest:openFileFeedBackSelect::"+callbackfunc+"::1::0::3";
    }
  },
	//图片上传 fileSavePath 文件路径数组，多个文件时以，进行分割,不需要可传空字符串
	submitFormWithMultiFiles: function(filePaths,uploadURL,uploadParams,callbackfunc){
		let formid = '';
		let fileSavePath =''; //文件保存路径
		let fileIds = '';//预留，没使用不需要可传空字符串
		let uploadHeader = '';//请求头，不需要传空,传值时形式如：  key1,value1;key2,value2 Android有，iOS没有该参数,不需要可传空字符串

		if(this.isAndroid){
			window.WebViewFunc.submitFormWithMultiFiles(formid,filePaths,fileIds,fileSavePath,uploadURL,uploadParams,uploadHeader,callbackfunc);
		}else{
			window.location="clientrequest:submitFormWithMultiFiles::"+formid+"::"+filePaths+"::"+fileIds+"::"+fileSavePath+"::"+uploadURL+"::"+uploadParams+"::"+callbackfunc;
		}
	},

	//刷脸注册成功跳转到客户端的登录页面
	faceRegistFinished:function(){
		if(this.isAndroid){
			window.WebViewFunc.faceRegistFinished();
		}else{
			window.location="clientrequest:faceRegistFinished::";
		}
	},
	/*
	* 打开webview页面错误返回
	*/
	openWebKitError: function(msg){
		alert("错误："+msg);
	},
	//返回客户端注册页面
	goClientLogin:function(){
		if(this.isAndroid){
			window.WebViewFunc.closeCallBack("");
		}else{
			window.location="clientRequest:sysback::";
		}
	},
	/*
	* 关闭webview页面
	*/
	closeCallBack: function(params){
		if(!params){
			if(this.isAndroid){
				window.WebViewFunc.closeCallBack("");
			}else{
				window.location="clientRequest:closeCallBack::";
			}
		}else{
			//eval(params);
			if(this.isAndroid){
				window.WebViewFunc.closeCallBack(params);
			}else{
				window.location="clientRequest:closeCallBack::"+params;
			}
		}
	},

	/*
	* 关闭webview页面错误返回
	*/
	closeCallBackError: function(msg){
		alert("错误："+msg);
	},



	/*
	* 打开GPS错误返回
	*/
	getLocationError: function(msg){
		alert("错误："+msg);
	},

	/*
	* 打开GPS回调返回经纬度和地址
	*/
	getLocationBack: function(longitude,latitude,address){
		this.longitude=longitude;
		this.latitude=latitude;
		this.address=address;
		alert("longitude:"+longitude+" latitude:"+latitude+" address:"+address);
	},

	openLoading: function(msg){
		if(this.isAndroid){
			window.WebViewFunc.openWaitView(msg);
		}else{
			window.location="clientrequest:openWaitView::"+msg;
		}
	},
	closeLoading: function(msg){
		if(this.isAndroid){
			window.WebViewFunc.dismissWaitView();
		}else{
			window.location="clientrequest:dismissWaitView::";
		}
	},

	checkAPKIsInstall: function(packageName,callbackfunc){
		if(this.isAndroid){
			window.WebViewFunc.checkAPKIsInstall(packageName,callbackfunc);
		}
	},

	downloadAPP: function(androidInstallUrl,iosInstallUrl){
		if(this.isAndroid){
			window.WebViewFunc.downloadAPP(androidInstallUrl,iosInstallUrl);
		}
	},

	invokeJar: function(className,methodName,jsonParam) {
		if(this.isAndroid){
			window.WebViewFunc.invokeJar(className,methodName,jsonParam);
		}
	},

	close: function(params) {
		if(this.isAndroid){
			window.WebViewFunc.closeCallBack("");
		}else{
			window.location="clientRequest:closeCallBack::";
		}
	},

	openGeo: function(regionId,operatorId,operatorName,operatorPhone,systemId,callbackfunc) {
		if(this.isAndroid){
			window.WebViewFunc.startTimeLocation(regionId,operatorId,operatorName,operatorPhone,systemId,callbackfunc);
		}else{
			window.location="clientrequest:startTimeLocation::"+regionId+"::"+operatorId+"::"+operatorName+"::"+operatorPhone+"::"+systemId+"::"+callbackfunc;
		}
	},
	scanQRcode: function(params) {
		if(this.isAndroid){
			window.WebViewFunc.scanQRcode(params);
		}else{
			window.location="clientRequest:scanQRcode::"+params;
		}
	},
  scanBarcode: function(params) {
    if(this.isAndroid){
      window.WebViewFunc.scanBarcode(params);
    }else{
      window.webkit.messageHandlers.scanBarcode.postMessage(params);
    }
  },
	//关闭页面，并把参数带给上个页面处理
	closeWebKitWithCallBack: function(params1,params2) {
		if(this.isAndroid){
			window.WebViewFunc.closeWebKitWithCallBack(params1,params2);
		}else{
			window.location="clientRequest:closeWebKitWithCallBack::"+params1+"::"+params2;
		}
	},

	openSlidingMenu: function() {
		if(this.isAndroid){
			window.WebViewFunc.openSlidingMenu();
		}else{
			window.location="clientRequest:openSlidingMenu::";
		}
	},

    /*
     * 调用打电话功能
     */
    openCallPhoneView:function(phoneNumber){
        if(this.isAndroid){
            window.WebViewFunc.openCallPhoneView(phoneNumber);
        }else{
            window.location="clientrequest:openCallPhoneView::"+phoneNumber;
        }
	},

	/*
     * 注册之后调用自动登录
     */
    appAutoLogin:function(phoneNumber,password){
        if(this.isAndroid){
            window.WebViewFunc.appAutoLogin(phoneNumber,password);
        }else{
            window.location="clientrequest:appAutoAutoLogin::"+phoneNumber+"::"+ password;
        }
	},
     /*
     * 打卡上班打卡
     */
    startLocationForALD:function(regionId,staffId,phoneNumber,name,callBack,crmId){
		if(this.isAndroid){
			window.WebViewFunc.startLocationForALD(regionId,staffId,phoneNumber,name,callBack,0,crmId);
		}else{
			window.location="clientrequest:startLocation::"+regionId+"::"+staffId+"::"+phoneNumber+"::"+name+"::"+callBack+"::0::"+crmId;
		}
	},
	/*
     *  打卡下班打卡
     */
    stopLocationForALD:function(seq,endCallBack){
		if(this.isAndroid){
			window.WebViewFunc.stopLocationForALD(seq,endCallBack,0);
		}else{
			window.location="clientrequest:stopLocation::"+seq+"::+"+endCallBack+"::0";
		}
	},
		/*
     *  打卡中途打卡
     */
    uploadLocationForALD:function(seq,callBack,type){
		if(this.isAndroid){
			window.WebViewFunc.uploadLocationForALD(seq,callBack,type,0);
		}else{
			window.location="clientrequest:uploadLocation::"+seq+"::"+callBack+"::"+type+"::0";
		}
	},

	/**
 * 电话录音
 */
    openCallPhoneViewAndRecord:function(phoneNumber,callback){
	   if(this.isAndroid){
		  window.WebViewFunc.openCallPhoneViewAndRecord(phoneNumber,callback);
	   }else{
	    	window.location="clientrequest:openCallPhoneView::"+phoneNumber;
	}
},
    // 下载安装软件包
    downLoadInstallPackage:function(url){
        if(this.isAndroid){
            window.WebViewFunc.downloadAppVersion(url);
        }else{
            window.location="clientrequest:downloadAppVersion::"+url;
        }
	},
	getIosOrBrowserAppVersion:function(){
		if(this.isIos){//苹果系统
			window.location="clientrequest:getAppVersion::versionBack";
		} else {
			let res = '{"retCode":"0","retMsg":"","verison":"2.20.82"}';
			setTimeout(()=>{
				window['versionBack'](res);
			},1000);
		}
	},
	//视频采集
	collectVideo:function(h5Param,url,subtitle,noticeCallBack){
		let dict = {
			"callBack": noticeCallBack,
			"uploadUrl": url,
			"subtitle": subtitle,
			"h5Param": h5Param,
			"countdown":20
		};
		if(this.isAndroid){
			window.WebViewFunc.videoRecord(JSON.stringify(dict));
		}else{
			//window.webkit.messageHandlers.videoRecord.postMessage(dict);
			window.location="clientrequest:videoRecord::"+JSON.stringify(dict);
		}
	},
  //视频采集机读
  collectVideoMachine:function(h5Param,url,subtitle,noticeCallBack,isRead,readContext,language,countdown){
    let dict = {
      "callBack": noticeCallBack,
      "uploadUrl": url,
      "subtitle": subtitle,
      "h5Param": h5Param,
      "countdown":countdown,
      "isRead":isRead,  //是否机读
      "readContext":readContext, //机读内容
      "language":language,//机读语种
    };
    if(this.isAndroid){
      window.WebViewFunc.videoRecord(JSON.stringify(dict));
    }else{
      //window.webkit.messageHandlers.videoRecord.postMessage(dict);
      window.location="clientrequest:video_recording_started::"+JSON.stringify(dict);
    }
  },
	//播放已采集的视频
	playRecord:function(filePaths){
		let dict = {
			"filePath": filePaths
		};
		if(this.isAndroid){
			window.WebViewFunc.playRecord(JSON.stringify(dict));
		}else{
			window.location="clientrequest:playRecord::"+JSON.stringify(dict);
		}
	},
	//清理已采集的视频
	clearCashFile:function(){
		if(this.isAndroid){
			window.WebViewFunc.clearCashFile();
		}else{
			window.location="clientrequest:clearCashFile";
		}
	},
	//泛渠道上传门头照片
	pancUploadAttach:function(fileCode,callBack,fileType,filePath){
    let param = {
      "fileCode": fileCode,//
      "callBack": callBack,//h5回调方法名称
      "fileType": fileType,//文件类型，1打卡图片，2附件图片，3音频，4视频
      "filePath": filePath //文件本地路径，续传时传文件本地路径，选择新文件时，传空
    };
    if(this.isAndroid){
      window.WebViewFunc.selectBigFileUpload(JSON.stringify(param));
    }else{
      window.location = "clientrequest:selectBigFileUpload::"+JSON.stringify(param);
    }
  },
  //暗访泛渠道-上传附件（包括门头照片和暗访附件）
  anfangUploadAttach:function(fileCode,callBack,fileType,filePath){
    let param = {
      "fileCode": fileCode,//暗访编号,即打卡后生成的唯一dataId
      "callBack": callBack,//h5回调方法名称
      "fileType": fileType,//文件类型，1打卡图片，2附件图片，3音频，4视频
      "filePath": filePath //文件本地路径，续传时传文件本地路径，选择新文件时，传空
    };
    if(this.isAndroid){
      window.WebViewFunc.selectBigFileUpload(JSON.stringify(param));
    }else{
      window.location = "clientrequest:selectBigFileUpload::"+JSON.stringify(param);
    }
  },
  //暗访泛渠道-预览音频或视频
  anfangReviewAttach:function(previewType,mediaPath,suffixName,mediaClass,callback){
    let param = {
      "mediaClass": mediaClass,
      "mediaPath": mediaPath,
      "previewType": previewType,
      "suffixName": suffixName,
      "callback": callback
    }
    if(this.isAndroid){
      window.WebViewFunc.mediaPreview(JSON.stringify(param));
    }else{
      window.location = "clientrequest:mediaPreview::" + JSON.stringify(param);
    }
  },
  //暗访泛渠道-停止播放音频
  anfangStopAudio:function(){
    if(this.isAndroid){
      window.WebViewFunc.stopAudioPreview();
    }else{
      window.location = "clientrequest:stopAudioPreview";
    }
  },
  //学习园地菜单拉起
  openLearningGarden: function(params) {
    if(this.isAndroid){
        window.WebViewFunc.openLearningGarden(JSON.stringify(params));
      }else{
        window.location = "clientrequest:openLearningGarden::" + JSON.stringify(params);
      }
  },
  //阿拉盯拉起外围渠道app,包名，入口名，json对象字符串
  aldPullOutApk:function (packageName,activeName,jsonStr) {
	if(this.isAndroid){
		window.WebViewFunc.aldPullOutApk(packageName,activeName,jsonStr);
	  }else{
		alert('IOS暂未开放');
	  }
 },
  //跨省视频协同
  openVideoBusiness:function() {
    if(this.isAndroid){
		if(window.WebViewFunc.openVideoBusiness){
			window.WebViewFunc.openVideoBusiness();
		} else {
			alert('当前版本暂不支持该功能，请更新版本');
		}
    }else{
      alert('IOS暂未开放');
    }
  },
 //安卓端获取退出键返回状态
 getGoBackAndExit:function(param) {
	if(this.isAndroid){
		window.WebViewFunc.getGoBackAndExit(param);
	  }
 },
 //安卓端更改退出键返回状态
 setGoBackAndExit:function(backHomeAndExit) {
	if(this.isAndroid){
		window.WebViewFunc.setGoBackAndExit(backHomeAndExit);
	  }
 },
	//透传了调用链日志的客户端上班打卡方法
	collectLocation:function(param){
		if(this.isAndroid){
			window.WebViewFunc.collectLocation(param);
		}else{
			window.webkit.messageHandlers.collectLocation.postMessage(param);
		}
	},
	//透传了调用链日志的客户端下班打卡方法
	endLocation:function(param){
		if(this.isAndroid){
			window.WebViewFunc.endLocation(param);
		}else{
			window.webkit.messageHandlers.endLocation.postMessage(param);
		}
	},
  //调用客户端能力你说我记sdk
  openSpeechSDK:function(){
    if(this.isAndroid){
      if(window.WebViewFunc && window.WebViewFunc.openSpeechSDK){
        window.WebViewFunc.openSpeechSDK();
      }
    }else{
		console.log('1');
      if(window.webkit && window.webkit.messageHandlers.openSpeech){
		console.log('2');
		window.webkit.messageHandlers.openSpeech.postMessage('1');
		console.log('3');
      }
    }
  },
	//透传H5里的操作员工号和地市给客户端，用于刷脸注册场景
	transInfoToClient:function(param){
		let retStr = '';
		let retObj = {};
		//判断param是否是json对象，如果是则转换成json字符串
		let paramStr = param;
		if(typeof param == 'object'){
			Object.assign(param,{"funName":"getAldRegion"})
			paramStr = JSON.stringify(param);
		}
		if(this.isAndroid){
			retStr = window.WebViewFunc.getAldRegion && window.WebViewFunc.getAldRegion(paramStr);
		}else{
			retStr = window.prompt(paramStr);
		}
		if(retStr){//如果有值，判断是否是json字符串，如果是则转换成json对象
			retObj = JSON.parse(retStr);
		} else {
			retObj = {};//对于客户端没有更新，没有提供此方法的情况，返回空对象
		}
		return retObj;
	},
  //获取录音权限
  getAudioPermiss:function(callback){
    if(this.isAndroid){
      window.WebViewFunc.getAudioPermiss && window.WebViewFunc.getAudioPermiss(callback);
    }else{
      window.webkit.messageHandlers.getAudioPermiss&&window.webkit.messageHandlers.getAudioPermiss.postMessage(callback);
    }
  },
  //录音
  openVoiceRecord:function(callBack){
    if(this.isAndroid){
      window.WebViewFunc.openVoiceRecord(callBack);
    }else{
      window.webkit.messageHandlers&&window.webkit.messageHandlers.openVoiceRecord.postMessage(callBack);
    }
  },
  //调用语音转文字原生组件
  openChatSpeech:function(callbackfunc){
	if(this.isAndroid){
		window.WebViewFunc.openChatSpeech(callbackfunc);
	  }else{
		let param = {'callbackfunc':callbackfunc};
		window.webkit.messageHandlers&&window.webkit.messageHandlers.openChatSpeech.postMessage(param);
	  }
  },
	//复制到系统粘贴板 其中paramStr为内容
	copyShare:function(paramStr){
		if(this.isAndroid){
			if (window.WebViewFunc && window.WebViewFunc.copyShare) {
				window.WebViewFunc.copyShare(paramStr);
			}
		}else{
			if(window.webkit && window.webkit.messageHandlers.copyShare && window.webkit.messageHandlers.copyShare.postMessage){
				window.webkit.messageHandlers.copyShare.postMessage(paramStr);
			}
		}
	},
	//分享到微信 其中paramStr为内容
	copyShareToWechat:function(paramStr){
		if(this.isAndroid){
			if(window.WebViewFunc && window.WebViewFunc.copyShareToWechat){
				window.WebViewFunc.copyShareToWechat(paramStr);
			}
		}else{
			if(window.webkit && window.webkit.messageHandlers.copyShareToWechat && window.webkit.messageHandlers.copyShareToWechat.postMessage){
				window.webkit.messageHandlers.copyShareToWechat.postMessage(paramStr);
			}
		}
	},
	//查询系统粘贴板里的内容
	getCopyShare:function(callbackfunc){
		if(this.isAndroid){
			if(window.WebViewFunc && window.WebViewFunc.getCopyShare){
				window.WebViewFunc.getCopyShare(callbackfunc);
			}
		}else{
			if(window.webkit && window.webkit.messageHandlers.getCopyShareForIOS && window.webkit.messageHandlers.getCopyShareForIOS.postMessage){
				window.webkit.messageHandlers.getCopyShareForIOS.postMessage(callbackfunc);
			}
		}
	},
	//清空粘贴板里的内容
	clearCopy:function(mCallBack){
		if(this.isAndroid){
			if(window.WebViewFunc && window.WebViewFunc.clearCopy){
				window.WebViewFunc.clearCopy(mCallBack);
			}
		}else{
			if(window.webkit && window.webkit.messageHandlers.clearCopy && window.webkit.messageHandlers.clearCopy.postMessage){
				window.webkit.messageHandlers.clearCopy.postMessage(mCallBack);
			}
		}
	},
  //判断是否Pad
  getDeviceType: function(params) {
    if(this.isAndroid){
      window.WebViewFunc.getDeviceType(params);
    }else{
      window.webkit.messageHandlers.getDeviceType.postMessage(params);
    }
  },
    //手机相册拍照
    //isPreView 是否需要预览，1是，其他值否。预览时是，会将照片转换成base64字符串回调给页面
  openMultiplePhoto: function(callbackfunc){
      if(this.isAndroid){
        window.WebViewFunc.openMultiplePhoto(callbackfunc);
      }else{
        window.webkit.messageHandlers.openMultiplePhoto.postMessage(callbackfunc)
      }
  },
  //下载图片并保存到手机相册
  savePhotoWithBase64Image: function(paramStr){
    if(this.isAndroid){
      if(window.WebViewFunc.savePhotoWithBase64Image){
        window.WebViewFunc.savePhotoWithBase64Image(paramStr);
      }else{
        alert('当前APP版本不支持该功能，请升级APP');
      }
    }else{
      window.webkit.messageHandlers.savePhotoWithBase64Image.postMessage(paramStr)
    }
  },

  copyImageShareWeChat: function(paramStr){
    if(this.isAndroid){
      if(window.WebViewFunc.copyImageShareWeChat){
        window.WebViewFunc.copyImageShareWeChat(paramStr);
      }else{
        alert('当前APP版本不支持该功能，请升级APP');
      }
    }else{
      window.webkit.messageHandlers.copyImageShareWeChat.postMessage(paramStr)
    }
  },
  downloadSavePhoto:function(fileUrl){
	if(this.isAndroid){
		window.WebViewFunc.downloadSavePhoto(fileUrl);
	} else {
		window.location="clientRequest:downloadSavePhoto::"+fileUrl;
	}
  },
  //切换岗位更新到客户端最新的岗位编码userInfo-stationId
  updateStationId: function(paramStr) {
    if (this.isAndroid) {
      if (window.WebViewFunc && window.WebViewFunc.updateStationId) {
        window.WebViewFunc.updateStationId(paramStr)
      }
    } else {
      if (window.webkit && window.webkit.messageHandlers.updateStationId && window.webkit.messageHandlers.updateStationId.postMessage) {
        window.webkit.messageHandlers.updateStationId.postMessage(paramStr)
      }
    }
  },
    //手机震动方法
    SoundVibration: function(paramStr) {
        if (this.isAndroid) {
            console.info('Android震动方法调用')
            if (window.WebViewFunc && window.WebViewFunc.SoundVibration) {
                window.WebViewFunc.SoundVibration()
            }
        } else {
            console.info('ios震动方法调用')
            if (window.webkit && window.webkit.messageHandlers) {
                window.webkit.messageHandlers.SoundVibration.postMessage('1')
            }
        }
    },
	// 截屏能力开关方法
	screenShot: function(havePri, callbackfunc = 'screenShotCallbackfunc'){
		let timestamp = Math.floor(new Date().getTime() / 1000);
		let paramers= JSON.stringify(
			{
				"callBackName": callbackfunc,
				"havePri": havePri,
				"siginInfo": {
					"appId": "wwwwwww8888888888",
					"timestamp": String(timestamp),
					"signStr": "dsdsfdfdfdfdfr343fdfdfdfdff3434fefdfdfdfdfdfd"
				}
			})
		console.log('截屏请求', paramers);
		try{
			if(this.isAndroid){
				if (window.WebViewFunc && window.WebViewFunc.checkScreenShot) {
					window.WebViewFunc.checkScreenShot(paramers);
					console.log("android", havePri);
				}
			}else{
				if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.screenShot){
					window.webkit.messageHandlers.screenShot.postMessage(paramers);
					console.log("ios",havePri);
				}
			}
		}catch (error) {
			console.log('截屏原生异常', error)
		}
	}
}
if(JSON.parse(token) == 'QFOxpuDlWRM='
|| window.location.host.indexOf('172') == '0'
||~window.location.host.indexOf('250.50')){//固定的tokens
	//浏览器测试走 模拟客户端交互返回相应的报文
	// ClientJs = require('./clientjs_browser.js')
}

  //开发联调环境需要配置跨域标识
    if ((/android/gi).test(navigator.appVersion)) {
      // url = '/apiM' + url;
    } else {
      ClientJs = MockClientJs;


}

export default ClientJs;
