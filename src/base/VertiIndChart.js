/** 垂直行业echarts画图js方法 2021/12/13 yesenwei */
import * as echarts from 'echarts'

export function createVertiIndIncomePie(domId, busiData) {
  let domObj = document.getElementById(domId)
  // 基于准备好的dom，初始化echarts实例
  let myChart = echarts.init(domObj)
  myChart.setOption({
    tooltip: {
      trigger: 'none'
    },
    series: [
      {
        name: 'Access From',
        type: 'pie',
        radius: ['55%', '90%'],
        avoidLabelOverlap: false,
        label: {
          show: true,
          position: 'center',
          fontSize: '16',
          fontWeight: 'bold'
        },
        emphasis: {
          scale: false,
          label: {
            show: true,
            fontSize: '16',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          {
            value: 100 * busiData,
            name: '' + Math.floor(100 * busiData) + '%',
            itemStyle: {
              normal: {
                color: {
                  type: 'linear',
                  colorStops: [
                    {
                      offset: 0, color: '#14BDA2' // 0% 处的颜色
                    },
                    {
                      offset: 1, color: '#0281F6' // 100% 处的颜色
                    }
                  ]
                }
              }
            }
          },
          {
            value: 100 * (1 - busiData),
            name: '',
            itemStyle: {
              color: '#F1F1F1'
            }
          }
        ]
      }
    ]
  })
}

export function createVertiIndTrendLine(domId, xCategory, busiData, themeColor) {
  let domObj = document.getElementById(domId)
  // 基于准备好的dom，初始化echarts实例
  let myChart = echarts.init(domObj)

  myChart.setOption({
    grid: {
      height: 100,
      top: 20,
      left: '18%',
      width: '70%',
      right: 0,
      bottom: 0
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xCategory//['2021/01', '2021/02', '2021/03', '2021/04', '2021/05', '2021/06', '2021/07', '2021/08', '2021/09', '2021/10', '2021/11', '2021/12']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        data: busiData,//[0, 0, 0, 9999999, 1224, 3572, 8572, 1427, 2756, 1234, 758, 6522],
        type: 'line',
        smooth: true,
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: themeColor  // 100% 处的颜色
            }, {
              offset: 1, color: '#FFFFFF' //   0% 处的颜色
            }]
          }
        },
        color: themeColor
      }
    ]
  })
}

export function createVertiIndIncomePie2(domId, busiData, color) {
  //domId：绘制的盒子，busiData:绘制的数据，color:绘制的图表颜色
  let domObj = document.getElementById(domId)
  // 基于准备好的dom，初始化echarts实例
  let myChart = echarts.init(domObj)
  myChart.setOption({
    tooltip: {
      trigger: 'item'
    },
    legend: {
      top: '5%',
      left: 'center'
    },
    emphasis: {
      label: {
        show: true,
        fontSize: 40,
        fontWeight: 'bold'
      }
    },
    series: [
      {
        name: 'Access From',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: false,
            fontSize: 40,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        color: color ? color : ['#f96300', '#f9e5d4'],
        data: busiData

      }
    ]
  })
}

// 绘制饼图（商客专区薪酬看板）
export function createPie1(domId, busiData, color, dataSum,type) {
  //domId：绘制的盒子，busiData:绘制的数据，color:绘制的图表颜色
  let domObj = document.getElementById(domId)
  if (domObj) {
    // 基于准备好的dom，初始化echarts实例
    let myChart = echarts.init(domObj)
    let line2Text = dataSum
    let text = type == 1 ? '总薪酬' : '总积分'
    myChart.setOption({
      tooltip: {
        trigger: 'item'
      },
      series: [
        {
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['50%', '50%'],
          color: color,
          data: busiData,
          label: {
            fontSize: 12, // 标签文字大小
            color: '#3D3D3D', // 标签文字颜色
            formatter: function(params) { // 自定义标签显示格式
              return params.name + ':' + params.value // 在名称和值之间添加换行符
            }

          },
          labelLine: {
            length: 10,
            length2: 10,
            lineStyle: {
              width: 2
            }
          }
        }
      ],
      // 自定义文字
      graphic: [
        {
          type: 'text',
          left: 'center', // 水平居中
          top: 'center',  // 垂直居中
          silent: true, // 设置为 true，避免该图形元素响应鼠标等交互事件，防止干扰原有饼图交互逻辑
          style: {
            // 使用换行符 \n 来实现换行
            text: type == 2 ? `{line1|总薪酬}\n{line2|${line2Text}}` : `{line1|总积分}\n{line2|${line2Text}}`,
            textAlign: 'center',
            textVerticalAlign: 'middle',
            // 定义富文本样式
            rich: {
              line1: {
                fontSize: 10,
                fill: '#616161'
              },
              line2: {
                fontSize: 17,
                fontWeight: '600',
                fill: '#D47A20'
              }
            }
          }
        }
      ]
    })
  }
}

// 绘制柱图（商客专区薪酬看板）
export function createBar1(domId, names, values,length) {
  //domId：绘制的盒子，busiData:绘制的数据，color:绘制的图表颜色
  let domObj = document.getElementById(domId)
  if (domObj) {
    // 基于准备好的dom，初始化echarts实例
    let myChart = echarts.init(domObj)
    myChart.setOption({
      dataZoom: [
        {
          type: 'inside', // 或者 'inside'，根据需要选择
          yAxisIndex: 0, // 指定 y 轴
          startValue: 0, // 起始值
          endValue: 5, // 结束值
          zoomLock: true, // 禁用缩放
          moveOnMouseWheel: true, // 启用鼠标滚轮滚动
          moveOnMouseMove: true, // 启用鼠标拖动滚动
          height: 200, // 滚动条高度
          handleSize: 5, // 滚动条手柄大小
          fillerColor: 'rgba(17, 100, 210, 0.42)', // 滚动条颜色
        }
      ],
      grid: {
        top: '10px',
        left: (30 + 14 * (length - 4)) + 'px',
        right: '10%',
        bottom: '20px' // 确保底部也设置
      },
      tooltip: {},
      legend: false,
      xAxis: {
        type: 'value',
        // 刻度不显示
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          // 设置间隔为 1，隔一个显示一个
          formatter: function(value, index) {
            if (index % 2 === 0) {
              return value;
            }
            return '';
          }
        }
        // interval: 2, // 根据数据长度计算间隔
      },
      yAxis: {
        type: 'category',
        // data: ['Brazil', 'Indonesia', 'USA', 'India', 'China', 'World'],
        data: names,
        show: true,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false, // 显示分隔线
        },
        axisLabel: {
          interval: 0, // 强制显示所有标签
        }
      },
      series: [
        {
          name: '202502',
          type: 'bar',
          color: '#0079FD',
          // data: [18203, 23489, 29034, 104970, 131744,6666],
          data: values,
          // zlevel: 1, // 数据柱子的zlevel设为1
          barWidth: 15, // 设置柱子宽度
          barGap: '20%', // 设置不同系列柱子之间的间距
          barCategoryGap: '20%', // 设置柱子之间的间距
          label: {
            normal: {
              show: true,
              position: 'right', // 将标签置于柱状条内部左侧
              // alignTo: 'edge', // 使标签对齐到柱状条边缘
              color: '#373737',
              formatter: (params) => params.value,
            }
          },
        },
      ]
    })
  }
}


export function createBar2(domId, names, values, length) {
  // domId：绘制的盒子，names: Y轴数据，values: X轴数据，length: 数据长度
  let domObj = document.getElementById(domId);
  if (domObj) {
    // 基于准备好的dom，初始化echarts实例
    let myChart = echarts.init(domObj);
    myChart.setOption({
      grid: {
        top: '20px',
        // left: '10%',
        // right: (23 + 14 * (length - 4)) + 'px',
      },
      tooltip: {},
      legend: false,
      yAxis: {
        type: 'value',
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false // 隐藏 Y 轴的水平分隔线
        },
        axisLabel: {
          show: false // 确保 Y 轴标签显示
        }
      },
      xAxis: {
        type: 'category',
        data: names,
        show: true,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        zlevel: 2,
        axisLabel: {
          show: true,
          interval: 0, // 强制显示所有标签
        }
      },
      series: [
        {
          name: '202502',
          type: 'bar',
          color: ['#FAC858', '#93BEFF', '#507BFF'],
          // data: values,
          zlevel: 1,
          barWidth: 20,
          barGap: 0, // 减小柱子之间的间距
          orient: 'horizontal', // 设置柱状图为水平方向
          data: values.map((value, index) => ({
            value: value,
            itemStyle: {
              color: ['#FAC858', '#93BEFF', '#507BFF'][index % 3] // 循环使用颜色
            }
          })),
          label: {
            normal: {
              show: true,
              position: 'top', // 标签位置
              color: '#373737',
              formatter: (params) => params.value,
            }
          },
        },
      ]
    });
  }
}

// 绘制折线图（客户画像内画像详情）
export function lineChart(domId, values, legend = ['流量', '通话'], length) {
  // domId：绘制的盒子，names: Y轴数据，values: X轴数据，length: 数据长度
  let domObj = document.getElementById(domId)
  if (domObj) {
    // 基于准备好的dom，初始化echarts实例
    let myChart = echarts.init(domObj)
    let options = {
      // 文字提示
      tooltip: {
        trigger: 'axis'
      },
      // 图例
      legend: {
        data: legend,
        top: '0%',
        fontSize: 12
      },
      grid: {
        left: '4%',
        right: '10%',
        top: '15%',
        bottom: '5%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: values.x
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: legend[0],
          type: 'line',
          smooth: true,
          data: values[legend[0]],
          lineStyle: {
            //设置线条颜色
            normal: {
              color: '#007AFF' // 折线线条颜色:红色
            }
          },
          itemStyle: {
            //设置端点颜色
            normal: {
              color: '#007AFF' // 设置线条上点的颜色（和图例的颜色）
            }
          },
        },
        {
          name: legend[1],
          type: 'line',
          smooth: true,
          data: values[legend[1]],
          lineStyle: {
            //设置线条颜色
            normal: {
              color: '#91CC75' // 折线线条颜色
            }
          },
          itemStyle: {
            //设置端点颜色
            normal: {
              color: '#91CC75' // 设置线条上点的颜色（和图例的颜色）
            }
          },
        }
      ]
    }
    myChart.setOption(options)
  }
}
