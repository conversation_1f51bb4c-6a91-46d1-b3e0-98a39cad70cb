<template>
  <div class="html-wrap">
    <!--缓存-->
    <keep-alive>
      <router-view v-if="$route.meta.keepAlive"></router-view>
    </keep-alive>
    <!--不缓存-->
    <router-view v-if="!$route.meta.keepAlive"></router-view>
  </div>
</template>
<script>
import ClientJs from '@/base/clientjs.js'
import Storage from '@/base/storage.js'

export default {
  data(){
    return{
      ipUrl: '',
    }
  },
  methods: {
    //触发
    async goDictOnsale() {

      // const dictProUrl = 'http://*************:5173/dictonsale/#/dictHomePage?source=2' //onsale地址 本地测试地址
      // const dictProUrl = 'https://spare.jsalading.cn/dictonsale/index.html#/dictHomePage?source=fqd' //onsale地址 备用中心
      // const dictProUrl = 'https://main.jsalading.cn:/dictonsale/index.html#/dictHomePage?source=fqd' //onsale地址 主中心

      const dictProUrl = this.ipUrl + '/dictonsale/#/dictHomePage?source=fqd' //onsale地址

      // document.addEventListener('visibilitychange', function () {
      //   let vState = document.visibilityState
      //   console.log(vState)
      //   if (vState === 'hidden') {
      //     // 当页面由前端运行在后端时，出发此代码
      //     console.log('我被隐藏了')
      //   }
      //   if (vState === 'visible') {
      //     // 当页面由隐藏至显示时
      //     ClientJs.closeCallBack()
      //   }
      // })

      window.location.href = dictProUrl
    },
    goAiDingHuo() {
      let client = /android|harmony/gi.test(navigator.userAgent)?'ANDROID':'IOS';
      let uInfo = Storage.session.get('userInfo');
      let panChannelInfo = uInfo.panChannelInfo;

      let url = `/xsb/api-user/tokenAuthen/h5genToken.do?opId=dms&prviId=-1&clientType=${client}`;
      url += `&regionId=${panChannelInfo.cityid}&operId=1&stationId=1&phoneNumber=${uInfo.opernum}`;

      this.$http.get(url).then((response) => {
        let data = response.data.data;
        let opUrl = data.opUrl;
        console.log('拉起爱订货页面======',opUrl);
        ClientJs.openWebKit(encodeURIComponent(opUrl), '', '0', '', '', '', '', '', '', '', '');
      }).catch((response) => {
        console.log('拉起爱订货页面异常,',response)
      })
    },
    getSysInfo(){
      // 调用客户端方法获取域名
      window['getSysInfoCB'] = (result) => {
        this.ipUrl = result.serverUrl
      }
    }
  },
  components: {},
  computed: {},
  created() {
    ClientJs.getSysInfo('getSysInfoCB')
  },
  mounted() {
    this.getSysInfo()
    let uinfo = Storage.session.get('userInfo')
    if (uinfo.isGroup == '1') {
      //跳集客泛渠道的首页
      this.$router.push('/pancIndexGroup')
    } else if (uinfo.isGroup == '2') {
      //跳暗访泛渠道的首页
      this.$router.push('/secretVisitPancIndex')
    } else if (uinfo.isGroup == '4') {
      //跳dict泛渠道的首页
      this.goDictOnsale()
      // this.$router.push('/dictHomePage');
    } else if (uinfo.isGroup == '6') {
      // 拉起爱订货页面
      this.goAiDingHuo()
    }
  },
}
</script>
<style scoped lang="less">
</style>
