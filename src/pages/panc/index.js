// import Vue from 'vue'
import router from '@/router/panc/panRouter.js';
import {decrptParam,digitSign,aesEncryptAllParams} from '@/base/AesEncrptUtil.js'
import {iEncrpt,iEncrptParamMap,iEncrptParam} from '@/base/encrptH5.js'
import axios from 'axios'
import 'mint-ui/lib/style.css'
import '!style-loader!css-loader!less-loader!@/base/less/panc.less'
import Storage from '@/base/storage'
import {CONSTVAL} from '@/base/config'

import ClientJs from '@/base/clientjs'
import {BASE64} from '@/base/coding'
import { Toast,DatetimePicker,Indicator,Switch } from 'mint-ui';
import MessageBox from 'components/common/NlMessageBox/message-box.js';
import Header from 'components/common/Header.vue'

import Main from '@/pages/panc/index.vue'
import {chgStrToDate,imageIsExist,getQueryVariable,getRandomStr} from '@/base/utils'
import LoadingDirective from '@/components/common/NlLoading/loading.js'
import Clickoutside from '@/base/clickoutside'

import '../../assets/css/iconfont.css'

//如果需要防止重复点击 请给相应的元素添加样式needsclick 例 class=" needsclick"
import  '@/base/fastclick.js'
FastClick.attach(document.body,{tapDelay:4000})
var deviceWidth = document.documentElement.clientWidth || window.screen.availWidth;


document.documentElement.style.fontSize = (deviceWidth / 375)* 100 +'%';//Math.min((deviceWidth / 375),1) * 100 +'%';

document.title = '阿拉盯商户版';
window.onresize = function(){
    deviceWidth = document.documentElement.clientWidth || window.screen.availWidth;
    document.documentElement.style.fontSize = (deviceWidth / 375)* 100 +'%';//Math.min((deviceWidth / 375),1) * 100 +'%';
}

let uinfo = {};
//获取客户端请求入口的参数
function getInitData(){
    // Storage.session.set('locationHref',window.location.href);
    let reg = new RegExp("(^|&)" + 'userInfo' + "=([^&]*)(&|$)");
    let r = window.location.search.substr(1).match(reg);

    if(r != null){
        uinfo = BASE64.decode(r[2]);
        uinfo = JSON.parse(uinfo);

    } else {
        //测试数据
        let qryMap = getQueryVariable();
        if(qryMap['tokenid']){
            let tokenid = qryMap['tokenid'];
            tokenid = tokenid.replace(/%3D/g,'=')//%3D
            tokenid = BASE64.decode(tokenid);
            tokenid = tokenid.replace(/\"/g,'')
            uinfo = {
                "region": qryMap['region'],
                "staffId": qryMap['staffId'],
                "servNumber": qryMap['servNumber'],
                "staffId": qryMap['servNumber'],
                "crmId": qryMap['crmId'],
                tokenid: tokenid
            };
        } else {
            uinfo = {}
        }
    }

    if(Storage.session.get('userInfoNew')){
        Storage.session.set('userInfo',Storage.session.get('userInfoNew'));
    } else {
        Storage.session.set('userInfo',uinfo);
    }
    Storage.session.set('tokenId',uinfo.tokenid);
    if(uinfo && uinfo.tokenid){
        axios.defaults.headers.tokenid = uinfo.tokenid;
    }
    axios.defaults.headers.aldregion = uinfo.region+'_'+uinfo.servNumber;//新增aldregion

}
getInitData();


/**
 * 页面初始化获取客户端提供的路由信息
 */
window.getSysInfoCB = function(obj){
    let serverUrl = obj.serverUrl;
    if(process.env.NODE_ENV === 'development'){
        //开发联调环境需要配置跨域标识
        // serverUrl = '/apiM' + serverUrl;
    }
    //从客户端获取 走真数据的时候 放开注释
    axios.defaults.baseURL = serverUrl;
    try {
        Storage.set('webUrl',serverUrl);
        // Storage.set('location',obj.location);
        // Storage.set('latitude',obj.latitude);
        // Storage.set('longitude',obj.longitude);

        if(!obj.location && Storage.get('location')){//客户端返回的位置信息为空
            //上一次的位置信息不为空，则不更新
        } else {
            Storage.set('location',obj.location || 'empty');
            Storage.set('latitude',obj.latitude || 'empty');
            Storage.set('longitude',obj.longitude || 'empty');
        }
    } catch (error) {
    }
    Storage.set('deviceInfo',obj);
    checkCurVersion();//获取客户端版本信息
}
ClientJs.getSysInfo('getSysInfoCB');


//调用客户端获取当前版本
function checkCurVersion(){
    if(/android|harmony/gi.test(navigator.userAgent)){
        var res = window.WebViewFunc && window.WebViewFunc.getAppVersion();
        var obj = res && eval('(' + res + ')');
        Storage.set('panVersion',obj && obj.verison);
    }else{
        window.location="clientrequest:getAppVersion::panVersion";
    }
}

window.panVersion = function(res){
    var obj = eval('(' + res + ')');
    Storage.set('panVersion',obj.verison);
}
//提供给客户端切换路由调用的方法
window.setSysRouteInfo = function(serverUrl){
    axios.defaults.baseURL = serverUrl;
    Storage.set('webUrl', serverUrl);
}

//客户端重新登录的回调
window.refreshUserInfo = function(res){
    uinfo = JSON.parse(BASE64.decode(res));
    let oldUinfo = Storage.session.get('userInfo');
    let relTelnum = oldUinfo && oldUinfo.relTelnum;//学生手机号
    if(relTelnum){
        uinfo.relTelnum = relTelnum;
    }
    Storage.session.set('userInfo',uinfo);
    Storage.session.set('tokenId',uinfo.tokenid);

    axios.defaults.headers.tokenid = uinfo.tokenid;
}

axios.defaults.timeout = 70000;//默认70秒


axios.interceptors.request.use(
  config => {


      let method = config.method;
      let url = config.url;
      let unLoadFlg = config.unLoadFlg || (config.data && config.data.unLoadFlg)
      if(method == 'get'){
          if(config.data == void 0) {
              config.data = {};//绕过axios不给get设置content-type的判断
              config.headers['Content-Type'] = "application/json"
          }
      }
      if(process.env.NODE_ENV === 'development'){
          //开发联调环境需要配置跨域标识
          if(!window.WebViewFunc){
              // url = '/apiM' + url;
          }
      }
      if(!unLoadFlg){
          Indicator.open('加载中...');
      }
      let sign = '';
      //不需要加crmID
      if(~url.indexOf('/xsb/api-pancUser/appVersionCenter/h5appVersionInfo')){
          sign = digitSign(false,config.data,method);
      } else {
          let uinfo =  Storage.session.get('userInfo');
          let tokenId = '';

          if(uinfo.tokenid){
              tokenId = uinfo.tokenid;
              //转译处理
              tokenId = tokenId.replace(/\+/g,'%2B').replace(/&/g,'%26').replace(/\//g,'%2F').replace(/=/g,'%3D')
          }

          if(method == 'get'){
              if(uinfo.crmId){
                  //URL链接中有?，则用&连接
                  url += ~url.indexOf('?')?'&':'?';

                  url += `staffId=${uinfo.servNumber}&region=${uinfo.region}&crmId=${uinfo.crmId}&servNumber=${uinfo.servNumber}`;
                  if(uinfo.relTelnum){
                      url += `&studentPhone=${uinfo.relTelnum}`;
                  }
                  if(uinfo.panChannelInfo.channelId){
                      url += `&channelId=${uinfo.panChannelInfo.channelId}`;
                  }
                  if(uinfo.panChannelInfo.globalId){
                      url += `&globalId=${uinfo.panChannelInfo.globalId}`;
                  }
              }
              if(tokenId){//tokenid
                  url += `&tknZigZa=${tokenId}`
              }

          } else if(method == 'post'){
              if(!config.data){
                  config.data = {};
              }
              if(uinfo.crmId){
                  config.data.staffId = uinfo.servNumber;
                  config.data.region = uinfo.region;
                  config.data.crmId = uinfo.crmId;
                  config.data.servNumber = uinfo.servNumber;

                  if(uinfo.panChannelInfo.channelId){
                      config.data.channelId = uinfo.panChannelInfo.channelId;
                  }
                  if(uinfo.panChannelInfo.globalId){
                      config.data.globalId = uinfo.panChannelInfo.globalId;
                  }
              }

              if(uinfo.relTelnum){
                  config.data.studentPhone = uinfo.relTelnum;
              }
              if(tokenId){//tokenid
                  config.data.tknZigZa = tokenId;
              }
          }

          //签名
          if(method === 'get'){
              sign = digitSign(false,url,method);
          }else{
              sign = digitSign(false,config.data,method);//获取数字签名
          }

          url = encryptParam(config,url);//加密处理
      }
      //添加泛渠道标识isPanc,为协助后端定位问题，统一添加随机数
      url += (~url.indexOf('?')?'&':'?') + 'isPanc=' + iEncrptParam('1')+'&rdom=' + getRandomStr();
      config.headers.sign = sign;
      config.url = url;
      config.unLoadFlg = unLoadFlg;//加载圈再单独加上去，可能被整体加密了，响应拦截就获取不到了
      // console.info(url);
      return config;
  },
  err => {
      return Promise.reject(err)
  })

//参数加密处理
function encryptParam(config,urlParam){
    let url = urlParam || config.url;
    // //判断是否要参数整体加密
    // let isEncrptAll =  Storage.session.get('isEncrptAllFlg');

    let skipEncrptFlg = false;//是否跳过加解密，默认加密，false
    skipEncrptFlg = config.method == 'get'?(config.unEncrpt||(config.data && config.data.unEncrpt)):config.data && config.data.unEncrpt;
    if(skipEncrptFlg){//跳过加解密
        return url;
    }
    // if(isEncrptAll == '1'){//整体加解密
    //如果全局加密，则在请求头给标识，用于后端全局解密
    config.headers.c03be90046a7e7f = 'GwRT4HjrxC9Davw';
    config.headers['Content-Type'] = "application/json"
    url = dealEncryptParam(config,url)
    // } else {
    //     url = dealEncryptParamOld(config,url);
    // }
    return url;
}
//新版本加密处理
function dealEncryptParam(config,url){
    let method = config.method;//请求方式，目前用到了get、post
    let beforeStr = url;
    if(method == 'post'){
        beforeStr = config.data
    }
    try{
        let afterParam = aesEncryptAllParams(beforeStr);
        if(method == 'get'){
            // console.info("get加密后：：：",afterParam)
            return afterParam;
        } else {
            config.data = afterParam;
        }
    }catch(e){
        //新版加密异常，切换老的加密方式
        axios.defaults.headers.c03be90046a7e7f = '';
        // application/json
        // return dealEncryptParamOld(config,url);
        console.info(e);
        return url;
    }
    return url;
}
// http响应拦截器
axios.interceptors.response.use(res => {
    let url = res.config.url;
    if(~url.indexOf('/preTelEnterNet/h5getPaperlessStatus')
      ||~url.indexOf('h5pancCommit')){
        //不需要关闭加载圈
    } else {
        Indicator.close();// 响应成功关闭loading
    }
    // if(~url.indexOf('/xsb/personBusiness/customerView/h5IdentifyUser')
    //     || ~url.indexOf('/xsb/personBusiness/customerView/h5QryCgetusercust')){
    //     //不需要解密，鉴权已经单独解密
    // } else {
    try{
        if(typeof(res.data) != "object"){//不是对象的话就是加密串
            let data = decrptParam(res.data);
            if(~url.indexOf('/xsb/personBusiness/customerView/h5IdentifyUser')
              || ~url.indexOf('/xsb/personBusiness/customerView/h5QryCgetusercust')){
                //需要二次解密，鉴权加了两次密
            } else {
                data = JSON.parse(data);
            }
            res.data = data
        }
        console.info(res.data);
    } catch(err){
        Toast('统一解密异常：' + err);
    }
    //鉴权失败状态码9999
    if(res.data.retCode =='9999'){
        // //请求客户端重新登录
        // ClientJs.autologinInterface('refreshUserInfo');
        MessageBox.alert(res.data.retMsg || '会话已失效请重新登录','温馨提示','温馨提示')
          .then((action) => {
              ClientJs.userLoginOut();//请求客户端注销登录
          }).catch(() => {});

    } else {
        return res;
    }
}, error => {
    Indicator.close();
    if(error.code == 'ECONNABORTED' && error.message.indexOf('timeout')!=-1){
        if(res.config && res.config.headers && res.config.headers.tokenid){
            MessageBox.alert('服务器忙，请稍候再试！','温馨提示');
        } else {
            return Promise.reject(error);
        }
    }else{
        return Promise.reject(error)
    }
})

//业务提交封装请求参数中的定位信息
function busiSubmit(url,param){
    param.location = Storage.get('location');//位置信息
    param.latitude = Storage.get('latitude');//纬度
    param.longitude = Storage.get('longitude');//经度
    param.stationId = uinfo.currentRoleid;//岗位编码
    param.clientType = /android|harmony/gi.test(navigator.userAgent)?'ANDROID':'IOS'
    return new Promise((resolve,reject) => {
        axios.post(url,param).then(response => {
            resolve(response.data);
        },err => {
            Indicator.close();
            reject(err);
        })
    })
}

const install = function(Vue, config = {}) {
    if (install.installed) return;
    Vue.config.productionTip = false

    //日期控件
    Vue.component(DatetimePicker.name, DatetimePicker);
    // Vue.component(Loadmore.name, Loadmore);
    //头部组件
    Vue.component(Header.name, Header);
    // 调用 提示框
    Vue.prototype.$messagebox = MessageBox;

    //alert对话框
    Vue.prototype.$alert = (msg,title) =>{
        if( title === void 0 || title === '' ){
            title = '温馨提示';
        }
        if(msg && msg.length > 300){
            msg = msg.substr(0,300)
        }
        Vue.prototype.$messagebox.alert(msg,title);
    }

    Vue.$toast = Vue.prototype.$toast = Toast;
    Vue.$switch = Vue.prototype.$switch = Switch;
    //异步查询
    Vue.prototype.$http = axios;

    //业务提交
    Vue.prototype.$submit = busiSubmit;

    //局部加载
    Vue.directive('loading',LoadingDirective);
    Vue.directive('clickoutside',Clickoutside);
    Vue.prototype.CONSTVAL = CONSTVAL;

    //全局注册自定义指令，用于判断当前图片是否能够加载成功，可以加载成功则赋值为img的src属性，否则使用默认图片
    Vue.directive('real-img', async function (el, binding) {//指令名称为：real-img
        let imgURL = binding.value;//获取图片地址
        if (imgURL) {
            let exist = await imageIsExist(imgURL);
            if (exist) {
                el.setAttribute('src', imgURL);
            }
        }
    })

    /**
     * @param {日期} val
     * @param {转换后的格式} fmt  如yyyy-MM-dd hh:mm:ss
     * @param {是否转换成今天 昨天}zhFlg
     */
    Vue.filter('dateShow', function(val,fmt,zhFlg) {
        val = chgStrToDate(val,true);
        if(isNaN(val) && !isNaN(Date.parse(val))){
            return val;
        }

        let now = new Date();
        let nowDay = now.getDate();
        let nowMonth = now.getMonth();
        let nowYear = now.getFullYear();
        let isTian = false;
        //参数中的日期是今天  明天  昨天
        if(nowYear == val.getFullYear() && nowMonth == val.getMonth() && Math.abs(nowDay - val.getDate()) <=1){
            isTian = true;
        }
        let o = {
            "M+": val.getMonth() + 1, //月份
            "d+": val.getDate(), //日
            "h+": val.getHours(), //小时
            "m+": val.getMinutes(), //分
            "s+": val.getSeconds(), //秒
            "q+": Math.floor((val.getMonth() + 3) / 3), //季度
            "S": val.getMilliseconds() //毫秒
        };

        if (/(y+)/.test(fmt)) {
            fmt = fmt.replace(RegExp.$1, (val.getFullYear() + "").substr(4 - RegExp.$1.length));
        }
        for (let k in o){
            if (new RegExp("(" + k + ")").test(fmt)) {
                if(isTian && zhFlg){//日
                    if(k == "M+"){
                        fmt = fmt.replace(RegExp.$1+'/', '');
                    } else if(k == "d+"){
                        if(o[k] === nowDay){
                            fmt = fmt.replace(RegExp.$1, '今日');
                        }else if(o[k] === nowDay - 1){
                            fmt = fmt.replace(RegExp.$1, '昨日');
                        }else if(o[k] === nowDay + 1){
                            fmt = fmt.replace(RegExp.$1, '明日');
                        }
                    } else {
                        fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
                    }
                }else{
                    fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
                }
            }
        }
        return fmt;
    });
}
// auto install
if (typeof window !== 'undefined' && window.Vue) {
    install(window.Vue);
};


//路由前置钩子
router.beforeEach((to, from, next) => {
    if(to.path == '/resetpassword' || to.path == '/changePassword'|| to.path == '/regist' || to.path == '/privacy' || to.path == '/LuckDraw' || to.path == '/chooseRegister' || to.path == '/usingSDKS'){
        next();
    } else if(to.path != '/page404' && !Storage.session.get('tokenId')){
        //这里判断用户是否登录，验证本地存储是否有token
        next({
            path: '/page404'
        })
    } else {
        if(to.name === 'PancIndex'){
            ClientJs.getSysInfo('getSysInfoCB');
        }
        next();
    }


});


new Vue({
    el: '#app',
    router,
    components: { Main },
    template: '<Main/>'
})
