// import Vue from 'vue'
import Vue from 'vue'
import Router from 'vue-router'
import '!style-loader!css-loader!less-loader!@/base/less/style.less'
import {iEncrpt,iEncrptParamMap} from '@/base/encrptH5.js'
import axios from 'axios'
import 'mint-ui/lib/style.css'
import {getQueryVariable,getRandomStr} from '@/base/utils'
import {decrptParam,digitAldSign,aesEncryptAllParams} from '@/base/AesEncrptUtil.js'
import Storage from '@/base/storage'
import { Toast,DatetimePicker,MessageBox,Indicator } from 'mint-ui';
// import MessageBox from 'components/common/NlMessageBox/message-box.js';
import Main from '@/pages/thirds/Index.vue';
import router from '@/router/thirdRouter.js'
import Page404 from '@/pages/ole/Page404';
import {CONSTVAL} from '@/base/config'
import '../../assets/css/iconfont.css'
import '@/base/less/reset.css'

//如果需要防止重复点击 请给相应的元素添加样式needsclick 例 class=" needsclick"
import  '@/base/fastclick.js'
// import axios from "../../base/nlAxios";
FastClick.attach(document.body,{tapDelay:4000})
var deviceWidth = document.documentElement.clientWidth || window.screen.availWidth;


document.documentElement.style.fontSize = (deviceWidth / 375)* 100 +'%';//Math.min((deviceWidth / 375),1) * 100 +'%';


window.onresize = function(){
    deviceWidth = document.documentElement.clientWidth || window.screen.availWidth;
    document.documentElement.style.fontSize = (deviceWidth / 375)* 100 +'%';//Math.min((deviceWidth / 375),1) * 100 +'%';
}

document.title = '第三方平台';

window.triggerFrom = 'qw';

let uinfo = {};
let page404 = false;
//获取客户端请求入口的参数
function getInitData(){
    let qryMap = getQueryVariable();
    let token = qryMap['token'];//token
    if(token){
        token = token.replace(/%2B/g,'+').replace(/%26/g,'&');
    }

    let daiweiId = qryMap['operid'];//代维工号
    let region = qryMap['regionId'];//地市编码
    let userName = qryMap['username'];//用户姓名
    let telnum = qryMap['telnum'];//用户手机号码
    let mobile = qryMap['mobile'];//代维工号用于插入业务日志的student_phone

    if(daiweiId){
        // uinfo.tokenid = token;
        uinfo.region = region;
        uinfo.crmId = daiweiId;
        uinfo.stationId = '9988021754066058';//岗位编码 社区直销员
        uinfo.staffId = '123456789';
        uinfo.operatorName = userName;
        uinfo.telnum = telnum;
        uinfo.relTelnum = mobile;
    } else {
        page404 = true;
    }

    Storage.session.set('userInfo',uinfo);
    //第三方没有token
    // Storage.session.set('tokenId',uinfo.tokenid);
    // axios.defaults.headers.tokenid = uinfo.tokenid;

}


// getInitData();

axios.defaults.timeout = 30000;//默认30秒
const RETRY = 0;//超时默认再请求一次
const RTRY_DELAY = 1000;//超时后的请求间隙

axios.interceptors.request.use(
  config => {
    let method = config.method;
    let url = config.url;
    let unLoadFlg = config.unLoadFlg || (config.data && config.data.unLoadFlg)

    if(method == 'get'){
      if(config.data == void 0) {
        config.data = {};//绕过axios不给get设置content-type的判断
        config.headers['Content-Type'] = "application/json"
      }
    }

    if(!unLoadFlg){
      Indicator.open('加载中...');
    }

    if(process.env.NODE_ENV === 'development'){
      // //开发联调环境需要配置跨域标识
      if(~url.indexOf('/xsb')){//行商宝新框架
        url = '/apiM' + url;
      }else {
        url = '/apiOld' + url;
      }
    }
    //不需要加crmID
    if(~url.indexOf('/xsb/api-user/appVersionCenter/appVersionInfo')){
    } else {
      let uinfo =  Storage.session.get('userInfo');
      let tokenId = '';

      if(uinfo.tokenid){
        tokenId = uinfo.tokenid;
        //转译处理
        tokenId = tokenId.replace(/\+/g,'%2B').replace(/&/g,'%26').replace(/\//g,'%2F').replace(/=/g,'%3D')
      }

      if(method == 'get'){
        //URL链接中有?，则用&连接
        url += ~url.indexOf('?')?'&':'?';

        url += `staffId=${uinfo.staffId}&region=${uinfo.region}&crmId=${uinfo.crmId}&servNumber=${uinfo.servNumber}`;
        if(uinfo.relTelnum){
          url += `&studentPhone=${uinfo.relTelnum}`;
        }
        if(tokenId){//tokenid
          url += `&tknZigZa=${tokenId}`
        }

      } else {
        if(!config.data){
          config.data = {};
        }
        config.data.staffId = uinfo.staffId;
        config.data.region = uinfo.region;
        config.data.crmId = uinfo.crmId;
        config.data.servNumber = uinfo.servNumber;
        if(uinfo.relTelnum){
          config.data.studentPhone = uinfo.relTelnum;
        }
        if(tokenId){//tokenid
          config.data.tknZigZa = tokenId;
        }
      }

      url = encryptParam(config,url);//加密处理
    }
    url += (~url.indexOf('?')?'&':'?') + 'rdom=' + getRandomStr();//为协助后端定位问题，统一添加随机数
    config.unLoadFlg = unLoadFlg;//加载圈再单独加上去，可能被整体加密了，响应拦截就获取不到了
    config.url = url;
    return config;
  },
  err => {
    return Promise.reject(err)
  })

//参数加密处理
function encryptParam(config,urlParam){
  let url = urlParam || config.url;

  let skipEncrptFlg = false;//是否跳过加解密，默认加密，false
  skipEncrptFlg = config.method == 'get'?(config.unEncrpt||(config.data && config.data.unEncrpt)):config.data && config.data.unEncrpt;
  if(skipEncrptFlg){//跳过加解密
    return url;
  }

  //如果全局加密，则在请求头给标识，用于后端全局解密
  config.headers.c03be90046a7e7f = 'GwRT4HjrxC9Davw';
  config.headers['Content-Type'] = "application/json"
  url = dealEncryptParam(config,url)

  return url;
}

function validateRespSign(res){
  let resData = res.data;
  let respSign = res.headers && res.headers['respsign'];
  try{
    if(respSign){//如果服务端对返回报文签名了
      let h5Sign = digitAldSign(resData);
      if(h5Sign == respSign){
        res.data = JSON.parse(resData);
      } else {
        res.data = {'retCode':'4444','retMsg':'非法请求'}
      }
    } else {
      res.data = JSON.parse(resData);
    }
  }catch(e){
    //无法转json,是需要相关业务代码二次解密
  }
}

//新版本加密处理
function dealEncryptParam(config,url){
  let method = config.method;//请求方式，目前用到了get、post
  let beforeStr = url;
  if(method == 'post'){
    beforeStr = config.data
  }
  try{
    let afterParam = aesEncryptAllParams(beforeStr);
    if(method == 'get'){
      // console.info("get加密后：：：",afterParam)
      return afterParam;
    } else {
      config.data = afterParam;
    }
  }catch(e){
    //新版加密异常，切换老的加密方式
    axios.defaults.headers.c03be90046a7e7f = '';
    // application/json
    // return dealEncryptParamOld(config,url);
    console.info(e);
    return url;
  }
  return url;
}

// http响应拦截器
axios.interceptors.response.use(res => {
  // alert(res)
  let url = res.config.url;
  let configData = res.config.data;
  if(~url.indexOf('/preTelEnterNet/h5getPaperlessStatus')
    || ~url.indexOf('/preTelEnterNet/h5PreTelCommitNew')
    || res.config.unLoadFlg
    ||(configData && (configData.unLoadFlg||~configData.indexOf('unLoadFlg')))){
    //不需要关闭加载圈
  } else {
    Indicator.close();// 响应成功关闭loading
  }

  let resData = res.data;
  // alert(res.data);
  if (typeof resData === 'string') {
    try {
      JSON.parse(resData);//明文返回的报文字符串是可以解析成json的
      //验证明文签名的情况
      validateRespSign(res);
      // return res;
    } catch (e) {
      //不可解析的表示是密文
      try{//解决阿拉盯端调用泛渠道的接口未解密，导致前端报错
        let data = decrptParam(resData);//aes解密，遇到旧业务des加密的会catch异常
        res.data = data;
      } catch(err){
        //Toast('统一解密异常：' + err);
      }
      validateRespSign(res);
    }
  }

  //鉴权失败状态码9999
  if(res.data.retCode =='9999'){
    MessageBox.alert(res.data.retMsg || '会话已失效请重新登录','温馨提示','温馨提示')
      .then((action) => {
        window.location.href = 'https://wx.apollojs.cn/wxh-web/businessHandling';
      }).catch(() => {
        window.location.href = 'https://wx.apollojs.cn/wxh-web/businessHandling';
      });
  }
  return res;
}, error => {
  Indicator.close();

  //获取状态码
  const status =
    (error.response &&
      error.response.status &&
      error.response.status) ||
    '';
  const data = (error.response && error.response.data) || {};
  if (data.message) {
    Toast(data.message);
    return Promise.reject(data.message);
  }

  if (error.code == 'ECONNABORTED' && error.message.indexOf('timeout') != -1) {
    // Toast('请求超时');
    return reqTimeOutRetry(error.config);
  }

  if (status === 401) {
    Toast('登录过期,请重新登录');
    return Promise.reject('登录过期,请重新登录');
  }
  let url = error.config.url;
  if (status === 404) {
    // Toast('接口404报错');
    return Promise.reject('接口404报错:'+url);
  }
  if (status === 500) {
    Toast('服务器错误');
    return Promise.reject('服务器报错:'+url);
  }
  return Promise.reject('未知错误:'+url);
})

const install = function(Vue, config = {}) {
  if (install.installed) return;
  Vue.config.productionTip = false
  //日期控件
  Vue.component(DatetimePicker.name, DatetimePicker);


  // 调用 提示框
  Vue.prototype.$messagebox = MessageBox;

  //alert对话框
  Vue.prototype.$alert = (msg,title) =>{
    if( title === void 0 || title === '' ){
      title = '温馨提示';
    }
    if(msg && msg.length > 300){
      msg = msg.substr(0,300)
    }
    Vue.prototype.$messagebox.alert(msg,title);
  }

  Vue.$toast = Vue.prototype.$toast = Toast;

  //异步查询
  Vue.prototype.$http = axios;
  Vue.prototype.CONSTVAL = CONSTVAL;
}
// auto install
if (typeof window !== 'undefined' && window.Vue) {
  install(window.Vue);
};

//请求超时重试机制
function reqTimeOutRetry(config){

  if(RETRY == 0){//不设置重新请求
    return Promise.reject('请求超时,'+config.url);
  }
  //保存重试次数的变量
  config.__retryCount = config.__retryCount || 0;
  //判断是否超过配置的重试次数
  if(config.__retryCount >= RETRY) {
    //超过重试次数则不再重试
    return Promise.reject('请求超时,'+config.url);
  }
  config.__retryCount += 1;

  var backoff = new Promise(function(resolve) {
    //配置的请求间隙后重新发送
    setTimeout(function() {
      resolve();
    }, RTRY_DELAY || 1);

  });
  return backoff.then(function() {
    return axios(config);
  });
}

if(page404){
    new Vue({
        el: '#app',
        router,
        components: { Page404 },
        template: '<Page404/>'
    })
} else {
    new Vue({
        el: '#app',
        router,
        components: { Main },
        template: '<Main/>'
    })
}
