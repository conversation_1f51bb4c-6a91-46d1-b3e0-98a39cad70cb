const crypto = require('crypto');

export class AesUtil {
    // 密钥和偏移量使用 Buffer 来正确处理二进制数据
    static readonly_key = Buffer.from('royasoft'+'\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00', 'binary');
    static readonly_iv = Buffer.alloc(16, 0); // 16 字节的偏移量，全部为 0

    static decode(str) {
        try {
            // 将输入字符串从 Base64 解码为 Buffer
            const encryptedData = Buffer.from(str, 'base64');

            // 创建解密器
            const decipher = crypto.createDecipheriv('aes-256-cbc', AesUtil.readonly_key, AesUtil.readonly_iv);

            // 解密过程
            let decrypted = decipher.update(encryptedData, undefined, 'utf8');
            decrypted += decipher.final('utf8');

            return decrypted;
        } catch (error) {
            console.error('解密失败:', error);
            throw error;
        }
    }
}


