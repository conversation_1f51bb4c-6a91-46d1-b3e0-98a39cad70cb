import Vue from 'vue'
import Router from 'vue-router'
import {iEncrpt,iEncrptParamMap} from '@/base/encrptH5.js'
import axios from '@/base/nlAxios.js'
import 'mint-ui/lib/style.css'

import Storage from '@/base/storage'
import { Toast,DatetimePicker,Indicator } from 'mint-ui';
import MessageBox from 'components/common/NlMessageBox/message-box.js';
import Main from '@/pages/ole/Index.vue';
import Kuandai from '@/pages/ole/Kuandai';
import Page404 from '@/pages/ole/Page404';
import {CONSTVAL} from '@/base/config'
import '../../assets/css/iconfont.css'
import '@/base/less/reset.css'
import {getRealUrl} from '@/base/utils'
import { AesUtil } from './aes'


import Vconsole from 'vconsole'



//如果需要防止重复点击 请给相应的元素添加样式needsclick 例 class=" needsclick"
import  '@/base/fastclick.js'
import CryptoJS from 'crypto-js'
FastClick.attach(document.body,{tapDelay:4000})
var deviceWidth = document.documentElement.clientWidth || window.screen.availWidth;
Vue.use(Router);
document.documentElement.style.fontSize = (deviceWidth / 375)* 100 +'%';//Math.min((deviceWidth / 375),1) * 100 +'%';


window.onresize = function(){
    deviceWidth = document.documentElement.clientWidth || window.screen.availWidth;
    document.documentElement.style.fontSize = (deviceWidth / 375)* 100 +'%';//Math.min((deviceWidth / 375),1) * 100 +'%';
}

// document.title = '员工套餐';
function getUrlParams(url) {
    const urlParams = new URLSearchParams(url.split('?')[1]);
    const params = {};
    for (let param of urlParams.entries()) {
        params[param[0]] = param[1];
    }
    return params;
}


function getQueryVariable(){
    console.info(query);
    console.info(window.location.href.substring(1));
    let vars = query.split("&");
    console.info(vars);
    let qryMap = {};
    for (let i = 0; i < vars.length; i++) {
        var pair = vars[i].split("=");
        // if(pair[0] == variable){
        //     return pair[1];
        // }
        let value = pair[1];
        if(pair.length > 2){//针对这样的参数 token=V8x7/bXcqAw====3
            for(let j = 2; j < pair.length; j++){
                if(pair[j]){
                    value += pair[j];
                } else {
                    value += '=';
                }
            }
        }
        qryMap[pair[0]] = value;
    }
    return qryMap;
}
let page404 = false;
//获取客户端请求入口的参数


function hexToString(hex) {
    let str = '';
    for (let i = 0; i < hex.length; i += 2) {
        const hexPair = hex.substr(i, 2);
        const decimal = parseInt(hexPair, 16);
        str += String.fromCharCode(decimal);
    }
    return str;
}
function getInitData(){
    if (window.location.href.indexOf('olVisitPage') >= 0) {
        let qryMap = getUrlParams(window.location.href);
        let src = qryMap['src'];//ole页面的来源
        let telnum = qryMap['FromUserTelNum'];

        let requestBody = qryMap['requestBody']
        var key = CryptoJS.enc.Utf8.parse("royasoft"); //十六位十六进制数作为秘钥
        var iv = CryptoJS.enc.Utf8.parse('000000');//十六位十六进制数作为秘钥偏移量
        let hexStr = hexToString(requestBody)
        let query = JSON.parse(AesUtil.decode(hexStr))




        let token = qryMap['token'];//token
        if(token){
            token = token.replace(/%2B/g,'+').replace(/%26/g,'&');
        }

        let uinfo = {};
        if(token && telnum){
            uinfo.tokenid = token;
            uinfo.region = query.region ? query.region == '99'? '14' : query.region : '14';
            uinfo.crmId = '14150759';
            uinfo.stationId = '9988021754066058';//岗位编码 社区直销员
            uinfo.staffId = '123456789';
            uinfo.telnum = telnum;

        } else {
            uinfo = {}
        }

        Storage.session.set('userInfo',uinfo);
        Storage.session.set('tokenId',uinfo.tokenid);

        axios.defaults.headers.tokenid = uinfo.tokenid;
        let hostname =  window.location.hostname
        let url222 = "https://"+hostname
        Storage.set('webUrl',url222);
    } else if (window.location.href.indexOf('moaVisitPage') >= 0){
        console.info('moaVisitPage');
        
        // new Vconsole()

        let hostname =  window.location.hostname
        let protocol = window.location.protocol;
        let port = window.location.port;
        let urlWeb = protocol + "//"+hostname + ':' + port
        console.info('moaVisitPage----',urlWeb);

        Storage.set('webUrl',urlWeb);

    }

}
getInitData();






const install = function(Vue, config = {}) {
    if (install.installed) return;
    Vue.config.productionTip = false
    //日期控件
    Vue.component(DatetimePicker.name, DatetimePicker);


    // 调用 提示框
    Vue.prototype.$messagebox = MessageBox;

    //alert对话框
    Vue.prototype.$alert = (msg,title) =>{
        if( title === void 0 || title === '' ){
            title = '温馨提示';
        }
        if(msg && msg.length > 300){
            msg = msg.substr(0,300)
        }
        Vue.prototype.$messagebox.alert(msg,title);
    }

    Vue.$toast = Vue.prototype.$toast = Toast;

    //异步查询
    Vue.prototype.$http = axios;
    Vue.prototype.CONSTVAL = CONSTVAL;
}
// auto install
if (typeof window !== 'undefined' && window.Vue) {
  install(window.Vue);
};






var router = new Router({
    routes: [
        {
            path:'/',
        },
        {
            path:'/workStage',
            redirect: '/'
        },
        {
            path: '/olVisitPage',
            name: 'olVisitPage',
            component: () => import('components/PersonalBusinessOpportunity2/tempPage.vue')
        },

        {
            path: '/PotentialuserMaintenance2',
            name: 'PotentialuserMaintenance2',
            component: () => import('components/PersonalBusinessOpportunity2/PotentialuserMaintenance.vue')
        },
        {
            path: '/PersonalBusinessOpportunity2',
            name: 'PersonalBusinessOpportunity2',
            component: () => import('components/PersonalBusinessOpportunity2/PersonalBusinessOpportunity.vue')
        },
        {
            path: '/BusinessOpportMaintenance2',
            name: 'BusinessOpportMaintenance2',
            component: () => import('components/PersonalBusinessOpportunity2/BusinessOpportMaintenance.vue')
        },
        {
            path: '/moaVisitPage',
            name: 'moaVisitPage',
            component: () => import('components/PersonalBusinessOpportunity3/moaVisitPage.vue')
        },
        {
            path: '/PotentialuserMaintenance3',
            name: 'PotentialuserMaintenance3',
            component: () => import('components/PersonalBusinessOpportunity3/PotentialuserMaintenance.vue')
        },
        {
            path: '/PersonalBusinessOpportunity3',
            name: 'PersonalBusinessOpportunity3',
            component: () => import('components/PersonalBusinessOpportunity3/PersonalBusinessOpportunity.vue')
        },
        {
            path: '/BusinessOpportMaintenance3',
            name: 'BusinessOpportMaintenance3',
            component: () => import('components/PersonalBusinessOpportunity3/BusinessOpportMaintenance.vue')
        },
    ]
});

if(page404){
    new Vue({
        el: '#app',
        router,
        components: { Page404 },
        template: '<Page404/>'
    })
} else {
    new Vue({
        el: '#app',
        router,
        //components: { Main },
        template: '<div><router-view></router-view></div>'
    })
}
