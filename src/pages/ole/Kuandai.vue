<template>
  <div class="wrapper">
    <section class="contentBox"  v-show="currentStep =='step2'">
        <!--宽地信息展示-->
        <ul class="productInfo">
            <li class="mainInfo">
                <span class="productInfo-icon iconfont kuandai"></span>
                <span class="productInfo-title product-more">{{selectProd.prodName}}</span>
                <span class="productInfo-label">宽带产品</span>
            </li>

        </ul>
        <!--宽地信息展示-->
        <div class="installInfo">
            <h4 class="installInfo-title"><img src="static/img/installTiltle.jpg" class="installInfo-title-img"></h4>
            <ul class="installUl">
                <li>
                    <span class="installUl-title">选择安装地址</span>
                    <a class="installUl-choose" @click="openAddr"><span class="locationText" :class="{'villageColor':fullName=='点击选择'}">{{fullName}}</span><span
                            class="iconfont jiantou-copy-copy moreIcon"></span></a>
                </li>
				 <li v-show="deviceName">
				    <span class="installUl-title">设备</span>
					 <span class="installUl-choose">{{deviceName}}</span>
				</li>
                <li>
                    <span class="installUl-title">姓名</span>
                    <input class="installUl-input" v-model="linkName" placeholder="请输入" type="text">
                </li>
                <li>
                    <span class="installUl-title">联系方式</span>
                    <input class="installUl-input" v-model="linkPhone" type="tel" maxlength="11" placeholder="请输入">
                </li>
                <li>
                    <span class="installUl-title">密码</span>
                    <a class="installUl-choose">
                        <input v-show="!pwdShowFlg" v-model="pwd" type="password" class="installUl-choose-input" maxlength="6" placeholder="请输入">
                        <input v-show="pwdShowFlg" v-model="pwd" type="text" class="installUl-choose-input" maxlength="6" placeholder="请输入">
                        <span class="iconfont" :class="[pwdShowFlg?'kejian':'bukejian']" @click="pwdShowFlg=!pwdShowFlg"></span></a>
                </li>
                <li>
                    <span class="installUl-title">预约安装时间</span>
                    <a class="installUl-choose" @click="openDate('yuyueDate')"><span class="locationText">{{installDate}}</span><span
                            class="iconfont jiantou-copy-copy moreIcon"></span></a>
                </li>
                <li>
                    <span class="installUl-title">备注</span>
                    <textarea class="installUl-textarea" v-model="remark"  rows="4" placeholder="请输入"></textarea>
                </li>
            </ul>
            <a class="agreement" @click="goService">服务协议</a>
        </div>
        <mt-datetime-picker
            type="datetime"
            ref="yuyueDate"
            v-model="dateval"
            @confirm="formatDateVal">
        </mt-datetime-picker>
    </section>
    <!--按钮-->
    <div class="op-button-box"  v-show="currentStep =='step2'">
        <button class="op-button" @click="broadbandPro">宽带办理</button>
    </div>

	 <section class="bandwidth"  v-show="currentStep =='step3'">
			<div class="bandwidhtHead">
				<p><span class="iconfont yingxiao2"></span>{{selectProd.prodName}}</p>
			</div>
			<div class="totalMoney">
				<div class="moneyText"><span class="iconfont shituxiangqing"></span>费用明细</div>
				<span class="money">{{totalFee | chgYuan}}元</span>
			</div>
			<ul class="bandwidthInfo">
				<li v-for="(mitem,mid) in chargeList" :key="mid">
					<span>{{mitem.charegename}}</span>
					<span>{{mitem.chargemoney | chgYuan}}元</span>
				</li>
			</ul>

			<BandwidthPay :tsBusiType="busiType" :tsFeeVal="totalFee" :isDevicefee="isDevicefee" @esClick="updatePay"></BandwidthPay>

            <div class="brandwidthMoney">
				<div class="brandwidthP">
					<button @click="goSign" v-show="changeSign">签 名</button>
					<NlButton count="10" defaultState="counting" @click="goSubmit" v-show="!changeSign"></NlButton>
				<!-- 	<button @click="goSubmit" v-show="!changeSign">提 交</button> -->
				</div>
			 </div>
		</section>
  </div>
</template>

<script>

import {dateFormat} from '@/base/utils'
import VillAdress from 'components/common/VillageAddress/VillageAddress'
import Storage from '@/base/storage'
// import NlDropdown from 'components/common/NlDropdown/dropdown.js'
import BandwidthPay from 'components/business/BandTvMarket/BandwidthPay.vue'
// import sign from 'components/common/Sign'
import {Indicator} from 'mint-ui'
import {createOrder} from 'components/common/pay/pay.js'
import ClientJs from '@/base/clientjs'
import Authenct from 'components/common/Authenticate/Authenticate'
import NlButton from 'components/common/NlButton';

const BUSI_TYPE_MBAND = 'OLE_MBAND'
export default {
  components:{BandwidthPay,NlButton},
  props:{},
  data(){
    return {
        currentStep:'step2',
        telnum: '', //电话
        pwd: '', //宽带密码
        installDate: '点击选择', //安装时间
        linkName: '', //联系人姓名
        businessName: '宽带开通', //业务名称
        netWorkType: '', //网络接入类型
        deviceClass: '', //设备大类 "FTTH_ONU,3;FTTH_ONU,TTP华为"(包含设备大类，自备或设备小类)(3,自备，-1，不选择)
        linkPhone: '', //联系电话
        villageId: '', //小区id
        villageName: '', //小区名称
		fullName:'点击选择',//小区全名
        villageAddress: '', //小区地址
        addressId: '', //地址id
        otherAgent: '', //资源提供方 1 移动 2 广电 3 铁通 4 其它运营商 5 复合运营商
        constMgr: '', // 施工方 1 移动 2 广电 3 铁通 4 其它运营商 5 复合运营商
        radiusFor: '', //Radius归属 1 移动 2 广电 3 铁通 4 其它运营商 5 复合运营商
        userType: '', //宽带用户类型
        remark: '',//备注
        extraProdList:[],//附加产品
        mustProdList:[],//必选产品
        selectProd:{},//选择的基础产品
        selectExtraList:[],//选择的附加产品
        pwdShowFlg: false,//是否展示密码明文
        effectList:[{id:'0',label:'立即生效'},
                    {id:'1',label:'次日生效'},
                    {id:'2',label:'次月生效'}],
		busiType:BUSI_TYPE_MBAND,
		chargeList:'',//宽带调度费
        totalFee:'',//总费用
        isDevicefee:'0',//是否仅含设备
        isShowSign:false,//是否展示客户端签名页面
        signImg:'',//签名base64串
        signId:'',//上传签名后的ID
		payType:'2',//支付方式
		parmes:{},//公用的变量
		changeSign:false,//判断是签名还是提交
		uinfo:{},//当前登录用户信息
		jqData:{},//鉴权对象
		commitResultList:[],//传给无纸化的信息
        deviceName:'',//设备信息
        dateval:'',

    }
  },
  methods:{

    formatDateVal(val){
        this.installDate = dateFormat(val,"yyyy-MM-dd hh:mm:ss");
    },
    //展开日期控件
    openDate(picker) {
        this.$refs[picker].open();
    },

    openAddr(){
        let self = this;
        VillAdress({
            popFlag:true,
            telnum: self.telnum,
            title:'宽带开通'
        },function (obj){
            console.info(obj);
			self.fullName=obj.fullName || obj.villageAddress;
            self.netWorkType = obj.netWorkType; //网络接入类型
            self.villageId = obj.villageId;//小区id
            self.villageName = obj.villageName;//小区名称
            self.villageAddress = obj.villageAddress;//小区地址
            self.addressId = obj.addressId;//地址id
            self.otherAgent = obj.otherAgent; //资源提供方 1 移动 2 广电 3 铁通 4 其它运营商 5 复合运营商
            self.constMgr = obj.constMgr;// 施工方 1 移动 2 广电 3 铁通 4 其它运营商 5 复合运营商
            self.radiusFor = obj.radiusFor; //Radius归属 1 移动 2 广电 3 铁通 4 其它运营商 5 复合运营商
            self.userType = obj.userType; //宽带用户类型
            let deviceBigList = obj.deviceBigList;//LAN家庭网关信息
            let deviceSmallList = obj.deviceSmallList;
            //设备大类 "FTTH_ONU,3;FTTH_ONU,TTP华为"(包含设备大类，自备或设备小类)(3,自备，-1，不选择)
            if(deviceBigList.length > 0 && self.netWorkType=="0"){  //等于0的时候不展示设备
               self.deviceClass='';
			   self.deviceName='';
            }else if(deviceBigList.length > 0 && self.netWorkType !="0"){
				 self.deviceClass = deviceBigList[0].deviceClass + ',';
				  if(deviceSmallList.length > 0) {
				     self.deviceClass += deviceSmallList[0].typeid;
				 }
				 self.deviceName= deviceBigList[0].deviceClassName;
			}else{
				 self.deviceName='';
				 self.deviceClass='';
			}
        });
    },

	//服务协议
	goService(){
        let url = window.location.href
        url = url.substring(0,url.indexOf('?')) + '#/protocole?busiType=mband';
        ClientJs.openWebKit(encodeURIComponent(url), '', '0', '', '', '', '', '', '', '', '');
    },
	//宽带算费
	broadbandPro(){
		if(this.villageAddress =="" || this.fullName=="点击选择"){
			this.$toast("请选择安装地址");
			return;
		}else if(this.linkName==""){
			 this.$toast("请输入姓名");
			 return;
		}else if(this.linkPhone == ""){
			 this.$toast("请输入联系方式");
			 return;
		}else if(!/^((1)+\d{10})$/.test(this.linkPhone)){
			 this.$toast("请输入正确联系方式");
			 return;
		}else if(this.pwd == ""){
			 this.$toast("请输入密码");
			 return;
		}else if(this.installDate == ""){
			 this.$toast("请选择安装时间");
			 return;
		}

        let _this = this;
        _this.selectExtraList = [];
        //默认添加 当月免费 只有立即生效
        _this.selectExtraList.push({
            effectType:"0",
            isPackage:"0",
            pkgprodId:"2013000001",
            prodId:"2011002057",
            prodName:"当月免费套餐"
        });
		let url = '/xsb/personBusiness/staffpkg/h5MbandCharge';
		_this.parmes = {
			"telnum":_this.telnum, //电话
			"pwd":_this.pwd, //宽带密码
			"installDate": _this.installDate, //安装时间
			"prodId":_this.selectProd.prodId, //产品id
			"pkgProdId":_this.selectProd.pkgprodId, //产品所属包ID
			"isPackage":_this.selectProd.isPackage, //是否产品包
            "effectType":_this.selectProd.effectType,  //生效方式 0、立即生效 1、次日生效 2 次月生效
            "limitBandWidth":_this.selectProd.limitBandWidth,//带宽
			"linkName":_this.linkName, //联系人姓名
			"businessName":_this.businessName, //业务名称
			"netWorkType":_this.netWorkType, //网络接入类型
			"deviceClass":_this.deviceClass, //设备类型 "FTTH_ONU,3;FTTH_ONU,TTP华为"(包含设备大类，自备或设备小类)(3,自备，-1，不选择)
			"linkPhone":_this.linkPhone, //联系电话
			"villageId":_this.villageId, //小区id
			"villageName":_this.villageName, //小区名称
			"villageAddress":_this.fullName, //小区地址
			"addressId":_this.addressId, //地址id
			"otherAgent":_this.otherAgent, //资源提供方 1 移动 2 广电 3 铁通 4 其它运营商 5 复合运营商
			"constMgr":_this.constMgr, // 施工方 1 移动 2 广电 3 铁通 4 其它运营商 5 复合运营商
			"radiusFor":_this.radiusFor, //Radius归属 1 移动 2 广电 3 铁通 4 其它运营商 5 复合运营商
			"userType":_this.userType, //宽带用户类型
			"remark":_this.remark, //备注
			"extraProdList":JSON.stringify(_this.selectExtraList),//附加产品
			"mustProdList":JSON.stringify(_this.mustProdList)//必加产品
		}
		_this.$http.post(url,_this.parmes).then((res)=>{
			if(res.data.retCode == '0'){
				_this.chargeList=res.data.data.chargeList;
				_this.totalFee=res.data.data.totalFee;
				_this.isDevicefee = res.data.data.isDevicefee;
				_this.currentStep="step3";
			} else {
			    _this.$alert(res.data.retMsg || '宽带办理失败');
			}
		}).catch((response)=>{

		})
 	},
	//点击签名按钮的事件
	goSign(){
	    this.isShowSign = true;
		this.changeSign = false;
	},
	//签名回调
	qianmingBack(signImg){
	    this.signImg = signImg;//签名的base64串
	},
	//上传签名
	uploadSign(){
	    Indicator.open('上传签名中');
	    this.signId = '';
	    let param = {
	        signBase:this.signImg,
	        unEncrpt:true
	    }
	    //1、上传签名
	    return this.$http.post('/xsb/ability/paperless/h5SignUpLoad', param);
	},
	//电子支付创建订单
	async orderSubmit(){
	    let res = {data:{retCode:'0'}};
	    let queryjson = {};
	    queryjson.crmId = this.uinfo.crmId;
	    queryjson.region = this.uinfo.region;
	    queryjson.telnum = this.telnum;//手机号
	    queryjson.authtype = this.jqData.authtype;
	    queryjson.studentPhone = this.uinfo.relTelnum;//学生手机号（校园营销）

		Object.assign(this.parmes,queryjson);//合并
	    let keyJson = {
			studentPhone:this.uinfo.relTelnum,
	        authType:this.jqData.authtype,
	        telnum:this.telnum,
	    };
	    let body = res.data;
	    let self = this;
	    if(body.retCode == '0'){
	        this.signId = body.data.signId;
 	        this.parmes.signId = this.signId ;
	        createOrder({
	            busitype: BUSI_TYPE_MBAND,
	            amount: this.totalFee,
	            offerName: 'ole员工宽带 ',
	            payMethod: 'zxzf',
	            telnum: this.telnum,
	            keyParameterJson: keyJson,
	            queryjson: this.parmes
	        },self.payBack);
	    } else {
	        this.$alert('上传签名失败');
	    }
	},
	//创建电子支付订单回调方法
	payBack(body){
	    if(body.retCode == "0"){
	        let data = body.data;
	        if(data.payUrl==''){
	            this.$alert('支付链接为空');
	        }else{
	           window.location.href = data.payUrl;
	        }
	    }else{
	        this.$alert(body.retMsg||'支付接口异常！')
	    }
	},
   //非电子支付业务提交
    async plainSubmit(){
		let _this=this;
        let res = {data:{retCode:'0'}}//await this.uploadSign();
        let body = res.data;
        if(body.retCode == '0'){
			// this.signId = body.data.signId;
			// this.parmes.signId = this.signId ;
        	let url='/xsb/personBusiness/staffpkg/h5MbandSubmit';
			_this.$http.post(url,this.parmes).then((res)=>{
        		if(res.data.retCode == '0'){
        			_this.$messagebox.alert('办理成功', '温馨提示').then(action => {
                        _this.$router.back();
                    })
        		} else {
        			_this.$alert(res.data.retMsg || '提交失败');
        		}
        	}).catch((respose)=>{
        		  _this.$alert('宽带办理网络请求失败');
        	})
        }else{
        	this.$alert('上传签名失败');
        }
    },
	//提交的提取公共代码
	pubSubmit(){
		let _this=this;
		let ps={
			'authType':_this.jqData.authtype,
			'payType':_this.payType,
			'totalFee':_this.totalFee,
			'isMqd':'N',
			'chargeList':JSON.stringify(_this.chargeList),
			'location':Storage.get('location'),//位置信息
			'latitude':Storage.get('latitude'),//纬度
			'longitude':Storage.get('longitude'),//经度
			'staffId': _this.uinfo.staffId,
			'stationId':_this.uinfo.stationId,//岗位编码
			'clientType':/android|harmony/gi.test(navigator.userAgent)?'ANDROID':'IOS'
		}

		Object.assign(_this.parmes,ps);
		if(_this.payType == "1"){
			  _this.$messagebox.confirm('是否确认支付操作', '温馨提示')
			  .then(function(action){
		        //电子订单提交
				  _this.orderSubmit();
		    }).catch(function(action){});
		}else{
			//非电子订单提交
			_this.plainSubmit();
		}
	},
	//提交按钮
	goSubmit(){
		if(this.jqData.result !="1"){ //没鉴权的情况下
			  Authenct({
			    popFlag:true,
                idCardWay:false,
                readOnlyFlag:true,
				telnum:this.telnum
			},(obj) => {
			   	this.jqData=obj;
				this.pubSubmit();
			});
		}else{	//鉴权登录过的情况
			this.pubSubmit();
		}
	},
	//获取支付方式
	updatePay(data){
		this.payType = data;
	 }
  },
  //判断钱是否是整数
  filters:{
  	chgYuan(val){
  		if(!val){
  			return 0;
  		}
  		try{
  			let money= parseInt(val);
  			return money / 100;
  		}catch(error){
  			return 0;
  		}
  	}
  },
  created(){
    //设置安装日期的默认日期为明天
    var tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    this.installDate = dateFormat(tomorrow,("yyyy-MM-dd hh:mm:ss"));
    this.dateval = this.installDate;
    let qry = this.$route.query;
    this.telnum = qry.telnum;
    this.selectProd = JSON.parse(qry.prod)

    this.linkPhone = this.telnum;
	this.uinfo = Storage.session.get('userInfo');
	this.jqData =  Storage.session.get('jqData');
	this.linkName = this.jqData && this.jqData.userName;
  }
}
</script>
<style lang="less" scoped>
.wrapper{
    background-color: #ECF0FA;
    height:100%;
}
.op-button {
    background: #1681FB;
    border-radius: 22px;
    height: 44px;
    width: 100%;
    font-size: 14px;
    color: #FFFFFF;
    outline: none;
    border: none;
}

.op-button-box {
    background-color: white;
    width: 100%;
    margin-top: 12px;
    text-align: center;
    line-height: 76px;
    position: fixed;
    bottom: 0px;
    padding: 0 12px;
    box-sizing: border-box;
}


.productBox {
    flex-grow: 1;
    flex-shrink: 1;
	max-height: 50%;
    height: 50%;
    overflow: hidden;
    position: relative;
}

.productInfo {
    margin: 16px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 2px 2px 6px 0px rgba(220, 220, 220, 0.5);
}

.productInfo li {
    display: flex;
    flex-direction: row;
    border-bottom: 1px solid #F2F2F2;
    padding: 0 12px;
}

.productInfo li:last-child {
    border-bottom: none
}
.productInfo li.mainInfo .productInfo-title{
    color: #1681FB;
	font-weight:bold;
}
.productInfo-title {
    flex-grow: 1;
    flex-shrink: 1;
    color: #585858;
    font-size: 14px;
    font-weight: 500;
    line-height: 48px;
}
.product-more{
	padding: 10px 4px 10px 0;
    line-height: 16px;
    align-items: center;
    display: inline-flex;
}

.productInfo-label {
    line-height: 48px;
    color: #BBBBBB;
    font-size: 12px;
    flex-grow: 0;
    flex-shrink: 0;
}

.contentBox {
    height: 100%;
    box-sizing: border-box;
    padding: 0px 0 76px 0;
    overflow: auto;
}
.productInfo-icon{
    line-height: 48px;
    margin-right: 6px;
    color:#007AFF;
    font-size: 18px !important;
}
.installInfo{
    margin: 16px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 2px 2px 6px 0px rgba(220, 220, 220, 0.5);
    overflow: hidden;
}
.installInfo-title-img{
    width: 100%;
    display: block;
}
.installUl>li{
    display: flex;
    flex-direction: row;
    padding:  12px;
    box-sizing: border-box;
    border-bottom: 1px solid #EAEAEA;
    position: relative;
}
.installUl>li:last-child{
    border-bottom:none;
}
.installUl-title{
    color: #232323;
    font-size: 14px;
    line-height: 18px;
    flex-grow: 0;
    flex-shrink: 0;
    text-align: right;
    margin-right: 6px;
}
.installUl-choose{
    flex-grow: 1;
    flex-shrink: 1;
    text-align: right;
    font-size: 14px;
    position:relative;
}
.installUl-input{
    flex-grow: 1;
    flex-shrink: 1;
    text-align: right;
    color:#323232;
    font-size: 14px;
    outline: none;
    border:none;
}
.installUl-input::-webkit-input-placeholder{
    color: #BBBBBB;
    font-size:14px;
}
.installUl-choose-input{
    text-align: right;
    outline: none;
    border:none;
    font-size: 14px;
}
.installUl-choose-input::-webkit-input-placeholder{
    color: #BBBBBB;
}
.installUl-choose *{
    vertical-align: middle;
}
.agreement{
    text-align: center;
    display: block;
    color:#1681FB ;
    font-size: 12px;
    line-height: 40px;
    border-top:1px solid #EAEAEA;
}
.locationText{
    width: 100%;
    padding-right: 20px;
    box-sizing: border-box;
    display: block;
    line-height: 20px;
}
.moreIcon{
    position: absolute;
    top: 50%;
    right: 0px;
	color:#BBBBBB;
    transform: translateY(-50%);
}
.installUl-textarea{
    flex-grow: 1;
    flex-shrink: 1;
    text-align: right;
    color:#323232;
    font-size: 14px;
    outline: none;
    border:none;
    resize: none;
    padding: 0;
}
.installUl-textarea::-webkit-input-placeholder{
    color: #BBBBBB;
    font-size:14px;
}
.gantanhao-yuankuang{
	color:#1681FB
}

.secondaryUl{
    padding-top: 48px;
    overflow: auto;
    height: 100%;
    box-sizing: border-box;
}
.secondaryUl-li{
    display: flex;
    flex-direction: row;
    border-bottom: 1px solid #EAEAEA;
    background-color: white;
    padding: 14px 12px;
}
.secondaryUl-li:last-child{
    border-bottom: none;
}

.secondaryUl-li-checkbox,.secondaryUl-li-effect,.secondaryUl-li--icon{
    flex-grow: 0;
    flex-shrink: 0;
    display: flex;
    align-items: center;
}
.secondaryUl-li-effect{
    margin-right: 6px;
    font-size: 14px;
    color: #FF4848;
}
.secondaryUl-li--title{
    flex-grow: 1;
    flex-shrink: 1;
    margin: 0 6px;
    font-size: 14px;
    line-height: 18px;
    color: #232323;
    word-break: break-all;
    display: inline-flex;
    align-items: center;
}
.secondaryUl-li-checkbox .op-icon-left{
    color: #979797;
    font-size: 20px !important;
}
.secondaryUl-li-checkbox.selected .op-icon-left{
    color: #1680F9;
}

//宽带付费
.bandwidth{
	font-size:14px;
	height: 100%;
    overflow: auto;
}
.bandwidhtHead{
	border-top:1px solid #D9D9D9;
}
.bandwidhtHead p,
.totalMoney .moneyText,
.totalMoney .money
{
	color:#282828;
	font-size:16px;
	font-weight: bold;
}
.bandwidhtHead .yingxiao2{
	font-size:18px;
}
.bandwidhtHead .yingxiao2,
.totalMoney .shituxiangqing{
	padding-right:4px;
}
.totalMoney .money,
.bandwidhtHead .yingxiao2,
.totalMoney .shituxiangqing{
	color:#007AFF;
}
.bandwidthText{
	display:flex;
	justify-content: space-between;
	width:100%;
	margin-top:10px;
}
.bandwidthText:first-child{
	color:#232323
}
.bandwidthText:last-child{
	color:#2C2C2C
}
.bandwidhtHead{
	background:#fff;
	padding:9px 12px;
	margin-bottom:9px;
}

.bandwidthInfo{
   width:100%;
}
// .tip-info{
// 	i{
// 		vertical-align:middle;
// 		font-size:12px;
// 		margin-right:2px;
// 		display:inline-block;
// 		color:#FF4B4B;
// 	}
// 	.tip-text{
// 		font-size:12px;display:inline-block;
// 		color:#FF4B4B;
// 		white-space:nowrap;
// 		 transform-origin:left center;
// 		-webkit-transform: scale(0.8);
// 		-moz-transform: scale(0.8);
// 		-o-transform: scale(0.8);
// 		transform: scale(0.8);
// 	}
// 	background:#fff;
// 	padding:12px;
// 	display:flex;
// 	align-items:center;
// 	width:100%
// }
.bandwidthInfo li,
.totalMoney{
	background:#fff;
	margin-bottom:9px;
	padding:0 12px;
	height:44px;
	line-height:44px;
	border-bottom:1px solid #EBEBEB;
	margin-bottom:0 !important;
	display:flex;
	justify-content: space-between;
	box-sizing:border-box;
}
.brandwidthMoney{
	background:#fff;
	position:fixed;
	bottom:0;
	left:0;
	width:100%;
	border-top:1px solid #EAEAEA;
	box-sizing:border-box;
}
.brandwidthMoney .brandwidthP{
	padding:16px;
}
.brandwidthMoney button{
	background:#1681FB;
	height:44px;
	line-height:44px;
	color:#fff;
	text-align: center;
	border-radius:22px;
	border:none;
	width:100%;
}
.sign-wrapper{
    margin: 12px 12px 120px 12px;
}
.villageColor{
	color:#BBBBBB
	}
</style>
