<template>
    <div class="wrap">
       <div class="tab-wrap">
           <span class="item" :class="{'active':tabIndex==0}" @click="tabIndex=0">已开服务</span>
           <span class="item" :class="{'active':tabIndex==1}" @click="tabIndex=1">员工套餐</span>
           <span class="item" :class="{'active':tabIndex==3}" @click="tabIndex=3">H套餐</span>
           <!-- <span class="item" :class="{'active':tabIndex==2}" @click="tabIndex=2">宽带套餐</span> -->
       </div>
       <section class="content-wrap" v-show="tabIndex==0">
           <div class="line">
               <span class="lbl">员工套餐：</span>
               <span class="val" v-if="selfStaffProd.isOpen == '0'">未开通</span>
               <ul class="val-ul" v-else>
                   <li class="val-li" :key="item.prodId" v-for="item in selfStaffProd.pkgInfo">
                       <p class="title">{{item.prodName}}</p>
                       <p v-show="item.endDate" class="date">失效时间:{{item.endDate}}</p>
                       <span class="btn" @click="cancelProd(item)" v-show="ifCancel(item.endDate)">退订</span>
                   </li>
               </ul>
               <span class="btn"  @click="tabIndex=1" v-show="selfStaffProd.isOpen == '0'">开通</span>
           </div>

           <div class="line">
               <span class="lbl">叠加包套餐：</span>
               <span class="val" v-if="selfAttach.isOpen == '0'">未开通</span>
               <ul class="val-ul" v-else>
                   <li class="val-li" :key="item.prodId" v-for="item in selfAttach.pkgInfo">
                       <p class="title">{{item.prodName}}</p>
                       <p v-show="item.endDate" class="date">失效时间:{{item.endDate}}</p>
                       <span class="btn" @click="cancelProd(item)" v-show="ifCancel(item.endDate)">退订</span>
                   </li>
               </ul>
               <span class="btn" @click="tabIndex=1" v-show="selfAttach.isOpen == '0'">开通</span>
           </div>
           <div class="line">
               <div>
                    <span class="lbl">H套餐：</span>
                    <span class="hProdbtn" style="margin-right:50px;" v-if="selfHProd.isOpen != '0'" v-show="doHProdDeleteSecond"  @click="tjHPreDelete(selfHProd.pkgInfo[0])">退订</span>
                    <span class="hProdbtn" @click="tabIndex=3" v-if="selfHProd.isOpen != '0'" v-show="doHProdDeleteSecond" >变更</span>
                    <span class="val" v-if="selfHProd.isOpen == '0'">未开通</span>
               </div>
               <div v-if="selfHProd.pkgInfo">
                    <p v-if="selfHProd.pkgInfo.length>0 && selfHProd.pkgInfo[0].endDate"  class="hProddate">失效时间:{{selfHProd.pkgInfo.length>0&&selfHProd.pkgInfo[0].endDate}}</p>
               </div>
               <div v-if="selfHProd.pkgInfo">
                    <ul class="val-ul" v-if="selfHProd.isOpen != '0'">
                        <li class="val-li" :key="item.prodId" v-for="item in selfHProd.pkgInfo.length>0 && selfHProd.pkgInfo[0].subProdItem">
                            <p class="title">{{item.prodName}}</p>
                        </li>
                    </ul>
               </div>
               <span class="btn" @click="tabIndex=3" v-show="selfHProd.isOpen == '0'">开通</span>

           </div>
           <div style="display:none" :class="[selfBandProd.openedBand == '1' && selfBandProd.isOpen == '0'?'flex-line':'line']">
               <span class="lbl">宽带套餐：</span>
               <div class="val-multi" v-if="selfBandProd.isOpen == '0' && selfBandProd.openedBand == '1'">已开通普通家庭宽带套餐,须至前台退订后才能办理</div>
               <span class="val" v-else-if="selfBandProd.isOpen == '0'">未开通</span>
               <ul class="val-ul" v-else>
                   <li class="val-li" :key="item.prodId" v-for="item in selfBandProd.pkgInfo">
                        <p class="title">{{item.prodName}}</p>
                        <p v-show="item.endDate" class="date">失效时间:{{item.endDate}}</p>
                        <span class="btn" @click="tabIndex=2">变更</span>
                   </li>
                   <!-- <p class="val" :key="item.prodId" v-for="item in selfBandProd.pkgInfo">{{item.prodName}}</p> -->
               </ul>
               <span class="btn" @click="tabIndex=2" v-if="selfBandProd.openedBand == '0' && selfBandProd.isOpen == '0'">开通</span>
           </div>
       </section>
       <section class="content-wrap"  v-show="tabIndex==1">
           <div class="prod-wrap">
               <h1>员工套餐（三选一）</h1>
                <div class="prod-div" v-for="item in staffList"
                    @click="clickProd('staff',item)"
                    :key="item.prodId">
                    <a class="op-left">
                        <span class="iconfont" :class="ifInListClz('staff',true,item)"></span>
                    </a>
                    <p class="text">
                        <span class="title">{{item.prodName}}</span>
                        {{item.prodDesc}}
                    </p>
                </div>
           </div>
           <div class="prod-wrap margin16">
               <h1>叠加包套餐</h1>
                <div class="prod-div" v-for="item in attachList"
                    @click="clickProd('attach',item)"
                    :key="item.prodId">
                    <a class="op-left">
                        <span class="iconfont" :class="ifInListClz('attach',false,item)"></span>
                    </a>
                    <p class="text">
                        <span class="title">{{item.prodName}}</span>
                        {{item.prodDesc}}
                    </p>
                </div>
           </div>

           <div class="tip-wrapper">
               <h2 class="tip-title">提示</h2>
               <p class="tip-desc">
                   1·员工套餐：A、B、C套餐为叠加套餐（三选一），不可共享。叠加套餐开通后，用户原有主套餐不会自动退订，如用户无需继续使用原主套餐，需自行将原主套餐转自由选8元或其他合适的主套餐。
               </p>
               <p class="tip-desc">
                   2·员工叠加套餐，订购为立即生效，退订为次月生效，开通首月按天折算，月底补齐。流量叠加包和语音叠加包不互斥，可叠加订购，最多订购1次。
               </p>
               <p class="tip-desc">
                   3.原5G极速服务办理已升级，员工无需自主开通。已开通员工优惠套餐的客户，在更换5G终端后，自动触发开通5G功能对应的速率服务，默认为1000Mbps。
               </p>
               <p class="tip-desc">
                   4.30元包月（200M宽带+1台互联网电视）、30元提速员工体验套餐（提至1000M）业务需至营业厅办理。
               </p>
           </div>
           <div class="btn-wrap">
               <NlButton count="10" defaultState="counting" enableTip="一键办理" @click="subscribeProducts"></NlButton>
           </div>
       </section>
       <section class="content-wrap"  v-show="tabIndex==3">
           <div class="fatherprod-wrap">
               <h1 class="whitecolor padding75">可选H套餐(多选二)</h1>
                <div v-for="item in hList"
                    @click.stop="clickProd('h',item)"
                    :key="item.prodId" class="fatheritembox">

                    <div class="whitecolor padding75">
                        <div class="prod-div"  @click.stop="clickfatherProd(item)" >
                            <a class="op-left" >
                                <span class="iconfont" :class="ifInListClz('h',false,item)"></span>
                            </a>
                            <p class="text">
                                <span class="title">{{item.prodName}}</span>
                                {{item.prodDesc}}
                            </p>
                        </div>
                    </div>


                    <div class="prodchild-wrap margin4">
                        <div class="prod-div" v-for="childitem in item.subProdItem" @click.stop="clickchildProd(childitem,item)" :key="childitem.prodId">
                            <a class="op-left">
                                <span class="iconfont" :class="[childitem.openFlag?'checkboxround1':'checkboxround0']"></span>
                            </a>
                            <p class="text">
                                <span class="title">{{childitem.prodName}}</span>
                                {{childitem.prodDesc}}
                            </p>
                        </div>
                    </div>
                </div>
           </div>

           <div class="tip-wrapper">
               <h2 class="tip-title">提示</h2>
               <p class="tip-desc">
                   1.用户开通H套餐后，选择两项新业务，若用户已开通对应的产品，次月起减免费用，当月正常收费。
               </p>
               <p class="tip-desc">
                   2·H套餐仅做功能费减免，选中的商品需用户自行前往办理，办理的路径或URL见新业务描述。
               </p>
               <p class="tip-desc">
                   3.H套餐中的新业务支持变更，变更次月生效。
               </p>
           </div>
           <div class="btn-wrap">
               <NlButton count="10" defaultState="counting" enableTip="一键办理" @click="tjHPre"></NlButton>
           </div>
       </section>
       <section v-show="tabIndex==2">
           <div class="prod-wrap">
               <h1>可选宽带套餐（三选一）</h1>
                <div class="prod-div" v-for="item in bandList"
                    @click="clickProd('band',item)"
                    :key="item.prodId">
                    <a class="op-left">
                        <span class="iconfont" :class="ifInListClz('band',true,item)"></span>
                    </a>
                    <p class="text">
                        <span class="title">{{item.prodName}}</span>
                        {{item.prodDesc}}
                    </p>
                </div>
           </div>
           <div class="tip-wrapper">
               <h2 class="tip-title">提示</h2>
               <p class="tip-desc">
                   1·员工宽带套餐，订购/变更为次月生效，本渠道暂不支持退订，当月可进行多次变更，以最新一次变更为准；若员工前期已开通普通家庭宽带套餐，则需要至营业前台退订原宽带套餐后，方可在本页面重新订购员工宽带套餐。
               </p>
           </div>
           <div class="btn-wrap">
               <button class="btn" @click="bandSubmit"> {{selfAttach.isOpen == '0'?'下一步':'办理'}}</button>
           </div>
       </section>
    </div>

</template>
<script>

    import Storage from '@/base/storage'
    import Authenct from 'components/common/Authenticate/Authenticate'
    import {dateFormat} from '@/base/utils'
    import NlButton from 'components/common/NlButton';

    //2:立即，4:次日，3:次月
    const EFFECT_NOW = '2';//立即
    const EFFECT_NEXT_DAY = '4';//次日
    const EFFECT_NEXT_MONTH = '3';//次月

    export default {
        data(){
            return {
                tabIndex:0,//头部tab索引
                telnum:'',//客户手机号
                uinfo:{},
                jqData:{},//鉴权结果
                staffList:[],//员工套餐
                attachList:[],//叠加包
                bandList:[],//宽带套餐
                hList:[],//H套餐
                curAttach:[],//员工当前点击的叠加包
                curBandProd:[],//员工当前点击的宽带
                curStaffProd:[],//员工当前点击的员工套餐
                selfAttach:{},//员工已经办理的叠加包
                selfBandProd:{},//员工已经办理的宽带
                selfStaffProd:{},//员工已经办理的员工套餐
                curHProd:[],//员工当前点击的H套餐
                selfHProd:{},//员工已经办理的H套餐
                doHProdDeleteSecond:true,  //H套餐是否是第一次退订
                curHSubProd:[],//H套餐子产品
            }
        },
        methods:{
            //退订
            cancelProd(prod){
                this.doAuthenct(this.doTuiDing,prod);
            },
            //员工套餐、叠加包套餐办理 及宽带产品变更
            doTuiDing(prod){
                let param = {
                    authType: this.jqData.authtype,
                    telnum: this.telnum,
                    location: Storage.get('location'),//位置信息
                    latitude: Storage.get('latitude'),//纬度
                    longitude: Storage.get('longitude'),//经度
                    staffId: this.uinfo.staffId,
                    stationId:this.uinfo.stationId,//岗位编码
                    deviceType:/android|harmony/gi.test(navigator.userAgent)?'ANDROID':'IOS'
                };
                let orderList = [];
                let cancelList = [];
                let prodItem = {
                    'opertype':'PCOpDel',// PCOpRec:开通  PCOpDel:关闭
                    'attrlist':'',
                    'oldprodid':'',
                    'servlist':'',
                    'pcintrela':'PCIntRelaNormal',
                    'ismodel':'0',
                    'starttime':'',
                    'endtime':'',
                    'prodpkg':'',
                    'objtype':'',
                    'privid':'',
                    'modelid':''
                };
                prodItem.prodid = prod.prodId;//产品编码
                // 员工基础套餐，订购/变更/退订均为次月生效
                prodItem.efftype = EFFECT_NEXT_MONTH;//2:立即，4:次日，3:次月
                cancelList.push(prodItem);
                param.prodOrder = JSON.stringify(orderList);//订购的产品列表
                param.prodCancel = JSON.stringify(cancelList);//取消的产品列表
                this.$http.post('/xsb/personBusiness/staffpkg/h5SubscribeProducts',param).then(res => {
                    let {retCode,retMsg} = res.data;
                    if(retCode === '0'){
                        this.$alert('办理成功');
                    } else {
                        this.$alert(retMsg || '办理失败');
                    }
                });
            },
            //判断是否为当月失效
            ifCancel(date){
                let retFlg = true;
                let now = new Date();//当天
                let monthLast = new Date(now.getFullYear(), now.getMonth() + 1, 0);//当月的最后一天
                let nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1);//下个月的最后一天

                if(date){
                    //失效时间为当月的最后一天，则不能再退订
                    if(date == dateFormat(monthLast,'yyyy-MM-dd') || date == dateFormat(nextMonth,'yyyy-MM-dd')){
                        retFlg = false;
                    }
                }
                return retFlg;
            },
            //点击宽带TAB页面的办理
            bandSubmit(){
                if(this.selfBandProd.openedBand == '1'&& this.selfBandProd.isOpen == '0'){
                    this.$alert('您已开通普通家庭宽带套餐，须至前台退订后才能办理');
                    return;
                }
                if(this.curBandProd.length == 0){
                    this.$alert('请选择宽带产品');
                    return;
                }
                if (this.curBandProd.length == 1 && this.selfBandProd.pkgInfo.length == 1) {
                    let itemA = this.curBandProd[0];
                    let itemB = this.selfBandProd.pkgInfo[0];
                    if(itemA.prodId == itemB.prodId){
                        this.$alert('宽带产品没有发生变更');
                        return;
                    }
                }
                if(this.selfBandProd.isOpen == '1'){
                    //走宽带产品变更
                    this.doAuthenct(this.doBandChange);
                } else {
                    //走宽带开通
                    this.doAuthenct(this.goKuanPage);
                }
            },
            //跳到宽带开通页面
            goKuanPage(){
                let item = this.curBandProd[0];//因为只能先一个，所以取第一个
                let qryObj = {
                    prodName:item.prodName,
                    prodId:item.prodId,
                    pkgprodId:'2013000001',
                    limitBandWidth:item.bandWidth,
                    isPackage:'0',//是否产品包
                    effectType: '2'//生效方式 0、立即生效 1、次日生效 2 次月生效
                }
                //跳到宽带开通页面
                this.$router.push({
                    path:'/kuandai',
                    query: {
                        telnum:this.telnum,
                        prod: JSON.stringify(qryObj)
                    }
                })
            },
            //宽带产品变更
            doBandChange(){
                let param = {
                    authType: this.jqData.authtype,
                    telnum: this.telnum,
                    location: Storage.get('location'),//位置信息
                    latitude: Storage.get('latitude'),//纬度
                    longitude: Storage.get('longitude'),//经度
                    staffId: this.uinfo.staffId,
                    stationId:this.uinfo.stationId,//岗位编码
                    deviceType:/android|harmony/gi.test(navigator.userAgent)?'ANDROID':'IOS'
                };
                let orderList = this.setOrderList('PCOpRec','staff_pack',this.curBandProd,this.selfBandProd.pkgInfo);//订购的员工基础套餐
                let cancelList = this.setOrderList('PCOpDel','staff_pack',this.selfBandProd.pkgInfo,this.curBandProd);//取消的员工基础套餐

                if(orderList.length == 0 && cancelList.length == 0){
                    this.$alert('没有选择产品或者没有变更');
                    return;
                }
                param.prodOrder = JSON.stringify(orderList);//订购的产品列表
                param.prodCancel = JSON.stringify(cancelList);//取消的产品列表
                this.$http.post('/xsb/personBusiness/staffpkg/h5SubscribeProducts',param).then(res => {
                    let {retCode,retMsg} = res.data;
                    if(retCode === '0'){
                        this.$alert('办理成功');
                    } else {
                        this.$alert(retMsg || '员工宽带变更办理失败');
                    }
                });
            },
            doAuthenct(fn,param){
                if(this.jqData.result != '1'){ //鉴权首次登录的情况
					  Authenct({
					    popFlag:true,
                        idCardWay:false,
                        readOnlyFlag:true,
						telnum:this.telnum
					}, (obj) => {
					    this.jqData = obj;
						fn(param);
					});
				}else{	//鉴权登录过的情况
					fn(param);
				}
            },
            //员工套餐、叠加包套餐办理前鉴权
            subscribeProducts(){
                if (this.curStaffProd.length>0){
                   //弹出确认框，让用户确认
                  // var prodName=this.curStaffProd[0].prodName;
                  this.$messagebox({
                    title: '提示',
                    message: '员工套餐：A、B、C套餐为叠加套餐（三选一），不可共享。叠加套餐开通后，用户原有主套餐不会自动退订，如用户无需继续使用原主套餐，需自行将原主套餐转自由选8元或其他合适的主套餐。',
                    showCancelButton: true,
                    showConfirmButton: true,
                    confirmButtonText: "已知继续办理",
                    cancelButtonText: "取消办理"
                  }).then(action => {
                    if (action == 'confirm') {
                      this.doAuthenct(this.doSubmit);
                    }
                  });
                }else{
                   this.doAuthenct(this.doSubmit);
                }
            },
            //H套餐办理前鉴权
            tjHPre(){
                this.doAuthenct(this.doHProdSubmit);
            },
            //H套餐办理前鉴权HProdDelete
            tjHPreDelete(item){
                this.doAuthenct(this.doHProdDelete,item);
            },
            //员工套餐、叠加包套餐办理 及宽带产品变更
            doSubmit(){
                let param = {
                    authType: this.jqData.authtype,
                    telnum: this.telnum,
                    location: Storage.get('location'),//位置信息
                    latitude: Storage.get('latitude'),//纬度
                    longitude: Storage.get('longitude'),//经度
                    staffId: this.uinfo.staffId,
                    stationId:this.uinfo.stationId,//岗位编码
                    deviceType:/android|harmony/gi.test(navigator.userAgent)?'ANDROID':'IOS'
                };

                let orderList = this.setOrderList('PCOpRec','staff_pack',this.curStaffProd,this.selfStaffProd.pkgInfo);//订购的员工基础套餐
                orderList.push(...this.setOrderList('PCOpRec','attach_pack',this.curAttach,this.selfAttach.pkgInfo));

                // let cancelList = this.setOrderList('PCOpDel','staff_pack',this.selfStaffProd.pkgInfo,this.curStaffProd);//取消的员工基础套餐
                // cancelList.push(...this.setOrderList('PCOpDel','attach_pack',this.selfAttach.pkgInfo,this.curAttach))
                let cancelList = [];
                if(orderList.length == 0 && cancelList.length == 0){
                    this.$alert('没有选择产品或者没有变更');
                    return;
                }
                param.prodOrder = JSON.stringify(orderList);//订购的产品列表
                param.prodCancel = JSON.stringify(cancelList);//取消的产品列表
                this.$http.post('/xsb/personBusiness/staffpkg/h5SubscribeProducts',param).then(res => {
                    let {retCode,retMsg} = res.data;
                    if(retCode === '0'){
                        this.$alert('办理成功');
                    } else {
                        this.$alert(retMsg || '办理失败');
                    }
                });
            },
            getInstanceid(prodId){
                let retId = "";
                for(let i = 0; i < this.curHProd.length; i++){
                    if(prodId == this.curHProd[i].prodId){
                        retId = this.curHProd[i].instanceid;
                    }
                }
                return retId;
            },
             //H套餐退订
            doHProdDelete(item){
                let url = '/xsb/personBusiness/staffpkg/h5OrderSubmit';
                let param = {
                    servicenumber:this.telnum,
                    location: Storage.get('location'),//位置信息
                    latitude: Storage.get('latitude'),//纬度
                    longitude: Storage.get('longitude'),//经度
                    staffId: this.uinfo.staffId,
                    stationId:this.uinfo.stationId,//岗位编码
                    deviceType:/android|harmony/gi.test(navigator.userAgent)?'ANDROID':'IOS',
                    signId:'empty'
                }
                let orderItemArr = [];
                let orderItem = {};
                orderItem.instanceid = this.getInstanceid(item.prodId);//获取已选产品的instanceid
                orderItem.itemid = item.prodId;//包产品编码
                orderItem.effectmode =  '2';  //effectmode 2次月生效
                orderItem.actiontype  = 'D'; // actiontype A添加 M修改 D删除
                orderItemArr[orderItemArr.length] = orderItem;
                param.oderItemStr = JSON.stringify(orderItemArr);
                console.info(param);
                //请求服务端
                this.$http.post(url,param).then(res => {
                    let {retCode,retMsg} = res.data;
                    if(retCode === '0'){
                        this.$alert('办理成功');
                        this.doHProdDeleteSecond = false;
                    } else {
                        this.$alert(retMsg || '办理失败');
                    }
                })
            },
            //H套餐订购/变更--FSOP2BDS1113
            doHProdSubmit(){
                let url = '/xsb/personBusiness/staffpkg/h5OrderSubmit';
                let param = {
                    servicenumber:this.telnum,
                    location: Storage.get('location'),//位置信息
                    latitude: Storage.get('latitude'),//纬度
                    longitude: Storage.get('longitude'),//经度
                    staffId: this.uinfo.staffId,
                    stationId:this.uinfo.stationId,//岗位编码
                    deviceType:/android|harmony/gi.test(navigator.userAgent)?'ANDROID':'IOS',
                    signId:'empty'
                }
                let orderItemArr = [];
                let actiontype = 'M';// actiontype A添加 M修改
                if(this.selfHProd.isOpen == '0'){//添加
                    actiontype = 'A';
                }
                for(let i = 0; i < this.hList.length; i++){
                    let pkgProd = this.hList[i];//产品包
                    if(!pkgProd.openFlag){
                        break;
                    }
                    let orderItem = {instanceid:"",orderitemprop:[]};
                    orderItem.instanceid = this.getInstanceid(pkgProd.prodId);//获取已选产品的instanceid
                    orderItem.itemid = pkgProd.prodId;//包产品编码
                    orderItem.effectmode = '2';//传2 变更是次月生效
                    if(actiontype == 'A'){ //添加时立即生效
                        orderItem.effectmode = '0';
                    }else if(actiontype == 'M'){ //变更时次月生效
                        orderItem.effectmode = '2';
                    }
                    orderItem.actiontype = actiontype;//A添加 M修改
                    let prodStr = [];//产品串
                    let childList = pkgProd.subProdItem;
                    for(let j = 0; j < childList.length; j++){//循环子奖品
                        if(childList[j].openFlag){
                            prodStr[prodStr.length] = childList[j].prodId;//子产品编码，用逗号隔开
                        }
                    }
                    if(prodStr.length < 2){//目前只有一个产品包，暂时可以这么判断
                        this.$alert('必须选择两个产品');
                        return;
                    }
                    if(prodStr.length > 2){
                        this.$alert('只能选择两个产品');
                        return;
                    }
                    let orderitemprop = {
                        propacttype:actiontype,
                        propcode:'3123131232',//固定值 3123131232
                        svalue:prodStr.join(',')//用逗号隔开
                    };
                    orderItem.orderitemprop[0] = orderitemprop;
                    orderItemArr[orderItemArr.length] = orderItem;
                }
                if(orderItemArr.length == 0){
                    this.$alert('请选择两个产品');
                    return;
                }
                param.oderItemStr = JSON.stringify(orderItemArr);
                console.info(param);
                //请求服务端
                this.$http.post(url,param).then(res => {
                    let {retCode,retMsg} = res.data;
                    if(retCode === '0'){
                        this.$alert('办理成功');
                    } else {
                        this.$alert(retMsg || '办理失败');
                    }
                })
            },
            //只做push
            setSimpleList(opertype,pkgType,selectProd){
                let retList = [];
                let list = Object.assign([],selectProd);//员工套餐

                if(!list || list.length == 0){//如果当前选中的套餐为空，则表示没有订购的产品
                    return [];
                }
                for(let k = 0; k < list.length; k++){//循环当前选中的套餐
                    let item = list[k];
                    let prodItem = {
                        'opertype':opertype,// PCOpRec:开通  PCOpDel:关闭
                        'attrlist':'',
                        'oldprodid':'',
                        'servlist':'',
                        'pcintrela':'PCIntRelaNormal',
                        'ismodel':'0',
                        'starttime':'',
                        'endtime':'',
                        'prodpkg':'',
                        'objtype':'',
                        'privid':'',
                        'modelid':''
                    };
                    // let isChange = true;
                    // if(selfList && selfList.length > 0){
                    //     //针对变更，循环订购过的套餐
                    //     for(let i = 0; i < selfList.length; i++){
                    //         //原先的套餐没有发生变更
                    //         if(selfList[i].prodId === item.prodId){
                    //             isChange = false;
                    //             break;
                    //         }
                    //     }
                    // }
                    // if(isChange){
                        prodItem.prodid = item.prodId;//产品编码
                        // 员工基础套餐，订购/变更/退订均为次月生效
                        prodItem.efftype = EFFECT_NEXT_MONTH;//2:立即，4:次日，3:次月
                        if(pkgType === 'attach_pack' && opertype === 'PCOpRec'){
                            prodItem.efftype = EFFECT_NOW;//员工叠加套餐，订购为立即生效
                        }
                        if(pkgType === 'band_pack' &&　opertype == 'PCOpRec'){//宽带套餐订购
                            prodItem.attrlist = 'p100048=' + item.bandWidth;
                        }
                        retList.push(prodItem);

                    // }
                }
                return retList;
            },
            //设置订购的产品数组对象
            setOrderList(opertype,pkgType,selectProd,orderedProd){
                let retList = [];
                let list = Object.assign([],selectProd);//员工套餐

                if(!list || list.length == 0){//如果当前选中的套餐为空，则表示没有订购的产品
                    return [];
                }
                let selfList = Object.assign([],orderedProd);//之前订购过的员工套餐
                for(let k = 0; k < list.length; k++){//循环当前选中的套餐
                    let item = list[k];
                    let prodItem = {
                        'opertype':opertype,// PCOpRec:开通  PCOpDel:关闭
                        'attrlist':'',
                        'oldprodid':'',
                        'servlist':'',
                        'pcintrela':'PCIntRelaNormal',
                        'ismodel':'0',
                        'starttime':'',
                        'endtime':'',
                        'prodpkg':'',
                        'objtype':'',
                        'privid':'',
                        'modelid':''
                    };
                    let isChange = true;
                    if(selfList && selfList.length > 0){
                        //针对变更，循环订购过的套餐
                        for(let i = 0; i < selfList.length; i++){
                            //原先的套餐没有发生变更
                            if(selfList[i].prodId === item.prodId){
                                isChange = false;
                                break;
                            }
                        }
                    }
                    if(isChange){
                        prodItem.prodid = item.prodId;//产品编码
                        // 员工基础套餐，订购/变更/退订均为次月生效
                        prodItem.efftype = EFFECT_NEXT_MONTH;//2:立即，4:次日，3:次月
                        if(pkgType === 'attach_pack' && opertype === 'PCOpRec'){
                            prodItem.efftype = EFFECT_NOW;//员工叠加套餐，订购为立即生效
                        }
                        if(pkgType === 'band_pack' &&　opertype == 'PCOpRec'){//宽带套餐订购
                            prodItem.attrlist = 'p100048=' + item.bandWidth;
                        }
                        retList.push(prodItem);

                    }
                }
                return retList;
            },
            //设置产品对象
            setProdList(pkgType,isOpen){
                let retList = [];
                let list = [];
                if(pkgType === 'staff'){//员工套餐
                    list = Object.assign([],this.curStaffProd);
                } else if (pkgType === 'attach'){//叠加包
                    list = Object.assign([],this.curAttach);
                }
                if(!list || list.length == 0){
                    return [];
                }
                let effectmode = '2';//2次月生效
                if(isOpen === '0'){//0未开通
                    //员工叠加套餐，订购为立即生效
                    if(pkgType === 'attach'){
                        effectmode = '0';//0立即生效
                    }
                    //actiontype A订购
                    for(let i = 0; i < list.length; i++){
                        let item = {};
                        item.itemid = list[i].prodId;//产品编码
                        item.instanceid = '';//默认传空
                        item.actiontype = 'A';//订购
                        //员工基础套餐，订购/变更/退订均为次月生效
                        item.effectmode = effectmode;//生效方式 0立即生效 1次日生效 2次月生效
                        retList.push(item);
                    }
                } else {//变更
                    let actiontype = 'A';
                    let selfList = Object.assign([],this.selfAttach.pkgInfo);
                    if(pkgType === 'staff'){//订购的员工套餐
                        selfList = Object.assign([],this.selfStaffProd.pkgInfo);
                    }
                    //循环订购的员工套餐
                    for(let k = 0 ; k < selfList.length; k++){
                        let selfItem = selfList[k];
                        let isChange = true;
                        //循环当前选中的套餐
                        for(let i = 0; i < list.length; i++){
                            //原先的套餐没有发生变更
                            if(list[i].prodId === selfItem.prodId){
                                list.splice(i,1);
                                isChange = false;
                                break;
                            }
                        }
                        if (isChange) {//原来订购里有，但是当前选中的包里没有,则对之前的做退订
                            let item = {};
                            item.itemid = selfItem.prodId;//产品编码
                            item.instanceid = '';//默认传空
                            item.actiontype = 'D';//D退订
                            //员工基础套餐 叠加包，退订均为次月生效
                            item.effectmode = '2';//生效方式 0立即生效 1次日生效 2次月生效
                            retList.push(item);
                        }
                    }
                    //员工叠加套餐，订购为立即生效
                    if(pkgType === 'attach'){
                        effectmode = '0';//0立即生效
                    }
                    for(let i = 0; i < list.length; i++){
                        let item = {};
                        item.itemid = list[i].prodId;//产品编码
                        item.instanceid = '';//默认传空
                        //判断当前产品是否在用户订购的产品中，不在则为订购
                        item.actiontype = actiontype;//订购
                        //员工基础套餐，订购/变更/退订均为次月生效
                        item.effectmode = effectmode;//生效方式 0立即生效 1次日生效 2次月生效
                        retList.push(item);
                    }
                }
                return retList;
            },
            //staff员工套餐 attach 叠加包 band宽带套餐
            ifInListClz(pkgType,radioFlg,info){
                let list = [];
                let hasIt = false;
                if(pkgType === 'staff'){//员工套餐
                    list = this.curStaffProd;
                } else if (pkgType === 'band'){//宽带套餐
                    list = this.curBandProd;
                } else if (pkgType === 'attach'){//叠加包
                    list = this.curAttach;
                } else if (pkgType === 'h'){
                    list = this.hList;
                    let fatherflag = false;
                    for(let m = 0;m < list.length;m++){
                        if(info.prodId == list[m].prodId ){
                            for(var n = 0;n < list[m].subProdItem.length;n++){
                                if(list[m].subProdItem[n].openFlag){
                                    fatherflag = true;
                                    break;
                                }
                            }
                            this.$set(list[m],'openFlag',fatherflag);
                            break;
                        }
                    }
                    return fatherflag?'checkboxround1':'checkboxround0'
                }
                hasIt = list.find((item) => {
                    return item.prodId == info.prodId;
                });
                if(hasIt){//已经选中
                    if(radioFlg){
                        return 'yuandian'
                    } else {
                        return 'checkboxround1'
                    }
                } else {//未选中
                    return 'checkboxround0';
                }
            },
            //H套餐点击  实现全选  反全选
            clickfatherProd(item){
                let fatherflag = false;
                if(!item.openFlag){//初始没有openFlag值  或者 之前是为假  点击后变成true
                    fatherflag = true;
                }
                this.$set(item,'openFlag',fatherflag);
                for(let n = 0; n < item.subProdItem.length; n++){
                    this.$set(item.subProdItem[n],'openFlag', fatherflag);
                }
            },
            //H套餐子套餐点击事件
            clickchildProd(childitem,item){
                if(childitem.openFlag){
                    childitem.openFlag = !childitem.openFlag;
                } else {
                    this.$set(childitem,'openFlag',true);
                }
            },
            //点击产品事件
            clickProd(pkgType,info){
                let list = [];
                let hasIt = -1;
                if(pkgType === 'staff'){//员工套餐 只能选一个
                    list = this.curStaffProd;
                } else if (pkgType === 'band'){//宽带套餐
                    list = this.curBandProd;
                } else if (pkgType === 'attach'){//叠加包
                    list = this.curAttach;
                }
                list.find((item,idx) => {
                    if (item.prodId == info.prodId){
                        hasIt = idx;
                        return hasIt;
                    }
                });
                if(~hasIt){//已经选中
                    // if(pkgType === 'attach'){//可多选
                    // }
                    list.splice(hasIt,1);//删除
                } else {//未选中
                    if(pkgType === 'attach'){//可多选
                        list.push(info);
                    } else {//只能单选
                        // list.length = 0;//先清空，保证当前只能选一个
                        // list.push(info);
                        list.splice(hasIt,1,info);
                    }
                }
            },
            //查询已经开通的OLE员工套餐
            getSelfData(){
                let url = '/xsb/personBusiness/staffpkg/h5QryOpenedService?telnum=' + this.telnum;
                this.$http.get(url).then(res =>{
                    let {retCode,retMsg,data} = res.data;
                    if(retCode === '0'){
                        for(let i = 0; i < data.length; i++){
                            if(data[i].pkgType === 'staff_pack'){//staff_pack员工套餐
                                this.$set(this.selfStaffProd,'isOpen',data[i].isOpen);
                                this.$set(this.selfStaffProd,'pkgInfo',data[i].pkgInfo);
                                if(data[i].pkgInfo &&　data[i].pkgInfo.length > 0){
                                    this.curStaffProd = Object.assign([],data[i].pkgInfo);
                                }
                            } else if(data[i].pkgType === 'attach_pack'){//attach_pack叠加包
                                this.$set(this.selfAttach,'isOpen',data[i].isOpen);
                                this.$set(this.selfAttach,'pkgInfo',data[i].pkgInfo);
                                if(data[i].pkgInfo &&　data[i].pkgInfo.length > 0){
                                    this.curAttach = Object.assign([],data[i].pkgInfo);
                                }
                            } else if(data[i].pkgType === 'band_pack'){// band_pack宽带套餐
                                this.$set(this.selfBandProd,'isOpen',data[i].isOpen);
                                this.$set(this.selfBandProd,'pkgInfo',data[i].pkgInfo);
                                this.$set(this.selfBandProd,'openedBand',data[i].openedBand)
                                if(data[i].openedBand == '0' && data[i].pkgInfo &&　data[i].pkgInfo.length > 0){
                                    this.curBandProd = Object.assign([],data[i].pkgInfo);
                                }
                            } else if(data[i].pkgType === 'h_pack'){//H套餐
                                this.$set(this.selfHProd,'isOpen',data[i].isOpen);
                                this.$set(this.selfHProd,'pkgInfo',data[i].pkgInfo);
                                if(data[i].pkgInfo &&　data[i].pkgInfo.length > 0){
                                    this.curHProd = Object.assign([],data[i].pkgInfo);
                                }
                            }
                        }
                        //查询员工套餐信息
                        this.getAllData();
                    }else {
                        this.$alert(retMsg || '获取套餐数据异常');
                    }
                });

            },
            //查询员工套餐信息
            getAllData(){
                let url = '/xsb/personBusiness/staffpkg/h5QryStaffPkgInfo';
                this.$http.get(url).then(res => {
                    let {retCode,retMsg,data} = res.data;
                    if(retCode === '0'){
                        for(let i = 0; i < data.length; i++){
                            if(data[i].pkgType === 'staff_pack'){//staff_pack员工套餐
                                this.staffList = data[i].pkgInfo;
                            } else if(data[i].pkgType === 'attach_pack'){//attach_pack叠加包
                                this.attachList = data[i].pkgInfo;
                            } else if(data[i].pkgType === 'band_pack'){// band_pack宽带套餐
                                this.bandList = data[i].pkgInfo;
                            } else if(data[i].pkgType === 'h_pack'){// h_pack H套餐
                                this.hList = data[i].pkgInfo;
                                this.initHprodOpenFlag();//初始化选中状态
                            }
                        }
                    } else {
                        this.$alert(retMsg || '获取套餐数据异常');
                    }
                })
            },
            //根据手机号获取地市编号
            resetCrmIdAndRegion(){
                let url = '/xsb/personBusiness/customerView/h5CustomerInfo?telnum='+ this.telnum;
                this.$http.get(url).then(res => {
                    let {retCode,retMsg,data} = res.data;
                    if(retCode === '0'){
                        this.uinfo.region = data.cityId;
                        //虚拟工号规则：地市编码两位+7777+渠道号，渠道号 03
                        this.uinfo.crmId = data.cityId + '777703';
                        Storage.session.set('userInfo',this.uinfo);
                        this.getSelfData();//查询已经开通的OLE员工套餐

                    } else {
                        this.$alert(retMsg || '获取平台账号失败')
                    }
                });
            },
            initHprodOpenFlag(){
                if(!this.curHProd || this.curHProd.length == 0 || !this.curHProd[0].subProdItem){
                    return;
                }
                let firstProd = this.curHProd[0];
                for(let j = 0; j < firstProd.subProdItem.length; j++){
                    let item = firstProd.subProdItem[j];
                    for(let i = 0; i < this.hList.length; i++){
                        let childList = this.hList[i].subProdItem;
                        for(let k = 0; k < childList.length; k++){
                            if(childList[k].prodId == item.prodId){
                                this.$set(childList[k],'openFlag',true);
                                // this.$set(item,'prodName',childList[k].prodName); 接口已返回产品名称
                            }
                        }
                    }
                }
            }
        },
        components:{NlButton},
        created(){
            console.info(this.$router);
            this.uinfo = Storage.session.get('userInfo');
            this.telnum = this.uinfo.telnum;
            this.resetCrmIdAndRegion();//更新工号和地市

        }

    }
</script>
<style scoped lang="less">

.wrap{
    background-color:#ECF0FA;
    height:100%;
    overflow: auto;
    section{
        margin-top:34px;
    }
    .tab-wrap{
        height:48px;
        line-height: 48px;
        background-color:#F8F8F8;
        box-sizing: border-box;
        color:#3D3D3D;
        font-size:0px;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 100;
        display:flex;
        .item{
            // flex-shrink:1;
            // flex-grow: 1;
            // flex-basis: auto;
            flex:1 1 auto;
            height:100%;
            box-sizing: border-box;
            font-size: 14px;
            text-align: center;
            &.active{
                color:#1681FB;
                border-bottom:2px solid #187BEC;
                background-color:#fff;
                font-weight: bolder;
            }
        }
    }
    .content-wrap{
        padding: 8px 0;
        background-color:#fff;
    }
    .multi-line{
        .lbl{
            vertical-align: top;
        }


    }
    .flex-line{
        display:flex;
        min-height:42px;
        line-height: 42px;
        position:relative;
        align-items: baseline;
        .lbl{
            font-size:14px;
            color:#7F7F7F;
            flex-shrink: 0;
            padding-left:12px;
        }
        .val-multi{
            font-size:14px;
            color:#4F4F4F;
            line-height: 20px;
            padding-left: 4px;
            padding-right: 8px;
        }
    }
    .line{
        min-height:42px;
        position:relative;
        .lbl{
            font-size:14px;
            color:#7F7F7F;
            padding-left:12px;
            line-height: 42px;
        }

        .hProddate{
            color:#CA4848;
            font-size:12px;
            height:16px;
            margin-left: 12px;
            margin-top: -10px;
        }
        .val{
            font-size:14px;
            color:#4F4F4F;
        }
        .val-list{
            display:inline-block;
            .val{
                height:30px;
            }
        }

        .btn{
            position:absolute;
            right:1rem;
            font-size:10px;
            color:#0081FF;
            border-radius:15px;
            border:1px solid rgba(0,129,255,1);
            top: 50%;
            transform: translateY(-50%);
            line-height: 18px;
            height: 20px;
            padding: 0 8px;
        }

        .hProdbtn{
            position:absolute;
            right:1rem;
            font-size:10px;
            color:#0081FF;
            border-radius:15px;
            border:1px solid rgba(0,129,255,1);
            transform: translateY(50%);
            line-height: 18px;
            height: 20px;
            padding: 0 8px;
        }

    }
}
.val-ul{
    padding: 0 0.5rem;
    border-bottom: 1px solid #EAEAEA;
    .val-li{
        border-bottom:1px dashed #EAEAEA;
        position:relative;
        padding: 12px 12px;
        .title{
            font-size:13px;
            color:#4F4F4F;
            padding-right:70px;
        }
        .date{
            color:#CA4848;
            font-size:12px;
            height:16px;
        }
        .btn{
            right:0.5rem;
        }

    }
    li:last-child{
        border-bottom:none;
    }
}
.margin16{
    margin-top:16px;
}
.prod-wrap{
    padding: 0 0.75rem;
    background-color:#fff;
    h1{
        font-size:14px;
        font-weight:600;
        color:rgba(80,80,80,1);
        height: 36px;
        line-height: 36px;
    }
    .prod-div:last-child{
        border:none;
    }
    .prod-div{
        position:relative;
        padding: 16px 0 16px 0;
        border-bottom:1px dashed #D7D7D7;
        .op-left{
            position:absolute;
            display: inline-block;
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            .yuandian,.checkboxround1{
                color:#1681FB;
            }
        }
        .text{
            font-size:12px;
            color:#7F7F7F;
            padding-left: 40px;
            .title{
                font-size:12px;
                font-weight:600;
                color:rgba(80,80,80,1);
                display:block;
                width:100%;
            }
        }
    }

}
.tip-wrapper{
    background-color:#fff;
    margin-top:16px;
    margin-bottom:86px;
    padding-bottom: 8px;
    .tip-title{
        font-size:12px;
        font-weight:600;
        color:#505050;
        text-align: center;
        padding:10px 0 6px 0;
    }
    .tip-desc{
        font-size:12px;
        line-height:16px;
        color:#838383;
        padding: 4px 24px;
    }
}
.btn-wrap{
    height:76px;
    background-color:#FFFFFF;
    width: 100%;
    text-align: center;
    line-height: 76px;
    position: fixed;
    bottom: 0px;
    padding: 0 12px;
    box-sizing: border-box;
    border-top: 1px solid #EAEAEA;
    .btn{
        background: #1681FB;
        border-radius: 22px;
        height: 44px;
        width: 100%;
        font-size: 14px;
        color: #FFFFFF;
        outline: none;
        border: none;
    }
}
.fatherprod-wrap{
    padding:0 0;
    background:rgba(236,240,250,1);
    box-shadow:0px 2px 6px 0px rgba(214,214,214,1);
    h1{
        font-size:14px;
        font-weight:600;
        color:rgba(80,80,80,1);
        height: 36px;
        line-height: 36px;
    }

    .prod-div{
        position:relative;
        padding: 16px 0 16px 0;
        .op-left{
            position:absolute;
            display: inline-block;
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            .yuandian,.checkboxround1{
                color:#1681FB;
            }
        }
        .text{
            font-size:12px;
            color:#7F7F7F;
            padding-left: 40px;
            .title{
                font-size:12px;
                font-weight:600;
                color:rgba(80,80,80,1);
                display:block;
                width:100%;
            }
        }
    }

}


.prodchild-wrap{
    padding: 0 0.25rem;
    margin-left: 8px;
    margin-right: 8px;
    margin-bottom: 4px;
    background-color:rgba(243,243,243,1);

    .prod-div:last-child{
        border:none;
    }
    .prod-div{
        position:relative;
        padding: 16px 0 16px 0;
        border-bottom:1px dashed #D7D7D7;
        .op-left{
            position:absolute;
            display: inline-block;
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            .yuandian,.checkboxround1{
                color:#1681FB;
            }
        }
        .text{
            font-size:12px;
            color:#7F7F7F;
            padding-left: 40px;
            .title{
                font-size:12px;
                font-weight:600;
                color:rgba(80,80,80,1);
                display:block;
                width:100%;
            }
        }
    }

}
.margin4{
    margin-top: 4px;
}

.padding75{
    padding: 0 0.75rem;
}

.orginalcolor{
    background:rgba(236,240,250,1);
}

.whitecolor{
    background-color:#fff;
}
</style>
