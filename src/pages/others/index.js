//import Vue from 'vue'
import Others from '@/pages/others/DownloadPage.vue'
import PancDownload from './PancDownload.vue'
import YsxDownload from './YsxDownload.vue'
import PadDownload from './PadDownload.vue'
import ResidentTip from './ResidentTip.vue'
import Router from 'vue-router'
import axios from 'axios'
import {decrptParam} from '@/base/AesEncrptUtil.js'

import '../../assets/css/iconfont.css'
import '../../base/less/download.css'


// http响应拦截器
axios.interceptors.response.use(res => {
    let url = res.config.url;
    try{
        if(typeof(res.data) != "object"){//不是对象的话就是加密串
            let data = decrptParam(res.data);
            data = JSON.parse(data);
            res.data = data
        }
    } catch(err){
        console.info('统一解密异常：' + err);
    }
    return res;
  }, error => {
    return Promise.reject(error)
})

const install = function (Vue, config = {}) {
    if (install.installed) return;
    Vue.prototype.$http = axios;
}
// auto install
if (typeof window !== 'undefined' && window.Vue) {
    install(window.Vue);
};
var router = new Router({
    routes: [
        {
            path:'/',
            redirect: '/ald'
        },
        {
            path:'/ald',
            component:Others
        },
        {
            path:'/downPanc',
            component:PancDownload
        },
        {
            path:'/downYsx',
            component:YsxDownload
        },
        {
          path:'/downPad',
          component: PadDownload
        },
        {
            path:'/residentTip',
            component: ResidentTip
        },
    ]
});

new Vue({
    el: '#app',
    router,
    // components: {Others},
    // template: '<Others/>'
    template: '<div><router-view></router-view></div>'
})
