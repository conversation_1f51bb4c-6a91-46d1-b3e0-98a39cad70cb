<template>
    <div class="download">
        <div class="dl-tip" v-show="isInWeiXin"><img src="static/img/down-tip.png" width="100%" height="100%"/></div>
        <div class="dl-main">
            <div class="dl-mhead">
                <div class="dl-mh-logo"><img src="../../../static/img/logo.png" width="100%" height="100%"/></div>
                <div class="dl-mh-right">
                    <div class="dl-mhr-title">阿拉盯</div>
                    <div class="dl-mhr-desc">移动端统一受理平台</div>
                    <div class="dl-mhr-btns">
                        <div class="dl-btn apple" @click="iosCk">
                            <a :href="iosUrl">
                                <span class="iconfont changyonglogo35 iconapple"></span>
                                <span class="dl-btn-txt">IOS</span>
                            </a>
                        </div>

                        <div class="dl-btn android">
                            <a :href="androidUrl">
                                <span class="iconfont ai-app iconandroid"></span>
                                <span class="dl-btn-txt">安卓</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="dl-minfo">
                <div class="dl-mf-title">更新内容</div>
                <div class="dl-title">Ios：</div>
                <ul class="dl-mf-ul">
                    <li v-for="(item,index) in descIosArr" :key="index">{{item}}</li>
                </ul>
                <div class="dl-mf-size">
                    <span>大小：{{iosObj.appSize}}</span>
                </div>
                <div class="dl-mf-size martop0">
                    <span>版本：{{iosObj.versionNameReal||iosObj.versionName}}</span>
                </div>
                <div class="dl-mf-size martop0">
                    <span class="">更新时间：{{iosObj.updateTime}}</span>
                </div>

                <div class="dl-title">Andriod：</div>
                <ul class="dl-mf-ul">
                    <li v-for="(item,index) in descAdArr" :key="index">{{item}}</li>
                </ul>
                <div class="dl-mf-size">
                    <span>大小：{{androidObj.appSize}}</span>
                </div>
                <div class="dl-mf-size martop0">
                    <span>版本：{{androidObj.versionNameReal||androidObj.versionName}}</span>
                </div>
                <div class="dl-mf-size martop0">
                    <span class="">更新时间：{{androidObj.updateTime}}</span>
                </div>

                <div class="dl-ios-resolve" @click="resolveInfoCk">ios“未受信用的企业开发者”的解决办法</div>
            </div>
            <div class="dl-mpic">
                <div class="dl-piclis"><img :src="imgArr[0]" width="100%"/></div>
                <div class="dl-piclis"><img :src="imgArr[1]" width="100%"/></div>
            </div>
        </div>
        <!--网页底部呈现信息-->
        <div class="copyright-info">
            <a class="info" href="https://beian.miit.gov.cn" target="_blank">{{ beiAnIcp }}</a>
            <p class="info">{{ beiAnCopyRight }}</p>
        </div>
        <!--弹框-->
        <div class="pop-filter" v-show="showResolve"></div>
        <div class="pop-resolve" :class="{fla:showResolve}" v-show="showResolve">
            <div class="pop-rs-head">
                <span>“未受信用的企业级开发者”的解决办法</span>
                <span class="pop-close" @click="closePop">
                    <span class="iconfont guanbi iconguanbi"></span>
                </span>
            </div>
            <div class="pop-rs-inner">
                <div class="pop-appledesc">
                    <p class="pop-plis">因苹果公司的政策原因，在iOS系统中首次安装企业版应用时会出现"未受信任的企业级开发者"提示 </p>
                    <div class="pinfo-img"><img src="../../../static/img/resolve1.png" width="100%"/></div>
                </div>
                <div class="pop-appleresolve">
                    <p class="pop-plis">解决办法如下（以IOS11系统为例）：</p>

                    <div class="resolve-lis">
                        <p class="pop-plis">1、在手机中打开设置功能，选择“通用”</p>
                        <div class="resolve-lisimg"><img src="../../../static/img/resolve2.png" width="100%"/></div>
                    </div>

                    <div class="resolve-lis">
                        <p class="pop-plis">2、在通用中，选择"描述文件与设备管理"功能</p>
                        <div class="resolve-lisimg"><img src="../../../static/img/resolve3.png" width="100%"/></div>
                    </div>

                    <div class="resolve-lis">
                        <p class="pop-plis">3、在描述文件与设备管理中的企业级应用分类中，选择要安装的企业应用的文件名称（与打开时的提示一致），点击进入</p>
                        <div class="resolve-lisimg"><img src="../../../static/img/resolve4.png" width="100%"/></div>
                    </div>

                    <div class="resolve-lis">
                        <p class="pop-plis">4、进入企业签名页面后，确认企业签名中的公司名称与应用名称后，点击信任"企业签名公司名称"</p>
                        <div class="resolve-lisimg"><img src="../../../static/img/resolve5.png" width="100%"/></div>
                        <div class="resolve-lisimg"><img src="../../../static/img/resolve6.png" width="100%"/></div>
                    </div>
                    <div class="resolve-lis">
                        <p class="pop-plis">5、回到桌面，重新打开应用即可使用</p>
                    </div>
                </div>
            </div>
        </div>

        <!--下载提示-->
        <div class="downloadTip" v-show="iosDownloadTip">正在安装，请稍后查看桌面</div>

    </div>
</template>

<script>
    import {getRealUrl} from '@/base/utils'
    import downMixin from './downMixin.js'
    export default {
        mixins:[downMixin],
        data() {
            return {
                showResolve: false,          //解决方案弹框是否显示
                iosObj: {},                  //ios信息
                androidObj: {},              //安卓信息
                descIosArr: [],                 //更新信息列表
                descAdArr: [],                 //更新信息列表
                imgArr: [],                  //图片路径
                iosDownloadTip:false,           //ios下载提示
                androidUrl:'',//安卓端下载URL
                iosUrl:'',//苹果端下载URL
            };
        },
        created() {
            this.getVersion();
        },
        methods: {
            //打开解决方案弹框
            resolveInfoCk() {
                this.showResolve = true;
            },

            //关闭解决方案弹框
            closePop() {
                this.showResolve = false;
            },
            //获取版本信息
            getVersion() {
                let _this = this;
                this.$http.get('/xsb/api-user/appVersionCenter/h5appVersionInfo').then((res) => {

                    if (res.data.retCode == '0') {
                        let d = res.data;
                        _this.iosObj = d.data.ios;
                        _this.androidObj = d.data.android;
                        _this.imgArr = d.data.image.split('<br>');
                        _this.androidUrl = _this.androidObj.url;
                        _this.iosUrl = _this.iosObj.url;
                        _this.descIosArr = _this.iosObj.desc.split('<br>');
                        _this.descAdArr = _this.androidObj.desc.split('<br>');
                        _this.checkUrl();//根据当前URL判断下载URL
                    }
                }).catch((err) => {
                })
            },
            //根据当前URL判断下载URL
            checkUrl(){
                let url = window.location.href;
                if(~url.indexOf(getRealUrl('center1'))){
                    this.androidUrl = this.androidUrl.replace(getRealUrl('center2'),getRealUrl('center1'));
                    this.iosUrl = this.iosUrl.replace('.plist','_221.plist');
                }
            },
            //ios
            iosCk(){
                this.iosDownloadTip = true;
                setTimeout(()=> {
                    this.iosDownloadTip = false;
                },5000)
            }
        },
        computed:{
            isInWeiXin(){
                return ~navigator.userAgent.toLowerCase().indexOf('micromessenger');
            }
        }
    };
</script>


<style scoped lang="less">

</style>
