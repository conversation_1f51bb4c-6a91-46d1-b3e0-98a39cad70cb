<template>
    <div>
        <Header class="header" tsTitleTxt="网格通app常驻后台配置流程"  backType="custom" @emGoPrev="goPrev"></Header>  
        <div class="wrapper">
            <div class="title" @click="show()">
                <i :class="['iconfont',{'youjiantou': !showIos},{'xiala':showIos}]"></i>
                1. iOS手机请参考以下配置流程
            </div>
             <div class="content" v-show="showIos">
                <div v-for="(son,index) in ios.sonList" :key="index">
                    <div class="txt">{{son.desc}}</div>
                    <div class="img"><img :src="son.img" /></div>
                </div>
            </div>
             <div class="title" @click="showAnd()">
                <i :class="['iconfont',{'youjiantou': !showAndroid},{'xiala':showAndroid}]"></i>
                2. Android手机请参考以下配置流程
            </div>
            <div  v-for="(item,index) in androidList" :key="index" v-show="showAndroid">
                <div class="title item-top" @click="showAndroidItem(item)">
                    <i :class="['iconfont',{'youjiantou': !item.isShow},{'xiala':item.isShow}]"></i>
                    {{item.name}}
                </div>
                <div class="content" v-show="item.isShow">
                    <div v-for="(son,index) in item.sonList" :key="index">
                        <div class="txt">{{son.desc}}</div>
                        <div class="img"><img :src="son.img" /></div>
                    </div>
                </div>
            </div>
           
        </div>
    </div>

</template>

<script>
import Header from "components/common/Header.vue";
import {ANDROID_LIST,IOS} from '@/base/ResidentTip.js';

export default {
    components: { Header },
    data(){
        return{
            showIos: false,
            showAndroid:false,
            androidList:[],
            ios:{}
        }
    },
    methods:{
        goPrev(){
            history.go(-1);
        },
        show(){
            this.showIos = !this.showIos;
        },
        showAnd(){
            this.showAndroid = !this.showAndroid;
        },
        showAndroidItem(item){
            item.isShow = !item.isShow;
        }
    },
    created(){
        this.androidList = ANDROID_LIST;
        this.ios = IOS;
        console.log(ANDROID_LIST)
    }
    
}
</script>

<style scoped lang="less">
    .header{
        border-bottom: 1px solid #EAEAEA;
    }
    .wrapper {
    position: absolute;
    top: 0px;
    left: 0px;
    right: 0px;
    bottom: 0px;
    z-index: 10;
    margin-top: 45px;
    margin-bottom: 10px;
    margin-right: 10px;
    background-color: #fff;
    height:100%;
    overflow: auto;
    width: 100%;
}
    .title{
    margin-top: 20px;
    margin-left: 26px;
        i{
            font-size:10px;
        }
    }
    .item-top{
        font-size: 15px;
        margin-top: 10px;
        margin-left: 45px;
    }
    .content{
        padding: 15px;
        background:rgba(255,255,255,1);
        box-shadow:4px 4px 12px 0px rgba(184,184,184,0.5);
        border-radius:20px 20px 20px 20px;
        position:relative;
        z-index:98;
        margin-left: 10px;
        margin-right: 10px;
        margin-top: 14px;
        margin-bottom: 25px;
    .img{
        text-align: center;
    }
    img{
        width: 290px;
        margin-top:10px;
    }   
    .txt{
        font-size: 15px;
        color: #333;
        margin-left: 26px;
        margin-top: 10px;
        line-height: 20px;
    }
    }
</style>

