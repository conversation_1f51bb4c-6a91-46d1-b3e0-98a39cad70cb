import router from '@/router/pad/padRouter.js'
import 'mint-ui/lib/style.css'
import '!style-loader!css-loader!less-loader!@/base/less/pad.less'
import Storage from '@/base/storage'
import {LOG_CONFIG,CONSTVAL} from '@/base/config'

import ClientJs from '@/base/clientjs'
import {BASE64} from '@/base/coding'
import { Toast,DatetimePicker,Loadmore,Indicator,Switch } from 'mint-ui';
import MessageBox from 'components/common/NlMessageBox/message-box.js';
import Main from '@/pages/pad/Index.vue'
import {chgStrToDate,imageIsExist,getQueryVariable,getRandomStr,getGroupKey} from '@/base/utils'
//vuex
import store from '../../store'
import LoadingDirective from 'components/common/NlLoading/loading.js'
import Clickoutside from '@/base/clickoutside'
//记录日志用
import '@/base/smartLog.js'
import '../../assets/css/iconfont.css'
// import Vconsole from 'vconsole'
// new Vconsole();
//如果需要防止重复点击 请给相应的元素添加样式needsclick 例 class=" needsclick"
import  '@/base/fastclick.js'
import axios from '@/base/nlAxios.js'
import {PointLessLog,pointMixin} from '@/base/log/pointLessLog.js'
FastClick.attach(document.body,{tapDelay:4000})

let pageName = 'NonePage';//首页名称
let logStep = 0;//记录日志的步骤

// 菜单链
window.breadIds = [];
window.updateBreadId = (level, privId, resetLowerLevels = true) => {
    // 判断是否有父级菜单
    if(level - 1  > window.breadIds.length) {
        console.log('未添加父级菜单，无法添加此级别菜单')
        return;
    }
    // 重置子菜单
    if (resetLowerLevels) {
        window.breadIds.length = level;
    }
    window.breadIds[level - 1] = privId
}


//获取客户端请求入口的参数
function getInitData(){
    let qryMap = getQueryVariable();
    // Storage.session.set('locationHref',window.location.href);
    let reg = new RegExp("(^|&)" + 'userInfo' + "=([^&]*)(&|$)");
    let matchSearch = window.location.search.substr(1) || window.location.hash.split("?")[1];
    let r = matchSearch && matchSearch.match(reg);
    let uinfo = {};

    if(r != null){
        uinfo = BASE64.decode(r[2]);
        uinfo = JSON.parse(uinfo);
    } else {
        uinfo = {};
        let qryMap = getQueryVariable();
        if(qryMap['tokenid']){
            uinfo = {
                "region": qryMap['region'],
                "staffId": qryMap['staffId'],
                "servNumber": qryMap['servNumber'],
                "staffId1": qryMap['servNumber'],
                "crmId": qryMap['crmId'],
                "tokenid": qryMap['tokenid']
            };
        }
    }
    window.isZwsx = uinfo.comeFrom == 'ams';//是否为装维随销
    if(Storage.session.get('userInfoNew')){
        Storage.session.set('userInfo',Storage.session.get('userInfoNew'));
    } else {
        Storage.session.set('userInfo',uinfo);
    }
    Storage.session.set('tokenId',uinfo.tokenid);
    Storage.session.set('isPad','1');//判断是否为pad

  if(uinfo.tokenid && qryMap['gobackFlag']!='webview') {//防止瑞月等相关页面拉起无token的情况
    if(uinfo.tokenid){
      axios.defaults.headers.tokenid = uinfo.tokenid;
    }
    axios.defaults.headers.aldregion = uinfo.region+'_'+uinfo.servNumber;//新增aldregion
    axios.defaults.headers.operaterPhone = uinfo.servNumber;//add by qhuang 2021-11-24登录token改造
    axios.defaults.headers.imei = uinfo.imei;//add by qhuang 2021-11-24登录token改造
  }
}
getInitData();

/**
 * 页面初始化获取客户端提供的路由信息
 */
window.getSysInfoCB = function(obj){
    let serverUrl = obj.serverUrl;
    if(process.env.NODE_ENV === 'development'){
        //开发联调环境需要配置跨域标识
        serverUrl = '/apiM' + serverUrl;
    }
    //从客户端获取 走真数据的时候 放开注释
    axios.defaults.baseURL = serverUrl;
    try {
        Storage.set('webUrl',serverUrl);
        if(!obj.location && Storage.get('location')){//客户端返回的位置信息为空
            //上一次的位置信息不为空，则不更新
        } else {
            Storage.set('location',obj.location || 'empty');
            Storage.set('latitude',obj.latitude || 'empty');
            Storage.set('longitude',obj.longitude || 'empty');
        }
    } catch (error) {
    }
    Storage.set('deviceInfo',obj);

}
ClientJs.getSysInfo('getSysInfoCB');

//从第三方页面回阿拉盯相关页面初始化axios中的信息
window.initTokenAfterBack = function(_axios, uinfo){
  _axios.defaults.headers.tokenid = uinfo.tokenid;
  _axios.defaults.headers.operaterPhone = uinfo.servNumber;//add by qhuang 2021-11-24登录token改造
  _axios.defaults.headers.imei = uinfo.imei;//add by qhuang 2021-11-24登录token改造
  _axios.defaults.headers.aldregion = uinfo.region+'_'+uinfo.servNumber;//新增aldregion
  Storage.session.set('tokenId',uinfo.tokenid);
  let oldUinfo = Storage.session.get('userInfo');
  let relTelnum = oldUinfo && oldUinfo.relTelnum;//学生手机号
  if(relTelnum){
    uinfo.relTelnum = relTelnum;
  }
  Storage.session.set('userInfo',uinfo);
}

//提供给客户端切换路由调用的方法
window.setSysRouteInfo = function(serverUrl){
    axios.defaults.baseURL = serverUrl;
    Storage.set('webUrl', serverUrl);
}

axios.defaults.timeout = 70000;//默认70秒


const install = function(Vue, config = {}) {
    if (install.installed) return;
    Vue.config.productionTip = false;
    Vue.config.devtools = true;
    //日期控件
    Vue.component(DatetimePicker.name, DatetimePicker);
    Vue.component(Loadmore.name, Loadmore);

    // 调用 提示框
    Vue.prototype.$messagebox = MessageBox;

    //alert对话框
    Vue.prototype.$alert = (msg,title) =>{
        if( title === void 0 || title === '' ){
            title = '温馨提示';
        }
        if(msg && msg.length > 300){
            msg = msg.substr(0,300)
        }
        Vue.prototype.$messagebox.alert(msg,title);
    }

    Vue.mixin(pointMixin);
    Vue.$toast = Vue.prototype.$toast = Toast;
    Vue.$switch = Vue.prototype.$switch = Switch;
    //异步查询
    Vue.prototype.$http = axios;
    //局部加载
    Vue.directive('loading',LoadingDirective);
    Vue.directive('clickoutside',Clickoutside);
    Vue.prototype.CONSTVAL = CONSTVAL;

    //全局注册自定义指令，用于判断当前图片是否能够加载成功，可以加载成功则赋值为img的src属性，否则使用默认图片
    Vue.directive('real-img', async function (el, binding) {//指令名称为：real-img
        let imgURL = binding.value;//获取图片地址
        if (imgURL) {
            let exist = await imageIsExist(imgURL);
            if (exist) {
                el.setAttribute('src', imgURL);
            }
        }
    })

    if(Storage.session.get('tokenId')){//确保有token的情况下实例化
      console.info('===初始化无埋点调用链对象===')
      //无埋点调用链
      const pointLessLog = new PointLessLog(Vue,axios)
      Vue.$pointLesslog = Vue.prototype.$pointLesslog = pointLessLog
    }

    /**
     * 记录日志接口
     * eventName 事件名称或者方法名称、如果是点击类事件 传event对象
     * businessCode 功能编码（一般和服务端保持一致）
     * gatherBody 采集内容
     * gatherType 采集类型 1:页面加载 2:页面操作、3：页面js报错 4：系统奔溃、5：客户端操作 默认2页面操作
     * gatherStep 采集步骤 00 到 99（不含00 99）
     */
    Vue.prototype.$log = (eventName,businessCode,gatherBody,gatherType) =>{
        if(LOG_CONFIG[pageName]){
            logStep++;
            let gatherStep = ("00" + logStep).substr(("" + logStep).length);
            let properties = {gatherBody:gatherBody,gatherStep:gatherStep};
            if(gatherType){
                properties.gatherType = gatherType;
            }
            smart['__loaded'] && smart.track_event(eventName,businessCode,properties)
        }
    }
    /**
    * @param {日期} val
    * @param {转换后的格式} fmt  如yyyy-MM-dd hh:mm:ss
    * @param {是否转换成今天 昨天}zhFlg
     */
    Vue.filter('dateShow', function(val,fmt,zhFlg) {
        val = chgStrToDate(val,true);
        if(isNaN(val) && !isNaN(Date.parse(val))){
            return val;
        }

        let now = new Date();
        let nowDay = now.getDate();
        let nowMonth = now.getMonth();
        let nowYear = now.getFullYear();
        let isTian = false;
        //参数中的日期是今天  明天  昨天
        if(nowYear == val.getFullYear() && nowMonth == val.getMonth() && Math.abs(nowDay - val.getDate()) <=1){
            isTian = true;
        }
        let o = {
            "M+": val.getMonth() + 1, //月份
            "d+": val.getDate(), //日
            "h+": val.getHours(), //小时
            "m+": val.getMinutes(), //分
            "s+": val.getSeconds(), //秒
            "q+": Math.floor((val.getMonth() + 3) / 3), //季度
            "S": val.getMilliseconds() //毫秒
        };

        if (/(y+)/.test(fmt)) {
            fmt = fmt.replace(RegExp.$1, (val.getFullYear() + "").substr(4 - RegExp.$1.length));
        }
        for (let k in o){
            if (new RegExp("(" + k + ")").test(fmt)) {
                if(isTian && zhFlg){//日
                    if(k == "M+"){
                        fmt = fmt.replace(RegExp.$1+'/', '');
                    } else if(k == "d+"){
                        if(o[k] === nowDay){
                            fmt = fmt.replace(RegExp.$1, '今日');
                        }else if(o[k] === nowDay - 1){
                            fmt = fmt.replace(RegExp.$1, '昨日');
                        }else if(o[k] === nowDay + 1){
                            fmt = fmt.replace(RegExp.$1, '明日');
                        }
                    } else {
                        fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
                    }
                }else{
                    fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
                }
            }
        }
        return fmt;
    });
}
// auto install
if (typeof window !== 'undefined' && window.Vue) {
  install(window.Vue);
};



//路由前置钩子
router.beforeEach((to, from, next) => {
    let rname = to.name;
    pageName = rname;
    logStep = 0;
    if(LOG_CONFIG[rname]){
        //需要记录日志
        smart['__loaded'] && smart.track_pv(rname,{gatherType:1,gatherBody:'',gatherStep:'00'});
    }

    let gobackFlag = to.query.gobackFlag == 'webview'?true:false;
    if(to.path == '/resetpassword' || to.path == '/faceRegister' || to.path =='/loginForFace' || gobackFlag){//刷脸注册、忘记密码
        next();
    } else if(to.path != '/page404' && !Storage.session.get('tokenId')){
         //这里判断用户是否登录，验证本地存储是否有token
         next({
            path: '/page404'
        })
    } else {
        if(to.name === 'NonePage'){
            ClientJs.getSysInfo('getSysInfoCB');
        }
        next();
    }

});

//路由后置钩子
router.afterEach((to, from) => {
    let rname = from.name;
    if(LOG_CONFIG[rname]){
        //需要记录上个页面结束的日志
        smart['__loaded'] && smart.track_pv(rname,{gatherType:1,gatherBody:'',gatherStep:'99',eventType:'exitPage'});
    }
})

new Vue({
    el: '#app',
    router,
    store,
    components: { Main },
    template: '<Main/>'
})
