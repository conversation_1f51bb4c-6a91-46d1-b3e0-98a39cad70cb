<template>
    <div class="pad-box">
        <LeftBar :flag="flag" ref="childBar"></LeftBar>
        <div class="right-content">
              <!--不缓存-->
            <router-view v-if="!$route.meta.keepAlive" :key="activeDate"></router-view>
             <!--缓存-->
            <keep-alive>
    	        <router-view v-if="$route.meta.keepAlive" :key="activeDate"></router-view>
            </keep-alive>
        </div>
        <div class="pad-content" v-if="showBox">
            <span class="pad-title">请选择岗位</span>
            <ul class="pad-station">
                <li @click="select(item)"
              :key="item.id"
              :class="{'selected':currentItem.stationId === item.stationId}"
              v-for="item in uinfo.stations">{{item.stationName}}</li>
            </ul>
        </div>
        <div class="pad-bg" v-if="showBox"></div>
    </div>
</template>
<script>
    import LeftBar from 'components/business/zhengqi/Ipad/LeftBar.vue'
    import Storage from '@/base/storage'

    export default {
        data(){
            return {
                uinfo:{},
                flag:'',//判断是哪个模块
                activeDate:'',
                showBox:'',//岗位弹出框
                currentItem:{},//岗位当前的值
                convs:[this.CONSTVAL.STATION_GB,this.CONSTVAL.STATION_AGB,
                    this.CONSTVAL.STATION_KHJL,this.CONSTVAL.STATION_HUWAI]//当前支持的岗位
            }
        },
        watch: {
            '$route'(to, from) {
               this.activeDate = new Date();
                let paths =['AdminGovernment','GovernmentIndex','zqWorkStage'],names=to.name;
                for(let i=0;i < paths.length;i++){
                    if(paths[i] == names){
                        this.flag = i+1;
                        this.$refs.childBar.goLi(paths[i],0);
                        this.$router.push('/');
                        return;
                    }
                }
            }
        },
        methods:{
            //选择岗位
            select(item) {
                this.currentItem = item;
                this.switchStation(item);
                // let results = this.convs.filter(function (items) {
                //     return items == item.stationId;
                // });
                // if(results.length > 0){
                //     this.switchStation(item);//切换岗位
                // }else{
                //     this.$toast('暂未开放');
                //     return;
                // }
            },
            switchStation(info){
                let url = `/xsb/api-user/user/h5switchStation?stationId=${info.stationId}&imei=${this.uinfo.imei}`;
                this.$http.get(url).then((res) => {
                    let data = res.data;
                    if(data.retCode == '0'){
                        this.uinfo.stationId = info.stationId;
                        this.uinfo.stationName = info.stationName;
                        this.uinfo.crmId = data.data.crmId;
                        // this.uinfo.relTelnum = this.uinfo.relTelnum;
                        this.uinfo.viewInfoList = data.data.viewInfoList;
                        Storage.session.set('userInfoNew',this.uinfo);
                        Storage.session.set('userInfo',this.uinfo);
                        this.chooseFlag();
                        this.showBox = false;
                    }else{
                        this.showBox = true;
                        this.$alert(data.retMsg || '切换岗位服务异常');
                    }
                })
            },
            //是否是这4个角色
            chooseFlag(){
                for(let i=0;i < this.convs.length;i++){
                    if(this.convs[i] == this.uinfo.stationId){
                        this.flag = i+1;
                        return;
                    }
                }
            }
        },
        created(){
            this.showBox = false;
            this.uinfo = Storage.session.get('userInfo');
            this.chooseFlag();
            if(!this.flag){
                this.showBox = true;
            }
          window['wakeUpFromApp'] = ()=>{

          }
        },
        components:{
            LeftBar
        },
        computed:{

        }

    }
</script>
<style scoped lang="less">
    .right-content{
        float: right;
        position:absolute;
        right:0;
        width:45%;
        height:100vh;
        overflow-y:auto;
    }
    .pad-content{
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%,-50%);
        background: #fff;
        z-index: 1000;
        border-radius: 9px;
        padding: 1rem 0;
        .pad-title{
            text-align: center;
            width: 100%;
            display: block;
            font-size: 16px;
            color: #686868;
            margin-bottom:10px;
        }
        .pad-station{
            max-height:200px;
            overflow:auto;
            height:auto;
            font-size:14px;
            li{
                color:#6b6b6b;
                text-align: center;
                border-bottom:1px solid #ddd;
                box-sizing: border-box;
                padding:0.5rem 5.5rem;
                &.selected{
                   color:#000;
                }
                &:last-child{
                	border:none;
                }
            }
        }

    }
    .pad-bg{
        position: fixed;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        opacity: 0.5;
        z-index: 999;
        background: #000;
        overflow: hidden;
    }
</style>
