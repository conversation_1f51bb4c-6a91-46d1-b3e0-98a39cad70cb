<template>
    <div class="html-wrap ">
        <!--缓存-->
        <keep-alive>
    	    <router-view v-if="$route.meta.keepAlive"></router-view>
        </keep-alive>
        <!--不缓存-->
        <router-view v-if="!$route.meta.keepAlive"></router-view>


    </div>

</template>
<script>

    import Storage from '@/base/storage'
    import axios from "axios";
    import {decrptParamN} from '@/base/AesEncrptUtil.js'

    export default {
      created(){
        //校验令牌
        this.checkAmsToken();
      },
        methods:{
          checkAmsToken() {
            if(!Storage.session.get('amsTokenId')){
              this.$alert("未传入TOKEN");
              this.$router.push('/page404');
              return false;
            }
            let parmes = {
              "paramInfo": Storage.session.get('amsTokenId'),
              unEncrpt:true
            };
            let url = "/xsb/api-user/amsLogin/amsTokenCheckRegion";
            this.$http.post(url, parmes).then((res) => {
              if (res.data) {
                let dataReturn = decrptParamN(res.data,"jSs1bHB3ZA==AlDT");
                axios.defaults.headers.aldregion = dataReturn;//新增aldregion
                this.checkAmsTokenCheck();
              } else {
                this.$alert("TOKEN异常");
                this.$router.push('/page404');
              }
            }).catch(res => {
              this.$alert("TOKEN异常");
              this.$router.push('/page404');
            });

          },
          checkAmsTokenCheck(){
            let parmes = {
              "paramInfo": Storage.session.get('amsTokenId'),
              unEncrpt:true
            };
            let url = "/xsb/api-user/amsLogin/amsToken";
            this.$http.post(url, parmes).then((res) => {
              let dataReturn = decrptParamN(res.data,"jSs1bHB3ZA==AlDT");
              dataReturn =JSON.parse(dataReturn);
              let {retCode, retMsg, data} = dataReturn
              if (retCode === '0') {
               // let serverUrl= 'https://' + window.location.host+":";
               //  Storage.set('webUrl',serverUrl);
                Storage.session.set('userInfo',data);
                Storage.session.set('tokenId',data.tokenid);
                Storage.set('location','empty');
                Storage.set('latitude','empty');
                Storage.set('longitude','empty');
                Storage.session.set('clientVersion','3.20.51');
                axios.defaults.headers.tokenid = data.tokenid;
                axios.defaults.headers.appId = "3001";
                axios.defaults.headers.aldregion = data.region+'_'+data.servNumber;//新增aldregion
                axios.defaults.headers.source = "27";
                axios.defaults.headers.operaterPhone = data.servNumber;
                axios.defaults.headers.imei = data.imei;
                this.$router.push('/amsMaintain');

              } else {
                this.$alert(retMsg || "TOKEN异常");
                this.$router.push('/page404');
              }
            }).catch(res => {
              this.$alert("TOKEN异常");
              this.$router.push('/page404');
            });
          },

        },
        components:{},
        computed:{},
        mounted(){
          // this.$router.push('/amsMaintain');

        }
    }
</script>
<style scoped lang="less">

</style>
