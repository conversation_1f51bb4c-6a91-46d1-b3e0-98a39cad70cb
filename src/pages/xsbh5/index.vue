<template>
    <div class="html-wrap " :class="{'flex-layout':ifFlex}">
        <!--缓存-->
        <keep-alive>
    	    <router-view v-if="$route.meta.keepAlive"></router-view>
        </keep-alive>
        <!--不缓存-->
        <router-view v-if="!$route.meta.keepAlive"></router-view>

        <Bottom v-if="ifFlex && !this.isJksx && !this.isZwsx&& !isBld && !isGb && !isAgb && !isJkzw &&!isSkzq"></Bottom>
        <BottomJikeZw v-if="ifFlex && this.isJksx"></BottomJikeZw>
        <BottomZw v-if="ifFlex && this.isZwsx"></BottomZw>
        <BottomBld v-if="ifFlex && isBld"></BottomBld>
        <BottomGB v-if="ifFlex && isGb"></BottomGB>
        <BottomAgb v-if="ifFlex && isAgb"></BottomAgb>
        <BottomFree v-if="ifFlex && isJkzw"></BottomFree>
        <!--商客专区底部按钮-->
        <BottomSk v-if="ifFlex && isSkzq"></BottomSk>
        <!--引导页-->
        <div class="guide-mask" :style="guideBig" v-show="promoteShowFlg">
            <div class="iconfont guanbi2 icon-close" @click="promoteClickRecord('2')"></div>
            <div :style="guideDivImg">
            <img class="guide-img" @click="promoteClickRecord('1')" ref="imgSize" @load="imgload" :src="imgSrc"/>
            </div>
        </div>

        <UpdatePrompts
            v-show='isUpdateShow'
            :nowVersion="version"
            :latestVersion="latestVersion"
            :downUrl='downUrl'
            :downLoadUrl="downLoadUrl"
            :isForceUpdate="isForceUpdate"
            :latestVersionDesc="latestVersionDesc"
            @afterUpdate="afterUpdate">
        </UpdatePrompts>

    <hegemony-screen-list :dataList='heScreenDataList' v-if='heScreenflag' :showType='showType' :onlineHour='onlineHour'></hegemony-screen-list>
      <FastTuiJianBench v-if='fastTuiJianBenchFlah'></FastTuiJianBench>
      <FastEntryPlan ref='fastEnRef'></FastEntryPlan>
      <xy-guide-page v-if='guideFlg' v-model:guideFlg='guideFlg'></xy-guide-page>
    </div>

</template>
<script>
    import Bottom from 'components/bottom.vue'
    import BottomZw from 'components/area/cmopInstall/bottomZw.vue'
    import BottomJikeZw from 'components/area/jikeInstall/bottomJikeZw.vue'
    import BottomBld from 'components/area/bld/bottomBld.vue'
    import BottomGB from 'components/business/zhengqi/BottomGb.vue'
    import BottomAgb from 'components/business/zhengqi/AdminGover/BottomAgb.vue'
    import Storage from '@/base/storage'
    import {iEncrpt} from '@/base/encrptH5.js'
    import ClientJs from '@/base/clientjs'
    import HandleMode from 'components/common/HandleModeSelect/HandleModeSelect'
    import { getRealUrl ,dateFormat} from "@/base/utils";
    import {cmpClientVersion, getClientType} from '@/base/utils';
    import ImageObj  from '@/base/request/ImageServReq';
    import UpdatePrompts  from 'components/common/updateprompts/UpdatePrompts.vue';
    import { aesEncryptAllParams, decrptParam } from '@/base/AesEncrptUtil.js';
    import HegemonyScreenList from 'components/business/HegemonyScreen/HegemonyScreenList';
    import FastEntryPlan from 'components/desk/FastEntryPlan.vue'
    import NLGetCopyShare  from 'components/common/NLGetCopyShare/getCopyShare.js'
    import FastTuiJianBench from 'components/business/marketingView/FastTuiJianBench.vue'
    import { setWaterMark,clearWatermark }  from "@/base/WaterMark.js";
    import XyGuidePage from 'components/desk/XYGuidePage.vue'
    import BottomFree from 'components/desk/jiaKeInstallMain/bottomFree.vue'
    import BottomSk from 'components/desk/skzq/bottomSk.vue'
    import { updateMixin } from '@/base/updateMixin.js'
    export default {
      mixins:[updateMixin],
        data(){
            return {
                isZwsx:false,
                isJksx:false,
                uinfo:{},
                busiPromoteObject:{},//业务推荐对象
                promoteShowFlg:false,
                localFlag:'',//业务推荐session标识
                knowledge:{},//知识点对象
                clientHeight:'',//页面高度
                imgBigSize:false,//长图
                jikeHallUrl:'',//集客大厅跳转地址
                imgSrc:"",
                isUpdateShow: false,
                heScreenflag: false,//霸屏展示
                showType: '1',//霸屏类型
                onlineHour: null,
                heScreenDataList:[],
                // 霸屏地市调用时间间隔，实现错峰调用
                regionInterval: {
                  '99': -60 * 1000,
                  '22': -50 * 1000,
                  '18': -30 * 1000,
                  '13': -20 * 1000,
                  '19': 0,
                  '12': 10 * 1000,
                  '17': 20 * 1000,
                  '16': 30 * 1000,
                  '23': 50 * 1000,
                  '14': 60 * 1000,
                  '21': 80 * 1000,
                  '20': 90 * 1000,
                  '15': 105 * 1000,
                  '11': 115 * 1000,
                },
                fastTuiJianBenchFlah:false,//是否展现活动推荐
                guideFlg:false,//是否展示客户经理小助手的引导页
                isForceUpdate: false,   //是否强制更新
                latestVersionDesc: "",
    };
        },
        created(){
            this.version = localStorage.getItem('appVersion');
            this.isZwsx = window.isZwsx;
            this.isJksx = window.isJksx;
            this.uinfo = Storage.session.get('userInfo');
            this.busiPromoteObject.imgname = 'area_default';
            this.clientHeight = document.documentElement.clientHeight || document.body.clientHeight;
            //this.getBusiPromoteList();
            let pagehash = window.location.hash;//获取当前页面的hash值
            if(this.isJksx){ //集客随销
              this.$router.push('/jikeInstallMaintain');
            }else if(this.isZwsx){//装维随销岗位
                this.$router.push('/installMaintain');
            } else if(~pagehash.indexOf('faceRegister')//add by qhuang at 20240417 刷脸登录、刷脸注册、忘记密码跳过霸屏等
              ||~pagehash.indexOf('loginForFace')
              ||~pagehash.indexOf('resetpassword')){//忘记密码

                return;
            } else if(this.uinfo&&this.uinfo.stationId==this.CONSTVAL.STATION_BLD){//岗位是便利店业主
                this.$router.push('/bldMaintain');
            } else if(this.uinfo&&this.uinfo.stationId==this.CONSTVAL.STATION_GB){//政企室经理
                this.$router.push('/adminGovernment');
            }else if(this.uinfo&&this.uinfo.stationId==this.CONSTVAL.STATION_AGB){//政企室客户经理
                this.$router.push('/governmentIndex');
            }else if(this.uinfo&&this.uinfo.stationId==this.CONSTVAL.STATION_MSC){//品专店合伙人
              this.$router.push('/mscHomePage');
            }else {
                if(window.location.hash == '#/'){//只有首页的时候查询
                    this.getBusiPromoteList();
                    //this.qryZkSwitch();
                    this.qrySwitchAuth();
                }
            }

            this.$store.commit('stationChangeModule/rewriteStation',this.uinfo.stationId);//保存当前登录的stationId
              // 霸屏
            if(this.uinfo&&this.uinfo.tokenid){
              this.getHScreenSwitch();
              this.getTimeHScreenSwitch();
            }
            ClientJs.getSysInfo('getSysInfoRM');

            if(this.shouldShowGuidePage()){
              this.guideFlg = true;
              localStorage.setItem('last_xy_enter_date', new Date());
            }
        },
        components: {
          Bottom,
          BottomZw,
          BottomBld,
          BottomGB,
          BottomAgb,
          BottomJikeZw,
          BottomFree,
          UpdatePrompts,
          HegemonyScreenList,
          FastEntryPlan,
          NLGetCopyShare,
          FastTuiJianBench,
          XyGuidePage,
          BottomSk
        },
        computed:{
            ifFlex(){
                if(this.$route.name == 'WorkStage'
                    || this.$route.name == 'WorkStageNew'
                    || this.$route.name == 'zqWorkStage'
                    || this.$route.name == 'Business'
                    || this.$route.name == 'businessNew'
                    || this.$route.name == 'My'
                    || this.$route.name == 'Tools'
                    || this.$route.name == 'toolsNew'
                    || this.$route.name == 'Area'
                    || this.$route.name == 'InstallMaintain'
                    || this.$route.name == 'MyZwsx'
                    || this.$route.name == 'JikeInstallMaintain'
                    || this.$route.name == 'MyJkZwsx'
                    || this.$route.name == 'MyBld'
                    || this.$route.name == 'BldArea'
                    || this.$route.name == 'BldMaintain'
                    || this.$route.name == 'GovernmentIndex'
                    || this.$route.name == 'GovernmentBusniess'
                    || this.$route.name == 'MyGb'
                    || this.$route.name == 'AdminGovernment'
                    || this.$route.name =='AdminGoverBusniess'
                    || this.$route.name =='AdminMyGb'
                    || this.$route.name == 'OutH5MidPage'
                    || this.$route.name == 'skzqHome'
                 ){
                    return true;
                }
                if(this.$route.name == 'GateWayBox'){
                  let query = this.$route.query;
                  let hasBottom = query.hasBottom;
                  if(hasBottom){
                    return true;
                  }
                }
                return false;
            },
            isBld(){
                if(this.$route.name == 'MyBld'
                    || this.$route.name == 'BldArea'
                    || this.$route.name == 'BldMaintain'
                ){
                    return true;
                }
                return false;
            },
            isGb(){
                if(this.$route.name == 'GovernmentIndex'
                    || this.$route.name == 'GovernmentBusniess'
                    || this.$route.name == 'MyGb'){
                    return true;
                }
                return false;
            },
            isAgb(){
                if(this.$route.name == 'AdminGovernment'
                    || this.$route.name == 'AdminGoverBusniess'
                    || this.$route.name == 'AdminMyGb'){
                    return true;
                }
                return false;
            },
            isJkzw(){
              return this.$store.state.stationChangeModule.currentStationId == this.CONSTVAL.STATION_JIAKEZW &&
              ['My', 'Business', 'Area', 'OutH5MidPage', 'Tools'].includes(this.$route.name);
            },
            // 商客专区（相关角色）
            isSkzq(){
              let currentStationId = this.$store.state.stationChangeModule.currentStationId
              return  ~this.CONSTVAL.SKZQ_ACCESS_STATIONS.indexOf(currentStationId) &&
                ['My', 'Business', 'Area', 'skzqHome', 'Tools'].includes(this.$route.name);
            },
            guideBig() {
                if (this.imgBigSize) {
                    return {"justify-content": "flex-start", "padding-top": "40px"};
                } else {
                    return {"justify-content": "center"};
                }
            },
            guideDivImg(){
                if (this.imgBigSize) {
                    return {"height":"90%", "overflow":"auto"};
                } else {
                    return {"overflow": "auto"};
                }
            }
        },
        methods:{
          //打开活动推荐弹窗
          openFastTuiJianBenchFlah(){
            this.fastTuiJianBenchFlah = !this.fastTuiJianBenchFlah;
          },

            //调用客户端获取当前版本
            checkCurVersion(){
                if (!cmpClientVersion(this.version, '2.21.120')) {
                    this.$toast("请升级最新版本");
                }else {
                    setTimeout(() => {
                        this.goStudyField();
                    }, 1000)
                }
            },
            //年度成绩单
            goStudyField() {
                let params = {
                    "userId": Storage.session.get('userInfo').servNumber,
                    "userName": Storage.session.get('userInfo').servNumber,
                    "phone": Storage.session.get('userInfo').servNumber
                }
                let url = "/xsb/personBusiness/learnGarden/h5GenerateLearnGardenToken";
                this.$http.post(url, params).then(res => {
                    let {retCode,retMsg,data} = res.data;
                    if (retCode == '0') {//成功的情况
                        let params = {
                            token: data.token,
                          baseUrl: getRealUrl("smartGridUrl"),
                          // linkUrl: `${getRealUrl("studyGarden")}/premiseui/ui-designer/vapps/grid-ziyue/h5-pages/dist/index.html#/?vappId=c0e8d3d2-4046-4e1d-a495-61039c1da6f6&instanceId=a7b4feb0-3e2f-421e-8474-88628b7f0091&vconsole=false`,
                          linkUrl: `${getRealUrl("smartGridUrl")}premiseui/ui-designer/vapps/grid-ziyue/h5-pages/dist/index.html#/?vappId=c0e8d3d2-4046-4e1d-a495-61039c1da6f6&instanceId=a7b4feb0-3e2f-421e-8474-88628b7f0091&vconsole=false`,
                        };
                        ClientJs.openLearningGarden(params);
                    } else {
                        this.$alert(retMsg || '请求失败');
                    }
                }).catch(err => {
                    this.$alert(err)
                })
            },
            imgload() {
                let imgSize = this.$refs['imgSize'].offsetHeight;
                if (imgSize-40 > this.clientHeight) {
                    this.imgBigSize=true;
                }else{
                    this.imgBigSize=false;

                }
            },
            getBusiPromoteList(){
                this.$http.get('/xsb/personBusiness/busiPromote/h5QryBusiPromoteList?telnum='+this.uinfo.servNumber+"&stationId="+this.uinfo.stationId)
                //this.$http.get('apiM' +('/xsb/personBusiness/busiPromote/h5QryBusiPromoteList?telnum='+this.uinfo.servNumber+"&stationId="+this.uinfo.stationId))
                    .then((res) => {
                        if(res.data.retCode=='0'){
                            this.busiPromoteObject = res.data.data;
                            //this.localFlag = '_' + this.busiPromoteObject.busicode + '_' + 'localPromoteFlag';
                            //let tmpPromote = Storage.get(this.localFlag);
                            //this.$alert(this.busiPromoteObject);
                            this.promoteShowFlg = this.busiPromoteObject.hasShow == '0';
                            // 获取图片src
                            if(!this.promoteShowFlg) return;
                            ImageObj.getImgUrl(this.busiPromoteObject.imgname).then(res=>{
                              this.imgSrc=res;
                              this.imgload();
                            })

                        }else{
                            //this.$alert(res.data.retMsg || "查询推荐活动失败");
                        }
                    })
                    .catch(res =>{
                        //this.$alert('报错:' + res);
                    });
            },
            //获取集客大厅跳转地址
            getJiKeHallUrl(param){
                let _this=this;
                this.$http.get('/xsb/personBusiness/jikeHall/h5getJiKeJumpUrl').then((res)=>{
                    if(res.data.retCode == '0'){
                        _this.jikeHallUrl = res.data.data;
                        _this.goJikeApp(param);
                    } else {
                        _this.$alert('获取集客大厅跳转地址失败');
                    }
                }).catch((response)=>{
                })
            },
            promoteDetail(){
                this.promoteShowFlg=false;
                let r=this.busiPromoteObject.route;
                if(r =='jikedating'){
                  this.bbossAuth();
                }else if(r.indexOf('gridReport')!=-1){  //年度成绩单
                    this.checkCurVersion();
                }else if(r.indexOf('study-')!=-1){
                    let knowledgeId=r.substring(6);
                    let url=`/xsb/personBusiness/studyCenter/h5GetKnowledge?knowledgeId=${knowledgeId}`;
                    this.$http.get(url).then(res=>{
                      if(res.data.retCode=='0'){
                        this.knowledge=res.data.data;
                        this.$router.push(
                          {
                            path: '/studyCenterDetail',
                            query: {
                              tiaozhuan:this.knowledge.route,
                              knowledgeId:this.knowledge.knowledgeId,
                              title:this.knowledge.title,
                              releaserName:this.knowledge.releaseName,
                              releaseTime:this.knowledge.releaseTime,
                              content:this.knowledge.content,
                              fileUrl:this.knowledge.fileUrl,
                              fileName:this.knowledge.fileName,
                              readNumber:this.knowledge.readNumber,
                              likeNumber:this.knowledge.likeNumber,
                              likeFlag:this.knowledge.likeFlag,
                              typeCode:this.knowledge.typeCode
                            }
                          }
                        )
                    }
                  })
                }else if(r.indexOf('group-Vnet-add')!=-1){
                    let _this=this;
                    let isAuthorParam = {
                       busiType: 'group_v_net_add_pre'
                    }
                    this.$http.post('/xsb/ability/businessLimit/h5QryBusiPermission',isAuthorParam).then((res)=>{
                    if(res.data.retCode == '0'){
                        HandleMode({},function (){
                          _this.$router.push({
                              name:'MemberAdd',
                              query: {
                                  type: '01',//01集团V网 02校园V网
                              }
                          });
                      })
                    } else {
                        this.$router.push({
                          name:'MemberAdd',
                          query: {
                              type: '01'  //01集团V网 02校园V网
                          }
                      });
                    }
                   });
                }else{
                  this.$router.push({
                    path: '/'+this.busiPromoteObject.route,
                    query: {
                    }
                  });
                }
            },
            promoteClickRecord(flag){
                Storage.set(this.localFlag,'1');
                if (flag == '1'){
                    this.promoteDetail();
                }else if(flag=='2'){
                    this.promoteShowFlg=false;
                }
                this.$http.get('/xsb/personBusiness/busiPromote/h5RecordBusiPromote?busiCode='+this.busiPromoteObject.busicode+'&telnum='+this.uinfo.servNumber +'&operation='+flag)
                    .then((res) => {
                        if(res.data.retCode=='0'){

                        }else{
                            this.$alert(res.data.retMsg || "记录推荐活动动作失败");
                        }
                    })
            },
          bbossAuth(){
            let u = this.uinfo;
            let client = /android|harmony/gi.test(navigator.userAgent)?'ANDROID':'IOS';
            let param = {
              "bbossId": "250"+u.crmId,
              "province":"250",
              "region":u.region,
              "clientType":client,
              "clientVersion":"1",//ald版本
              "imei":u.imei,
              "latitude":Storage.get('latitude'),//纬度
              "longitude":Storage.get('longitude'),//经度
              "stationId":u.stationId,//岗位编码
              // "unEncrpt": true
            };
            let url = '/group/usercenter/bbossAuth/h5bbossAuthNew';
            this.$http.post(url,param).then(res => {
              let {retCode,retMsg,data} = res.data;
              if(retCode == '0'){
                Object.assign(param,u);
                param.tokenid = data.token;//集客大厅版本的tokenid
                this.getJiKeHallUrl(param);
                // this.goJikeApp(param);
              } else {
                this.$alert('bboss工号校验失败');
              }
            });
          },
          //跳集客大厅首页
          goJikeApp(param){
              //对param参数进行加密处理，修复漏洞
            let aldParam =  aesEncryptAllParams(param);
            // let aldParam = encodeURIComponent(JSON.stringify(param));
            let jikeUrl = this.jikeHallUrl + '/xxTool/index.html?srcFrom=ald&aldParam=' + aldParam;
            ClientJs.openWebKit(encodeURIComponent(jikeUrl), '', '0', '', '', '', '', '', '', '', '');
          },
          qryZkSwitch() {
            let url = `/xsb/ability/zkSwitchController/h5qryZkSwitchStatus`;
            let param = {
              unLoadFlg: true,
            }
            this.$http.post(url, param).then(res => {
              let {retCode} = res.data;
              if (retCode === "0") {
                this.latestCheckDownload();
              }
            })
          },

          qrySwitchAuth() {
            let url = `/xsb/ability/businessLimit/h5qrySwitchAuth`
            let param = {
              switchName: "ald_update_switch",
              stationId: this.uinfo.stationId
            }
            this.$http.post(url, param, { unLoadFlg: true }).then(res => {
              let { retCode, data } = res.data;
              if (retCode === "0") {
                this.isForceUpdate = data.isForceUpdate === "1";
                this.latestCheckDownload("1");
              }
            })
          },
          isUpdateCallBack(isUpdate) {
            if(isUpdate && getClientType() !== 'harmony') {
              this.isUpdateShow = true;
            }
          },
          afterUpdate() {
            this.isUpdateShow = false;
          },
          // 登录霸屏通知开关
          getHScreenSwitch() {
            let param = {
              busiType: 'hegemony_screen',
              unLoadFlg: true
            };
            this.$http.post('/xsb/ability/businessLimit/h5QryBusiPermission', param).then(res => {
              //有权限
              if (res.data.retCode == '0') {
                //建定时任务
                // this.timing();

                let time = dateFormat(new Date(), 'yyyyMMdd');
                let value = Storage.get('hegemony_screen')
                //表示已完成霸屏
                if(value&&value[time] === 'finish') return
                //超出次数

                if(value&&value[time]&&value.max_login&& value.showNum >= value.max_login) return
                this.getHScreenInfo();
              }
            });
          },
          //查询霸屏信息
          getHScreenInfo() {
            if(this.uinfo&&this.uinfo.tokenid) {
              let url = '/xsb/gridCenter/floodScreen/h5getFloodScreenInfosByPhone';

              let params = {
                qdType: '1',//来源渠道
                schType: '1',//人员登录
                regionId: this.uinfo.region,
                userMobile: this.uinfo.servNumber,
                userName: this.uinfo.operatorName,
                operatorCrm: this.uinfo.crmId,
                operatorId: this.uinfo.staffId,
              };
              let time = dateFormat(new Date(), 'yyyyMMdd');
              this.$http.post(url, params).then(res => {
                let { retCode, data, retMsg } = res.data;
                if (retCode == '0') {
                  //已完成
                  let value = Storage.get('hegemony_screen');
                  if (Array.isArray(data) && data.length > 0) {//需要霸屏
                    this.showType = '1';

                    //排除msgType为6的情况,智能陪练
                    let index = data.findIndex(item=>item.msg.msgType == '6');
                    if(index === -1){
                      this.heScreenflag = true;
                    }

                    // 默认优先推送省统项目
                    this.heScreenDataList = data.sort((a,b)=> Number(b.msg.regionId) - Number(a.msg.regionId)).slice(0,2);

                    //储存今天有通知的信息，目前未完成
                    let StorageObj = { [time]: 'notHave'}

                    // 限制登录次数
                    let mustShowIndex = data.findIndex(item=>item.msg.mustShow === '1');
                    if(mustShowIndex === -1){//都是可关闭弹窗
                      //最大登录次数
                      let max_login = data[0].msg.max_login;
                      Object.assign(StorageObj,{'max_login':max_login })

                       if(value && value[time]){
                         let showNum =  value['showNum'];
                         Object.assign(StorageObj,{"showNum":showNum+1})
                       }else{
                         Object.assign(StorageObj,{"showNum":1})
                       }
                    }
                    Storage.set('hegemony_screen', StorageObj);
                  } else {
                    //今天有霸屏通知，现在没有了，表示已完成
                    if (value && value[time] === 'notHave') {
                      //查询结果已完成
                      Storage.set('hegemony_screen', { [time]: 'finish' });
                    }
                  }
                } else {
                  this.heScreenflag = false;
                }

              }).catch(err => {
                this.heScreenflag = false;
              });
            }
          },
          closeHeScreen() {
            this.heScreenflag = false;
          },
          //定时霸屏,获取人员霸屏时间
          getTimeHScreenSwitch() {
            //获取存储中定时霸屏信息
            let time = dateFormat(new Date(), 'yyyyMMdd');
            let value = Storage.get('hegemony_screen_OnlineHour');
            if(value&&value[time]){
              //今天有定时霸屏
              this.timing(value[time]);
              return
            }

            this.$http.get(`/xsb/gridCenter/floodScreen/h5getOnlineHourByPhone`).then(res => {
              const {retCode, retMsg, data} = res.data;
              if (retCode === '0') {
                Storage.set('hegemony_screen_OnlineHour', { [time]: data })
                  //建定时任务
                this.timing(data);
              } else {
                console.log('查询定时霸屏时间点异常' + retMsg);
              }
            });
          },
          // 定时
          timing(OnlineHour) {
            if(!OnlineHour) return;

            this.OnlineHourList = OnlineHour.split(',').sort((a,b)=>a-b);
            //新建定时任务
            for (let key of this.OnlineHourList) {
              let time = this.getTimeDifference(key);
              if (time >= 0) {
                this[`timer${key}`] = setTimeout(this.getTimeInfo, time, key);
              }
            }
          },
          //获取时间差
          getTimeDifference(time) {
            const now = new Date();
            const targetTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), time, 0, 0);
            return  targetTime.getTime() - now.getTime();
          },
          //定时霸屏信息查询
          getTimeInfo(onlineHour) {
            if(this.uinfo&&this.uinfo.tokenid){
              let url = '/xsb/gridCenter/floodScreen/h5getFloodScreenInfosByPhone';

              let params = {
                qdType: '1',//来源渠道
                schType: '2',//定时在线
                regionId: this.uinfo.region,
                userMobile: this.uinfo.servNumber,
                userName: this.uinfo.operatorName,
                operatorCrm: this.uinfo.crmId,
                operatorId: this.uinfo.staffId,
                onlineHour: onlineHour,
              };

              this.$http.post(url, params).then(res => {
                let { retCode, data, retMsg } = res.data;
                if (retCode == '0') {
                  if (Array.isArray(data) && data.length > 0) {//需要霸屏
                    this.showType = '2';
                    this.onlineHour = onlineHour;
                    this.heScreenflag = true;
                    this.heScreenDataList = data;
                }
                }
              })
            }
          },
          //加水印
          addWaterMark(ip,crmId){
            let strList = [];
            //添加时间
            strList.push(dateFormat(new Date(),"yyyy-MM-dd hh:mm:ss"))
            //添加ip
            if (ip){
              strList.push(ip)
            }
            //添加crmid
            if (crmId){
              strList.push(crmId)
            }

            setWaterMark(strList,'whole-watermark-id');
          },
          // 检查缓存中是否有记录表示用户在 30 天内未点击过客户经理小助手图标且未提示过引导页面
          shouldShowGuidePage() {
            const lastCheckedDate = localStorage.getItem('last_xy_enter_date')
            if (!lastCheckedDate) {
              localStorage.setItem('last_xy_enter_date', new Date());
              return false
            }
            const currentDate = new Date()
            const lastChecked = new Date(lastCheckedDate);
            const thirtyDaysInMilliseconds = 30 * 24 * 60 * 60 * 1000

            return Math.abs(currentDate - lastChecked) > thirtyDaysInMilliseconds
          },
          /*打开数智助手弹窗*/
          openXyDialog(enterFrom){
            this.$refs.fastEnRef.aiDialog(enterFrom);
          }
        },
        mounted() {
          window['getSysInfoRM'] = (res) =>{
            //加水印
            if(/android|harmony/gi.test(navigator.userAgent)){
              this.addWaterMark(res.ip,res.userInfo.crmId);
            }else{
              this.addWaterMark(res.ip['pdp_ip0/ipv4'],res.userInfo.crmId);
            }
          }
          //获取剪贴板内容
          window['getCopyContent'] = (res) => {
            if (res.retCode === '0' && res.copyText && res.copyText.startsWith('JSWGT')) {
              //防止第三方拉起，及没有token情况
              let url = window.location.href;
              if(this.uinfo && this.uinfo.tokenid && url.indexOf('gobackFlag=webview') === -1){
                NLGetCopyShare({ code: res.copyText });
              }
            }
          };
          //用于监听APP从后台切到前台的动作
          window['wakeUpFromApp'] = (res) => {
            if (res.retCode === '0') {
              if (res.wakeUpApp === '1') {//切回前台
                //获取粘贴板内容
                ClientJs.getCopyShare('getCopyContent');
              }
            }
          };

          //直接登录进来，客户端wakeUpFromApp调用的比mounted早，所以手动触发此方法，获取拷贝内容
          ClientJs.getCopyShare('getCopyContent');
        },
        beforeDestroy() {
          if(this.OnlineHourList && Array.isArray(this.OnlineHourList)){
            this.OnlineHourList.forEach(item=>{
              this[`timer${item}`] && clearInterval([`timer${item}`]);
            })
          }
          //清除水印
          clearWatermark('whole-watermark-id');
        }
    }
</script>
<style scoped lang="less">
    .guide-mask{
        top:0;
        left:0;
        bottom:0;
        right:0;
        background:rgba(0,0,0,0.6);
        z-index:99999;
        text-align: center;
        position: fixed;
        height: 100%;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        justify-content: center;
        .guide-img {
          width: 80%;
        }
        .icon-close{
            font-size: 20px;
            color: #ccc;
            display: inline-flex;
            margin-left: 7%;
            margin-top: -20px;
        }
    }
</style>
