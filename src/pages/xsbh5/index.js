// import Vue from 'vue'
import router from '@/router/main/mainRouter.js'
import axios from '@/base/nlAxios.js'
import 'mint-ui/lib/style.css'
import '!style-loader!css-loader!less-loader!@/base/less/style.less'
import Storage from '@/base/storage'
import {CONSTVAL} from '@/base/config'
import {BASE64} from '@/base/coding'
import ClientJs from '@/base/clientjs'
import { Toast,DatetimePicker,Loadmore,Switch,Indicator } from 'mint-ui';
import MessageBox from 'components/common/NlMessageBox/message-box.js';
// import Indicator from 'components/common/NlIndicator/indicator.js'
import Main from '@/pages/xsbh5/index.vue'

let appTemplate = '<Main/>'
import IndexOut from '@/pages/xsbh5/indexOut.vue'
import {chgStrToDate,imageIsExist,getQueryVariable} from '@/base/utils'
//vuex
import store from '../../store'
import LoadingDirective from 'components/common/NlLoading/loading.js'
import Clickoutside from '@/base/clickoutside'

import '../../assets/css/iconfont.css'
// import Vconsole from 'vconsole'
// new Vconsole();
//如果需要防止重复点击 请给相应的元素添加样式needsclick 例 class=" needsclick"
import  '@/base/fastclick.js'
import {PointLessLog,pointMixin} from '@/base/log/pointLessLog.js'
// import {qryBusiPermission} from '@/base/request/commonReq.js'
import * as globalFilters from '@/base/globalFilters'

FastClick.attach(document.body,{tapDelay:4000})
var deviceWidth = document.documentElement.clientWidth || window.screen.availWidth;

document.documentElement.style.fontSize = (deviceWidth / 375)* 100 +'%';//Math.min((deviceWidth / 375),1) * 100 +'%';

window.onresize = function(){
    deviceWidth = document.documentElement.clientWidth || window.screen.availWidth;
    document.documentElement.style.fontSize = (deviceWidth / 375)* 100 +'%';//Math.min((deviceWidth / 375),1) * 100 +'%';
}
const outPage = ['#/resetpassword','#/faceRegister','#/loginForFace','#/aldPrivacy','#/usingSDKS','#/authSingleOn'];//非业务受理等页面，如刷脸注册、忘记密码等
//获取客户端请求入口的参数
function getInitData(){
    let qryMap = getQueryVariable();
    // Storage.session.set('locationHref',window.location.href);
    let reg = new RegExp("(^|&)" + 'userInfo' + "=([^&]*)(&|$)");
    let matchSearch = window.location.search.substr(1) || window.location.hash.split("?")[1];
    let r = matchSearch && matchSearch.match(reg);
    let uinfo = {};
    let entryHash = window.location.hash;
    entryHash = ~entryHash.indexOf('?')?entryHash.substring(0,entryHash.indexOf('?')):entryHash;
    console.info('entryHash=='+entryHash+',outPage.includes(entryHash):'+outPage.includes(entryHash));
    if(outPage.includes(entryHash)){// 刷脸注册、忘记密码等不需要处理的页面

        appTemplate = '<IndexOut/>'
        // console.info(appTemplate+'===appTemplate')
    }
    if(r != null){
        uinfo = BASE64.decode(r[2]);
        uinfo = JSON.parse(uinfo);
    } else {
        uinfo = {};
        if(process.env.NODE_ENV === 'development'){
            uinfo = {"servNumber": "13901582505",
            "operatorName": "无名2",
            "region": "14",
            "regionName": "南t",
            "staffId": "9988021754066058",
            "stationId": "9988020036385338",
            "stationName": "客户经理",
            "crmId": "14150759",            //21115457
            "oa":"20394123",//OA账号
            "imei":"123",
            "roleInfo":[],
            "imei":"-1",
              "orgId":"1488018465153416",
            "panChannelInfo":{"panchannelid":"1"},
            "tokenid":"QFOxpuDlWRM=",       //1 V8x7/bXcqAw= 2 QFOxpuDlWRM=
            "viewInfoList":['4','1','2','3','5','6'],//视图 1：集客视图 2：小区视图 3：渠道视图 4：个人视图
            "stations": [
              {
                "stationId": "10011406",
                "stationName": "工作台-客户经理"
              },
              {
                "stationId": "10011806",
                "stationName": "商客经理"
              },
              {
                "stationId": "10012007",
                "stationName": "商客管理员"
              },
              {
                "stationId": "10009001",
                "stationName": "营业员户外营销"
              },
              {
                    "stationId": "1811489",
                    "stationName": "地市渠道管理员"
              },
              {
                    "stationId": "1488018492933394",
                    "stationName": "社区直销人员(新)"
              },
              {
                    "stationId": "9988020036385338",
                    "stationName": "客户经理"
              },
              {
                    "stationId": "9988021754066058",
                    "stationName": "社区直销人员"
              },
              {
                "stationId": "10013601",
                "stationName": "副网格长"
              },
              {
                "stationId": "10013401",
                "stationName": "楼园代理"
              },
            ]};
        }
        if(qryMap['tokenid']){
            uinfo = {
                "region": qryMap['region'],
                "servNumber": qryMap['servNumber'],
                "staffId": qryMap['servNumber'],
                "crmId": qryMap['crmId'],
                "tokenid": qryMap['tokenid']
            };
        }
    }

    Storage.session.set('offsiteLoginFlg',!!uinfo.offsiteLoginFlg);//异地登录标识
    window.isJksx = uinfo.comeFrom == 'jksx';//是否为集客随销
    window.isZwsx = uinfo.comeFrom == 'ams';//是否为装维随销
    if(Storage.session.get('userInfoNew')){
        Storage.session.set('userInfo',Storage.session.get('userInfoNew'));
    } else {
        Storage.session.set('userInfo',uinfo);
    }
    Storage.session.set('tokenId',uinfo.tokenid);
  if(uinfo.tokenid && qryMap['gobackFlag']!='webview'){//防止瑞月等相关页面拉起无token的情况
    axios.defaults.headers.tokenid = uinfo.tokenid;
    axios.defaults.headers.operaterPhone = uinfo.servNumber;//add by qhuang 2021-11-24登录token改造
    axios.defaults.headers.imei = uinfo.imei;//add by qhuang 2021-11-24登录token改造
    axios.defaults.headers.aldregion = uinfo.region+'_'+uinfo.servNumber;//新增aldregion
    axios.defaults.headers.validateType = 'strict';//严格校验token
    // getInstance();//获取日志采集实例
  }
}


/**
 * 页面初始化获取客户端提供的路由信息
 */
window.getSysInfoCB = function(obj){
    let serverUrl = obj.serverUrl;
    if(process.env.NODE_ENV === 'development'){
        //开发联调环境需要配置跨域标识
        // serverUrl = '/apiM' + serverUrl;
    }
    //从客户端获取 走真数据的时候 放开注释
    axios.defaults.baseURL = serverUrl;
    try {
        Storage.set('webUrl',serverUrl);
        if(!obj.location && Storage.get('location')){//客户端返回的位置信息为空
            //上一次的位置信息不为空，则不更新
        } else {
            Storage.set('appVersion',obj.verison);
            Storage.set('location',obj.location || 'empty');
            Storage.set('latitude',obj.latitude || 'empty');
            Storage.set('longitude',obj.longitude || 'empty');
        }
    } catch (error) {
    }
}
//从第三方页面回阿拉盯相关页面初始化axios中的信息
window.initTokenAfterBack = function(_axios, uinfo){
    _axios.defaults.headers.tokenid = uinfo.tokenid;
    _axios.defaults.headers.operaterPhone = uinfo.servNumber;//add by qhuang 2021-11-24登录token改造
    _axios.defaults.headers.imei = uinfo.imei;//add by qhuang 2021-11-24登录token改造
    _axios.defaults.headers.aldregion = uinfo.region+'_'+uinfo.servNumber;//新增aldregion
    Storage.session.set('tokenId',uinfo.tokenid);
    let oldUinfo = Storage.session.get('userInfo');
    let relTelnum = oldUinfo && oldUinfo.relTelnum;//学生手机号
    if(relTelnum){
        uinfo.relTelnum = relTelnum;
    }
    Storage.session.set('userInfo',uinfo);
}
ClientJs.getSysInfo('getSysInfoCB');

//提供给客户端切换路由调用的方法
window.setSysRouteInfo = function(serverUrl){
    axios.defaults.baseURL = serverUrl;
    Storage.set('webUrl', serverUrl);
}

// 菜单链
window.breadIds = [];
window.updateBreadId = (level, privId, resetLowerLevels = true) => {
  // 判断是否有父级菜单
  if(level - 1  > window.breadIds.length) {
    console.log('未添加父级菜单，无法添加此级别菜单')
    return;
  }
  // 重置子菜单
  if (resetLowerLevels) {
    window.breadIds.length = level;
  }
  window.breadIds[level - 1] = privId
}

getInitData();
const install = function(Vue, config = {}) {
    if (install.installed) return;
    Vue.config.productionTip = false;
    Vue.config.devtools = true;
    //日期控件
    Vue.component(DatetimePicker.name, DatetimePicker);
    Vue.component(Loadmore.name, Loadmore);
    Vue.component('Main',Main);
    Vue.component(IndexOut.name,IndexOut);
    // 调用 提示框
    Vue.prototype.$messagebox = MessageBox;

    //alert对话框
    Vue.prototype.$alert = (msg,title) =>{
        if( title === void 0 || title === '' ){
            title = '温馨提示';
        }
        if(msg && msg.length > 300){
            msg = msg.substr(0,300)
        }
        Vue.prototype.$messagebox.alert(msg,title);
    }
    Vue.mixin(pointMixin);
    Vue.$toast = Vue.prototype.$toast = Toast;
    Vue.$switch = Vue.prototype.$switch = Switch;
    //异步查询
    Vue.prototype.$http = axios;
    //局部加载
    Vue.directive('loading',LoadingDirective);
    Vue.directive('clickoutside',Clickoutside);
    Vue.prototype.CONSTVAL = CONSTVAL;

    //全局注册自定义指令，用于判断当前图片是否能够加载成功，可以加载成功则赋值为img的src属性，否则使用默认图片
    Vue.directive('real-img', async function (el, binding) {//指令名称为：real-img
        let imgURL = binding.value;//获取图片地址
        if (imgURL) {
            let exist = await imageIsExist(imgURL);
            if (exist) {
                el.setAttribute('src', imgURL);
            }
        }
    })

    if(Storage.session.get('tokenId')){//确保有token的情况下实例化
        console.info('===初始化无埋点调用链对象===')
        //无埋点调用链
        const pointLessLog = new PointLessLog(Vue,axios)
        Vue.$pointLesslog = Vue.prototype.$pointLesslog = pointLessLog
    }

    /**
     * 记录日志接口
     * eventName 事件名称或者方法名称、如果是点击类事件 传event对象
     * businessCode 功能编码（一般和服务端保持一致）
     * gatherBody 采集内容
     * gatherType 采集类型 1:页面加载 2:页面操作、3：页面js报错 4：系统奔溃、5：客户端操作 默认2页面操作
     * gatherStep 采集步骤 00 到 99（不含00 99）
     */
    Vue.prototype.$log = (eventName,businessCode,gatherBody,gatherType) =>{
    }

    Object.keys(globalFilters.default).forEach((item)=>{
      Vue.filter(item,globalFilters.default[item])
    })
}
// auto install
if (typeof window !== 'undefined' && window.Vue) {
  install(window.Vue);
};



//路由前置钩子
router.beforeEach((to, from, next) => {
    //判断是不是在客户端内
    if(process.env.NODE_ENV === 'production' && to.path != '/page404'){
        if (!((window.WebViewFunc&&window.WebViewFunc.videoRecord)
        ||(window.webkit&&window.webkit.messageHandlers&&window.webkit.messageHandlers.videoRecord))){
            next({
                path: '/page404'
            })
            return;
        }
    }

    // let rname = to.name;
    // pageName = rname;
    // logStep = 0;
    // if(LOG_CALL_CONFIG && LOG_CALL_CONFIG[rname]){
    //     // console.info(window['SAMRTLOG']);
    //     //需要记录日志
    //     // smart['__loaded'] && smart.track_pv(rname,{gatherType:1,gatherBody:'',gatherStep:'00'});
    //     window['SAMRTLOG'] && window['SAMRTLOG'].initLog(LOG_CALL_CONFIG[rname],from.name,rname);
    // }

    let gobackFlag = to.query.gobackFlag == 'webview'?true:false;
    if(to.path == '/resetpassword' || to.path == '/faceRegister' || to.path =='/loginForFace' || gobackFlag || to.path == '/aldPrivacy' || to.path == '/usingSDKS' || to.path=='/authSingleOn'){//刷脸注册、忘记密码
        appTemplate = '<IndexOut/>'
        next();
    } else if(to.path != '/page404' && !Storage.session.get('tokenId')){
         //这里判断用户是否登录，验证本地存储是否有token
         next({
            path: '/page404'
        })
    } else {
        let uinfo = Storage.session.get('userInfo');
        const stationSwitch = Storage.session.get('stationSwitch')
        const offsiteLoginFlg = Storage.session.get('offsiteLoginFlg');//异地登录标识
        if(to.name === 'WorkStage'){
          //异地登录
          if(offsiteLoginFlg){
            if(stationSwitch == 1){
              next({ path: '/toolsNew' })
            }else{
              next({ path: '/tools' })
            }
          }else{
            if(uinfo && (uinfo.stationId==CONSTVAL.STATION_KHJL)){
              next({
                path: '/workStageNew'
              })
            } else if (uinfo && (uinfo.stationId == CONSTVAL.STATION_GZT_KHJL)) {
              next({
                path: '/zqWorkStage'
              })
            } else if (uinfo && (uinfo.stationId == CONSTVAL.STATION_SKZQ_KHJL || uinfo.stationId == CONSTVAL.STATION_SKZQ_GLY )) {
              next({
                path: '/skzqHome'
              })
            } else if (uinfo && (uinfo.stationId == CONSTVAL.STATION_JIAKEZW)) {
              next({
                path: '/outH5MidPage'
              })
            }
          }
            ClientJs.getSysInfo('getSysInfoCB');
        } else if (to.name === 'Tools' || to.path === '/tools') {
            if(stationSwitch == 1){
                next({
                  path: '/toolsNew'
                })
            }
            ClientJs.getSysInfo('getSysInfoCB');
        } else if (to.name === 'Business' || to.path === '/business') {
           //异地登录
           if(offsiteLoginFlg){
             if(stationSwitch == 1){
               next({ path: '/toolsNew'})
             }else{
               next({ path: '/tools'})
             }
           }else{
             if(stationSwitch == 1){
               next({
                 path: '/businessNew'
               })
             }
           }
            ClientJs.getSysInfo('getSysInfoCB');
        }
        next();
    }
    if(window.isJksx){
      if(to.name === 'My'){
        next({
          path: '/myJkZwsx'
        })
      }else{
        next();
      }
    }


});

//路由后置钩子
router.afterEach((to, from) => {
    // let rname = from.name;
    // if(LOG_CONFIG[rname]){
    //     //需要记录上个页面结束的日志
    //     // smart['__loaded'] && smart.track_pv(rname,{gatherType:1,gatherBody:'',gatherStep:'99',eventType:'exitPage'});
    // }
})

let aldVue = new Vue({
    el: '#app',
    router,
    store,
    // components: { Main },
    template: appTemplate
})
export default aldVue;
