<!-- 融合订购页面 -->
<template>
  <div class="wrapper">
    <Header :tsTitleTxt="headTitle"  backType="custom" @emGoPrev="goPrev"></Header>

    <!-- 宽带产品选择 -->
    <div class="flex-box" v-show="currentStep == 'step1' && canSubmitFlag">
      <!-- 宽带基础产品选择 -->
      <div class="productBox-main">
          <h4 class="productBox-title">宽带资费</h4>
          <ul class="productList" v-show="mbandBaseProdList && mbandBaseProdList.length > 0">
              <li class="op-content" v-for="(item,idx) in mbandBaseProdList" @click="checkMbandProd(item)" :key="idx">
                  <a class="op-content-checkbox" :class="{'selected':selectMbandProd.prodId == item.prodId}">
                      <span class="iconfont op-icon-left" :class="[selectMbandProd.prodId == item.prodId?'checkboxround1':'checkboxround0']"></span>
                  </a>
                  <span class="op-content--title">{{item.prodName}}</span>
                  <a class="op-content--icon" @click.stop="showTipMsg(item)">
                    <span class="iconfont gantanhao-yuankuang op-icon"></span>
                  </a>
              </li>
          </ul>
      </div>

      <!-- 宽带附加产品选择 -->
      <div class="productBox">
          <h4 class="productBox-title">宽带产品选择</h4>
          <ul class="secondaryUl" v-show="mbandExtraProdList && mbandExtraProdList.length> 0">
              <li class="secondaryUl-li " v-for="(item,idx) in mbandExtraProdList" :key="idx"
                  @click="checkMbandExtra(item)">
                  <a class="secondaryUl-li-checkbox" :class="{'selected':~checkMbandInList(item)}">
                      <span class="iconfont op-icon-left"
                            :class="[~checkMbandInList(item)?'checkboxround1':'checkboxround0']"></span></a>
                  <span class="secondaryUl-li--title">{{item.prodName}}</span>
                  <span class="secondaryUl-li-effect"
                        v-if="item.effectType == 0 && ~checkMbandInList(item)">立即生效</span>
                  <span class="secondaryUl-li-effect"
                        v-if="item.effectType == 1 && ~checkMbandInList(item)">次日生效</span>
                  <span class="secondaryUl-li-effect"
                        v-if="item.effectType == 2 && ~checkMbandInList(item)">次月生效</span>
                  <a class="secondaryUl-li--icon" @click.stop="showTipMsg(item)"><span
                          class="iconfont gantanhao-yuankuang op-icon"></span></a>
              </li>
          </ul>
      </div>

      <div class="op-button-box">
          <NlButton enableTip="下一步"  @click="doChooseTV"></NlButton>
      </div>
    </div>

    <!--提示信息-->
    <div class="msgbox-bg" v-show="tipsFlag" @click="hideTipMsg">
        <div class="msgbox">
            <div class="msgbox-main">{{tipsDetailData}}</div>
            <span class="msgbox-footer" @click.stop="hideTipMsg">关闭</span>
        </div>
    </div>

    <!-- 电视产品选择 -->
    <div class="flex-box" v-show="currentStep == 'step2' && canSubmitFlag">
      <!-- 电视基础产品选择 -->
      <ul class="typeList">
          <li class="type-content" @click="tvTypeSelect">
              <a class="type-content-icon"><span class="iconfont dianshi type-icon-left"></span></a>
              <span class="type-content--title">{{ selectTvType.label }}</span>
              <a class="type-content--jt"><span class="iconfont youjiantou icon-yjt"></span></a>
          </li>
          <li class="type-content" @click="getTVModeTypeSelect" v-show="getTVModeFlag">
              <a class="type-content-icon"><span class="iconfont shezhi type-icon-left"></span></a>
              <span class="type-content--title">{{ selectTVGetModeType.label }}</span>
              <a class="type-content--jt"><span class="iconfont youjiantou icon-yjt"></span></a>
          </li>
      </ul>

      <!-- 电视基础附加产品选择 -->
      <div class="tvProductBox">
          <h4 class="tvProductBox-title">电视产品选择</h4>
          <ul class="tvProductList" v-show="tvProdList && tvProdList.length > 0">
              <li class="prod-content" v-for="(item,idx) in tvProdList" @click="checkTVProd(item)" :key="idx">
                  <a class="tvop-content-checkbox" :class="{'selected':selectTVProd.prodId == item.prodId}">
                      <span class="iconfont op-icon-left" :class="[selectTVProd.prodId == item.prodId?'checkboxround1':'checkboxround0']"></span>
                  </a>
                  <span class="tvop-content--title">{{item.prodName}}</span>
                  <a class="tvop-content--icon" @click.stop="showTipMsg(item)">
                    <span class="iconfont gantanhao-yuankuang op-icon"></span>
                  </a>
              </li>
          </ul>
      </div>

      <div class="tvop-button-box">
        <NlButton enableTip="下一步" :disabled="canOpenTv=='false'" @click="doJudgeType"></NlButton>
      </div>
    </div>

    <!-- 安装信息详情产品选择 -->
    <div class="cal-box" v-show="currentStep == 'step3' && canSubmitFlag">

        <!-- 已选信息 -->
        <section class="contentBox installBox">
            <!-- 小区安装地址 -->
            <div class="fixedtext" v-show="this.prodType == '2'">
                <span>{{villageAddress}}</span>
            </div>

            <ul class="tvProductInfo" :class="{'mBandDetail':prodType == '1'}">
                <li class="mainInfo" v-show="this.prodType == '1'">
                    <span class="tvProductInfo-icon iconfont kuandai"></span>
                    <span class="tvProductInfo-title">{{ selectMbandProd.prodName }}</span>
                    <span class="tvProductInfo-label">宽带资费</span>
                </li>
                <li class="mainInfo">
                    <span class="tvProductInfo-icon iconfont dianshi"></span>
                    <span class="tvProductInfo-title">{{ selectTvType.label }}</span>
                    <span class="tvProductInfo-label">电视品牌</span>
                </li>
                <li class="mainInfo">
                    <span class="tvProductInfo-icon iconfont fujian"></span>
                    <span class="tvProductInfo-title">语音遥控器</span>
                    <span class="tvProductInfo-label">附加套餐</span>
                </li>
            </ul>
            <div class="tvInstallInfo">
                <h4 class="tvInstallInfo-title"><img src="static/img/installTiltle.jpg" class="tvInstallInfo-title-img"></h4>
                <ul class="tvInstallUl" v-show="prodType == '1'">
                    <li>
                        <span class="tvInstallUl-title">安装地址</span>
                        <a class="tvInstallUl-choose" @click="openAddr">
                            <span class="locationText" :class="{'villageColor':fullName=='点击选择'}">{{fullName}}</span>
                            <span class="iconfont jiantou-copy-copy moreIcon"></span>
                        </a>
                    </li>
                    <li>
                        <span class="tvInstallUl-title">姓名</span>
                        <input class="tvInstallUl-input" v-model="dimLinkName" placeholder="请输入" type="text">
                    </li>
                    <li>
                        <span class="tvInstallUl-title">联系方式</span>
                        <input class="tvInstallUl-input" v-model="dimLinkPhone" type="tel" maxlength="11" placeholder="请输入">
                    </li>
                    <li>
                      <span class="tvInstallUl-title">密码</span>
                      <a class="tvInstallUl-choose">
                        <input v-show="!pwdShowFlg" v-model="pwd" type="password" class="tvInstallUl-choose-input" maxlength="6" placeholder="请输入">
                        <input v-show="pwdShowFlg" v-model="pwd" type="text" class="tvInstallUl-choose-input" maxlength="6" placeholder="请输入">
                        <span class="iconfont" :class="[pwdShowFlg?'kejian':'bukejian']" @click="pwdShowFlg=!pwdShowFlg"></span>
                      </a>
                    </li>
                    <li>
                        <span class="tvInstallUl-title">预约安装时间</span>
                        <a class="tvInstallUl-choose" @click="openDatePicker('yuyueDate')">
                            <span class="locationText">{{installDate}}</span>
                            <span class="iconfont jiantou-copy-copy moreIcon"></span>
                        </a>
                    </li>
                    <li>
                        <span class="tvInstallUl-title">备注</span>
                        <textarea class="installUl-textarea" v-model="remark" rows="4" placeholder="请输入"></textarea>
                    </li>
                </ul>
                <ul class="tvInstallUl" v-show="prodType == '2'">
                    <li>
                        <span class="tvInstallUl-title">姓名</span>
                        <input class="tvInstallUl-input" v-model="dimLinkName" placeholder="请输入" type="text">
                    </li>
                    <li>
                        <span class="tvInstallUl-title">联系方式</span>
                        <input class="tvInstallUl-input" v-model="dimLinkPhone" type="tel" maxlength="11" placeholder="请输入">
                    </li>
                    <li>
                        <span class="tvInstallUl-title">预约安装时间</span>
                        <a class="tvInstallUl-choose" @click="openDatePicker('yuyueDate')">
                            <span class="locationText">{{installDate}}</span>
                            <span class="iconfont jiantou-copy-copy moreIcon"></span>
                        </a>
                    </li>
                    <li>
                        <span class="tvInstallUl-title">备注</span>
                        <textarea class="installUl-textarea" v-model="remark" rows="4" placeholder="请输入"></textarea>
                    </li>
                </ul>
                <a class="agreement" v-show="prodType == '1'">服务协议</a>
            </div>
            <div class="tvInstallInfo-box">
                <NlButton enableTip="下一步" @click="doMbandInfoCheck"></NlButton>
            </div>
            <mt-datetime-picker
                    type="datetime"
                    ref="yuyueDate"
                    v-model="dateval"
                    :startDate="minDate"
                    @confirm="formatDateVal">
            </mt-datetime-picker>
        </section>
    </div>

    <!-- 算费信息详情页面 -->
    <div class="flex-box" v-show="currentStep == 'step4' && canSubmitFlag">

        <section class="contentBox">
            <div class="box-title" @click="topInfoFlg=!topInfoFlg">
                <span class="iconfont yingxiao2"></span>
                <h1 class="title">已选活动</h1>
                <span class="iconfont arrow" :class="[topInfoFlg?'jiantouxiangshang':'jiantou2']"></span>
            </div>

            <div class="alreadyfixed-info" v-show="topInfoFlg">

            <!-- 宽带产品 -->
            <div class="marketInfoBox" v-show="prodType == '1'">
                <div class="value">{{selectMbandProd.prodName}}【{{selectMbandProd.prodId}}】</div>
                <div class="zi">宽带资费</div>
            </div>
            <ul class="productul" v-show="prodType == '1'">
                <li v-for="(citem,cindex) in selectMbandExtraList" :key="cindex">
                <div class="marketInfoBox">
                    <div class="value">{{citem.prodName}}【{{citem.prodId}}】</div>
                    <div class="zi">附加产品</div>
                </div>
                </li>
            </ul>
            <div class="with-border-line" v-show="prodType == '1'"></div>

            <!-- 电视产品 -->
            <div class="marketInfoBox">
                <div class="value">{{ selectTvType.label }}【{{selectTvType.id}}】</div>
                <div class="zi">电视品牌</div>
            </div>
            <div class="marketInfoBox">
                <div class="value">{{selectTVProd.prodName}}【{{selectTVProd.prodId}}】</div>
                <div class="zi">附加产品</div>
            </div>

            <div class="with-border-line"></div>

            <div class="marketInfoBox">
                <div class="value">语音遥控套餐</div>
                <div class="zi">附加套餐</div>
            </div>

            </div>

            <div class="totalMoney">
                <div class="moneyText"><span class="iconfont shituxiangqing"></span>费用明细</div>
                <span class="money">{{totalFee | chgYuan}}元</span>
            </div>
            <ul class="bandwidthInfo">
                <li v-for="(mitem,mid) in chargeList" :key="mid">
                    <span>{{mitem.chargename}}</span>
                    <span>{{mitem.priceamount | chgYuan}}元</span>
                </li>
            </ul>

        </section>

        <div class="tvop-button-box">
            <NlButton enableTip="提  交"  count="30" defaultState="counting" @click="doSumbitCheck"></NlButton>
        </div>
    </div>


  </div>
</template>

<script>
import Header from "components/common/Header.vue";
import Storage from '@/base/storage';
import NlDropdown from "components/common/NlDropdown/dropdown.js";
import NlButton from 'components/common/NlButton';
import VillAdress from 'components/common/VillageAddress/VillageAddress';
import {dateFormat,chgStrToDate} from '@/base/utils';
import ClientJs from '@/base/clientjs';
import {Indicator} from 'mint-ui';

export default {
  components:{Header, NlButton},
  props:{},
  data(){
    return {
        headTitle: '融合订购',//标题
        currentStep: 'step1',//当前页面
        prodType: '1',  //1未开宽带 2未开电视 3已开电视
        canSubmitFlag: false, //可以提交
        selectMbandProd: {prodId:'-99',prodName:'请选择'},//选择的宽带基础产品
        selectMbandExtraList: [],//选择的宽带附加产品
        mbandBaseProdList: [],//宽带基础产品
        mbandExtraProdList: [],//宽带附加产品
        mbandMustProdList: [],//宽带必选产品
        tipsFlag: false, //产品详情弹出框
        tipsDetailData: '',  //产品详情提示内容
        effectMbandList: [
          {id: '0', label: '立即生效'},
          {id: '1', label: '次日生效'},
          {id: '2', label: '次月生效'}],  //宽带产品生效方式
        tvTypeList: [],//电视类型
        selectTvType: {},//选择的电视类型
        getTVModeFlag: true,//是否展示发放类型
        netWorkType: '', //网络接入类型
        villageId: '', //小区id
        villageName: '', //小区名称
        villageAddress: '', //小区地址
        addressId: '', //地址id
        otherAgent: '', //资源提供方 1 移动 2 广电 3 铁通 4 其它运营商 5 复合运营商
        constMgr: '', // 施工方 1 移动 2 广电 3 铁通 4 其它运营商 5 复合运营商
        radiusFor: '', //Radius归属 1 移动 2 广电 3 铁通 4 其它运营商 5 复合运营商
        userType: '', //宽带用户类型
        planCategory: '', //宽带区域类型  1  市区城区 2  市辖乡镇 3  市辖村 4  县城城区 5  县辖乡镇 6  县辖村 7  	其他
        deviceSmallList: [],    //小类类型
        deviceClass: "",    //设备大类
        canOpenTv: "false", //电视是否可以开
        countryId: "",  //县市id
        mustTVProdIds: '',  //电视必选产品id
        mustTVProdIdList:[], //电视必选产品id列表
        tvProdList: [], //电视产品选择
        getTVModeTypeList: [
          {id: '1', label: '营业厅发放'},
          {id: '2', label: '代维发放'},
          {id: '3', label: '用户自备'}], //选择的发放模式列表
        selectTVGetModeType: {id: '2', label: '代维发放'}, //选择的发放模式
        selectTVProd: {prodId:'-99',prodName:'请选择'},//选择电视的基础产品
        busiType: this.CONSTVAL.BUSI_TYPE_ORDER_INTEGRATED, //业务类型
        fullName: '点击选择',//小区全名
        deviceName: '',//设备信息
        pwdShowFlg: false,//是否展示密码明文
        telnum: '', //电话
        pwd: '', //宽带密码
        linkName: '', //联系人姓名
        linkPhone: '', //联系电话
        dateval: '',
        installDate: '', //安装时间
        minDate:new Date(),//时间控件的最小开始时间
        remark: '',//备注
        topInfoFlg: true,//头部信息是否展示
        chargeList: [],//宽带调度费
        totalFee: '',//总费用
        payType:'0',//支付方式 1.在线支付 2.话费支付 0.现金支付  默认选中现金支付
        payTypeDict:'1', //支付方式字典 1.在线支付 2.话费支付 3.先装后付
        payMethodDict:'1000', //支付状态字典  在线支付8520  话费支付9000  先装后付8201
        userId: "", //用户的subsid
        subsId:"", //subid
        mainOfferId:"", //主体产品编码
        custContactAddr:"", //用户身份证地址
        custName: "", //用户身份证用户名
        bandSpeed: "", //带宽

    }
  },
  methods:{
    //返回上一页
    goPrev(){
      if(this.currentStep == 'step1'){
          this.$router.back();
      } else if(this.currentStep == 'step2'){
          if(this.prodType == '1'){
            this.currentStep = 'step1';
            this.effType = '';
          }else{
            this.$router.back();
          }
      }else if(this.currentStep == 'step3'){
          this.currentStep = 'step2';
      }else if(this.currentStep == 'step4'){
          this.currentStep = 'step3';
          this.chargeList = [];
          this.totalFee = '';
      }
    },
    //下一步
    nextStep(){
      if(this.currentStep == 'step1'){
        if(this.selectTVProd.prodId == '-99'){
            this.initTvOpenInfo();
        }
        this.currentStep = 'step2';
      }else if(this.currentStep == 'step2'){
        this.currentStep = 'step3';
      }else if(this.currentStep == 'step3'){
        this.currentStep = 'step4';
      }
    },
    //判断电视宽带开通情况
    checkBandTv(){
        Indicator.open('加载中');
        let url = `/xsb/personBusiness/personInfo/h5QryAlreadyOpened?privid=100012&telnum=${this.telnum}`;
        this.$http.get(url).then(res =>{
            let {retCode,retMsg,data} = res.data;
            if(retCode == '0'){
                let isMband = data.isMband; // 是否开通宽带 0：否，1：是
                let isNetTv = data.isNetTv; // 是否开通互联网电视 0：否，1：是
                if("0" == isMband){
                    //未开通宽带
                    this.prodType = '1';
                    this.currentStep = 'step1';
                    this.canSubmitFlag = true;
                    this.getMbandProdList();
                }else{
                  if("0" == isNetTv){
                    //未开通电视
                    this.prodType = '2';
                    this.canSubmitFlag = true;
                    this.nextStep();
                  }else{
                    this.$alert("已开通宽带、电视")
                  }
                }
            } else {
                this.$alert(retMsg || '查询开通宽带电视信息异常');
            }
            Indicator.close();
        }).catch(res =>{
            this.$alert('查询开通宽带电视信息网络异常:'+ res);
            Indicator.close();
        });
    },
    //查询宽带信息和附加产品
    getMbandProdList() {
        this.selectMbandProd = {prodId:'-99',prodName:'请选择'};
        this.selectMbandExtraList = [];
        this.mbandBaseProdList = [];
        this.mbandExtraProdList = [];
        this.mbandExtraProdList = [];
        this.mbandMustProdList = [];
        let url = `/xsb/personBusiness/businessOpen/h5QryMBandAndExtraProd?telnum=${this.telnum}&pkgprodId=2013000001`;
        this.$http.get(url).then(res => {
            if (res.data.retCode == '0') {
                let data = res.data.data;
                let objs = {
                    effectType: "2",
                    isPackage: "0",
                    pkgprodId: "2013000001",
                    prodId: "2000001683",
                    prodName: "宽带标准资费"
                }
                this.mbandBaseProdList = data.mbandProdList;//基础产品
                this.mbandBaseProdList.splice(0, 0, objs);
                this.mbandExtraProdList = data.extraProdList;//附加产品
                this.mbandMustProdList = data.mustProdList;//必选产品
            } else {
                this.$alert(res.data.retMsg || '查询宽带信息和附加产品失败')
            }
        })
    },
    //选择宽带基础产品
    checkMbandProd(item) {
      this.selectMbandProd = item;
    },
    //展示宽带产品提示信息
    showTipMsg(data) {
        this.tipsDetailData = '';
        let prodIdList = [{packageCode: data.prodId}];
        let url = '/xsb/personBusiness/businessOpenTv/h5QryProdDesc?prodIdList=' + JSON.stringify(prodIdList);
        this.$http.get(url).then(res => {
            if (res.data.retCode == '0') {
                this.tipsFlag = true;
                this.tipsDetailData = res.data.data.prodDesc;
            } else {
                this.$alert(res.data.retMsg || '暂无描述');
            }
        })
    },
    //关闭宽带产品提示
    hideTipMsg() {
        this.tipsFlag = false;
    },
    //点击宽带附加列表产品
    checkMbandExtra(item) {
        let idx = this.checkMbandInList(item);
        if (idx != -1) {
            this.selectMbandExtraList.splice(idx, 1);
        } else {
            if (item.prodId == '2011002057' || item.prodId == '2000002748') {//当月免费,竣工套餐只有立即生效
                this.$set(item, 'effectType', 0);//0 立即生效 1次日生效 2次月生效
                this.selectMbandExtraList.push(item);
            } else {
                //需要弹出生效方式选择框
                let self = this;
                NlDropdown({
                    confirmBtn: false,
                    datalist: self.effectMbandList
                }, function (retVal) {
                    self.$set(item, 'effectType', retVal.id);
                    self.selectMbandExtraList.push(item);
                });
            }
        }
    },
    //查看是否在选中列表中
    checkMbandInList(item) {
        for (let i = 0; i < this.selectMbandExtraList.length; i++) {
            if (this.selectMbandExtraList[i].prodId == item.prodId) {
                return i;
            }
        }
        return -1;
    },
    //校验宽带产品选择
    doChooseTV(){
        if(this.selectMbandProd.prodId == '-99') {
            this.$alert('请选择宽带基础产品');
        }else{
            this.nextStep();
        }
    },
    //查询开通电视信息
    initTvOpenInfo() {
        Indicator.open('加载中');
        let self = this;
        let url = `/xsb/personBusiness/businessOpenTv/h5QryTvOpenInfo?telnum=${self.telnum}&tvSort=${self.tvSort}`;
        self.$http.get(url).then(async function (res){
            if (res.data.retCode == '0') {
                let data = res.data.data;
                let tvTypeList = data.tvTypeList;//电视包类型
                for (let i = 0; i < tvTypeList.length; i++) {
                    let item = {'id': tvTypeList[i].pkgProdId, 'label': tvTypeList[i].pkgProdName};
                    self.tvTypeList.push(item);
                }
                //根据产品包id获取产品列表
                if (self.tvTypeList.length > 0) {
                    let pkgId = self.tvTypeList[0].id;
                    self.selectTvType = self.tvTypeList[0];
                    self.getTVProdList(pkgId);
                }
                if (self.selectTvType.id == '2013000019' || self.selectTvType.id == '2013000020' || self.selectTvType.id == '2013000021') {
                    self.getTVModeFlag = false;
                } else {
                    self.getTVModeFlag = true;
                }
                let mbandInfoDetail = data.mbandInfoDetail; //用户宽带详情
                self.villageId = mbandInfoDetail.villageId;
                self.villageName = mbandInfoDetail.villageName;
                self.villageAddress = mbandInfoDetail.villageAddress;
                self.addressId = mbandInfoDetail.addressId;
                self.fullName = mbandInfoDetail.villageAddress;
                self.otherAgent = mbandInfoDetail.otherAgent;
                self.constMgr = mbandInfoDetail.constMgr;
                self.radiusFor = mbandInfoDetail.radiusFor;
                self.userType = mbandInfoDetail.userType;
                self.deviceSmallList = data.deviceSmallList;  //小类
                self.deviceClass = data.deviceClass;  //设备大类
                self.canOpenTv = data.canOpenTv;  //电视是否可以开
                self.countryId = data.countryId;    //县市id
                self.netWorkType = mbandInfoDetail.netWorkType; //网络接入类型
                if (data.canOpenTv == "false") {
                    self.$alert(res.data.retMsg || '暂无开通此类型互联网电视的权限');
                }
            }
            Indicator.close();
        })
    },
    //查询电视产品
    getTVProdList(pkgId) {
        let self = this;
        self.tvProdList = [];
        self.selectTVProd = {prodId:'-99',prodName:'请选择'};
        let url = `/xsb/personBusiness/businessOpenTv/h5QryTvProd?pkgProdId=${pkgId}&telnum=${this.telnum}`;
        this.$http.get(url).then(res => {
            if (res.data.retCode == '0') {
                let data = res.data.data;
                this.mustTVProdIds = data.mustProdIds;
                if(this.mustTVProdIds){
                    let prodList =  this.mustTVProdIds.substring(0, this.mustTVProdIds.length - 1);
                    this.mustTVProdIdList =  prodList.split("|");
                }
                if (self.tvSort == '2') {
                    for (let i = 0; i < data.prodList.length; i++) {
                        if(pkgId=="2013000011"||pkgId=="2013000012"){
                            if (data.prodList[i].prodId == '2000009232' || data.prodList[i].prodId == '2000009233') {
                                this.tvProdList.push(data.prodList[i]);
                            }
                        }else{
                          this.tvProdList.push(data.prodList[i]);
                        }
                    }
                } else {
                    this.tvProdList = data.prodList;
                }
            }
        })
    },
    //电视类型选择
    tvTypeSelect() {
        let self = this;
        NlDropdown({
            confirmBtn: false,
            datalist: self.tvTypeList
        }, function (res) {
            self.selectTvType = res;
            if (res.id == '2013000019' || res.id == '2013000020' || res.id == '2013000021') {
                self.getTVModeFlag = false;
            } else {
                self.getTVModeFlag = true;
            }
            //能否开通此类型电视 判断busitype
            let url = `/xsb/personBusiness/businessOpenTv/h5CanOpenTv?telnum=${self.telnum}&pkgId=${res.id}&tvSort=${self.tvSort}`;
            self.$http.get(url).then(res => {
                if (res.data.retCode == '0') {
                    self.canOpenTv = "true";
                } else {
                    self.canOpenTv = "false";
                    self.$alert(res.data.retMsg || '暂无开通此类型互联网电视的权限');
                }
            })
            self.getTVProdList(res.id);
        });
    },
    //电视发放类型选择
    getTVModeTypeSelect() {
        let self = this;
        if ("22" == self.userInfo.region) {
            self.getTVModeTypeList.splice(2, 1);
        }
        NlDropdown({
            confirmBtn: false,
            datalist: self.getTVModeTypeList
        }, function (res) {
            self.selectTVGetModeType = res;
        });
    },
    //选择基础产品
    checkTVProd(item) {
      this.selectTVProd = item;
    },
    //校验电视产品选择
    doJudgeType(){
        if(this.selectTVProd.prodId == '-99') {
            this.$alert('请选择互联网电视基础产品');
        }else{
            this.nextStep();
        }
    },
    //宽带选址
    openAddr(){
        let self = this;
        VillAdress({
            popFlag: true,
            telnum: self.telnum,
            title: '宽带开通'
        }, function (obj) {
            self.fullName = obj.fullName || obj.villageAddress;
            self.netWorkType = obj.netWorkType; //网络接入类型
            self.villageId = obj.villageId;//小区id
            self.villageName = obj.villageName;//小区名称
            self.villageAddress = obj.villageAddress;//小区地址
            self.countryId = obj.countryId;//县市地址
            self.addressId = obj.addressId;//地址id
            self.otherAgent = obj.otherAgent; //资源提供方 1 移动 2 广电 3 铁通 4 其它运营商 5 复合运营商
            self.constMgr = obj.constMgr;// 施工方 1 移动 2 广电 3 铁通 4 其它运营商 5 复合运营商
            self.radiusFor = obj.radiusFor; //Radius归属 1 移动 2 广电 3 铁通 4 其它运营商 5 复合运营商
            self.userType = obj.userType; //宽带用户类型
            self.isSchoolVillage = obj.isSchoolVillage;//是否校园小区 false true
            self.planCategory = obj.planCategory; //宽带区域类型  1  市区城区 2  市辖乡镇 3  市辖村 4  县城城区 5  县辖乡镇 6  县辖村 7  	其他
            let deviceBigList = obj.deviceBigList || [];//LAN家庭网关信息
            let deviceSmallList = obj.deviceSmallList || [];
            //设备大类 "FTTH_ONU,3;FTTH_ONU,TTP华为"(包含设备大类，自备或设备小类)(3,自备，-1，不选择)
            if (deviceBigList.length > 0 && self.netWorkType == "0") {  //等于0的时候不展示设备
                self.deviceClass = '';
                self.deviceName = '';
            } else if (deviceBigList.length > 0 && self.netWorkType != "0") {
                self.deviceClass = deviceBigList[0].deviceClass + ',';
                if (deviceSmallList.length > 0) {
                    self.deviceClass += deviceSmallList[0].typeid;
                }
                self.deviceName = deviceBigList[0].deviceClassName;
            } else {
                self.deviceName = '';
                self.deviceClass = '';
            }
        });
    },
    //打开时间选择控件
    openDatePicker(picker) {
        this.$refs[picker].open();
    },
    //格式化时间
    formatDateVal(val) {
        this.installDate = dateFormat(val, "yyyy-MM-dd hh:mm:ss");
    },
    //服务协议
    goService() {
        let url = window.location.href
        url = url.substring(0, url.indexOf('?')) + '#/protocole?busiType=mband&gobackFlag=webview';
        ClientJs.openWebKit(encodeURIComponent(url), '', '0', '', '', '', '', '', '', '', '');
    },
    //校验宽带信息
    doMbandInfoCheck(){
        if(this.remark  == ""){
            this.$toast("请输入备注");
            return;
        }
        if(this.prodType == '1'){
            //宽带+电视
            if (this.villageAddress == "" || this.fullName == "点击选择") {
                this.$toast("请选择安装地址");
                return;
            } else if (this.linkName == "") {
                this.$toast("请输入姓名");
                return;
            } else if (this.linkPhone == "") {
                this.$toast("请输入联系方式");
                return;
            } else if (!/^((1)+\d{10})$/.test(this.linkPhone)) {
                this.$toast("请输入正确联系方式");
                return;
            } else if (this.pwd == "") {
                this.$toast("请输入密码");
                return;
            } else if (this.installDate == "") {
                this.$toast("请选择安装时间");
                return;
            }
            //处理代付费2400000979产品包的问题 20200514上线把下面的注释打开
            this.dealDaiPay();
        }else{
            //电视
            if (this.linkName == "") {
                this.$toast("请输入姓名");
                return;
            } else if (this.linkPhone == "") {
                this.$toast("请输入联系方式");
                return;
            } else if (!/^((1)+\d{10})$/.test(this.linkPhone)) {
                this.$toast("请输入正确联系方式");
                return;
            } else if (this.installDate == "") {
                this.$toast("请选择安装时间");
                return;
            }
            this.calAction();
        }

    },
    //处理代付费2400000979产品包的问题
    dealDaiPay(){
        //接入费产品包
        let prod979 = {
            isPackage: "0",
            prodId:'2400000979',//接入费产品包编码
            effectType:'0',//立即生效
            pkgprodId: "2013000001",
            prodName: "宽带接入费用"
        };
        let idx = this.checkMbandInList(prod979);//查询979是否在用户选中的包里
        let mustIdx = -1;
        for (let i = 0; i < this.mbandMustProdList.length; i++) {
            if (this.mbandMustProdList[i].prodId == prod979.prodId) {
                mustIdx = i;
                break;
            }
        }
        if(this.userType != '2'){//非校园小区
            //必须有且只有一个979代付费
            if (idx == -1 && mustIdx == -1) {//如果可选包里和必选包里都不存在979
                this.selectMbandExtraList.push(prod979);
            }
            if(~mustIdx && ~idx){//必选包里有979
                this.mbandMustProdList.splice(mustIdx,1);
            }
        } else {//校园小区
            //不能传979代付费（因为此包为可选包，防止用户勾选）
            if (~idx) {//如果存在979了
                this.selectMbandExtraList.splice(idx, 1);//删除979接入费的产品包
            }
            if(~mustIdx){
                this.mbandMustProdList.splice(mustIdx,1);
            }
        }
        this.calAction();
    },
    //算费
    calAction(){
        this.chargeList = [];
        this.totalFee = '';
        if(this.prodType == '1'){//宽带
            this.mBandCalBefore();
        }else{
            this.tvCalBefore();
        }
    },
    //宽带预处理
    mBandCalBefore(){
        let param = {};
        param.operid = this.userInfo.crmId;
        param.region= this.userInfo.region;
        param.subsid = this.subsId;
        param.mainofferid = this.mainOfferId;
        param.orderitem = [];

        let mBandObjItem = {
            "itemid": "2013000001",   //宽带包名id
            "instanceid": "",
            "actiontype": "A",
            "effectmode": "0",
            "suborderitem": {
                "orderitem": []
            }
        }
        let suborderitemObj = {
            "itemid": "2413000001",   //手机代付费宽带
            "instanceid": "",
            "actiontype": "A",
            "effectmode": "0"
        }
        mBandObjItem.suborderitem.orderitem.push(suborderitemObj);
        if(this.netWorkType == '1'){
            let mBandOtherDevice = {   //设备
                "itemid": "151000000000000016",
                "instanceid": "",
                "actiontype": "A",
                "effectmode": "0"
            }
            mBandObjItem.suborderitem.orderitem.push(mBandOtherDevice)
        }
        param.orderitem.push(mBandObjItem);

        let tvObjItem = {   //电视
            "itemid": this.selectTvType.id,
            "instanceid": "",
            "actiontype": "A",
            "effectmode": "0",
            "suborderitem":{
                "orderitem": [{
                    "itemid": "151000000000000011",
                    "instanceid": "",
                    "actiontype": "A",
                    "effectmode": "0"
                }]
            }
        }
        param.orderitem.push(tvObjItem);
        let voiceObjItem = {   //语音遥控器
            "itemid": "2000012770",
            "instanceid": "",
            "actiontype": "A",
            "effectmode": "0"
        }
        param.orderitem.push(voiceObjItem);
        this.doCal(param);

    },
    //电视预处理
    tvCalBefore(){
        let param = {};
        param.operid = this.userInfo.crmId;
        param.region= this.userInfo.region;
        param.subsid = this.subsId;
        param.mainofferid = this.mainOfferId;
        param.orderitem = [];
        let tvObjItem = {   //电视
            "itemid": this.selectTvType.id,
            "instanceid": "",
            "actiontype": "A",
            "effectmode": "0",
            "suborderitem":{
                "orderitem": [{
                    "itemid": "151000000000000011",
                    "instanceid": "",
                    "actiontype": "A",
                    "effectmode": "0"
                }]
            }
        }
        param.orderitem.push(tvObjItem);
        let voiceObjItem = {   //语音遥控器
            "itemid": "2000012770",
            "instanceid": "",
            "actiontype": "A",
            "effectmode": "0"
        }
        param.orderitem.push(voiceObjItem);
        this.doCal(param);
    },
    //算费
    doCal(param){
        let totalMoney=0;
        this.chargeList=[];
        let params = {
            "params":JSON.stringify(param)
        }
        let url = `/xsb/personBusiness/orderIntegrated/h5calSuperfeePost?`;
        this.$http.post(url,params).then(res => {
            let data = res.data;
            if (data.retCode == "0") {
                let orderitemList = data.data.orderitem;
                for(let i=0;i<orderitemList.length;i++){
                    if(orderitemList[i].totalamount !=null) {
                        totalMoney = Number(totalMoney) + Number(orderitemList[i].totalamount);
                    }
                    if(orderitemList[i].itempriceinfo !=null){
                        for(let j=0;j<orderitemList[i].itempriceinfo.length;j++){
                            this.chargeList.push(orderitemList[i].itempriceinfo[j]);
                        }
                    }
                }
                this.totalFee= totalMoney;
                this.currentStep = "step4";
            } else {
                this.$alert(res.data.retMsg || "算费异常，无法办理此业务");
            }
        }).catch(err => {
            this.$alert("算费请求失败，无法办理此业务");
        });
    },
    //提交校验
    doSumbitCheck(){
        this.payTypeDict = '1';
        this.payMethodDict = '1000';

        let self  = this;
          this.$messagebox({
              title: '温馨提示',
              message: "确认进行融合订购操作?",
              showCancelButton: true,
              cancelButtonText: '关闭',
              confirmButtonText: '确认'
          }).then((action) => {
            if (action === 'confirm') {
                self.checkAction();
            }
        });
    },
    //查询带宽
    qryBandSpeed(){
        Indicator.open('加载中');
        let prodIdList = [{
            "prodId" : this.selectMbandProd.prodId
        }];
        this.bandSpeed = "";
        let url = `/xsb/personBusiness/busiPreAccept/h5qryLimitBandWidth?mbandProdAll=${JSON.stringify(prodIdList)}`;
        this.$http.get(url).then(res => {
            if (res.data.retCode == '0') {
                this.bandSpeed = res.data.data[0].limitBandWidth;
                this.mBandCheckBefore();
            } else {
                this.$alert(res.data.retMsg || '查询带宽失败');
                Indicator.close();
            }
        }).catch((response)=>{
            this.$alert(response || '获取查询带宽网络超时');
            Indicator.close();
        })
    },
    //校验接口
    checkAction(){
        if(this.prodType == '1'){//宽带
            this.qryBandSpeed();
        }else{
            this.tvCheckBefore();
        }
    },
    //宽带校验封装参数
    mBandCheckBefore(){
        let param = {
            "orderItemInfo":[],
            "addressinfo":[
                {
                    "usAddr": this.fullName,//用户身份证地址
                    "thirdDistrictId": this.villageId,  //小区id
                    "thirdDistrictName": this.villageName, //小区名称
                    "addrId": this.addressId,  //地址id
                    "relaid": this.addressId  //地址id
                }
            ],
            "productOrderLine": [
                {
                    "beId": this.userInfo.region,
                    "businessCode":"ChangeProduct",
                    "ownerId":"",
                    "serviceNumber": this.telnum
                }
            ]
        }
        let mBandObjItem = {
            "itemid": "2013000001",   //宽带包名id
            "actionType": "A",
            "suborderitem":[]
        }
        let suborderitemObj = {
            "itemid": "2413000001",   //手机代付费宽带
            "actionType": "A",
            "orderitemprop": [],
            "addrreference": [{
                "relaid": this.addressId
            }]
        }
        let addrIdItem = { //地址编码
            "propcode":"addrId",
            "svalue": this.addressId
        }
        let districtIdItem = { //小区编码
            "propcode":"districtId",
            "svalue": this.villageId
        }
        let rangeTypeItem = { //地址是否具备开通条件
            "propcode":"rangeType",
            "svalue": "1"
        }
        let contactPhoneItem = { //联系人电话
            "propcode":"contactPhone",
            "svalue": this.linkPhone
        }
        let districtNameItem = { //小区名称
            "propcode":"districtName",
            "svalue": this.villageName
        }
        let linkNameItem = { //联系人姓名
            "propcode":"linkMan",
            "svalue": this.linkName
        }
        let factoryTypeItem = { //施工厂商类型
            "propcode":"factoryType",
            "svalue": this.constMgr
        }
        let gimsUserTypeItem = { //宽带用户类型
            "propcode":"gimsUserType",
            "svalue": this.userType
        }
        let radiusTypeItem = { //radius归属
            "propcode":"radiusType",
            "svalue": this.radiusFor
        }
        let limitBandWidthItem = { //带宽限制
            "propcode":"limitBandWidth",
            "svalue": this.bandSpeed
        }
        let passWordItem = { //宽带密码
            "propcode":"passWord",
            "svalue": this.pwd
        }
        let netWorkTypeItem = { //网络类型
            "propcode":"networkType",
            "svalue": this.netWorkType
        }
        let supplyTypeItem = { //宽带提供方
            "propcode":"supplyType",
            "svalue": this.otherAgent
        }
        let addrNameItem = { //地址名称
            "propcode":"addrName",
            "svalue": this.villageName
        }
        let linknameItem = { //联系人名字
            "propcode":"linkname",
            "svalue": this.linkName
        }
        let linktelItem = { //联系人手机号
            "propcode":"linktel",
            "svalue": this.linkPhone
        }
        let mbrandpwdItem = { //属性编码 密码
            "propcode":"mbrandpwd",
            "svalue": this.pwd
        }
        let installDateItem = { //预约安装时间
            "propcode":"installtime",
            "svalue": this.installDate
        }
        let noteItem = { //备注
            "propcode":"note",
            "svalue": this.remark
        }
        let notesItem = { //备注
            "propcode":"notes",
            "svalue": this.remark
        }
        let callForItem = { //县市编码
            "propcode":"callFor",
            "svalue": this.countryId
        }
        suborderitemObj.orderitemprop.push(addrIdItem,districtIdItem,rangeTypeItem,contactPhoneItem,districtNameItem);
        suborderitemObj.orderitemprop.push(linkNameItem,factoryTypeItem,gimsUserTypeItem,radiusTypeItem,limitBandWidthItem);
        suborderitemObj.orderitemprop.push(passWordItem,netWorkTypeItem,supplyTypeItem,addrNameItem,linknameItem,linktelItem);
        suborderitemObj.orderitemprop.push(mbrandpwdItem,installDateItem,noteItem,notesItem,callForItem);
        mBandObjItem.suborderitem.push(suborderitemObj);

        let mBandProdItem = {   //资费
            "itemid": this.selectMbandProd.prodId,
            "actionType": "A"
        }
        mBandObjItem.suborderitem.push(mBandProdItem)
        if(this.selectMbandExtraList && this.selectMbandExtraList.length > 0){
            for(let citem of this.selectMbandExtraList){
                let mBandProdExtraItem = {   //产品
                    "itemid": citem.prodId,
                    "actionType": "A"
                }
                mBandObjItem.suborderitem.push(mBandProdExtraItem)
            }
        }
        param.orderItemInfo.push(mBandObjItem);

        let deviceObjItem = { //设备写死
            "itemid": "151000000000000011",
            "actionType": "A"
        }
        param.orderItemInfo.push(deviceObjItem);
        let tvObjItem = {   //电视
            "itemid": this.selectTvType.id,
            "actionType": "A",
            "suborderitem": [{
                "itemid": this.selectTVProd.prodId,
                "actionType": "A"
            }]
        }
        if(this.mustTVProdIdList && this.mustTVProdIdList.length > 0){
            for(let citem of this.mustTVProdIdList){
                let tvMustProdExtraItem = {   //产品
                    "itemid": citem,
                    "actionType": "A"
                }
                tvObjItem.suborderitem.push(tvMustProdExtraItem)
            }
        }
        param.orderItemInfo.push(tvObjItem);
        let voiceObjItem = {   //语音遥控器
            "itemid": "2000012770",
            "actionType": "A"
        }
        param.orderItemInfo.push(voiceObjItem);
        this.doCheck(param);
    },
    //电视校验封装参数
    tvCheckBefore(){
        let param = {
            "orderItemInfo":[],
            "addressinfo":[
                {
                    "usAddr": this.fullName,//用户身份证地址
                    "thirdDistrictId": this.villageId,  //小区id
                    "thirdDistrictName": this.villageName, //小区名称
                    "addrId": this.addressId,  //地址id
                    "relaid": this.addressId  //地址id
                }
            ],
            "productOrderLine": [
                {
                    "beId": this.userInfo.region,
                    "businessCode":"ChangeProduct",
                    "ownerId":"",
                    "serviceNumber": this.telnum
                }
            ]
        }
        let deviceObjItem = { //设备写死
            "itemid": "151000000000000011",
            "actionType": "A"
        }
        param.orderItemInfo.push(deviceObjItem);
        let tvObjItem = {   //电视
            "itemid": this.selectTvType.id,
            "actionType": "A",
            "suborderitem": [{
                "itemid": this.selectTVProd.prodId,
                "actionType": "A"
            }]
        }
        if(this.mustTVProdIdList && this.mustTVProdIdList.length > 0){
            for(let citem of this.mustTVProdIdList){
                let tvMustProdExtraItem = {   //产品
                    "itemid": citem,
                    "actionType": "A"
                }
                tvObjItem.suborderitem.push(tvMustProdExtraItem)
            }
        }
        param.orderItemInfo.push(tvObjItem);
        let voiceObjItem = {   //语音遥控器
            "itemid": "2000012770",
            "actionType": "A"
        }
        param.orderItemInfo.push(voiceObjItem);
        this.doCheck(param);
    },
    //校验请求
    doCheck(info){
        let url = '/xsb/personBusiness/orderIntegrated/h5IntegratedCheck';
        let param = {
            "order": this.userId,
            "orderItemInfo": JSON.stringify(info.orderItemInfo),
            "addressinfo": JSON.stringify(info.addressinfo),
            "productOrderLine": JSON.stringify(info.productOrderLine)
        }
        this.$http.post(url, param).then((res) => {
            if (res.data.retCode == '0') {
                if(this.prodType == '1'){//宽带
                    this.mBandSubmitBefore();
                }else{
                    this.tvSubmitBefore();
                }
            } else {
                this.$alert(res.data.retMsg || '订单创建校验接口失败');
            }
        })
    },
    //宽带提交分装参数
    mBandSubmitBefore(){
        let param = {
            "goodsInfoList":[{
                "commodityCode": this.selectMbandProd.prodId,
                "number": "1",
                "price": "100",
                "feeType": "O",
                "payType": "1",
                "payDirection": "PAY",
                "chargeCode": "3212",
                "extAttr":[{
                    "extCode": "1",
                    "extValue": "2"
                }]
            }],
            "shipmentInfo":[
                {
                    "receiverName": this.linkName,
                    "receiverPhone": this.linkPhone,
                    "receiverAddress": this.fullName
                }
            ],
            "addressInfo":[
                {
                    "usAddr": this.fullName,
                    "thirdAddressId": this.addressId,
                    "thirdDistrictId": this.villageId,
                    "thirdDistrictName": this.villageAddress,
                    "addrId": this.addressId
                }
            ],
            "constructInfo":[
                {
                    "contactPhone": this.linkPhone,
                    "linkMan": this.linkName,
                    "isAppoint": "1",
                    "appointDate": dateFormat(chgStrToDate(this.installDate), ("yyyyMMddhhmmss"))
                }
            ]
        }

        if(this.selectMbandExtraList && this.selectMbandExtraList.length > 0){
            for(let citem of this.selectMbandExtraList){
                let mBandProdExtraItem = {   //产品
                    "commodityCode": citem.prodId,
                    "number": "1",
                    "price": "100",
                    "feeType": "O",
                    "payType": "1",
                    "payDirection": "PAY",
                    "chargeCode": "3212",
                    "extAttr":[{
                        "extCode": "1",
                        "extValue": "2"
                    }]
                }
                param.goodsInfoList.push(mBandProdExtraItem)
            }
        }

        let mBandObjItem = {    //宽带包名isPackage
            "commodityCode": "2013000001",
            "number": "1",
            "price": "100",
            "payType": "1",
            "payDirection": "PAY",
            "chargeCode": "3212",
            "extAttr":[{
                "extCode": "1",
                "extValue": "2"
            }]
        }
        param.goodsInfoList.push(mBandObjItem);
        let mBandPayItem = { //手机代付费宽带
            "commodityCode": "2413000001",
            "number": "1",
            "price": "100",
            "payType": "1",
            "chargeCode": "3212",
            "payDirection": "REC",
            "extAttr":[{
                "extCode": "factoryType",
                "extValue": this.constMgr
            },{
                "extCode": "supplyType",
                "extValue": this.otherAgent
            },{
                "extCode": "radiusType",
                "extValue": this.radiusFor
            },{
                "extCode": "gimsUserType",
                "extValue": this.userType
            },{
                "extCode": "gimsAreaType",
                "extValue": this.planCategory
            },{
                "extCode": "password",
                "extValue": this.pwd
            },{
                "extCode": "oldUserAcct",
                "extValue": ""
            },{
                "extCode": "oldUserType",
                "extValue": "7"
            },{
                "extCode": "networkType",
                "extValue": this.netWorkType
            },{
                "extCode": "bandSpeed",
                "extValue": this.bandSpeed
            },{
                "extCode": "addrBeId",
                "extValue": ""
            },{
                "extCode": "isCurrentDayFinish",
                "extValue": "否"
            },{
                "extCode": "C_O_NETWORK_TYPE",
                "extValue": this.netWorkType
            },{
                "extCode": "PonType",
                "extValue": "1"
            }]
        }
        param.goodsInfoList.push(mBandPayItem);

        let tvItem = [{
            "commodityCode": this.selectTvType.id,   //省内央广银河-CP
            "number": "1",
            "price": "100",
            "feeType": "O",
            "payType": "1",
            "payDirection": "PAY",
            "chargeCode": "3212",
            "extAttr":[{
                "extCode": "1",
                "extValue": "2"
            }]
        },{
            "commodityCode": this.selectTVProd.prodId, // 20元互联网电视
            "number": "1",
            "price": "100",
            "feeType": "O",
            "payType": "1",
            "payDirection": "PAY",
            "chargeCode": "3212",
            "extAttr":[{
                "extCode": "1",
                "extValue": "2"
            }]
        },{
            "commodityCode": "151000000000000011",  //设备
            "number": "1",
            "price": "100",
            "feeType": "O",
            "payType": "1",
            "payDirection": "PAY",
            "chargeCode": "3212",
            "extAttr":[{
                "extCode": "1",
                "extValue": "2"
            }]
        },{
            "commodityCode": "2000012770",  //语音遥控器
            "number": "1",
            "price": "100",
            "feeType": "O",
            "payType": "1",
            "payDirection": "PAY",
            "chargeCode": "3212",
            "extAttr":[{
                "extCode": "1",
                "extValue": "2"
            }]
        }]
        if(this.mustTVProdIdList && this.mustTVProdIdList.length > 0){
            for(let citem of this.mustTVProdIdList){
                if(citem !== "2400000236"){
                    let tvMustProdExtraItem = {   //产品
                        "commodityCode": citem,   //省内央广银河-CP必选 mustProdIds 商品编号
                        "number": "1",
                        "price": "100",
                        "feeType": "O",
                        "payType": "1",
                        "payDirection": "PAY",
                        "chargeCode": "3212",
                        "extAttr":[{
                            "extCode": "1",
                            "extValue": "2"
                        }]
                    }
                    tvItem.push(tvMustProdExtraItem)
                }
            }
        }
        param.goodsInfoList = param.goodsInfoList.concat(tvItem);
        this.doSubmit(param);
    },
    //电视提交封装参数
    tvSubmitBefore(){
        let param = {
            "goodsInfoList":[
            {
                "commodityCode": this.selectTvType.id,   //省内央广银河-CP
                "number": "1",
                "price": "100",
                "feeType": "O",
                "payType": "1",
                "payDirection": "PAY",
                "chargeCode": "3212",
                "extAttr":[{
                    "extCode": "1",
                    "extValue": "2"
                }]
            },
            {
                "commodityCode": this.selectTVProd.prodId, // 20元互联网电视
                "number": "1",
                "price": "100",
                "feeType": "O",
                "payType": "1",
                "payDirection": "PAY",
                "chargeCode": "3212",
                "extAttr":[{
                    "extCode": "1",
                    "extValue": "2"
                }]
            },
            {
                "commodityCode": "151000000000000011",  //设备
                "number": "1",
                "price": "100",
                "feeType": "O",
                "payType": "1",
                "payDirection": "PAY",
                "chargeCode": "3212",
                "extAttr":[{
                    "extCode": "1",
                    "extValue": "2"
                }]
            },
            {
                "commodityCode": "2000012770",  //语音遥控器
                "number": "1",
                "price": "100",
                "feeType": "O",
                "payType": "1",
                "payDirection": "PAY",
                "chargeCode": "3212",
                "extAttr":[{
                    "extCode": "1",
                    "extValue": "2"
                }]
            }],
            "shipmentInfo":[
                {
                    "receiverName": this.linkName,
                    "receiverPhone": this.linkPhone,
                    "receiverAddress": this.fullName
                }
            ],
            "addressInfo":[],
            "constructInfo":[],
        }
        if(this.mustTVProdIdList && this.mustTVProdIdList.length > 0){
            for(let citem of this.mustTVProdIdList){
                if(citem !== "2400000236"){
                    let tvMustProdExtraItem = {   //产品
                        "commodityCode": citem,   //省内央广银河-CP必选 mustProdIds 商品编号
                        "number": "1",
                        "price": "100",
                        "feeType": "O",
                        "payType": "1",
                        "payDirection": "PAY",
                        "chargeCode": "3212",
                        "extAttr":[{
                            "extCode": "1",
                            "extValue": "2"
                        }]
                    }
                    param.goodsInfoList.push(tvMustProdExtraItem)
                }
            }
        }
        this.doSubmit(param);
    },
    //提交请求
    doSubmit(info){
        let authType = 'AuthCheckB';
        let authTypeNumber = '1';
        let param = {
            "custId": this.userId,   //客户标识
            "serviceNumber": this.telnum,   //用户号码
            "channelId": "601", //渠道编码
            "cityCode": this.userInfo.region,   //地市编码
            "goodsInfoList": JSON.stringify(info.goodsInfoList), //商品列表
            "operNo": this.userInfo.crmId,   //操作员标识
            "shipmentInfo": JSON.stringify(info.shipmentInfo), //物流配送信息
            "constructInfo": JSON.stringify(info.constructInfo), //地址
            "addressInfo": JSON.stringify(info.addressInfo), //联系人
            "totalFee": this.totalFee,  //订单金额 单位：分
            "telnum": this.telnum,  //用户号码
            "channelType": "601",   //渠道编码
            "paymethod": this.payMethodDict,    //支付方式
            "authCheckType": authType,  //鉴权方式
            "studentPhone": this.userInfo.relTelnum,    //协销工号
            "cardAuthSrl": "", //身份证鉴权记录读证信息主键
            "signId": "",   //暂无签名附件编码
            "authTypeNumber": authTypeNumber, //鉴权方式1:服务密码鉴权，2：短信验证码鉴权，3：身份证鉴权
            "type": /android|harmony/gi.test(navigator.userAgent)?'ANDROID':'IOS',//设备型号
            "stationId": this.userInfo.stationId,   //岗位
            "longitude": "",  //经度
            "latitude": "",    //纬度
            "location": "",    //定位信息
            "operatorName": this.userInfo.operatorName, //操作员名称
        }
        let url = '/xsb/personBusiness/orderIntegrated/h5IntegratedOrder';
        this.$http.post(url, param).then((res) => {
            if (res.data.retCode == '0') {
                let alertMessage = '融合订购业务办理成功</br>订单号：' + res.data.data.orderId;
                this.$messagebox.alert(alertMessage, '温馨提示').then(action => {
                    this.currentStep = 'step1';
                    this.goPrev();
                });
            } else {
                this.$alert(res.data.retMsg || '订单创建提交接口失败');
            }
        })
    },
    //查询用户订购信息
    getOrderInfo(){
        let url = `/xsb/personBusiness/familyNetWorking/h5GetOrderInfo?telnum=${this.telnum}&regionId=${this.userInfo.region}&operId=${this.userInfo.crmId}`;
        this.$http.get(url,{unLoadFlg:true}).then(res => {
            if(res.data.retCode == '0') {
                this.subsId = res.data.data.subsid;
                this.mainOfferId = res.data.data.offerlist[0].offerid;
            }else{
                this.$alert(res.data.retMsg || '查询主题套餐信息错误')
            }
        })
    },
    //获取身份证信息
    qryUserId() {
        let getIdCardUrl ="/xsb/personBusiness/personInfo/h5bdsQryPersonInfo?telnum=" +this.telnum;
        this.$http.get(getIdCardUrl,{unLoadFlg:true}).then(res => {
            if (res.data.retCode == "0") {
                this.userId = res.data.data.user_id;
                this.custContactAddr = res.data.data.custContactAddr;
                this.custName = res.data.data.custName;
                this.linkName = res.data.data.custName;
                this.linkPhone = this.telnum;
                this.dimLinkName = this.linkName;
                this.dimLinkPhone = this.linkPhone;
            } else {
                this.$toast(res.data.retMsg||"身份信息查询失败");
            }
        }).catch(err => {
            this.$toast( "身份信息查询异常");
        });
    },

  },
  computed:{
        //模糊化联系人名称
        dimLinkName:{
            get(){
                if(this.custName && this.custName == this.linkName){
                    let name = this.custName;
                    return name.substring(0,1) + new Array(name.length).join('*');
                }
                return this.linkName;
            },
            set(value){
                this.linkName = value;
            }
        },
        //模糊化联系人电话
        dimLinkPhone:{
            get(){
                if (this.telnum && this.telnum == this.linkPhone) {
                    let reg = /^(\d{3})\d*(\d{4})$/;
                    return this.telnum.replace(reg, '$1****$2')
                }
                return this.linkPhone;
            },
            set(value){
                this.linkPhone = value;
            }
        }
  },
  filters: {
    //判断钱是否是整数
    chgYuan(val) {
        if (!val) {
            return 0;
        }
        try {
            let money = parseInt(val);
            return money / 100;
        } catch (error) {
            return 0;
        }
    },
  },
  created(){
        this.userInfo = Storage.session.get("userInfo");
        this.telnum = this.userInfo.telnum;   //用户手机号
        this.srcFrom = this.$route.query.srcFrom || 'csView';
        //设置安装日期的默认日期为明天
        let tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        this.installDate = dateFormat(tomorrow, ("yyyy-MM-dd hh:mm:ss"));
        this.dateval = this.installDate;
        this.tvSort = '1';
        this.checkBandTv();
        this.qryUserId();
        this.getOrderInfo();
  },
}
</script>
<style lang="less" scoped>
.wrapper{
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  position: fixed;
  overflow: scroll;
  background-color: #ECF0FA;
}

.flex-box {
    height: 100%;
    box-sizing: border-box;
    padding: 44px 0 76px 0;
    display: flex;
    flex-direction: column;
    border-top: 1px solid #D9D9D9;
    overflow: hidden;
}

.productBox-main {
    max-height: 50%;
    height: 50%;
    flex-shrink: 0;
    overflow: hidden;
    position: relative;
    .productList {
        height: 100%;
        padding-bottom:50px;
        box-sizing: border-box;
        overflow: auto;
        border-bottom: 1px solid #EAEAEA;
    }
}

.productBox-title {
    height: 48px;
    line-height: 48px;
    color: #2C2C2C;
    font-size: 16px;
    padding: 0 12px;
    background-color: white;
    border-bottom: 1px solid #EBEBEB;
}

.op-content {
    background-color: white;
    overflow: hidden;
    border-bottom: 1px solid #EAEAEA;
    position: relative;
    padding: 14px 0;

    .op-content:last-child {
        border-bottom: none;
    }
    .op-content-checkbox {
        display: inline-block;
        padding: 12px;
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
    }
    .op-content-checkbox .op-icon-left {
        font-size: 20px !important;
        color: #979797;
    }
    .op-content-checkbox.selected .op-icon-left {
        color: #1680F9;
    }

    .op-content--title {
        font-size: 14px;
        line-height: 18px;
        color: #232323;
        display: block;
        width: 100%;
        padding-left: 44px;
        padding-right: 48px;
        box-sizing: border-box;
        word-break: break-all;
    }

    .op-content--icon {
        float: right;
        padding: 12px;
        color: #BBBBBB;
        font-size: 24px;
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
    }
}
.op-icon {
    font-size: 20px !important;
    color: #1680F9;
}

.productBox {
    flex-grow: 1;
    flex-shrink: 1;
    max-height: 50%;
    height: 50%;
    overflow: hidden;
    position: relative;
    .productBox-main {
        max-height: 50%;
        height: 50%;
        flex-shrink: 0;
        overflow: hidden;
        position: relative;
        .productBox-title {
            height: 48px;
            line-height: 48px;
            color: #2C2C2C;
            font-size: 16px;
            padding: 0 12px;
            background-color: white;
            border-bottom: 1px solid #EBEBEB;
        }
        .productList {
            height: 100%;
            padding-bottom:50px;
            box-sizing: border-box;
            overflow: auto;
            border-bottom: 1px solid #EAEAEA;
        }
    }

    .secondaryUl {
        padding-bottom:48px;
        overflow: auto;
        height: 100%;
        box-sizing: border-box;
        .secondaryUl-li {
            display: flex;
            flex-direction: row;
            border-bottom: 1px solid #EAEAEA;
            background-color: white;
            padding: 14px 12px;
        }

        .secondaryUl-li:last-child {
            border-bottom: none;
        }

        .secondaryUl-li-checkbox, .secondaryUl-li-effect, .secondaryUl-li--icon {
            flex-grow: 0;
            flex-shrink: 0;
            display: flex;
            align-items: center;
        }

        .secondaryUl-li-effect {
            margin-right: 6px;
            font-size: 14px;
            color: #FF4848;
        }

        .secondaryUl-li--title {
            flex-grow: 1;
            flex-shrink: 1;
            margin: 0 6px;
            font-size: 14px;
            line-height: 18px;
            color: #232323;
            word-break: break-all;
            display: inline-flex;
            align-items: center;
        }

        .secondaryUl-li-checkbox .op-icon-left {
            color: #979797;
            font-size: 20px !important;
        }

        .secondaryUl-li-checkbox.selected .op-icon-left {
            color: #1680F9;
        }
    }

}

.typeList {
    box-sizing: border-box;
    border-bottom: 1px solid #EAEAEA;
    .icon-yjt {
        color: #c7c7cc;
    }
    .type-content {
        border-bottom: 1px solid #EAEAEA;
        position: relative;
        padding: 14px 0;
        background-color: white;
        overflow: hidden;
        .type-content-icon {
            display: inline-block;
            padding: 12px;
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
        }
        .type-icon-left {
            font-size: 20px !important;
            color: #1680F9;
        }
        .type-content--title {
            font-size: 14px;
            line-height: 18px;
            color: #232323;
            display: block;
            width: 100%;
            padding-left: 44px;
            padding-right: 48px;
            box-sizing: border-box;
            word-break: break-all;
        }
        .type-content--jt {
            float: right;
            padding: 12px;
            color: #BBBBBB;
            font-size: 24px;
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
        }
    }
}

.tvProductBox {
    height: 100%;
    overflow: hidden;
    position: relative;
    .tvProductBox-title {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 48px;
        line-height: 48px;
        color: #2C2C2C;
        font-size: 16px;
        padding: 0 12px;
        background-color: white;
        border-bottom: 1px solid #EBEBEB;
        z-index: 100;
    }
    .tvProductList {
        height: 100%;
        box-sizing: border-box;
        padding-top: 48px;
        overflow: auto;
        border-bottom: 1px solid #EAEAEA;
    }
    .prod-content {
        border-bottom: 1px solid #EAEAEA;
        position: relative;
        padding: 14px 0;
        background-color: white;
        overflow: hidden;
    }
    .tvop-content-checkbox {
        display: inline-block;
        padding: 12px;
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
    }
    .tvop-content-checkbox .op-icon-left {
        font-size: 20px !important;
        color: #979797;
    }
    .tvop-content-checkbox.selected .op-icon-left {
        color: #1680F9;
    }
    .tvop-content--title {
        font-size: 14px;
        line-height: 18px;
        color: #232323;
        display: block;
        width: 100%;
        padding-left: 44px;
        padding-right: 48px;
        box-sizing: border-box;
        word-break: break-all;
    }
    .tvop-content--icon {
        float: right;
        padding: 12px;
        color: #BBBBBB;
        font-size: 24px;
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
    }
}
.tvop-button-box {
    background-color: white;
    width: 100%;
    margin-top: 12px;
    text-align: center;
    line-height: 76px;
    position: fixed;
    bottom: 0px;
    padding: 0 12px;
    box-sizing: border-box;
}

.msgbox-bg {
    position: fixed;
    top: 0px;
    left: 0px;
    right: 0px;
    bottom: 0px;
    z-index: 111;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    flex-direction: column;
    justify-content: center;
    .msgbox {
        margin: 0 30px;
        background: #fff;
        border-radius: 7px;
    }

    .msgbox .msgbox-main {
        padding: 20px;
        color: #727272;
        min-height: 40px;
        max-height: 120px;
        overflow-y: auto;
        font-size: 14px;
        line-height: 18px;
    }

    .msgbox .msgbox-footer {
        display: block;
        text-align: center;
        height: 40px;
        line-height: 40px;
        border-top: 1px solid #EAEAEA;
        box-sizing: border-box
    }
}

.fixedtext {
    position: fixed;
    top: 44px;
    left: 0;
    right: 0;
    height: 41px;
    display: table;
    text-align: center;
    font-size: 14px;
    color: #585858;
    box-shadow: 2px 2px 6px 0px rgba(220, 220, 220, 0.5);
    background-color: white;
    width: 100%;
    border-top: 1px solid #D9D9D9;
    span {
        display: table-cell;
        line-height: 20px;
        vertical-align: middle;
        text-align: left;
        padding: 0 8px;
    }
}

.contentBox {
    height: 100%;
    box-sizing: border-box;
    padding: 4px 0px 10px 0px;
    overflow: auto;
    .tvProductInfo {
        margin: 50px 16px 16px;
        background-color: white;
        border-radius: 8px;
        box-shadow: 2px 2px 6px 0px rgba(220, 220, 220, 0.5);
        .tvProductInfo-icon {
            line-height: 48px;
            margin-right: 6px;
            color: #1667F7;
            font-size: 18px !important;
        }

        .tvProductInfo-title {
            flex-grow: 1;
            flex-shrink: 1;
            color: #585858;
            font-size: 14px;
            font-weight: 500;
            line-height: 48px;
        }

        .tvProductInfo-label {
            line-height: 48px;
            color: #818181;
            font-size: 12px;
            flex-grow: 0;
            flex-shrink: 0;
        }
    }

    .mBandDetail{
        margin: 6px 16px 16px;
    }

    .tvProductInfo li {
        display: flex;
        flex-direction: row;
        border-bottom: 1px solid #F2F2F2;
        padding: 0 12px;
    }

    .tvProductInfo li:last-child {
        border-bottom: none
    }

    .tvProductInfo li.mainInfo .tvProductInfo-title {
        color: #1681FB;
        line-height: 16px;
        padding: 16px 0px;
    }


}

.cal-box{
    height: 100%;
    box-sizing: border-box;
    padding: 44px 0 0 0;
    display: flex;
    flex-direction: column;
    border-top: 1px solid #D9D9D9;
    overflow: hidden;
}

.installBox{
    height: 100%;
    box-sizing: border-box;
    padding: 0px;
    overflow: auto;
    display: flex;
    flex-direction: column;
    .tvInstallInfo {
        margin: 12px 16px;
        background-color: white;
        border-radius: 8px;
        box-shadow: 2px 2px 6px 0px rgba(220, 220, 220, 0.5);
        overflow: hidden;
        flex: 0 0 auto;
        .tvInstallInfo-title-img {
            width: 100%;
            display: block;
        }
        .tvInstallUl > li {
            display: flex;
            flex-direction: row;
            padding: 12px;
            box-sizing: border-box;
            border-bottom: 1px solid #EAEAEA;
            position: relative;
        }
        .tvInstallUl > li:last-child {
            border-bottom: none;
        }
        .tvInstallUl-title {
            color: #232323;
            font-size: 14px;
            line-height: 18px;
            flex-grow: 0;
            flex-shrink: 0;
            text-align: right;
            margin-right: 6px;
        }
        .tvInstallUl-choose {
            flex-grow: 1;
            flex-shrink: 1;
            text-align: right;
            color: #BBBBBB;
            font-size: 14px;
            position: relative;
        }
        .tvInstallUl-input {
            flex-grow: 1;
            flex-shrink: 1;
            text-align: right;
            color: #323232;
            font-size: 14px;
            outline: none;
            border: none;
        }
        .tvInstallUl-input::-webkit-input-placeholder {
            color: #BBBBBB;
            font-size: 14px;
        }
        .tvInstallUl-choose-input {
            text-align: right;
            outline: none;
            border: none;
            font-size: 14px;
        }
        .tvInstallUl-choose-input::-webkit-input-placeholder {
            color: #BBBBBB;
        }
        .tvInstallUl-choose * {
            vertical-align: middle;
        }
        .agreement {
            text-align: center;
            display: block;
            color: #1681FB;
            font-size: 12px;
            line-height: 40px;
            border-top: 1px solid #EAEAEA;
        }
        .locationText {
            width: 100%;
            padding-right: 20px;
            box-sizing: border-box;
            display: block;
            line-height: 20px;
            color: #232323;
        }
        .villageColor {
            color: #BBBBBB;
        }
        .moreIcon {
            position: absolute;
            top: 50%;
            right: 0px;
            transform: translateY(-50%);
        }
        .installUl-textarea {
            flex-grow: 1;
            flex-shrink: 1;
            text-align: right;
            color: #323232;
            font-size: 14px;
            outline: none;
            border: none;
            resize: none;
            padding: 0;
        }

        .installUl-textarea::-webkit-input-placeholder {
            color: #BBBBBB;
            font-size: 14px;
        }
    }
    .tvInstallInfo-box{
        flex: 0 0 80px;
    }
}

.box-title{
    padding:0 12px;
    height:44px;
    line-height:44px;
    position:relative;
    border-bottom:1px solid #EBEBEB;
    background: white;
    .arrow{
        position:absolute;
        right:1rem;
        top:0;
    }
    .title{
        font-weight: 600;
        font-size: 16px;
        display:inline-block;
        line-height:22px;
    }
    .iconfont{
        color:#1681FB;
        font-size:18px;
    }
}

.alreadyfixed-info{
    background: #FFFFFF;
    padding: 6px 12px 6px 12px;
    .title{
        width: 100%;
        font-size:14px;
        font-weight:500;
        color:rgba(40,40,40,1);
        line-height:30px;
        float: left;
        height: 31px;
        margin-top: 6px;
        .redDot{
            width:8px;
            height:8px;
            background:rgba(244,58,69,1);
            border-radius: 4px;
            float: left;
            margin-top: 11px;
            margin-left: 12px;
            margin-right: 3px;
        }
        .green{
            background:rgba(24,190,50,1);
        }
    }

    .with-border-line{
        border-top: 1px dashed #EBEBEB;
        margin: 12px 0px;
    }

    .marketInfoBox{
        overflow: hidden;
        display: flex;
        width: 100%;
        margin: 6px 0px;
        .zi{
            flex: 0 0 58px;
            font-size:14px;
            font-weight:400;
            color:rgba(146,146,146,1);
            line-height:20px;
        }
        .value{
            flex: 1;
            font-size:14px;
            font-weight:400;
            color:rgba(59,59,59,1);
            line-height:20px;
        }
    }
}

.totalMoney{
  flex: 0 0 42px;
  margin-top: 9px;
  background:#fff;
  padding:0 12px;
  height:44px;
  line-height:44px;
  border-bottom:1px solid #EBEBEB;
  display:flex;
  justify-content: space-between;
  .moneyText{
      font-size:16px;
      color:#282828;
      font-weight: 600;
  }
  .shituxiangqing{
      padding-right:4px;
      color:#1681FB;
  }
  .money{
      font-size:16px;
      color:#007AFF;
      font-weight: 500;
  }
}

.bandwidthInfo li{
    background: #fff;
    padding: 0 12px;
    height: 44px;
    line-height: 44px;
    border-bottom: 1px solid #EBEBEB;
    margin-bottom: 0 !important;
    display: flex;
    justify-content: space-between;
    box-sizing: border-box;
}

.band-detail{
    margin-bottom: 4px;
}

</style>
