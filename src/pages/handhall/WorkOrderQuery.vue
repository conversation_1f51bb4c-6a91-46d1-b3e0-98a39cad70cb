<template>
  <div class="wrapper">
    <Header :tsTitleTxt="title" backType="custom" @emGoPrev="goPrev"></Header>

    <!--工单查询-->
    <!--搜索框-->
    <div class="pbo-top" v-show="chooseFlag==1">
      <div class="ipt-wrapper">
          <div class="process-box" @click="choiseState">{{orderStateName}}</div>
          <div class="icon-container" @click="choiseState"><i class="iconfont jiantou2"></i></div>
          <input type="text" class="seach-ipt" onkeyup="value=value.replace(/[^\d]/g,'')" v-model="telnum" placeholder="请输入手机号码进行搜索" maxlength="11">
          <span class="query-btn" @click="searchBusi()">搜索</span>
      </div>
    </div >
    <!--工单查询列表-->
    <div class="pbo-list" v-show="chooseFlag==1&&dataList.length>0" ref="listFlag">
      <div class="pbo-info" >
        <div class="pbo-detail" v-for="(item,index) in dataList" :key="index" >
          <div class="pbo-main" @click="findWorkOrderList(item)" >
            <!--集团&个人标志-->
            <div class="pbo-top-time" >
              <span class="pbo-time" v-show="item.customType== '0'">个人</span>
              <span class="pbo-time" v-show="item.customType== '1'">集团</span>

              <!--手机号-->
              <span class="pbo-tel">{{item.srvNumber}}</span>
            </div>
            <div class="pbo-order" >
                <div class="pbo-order-info">
                    <div class="pbo-txt">工单流水号:</div>
                    <div class="pbo-ziti">{{item.processWOID}}</div>
                </div>
                <div class="pbo-order-info">
                    <div class="pbo-txt">受理渠道:</div>
                    <div class="pbo-ziti" v-show="item.acptChnlID=='0'">营业厅</div>
                    <div class="pbo-ziti" v-show="item.acptChnlID=='1'">在线客服</div>
                </div>
                <div class="pbo-order-info" v-show="item.customID">
                    <div class="pbo-txt">客户编号:</div>
                    <div class="pbo-ziti">{{item.customID}}</div>
                </div>
                <div class="pbo-order-info">
                    <div class="pbo-txt">处理环节:</div>
                    <div class="pbo-ziti">{{item.node|dealName}}</div>
                </div>

                <div class="pbo-order-info" v-show="item.productID">
                    <div class="pbo-txt">关联商品:</div>
                    <div class="pbo-ziti">{{item.productID}}</div>
                </div>
                <div class="pbo-order-info">
                    <div class="pbo-txt">受理时间:</div>
                    <div class="pbo-ziti">{{item.acptTime|changeTimeType}}</div>
                </div>
            </div>
            <div class="level-jian" v-show="item.processList && item.processList.length > 0">
              <i class="iconfont youjiantou iconsyou"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!--工单详情-->
    <div class="order-info-tel" v-show="chooseFlag==2">
      <div class="order-info-liu">
        <div class="order-txt">工单流水号</div>
        <div class="order-ziti">{{workOrderId}}</div>
      </div>
    </div>
    <div class="pbo-list" v-show="chooseFlag==2">
      <div class="pbo-info" >
        <div class="pbo-detail" v-for="(item,index) in workInfoOrderList" :key="index">
          <div class="pbo-main" >
            <div class="pbo-order" >
              <div class="work-order-info">
                  <div class="work-txt">工单编号:</div>
                  <div class="work-ziti">{{item.processWOID}}</div>
              </div>
              <div class="work-order-info">
                   <div class="work-txt">处理环节:</div>
                   <div class="work-ziti">{{item.processWONode|dealName}}</div>
               </div>
              <div class="work-order-info">
                  <div class="work-txt">工单处理人:</div>
                  <div class="work-ziti">{{item.staffName}}({{item.staffNO}})</div>
              </div>
              <div class="work-order-info" >
                  <div class="work-txt">处理结果:</div>
                  <div class="work-ziti" v-show="item.bizCode!='0000'">失败</div>
                  <div class="work-ziti" v-show="item.bizCode=='0000'">成功</div>
              </div>
              <!--失败描述、成功不显示-->
              <div class="work-order-info" v-show="item.bizCode!='0000' && item.bizDesc">
                 <div class="work-txt">失败原因:</div>
                 <div class="work-ziti">{{item.bizDesc}}</div>
              </div>
              <div class="work-order-info">
                 <div class="work-txt">受理时间:</div>
                 <div class="work-ziti">{{item.feedbackTime|changeTimeType}}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 暂无数据-->
    <NoDataPage tipTxt="暂无工单" v-show="dataList.length==0" style="margin-top:15px;"></NoDataPage>

  </div>
</template>

<script>
  import Header from "components/common/Header.vue";
  import NoDataPage from 'components/common/NoDataPage';
  import NlDropdown from "components/common/NlDropdown/dropdown.js";
  import {dateFormat, chgStrToDate} from '@/base/utils'
  import Storage from '@/base/storage';

  export default {
    components:{ NoDataPage,Header},
    data(){
      return{
        title:'工单查询', //标题
        telnum: '',//用户电话号码
        orderStateName:'处理中',
        orderStateId:'0',
        chooseFlag: "1",
        selectState:[{id:'0',label:'处理中'},
                    {id:'1',label:'挂起'},
                    {id:'2',label:'关闭'}],
        workOrderState:{id:'0',label:'处理中'},//工单状态
        workOrderId:'', //工单流水号
        workInfoOrderList:[], //订单详情
        dataList:[],// 工单数据列表
      }
    },
    methods:{
      goPrev(){
        if(this.chooseFlag==2){
          this.title = '工单查询';
          this.chooseFlag='1';
          this.workInfoOrderList=[];
          this.workOrderId='';
        }else{
          history.go(-1);
        }
      },
      //根据手机号查询工单
      getWorkOrderData(){
          this.dataList = [];
          let url = `/xsb/personBusiness/workOrderQuery/h5getCustomerBusinessOrderList?srvNumber=${this.telnum}&status=${this.orderStateId}`;
          this.$http.get(url).then(res => {
              if (res.data.retCode == "0") {
                  this.dataList = res.data.data;
                  if(this.dataList == null || this.dataList.length == 0){
                      this.$alert("暂无工单记录");
                  }
              } else {
                  this.$alert(res.data.retMsg);
              }
          }).catch(response => {
              this.$alert("查询客户下的业务长流程工单列表数据异常:" + response);
          });
      },
     //查找列表详情
      findWorkOrderList(item){
          if(item.processList && item.processList.length > 0){
            this.chooseFlag  = '2';
            this.workOrderId = item.processWOID;
            this.workInfoOrderList = item.processList;
          }
      },
      //筛选状态
      choiseState(){
        var self = this;
        NlDropdown({
          confirmBtn: false,
          datalist: self.selectState,
          defaultValue: self.workOrderState,
        }, function(retVal) {
          //获取返回回调
          self.orderStateId=retVal.id;
          self.orderStateName=retVal.label;
        });
      },
      //搜索
      searchBusi(){
        //判断有没有手机号
        if (!/^((1)+\d{10})$/.test(this.telnum)) {
          this.$alert('请输入手机号码进行搜索');
          return;
        }
        this.getWorkOrderData();
      },
      clear() {
        this.telnum = '';
      },
    },
    filters:{
      changeTimeType(val) {
        if(val.length != 8) {
          return dateFormat(chgStrToDate(val,true),"yyyy-MM-dd hh:mm:ss")
        }else {
          return dateFormat(chgStrToDate(val,true),"yyyy-MM-dd")
        }
      },
      //处理环节
      dealName(val){
          let dName = ''
          if(val == '0'){
              dName = '客户报装';
          } else if(val == '1'){
              dName = '订单受理';
          } else if(val == '2'){
              dName = '施工调度';
          } else if(val == '3'){
              dName = '安装收费';
          } else if(val == '4'){
              dName = '回单竣工';
          }
          return dName;
      },
    },
    watch: {
        chooseFlag(val) {
            if(val == '1'){
                this.title = '工单查询';
            }else {
                this.title = '工单详情';
            }
        }
    },
    created(){
        this.telnum = Storage.session.get("userInfo").telnum; //用户手机号
        this.getWorkOrderData();
    },
  }
</script>

<style lang="less" scoped>
  @import '../../base/less/variable.less';
  .wrapper{
    width: 100%;
    height: 100%;
    overflow: hidden;
    background: #F4F4F6;
    display: flex;
    flex-direction: column;
    position: fixed;
  }
  .pbo-top {
    width: 100%;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    background: #fff;
    margin-top: 44px;
    border-top: 1px solid #E1E1E1;
  }
  .ipt-wrapper {
    width: 100%;
    height: 32px;
    line-height: 32px;
    border-radius: 16px;
    background: rgba(241, 241, 241, 1);
    padding: 0 12px;
    overflow: hidden;
    display: flex;
    .process-box{
      flex: 0 0 46px;
      font-weight:600;
      color: #007AFF;
      font-size: 14px;
      text-align: center;
    }
    .seach-ipt {
        flex: 1;
        font-size: 12px;
        height: 32px;
        border-left: 1px solid #8F8F8F;
        padding-left: 10px;
        padding-right: 40px;
        margin-left: 10px;
        background: rgba(241, 241, 241, 1);
        box-sizing: border-box;
            border-radius: 0px;
        &:focus {
            outline: none;
        }
    }
    .query-btn {
        flex: 0 0 30px;
        font-size: 12px;
        right: 12px;
        color: #007AFF;
    }
}
  .search-box {
    flex: auto;
    position: relative;
    margin-right: 0.5rem;
    .box {
      width: 100%;
      height: 30px;
      font-weight: 400;
      background: rgba(241, 241, 241, 1);
      border-radius:12px;
      box-sizing: border-box;
      outline: 0;
      padding-left: 6rem;
      padding-right: 1.5rem;
      overflow: hidden;
      font-size: 12px;
      &::placeholder {
        color: #8F8F8F;
      }
    }
  }
  .pbo-top-time {
    line-height: 20px;
    .pbo-time {
      border: 1px solid #1680F9;
      border-radius: 6px;
      font-size: 12px;
      font-weight: 400;
      color: #1680F9;
      padding: 2px 8px;
      background: #fff;
      letter-spacing: 1px;
    }
  }

  .pbo-list{
    overflow: auto;
    height: 90%;
  }
  .pbo-info{
    padding: 15px;
  }
  .pbo-detail{
    margin-bottom: 15px;
    box-sizing: border-box;
    border-radius: 10px;
    background: #fff;

  }
  .pbo-main{
    padding: 10px 12px;
    box-sizing: border-box;
    position: relative;
  }
  .pbo-tel{
    font-size: 16px;
    font-weight: 600;
    color: #232323;
  }
  .pbo-order-info{
    display: flex;
    overflow: hidden;
    font-size: 14px;
    color: #868686;
    line-height: 26px;
  }
  .pbo-txt{
    flex: 0 0 80px;
  }
  .pbo-ziti{
    flex: 1;
    font-size: 14px;
    color: #1D1D1D;
    display: inline-flex;
    word-break: break-all;
    margin-right: 12px;
  }
  .level-jian{
    position: absolute;
    top: 44%;
    right: 10px;
    .iconsyou{
      color: #BDBDBD;
      margin-left: -7px;
      font-size: 12px;
    }
  }
  .work-order-info{
    display: flex;
    font-size: 14px;
    color: #868686;
    line-height: 26px;
    overflow: hidden;
  }
  .work-txt{
    flex: 0 0 80px;
  }
  .work-ziti{
    flex: 1;
    font-size: 14px;
    color: #1D1D1D;
    word-break: break-all;
  }
  .order-info-tel{
    margin-left: 10px;
    margin-top: 60px;
  }
  .order-info-liu{
    display: flex;
    font-size: 14px;
    font-weight: 400;
    color: #868686;
    line-height: 26px;
  }
  .order-txt{
    flex: 0 0 80px;
  }
  .order-ziti{
    font-size: 14px;
    font-weight: 400;
    color: #1D1D1D;
    display: inline-flex;
    word-break: break-all;
    flex: 1;
    margin-right: 6px;
  }
  .icon-container{
    flex: 0 0 auto;
  }
  .jiantou2{
    color: #007AFF;
    font-size: 12px;
  }
  .search-type-xian{
    position: absolute;
    left: 5.23rem;
    top: 50%;
    transform: translateY(-50%);
    height: 24px;
    width: 1px;
    background: #8F8F8F;
  }

</style>
