import Vue from 'vue'
import Router from 'vue-router'
import {iEncrpt,iEncrptParamMap} from '@/base/encrptH5.js'
import {decrptParam,digitAldSign} from '@/base/AesEncrptUtil.js'
import axios from 'axios'
import 'mint-ui/lib/style.css'

import Storage from '@/base/storage'
import { Toast,DatetimePicker,Indicator } from 'mint-ui';
import MessageBox from 'components/common/NlMessageBox/message-box.js';
import Main from '@/pages/handhall/Index.vue';
import Page404 from '@/pages/handhall/Page404';
// import OrderIntegratedEntry from '@/pages/handhall/OrderIntegratedEntry.vue';
// import WorkOrderQuery from '@/pages/handhall/WorkOrderQuery.vue';

import SmartStore from 'components/business/shopScene/SmartStore.vue';
const PaperPdf = () => import('components/common/PaperPdf.vue')

import {CONSTVAL} from '@/base/config'
import '../../assets/css/iconfont.css'
import '@/base/less/reset.css'
import {getRealUrl} from '@/base/utils'
const danghuanCryptoJS = require('@/base/danghuan/encrypt.js')
let encrypt = danghuanCryptoJS.Encrypt;
let decrypt = danghuanCryptoJS.Decrypt;

//如果需要防止重复点击 请给相应的元素添加样式needsclick 例 class=" needsclick"
import  '@/base/fastclick.js'
FastClick.attach(document.body,{tapDelay:4000})
var deviceWidth = document.documentElement.clientWidth || window.screen.availWidth;

Vue.use(Router);
document.documentElement.style.fontSize = (deviceWidth / 375)* 100 +'%';//Math.min((deviceWidth / 375),1) * 100 +'%';

document.title = '家庭业务';
window.onresize = function(){
    deviceWidth = document.documentElement.clientWidth || window.screen.availWidth;
    document.documentElement.style.fontSize = (deviceWidth / 375)* 100 +'%';//Math.min((deviceWidth / 375),1) * 100 +'%';
}


function getQueryVariable(){
    let query = window.location.search.substring(1);
    let paramList = query.split("=");
    let qryMap = {};
    if("param" == paramList[0]){
        let json = decrypt(paramList[1]);
        let obj = eval('(' + json + ')');
        for(let k in obj ){
            qryMap[k] = obj[k];
        }
    }
    return qryMap;
}
let page404 = false;
//获取客户端请求入口的参数
function getInitData(){
    let qryMap = getQueryVariable();
    let info = qryMap;
    let uinfo = {};
    let jqData ={};
    if(info.telnum){
        // uinfo.telnum = telnum;
        // uinfo.servNumber = "13814079660";
        // uinfo.region = '14';
        // uinfo.crmId = '14095583';
        // uinfo.stationId = '9988020036375130';//岗位编码 地市集团支撑人员
        // uinfo.staffId = '100000070887';
        // uinfo.tokenid = "";
      uinfo.telnum = info.telnum;
      uinfo.crmId = info.crmId;
      uinfo.type = info.type;
      uinfo.region = info.crmId.substring(0,2);
      uinfo.tokenid = "";
      jqData = {
        jqType: '',   //0:服务密码，1:验证码，2:身份证
        result: '0',             //鉴权结果，1为成功
        authtype:'',
        telnum: info.telnum
      };
    } else {
        page404 = true;
    }
    Storage.session.set('userInfo',uinfo);
    Storage.session.set('jqData',jqData);
    Storage.set('webUrl',getRealUrl('center2'));
    axios.defaults.headers.tokenid = uinfo.tokenid;
}
getInitData();


axios.defaults.timeout = 30000;//默认30秒

axios.interceptors.request.use(
    config => {
        let method = config.method;
        let url = config.url;
        if(method == 'get'){
            if(!config.unLoadFlg){
                Indicator.open('加载中...');
            }
        } else {
            if(!config.data || !config.data.unLoadFlg ) {//如果data中传了unLoadFlg：true 则不展示加载圈，传false或者不传都展示
                Indicator.open('加载中...');
            }
        }
        if(process.env.NODE_ENV === 'development'){
            // //开发联调环境需要配置跨域标识
            if(~url.indexOf('/xsb')){//行商宝新框架
                url = '/apiM' + url;
            }else {
                url = '/apiOld' + url;
            }
        }
        //不需要加crmID
        if(~url.indexOf('/xsb/api-user/appVersionCenter/appVersionInfo')){
        } else {
            let uinfo =  Storage.session.get('userInfo');
            if(method == 'get'){
                if(~url.indexOf('?')){//URL链接中有?，则用&连接
                    url += '&';
                }else {
                    url += '?';
                }

                url += `staffId=${uinfo.staffId}&region=${uinfo.region}&crmId=${uinfo.crmId}&servNumber=${uinfo.servNumber}`;
                if(uinfo.relTelnum){
                    url += `&studentPhone=${uinfo.relTelnum}`;
                }
            } else {
                if(!config.data){
                    config.data = {};
                }
                config.data.staffId = uinfo.staffId;
                config.data.region = uinfo.region;
                config.data.crmId = uinfo.crmId;
                config.data.servNumber = uinfo.servNumber;
                if(uinfo.relTelnum){
                    config.data.studentPhone = uinfo.relTelnum;
                }
            }
            if(!config.data || !config.data.unEncrpt){//如果data中传了unEncrpt：true 则不加密，传false或者不传都默认加密
                if(method == 'get'){
                    if(!config.unEncrpt){
                        url = iEncrpt(url);
                    }
                }else {
                    config.data = iEncrptParamMap(config.data);
                }

            }
        }

        config.url = url;
	    return config;
	},
	err => {
	    return Promise.reject(err)
})

// http响应拦截器
axios.interceptors.response.use(res => {
    Indicator.close();// 响应成功关闭loading
  let resData =res.data;
  if (typeof resData === 'string') {
    try {
      JSON.parse(resData);//明文返回的报文字符串是可以解析成json的
      //验证明文签名的情况
      validateRespSign(res);
      // return res;
    } catch (e) {
      //不可解析的表示是密文
      try{//解决阿拉盯端调用泛渠道的接口未解密，导致前端报错
        let data = decrptParam(resData);//aes解密，遇到旧业务des加密的会catch异常
        res.data = data;
      } catch(err){
        //Toast('统一解密异常：' + err);
      }
      validateRespSign(res);
    }
  }

    //鉴权失败状态码9999
    if(res.data.retCode =='9999'){
        // //请求客户端重新登录
        // ClientJs.autologinInterface('refreshUserInfo');
        MessageBox.alert(res.data.retMsg,'温馨提示')
        .then((action) => {

        }).catch(() => {});

    } else {
        return res;
    }
  }, error => {
    Indicator.close();
    if(error.code == 'ECONNABORTED' && error.message.indexOf('timeout')!=-1){
        if(res.config && res.config.headers && res.config.headers.tokenid){
            MessageBox.alert('服务器忙，请稍候再试！','温馨提示');
        } else {
            return Promise.reject(error);
        }
    }else{
        return Promise.reject(error)
    }
})

function validateRespSign(res){
  let resData = res.data;
  let respSign = res.headers && res.headers['respsign'];
  try{
    if(respSign){//如果服务端对返回报文签名了
      let h5Sign = digitAldSign(resData);
      if(h5Sign == respSign){
        res.data = JSON.parse(resData);
      } else {
        res.data = {'retCode':'4444','retMsg':'非法请求'}
      }
    } else {
      res.data = JSON.parse(resData);
    }
  }catch(e){
    //无法转json,是需要相关业务代码二次解密
  }
}

const install = function(Vue, config = {}) {
    if (install.installed) return;
    Vue.config.productionTip = false
    //日期控件
    Vue.component(DatetimePicker.name, DatetimePicker);


    // 调用 提示框
    Vue.prototype.$messagebox = MessageBox;

    //alert对话框
    Vue.prototype.$alert = (msg,title) =>{
        if( title === void 0 || title === '' ){
            title = '温馨提示';
        }
        if(msg && msg.length > 300){
            msg = msg.substr(0,300)
        }
        Vue.prototype.$messagebox.alert(msg,title);
    }

    Vue.$toast = Vue.prototype.$toast = Toast;

    //异步查询
    Vue.prototype.$http = axios;
    Vue.prototype.CONSTVAL = CONSTVAL;
}
// auto install
if (typeof window !== 'undefined' && window.Vue) {
  install(window.Vue);
};






var router = new Router({
    routes: [
        {
            path:'/',
            redirect: '/main'
        },
        {
            path: '/main',
            component: Main
        },
      {
        path: '/smartStore',
        component: SmartStore
      },
      {
        path: '/paperPdf',
        component: PaperPdf
      },
        // {
        //     path: '/orderIntegratedEntry',
        //     component: OrderIntegratedEntry
        // },
        // {
        //     path: '/workOrderQuery',
        //     component: WorkOrderQuery
        // }
    ]
});
if(page404){
    new Vue({
        el: '#app',
        router,
        components: { Page404 },
        template: '<Page404/>'
    })
} else {
    new Vue({
        el: '#app',
        router,
        //components: { Main },
        template: '<div><router-view></router-view></div>'
    })
}
