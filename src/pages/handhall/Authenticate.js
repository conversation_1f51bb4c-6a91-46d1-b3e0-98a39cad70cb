//import Vue from 'vue'
import Authenticate from './Authenticate.vue'

const AuthenticateConstructor = Vue.extend(Authenticate);

let authenticatePool = [];
let getAnInstance = () => {
    if (authenticatePool.length > 0) {
        let instance = authenticatePool[0];
        authenticatePool.splice(0, 1);
        return instance;
    }
    return new AuthenticateConstructor({
        el: document.createElement('div')
    });
};

let returnAnInstance = instance => {
    if (instance) {
        authenticatePool.push(instance);
    }
};

AuthenticateConstructor.prototype.close = function() {
    document.body.removeChild(this.$el);
    this.popFlag = false;
    returnAnInstance(this);
};

let Authenct = (options = {}, callback) => {

    let instance = getAnInstance();
    instance.callback = callback;
    instance.popFlag = options.popFlag;
    instance.hasPwd = options.hasPwd;
    if(instance.hasPwd == void 0){
        instance.hasPwd = '1';//如果没有设置hasPwd 默认需要输入密码
    }
    instance.serverType.telnum = options.telnum;
    instance.validateType.telnum = options.telnum;
    instance.dimTelnumFlg = options.dimTelnumFlg;
    instance.readOnlyFlag = options.readOnlyFlag;
    instance.onlyTelnum = options.telnum;//没有密码框的弹出框设置手机号默认值
    instance.noSendPassFlag = options.noSendPassFlag;
    document.body.appendChild(instance.$el);
    Vue.nextTick(() => {
        instance.popFlag = true;
    });

    return instance;
};

export default Authenct;
