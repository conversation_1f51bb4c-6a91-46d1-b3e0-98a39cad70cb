<template>
<div v-show="popFlag"  @click.stop="jqPopClose">
    <div class="authenct">
        <div class="pop-main" @click.stop v-if="hasPwd=='1'">
            <ul class="pjq-tab">
                <li class="pjq-tab-li" :class="{active:tabIndex==0}" @click="jqTab(0)">服务密码</li>
                <li class="pjq-tab-li" :class="{active:tabIndex==1}" @click="jqTab(1)" >短信验证码</li>
            </ul>
            <!--服务密码方式-->
            <div class="pop-mslis" v-show="tabIndex==0">
                <div class="pjq-ipt tel" :class="{err:serverType.telErrFlag}">
                    <input type="tel" maxlength="13" placeholder="请输入手机号码或宽带账号"
                           v-model="dimTelPhone" :readOnly="readOnlyFlag"/>
                </div>
                <div class="pjq-ipt telpwd" :class="{err:serverType.passwordFlag}">
                    <input type="password" placeholder="请输入服务密码" maxlength="6"
                           v-model="serverType.password" />
                </div>
            </div>
            <!--验证码方式-->
            <div class="pop-mslis" v-show="tabIndex==1 && !auth5GFlg">
                <div class="pjq-ipt tel" :class="{err:validateType.telErrFlag}">
                    <input type="tel" placeholder="请输入手机号码" maxlength="13"
                           v-model="dimTelPhone" :readOnly="readOnlyFlag"/>
                </div>
                <div class="pjq-ipt bordernone">
                    <div class="pjq-iptbtn" @click="sendValiCode" :class="{gray:noSendFlag}">
                        <span class="pjq-ipb-send">发送验证码</span>
                        <span class="pjq-ipb-nosend">{{sCountDown}}s后可重发</span>
                    </div>
                    <div class="pjq-iptleft validcode" :class="{err:validateType.valCodeFlag}">
                        <input type="text" placeholder="请输入验证码"
                               v-model="validateType.valCode" />
                    </div>
                </div>
            </div>

            <div class="pop-bottom">
                <div class="pop-blis" @click="jqClose">关闭</div>
                <div class="pop-blis needsclick"
                :class="[canClickFlg?'active':'deactive']"
                 @click="jqSure" @touchstart="jqTouchStart" @touchend="jqTouchEnd">确定</div>
            </div>
        </div>
    </div>
    <div class="modal"></div>
</div>
</template>

<script>
    import Storage from '@/base/storage'
    import {decrptParam} from '@/base/encrptH5.js'
    export default {

        data() {
            return {
                tabIndex: 0,                                     //鉴权tab
                serverType: {                                    //服务密码方式
                    telnum: '',
                    telErrFlag: false,                           //手机号是否正确
                    password: '',
                    passwordFlag: false,                              //密码格式是否正确

                },
                validateType: {                                  //验证码方式
                    telnum: '',                                  //手机号
                    telErrFlag: false,                           //手机号是否正确
                    valCode: '',                                 //验证码
                    valCodeFlag: false,                          //验证码是否正确
                },

                sCountDown: 30,                                  //重新发送倒计时时间（默认30s）
                noSendFlag: false,                               //发送验证码样式置灰色
                url: '/xsb/personBusiness/customerView/h5IdentifyUserYanShou',       //鉴权url
                popFlag: true,
                hasPwd:'',
                readOnlyFlag: false,//是否只读
                onlyTelnum:'',//只有手机号
                onlyTelErrFlag:false,
                canClickFlg:true,//是否能点击
                noSendPassFlag:"",//发送验证码是否存在
            }
        },
        created(){
        },
        computed:{
            //模糊化联系人电话
            dimTelPhone:{
                get(){
                    let tel = this.serverType.telnum;//0:服务密码
                    if(this.tabIndex == 1){//验证码鉴权
                        tel = this.validateType.telnum;
                    }
                    if (this.dimTelnumFlg && tel && tel.length == 11) {//模糊化手机号
                        let reg = /^(\d{3})\d*(\d{4})$/;
                        tel = tel.replace(reg, '$1****$2');
                    }
                    return tel;
                },
                set(value){
                    this.serverType.telnum = value;
                    this.validateType.telnum = value;
                    this.idCardType.telnum = value;
                }
            }
        },
        methods: {
            //鉴权弹框tab
            jqTab(d) {
                this.canClickFlg = true;
                //0:服务密码，1:验证码，2:身份证
                this.tabIndex = d;
            },
            //鉴权关闭按钮点击
            jqClose() {
                this.clearFormData();
                this.pollFlg = false;
                clearInterval(this.pollTimer);
                this.close();
            },
            goTool(){
                this.popFlag = false;
                let _this = this;
                //this.$alert('请先前往工具页面进行实名补登记')
                this.$messagebox.alert('该号码状态是未审核，请使用实名制后再进行业务受理', '温馨提示').then(action => {
                    _this.$router.push('/tools');
                })
            },

            //请求服务端鉴权
            doIdentifyUser(url,telnum,pwd,authtype,obj){
                //authtype 验证类型 06服务密码登录 07验证码 00二代证;
                this.popFlag = false;

                this.$http.get(url).then((res) => {
                    let resData = res.data;
                    if(typeof(resData) != "object"){//不是对象的话就是加密串
                        resData = decrptParam(res.data);//解密
                        resData = JSON.parse(resData);//转换成对象
                    }
                    let {retCode,retMsg,data} = resData;
                    this.canClickFlg = true;
                    if (retCode == '0') {
                        let realstatecode = data.realstatecode;
                        if(realstatecode === '4'){//需要实名制审核通过
                            obj.telnum = telnum;
                            obj.authtype = authtype;
                            obj.userName = data.userName;
                            obj.dynamicKey = data.dynamicKey;
                            obj.userCity = data.region; //用户地市 add 20220729 qp

                            this.clearFormData();
                            Storage.session.set('jqData',obj);
                            this.callback(obj);
                        } else {
                            this.goTool();
                        }

                    } else {
                        this.$toast({
                            message: retMsg||'鉴权失败',
                            position: 'bottom',
                        });
                        this.popFlag = true;
                    }
                }).catch((err) => {
                    this.$alert('鉴权异常：' + err);
                })
            },
            //验证手机号 、密码是否正确
            validateTelAndPwd(tabObj,pwdKey,pwdRule){
                let result = true;
                if (!/^\d{11,13}$/.test(tabObj.telnum)) {
                    tabObj.telErrFlag = true;
                    result = false;
                } else {
                    tabObj.telErrFlag = false;
                }

                if (!pwdRule.test(tabObj[pwdKey])) {
                    tabObj[pwdKey+'Flag'] = true;
                    result = false;
                } else {
                    tabObj[pwdKey+'Flag'] = false;
                }
                return result;
            },
            //鉴权弹框确定按钮点击
            jqSure() {
                if(!this.canClickFlg){
                    return;
                }
                this.canClickFlg = false;
                let obj = {
                    jqType: this.tabIndex,   //0:服务密码，1:验证码，2:身份证
                    result: '1',             //鉴权结果，1为成功
                };
                let authtype = '06';//默认服务密码
                let curObj = this.serverType;//默认服务密码
                let pwdRule =/^(\d{6})$/;//服务密码验证规则
                let pwd = this.serverType.password;
                let url = `${this.url}?telnum=${curObj.telnum}&idCardType=06&idCardNum=${pwd}`;
                let pwdKey = 'password';

                //验证码
                if (this.tabIndex == 1) {

                    authtype = '07';//验证码鉴权
                    curObj = this.validateType;//短信验证码
                    pwdRule = /\S/;
                    pwd = this.validateType.valCode;
                    pwdKey = 'valCode';
                    url = `${this.url}?telnum=${curObj.telnum}&idCardType=07&idCardNum=&randomNum=${pwd}`;
                }
                if (this.validateTelAndPwd(curObj,pwdKey,pwdRule)) {
                    this.doIdentifyUser(url,curObj.telnum,curObj[pwdKey],authtype,obj);
                } else {
                    this.canClickFlg = true;
                }
            },


            //清空表单
            clearFormData() {
                this.tabIndex = 0;
                this.serverType.password = '';
                this.serverType.telErrFlag = false;
                this.serverType.passwordFlag = false;
                this.validateType.telErrFlag = false;
                this.validateType.valCode = '';
                this.validateType.valCodeFlag = false;
                this.onlyTelErrFlag = false;
            },

            //弹框空白处点击
            jqPopClose() {
                this.clearFormData();
                this.close();
            },
            //发送验证码点击
            sendValiCode() {
                let _this = this;
                //判断有没有输入手机号
                if (!/^((1)+\d{10})$/.test(this.validateType.telnum)) {
                    this.validateType.telErrFlag = true;
                } else {
                    //在倒计时内，不允许重复请求
                    if (!_this.noSendFlag) {
                        let url = `/xsb/personBusiness/customerView/h5SendVerifyCodeYanShou?telnum=${_this.validateType.telnum}`;
                        this.$http.get(url).then((res) => {
                            if (res.data.retCode == '0') {
                                _this.$toast('短信发送成功，注意查收');
                                _this.noSendFlag = true;
                                let timer = setInterval(() => {
                                    _this.sCountDown--;
                                    if (_this.sCountDown <= 0) {
                                        _this.noSendFlag = false;
                                        _this.sCountDown = 30;          //恢复初始时间
                                        clearInterval(timer);
                                    }
                                }, 1000);
                            }else{
                                _this.$toast({
                                    message: res.data.retMsg ||'短信发送失败',
                                    position: 'bottom',
                                });
                            }
                        }).catch((response) => {
                        })
                    }
                }

            },

            //鉴权确定按钮touchstart
            jqTouchStart(e){
                let ele = e.target;
                ele.style.background = '#d5d9dc';
            },

            //鉴权确定按钮touchend
            jqTouchEnd(e){
                let ele = e.target;
                ele.style.background = '';
            },
        },
        destroyed(){
        },
        mounted(){
        }
    }
</script>

<style scoped lang="less">
    @import '../../base/less/variable.less';

    .authenct {
        // position: absolute;
        // top: 0px;
        // left: 0px;
        // right: 0px;
        // bottom: 0px;
        position: absolute;
        z-index: 2001;
        background: rgba(0, 0, 0, 0.4);
    }

    .pop-main {
        position: fixed;
        top: 50%;
        left: 50%;
        background-color: #fff;
        width: 85%;
        border-radius: 7px;
        transform: translate3d(-50%, -50%, 0);/*oppoR7 不支持*/
        transition: .2s;
    }

    .pop-main h4 {
        text-align: center;
        margin-top: 22px;
        overflow: hidden;
        font-size: 18px;
        color: #000;
        font-weight: 400;
    }

    .cs-banner {
        height: auto;
        border-bottom: 1px solid #F0F0F0;
        border-top: 1px solid #F0F0F0;
    }

    .cs-bannernei {
        margin-left: 16px;
        margin-right: 16px;
        margin-top: 16px;
        overflow: hidden;
        padding-bottom: 30px;
    }

    .pjq-iptbtn {
        width: 106px;
        height: 32px;
        background: rgba(0, 122, 255, 1);
        font-size: 14px;
        line-height: 32px;
        font-weight: 400;
        text-align: center;
        color: rgba(255, 255, 255, 1);
        float: right;
        border-radius: 4px;
    }

    .pop-mslis {
        height: auto;
        overflow: hidden;
        min-height: 130px;
    }

    .pjq-tab {
        height: 30px;
        line-height: 30px;
        border-bottom: 1px solid #D5D5D5;
        margin-top: 20px;
        display: flex;
        justify-content: space-around;
    }

    .pjq-tab-li {
        display: block;
        height: 30px;
        text-align: center;
        width: 33.33%;
        font-size: 16px;
        color: #C2C3C3;

    }

    .pjq-tab-li.active {
        color: #0C78FA;
        font-weight: 600;
    }

    .pjq-iptbtn {
        width: 106px;
        height: 32px;
        background: rgba(0, 122, 255, 1);
        font-size: 14px;
        line-height: 32px;
        font-weight: 400;
        text-align: center;
        color: rgba(255, 255, 255, 1);
        float: right;
    }

    .pop-mslis {
        height: auto;
        overflow: hidden;
    }

    .pjq-ipt {
        height: 33px;
        position: relative;
        margin: 20px 24px;
        border: 1px solid #CECECE;
        border-radius: 4px;
    }

    .pjq-ipt input {
        background: none;
        border: none;
        height: 23px;
        line-height: 23px;
        margin-top: 6px;
        width: 100%;
        outline: none;
        text-indent: 6px;
        font-size: 14px;
        color: #333;
    }

    .pjq-ipt.bordernone {
        border: none;
    }

    .pjq-ipt.tel.err {
        border: 1px solid red;
    }

    .pjq-ipt.tel.err:before {
        content: '手机格式不正确';
        height: 12px;
        position: absolute;
        top: 38px;
        left: 0px;
        font-size: 10px;
        color: red;
        line-height: 12px;
    }

    .pjq-ipt.telpwd.err {
        border: 1px solid red;
    }

    .pjq-ipt.telpwd.err:before {
        content: '服务密码格式不正确';
        height: 12px;
        position: absolute;
        top: 38px;
        left: 0px;
        font-size: 10px;
        color: red;
        line-height: 12px;
    }

    .pop-bottom {
        height: auto;
        border-top: 1px solid #D5D5D5;
    }

    .pop-blis {
        width: 50%;
        height: 50px;
        line-height: 50px;
        float: left;
        box-sizing: border-box;
        text-align: center;
        font-size: 18px;
        color: #000;
        border-right: 1px solid #DCDEE3;
        &.deactive{
            color:#C2C3C3;
        }
        &.active {
            color: #108EE9;
        }
    }

    .pop-blis:last-child {
        border-right: none;
    }


    .pjq-iptleft {
        margin-right: 114px;
        height: 32px;
        border: 1px solid rgba(206, 206, 206, 1);
        box-sizing: border-box;
        border-radius: 4px;
    }

    .pjq-iptleft input {
        border: none;
        outline: none;
        margin-top: 3px;
    }

    .pjq-iptleft.validcode.err {
        border: 1px solid red;
    }

    .pjq-iptleft.validcode.err:before {
        content: '验证码输入有误';
        height: 12px;
        position: absolute;
        top: 38px;
        left: 0px;
        font-size: 10px;
        color: red;
        line-height: 12px;
    }

    .pjq-iptbtn.gray {
        background: #b1adad;
    }

    .pjq-ipb-nosend {
        display: none;
    }

    .pjq-iptbtn.gray .pjq-ipb-send {
        display: none;
    }

    .pjq-iptbtn.gray .pjq-ipb-nosend {
        display: block;
    }
    .cs-vfpop-title{
        height: 45px;
        line-height: 45px;
        padding-left: 16px;
        border-bottom: 1px solid #eee;
    }
    .modal {
        position: fixed;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        opacity: 0.5;
        z-index: 2000;
        background: #000;
    }
    .client-version-tip{
        color: #f86e21;
        font-size: 14px;
        padding-left: 24px;
    }
    .msg-5g-div{
        margin: auto;
        text-align: center;
    }
    .msg-5g{
        height: 34px;
        background: #e5ebf3;
        border-radius: 8px;
        line-height: 34px;
        display: inline-block;
        padding: 0 12px;
        margin-top: 40px;
    }
</style>
