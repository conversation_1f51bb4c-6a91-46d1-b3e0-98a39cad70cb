<template>
  <div class="tool-wrap"  @click="jumpUrl">
    <div class="tool-content-wrap">
      <!--      <section class="func-modules" v-for="(item,index) in dataMap" :key="index">-->
      <!--        <h4 class="seciton-title" style=" margin-bottom: 16px;">{{item.name}}</h4>-->
      <!--        <ul class="hand-list">-->
      <!--          <li v-for="(item,index) in item.list" :key="index" @click="jumpUrl(item)">-->
      <!--            <img :src="item.background" class="icon-img" />-->
      <!--            <span class="icon-name">{{item.text}}</span>-->
      <!--          </li>-->
      <!--        </ul>-->
      <!--      </section>-->
      <h4 class="seciton-title" style="margin: 30px;font-size: 25px;color: #0664ec;">沿街商铺</h4>-
      <!-- <img   src="static/yearbill/2021/countbusiness-bottom.gif"  style="width: 100%;">
      <img   src="static/yearbill/2021/page4-blue.png"  style="width: 100%;position: fixed;left:0;bottom:0;"> -->
      <button class="op-button" @click="dabao" >点 击 鉴 权</button>

    </div>
  </div>
</template>



<script>
    import Storage from '@/base/storage'
    import Authenct from './Authenticate'

    export default {
        data() {
            return {
                dataMap:[
                    { name:'营销专区',
                        list:[
                            // {'text':'融合订购','background':'static/business/10024.png','routePath':'/orderIntegratedEntry'},
                            // {'text':'工单查询','background':'static/business/100040.png','routePath':'/workOrderQuery'},

                            {'text':'智慧店铺','background':'static/business/10024.png','routePath':'/smartStore?type=dian'},
                            {'text':'阳光厨房','background':'static/business/100040.png','routePath':'/smartStore?type=yang'},
                            {'text':'远程巡店','background':'static/business/100040.png','routePath':'/smartStore?type=lang'},
                        ]
                    }
                ],

            }
        },
        created(){
            this.jumpUrl();
        },
        methods:{
            //跳转URL
            jumpUrl(){
                let self = this;
                let jqData=Storage.session.get('jqData');
                Authenct({
                        popFlag: true,
                        idCardWay: false,//是否需要身份证鉴权,
                        readOnlyFlag: true,
                        telnum: jqData.telnum,
                    }, function (obj) {
                        self.$router.push({
                            path:'/smartStore',
                            query: {
                                srcFrom:'/main',
                                type:Storage.session.get('userInfo').type,
                            }
                        });
                    });

            },
        },

    }
</script>

<style lang="less" scoped>
  .tool-wrap{
    flex: 1;
    overflow: auto;
    display:flex;
    flex-direction:column;
    background-color:#fff;
    text-align: center;
    .tool-content-wrap{
      flex:1;
      .func-modules{
        overflow: hidden;
        height: auto;
        margin: 0 16px;
        padding: 12px 0 16px 0;
        border-bottom: 1px solid #F0F0F0;
        margin-bottom: 16px;
        .hand-list {
          text-align: center;
          li {
            float: left;
            width: 25%;
            .icon-img {
              width: 50%;
              margin-bottom: 4px;
            }
            .icon-name {
              display: block;
              font-size: 12px;
              color: @color-TextContent;
            }
          }
          li:nth-child(n+5){
            margin-top:16px;
          }
        }
      }
    }

    .op-button {
      border-radius: 22px;
      height: 44px;
      width: 80%;
      font-size: 14px;
      outline: none;
      border:1px solid rgba(204,204,204,1);
      background: #1681FB;
      color:#fff;
    }

  }
</style>
