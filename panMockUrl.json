{"pan": [{"url": "/xsb/api-pancUser/register/h5sendCode", "method": "get", "data": "successRes"}, {"url": "/xsb/api-pancUser/register/h5checkMobile", "method": "get", "data": "pancregister.h5checkMobile"}, {"url": "/xsb/api-pancUser/register/h5queryPancChanBossInfo", "method": "get", "data": "pancregister.h5queryPancChanBossInfo"}, {"url": "/xsb/api-pancUser/password/h5sendCode", "method": "get", "data": "successRes"}, {"url": "/xsb/api-pancUser/password/h5checkMobile", "method": "get", "data": "pancregister.passCheckMobile"}, {"url": "/xsb/api-pancUser/password/h5queryPancChanBossInfo", "method": "get", "data": "pancregister.h5queryPancChanBossInfo"}, {"url": "/xsb/api-pancUser/appVersionCenter/h5appVersionInfo", "method": "post", "data": "pdata01.appVersionInfo"}, {"url": "/xsb/api-pancUser/businessList/h5qryOperatorList", "method": "get", "data": "busiSrlData.h5qryOperatorList"}, {"url": "/xsb/api-pancUser/businessList/h5qryTypeTotalBusi", "method": "get", "data": "busiSrlData.h5qryTypeTotalBusi"}, {"url": "/xsb/api-pancUser/businessList/h5qryBusiList", "method": "get", "data": "busiSrlData.h5qryBusiList"}, {"url": "/xsb/api-pancUser/orderManage/h5qryPancBusiType", "method": "get", "data": "pdata01.h5qryPancBusiType"}, {"url": "/xsb/api-pancUser/orderManage/h5qryOrderList", "method": "post", "data": "pdata01.h5qryOrderList"}, {"url": "/xsb/api-pancUser/orderManage/h5qryOrderDetail", "method": "get", "data": "pdata01.h5qryOrderDetail"}, {"url": "/xsb/api-pancUser/businessCommon/h5GetSubmitInfo", "method": "get", "data": "pdata01.qcodeSubmitInfo"}, {"url": "/xsb/api-pancUser/businessCommon/h5StoreSubmitInfo", "method": "post", "data": "pdata01.h5storeSubmitInfo"}, {"url": "/xsb/api-pancUser/businessCommon/h5GetYeWuStatus", "method": "get", "data": "pdata01.h5GetYeWuStatus"}, {"url": "/xsb/api-pancUser/pancuser/h5qryLoginRecord", "method": "get", "data": "pdata01.h5qryLoginRecord"}, {"url": "/xsb/personBusiness/creditLoanPanc/h5GetPancOrders", "method": "get", "data": "pdata01.h5GetPancOrders"}, {"url": "/xsb/personBusiness/creditLoanPanc/h5pancCommit", "method": "post", "data": "pdata01.h5pancCommit"}, {"url": "/xsb/api-pancUser/businessCommon/h5SendBusiVerifyCode", "method": "post", "data": "successRes"}, {"url": "/xsb/api-pancUser/register/h5updateUserInfo", "method": "post", "data": "successRes"}, {"url": "/xsb/api-pancUser/register/h5uploadFile", "method": "post", "data": "pdata01.h5uploadFile"}, {"url": "/xsb/api-pancUser/businessCommon/h5SendBusiConfirm", "method": "post", "data": "successRes"}, {"url": "/xsb/personBusiness/pancBandOpen/h5pancCommit", "method": "post", "data": "successRes"}, {"url": "/xsb/api-pancUser/password/h5updPassword", "method": "post", "data": "successRes"}, {"url": "/xsb/api-pancUser/updateUser/h5UpdateUserInfo", "method": "get", "data": "pancregister.h5UpdateUserInfo"}, {"url": "/xsb/api-pancUser/changePassword/h5updPassword", "method": "post", "data": "pancregister.h5updPassword"}, {"url": "/xsb/api-pancUser/changePassword/h5checkLock", "method": "get", "data": "pancregister.h5checkLock"}, {"method": "get", "url": "/xsb/personBusiness/pancAdminister/h5qryMenuFeeInfos", "data": "pancControllor.h5qryMenuFeeInfos"}, {"method": "get", "url": "/xsb/personBusiness/pancAdminister/h5updatePancMenuFeeInfos", "data": "successRes"}, {"method": "get", "url": "/xsb/personBusiness/pancAdminister/h5delPancMenuFeeInfos", "data": "successRes"}, {"method": "post", "url": "/xsb/personBusiness/pancAdminister/h5QryPancList", "data": "pancControllor.h5QryPancList"}, {"method": "post", "url": "/xsb/personBusiness/pancAdminister/h5AddPanc", "data": "successRes"}, {"method": "post", "url": "/xsb/personBusiness/pancAdminister/h5UpdatePanc", "data": "successRes"}, {"method": "post", "url": "/xsb/personBusiness/pancAdminister/h5DelPanc", "data": "successRes"}, {"method": "get", "url": "/xsb/personBusiness/pancAdminister/h5qryMenuFee", "data": "pancControllor.h5qryMenuFee"}, {"method": "post", "url": "/xsb/personBusiness/pancAdminister/h5addMenuFee", "data": "successRes"}, {"method": "get", "url": "/xsb/api-pancUser/updateUser/h5qryPancUserInfo", "data": "pancControllor.h5qryPancUserInfo"}, {"method": "post", "url": "/xsb/api-pancUser/updateUser/h5qryPancUserInfoWithChannelId", "data": "successRes"}, {"method": "get", "url": "/xsb/api-pancUser/pancuser/h5qryLoginRecord", "data": "pdata01.h5qryLoginRecord"}, {"method": "get", "url": "/xsb/api-pancUser/businessCommon/h5ResetQRCode", "data": "successRes"}, {"method": "post", "url": "/xsb/personBusiness/pancPreTel/h5pancPreTelCheck", "data": "pdata01.h5pancPreTelCheck"}, {"method": "post", "url": "/xsb/personBusiness/pancPreTel/h5PancPreTelCommit", "data": "pdata01.h5PancPreTelCommit"}, {"method": "get", "url": "/xsb/api-user/location/h5qryLocation", "data": "aldData01.h5qryLocation"}, {"method": "post", "url": "/xsb/api-pancUser/appVersionCenter/appVersionInfo", "data": "pdata01.appVersionInfo"}, {"method": "get", "url": "/xsb/personBusiness/pancMarketPackage/h5QryCustomMarketPkg", "data": "pdata01.h5QryCustomMarketPkg"}, {"method": "get", "url": "/xsb/personBusiness/pancOrderedList/h5qryBusiTypeClass", "data": "pdata01.h5qryBusiTypeClass"}, {"method": "get", "url": "/xsb/personBusiness/pancOrderedList/h5qryBusiTypes", "data": "pdata01.h5qryBusiTypes"}, {"method": "post", "url": "/xsb/personBusiness/pancOrderedList/h5recordOrderedList", "data": "pdata01.h5recordOrderedList"}, {"method": "get", "url": "/xsb/personBusiness/pancOrderedList/h5listOrderedInfo", "data": "pdata01.h5listOrderedInfo"}, {"method": "get", "url": "/xsb/personBusiness/pancOrderedList/h5cancelOrderedInfo", "data": "pdata01.h5cancelOrderedInfo"}, {"method": "get", "url": "/xsb/personBusiness/pancOrderedList/h5qryBusitype", "data": "pdata01.h5qryBusitype"}, {"method": "post", "url": "/xsb/personBusiness/fifthGMessage/h5useSendFifthGMsg", "data": "pdata01.h5useSendFifthGMsg"}, {"method": "get", "url": "/xsb/personBusiness/fifthGMessage/h5qryAuthenRecord", "data": "successRes"}, {"method": "get", "url": "/xsb/switchUrl", "data": "pdata01.h5switchLog"}, {"method": "post", "url": "/xsb/ability/trace/h5qryTraceDictInfo", "data": "pdata01.h5qryTraceDictInfo"}, {"method": "post", "url": "/xsb/ability/common/h5getSeqByName", "data": "pdata01.getSeqByName"}, {"method": "post", "url": "/xsb/ability/trace/h5receiveTrace", "data": "successRes"}, {"method": "post", "url": "/xsb/ability/trace/h5receiveTraceNew", "data": "successRes"}, {"method": "post", "url": "/xsb/personBusiness/promotionCenter/h5queryPromotionList", "data": "appData.h5queryPromotionList"}, {"method": "get", "url": "/xsb/personBusiness/fifthGMessage/h5qryAuthenRecord", "data": "failRes"}, {"method": "get", "url": "/xsb/personBusiness/pancOrderedList/h5qryBusitype", "data": "pdata01.h5qryBusitype"}, {"method": "get", "url": "/xsb/api-pancUser/pancMenu/h5getGroupMenu", "data": "pdata01.h5getGroupMenu"}, {"method": "get", "url": "/xsb/personBusiness/groupPancAllInOne/h5qryGroupOfferingInfo", "data": "pdata01.h5qryGroupOfferingInfo"}, {"method": "get", "url": "/xsb/personBusiness/groupPancAllInOne/h5getYwtGroupCustInfo", "data": "pdata01.h5getYwtGroupCustInfo"}, {"method": "get", "url": "/xsb/personBusiness/groupPancAllInOne/h5querySonProds", "data": "pdata01.h5querySonProds"}, {"method": "get", "url": "/xsb/personBusiness/groupPancAllInOne/h5qryGroupOfferingDetail", "data": "pdata01.h5qryGroupOfferingDetail"}, {"method": "post", "url": "/xsb/personBusiness/groupPancAllInOne/h5groupBusiValidate", "data": "pdata01.h5groupBusiValidate"}, {"method": "post", "url": "/xsb/personBusiness/groupPancAllInOne/h5groupCreateOrder", "data": "pdata01.h5groupCreateOrder"}, {"method": "get", "url": "/xsb/personBusiness/groupPancAllInOne/h5queryContract", "data": "pdata01.h5queryContract"}, {"method": "post", "url": "/xsb/personBusiness/pancOrderedList/h5qryOrderPush", "data": "pdata01.h5qryOrderPush"}, {"method": "post", "url": "/xsb/personBusiness/fifthGMessage/h5judgeTime", "data": "successRes"}, {"method": "get", "url": "/xsb/personBusiness/fifthGHandle/h5GetHandleOrderByOrderId", "data": "pdata01.h5GetHandleOrderByOrderId"}, {"method": "get", "url": "/xsb/personBusiness/productZzOrder/h5getCfgDict", "data": "pdata01.h5getCfgDict"}, {"method": "get", "url": "/xsb/personBusiness/fifthGHandle/h5GetHandleOrderList", "data": "pdata01.h5GetHandleOrderList"}, {"method": "get", "url": "/xsb/api-user/user/getOut5gToken", "data": "pdata01.getOut5gToken"}, {"method": "get", "url": "/xsb/pancSecretBusi/secretVisitTask/h5queryTemplateList", "data": "pdata01.h5queryTemplateList"}, {"method": "get", "url": "/xsb/personBusiness/luckdrawCfg/h5queryPrizeInfos", "data": "pdata01.h5queryPrizeInfos"}, {"method": "get", "url": "/xsb/personBusiness/fifthGHandle/h5recordSignId", "data": "successRes"}, {"method": "post", "url": "/xsb/api-user/tokenAuthUrlCfg/h5qryValStrictUrl", "data": "pdata01.h5qryValStrictUrl"}, {"method": "get", "url": "/chatbot/business/chatbot/qryProductZZByOfferId", "data": "pdata01.qryProductZZByOfferId"}, {"method": "post", "url": "/xsb/personBusiness/personInfo/h5checkUsercust", "data": "successRes"}, {"method": "post", "url": "/xsb/personBusiness/accountCancel/h5accountValidate", "data": "pdata01.h5accountValidate"}, {"method": "post", "url": "/xsb/personBusiness/accountCancel/h5subsReopenValidate", "data": "successRes"}, {"method": "get", "url": "/xsb/personBusiness/makeUpBusi/h5GetOpenAccountMakeUpBusiType", "data": "pdata01.h5GetOpenAccountMakeUpBusiType"}, {"method": "post", "url": "/xsb/personBusiness/guhua/h5getTelList", "data": "pdata01.h5getTelList"}, {"method": "get", "url": "/xsb/personBusiness/transUser/h5transUserCheck", "data": "successRes"}, {"method": "post", "url": "/xsb/personBusiness/guhua/h5telPick", "data": "successRes"}, {"method": "get", "url": "/xsb/personBusiness/cardforcard/h5getCardSIMInfo", "data": "pdata01.h5getCardSIMInfo"}, {"method": "post", "url": "/xsb/personBusiness/cardforcard/h5saveCardInfoToOrder", "data": "successRes"}, {"method": "post", "url": "/xsb/personBusiness/cardforcard/h5CreateOrder", "data": "successRes"}, {"method": "post", "url": "/xsb/ability/trace/h5qryTraceBusiInfo", "data": "pdata01.h5qryTraceBusiInfo"}, {"method": "get", "url": "/xsb/personBusiness/cardforcard/h5checkCardForCardFree", "data": "pdata01.h5checkCardForCardFree"}, {"method": "post", "url": "/xsb/ability/businessLimit/h5qryIfCrmHasAuth", "data": "pdata01.h5qryIfCrmHasAuth"}, {"method": "post", "url": "/xsb/ability/businessLimit/h5qryAuthFaceIdenRerturn", "data": "pdata01.h5qryAuthFaceIdenRerturn"}, {"method": "post", "url": "/xsb/ability/businessLimit/h5whitelistQuery", "data": "successRes"}, {"method": "post", "url": "/xsb/ability/businessLimit/h5qryCsviewAuth", "data": "pdata01.h5qryCsviewAuth"}, {"method": "get", "url": "/xsb/personBusiness/productZzOrder/h5getSchoolProp", "data": "successRes"}, {"method": "post", "url": "/xsb/personBusiness/productZzOrder/h5offerAttrQuery", "data": "successRes"}, {"method": "get", "url": "/xsb/personBusiness/businessOpen/h5qryOperAuth", "data": "successRes"}, {"method": "get", "url": "/xsb/personBusiness/servlet/h5imageServletNew", "data": "successRes"}, {"method": "get", "url": "/xsb/api-user/commonFunc/h5funcQry", "data": "pdata01.h5funcQry"}, {"method": "get", "url": "/xsb/personBusiness/dakaBmmc/h5init", "data": "pdata01.dakaBmmch5init"}, {"method": "post", "url": "/xsb/ability/trueName/h5operCampon", "data": "successRes"}, {"method": "post", "url": "/xsb/ability/businessLimit/h5ReqDynamicRoute", "data": "pdata01.h5ReqDynamicRoute"}, {"method": "post", "url": "/xsb/personBusiness/chooseTelEnterNet/h5ChooseTelnumCheck", "data": "successRes"}, {"method": "post", "url": "/xsb/personBusiness/chooseTelEnterNet/h5QryTaocanByInter", "data": "pdata01.h5QryTaocanByInter"}, {"method": "post", "url": "/xsb/ability/qryAccessNum/h5idcheck", "data": "successRes"}, {"method": "post", "url": "/xsb/ability/recordVideo/h5QueryReadList", "data": "pdata01.h5QueryReadList"}, {"method": "post", "url": "/group/usercenter/bbossAuth/h5bbossAuthNew", "data": "pdata01.h5bbossAuthNew"}, {"method": "post", "url": "/xsb/toolCenter/menuLog/h5menuLogRecord", "data": "successRes"}, {"method": "post", "url": "/xsb/paperless/locationLimit/h5QueryLocationLimit", "data": "successRes"}]}