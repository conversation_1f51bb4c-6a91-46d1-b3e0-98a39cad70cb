<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,user-scalable=no">
    <meta http-equiv="Pragma" content="no-cache">
    <link rel="icon" href="data:;base64,=">
    <script async id="wg-browser-probe" src="static/webgate-browser-probe-3.12.0.min.js" data-appkey="56c0eb48-5446-43c8-8c6e-efd5b152174c" data-appversion="" data-serveraddr="https://spare.jsalading.cn"></script>
    <script src="static/jquery-3.6.4.min.js"></script>
    <script src="/resource/cmcc/group/js/common/moaBridge.min.js"></script>
    <title>阿拉盯</title>
    <style type="text/css">
        #Loading {
            top: 50%;
            left: 50%;
            position: absolute;
            -webkit-transform: translateY(-50%) translateX(-50%);
            transform: translateY(-50%) translateX(-50%);
            z-index: 100;
        }

        @-webkit-keyframes ball-beat {
            50% {
                opacity: 0.2;
                -webkit-transform: scale(0.75);
                transform: scale(0.75);
            }

            100% {
                opacity: 1;
                -webkit-transform: scale(1);
                transform: scale(1);
            }
        }

        @keyframes ball-beat {
            50% {
                opacity: 0.2;
                -webkit-transform: scale(0.75);
                transform: scale(0.75);
            }

            100% {
                opacity: 1;
                -webkit-transform: scale(1);
                transform: scale(1);
            }
        }

        .ball-beat > div {
            background-color: #279fcf;
            width: 15px;
            height: 15px;
            border-radius: 100% !important;
            margin: 2px;
            -webkit-animation-fill-mode: both;
            animation-fill-mode: both;
            display: inline-block;
            -webkit-animation: ball-beat 0.7s 0s infinite linear;
            animation: ball-beat 0.7s 0s infinite linear;
        }

        .ball-beat > div:nth-child(2n-1) {
            -webkit-animation-delay: 0.35s !important;
            animation-delay: 0.35s !important;
        }
    </style>
    <script>
      var _hmt = _hmt || [];
      (function() {
        var hm = document.createElement("script");
        let url = window.location.host;
        let s = document.getElementsByTagName("script")[0];
        if (url === "main.jsalading.cn") {
          hm.src = "https://hm.baidu.com/hm.js?ca9ad5a5d3a3396871468b71414d54b7";
          s.parentNode.insertBefore(hm, s);

        }else if(url === "spare.jsalading.cn"){
          hm.src = "https://hm.baidu.com/hm.js?e2fe2d6909da53d85e29c5d3c5680cd3";
          s.parentNode.insertBefore(hm, s);

          // 添加保旺达的日志采集JS
          var wgscript = document.createElement('script');
          wgscript.id = 'wg-browser-probe';
          wgscript.src = 'static/webgate-browser-probe-3.12.0.min.js';
          wgscript.setAttribute('data-appkey', '56c0eb48-5444-43c8-8c6e-efd5b152174c');
          wgscript.setAttribute('data-appversion', '');
          wgscript.setAttribute('data-serveraddr', 'https://spare.jsalading.cn');
          wgscript.async = true;
          //s.parentNode.insertBefore(wgscript, s);
        }
      })();
    </script>
</head>
<body>
<div id="app">
    <div id="Loading">
        <span class="iconfont WiFi"></span>
        <div class="loader-inner ball-beat">
            <div></div>
            <div></div>
            <div></div>
        </div>
    </div>
</div>
<!-- built files will be auto injected -->


<% if (process.env.NODE_ENV === 'development') { %>
    <script src="static/vue.js"></script>
<% } else {%>
    <script src="static/vue.js"></script>
<%} %>

<!-- <script src="static/vue.js"></script> -->


<script src="static/Chart.bundle.min.js"></script>

</body>
</html>
