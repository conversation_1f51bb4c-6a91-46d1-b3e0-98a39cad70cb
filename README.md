# xsbao_front

> 行商宝前端

## Build Setup

``` bash
# install dependencies
npm install

# serve with hot reload at localhost:8080
npm run dev

# build for production with minification
npm run build

# build for production and view the bundle analyzer report
npm run build --report

# run unit tests
npm run unit

# run all tests
npm test
```



## 开发环境HTTPS服务
``` bash
build/webpack.dev.conf.js

const fs = require('fs');


devServer: {
    https: {
        key: fs.readFileSync(path.join(__dirname, './cert/private.key'),'utf8'),
        cert: fs.readFileSync(path.join(__dirname, './cert/server.crt'),'utf8')
    },
}

起好之后控制台现实的还是http服务,复制到浏览器自行修改一下https
node版本限制10.24.1
```

For a detailed explanation on how things work, check out the [guide](http://vuejs-templates.github.io/webpack/) and [docs for vue-loader](http://vuejs.github.io/vue-loader).
