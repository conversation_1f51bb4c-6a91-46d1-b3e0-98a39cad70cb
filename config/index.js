'use strict'
// Template version: 1.3.1
// see http://vuejs-templates.github.io/webpack for documentation.

const path = require('path')

module.exports = {
  dev: {

    // Paths
    assetsSubDirectory: 'static',
    assetsPublicPath: '/',
    proxyTable: {
      '/apiM': {
        target: 'http://192.168.1.102:6518',
        // target: 'http://192.168.31.72:6001',//开发环境地址
        //  target:'http://**************:8080',//正式环境
          // target:'http://221.178.251.152:8080',
           //target:'http://112.4.23.65:8080',//正式环境,主中心地址
        // target:'http://172.32.147.124:6001',//测试环境
        changeOrigin: true,//允许跨域
        pathRewrite: {
          '^/apiM': ''
        }
      },
      //5G消息中心的服务端
      '/api5g':{
        target: 'http://**************:8080',//生产现有中心
        changeOrigin: true,//允许跨域
        pathRewrite: {
          '^/api5g': ''
        }
      },
      '/api':{
        target:'https://zqgrid.it.10086.cn',//https://zqgrid.it.10086.cn/
        changeOrigin:true,
        secure:false,//不进行证书验证
        pathRewrite:{
          '^/api':''
        }
      }

      /* ,
      '/apiS': {
        target: 'http://**************:8080',//备用中心
        changeOrigin: true,//允许跨域
        pathRewrite: {
          '^/apiS': ''//这里理解成用‘/apiS’代替target里面的地址，后面组件中我们掉接口时直接用api代替 比如我要调用'http://*************:3002/user/add'，直接写‘/api/user/add’即可
        }
      } */
    },

    // Various Dev Server settings
    host: '0.0.0.0', // can be overwritten by process.env.HOST   ************** **************
    port: 8080, // can be overwritten by process.env.PORT, if port is in use, a free one will be determined
    autoOpenBrowser: false,
    errorOverlay: true,
    notifyOnErrors: true,
    poll: false, // https://webpack.js.org/configuration/dev-server/#devserver-watchoptions-

    // Use Eslint Loader?
    // If true, your code will be linted during bundling and
    // linting errors and warnings will be shown in the console.
    useEslint: true,
    // If true, eslint errors and warnings will also be shown in the error overlay
    // in the browser.
    showEslintErrorsInOverlay: false,

    /**
     * Source Maps
     */

    // https://webpack.js.org/configuration/devtool/#development
    devtool: 'cheap-module-eval-source-map',

    // If you have problems debugging vue-files in devtools,
    // set this to false - it *may* help
    // https://vue-loader.vuejs.org/en/options.html#cachebusting
    cacheBusting: true,

    cssSourceMap: true
  },

  build: {
    // Template for index.html
    index: path.resolve(__dirname, '../dist/index.html'),

    // Paths
    assetsRoot: path.resolve(__dirname, '../dist'),
    assetsSubDirectory: 'xsbh5/static',
    assetsPublicPath: '../',

    /**
     * Source Maps
     */

    productionSourceMap: false,
    // https://webpack.js.org/configuration/devtool/#production
    devtool: '#source-map',

    // Gzip off by default as many popular static hosts such as
    // Surge or Netlify already gzip all static assets for you.
    // Before setting to `true`, make sure to:
    // npm install --save-dev compression-webpack-plugin
    productionGzip: false,
    productionGzipExtensions: ['js', 'css'],

    // Run the build command with an extra argument to
    // View the bundle analyzer report after build finishes:
    // `npm run build --report`
    // Set to `true` or `false` to always turn it on or off
    bundleAnalyzerReport: false
  },
  test: {
    // Template for index.html
    index: path.resolve(__dirname, '../dist/index.html'),

    // Paths
    assetsRoot: path.resolve(__dirname, '../dist'),
    assetsSubDirectory: 'xsbtest/static',
    assetsPublicPath: '../',

    /**
     * Source Maps
     */

    productionSourceMap: false,
    // https://webpack.js.org/configuration/devtool/#production
    devtool: '#source-map',

    // Gzip off by default as many popular static hosts such as
    // Surge or Netlify already gzip all static assets for you.
    // Before setting to `true`, make sure to:
    // npm install --save-dev compression-webpack-plugin
    productionGzip: false,
    productionGzipExtensions: ['js', 'css'],

    // Run the build command with an extra argument to
    // View the bundle analyzer report after build finishes:
    // `npm run build --report`
    // Set to `true` or `false` to always turn it on or off
    bundleAnalyzerReport: true
  }
}
