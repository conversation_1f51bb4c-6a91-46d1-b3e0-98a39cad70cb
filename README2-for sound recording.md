# 录音代码cv案例


# html
```html
// 录音波浪
<div class="dialog-box">
    <img src="../../assets/img/chatloading.gif"/>
    <div class="recwave-box1">正在录音识别中...</div>
    <div class="recwave-box"></div>
</div>

// css
.dialog-box {
    position: fixed;
    top: -300px;
    left: 50%;
    transform: translateX(-50%);
    width: 70vw;
    padding: 40px 20px;
    box-sizing: border-box;
    background: #f5f6f6;
    border-radius: 8px;
    text-align: center;
    z-index: 777;
    img {
        width: 85%;
        margin-bottom: 30px;
    }
    div {
        font-size: 12px;
        color: #ccc;
    }
}
```



# 初始化
```javascript
import RecorderManger from '@/base/chatUtil/RecorderManager.js'
import ClientJs from '@/base/clientjs'


// data里面的实体类
data(){return{ recordingEntity: {}}}



// 语音--created
if (/android|harmony/gi.test(navigator.userAgent)) {
    let str1 = localStorage.getItem('getAudioPermissfun')
    if (str1) {
        this.recordingEntity = new RecorderManger({}, { input: function() {},elem: '.recwave-box' }, this.getRecordResultCbFn)
    } else {
        ClientJs.getAudioPermiss('AudioPermissGroup')
    }
} else {
    this.recordingEntity = new RecorderManger({}, { input: function() {},elem: '.recwave-box' }, this.getRecordResultCbFn)
}



// mounted--里面必须加的,推出后台销毁录音,groupOrdersBusiness这个是当前路由
window['AudioPermissGroup'] = (res) => {
    localStorage.setItem('getAudioPermissfun', 1)
    this.recordingEntity = new RecorderManger({}, { input: function() {},elem: '.recwave-box' }, this.getRecordResultCbFn)
}
const _this = this
document.addEventListener("visibilitychange", function() {
    if (window.location.href.indexOf('groupOrdersBusiness') >= 0 && document.visibilityState === 'hidden') {
        // 当页面由前端运行在后端时，出发此代码
        if (_this.rec) {
            _this.rec && _this.rec.recorder.close();
            _this.rec = null
        }
    }
});
```

# 长按开始,松开结束,回调文本
```javascript
//录音回调
getRecordResultCbFn(obj) {
    if (!obj.voiceTxt) return
    if (obj.code == '-1') {
        this.$toast(obj.voiceTxt)
        return
    }
    if (obj.voiceTxt && obj.voiceTxt.length > 500) {
        let result = obj.voiceTxt.substring(0, 500);
        this.remark = result
    } else {
        this.remark = obj.voiceTxt
    }
},
handlerTouchstart(e) {
    e.preventDefault()
    this.recordingEntity.startRecorder()
    document.querySelector('.dialog-box').style.top = '42px'
},
handlerTouchend() {
    this.recordingEntity.endRecorder()
    document.querySelector('.dialog-box').style.top = '-3300px'
},
```
