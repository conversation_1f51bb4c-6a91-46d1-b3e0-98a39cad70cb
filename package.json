{"name": "xsbao_front", "version": "1.0.0", "description": "阿拉盯前端", "author": "hq", "private": true, "scripts": {"dev-ald": "cross-env DEV_PRO=xsbh5 npm run dev", "dev-panc": "cross-env DEV_PRO=panc npm run dev", "dev-pad": "cross-env DEV_PRO=pad npm run dev", "dev-others": "cross-env DEV_PRO=others npm run dev", "dev-ole": "cross-env DEV_PRO=ole npm run dev", "dev-thirds": "cross-env DEV_PRO=thirds npm run dev", "dev-handhall": "cross-env DEV_PRO=handhall npm run dev", "dev-amsdw": "cross-env DEV_PRO=amsdw npm run dev", "dev": "webpack-dev-server --hot --inline --progress --config build/webpack.dev.conf.js --host 0.0.0.0", "start": "npm run dev", "unit": "jest --config test/unit/jest.conf.js", "test": "npm run unit", "lint": "eslint --ext .js,.vue src test/unit", "build": "node --max-old-space-size=4096 build/build.js", "build-all": "node build/build_all.js", "build-ald": "node build/build_ald.js", "build-panc": "node build/build_panc.js"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "axios": "0.21.1", "better-scroll": "1.15.2", "crypto-js": "4.2.0", "current-processes": "0.2.1", "echarts": "4.8.0", "fastclick": "1.0.6", "html2canvas": "1.1.1", "markdown-it": "^14.1.0", "mint-ui": "2.2.13", "pdfh5": "1.4.2", "recorder-core": "1.3.23122400", "vconsole": "3.15.1", "vue": "2.6.10", "vue-router": "3.0.6", "vuedraggable": "2.24.3", "vuex": "3.1.1"}, "devDependencies": {"@vue/test-utils": "1.0.0-beta.33", "autoprefixer": "7.2.6", "babel-core": "6.26.3", "babel-eslint": "8.2.6", "babel-helper-vue-jsx-merge-props": "2.0.3", "babel-jest": "21.2.0", "babel-loader": "7.1.5", "babel-plugin-dynamic-import-node": "1.2.0", "babel-plugin-syntax-jsx": "6.18.0", "babel-plugin-transform-es2015-modules-commonjs": "6.26.2", "babel-plugin-transform-runtime": "6.23.0", "babel-plugin-transform-vue-jsx": "3.7.0", "babel-preset-env": "1.7.0", "babel-preset-stage-2": "6.24.1", "chalk": "2.4.2", "copy-webpack-plugin": "4.6.0", "cross-env": "^7.0.3", "css-loader": "0.28.11", "eslint": "4.19.1", "eslint-friendly-formatter": "3.0.0", "eslint-loader": "1.9.0", "eslint-plugin-vue": "4.7.1", "express": "4.17.1", "express-route": "0.1.4", "extract-text-webpack-plugin": "3.0.2", "file-loader": "1.1.11", "friendly-errors-webpack-plugin": "1.7.0", "glob": "7.1.4", "html-webpack-plugin": "2.30.1", "jest": "22.4.4", "jest-serializer-vue": "0.3.0", "less": "3.9.0", "less-loader": "4.1.0", "node-notifier": "5.4.0", "optimize-css-assets-webpack-plugin": "3.2.0", "ora": "1.4.0", "portfinder": "1.0.20", "postcss-import": "11.1.0", "postcss-loader": "2.1.6", "postcss-url": "7.3.2", "rimraf": "2.6.3", "sass-resources-loader": "2.0.3", "semver": "5.7.0", "shelljs": "0.7.8", "style-loader": "0.23.1", "uglifyjs-webpack-plugin": "1.3.0", "url-loader": "0.5.9", "vite": "^4.5.14", "vue-jest": "1.4.0", "vue-loader": "13.7.3", "vue-style-loader": "3.1.2", "vue-template-compiler": "2.6.10", "webpack": "3.12.0", "webpack-bundle-analyzer": "2.13.1", "webpack-dev-server": "2.11.5", "webpack-merge": "4.2.1"}, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}