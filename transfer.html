<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,user-scalable=no">
    <title></title>
    
</head>
<body>
</body>
<script>
    function getQueryString(name){
	    var reg = new RegExp("(^|&)"+name+"=([^&]*)(&|$)","i");
		var r = window.location.search.substr(1).match(reg);
		if(r!=null){
			return decodeURI(r[2]);
		}
		return null;
	}
	var pageHash = getQueryString('pageHash');
	// if(pageHash == 'loginForFace'){//刷脸注册前的登录
	// 	window.location.href = window.location.origin + "/xsbh5/index.html#/loginForFace"
	// } else if(pageHash == 'faceRegister'){//刷脸注册前的登录
	// 	window.location.href = window.location.origin + "/xsbh5/index.html#/faceRegister" + window.location.search;
	// } else if(pageHash == 'resetpassword'){//忘记密码
	// 	window.location.href = window.location.origin + "/xsbh5/index.html#/resetpassword" + window.location.search;
	if(pageHash){
		window.location.href = window.location.origin + "/xsbh5/index.html#/"+pageHash+ window.location.search;
	} else {
		window.location.href = window.location.origin + "/xsbh5/index.html#/cardBack" + window.location.search;
	}
    
</script>
</html>
